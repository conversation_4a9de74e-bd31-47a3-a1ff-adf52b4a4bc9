// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/predefined_rules_proto_editions.proto

package cases

import (
	validate "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PredefinedEnumRuleEdition2023_EnumEdition2023 int32

const (
	PredefinedEnumRuleEdition2023_ENUM_EDITION2023_ZERO_UNSPECIFIED PredefinedEnumRuleEdition2023_EnumEdition2023 = 0
	PredefinedEnumRuleEdition2023_ENUM_EDITION2023_ONE              PredefinedEnumRuleEdition2023_EnumEdition2023 = 1
)

// Enum value maps for PredefinedEnumRuleEdition2023_EnumEdition2023.
var (
	PredefinedEnumRuleEdition2023_EnumEdition2023_name = map[int32]string{
		0: "ENUM_EDITION2023_ZERO_UNSPECIFIED",
		1: "ENUM_EDITION2023_ONE",
	}
	PredefinedEnumRuleEdition2023_EnumEdition2023_value = map[string]int32{
		"ENUM_EDITION2023_ZERO_UNSPECIFIED": 0,
		"ENUM_EDITION2023_ONE":              1,
	}
)

func (x PredefinedEnumRuleEdition2023_EnumEdition2023) Enum() *PredefinedEnumRuleEdition2023_EnumEdition2023 {
	p := new(PredefinedEnumRuleEdition2023_EnumEdition2023)
	*p = x
	return p
}

func (x PredefinedEnumRuleEdition2023_EnumEdition2023) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PredefinedEnumRuleEdition2023_EnumEdition2023) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_enumTypes[0].Descriptor()
}

func (PredefinedEnumRuleEdition2023_EnumEdition2023) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_enumTypes[0]
}

func (x PredefinedEnumRuleEdition2023_EnumEdition2023) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PredefinedEnumRuleEdition2023_EnumEdition2023.Descriptor instead.
func (PredefinedEnumRuleEdition2023_EnumEdition2023) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{15, 0}
}

type PredefinedFloatRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *float32 `protobuf:"fixed32,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedFloatRuleEdition2023) Reset() {
	*x = PredefinedFloatRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFloatRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFloatRuleEdition2023) ProtoMessage() {}

func (x *PredefinedFloatRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFloatRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedFloatRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{0}
}

func (x *PredefinedFloatRuleEdition2023) GetVal() float32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedDoubleRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *float64 `protobuf:"fixed64,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedDoubleRuleEdition2023) Reset() {
	*x = PredefinedDoubleRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedDoubleRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedDoubleRuleEdition2023) ProtoMessage() {}

func (x *PredefinedDoubleRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedDoubleRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedDoubleRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{1}
}

func (x *PredefinedDoubleRuleEdition2023) GetVal() float64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedInt32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedInt32RuleEdition2023) Reset() {
	*x = PredefinedInt32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedInt32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedInt32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedInt32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedInt32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedInt32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{2}
}

func (x *PredefinedInt32RuleEdition2023) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedInt64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int64 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedInt64RuleEdition2023) Reset() {
	*x = PredefinedInt64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedInt64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedInt64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedInt64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedInt64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedInt64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{3}
}

func (x *PredefinedInt64RuleEdition2023) GetVal() int64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedUInt32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *uint32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedUInt32RuleEdition2023) Reset() {
	*x = PredefinedUInt32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedUInt32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedUInt32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedUInt32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedUInt32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedUInt32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{4}
}

func (x *PredefinedUInt32RuleEdition2023) GetVal() uint32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedUInt64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *uint64 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedUInt64RuleEdition2023) Reset() {
	*x = PredefinedUInt64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedUInt64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedUInt64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedUInt64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedUInt64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedUInt64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{5}
}

func (x *PredefinedUInt64RuleEdition2023) GetVal() uint64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedSInt32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"zigzag32,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedSInt32RuleEdition2023) Reset() {
	*x = PredefinedSInt32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSInt32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSInt32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedSInt32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSInt32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedSInt32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{6}
}

func (x *PredefinedSInt32RuleEdition2023) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedSInt64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int64 `protobuf:"zigzag64,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedSInt64RuleEdition2023) Reset() {
	*x = PredefinedSInt64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSInt64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSInt64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedSInt64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSInt64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedSInt64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{7}
}

func (x *PredefinedSInt64RuleEdition2023) GetVal() int64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedFixed32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *uint32 `protobuf:"fixed32,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedFixed32RuleEdition2023) Reset() {
	*x = PredefinedFixed32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFixed32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFixed32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedFixed32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFixed32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedFixed32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{8}
}

func (x *PredefinedFixed32RuleEdition2023) GetVal() uint32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedFixed64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *uint64 `protobuf:"fixed64,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedFixed64RuleEdition2023) Reset() {
	*x = PredefinedFixed64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFixed64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFixed64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedFixed64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFixed64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedFixed64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{9}
}

func (x *PredefinedFixed64RuleEdition2023) GetVal() uint64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedSFixed32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"fixed32,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedSFixed32RuleEdition2023) Reset() {
	*x = PredefinedSFixed32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSFixed32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSFixed32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedSFixed32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSFixed32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedSFixed32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{10}
}

func (x *PredefinedSFixed32RuleEdition2023) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedSFixed64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int64 `protobuf:"fixed64,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedSFixed64RuleEdition2023) Reset() {
	*x = PredefinedSFixed64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSFixed64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSFixed64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedSFixed64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSFixed64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedSFixed64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{11}
}

func (x *PredefinedSFixed64RuleEdition2023) GetVal() int64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedBoolRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *bool `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedBoolRuleEdition2023) Reset() {
	*x = PredefinedBoolRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedBoolRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedBoolRuleEdition2023) ProtoMessage() {}

func (x *PredefinedBoolRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedBoolRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedBoolRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{12}
}

func (x *PredefinedBoolRuleEdition2023) GetVal() bool {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return false
}

type PredefinedStringRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedStringRuleEdition2023) Reset() {
	*x = PredefinedStringRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedStringRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedStringRuleEdition2023) ProtoMessage() {}

func (x *PredefinedStringRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedStringRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedStringRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{13}
}

func (x *PredefinedStringRuleEdition2023) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type PredefinedBytesRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []byte `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedBytesRuleEdition2023) Reset() {
	*x = PredefinedBytesRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedBytesRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedBytesRuleEdition2023) ProtoMessage() {}

func (x *PredefinedBytesRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedBytesRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedBytesRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{14}
}

func (x *PredefinedBytesRuleEdition2023) GetVal() []byte {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedEnumRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *PredefinedEnumRuleEdition2023_EnumEdition2023 `protobuf:"varint,1,opt,name=val,enum=buf.validate.conformance.cases.PredefinedEnumRuleEdition2023_EnumEdition2023" json:"val,omitempty"`
}

func (x *PredefinedEnumRuleEdition2023) Reset() {
	*x = PredefinedEnumRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedEnumRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedEnumRuleEdition2023) ProtoMessage() {}

func (x *PredefinedEnumRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedEnumRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedEnumRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{15}
}

func (x *PredefinedEnumRuleEdition2023) GetVal() PredefinedEnumRuleEdition2023_EnumEdition2023 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return PredefinedEnumRuleEdition2023_ENUM_EDITION2023_ZERO_UNSPECIFIED
}

type PredefinedRepeatedRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []uint64 `protobuf:"varint,1,rep,packed,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedRuleEdition2023) Reset() {
	*x = PredefinedRepeatedRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedRuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{16}
}

func (x *PredefinedRepeatedRuleEdition2023) GetVal() []uint64 {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedMapRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[uint64]uint64 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *PredefinedMapRuleEdition2023) Reset() {
	*x = PredefinedMapRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedMapRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedMapRuleEdition2023) ProtoMessage() {}

func (x *PredefinedMapRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedMapRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedMapRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{17}
}

func (x *PredefinedMapRuleEdition2023) GetVal() map[uint64]uint64 {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedDurationRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *durationpb.Duration `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedDurationRuleEdition2023) Reset() {
	*x = PredefinedDurationRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedDurationRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedDurationRuleEdition2023) ProtoMessage() {}

func (x *PredefinedDurationRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedDurationRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedDurationRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{18}
}

func (x *PredefinedDurationRuleEdition2023) GetVal() *durationpb.Duration {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedTimestampRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedTimestampRuleEdition2023) Reset() {
	*x = PredefinedTimestampRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedTimestampRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedTimestampRuleEdition2023) ProtoMessage() {}

func (x *PredefinedTimestampRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedTimestampRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedTimestampRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{19}
}

func (x *PredefinedTimestampRuleEdition2023) GetVal() *timestamppb.Timestamp {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedFloatRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.FloatValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedFloatRuleEdition2023) Reset() {
	*x = PredefinedWrappedFloatRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedFloatRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedFloatRuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedFloatRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedFloatRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedFloatRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{20}
}

func (x *PredefinedWrappedFloatRuleEdition2023) GetVal() *wrapperspb.FloatValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedDoubleRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.DoubleValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedDoubleRuleEdition2023) Reset() {
	*x = PredefinedWrappedDoubleRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedDoubleRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedDoubleRuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedDoubleRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedDoubleRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedDoubleRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{21}
}

func (x *PredefinedWrappedDoubleRuleEdition2023) GetVal() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedInt32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.Int32Value `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedInt32RuleEdition2023) Reset() {
	*x = PredefinedWrappedInt32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedInt32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedInt32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedInt32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedInt32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedInt32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{22}
}

func (x *PredefinedWrappedInt32RuleEdition2023) GetVal() *wrapperspb.Int32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedInt64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.Int64Value `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedInt64RuleEdition2023) Reset() {
	*x = PredefinedWrappedInt64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedInt64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedInt64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedInt64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedInt64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedInt64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{23}
}

func (x *PredefinedWrappedInt64RuleEdition2023) GetVal() *wrapperspb.Int64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedUInt32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedUInt32RuleEdition2023) Reset() {
	*x = PredefinedWrappedUInt32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedUInt32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedUInt32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedUInt32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedUInt32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedUInt32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{24}
}

func (x *PredefinedWrappedUInt32RuleEdition2023) GetVal() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedUInt64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.UInt64Value `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedUInt64RuleEdition2023) Reset() {
	*x = PredefinedWrappedUInt64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedUInt64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedUInt64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedUInt64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedUInt64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedUInt64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{25}
}

func (x *PredefinedWrappedUInt64RuleEdition2023) GetVal() *wrapperspb.UInt64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedBoolRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.BoolValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedBoolRuleEdition2023) Reset() {
	*x = PredefinedWrappedBoolRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedBoolRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedBoolRuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedBoolRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedBoolRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedBoolRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{26}
}

func (x *PredefinedWrappedBoolRuleEdition2023) GetVal() *wrapperspb.BoolValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedStringRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedStringRuleEdition2023) Reset() {
	*x = PredefinedWrappedStringRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedStringRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedStringRuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedStringRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedStringRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedStringRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{27}
}

func (x *PredefinedWrappedStringRuleEdition2023) GetVal() *wrapperspb.StringValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedBytesRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.BytesValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedBytesRuleEdition2023) Reset() {
	*x = PredefinedWrappedBytesRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedBytesRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedBytesRuleEdition2023) ProtoMessage() {}

func (x *PredefinedWrappedBytesRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedBytesRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedBytesRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{28}
}

func (x *PredefinedWrappedBytesRuleEdition2023) GetVal() *wrapperspb.BytesValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedFloatRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.FloatValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedFloatRuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedFloatRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedFloatRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedFloatRuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedFloatRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedFloatRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedFloatRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{29}
}

func (x *PredefinedRepeatedWrappedFloatRuleEdition2023) GetVal() []*wrapperspb.FloatValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedDoubleRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.DoubleValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedDoubleRuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedDoubleRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedDoubleRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedDoubleRuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedDoubleRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedDoubleRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedDoubleRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{30}
}

func (x *PredefinedRepeatedWrappedDoubleRuleEdition2023) GetVal() []*wrapperspb.DoubleValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedInt32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.Int32Value `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedInt32RuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedInt32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedInt32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedInt32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedInt32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedInt32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedInt32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{31}
}

func (x *PredefinedRepeatedWrappedInt32RuleEdition2023) GetVal() []*wrapperspb.Int32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedInt64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.Int64Value `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedInt64RuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedInt64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedInt64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedInt64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedInt64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedInt64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedInt64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{32}
}

func (x *PredefinedRepeatedWrappedInt64RuleEdition2023) GetVal() []*wrapperspb.Int64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedUInt32RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.UInt32Value `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedUInt32RuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedUInt32RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedUInt32RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedUInt32RuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedUInt32RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedUInt32RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedUInt32RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{33}
}

func (x *PredefinedRepeatedWrappedUInt32RuleEdition2023) GetVal() []*wrapperspb.UInt32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedUInt64RuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.UInt64Value `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedUInt64RuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedUInt64RuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedUInt64RuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedUInt64RuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedUInt64RuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedUInt64RuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedUInt64RuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{34}
}

func (x *PredefinedRepeatedWrappedUInt64RuleEdition2023) GetVal() []*wrapperspb.UInt64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedBoolRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.BoolValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedBoolRuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedBoolRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedBoolRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedBoolRuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedBoolRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedBoolRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedBoolRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{35}
}

func (x *PredefinedRepeatedWrappedBoolRuleEdition2023) GetVal() []*wrapperspb.BoolValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedStringRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.StringValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedStringRuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedStringRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedStringRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedStringRuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedStringRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedStringRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedStringRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{36}
}

func (x *PredefinedRepeatedWrappedStringRuleEdition2023) GetVal() []*wrapperspb.StringValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedBytesRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.BytesValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedBytesRuleEdition2023) Reset() {
	*x = PredefinedRepeatedWrappedBytesRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedBytesRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedBytesRuleEdition2023) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedBytesRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedBytesRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedBytesRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{37}
}

func (x *PredefinedRepeatedWrappedBytesRuleEdition2023) GetVal() []*wrapperspb.BytesValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedAndCustomRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32                                     `protobuf:"zigzag32,1,opt,name=a" json:"a,omitempty"`
	B *PredefinedAndCustomRuleEdition2023_Nested `protobuf:"bytes,2,opt,name=b" json:"b,omitempty"`
}

func (x *PredefinedAndCustomRuleEdition2023) Reset() {
	*x = PredefinedAndCustomRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedAndCustomRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedAndCustomRuleEdition2023) ProtoMessage() {}

func (x *PredefinedAndCustomRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedAndCustomRuleEdition2023.ProtoReflect.Descriptor instead.
func (*PredefinedAndCustomRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{38}
}

func (x *PredefinedAndCustomRuleEdition2023) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *PredefinedAndCustomRuleEdition2023) GetB() *PredefinedAndCustomRuleEdition2023_Nested {
	if x != nil {
		return x.B
	}
	return nil
}

type StandardPredefinedAndCustomRuleEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"zigzag32,1,opt,name=a" json:"a,omitempty"`
}

func (x *StandardPredefinedAndCustomRuleEdition2023) Reset() {
	*x = StandardPredefinedAndCustomRuleEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StandardPredefinedAndCustomRuleEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StandardPredefinedAndCustomRuleEdition2023) ProtoMessage() {}

func (x *StandardPredefinedAndCustomRuleEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StandardPredefinedAndCustomRuleEdition2023.ProtoReflect.Descriptor instead.
func (*StandardPredefinedAndCustomRuleEdition2023) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{39}
}

func (x *StandardPredefinedAndCustomRuleEdition2023) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

type PredefinedAndCustomRuleEdition2023_Nested struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C *int32 `protobuf:"zigzag32,1,opt,name=c" json:"c,omitempty"`
}

func (x *PredefinedAndCustomRuleEdition2023_Nested) Reset() {
	*x = PredefinedAndCustomRuleEdition2023_Nested{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedAndCustomRuleEdition2023_Nested) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedAndCustomRuleEdition2023_Nested) ProtoMessage() {}

func (x *PredefinedAndCustomRuleEdition2023_Nested) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedAndCustomRuleEdition2023_Nested.ProtoReflect.Descriptor instead.
func (*PredefinedAndCustomRuleEdition2023_Nested) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP(), []int{38, 0}
}

func (x *PredefinedAndCustomRuleEdition2023_Nested) GetC() int32 {
	if x != nil && x.C != nil {
		return *x.C
	}
	return 0
}

var file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*validate.FloatRules)(nil),
		ExtensionType: (*float32)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.float_abs_range_edition_2023",
		Tag:           "fixed32,1162,opt,name=float_abs_range_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.DoubleRules)(nil),
		ExtensionType: (*float64)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.double_abs_range_edition_2023",
		Tag:           "fixed64,1162,opt,name=double_abs_range_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.Int32Rules)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.int32_abs_in_edition_2023",
		Tag:           "varint,1162,rep,packed,name=int32_abs_in_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.Int64Rules)(nil),
		ExtensionType: ([]*wrapperspb.Int64Value)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.int64_abs_in_edition_2023",
		Tag:           "bytes,1162,rep,name=int64_abs_in_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.UInt32Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.uint32_even_edition_2023",
		Tag:           "varint,1162,opt,name=uint32_even_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.UInt64Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.uint64_even_edition_2023",
		Tag:           "varint,1162,opt,name=uint64_even_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.SInt32Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.sint32_even_edition_2023",
		Tag:           "varint,1162,opt,name=sint32_even_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.SInt64Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.sint64_even_edition_2023",
		Tag:           "varint,1162,opt,name=sint64_even_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.Fixed32Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.fixed32_even_edition_2023",
		Tag:           "varint,1162,opt,name=fixed32_even_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.Fixed64Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.fixed64_even_edition_2023",
		Tag:           "varint,1162,opt,name=fixed64_even_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.SFixed32Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.sfixed32_even_edition_2023",
		Tag:           "varint,1162,opt,name=sfixed32_even_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.SFixed64Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.sfixed64_even_edition_2023",
		Tag:           "varint,1162,opt,name=sfixed64_even_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.BoolRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.bool_false_edition_2023",
		Tag:           "varint,1162,opt,name=bool_false_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.StringRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.string_valid_path_edition_2023",
		Tag:           "varint,1162,opt,name=string_valid_path_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.BytesRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.bytes_valid_path_edition_2023",
		Tag:           "varint,1162,opt,name=bytes_valid_path_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.EnumRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.enum_non_zero_edition_2023",
		Tag:           "varint,1162,opt,name=enum_non_zero_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.RepeatedRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.repeated_at_least_five_edition_2023",
		Tag:           "varint,1162,opt,name=repeated_at_least_five_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.MapRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.map_at_least_five_edition_2023",
		Tag:           "varint,1162,opt,name=map_at_least_five_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.DurationRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.duration_too_long_edition_2023",
		Tag:           "varint,1162,opt,name=duration_too_long_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
	{
		ExtendedType:  (*validate.TimestampRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1162,
		Name:          "buf.validate.conformance.cases.timestamp_in_range_edition_2023",
		Tag:           "varint,1162,opt,name=timestamp_in_range_edition_2023",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto_editions.proto",
	},
}

// Extension fields to validate.FloatRules.
var (
	// optional float float_abs_range_edition_2023 = 1162;
	E_FloatAbsRangeEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[0]
)

// Extension fields to validate.DoubleRules.
var (
	// optional double double_abs_range_edition_2023 = 1162;
	E_DoubleAbsRangeEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[1]
)

// Extension fields to validate.Int32Rules.
var (
	// repeated int32 int32_abs_in_edition_2023 = 1162;
	E_Int32AbsInEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[2]
)

// Extension fields to validate.Int64Rules.
var (
	// repeated google.protobuf.Int64Value int64_abs_in_edition_2023 = 1162;
	E_Int64AbsInEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[3]
)

// Extension fields to validate.UInt32Rules.
var (
	// optional bool uint32_even_edition_2023 = 1162;
	E_Uint32EvenEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[4]
)

// Extension fields to validate.UInt64Rules.
var (
	// optional bool uint64_even_edition_2023 = 1162;
	E_Uint64EvenEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[5]
)

// Extension fields to validate.SInt32Rules.
var (
	// optional bool sint32_even_edition_2023 = 1162;
	E_Sint32EvenEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[6]
)

// Extension fields to validate.SInt64Rules.
var (
	// optional bool sint64_even_edition_2023 = 1162;
	E_Sint64EvenEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[7]
)

// Extension fields to validate.Fixed32Rules.
var (
	// optional bool fixed32_even_edition_2023 = 1162;
	E_Fixed32EvenEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[8]
)

// Extension fields to validate.Fixed64Rules.
var (
	// optional bool fixed64_even_edition_2023 = 1162;
	E_Fixed64EvenEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[9]
)

// Extension fields to validate.SFixed32Rules.
var (
	// optional bool sfixed32_even_edition_2023 = 1162;
	E_Sfixed32EvenEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[10]
)

// Extension fields to validate.SFixed64Rules.
var (
	// optional bool sfixed64_even_edition_2023 = 1162;
	E_Sfixed64EvenEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[11]
)

// Extension fields to validate.BoolRules.
var (
	// optional bool bool_false_edition_2023 = 1162;
	E_BoolFalseEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[12]
)

// Extension fields to validate.StringRules.
var (
	// optional bool string_valid_path_edition_2023 = 1162;
	E_StringValidPathEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[13]
)

// Extension fields to validate.BytesRules.
var (
	// optional bool bytes_valid_path_edition_2023 = 1162;
	E_BytesValidPathEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[14]
)

// Extension fields to validate.EnumRules.
var (
	// optional bool enum_non_zero_edition_2023 = 1162;
	E_EnumNonZeroEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[15]
)

// Extension fields to validate.RepeatedRules.
var (
	// optional bool repeated_at_least_five_edition_2023 = 1162;
	E_RepeatedAtLeastFiveEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[16]
)

// Extension fields to validate.MapRules.
var (
	// optional bool map_at_least_five_edition_2023 = 1162;
	E_MapAtLeastFiveEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[17]
)

// Extension fields to validate.DurationRules.
var (
	// optional bool duration_too_long_edition_2023 = 1162;
	E_DurationTooLongEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[18]
)

// Extension fields to validate.TimestampRules.
var (
	// optional bool timestamp_in_range_edition_2023 = 1162;
	E_TimestampInRangeEdition_2023 = &file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes[19]
)

var File_buf_validate_conformance_cases_predefined_rules_proto_editions_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDesc = []byte{
	0x0a, 0x44, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x73, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x1e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x0a, 0x06, 0xd5, 0x48, 0x00, 0x00, 0x80, 0x3f,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x44, 0x0a, 0x1f, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x12, 0x0a, 0xd1, 0x48, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x46, 0x0a, 0x1e, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x24, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x12, 0xba, 0x48, 0x0f, 0x1a,
	0x0d, 0xd2, 0x48, 0x0a, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x47, 0x0a, 0x1e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x25, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x13, 0xba, 0x48, 0x10, 0x22, 0x0e, 0xd2, 0x48, 0x0b, 0x08, 0xfe, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3d, 0x0a, 0x1f,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12,
	0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x08, 0xba, 0x48,
	0x05, 0x2a, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3d, 0x0a, 0x1f, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52,
	0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x32, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3d, 0x0a, 0x1f, 0x50, 0x72,
	0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x08, 0xba, 0x48, 0x05, 0x3a,
	0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3d, 0x0a, 0x1f, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x08, 0xba, 0x48, 0x05, 0x42, 0x03,
	0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3e, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75, 0x6c,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x08, 0xba, 0x48, 0x05, 0x4a, 0x03,
	0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3e, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x08, 0xba, 0x48, 0x05, 0x52, 0x03,
	0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3f, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x08, 0xba, 0x48, 0x05, 0x5a,
	0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3f, 0x0a, 0x21, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52,
	0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x62, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3b, 0x0a, 0x1d, 0x50, 0x72,
	0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x08, 0xba, 0x48, 0x05, 0x6a, 0x03, 0xd0,
	0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3d, 0x0a, 0x1f, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xd0, 0x48,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3c, 0x0a, 0x1e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x08, 0xba, 0x48, 0x05, 0x7a, 0x03, 0xd0, 0x48, 0x01, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0xdf, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x6a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x4d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30,
	0x32, 0x33, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30,
	0x32, 0x33, 0x42, 0x09, 0xba, 0x48, 0x06, 0x82, 0x01, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x52, 0x0a, 0x0f, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x45, 0x44,
	0x49, 0x54, 0x49, 0x4f, 0x4e, 0x32, 0x30, 0x32, 0x33, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x32, 0x30, 0x32, 0x33,
	0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x22, 0x40, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1b, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x09, 0xba, 0x48, 0x06, 0x92, 0x01, 0x03,
	0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xba, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x61, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x62, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x4d, 0x61, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x30, 0x32, 0x33, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x09, 0xba,
	0x48, 0x06, 0x9a, 0x01, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a,
	0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5b, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x36, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x09, 0xba, 0x48, 0x06, 0xaa, 0x01, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x5d, 0x0a, 0x22, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x37, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x42, 0x09, 0xba, 0x48, 0x06, 0xb2, 0x01, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x63, 0x0a, 0x25, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x3a, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x0a, 0x06, 0xd5, 0x48, 0x00, 0x00, 0x80,
	0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x69, 0x0a, 0x26, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33,
	0x12, 0x3f, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0f, 0xba, 0x48, 0x0c,
	0x12, 0x0a, 0xd1, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x6a, 0x0a, 0x25, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x41, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x12, 0xba, 0x48, 0x0f, 0x1a, 0x0d, 0xd2, 0x48, 0x0a, 0xfe, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6b, 0x0a,
	0x25, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x42, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x13, 0xba, 0x48, 0x10, 0x22, 0x0e, 0xd2, 0x48, 0x0b, 0x08, 0xfe, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x62, 0x0a, 0x26, 0x50, 0x72,
	0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x30, 0x32, 0x33, 0x12, 0x38, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x08, 0xba, 0x48, 0x05, 0x2a, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x62,
	0x0a, 0x26, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x38, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x32, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x5e, 0x0a, 0x24, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x36, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x6a, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x62, 0x0a, 0x26, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x38, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xd0, 0x48,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x60, 0x0a, 0x25, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12,
	0x37, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x7a, 0x03,
	0xd0, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x70, 0x0a, 0x2d, 0x50, 0x72, 0x65, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x3f, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x92, 0x01, 0x0a, 0x22, 0x08, 0x0a, 0x06, 0xd5,
	0x48, 0x00, 0x00, 0x80, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x76, 0x0a, 0x2e, 0x50, 0x72,
	0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x44, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x14, 0xba, 0x48, 0x11, 0x92, 0x01, 0x0e, 0x22,
	0x0c, 0x12, 0x0a, 0xd1, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x77, 0x0a, 0x2d, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x12, 0x46, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x17, 0xba,
	0x48, 0x14, 0x92, 0x01, 0x11, 0x22, 0x0f, 0x1a, 0x0d, 0xd2, 0x48, 0x0a, 0xfe, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x78, 0x0a, 0x2d, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x47, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x18, 0xba, 0x48, 0x15, 0x92, 0x01, 0x12, 0x22, 0x10,
	0x22, 0x0e, 0xd2, 0x48, 0x0b, 0x08, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6f, 0x0a, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x3d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x2a, 0x03, 0xd0, 0x48,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6f, 0x0a, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x3d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x32, 0x03, 0xd0,
	0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6b, 0x0a, 0x2c, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x3b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x6a, 0x03, 0xd0, 0x48, 0x01, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x6f, 0x0a, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x3d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x72, 0x03, 0xd0, 0x48, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6d, 0x0a, 0x2d, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x3c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x7a, 0x03, 0xd0, 0x48, 0x01, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0xda, 0x03, 0x0a, 0x22, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x77, 0x0a, 0x01, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x69, 0xba, 0x48, 0x66, 0xba, 0x01, 0x5e, 0x0a, 0x2e,
	0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x61,
	0x72, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x1a, 0x2c,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x3e, 0x20, 0x32, 0x34, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x20, 0x3a,
	0x20, 0x27, 0x61, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x6e, 0x20, 0x32, 0x34, 0x27, 0x3a, 0x03, 0xd0, 0x48,
	0x01, 0x52, 0x01, 0x61, 0x12, 0xbf, 0x01, 0x0a, 0x01, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x49, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x42, 0x66, 0xba, 0x48, 0x63,
	0xba, 0x01, 0x60, 0x0a, 0x30, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1b, 0x62, 0x2e, 0x63, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20,
	0x62, 0x65, 0x20, 0x61, 0x20, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x20, 0x6f, 0x66,
	0x20, 0x33, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x63, 0x20, 0x25, 0x20, 0x33, 0x20, 0x3d,
	0x3d, 0x20, 0x30, 0x52, 0x01, 0x62, 0x1a, 0x79, 0x0a, 0x06, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x12, 0x6f, 0x0a, 0x01, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x61, 0xba, 0x48, 0x5e,
	0xba, 0x01, 0x56, 0x0a, 0x2e, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32,
	0x30, 0x32, 0x33, 0x1a, 0x24, 0x74, 0x68, 0x69, 0x73, 0x20, 0x3e, 0x20, 0x30, 0x20, 0x3f, 0x20,
	0x27, 0x27, 0x20, 0x3a, 0x20, 0x27, 0x63, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x27, 0x3a, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x01,
	0x63, 0x22, 0xb1, 0x01, 0x0a, 0x2a, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x50, 0x72,
	0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33,
	0x12, 0x82, 0x01, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x74, 0xba, 0x48,
	0x71, 0xba, 0x01, 0x67, 0x0a, 0x37, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x5f, 0x70,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x72,
	0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x1a, 0x2c, 0x74,
	0x68, 0x69, 0x73, 0x20, 0x3e, 0x20, 0x32, 0x34, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x20, 0x3a, 0x20,
	0x27, 0x61, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x6e, 0x20, 0x32, 0x34, 0x27, 0x3a, 0x05, 0xd0, 0x48, 0x01,
	0x10, 0x38, 0x52, 0x01, 0x61, 0x3a, 0xba, 0x01, 0x0a, 0x1c, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f,
	0x61, 0x62, 0x73, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x18, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x02, 0x42, 0x5f, 0xc2, 0x48, 0x5c, 0x0a, 0x5a, 0x0a, 0x1c,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x2e, 0x61, 0x62, 0x73, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1b, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6f, 0x75, 0x74,
	0x20, 0x6f, 0x66, 0x20, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x1a, 0x1d, 0x74, 0x68, 0x69, 0x73, 0x20,
	0x3e, 0x3d, 0x20, 0x2d, 0x72, 0x75, 0x6c, 0x65, 0x20, 0x26, 0x26, 0x20, 0x74, 0x68, 0x69, 0x73,
	0x20, 0x3c, 0x3d, 0x20, 0x72, 0x75, 0x6c, 0x65, 0x52, 0x18, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x41,
	0x62, 0x73, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30,
	0x32, 0x33, 0x3a, 0xbf, 0x01, 0x0a, 0x1d, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x62,
	0x73, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x32, 0x30, 0x32, 0x33, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x8a, 0x09, 0x20, 0x01, 0x28, 0x01, 0x42, 0x61, 0xc2, 0x48, 0x5e, 0x0a, 0x5c, 0x0a, 0x1d, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x62, 0x73, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1c, 0x64, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6f, 0x75,
	0x74, 0x20, 0x6f, 0x66, 0x20, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x1a, 0x1d, 0x74, 0x68, 0x69, 0x73,
	0x20, 0x3e, 0x3d, 0x20, 0x2d, 0x72, 0x75, 0x6c, 0x65, 0x20, 0x26, 0x26, 0x20, 0x74, 0x68, 0x69,
	0x73, 0x20, 0x3c, 0x3d, 0x20, 0x72, 0x75, 0x6c, 0x65, 0x52, 0x19, 0x64, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x41, 0x62, 0x73, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x30, 0x32, 0x33, 0x3a, 0xc7, 0x01, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x61,
	0x62, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30,
	0x32, 0x33, 0x12, 0x18, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20,
	0x03, 0x28, 0x05, 0x42, 0x72, 0xc2, 0x48, 0x6f, 0x0a, 0x6d, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x2e, 0x61, 0x62, 0x73, 0x5f, 0x69, 0x6e, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x27, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6d, 0x75, 0x73,
	0x74, 0x20, 0x62, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x61, 0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65,
	0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6f, 0x66, 0x20, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x27,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x72, 0x75, 0x6c, 0x65, 0x20, 0x7c, 0x7c, 0x20,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x28, 0x6e, 0x2c, 0x20, 0x2d, 0x6e, 0x29, 0x52, 0x15, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x41, 0x62,
	0x73, 0x49, 0x6e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0xe4,
	0x01, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x61, 0x62, 0x73, 0x5f, 0x69, 0x6e, 0x5f,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x18, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x72, 0xc2, 0x48, 0x6f, 0x0a,
	0x6d, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x2e, 0x61, 0x62, 0x73, 0x5f, 0x69, 0x6e, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x27, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x61,
	0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6f, 0x66,
	0x20, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x27, 0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x72,
	0x75, 0x6c, 0x65, 0x20, 0x7c, 0x7c, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x72,
	0x75, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x28, 0x6e, 0x2c, 0x20, 0x2d, 0x6e, 0x29, 0x52, 0x15,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x41, 0x62, 0x73, 0x49, 0x6e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0x9f, 0x01, 0x0a, 0x18, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30,
	0x32, 0x33, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x4a, 0xc2, 0x48, 0x47, 0x0a, 0x45, 0x0a, 0x18, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x18, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x20, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a,
	0x0f, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x75, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x75,
	0x52, 0x15, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x76, 0x65, 0x6e, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0x9f, 0x01, 0x0a, 0x18, 0x75, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x32, 0x30, 0x32, 0x33, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x4a, 0xc2, 0x48, 0x47, 0x0a, 0x45, 0x0a, 0x18, 0x75,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x18, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x20,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65,
	0x6e, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x75, 0x20, 0x3d, 0x3d, 0x20,
	0x30, 0x75, 0x52, 0x15, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x76, 0x65, 0x6e, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0x9d, 0x01, 0x0a, 0x18, 0x73, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x48, 0xc2, 0x48, 0x45, 0x0a, 0x43, 0x0a,
	0x18, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x18, 0x73, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65,
	0x76, 0x65, 0x6e, 0x1a, 0x0d, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x20, 0x3d, 0x3d,
	0x20, 0x30, 0x52, 0x15, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x76, 0x65, 0x6e, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0x9d, 0x01, 0x0a, 0x18, 0x73, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x48, 0xc2, 0x48, 0x45, 0x0a, 0x43, 0x0a,
	0x18, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x18, 0x73, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65,
	0x76, 0x65, 0x6e, 0x1a, 0x0d, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x20, 0x3d, 0x3d,
	0x20, 0x30, 0x52, 0x15, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x76, 0x65, 0x6e, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0xa4, 0x01, 0x0a, 0x19, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x4c, 0xc2, 0x48, 0x49, 0x0a,
	0x47, 0x0a, 0x19, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x19, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e,
	0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20,
	0x32, 0x75, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x75, 0x52, 0x16, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x45, 0x76, 0x65, 0x6e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33,
	0x3a, 0xa4, 0x01, 0x0a, 0x19, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x4c, 0xc2, 0x48, 0x49, 0x0a, 0x47, 0x0a, 0x19, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32,
	0x30, 0x32, 0x33, 0x12, 0x19, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x20, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a, 0x0f,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x75, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x75, 0x52,
	0x16, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x76, 0x65, 0x6e, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0xa7, 0x01, 0x0a, 0x1a, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1b, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x4c, 0xc2, 0x48, 0x49, 0x0a,
	0x47, 0x0a, 0x1a, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x2e, 0x65, 0x76, 0x65, 0x6e,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73,
	0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a, 0x0d, 0x74, 0x68, 0x69, 0x73, 0x20,
	0x25, 0x20, 0x32, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x52, 0x17, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x45, 0x76, 0x65, 0x6e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32,
	0x33, 0x3a, 0xa7, 0x01, 0x0a, 0x1a, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x65,
	0x76, 0x65, 0x6e, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33,
	0x12, 0x1b, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x4c, 0xc2, 0x48, 0x49, 0x0a, 0x47, 0x0a, 0x1a, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1a, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65,
	0x76, 0x65, 0x6e, 0x1a, 0x0d, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x20, 0x3d, 0x3d,
	0x20, 0x30, 0x52, 0x17, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x76, 0x65, 0x6e,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0x97, 0x01, 0x0a, 0x17,
	0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x17, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x46, 0xc2, 0x48, 0x43, 0x0a, 0x41, 0x0a, 0x17,
	0x62, 0x6f, 0x6f, 0x6c, 0x2e, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x17, 0x62, 0x6f, 0x6f, 0x6c, 0x20, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x66, 0x61, 0x6c, 0x73, 0x65,
	0x1a, 0x0d, 0x74, 0x68, 0x69, 0x73, 0x20, 0x3d, 0x3d, 0x20, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x14, 0x62, 0x6f, 0x6f, 0x6c, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0x8f, 0x02, 0x0a, 0x1e, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0xae, 0x01, 0xc2, 0x48, 0xaa,
	0x01, 0x0a, 0xa7, 0x01, 0x0a, 0x1e, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x32, 0x30, 0x32, 0x33, 0x1a, 0x84, 0x01, 0x21, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x5b, 0x5e, 0x2f,
	0x5d, 0x3f, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x7c, 0x5b, 0x5e, 0x2f,
	0x5d, 0x7b, 0x33, 0x2c, 0x7d, 0x29, 0x28, 0x2f, 0x28, 0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x5b, 0x5e,
	0x2f, 0x5d, 0x3f, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x7c, 0x5b, 0x5e,
	0x2f, 0x5d, 0x7b, 0x33, 0x2c, 0x7d, 0x29, 0x29, 0x2a, 0x24, 0x27, 0x29, 0x20, 0x3f, 0x20, 0x27,
	0x6e, 0x6f, 0x74, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x70, 0x61, 0x74, 0x68,
	0x3a, 0x20, 0x60, 0x25, 0x73, 0x60, 0x27, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x28, 0x5b,
	0x74, 0x68, 0x69, 0x73, 0x5d, 0x29, 0x20, 0x3a, 0x20, 0x27, 0x27, 0x52, 0x1a, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x61, 0x74, 0x68, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0x93, 0x02, 0x0a, 0x1d, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x18, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0xb5, 0x01, 0xc2, 0x48, 0xb1,
	0x01, 0x0a, 0xae, 0x01, 0x0a, 0x1d, 0x62, 0x79, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32,
	0x30, 0x32, 0x33, 0x1a, 0x8c, 0x01, 0x21, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x28, 0x74, 0x68,
	0x69, 0x73, 0x29, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x5b,
	0x5e, 0x2f, 0x2e, 0x5d, 0x5b, 0x5e, 0x2f, 0x5d, 0x3f, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x5b, 0x5e,
	0x2f, 0x2e, 0x5d, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x7b, 0x33, 0x2c, 0x7d, 0x29, 0x28, 0x2f, 0x28,
	0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x5b, 0x5e, 0x2f, 0x5d, 0x3f, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x5b,
	0x5e, 0x2f, 0x2e, 0x5d, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x7b, 0x33, 0x2c, 0x7d, 0x29, 0x29, 0x2a,
	0x24, 0x27, 0x29, 0x20, 0x3f, 0x20, 0x27, 0x6e, 0x6f, 0x74, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x20, 0x70, 0x61, 0x74, 0x68, 0x3a, 0x20, 0x60, 0x25, 0x73, 0x60, 0x27, 0x2e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x28, 0x5b, 0x74, 0x68, 0x69, 0x73, 0x5d, 0x29, 0x20, 0x3a, 0x20,
	0x27, 0x27, 0x52, 0x19, 0x62, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x61,
	0x74, 0x68, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x3a, 0xa3, 0x01,
	0x0a, 0x1a, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x6e, 0x6f, 0x6e, 0x5f, 0x7a, 0x65, 0x72, 0x6f, 0x5f,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x17, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x4d, 0xc2, 0x48,
	0x4a, 0x0a, 0x48, 0x0a, 0x1a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x6e, 0x6f, 0x6e, 0x5f, 0x7a, 0x65,
	0x72, 0x6f, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12,
	0x1a, 0x65, 0x6e, 0x75, 0x6d, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e,
	0x6f, 0x74, 0x20, 0x6e, 0x6f, 0x6e, 0x2d, 0x7a, 0x65, 0x72, 0x6f, 0x1a, 0x0e, 0x69, 0x6e, 0x74,
	0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x21, 0x3d, 0x20, 0x30, 0x52, 0x16, 0x65, 0x6e, 0x75,
	0x6d, 0x4e, 0x6f, 0x6e, 0x5a, 0x65, 0x72, 0x6f, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x3a, 0xdd, 0x01, 0x0a, 0x23, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x5f, 0x6c, 0x65, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1b, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x72, 0xc2, 0x48, 0x6f, 0x0a, 0x6d, 0x0a, 0x23, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x2e, 0x61, 0x74, 0x5f, 0x6c, 0x65, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x2d, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x20, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x20, 0x6d, 0x75, 0x73, 0x74,
	0x20, 0x68, 0x61, 0x76, 0x65, 0x20, 0x61, 0x74, 0x20, 0x6c, 0x65, 0x61, 0x73, 0x74, 0x20, 0x66,
	0x69, 0x76, 0x65, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x17, 0x75, 0x69, 0x6e, 0x74,
	0x28, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x73, 0x69, 0x7a, 0x65, 0x28, 0x29, 0x29, 0x20, 0x3e, 0x3d,
	0x20, 0x35, 0x75, 0x52, 0x1e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x4c,
	0x65, 0x61, 0x73, 0x74, 0x46, 0x69, 0x76, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x3a, 0xbd, 0x01, 0x0a, 0x1e, 0x6d, 0x61, 0x70, 0x5f, 0x61, 0x74, 0x5f, 0x6c,
	0x65, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x16, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4d, 0x61, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x8a,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x61, 0xc2, 0x48, 0x5e, 0x0a, 0x5c, 0x0a, 0x1e, 0x6d, 0x61,
	0x70, 0x2e, 0x61, 0x74, 0x5f, 0x6c, 0x65, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x21, 0x6d, 0x61,
	0x70, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x68, 0x61, 0x76, 0x65, 0x20, 0x61, 0x74, 0x20, 0x6c,
	0x65, 0x61, 0x73, 0x74, 0x20, 0x66, 0x69, 0x76, 0x65, 0x20, 0x70, 0x61, 0x69, 0x72, 0x73, 0x1a,
	0x17, 0x75, 0x69, 0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x73, 0x69, 0x7a, 0x65, 0x28,
	0x29, 0x29, 0x20, 0x3e, 0x3d, 0x20, 0x35, 0x75, 0x52, 0x19, 0x6d, 0x61, 0x70, 0x41, 0x74, 0x4c,
	0x65, 0x61, 0x73, 0x74, 0x46, 0x69, 0x76, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x3a, 0xca, 0x01, 0x0a, 0x1e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x6f, 0x6f, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x12, 0x1b, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x68, 0xc2, 0x48, 0x65, 0x0a,
	0x63, 0x0a, 0x1e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x6f, 0x6f, 0x5f,
	0x6c, 0x6f, 0x6e, 0x67, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32, 0x30, 0x32,
	0x33, 0x12, 0x28, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x61, 0x6e, 0x27,
	0x74, 0x20, 0x62, 0x65, 0x20, 0x6c, 0x6f, 0x6e, 0x67, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x6e,
	0x20, 0x31, 0x30, 0x20, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x1a, 0x17, 0x74, 0x68, 0x69,
	0x73, 0x20, 0x3c, 0x3d, 0x20, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x28, 0x27, 0x31,
	0x30, 0x73, 0x27, 0x29, 0x52, 0x1a, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f,
	0x6f, 0x4c, 0x6f, 0x6e, 0x67, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33,
	0x3a, 0xd9, 0x01, 0x0a, 0x1f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x69,
	0x6e, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x32, 0x30, 0x32, 0x33, 0x12, 0x1c, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x8a, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x74, 0xc2, 0x48, 0x71, 0x0a, 0x6f,
	0x0a, 0x21, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x32,
	0x30, 0x32, 0x33, 0x12, 0x16, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x6f,
	0x75, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x1a, 0x32, 0x69, 0x6e, 0x74,
	0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x3e, 0x3d, 0x20, 0x31, 0x30, 0x34, 0x39, 0x35, 0x38,
	0x37, 0x32, 0x30, 0x30, 0x20, 0x26, 0x26, 0x20, 0x69, 0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73,
	0x29, 0x20, 0x3c, 0x3d, 0x20, 0x31, 0x30, 0x38, 0x30, 0x34, 0x33, 0x32, 0x30, 0x30, 0x30, 0x52,
	0x1b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x49, 0x6e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x42, 0xb8, 0x02, 0x0a,
	0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x42, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c,
	0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62,
	0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02, 0x04,
	0x42, 0x56, 0x43, 0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65, 0x73, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x70, 0xe8, 0x07,
}

var (
	file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescData = file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDesc
)

func file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDescData
}

var file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes = make([]protoimpl.MessageInfo, 42)
var file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_goTypes = []any{
	(PredefinedEnumRuleEdition2023_EnumEdition2023)(0),     // 0: buf.validate.conformance.cases.PredefinedEnumRuleEdition2023.EnumEdition2023
	(*PredefinedFloatRuleEdition2023)(nil),                 // 1: buf.validate.conformance.cases.PredefinedFloatRuleEdition2023
	(*PredefinedDoubleRuleEdition2023)(nil),                // 2: buf.validate.conformance.cases.PredefinedDoubleRuleEdition2023
	(*PredefinedInt32RuleEdition2023)(nil),                 // 3: buf.validate.conformance.cases.PredefinedInt32RuleEdition2023
	(*PredefinedInt64RuleEdition2023)(nil),                 // 4: buf.validate.conformance.cases.PredefinedInt64RuleEdition2023
	(*PredefinedUInt32RuleEdition2023)(nil),                // 5: buf.validate.conformance.cases.PredefinedUInt32RuleEdition2023
	(*PredefinedUInt64RuleEdition2023)(nil),                // 6: buf.validate.conformance.cases.PredefinedUInt64RuleEdition2023
	(*PredefinedSInt32RuleEdition2023)(nil),                // 7: buf.validate.conformance.cases.PredefinedSInt32RuleEdition2023
	(*PredefinedSInt64RuleEdition2023)(nil),                // 8: buf.validate.conformance.cases.PredefinedSInt64RuleEdition2023
	(*PredefinedFixed32RuleEdition2023)(nil),               // 9: buf.validate.conformance.cases.PredefinedFixed32RuleEdition2023
	(*PredefinedFixed64RuleEdition2023)(nil),               // 10: buf.validate.conformance.cases.PredefinedFixed64RuleEdition2023
	(*PredefinedSFixed32RuleEdition2023)(nil),              // 11: buf.validate.conformance.cases.PredefinedSFixed32RuleEdition2023
	(*PredefinedSFixed64RuleEdition2023)(nil),              // 12: buf.validate.conformance.cases.PredefinedSFixed64RuleEdition2023
	(*PredefinedBoolRuleEdition2023)(nil),                  // 13: buf.validate.conformance.cases.PredefinedBoolRuleEdition2023
	(*PredefinedStringRuleEdition2023)(nil),                // 14: buf.validate.conformance.cases.PredefinedStringRuleEdition2023
	(*PredefinedBytesRuleEdition2023)(nil),                 // 15: buf.validate.conformance.cases.PredefinedBytesRuleEdition2023
	(*PredefinedEnumRuleEdition2023)(nil),                  // 16: buf.validate.conformance.cases.PredefinedEnumRuleEdition2023
	(*PredefinedRepeatedRuleEdition2023)(nil),              // 17: buf.validate.conformance.cases.PredefinedRepeatedRuleEdition2023
	(*PredefinedMapRuleEdition2023)(nil),                   // 18: buf.validate.conformance.cases.PredefinedMapRuleEdition2023
	(*PredefinedDurationRuleEdition2023)(nil),              // 19: buf.validate.conformance.cases.PredefinedDurationRuleEdition2023
	(*PredefinedTimestampRuleEdition2023)(nil),             // 20: buf.validate.conformance.cases.PredefinedTimestampRuleEdition2023
	(*PredefinedWrappedFloatRuleEdition2023)(nil),          // 21: buf.validate.conformance.cases.PredefinedWrappedFloatRuleEdition2023
	(*PredefinedWrappedDoubleRuleEdition2023)(nil),         // 22: buf.validate.conformance.cases.PredefinedWrappedDoubleRuleEdition2023
	(*PredefinedWrappedInt32RuleEdition2023)(nil),          // 23: buf.validate.conformance.cases.PredefinedWrappedInt32RuleEdition2023
	(*PredefinedWrappedInt64RuleEdition2023)(nil),          // 24: buf.validate.conformance.cases.PredefinedWrappedInt64RuleEdition2023
	(*PredefinedWrappedUInt32RuleEdition2023)(nil),         // 25: buf.validate.conformance.cases.PredefinedWrappedUInt32RuleEdition2023
	(*PredefinedWrappedUInt64RuleEdition2023)(nil),         // 26: buf.validate.conformance.cases.PredefinedWrappedUInt64RuleEdition2023
	(*PredefinedWrappedBoolRuleEdition2023)(nil),           // 27: buf.validate.conformance.cases.PredefinedWrappedBoolRuleEdition2023
	(*PredefinedWrappedStringRuleEdition2023)(nil),         // 28: buf.validate.conformance.cases.PredefinedWrappedStringRuleEdition2023
	(*PredefinedWrappedBytesRuleEdition2023)(nil),          // 29: buf.validate.conformance.cases.PredefinedWrappedBytesRuleEdition2023
	(*PredefinedRepeatedWrappedFloatRuleEdition2023)(nil),  // 30: buf.validate.conformance.cases.PredefinedRepeatedWrappedFloatRuleEdition2023
	(*PredefinedRepeatedWrappedDoubleRuleEdition2023)(nil), // 31: buf.validate.conformance.cases.PredefinedRepeatedWrappedDoubleRuleEdition2023
	(*PredefinedRepeatedWrappedInt32RuleEdition2023)(nil),  // 32: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt32RuleEdition2023
	(*PredefinedRepeatedWrappedInt64RuleEdition2023)(nil),  // 33: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt64RuleEdition2023
	(*PredefinedRepeatedWrappedUInt32RuleEdition2023)(nil), // 34: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt32RuleEdition2023
	(*PredefinedRepeatedWrappedUInt64RuleEdition2023)(nil), // 35: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt64RuleEdition2023
	(*PredefinedRepeatedWrappedBoolRuleEdition2023)(nil),   // 36: buf.validate.conformance.cases.PredefinedRepeatedWrappedBoolRuleEdition2023
	(*PredefinedRepeatedWrappedStringRuleEdition2023)(nil), // 37: buf.validate.conformance.cases.PredefinedRepeatedWrappedStringRuleEdition2023
	(*PredefinedRepeatedWrappedBytesRuleEdition2023)(nil),  // 38: buf.validate.conformance.cases.PredefinedRepeatedWrappedBytesRuleEdition2023
	(*PredefinedAndCustomRuleEdition2023)(nil),             // 39: buf.validate.conformance.cases.PredefinedAndCustomRuleEdition2023
	(*StandardPredefinedAndCustomRuleEdition2023)(nil),     // 40: buf.validate.conformance.cases.StandardPredefinedAndCustomRuleEdition2023
	nil, // 41: buf.validate.conformance.cases.PredefinedMapRuleEdition2023.ValEntry
	(*PredefinedAndCustomRuleEdition2023_Nested)(nil), // 42: buf.validate.conformance.cases.PredefinedAndCustomRuleEdition2023.Nested
	(*durationpb.Duration)(nil),                       // 43: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),                     // 44: google.protobuf.Timestamp
	(*wrapperspb.FloatValue)(nil),                     // 45: google.protobuf.FloatValue
	(*wrapperspb.DoubleValue)(nil),                    // 46: google.protobuf.DoubleValue
	(*wrapperspb.Int32Value)(nil),                     // 47: google.protobuf.Int32Value
	(*wrapperspb.Int64Value)(nil),                     // 48: google.protobuf.Int64Value
	(*wrapperspb.UInt32Value)(nil),                    // 49: google.protobuf.UInt32Value
	(*wrapperspb.UInt64Value)(nil),                    // 50: google.protobuf.UInt64Value
	(*wrapperspb.BoolValue)(nil),                      // 51: google.protobuf.BoolValue
	(*wrapperspb.StringValue)(nil),                    // 52: google.protobuf.StringValue
	(*wrapperspb.BytesValue)(nil),                     // 53: google.protobuf.BytesValue
	(*validate.FloatRules)(nil),                       // 54: buf.validate.FloatRules
	(*validate.DoubleRules)(nil),                      // 55: buf.validate.DoubleRules
	(*validate.Int32Rules)(nil),                       // 56: buf.validate.Int32Rules
	(*validate.Int64Rules)(nil),                       // 57: buf.validate.Int64Rules
	(*validate.UInt32Rules)(nil),                      // 58: buf.validate.UInt32Rules
	(*validate.UInt64Rules)(nil),                      // 59: buf.validate.UInt64Rules
	(*validate.SInt32Rules)(nil),                      // 60: buf.validate.SInt32Rules
	(*validate.SInt64Rules)(nil),                      // 61: buf.validate.SInt64Rules
	(*validate.Fixed32Rules)(nil),                     // 62: buf.validate.Fixed32Rules
	(*validate.Fixed64Rules)(nil),                     // 63: buf.validate.Fixed64Rules
	(*validate.SFixed32Rules)(nil),                    // 64: buf.validate.SFixed32Rules
	(*validate.SFixed64Rules)(nil),                    // 65: buf.validate.SFixed64Rules
	(*validate.BoolRules)(nil),                        // 66: buf.validate.BoolRules
	(*validate.StringRules)(nil),                      // 67: buf.validate.StringRules
	(*validate.BytesRules)(nil),                       // 68: buf.validate.BytesRules
	(*validate.EnumRules)(nil),                        // 69: buf.validate.EnumRules
	(*validate.RepeatedRules)(nil),                    // 70: buf.validate.RepeatedRules
	(*validate.MapRules)(nil),                         // 71: buf.validate.MapRules
	(*validate.DurationRules)(nil),                    // 72: buf.validate.DurationRules
	(*validate.TimestampRules)(nil),                   // 73: buf.validate.TimestampRules
}
var file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_depIdxs = []int32{
	0,  // 0: buf.validate.conformance.cases.PredefinedEnumRuleEdition2023.val:type_name -> buf.validate.conformance.cases.PredefinedEnumRuleEdition2023.EnumEdition2023
	41, // 1: buf.validate.conformance.cases.PredefinedMapRuleEdition2023.val:type_name -> buf.validate.conformance.cases.PredefinedMapRuleEdition2023.ValEntry
	43, // 2: buf.validate.conformance.cases.PredefinedDurationRuleEdition2023.val:type_name -> google.protobuf.Duration
	44, // 3: buf.validate.conformance.cases.PredefinedTimestampRuleEdition2023.val:type_name -> google.protobuf.Timestamp
	45, // 4: buf.validate.conformance.cases.PredefinedWrappedFloatRuleEdition2023.val:type_name -> google.protobuf.FloatValue
	46, // 5: buf.validate.conformance.cases.PredefinedWrappedDoubleRuleEdition2023.val:type_name -> google.protobuf.DoubleValue
	47, // 6: buf.validate.conformance.cases.PredefinedWrappedInt32RuleEdition2023.val:type_name -> google.protobuf.Int32Value
	48, // 7: buf.validate.conformance.cases.PredefinedWrappedInt64RuleEdition2023.val:type_name -> google.protobuf.Int64Value
	49, // 8: buf.validate.conformance.cases.PredefinedWrappedUInt32RuleEdition2023.val:type_name -> google.protobuf.UInt32Value
	50, // 9: buf.validate.conformance.cases.PredefinedWrappedUInt64RuleEdition2023.val:type_name -> google.protobuf.UInt64Value
	51, // 10: buf.validate.conformance.cases.PredefinedWrappedBoolRuleEdition2023.val:type_name -> google.protobuf.BoolValue
	52, // 11: buf.validate.conformance.cases.PredefinedWrappedStringRuleEdition2023.val:type_name -> google.protobuf.StringValue
	53, // 12: buf.validate.conformance.cases.PredefinedWrappedBytesRuleEdition2023.val:type_name -> google.protobuf.BytesValue
	45, // 13: buf.validate.conformance.cases.PredefinedRepeatedWrappedFloatRuleEdition2023.val:type_name -> google.protobuf.FloatValue
	46, // 14: buf.validate.conformance.cases.PredefinedRepeatedWrappedDoubleRuleEdition2023.val:type_name -> google.protobuf.DoubleValue
	47, // 15: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt32RuleEdition2023.val:type_name -> google.protobuf.Int32Value
	48, // 16: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt64RuleEdition2023.val:type_name -> google.protobuf.Int64Value
	49, // 17: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt32RuleEdition2023.val:type_name -> google.protobuf.UInt32Value
	50, // 18: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt64RuleEdition2023.val:type_name -> google.protobuf.UInt64Value
	51, // 19: buf.validate.conformance.cases.PredefinedRepeatedWrappedBoolRuleEdition2023.val:type_name -> google.protobuf.BoolValue
	52, // 20: buf.validate.conformance.cases.PredefinedRepeatedWrappedStringRuleEdition2023.val:type_name -> google.protobuf.StringValue
	53, // 21: buf.validate.conformance.cases.PredefinedRepeatedWrappedBytesRuleEdition2023.val:type_name -> google.protobuf.BytesValue
	42, // 22: buf.validate.conformance.cases.PredefinedAndCustomRuleEdition2023.b:type_name -> buf.validate.conformance.cases.PredefinedAndCustomRuleEdition2023.Nested
	54, // 23: buf.validate.conformance.cases.float_abs_range_edition_2023:extendee -> buf.validate.FloatRules
	55, // 24: buf.validate.conformance.cases.double_abs_range_edition_2023:extendee -> buf.validate.DoubleRules
	56, // 25: buf.validate.conformance.cases.int32_abs_in_edition_2023:extendee -> buf.validate.Int32Rules
	57, // 26: buf.validate.conformance.cases.int64_abs_in_edition_2023:extendee -> buf.validate.Int64Rules
	58, // 27: buf.validate.conformance.cases.uint32_even_edition_2023:extendee -> buf.validate.UInt32Rules
	59, // 28: buf.validate.conformance.cases.uint64_even_edition_2023:extendee -> buf.validate.UInt64Rules
	60, // 29: buf.validate.conformance.cases.sint32_even_edition_2023:extendee -> buf.validate.SInt32Rules
	61, // 30: buf.validate.conformance.cases.sint64_even_edition_2023:extendee -> buf.validate.SInt64Rules
	62, // 31: buf.validate.conformance.cases.fixed32_even_edition_2023:extendee -> buf.validate.Fixed32Rules
	63, // 32: buf.validate.conformance.cases.fixed64_even_edition_2023:extendee -> buf.validate.Fixed64Rules
	64, // 33: buf.validate.conformance.cases.sfixed32_even_edition_2023:extendee -> buf.validate.SFixed32Rules
	65, // 34: buf.validate.conformance.cases.sfixed64_even_edition_2023:extendee -> buf.validate.SFixed64Rules
	66, // 35: buf.validate.conformance.cases.bool_false_edition_2023:extendee -> buf.validate.BoolRules
	67, // 36: buf.validate.conformance.cases.string_valid_path_edition_2023:extendee -> buf.validate.StringRules
	68, // 37: buf.validate.conformance.cases.bytes_valid_path_edition_2023:extendee -> buf.validate.BytesRules
	69, // 38: buf.validate.conformance.cases.enum_non_zero_edition_2023:extendee -> buf.validate.EnumRules
	70, // 39: buf.validate.conformance.cases.repeated_at_least_five_edition_2023:extendee -> buf.validate.RepeatedRules
	71, // 40: buf.validate.conformance.cases.map_at_least_five_edition_2023:extendee -> buf.validate.MapRules
	72, // 41: buf.validate.conformance.cases.duration_too_long_edition_2023:extendee -> buf.validate.DurationRules
	73, // 42: buf.validate.conformance.cases.timestamp_in_range_edition_2023:extendee -> buf.validate.TimestampRules
	48, // 43: buf.validate.conformance.cases.int64_abs_in_edition_2023:type_name -> google.protobuf.Int64Value
	44, // [44:44] is the sub-list for method output_type
	44, // [44:44] is the sub-list for method input_type
	43, // [43:44] is the sub-list for extension type_name
	23, // [23:43] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_init() }
func file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_init() {
	if File_buf_validate_conformance_cases_predefined_rules_proto_editions_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFloatRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedDoubleRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedInt32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedInt64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedUInt32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedUInt64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSInt32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSInt64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFixed32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFixed64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSFixed32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSFixed64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedBoolRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedStringRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedBytesRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedEnumRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedMapRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedDurationRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedTimestampRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedFloatRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedDoubleRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedInt32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedInt64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedUInt32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedUInt64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedBoolRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedStringRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedBytesRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedFloatRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedDoubleRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedInt32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedInt64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedUInt32RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedUInt64RuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedBoolRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedStringRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedBytesRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedAndCustomRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*StandardPredefinedAndCustomRuleEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedAndCustomRuleEdition2023_Nested); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   42,
			NumExtensions: 20,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_depIdxs,
		EnumInfos:         file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_enumTypes,
		MessageInfos:      file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_msgTypes,
		ExtensionInfos:    file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_extTypes,
	}.Build()
	File_buf_validate_conformance_cases_predefined_rules_proto_editions_proto = out.File
	file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_rawDesc = nil
	file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_goTypes = nil
	file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_depIdxs = nil
}
