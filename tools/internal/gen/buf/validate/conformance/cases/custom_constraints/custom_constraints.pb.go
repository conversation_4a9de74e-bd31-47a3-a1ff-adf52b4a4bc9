// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/custom_constraints/custom_constraints.proto

package custom_constraints

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Enum int32

const (
	Enum_ENUM_UNSPECIFIED Enum = 0
	Enum_ENUM_ONE         Enum = 1
)

// Enum value maps for Enum.
var (
	Enum_name = map[int32]string{
		0: "ENUM_UNSPECIFIED",
		1: "ENUM_ONE",
	}
	Enum_value = map[string]int32{
		"ENUM_UNSPECIFIED": 0,
		"ENUM_ONE":         1,
	}
)

func (x Enum) Enum() *Enum {
	p := new(Enum)
	*p = x
	return p
}

func (x Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_enumTypes[0].Descriptor()
}

func (Enum) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_enumTypes[0]
}

func (x Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Enum.Descriptor instead.
func (Enum) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{0}
}

// A message that does not contain any expressions
type NoExpressions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32                 `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
	B Enum                  `protobuf:"varint,2,opt,name=b,proto3,enum=buf.validate.conformance.cases.custom_constraints.Enum" json:"b,omitempty"`
	C *NoExpressions_Nested `protobuf:"bytes,3,opt,name=c,proto3" json:"c,omitempty"`
}

func (x *NoExpressions) Reset() {
	*x = NoExpressions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoExpressions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoExpressions) ProtoMessage() {}

func (x *NoExpressions) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoExpressions.ProtoReflect.Descriptor instead.
func (*NoExpressions) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{0}
}

func (x *NoExpressions) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

func (x *NoExpressions) GetB() Enum {
	if x != nil {
		return x.B
	}
	return Enum_ENUM_UNSPECIFIED
}

func (x *NoExpressions) GetC() *NoExpressions_Nested {
	if x != nil {
		return x.C
	}
	return nil
}

// A message with message-level custom expressions
type MessageExpressions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32                      `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
	B int32                      `protobuf:"varint,2,opt,name=b,proto3" json:"b,omitempty"`
	C Enum                       `protobuf:"varint,3,opt,name=c,proto3,enum=buf.validate.conformance.cases.custom_constraints.Enum" json:"c,omitempty"`
	D Enum                       `protobuf:"varint,4,opt,name=d,proto3,enum=buf.validate.conformance.cases.custom_constraints.Enum" json:"d,omitempty"`
	E *MessageExpressions_Nested `protobuf:"bytes,5,opt,name=e,proto3" json:"e,omitempty"`
	F *MessageExpressions_Nested `protobuf:"bytes,6,opt,name=f,proto3" json:"f,omitempty"`
}

func (x *MessageExpressions) Reset() {
	*x = MessageExpressions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageExpressions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageExpressions) ProtoMessage() {}

func (x *MessageExpressions) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageExpressions.ProtoReflect.Descriptor instead.
func (*MessageExpressions) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{1}
}

func (x *MessageExpressions) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

func (x *MessageExpressions) GetB() int32 {
	if x != nil {
		return x.B
	}
	return 0
}

func (x *MessageExpressions) GetC() Enum {
	if x != nil {
		return x.C
	}
	return Enum_ENUM_UNSPECIFIED
}

func (x *MessageExpressions) GetD() Enum {
	if x != nil {
		return x.D
	}
	return Enum_ENUM_UNSPECIFIED
}

func (x *MessageExpressions) GetE() *MessageExpressions_Nested {
	if x != nil {
		return x.E
	}
	return nil
}

func (x *MessageExpressions) GetF() *MessageExpressions_Nested {
	if x != nil {
		return x.F
	}
	return nil
}

type FieldExpressions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32                    `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
	B Enum                     `protobuf:"varint,2,opt,name=b,proto3,enum=buf.validate.conformance.cases.custom_constraints.Enum" json:"b,omitempty"`
	C *FieldExpressions_Nested `protobuf:"bytes,3,opt,name=c,proto3" json:"c,omitempty"`
	D int32                    `protobuf:"varint,4,opt,name=d,proto3" json:"d,omitempty"`
}

func (x *FieldExpressions) Reset() {
	*x = FieldExpressions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldExpressions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldExpressions) ProtoMessage() {}

func (x *FieldExpressions) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldExpressions.ProtoReflect.Descriptor instead.
func (*FieldExpressions) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{2}
}

func (x *FieldExpressions) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

func (x *FieldExpressions) GetB() Enum {
	if x != nil {
		return x.B
	}
	return Enum_ENUM_UNSPECIFIED
}

func (x *FieldExpressions) GetC() *FieldExpressions_Nested {
	if x != nil {
		return x.C
	}
	return nil
}

func (x *FieldExpressions) GetD() int32 {
	if x != nil {
		return x.D
	}
	return 0
}

type MissingField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32 `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
}

func (x *MissingField) Reset() {
	*x = MissingField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MissingField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MissingField) ProtoMessage() {}

func (x *MissingField) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MissingField.ProtoReflect.Descriptor instead.
func (*MissingField) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{3}
}

func (x *MissingField) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

type IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32 `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
}

func (x *IncorrectType) Reset() {
	*x = IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncorrectType) ProtoMessage() {}

func (x *IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncorrectType.ProtoReflect.Descriptor instead.
func (*IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{4}
}

func (x *IncorrectType) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

type DynRuntimeError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32 `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
}

func (x *DynRuntimeError) Reset() {
	*x = DynRuntimeError{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynRuntimeError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynRuntimeError) ProtoMessage() {}

func (x *DynRuntimeError) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynRuntimeError.ProtoReflect.Descriptor instead.
func (*DynRuntimeError) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{5}
}

func (x *DynRuntimeError) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

type NowEqualsNow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NowEqualsNow) Reset() {
	*x = NowEqualsNow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NowEqualsNow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NowEqualsNow) ProtoMessage() {}

func (x *NowEqualsNow) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NowEqualsNow.ProtoReflect.Descriptor instead.
func (*NowEqualsNow) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{6}
}

type NoExpressions_Nested struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NoExpressions_Nested) Reset() {
	*x = NoExpressions_Nested{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoExpressions_Nested) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoExpressions_Nested) ProtoMessage() {}

func (x *NoExpressions_Nested) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoExpressions_Nested.ProtoReflect.Descriptor instead.
func (*NoExpressions_Nested) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{0, 0}
}

type MessageExpressions_Nested struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32 `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
	B int32 `protobuf:"varint,2,opt,name=b,proto3" json:"b,omitempty"`
}

func (x *MessageExpressions_Nested) Reset() {
	*x = MessageExpressions_Nested{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageExpressions_Nested) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageExpressions_Nested) ProtoMessage() {}

func (x *MessageExpressions_Nested) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageExpressions_Nested.ProtoReflect.Descriptor instead.
func (*MessageExpressions_Nested) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{1, 0}
}

func (x *MessageExpressions_Nested) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

func (x *MessageExpressions_Nested) GetB() int32 {
	if x != nil {
		return x.B
	}
	return 0
}

type FieldExpressions_Nested struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32 `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
}

func (x *FieldExpressions_Nested) Reset() {
	*x = FieldExpressions_Nested{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldExpressions_Nested) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldExpressions_Nested) ProtoMessage() {}

func (x *FieldExpressions_Nested) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldExpressions_Nested.ProtoReflect.Descriptor instead.
func (*FieldExpressions_Nested) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP(), []int{2, 0}
}

func (x *FieldExpressions_Nested) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

var File_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDesc = []byte{
	0x0a, 0x4a, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x74, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x31, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x1a,
	0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc5, 0x01, 0x0a,
	0x0d, 0x4e, 0x6f, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x0c,
	0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x45, 0x0a, 0x01,
	0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x01, 0x62, 0x12, 0x55, 0x0a, 0x01, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x74, 0x73, 0x2e, 0x4e, 0x6f, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x01, 0x63, 0x1a, 0x08, 0x0a, 0x06, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x22, 0xc3, 0x05, 0x0a, 0x12, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x0c, 0x0a, 0x01, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x0c, 0x0a, 0x01, 0x62, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x62, 0x12, 0x45, 0x0a, 0x01, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x37, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x01, 0x63, 0x12, 0x45,
	0x0a, 0x01, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x01, 0x64, 0x12, 0x5a, 0x0a, 0x01, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x4c, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x01,
	0x65, 0x12, 0x5a, 0x0a, 0x01, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x01, 0x66, 0x1a, 0x78, 0x0a,
	0x06, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x0c, 0x0a, 0x01, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x62, 0x3a, 0x52, 0xba, 0x48, 0x4f, 0x1a, 0x4d, 0x0a, 0x19, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x1a, 0x30, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x20, 0x3e, 0x20,
	0x74, 0x68, 0x69, 0x73, 0x2e, 0x62, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x3a, 0x20, 0x27, 0x61, 0x20,
	0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x20,
	0x74, 0x68, 0x61, 0x6e, 0x20, 0x62, 0x27, 0x3a, 0xd0, 0x01, 0xba, 0x48, 0xcc, 0x01, 0x1a, 0x43,
	0x0a, 0x19, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x12, 0x15, 0x61, 0x20, 0x6d,
	0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x6c, 0x65, 0x73, 0x73, 0x20, 0x74, 0x68, 0x61, 0x6e,
	0x20, 0x62, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x20, 0x3c, 0x20, 0x74, 0x68, 0x69,
	0x73, 0x2e, 0x62, 0x1a, 0x3f, 0x0a, 0x17, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x12,
	0x63, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x71, 0x75, 0x61, 0x6c,
	0x20, 0x64, 0x1a, 0x10, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x63, 0x20, 0x21, 0x3d, 0x20, 0x74, 0x68,
	0x69, 0x73, 0x2e, 0x64, 0x1a, 0x44, 0x0a, 0x18, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x12, 0x12, 0x65, 0x2e, 0x61, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x65, 0x71, 0x75, 0x61, 0x6c,
	0x20, 0x66, 0x2e, 0x61, 0x1a, 0x14, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x65, 0x2e, 0x61, 0x20, 0x3d,
	0x3d, 0x20, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x66, 0x2e, 0x61, 0x22, 0xaa, 0x05, 0x0a, 0x10, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x5a, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x4c, 0xba, 0x48, 0x49, 0xba,
	0x01, 0x46, 0x0a, 0x17, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x1a, 0x2b, 0x74, 0x68, 0x69,
	0x73, 0x20, 0x3e, 0x20, 0x34, 0x32, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x3a, 0x20, 0x27, 0x61, 0x20,
	0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x20,
	0x74, 0x68, 0x61, 0x6e, 0x20, 0x34, 0x32, 0x27, 0x52, 0x01, 0x61, 0x12, 0x7f, 0x0a, 0x01, 0x62,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42,
	0x38, 0xba, 0x48, 0x35, 0xba, 0x01, 0x32, 0x0a, 0x15, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x65,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x0e,
	0x62, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x7e, 0x4f, 0x4e, 0x45, 0x1a, 0x09,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x3d, 0x3d, 0x20, 0x31, 0x52, 0x01, 0x62, 0x12, 0xa6, 0x01, 0x0a,
	0x01, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x42, 0x4c, 0xba, 0x48, 0x49, 0xba, 0x01, 0x46, 0x0a, 0x16, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65,
	0x6d, 0x62, 0x65, 0x64, 0x12, 0x1b, 0x63, 0x2e, 0x61, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62,
	0x65, 0x20, 0x61, 0x20, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x20, 0x6f, 0x66, 0x20,
	0x34, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x20, 0x25, 0x20, 0x34, 0x20, 0x3d, 0x3d,
	0x20, 0x30, 0x52, 0x01, 0x63, 0x12, 0xb1, 0x01, 0x0a, 0x01, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x42, 0xa2, 0x01, 0xba, 0x48, 0x9e, 0x01, 0xba, 0x01, 0x4c, 0x0a, 0x22, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63,
	0x61, 0x6c, 0x61, 0x72, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x31, 0x1a,
	0x26, 0x74, 0x68, 0x69, 0x73, 0x20, 0x3c, 0x20, 0x31, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x3a, 0x20,
	0x27, 0x64, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x6c, 0x65, 0x73, 0x73, 0x20,
	0x74, 0x68, 0x61, 0x6e, 0x20, 0x31, 0x27, 0xba, 0x01, 0x4c, 0x0a, 0x22, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x61,
	0x6c, 0x61, 0x72, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x32, 0x1a, 0x26,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x3c, 0x20, 0x32, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x3a, 0x20, 0x27,
	0x64, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x6c, 0x65, 0x73, 0x73, 0x20, 0x74,
	0x68, 0x61, 0x6e, 0x20, 0x32, 0x27, 0x52, 0x01, 0x64, 0x1a, 0x5c, 0x0a, 0x06, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x12, 0x52, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x44,
	0xba, 0x48, 0x41, 0xba, 0x01, 0x3e, 0x0a, 0x17, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x65, 0x78,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x1a,
	0x23, 0x74, 0x68, 0x69, 0x73, 0x20, 0x3e, 0x20, 0x30, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x3a, 0x20,
	0x27, 0x61, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x27, 0x52, 0x01, 0x61, 0x22, 0x52, 0x0a, 0x0c, 0x4d, 0x69, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x61, 0x3a, 0x34, 0xba, 0x48, 0x31, 0x1a, 0x2f, 0x0a, 0x0d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x12, 0x62, 0x20, 0x6d,
	0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x1a,
	0x0a, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x62, 0x20, 0x3e, 0x20, 0x30, 0x22, 0x67, 0x0a, 0x0d, 0x49,
	0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x01,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x3a, 0x48, 0xba, 0x48, 0x45, 0x1a,
	0x43, 0x0a, 0x0e, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x17, 0x61, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x73, 0x74, 0x61, 0x72, 0x74, 0x20,
	0x77, 0x69, 0x74, 0x68, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0x1a, 0x18, 0x74, 0x68, 0x69, 0x73,
	0x2e, 0x61, 0x2e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x28, 0x27, 0x66,
	0x6f, 0x6f, 0x27, 0x29, 0x22, 0x7d, 0x0a, 0x0f, 0x44, 0x79, 0x6e, 0x52, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x61, 0x3a, 0x5c, 0xba, 0x48, 0x59, 0x1a, 0x57, 0x0a, 0x0f, 0x64, 0x79,
	0x6e, 0x5f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x12, 0x2e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x20, 0x74, 0x79, 0x70, 0x65, 0x20, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x20, 0x74, 0x6f, 0x20, 0x75, 0x73, 0x65, 0x20, 0x61, 0x20, 0x6e, 0x6f, 0x6e, 0x2d, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x20, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x1a, 0x14, 0x64,
	0x79, 0x6e, 0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x2e, 0x62, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66,
	0x6f, 0x6f, 0x27, 0x22, 0x5c, 0x0a, 0x0c, 0x4e, 0x6f, 0x77, 0x45, 0x71, 0x75, 0x61, 0x6c, 0x73,
	0x4e, 0x6f, 0x77, 0x3a, 0x4c, 0xba, 0x48, 0x49, 0x1a, 0x47, 0x0a, 0x0e, 0x6e, 0x6f, 0x77, 0x5f,
	0x65, 0x71, 0x75, 0x61, 0x6c, 0x73, 0x5f, 0x6e, 0x6f, 0x77, 0x12, 0x29, 0x6e, 0x6f, 0x77, 0x20,
	0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x20, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x20, 0x6e, 0x6f, 0x77,
	0x20, 0x77, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x20, 0x61, 0x6e, 0x20, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x0a, 0x6e, 0x6f, 0x77, 0x20, 0x3d, 0x3d, 0x20, 0x6e, 0x6f,
	0x77, 0x2a, 0x2a, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x4e, 0x55,
	0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x42, 0x9d, 0x03,
	0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x16, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x66, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75,
	0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0xa2, 0x02, 0x05, 0x42, 0x56, 0x43, 0x43,
	0x43, 0xaa, 0x02, 0x30, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x73, 0xca, 0x02, 0x30, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c,
	0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0xe2, 0x02, 0x3c, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x34, 0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65, 0x73, 0x3a, 0x3a, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescData = file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDesc
)

func file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDescData
}

var file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_goTypes = []any{
	(Enum)(0),                         // 0: buf.validate.conformance.cases.custom_constraints.Enum
	(*NoExpressions)(nil),             // 1: buf.validate.conformance.cases.custom_constraints.NoExpressions
	(*MessageExpressions)(nil),        // 2: buf.validate.conformance.cases.custom_constraints.MessageExpressions
	(*FieldExpressions)(nil),          // 3: buf.validate.conformance.cases.custom_constraints.FieldExpressions
	(*MissingField)(nil),              // 4: buf.validate.conformance.cases.custom_constraints.MissingField
	(*IncorrectType)(nil),             // 5: buf.validate.conformance.cases.custom_constraints.IncorrectType
	(*DynRuntimeError)(nil),           // 6: buf.validate.conformance.cases.custom_constraints.DynRuntimeError
	(*NowEqualsNow)(nil),              // 7: buf.validate.conformance.cases.custom_constraints.NowEqualsNow
	(*NoExpressions_Nested)(nil),      // 8: buf.validate.conformance.cases.custom_constraints.NoExpressions.Nested
	(*MessageExpressions_Nested)(nil), // 9: buf.validate.conformance.cases.custom_constraints.MessageExpressions.Nested
	(*FieldExpressions_Nested)(nil),   // 10: buf.validate.conformance.cases.custom_constraints.FieldExpressions.Nested
}
var file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_depIdxs = []int32{
	0,  // 0: buf.validate.conformance.cases.custom_constraints.NoExpressions.b:type_name -> buf.validate.conformance.cases.custom_constraints.Enum
	8,  // 1: buf.validate.conformance.cases.custom_constraints.NoExpressions.c:type_name -> buf.validate.conformance.cases.custom_constraints.NoExpressions.Nested
	0,  // 2: buf.validate.conformance.cases.custom_constraints.MessageExpressions.c:type_name -> buf.validate.conformance.cases.custom_constraints.Enum
	0,  // 3: buf.validate.conformance.cases.custom_constraints.MessageExpressions.d:type_name -> buf.validate.conformance.cases.custom_constraints.Enum
	9,  // 4: buf.validate.conformance.cases.custom_constraints.MessageExpressions.e:type_name -> buf.validate.conformance.cases.custom_constraints.MessageExpressions.Nested
	9,  // 5: buf.validate.conformance.cases.custom_constraints.MessageExpressions.f:type_name -> buf.validate.conformance.cases.custom_constraints.MessageExpressions.Nested
	0,  // 6: buf.validate.conformance.cases.custom_constraints.FieldExpressions.b:type_name -> buf.validate.conformance.cases.custom_constraints.Enum
	10, // 7: buf.validate.conformance.cases.custom_constraints.FieldExpressions.c:type_name -> buf.validate.conformance.cases.custom_constraints.FieldExpressions.Nested
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_init() }
func file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_init() {
	if File_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*NoExpressions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*MessageExpressions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*FieldExpressions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*MissingField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DynRuntimeError); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*NowEqualsNow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*NoExpressions_Nested); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*MessageExpressions_Nested); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*FieldExpressions_Nested); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_depIdxs,
		EnumInfos:         file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_enumTypes,
		MessageInfos:      file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto = out.File
	file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_rawDesc = nil
	file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_goTypes = nil
	file_buf_validate_conformance_cases_custom_constraints_custom_constraints_proto_depIdxs = nil
}
