// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/strings.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StringNone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNone) Reset() {
	*x = StringNone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNone) ProtoMessage() {}

func (x *StringNone) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNone.ProtoReflect.Descriptor instead.
func (*StringNone) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{0}
}

func (x *StringNone) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringConst struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringConst) Reset() {
	*x = StringConst{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringConst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringConst) ProtoMessage() {}

func (x *StringConst) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringConst.ProtoReflect.Descriptor instead.
func (*StringConst) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{1}
}

func (x *StringConst) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIn) Reset() {
	*x = StringIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIn) ProtoMessage() {}

func (x *StringIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIn.ProtoReflect.Descriptor instead.
func (*StringIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{2}
}

func (x *StringIn) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIn) Reset() {
	*x = StringNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIn) ProtoMessage() {}

func (x *StringNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIn.ProtoReflect.Descriptor instead.
func (*StringNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{3}
}

func (x *StringNotIn) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringLen) Reset() {
	*x = StringLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringLen) ProtoMessage() {}

func (x *StringLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringLen.ProtoReflect.Descriptor instead.
func (*StringLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{4}
}

func (x *StringLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringMinLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringMinLen) Reset() {
	*x = StringMinLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringMinLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMinLen) ProtoMessage() {}

func (x *StringMinLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMinLen.ProtoReflect.Descriptor instead.
func (*StringMinLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{5}
}

func (x *StringMinLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringMaxLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringMaxLen) Reset() {
	*x = StringMaxLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringMaxLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMaxLen) ProtoMessage() {}

func (x *StringMaxLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMaxLen.ProtoReflect.Descriptor instead.
func (*StringMaxLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{6}
}

func (x *StringMaxLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringMinMaxLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringMinMaxLen) Reset() {
	*x = StringMinMaxLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringMinMaxLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMinMaxLen) ProtoMessage() {}

func (x *StringMinMaxLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMinMaxLen.ProtoReflect.Descriptor instead.
func (*StringMinMaxLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{7}
}

func (x *StringMinMaxLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringEqualMinMaxLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringEqualMinMaxLen) Reset() {
	*x = StringEqualMinMaxLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringEqualMinMaxLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringEqualMinMaxLen) ProtoMessage() {}

func (x *StringEqualMinMaxLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringEqualMinMaxLen.ProtoReflect.Descriptor instead.
func (*StringEqualMinMaxLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{8}
}

func (x *StringEqualMinMaxLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringLenBytes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringLenBytes) Reset() {
	*x = StringLenBytes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringLenBytes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringLenBytes) ProtoMessage() {}

func (x *StringLenBytes) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringLenBytes.ProtoReflect.Descriptor instead.
func (*StringLenBytes) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{9}
}

func (x *StringLenBytes) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringMinBytes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringMinBytes) Reset() {
	*x = StringMinBytes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringMinBytes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMinBytes) ProtoMessage() {}

func (x *StringMinBytes) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMinBytes.ProtoReflect.Descriptor instead.
func (*StringMinBytes) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{10}
}

func (x *StringMinBytes) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringMaxBytes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringMaxBytes) Reset() {
	*x = StringMaxBytes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringMaxBytes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMaxBytes) ProtoMessage() {}

func (x *StringMaxBytes) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMaxBytes.ProtoReflect.Descriptor instead.
func (*StringMaxBytes) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{11}
}

func (x *StringMaxBytes) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringMinMaxBytes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringMinMaxBytes) Reset() {
	*x = StringMinMaxBytes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringMinMaxBytes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMinMaxBytes) ProtoMessage() {}

func (x *StringMinMaxBytes) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMinMaxBytes.ProtoReflect.Descriptor instead.
func (*StringMinMaxBytes) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{12}
}

func (x *StringMinMaxBytes) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringEqualMinMaxBytes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringEqualMinMaxBytes) Reset() {
	*x = StringEqualMinMaxBytes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringEqualMinMaxBytes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringEqualMinMaxBytes) ProtoMessage() {}

func (x *StringEqualMinMaxBytes) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringEqualMinMaxBytes.ProtoReflect.Descriptor instead.
func (*StringEqualMinMaxBytes) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{13}
}

func (x *StringEqualMinMaxBytes) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringPattern struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringPattern) Reset() {
	*x = StringPattern{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringPattern) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringPattern) ProtoMessage() {}

func (x *StringPattern) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringPattern.ProtoReflect.Descriptor instead.
func (*StringPattern) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{14}
}

func (x *StringPattern) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringPatternEscapes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringPatternEscapes) Reset() {
	*x = StringPatternEscapes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringPatternEscapes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringPatternEscapes) ProtoMessage() {}

func (x *StringPatternEscapes) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringPatternEscapes.ProtoReflect.Descriptor instead.
func (*StringPatternEscapes) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{15}
}

func (x *StringPatternEscapes) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringPrefix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringPrefix) Reset() {
	*x = StringPrefix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringPrefix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringPrefix) ProtoMessage() {}

func (x *StringPrefix) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringPrefix.ProtoReflect.Descriptor instead.
func (*StringPrefix) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{16}
}

func (x *StringPrefix) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringContains struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringContains) Reset() {
	*x = StringContains{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringContains) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringContains) ProtoMessage() {}

func (x *StringContains) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringContains.ProtoReflect.Descriptor instead.
func (*StringContains) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{17}
}

func (x *StringContains) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotContains struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotContains) Reset() {
	*x = StringNotContains{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotContains) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotContains) ProtoMessage() {}

func (x *StringNotContains) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotContains.ProtoReflect.Descriptor instead.
func (*StringNotContains) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{18}
}

func (x *StringNotContains) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringSuffix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringSuffix) Reset() {
	*x = StringSuffix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringSuffix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringSuffix) ProtoMessage() {}

func (x *StringSuffix) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringSuffix.ProtoReflect.Descriptor instead.
func (*StringSuffix) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{19}
}

func (x *StringSuffix) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringEmail) Reset() {
	*x = StringEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringEmail) ProtoMessage() {}

func (x *StringEmail) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringEmail.ProtoReflect.Descriptor instead.
func (*StringEmail) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{20}
}

func (x *StringEmail) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotEmail) Reset() {
	*x = StringNotEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotEmail) ProtoMessage() {}

func (x *StringNotEmail) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotEmail.ProtoReflect.Descriptor instead.
func (*StringNotEmail) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{21}
}

func (x *StringNotEmail) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringAddress) Reset() {
	*x = StringAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringAddress) ProtoMessage() {}

func (x *StringAddress) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringAddress.ProtoReflect.Descriptor instead.
func (*StringAddress) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{22}
}

func (x *StringAddress) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotAddress) Reset() {
	*x = StringNotAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotAddress) ProtoMessage() {}

func (x *StringNotAddress) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotAddress.ProtoReflect.Descriptor instead.
func (*StringNotAddress) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{23}
}

func (x *StringNotAddress) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringHostname struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringHostname) Reset() {
	*x = StringHostname{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringHostname) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringHostname) ProtoMessage() {}

func (x *StringHostname) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringHostname.ProtoReflect.Descriptor instead.
func (*StringHostname) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{24}
}

func (x *StringHostname) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotHostname struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotHostname) Reset() {
	*x = StringNotHostname{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotHostname) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotHostname) ProtoMessage() {}

func (x *StringNotHostname) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotHostname.ProtoReflect.Descriptor instead.
func (*StringNotHostname) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{25}
}

func (x *StringNotHostname) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIP) Reset() {
	*x = StringIP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIP) ProtoMessage() {}

func (x *StringIP) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIP.ProtoReflect.Descriptor instead.
func (*StringIP) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{26}
}

func (x *StringIP) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIP) Reset() {
	*x = StringNotIP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIP) ProtoMessage() {}

func (x *StringNotIP) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIP.ProtoReflect.Descriptor instead.
func (*StringNotIP) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{27}
}

func (x *StringNotIP) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIPv4 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIPv4) Reset() {
	*x = StringIPv4{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIPv4) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIPv4) ProtoMessage() {}

func (x *StringIPv4) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIPv4.ProtoReflect.Descriptor instead.
func (*StringIPv4) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{28}
}

func (x *StringIPv4) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIPv4 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIPv4) Reset() {
	*x = StringNotIPv4{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIPv4) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIPv4) ProtoMessage() {}

func (x *StringNotIPv4) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIPv4.ProtoReflect.Descriptor instead.
func (*StringNotIPv4) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{29}
}

func (x *StringNotIPv4) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIPv6 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIPv6) Reset() {
	*x = StringIPv6{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIPv6) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIPv6) ProtoMessage() {}

func (x *StringIPv6) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIPv6.ProtoReflect.Descriptor instead.
func (*StringIPv6) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{30}
}

func (x *StringIPv6) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIPv6 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIPv6) Reset() {
	*x = StringNotIPv6{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIPv6) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIPv6) ProtoMessage() {}

func (x *StringNotIPv6) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIPv6.ProtoReflect.Descriptor instead.
func (*StringNotIPv6) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{31}
}

func (x *StringNotIPv6) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIPWithPrefixLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIPWithPrefixLen) Reset() {
	*x = StringIPWithPrefixLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIPWithPrefixLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIPWithPrefixLen) ProtoMessage() {}

func (x *StringIPWithPrefixLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIPWithPrefixLen.ProtoReflect.Descriptor instead.
func (*StringIPWithPrefixLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{32}
}

func (x *StringIPWithPrefixLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIPWithPrefixLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIPWithPrefixLen) Reset() {
	*x = StringNotIPWithPrefixLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIPWithPrefixLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIPWithPrefixLen) ProtoMessage() {}

func (x *StringNotIPWithPrefixLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIPWithPrefixLen.ProtoReflect.Descriptor instead.
func (*StringNotIPWithPrefixLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{33}
}

func (x *StringNotIPWithPrefixLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIPv4WithPrefixLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIPv4WithPrefixLen) Reset() {
	*x = StringIPv4WithPrefixLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIPv4WithPrefixLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIPv4WithPrefixLen) ProtoMessage() {}

func (x *StringIPv4WithPrefixLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIPv4WithPrefixLen.ProtoReflect.Descriptor instead.
func (*StringIPv4WithPrefixLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{34}
}

func (x *StringIPv4WithPrefixLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIPv4WithPrefixLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIPv4WithPrefixLen) Reset() {
	*x = StringNotIPv4WithPrefixLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIPv4WithPrefixLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIPv4WithPrefixLen) ProtoMessage() {}

func (x *StringNotIPv4WithPrefixLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIPv4WithPrefixLen.ProtoReflect.Descriptor instead.
func (*StringNotIPv4WithPrefixLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{35}
}

func (x *StringNotIPv4WithPrefixLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIPv6WithPrefixLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIPv6WithPrefixLen) Reset() {
	*x = StringIPv6WithPrefixLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIPv6WithPrefixLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIPv6WithPrefixLen) ProtoMessage() {}

func (x *StringIPv6WithPrefixLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIPv6WithPrefixLen.ProtoReflect.Descriptor instead.
func (*StringIPv6WithPrefixLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{36}
}

func (x *StringIPv6WithPrefixLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIPv6WithPrefixLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIPv6WithPrefixLen) Reset() {
	*x = StringNotIPv6WithPrefixLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIPv6WithPrefixLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIPv6WithPrefixLen) ProtoMessage() {}

func (x *StringNotIPv6WithPrefixLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIPv6WithPrefixLen.ProtoReflect.Descriptor instead.
func (*StringNotIPv6WithPrefixLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{37}
}

func (x *StringNotIPv6WithPrefixLen) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIPPrefix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIPPrefix) Reset() {
	*x = StringIPPrefix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIPPrefix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIPPrefix) ProtoMessage() {}

func (x *StringIPPrefix) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIPPrefix.ProtoReflect.Descriptor instead.
func (*StringIPPrefix) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{38}
}

func (x *StringIPPrefix) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIPPrefix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIPPrefix) Reset() {
	*x = StringNotIPPrefix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIPPrefix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIPPrefix) ProtoMessage() {}

func (x *StringNotIPPrefix) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIPPrefix.ProtoReflect.Descriptor instead.
func (*StringNotIPPrefix) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{39}
}

func (x *StringNotIPPrefix) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIPv4Prefix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIPv4Prefix) Reset() {
	*x = StringIPv4Prefix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIPv4Prefix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIPv4Prefix) ProtoMessage() {}

func (x *StringIPv4Prefix) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIPv4Prefix.ProtoReflect.Descriptor instead.
func (*StringIPv4Prefix) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{40}
}

func (x *StringIPv4Prefix) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIPv4Prefix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIPv4Prefix) Reset() {
	*x = StringNotIPv4Prefix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIPv4Prefix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIPv4Prefix) ProtoMessage() {}

func (x *StringNotIPv4Prefix) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIPv4Prefix.ProtoReflect.Descriptor instead.
func (*StringNotIPv4Prefix) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{41}
}

func (x *StringNotIPv4Prefix) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringIPv6Prefix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringIPv6Prefix) Reset() {
	*x = StringIPv6Prefix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringIPv6Prefix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringIPv6Prefix) ProtoMessage() {}

func (x *StringIPv6Prefix) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringIPv6Prefix.ProtoReflect.Descriptor instead.
func (*StringIPv6Prefix) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{42}
}

func (x *StringIPv6Prefix) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotIPv6Prefix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotIPv6Prefix) Reset() {
	*x = StringNotIPv6Prefix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotIPv6Prefix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotIPv6Prefix) ProtoMessage() {}

func (x *StringNotIPv6Prefix) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotIPv6Prefix.ProtoReflect.Descriptor instead.
func (*StringNotIPv6Prefix) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{43}
}

func (x *StringNotIPv6Prefix) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringURI struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringURI) Reset() {
	*x = StringURI{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringURI) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringURI) ProtoMessage() {}

func (x *StringURI) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringURI.ProtoReflect.Descriptor instead.
func (*StringURI) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{44}
}

func (x *StringURI) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotURI struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotURI) Reset() {
	*x = StringNotURI{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotURI) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotURI) ProtoMessage() {}

func (x *StringNotURI) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotURI.ProtoReflect.Descriptor instead.
func (*StringNotURI) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{45}
}

func (x *StringNotURI) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringURIRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringURIRef) Reset() {
	*x = StringURIRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringURIRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringURIRef) ProtoMessage() {}

func (x *StringURIRef) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringURIRef.ProtoReflect.Descriptor instead.
func (*StringURIRef) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{46}
}

func (x *StringURIRef) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotURIRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotURIRef) Reset() {
	*x = StringNotURIRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotURIRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotURIRef) ProtoMessage() {}

func (x *StringNotURIRef) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotURIRef.ProtoReflect.Descriptor instead.
func (*StringNotURIRef) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{47}
}

func (x *StringNotURIRef) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringUUID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringUUID) Reset() {
	*x = StringUUID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringUUID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringUUID) ProtoMessage() {}

func (x *StringUUID) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringUUID.ProtoReflect.Descriptor instead.
func (*StringUUID) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{48}
}

func (x *StringUUID) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotUUID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotUUID) Reset() {
	*x = StringNotUUID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotUUID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotUUID) ProtoMessage() {}

func (x *StringNotUUID) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotUUID.ProtoReflect.Descriptor instead.
func (*StringNotUUID) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{49}
}

func (x *StringNotUUID) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringTUUID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringTUUID) Reset() {
	*x = StringTUUID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringTUUID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringTUUID) ProtoMessage() {}

func (x *StringTUUID) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringTUUID.ProtoReflect.Descriptor instead.
func (*StringTUUID) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{50}
}

func (x *StringTUUID) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringNotTUUID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringNotTUUID) Reset() {
	*x = StringNotTUUID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringNotTUUID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringNotTUUID) ProtoMessage() {}

func (x *StringNotTUUID) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringNotTUUID.ProtoReflect.Descriptor instead.
func (*StringNotTUUID) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{51}
}

func (x *StringNotTUUID) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringHttpHeaderName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringHttpHeaderName) Reset() {
	*x = StringHttpHeaderName{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringHttpHeaderName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringHttpHeaderName) ProtoMessage() {}

func (x *StringHttpHeaderName) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringHttpHeaderName.ProtoReflect.Descriptor instead.
func (*StringHttpHeaderName) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{52}
}

func (x *StringHttpHeaderName) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringHttpHeaderValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringHttpHeaderValue) Reset() {
	*x = StringHttpHeaderValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringHttpHeaderValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringHttpHeaderValue) ProtoMessage() {}

func (x *StringHttpHeaderValue) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringHttpHeaderValue.ProtoReflect.Descriptor instead.
func (*StringHttpHeaderValue) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{53}
}

func (x *StringHttpHeaderValue) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringHttpHeaderNameLoose struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringHttpHeaderNameLoose) Reset() {
	*x = StringHttpHeaderNameLoose{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringHttpHeaderNameLoose) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringHttpHeaderNameLoose) ProtoMessage() {}

func (x *StringHttpHeaderNameLoose) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringHttpHeaderNameLoose.ProtoReflect.Descriptor instead.
func (*StringHttpHeaderNameLoose) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{54}
}

func (x *StringHttpHeaderNameLoose) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringHttpHeaderValueLoose struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringHttpHeaderValueLoose) Reset() {
	*x = StringHttpHeaderValueLoose{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringHttpHeaderValueLoose) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringHttpHeaderValueLoose) ProtoMessage() {}

func (x *StringHttpHeaderValueLoose) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringHttpHeaderValueLoose.ProtoReflect.Descriptor instead.
func (*StringHttpHeaderValueLoose) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{55}
}

func (x *StringHttpHeaderValueLoose) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringUUIDIgnore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringUUIDIgnore) Reset() {
	*x = StringUUIDIgnore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringUUIDIgnore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringUUIDIgnore) ProtoMessage() {}

func (x *StringUUIDIgnore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringUUIDIgnore.ProtoReflect.Descriptor instead.
func (*StringUUIDIgnore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{56}
}

func (x *StringUUIDIgnore) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringInOneof struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Foo:
	//
	//	*StringInOneof_Bar
	Foo isStringInOneof_Foo `protobuf_oneof:"foo"`
}

func (x *StringInOneof) Reset() {
	*x = StringInOneof{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringInOneof) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringInOneof) ProtoMessage() {}

func (x *StringInOneof) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringInOneof.ProtoReflect.Descriptor instead.
func (*StringInOneof) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{57}
}

func (m *StringInOneof) GetFoo() isStringInOneof_Foo {
	if m != nil {
		return m.Foo
	}
	return nil
}

func (x *StringInOneof) GetBar() string {
	if x, ok := x.GetFoo().(*StringInOneof_Bar); ok {
		return x.Bar
	}
	return ""
}

type isStringInOneof_Foo interface {
	isStringInOneof_Foo()
}

type StringInOneof_Bar struct {
	Bar string `protobuf:"bytes,1,opt,name=bar,proto3,oneof"`
}

func (*StringInOneof_Bar) isStringInOneof_Foo() {}

type StringHostAndPort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringHostAndPort) Reset() {
	*x = StringHostAndPort{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringHostAndPort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringHostAndPort) ProtoMessage() {}

func (x *StringHostAndPort) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringHostAndPort.ProtoReflect.Descriptor instead.
func (*StringHostAndPort) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{58}
}

func (x *StringHostAndPort) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringHostAndOptionalPort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringHostAndOptionalPort) Reset() {
	*x = StringHostAndOptionalPort{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringHostAndOptionalPort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringHostAndOptionalPort) ProtoMessage() {}

func (x *StringHostAndOptionalPort) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringHostAndOptionalPort.ProtoReflect.Descriptor instead.
func (*StringHostAndOptionalPort) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{59}
}

func (x *StringHostAndOptionalPort) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type StringExample struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *StringExample) Reset() {
	*x = StringExample{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringExample) ProtoMessage() {}

func (x *StringExample) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_strings_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringExample.ProtoReflect.Descriptor instead.
func (*StringExample) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_strings_proto_rawDescGZIP(), []int{60}
}

func (x *StringExample) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

var File_buf_validate_conformance_cases_strings_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_strings_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x1b,
	0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1e, 0x0a, 0x0a, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0b, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x72, 0x05, 0x0a, 0x03,
	0x66, 0x6f, 0x6f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x08, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x72, 0x0a, 0x52, 0x03, 0x62, 0x61, 0x72, 0x52, 0x03, 0x62,
	0x61, 0x7a, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x32, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x23, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0xba, 0x48, 0x0e, 0x72, 0x0c, 0x5a, 0x04, 0x66, 0x69, 0x7a, 0x7a,
	0x5a, 0x04, 0x62, 0x75, 0x7a, 0x7a, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x09, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0x98, 0x01, 0x03, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x29, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x69,
	0x6e, 0x4c, 0x65, 0x6e, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x29, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x12,
	0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x72, 0x02, 0x18, 0x05, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0f, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x4d, 0x69, 0x6e, 0x4d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x1b, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xba, 0x48, 0x06, 0x72,
	0x04, 0x10, 0x03, 0x18, 0x05, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x14, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x45, 0x71, 0x75, 0x61, 0x6c, 0x4d, 0x69, 0x6e, 0x4d, 0x61, 0x78, 0x4c,
	0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xba, 0x48, 0x06, 0x72, 0x04, 0x10, 0x05, 0x18, 0x05, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x2c, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x65, 0x6e, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xba, 0x48, 0x05, 0x72, 0x03, 0xa0, 0x01, 0x04, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a,
	0x0e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x69, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12,
	0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x72, 0x02, 0x20, 0x04, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x78, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02,
	0x28, 0x08, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x30, 0x0a, 0x11, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x4d, 0x69, 0x6e, 0x4d, 0x61, 0x78, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xba, 0x48, 0x06, 0x72, 0x04,
	0x20, 0x04, 0x28, 0x08, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x35, 0x0a, 0x16, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x45, 0x71, 0x75, 0x61, 0x6c, 0x4d, 0x69, 0x6e, 0x4d, 0x61, 0x78, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xba, 0x48, 0x06, 0x72, 0x04, 0x20, 0x04, 0x28, 0x04, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x39, 0x0a, 0x0d, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x6e, 0x12, 0x28, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16,
	0xba, 0x48, 0x13, 0x72, 0x11, 0x32, 0x0f, 0x28, 0x3f, 0x69, 0x29, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x39, 0x0a, 0x14, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x45, 0x73, 0x63, 0x61,
	0x70, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0f, 0xba, 0x48, 0x0c, 0x72, 0x0a, 0x32, 0x08, 0x5c, 0x2a, 0x20, 0x5c, 0x5c, 0x20, 0x5c,
	0x77, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x72, 0x05, 0x3a, 0x03, 0x66, 0x6f, 0x6f, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x72, 0x05, 0x4a, 0x03, 0x62, 0x61, 0x72, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x32, 0x0a, 0x11, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x72, 0x06, 0xba, 0x01, 0x03,
	0x62, 0x61, 0x72, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x72, 0x05, 0x42, 0x03, 0x62, 0x61,
	0x7a, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x60, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x2b, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x60, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a,
	0x0d, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x72, 0x03, 0xa8, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x10, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x72, 0x03, 0xa8, 0x01, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02,
	0x68, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x11, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x4e, 0x6f, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02,
	0x68, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x49, 0x50, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x70, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28,
	0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x49, 0x50, 0x12, 0x19, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72,
	0x02, 0x70, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x0a, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x49, 0x50, 0x76, 0x34, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x78, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x2a, 0x0a, 0x0d, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x49, 0x50,
	0x76, 0x34, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x78, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a,
	0x0a, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x50, 0x76, 0x36, 0x12, 0x1a, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0x80,
	0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0d, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x4e, 0x6f, 0x74, 0x49, 0x50, 0x76, 0x36, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0x80, 0x01, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x15, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x50,
	0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72,
	0x03, 0xd0, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x36, 0x0a, 0x18, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x49, 0x50, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xd0, 0x01, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x35, 0x0a, 0x17, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x50, 0x76, 0x34, 0x57,
	0x69, 0x74, 0x68, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03,
	0xd8, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x1a, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x49, 0x50, 0x76, 0x34, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x65,
	0x66, 0x69, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xd8, 0x01, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x35, 0x0a, 0x17, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x50, 0x76, 0x36,
	0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72,
	0x03, 0xe0, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x1a, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x49, 0x50, 0x76, 0x36, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72,
	0x65, 0x66, 0x69, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xe0, 0x01, 0x00, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x50, 0x50,
	0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xe8, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x2f, 0x0a, 0x11, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x49, 0x50,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xe8, 0x01, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x50, 0x76, 0x34,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xf0, 0x01, 0x01, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x31, 0x0a, 0x13, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x49,
	0x50, 0x76, 0x34, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xf0, 0x01, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49,
	0x50, 0x76, 0x36, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xf8, 0x01, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x31, 0x0a, 0x13, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e,
	0x6f, 0x74, 0x49, 0x50, 0x76, 0x36, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03,
	0xf8, 0x01, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x09, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x55, 0x52, 0x49, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0x88, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x2a, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x55, 0x52,
	0x49, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xba, 0x48, 0x05, 0x72, 0x03, 0x88, 0x01, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a,
	0x0c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x52, 0x49, 0x52, 0x65, 0x66, 0x12, 0x1a, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72,
	0x03, 0x90, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x0f, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x55, 0x52, 0x49, 0x52, 0x65, 0x66, 0x12, 0x1a, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03,
	0x90, 0x01, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x0a, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x55, 0x55, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xb0, 0x01, 0x01, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0d, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x55,
	0x55, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xb0, 0x01, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x29, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x54, 0x55, 0x55, 0x49, 0x44, 0x12, 0x1a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x72, 0x03, 0x88, 0x02, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x54, 0x55, 0x55, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03,
	0x88, 0x02, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x32, 0x0a, 0x14, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x48, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba,
	0x48, 0x05, 0x72, 0x03, 0xc0, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x15,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x48, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xc0, 0x01, 0x02, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x3a, 0x0a, 0x19, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x48, 0x74, 0x74, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x6f, 0x6f, 0x73, 0x65, 0x12, 0x1d,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48, 0x08,
	0x72, 0x06, 0xc8, 0x01, 0x00, 0xc0, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3b, 0x0a,
	0x1a, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x48, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x6f, 0x6f, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x72, 0x06, 0xc8,
	0x01, 0x00, 0xc0, 0x01, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x31, 0x0a, 0x10, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x55, 0x55, 0x49, 0x44, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x1d,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48, 0x08,
	0xd8, 0x01, 0x01, 0x72, 0x03, 0xb0, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x37, 0x0a,
	0x0d, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x12, 0x1f,
	0x0a, 0x03, 0x62, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48, 0x08,
	0x72, 0x06, 0x52, 0x01, 0x61, 0x52, 0x01, 0x62, 0x48, 0x00, 0x52, 0x03, 0x62, 0x61, 0x72, 0x42,
	0x05, 0x0a, 0x03, 0x66, 0x6f, 0x6f, 0x22, 0x2f, 0x0a, 0x11, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x48, 0x6f, 0x73, 0x74, 0x41, 0x6e, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0x80,
	0x02, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xa4, 0x01, 0x0a, 0x19, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x48, 0x6f, 0x73, 0x74, 0x41, 0x6e, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x86, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x74, 0xba, 0x48, 0x71, 0xba, 0x01, 0x6e, 0x0a, 0x22, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x70, 0x6f, 0x72,
	0x74, 0x2e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x2d, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61,
	0x20, 0x68, 0x6f, 0x73, 0x74, 0x20, 0x61, 0x6e, 0x64, 0x20, 0x28, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x29, 0x20, 0x70, 0x6f, 0x72, 0x74, 0x20, 0x70, 0x61, 0x69, 0x72, 0x1a, 0x19,
	0x74, 0x68, 0x69, 0x73, 0x2e, 0x69, 0x73, 0x48, 0x6f, 0x73, 0x74, 0x41, 0x6e, 0x64, 0x50, 0x6f,
	0x72, 0x74, 0x28, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x29, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e,
	0x0a, 0x0d, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12,
	0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48,
	0x08, 0x72, 0x06, 0x92, 0x02, 0x03, 0x66, 0x6f, 0x6f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0xa3,
	0x02, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x42, 0x0c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02, 0x04, 0x42, 0x56, 0x43,
	0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61,
	0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_buf_validate_conformance_cases_strings_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_strings_proto_rawDescData = file_buf_validate_conformance_cases_strings_proto_rawDesc
)

func file_buf_validate_conformance_cases_strings_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_strings_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_strings_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_strings_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_strings_proto_rawDescData
}

var file_buf_validate_conformance_cases_strings_proto_msgTypes = make([]protoimpl.MessageInfo, 61)
var file_buf_validate_conformance_cases_strings_proto_goTypes = []any{
	(*StringNone)(nil),                 // 0: buf.validate.conformance.cases.StringNone
	(*StringConst)(nil),                // 1: buf.validate.conformance.cases.StringConst
	(*StringIn)(nil),                   // 2: buf.validate.conformance.cases.StringIn
	(*StringNotIn)(nil),                // 3: buf.validate.conformance.cases.StringNotIn
	(*StringLen)(nil),                  // 4: buf.validate.conformance.cases.StringLen
	(*StringMinLen)(nil),               // 5: buf.validate.conformance.cases.StringMinLen
	(*StringMaxLen)(nil),               // 6: buf.validate.conformance.cases.StringMaxLen
	(*StringMinMaxLen)(nil),            // 7: buf.validate.conformance.cases.StringMinMaxLen
	(*StringEqualMinMaxLen)(nil),       // 8: buf.validate.conformance.cases.StringEqualMinMaxLen
	(*StringLenBytes)(nil),             // 9: buf.validate.conformance.cases.StringLenBytes
	(*StringMinBytes)(nil),             // 10: buf.validate.conformance.cases.StringMinBytes
	(*StringMaxBytes)(nil),             // 11: buf.validate.conformance.cases.StringMaxBytes
	(*StringMinMaxBytes)(nil),          // 12: buf.validate.conformance.cases.StringMinMaxBytes
	(*StringEqualMinMaxBytes)(nil),     // 13: buf.validate.conformance.cases.StringEqualMinMaxBytes
	(*StringPattern)(nil),              // 14: buf.validate.conformance.cases.StringPattern
	(*StringPatternEscapes)(nil),       // 15: buf.validate.conformance.cases.StringPatternEscapes
	(*StringPrefix)(nil),               // 16: buf.validate.conformance.cases.StringPrefix
	(*StringContains)(nil),             // 17: buf.validate.conformance.cases.StringContains
	(*StringNotContains)(nil),          // 18: buf.validate.conformance.cases.StringNotContains
	(*StringSuffix)(nil),               // 19: buf.validate.conformance.cases.StringSuffix
	(*StringEmail)(nil),                // 20: buf.validate.conformance.cases.StringEmail
	(*StringNotEmail)(nil),             // 21: buf.validate.conformance.cases.StringNotEmail
	(*StringAddress)(nil),              // 22: buf.validate.conformance.cases.StringAddress
	(*StringNotAddress)(nil),           // 23: buf.validate.conformance.cases.StringNotAddress
	(*StringHostname)(nil),             // 24: buf.validate.conformance.cases.StringHostname
	(*StringNotHostname)(nil),          // 25: buf.validate.conformance.cases.StringNotHostname
	(*StringIP)(nil),                   // 26: buf.validate.conformance.cases.StringIP
	(*StringNotIP)(nil),                // 27: buf.validate.conformance.cases.StringNotIP
	(*StringIPv4)(nil),                 // 28: buf.validate.conformance.cases.StringIPv4
	(*StringNotIPv4)(nil),              // 29: buf.validate.conformance.cases.StringNotIPv4
	(*StringIPv6)(nil),                 // 30: buf.validate.conformance.cases.StringIPv6
	(*StringNotIPv6)(nil),              // 31: buf.validate.conformance.cases.StringNotIPv6
	(*StringIPWithPrefixLen)(nil),      // 32: buf.validate.conformance.cases.StringIPWithPrefixLen
	(*StringNotIPWithPrefixLen)(nil),   // 33: buf.validate.conformance.cases.StringNotIPWithPrefixLen
	(*StringIPv4WithPrefixLen)(nil),    // 34: buf.validate.conformance.cases.StringIPv4WithPrefixLen
	(*StringNotIPv4WithPrefixLen)(nil), // 35: buf.validate.conformance.cases.StringNotIPv4WithPrefixLen
	(*StringIPv6WithPrefixLen)(nil),    // 36: buf.validate.conformance.cases.StringIPv6WithPrefixLen
	(*StringNotIPv6WithPrefixLen)(nil), // 37: buf.validate.conformance.cases.StringNotIPv6WithPrefixLen
	(*StringIPPrefix)(nil),             // 38: buf.validate.conformance.cases.StringIPPrefix
	(*StringNotIPPrefix)(nil),          // 39: buf.validate.conformance.cases.StringNotIPPrefix
	(*StringIPv4Prefix)(nil),           // 40: buf.validate.conformance.cases.StringIPv4Prefix
	(*StringNotIPv4Prefix)(nil),        // 41: buf.validate.conformance.cases.StringNotIPv4Prefix
	(*StringIPv6Prefix)(nil),           // 42: buf.validate.conformance.cases.StringIPv6Prefix
	(*StringNotIPv6Prefix)(nil),        // 43: buf.validate.conformance.cases.StringNotIPv6Prefix
	(*StringURI)(nil),                  // 44: buf.validate.conformance.cases.StringURI
	(*StringNotURI)(nil),               // 45: buf.validate.conformance.cases.StringNotURI
	(*StringURIRef)(nil),               // 46: buf.validate.conformance.cases.StringURIRef
	(*StringNotURIRef)(nil),            // 47: buf.validate.conformance.cases.StringNotURIRef
	(*StringUUID)(nil),                 // 48: buf.validate.conformance.cases.StringUUID
	(*StringNotUUID)(nil),              // 49: buf.validate.conformance.cases.StringNotUUID
	(*StringTUUID)(nil),                // 50: buf.validate.conformance.cases.StringTUUID
	(*StringNotTUUID)(nil),             // 51: buf.validate.conformance.cases.StringNotTUUID
	(*StringHttpHeaderName)(nil),       // 52: buf.validate.conformance.cases.StringHttpHeaderName
	(*StringHttpHeaderValue)(nil),      // 53: buf.validate.conformance.cases.StringHttpHeaderValue
	(*StringHttpHeaderNameLoose)(nil),  // 54: buf.validate.conformance.cases.StringHttpHeaderNameLoose
	(*StringHttpHeaderValueLoose)(nil), // 55: buf.validate.conformance.cases.StringHttpHeaderValueLoose
	(*StringUUIDIgnore)(nil),           // 56: buf.validate.conformance.cases.StringUUIDIgnore
	(*StringInOneof)(nil),              // 57: buf.validate.conformance.cases.StringInOneof
	(*StringHostAndPort)(nil),          // 58: buf.validate.conformance.cases.StringHostAndPort
	(*StringHostAndOptionalPort)(nil),  // 59: buf.validate.conformance.cases.StringHostAndOptionalPort
	(*StringExample)(nil),              // 60: buf.validate.conformance.cases.StringExample
}
var file_buf_validate_conformance_cases_strings_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_strings_proto_init() }
func file_buf_validate_conformance_cases_strings_proto_init() {
	if File_buf_validate_conformance_cases_strings_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_strings_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*StringNone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*StringConst); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*StringIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*StringLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*StringMinLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*StringMaxLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*StringMinMaxLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*StringEqualMinMaxLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*StringLenBytes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*StringMinBytes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*StringMaxBytes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*StringMinMaxBytes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*StringEqualMinMaxBytes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*StringPattern); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*StringPatternEscapes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*StringPrefix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*StringContains); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotContains); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*StringSuffix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*StringEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*StringAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*StringHostname); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotHostname); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*StringIP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*StringIPv4); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIPv4); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*StringIPv6); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIPv6); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*StringIPWithPrefixLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIPWithPrefixLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*StringIPv4WithPrefixLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIPv4WithPrefixLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*StringIPv6WithPrefixLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIPv6WithPrefixLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*StringIPPrefix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIPPrefix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*StringIPv4Prefix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIPv4Prefix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*StringIPv6Prefix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotIPv6Prefix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*StringURI); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotURI); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*StringURIRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotURIRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*StringUUID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotUUID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*StringTUUID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*StringNotTUUID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[52].Exporter = func(v any, i int) any {
			switch v := v.(*StringHttpHeaderName); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[53].Exporter = func(v any, i int) any {
			switch v := v.(*StringHttpHeaderValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[54].Exporter = func(v any, i int) any {
			switch v := v.(*StringHttpHeaderNameLoose); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[55].Exporter = func(v any, i int) any {
			switch v := v.(*StringHttpHeaderValueLoose); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[56].Exporter = func(v any, i int) any {
			switch v := v.(*StringUUIDIgnore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[57].Exporter = func(v any, i int) any {
			switch v := v.(*StringInOneof); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[58].Exporter = func(v any, i int) any {
			switch v := v.(*StringHostAndPort); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[59].Exporter = func(v any, i int) any {
			switch v := v.(*StringHostAndOptionalPort); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_strings_proto_msgTypes[60].Exporter = func(v any, i int) any {
			switch v := v.(*StringExample); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_strings_proto_msgTypes[57].OneofWrappers = []any{
		(*StringInOneof_Bar)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_strings_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   61,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_strings_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_strings_proto_depIdxs,
		MessageInfos:      file_buf_validate_conformance_cases_strings_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_strings_proto = out.File
	file_buf_validate_conformance_cases_strings_proto_rawDesc = nil
	file_buf_validate_conformance_cases_strings_proto_goTypes = nil
	file_buf_validate_conformance_cases_strings_proto_depIdxs = nil
}
