// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/ignore_empty_proto_editions.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IgnoreEmptyEditionsScalarExplicitPresence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsScalarExplicitPresence) Reset() {
	*x = IgnoreEmptyEditionsScalarExplicitPresence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsScalarExplicitPresence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsScalarExplicitPresence) ProtoMessage() {}

func (x *IgnoreEmptyEditionsScalarExplicitPresence) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsScalarExplicitPresence.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsScalarExplicitPresence) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{0}
}

func (x *IgnoreEmptyEditionsScalarExplicitPresence) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type IgnoreEmptyEditionsScalarExplicitPresenceWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,def=42" json:"val,omitempty"`
}

// Default values for IgnoreEmptyEditionsScalarExplicitPresenceWithDefault fields.
const (
	Default_IgnoreEmptyEditionsScalarExplicitPresenceWithDefault_Val = int32(42)
)

func (x *IgnoreEmptyEditionsScalarExplicitPresenceWithDefault) Reset() {
	*x = IgnoreEmptyEditionsScalarExplicitPresenceWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsScalarExplicitPresenceWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsScalarExplicitPresenceWithDefault) ProtoMessage() {}

func (x *IgnoreEmptyEditionsScalarExplicitPresenceWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsScalarExplicitPresenceWithDefault.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsScalarExplicitPresenceWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{1}
}

func (x *IgnoreEmptyEditionsScalarExplicitPresenceWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_IgnoreEmptyEditionsScalarExplicitPresenceWithDefault_Val
}

type IgnoreEmptyEditionsScalarImplicitPresence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsScalarImplicitPresence) Reset() {
	*x = IgnoreEmptyEditionsScalarImplicitPresence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsScalarImplicitPresence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsScalarImplicitPresence) ProtoMessage() {}

func (x *IgnoreEmptyEditionsScalarImplicitPresence) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsScalarImplicitPresence.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsScalarImplicitPresence) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{2}
}

func (x *IgnoreEmptyEditionsScalarImplicitPresence) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type IgnoreEmptyEditionsScalarLegacyRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsScalarLegacyRequired) Reset() {
	*x = IgnoreEmptyEditionsScalarLegacyRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsScalarLegacyRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsScalarLegacyRequired) ProtoMessage() {}

func (x *IgnoreEmptyEditionsScalarLegacyRequired) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsScalarLegacyRequired.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsScalarLegacyRequired) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{3}
}

func (x *IgnoreEmptyEditionsScalarLegacyRequired) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type IgnoreEmptyEditionsScalarLegacyRequiredWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val,def=42" json:"val,omitempty"`
}

// Default values for IgnoreEmptyEditionsScalarLegacyRequiredWithDefault fields.
const (
	Default_IgnoreEmptyEditionsScalarLegacyRequiredWithDefault_Val = int32(42)
)

func (x *IgnoreEmptyEditionsScalarLegacyRequiredWithDefault) Reset() {
	*x = IgnoreEmptyEditionsScalarLegacyRequiredWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsScalarLegacyRequiredWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsScalarLegacyRequiredWithDefault) ProtoMessage() {}

func (x *IgnoreEmptyEditionsScalarLegacyRequiredWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsScalarLegacyRequiredWithDefault.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsScalarLegacyRequiredWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{4}
}

func (x *IgnoreEmptyEditionsScalarLegacyRequiredWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_IgnoreEmptyEditionsScalarLegacyRequiredWithDefault_Val
}

type IgnoreEmptyEditionsMessageExplicitPresence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *IgnoreEmptyEditionsMessageExplicitPresence_Msg `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsMessageExplicitPresence) Reset() {
	*x = IgnoreEmptyEditionsMessageExplicitPresence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMessageExplicitPresence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMessageExplicitPresence) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMessageExplicitPresence) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMessageExplicitPresence.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMessageExplicitPresence) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{5}
}

func (x *IgnoreEmptyEditionsMessageExplicitPresence) GetVal() *IgnoreEmptyEditionsMessageExplicitPresence_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type IgnoreEmptyEditionsMessageExplicitPresenceDelimited struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg `protobuf:"group,1,opt,name=Msg,json=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsMessageExplicitPresenceDelimited) Reset() {
	*x = IgnoreEmptyEditionsMessageExplicitPresenceDelimited{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMessageExplicitPresenceDelimited) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMessageExplicitPresenceDelimited) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMessageExplicitPresenceDelimited) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMessageExplicitPresenceDelimited.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMessageExplicitPresenceDelimited) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{6}
}

func (x *IgnoreEmptyEditionsMessageExplicitPresenceDelimited) GetVal() *IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type IgnoreEmptyEditionsMessageLegacyRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *IgnoreEmptyEditionsMessageLegacyRequired_Msg `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsMessageLegacyRequired) Reset() {
	*x = IgnoreEmptyEditionsMessageLegacyRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMessageLegacyRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMessageLegacyRequired) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMessageLegacyRequired) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMessageLegacyRequired.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMessageLegacyRequired) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{7}
}

func (x *IgnoreEmptyEditionsMessageLegacyRequired) GetVal() *IgnoreEmptyEditionsMessageLegacyRequired_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type IgnoreEmptyEditionsMessageLegacyRequiredDelimited struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg `protobuf:"group,1,req,name=Msg,json=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsMessageLegacyRequiredDelimited) Reset() {
	*x = IgnoreEmptyEditionsMessageLegacyRequiredDelimited{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMessageLegacyRequiredDelimited) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMessageLegacyRequiredDelimited) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMessageLegacyRequiredDelimited) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMessageLegacyRequiredDelimited.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMessageLegacyRequiredDelimited) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{8}
}

func (x *IgnoreEmptyEditionsMessageLegacyRequiredDelimited) GetVal() *IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type IgnoreEmptyEditionsOneof struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*IgnoreEmptyEditionsOneof_Val
	O isIgnoreEmptyEditionsOneof_O `protobuf_oneof:"o"`
}

func (x *IgnoreEmptyEditionsOneof) Reset() {
	*x = IgnoreEmptyEditionsOneof{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsOneof) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsOneof) ProtoMessage() {}

func (x *IgnoreEmptyEditionsOneof) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsOneof.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsOneof) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{9}
}

func (m *IgnoreEmptyEditionsOneof) GetO() isIgnoreEmptyEditionsOneof_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *IgnoreEmptyEditionsOneof) GetVal() int32 {
	if x, ok := x.GetO().(*IgnoreEmptyEditionsOneof_Val); ok {
		return x.Val
	}
	return 0
}

type isIgnoreEmptyEditionsOneof_O interface {
	isIgnoreEmptyEditionsOneof_O()
}

type IgnoreEmptyEditionsOneof_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof"`
}

func (*IgnoreEmptyEditionsOneof_Val) isIgnoreEmptyEditionsOneof_O() {}

type IgnoreEmptyEditionsRepeated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsRepeated) Reset() {
	*x = IgnoreEmptyEditionsRepeated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsRepeated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsRepeated) ProtoMessage() {}

func (x *IgnoreEmptyEditionsRepeated) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsRepeated.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsRepeated) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{10}
}

func (x *IgnoreEmptyEditionsRepeated) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type IgnoreEmptyEditionsRepeatedExpanded struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsRepeatedExpanded) Reset() {
	*x = IgnoreEmptyEditionsRepeatedExpanded{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsRepeatedExpanded) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsRepeatedExpanded) ProtoMessage() {}

func (x *IgnoreEmptyEditionsRepeatedExpanded) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsRepeatedExpanded.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsRepeatedExpanded) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{11}
}

func (x *IgnoreEmptyEditionsRepeatedExpanded) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type IgnoreEmptyEditionsMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *IgnoreEmptyEditionsMap) Reset() {
	*x = IgnoreEmptyEditionsMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMap) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMap) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMap.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMap) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{12}
}

func (x *IgnoreEmptyEditionsMap) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type IgnoreEmptyEditionsMessageExplicitPresence_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsMessageExplicitPresence_Msg) Reset() {
	*x = IgnoreEmptyEditionsMessageExplicitPresence_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMessageExplicitPresence_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMessageExplicitPresence_Msg) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMessageExplicitPresence_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMessageExplicitPresence_Msg.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMessageExplicitPresence_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{5, 0}
}

func (x *IgnoreEmptyEditionsMessageExplicitPresence_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg) Reset() {
	*x = IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{6, 0}
}

func (x *IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type IgnoreEmptyEditionsMessageLegacyRequired_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsMessageLegacyRequired_Msg) Reset() {
	*x = IgnoreEmptyEditionsMessageLegacyRequired_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMessageLegacyRequired_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMessageLegacyRequired_Msg) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMessageLegacyRequired_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMessageLegacyRequired_Msg.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMessageLegacyRequired_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{7, 0}
}

func (x *IgnoreEmptyEditionsMessageLegacyRequired_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg) Reset() {
	*x = IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg) ProtoMessage() {}

func (x *IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg.ProtoReflect.Descriptor instead.
func (*IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP(), []int{8, 0}
}

func (x *IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

var File_buf_validate_conformance_cases_ignore_empty_proto_editions_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDesc = []byte{
	0x0a, 0x40, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x49, 0x0a, 0x29, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45, 0x78, 0x70, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01,
	0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x58, 0x0a, 0x34, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a,
	0x02, 0x34, 0x32, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x4e, 0x0a, 0x29, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61,
	0x72, 0x49, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0f,
	0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x4c, 0x0a, 0x27, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61,
	0x72, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12,
	0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0f, 0xba, 0x48,
	0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x5b, 0x0a, 0x32, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4c,
	0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x57, 0x69, 0x74,
	0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x02, 0x34, 0x32, 0x42, 0x0f, 0xba, 0x48, 0x07, 0xd8, 0x01,
	0x01, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0xed, 0x01, 0x0a, 0x2a, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78,
	0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0xa5,
	0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x43, 0xba, 0x48,
	0x40, 0xba, 0x01, 0x3a, 0x0a, 0x1d, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69,
	0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x84, 0x02, 0x0a, 0x33, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78,
	0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0xb3, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x57, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x48,
	0xba, 0x48, 0x40, 0xba, 0x01, 0x3a, 0x0a, 0x1d, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74,
	0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27,
	0xd8, 0x01, 0x01, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a,
	0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xee, 0x01, 0x0a, 0x28, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x12, 0xa8, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x4c, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67,
	0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2e, 0x4d, 0x73, 0x67, 0x42,
	0x48, 0xba, 0x48, 0x40, 0xba, 0x01, 0x3a, 0x0a, 0x1d, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11,
	0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f,
	0x27, 0xd8, 0x01, 0x01, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17,
	0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x82, 0x02, 0x0a, 0x31, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0xb3, 0x01,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x2e, 0x4d,
	0x73, 0x67, 0x42, 0x4a, 0xba, 0x48, 0x40, 0xba, 0x01, 0x3a, 0x0a, 0x1d, 0x69, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61,
	0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27,
	0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x01, 0xaa, 0x01, 0x04, 0x08, 0x03, 0x28, 0x02, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3f, 0x0a, 0x18,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x3c, 0x0a,
	0x1b, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01,
	0x01, 0x92, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x49, 0x0a, 0x23, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x70, 0x61, 0x6e, 0x64,
	0x65, 0x64, 0x12, 0x22, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42,
	0x10, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x92, 0x01, 0x02, 0x08, 0x03, 0xaa, 0x01, 0x02, 0x18,
	0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xb0, 0x01, 0x0a, 0x16, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61,
	0x70, 0x12, 0x5e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42,
	0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x9a, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0xb4, 0x02, 0x0a, 0x22, 0x63, 0x6f,
	0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x42, 0x1d, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75,
	0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02, 0x04, 0x42, 0x56, 0x43, 0x43, 0xaa, 0x02, 0x1e,
	0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x73, 0xca, 0x02,
	0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0xe2,
	0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x21, 0x42,
	0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescData = file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDesc
)

func file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDescData
}

var file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_goTypes = []any{
	(*IgnoreEmptyEditionsScalarExplicitPresence)(nil),               // 0: buf.validate.conformance.cases.IgnoreEmptyEditionsScalarExplicitPresence
	(*IgnoreEmptyEditionsScalarExplicitPresenceWithDefault)(nil),    // 1: buf.validate.conformance.cases.IgnoreEmptyEditionsScalarExplicitPresenceWithDefault
	(*IgnoreEmptyEditionsScalarImplicitPresence)(nil),               // 2: buf.validate.conformance.cases.IgnoreEmptyEditionsScalarImplicitPresence
	(*IgnoreEmptyEditionsScalarLegacyRequired)(nil),                 // 3: buf.validate.conformance.cases.IgnoreEmptyEditionsScalarLegacyRequired
	(*IgnoreEmptyEditionsScalarLegacyRequiredWithDefault)(nil),      // 4: buf.validate.conformance.cases.IgnoreEmptyEditionsScalarLegacyRequiredWithDefault
	(*IgnoreEmptyEditionsMessageExplicitPresence)(nil),              // 5: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageExplicitPresence
	(*IgnoreEmptyEditionsMessageExplicitPresenceDelimited)(nil),     // 6: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageExplicitPresenceDelimited
	(*IgnoreEmptyEditionsMessageLegacyRequired)(nil),                // 7: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageLegacyRequired
	(*IgnoreEmptyEditionsMessageLegacyRequiredDelimited)(nil),       // 8: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageLegacyRequiredDelimited
	(*IgnoreEmptyEditionsOneof)(nil),                                // 9: buf.validate.conformance.cases.IgnoreEmptyEditionsOneof
	(*IgnoreEmptyEditionsRepeated)(nil),                             // 10: buf.validate.conformance.cases.IgnoreEmptyEditionsRepeated
	(*IgnoreEmptyEditionsRepeatedExpanded)(nil),                     // 11: buf.validate.conformance.cases.IgnoreEmptyEditionsRepeatedExpanded
	(*IgnoreEmptyEditionsMap)(nil),                                  // 12: buf.validate.conformance.cases.IgnoreEmptyEditionsMap
	(*IgnoreEmptyEditionsMessageExplicitPresence_Msg)(nil),          // 13: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageExplicitPresence.Msg
	(*IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg)(nil), // 14: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageExplicitPresenceDelimited.Msg
	(*IgnoreEmptyEditionsMessageLegacyRequired_Msg)(nil),            // 15: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageLegacyRequired.Msg
	(*IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg)(nil),   // 16: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageLegacyRequiredDelimited.Msg
	nil, // 17: buf.validate.conformance.cases.IgnoreEmptyEditionsMap.ValEntry
}
var file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_depIdxs = []int32{
	13, // 0: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageExplicitPresence.val:type_name -> buf.validate.conformance.cases.IgnoreEmptyEditionsMessageExplicitPresence.Msg
	14, // 1: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageExplicitPresenceDelimited.val:type_name -> buf.validate.conformance.cases.IgnoreEmptyEditionsMessageExplicitPresenceDelimited.Msg
	15, // 2: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageLegacyRequired.val:type_name -> buf.validate.conformance.cases.IgnoreEmptyEditionsMessageLegacyRequired.Msg
	16, // 3: buf.validate.conformance.cases.IgnoreEmptyEditionsMessageLegacyRequiredDelimited.val:type_name -> buf.validate.conformance.cases.IgnoreEmptyEditionsMessageLegacyRequiredDelimited.Msg
	17, // 4: buf.validate.conformance.cases.IgnoreEmptyEditionsMap.val:type_name -> buf.validate.conformance.cases.IgnoreEmptyEditionsMap.ValEntry
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_init() }
func file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_init() {
	if File_buf_validate_conformance_cases_ignore_empty_proto_editions_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsScalarExplicitPresence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsScalarExplicitPresenceWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsScalarImplicitPresence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsScalarLegacyRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsScalarLegacyRequiredWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMessageExplicitPresence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMessageExplicitPresenceDelimited); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMessageLegacyRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMessageLegacyRequiredDelimited); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsOneof); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsRepeated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsRepeatedExpanded); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMessageExplicitPresence_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMessageExplicitPresenceDelimited_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMessageLegacyRequired_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*IgnoreEmptyEditionsMessageLegacyRequiredDelimited_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes[9].OneofWrappers = []any{
		(*IgnoreEmptyEditionsOneof_Val)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_depIdxs,
		MessageInfos:      file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_ignore_empty_proto_editions_proto = out.File
	file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_rawDesc = nil
	file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_goTypes = nil
	file_buf_validate_conformance_cases_ignore_empty_proto_editions_proto_depIdxs = nil
}
