// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/predefined_rules_proto3.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PredefinedEnumRuleProto3_EnumProto3 int32

const (
	PredefinedEnumRuleProto3_ENUM_PROTO3_ZERO_UNSPECIFIED PredefinedEnumRuleProto3_EnumProto3 = 0
	PredefinedEnumRuleProto3_ENUM_PROTO3_ONE              PredefinedEnumRuleProto3_EnumProto3 = 1
)

// Enum value maps for PredefinedEnumRuleProto3_EnumProto3.
var (
	PredefinedEnumRuleProto3_EnumProto3_name = map[int32]string{
		0: "ENUM_PROTO3_ZERO_UNSPECIFIED",
		1: "ENUM_PROTO3_ONE",
	}
	PredefinedEnumRuleProto3_EnumProto3_value = map[string]int32{
		"ENUM_PROTO3_ZERO_UNSPECIFIED": 0,
		"ENUM_PROTO3_ONE":              1,
	}
)

func (x PredefinedEnumRuleProto3_EnumProto3) Enum() *PredefinedEnumRuleProto3_EnumProto3 {
	p := new(PredefinedEnumRuleProto3_EnumProto3)
	*p = x
	return p
}

func (x PredefinedEnumRuleProto3_EnumProto3) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PredefinedEnumRuleProto3_EnumProto3) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_enumTypes[0].Descriptor()
}

func (PredefinedEnumRuleProto3_EnumProto3) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_enumTypes[0]
}

func (x PredefinedEnumRuleProto3_EnumProto3) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PredefinedEnumRuleProto3_EnumProto3.Descriptor instead.
func (PredefinedEnumRuleProto3_EnumProto3) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{15, 0}
}

type PredefinedFloatRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedFloatRuleProto3) Reset() {
	*x = PredefinedFloatRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFloatRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFloatRuleProto3) ProtoMessage() {}

func (x *PredefinedFloatRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFloatRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedFloatRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{0}
}

func (x *PredefinedFloatRuleProto3) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedDoubleRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedDoubleRuleProto3) Reset() {
	*x = PredefinedDoubleRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedDoubleRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedDoubleRuleProto3) ProtoMessage() {}

func (x *PredefinedDoubleRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedDoubleRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedDoubleRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{1}
}

func (x *PredefinedDoubleRuleProto3) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedInt32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedInt32RuleProto3) Reset() {
	*x = PredefinedInt32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedInt32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedInt32RuleProto3) ProtoMessage() {}

func (x *PredefinedInt32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedInt32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedInt32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{2}
}

func (x *PredefinedInt32RuleProto3) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedInt64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedInt64RuleProto3) Reset() {
	*x = PredefinedInt64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedInt64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedInt64RuleProto3) ProtoMessage() {}

func (x *PredefinedInt64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedInt64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedInt64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{3}
}

func (x *PredefinedInt64RuleProto3) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedUInt32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedUInt32RuleProto3) Reset() {
	*x = PredefinedUInt32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedUInt32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedUInt32RuleProto3) ProtoMessage() {}

func (x *PredefinedUInt32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedUInt32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedUInt32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{4}
}

func (x *PredefinedUInt32RuleProto3) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedUInt64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedUInt64RuleProto3) Reset() {
	*x = PredefinedUInt64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedUInt64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedUInt64RuleProto3) ProtoMessage() {}

func (x *PredefinedUInt64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedUInt64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedUInt64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{5}
}

func (x *PredefinedUInt64RuleProto3) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedSInt32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedSInt32RuleProto3) Reset() {
	*x = PredefinedSInt32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSInt32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSInt32RuleProto3) ProtoMessage() {}

func (x *PredefinedSInt32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSInt32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedSInt32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{6}
}

func (x *PredefinedSInt32RuleProto3) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedSInt64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedSInt64RuleProto3) Reset() {
	*x = PredefinedSInt64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSInt64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSInt64RuleProto3) ProtoMessage() {}

func (x *PredefinedSInt64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSInt64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedSInt64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{7}
}

func (x *PredefinedSInt64RuleProto3) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedFixed32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedFixed32RuleProto3) Reset() {
	*x = PredefinedFixed32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFixed32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFixed32RuleProto3) ProtoMessage() {}

func (x *PredefinedFixed32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFixed32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedFixed32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{8}
}

func (x *PredefinedFixed32RuleProto3) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedFixed64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedFixed64RuleProto3) Reset() {
	*x = PredefinedFixed64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFixed64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFixed64RuleProto3) ProtoMessage() {}

func (x *PredefinedFixed64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFixed64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedFixed64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{9}
}

func (x *PredefinedFixed64RuleProto3) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedSFixed32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedSFixed32RuleProto3) Reset() {
	*x = PredefinedSFixed32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSFixed32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSFixed32RuleProto3) ProtoMessage() {}

func (x *PredefinedSFixed32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSFixed32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedSFixed32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{10}
}

func (x *PredefinedSFixed32RuleProto3) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedSFixed64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedSFixed64RuleProto3) Reset() {
	*x = PredefinedSFixed64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSFixed64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSFixed64RuleProto3) ProtoMessage() {}

func (x *PredefinedSFixed64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSFixed64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedSFixed64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{11}
}

func (x *PredefinedSFixed64RuleProto3) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type PredefinedBoolRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val bool `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedBoolRuleProto3) Reset() {
	*x = PredefinedBoolRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedBoolRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedBoolRuleProto3) ProtoMessage() {}

func (x *PredefinedBoolRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedBoolRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedBoolRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{12}
}

func (x *PredefinedBoolRuleProto3) GetVal() bool {
	if x != nil {
		return x.Val
	}
	return false
}

type PredefinedStringRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedStringRuleProto3) Reset() {
	*x = PredefinedStringRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedStringRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedStringRuleProto3) ProtoMessage() {}

func (x *PredefinedStringRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedStringRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedStringRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{13}
}

func (x *PredefinedStringRuleProto3) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type PredefinedBytesRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []byte `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedBytesRuleProto3) Reset() {
	*x = PredefinedBytesRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedBytesRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedBytesRuleProto3) ProtoMessage() {}

func (x *PredefinedBytesRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedBytesRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedBytesRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{14}
}

func (x *PredefinedBytesRuleProto3) GetVal() []byte {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedEnumRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val PredefinedEnumRuleProto3_EnumProto3 `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.PredefinedEnumRuleProto3_EnumProto3" json:"val,omitempty"`
}

func (x *PredefinedEnumRuleProto3) Reset() {
	*x = PredefinedEnumRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedEnumRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedEnumRuleProto3) ProtoMessage() {}

func (x *PredefinedEnumRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedEnumRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedEnumRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{15}
}

func (x *PredefinedEnumRuleProto3) GetVal() PredefinedEnumRuleProto3_EnumProto3 {
	if x != nil {
		return x.Val
	}
	return PredefinedEnumRuleProto3_ENUM_PROTO3_ZERO_UNSPECIFIED
}

type PredefinedMapRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[uint64]uint64 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *PredefinedMapRuleProto3) Reset() {
	*x = PredefinedMapRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedMapRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedMapRuleProto3) ProtoMessage() {}

func (x *PredefinedMapRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedMapRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedMapRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{16}
}

func (x *PredefinedMapRuleProto3) GetVal() map[uint64]uint64 {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []uint64 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedRuleProto3) Reset() {
	*x = PredefinedRepeatedRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedRuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{17}
}

func (x *PredefinedRepeatedRuleProto3) GetVal() []uint64 {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedDurationRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *durationpb.Duration `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedDurationRuleProto3) Reset() {
	*x = PredefinedDurationRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedDurationRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedDurationRuleProto3) ProtoMessage() {}

func (x *PredefinedDurationRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedDurationRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedDurationRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{18}
}

func (x *PredefinedDurationRuleProto3) GetVal() *durationpb.Duration {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedTimestampRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedTimestampRuleProto3) Reset() {
	*x = PredefinedTimestampRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedTimestampRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedTimestampRuleProto3) ProtoMessage() {}

func (x *PredefinedTimestampRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedTimestampRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedTimestampRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{19}
}

func (x *PredefinedTimestampRuleProto3) GetVal() *timestamppb.Timestamp {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedFloatRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.FloatValue `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedFloatRuleProto3) Reset() {
	*x = PredefinedWrappedFloatRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedFloatRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedFloatRuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedFloatRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedFloatRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedFloatRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{20}
}

func (x *PredefinedWrappedFloatRuleProto3) GetVal() *wrapperspb.FloatValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedDoubleRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.DoubleValue `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedDoubleRuleProto3) Reset() {
	*x = PredefinedWrappedDoubleRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedDoubleRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedDoubleRuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedDoubleRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedDoubleRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedDoubleRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{21}
}

func (x *PredefinedWrappedDoubleRuleProto3) GetVal() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedInt32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.Int32Value `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedInt32RuleProto3) Reset() {
	*x = PredefinedWrappedInt32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedInt32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedInt32RuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedInt32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedInt32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedInt32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{22}
}

func (x *PredefinedWrappedInt32RuleProto3) GetVal() *wrapperspb.Int32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedInt64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.Int64Value `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedInt64RuleProto3) Reset() {
	*x = PredefinedWrappedInt64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedInt64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedInt64RuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedInt64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedInt64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedInt64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{23}
}

func (x *PredefinedWrappedInt64RuleProto3) GetVal() *wrapperspb.Int64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedUInt32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedUInt32RuleProto3) Reset() {
	*x = PredefinedWrappedUInt32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedUInt32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedUInt32RuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedUInt32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedUInt32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedUInt32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{24}
}

func (x *PredefinedWrappedUInt32RuleProto3) GetVal() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedUInt64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.UInt64Value `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedUInt64RuleProto3) Reset() {
	*x = PredefinedWrappedUInt64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedUInt64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedUInt64RuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedUInt64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedUInt64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedUInt64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{25}
}

func (x *PredefinedWrappedUInt64RuleProto3) GetVal() *wrapperspb.UInt64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedBoolRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.BoolValue `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedBoolRuleProto3) Reset() {
	*x = PredefinedWrappedBoolRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedBoolRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedBoolRuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedBoolRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedBoolRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedBoolRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{26}
}

func (x *PredefinedWrappedBoolRuleProto3) GetVal() *wrapperspb.BoolValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedStringRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedStringRuleProto3) Reset() {
	*x = PredefinedWrappedStringRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedStringRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedStringRuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedStringRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedStringRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedStringRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{27}
}

func (x *PredefinedWrappedStringRuleProto3) GetVal() *wrapperspb.StringValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedBytesRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.BytesValue `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedWrappedBytesRuleProto3) Reset() {
	*x = PredefinedWrappedBytesRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedBytesRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedBytesRuleProto3) ProtoMessage() {}

func (x *PredefinedWrappedBytesRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedBytesRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedBytesRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{28}
}

func (x *PredefinedWrappedBytesRuleProto3) GetVal() *wrapperspb.BytesValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedFloatRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.FloatValue `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedFloatRuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedFloatRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedFloatRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedFloatRuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedFloatRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedFloatRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedFloatRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{29}
}

func (x *PredefinedRepeatedWrappedFloatRuleProto3) GetVal() []*wrapperspb.FloatValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedDoubleRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.DoubleValue `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedDoubleRuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedDoubleRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedDoubleRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedDoubleRuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedDoubleRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedDoubleRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedDoubleRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{30}
}

func (x *PredefinedRepeatedWrappedDoubleRuleProto3) GetVal() []*wrapperspb.DoubleValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedInt32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.Int32Value `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedInt32RuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedInt32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedInt32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedInt32RuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedInt32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedInt32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedInt32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{31}
}

func (x *PredefinedRepeatedWrappedInt32RuleProto3) GetVal() []*wrapperspb.Int32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedInt64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.Int64Value `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedInt64RuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedInt64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedInt64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedInt64RuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedInt64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedInt64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedInt64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{32}
}

func (x *PredefinedRepeatedWrappedInt64RuleProto3) GetVal() []*wrapperspb.Int64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedUInt32RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.UInt32Value `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedUInt32RuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedUInt32RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedUInt32RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedUInt32RuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedUInt32RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedUInt32RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedUInt32RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{33}
}

func (x *PredefinedRepeatedWrappedUInt32RuleProto3) GetVal() []*wrapperspb.UInt32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedUInt64RuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.UInt64Value `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedUInt64RuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedUInt64RuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedUInt64RuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedUInt64RuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedUInt64RuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedUInt64RuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedUInt64RuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{34}
}

func (x *PredefinedRepeatedWrappedUInt64RuleProto3) GetVal() []*wrapperspb.UInt64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedBoolRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.BoolValue `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedBoolRuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedBoolRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedBoolRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedBoolRuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedBoolRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedBoolRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedBoolRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{35}
}

func (x *PredefinedRepeatedWrappedBoolRuleProto3) GetVal() []*wrapperspb.BoolValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedStringRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.StringValue `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedStringRuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedStringRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedStringRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedStringRuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedStringRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedStringRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedStringRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{36}
}

func (x *PredefinedRepeatedWrappedStringRuleProto3) GetVal() []*wrapperspb.StringValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedBytesRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.BytesValue `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedBytesRuleProto3) Reset() {
	*x = PredefinedRepeatedWrappedBytesRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedBytesRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedBytesRuleProto3) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedBytesRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedBytesRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedBytesRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{37}
}

func (x *PredefinedRepeatedWrappedBytesRuleProto3) GetVal() []*wrapperspb.BytesValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedAndCustomRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32                                 `protobuf:"zigzag32,1,opt,name=a,proto3" json:"a,omitempty"`
	B *PredefinedAndCustomRuleProto3_Nested `protobuf:"bytes,2,opt,name=b,proto3,oneof" json:"b,omitempty"`
}

func (x *PredefinedAndCustomRuleProto3) Reset() {
	*x = PredefinedAndCustomRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedAndCustomRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedAndCustomRuleProto3) ProtoMessage() {}

func (x *PredefinedAndCustomRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedAndCustomRuleProto3.ProtoReflect.Descriptor instead.
func (*PredefinedAndCustomRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{38}
}

func (x *PredefinedAndCustomRuleProto3) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

func (x *PredefinedAndCustomRuleProto3) GetB() *PredefinedAndCustomRuleProto3_Nested {
	if x != nil {
		return x.B
	}
	return nil
}

type StandardPredefinedAndCustomRuleProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32 `protobuf:"zigzag32,1,opt,name=a,proto3" json:"a,omitempty"`
}

func (x *StandardPredefinedAndCustomRuleProto3) Reset() {
	*x = StandardPredefinedAndCustomRuleProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StandardPredefinedAndCustomRuleProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StandardPredefinedAndCustomRuleProto3) ProtoMessage() {}

func (x *StandardPredefinedAndCustomRuleProto3) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StandardPredefinedAndCustomRuleProto3.ProtoReflect.Descriptor instead.
func (*StandardPredefinedAndCustomRuleProto3) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{39}
}

func (x *StandardPredefinedAndCustomRuleProto3) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

// This is a workaround for https://github.com/bufbuild/buf/issues/3306.
// TODO(jchadwick-buf): Remove this when bufbuild/buf#3306 is fixed.
type PredefinedRulesProto3UnusedImportBugWorkaround struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dummy_1 *StandardPredefinedAndCustomRuleProto2      `protobuf:"bytes,1,opt,name=dummy_1,json=dummy1,proto3" json:"dummy_1,omitempty"`
	Dummy_2 *StandardPredefinedAndCustomRuleEdition2023 `protobuf:"bytes,2,opt,name=dummy_2,json=dummy2,proto3" json:"dummy_2,omitempty"`
}

func (x *PredefinedRulesProto3UnusedImportBugWorkaround) Reset() {
	*x = PredefinedRulesProto3UnusedImportBugWorkaround{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRulesProto3UnusedImportBugWorkaround) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRulesProto3UnusedImportBugWorkaround) ProtoMessage() {}

func (x *PredefinedRulesProto3UnusedImportBugWorkaround) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRulesProto3UnusedImportBugWorkaround.ProtoReflect.Descriptor instead.
func (*PredefinedRulesProto3UnusedImportBugWorkaround) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{40}
}

func (x *PredefinedRulesProto3UnusedImportBugWorkaround) GetDummy_1() *StandardPredefinedAndCustomRuleProto2 {
	if x != nil {
		return x.Dummy_1
	}
	return nil
}

func (x *PredefinedRulesProto3UnusedImportBugWorkaround) GetDummy_2() *StandardPredefinedAndCustomRuleEdition2023 {
	if x != nil {
		return x.Dummy_2
	}
	return nil
}

type PredefinedAndCustomRuleProto3_Nested struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C int32 `protobuf:"zigzag32,1,opt,name=c,proto3" json:"c,omitempty"`
}

func (x *PredefinedAndCustomRuleProto3_Nested) Reset() {
	*x = PredefinedAndCustomRuleProto3_Nested{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedAndCustomRuleProto3_Nested) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedAndCustomRuleProto3_Nested) ProtoMessage() {}

func (x *PredefinedAndCustomRuleProto3_Nested) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedAndCustomRuleProto3_Nested.ProtoReflect.Descriptor instead.
func (*PredefinedAndCustomRuleProto3_Nested) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP(), []int{38, 0}
}

func (x *PredefinedAndCustomRuleProto3_Nested) GetC() int32 {
	if x != nil {
		return x.C
	}
	return 0
}

var File_buf_validate_conformance_cases_predefined_rules_proto3_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x73, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x3c,
	0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2f, 0x70,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x62, 0x75,
	0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x3a, 0x0a, 0x19, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1d, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x0a,
	0x06, 0xcd, 0x48, 0x00, 0x00, 0x80, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3f, 0x0a, 0x1a,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x12, 0x0a, 0xc9, 0x48,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x40, 0x0a,
	0x19, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x23, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0xba, 0x48, 0x0e, 0x1a, 0x0c, 0xc8, 0x48,
	0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x42, 0x0a, 0x19, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x25, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x13, 0xba, 0x48, 0x10, 0x22, 0x0e,
	0xd2, 0x48, 0x0b, 0x08, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x08,
	0xba, 0x48, 0x05, 0x2a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a,
	0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x08, 0xba, 0x48, 0x05, 0x32, 0x03, 0xc8,
	0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x11, 0x42, 0x08, 0xba, 0x48, 0x05, 0x3a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x38, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12,
	0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x08, 0xba, 0x48,
	0x05, 0x42, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x39, 0x0a, 0x1b, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x08, 0xba, 0x48, 0x05, 0x4a, 0x03, 0xc8, 0x48,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x39, 0x0a, 0x1b, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x06, 0x42, 0x08, 0xba, 0x48, 0x05, 0x52, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x3a, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x08,
	0xba, 0x48, 0x05, 0x5a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3a, 0x0a,
	0x1c, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1a, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x08, 0xba, 0x48, 0x05, 0x62,
	0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x36, 0x0a, 0x18, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x08, 0xba, 0x48, 0x05, 0x6a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x38, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12,
	0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48,
	0x05, 0x72, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x37, 0x0a, 0x19, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x08, 0xba, 0x48, 0x05, 0x7a, 0x03, 0xc8, 0x48, 0x01, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0xc1, 0x01, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x12, 0x60, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x33, 0x42, 0x09, 0xba, 0x48, 0x06, 0x82, 0x01, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x43, 0x0a, 0x0a, 0x45, 0x6e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x33,
	0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x50, 0x52, 0x4f, 0x54,
	0x4f, 0x33, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x22, 0xb0, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x61, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x12, 0x5d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x40, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x61, 0x70,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x42, 0x09, 0xba, 0x48, 0x06, 0x9a, 0x01, 0x03, 0xd0, 0x48, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3b, 0x0a, 0x1c, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1b, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x09, 0xba, 0x48, 0x06, 0x92, 0x01, 0x03,
	0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x56, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x36, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x09, 0xba, 0x48, 0x06, 0xaa, 0x01, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x58, 0x0a, 0x1d, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x12, 0x37, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x09, 0xba, 0x48, 0x06, 0xb2,
	0x01, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5e, 0x0a, 0x20, 0x50, 0x72,
	0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x3a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x0a, 0x06, 0xcd,
	0x48, 0x00, 0x00, 0x80, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x64, 0x0a, 0x21, 0x50, 0x72,
	0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12,
	0x3f, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x12,
	0x0a, 0xc9, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x64, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x12, 0x40, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x11,
	0xba, 0x48, 0x0e, 0x1a, 0x0c, 0xc8, 0x48, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x66, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x42, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x13, 0xba, 0x48, 0x10, 0x22, 0x0e, 0xca, 0x48, 0x0b, 0x08, 0xfe,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5d,
	0x0a, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x33, 0x12, 0x38, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08,
	0xba, 0x48, 0x05, 0x2a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5d, 0x0a,
	0x21, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x12, 0x38, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba,
	0x48, 0x05, 0x32, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x59, 0x0a, 0x1f,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12,
	0x36, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x6a, 0x03, 0xc8,
	0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5d, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x38, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xc8, 0x48,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5b, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x37, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x7a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x6b, 0x0a, 0x28, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12,
	0x3f, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x92, 0x01,
	0x0a, 0x22, 0x08, 0x0a, 0x06, 0xcd, 0x48, 0x00, 0x00, 0x80, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x71, 0x0a, 0x29, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x44, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x14, 0xba, 0x48, 0x11, 0x92, 0x01, 0x0e,
	0x22, 0x0c, 0x12, 0x0a, 0xc9, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x71, 0x0a, 0x28, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12,
	0x45, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x16, 0xba, 0x48, 0x13, 0x92, 0x01,
	0x10, 0x22, 0x0e, 0x1a, 0x0c, 0xc8, 0x48, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x73, 0x0a, 0x28, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x12, 0x47, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x18, 0xba, 0x48,
	0x15, 0x92, 0x01, 0x12, 0x22, 0x10, 0x22, 0x0e, 0xca, 0x48, 0x0b, 0x08, 0xfe, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6a, 0x0a, 0x29, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x3d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x2a, 0x03, 0xc8,
	0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6a, 0x0a, 0x29, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x12, 0x3d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x32, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x66, 0x0a, 0x27, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64,
	0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x3b,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22,
	0x05, 0x6a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6a, 0x0a, 0x29, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x3d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x72, 0x03, 0xc8,
	0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x68, 0x0a, 0x28, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x33, 0x12, 0x3c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba,
	0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x7a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0xc9, 0x03, 0x0a, 0x1d, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x41, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x33, 0x12, 0x71, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x63,
	0xba, 0x48, 0x60, 0xba, 0x01, 0x58, 0x0a, 0x28, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
	0x1a, 0x2c, 0x74, 0x68, 0x69, 0x73, 0x20, 0x3e, 0x20, 0x32, 0x34, 0x20, 0x3f, 0x20, 0x27, 0x27,
	0x20, 0x3a, 0x20, 0x27, 0x61, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x6e, 0x20, 0x32, 0x34, 0x27, 0x3a, 0x03,
	0xd0, 0x48, 0x01, 0x52, 0x01, 0x61, 0x12, 0xb9, 0x01, 0x0a, 0x01, 0x62, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x42, 0x60, 0xba, 0x48, 0x5d, 0xba, 0x01, 0x5a,
	0x0a, 0x2a, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x1b, 0x62, 0x2e,
	0x63, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x70, 0x6c, 0x65, 0x20, 0x6f, 0x66, 0x20, 0x33, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x2e,
	0x63, 0x20, 0x25, 0x20, 0x33, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x48, 0x00, 0x52, 0x01, 0x62, 0x88,
	0x01, 0x01, 0x1a, 0x73, 0x0a, 0x06, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x69, 0x0a, 0x01,
	0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x5b, 0xba, 0x48, 0x58, 0xba, 0x01, 0x50, 0x0a,
	0x28, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x1a, 0x24, 0x74, 0x68, 0x69, 0x73, 0x20,
	0x3e, 0x20, 0x30, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x20, 0x3a, 0x20, 0x27, 0x63, 0x20, 0x6d, 0x75,
	0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x27, 0x3a,
	0x03, 0xd0, 0x48, 0x01, 0x52, 0x01, 0x63, 0x42, 0x04, 0x0a, 0x02, 0x5f, 0x62, 0x22, 0xa5, 0x01,
	0x0a, 0x25, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x12, 0x7c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x11, 0x42, 0x6e, 0xba, 0x48, 0x6b, 0xba, 0x01, 0x61, 0x0a, 0x31, 0x73, 0x74, 0x61, 0x6e,
	0x64, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x73, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x1a, 0x2c, 0x74,
	0x68, 0x69, 0x73, 0x20, 0x3e, 0x20, 0x32, 0x34, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x20, 0x3a, 0x20,
	0x27, 0x61, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x6e, 0x20, 0x32, 0x34, 0x27, 0x3a, 0x05, 0xc8, 0x48, 0x01,
	0x10, 0x38, 0x52, 0x01, 0x61, 0x22, 0xf5, 0x01, 0x0a, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x55,
	0x6e, 0x75, 0x73, 0x65, 0x64, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x75, 0x67, 0x57, 0x6f,
	0x72, 0x6b, 0x61, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x5e, 0x0a, 0x07, 0x64, 0x75, 0x6d, 0x6d,
	0x79, 0x5f, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x6e, 0x64,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e, 0x64,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x52, 0x06, 0x64, 0x75, 0x6d, 0x6d, 0x79, 0x31, 0x12, 0x63, 0x0a, 0x07, 0x64, 0x75, 0x6d, 0x6d,
	0x79, 0x5f, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x6e, 0x64,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e, 0x64,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x32, 0x30, 0x32, 0x33, 0x52, 0x06, 0x64, 0x75, 0x6d, 0x6d, 0x79, 0x32, 0x42, 0xb1, 0x02,
	0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x42, 0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x50, 0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62,
	0x75, 0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02, 0x04, 0x42, 0x56, 0x43, 0x43, 0xaa, 0x02,
	0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x73, 0xca,
	0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73,
	0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c,
	0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65,
	0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x21,
	0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65,
	0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescData = file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDesc
)

func file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDescData
}

var file_buf_validate_conformance_cases_predefined_rules_proto3_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes = make([]protoimpl.MessageInfo, 43)
var file_buf_validate_conformance_cases_predefined_rules_proto3_proto_goTypes = []any{
	(PredefinedEnumRuleProto3_EnumProto3)(0),               // 0: buf.validate.conformance.cases.PredefinedEnumRuleProto3.EnumProto3
	(*PredefinedFloatRuleProto3)(nil),                      // 1: buf.validate.conformance.cases.PredefinedFloatRuleProto3
	(*PredefinedDoubleRuleProto3)(nil),                     // 2: buf.validate.conformance.cases.PredefinedDoubleRuleProto3
	(*PredefinedInt32RuleProto3)(nil),                      // 3: buf.validate.conformance.cases.PredefinedInt32RuleProto3
	(*PredefinedInt64RuleProto3)(nil),                      // 4: buf.validate.conformance.cases.PredefinedInt64RuleProto3
	(*PredefinedUInt32RuleProto3)(nil),                     // 5: buf.validate.conformance.cases.PredefinedUInt32RuleProto3
	(*PredefinedUInt64RuleProto3)(nil),                     // 6: buf.validate.conformance.cases.PredefinedUInt64RuleProto3
	(*PredefinedSInt32RuleProto3)(nil),                     // 7: buf.validate.conformance.cases.PredefinedSInt32RuleProto3
	(*PredefinedSInt64RuleProto3)(nil),                     // 8: buf.validate.conformance.cases.PredefinedSInt64RuleProto3
	(*PredefinedFixed32RuleProto3)(nil),                    // 9: buf.validate.conformance.cases.PredefinedFixed32RuleProto3
	(*PredefinedFixed64RuleProto3)(nil),                    // 10: buf.validate.conformance.cases.PredefinedFixed64RuleProto3
	(*PredefinedSFixed32RuleProto3)(nil),                   // 11: buf.validate.conformance.cases.PredefinedSFixed32RuleProto3
	(*PredefinedSFixed64RuleProto3)(nil),                   // 12: buf.validate.conformance.cases.PredefinedSFixed64RuleProto3
	(*PredefinedBoolRuleProto3)(nil),                       // 13: buf.validate.conformance.cases.PredefinedBoolRuleProto3
	(*PredefinedStringRuleProto3)(nil),                     // 14: buf.validate.conformance.cases.PredefinedStringRuleProto3
	(*PredefinedBytesRuleProto3)(nil),                      // 15: buf.validate.conformance.cases.PredefinedBytesRuleProto3
	(*PredefinedEnumRuleProto3)(nil),                       // 16: buf.validate.conformance.cases.PredefinedEnumRuleProto3
	(*PredefinedMapRuleProto3)(nil),                        // 17: buf.validate.conformance.cases.PredefinedMapRuleProto3
	(*PredefinedRepeatedRuleProto3)(nil),                   // 18: buf.validate.conformance.cases.PredefinedRepeatedRuleProto3
	(*PredefinedDurationRuleProto3)(nil),                   // 19: buf.validate.conformance.cases.PredefinedDurationRuleProto3
	(*PredefinedTimestampRuleProto3)(nil),                  // 20: buf.validate.conformance.cases.PredefinedTimestampRuleProto3
	(*PredefinedWrappedFloatRuleProto3)(nil),               // 21: buf.validate.conformance.cases.PredefinedWrappedFloatRuleProto3
	(*PredefinedWrappedDoubleRuleProto3)(nil),              // 22: buf.validate.conformance.cases.PredefinedWrappedDoubleRuleProto3
	(*PredefinedWrappedInt32RuleProto3)(nil),               // 23: buf.validate.conformance.cases.PredefinedWrappedInt32RuleProto3
	(*PredefinedWrappedInt64RuleProto3)(nil),               // 24: buf.validate.conformance.cases.PredefinedWrappedInt64RuleProto3
	(*PredefinedWrappedUInt32RuleProto3)(nil),              // 25: buf.validate.conformance.cases.PredefinedWrappedUInt32RuleProto3
	(*PredefinedWrappedUInt64RuleProto3)(nil),              // 26: buf.validate.conformance.cases.PredefinedWrappedUInt64RuleProto3
	(*PredefinedWrappedBoolRuleProto3)(nil),                // 27: buf.validate.conformance.cases.PredefinedWrappedBoolRuleProto3
	(*PredefinedWrappedStringRuleProto3)(nil),              // 28: buf.validate.conformance.cases.PredefinedWrappedStringRuleProto3
	(*PredefinedWrappedBytesRuleProto3)(nil),               // 29: buf.validate.conformance.cases.PredefinedWrappedBytesRuleProto3
	(*PredefinedRepeatedWrappedFloatRuleProto3)(nil),       // 30: buf.validate.conformance.cases.PredefinedRepeatedWrappedFloatRuleProto3
	(*PredefinedRepeatedWrappedDoubleRuleProto3)(nil),      // 31: buf.validate.conformance.cases.PredefinedRepeatedWrappedDoubleRuleProto3
	(*PredefinedRepeatedWrappedInt32RuleProto3)(nil),       // 32: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt32RuleProto3
	(*PredefinedRepeatedWrappedInt64RuleProto3)(nil),       // 33: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt64RuleProto3
	(*PredefinedRepeatedWrappedUInt32RuleProto3)(nil),      // 34: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt32RuleProto3
	(*PredefinedRepeatedWrappedUInt64RuleProto3)(nil),      // 35: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt64RuleProto3
	(*PredefinedRepeatedWrappedBoolRuleProto3)(nil),        // 36: buf.validate.conformance.cases.PredefinedRepeatedWrappedBoolRuleProto3
	(*PredefinedRepeatedWrappedStringRuleProto3)(nil),      // 37: buf.validate.conformance.cases.PredefinedRepeatedWrappedStringRuleProto3
	(*PredefinedRepeatedWrappedBytesRuleProto3)(nil),       // 38: buf.validate.conformance.cases.PredefinedRepeatedWrappedBytesRuleProto3
	(*PredefinedAndCustomRuleProto3)(nil),                  // 39: buf.validate.conformance.cases.PredefinedAndCustomRuleProto3
	(*StandardPredefinedAndCustomRuleProto3)(nil),          // 40: buf.validate.conformance.cases.StandardPredefinedAndCustomRuleProto3
	(*PredefinedRulesProto3UnusedImportBugWorkaround)(nil), // 41: buf.validate.conformance.cases.PredefinedRulesProto3UnusedImportBugWorkaround
	nil, // 42: buf.validate.conformance.cases.PredefinedMapRuleProto3.ValEntry
	(*PredefinedAndCustomRuleProto3_Nested)(nil),       // 43: buf.validate.conformance.cases.PredefinedAndCustomRuleProto3.Nested
	(*durationpb.Duration)(nil),                        // 44: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),                      // 45: google.protobuf.Timestamp
	(*wrapperspb.FloatValue)(nil),                      // 46: google.protobuf.FloatValue
	(*wrapperspb.DoubleValue)(nil),                     // 47: google.protobuf.DoubleValue
	(*wrapperspb.Int32Value)(nil),                      // 48: google.protobuf.Int32Value
	(*wrapperspb.Int64Value)(nil),                      // 49: google.protobuf.Int64Value
	(*wrapperspb.UInt32Value)(nil),                     // 50: google.protobuf.UInt32Value
	(*wrapperspb.UInt64Value)(nil),                     // 51: google.protobuf.UInt64Value
	(*wrapperspb.BoolValue)(nil),                       // 52: google.protobuf.BoolValue
	(*wrapperspb.StringValue)(nil),                     // 53: google.protobuf.StringValue
	(*wrapperspb.BytesValue)(nil),                      // 54: google.protobuf.BytesValue
	(*StandardPredefinedAndCustomRuleProto2)(nil),      // 55: buf.validate.conformance.cases.StandardPredefinedAndCustomRuleProto2
	(*StandardPredefinedAndCustomRuleEdition2023)(nil), // 56: buf.validate.conformance.cases.StandardPredefinedAndCustomRuleEdition2023
}
var file_buf_validate_conformance_cases_predefined_rules_proto3_proto_depIdxs = []int32{
	0,  // 0: buf.validate.conformance.cases.PredefinedEnumRuleProto3.val:type_name -> buf.validate.conformance.cases.PredefinedEnumRuleProto3.EnumProto3
	42, // 1: buf.validate.conformance.cases.PredefinedMapRuleProto3.val:type_name -> buf.validate.conformance.cases.PredefinedMapRuleProto3.ValEntry
	44, // 2: buf.validate.conformance.cases.PredefinedDurationRuleProto3.val:type_name -> google.protobuf.Duration
	45, // 3: buf.validate.conformance.cases.PredefinedTimestampRuleProto3.val:type_name -> google.protobuf.Timestamp
	46, // 4: buf.validate.conformance.cases.PredefinedWrappedFloatRuleProto3.val:type_name -> google.protobuf.FloatValue
	47, // 5: buf.validate.conformance.cases.PredefinedWrappedDoubleRuleProto3.val:type_name -> google.protobuf.DoubleValue
	48, // 6: buf.validate.conformance.cases.PredefinedWrappedInt32RuleProto3.val:type_name -> google.protobuf.Int32Value
	49, // 7: buf.validate.conformance.cases.PredefinedWrappedInt64RuleProto3.val:type_name -> google.protobuf.Int64Value
	50, // 8: buf.validate.conformance.cases.PredefinedWrappedUInt32RuleProto3.val:type_name -> google.protobuf.UInt32Value
	51, // 9: buf.validate.conformance.cases.PredefinedWrappedUInt64RuleProto3.val:type_name -> google.protobuf.UInt64Value
	52, // 10: buf.validate.conformance.cases.PredefinedWrappedBoolRuleProto3.val:type_name -> google.protobuf.BoolValue
	53, // 11: buf.validate.conformance.cases.PredefinedWrappedStringRuleProto3.val:type_name -> google.protobuf.StringValue
	54, // 12: buf.validate.conformance.cases.PredefinedWrappedBytesRuleProto3.val:type_name -> google.protobuf.BytesValue
	46, // 13: buf.validate.conformance.cases.PredefinedRepeatedWrappedFloatRuleProto3.val:type_name -> google.protobuf.FloatValue
	47, // 14: buf.validate.conformance.cases.PredefinedRepeatedWrappedDoubleRuleProto3.val:type_name -> google.protobuf.DoubleValue
	48, // 15: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt32RuleProto3.val:type_name -> google.protobuf.Int32Value
	49, // 16: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt64RuleProto3.val:type_name -> google.protobuf.Int64Value
	50, // 17: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt32RuleProto3.val:type_name -> google.protobuf.UInt32Value
	51, // 18: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt64RuleProto3.val:type_name -> google.protobuf.UInt64Value
	52, // 19: buf.validate.conformance.cases.PredefinedRepeatedWrappedBoolRuleProto3.val:type_name -> google.protobuf.BoolValue
	53, // 20: buf.validate.conformance.cases.PredefinedRepeatedWrappedStringRuleProto3.val:type_name -> google.protobuf.StringValue
	54, // 21: buf.validate.conformance.cases.PredefinedRepeatedWrappedBytesRuleProto3.val:type_name -> google.protobuf.BytesValue
	43, // 22: buf.validate.conformance.cases.PredefinedAndCustomRuleProto3.b:type_name -> buf.validate.conformance.cases.PredefinedAndCustomRuleProto3.Nested
	55, // 23: buf.validate.conformance.cases.PredefinedRulesProto3UnusedImportBugWorkaround.dummy_1:type_name -> buf.validate.conformance.cases.StandardPredefinedAndCustomRuleProto2
	56, // 24: buf.validate.conformance.cases.PredefinedRulesProto3UnusedImportBugWorkaround.dummy_2:type_name -> buf.validate.conformance.cases.StandardPredefinedAndCustomRuleEdition2023
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_predefined_rules_proto3_proto_init() }
func file_buf_validate_conformance_cases_predefined_rules_proto3_proto_init() {
	if File_buf_validate_conformance_cases_predefined_rules_proto3_proto != nil {
		return
	}
	file_buf_validate_conformance_cases_predefined_rules_proto2_proto_init()
	file_buf_validate_conformance_cases_predefined_rules_proto_editions_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFloatRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedDoubleRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedInt32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedInt64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedUInt32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedUInt64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSInt32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSInt64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFixed32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFixed64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSFixed32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSFixed64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedBoolRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedStringRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedBytesRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedEnumRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedMapRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedDurationRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedTimestampRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedFloatRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedDoubleRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedInt32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedInt64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedUInt32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedUInt64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedBoolRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedStringRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedBytesRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedFloatRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedDoubleRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedInt32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedInt64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedUInt32RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedUInt64RuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedBoolRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedStringRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedBytesRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedAndCustomRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*StandardPredefinedAndCustomRuleProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRulesProto3UnusedImportBugWorkaround); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedAndCustomRuleProto3_Nested); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes[38].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   43,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_predefined_rules_proto3_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_predefined_rules_proto3_proto_depIdxs,
		EnumInfos:         file_buf_validate_conformance_cases_predefined_rules_proto3_proto_enumTypes,
		MessageInfos:      file_buf_validate_conformance_cases_predefined_rules_proto3_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_predefined_rules_proto3_proto = out.File
	file_buf_validate_conformance_cases_predefined_rules_proto3_proto_rawDesc = nil
	file_buf_validate_conformance_cases_predefined_rules_proto3_proto_goTypes = nil
	file_buf_validate_conformance_cases_predefined_rules_proto3_proto_depIdxs = nil
}
