// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/ignore_proto2.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Proto2ScalarOptionalIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2ScalarOptionalIgnoreUnspecified) Reset() {
	*x = Proto2ScalarOptionalIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarOptionalIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarOptionalIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2ScalarOptionalIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarOptionalIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2ScalarOptionalIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{0}
}

func (x *Proto2ScalarOptionalIgnoreUnspecified) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto2ScalarOptionalIgnoreUnspecifiedWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,def=-42" json:"val,omitempty"`
}

// Default values for Proto2ScalarOptionalIgnoreUnspecifiedWithDefault fields.
const (
	Default_Proto2ScalarOptionalIgnoreUnspecifiedWithDefault_Val = int32(-42)
)

func (x *Proto2ScalarOptionalIgnoreUnspecifiedWithDefault) Reset() {
	*x = Proto2ScalarOptionalIgnoreUnspecifiedWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarOptionalIgnoreUnspecifiedWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarOptionalIgnoreUnspecifiedWithDefault) ProtoMessage() {}

func (x *Proto2ScalarOptionalIgnoreUnspecifiedWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarOptionalIgnoreUnspecifiedWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2ScalarOptionalIgnoreUnspecifiedWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{1}
}

func (x *Proto2ScalarOptionalIgnoreUnspecifiedWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_Proto2ScalarOptionalIgnoreUnspecifiedWithDefault_Val
}

type Proto2ScalarOptionalIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2ScalarOptionalIgnoreEmpty) Reset() {
	*x = Proto2ScalarOptionalIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarOptionalIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarOptionalIgnoreEmpty) ProtoMessage() {}

func (x *Proto2ScalarOptionalIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarOptionalIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2ScalarOptionalIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{2}
}

func (x *Proto2ScalarOptionalIgnoreEmpty) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto2ScalarOptionalIgnoreEmptyWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,def=-42" json:"val,omitempty"`
}

// Default values for Proto2ScalarOptionalIgnoreEmptyWithDefault fields.
const (
	Default_Proto2ScalarOptionalIgnoreEmptyWithDefault_Val = int32(-42)
)

func (x *Proto2ScalarOptionalIgnoreEmptyWithDefault) Reset() {
	*x = Proto2ScalarOptionalIgnoreEmptyWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarOptionalIgnoreEmptyWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarOptionalIgnoreEmptyWithDefault) ProtoMessage() {}

func (x *Proto2ScalarOptionalIgnoreEmptyWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarOptionalIgnoreEmptyWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2ScalarOptionalIgnoreEmptyWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{3}
}

func (x *Proto2ScalarOptionalIgnoreEmptyWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_Proto2ScalarOptionalIgnoreEmptyWithDefault_Val
}

type Proto2ScalarOptionalIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2ScalarOptionalIgnoreDefault) Reset() {
	*x = Proto2ScalarOptionalIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarOptionalIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarOptionalIgnoreDefault) ProtoMessage() {}

func (x *Proto2ScalarOptionalIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarOptionalIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2ScalarOptionalIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{4}
}

func (x *Proto2ScalarOptionalIgnoreDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto2ScalarOptionalIgnoreDefaultWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,def=-42" json:"val,omitempty"`
}

// Default values for Proto2ScalarOptionalIgnoreDefaultWithDefault fields.
const (
	Default_Proto2ScalarOptionalIgnoreDefaultWithDefault_Val = int32(-42)
)

func (x *Proto2ScalarOptionalIgnoreDefaultWithDefault) Reset() {
	*x = Proto2ScalarOptionalIgnoreDefaultWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarOptionalIgnoreDefaultWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarOptionalIgnoreDefaultWithDefault) ProtoMessage() {}

func (x *Proto2ScalarOptionalIgnoreDefaultWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarOptionalIgnoreDefaultWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2ScalarOptionalIgnoreDefaultWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{5}
}

func (x *Proto2ScalarOptionalIgnoreDefaultWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_Proto2ScalarOptionalIgnoreDefaultWithDefault_Val
}

type Proto2ScalarRequiredIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val" json:"val,omitempty"`
}

func (x *Proto2ScalarRequiredIgnoreUnspecified) Reset() {
	*x = Proto2ScalarRequiredIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarRequiredIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarRequiredIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2ScalarRequiredIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarRequiredIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2ScalarRequiredIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{6}
}

func (x *Proto2ScalarRequiredIgnoreUnspecified) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto2ScalarRequiredIgnoreUnspecifiedWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val,def=-42" json:"val,omitempty"`
}

// Default values for Proto2ScalarRequiredIgnoreUnspecifiedWithDefault fields.
const (
	Default_Proto2ScalarRequiredIgnoreUnspecifiedWithDefault_Val = int32(-42)
)

func (x *Proto2ScalarRequiredIgnoreUnspecifiedWithDefault) Reset() {
	*x = Proto2ScalarRequiredIgnoreUnspecifiedWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarRequiredIgnoreUnspecifiedWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarRequiredIgnoreUnspecifiedWithDefault) ProtoMessage() {}

func (x *Proto2ScalarRequiredIgnoreUnspecifiedWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarRequiredIgnoreUnspecifiedWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2ScalarRequiredIgnoreUnspecifiedWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{7}
}

func (x *Proto2ScalarRequiredIgnoreUnspecifiedWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_Proto2ScalarRequiredIgnoreUnspecifiedWithDefault_Val
}

type Proto2ScalarRequiredIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val" json:"val,omitempty"`
}

func (x *Proto2ScalarRequiredIgnoreEmpty) Reset() {
	*x = Proto2ScalarRequiredIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarRequiredIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarRequiredIgnoreEmpty) ProtoMessage() {}

func (x *Proto2ScalarRequiredIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarRequiredIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2ScalarRequiredIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{8}
}

func (x *Proto2ScalarRequiredIgnoreEmpty) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto2ScalarRequiredIgnoreEmptyWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val,def=-42" json:"val,omitempty"`
}

// Default values for Proto2ScalarRequiredIgnoreEmptyWithDefault fields.
const (
	Default_Proto2ScalarRequiredIgnoreEmptyWithDefault_Val = int32(-42)
)

func (x *Proto2ScalarRequiredIgnoreEmptyWithDefault) Reset() {
	*x = Proto2ScalarRequiredIgnoreEmptyWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarRequiredIgnoreEmptyWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarRequiredIgnoreEmptyWithDefault) ProtoMessage() {}

func (x *Proto2ScalarRequiredIgnoreEmptyWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarRequiredIgnoreEmptyWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2ScalarRequiredIgnoreEmptyWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{9}
}

func (x *Proto2ScalarRequiredIgnoreEmptyWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_Proto2ScalarRequiredIgnoreEmptyWithDefault_Val
}

type Proto2ScalarRequiredIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val" json:"val,omitempty"`
}

func (x *Proto2ScalarRequiredIgnoreDefault) Reset() {
	*x = Proto2ScalarRequiredIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarRequiredIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarRequiredIgnoreDefault) ProtoMessage() {}

func (x *Proto2ScalarRequiredIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarRequiredIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2ScalarRequiredIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{10}
}

func (x *Proto2ScalarRequiredIgnoreDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto2ScalarRequiredIgnoreDefaultWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val,def=-42" json:"val,omitempty"`
}

// Default values for Proto2ScalarRequiredIgnoreDefaultWithDefault fields.
const (
	Default_Proto2ScalarRequiredIgnoreDefaultWithDefault_Val = int32(-42)
)

func (x *Proto2ScalarRequiredIgnoreDefaultWithDefault) Reset() {
	*x = Proto2ScalarRequiredIgnoreDefaultWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2ScalarRequiredIgnoreDefaultWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2ScalarRequiredIgnoreDefaultWithDefault) ProtoMessage() {}

func (x *Proto2ScalarRequiredIgnoreDefaultWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2ScalarRequiredIgnoreDefaultWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2ScalarRequiredIgnoreDefaultWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{11}
}

func (x *Proto2ScalarRequiredIgnoreDefaultWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_Proto2ScalarRequiredIgnoreDefaultWithDefault_Val
}

type Proto2MessageOptionalIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto2MessageOptionalIgnoreUnspecified_Msg `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageOptionalIgnoreUnspecified) Reset() {
	*x = Proto2MessageOptionalIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageOptionalIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageOptionalIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2MessageOptionalIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageOptionalIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2MessageOptionalIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{12}
}

func (x *Proto2MessageOptionalIgnoreUnspecified) GetVal() *Proto2MessageOptionalIgnoreUnspecified_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MessageOptionalIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto2MessageOptionalIgnoreEmpty_Msg `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageOptionalIgnoreEmpty) Reset() {
	*x = Proto2MessageOptionalIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageOptionalIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageOptionalIgnoreEmpty) ProtoMessage() {}

func (x *Proto2MessageOptionalIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageOptionalIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2MessageOptionalIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{13}
}

func (x *Proto2MessageOptionalIgnoreEmpty) GetVal() *Proto2MessageOptionalIgnoreEmpty_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MessageOptionalIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto2MessageOptionalIgnoreDefault_Msg `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageOptionalIgnoreDefault) Reset() {
	*x = Proto2MessageOptionalIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageOptionalIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageOptionalIgnoreDefault) ProtoMessage() {}

func (x *Proto2MessageOptionalIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageOptionalIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2MessageOptionalIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{14}
}

func (x *Proto2MessageOptionalIgnoreDefault) GetVal() *Proto2MessageOptionalIgnoreDefault_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MessageRequiredIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto2MessageRequiredIgnoreUnspecified_Msg `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageRequiredIgnoreUnspecified) Reset() {
	*x = Proto2MessageRequiredIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageRequiredIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageRequiredIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2MessageRequiredIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageRequiredIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2MessageRequiredIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{15}
}

func (x *Proto2MessageRequiredIgnoreUnspecified) GetVal() *Proto2MessageRequiredIgnoreUnspecified_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MessageRequiredIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto2MessageRequiredIgnoreEmpty_Msg `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageRequiredIgnoreEmpty) Reset() {
	*x = Proto2MessageRequiredIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageRequiredIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageRequiredIgnoreEmpty) ProtoMessage() {}

func (x *Proto2MessageRequiredIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageRequiredIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2MessageRequiredIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{16}
}

func (x *Proto2MessageRequiredIgnoreEmpty) GetVal() *Proto2MessageRequiredIgnoreEmpty_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MessageRequiredIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto2MessageRequiredIgnoreDefault_Msg `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageRequiredIgnoreDefault) Reset() {
	*x = Proto2MessageRequiredIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageRequiredIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageRequiredIgnoreDefault) ProtoMessage() {}

func (x *Proto2MessageRequiredIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageRequiredIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2MessageRequiredIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{17}
}

func (x *Proto2MessageRequiredIgnoreDefault) GetVal() *Proto2MessageRequiredIgnoreDefault_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2OneofIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto2OneofIgnoreUnspecified_Val
	O isProto2OneofIgnoreUnspecified_O `protobuf_oneof:"o"`
}

func (x *Proto2OneofIgnoreUnspecified) Reset() {
	*x = Proto2OneofIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2OneofIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2OneofIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2OneofIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2OneofIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2OneofIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{18}
}

func (m *Proto2OneofIgnoreUnspecified) GetO() isProto2OneofIgnoreUnspecified_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto2OneofIgnoreUnspecified) GetVal() int32 {
	if x, ok := x.GetO().(*Proto2OneofIgnoreUnspecified_Val); ok {
		return x.Val
	}
	return 0
}

type isProto2OneofIgnoreUnspecified_O interface {
	isProto2OneofIgnoreUnspecified_O()
}

type Proto2OneofIgnoreUnspecified_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof"`
}

func (*Proto2OneofIgnoreUnspecified_Val) isProto2OneofIgnoreUnspecified_O() {}

type Proto2OneofIgnoreUnspecifiedWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto2OneofIgnoreUnspecifiedWithDefault_Val
	O isProto2OneofIgnoreUnspecifiedWithDefault_O `protobuf_oneof:"o"`
}

// Default values for Proto2OneofIgnoreUnspecifiedWithDefault fields.
const (
	Default_Proto2OneofIgnoreUnspecifiedWithDefault_Val = int32(-42)
)

func (x *Proto2OneofIgnoreUnspecifiedWithDefault) Reset() {
	*x = Proto2OneofIgnoreUnspecifiedWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2OneofIgnoreUnspecifiedWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2OneofIgnoreUnspecifiedWithDefault) ProtoMessage() {}

func (x *Proto2OneofIgnoreUnspecifiedWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2OneofIgnoreUnspecifiedWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2OneofIgnoreUnspecifiedWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{19}
}

func (m *Proto2OneofIgnoreUnspecifiedWithDefault) GetO() isProto2OneofIgnoreUnspecifiedWithDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto2OneofIgnoreUnspecifiedWithDefault) GetVal() int32 {
	if x, ok := x.GetO().(*Proto2OneofIgnoreUnspecifiedWithDefault_Val); ok {
		return x.Val
	}
	return Default_Proto2OneofIgnoreUnspecifiedWithDefault_Val
}

type isProto2OneofIgnoreUnspecifiedWithDefault_O interface {
	isProto2OneofIgnoreUnspecifiedWithDefault_O()
}

type Proto2OneofIgnoreUnspecifiedWithDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof,def=-42"`
}

func (*Proto2OneofIgnoreUnspecifiedWithDefault_Val) isProto2OneofIgnoreUnspecifiedWithDefault_O() {}

type Proto2OneofIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto2OneofIgnoreEmpty_Val
	O isProto2OneofIgnoreEmpty_O `protobuf_oneof:"o"`
}

func (x *Proto2OneofIgnoreEmpty) Reset() {
	*x = Proto2OneofIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2OneofIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2OneofIgnoreEmpty) ProtoMessage() {}

func (x *Proto2OneofIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2OneofIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2OneofIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{20}
}

func (m *Proto2OneofIgnoreEmpty) GetO() isProto2OneofIgnoreEmpty_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto2OneofIgnoreEmpty) GetVal() int32 {
	if x, ok := x.GetO().(*Proto2OneofIgnoreEmpty_Val); ok {
		return x.Val
	}
	return 0
}

type isProto2OneofIgnoreEmpty_O interface {
	isProto2OneofIgnoreEmpty_O()
}

type Proto2OneofIgnoreEmpty_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof"`
}

func (*Proto2OneofIgnoreEmpty_Val) isProto2OneofIgnoreEmpty_O() {}

type Proto2OneofIgnoreEmptyWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto2OneofIgnoreEmptyWithDefault_Val
	O isProto2OneofIgnoreEmptyWithDefault_O `protobuf_oneof:"o"`
}

// Default values for Proto2OneofIgnoreEmptyWithDefault fields.
const (
	Default_Proto2OneofIgnoreEmptyWithDefault_Val = int32(-42)
)

func (x *Proto2OneofIgnoreEmptyWithDefault) Reset() {
	*x = Proto2OneofIgnoreEmptyWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2OneofIgnoreEmptyWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2OneofIgnoreEmptyWithDefault) ProtoMessage() {}

func (x *Proto2OneofIgnoreEmptyWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2OneofIgnoreEmptyWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2OneofIgnoreEmptyWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{21}
}

func (m *Proto2OneofIgnoreEmptyWithDefault) GetO() isProto2OneofIgnoreEmptyWithDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto2OneofIgnoreEmptyWithDefault) GetVal() int32 {
	if x, ok := x.GetO().(*Proto2OneofIgnoreEmptyWithDefault_Val); ok {
		return x.Val
	}
	return Default_Proto2OneofIgnoreEmptyWithDefault_Val
}

type isProto2OneofIgnoreEmptyWithDefault_O interface {
	isProto2OneofIgnoreEmptyWithDefault_O()
}

type Proto2OneofIgnoreEmptyWithDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof,def=-42"`
}

func (*Proto2OneofIgnoreEmptyWithDefault_Val) isProto2OneofIgnoreEmptyWithDefault_O() {}

type Proto2OneofIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto2OneofIgnoreDefault_Val
	O isProto2OneofIgnoreDefault_O `protobuf_oneof:"o"`
}

func (x *Proto2OneofIgnoreDefault) Reset() {
	*x = Proto2OneofIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2OneofIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2OneofIgnoreDefault) ProtoMessage() {}

func (x *Proto2OneofIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2OneofIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2OneofIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{22}
}

func (m *Proto2OneofIgnoreDefault) GetO() isProto2OneofIgnoreDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto2OneofIgnoreDefault) GetVal() int32 {
	if x, ok := x.GetO().(*Proto2OneofIgnoreDefault_Val); ok {
		return x.Val
	}
	return 0
}

type isProto2OneofIgnoreDefault_O interface {
	isProto2OneofIgnoreDefault_O()
}

type Proto2OneofIgnoreDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof"`
}

func (*Proto2OneofIgnoreDefault_Val) isProto2OneofIgnoreDefault_O() {}

type Proto2OneofIgnoreDefaultWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto2OneofIgnoreDefaultWithDefault_Val
	O isProto2OneofIgnoreDefaultWithDefault_O `protobuf_oneof:"o"`
}

// Default values for Proto2OneofIgnoreDefaultWithDefault fields.
const (
	Default_Proto2OneofIgnoreDefaultWithDefault_Val = int32(-42)
)

func (x *Proto2OneofIgnoreDefaultWithDefault) Reset() {
	*x = Proto2OneofIgnoreDefaultWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2OneofIgnoreDefaultWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2OneofIgnoreDefaultWithDefault) ProtoMessage() {}

func (x *Proto2OneofIgnoreDefaultWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2OneofIgnoreDefaultWithDefault.ProtoReflect.Descriptor instead.
func (*Proto2OneofIgnoreDefaultWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{23}
}

func (m *Proto2OneofIgnoreDefaultWithDefault) GetO() isProto2OneofIgnoreDefaultWithDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto2OneofIgnoreDefaultWithDefault) GetVal() int32 {
	if x, ok := x.GetO().(*Proto2OneofIgnoreDefaultWithDefault_Val); ok {
		return x.Val
	}
	return Default_Proto2OneofIgnoreDefaultWithDefault_Val
}

type isProto2OneofIgnoreDefaultWithDefault_O interface {
	isProto2OneofIgnoreDefaultWithDefault_O()
}

type Proto2OneofIgnoreDefaultWithDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof,def=-42"`
}

func (*Proto2OneofIgnoreDefaultWithDefault_Val) isProto2OneofIgnoreDefaultWithDefault_O() {}

type Proto2RepeatedIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *Proto2RepeatedIgnoreUnspecified) Reset() {
	*x = Proto2RepeatedIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2RepeatedIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2RepeatedIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2RepeatedIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2RepeatedIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2RepeatedIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{24}
}

func (x *Proto2RepeatedIgnoreUnspecified) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2RepeatedIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *Proto2RepeatedIgnoreEmpty) Reset() {
	*x = Proto2RepeatedIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2RepeatedIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2RepeatedIgnoreEmpty) ProtoMessage() {}

func (x *Proto2RepeatedIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2RepeatedIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2RepeatedIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{25}
}

func (x *Proto2RepeatedIgnoreEmpty) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2RepeatedIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *Proto2RepeatedIgnoreDefault) Reset() {
	*x = Proto2RepeatedIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2RepeatedIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2RepeatedIgnoreDefault) ProtoMessage() {}

func (x *Proto2RepeatedIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2RepeatedIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2RepeatedIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{26}
}

func (x *Proto2RepeatedIgnoreDefault) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapIgnoreUnspecified) Reset() {
	*x = Proto2MapIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2MapIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2MapIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{27}
}

func (x *Proto2MapIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapIgnoreEmpty) Reset() {
	*x = Proto2MapIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapIgnoreEmpty) ProtoMessage() {}

func (x *Proto2MapIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2MapIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{28}
}

func (x *Proto2MapIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapIgnoreDefault) Reset() {
	*x = Proto2MapIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapIgnoreDefault) ProtoMessage() {}

func (x *Proto2MapIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2MapIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{29}
}

func (x *Proto2MapIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2RepeatedItemIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *Proto2RepeatedItemIgnoreUnspecified) Reset() {
	*x = Proto2RepeatedItemIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2RepeatedItemIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2RepeatedItemIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2RepeatedItemIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2RepeatedItemIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2RepeatedItemIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{30}
}

func (x *Proto2RepeatedItemIgnoreUnspecified) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2RepeatedItemIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *Proto2RepeatedItemIgnoreEmpty) Reset() {
	*x = Proto2RepeatedItemIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2RepeatedItemIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2RepeatedItemIgnoreEmpty) ProtoMessage() {}

func (x *Proto2RepeatedItemIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2RepeatedItemIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2RepeatedItemIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{31}
}

func (x *Proto2RepeatedItemIgnoreEmpty) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2RepeatedItemIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *Proto2RepeatedItemIgnoreDefault) Reset() {
	*x = Proto2RepeatedItemIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2RepeatedItemIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2RepeatedItemIgnoreDefault) ProtoMessage() {}

func (x *Proto2RepeatedItemIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2RepeatedItemIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2RepeatedItemIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{32}
}

func (x *Proto2RepeatedItemIgnoreDefault) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapKeyIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapKeyIgnoreUnspecified) Reset() {
	*x = Proto2MapKeyIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapKeyIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapKeyIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2MapKeyIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapKeyIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2MapKeyIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{33}
}

func (x *Proto2MapKeyIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapKeyIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapKeyIgnoreEmpty) Reset() {
	*x = Proto2MapKeyIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapKeyIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapKeyIgnoreEmpty) ProtoMessage() {}

func (x *Proto2MapKeyIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapKeyIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2MapKeyIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{34}
}

func (x *Proto2MapKeyIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapKeyIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapKeyIgnoreDefault) Reset() {
	*x = Proto2MapKeyIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapKeyIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapKeyIgnoreDefault) ProtoMessage() {}

func (x *Proto2MapKeyIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapKeyIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2MapKeyIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{35}
}

func (x *Proto2MapKeyIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapValueIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapValueIgnoreUnspecified) Reset() {
	*x = Proto2MapValueIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapValueIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapValueIgnoreUnspecified) ProtoMessage() {}

func (x *Proto2MapValueIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapValueIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto2MapValueIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{36}
}

func (x *Proto2MapValueIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapValueIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapValueIgnoreEmpty) Reset() {
	*x = Proto2MapValueIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapValueIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapValueIgnoreEmpty) ProtoMessage() {}

func (x *Proto2MapValueIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapValueIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto2MapValueIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{37}
}

func (x *Proto2MapValueIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MapValueIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *Proto2MapValueIgnoreDefault) Reset() {
	*x = Proto2MapValueIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MapValueIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MapValueIgnoreDefault) ProtoMessage() {}

func (x *Proto2MapValueIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MapValueIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto2MapValueIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{38}
}

func (x *Proto2MapValueIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto2MessageOptionalIgnoreUnspecified_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageOptionalIgnoreUnspecified_Msg) Reset() {
	*x = Proto2MessageOptionalIgnoreUnspecified_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageOptionalIgnoreUnspecified_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageOptionalIgnoreUnspecified_Msg) ProtoMessage() {}

func (x *Proto2MessageOptionalIgnoreUnspecified_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageOptionalIgnoreUnspecified_Msg.ProtoReflect.Descriptor instead.
func (*Proto2MessageOptionalIgnoreUnspecified_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{12, 0}
}

func (x *Proto2MessageOptionalIgnoreUnspecified_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto2MessageOptionalIgnoreEmpty_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageOptionalIgnoreEmpty_Msg) Reset() {
	*x = Proto2MessageOptionalIgnoreEmpty_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageOptionalIgnoreEmpty_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageOptionalIgnoreEmpty_Msg) ProtoMessage() {}

func (x *Proto2MessageOptionalIgnoreEmpty_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageOptionalIgnoreEmpty_Msg.ProtoReflect.Descriptor instead.
func (*Proto2MessageOptionalIgnoreEmpty_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{13, 0}
}

func (x *Proto2MessageOptionalIgnoreEmpty_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto2MessageOptionalIgnoreDefault_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageOptionalIgnoreDefault_Msg) Reset() {
	*x = Proto2MessageOptionalIgnoreDefault_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageOptionalIgnoreDefault_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageOptionalIgnoreDefault_Msg) ProtoMessage() {}

func (x *Proto2MessageOptionalIgnoreDefault_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageOptionalIgnoreDefault_Msg.ProtoReflect.Descriptor instead.
func (*Proto2MessageOptionalIgnoreDefault_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{14, 0}
}

func (x *Proto2MessageOptionalIgnoreDefault_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto2MessageRequiredIgnoreUnspecified_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageRequiredIgnoreUnspecified_Msg) Reset() {
	*x = Proto2MessageRequiredIgnoreUnspecified_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageRequiredIgnoreUnspecified_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageRequiredIgnoreUnspecified_Msg) ProtoMessage() {}

func (x *Proto2MessageRequiredIgnoreUnspecified_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageRequiredIgnoreUnspecified_Msg.ProtoReflect.Descriptor instead.
func (*Proto2MessageRequiredIgnoreUnspecified_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{15, 0}
}

func (x *Proto2MessageRequiredIgnoreUnspecified_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto2MessageRequiredIgnoreEmpty_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageRequiredIgnoreEmpty_Msg) Reset() {
	*x = Proto2MessageRequiredIgnoreEmpty_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageRequiredIgnoreEmpty_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageRequiredIgnoreEmpty_Msg) ProtoMessage() {}

func (x *Proto2MessageRequiredIgnoreEmpty_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageRequiredIgnoreEmpty_Msg.ProtoReflect.Descriptor instead.
func (*Proto2MessageRequiredIgnoreEmpty_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{16, 0}
}

func (x *Proto2MessageRequiredIgnoreEmpty_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto2MessageRequiredIgnoreDefault_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *Proto2MessageRequiredIgnoreDefault_Msg) Reset() {
	*x = Proto2MessageRequiredIgnoreDefault_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto2MessageRequiredIgnoreDefault_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto2MessageRequiredIgnoreDefault_Msg) ProtoMessage() {}

func (x *Proto2MessageRequiredIgnoreDefault_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto2MessageRequiredIgnoreDefault_Msg.ProtoReflect.Descriptor instead.
func (*Proto2MessageRequiredIgnoreDefault_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP(), []int{17, 0}
}

func (x *Proto2MessageRequiredIgnoreDefault_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

var File_buf_validate_conformance_cases_ignore_proto2_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_ignore_proto2_proto_rawDesc = []byte{
	0x0a, 0x32, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x42, 0x0a, 0x25, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61,
	0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x52, 0x0a, 0x30, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x53,
	0x63, 0x61, 0x6c, 0x61, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x57, 0x69,
	0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x07, 0xba, 0x48, 0x04,
	0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3f, 0x0a, 0x1f, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01,
	0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4f, 0x0a, 0x2a, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x57, 0x69, 0x74,
	0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8,
	0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x41, 0x0a, 0x21, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba,
	0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x51,
	0x0a, 0x2c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32,
	0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x42, 0x0a, 0x25, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x52, 0x0a, 0x30, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x53,
	0x63, 0x61, 0x6c, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x57, 0x69,
	0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x07, 0xba, 0x48, 0x04,
	0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3f, 0x0a, 0x1f, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01,
	0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4f, 0x0a, 0x2a, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x57, 0x69, 0x74,
	0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8,
	0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x41, 0x0a, 0x21, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x42, 0x0a, 0xba,
	0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x51,
	0x0a, 0x2c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32,
	0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0xe0, 0x01, 0x0a, 0x26, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x9c, 0x01, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x3e, 0xba, 0x48, 0x3b, 0xba, 0x01, 0x38, 0x0a, 0x1b,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f,
	0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d,
	0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d,
	0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0xd7, 0x01, 0x0a, 0x20, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x99, 0x01, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x41, 0xba,
	0x48, 0x3e, 0xba, 0x01, 0x38, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73,
	0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xdb,
	0x01, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x9b, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x41, 0xba, 0x48, 0x3e,
	0xba, 0x01, 0x38, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76,
	0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x02, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xe0, 0x01, 0x0a,
	0x26, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x9c, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x4d, 0x73,
	0x67, 0x42, 0x3e, 0xba, 0x48, 0x3b, 0xba, 0x01, 0x38, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11,
	0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f,
	0x27, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0xd7, 0x01, 0x0a, 0x20, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x99, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0b, 0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x41, 0xba, 0x48, 0x3e, 0xba, 0x01, 0x38,
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66,
	0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20,
	0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xdb, 0x01, 0x0a, 0x22, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x12, 0x9b, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x46,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x41, 0xba, 0x48, 0x3e, 0xba, 0x01, 0x38, 0x0a, 0x1b,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f,
	0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d,
	0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17,
	0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x40, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x50, 0x0a, 0x27, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x3d, 0x0a, 0x16, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x4d, 0x0a, 0x21, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12,
	0x23, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34,
	0x32, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x3f, 0x0a, 0x18, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x4f, 0x0a, 0x23, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x12, 0x23, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03,
	0x2d, 0x34, 0x32, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x3d, 0x0a, 0x1f, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x92, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3a, 0x0a, 0x19, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x92, 0x01, 0x02, 0x08,
	0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3c, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x02, 0x92, 0x01, 0x02, 0x08, 0x03, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0xb5, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d,
	0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x12, 0x5f, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x43, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x56, 0x61, 0x6c,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x08, 0xba, 0x48, 0x05, 0x9a, 0x01, 0x02, 0x08, 0x03, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xac, 0x01, 0x0a,
	0x14, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x9a, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb0, 0x01, 0x0a, 0x16,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x5e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x02, 0x9a, 0x01, 0x02, 0x08,
	0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x45,
	0x0a, 0x23, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x05, 0x42, 0x0c, 0xba, 0x48, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x42, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x01,
	0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x44, 0x0a, 0x1f, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x92, 0x01,
	0x09, 0x22, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0xbf, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x12, 0x66, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x56, 0x61,
	0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xba, 0x48, 0x09, 0x9a, 0x01, 0x06, 0x22, 0x04,
	0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xb6, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x4b,
	0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x63, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0f, 0xba, 0x48,
	0x0c, 0x9a, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xba, 0x01, 0x0a, 0x19, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x65, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70,
	0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x9a, 0x01,
	0x09, 0x22, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a,
	0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc3, 0x01, 0x0a, 0x1f, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x68, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x42, 0x0c, 0xba, 0x48, 0x09, 0x9a, 0x01, 0x06, 0x2a, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xba, 0x01,
	0x0a, 0x19, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x65, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0f, 0xba, 0x48,
	0x0c, 0x9a, 0x01, 0x09, 0x2a, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbe, 0x01, 0x0a, 0x1b, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x4d,
	0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0f, 0xba,
	0x48, 0x0c, 0x9a, 0x01, 0x09, 0x2a, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0xa8, 0x02, 0x0a, 0x22,
	0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x42, 0x11, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75,
	0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02, 0x04, 0x42,
	0x56, 0x43, 0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43,
	0x61, 0x73, 0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c,
	0x43, 0x61, 0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x3a,
	0x3a, 0x43, 0x61, 0x73, 0x65, 0x73,
}

var (
	file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescData = file_buf_validate_conformance_cases_ignore_proto2_proto_rawDesc
)

func file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_ignore_proto2_proto_rawDescData
}

var file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes = make([]protoimpl.MessageInfo, 54)
var file_buf_validate_conformance_cases_ignore_proto2_proto_goTypes = []any{
	(*Proto2ScalarOptionalIgnoreUnspecified)(nil),            // 0: buf.validate.conformance.cases.Proto2ScalarOptionalIgnoreUnspecified
	(*Proto2ScalarOptionalIgnoreUnspecifiedWithDefault)(nil), // 1: buf.validate.conformance.cases.Proto2ScalarOptionalIgnoreUnspecifiedWithDefault
	(*Proto2ScalarOptionalIgnoreEmpty)(nil),                  // 2: buf.validate.conformance.cases.Proto2ScalarOptionalIgnoreEmpty
	(*Proto2ScalarOptionalIgnoreEmptyWithDefault)(nil),       // 3: buf.validate.conformance.cases.Proto2ScalarOptionalIgnoreEmptyWithDefault
	(*Proto2ScalarOptionalIgnoreDefault)(nil),                // 4: buf.validate.conformance.cases.Proto2ScalarOptionalIgnoreDefault
	(*Proto2ScalarOptionalIgnoreDefaultWithDefault)(nil),     // 5: buf.validate.conformance.cases.Proto2ScalarOptionalIgnoreDefaultWithDefault
	(*Proto2ScalarRequiredIgnoreUnspecified)(nil),            // 6: buf.validate.conformance.cases.Proto2ScalarRequiredIgnoreUnspecified
	(*Proto2ScalarRequiredIgnoreUnspecifiedWithDefault)(nil), // 7: buf.validate.conformance.cases.Proto2ScalarRequiredIgnoreUnspecifiedWithDefault
	(*Proto2ScalarRequiredIgnoreEmpty)(nil),                  // 8: buf.validate.conformance.cases.Proto2ScalarRequiredIgnoreEmpty
	(*Proto2ScalarRequiredIgnoreEmptyWithDefault)(nil),       // 9: buf.validate.conformance.cases.Proto2ScalarRequiredIgnoreEmptyWithDefault
	(*Proto2ScalarRequiredIgnoreDefault)(nil),                // 10: buf.validate.conformance.cases.Proto2ScalarRequiredIgnoreDefault
	(*Proto2ScalarRequiredIgnoreDefaultWithDefault)(nil),     // 11: buf.validate.conformance.cases.Proto2ScalarRequiredIgnoreDefaultWithDefault
	(*Proto2MessageOptionalIgnoreUnspecified)(nil),           // 12: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreUnspecified
	(*Proto2MessageOptionalIgnoreEmpty)(nil),                 // 13: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreEmpty
	(*Proto2MessageOptionalIgnoreDefault)(nil),               // 14: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreDefault
	(*Proto2MessageRequiredIgnoreUnspecified)(nil),           // 15: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreUnspecified
	(*Proto2MessageRequiredIgnoreEmpty)(nil),                 // 16: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreEmpty
	(*Proto2MessageRequiredIgnoreDefault)(nil),               // 17: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreDefault
	(*Proto2OneofIgnoreUnspecified)(nil),                     // 18: buf.validate.conformance.cases.Proto2OneofIgnoreUnspecified
	(*Proto2OneofIgnoreUnspecifiedWithDefault)(nil),          // 19: buf.validate.conformance.cases.Proto2OneofIgnoreUnspecifiedWithDefault
	(*Proto2OneofIgnoreEmpty)(nil),                           // 20: buf.validate.conformance.cases.Proto2OneofIgnoreEmpty
	(*Proto2OneofIgnoreEmptyWithDefault)(nil),                // 21: buf.validate.conformance.cases.Proto2OneofIgnoreEmptyWithDefault
	(*Proto2OneofIgnoreDefault)(nil),                         // 22: buf.validate.conformance.cases.Proto2OneofIgnoreDefault
	(*Proto2OneofIgnoreDefaultWithDefault)(nil),              // 23: buf.validate.conformance.cases.Proto2OneofIgnoreDefaultWithDefault
	(*Proto2RepeatedIgnoreUnspecified)(nil),                  // 24: buf.validate.conformance.cases.Proto2RepeatedIgnoreUnspecified
	(*Proto2RepeatedIgnoreEmpty)(nil),                        // 25: buf.validate.conformance.cases.Proto2RepeatedIgnoreEmpty
	(*Proto2RepeatedIgnoreDefault)(nil),                      // 26: buf.validate.conformance.cases.Proto2RepeatedIgnoreDefault
	(*Proto2MapIgnoreUnspecified)(nil),                       // 27: buf.validate.conformance.cases.Proto2MapIgnoreUnspecified
	(*Proto2MapIgnoreEmpty)(nil),                             // 28: buf.validate.conformance.cases.Proto2MapIgnoreEmpty
	(*Proto2MapIgnoreDefault)(nil),                           // 29: buf.validate.conformance.cases.Proto2MapIgnoreDefault
	(*Proto2RepeatedItemIgnoreUnspecified)(nil),              // 30: buf.validate.conformance.cases.Proto2RepeatedItemIgnoreUnspecified
	(*Proto2RepeatedItemIgnoreEmpty)(nil),                    // 31: buf.validate.conformance.cases.Proto2RepeatedItemIgnoreEmpty
	(*Proto2RepeatedItemIgnoreDefault)(nil),                  // 32: buf.validate.conformance.cases.Proto2RepeatedItemIgnoreDefault
	(*Proto2MapKeyIgnoreUnspecified)(nil),                    // 33: buf.validate.conformance.cases.Proto2MapKeyIgnoreUnspecified
	(*Proto2MapKeyIgnoreEmpty)(nil),                          // 34: buf.validate.conformance.cases.Proto2MapKeyIgnoreEmpty
	(*Proto2MapKeyIgnoreDefault)(nil),                        // 35: buf.validate.conformance.cases.Proto2MapKeyIgnoreDefault
	(*Proto2MapValueIgnoreUnspecified)(nil),                  // 36: buf.validate.conformance.cases.Proto2MapValueIgnoreUnspecified
	(*Proto2MapValueIgnoreEmpty)(nil),                        // 37: buf.validate.conformance.cases.Proto2MapValueIgnoreEmpty
	(*Proto2MapValueIgnoreDefault)(nil),                      // 38: buf.validate.conformance.cases.Proto2MapValueIgnoreDefault
	(*Proto2MessageOptionalIgnoreUnspecified_Msg)(nil),       // 39: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreUnspecified.Msg
	(*Proto2MessageOptionalIgnoreEmpty_Msg)(nil),             // 40: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreEmpty.Msg
	(*Proto2MessageOptionalIgnoreDefault_Msg)(nil),           // 41: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreDefault.Msg
	(*Proto2MessageRequiredIgnoreUnspecified_Msg)(nil),       // 42: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreUnspecified.Msg
	(*Proto2MessageRequiredIgnoreEmpty_Msg)(nil),             // 43: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreEmpty.Msg
	(*Proto2MessageRequiredIgnoreDefault_Msg)(nil),           // 44: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreDefault.Msg
	nil, // 45: buf.validate.conformance.cases.Proto2MapIgnoreUnspecified.ValEntry
	nil, // 46: buf.validate.conformance.cases.Proto2MapIgnoreEmpty.ValEntry
	nil, // 47: buf.validate.conformance.cases.Proto2MapIgnoreDefault.ValEntry
	nil, // 48: buf.validate.conformance.cases.Proto2MapKeyIgnoreUnspecified.ValEntry
	nil, // 49: buf.validate.conformance.cases.Proto2MapKeyIgnoreEmpty.ValEntry
	nil, // 50: buf.validate.conformance.cases.Proto2MapKeyIgnoreDefault.ValEntry
	nil, // 51: buf.validate.conformance.cases.Proto2MapValueIgnoreUnspecified.ValEntry
	nil, // 52: buf.validate.conformance.cases.Proto2MapValueIgnoreEmpty.ValEntry
	nil, // 53: buf.validate.conformance.cases.Proto2MapValueIgnoreDefault.ValEntry
}
var file_buf_validate_conformance_cases_ignore_proto2_proto_depIdxs = []int32{
	39, // 0: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto2MessageOptionalIgnoreUnspecified.Msg
	40, // 1: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto2MessageOptionalIgnoreEmpty.Msg
	41, // 2: buf.validate.conformance.cases.Proto2MessageOptionalIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto2MessageOptionalIgnoreDefault.Msg
	42, // 3: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto2MessageRequiredIgnoreUnspecified.Msg
	43, // 4: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto2MessageRequiredIgnoreEmpty.Msg
	44, // 5: buf.validate.conformance.cases.Proto2MessageRequiredIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto2MessageRequiredIgnoreDefault.Msg
	45, // 6: buf.validate.conformance.cases.Proto2MapIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto2MapIgnoreUnspecified.ValEntry
	46, // 7: buf.validate.conformance.cases.Proto2MapIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto2MapIgnoreEmpty.ValEntry
	47, // 8: buf.validate.conformance.cases.Proto2MapIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto2MapIgnoreDefault.ValEntry
	48, // 9: buf.validate.conformance.cases.Proto2MapKeyIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto2MapKeyIgnoreUnspecified.ValEntry
	49, // 10: buf.validate.conformance.cases.Proto2MapKeyIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto2MapKeyIgnoreEmpty.ValEntry
	50, // 11: buf.validate.conformance.cases.Proto2MapKeyIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto2MapKeyIgnoreDefault.ValEntry
	51, // 12: buf.validate.conformance.cases.Proto2MapValueIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto2MapValueIgnoreUnspecified.ValEntry
	52, // 13: buf.validate.conformance.cases.Proto2MapValueIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto2MapValueIgnoreEmpty.ValEntry
	53, // 14: buf.validate.conformance.cases.Proto2MapValueIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto2MapValueIgnoreDefault.ValEntry
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_ignore_proto2_proto_init() }
func file_buf_validate_conformance_cases_ignore_proto2_proto_init() {
	if File_buf_validate_conformance_cases_ignore_proto2_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarOptionalIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarOptionalIgnoreUnspecifiedWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarOptionalIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarOptionalIgnoreEmptyWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarOptionalIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarOptionalIgnoreDefaultWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarRequiredIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarRequiredIgnoreUnspecifiedWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarRequiredIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarRequiredIgnoreEmptyWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarRequiredIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2ScalarRequiredIgnoreDefaultWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageOptionalIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageOptionalIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageOptionalIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageRequiredIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageRequiredIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageRequiredIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2OneofIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2OneofIgnoreUnspecifiedWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2OneofIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2OneofIgnoreEmptyWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2OneofIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2OneofIgnoreDefaultWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2RepeatedIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2RepeatedIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2RepeatedIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2RepeatedItemIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2RepeatedItemIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2RepeatedItemIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapKeyIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapKeyIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapKeyIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapValueIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapValueIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MapValueIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageOptionalIgnoreUnspecified_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageOptionalIgnoreEmpty_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageOptionalIgnoreDefault_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageRequiredIgnoreUnspecified_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageRequiredIgnoreEmpty_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*Proto2MessageRequiredIgnoreDefault_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[18].OneofWrappers = []any{
		(*Proto2OneofIgnoreUnspecified_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[19].OneofWrappers = []any{
		(*Proto2OneofIgnoreUnspecifiedWithDefault_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[20].OneofWrappers = []any{
		(*Proto2OneofIgnoreEmpty_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[21].OneofWrappers = []any{
		(*Proto2OneofIgnoreEmptyWithDefault_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[22].OneofWrappers = []any{
		(*Proto2OneofIgnoreDefault_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes[23].OneofWrappers = []any{
		(*Proto2OneofIgnoreDefaultWithDefault_Val)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_ignore_proto2_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   54,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_ignore_proto2_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_ignore_proto2_proto_depIdxs,
		MessageInfos:      file_buf_validate_conformance_cases_ignore_proto2_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_ignore_proto2_proto = out.File
	file_buf_validate_conformance_cases_ignore_proto2_proto_rawDesc = nil
	file_buf_validate_conformance_cases_ignore_proto2_proto_goTypes = nil
	file_buf_validate_conformance_cases_ignore_proto2_proto_depIdxs = nil
}
