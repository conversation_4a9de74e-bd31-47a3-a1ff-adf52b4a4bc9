// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/enums.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	other_package "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate/conformance/cases/other_package"
	yet_another_package "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate/conformance/cases/yet_another_package"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TestEnum int32

const (
	TestEnum_TEST_ENUM_UNSPECIFIED TestEnum = 0
	TestEnum_TEST_ENUM_ONE         TestEnum = 1
	TestEnum_TEST_ENUM_TWO         TestEnum = 2
)

// Enum value maps for TestEnum.
var (
	TestEnum_name = map[int32]string{
		0: "TEST_ENUM_UNSPECIFIED",
		1: "TEST_ENUM_ONE",
		2: "TEST_ENUM_TWO",
	}
	TestEnum_value = map[string]int32{
		"TEST_ENUM_UNSPECIFIED": 0,
		"TEST_ENUM_ONE":         1,
		"TEST_ENUM_TWO":         2,
	}
)

func (x TestEnum) Enum() *TestEnum {
	p := new(TestEnum)
	*p = x
	return p
}

func (x TestEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_enums_proto_enumTypes[0].Descriptor()
}

func (TestEnum) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_enums_proto_enumTypes[0]
}

func (x TestEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestEnum.Descriptor instead.
func (TestEnum) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{0}
}

type TestEnumAlias int32

const (
	TestEnumAlias_TEST_ENUM_ALIAS_UNSPECIFIED TestEnumAlias = 0
	TestEnumAlias_TEST_ENUM_ALIAS_A           TestEnumAlias = 1
	TestEnumAlias_TEST_ENUM_ALIAS_B           TestEnumAlias = 2
	TestEnumAlias_TEST_ENUM_ALIAS_C           TestEnumAlias = 3
	TestEnumAlias_TEST_ENUM_ALIAS_ALPHA       TestEnumAlias = 1
	TestEnumAlias_TEST_ENUM_ALIAS_BETA        TestEnumAlias = 2
	TestEnumAlias_TEST_ENUM_ALIAS_GAMMA       TestEnumAlias = 3
)

// Enum value maps for TestEnumAlias.
var (
	TestEnumAlias_name = map[int32]string{
		0: "TEST_ENUM_ALIAS_UNSPECIFIED",
		1: "TEST_ENUM_ALIAS_A",
		2: "TEST_ENUM_ALIAS_B",
		3: "TEST_ENUM_ALIAS_C",
		// Duplicate value: 1: "TEST_ENUM_ALIAS_ALPHA",
		// Duplicate value: 2: "TEST_ENUM_ALIAS_BETA",
		// Duplicate value: 3: "TEST_ENUM_ALIAS_GAMMA",
	}
	TestEnumAlias_value = map[string]int32{
		"TEST_ENUM_ALIAS_UNSPECIFIED": 0,
		"TEST_ENUM_ALIAS_A":           1,
		"TEST_ENUM_ALIAS_B":           2,
		"TEST_ENUM_ALIAS_C":           3,
		"TEST_ENUM_ALIAS_ALPHA":       1,
		"TEST_ENUM_ALIAS_BETA":        2,
		"TEST_ENUM_ALIAS_GAMMA":       3,
	}
)

func (x TestEnumAlias) Enum() *TestEnumAlias {
	p := new(TestEnumAlias)
	*p = x
	return p
}

func (x TestEnumAlias) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestEnumAlias) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_enums_proto_enumTypes[1].Descriptor()
}

func (TestEnumAlias) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_enums_proto_enumTypes[1]
}

func (x TestEnumAlias) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestEnumAlias.Descriptor instead.
func (TestEnumAlias) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{1}
}

type EnumNone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnum `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnum" json:"val,omitempty"`
}

func (x *EnumNone) Reset() {
	*x = EnumNone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumNone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumNone) ProtoMessage() {}

func (x *EnumNone) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumNone.ProtoReflect.Descriptor instead.
func (*EnumNone) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{0}
}

func (x *EnumNone) GetVal() TestEnum {
	if x != nil {
		return x.Val
	}
	return TestEnum_TEST_ENUM_UNSPECIFIED
}

type EnumConst struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnum `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnum" json:"val,omitempty"`
}

func (x *EnumConst) Reset() {
	*x = EnumConst{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumConst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumConst) ProtoMessage() {}

func (x *EnumConst) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumConst.ProtoReflect.Descriptor instead.
func (*EnumConst) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{1}
}

func (x *EnumConst) GetVal() TestEnum {
	if x != nil {
		return x.Val
	}
	return TestEnum_TEST_ENUM_UNSPECIFIED
}

type EnumAliasConst struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnumAlias `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnumAlias" json:"val,omitempty"`
}

func (x *EnumAliasConst) Reset() {
	*x = EnumAliasConst{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumAliasConst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumAliasConst) ProtoMessage() {}

func (x *EnumAliasConst) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumAliasConst.ProtoReflect.Descriptor instead.
func (*EnumAliasConst) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{2}
}

func (x *EnumAliasConst) GetVal() TestEnumAlias {
	if x != nil {
		return x.Val
	}
	return TestEnumAlias_TEST_ENUM_ALIAS_UNSPECIFIED
}

type EnumDefined struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnum `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnum" json:"val,omitempty"`
}

func (x *EnumDefined) Reset() {
	*x = EnumDefined{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumDefined) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumDefined) ProtoMessage() {}

func (x *EnumDefined) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumDefined.ProtoReflect.Descriptor instead.
func (*EnumDefined) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{3}
}

func (x *EnumDefined) GetVal() TestEnum {
	if x != nil {
		return x.Val
	}
	return TestEnum_TEST_ENUM_UNSPECIFIED
}

type EnumAliasDefined struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnumAlias `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnumAlias" json:"val,omitempty"`
}

func (x *EnumAliasDefined) Reset() {
	*x = EnumAliasDefined{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumAliasDefined) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumAliasDefined) ProtoMessage() {}

func (x *EnumAliasDefined) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumAliasDefined.ProtoReflect.Descriptor instead.
func (*EnumAliasDefined) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{4}
}

func (x *EnumAliasDefined) GetVal() TestEnumAlias {
	if x != nil {
		return x.Val
	}
	return TestEnumAlias_TEST_ENUM_ALIAS_UNSPECIFIED
}

type EnumIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnum `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnum" json:"val,omitempty"`
}

func (x *EnumIn) Reset() {
	*x = EnumIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumIn) ProtoMessage() {}

func (x *EnumIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumIn.ProtoReflect.Descriptor instead.
func (*EnumIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{5}
}

func (x *EnumIn) GetVal() TestEnum {
	if x != nil {
		return x.Val
	}
	return TestEnum_TEST_ENUM_UNSPECIFIED
}

type EnumAliasIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnumAlias `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnumAlias" json:"val,omitempty"`
}

func (x *EnumAliasIn) Reset() {
	*x = EnumAliasIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumAliasIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumAliasIn) ProtoMessage() {}

func (x *EnumAliasIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumAliasIn.ProtoReflect.Descriptor instead.
func (*EnumAliasIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{6}
}

func (x *EnumAliasIn) GetVal() TestEnumAlias {
	if x != nil {
		return x.Val
	}
	return TestEnumAlias_TEST_ENUM_ALIAS_UNSPECIFIED
}

type EnumNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnum `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnum" json:"val,omitempty"`
}

func (x *EnumNotIn) Reset() {
	*x = EnumNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumNotIn) ProtoMessage() {}

func (x *EnumNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumNotIn.ProtoReflect.Descriptor instead.
func (*EnumNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{7}
}

func (x *EnumNotIn) GetVal() TestEnum {
	if x != nil {
		return x.Val
	}
	return TestEnum_TEST_ENUM_UNSPECIFIED
}

type EnumAliasNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnumAlias `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnumAlias" json:"val,omitempty"`
}

func (x *EnumAliasNotIn) Reset() {
	*x = EnumAliasNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumAliasNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumAliasNotIn) ProtoMessage() {}

func (x *EnumAliasNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumAliasNotIn.ProtoReflect.Descriptor instead.
func (*EnumAliasNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{8}
}

func (x *EnumAliasNotIn) GetVal() TestEnumAlias {
	if x != nil {
		return x.Val
	}
	return TestEnumAlias_TEST_ENUM_ALIAS_UNSPECIFIED
}

type EnumExternal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val other_package.Embed_Enumerated `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.other_package.Embed_Enumerated" json:"val,omitempty"`
}

func (x *EnumExternal) Reset() {
	*x = EnumExternal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumExternal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumExternal) ProtoMessage() {}

func (x *EnumExternal) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumExternal.ProtoReflect.Descriptor instead.
func (*EnumExternal) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{9}
}

func (x *EnumExternal) GetVal() other_package.Embed_Enumerated {
	if x != nil {
		return x.Val
	}
	return other_package.Embed_Enumerated(0)
}

type EnumExternal2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val other_package.Embed_DoubleEmbed_DoubleEnumerated `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.other_package.Embed_DoubleEmbed_DoubleEnumerated" json:"val,omitempty"`
}

func (x *EnumExternal2) Reset() {
	*x = EnumExternal2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumExternal2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumExternal2) ProtoMessage() {}

func (x *EnumExternal2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumExternal2.ProtoReflect.Descriptor instead.
func (*EnumExternal2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{10}
}

func (x *EnumExternal2) GetVal() other_package.Embed_DoubleEmbed_DoubleEnumerated {
	if x != nil {
		return x.Val
	}
	return other_package.Embed_DoubleEmbed_DoubleEnumerated(0)
}

type RepeatedEnumDefined struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []TestEnum `protobuf:"varint,1,rep,packed,name=val,proto3,enum=buf.validate.conformance.cases.TestEnum" json:"val,omitempty"`
}

func (x *RepeatedEnumDefined) Reset() {
	*x = RepeatedEnumDefined{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedEnumDefined) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedEnumDefined) ProtoMessage() {}

func (x *RepeatedEnumDefined) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedEnumDefined.ProtoReflect.Descriptor instead.
func (*RepeatedEnumDefined) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{11}
}

func (x *RepeatedEnumDefined) GetVal() []TestEnum {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedExternalEnumDefined struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []other_package.Embed_Enumerated `protobuf:"varint,1,rep,packed,name=val,proto3,enum=buf.validate.conformance.cases.other_package.Embed_Enumerated" json:"val,omitempty"`
}

func (x *RepeatedExternalEnumDefined) Reset() {
	*x = RepeatedExternalEnumDefined{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedExternalEnumDefined) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedExternalEnumDefined) ProtoMessage() {}

func (x *RepeatedExternalEnumDefined) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedExternalEnumDefined.ProtoReflect.Descriptor instead.
func (*RepeatedExternalEnumDefined) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{12}
}

func (x *RepeatedExternalEnumDefined) GetVal() []other_package.Embed_Enumerated {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedYetAnotherExternalEnumDefined struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []yet_another_package.Embed_Enumerated `protobuf:"varint,1,rep,packed,name=val,proto3,enum=buf.validate.conformance.cases.yet_another_package.Embed_Enumerated" json:"val,omitempty"`
}

func (x *RepeatedYetAnotherExternalEnumDefined) Reset() {
	*x = RepeatedYetAnotherExternalEnumDefined{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedYetAnotherExternalEnumDefined) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedYetAnotherExternalEnumDefined) ProtoMessage() {}

func (x *RepeatedYetAnotherExternalEnumDefined) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedYetAnotherExternalEnumDefined.ProtoReflect.Descriptor instead.
func (*RepeatedYetAnotherExternalEnumDefined) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{13}
}

func (x *RepeatedYetAnotherExternalEnumDefined) GetVal() []yet_another_package.Embed_Enumerated {
	if x != nil {
		return x.Val
	}
	return nil
}

type MapEnumDefined struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[string]TestEnum `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=buf.validate.conformance.cases.TestEnum"`
}

func (x *MapEnumDefined) Reset() {
	*x = MapEnumDefined{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapEnumDefined) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapEnumDefined) ProtoMessage() {}

func (x *MapEnumDefined) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapEnumDefined.ProtoReflect.Descriptor instead.
func (*MapEnumDefined) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{14}
}

func (x *MapEnumDefined) GetVal() map[string]TestEnum {
	if x != nil {
		return x.Val
	}
	return nil
}

type MapExternalEnumDefined struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[string]other_package.Embed_Enumerated `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=buf.validate.conformance.cases.other_package.Embed_Enumerated"`
}

func (x *MapExternalEnumDefined) Reset() {
	*x = MapExternalEnumDefined{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapExternalEnumDefined) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapExternalEnumDefined) ProtoMessage() {}

func (x *MapExternalEnumDefined) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapExternalEnumDefined.ProtoReflect.Descriptor instead.
func (*MapExternalEnumDefined) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{15}
}

func (x *MapExternalEnumDefined) GetVal() map[string]other_package.Embed_Enumerated {
	if x != nil {
		return x.Val
	}
	return nil
}

type EnumInsideOneof struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Foo:
	//
	//	*EnumInsideOneof_Val
	Foo isEnumInsideOneof_Foo `protobuf_oneof:"foo"`
	// Types that are assignable to Bar:
	//
	//	*EnumInsideOneof_Val2
	Bar isEnumInsideOneof_Bar `protobuf_oneof:"bar"`
}

func (x *EnumInsideOneof) Reset() {
	*x = EnumInsideOneof{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumInsideOneof) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumInsideOneof) ProtoMessage() {}

func (x *EnumInsideOneof) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumInsideOneof.ProtoReflect.Descriptor instead.
func (*EnumInsideOneof) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{16}
}

func (m *EnumInsideOneof) GetFoo() isEnumInsideOneof_Foo {
	if m != nil {
		return m.Foo
	}
	return nil
}

func (x *EnumInsideOneof) GetVal() TestEnum {
	if x, ok := x.GetFoo().(*EnumInsideOneof_Val); ok {
		return x.Val
	}
	return TestEnum_TEST_ENUM_UNSPECIFIED
}

func (m *EnumInsideOneof) GetBar() isEnumInsideOneof_Bar {
	if m != nil {
		return m.Bar
	}
	return nil
}

func (x *EnumInsideOneof) GetVal2() TestEnum {
	if x, ok := x.GetBar().(*EnumInsideOneof_Val2); ok {
		return x.Val2
	}
	return TestEnum_TEST_ENUM_UNSPECIFIED
}

type isEnumInsideOneof_Foo interface {
	isEnumInsideOneof_Foo()
}

type EnumInsideOneof_Val struct {
	Val TestEnum `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnum,oneof"`
}

func (*EnumInsideOneof_Val) isEnumInsideOneof_Foo() {}

type isEnumInsideOneof_Bar interface {
	isEnumInsideOneof_Bar()
}

type EnumInsideOneof_Val2 struct {
	Val2 TestEnum `protobuf:"varint,2,opt,name=val2,proto3,enum=buf.validate.conformance.cases.TestEnum,oneof"`
}

func (*EnumInsideOneof_Val2) isEnumInsideOneof_Bar() {}

type EnumExample struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val TestEnum `protobuf:"varint,1,opt,name=val,proto3,enum=buf.validate.conformance.cases.TestEnum" json:"val,omitempty"`
}

func (x *EnumExample) Reset() {
	*x = EnumExample{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumExample) ProtoMessage() {}

func (x *EnumExample) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_enums_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumExample.ProtoReflect.Descriptor instead.
func (*EnumExample) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_enums_proto_rawDescGZIP(), []int{17}
}

func (x *EnumExample) GetVal() TestEnum {
	if x != nil {
		return x.Val
	}
	return TestEnum_TEST_ENUM_UNSPECIFIED
}

var File_buf_validate_conformance_cases_enums_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_enums_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x38, 0x62, 0x75,
	0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2f, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2f, 0x79, 0x65, 0x74, 0x5f, 0x61, 0x6e, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x46, 0x0a, 0x08, 0x45, 0x6e, 0x75, 0x6d, 0x4e, 0x6f, 0x6e, 0x65,
	0x12, 0x3a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x51, 0x0a, 0x09,
	0x45, 0x6e, 0x75, 0x6d, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x08, 0xba, 0x48, 0x05, 0x82, 0x01, 0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x5b, 0x0a, 0x0e, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x43, 0x6f, 0x6e, 0x73,
	0x74, 0x12, 0x49, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x42, 0x08, 0xba,
	0x48, 0x05, 0x82, 0x01, 0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x53, 0x0a, 0x0b,
	0x45, 0x6e, 0x75, 0x6d, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e,
	0x75, 0x6d, 0x42, 0x08, 0xba, 0x48, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x5d, 0x0a, 0x10, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x49, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61,
	0x73, 0x42, 0x08, 0xba, 0x48, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x50, 0x0a, 0x06, 0x45, 0x6e, 0x75, 0x6d, 0x49, 0x6e, 0x12, 0x46, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75,
	0x6d, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x82, 0x01, 0x04, 0x18, 0x00, 0x18, 0x02, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x5a, 0x0a, 0x0b, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x49,
	0x6e, 0x12, 0x4b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x42, 0x0a, 0xba,
	0x48, 0x07, 0x82, 0x01, 0x04, 0x18, 0x00, 0x18, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x51,
	0x0a, 0x09, 0x45, 0x6e, 0x75, 0x6d, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x44, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e,
	0x75, 0x6d, 0x42, 0x08, 0xba, 0x48, 0x05, 0x82, 0x01, 0x02, 0x20, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x5b, 0x0a, 0x0e, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x4e, 0x6f,
	0x74, 0x49, 0x6e, 0x12, 0x49, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x42,
	0x08, 0xba, 0x48, 0x05, 0x82, 0x01, 0x02, 0x20, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6a,
	0x0a, 0x0c, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x5a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x7d, 0x0a, 0x0d, 0x45, 0x6e,
	0x75, 0x6d, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x32, 0x12, 0x6c, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x50, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x2e, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x42, 0x08, 0xba, 0x48, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x60, 0x0a, 0x13, 0x52, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x12, 0x49, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x28, 0x2e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x7e, 0x0a, 0x1b, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45,
	0x6e, 0x75, 0x6d, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x5f, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x8e, 0x01, 0x0a, 0x25,
	0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x59, 0x65, 0x74, 0x41, 0x6e, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x65, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x79, 0x65, 0x74, 0x5f, 0x61, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07,
	0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xcc, 0x01, 0x0a,
	0x0e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x75, 0x6d, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x12,
	0x58, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x75, 0x6d, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x2e, 0x56, 0x61, 0x6c,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x9a, 0x01, 0x07, 0x2a, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x60, 0x0a, 0x08, 0x56, 0x61, 0x6c,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf2, 0x01, 0x0a, 0x16,
	0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x60, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x45, 0x6e, 0x75, 0x6d, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x2e, 0x56, 0x61, 0x6c, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x9a, 0x01, 0x07, 0x2a, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x76, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x54, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xb3, 0x01, 0x0a, 0x0f, 0x45, 0x6e, 0x75, 0x6d, 0x49, 0x6e, 0x73, 0x69, 0x64, 0x65, 0x4f,
	0x6e, 0x65, 0x6f, 0x66, 0x12, 0x46, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x28, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x12, 0x4a, 0x0a, 0x04,
	0x76, 0x61, 0x6c, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x01, 0x52, 0x04, 0x76, 0x61, 0x6c, 0x32, 0x42, 0x05, 0x0a, 0x03, 0x66, 0x6f, 0x6f, 0x42,
	0x05, 0x0a, 0x03, 0x62, 0x61, 0x72, 0x22, 0x53, 0x0a, 0x0b, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x78,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x44, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x28, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xba, 0x48,
	0x05, 0x82, 0x01, 0x02, 0x28, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x2a, 0x4b, 0x0a, 0x08, 0x54,
	0x65, 0x73, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x45, 0x53, 0x54, 0x5f,
	0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x45, 0x53, 0x54, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f,
	0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x45, 0x53, 0x54, 0x5f, 0x45, 0x4e,
	0x55, 0x4d, 0x5f, 0x54, 0x57, 0x4f, 0x10, 0x02, 0x2a, 0xc9, 0x01, 0x0a, 0x0d, 0x54, 0x65, 0x73,
	0x74, 0x45, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x45,
	0x53, 0x54, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54,
	0x45, 0x53, 0x54, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x41,
	0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x45, 0x53, 0x54, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f,
	0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x42, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x45, 0x53,
	0x54, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x43, 0x10, 0x03,
	0x12, 0x19, 0x0a, 0x15, 0x54, 0x45, 0x53, 0x54, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x41, 0x4c,
	0x49, 0x41, 0x53, 0x5f, 0x41, 0x4c, 0x50, 0x48, 0x41, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x54,
	0x45, 0x53, 0x54, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x42,
	0x45, 0x54, 0x41, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x45, 0x53, 0x54, 0x5f, 0x45, 0x4e,
	0x55, 0x4d, 0x5f, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x47, 0x41, 0x4d, 0x4d, 0x41, 0x10, 0x03,
	0x1a, 0x02, 0x10, 0x01, 0x42, 0xa1, 0x02, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x42, 0x0a, 0x45, 0x6e, 0x75,
	0x6d, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f,
	0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02,
	0x04, 0x42, 0x56, 0x43, 0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x43, 0x61, 0x73, 0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_buf_validate_conformance_cases_enums_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_enums_proto_rawDescData = file_buf_validate_conformance_cases_enums_proto_rawDesc
)

func file_buf_validate_conformance_cases_enums_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_enums_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_enums_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_enums_proto_rawDescData
}

var file_buf_validate_conformance_cases_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_buf_validate_conformance_cases_enums_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_buf_validate_conformance_cases_enums_proto_goTypes = []any{
	(TestEnum)(0),                                 // 0: buf.validate.conformance.cases.TestEnum
	(TestEnumAlias)(0),                            // 1: buf.validate.conformance.cases.TestEnumAlias
	(*EnumNone)(nil),                              // 2: buf.validate.conformance.cases.EnumNone
	(*EnumConst)(nil),                             // 3: buf.validate.conformance.cases.EnumConst
	(*EnumAliasConst)(nil),                        // 4: buf.validate.conformance.cases.EnumAliasConst
	(*EnumDefined)(nil),                           // 5: buf.validate.conformance.cases.EnumDefined
	(*EnumAliasDefined)(nil),                      // 6: buf.validate.conformance.cases.EnumAliasDefined
	(*EnumIn)(nil),                                // 7: buf.validate.conformance.cases.EnumIn
	(*EnumAliasIn)(nil),                           // 8: buf.validate.conformance.cases.EnumAliasIn
	(*EnumNotIn)(nil),                             // 9: buf.validate.conformance.cases.EnumNotIn
	(*EnumAliasNotIn)(nil),                        // 10: buf.validate.conformance.cases.EnumAliasNotIn
	(*EnumExternal)(nil),                          // 11: buf.validate.conformance.cases.EnumExternal
	(*EnumExternal2)(nil),                         // 12: buf.validate.conformance.cases.EnumExternal2
	(*RepeatedEnumDefined)(nil),                   // 13: buf.validate.conformance.cases.RepeatedEnumDefined
	(*RepeatedExternalEnumDefined)(nil),           // 14: buf.validate.conformance.cases.RepeatedExternalEnumDefined
	(*RepeatedYetAnotherExternalEnumDefined)(nil), // 15: buf.validate.conformance.cases.RepeatedYetAnotherExternalEnumDefined
	(*MapEnumDefined)(nil),                        // 16: buf.validate.conformance.cases.MapEnumDefined
	(*MapExternalEnumDefined)(nil),                // 17: buf.validate.conformance.cases.MapExternalEnumDefined
	(*EnumInsideOneof)(nil),                       // 18: buf.validate.conformance.cases.EnumInsideOneof
	(*EnumExample)(nil),                           // 19: buf.validate.conformance.cases.EnumExample
	nil,                                           // 20: buf.validate.conformance.cases.MapEnumDefined.ValEntry
	nil,                                           // 21: buf.validate.conformance.cases.MapExternalEnumDefined.ValEntry
	(other_package.Embed_Enumerated)(0),           // 22: buf.validate.conformance.cases.other_package.Embed.Enumerated
	(other_package.Embed_DoubleEmbed_DoubleEnumerated)(0), // 23: buf.validate.conformance.cases.other_package.Embed.DoubleEmbed.DoubleEnumerated
	(yet_another_package.Embed_Enumerated)(0),             // 24: buf.validate.conformance.cases.yet_another_package.Embed.Enumerated
}
var file_buf_validate_conformance_cases_enums_proto_depIdxs = []int32{
	0,  // 0: buf.validate.conformance.cases.EnumNone.val:type_name -> buf.validate.conformance.cases.TestEnum
	0,  // 1: buf.validate.conformance.cases.EnumConst.val:type_name -> buf.validate.conformance.cases.TestEnum
	1,  // 2: buf.validate.conformance.cases.EnumAliasConst.val:type_name -> buf.validate.conformance.cases.TestEnumAlias
	0,  // 3: buf.validate.conformance.cases.EnumDefined.val:type_name -> buf.validate.conformance.cases.TestEnum
	1,  // 4: buf.validate.conformance.cases.EnumAliasDefined.val:type_name -> buf.validate.conformance.cases.TestEnumAlias
	0,  // 5: buf.validate.conformance.cases.EnumIn.val:type_name -> buf.validate.conformance.cases.TestEnum
	1,  // 6: buf.validate.conformance.cases.EnumAliasIn.val:type_name -> buf.validate.conformance.cases.TestEnumAlias
	0,  // 7: buf.validate.conformance.cases.EnumNotIn.val:type_name -> buf.validate.conformance.cases.TestEnum
	1,  // 8: buf.validate.conformance.cases.EnumAliasNotIn.val:type_name -> buf.validate.conformance.cases.TestEnumAlias
	22, // 9: buf.validate.conformance.cases.EnumExternal.val:type_name -> buf.validate.conformance.cases.other_package.Embed.Enumerated
	23, // 10: buf.validate.conformance.cases.EnumExternal2.val:type_name -> buf.validate.conformance.cases.other_package.Embed.DoubleEmbed.DoubleEnumerated
	0,  // 11: buf.validate.conformance.cases.RepeatedEnumDefined.val:type_name -> buf.validate.conformance.cases.TestEnum
	22, // 12: buf.validate.conformance.cases.RepeatedExternalEnumDefined.val:type_name -> buf.validate.conformance.cases.other_package.Embed.Enumerated
	24, // 13: buf.validate.conformance.cases.RepeatedYetAnotherExternalEnumDefined.val:type_name -> buf.validate.conformance.cases.yet_another_package.Embed.Enumerated
	20, // 14: buf.validate.conformance.cases.MapEnumDefined.val:type_name -> buf.validate.conformance.cases.MapEnumDefined.ValEntry
	21, // 15: buf.validate.conformance.cases.MapExternalEnumDefined.val:type_name -> buf.validate.conformance.cases.MapExternalEnumDefined.ValEntry
	0,  // 16: buf.validate.conformance.cases.EnumInsideOneof.val:type_name -> buf.validate.conformance.cases.TestEnum
	0,  // 17: buf.validate.conformance.cases.EnumInsideOneof.val2:type_name -> buf.validate.conformance.cases.TestEnum
	0,  // 18: buf.validate.conformance.cases.EnumExample.val:type_name -> buf.validate.conformance.cases.TestEnum
	0,  // 19: buf.validate.conformance.cases.MapEnumDefined.ValEntry.value:type_name -> buf.validate.conformance.cases.TestEnum
	22, // 20: buf.validate.conformance.cases.MapExternalEnumDefined.ValEntry.value:type_name -> buf.validate.conformance.cases.other_package.Embed.Enumerated
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_enums_proto_init() }
func file_buf_validate_conformance_cases_enums_proto_init() {
	if File_buf_validate_conformance_cases_enums_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_enums_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*EnumNone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*EnumConst); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*EnumAliasConst); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*EnumDefined); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*EnumAliasDefined); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*EnumIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*EnumAliasIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*EnumNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*EnumAliasNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*EnumExternal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*EnumExternal2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedEnumDefined); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedExternalEnumDefined); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedYetAnotherExternalEnumDefined); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*MapEnumDefined); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*MapExternalEnumDefined); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*EnumInsideOneof); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_enums_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*EnumExample); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_enums_proto_msgTypes[16].OneofWrappers = []any{
		(*EnumInsideOneof_Val)(nil),
		(*EnumInsideOneof_Val2)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_enums_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_enums_proto_depIdxs,
		EnumInfos:         file_buf_validate_conformance_cases_enums_proto_enumTypes,
		MessageInfos:      file_buf_validate_conformance_cases_enums_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_enums_proto = out.File
	file_buf_validate_conformance_cases_enums_proto_rawDesc = nil
	file_buf_validate_conformance_cases_enums_proto_goTypes = nil
	file_buf_validate_conformance_cases_enums_proto_depIdxs = nil
}
