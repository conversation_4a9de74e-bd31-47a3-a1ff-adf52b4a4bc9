// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/ignore_proto3.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Proto3ScalarOptionalIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3ScalarOptionalIgnoreUnspecified) Reset() {
	*x = Proto3ScalarOptionalIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3ScalarOptionalIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3ScalarOptionalIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3ScalarOptionalIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3ScalarOptionalIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3ScalarOptionalIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{0}
}

func (x *Proto3ScalarOptionalIgnoreUnspecified) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto3ScalarOptionalIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3ScalarOptionalIgnoreEmpty) Reset() {
	*x = Proto3ScalarOptionalIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3ScalarOptionalIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3ScalarOptionalIgnoreEmpty) ProtoMessage() {}

func (x *Proto3ScalarOptionalIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3ScalarOptionalIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3ScalarOptionalIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{1}
}

func (x *Proto3ScalarOptionalIgnoreEmpty) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto3ScalarOptionalIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3ScalarOptionalIgnoreDefault) Reset() {
	*x = Proto3ScalarOptionalIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3ScalarOptionalIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3ScalarOptionalIgnoreDefault) ProtoMessage() {}

func (x *Proto3ScalarOptionalIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3ScalarOptionalIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3ScalarOptionalIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{2}
}

func (x *Proto3ScalarOptionalIgnoreDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type Proto3ScalarIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3ScalarIgnoreUnspecified) Reset() {
	*x = Proto3ScalarIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3ScalarIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3ScalarIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3ScalarIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3ScalarIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3ScalarIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{3}
}

func (x *Proto3ScalarIgnoreUnspecified) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Proto3ScalarIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3ScalarIgnoreEmpty) Reset() {
	*x = Proto3ScalarIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3ScalarIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3ScalarIgnoreEmpty) ProtoMessage() {}

func (x *Proto3ScalarIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3ScalarIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3ScalarIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{4}
}

func (x *Proto3ScalarIgnoreEmpty) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Proto3ScalarIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3ScalarIgnoreDefault) Reset() {
	*x = Proto3ScalarIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3ScalarIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3ScalarIgnoreDefault) ProtoMessage() {}

func (x *Proto3ScalarIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3ScalarIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3ScalarIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{5}
}

func (x *Proto3ScalarIgnoreDefault) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Proto3MessageOptionalIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto3MessageOptionalIgnoreUnspecified_Msg `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageOptionalIgnoreUnspecified) Reset() {
	*x = Proto3MessageOptionalIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageOptionalIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageOptionalIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3MessageOptionalIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageOptionalIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3MessageOptionalIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{6}
}

func (x *Proto3MessageOptionalIgnoreUnspecified) GetVal() *Proto3MessageOptionalIgnoreUnspecified_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MessageOptionalIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto3MessageOptionalIgnoreEmpty_Msg `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageOptionalIgnoreEmpty) Reset() {
	*x = Proto3MessageOptionalIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageOptionalIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageOptionalIgnoreEmpty) ProtoMessage() {}

func (x *Proto3MessageOptionalIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageOptionalIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3MessageOptionalIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{7}
}

func (x *Proto3MessageOptionalIgnoreEmpty) GetVal() *Proto3MessageOptionalIgnoreEmpty_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MessageOptionalIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto3MessageOptionalIgnoreDefault_Msg `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageOptionalIgnoreDefault) Reset() {
	*x = Proto3MessageOptionalIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageOptionalIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageOptionalIgnoreDefault) ProtoMessage() {}

func (x *Proto3MessageOptionalIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageOptionalIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3MessageOptionalIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{8}
}

func (x *Proto3MessageOptionalIgnoreDefault) GetVal() *Proto3MessageOptionalIgnoreDefault_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MessageIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto3MessageIgnoreUnspecified_Msg `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3MessageIgnoreUnspecified) Reset() {
	*x = Proto3MessageIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3MessageIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3MessageIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{9}
}

func (x *Proto3MessageIgnoreUnspecified) GetVal() *Proto3MessageIgnoreUnspecified_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MessageIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto3MessageIgnoreEmpty_Msg `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3MessageIgnoreEmpty) Reset() {
	*x = Proto3MessageIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageIgnoreEmpty) ProtoMessage() {}

func (x *Proto3MessageIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3MessageIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{10}
}

func (x *Proto3MessageIgnoreEmpty) GetVal() *Proto3MessageIgnoreEmpty_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MessageIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *Proto3MessageIgnoreDefault_Msg `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3MessageIgnoreDefault) Reset() {
	*x = Proto3MessageIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageIgnoreDefault) ProtoMessage() {}

func (x *Proto3MessageIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3MessageIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{11}
}

func (x *Proto3MessageIgnoreDefault) GetVal() *Proto3MessageIgnoreDefault_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3OneofIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto3OneofIgnoreUnspecified_Val
	O isProto3OneofIgnoreUnspecified_O `protobuf_oneof:"o"`
}

func (x *Proto3OneofIgnoreUnspecified) Reset() {
	*x = Proto3OneofIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3OneofIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3OneofIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3OneofIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3OneofIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3OneofIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{12}
}

func (m *Proto3OneofIgnoreUnspecified) GetO() isProto3OneofIgnoreUnspecified_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto3OneofIgnoreUnspecified) GetVal() int32 {
	if x, ok := x.GetO().(*Proto3OneofIgnoreUnspecified_Val); ok {
		return x.Val
	}
	return 0
}

type isProto3OneofIgnoreUnspecified_O interface {
	isProto3OneofIgnoreUnspecified_O()
}

type Proto3OneofIgnoreUnspecified_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,proto3,oneof"`
}

func (*Proto3OneofIgnoreUnspecified_Val) isProto3OneofIgnoreUnspecified_O() {}

type Proto3OneofIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto3OneofIgnoreEmpty_Val
	O isProto3OneofIgnoreEmpty_O `protobuf_oneof:"o"`
}

func (x *Proto3OneofIgnoreEmpty) Reset() {
	*x = Proto3OneofIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3OneofIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3OneofIgnoreEmpty) ProtoMessage() {}

func (x *Proto3OneofIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3OneofIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3OneofIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{13}
}

func (m *Proto3OneofIgnoreEmpty) GetO() isProto3OneofIgnoreEmpty_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto3OneofIgnoreEmpty) GetVal() int32 {
	if x, ok := x.GetO().(*Proto3OneofIgnoreEmpty_Val); ok {
		return x.Val
	}
	return 0
}

type isProto3OneofIgnoreEmpty_O interface {
	isProto3OneofIgnoreEmpty_O()
}

type Proto3OneofIgnoreEmpty_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,proto3,oneof"`
}

func (*Proto3OneofIgnoreEmpty_Val) isProto3OneofIgnoreEmpty_O() {}

type Proto3OneofIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*Proto3OneofIgnoreDefault_Val
	O isProto3OneofIgnoreDefault_O `protobuf_oneof:"o"`
}

func (x *Proto3OneofIgnoreDefault) Reset() {
	*x = Proto3OneofIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3OneofIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3OneofIgnoreDefault) ProtoMessage() {}

func (x *Proto3OneofIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3OneofIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3OneofIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{14}
}

func (m *Proto3OneofIgnoreDefault) GetO() isProto3OneofIgnoreDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *Proto3OneofIgnoreDefault) GetVal() int32 {
	if x, ok := x.GetO().(*Proto3OneofIgnoreDefault_Val); ok {
		return x.Val
	}
	return 0
}

type isProto3OneofIgnoreDefault_O interface {
	isProto3OneofIgnoreDefault_O()
}

type Proto3OneofIgnoreDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,proto3,oneof"`
}

func (*Proto3OneofIgnoreDefault_Val) isProto3OneofIgnoreDefault_O() {}

type Proto3RepeatedIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3RepeatedIgnoreUnspecified) Reset() {
	*x = Proto3RepeatedIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3RepeatedIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3RepeatedIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3RepeatedIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3RepeatedIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3RepeatedIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{15}
}

func (x *Proto3RepeatedIgnoreUnspecified) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3RepeatedIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3RepeatedIgnoreEmpty) Reset() {
	*x = Proto3RepeatedIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3RepeatedIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3RepeatedIgnoreEmpty) ProtoMessage() {}

func (x *Proto3RepeatedIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3RepeatedIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3RepeatedIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{16}
}

func (x *Proto3RepeatedIgnoreEmpty) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3RepeatedIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3RepeatedIgnoreDefault) Reset() {
	*x = Proto3RepeatedIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3RepeatedIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3RepeatedIgnoreDefault) ProtoMessage() {}

func (x *Proto3RepeatedIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3RepeatedIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3RepeatedIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{17}
}

func (x *Proto3RepeatedIgnoreDefault) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapIgnoreUnspecified) Reset() {
	*x = Proto3MapIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3MapIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3MapIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{18}
}

func (x *Proto3MapIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapIgnoreEmpty) Reset() {
	*x = Proto3MapIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapIgnoreEmpty) ProtoMessage() {}

func (x *Proto3MapIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3MapIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{19}
}

func (x *Proto3MapIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapIgnoreDefault) Reset() {
	*x = Proto3MapIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapIgnoreDefault) ProtoMessage() {}

func (x *Proto3MapIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3MapIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{20}
}

func (x *Proto3MapIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3RepeatedItemIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3RepeatedItemIgnoreUnspecified) Reset() {
	*x = Proto3RepeatedItemIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3RepeatedItemIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3RepeatedItemIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3RepeatedItemIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3RepeatedItemIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3RepeatedItemIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{21}
}

func (x *Proto3RepeatedItemIgnoreUnspecified) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3RepeatedItemIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3RepeatedItemIgnoreEmpty) Reset() {
	*x = Proto3RepeatedItemIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3RepeatedItemIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3RepeatedItemIgnoreEmpty) ProtoMessage() {}

func (x *Proto3RepeatedItemIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3RepeatedItemIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3RepeatedItemIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{22}
}

func (x *Proto3RepeatedItemIgnoreEmpty) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3RepeatedItemIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *Proto3RepeatedItemIgnoreDefault) Reset() {
	*x = Proto3RepeatedItemIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3RepeatedItemIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3RepeatedItemIgnoreDefault) ProtoMessage() {}

func (x *Proto3RepeatedItemIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3RepeatedItemIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3RepeatedItemIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{23}
}

func (x *Proto3RepeatedItemIgnoreDefault) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapKeyIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapKeyIgnoreUnspecified) Reset() {
	*x = Proto3MapKeyIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapKeyIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapKeyIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3MapKeyIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapKeyIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3MapKeyIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{24}
}

func (x *Proto3MapKeyIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapKeyIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapKeyIgnoreEmpty) Reset() {
	*x = Proto3MapKeyIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapKeyIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapKeyIgnoreEmpty) ProtoMessage() {}

func (x *Proto3MapKeyIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapKeyIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3MapKeyIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{25}
}

func (x *Proto3MapKeyIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapKeyIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapKeyIgnoreDefault) Reset() {
	*x = Proto3MapKeyIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapKeyIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapKeyIgnoreDefault) ProtoMessage() {}

func (x *Proto3MapKeyIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapKeyIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3MapKeyIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{26}
}

func (x *Proto3MapKeyIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapValueIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapValueIgnoreUnspecified) Reset() {
	*x = Proto3MapValueIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapValueIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapValueIgnoreUnspecified) ProtoMessage() {}

func (x *Proto3MapValueIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapValueIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*Proto3MapValueIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{27}
}

func (x *Proto3MapValueIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapValueIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapValueIgnoreEmpty) Reset() {
	*x = Proto3MapValueIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapValueIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapValueIgnoreEmpty) ProtoMessage() {}

func (x *Proto3MapValueIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapValueIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*Proto3MapValueIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{28}
}

func (x *Proto3MapValueIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MapValueIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Proto3MapValueIgnoreDefault) Reset() {
	*x = Proto3MapValueIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MapValueIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MapValueIgnoreDefault) ProtoMessage() {}

func (x *Proto3MapValueIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MapValueIgnoreDefault.ProtoReflect.Descriptor instead.
func (*Proto3MapValueIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{29}
}

func (x *Proto3MapValueIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type Proto3MessageOptionalIgnoreUnspecified_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageOptionalIgnoreUnspecified_Msg) Reset() {
	*x = Proto3MessageOptionalIgnoreUnspecified_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageOptionalIgnoreUnspecified_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageOptionalIgnoreUnspecified_Msg) ProtoMessage() {}

func (x *Proto3MessageOptionalIgnoreUnspecified_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageOptionalIgnoreUnspecified_Msg.ProtoReflect.Descriptor instead.
func (*Proto3MessageOptionalIgnoreUnspecified_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{6, 0}
}

func (x *Proto3MessageOptionalIgnoreUnspecified_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto3MessageOptionalIgnoreEmpty_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageOptionalIgnoreEmpty_Msg) Reset() {
	*x = Proto3MessageOptionalIgnoreEmpty_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageOptionalIgnoreEmpty_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageOptionalIgnoreEmpty_Msg) ProtoMessage() {}

func (x *Proto3MessageOptionalIgnoreEmpty_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageOptionalIgnoreEmpty_Msg.ProtoReflect.Descriptor instead.
func (*Proto3MessageOptionalIgnoreEmpty_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{7, 0}
}

func (x *Proto3MessageOptionalIgnoreEmpty_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto3MessageOptionalIgnoreDefault_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageOptionalIgnoreDefault_Msg) Reset() {
	*x = Proto3MessageOptionalIgnoreDefault_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageOptionalIgnoreDefault_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageOptionalIgnoreDefault_Msg) ProtoMessage() {}

func (x *Proto3MessageOptionalIgnoreDefault_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageOptionalIgnoreDefault_Msg.ProtoReflect.Descriptor instead.
func (*Proto3MessageOptionalIgnoreDefault_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{8, 0}
}

func (x *Proto3MessageOptionalIgnoreDefault_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto3MessageIgnoreUnspecified_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageIgnoreUnspecified_Msg) Reset() {
	*x = Proto3MessageIgnoreUnspecified_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageIgnoreUnspecified_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageIgnoreUnspecified_Msg) ProtoMessage() {}

func (x *Proto3MessageIgnoreUnspecified_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageIgnoreUnspecified_Msg.ProtoReflect.Descriptor instead.
func (*Proto3MessageIgnoreUnspecified_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{9, 0}
}

func (x *Proto3MessageIgnoreUnspecified_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto3MessageIgnoreEmpty_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageIgnoreEmpty_Msg) Reset() {
	*x = Proto3MessageIgnoreEmpty_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageIgnoreEmpty_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageIgnoreEmpty_Msg) ProtoMessage() {}

func (x *Proto3MessageIgnoreEmpty_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageIgnoreEmpty_Msg.ProtoReflect.Descriptor instead.
func (*Proto3MessageIgnoreEmpty_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{10, 0}
}

func (x *Proto3MessageIgnoreEmpty_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type Proto3MessageIgnoreDefault_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Proto3MessageIgnoreDefault_Msg) Reset() {
	*x = Proto3MessageIgnoreDefault_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Proto3MessageIgnoreDefault_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proto3MessageIgnoreDefault_Msg) ProtoMessage() {}

func (x *Proto3MessageIgnoreDefault_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proto3MessageIgnoreDefault_Msg.ProtoReflect.Descriptor instead.
func (*Proto3MessageIgnoreDefault_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP(), []int{11, 0}
}

func (x *Proto3MessageIgnoreDefault_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

var File_buf_validate_conformance_cases_ignore_proto3_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_ignore_proto3_proto_rawDesc = []byte{
	0x0a, 0x32, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x4f, 0x0a, 0x25, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x53, 0x63, 0x61, 0x6c, 0x61,
	0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76,
	0x61, 0x6c, 0x22, 0x4c, 0x0a, 0x1f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x53, 0x63, 0x61, 0x6c,
	0x61, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c,
	0x22, 0x4e, 0x0a, 0x21, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c,
	0x22, 0x3a, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x37, 0x0a, 0x17,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x39, 0x0a, 0x19, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x53,
	0x63, 0x61, 0x6c, 0x61, 0x72, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0xfa, 0x01, 0x0a, 0x26, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0xa1, 0x01, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x3e, 0xba, 0x48, 0x3b, 0xba, 0x01, 0x38, 0x0a, 0x1b, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62,
	0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20,
	0x27, 0x66, 0x6f, 0x6f, 0x27, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x1a,
	0x24, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x15, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a,
	0x04, 0x5f, 0x76, 0x61, 0x6c, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c, 0x22, 0xf1, 0x01,
	0x0a, 0x20, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x9e, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x41, 0xba, 0x48, 0x3e, 0xba, 0x01, 0x38, 0x0a, 0x1b,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f,
	0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d,
	0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x01, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x88, 0x01, 0x01, 0x1a, 0x24, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x15, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01,
	0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61,
	0x6c, 0x22, 0xf5, 0x01, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0xa0, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x41,
	0xba, 0x48, 0x3e, 0xba, 0x01, 0x38, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69,
	0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01,
	0x02, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x1a, 0x24, 0x0a, 0x03, 0x4d,
	0x73, 0x67, 0x12, 0x15, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61,
	0x6c, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c, 0x22, 0xdd, 0x01, 0x0a, 0x1e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x94, 0x01, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x3e,
	0xba, 0x48, 0x3b, 0xba, 0x01, 0x38, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69,
	0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x1a, 0x24, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x15, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01,
	0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c, 0x22, 0xd4, 0x01, 0x0a, 0x18, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x91, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x4d,
	0x73, 0x67, 0x42, 0x41, 0xba, 0x48, 0x3e, 0xba, 0x01, 0x38, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a,
	0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f,
	0x6f, 0x27, 0xd8, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x24, 0x0a, 0x03, 0x4d, 0x73,
	0x67, 0x12, 0x15, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c,
	0x22, 0xd8, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12,
	0x93, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x41, 0xba,
	0x48, 0x3e, 0xba, 0x01, 0x38, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73,
	0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x02,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x24, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x15, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c, 0x22, 0x40, 0x0a, 0x1c, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x3d, 0x0a,
	0x16, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x3f, 0x0a, 0x18,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x3d, 0x0a,
	0x1f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x08, 0xba,
	0x48, 0x05, 0x92, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3a, 0x0a, 0x19,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x92, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3c, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x02, 0x92, 0x01, 0x02, 0x08,
	0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xb5, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x5f, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x43, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x56,
	0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x08, 0xba, 0x48, 0x05, 0x9a, 0x01, 0x02, 0x08,
	0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xac,
	0x01, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x9a, 0x01, 0x02, 0x08, 0x03,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb0, 0x01,
	0x0a, 0x16, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x5e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61,
	0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x02, 0x9a, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x45, 0x0a, 0x23, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x05, 0x42, 0x0c, 0xba, 0x48, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x42, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0xd8,
	0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x44, 0x0a, 0x1f, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x0c,
	0x92, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0xbf, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x4b,
	0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x12, 0x66, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x46, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e,
	0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xba, 0x48, 0x09, 0x9a, 0x01, 0x06,
	0x22, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56,
	0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xb6, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61,
	0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x63, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0f,
	0xba, 0x48, 0x0c, 0x9a, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xba, 0x01, 0x0a,
	0x19, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x65, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d,
	0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0f, 0xba, 0x48, 0x0c,
	0x9a, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc3, 0x01, 0x0a, 0x1f, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x68, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x56, 0x61, 0x6c, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xba, 0x48, 0x09, 0x9a, 0x01, 0x06, 0x2a, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xba, 0x01, 0x0a, 0x19, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x65, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0f,
	0xba, 0x48, 0x0c, 0x9a, 0x01, 0x09, 0x2a, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbe, 0x01, 0x0a,
	0x1b, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42,
	0x0f, 0xba, 0x48, 0x0c, 0x9a, 0x01, 0x09, 0x2a, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0xa8, 0x02,
	0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x42, 0x11, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f,
	0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02,
	0x04, 0x42, 0x56, 0x43, 0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x43, 0x61, 0x73, 0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescData = file_buf_validate_conformance_cases_ignore_proto3_proto_rawDesc
)

func file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_ignore_proto3_proto_rawDescData
}

var file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_buf_validate_conformance_cases_ignore_proto3_proto_goTypes = []any{
	(*Proto3ScalarOptionalIgnoreUnspecified)(nil),      // 0: buf.validate.conformance.cases.Proto3ScalarOptionalIgnoreUnspecified
	(*Proto3ScalarOptionalIgnoreEmpty)(nil),            // 1: buf.validate.conformance.cases.Proto3ScalarOptionalIgnoreEmpty
	(*Proto3ScalarOptionalIgnoreDefault)(nil),          // 2: buf.validate.conformance.cases.Proto3ScalarOptionalIgnoreDefault
	(*Proto3ScalarIgnoreUnspecified)(nil),              // 3: buf.validate.conformance.cases.Proto3ScalarIgnoreUnspecified
	(*Proto3ScalarIgnoreEmpty)(nil),                    // 4: buf.validate.conformance.cases.Proto3ScalarIgnoreEmpty
	(*Proto3ScalarIgnoreDefault)(nil),                  // 5: buf.validate.conformance.cases.Proto3ScalarIgnoreDefault
	(*Proto3MessageOptionalIgnoreUnspecified)(nil),     // 6: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreUnspecified
	(*Proto3MessageOptionalIgnoreEmpty)(nil),           // 7: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreEmpty
	(*Proto3MessageOptionalIgnoreDefault)(nil),         // 8: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreDefault
	(*Proto3MessageIgnoreUnspecified)(nil),             // 9: buf.validate.conformance.cases.Proto3MessageIgnoreUnspecified
	(*Proto3MessageIgnoreEmpty)(nil),                   // 10: buf.validate.conformance.cases.Proto3MessageIgnoreEmpty
	(*Proto3MessageIgnoreDefault)(nil),                 // 11: buf.validate.conformance.cases.Proto3MessageIgnoreDefault
	(*Proto3OneofIgnoreUnspecified)(nil),               // 12: buf.validate.conformance.cases.Proto3OneofIgnoreUnspecified
	(*Proto3OneofIgnoreEmpty)(nil),                     // 13: buf.validate.conformance.cases.Proto3OneofIgnoreEmpty
	(*Proto3OneofIgnoreDefault)(nil),                   // 14: buf.validate.conformance.cases.Proto3OneofIgnoreDefault
	(*Proto3RepeatedIgnoreUnspecified)(nil),            // 15: buf.validate.conformance.cases.Proto3RepeatedIgnoreUnspecified
	(*Proto3RepeatedIgnoreEmpty)(nil),                  // 16: buf.validate.conformance.cases.Proto3RepeatedIgnoreEmpty
	(*Proto3RepeatedIgnoreDefault)(nil),                // 17: buf.validate.conformance.cases.Proto3RepeatedIgnoreDefault
	(*Proto3MapIgnoreUnspecified)(nil),                 // 18: buf.validate.conformance.cases.Proto3MapIgnoreUnspecified
	(*Proto3MapIgnoreEmpty)(nil),                       // 19: buf.validate.conformance.cases.Proto3MapIgnoreEmpty
	(*Proto3MapIgnoreDefault)(nil),                     // 20: buf.validate.conformance.cases.Proto3MapIgnoreDefault
	(*Proto3RepeatedItemIgnoreUnspecified)(nil),        // 21: buf.validate.conformance.cases.Proto3RepeatedItemIgnoreUnspecified
	(*Proto3RepeatedItemIgnoreEmpty)(nil),              // 22: buf.validate.conformance.cases.Proto3RepeatedItemIgnoreEmpty
	(*Proto3RepeatedItemIgnoreDefault)(nil),            // 23: buf.validate.conformance.cases.Proto3RepeatedItemIgnoreDefault
	(*Proto3MapKeyIgnoreUnspecified)(nil),              // 24: buf.validate.conformance.cases.Proto3MapKeyIgnoreUnspecified
	(*Proto3MapKeyIgnoreEmpty)(nil),                    // 25: buf.validate.conformance.cases.Proto3MapKeyIgnoreEmpty
	(*Proto3MapKeyIgnoreDefault)(nil),                  // 26: buf.validate.conformance.cases.Proto3MapKeyIgnoreDefault
	(*Proto3MapValueIgnoreUnspecified)(nil),            // 27: buf.validate.conformance.cases.Proto3MapValueIgnoreUnspecified
	(*Proto3MapValueIgnoreEmpty)(nil),                  // 28: buf.validate.conformance.cases.Proto3MapValueIgnoreEmpty
	(*Proto3MapValueIgnoreDefault)(nil),                // 29: buf.validate.conformance.cases.Proto3MapValueIgnoreDefault
	(*Proto3MessageOptionalIgnoreUnspecified_Msg)(nil), // 30: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreUnspecified.Msg
	(*Proto3MessageOptionalIgnoreEmpty_Msg)(nil),       // 31: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreEmpty.Msg
	(*Proto3MessageOptionalIgnoreDefault_Msg)(nil),     // 32: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreDefault.Msg
	(*Proto3MessageIgnoreUnspecified_Msg)(nil),         // 33: buf.validate.conformance.cases.Proto3MessageIgnoreUnspecified.Msg
	(*Proto3MessageIgnoreEmpty_Msg)(nil),               // 34: buf.validate.conformance.cases.Proto3MessageIgnoreEmpty.Msg
	(*Proto3MessageIgnoreDefault_Msg)(nil),             // 35: buf.validate.conformance.cases.Proto3MessageIgnoreDefault.Msg
	nil,                                                // 36: buf.validate.conformance.cases.Proto3MapIgnoreUnspecified.ValEntry
	nil,                                                // 37: buf.validate.conformance.cases.Proto3MapIgnoreEmpty.ValEntry
	nil,                                                // 38: buf.validate.conformance.cases.Proto3MapIgnoreDefault.ValEntry
	nil,                                                // 39: buf.validate.conformance.cases.Proto3MapKeyIgnoreUnspecified.ValEntry
	nil,                                                // 40: buf.validate.conformance.cases.Proto3MapKeyIgnoreEmpty.ValEntry
	nil,                                                // 41: buf.validate.conformance.cases.Proto3MapKeyIgnoreDefault.ValEntry
	nil,                                                // 42: buf.validate.conformance.cases.Proto3MapValueIgnoreUnspecified.ValEntry
	nil,                                                // 43: buf.validate.conformance.cases.Proto3MapValueIgnoreEmpty.ValEntry
	nil,                                                // 44: buf.validate.conformance.cases.Proto3MapValueIgnoreDefault.ValEntry
}
var file_buf_validate_conformance_cases_ignore_proto3_proto_depIdxs = []int32{
	30, // 0: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto3MessageOptionalIgnoreUnspecified.Msg
	31, // 1: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto3MessageOptionalIgnoreEmpty.Msg
	32, // 2: buf.validate.conformance.cases.Proto3MessageOptionalIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto3MessageOptionalIgnoreDefault.Msg
	33, // 3: buf.validate.conformance.cases.Proto3MessageIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto3MessageIgnoreUnspecified.Msg
	34, // 4: buf.validate.conformance.cases.Proto3MessageIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto3MessageIgnoreEmpty.Msg
	35, // 5: buf.validate.conformance.cases.Proto3MessageIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto3MessageIgnoreDefault.Msg
	36, // 6: buf.validate.conformance.cases.Proto3MapIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto3MapIgnoreUnspecified.ValEntry
	37, // 7: buf.validate.conformance.cases.Proto3MapIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto3MapIgnoreEmpty.ValEntry
	38, // 8: buf.validate.conformance.cases.Proto3MapIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto3MapIgnoreDefault.ValEntry
	39, // 9: buf.validate.conformance.cases.Proto3MapKeyIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto3MapKeyIgnoreUnspecified.ValEntry
	40, // 10: buf.validate.conformance.cases.Proto3MapKeyIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto3MapKeyIgnoreEmpty.ValEntry
	41, // 11: buf.validate.conformance.cases.Proto3MapKeyIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto3MapKeyIgnoreDefault.ValEntry
	42, // 12: buf.validate.conformance.cases.Proto3MapValueIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.Proto3MapValueIgnoreUnspecified.ValEntry
	43, // 13: buf.validate.conformance.cases.Proto3MapValueIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.Proto3MapValueIgnoreEmpty.ValEntry
	44, // 14: buf.validate.conformance.cases.Proto3MapValueIgnoreDefault.val:type_name -> buf.validate.conformance.cases.Proto3MapValueIgnoreDefault.ValEntry
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_ignore_proto3_proto_init() }
func file_buf_validate_conformance_cases_ignore_proto3_proto_init() {
	if File_buf_validate_conformance_cases_ignore_proto3_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3ScalarOptionalIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3ScalarOptionalIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3ScalarOptionalIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3ScalarIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3ScalarIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3ScalarIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageOptionalIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageOptionalIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageOptionalIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3OneofIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3OneofIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3OneofIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3RepeatedIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3RepeatedIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3RepeatedIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3RepeatedItemIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3RepeatedItemIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3RepeatedItemIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapKeyIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapKeyIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapKeyIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapValueIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapValueIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MapValueIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageOptionalIgnoreUnspecified_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageOptionalIgnoreEmpty_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageOptionalIgnoreDefault_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageIgnoreUnspecified_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageIgnoreEmpty_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*Proto3MessageIgnoreDefault_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[0].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[1].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[2].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[6].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[7].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[8].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[12].OneofWrappers = []any{
		(*Proto3OneofIgnoreUnspecified_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[13].OneofWrappers = []any{
		(*Proto3OneofIgnoreEmpty_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[14].OneofWrappers = []any{
		(*Proto3OneofIgnoreDefault_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[30].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[31].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[32].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[33].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[34].OneofWrappers = []any{}
	file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes[35].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_ignore_proto3_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_ignore_proto3_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_ignore_proto3_proto_depIdxs,
		MessageInfos:      file_buf_validate_conformance_cases_ignore_proto3_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_ignore_proto3_proto = out.File
	file_buf_validate_conformance_cases_ignore_proto3_proto_rawDesc = nil
	file_buf_validate_conformance_cases_ignore_proto3_proto_goTypes = nil
	file_buf_validate_conformance_cases_ignore_proto3_proto_depIdxs = nil
}
