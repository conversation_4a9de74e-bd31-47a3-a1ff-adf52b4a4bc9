// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/repeated.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	other_package "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate/conformance/cases/other_package"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AnEnum int32

const (
	AnEnum_AN_ENUM_UNSPECIFIED AnEnum = 0
	AnEnum_AN_ENUM_X           AnEnum = 1
	AnEnum_AN_ENUM_Y           AnEnum = 2
)

// Enum value maps for AnEnum.
var (
	AnEnum_name = map[int32]string{
		0: "AN_ENUM_UNSPECIFIED",
		1: "AN_ENUM_X",
		2: "AN_ENUM_Y",
	}
	AnEnum_value = map[string]int32{
		"AN_ENUM_UNSPECIFIED": 0,
		"AN_ENUM_X":           1,
		"AN_ENUM_Y":           2,
	}
)

func (x AnEnum) Enum() *AnEnum {
	p := new(AnEnum)
	*p = x
	return p
}

func (x AnEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_repeated_proto_enumTypes[0].Descriptor()
}

func (AnEnum) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_repeated_proto_enumTypes[0]
}

func (x AnEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnEnum.Descriptor instead.
func (AnEnum) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{0}
}

type RepeatedEmbeddedEnumIn_AnotherInEnum int32

const (
	RepeatedEmbeddedEnumIn_ANOTHER_IN_ENUM_UNSPECIFIED RepeatedEmbeddedEnumIn_AnotherInEnum = 0
	RepeatedEmbeddedEnumIn_ANOTHER_IN_ENUM_A           RepeatedEmbeddedEnumIn_AnotherInEnum = 1
	RepeatedEmbeddedEnumIn_ANOTHER_IN_ENUM_B           RepeatedEmbeddedEnumIn_AnotherInEnum = 2
)

// Enum value maps for RepeatedEmbeddedEnumIn_AnotherInEnum.
var (
	RepeatedEmbeddedEnumIn_AnotherInEnum_name = map[int32]string{
		0: "ANOTHER_IN_ENUM_UNSPECIFIED",
		1: "ANOTHER_IN_ENUM_A",
		2: "ANOTHER_IN_ENUM_B",
	}
	RepeatedEmbeddedEnumIn_AnotherInEnum_value = map[string]int32{
		"ANOTHER_IN_ENUM_UNSPECIFIED": 0,
		"ANOTHER_IN_ENUM_A":           1,
		"ANOTHER_IN_ENUM_B":           2,
	}
)

func (x RepeatedEmbeddedEnumIn_AnotherInEnum) Enum() *RepeatedEmbeddedEnumIn_AnotherInEnum {
	p := new(RepeatedEmbeddedEnumIn_AnotherInEnum)
	*p = x
	return p
}

func (x RepeatedEmbeddedEnumIn_AnotherInEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RepeatedEmbeddedEnumIn_AnotherInEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_repeated_proto_enumTypes[1].Descriptor()
}

func (RepeatedEmbeddedEnumIn_AnotherInEnum) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_repeated_proto_enumTypes[1]
}

func (x RepeatedEmbeddedEnumIn_AnotherInEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RepeatedEmbeddedEnumIn_AnotherInEnum.Descriptor instead.
func (RepeatedEmbeddedEnumIn_AnotherInEnum) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{18, 0}
}

type RepeatedEmbeddedEnumNotIn_AnotherNotInEnum int32

const (
	RepeatedEmbeddedEnumNotIn_ANOTHER_NOT_IN_ENUM_UNSPECIFIED RepeatedEmbeddedEnumNotIn_AnotherNotInEnum = 0
	RepeatedEmbeddedEnumNotIn_ANOTHER_NOT_IN_ENUM_A           RepeatedEmbeddedEnumNotIn_AnotherNotInEnum = 1
	RepeatedEmbeddedEnumNotIn_ANOTHER_NOT_IN_ENUM_B           RepeatedEmbeddedEnumNotIn_AnotherNotInEnum = 2
)

// Enum value maps for RepeatedEmbeddedEnumNotIn_AnotherNotInEnum.
var (
	RepeatedEmbeddedEnumNotIn_AnotherNotInEnum_name = map[int32]string{
		0: "ANOTHER_NOT_IN_ENUM_UNSPECIFIED",
		1: "ANOTHER_NOT_IN_ENUM_A",
		2: "ANOTHER_NOT_IN_ENUM_B",
	}
	RepeatedEmbeddedEnumNotIn_AnotherNotInEnum_value = map[string]int32{
		"ANOTHER_NOT_IN_ENUM_UNSPECIFIED": 0,
		"ANOTHER_NOT_IN_ENUM_A":           1,
		"ANOTHER_NOT_IN_ENUM_B":           2,
	}
)

func (x RepeatedEmbeddedEnumNotIn_AnotherNotInEnum) Enum() *RepeatedEmbeddedEnumNotIn_AnotherNotInEnum {
	p := new(RepeatedEmbeddedEnumNotIn_AnotherNotInEnum)
	*p = x
	return p
}

func (x RepeatedEmbeddedEnumNotIn_AnotherNotInEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RepeatedEmbeddedEnumNotIn_AnotherNotInEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_repeated_proto_enumTypes[2].Descriptor()
}

func (RepeatedEmbeddedEnumNotIn_AnotherNotInEnum) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_repeated_proto_enumTypes[2]
}

func (x RepeatedEmbeddedEnumNotIn_AnotherNotInEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RepeatedEmbeddedEnumNotIn_AnotherNotInEnum.Descriptor instead.
func (RepeatedEmbeddedEnumNotIn_AnotherNotInEnum) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{19, 0}
}

type Embed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Embed) Reset() {
	*x = Embed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Embed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Embed) ProtoMessage() {}

func (x *Embed) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Embed.ProtoReflect.Descriptor instead.
func (*Embed) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{0}
}

func (x *Embed) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type RepeatedNone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int64 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedNone) Reset() {
	*x = RepeatedNone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedNone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedNone) ProtoMessage() {}

func (x *RepeatedNone) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedNone.ProtoReflect.Descriptor instead.
func (*RepeatedNone) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{1}
}

func (x *RepeatedNone) GetVal() []int64 {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedEmbedNone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*Embed `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedEmbedNone) Reset() {
	*x = RepeatedEmbedNone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedEmbedNone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedEmbedNone) ProtoMessage() {}

func (x *RepeatedEmbedNone) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedEmbedNone.ProtoReflect.Descriptor instead.
func (*RepeatedEmbedNone) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{2}
}

func (x *RepeatedEmbedNone) GetVal() []*Embed {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedEmbedCrossPackageNone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*other_package.Embed `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedEmbedCrossPackageNone) Reset() {
	*x = RepeatedEmbedCrossPackageNone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedEmbedCrossPackageNone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedEmbedCrossPackageNone) ProtoMessage() {}

func (x *RepeatedEmbedCrossPackageNone) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedEmbedCrossPackageNone.ProtoReflect.Descriptor instead.
func (*RepeatedEmbedCrossPackageNone) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{3}
}

func (x *RepeatedEmbedCrossPackageNone) GetVal() []*other_package.Embed {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedMin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*Embed `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedMin) Reset() {
	*x = RepeatedMin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedMin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedMin) ProtoMessage() {}

func (x *RepeatedMin) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedMin.ProtoReflect.Descriptor instead.
func (*RepeatedMin) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{4}
}

func (x *RepeatedMin) GetVal() []*Embed {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedMax struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []float64 `protobuf:"fixed64,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedMax) Reset() {
	*x = RepeatedMax{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedMax) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedMax) ProtoMessage() {}

func (x *RepeatedMax) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedMax.ProtoReflect.Descriptor instead.
func (*RepeatedMax) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{5}
}

func (x *RepeatedMax) GetVal() []float64 {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedMinMax struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"fixed32,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedMinMax) Reset() {
	*x = RepeatedMinMax{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedMinMax) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedMinMax) ProtoMessage() {}

func (x *RepeatedMinMax) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedMinMax.ProtoReflect.Descriptor instead.
func (*RepeatedMinMax) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{6}
}

func (x *RepeatedMinMax) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedExact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []uint32 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedExact) Reset() {
	*x = RepeatedExact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedExact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedExact) ProtoMessage() {}

func (x *RepeatedExact) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedExact.ProtoReflect.Descriptor instead.
func (*RepeatedExact) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{7}
}

func (x *RepeatedExact) GetVal() []uint32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedUnique struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedUnique) Reset() {
	*x = RepeatedUnique{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedUnique) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedUnique) ProtoMessage() {}

func (x *RepeatedUnique) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedUnique.ProtoReflect.Descriptor instead.
func (*RepeatedUnique) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{8}
}

func (x *RepeatedUnique) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedNotUnique struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedNotUnique) Reset() {
	*x = RepeatedNotUnique{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedNotUnique) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedNotUnique) ProtoMessage() {}

func (x *RepeatedNotUnique) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedNotUnique.ProtoReflect.Descriptor instead.
func (*RepeatedNotUnique) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{9}
}

func (x *RepeatedNotUnique) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedMultipleUnique struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A []string `protobuf:"bytes,1,rep,name=a,proto3" json:"a,omitempty"`
	B []int32  `protobuf:"varint,2,rep,packed,name=b,proto3" json:"b,omitempty"`
}

func (x *RepeatedMultipleUnique) Reset() {
	*x = RepeatedMultipleUnique{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedMultipleUnique) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedMultipleUnique) ProtoMessage() {}

func (x *RepeatedMultipleUnique) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedMultipleUnique.ProtoReflect.Descriptor instead.
func (*RepeatedMultipleUnique) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{10}
}

func (x *RepeatedMultipleUnique) GetA() []string {
	if x != nil {
		return x.A
	}
	return nil
}

func (x *RepeatedMultipleUnique) GetB() []int32 {
	if x != nil {
		return x.B
	}
	return nil
}

type RepeatedItemRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []float32 `protobuf:"fixed32,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedItemRule) Reset() {
	*x = RepeatedItemRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedItemRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedItemRule) ProtoMessage() {}

func (x *RepeatedItemRule) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedItemRule.ProtoReflect.Descriptor instead.
func (*RepeatedItemRule) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{11}
}

func (x *RepeatedItemRule) GetVal() []float32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedItemPattern struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedItemPattern) Reset() {
	*x = RepeatedItemPattern{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedItemPattern) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedItemPattern) ProtoMessage() {}

func (x *RepeatedItemPattern) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedItemPattern.ProtoReflect.Descriptor instead.
func (*RepeatedItemPattern) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{12}
}

func (x *RepeatedItemPattern) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedEmbedSkip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*Embed `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedEmbedSkip) Reset() {
	*x = RepeatedEmbedSkip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedEmbedSkip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedEmbedSkip) ProtoMessage() {}

func (x *RepeatedEmbedSkip) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedEmbedSkip.ProtoReflect.Descriptor instead.
func (*RepeatedEmbedSkip) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{13}
}

func (x *RepeatedEmbedSkip) GetVal() []*Embed {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedItemIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedItemIn) Reset() {
	*x = RepeatedItemIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedItemIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedItemIn) ProtoMessage() {}

func (x *RepeatedItemIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedItemIn.ProtoReflect.Descriptor instead.
func (*RepeatedItemIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{14}
}

func (x *RepeatedItemIn) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedItemNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedItemNotIn) Reset() {
	*x = RepeatedItemNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedItemNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedItemNotIn) ProtoMessage() {}

func (x *RepeatedItemNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedItemNotIn.ProtoReflect.Descriptor instead.
func (*RepeatedItemNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{15}
}

func (x *RepeatedItemNotIn) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedEnumIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []AnEnum `protobuf:"varint,1,rep,packed,name=val,proto3,enum=buf.validate.conformance.cases.AnEnum" json:"val,omitempty"`
}

func (x *RepeatedEnumIn) Reset() {
	*x = RepeatedEnumIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedEnumIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedEnumIn) ProtoMessage() {}

func (x *RepeatedEnumIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedEnumIn.ProtoReflect.Descriptor instead.
func (*RepeatedEnumIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{16}
}

func (x *RepeatedEnumIn) GetVal() []AnEnum {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedEnumNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []AnEnum `protobuf:"varint,1,rep,packed,name=val,proto3,enum=buf.validate.conformance.cases.AnEnum" json:"val,omitempty"`
}

func (x *RepeatedEnumNotIn) Reset() {
	*x = RepeatedEnumNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedEnumNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedEnumNotIn) ProtoMessage() {}

func (x *RepeatedEnumNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedEnumNotIn.ProtoReflect.Descriptor instead.
func (*RepeatedEnumNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{17}
}

func (x *RepeatedEnumNotIn) GetVal() []AnEnum {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedEmbeddedEnumIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []RepeatedEmbeddedEnumIn_AnotherInEnum `protobuf:"varint,1,rep,packed,name=val,proto3,enum=buf.validate.conformance.cases.RepeatedEmbeddedEnumIn_AnotherInEnum" json:"val,omitempty"`
}

func (x *RepeatedEmbeddedEnumIn) Reset() {
	*x = RepeatedEmbeddedEnumIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedEmbeddedEnumIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedEmbeddedEnumIn) ProtoMessage() {}

func (x *RepeatedEmbeddedEnumIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedEmbeddedEnumIn.ProtoReflect.Descriptor instead.
func (*RepeatedEmbeddedEnumIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{18}
}

func (x *RepeatedEmbeddedEnumIn) GetVal() []RepeatedEmbeddedEnumIn_AnotherInEnum {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedEmbeddedEnumNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []RepeatedEmbeddedEnumNotIn_AnotherNotInEnum `protobuf:"varint,1,rep,packed,name=val,proto3,enum=buf.validate.conformance.cases.RepeatedEmbeddedEnumNotIn_AnotherNotInEnum" json:"val,omitempty"`
}

func (x *RepeatedEmbeddedEnumNotIn) Reset() {
	*x = RepeatedEmbeddedEnumNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedEmbeddedEnumNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedEmbeddedEnumNotIn) ProtoMessage() {}

func (x *RepeatedEmbeddedEnumNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedEmbeddedEnumNotIn.ProtoReflect.Descriptor instead.
func (*RepeatedEmbeddedEnumNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{19}
}

func (x *RepeatedEmbeddedEnumNotIn) GetVal() []RepeatedEmbeddedEnumNotIn_AnotherNotInEnum {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedAnyIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*anypb.Any `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedAnyIn) Reset() {
	*x = RepeatedAnyIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedAnyIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedAnyIn) ProtoMessage() {}

func (x *RepeatedAnyIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedAnyIn.ProtoReflect.Descriptor instead.
func (*RepeatedAnyIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{20}
}

func (x *RepeatedAnyIn) GetVal() []*anypb.Any {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedAnyNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*anypb.Any `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedAnyNotIn) Reset() {
	*x = RepeatedAnyNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedAnyNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedAnyNotIn) ProtoMessage() {}

func (x *RepeatedAnyNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedAnyNotIn.ProtoReflect.Descriptor instead.
func (*RepeatedAnyNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{21}
}

func (x *RepeatedAnyNotIn) GetVal() []*anypb.Any {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedMinAndItemLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedMinAndItemLen) Reset() {
	*x = RepeatedMinAndItemLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedMinAndItemLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedMinAndItemLen) ProtoMessage() {}

func (x *RepeatedMinAndItemLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedMinAndItemLen.ProtoReflect.Descriptor instead.
func (*RepeatedMinAndItemLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{22}
}

func (x *RepeatedMinAndItemLen) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedMinAndMaxItemLen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedMinAndMaxItemLen) Reset() {
	*x = RepeatedMinAndMaxItemLen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedMinAndMaxItemLen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedMinAndMaxItemLen) ProtoMessage() {}

func (x *RepeatedMinAndMaxItemLen) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedMinAndMaxItemLen.ProtoReflect.Descriptor instead.
func (*RepeatedMinAndMaxItemLen) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{23}
}

func (x *RepeatedMinAndMaxItemLen) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedDuration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*durationpb.Duration `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedDuration) Reset() {
	*x = RepeatedDuration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedDuration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedDuration) ProtoMessage() {}

func (x *RepeatedDuration) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedDuration.ProtoReflect.Descriptor instead.
func (*RepeatedDuration) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{24}
}

func (x *RepeatedDuration) GetVal() []*durationpb.Duration {
	if x != nil {
		return x.Val
	}
	return nil
}

type RepeatedExactIgnore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []uint32 `protobuf:"varint,1,rep,packed,name=val,proto3" json:"val,omitempty"`
}

func (x *RepeatedExactIgnore) Reset() {
	*x = RepeatedExactIgnore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedExactIgnore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedExactIgnore) ProtoMessage() {}

func (x *RepeatedExactIgnore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_repeated_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedExactIgnore.ProtoReflect.Descriptor instead.
func (*RepeatedExactIgnore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP(), []int{25}
}

func (x *RepeatedExactIgnore) GetVal() []uint32 {
	if x != nil {
		return x.Val
	}
	return nil
}

var File_buf_validate_conformance_cases_repeated_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_repeated_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x1a,
	0x38, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2f,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x65, 0x6d,
	0x62, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x22, 0x0a, 0x05, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x20, 0x0a, 0x0c, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4c, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x37, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x66, 0x0a, 0x1d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x45, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x50, 0x0a,
	0x0b, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x69, 0x6e, 0x12, 0x41, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64,
	0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x29, 0x0a, 0x0b, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x61, 0x78, 0x12, 0x1a,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x01, 0x42, 0x08, 0xba, 0x48, 0x05,
	0x92, 0x01, 0x02, 0x10, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0e, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x69, 0x6e, 0x4d, 0x61, 0x78, 0x12, 0x1c, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0f, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x92, 0x01,
	0x04, 0x08, 0x02, 0x10, 0x04, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x0d, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x61, 0x63, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x92, 0x01, 0x04,
	0x08, 0x03, 0x10, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0e, 0x52, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02,
	0x18, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01,
	0x02, 0x18, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x48, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x55, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x12, 0x16, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xba,
	0x48, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x01, 0x61, 0x12, 0x16, 0x0a, 0x01, 0x62, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x05, 0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52,
	0x01, 0x62, 0x22, 0x35, 0x0a, 0x10, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x02, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x0a, 0x05, 0x25,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x44, 0x0a, 0x13, 0x52, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e,
	0x12, 0x2d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1b, 0xba,
	0x48, 0x18, 0x92, 0x01, 0x15, 0x22, 0x13, 0x72, 0x11, 0x32, 0x0f, 0x28, 0x3f, 0x69, 0x29, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x59, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6d, 0x62, 0x65, 0x64,
	0x53, 0x6b, 0x69, 0x70, 0x12, 0x44, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x92, 0x01, 0x05,
	0x22, 0x03, 0xd8, 0x01, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x0e, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x12, 0x26, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0xba, 0x48, 0x11, 0x92, 0x01,
	0x0e, 0x22, 0x0c, 0x72, 0x0a, 0x52, 0x03, 0x66, 0x6f, 0x6f, 0x52, 0x03, 0x62, 0x61, 0x72, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x3b, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x26, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0xba, 0x48, 0x11, 0x92, 0x01, 0x0e, 0x22, 0x0c,
	0x72, 0x0a, 0x5a, 0x03, 0x66, 0x6f, 0x6f, 0x5a, 0x03, 0x62, 0x61, 0x72, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x59, 0x0a, 0x0e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75,
	0x6d, 0x49, 0x6e, 0x12, 0x47, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x41, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07,
	0x22, 0x05, 0x82, 0x01, 0x02, 0x18, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5c, 0x0a, 0x11,
	0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x4e, 0x6f, 0x74, 0x49,
	0x6e, 0x12, 0x47, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x41, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xdf, 0x01, 0x0a, 0x16, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x49, 0x6e, 0x12, 0x65, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x49, 0x6e, 0x2e, 0x41, 0x6e, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x49, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07,
	0x22, 0x05, 0x82, 0x01, 0x02, 0x18, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5e, 0x0a, 0x0d,
	0x41, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x1f, 0x0a,
	0x1b, 0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x45, 0x4e, 0x55, 0x4d,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15,
	0x0a, 0x11, 0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x45, 0x4e, 0x55,
	0x4d, 0x5f, 0x41, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52,
	0x5f, 0x49, 0x4e, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x42, 0x10, 0x02, 0x22, 0xf7, 0x01, 0x0a,
	0x19, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x6b, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x4a, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x4e, 0x6f, 0x74,
	0x49, 0x6e, 0x2e, 0x41, 0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x82, 0x01, 0x02,
	0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6d, 0x0a, 0x10, 0x41, 0x6e, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x1f, 0x41,
	0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x45, 0x4e,
	0x55, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x19, 0x0a, 0x15, 0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x49, 0x4e, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x41, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x41,
	0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x45, 0x4e,
	0x55, 0x4d, 0x5f, 0x42, 0x10, 0x02, 0x22, 0x72, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x6e, 0x79, 0x49, 0x6e, 0x12, 0x61, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x42, 0x39, 0xba, 0x48, 0x36, 0x92,
	0x01, 0x33, 0x22, 0x31, 0xa2, 0x01, 0x2e, 0x12, 0x2c, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x76, 0x0a, 0x10, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x62,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e,
	0x79, 0x42, 0x3a, 0xba, 0x48, 0x37, 0x92, 0x01, 0x34, 0x22, 0x32, 0xa2, 0x01, 0x2f, 0x1a, 0x2d,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x3a, 0x0a, 0x15, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x69,
	0x6e, 0x41, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x92, 0x01, 0x09,
	0x08, 0x01, 0x22, 0x05, 0x72, 0x03, 0x98, 0x01, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38,
	0x0a, 0x18, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x69, 0x6e, 0x41, 0x6e, 0x64,
	0x4d, 0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x65, 0x6e, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x92, 0x01, 0x04, 0x08,
	0x01, 0x10, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x52, 0x0a, 0x10, 0x52, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0xba, 0x48, 0x0e, 0x92, 0x01, 0x0b, 0x22, 0x09, 0xaa, 0x01,
	0x06, 0x32, 0x04, 0x10, 0xc0, 0x84, 0x3d, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x36, 0x0a, 0x13,
	0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x61, 0x63, 0x74, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d,
	0x42, 0x0d, 0xba, 0x48, 0x0a, 0xd8, 0x01, 0x01, 0x92, 0x01, 0x04, 0x08, 0x03, 0x10, 0x03, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x2a, 0x3f, 0x0a, 0x06, 0x41, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x17,
	0x0a, 0x13, 0x41, 0x4e, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4e, 0x5f, 0x45, 0x4e,
	0x55, 0x4d, 0x5f, 0x58, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4e, 0x5f, 0x45, 0x4e, 0x55,
	0x4d, 0x5f, 0x59, 0x10, 0x02, 0x42, 0xa4, 0x02, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x42, 0x0d, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75, 0x69,
	0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73,
	0x65, 0x73, 0xa2, 0x02, 0x04, 0x42, 0x56, 0x43, 0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x2e,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75, 0x66,
	0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42, 0x75,
	0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50, 0x42,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a, 0x3a,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_buf_validate_conformance_cases_repeated_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_repeated_proto_rawDescData = file_buf_validate_conformance_cases_repeated_proto_rawDesc
)

func file_buf_validate_conformance_cases_repeated_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_repeated_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_repeated_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_repeated_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_repeated_proto_rawDescData
}

var file_buf_validate_conformance_cases_repeated_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_buf_validate_conformance_cases_repeated_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_buf_validate_conformance_cases_repeated_proto_goTypes = []any{
	(AnEnum)(0), // 0: buf.validate.conformance.cases.AnEnum
	(RepeatedEmbeddedEnumIn_AnotherInEnum)(0),       // 1: buf.validate.conformance.cases.RepeatedEmbeddedEnumIn.AnotherInEnum
	(RepeatedEmbeddedEnumNotIn_AnotherNotInEnum)(0), // 2: buf.validate.conformance.cases.RepeatedEmbeddedEnumNotIn.AnotherNotInEnum
	(*Embed)(nil),                         // 3: buf.validate.conformance.cases.Embed
	(*RepeatedNone)(nil),                  // 4: buf.validate.conformance.cases.RepeatedNone
	(*RepeatedEmbedNone)(nil),             // 5: buf.validate.conformance.cases.RepeatedEmbedNone
	(*RepeatedEmbedCrossPackageNone)(nil), // 6: buf.validate.conformance.cases.RepeatedEmbedCrossPackageNone
	(*RepeatedMin)(nil),                   // 7: buf.validate.conformance.cases.RepeatedMin
	(*RepeatedMax)(nil),                   // 8: buf.validate.conformance.cases.RepeatedMax
	(*RepeatedMinMax)(nil),                // 9: buf.validate.conformance.cases.RepeatedMinMax
	(*RepeatedExact)(nil),                 // 10: buf.validate.conformance.cases.RepeatedExact
	(*RepeatedUnique)(nil),                // 11: buf.validate.conformance.cases.RepeatedUnique
	(*RepeatedNotUnique)(nil),             // 12: buf.validate.conformance.cases.RepeatedNotUnique
	(*RepeatedMultipleUnique)(nil),        // 13: buf.validate.conformance.cases.RepeatedMultipleUnique
	(*RepeatedItemRule)(nil),              // 14: buf.validate.conformance.cases.RepeatedItemRule
	(*RepeatedItemPattern)(nil),           // 15: buf.validate.conformance.cases.RepeatedItemPattern
	(*RepeatedEmbedSkip)(nil),             // 16: buf.validate.conformance.cases.RepeatedEmbedSkip
	(*RepeatedItemIn)(nil),                // 17: buf.validate.conformance.cases.RepeatedItemIn
	(*RepeatedItemNotIn)(nil),             // 18: buf.validate.conformance.cases.RepeatedItemNotIn
	(*RepeatedEnumIn)(nil),                // 19: buf.validate.conformance.cases.RepeatedEnumIn
	(*RepeatedEnumNotIn)(nil),             // 20: buf.validate.conformance.cases.RepeatedEnumNotIn
	(*RepeatedEmbeddedEnumIn)(nil),        // 21: buf.validate.conformance.cases.RepeatedEmbeddedEnumIn
	(*RepeatedEmbeddedEnumNotIn)(nil),     // 22: buf.validate.conformance.cases.RepeatedEmbeddedEnumNotIn
	(*RepeatedAnyIn)(nil),                 // 23: buf.validate.conformance.cases.RepeatedAnyIn
	(*RepeatedAnyNotIn)(nil),              // 24: buf.validate.conformance.cases.RepeatedAnyNotIn
	(*RepeatedMinAndItemLen)(nil),         // 25: buf.validate.conformance.cases.RepeatedMinAndItemLen
	(*RepeatedMinAndMaxItemLen)(nil),      // 26: buf.validate.conformance.cases.RepeatedMinAndMaxItemLen
	(*RepeatedDuration)(nil),              // 27: buf.validate.conformance.cases.RepeatedDuration
	(*RepeatedExactIgnore)(nil),           // 28: buf.validate.conformance.cases.RepeatedExactIgnore
	(*other_package.Embed)(nil),           // 29: buf.validate.conformance.cases.other_package.Embed
	(*anypb.Any)(nil),                     // 30: google.protobuf.Any
	(*durationpb.Duration)(nil),           // 31: google.protobuf.Duration
}
var file_buf_validate_conformance_cases_repeated_proto_depIdxs = []int32{
	3,  // 0: buf.validate.conformance.cases.RepeatedEmbedNone.val:type_name -> buf.validate.conformance.cases.Embed
	29, // 1: buf.validate.conformance.cases.RepeatedEmbedCrossPackageNone.val:type_name -> buf.validate.conformance.cases.other_package.Embed
	3,  // 2: buf.validate.conformance.cases.RepeatedMin.val:type_name -> buf.validate.conformance.cases.Embed
	3,  // 3: buf.validate.conformance.cases.RepeatedEmbedSkip.val:type_name -> buf.validate.conformance.cases.Embed
	0,  // 4: buf.validate.conformance.cases.RepeatedEnumIn.val:type_name -> buf.validate.conformance.cases.AnEnum
	0,  // 5: buf.validate.conformance.cases.RepeatedEnumNotIn.val:type_name -> buf.validate.conformance.cases.AnEnum
	1,  // 6: buf.validate.conformance.cases.RepeatedEmbeddedEnumIn.val:type_name -> buf.validate.conformance.cases.RepeatedEmbeddedEnumIn.AnotherInEnum
	2,  // 7: buf.validate.conformance.cases.RepeatedEmbeddedEnumNotIn.val:type_name -> buf.validate.conformance.cases.RepeatedEmbeddedEnumNotIn.AnotherNotInEnum
	30, // 8: buf.validate.conformance.cases.RepeatedAnyIn.val:type_name -> google.protobuf.Any
	30, // 9: buf.validate.conformance.cases.RepeatedAnyNotIn.val:type_name -> google.protobuf.Any
	31, // 10: buf.validate.conformance.cases.RepeatedDuration.val:type_name -> google.protobuf.Duration
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_repeated_proto_init() }
func file_buf_validate_conformance_cases_repeated_proto_init() {
	if File_buf_validate_conformance_cases_repeated_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Embed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedNone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedEmbedNone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedEmbedCrossPackageNone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedMin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedMax); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedMinMax); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedExact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedUnique); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedNotUnique); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedMultipleUnique); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedItemRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedItemPattern); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedEmbedSkip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedItemIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedItemNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedEnumIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedEnumNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedEmbeddedEnumIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedEmbeddedEnumNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedAnyIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedAnyNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedMinAndItemLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedMinAndMaxItemLen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedDuration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_repeated_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*RepeatedExactIgnore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_repeated_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_repeated_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_repeated_proto_depIdxs,
		EnumInfos:         file_buf_validate_conformance_cases_repeated_proto_enumTypes,
		MessageInfos:      file_buf_validate_conformance_cases_repeated_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_repeated_proto = out.File
	file_buf_validate_conformance_cases_repeated_proto_rawDesc = nil
	file_buf_validate_conformance_cases_repeated_proto_goTypes = nil
	file_buf_validate_conformance_cases_repeated_proto_depIdxs = nil
}
