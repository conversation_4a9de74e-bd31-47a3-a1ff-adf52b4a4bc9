// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/required_field_proto_editions.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RequiredEditionsScalarExplicitPresence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsScalarExplicitPresence) Reset() {
	*x = RequiredEditionsScalarExplicitPresence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsScalarExplicitPresence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsScalarExplicitPresence) ProtoMessage() {}

func (x *RequiredEditionsScalarExplicitPresence) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsScalarExplicitPresence.ProtoReflect.Descriptor instead.
func (*RequiredEditionsScalarExplicitPresence) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{0}
}

func (x *RequiredEditionsScalarExplicitPresence) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type RequiredEditionsScalarExplicitPresenceDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val,def=foo" json:"val,omitempty"`
}

// Default values for RequiredEditionsScalarExplicitPresenceDefault fields.
const (
	Default_RequiredEditionsScalarExplicitPresenceDefault_Val = string("foo")
)

func (x *RequiredEditionsScalarExplicitPresenceDefault) Reset() {
	*x = RequiredEditionsScalarExplicitPresenceDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsScalarExplicitPresenceDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsScalarExplicitPresenceDefault) ProtoMessage() {}

func (x *RequiredEditionsScalarExplicitPresenceDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsScalarExplicitPresenceDefault.ProtoReflect.Descriptor instead.
func (*RequiredEditionsScalarExplicitPresenceDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{1}
}

func (x *RequiredEditionsScalarExplicitPresenceDefault) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_RequiredEditionsScalarExplicitPresenceDefault_Val
}

type RequiredEditionsScalarImplicitPresence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsScalarImplicitPresence) Reset() {
	*x = RequiredEditionsScalarImplicitPresence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsScalarImplicitPresence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsScalarImplicitPresence) ProtoMessage() {}

func (x *RequiredEditionsScalarImplicitPresence) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsScalarImplicitPresence.ProtoReflect.Descriptor instead.
func (*RequiredEditionsScalarImplicitPresence) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{2}
}

func (x *RequiredEditionsScalarImplicitPresence) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

type RequiredEditionsScalarLegacyRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsScalarLegacyRequired) Reset() {
	*x = RequiredEditionsScalarLegacyRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsScalarLegacyRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsScalarLegacyRequired) ProtoMessage() {}

func (x *RequiredEditionsScalarLegacyRequired) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsScalarLegacyRequired.ProtoReflect.Descriptor instead.
func (*RequiredEditionsScalarLegacyRequired) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{3}
}

func (x *RequiredEditionsScalarLegacyRequired) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type RequiredEditionsMessageExplicitPresence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *RequiredEditionsMessageExplicitPresence_Msg `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsMessageExplicitPresence) Reset() {
	*x = RequiredEditionsMessageExplicitPresence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMessageExplicitPresence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMessageExplicitPresence) ProtoMessage() {}

func (x *RequiredEditionsMessageExplicitPresence) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMessageExplicitPresence.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMessageExplicitPresence) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{4}
}

func (x *RequiredEditionsMessageExplicitPresence) GetVal() *RequiredEditionsMessageExplicitPresence_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type RequiredEditionsMessageExplicitPresenceDelimited struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *RequiredEditionsMessageExplicitPresenceDelimited_Msg `protobuf:"group,1,opt,name=Msg,json=val" json:"val,omitempty"`
}

func (x *RequiredEditionsMessageExplicitPresenceDelimited) Reset() {
	*x = RequiredEditionsMessageExplicitPresenceDelimited{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMessageExplicitPresenceDelimited) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMessageExplicitPresenceDelimited) ProtoMessage() {}

func (x *RequiredEditionsMessageExplicitPresenceDelimited) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMessageExplicitPresenceDelimited.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMessageExplicitPresenceDelimited) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{5}
}

func (x *RequiredEditionsMessageExplicitPresenceDelimited) GetVal() *RequiredEditionsMessageExplicitPresenceDelimited_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type RequiredEditionsMessageLegacyRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *RequiredEditionsMessageLegacyRequired_Msg `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsMessageLegacyRequired) Reset() {
	*x = RequiredEditionsMessageLegacyRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMessageLegacyRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMessageLegacyRequired) ProtoMessage() {}

func (x *RequiredEditionsMessageLegacyRequired) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMessageLegacyRequired.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMessageLegacyRequired) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{6}
}

func (x *RequiredEditionsMessageLegacyRequired) GetVal() *RequiredEditionsMessageLegacyRequired_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type RequiredEditionsMessageLegacyRequiredDelimited struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *RequiredEditionsMessageLegacyRequiredDelimited_Msg `protobuf:"group,1,req,name=Msg,json=val" json:"val,omitempty"`
}

func (x *RequiredEditionsMessageLegacyRequiredDelimited) Reset() {
	*x = RequiredEditionsMessageLegacyRequiredDelimited{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMessageLegacyRequiredDelimited) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMessageLegacyRequiredDelimited) ProtoMessage() {}

func (x *RequiredEditionsMessageLegacyRequiredDelimited) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMessageLegacyRequiredDelimited.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMessageLegacyRequiredDelimited) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{7}
}

func (x *RequiredEditionsMessageLegacyRequiredDelimited) GetVal() *RequiredEditionsMessageLegacyRequiredDelimited_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type RequiredEditionsOneof struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Val:
	//
	//	*RequiredEditionsOneof_A
	//	*RequiredEditionsOneof_B
	Val isRequiredEditionsOneof_Val `protobuf_oneof:"val"`
}

func (x *RequiredEditionsOneof) Reset() {
	*x = RequiredEditionsOneof{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsOneof) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsOneof) ProtoMessage() {}

func (x *RequiredEditionsOneof) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsOneof.ProtoReflect.Descriptor instead.
func (*RequiredEditionsOneof) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{8}
}

func (m *RequiredEditionsOneof) GetVal() isRequiredEditionsOneof_Val {
	if m != nil {
		return m.Val
	}
	return nil
}

func (x *RequiredEditionsOneof) GetA() string {
	if x, ok := x.GetVal().(*RequiredEditionsOneof_A); ok {
		return x.A
	}
	return ""
}

func (x *RequiredEditionsOneof) GetB() string {
	if x, ok := x.GetVal().(*RequiredEditionsOneof_B); ok {
		return x.B
	}
	return ""
}

type isRequiredEditionsOneof_Val interface {
	isRequiredEditionsOneof_Val()
}

type RequiredEditionsOneof_A struct {
	A string `protobuf:"bytes,1,opt,name=a,oneof"`
}

type RequiredEditionsOneof_B struct {
	B string `protobuf:"bytes,2,opt,name=b,oneof"`
}

func (*RequiredEditionsOneof_A) isRequiredEditionsOneof_Val() {}

func (*RequiredEditionsOneof_B) isRequiredEditionsOneof_Val() {}

type RequiredEditionsRepeated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsRepeated) Reset() {
	*x = RequiredEditionsRepeated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsRepeated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsRepeated) ProtoMessage() {}

func (x *RequiredEditionsRepeated) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsRepeated.ProtoReflect.Descriptor instead.
func (*RequiredEditionsRepeated) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{9}
}

func (x *RequiredEditionsRepeated) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RequiredEditionsRepeatedExpanded struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []string `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsRepeatedExpanded) Reset() {
	*x = RequiredEditionsRepeatedExpanded{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsRepeatedExpanded) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsRepeatedExpanded) ProtoMessage() {}

func (x *RequiredEditionsRepeatedExpanded) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsRepeatedExpanded.ProtoReflect.Descriptor instead.
func (*RequiredEditionsRepeatedExpanded) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{10}
}

func (x *RequiredEditionsRepeatedExpanded) GetVal() []string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RequiredEditionsMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[string]string `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
}

func (x *RequiredEditionsMap) Reset() {
	*x = RequiredEditionsMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMap) ProtoMessage() {}

func (x *RequiredEditionsMap) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMap.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMap) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{11}
}

func (x *RequiredEditionsMap) GetVal() map[string]string {
	if x != nil {
		return x.Val
	}
	return nil
}

type RequiredEditionsMessageExplicitPresence_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsMessageExplicitPresence_Msg) Reset() {
	*x = RequiredEditionsMessageExplicitPresence_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMessageExplicitPresence_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMessageExplicitPresence_Msg) ProtoMessage() {}

func (x *RequiredEditionsMessageExplicitPresence_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMessageExplicitPresence_Msg.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMessageExplicitPresence_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{4, 0}
}

func (x *RequiredEditionsMessageExplicitPresence_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type RequiredEditionsMessageExplicitPresenceDelimited_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsMessageExplicitPresenceDelimited_Msg) Reset() {
	*x = RequiredEditionsMessageExplicitPresenceDelimited_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMessageExplicitPresenceDelimited_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMessageExplicitPresenceDelimited_Msg) ProtoMessage() {}

func (x *RequiredEditionsMessageExplicitPresenceDelimited_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMessageExplicitPresenceDelimited_Msg.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMessageExplicitPresenceDelimited_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{5, 0}
}

func (x *RequiredEditionsMessageExplicitPresenceDelimited_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type RequiredEditionsMessageLegacyRequired_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsMessageLegacyRequired_Msg) Reset() {
	*x = RequiredEditionsMessageLegacyRequired_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMessageLegacyRequired_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMessageLegacyRequired_Msg) ProtoMessage() {}

func (x *RequiredEditionsMessageLegacyRequired_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMessageLegacyRequired_Msg.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMessageLegacyRequired_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{6, 0}
}

func (x *RequiredEditionsMessageLegacyRequired_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type RequiredEditionsMessageLegacyRequiredDelimited_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *RequiredEditionsMessageLegacyRequiredDelimited_Msg) Reset() {
	*x = RequiredEditionsMessageLegacyRequiredDelimited_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredEditionsMessageLegacyRequiredDelimited_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredEditionsMessageLegacyRequiredDelimited_Msg) ProtoMessage() {}

func (x *RequiredEditionsMessageLegacyRequiredDelimited_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredEditionsMessageLegacyRequiredDelimited_Msg.ProtoReflect.Descriptor instead.
func (*RequiredEditionsMessageLegacyRequiredDelimited_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP(), []int{7, 0}
}

func (x *RequiredEditionsMessageLegacyRequiredDelimited_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

var File_buf_validate_conformance_cases_required_field_proto_editions_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDesc = []byte{
	0x0a, 0x42, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x42, 0x0a, 0x26, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x69,
	0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4e, 0x0a, 0x2d, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45,
	0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x03, 0x66, 0x6f, 0x6f, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x47, 0x0a, 0x26, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x49,
	0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48,
	0x03, 0xc8, 0x01, 0x01, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x45,
	0x0a, 0x24, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0xaa, 0x01, 0x02, 0x08, 0x03,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xa9, 0x01, 0x0a, 0x27, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x65, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x06, 0xba, 0x48, 0x03,
	0xc8, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0xc0, 0x01, 0x0a, 0x30, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70,
	0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0x73, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x65, 0x64, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x0b, 0xba, 0x48, 0x03, 0xc8, 0x01,
	0x01, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d,
	0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0xaa, 0x01, 0x0a, 0x25, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x68,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x0b, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0xbe, 0x01, 0x0a, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67,
	0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x65, 0x64, 0x12, 0x73, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x52, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65,
	0x64, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x0d, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0xaa, 0x01, 0x04,
	0x08, 0x03, 0x28, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67,
	0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x46, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x12, 0x16, 0x0a, 0x01, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0x48, 0x00,
	0x52, 0x01, 0x61, 0x12, 0x0e, 0x0a, 0x01, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x01, 0x62, 0x42, 0x05, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x34, 0x0a, 0x18, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x41, 0x0a, 0x20, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x70, 0x61,
	0x6e, 0x64, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x0b, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0xa5, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x56, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x2e, 0x56, 0x61,
	0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0xb6, 0x02, 0x0a, 0x22,
	0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x42, 0x1f, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02, 0x04, 0x42, 0x56, 0x43,
	0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61,
	0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8,
	0x07,
}

var (
	file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescData = file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDesc
)

func file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDescData
}

var file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_buf_validate_conformance_cases_required_field_proto_editions_proto_goTypes = []any{
	(*RequiredEditionsScalarExplicitPresence)(nil),               // 0: buf.validate.conformance.cases.RequiredEditionsScalarExplicitPresence
	(*RequiredEditionsScalarExplicitPresenceDefault)(nil),        // 1: buf.validate.conformance.cases.RequiredEditionsScalarExplicitPresenceDefault
	(*RequiredEditionsScalarImplicitPresence)(nil),               // 2: buf.validate.conformance.cases.RequiredEditionsScalarImplicitPresence
	(*RequiredEditionsScalarLegacyRequired)(nil),                 // 3: buf.validate.conformance.cases.RequiredEditionsScalarLegacyRequired
	(*RequiredEditionsMessageExplicitPresence)(nil),              // 4: buf.validate.conformance.cases.RequiredEditionsMessageExplicitPresence
	(*RequiredEditionsMessageExplicitPresenceDelimited)(nil),     // 5: buf.validate.conformance.cases.RequiredEditionsMessageExplicitPresenceDelimited
	(*RequiredEditionsMessageLegacyRequired)(nil),                // 6: buf.validate.conformance.cases.RequiredEditionsMessageLegacyRequired
	(*RequiredEditionsMessageLegacyRequiredDelimited)(nil),       // 7: buf.validate.conformance.cases.RequiredEditionsMessageLegacyRequiredDelimited
	(*RequiredEditionsOneof)(nil),                                // 8: buf.validate.conformance.cases.RequiredEditionsOneof
	(*RequiredEditionsRepeated)(nil),                             // 9: buf.validate.conformance.cases.RequiredEditionsRepeated
	(*RequiredEditionsRepeatedExpanded)(nil),                     // 10: buf.validate.conformance.cases.RequiredEditionsRepeatedExpanded
	(*RequiredEditionsMap)(nil),                                  // 11: buf.validate.conformance.cases.RequiredEditionsMap
	(*RequiredEditionsMessageExplicitPresence_Msg)(nil),          // 12: buf.validate.conformance.cases.RequiredEditionsMessageExplicitPresence.Msg
	(*RequiredEditionsMessageExplicitPresenceDelimited_Msg)(nil), // 13: buf.validate.conformance.cases.RequiredEditionsMessageExplicitPresenceDelimited.Msg
	(*RequiredEditionsMessageLegacyRequired_Msg)(nil),            // 14: buf.validate.conformance.cases.RequiredEditionsMessageLegacyRequired.Msg
	(*RequiredEditionsMessageLegacyRequiredDelimited_Msg)(nil),   // 15: buf.validate.conformance.cases.RequiredEditionsMessageLegacyRequiredDelimited.Msg
	nil, // 16: buf.validate.conformance.cases.RequiredEditionsMap.ValEntry
}
var file_buf_validate_conformance_cases_required_field_proto_editions_proto_depIdxs = []int32{
	12, // 0: buf.validate.conformance.cases.RequiredEditionsMessageExplicitPresence.val:type_name -> buf.validate.conformance.cases.RequiredEditionsMessageExplicitPresence.Msg
	13, // 1: buf.validate.conformance.cases.RequiredEditionsMessageExplicitPresenceDelimited.val:type_name -> buf.validate.conformance.cases.RequiredEditionsMessageExplicitPresenceDelimited.Msg
	14, // 2: buf.validate.conformance.cases.RequiredEditionsMessageLegacyRequired.val:type_name -> buf.validate.conformance.cases.RequiredEditionsMessageLegacyRequired.Msg
	15, // 3: buf.validate.conformance.cases.RequiredEditionsMessageLegacyRequiredDelimited.val:type_name -> buf.validate.conformance.cases.RequiredEditionsMessageLegacyRequiredDelimited.Msg
	16, // 4: buf.validate.conformance.cases.RequiredEditionsMap.val:type_name -> buf.validate.conformance.cases.RequiredEditionsMap.ValEntry
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_required_field_proto_editions_proto_init() }
func file_buf_validate_conformance_cases_required_field_proto_editions_proto_init() {
	if File_buf_validate_conformance_cases_required_field_proto_editions_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsScalarExplicitPresence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsScalarExplicitPresenceDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsScalarImplicitPresence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsScalarLegacyRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMessageExplicitPresence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMessageExplicitPresenceDelimited); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMessageLegacyRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMessageLegacyRequiredDelimited); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsOneof); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsRepeated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsRepeatedExpanded); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMessageExplicitPresence_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMessageExplicitPresenceDelimited_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMessageLegacyRequired_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*RequiredEditionsMessageLegacyRequiredDelimited_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes[8].OneofWrappers = []any{
		(*RequiredEditionsOneof_A)(nil),
		(*RequiredEditionsOneof_B)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_required_field_proto_editions_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_required_field_proto_editions_proto_depIdxs,
		MessageInfos:      file_buf_validate_conformance_cases_required_field_proto_editions_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_required_field_proto_editions_proto = out.File
	file_buf_validate_conformance_cases_required_field_proto_editions_proto_rawDesc = nil
	file_buf_validate_conformance_cases_required_field_proto_editions_proto_goTypes = nil
	file_buf_validate_conformance_cases_required_field_proto_editions_proto_depIdxs = nil
}
