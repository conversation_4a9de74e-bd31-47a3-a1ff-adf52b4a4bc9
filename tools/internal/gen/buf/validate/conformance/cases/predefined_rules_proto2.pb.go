// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/predefined_rules_proto2.proto

package cases

import (
	validate "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PredefinedEnumRuleProto2_EnumProto2 int32

const (
	PredefinedEnumRuleProto2_ENUM_PROTO2_ZERO_UNSPECIFIED PredefinedEnumRuleProto2_EnumProto2 = 0
	PredefinedEnumRuleProto2_ENUM_PROTO2_ONE              PredefinedEnumRuleProto2_EnumProto2 = 1
)

// Enum value maps for PredefinedEnumRuleProto2_EnumProto2.
var (
	PredefinedEnumRuleProto2_EnumProto2_name = map[int32]string{
		0: "ENUM_PROTO2_ZERO_UNSPECIFIED",
		1: "ENUM_PROTO2_ONE",
	}
	PredefinedEnumRuleProto2_EnumProto2_value = map[string]int32{
		"ENUM_PROTO2_ZERO_UNSPECIFIED": 0,
		"ENUM_PROTO2_ONE":              1,
	}
)

func (x PredefinedEnumRuleProto2_EnumProto2) Enum() *PredefinedEnumRuleProto2_EnumProto2 {
	p := new(PredefinedEnumRuleProto2_EnumProto2)
	*p = x
	return p
}

func (x PredefinedEnumRuleProto2_EnumProto2) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PredefinedEnumRuleProto2_EnumProto2) Descriptor() protoreflect.EnumDescriptor {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_enumTypes[0].Descriptor()
}

func (PredefinedEnumRuleProto2_EnumProto2) Type() protoreflect.EnumType {
	return &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_enumTypes[0]
}

func (x PredefinedEnumRuleProto2_EnumProto2) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PredefinedEnumRuleProto2_EnumProto2) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PredefinedEnumRuleProto2_EnumProto2(num)
	return nil
}

// Deprecated: Use PredefinedEnumRuleProto2_EnumProto2.Descriptor instead.
func (PredefinedEnumRuleProto2_EnumProto2) EnumDescriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{15, 0}
}

type PredefinedFloatRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *float32 `protobuf:"fixed32,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedFloatRuleProto2) Reset() {
	*x = PredefinedFloatRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFloatRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFloatRuleProto2) ProtoMessage() {}

func (x *PredefinedFloatRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFloatRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedFloatRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{0}
}

func (x *PredefinedFloatRuleProto2) GetVal() float32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedDoubleRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *float64 `protobuf:"fixed64,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedDoubleRuleProto2) Reset() {
	*x = PredefinedDoubleRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedDoubleRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedDoubleRuleProto2) ProtoMessage() {}

func (x *PredefinedDoubleRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedDoubleRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedDoubleRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{1}
}

func (x *PredefinedDoubleRuleProto2) GetVal() float64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedInt32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedInt32RuleProto2) Reset() {
	*x = PredefinedInt32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedInt32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedInt32RuleProto2) ProtoMessage() {}

func (x *PredefinedInt32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedInt32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedInt32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{2}
}

func (x *PredefinedInt32RuleProto2) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedInt64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int64 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedInt64RuleProto2) Reset() {
	*x = PredefinedInt64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedInt64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedInt64RuleProto2) ProtoMessage() {}

func (x *PredefinedInt64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedInt64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedInt64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{3}
}

func (x *PredefinedInt64RuleProto2) GetVal() int64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedUInt32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *uint32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedUInt32RuleProto2) Reset() {
	*x = PredefinedUInt32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedUInt32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedUInt32RuleProto2) ProtoMessage() {}

func (x *PredefinedUInt32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedUInt32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedUInt32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{4}
}

func (x *PredefinedUInt32RuleProto2) GetVal() uint32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedUInt64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *uint64 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedUInt64RuleProto2) Reset() {
	*x = PredefinedUInt64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedUInt64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedUInt64RuleProto2) ProtoMessage() {}

func (x *PredefinedUInt64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedUInt64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedUInt64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{5}
}

func (x *PredefinedUInt64RuleProto2) GetVal() uint64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedSInt32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"zigzag32,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedSInt32RuleProto2) Reset() {
	*x = PredefinedSInt32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSInt32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSInt32RuleProto2) ProtoMessage() {}

func (x *PredefinedSInt32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSInt32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedSInt32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{6}
}

func (x *PredefinedSInt32RuleProto2) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedSInt64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int64 `protobuf:"zigzag64,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedSInt64RuleProto2) Reset() {
	*x = PredefinedSInt64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSInt64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSInt64RuleProto2) ProtoMessage() {}

func (x *PredefinedSInt64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSInt64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedSInt64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{7}
}

func (x *PredefinedSInt64RuleProto2) GetVal() int64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedFixed32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *uint32 `protobuf:"fixed32,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedFixed32RuleProto2) Reset() {
	*x = PredefinedFixed32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFixed32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFixed32RuleProto2) ProtoMessage() {}

func (x *PredefinedFixed32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFixed32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedFixed32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{8}
}

func (x *PredefinedFixed32RuleProto2) GetVal() uint32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedFixed64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *uint64 `protobuf:"fixed64,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedFixed64RuleProto2) Reset() {
	*x = PredefinedFixed64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedFixed64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedFixed64RuleProto2) ProtoMessage() {}

func (x *PredefinedFixed64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedFixed64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedFixed64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{9}
}

func (x *PredefinedFixed64RuleProto2) GetVal() uint64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedSFixed32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"fixed32,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedSFixed32RuleProto2) Reset() {
	*x = PredefinedSFixed32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSFixed32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSFixed32RuleProto2) ProtoMessage() {}

func (x *PredefinedSFixed32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSFixed32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedSFixed32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{10}
}

func (x *PredefinedSFixed32RuleProto2) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedSFixed64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int64 `protobuf:"fixed64,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedSFixed64RuleProto2) Reset() {
	*x = PredefinedSFixed64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedSFixed64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedSFixed64RuleProto2) ProtoMessage() {}

func (x *PredefinedSFixed64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedSFixed64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedSFixed64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{11}
}

func (x *PredefinedSFixed64RuleProto2) GetVal() int64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type PredefinedBoolRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *bool `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedBoolRuleProto2) Reset() {
	*x = PredefinedBoolRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedBoolRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedBoolRuleProto2) ProtoMessage() {}

func (x *PredefinedBoolRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedBoolRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedBoolRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{12}
}

func (x *PredefinedBoolRuleProto2) GetVal() bool {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return false
}

type PredefinedStringRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedStringRuleProto2) Reset() {
	*x = PredefinedStringRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedStringRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedStringRuleProto2) ProtoMessage() {}

func (x *PredefinedStringRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedStringRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedStringRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{13}
}

func (x *PredefinedStringRuleProto2) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type PredefinedBytesRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []byte `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedBytesRuleProto2) Reset() {
	*x = PredefinedBytesRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedBytesRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedBytesRuleProto2) ProtoMessage() {}

func (x *PredefinedBytesRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedBytesRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedBytesRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{14}
}

func (x *PredefinedBytesRuleProto2) GetVal() []byte {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedEnumRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *PredefinedEnumRuleProto2_EnumProto2 `protobuf:"varint,1,opt,name=val,enum=buf.validate.conformance.cases.PredefinedEnumRuleProto2_EnumProto2" json:"val,omitempty"`
}

func (x *PredefinedEnumRuleProto2) Reset() {
	*x = PredefinedEnumRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedEnumRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedEnumRuleProto2) ProtoMessage() {}

func (x *PredefinedEnumRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedEnumRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedEnumRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{15}
}

func (x *PredefinedEnumRuleProto2) GetVal() PredefinedEnumRuleProto2_EnumProto2 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return PredefinedEnumRuleProto2_ENUM_PROTO2_ZERO_UNSPECIFIED
}

type PredefinedRepeatedRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []uint64 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedRuleProto2) Reset() {
	*x = PredefinedRepeatedRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedRuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{16}
}

func (x *PredefinedRepeatedRuleProto2) GetVal() []uint64 {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedDurationRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *durationpb.Duration `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedDurationRuleProto2) Reset() {
	*x = PredefinedDurationRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedDurationRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedDurationRuleProto2) ProtoMessage() {}

func (x *PredefinedDurationRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedDurationRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedDurationRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{17}
}

func (x *PredefinedDurationRuleProto2) GetVal() *durationpb.Duration {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedTimestampRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedTimestampRuleProto2) Reset() {
	*x = PredefinedTimestampRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedTimestampRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedTimestampRuleProto2) ProtoMessage() {}

func (x *PredefinedTimestampRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedTimestampRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedTimestampRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{18}
}

func (x *PredefinedTimestampRuleProto2) GetVal() *timestamppb.Timestamp {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedFloatRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.FloatValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedFloatRuleProto2) Reset() {
	*x = PredefinedWrappedFloatRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedFloatRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedFloatRuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedFloatRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedFloatRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedFloatRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{19}
}

func (x *PredefinedWrappedFloatRuleProto2) GetVal() *wrapperspb.FloatValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedDoubleRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.DoubleValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedDoubleRuleProto2) Reset() {
	*x = PredefinedWrappedDoubleRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedDoubleRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedDoubleRuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedDoubleRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedDoubleRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedDoubleRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{20}
}

func (x *PredefinedWrappedDoubleRuleProto2) GetVal() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedInt32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.Int32Value `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedInt32RuleProto2) Reset() {
	*x = PredefinedWrappedInt32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedInt32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedInt32RuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedInt32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedInt32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedInt32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{21}
}

func (x *PredefinedWrappedInt32RuleProto2) GetVal() *wrapperspb.Int32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedInt64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.Int64Value `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedInt64RuleProto2) Reset() {
	*x = PredefinedWrappedInt64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedInt64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedInt64RuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedInt64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedInt64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedInt64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{22}
}

func (x *PredefinedWrappedInt64RuleProto2) GetVal() *wrapperspb.Int64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedUInt32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedUInt32RuleProto2) Reset() {
	*x = PredefinedWrappedUInt32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedUInt32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedUInt32RuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedUInt32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedUInt32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedUInt32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{23}
}

func (x *PredefinedWrappedUInt32RuleProto2) GetVal() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedUInt64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.UInt64Value `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedUInt64RuleProto2) Reset() {
	*x = PredefinedWrappedUInt64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedUInt64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedUInt64RuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedUInt64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedUInt64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedUInt64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{24}
}

func (x *PredefinedWrappedUInt64RuleProto2) GetVal() *wrapperspb.UInt64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedBoolRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.BoolValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedBoolRuleProto2) Reset() {
	*x = PredefinedWrappedBoolRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedBoolRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedBoolRuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedBoolRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedBoolRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedBoolRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{25}
}

func (x *PredefinedWrappedBoolRuleProto2) GetVal() *wrapperspb.BoolValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedStringRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedStringRuleProto2) Reset() {
	*x = PredefinedWrappedStringRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedStringRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedStringRuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedStringRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedStringRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedStringRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{26}
}

func (x *PredefinedWrappedStringRuleProto2) GetVal() *wrapperspb.StringValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedWrappedBytesRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *wrapperspb.BytesValue `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *PredefinedWrappedBytesRuleProto2) Reset() {
	*x = PredefinedWrappedBytesRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedWrappedBytesRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedWrappedBytesRuleProto2) ProtoMessage() {}

func (x *PredefinedWrappedBytesRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedWrappedBytesRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedWrappedBytesRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{27}
}

func (x *PredefinedWrappedBytesRuleProto2) GetVal() *wrapperspb.BytesValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedFloatRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.FloatValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedFloatRuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedFloatRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedFloatRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedFloatRuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedFloatRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedFloatRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedFloatRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{28}
}

func (x *PredefinedRepeatedWrappedFloatRuleProto2) GetVal() []*wrapperspb.FloatValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedDoubleRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.DoubleValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedDoubleRuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedDoubleRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedDoubleRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedDoubleRuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedDoubleRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedDoubleRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedDoubleRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{29}
}

func (x *PredefinedRepeatedWrappedDoubleRuleProto2) GetVal() []*wrapperspb.DoubleValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedInt32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.Int32Value `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedInt32RuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedInt32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedInt32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedInt32RuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedInt32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedInt32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedInt32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{30}
}

func (x *PredefinedRepeatedWrappedInt32RuleProto2) GetVal() []*wrapperspb.Int32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedInt64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.Int64Value `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedInt64RuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedInt64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedInt64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedInt64RuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedInt64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedInt64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedInt64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{31}
}

func (x *PredefinedRepeatedWrappedInt64RuleProto2) GetVal() []*wrapperspb.Int64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedUInt32RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.UInt32Value `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedUInt32RuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedUInt32RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedUInt32RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedUInt32RuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedUInt32RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedUInt32RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedUInt32RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{32}
}

func (x *PredefinedRepeatedWrappedUInt32RuleProto2) GetVal() []*wrapperspb.UInt32Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedUInt64RuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.UInt64Value `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedUInt64RuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedUInt64RuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedUInt64RuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedUInt64RuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedUInt64RuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedUInt64RuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedUInt64RuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{33}
}

func (x *PredefinedRepeatedWrappedUInt64RuleProto2) GetVal() []*wrapperspb.UInt64Value {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedBoolRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.BoolValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedBoolRuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedBoolRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedBoolRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedBoolRuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedBoolRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedBoolRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedBoolRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{34}
}

func (x *PredefinedRepeatedWrappedBoolRuleProto2) GetVal() []*wrapperspb.BoolValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedStringRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.StringValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedStringRuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedStringRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedStringRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedStringRuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedStringRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedStringRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedStringRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{35}
}

func (x *PredefinedRepeatedWrappedStringRuleProto2) GetVal() []*wrapperspb.StringValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedRepeatedWrappedBytesRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []*wrapperspb.BytesValue `protobuf:"bytes,1,rep,name=val" json:"val,omitempty"`
}

func (x *PredefinedRepeatedWrappedBytesRuleProto2) Reset() {
	*x = PredefinedRepeatedWrappedBytesRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedRepeatedWrappedBytesRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedRepeatedWrappedBytesRuleProto2) ProtoMessage() {}

func (x *PredefinedRepeatedWrappedBytesRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedRepeatedWrappedBytesRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedRepeatedWrappedBytesRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{36}
}

func (x *PredefinedRepeatedWrappedBytesRuleProto2) GetVal() []*wrapperspb.BytesValue {
	if x != nil {
		return x.Val
	}
	return nil
}

type PredefinedAndCustomRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32                                `protobuf:"zigzag32,1,opt,name=a" json:"a,omitempty"`
	B *PredefinedAndCustomRuleProto2_Nested `protobuf:"bytes,2,opt,name=b" json:"b,omitempty"`
}

func (x *PredefinedAndCustomRuleProto2) Reset() {
	*x = PredefinedAndCustomRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedAndCustomRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedAndCustomRuleProto2) ProtoMessage() {}

func (x *PredefinedAndCustomRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedAndCustomRuleProto2.ProtoReflect.Descriptor instead.
func (*PredefinedAndCustomRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{37}
}

func (x *PredefinedAndCustomRuleProto2) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *PredefinedAndCustomRuleProto2) GetB() *PredefinedAndCustomRuleProto2_Nested {
	if x != nil {
		return x.B
	}
	return nil
}

type StandardPredefinedAndCustomRuleProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"zigzag32,1,opt,name=a" json:"a,omitempty"`
}

func (x *StandardPredefinedAndCustomRuleProto2) Reset() {
	*x = StandardPredefinedAndCustomRuleProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StandardPredefinedAndCustomRuleProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StandardPredefinedAndCustomRuleProto2) ProtoMessage() {}

func (x *StandardPredefinedAndCustomRuleProto2) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StandardPredefinedAndCustomRuleProto2.ProtoReflect.Descriptor instead.
func (*StandardPredefinedAndCustomRuleProto2) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{38}
}

func (x *StandardPredefinedAndCustomRuleProto2) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

type PredefinedAndCustomRuleProto2_Nested struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C *int32 `protobuf:"zigzag32,1,opt,name=c" json:"c,omitempty"`
}

func (x *PredefinedAndCustomRuleProto2_Nested) Reset() {
	*x = PredefinedAndCustomRuleProto2_Nested{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredefinedAndCustomRuleProto2_Nested) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredefinedAndCustomRuleProto2_Nested) ProtoMessage() {}

func (x *PredefinedAndCustomRuleProto2_Nested) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredefinedAndCustomRuleProto2_Nested.ProtoReflect.Descriptor instead.
func (*PredefinedAndCustomRuleProto2_Nested) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP(), []int{37, 0}
}

func (x *PredefinedAndCustomRuleProto2_Nested) GetC() int32 {
	if x != nil && x.C != nil {
		return *x.C
	}
	return 0
}

var file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*validate.FloatRules)(nil),
		ExtensionType: (*float32)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.float_abs_range_proto2",
		Tag:           "fixed32,1161,opt,name=float_abs_range_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.DoubleRules)(nil),
		ExtensionType: (*float64)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.double_abs_range_proto2",
		Tag:           "fixed64,1161,opt,name=double_abs_range_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.Int32Rules)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.int32_abs_in_proto2",
		Tag:           "varint,1161,rep,name=int32_abs_in_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.Int64Rules)(nil),
		ExtensionType: ([]*wrapperspb.Int64Value)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.int64_abs_in_proto2",
		Tag:           "bytes,1161,rep,name=int64_abs_in_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.UInt32Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.uint32_even_proto2",
		Tag:           "varint,1161,opt,name=uint32_even_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.UInt64Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.uint64_even_proto2",
		Tag:           "varint,1161,opt,name=uint64_even_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.SInt32Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.sint32_even_proto2",
		Tag:           "varint,1161,opt,name=sint32_even_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.SInt64Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.sint64_even_proto2",
		Tag:           "varint,1161,opt,name=sint64_even_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.Fixed32Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.fixed32_even_proto2",
		Tag:           "varint,1161,opt,name=fixed32_even_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.Fixed64Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.fixed64_even_proto2",
		Tag:           "varint,1161,opt,name=fixed64_even_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.SFixed32Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.sfixed32_even_proto2",
		Tag:           "varint,1161,opt,name=sfixed32_even_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.SFixed64Rules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.sfixed64_even_proto2",
		Tag:           "varint,1161,opt,name=sfixed64_even_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.BoolRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.bool_false_proto2",
		Tag:           "varint,1161,opt,name=bool_false_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.StringRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.string_valid_path_proto2",
		Tag:           "varint,1161,opt,name=string_valid_path_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.BytesRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.bytes_valid_path_proto2",
		Tag:           "varint,1161,opt,name=bytes_valid_path_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.EnumRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.enum_non_zero_proto2",
		Tag:           "varint,1161,opt,name=enum_non_zero_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.RepeatedRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.repeated_at_least_five_proto2",
		Tag:           "varint,1161,opt,name=repeated_at_least_five_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.DurationRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.duration_too_long_proto2",
		Tag:           "varint,1161,opt,name=duration_too_long_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
	{
		ExtendedType:  (*validate.TimestampRules)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1161,
		Name:          "buf.validate.conformance.cases.timestamp_in_range_proto2",
		Tag:           "varint,1161,opt,name=timestamp_in_range_proto2",
		Filename:      "buf/validate/conformance/cases/predefined_rules_proto2.proto",
	},
}

// Extension fields to validate.FloatRules.
var (
	// optional float float_abs_range_proto2 = 1161;
	E_FloatAbsRangeProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[0]
)

// Extension fields to validate.DoubleRules.
var (
	// optional double double_abs_range_proto2 = 1161;
	E_DoubleAbsRangeProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[1]
)

// Extension fields to validate.Int32Rules.
var (
	// repeated int32 int32_abs_in_proto2 = 1161;
	E_Int32AbsInProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[2]
)

// Extension fields to validate.Int64Rules.
var (
	// repeated google.protobuf.Int64Value int64_abs_in_proto2 = 1161;
	E_Int64AbsInProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[3]
)

// Extension fields to validate.UInt32Rules.
var (
	// optional bool uint32_even_proto2 = 1161;
	E_Uint32EvenProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[4]
)

// Extension fields to validate.UInt64Rules.
var (
	// optional bool uint64_even_proto2 = 1161;
	E_Uint64EvenProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[5]
)

// Extension fields to validate.SInt32Rules.
var (
	// optional bool sint32_even_proto2 = 1161;
	E_Sint32EvenProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[6]
)

// Extension fields to validate.SInt64Rules.
var (
	// optional bool sint64_even_proto2 = 1161;
	E_Sint64EvenProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[7]
)

// Extension fields to validate.Fixed32Rules.
var (
	// optional bool fixed32_even_proto2 = 1161;
	E_Fixed32EvenProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[8]
)

// Extension fields to validate.Fixed64Rules.
var (
	// optional bool fixed64_even_proto2 = 1161;
	E_Fixed64EvenProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[9]
)

// Extension fields to validate.SFixed32Rules.
var (
	// optional bool sfixed32_even_proto2 = 1161;
	E_Sfixed32EvenProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[10]
)

// Extension fields to validate.SFixed64Rules.
var (
	// optional bool sfixed64_even_proto2 = 1161;
	E_Sfixed64EvenProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[11]
)

// Extension fields to validate.BoolRules.
var (
	// optional bool bool_false_proto2 = 1161;
	E_BoolFalseProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[12]
)

// Extension fields to validate.StringRules.
var (
	// optional bool string_valid_path_proto2 = 1161;
	E_StringValidPathProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[13]
)

// Extension fields to validate.BytesRules.
var (
	// optional bool bytes_valid_path_proto2 = 1161;
	E_BytesValidPathProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[14]
)

// Extension fields to validate.EnumRules.
var (
	// optional bool enum_non_zero_proto2 = 1161;
	E_EnumNonZeroProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[15]
)

// Extension fields to validate.RepeatedRules.
var (
	// optional bool repeated_at_least_five_proto2 = 1161;
	E_RepeatedAtLeastFiveProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[16]
)

// Extension fields to validate.DurationRules.
var (
	// optional bool duration_too_long_proto2 = 1161;
	E_DurationTooLongProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[17]
)

// Extension fields to validate.TimestampRules.
var (
	// optional bool timestamp_in_range_proto2 = 1161;
	E_TimestampInRangeProto2 = &file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes[18]
)

var File_buf_validate_conformance_cases_predefined_rules_proto2_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x73, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x1b,
	0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3a, 0x0a, 0x19,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x0a, 0x06, 0xcd, 0x48, 0x00,
	0x00, 0x80, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3f, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x12, 0x0a, 0xc9, 0x48, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x40, 0x0a, 0x19, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x23, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x11, 0xba, 0x48, 0x0e, 0x1a, 0x0c, 0xc8, 0x48, 0xfe, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x42, 0x0a, 0x19, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x25, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x13, 0xba, 0x48, 0x10, 0x22, 0x0e, 0xca, 0x48, 0x0b, 0x08,
	0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x38, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x55, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x08, 0xba, 0x48, 0x05, 0x2a,
	0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x1a, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x08, 0xba, 0x48, 0x05, 0x32, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x08,
	0xba, 0x48, 0x05, 0x3a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a,
	0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x08, 0xba, 0x48, 0x05, 0x42, 0x03, 0xc8,
	0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x39, 0x0a, 0x1b, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x07, 0x42, 0x08, 0xba, 0x48, 0x05, 0x4a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x39, 0x0a, 0x1b, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x08,
	0xba, 0x48, 0x05, 0x52, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3a, 0x0a,
	0x1c, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x08, 0xba, 0x48, 0x05, 0x5a,
	0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3a, 0x0a, 0x1c, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52,
	0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x08, 0xba, 0x48, 0x05, 0x62, 0x03, 0xc8, 0x48, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x36, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x08,
	0xba, 0x48, 0x05, 0x6a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a,
	0x1a, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xc8,
	0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x37, 0x0a, 0x19, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x42, 0x08, 0xba, 0x48, 0x05, 0x7a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0xc1, 0x01, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x60, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x42,
	0x09, 0xba, 0x48, 0x06, 0x82, 0x01, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x43, 0x0a, 0x0a, 0x45, 0x6e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x20, 0x0a,
	0x1c, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x32, 0x5f, 0x5a, 0x45, 0x52,
	0x4f, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x32, 0x5f, 0x4f,
	0x4e, 0x45, 0x10, 0x01, 0x22, 0x3b, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x04, 0x42, 0x09, 0xba, 0x48, 0x06, 0x92, 0x01, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x56, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x12, 0x36, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0xba, 0x48, 0x06, 0xaa, 0x01,
	0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x58, 0x0a, 0x1d, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x37, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x42, 0x09, 0xba, 0x48, 0x06, 0xb2, 0x01, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x5e, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x3a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x0a, 0x06, 0xcd, 0x48, 0x00, 0x00, 0x80, 0x3f, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x64, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x3f, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x12, 0x0a, 0xc9, 0x48, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x64, 0x0a, 0x20, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x40, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x11, 0xba, 0x48, 0x0e, 0x1a, 0x0c, 0xc8, 0x48,
	0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x66, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x12, 0x42, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x13, 0xba,
	0x48, 0x10, 0x22, 0x0e, 0xca, 0x48, 0x0b, 0x08, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5d, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x38, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x2a, 0x03, 0xc8, 0x48,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5d, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x55, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x38, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x32, 0x03, 0xc8, 0x48, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x59, 0x0a, 0x1f, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x36, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x6a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x5d, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x38, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x5b, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x57, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x12, 0x37, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xba,
	0x48, 0x05, 0x7a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x6b, 0x0a, 0x28,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x3f, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x92, 0x01, 0x0a, 0x22, 0x08, 0x0a, 0x06, 0xcd, 0x48,
	0x00, 0x00, 0x80, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x71, 0x0a, 0x29, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x44, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x14, 0xba, 0x48, 0x11, 0x92, 0x01, 0x0e, 0x22, 0x0c, 0x12, 0x0a, 0xc9, 0x48, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x71, 0x0a, 0x28,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x45, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x42, 0x16, 0xba, 0x48, 0x13, 0x92, 0x01, 0x10, 0x22, 0x0e, 0x1a, 0x0c, 0xc8, 0x48,
	0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x73, 0x0a, 0x28, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x47, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x18, 0xba, 0x48, 0x15, 0x92, 0x01, 0x12, 0x22, 0x10, 0x22,
	0x0e, 0xca, 0x48, 0x0b, 0x08, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x6a, 0x0a, 0x29, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x64, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x12, 0x3d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba, 0x48,
	0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x2a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x6a, 0x0a, 0x29, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x55, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x3d, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07,
	0x22, 0x05, 0x32, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x66, 0x0a, 0x27,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x3b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x6a, 0x03, 0xc8, 0x48, 0x01, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x6a, 0x0a, 0x29, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x12, 0x3d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba, 0x48,
	0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x72, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x68, 0x0a, 0x28, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x3c, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0d, 0xba, 0x48, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05,
	0x7a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xbe, 0x03, 0x0a, 0x1d, 0x50,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x71, 0x0a, 0x01,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x63, 0xba, 0x48, 0x60, 0xba, 0x01, 0x58, 0x0a,
	0x28, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x63, 0x61, 0x6c,
	0x61, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x1a, 0x2c, 0x74, 0x68, 0x69, 0x73, 0x20,
	0x3e, 0x20, 0x32, 0x34, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x20, 0x3a, 0x20, 0x27, 0x61, 0x20, 0x6d,
	0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x20, 0x74,
	0x68, 0x61, 0x6e, 0x20, 0x32, 0x34, 0x27, 0x3a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x01, 0x61, 0x12,
	0xb4, 0x01, 0x0a, 0x01, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x65,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x42, 0x60, 0xba, 0x48, 0x5d, 0xba, 0x01, 0x5a, 0x0a, 0x2a, 0x70, 0x72, 0x65, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1b, 0x62, 0x2e, 0x63, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20,
	0x62, 0x65, 0x20, 0x61, 0x20, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x20, 0x6f, 0x66,
	0x20, 0x33, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x63, 0x20, 0x25, 0x20, 0x33, 0x20, 0x3d,
	0x3d, 0x20, 0x30, 0x52, 0x01, 0x62, 0x1a, 0x73, 0x0a, 0x06, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x12, 0x69, 0x0a, 0x01, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x5b, 0xba, 0x48, 0x58,
	0xba, 0x01, 0x50, 0x0a, 0x28, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x1a, 0x24, 0x74,
	0x68, 0x69, 0x73, 0x20, 0x3e, 0x20, 0x30, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x20, 0x3a, 0x20, 0x27,
	0x63, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x27, 0x3a, 0x03, 0xc8, 0x48, 0x01, 0x52, 0x01, 0x63, 0x22, 0xa5, 0x01, 0x0a, 0x25,
	0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x7c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11,
	0x42, 0x6e, 0xba, 0x48, 0x6b, 0xba, 0x01, 0x61, 0x0a, 0x31, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x72, 0x64, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x6e,
	0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x63,
	0x61, 0x6c, 0x61, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x1a, 0x2c, 0x74, 0x68, 0x69,
	0x73, 0x20, 0x3e, 0x20, 0x32, 0x34, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x20, 0x3a, 0x20, 0x27, 0x61,
	0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72,
	0x20, 0x74, 0x68, 0x61, 0x6e, 0x20, 0x32, 0x34, 0x27, 0x3a, 0x05, 0xc8, 0x48, 0x01, 0x10, 0x38,
	0x52, 0x01, 0x61, 0x3a, 0xa9, 0x01, 0x0a, 0x16, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x61, 0x62,
	0x73, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x18,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x02, 0x42,
	0x59, 0xc2, 0x48, 0x56, 0x0a, 0x54, 0x0a, 0x16, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x2e, 0x61, 0x62,
	0x73, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1b,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6f,
	0x75, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x1a, 0x1d, 0x74, 0x68, 0x69,
	0x73, 0x20, 0x3e, 0x3d, 0x20, 0x2d, 0x72, 0x75, 0x6c, 0x65, 0x20, 0x26, 0x26, 0x20, 0x74, 0x68,
	0x69, 0x73, 0x20, 0x3c, 0x3d, 0x20, 0x72, 0x75, 0x6c, 0x65, 0x52, 0x13, 0x66, 0x6c, 0x6f, 0x61,
	0x74, 0x41, 0x62, 0x73, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a,
	0xae, 0x01, 0x0a, 0x17, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x62, 0x73, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x19, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x01, 0x42, 0x5b, 0xc2,
	0x48, 0x58, 0x0a, 0x56, 0x0a, 0x17, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x62, 0x73,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1c, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6f,
	0x75, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x1a, 0x1d, 0x74, 0x68, 0x69,
	0x73, 0x20, 0x3e, 0x3d, 0x20, 0x2d, 0x72, 0x75, 0x6c, 0x65, 0x20, 0x26, 0x26, 0x20, 0x74, 0x68,
	0x69, 0x73, 0x20, 0x3c, 0x3d, 0x20, 0x72, 0x75, 0x6c, 0x65, 0x52, 0x14, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x41, 0x62, 0x73, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x3a, 0xb6, 0x01, 0x0a, 0x13, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x61, 0x62, 0x73, 0x5f, 0x69,
	0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x18, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x03, 0x28, 0x05, 0x42, 0x6c, 0xc2, 0x48, 0x69, 0x0a, 0x67,
	0x0a, 0x13, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x2e, 0x61, 0x62, 0x73, 0x5f, 0x69, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x27, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6d, 0x75, 0x73,
	0x74, 0x20, 0x62, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x61, 0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65,
	0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6f, 0x66, 0x20, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x27,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x72, 0x75, 0x6c, 0x65, 0x20, 0x7c, 0x7c, 0x20,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x28, 0x6e, 0x2c, 0x20, 0x2d, 0x6e, 0x29, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x41, 0x62,
	0x73, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a, 0xd3, 0x01, 0x0a, 0x13, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x5f, 0x61, 0x62, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x12, 0x18, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x6c, 0xc2, 0x48, 0x69, 0x0a, 0x67, 0x0a, 0x13, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x2e, 0x61, 0x62,
	0x73, 0x5f, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x27, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x61, 0x62,
	0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6f, 0x66, 0x20,
	0x6c, 0x69, 0x73, 0x74, 0x1a, 0x27, 0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x72, 0x75,
	0x6c, 0x65, 0x20, 0x7c, 0x7c, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x72, 0x75,
	0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x28, 0x6e, 0x2c, 0x20, 0x2d, 0x6e, 0x29, 0x52, 0x10, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x41, 0x62, 0x73, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a,
	0x8e, 0x01, 0x0a, 0x12, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x44, 0xc2, 0x48, 0x41, 0x0a, 0x3f, 0x0a,
	0x12, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x12, 0x18, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x20, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a, 0x0f, 0x74,
	0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x75, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x75, 0x52, 0x10,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x76, 0x65, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x3a, 0x8e, 0x01, 0x0a, 0x12, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x44, 0xc2, 0x48, 0x41, 0x0a, 0x3f,
	0x0a, 0x12, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x12, 0x18, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x20, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a, 0x0f,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x75, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x75, 0x52,
	0x10, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x76, 0x65, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x3a, 0x8c, 0x01, 0x0a, 0x12, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x42, 0xc2, 0x48, 0x3f, 0x0a,
	0x3d, 0x0a, 0x12, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x18, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x20, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a,
	0x0d, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x52, 0x10,
	0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x76, 0x65, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x3a, 0x8c, 0x01, 0x0a, 0x12, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x42, 0xc2, 0x48, 0x3f, 0x0a, 0x3d,
	0x0a, 0x12, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x12, 0x18, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x20, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a, 0x0d,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x52, 0x10, 0x73,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x76, 0x65, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a,
	0x93, 0x01, 0x0a, 0x13, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x46, 0xc2, 0x48, 0x43, 0x0a,
	0x41, 0x0a, 0x13, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x19, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x20,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65,
	0x6e, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x75, 0x20, 0x3d, 0x3d, 0x20,
	0x30, 0x75, 0x52, 0x11, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x76, 0x65, 0x6e, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a, 0x93, 0x01, 0x0a, 0x13, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x2e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x46, 0xc2, 0x48, 0x43, 0x0a, 0x41, 0x0a, 0x13, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x19, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e,
	0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a, 0x0f, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20,
	0x32, 0x75, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x75, 0x52, 0x11, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x45, 0x76, 0x65, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a, 0x96, 0x01, 0x0a, 0x14,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1b, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x46, 0xc2, 0x48, 0x43, 0x0a, 0x41, 0x0a,
	0x14, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x20,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65,
	0x6e, 0x1a, 0x0d, 0x74, 0x68, 0x69, 0x73, 0x20, 0x25, 0x20, 0x32, 0x20, 0x3d, 0x3d, 0x20, 0x30,
	0x52, 0x12, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x76, 0x65, 0x6e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x3a, 0x96, 0x01, 0x0a, 0x14, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1b, 0x2e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x46, 0xc2, 0x48, 0x43, 0x0a, 0x41, 0x0a, 0x14, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1a,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69,
	0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x1a, 0x0d, 0x74, 0x68, 0x69, 0x73,
	0x20, 0x25, 0x20, 0x32, 0x20, 0x3d, 0x3d, 0x20, 0x30, 0x52, 0x12, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x45, 0x76, 0x65, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a, 0x86, 0x01,
	0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x12, 0x17, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x40, 0xc2, 0x48, 0x3d, 0x0a, 0x3b, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6c,
	0x2e, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x17, 0x62,
	0x6f, 0x6f, 0x6c, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74,
	0x20, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x1a, 0x0d, 0x74, 0x68, 0x69, 0x73, 0x20, 0x3d, 0x3d, 0x20,
	0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0f, 0x62, 0x6f, 0x6f, 0x6c, 0x46, 0x61, 0x6c, 0x73, 0x65,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a, 0xfe, 0x01, 0x0a, 0x18, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x12, 0x19, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0xa8, 0x01, 0xc2, 0x48, 0xa4, 0x01, 0x0a, 0xa1, 0x01, 0x0a,
	0x18, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x1a, 0x84, 0x01, 0x21, 0x74, 0x68, 0x69,
	0x73, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x5b, 0x5e, 0x2f,
	0x2e, 0x5d, 0x5b, 0x5e, 0x2f, 0x5d, 0x3f, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x5b, 0x5e, 0x2f, 0x2e,
	0x5d, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x7b, 0x33, 0x2c, 0x7d, 0x29, 0x28, 0x2f, 0x28, 0x5b, 0x5e,
	0x2f, 0x2e, 0x5d, 0x5b, 0x5e, 0x2f, 0x5d, 0x3f, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x5b, 0x5e, 0x2f,
	0x2e, 0x5d, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x7b, 0x33, 0x2c, 0x7d, 0x29, 0x29, 0x2a, 0x24, 0x27,
	0x29, 0x20, 0x3f, 0x20, 0x27, 0x6e, 0x6f, 0x74, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x20, 0x70, 0x61, 0x74, 0x68, 0x3a, 0x20, 0x60, 0x25, 0x73, 0x60, 0x27, 0x2e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x28, 0x5b, 0x74, 0x68, 0x69, 0x73, 0x5d, 0x29, 0x20, 0x3a, 0x20, 0x27, 0x27,
	0x52, 0x15, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x61, 0x74,
	0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a, 0x82, 0x02, 0x0a, 0x17, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x12, 0x18, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x42, 0xaf, 0x01, 0xc2, 0x48, 0xab, 0x01, 0x0a, 0xa8, 0x01, 0x0a, 0x17,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x1a, 0x8c, 0x01, 0x21, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28,
	0x27, 0x5e, 0x28, 0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x5b, 0x5e, 0x2f, 0x5d, 0x3f, 0x7c, 0x5b, 0x5e,
	0x2f, 0x5d, 0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x7b, 0x33, 0x2c, 0x7d,
	0x29, 0x28, 0x2f, 0x28, 0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x5b, 0x5e, 0x2f, 0x5d, 0x3f, 0x7c, 0x5b,
	0x5e, 0x2f, 0x5d, 0x5b, 0x5e, 0x2f, 0x2e, 0x5d, 0x7c, 0x5b, 0x5e, 0x2f, 0x5d, 0x7b, 0x33, 0x2c,
	0x7d, 0x29, 0x29, 0x2a, 0x24, 0x27, 0x29, 0x20, 0x3f, 0x20, 0x27, 0x6e, 0x6f, 0x74, 0x20, 0x61,
	0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x70, 0x61, 0x74, 0x68, 0x3a, 0x20, 0x60, 0x25, 0x73,
	0x60, 0x27, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x28, 0x5b, 0x74, 0x68, 0x69, 0x73, 0x5d,
	0x29, 0x20, 0x3a, 0x20, 0x27, 0x27, 0x52, 0x14, 0x62, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x50, 0x61, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a, 0x92, 0x01, 0x0a,
	0x14, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x6e, 0x6f, 0x6e, 0x5f, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x17, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x47, 0xc2, 0x48, 0x44, 0x0a, 0x42, 0x0a, 0x14, 0x65, 0x6e,
	0x75, 0x6d, 0x2e, 0x6e, 0x6f, 0x6e, 0x5f, 0x7a, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x12, 0x1a, 0x65, 0x6e, 0x75, 0x6d, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69,
	0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x6e, 0x6f, 0x6e, 0x2d, 0x7a, 0x65, 0x72, 0x6f, 0x1a, 0x0e,
	0x69, 0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x21, 0x3d, 0x20, 0x30, 0x52, 0x11,
	0x65, 0x6e, 0x75, 0x6d, 0x4e, 0x6f, 0x6e, 0x5a, 0x65, 0x72, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x3a, 0xcc, 0x01, 0x0a, 0x1d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x5f, 0x6c, 0x65, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x12, 0x1b, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x6c, 0xc2, 0x48, 0x69, 0x0a, 0x67, 0x0a, 0x1d,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x2e, 0x61, 0x74, 0x5f, 0x6c, 0x65, 0x61, 0x73,
	0x74, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x2d, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x20, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x20, 0x6d, 0x75,
	0x73, 0x74, 0x20, 0x68, 0x61, 0x76, 0x65, 0x20, 0x61, 0x74, 0x20, 0x6c, 0x65, 0x61, 0x73, 0x74,
	0x20, 0x66, 0x69, 0x76, 0x65, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x17, 0x75, 0x69,
	0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x73, 0x69, 0x7a, 0x65, 0x28, 0x29, 0x29, 0x20,
	0x3e, 0x3d, 0x20, 0x35, 0x75, 0x52, 0x19, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x4c, 0x65, 0x61, 0x73, 0x74, 0x46, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x3a, 0xb9, 0x01, 0x0a, 0x18, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f,
	0x6f, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1b, 0x2e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x62, 0xc2, 0x48, 0x5f, 0x0a, 0x5d, 0x0a, 0x18, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x74, 0x6f, 0x6f, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x12, 0x28, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x61, 0x6e,
	0x27, 0x74, 0x20, 0x62, 0x65, 0x20, 0x6c, 0x6f, 0x6e, 0x67, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61,
	0x6e, 0x20, 0x31, 0x30, 0x20, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x1a, 0x17, 0x74, 0x68,
	0x69, 0x73, 0x20, 0x3c, 0x3d, 0x20, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x28, 0x27,
	0x31, 0x30, 0x73, 0x27, 0x29, 0x52, 0x15, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x6f, 0x4c, 0x6f, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x3a, 0xc8, 0x01, 0x0a,
	0x19, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x69, 0x6e, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x1c, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x89, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x6e, 0xc2, 0x48, 0x6b, 0x0a, 0x69, 0x0a, 0x1b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x12, 0x16, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x6f,
	0x75, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x1a, 0x32, 0x69, 0x6e, 0x74,
	0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x3e, 0x3d, 0x20, 0x31, 0x30, 0x34, 0x39, 0x35, 0x38,
	0x37, 0x32, 0x30, 0x30, 0x20, 0x26, 0x26, 0x20, 0x69, 0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73,
	0x29, 0x20, 0x3c, 0x3d, 0x20, 0x31, 0x30, 0x38, 0x30, 0x34, 0x33, 0x32, 0x30, 0x30, 0x30, 0x52,
	0x16, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x49, 0x6e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x42, 0xb1, 0x02, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x42, 0x1a,
	0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67,
	0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65,
	0x73, 0xa2, 0x02, 0x04, 0x42, 0x56, 0x43, 0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66,
	0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65, 0x73,
}

var (
	file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescData = file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDesc
)

func file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDescData
}

var file_buf_validate_conformance_cases_predefined_rules_proto2_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_buf_validate_conformance_cases_predefined_rules_proto2_proto_goTypes = []any{
	(PredefinedEnumRuleProto2_EnumProto2)(0),          // 0: buf.validate.conformance.cases.PredefinedEnumRuleProto2.EnumProto2
	(*PredefinedFloatRuleProto2)(nil),                 // 1: buf.validate.conformance.cases.PredefinedFloatRuleProto2
	(*PredefinedDoubleRuleProto2)(nil),                // 2: buf.validate.conformance.cases.PredefinedDoubleRuleProto2
	(*PredefinedInt32RuleProto2)(nil),                 // 3: buf.validate.conformance.cases.PredefinedInt32RuleProto2
	(*PredefinedInt64RuleProto2)(nil),                 // 4: buf.validate.conformance.cases.PredefinedInt64RuleProto2
	(*PredefinedUInt32RuleProto2)(nil),                // 5: buf.validate.conformance.cases.PredefinedUInt32RuleProto2
	(*PredefinedUInt64RuleProto2)(nil),                // 6: buf.validate.conformance.cases.PredefinedUInt64RuleProto2
	(*PredefinedSInt32RuleProto2)(nil),                // 7: buf.validate.conformance.cases.PredefinedSInt32RuleProto2
	(*PredefinedSInt64RuleProto2)(nil),                // 8: buf.validate.conformance.cases.PredefinedSInt64RuleProto2
	(*PredefinedFixed32RuleProto2)(nil),               // 9: buf.validate.conformance.cases.PredefinedFixed32RuleProto2
	(*PredefinedFixed64RuleProto2)(nil),               // 10: buf.validate.conformance.cases.PredefinedFixed64RuleProto2
	(*PredefinedSFixed32RuleProto2)(nil),              // 11: buf.validate.conformance.cases.PredefinedSFixed32RuleProto2
	(*PredefinedSFixed64RuleProto2)(nil),              // 12: buf.validate.conformance.cases.PredefinedSFixed64RuleProto2
	(*PredefinedBoolRuleProto2)(nil),                  // 13: buf.validate.conformance.cases.PredefinedBoolRuleProto2
	(*PredefinedStringRuleProto2)(nil),                // 14: buf.validate.conformance.cases.PredefinedStringRuleProto2
	(*PredefinedBytesRuleProto2)(nil),                 // 15: buf.validate.conformance.cases.PredefinedBytesRuleProto2
	(*PredefinedEnumRuleProto2)(nil),                  // 16: buf.validate.conformance.cases.PredefinedEnumRuleProto2
	(*PredefinedRepeatedRuleProto2)(nil),              // 17: buf.validate.conformance.cases.PredefinedRepeatedRuleProto2
	(*PredefinedDurationRuleProto2)(nil),              // 18: buf.validate.conformance.cases.PredefinedDurationRuleProto2
	(*PredefinedTimestampRuleProto2)(nil),             // 19: buf.validate.conformance.cases.PredefinedTimestampRuleProto2
	(*PredefinedWrappedFloatRuleProto2)(nil),          // 20: buf.validate.conformance.cases.PredefinedWrappedFloatRuleProto2
	(*PredefinedWrappedDoubleRuleProto2)(nil),         // 21: buf.validate.conformance.cases.PredefinedWrappedDoubleRuleProto2
	(*PredefinedWrappedInt32RuleProto2)(nil),          // 22: buf.validate.conformance.cases.PredefinedWrappedInt32RuleProto2
	(*PredefinedWrappedInt64RuleProto2)(nil),          // 23: buf.validate.conformance.cases.PredefinedWrappedInt64RuleProto2
	(*PredefinedWrappedUInt32RuleProto2)(nil),         // 24: buf.validate.conformance.cases.PredefinedWrappedUInt32RuleProto2
	(*PredefinedWrappedUInt64RuleProto2)(nil),         // 25: buf.validate.conformance.cases.PredefinedWrappedUInt64RuleProto2
	(*PredefinedWrappedBoolRuleProto2)(nil),           // 26: buf.validate.conformance.cases.PredefinedWrappedBoolRuleProto2
	(*PredefinedWrappedStringRuleProto2)(nil),         // 27: buf.validate.conformance.cases.PredefinedWrappedStringRuleProto2
	(*PredefinedWrappedBytesRuleProto2)(nil),          // 28: buf.validate.conformance.cases.PredefinedWrappedBytesRuleProto2
	(*PredefinedRepeatedWrappedFloatRuleProto2)(nil),  // 29: buf.validate.conformance.cases.PredefinedRepeatedWrappedFloatRuleProto2
	(*PredefinedRepeatedWrappedDoubleRuleProto2)(nil), // 30: buf.validate.conformance.cases.PredefinedRepeatedWrappedDoubleRuleProto2
	(*PredefinedRepeatedWrappedInt32RuleProto2)(nil),  // 31: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt32RuleProto2
	(*PredefinedRepeatedWrappedInt64RuleProto2)(nil),  // 32: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt64RuleProto2
	(*PredefinedRepeatedWrappedUInt32RuleProto2)(nil), // 33: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt32RuleProto2
	(*PredefinedRepeatedWrappedUInt64RuleProto2)(nil), // 34: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt64RuleProto2
	(*PredefinedRepeatedWrappedBoolRuleProto2)(nil),   // 35: buf.validate.conformance.cases.PredefinedRepeatedWrappedBoolRuleProto2
	(*PredefinedRepeatedWrappedStringRuleProto2)(nil), // 36: buf.validate.conformance.cases.PredefinedRepeatedWrappedStringRuleProto2
	(*PredefinedRepeatedWrappedBytesRuleProto2)(nil),  // 37: buf.validate.conformance.cases.PredefinedRepeatedWrappedBytesRuleProto2
	(*PredefinedAndCustomRuleProto2)(nil),             // 38: buf.validate.conformance.cases.PredefinedAndCustomRuleProto2
	(*StandardPredefinedAndCustomRuleProto2)(nil),     // 39: buf.validate.conformance.cases.StandardPredefinedAndCustomRuleProto2
	(*PredefinedAndCustomRuleProto2_Nested)(nil),      // 40: buf.validate.conformance.cases.PredefinedAndCustomRuleProto2.Nested
	(*durationpb.Duration)(nil),                       // 41: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),                     // 42: google.protobuf.Timestamp
	(*wrapperspb.FloatValue)(nil),                     // 43: google.protobuf.FloatValue
	(*wrapperspb.DoubleValue)(nil),                    // 44: google.protobuf.DoubleValue
	(*wrapperspb.Int32Value)(nil),                     // 45: google.protobuf.Int32Value
	(*wrapperspb.Int64Value)(nil),                     // 46: google.protobuf.Int64Value
	(*wrapperspb.UInt32Value)(nil),                    // 47: google.protobuf.UInt32Value
	(*wrapperspb.UInt64Value)(nil),                    // 48: google.protobuf.UInt64Value
	(*wrapperspb.BoolValue)(nil),                      // 49: google.protobuf.BoolValue
	(*wrapperspb.StringValue)(nil),                    // 50: google.protobuf.StringValue
	(*wrapperspb.BytesValue)(nil),                     // 51: google.protobuf.BytesValue
	(*validate.FloatRules)(nil),                       // 52: buf.validate.FloatRules
	(*validate.DoubleRules)(nil),                      // 53: buf.validate.DoubleRules
	(*validate.Int32Rules)(nil),                       // 54: buf.validate.Int32Rules
	(*validate.Int64Rules)(nil),                       // 55: buf.validate.Int64Rules
	(*validate.UInt32Rules)(nil),                      // 56: buf.validate.UInt32Rules
	(*validate.UInt64Rules)(nil),                      // 57: buf.validate.UInt64Rules
	(*validate.SInt32Rules)(nil),                      // 58: buf.validate.SInt32Rules
	(*validate.SInt64Rules)(nil),                      // 59: buf.validate.SInt64Rules
	(*validate.Fixed32Rules)(nil),                     // 60: buf.validate.Fixed32Rules
	(*validate.Fixed64Rules)(nil),                     // 61: buf.validate.Fixed64Rules
	(*validate.SFixed32Rules)(nil),                    // 62: buf.validate.SFixed32Rules
	(*validate.SFixed64Rules)(nil),                    // 63: buf.validate.SFixed64Rules
	(*validate.BoolRules)(nil),                        // 64: buf.validate.BoolRules
	(*validate.StringRules)(nil),                      // 65: buf.validate.StringRules
	(*validate.BytesRules)(nil),                       // 66: buf.validate.BytesRules
	(*validate.EnumRules)(nil),                        // 67: buf.validate.EnumRules
	(*validate.RepeatedRules)(nil),                    // 68: buf.validate.RepeatedRules
	(*validate.DurationRules)(nil),                    // 69: buf.validate.DurationRules
	(*validate.TimestampRules)(nil),                   // 70: buf.validate.TimestampRules
}
var file_buf_validate_conformance_cases_predefined_rules_proto2_proto_depIdxs = []int32{
	0,  // 0: buf.validate.conformance.cases.PredefinedEnumRuleProto2.val:type_name -> buf.validate.conformance.cases.PredefinedEnumRuleProto2.EnumProto2
	41, // 1: buf.validate.conformance.cases.PredefinedDurationRuleProto2.val:type_name -> google.protobuf.Duration
	42, // 2: buf.validate.conformance.cases.PredefinedTimestampRuleProto2.val:type_name -> google.protobuf.Timestamp
	43, // 3: buf.validate.conformance.cases.PredefinedWrappedFloatRuleProto2.val:type_name -> google.protobuf.FloatValue
	44, // 4: buf.validate.conformance.cases.PredefinedWrappedDoubleRuleProto2.val:type_name -> google.protobuf.DoubleValue
	45, // 5: buf.validate.conformance.cases.PredefinedWrappedInt32RuleProto2.val:type_name -> google.protobuf.Int32Value
	46, // 6: buf.validate.conformance.cases.PredefinedWrappedInt64RuleProto2.val:type_name -> google.protobuf.Int64Value
	47, // 7: buf.validate.conformance.cases.PredefinedWrappedUInt32RuleProto2.val:type_name -> google.protobuf.UInt32Value
	48, // 8: buf.validate.conformance.cases.PredefinedWrappedUInt64RuleProto2.val:type_name -> google.protobuf.UInt64Value
	49, // 9: buf.validate.conformance.cases.PredefinedWrappedBoolRuleProto2.val:type_name -> google.protobuf.BoolValue
	50, // 10: buf.validate.conformance.cases.PredefinedWrappedStringRuleProto2.val:type_name -> google.protobuf.StringValue
	51, // 11: buf.validate.conformance.cases.PredefinedWrappedBytesRuleProto2.val:type_name -> google.protobuf.BytesValue
	43, // 12: buf.validate.conformance.cases.PredefinedRepeatedWrappedFloatRuleProto2.val:type_name -> google.protobuf.FloatValue
	44, // 13: buf.validate.conformance.cases.PredefinedRepeatedWrappedDoubleRuleProto2.val:type_name -> google.protobuf.DoubleValue
	45, // 14: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt32RuleProto2.val:type_name -> google.protobuf.Int32Value
	46, // 15: buf.validate.conformance.cases.PredefinedRepeatedWrappedInt64RuleProto2.val:type_name -> google.protobuf.Int64Value
	47, // 16: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt32RuleProto2.val:type_name -> google.protobuf.UInt32Value
	48, // 17: buf.validate.conformance.cases.PredefinedRepeatedWrappedUInt64RuleProto2.val:type_name -> google.protobuf.UInt64Value
	49, // 18: buf.validate.conformance.cases.PredefinedRepeatedWrappedBoolRuleProto2.val:type_name -> google.protobuf.BoolValue
	50, // 19: buf.validate.conformance.cases.PredefinedRepeatedWrappedStringRuleProto2.val:type_name -> google.protobuf.StringValue
	51, // 20: buf.validate.conformance.cases.PredefinedRepeatedWrappedBytesRuleProto2.val:type_name -> google.protobuf.BytesValue
	40, // 21: buf.validate.conformance.cases.PredefinedAndCustomRuleProto2.b:type_name -> buf.validate.conformance.cases.PredefinedAndCustomRuleProto2.Nested
	52, // 22: buf.validate.conformance.cases.float_abs_range_proto2:extendee -> buf.validate.FloatRules
	53, // 23: buf.validate.conformance.cases.double_abs_range_proto2:extendee -> buf.validate.DoubleRules
	54, // 24: buf.validate.conformance.cases.int32_abs_in_proto2:extendee -> buf.validate.Int32Rules
	55, // 25: buf.validate.conformance.cases.int64_abs_in_proto2:extendee -> buf.validate.Int64Rules
	56, // 26: buf.validate.conformance.cases.uint32_even_proto2:extendee -> buf.validate.UInt32Rules
	57, // 27: buf.validate.conformance.cases.uint64_even_proto2:extendee -> buf.validate.UInt64Rules
	58, // 28: buf.validate.conformance.cases.sint32_even_proto2:extendee -> buf.validate.SInt32Rules
	59, // 29: buf.validate.conformance.cases.sint64_even_proto2:extendee -> buf.validate.SInt64Rules
	60, // 30: buf.validate.conformance.cases.fixed32_even_proto2:extendee -> buf.validate.Fixed32Rules
	61, // 31: buf.validate.conformance.cases.fixed64_even_proto2:extendee -> buf.validate.Fixed64Rules
	62, // 32: buf.validate.conformance.cases.sfixed32_even_proto2:extendee -> buf.validate.SFixed32Rules
	63, // 33: buf.validate.conformance.cases.sfixed64_even_proto2:extendee -> buf.validate.SFixed64Rules
	64, // 34: buf.validate.conformance.cases.bool_false_proto2:extendee -> buf.validate.BoolRules
	65, // 35: buf.validate.conformance.cases.string_valid_path_proto2:extendee -> buf.validate.StringRules
	66, // 36: buf.validate.conformance.cases.bytes_valid_path_proto2:extendee -> buf.validate.BytesRules
	67, // 37: buf.validate.conformance.cases.enum_non_zero_proto2:extendee -> buf.validate.EnumRules
	68, // 38: buf.validate.conformance.cases.repeated_at_least_five_proto2:extendee -> buf.validate.RepeatedRules
	69, // 39: buf.validate.conformance.cases.duration_too_long_proto2:extendee -> buf.validate.DurationRules
	70, // 40: buf.validate.conformance.cases.timestamp_in_range_proto2:extendee -> buf.validate.TimestampRules
	46, // 41: buf.validate.conformance.cases.int64_abs_in_proto2:type_name -> google.protobuf.Int64Value
	42, // [42:42] is the sub-list for method output_type
	42, // [42:42] is the sub-list for method input_type
	41, // [41:42] is the sub-list for extension type_name
	22, // [22:41] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_predefined_rules_proto2_proto_init() }
func file_buf_validate_conformance_cases_predefined_rules_proto2_proto_init() {
	if File_buf_validate_conformance_cases_predefined_rules_proto2_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFloatRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedDoubleRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedInt32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedInt64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedUInt32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedUInt64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSInt32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSInt64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFixed32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedFixed64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSFixed32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedSFixed64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedBoolRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedStringRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedBytesRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedEnumRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedDurationRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedTimestampRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedFloatRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedDoubleRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedInt32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedInt64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedUInt32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedUInt64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedBoolRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedStringRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedWrappedBytesRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedFloatRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedDoubleRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedInt32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedInt64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedUInt32RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedUInt64RuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedBoolRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedStringRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedRepeatedWrappedBytesRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedAndCustomRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*StandardPredefinedAndCustomRuleProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*PredefinedAndCustomRuleProto2_Nested); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   40,
			NumExtensions: 19,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_predefined_rules_proto2_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_predefined_rules_proto2_proto_depIdxs,
		EnumInfos:         file_buf_validate_conformance_cases_predefined_rules_proto2_proto_enumTypes,
		MessageInfos:      file_buf_validate_conformance_cases_predefined_rules_proto2_proto_msgTypes,
		ExtensionInfos:    file_buf_validate_conformance_cases_predefined_rules_proto2_proto_extTypes,
	}.Build()
	File_buf_validate_conformance_cases_predefined_rules_proto2_proto = out.File
	file_buf_validate_conformance_cases_predefined_rules_proto2_proto_rawDesc = nil
	file_buf_validate_conformance_cases_predefined_rules_proto2_proto_goTypes = nil
	file_buf_validate_conformance_cases_predefined_rules_proto2_proto_depIdxs = nil
}
