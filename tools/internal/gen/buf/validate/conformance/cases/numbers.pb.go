// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/numbers.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FloatNone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatNone) Reset() {
	*x = FloatNone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatNone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatNone) ProtoMessage() {}

func (x *FloatNone) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatNone.ProtoReflect.Descriptor instead.
func (*FloatNone) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{0}
}

func (x *FloatNone) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatConst struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatConst) Reset() {
	*x = FloatConst{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatConst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatConst) ProtoMessage() {}

func (x *FloatConst) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatConst.ProtoReflect.Descriptor instead.
func (*FloatConst) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{1}
}

func (x *FloatConst) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatIn) Reset() {
	*x = FloatIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatIn) ProtoMessage() {}

func (x *FloatIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatIn.ProtoReflect.Descriptor instead.
func (*FloatIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{2}
}

func (x *FloatIn) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatNotIn) Reset() {
	*x = FloatNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatNotIn) ProtoMessage() {}

func (x *FloatNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatNotIn.ProtoReflect.Descriptor instead.
func (*FloatNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{3}
}

func (x *FloatNotIn) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatLT) Reset() {
	*x = FloatLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatLT) ProtoMessage() {}

func (x *FloatLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatLT.ProtoReflect.Descriptor instead.
func (*FloatLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{4}
}

func (x *FloatLT) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatLTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatLTE) Reset() {
	*x = FloatLTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatLTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatLTE) ProtoMessage() {}

func (x *FloatLTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatLTE.ProtoReflect.Descriptor instead.
func (*FloatLTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{5}
}

func (x *FloatLTE) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatGT) Reset() {
	*x = FloatGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatGT) ProtoMessage() {}

func (x *FloatGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatGT.ProtoReflect.Descriptor instead.
func (*FloatGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{6}
}

func (x *FloatGT) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatGTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatGTE) Reset() {
	*x = FloatGTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatGTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatGTE) ProtoMessage() {}

func (x *FloatGTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatGTE.ProtoReflect.Descriptor instead.
func (*FloatGTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{7}
}

func (x *FloatGTE) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatGTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatGTLT) Reset() {
	*x = FloatGTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatGTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatGTLT) ProtoMessage() {}

func (x *FloatGTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatGTLT.ProtoReflect.Descriptor instead.
func (*FloatGTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{8}
}

func (x *FloatGTLT) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatExLTGT) Reset() {
	*x = FloatExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatExLTGT) ProtoMessage() {}

func (x *FloatExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatExLTGT.ProtoReflect.Descriptor instead.
func (*FloatExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{9}
}

func (x *FloatExLTGT) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatGTELTE) Reset() {
	*x = FloatGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatGTELTE) ProtoMessage() {}

func (x *FloatGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatGTELTE.ProtoReflect.Descriptor instead.
func (*FloatGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{10}
}

func (x *FloatGTELTE) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatExGTELTE) Reset() {
	*x = FloatExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatExGTELTE) ProtoMessage() {}

func (x *FloatExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatExGTELTE.ProtoReflect.Descriptor instead.
func (*FloatExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{11}
}

func (x *FloatExGTELTE) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatFinite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatFinite) Reset() {
	*x = FloatFinite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatFinite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatFinite) ProtoMessage() {}

func (x *FloatFinite) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatFinite.ProtoReflect.Descriptor instead.
func (*FloatFinite) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{12}
}

func (x *FloatFinite) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatNotFinite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatNotFinite) Reset() {
	*x = FloatNotFinite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatNotFinite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatNotFinite) ProtoMessage() {}

func (x *FloatNotFinite) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatNotFinite.ProtoReflect.Descriptor instead.
func (*FloatNotFinite) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{13}
}

func (x *FloatNotFinite) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatIgnore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatIgnore) Reset() {
	*x = FloatIgnore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatIgnore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatIgnore) ProtoMessage() {}

func (x *FloatIgnore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatIgnore.ProtoReflect.Descriptor instead.
func (*FloatIgnore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{14}
}

func (x *FloatIgnore) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatIncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatIncorrectType) Reset() {
	*x = FloatIncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatIncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatIncorrectType) ProtoMessage() {}

func (x *FloatIncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatIncorrectType.ProtoReflect.Descriptor instead.
func (*FloatIncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{15}
}

func (x *FloatIncorrectType) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type FloatExample struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *FloatExample) Reset() {
	*x = FloatExample{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatExample) ProtoMessage() {}

func (x *FloatExample) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatExample.ProtoReflect.Descriptor instead.
func (*FloatExample) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{16}
}

func (x *FloatExample) GetVal() float32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleNone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleNone) Reset() {
	*x = DoubleNone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleNone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleNone) ProtoMessage() {}

func (x *DoubleNone) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleNone.ProtoReflect.Descriptor instead.
func (*DoubleNone) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{17}
}

func (x *DoubleNone) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleConst struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleConst) Reset() {
	*x = DoubleConst{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleConst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleConst) ProtoMessage() {}

func (x *DoubleConst) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleConst.ProtoReflect.Descriptor instead.
func (*DoubleConst) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{18}
}

func (x *DoubleConst) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleIn) Reset() {
	*x = DoubleIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleIn) ProtoMessage() {}

func (x *DoubleIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleIn.ProtoReflect.Descriptor instead.
func (*DoubleIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{19}
}

func (x *DoubleIn) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleNotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleNotIn) Reset() {
	*x = DoubleNotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleNotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleNotIn) ProtoMessage() {}

func (x *DoubleNotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleNotIn.ProtoReflect.Descriptor instead.
func (*DoubleNotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{20}
}

func (x *DoubleNotIn) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleLT) Reset() {
	*x = DoubleLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleLT) ProtoMessage() {}

func (x *DoubleLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleLT.ProtoReflect.Descriptor instead.
func (*DoubleLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{21}
}

func (x *DoubleLT) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleLTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleLTE) Reset() {
	*x = DoubleLTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleLTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleLTE) ProtoMessage() {}

func (x *DoubleLTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleLTE.ProtoReflect.Descriptor instead.
func (*DoubleLTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{22}
}

func (x *DoubleLTE) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleGT) Reset() {
	*x = DoubleGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleGT) ProtoMessage() {}

func (x *DoubleGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleGT.ProtoReflect.Descriptor instead.
func (*DoubleGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{23}
}

func (x *DoubleGT) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleGTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleGTE) Reset() {
	*x = DoubleGTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleGTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleGTE) ProtoMessage() {}

func (x *DoubleGTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleGTE.ProtoReflect.Descriptor instead.
func (*DoubleGTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{24}
}

func (x *DoubleGTE) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleGTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleGTLT) Reset() {
	*x = DoubleGTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleGTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleGTLT) ProtoMessage() {}

func (x *DoubleGTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleGTLT.ProtoReflect.Descriptor instead.
func (*DoubleGTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{25}
}

func (x *DoubleGTLT) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleExLTGT) Reset() {
	*x = DoubleExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleExLTGT) ProtoMessage() {}

func (x *DoubleExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleExLTGT.ProtoReflect.Descriptor instead.
func (*DoubleExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{26}
}

func (x *DoubleExLTGT) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleGTELTE) Reset() {
	*x = DoubleGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleGTELTE) ProtoMessage() {}

func (x *DoubleGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleGTELTE.ProtoReflect.Descriptor instead.
func (*DoubleGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{27}
}

func (x *DoubleGTELTE) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleExGTELTE) Reset() {
	*x = DoubleExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleExGTELTE) ProtoMessage() {}

func (x *DoubleExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleExGTELTE.ProtoReflect.Descriptor instead.
func (*DoubleExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{28}
}

func (x *DoubleExGTELTE) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleFinite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleFinite) Reset() {
	*x = DoubleFinite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleFinite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleFinite) ProtoMessage() {}

func (x *DoubleFinite) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleFinite.ProtoReflect.Descriptor instead.
func (*DoubleFinite) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{29}
}

func (x *DoubleFinite) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleNotFinite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleNotFinite) Reset() {
	*x = DoubleNotFinite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleNotFinite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleNotFinite) ProtoMessage() {}

func (x *DoubleNotFinite) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleNotFinite.ProtoReflect.Descriptor instead.
func (*DoubleNotFinite) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{30}
}

func (x *DoubleNotFinite) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleIgnore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleIgnore) Reset() {
	*x = DoubleIgnore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleIgnore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleIgnore) ProtoMessage() {}

func (x *DoubleIgnore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleIgnore.ProtoReflect.Descriptor instead.
func (*DoubleIgnore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{31}
}

func (x *DoubleIgnore) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleIncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleIncorrectType) Reset() {
	*x = DoubleIncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleIncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleIncorrectType) ProtoMessage() {}

func (x *DoubleIncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleIncorrectType.ProtoReflect.Descriptor instead.
func (*DoubleIncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{32}
}

func (x *DoubleIncorrectType) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type DoubleExample struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val float64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *DoubleExample) Reset() {
	*x = DoubleExample{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleExample) ProtoMessage() {}

func (x *DoubleExample) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleExample.ProtoReflect.Descriptor instead.
func (*DoubleExample) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{33}
}

func (x *DoubleExample) GetVal() float64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32None) Reset() {
	*x = Int32None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32None) ProtoMessage() {}

func (x *Int32None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32None.ProtoReflect.Descriptor instead.
func (*Int32None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{34}
}

func (x *Int32None) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32Const) Reset() {
	*x = Int32Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32Const) ProtoMessage() {}

func (x *Int32Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32Const.ProtoReflect.Descriptor instead.
func (*Int32Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{35}
}

func (x *Int32Const) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32In) Reset() {
	*x = Int32In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32In) ProtoMessage() {}

func (x *Int32In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32In.ProtoReflect.Descriptor instead.
func (*Int32In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{36}
}

func (x *Int32In) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32NotIn) Reset() {
	*x = Int32NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32NotIn) ProtoMessage() {}

func (x *Int32NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32NotIn.ProtoReflect.Descriptor instead.
func (*Int32NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{37}
}

func (x *Int32NotIn) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32LT) Reset() {
	*x = Int32LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32LT) ProtoMessage() {}

func (x *Int32LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32LT.ProtoReflect.Descriptor instead.
func (*Int32LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{38}
}

func (x *Int32LT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32LTE) Reset() {
	*x = Int32LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32LTE) ProtoMessage() {}

func (x *Int32LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32LTE.ProtoReflect.Descriptor instead.
func (*Int32LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{39}
}

func (x *Int32LTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32GT) Reset() {
	*x = Int32GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32GT) ProtoMessage() {}

func (x *Int32GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32GT.ProtoReflect.Descriptor instead.
func (*Int32GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{40}
}

func (x *Int32GT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32GTE) Reset() {
	*x = Int32GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32GTE) ProtoMessage() {}

func (x *Int32GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32GTE.ProtoReflect.Descriptor instead.
func (*Int32GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{41}
}

func (x *Int32GTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32GTLT) Reset() {
	*x = Int32GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32GTLT) ProtoMessage() {}

func (x *Int32GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32GTLT.ProtoReflect.Descriptor instead.
func (*Int32GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{42}
}

func (x *Int32GTLT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32ExLTGT) Reset() {
	*x = Int32ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32ExLTGT) ProtoMessage() {}

func (x *Int32ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32ExLTGT.ProtoReflect.Descriptor instead.
func (*Int32ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{43}
}

func (x *Int32ExLTGT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32GTELTE) Reset() {
	*x = Int32GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32GTELTE) ProtoMessage() {}

func (x *Int32GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32GTELTE.ProtoReflect.Descriptor instead.
func (*Int32GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{44}
}

func (x *Int32GTELTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32ExGTELTE) Reset() {
	*x = Int32ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32ExGTELTE) ProtoMessage() {}

func (x *Int32ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32ExGTELTE.ProtoReflect.Descriptor instead.
func (*Int32ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{45}
}

func (x *Int32ExGTELTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32Ignore) Reset() {
	*x = Int32Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32Ignore) ProtoMessage() {}

func (x *Int32Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32Ignore.ProtoReflect.Descriptor instead.
func (*Int32Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{46}
}

func (x *Int32Ignore) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32IncorrectType) Reset() {
	*x = Int32IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32IncorrectType) ProtoMessage() {}

func (x *Int32IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32IncorrectType.ProtoReflect.Descriptor instead.
func (*Int32IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{47}
}

func (x *Int32IncorrectType) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int32Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int32Example) Reset() {
	*x = Int32Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32Example) ProtoMessage() {}

func (x *Int32Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32Example.ProtoReflect.Descriptor instead.
func (*Int32Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{48}
}

func (x *Int32Example) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64None) Reset() {
	*x = Int64None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64None) ProtoMessage() {}

func (x *Int64None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64None.ProtoReflect.Descriptor instead.
func (*Int64None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{49}
}

func (x *Int64None) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64Const) Reset() {
	*x = Int64Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64Const) ProtoMessage() {}

func (x *Int64Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64Const.ProtoReflect.Descriptor instead.
func (*Int64Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{50}
}

func (x *Int64Const) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64In) Reset() {
	*x = Int64In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64In) ProtoMessage() {}

func (x *Int64In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64In.ProtoReflect.Descriptor instead.
func (*Int64In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{51}
}

func (x *Int64In) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64NotIn) Reset() {
	*x = Int64NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64NotIn) ProtoMessage() {}

func (x *Int64NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64NotIn.ProtoReflect.Descriptor instead.
func (*Int64NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{52}
}

func (x *Int64NotIn) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64LT) Reset() {
	*x = Int64LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64LT) ProtoMessage() {}

func (x *Int64LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64LT.ProtoReflect.Descriptor instead.
func (*Int64LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{53}
}

func (x *Int64LT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64LTE) Reset() {
	*x = Int64LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64LTE) ProtoMessage() {}

func (x *Int64LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64LTE.ProtoReflect.Descriptor instead.
func (*Int64LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{54}
}

func (x *Int64LTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64GT) Reset() {
	*x = Int64GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64GT) ProtoMessage() {}

func (x *Int64GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64GT.ProtoReflect.Descriptor instead.
func (*Int64GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{55}
}

func (x *Int64GT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64GTE) Reset() {
	*x = Int64GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64GTE) ProtoMessage() {}

func (x *Int64GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64GTE.ProtoReflect.Descriptor instead.
func (*Int64GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{56}
}

func (x *Int64GTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64GTLT) Reset() {
	*x = Int64GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64GTLT) ProtoMessage() {}

func (x *Int64GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64GTLT.ProtoReflect.Descriptor instead.
func (*Int64GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{57}
}

func (x *Int64GTLT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64ExLTGT) Reset() {
	*x = Int64ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64ExLTGT) ProtoMessage() {}

func (x *Int64ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64ExLTGT.ProtoReflect.Descriptor instead.
func (*Int64ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{58}
}

func (x *Int64ExLTGT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64GTELTE) Reset() {
	*x = Int64GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64GTELTE) ProtoMessage() {}

func (x *Int64GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64GTELTE.ProtoReflect.Descriptor instead.
func (*Int64GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{59}
}

func (x *Int64GTELTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64ExGTELTE) Reset() {
	*x = Int64ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64ExGTELTE) ProtoMessage() {}

func (x *Int64ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64ExGTELTE.ProtoReflect.Descriptor instead.
func (*Int64ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{60}
}

func (x *Int64ExGTELTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64Ignore) Reset() {
	*x = Int64Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64Ignore) ProtoMessage() {}

func (x *Int64Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64Ignore.ProtoReflect.Descriptor instead.
func (*Int64Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{61}
}

func (x *Int64Ignore) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64BigConstraints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Intentionally choose limits that are outside the range of both signed and unsigned 32-bit integers.
	LtPos       int64 `protobuf:"varint,1,opt,name=lt_pos,json=ltPos,proto3" json:"lt_pos,omitempty"`
	LtNeg       int64 `protobuf:"varint,2,opt,name=lt_neg,json=ltNeg,proto3" json:"lt_neg,omitempty"`
	GtPos       int64 `protobuf:"varint,3,opt,name=gt_pos,json=gtPos,proto3" json:"gt_pos,omitempty"`
	GtNeg       int64 `protobuf:"varint,4,opt,name=gt_neg,json=gtNeg,proto3" json:"gt_neg,omitempty"`
	LtePos      int64 `protobuf:"varint,5,opt,name=lte_pos,json=ltePos,proto3" json:"lte_pos,omitempty"`
	LteNeg      int64 `protobuf:"varint,6,opt,name=lte_neg,json=lteNeg,proto3" json:"lte_neg,omitempty"`
	GtePos      int64 `protobuf:"varint,7,opt,name=gte_pos,json=gtePos,proto3" json:"gte_pos,omitempty"`
	GteNeg      int64 `protobuf:"varint,8,opt,name=gte_neg,json=gteNeg,proto3" json:"gte_neg,omitempty"`
	ConstantPos int64 `protobuf:"varint,9,opt,name=constant_pos,json=constantPos,proto3" json:"constant_pos,omitempty"`
	ConstantNeg int64 `protobuf:"varint,10,opt,name=constant_neg,json=constantNeg,proto3" json:"constant_neg,omitempty"`
	In          int64 `protobuf:"varint,11,opt,name=in,proto3" json:"in,omitempty"`
	Notin       int64 `protobuf:"varint,12,opt,name=notin,proto3" json:"notin,omitempty"`
}

func (x *Int64BigConstraints) Reset() {
	*x = Int64BigConstraints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64BigConstraints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64BigConstraints) ProtoMessage() {}

func (x *Int64BigConstraints) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64BigConstraints.ProtoReflect.Descriptor instead.
func (*Int64BigConstraints) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{62}
}

func (x *Int64BigConstraints) GetLtPos() int64 {
	if x != nil {
		return x.LtPos
	}
	return 0
}

func (x *Int64BigConstraints) GetLtNeg() int64 {
	if x != nil {
		return x.LtNeg
	}
	return 0
}

func (x *Int64BigConstraints) GetGtPos() int64 {
	if x != nil {
		return x.GtPos
	}
	return 0
}

func (x *Int64BigConstraints) GetGtNeg() int64 {
	if x != nil {
		return x.GtNeg
	}
	return 0
}

func (x *Int64BigConstraints) GetLtePos() int64 {
	if x != nil {
		return x.LtePos
	}
	return 0
}

func (x *Int64BigConstraints) GetLteNeg() int64 {
	if x != nil {
		return x.LteNeg
	}
	return 0
}

func (x *Int64BigConstraints) GetGtePos() int64 {
	if x != nil {
		return x.GtePos
	}
	return 0
}

func (x *Int64BigConstraints) GetGteNeg() int64 {
	if x != nil {
		return x.GteNeg
	}
	return 0
}

func (x *Int64BigConstraints) GetConstantPos() int64 {
	if x != nil {
		return x.ConstantPos
	}
	return 0
}

func (x *Int64BigConstraints) GetConstantNeg() int64 {
	if x != nil {
		return x.ConstantNeg
	}
	return 0
}

func (x *Int64BigConstraints) GetIn() int64 {
	if x != nil {
		return x.In
	}
	return 0
}

func (x *Int64BigConstraints) GetNotin() int64 {
	if x != nil {
		return x.Notin
	}
	return 0
}

type Int64IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64IncorrectType) Reset() {
	*x = Int64IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64IncorrectType) ProtoMessage() {}

func (x *Int64IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64IncorrectType.ProtoReflect.Descriptor instead.
func (*Int64IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{63}
}

func (x *Int64IncorrectType) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Int64Example) Reset() {
	*x = Int64Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64Example) ProtoMessage() {}

func (x *Int64Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64Example.ProtoReflect.Descriptor instead.
func (*Int64Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{64}
}

func (x *Int64Example) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32None) Reset() {
	*x = UInt32None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32None) ProtoMessage() {}

func (x *UInt32None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32None.ProtoReflect.Descriptor instead.
func (*UInt32None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{65}
}

func (x *UInt32None) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32Const) Reset() {
	*x = UInt32Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32Const) ProtoMessage() {}

func (x *UInt32Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32Const.ProtoReflect.Descriptor instead.
func (*UInt32Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{66}
}

func (x *UInt32Const) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32In) Reset() {
	*x = UInt32In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32In) ProtoMessage() {}

func (x *UInt32In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32In.ProtoReflect.Descriptor instead.
func (*UInt32In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{67}
}

func (x *UInt32In) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32NotIn) Reset() {
	*x = UInt32NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32NotIn) ProtoMessage() {}

func (x *UInt32NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32NotIn.ProtoReflect.Descriptor instead.
func (*UInt32NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{68}
}

func (x *UInt32NotIn) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32LT) Reset() {
	*x = UInt32LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32LT) ProtoMessage() {}

func (x *UInt32LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32LT.ProtoReflect.Descriptor instead.
func (*UInt32LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{69}
}

func (x *UInt32LT) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32LTE) Reset() {
	*x = UInt32LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32LTE) ProtoMessage() {}

func (x *UInt32LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32LTE.ProtoReflect.Descriptor instead.
func (*UInt32LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{70}
}

func (x *UInt32LTE) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32GT) Reset() {
	*x = UInt32GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32GT) ProtoMessage() {}

func (x *UInt32GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32GT.ProtoReflect.Descriptor instead.
func (*UInt32GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{71}
}

func (x *UInt32GT) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32GTE) Reset() {
	*x = UInt32GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32GTE) ProtoMessage() {}

func (x *UInt32GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32GTE.ProtoReflect.Descriptor instead.
func (*UInt32GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{72}
}

func (x *UInt32GTE) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32GTLT) Reset() {
	*x = UInt32GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32GTLT) ProtoMessage() {}

func (x *UInt32GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32GTLT.ProtoReflect.Descriptor instead.
func (*UInt32GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{73}
}

func (x *UInt32GTLT) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32ExLTGT) Reset() {
	*x = UInt32ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32ExLTGT) ProtoMessage() {}

func (x *UInt32ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32ExLTGT.ProtoReflect.Descriptor instead.
func (*UInt32ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{74}
}

func (x *UInt32ExLTGT) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32GTELTE) Reset() {
	*x = UInt32GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32GTELTE) ProtoMessage() {}

func (x *UInt32GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32GTELTE.ProtoReflect.Descriptor instead.
func (*UInt32GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{75}
}

func (x *UInt32GTELTE) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32ExGTELTE) Reset() {
	*x = UInt32ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32ExGTELTE) ProtoMessage() {}

func (x *UInt32ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32ExGTELTE.ProtoReflect.Descriptor instead.
func (*UInt32ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{76}
}

func (x *UInt32ExGTELTE) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32Ignore) Reset() {
	*x = UInt32Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32Ignore) ProtoMessage() {}

func (x *UInt32Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32Ignore.ProtoReflect.Descriptor instead.
func (*UInt32Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{77}
}

func (x *UInt32Ignore) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32IncorrectType) Reset() {
	*x = UInt32IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32IncorrectType) ProtoMessage() {}

func (x *UInt32IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32IncorrectType.ProtoReflect.Descriptor instead.
func (*UInt32IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{78}
}

func (x *UInt32IncorrectType) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt32Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt32Example) Reset() {
	*x = UInt32Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32Example) ProtoMessage() {}

func (x *UInt32Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32Example.ProtoReflect.Descriptor instead.
func (*UInt32Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{79}
}

func (x *UInt32Example) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64None) Reset() {
	*x = UInt64None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64None) ProtoMessage() {}

func (x *UInt64None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64None.ProtoReflect.Descriptor instead.
func (*UInt64None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{80}
}

func (x *UInt64None) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64Const) Reset() {
	*x = UInt64Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64Const) ProtoMessage() {}

func (x *UInt64Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64Const.ProtoReflect.Descriptor instead.
func (*UInt64Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{81}
}

func (x *UInt64Const) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64In) Reset() {
	*x = UInt64In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64In) ProtoMessage() {}

func (x *UInt64In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64In.ProtoReflect.Descriptor instead.
func (*UInt64In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{82}
}

func (x *UInt64In) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64NotIn) Reset() {
	*x = UInt64NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64NotIn) ProtoMessage() {}

func (x *UInt64NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64NotIn.ProtoReflect.Descriptor instead.
func (*UInt64NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{83}
}

func (x *UInt64NotIn) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64LT) Reset() {
	*x = UInt64LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64LT) ProtoMessage() {}

func (x *UInt64LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64LT.ProtoReflect.Descriptor instead.
func (*UInt64LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{84}
}

func (x *UInt64LT) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64LTE) Reset() {
	*x = UInt64LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64LTE) ProtoMessage() {}

func (x *UInt64LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64LTE.ProtoReflect.Descriptor instead.
func (*UInt64LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{85}
}

func (x *UInt64LTE) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64GT) Reset() {
	*x = UInt64GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64GT) ProtoMessage() {}

func (x *UInt64GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64GT.ProtoReflect.Descriptor instead.
func (*UInt64GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{86}
}

func (x *UInt64GT) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64GTE) Reset() {
	*x = UInt64GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64GTE) ProtoMessage() {}

func (x *UInt64GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64GTE.ProtoReflect.Descriptor instead.
func (*UInt64GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{87}
}

func (x *UInt64GTE) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64GTLT) Reset() {
	*x = UInt64GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64GTLT) ProtoMessage() {}

func (x *UInt64GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64GTLT.ProtoReflect.Descriptor instead.
func (*UInt64GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{88}
}

func (x *UInt64GTLT) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64ExLTGT) Reset() {
	*x = UInt64ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64ExLTGT) ProtoMessage() {}

func (x *UInt64ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64ExLTGT.ProtoReflect.Descriptor instead.
func (*UInt64ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{89}
}

func (x *UInt64ExLTGT) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64GTELTE) Reset() {
	*x = UInt64GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64GTELTE) ProtoMessage() {}

func (x *UInt64GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64GTELTE.ProtoReflect.Descriptor instead.
func (*UInt64GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{90}
}

func (x *UInt64GTELTE) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64ExGTELTE) Reset() {
	*x = UInt64ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64ExGTELTE) ProtoMessage() {}

func (x *UInt64ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64ExGTELTE.ProtoReflect.Descriptor instead.
func (*UInt64ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{91}
}

func (x *UInt64ExGTELTE) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64Ignore) Reset() {
	*x = UInt64Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64Ignore) ProtoMessage() {}

func (x *UInt64Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64Ignore.ProtoReflect.Descriptor instead.
func (*UInt64Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{92}
}

func (x *UInt64Ignore) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64IncorrectType) Reset() {
	*x = UInt64IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64IncorrectType) ProtoMessage() {}

func (x *UInt64IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64IncorrectType.ProtoReflect.Descriptor instead.
func (*UInt64IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{93}
}

func (x *UInt64IncorrectType) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type UInt64Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *UInt64Example) Reset() {
	*x = UInt64Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64Example) ProtoMessage() {}

func (x *UInt64Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64Example.ProtoReflect.Descriptor instead.
func (*UInt64Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{94}
}

func (x *UInt64Example) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32None) Reset() {
	*x = SInt32None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32None) ProtoMessage() {}

func (x *SInt32None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32None.ProtoReflect.Descriptor instead.
func (*SInt32None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{95}
}

func (x *SInt32None) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32Const) Reset() {
	*x = SInt32Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32Const) ProtoMessage() {}

func (x *SInt32Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32Const.ProtoReflect.Descriptor instead.
func (*SInt32Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{96}
}

func (x *SInt32Const) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32In) Reset() {
	*x = SInt32In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32In) ProtoMessage() {}

func (x *SInt32In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32In.ProtoReflect.Descriptor instead.
func (*SInt32In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{97}
}

func (x *SInt32In) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32NotIn) Reset() {
	*x = SInt32NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32NotIn) ProtoMessage() {}

func (x *SInt32NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32NotIn.ProtoReflect.Descriptor instead.
func (*SInt32NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{98}
}

func (x *SInt32NotIn) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32LT) Reset() {
	*x = SInt32LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32LT) ProtoMessage() {}

func (x *SInt32LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32LT.ProtoReflect.Descriptor instead.
func (*SInt32LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{99}
}

func (x *SInt32LT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32LTE) Reset() {
	*x = SInt32LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32LTE) ProtoMessage() {}

func (x *SInt32LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32LTE.ProtoReflect.Descriptor instead.
func (*SInt32LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{100}
}

func (x *SInt32LTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32GT) Reset() {
	*x = SInt32GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32GT) ProtoMessage() {}

func (x *SInt32GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32GT.ProtoReflect.Descriptor instead.
func (*SInt32GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{101}
}

func (x *SInt32GT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32GTE) Reset() {
	*x = SInt32GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32GTE) ProtoMessage() {}

func (x *SInt32GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32GTE.ProtoReflect.Descriptor instead.
func (*SInt32GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{102}
}

func (x *SInt32GTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32GTLT) Reset() {
	*x = SInt32GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32GTLT) ProtoMessage() {}

func (x *SInt32GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32GTLT.ProtoReflect.Descriptor instead.
func (*SInt32GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{103}
}

func (x *SInt32GTLT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32ExLTGT) Reset() {
	*x = SInt32ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32ExLTGT) ProtoMessage() {}

func (x *SInt32ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32ExLTGT.ProtoReflect.Descriptor instead.
func (*SInt32ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{104}
}

func (x *SInt32ExLTGT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32GTELTE) Reset() {
	*x = SInt32GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32GTELTE) ProtoMessage() {}

func (x *SInt32GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32GTELTE.ProtoReflect.Descriptor instead.
func (*SInt32GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{105}
}

func (x *SInt32GTELTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32ExGTELTE) Reset() {
	*x = SInt32ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32ExGTELTE) ProtoMessage() {}

func (x *SInt32ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32ExGTELTE.ProtoReflect.Descriptor instead.
func (*SInt32ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{106}
}

func (x *SInt32ExGTELTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32Ignore) Reset() {
	*x = SInt32Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32Ignore) ProtoMessage() {}

func (x *SInt32Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32Ignore.ProtoReflect.Descriptor instead.
func (*SInt32Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{107}
}

func (x *SInt32Ignore) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32IncorrectType) Reset() {
	*x = SInt32IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32IncorrectType) ProtoMessage() {}

func (x *SInt32IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32IncorrectType.ProtoReflect.Descriptor instead.
func (*SInt32IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{108}
}

func (x *SInt32IncorrectType) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt32Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"zigzag32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt32Example) Reset() {
	*x = SInt32Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32Example) ProtoMessage() {}

func (x *SInt32Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32Example.ProtoReflect.Descriptor instead.
func (*SInt32Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{109}
}

func (x *SInt32Example) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64None) Reset() {
	*x = SInt64None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64None) ProtoMessage() {}

func (x *SInt64None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64None.ProtoReflect.Descriptor instead.
func (*SInt64None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{110}
}

func (x *SInt64None) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64Const) Reset() {
	*x = SInt64Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64Const) ProtoMessage() {}

func (x *SInt64Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64Const.ProtoReflect.Descriptor instead.
func (*SInt64Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{111}
}

func (x *SInt64Const) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64In) Reset() {
	*x = SInt64In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[112]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64In) ProtoMessage() {}

func (x *SInt64In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[112]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64In.ProtoReflect.Descriptor instead.
func (*SInt64In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{112}
}

func (x *SInt64In) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64NotIn) Reset() {
	*x = SInt64NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[113]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64NotIn) ProtoMessage() {}

func (x *SInt64NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[113]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64NotIn.ProtoReflect.Descriptor instead.
func (*SInt64NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{113}
}

func (x *SInt64NotIn) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64LT) Reset() {
	*x = SInt64LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[114]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64LT) ProtoMessage() {}

func (x *SInt64LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[114]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64LT.ProtoReflect.Descriptor instead.
func (*SInt64LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{114}
}

func (x *SInt64LT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64LTE) Reset() {
	*x = SInt64LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[115]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64LTE) ProtoMessage() {}

func (x *SInt64LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[115]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64LTE.ProtoReflect.Descriptor instead.
func (*SInt64LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{115}
}

func (x *SInt64LTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64GT) Reset() {
	*x = SInt64GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[116]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64GT) ProtoMessage() {}

func (x *SInt64GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[116]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64GT.ProtoReflect.Descriptor instead.
func (*SInt64GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{116}
}

func (x *SInt64GT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64GTE) Reset() {
	*x = SInt64GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[117]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64GTE) ProtoMessage() {}

func (x *SInt64GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[117]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64GTE.ProtoReflect.Descriptor instead.
func (*SInt64GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{117}
}

func (x *SInt64GTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64GTLT) Reset() {
	*x = SInt64GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[118]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64GTLT) ProtoMessage() {}

func (x *SInt64GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[118]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64GTLT.ProtoReflect.Descriptor instead.
func (*SInt64GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{118}
}

func (x *SInt64GTLT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64ExLTGT) Reset() {
	*x = SInt64ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[119]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64ExLTGT) ProtoMessage() {}

func (x *SInt64ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[119]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64ExLTGT.ProtoReflect.Descriptor instead.
func (*SInt64ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{119}
}

func (x *SInt64ExLTGT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64GTELTE) Reset() {
	*x = SInt64GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[120]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64GTELTE) ProtoMessage() {}

func (x *SInt64GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[120]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64GTELTE.ProtoReflect.Descriptor instead.
func (*SInt64GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{120}
}

func (x *SInt64GTELTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64ExGTELTE) Reset() {
	*x = SInt64ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[121]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64ExGTELTE) ProtoMessage() {}

func (x *SInt64ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[121]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64ExGTELTE.ProtoReflect.Descriptor instead.
func (*SInt64ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{121}
}

func (x *SInt64ExGTELTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64Ignore) Reset() {
	*x = SInt64Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[122]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64Ignore) ProtoMessage() {}

func (x *SInt64Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[122]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64Ignore.ProtoReflect.Descriptor instead.
func (*SInt64Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{122}
}

func (x *SInt64Ignore) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64IncorrectType) Reset() {
	*x = SInt64IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[123]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64IncorrectType) ProtoMessage() {}

func (x *SInt64IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[123]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64IncorrectType.ProtoReflect.Descriptor instead.
func (*SInt64IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{123}
}

func (x *SInt64IncorrectType) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SInt64Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"zigzag64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SInt64Example) Reset() {
	*x = SInt64Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[124]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64Example) ProtoMessage() {}

func (x *SInt64Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[124]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64Example.ProtoReflect.Descriptor instead.
func (*SInt64Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{124}
}

func (x *SInt64Example) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32None) Reset() {
	*x = Fixed32None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[125]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32None) ProtoMessage() {}

func (x *Fixed32None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[125]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32None.ProtoReflect.Descriptor instead.
func (*Fixed32None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{125}
}

func (x *Fixed32None) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32Const) Reset() {
	*x = Fixed32Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[126]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32Const) ProtoMessage() {}

func (x *Fixed32Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[126]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32Const.ProtoReflect.Descriptor instead.
func (*Fixed32Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{126}
}

func (x *Fixed32Const) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32In) Reset() {
	*x = Fixed32In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[127]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32In) ProtoMessage() {}

func (x *Fixed32In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[127]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32In.ProtoReflect.Descriptor instead.
func (*Fixed32In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{127}
}

func (x *Fixed32In) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32NotIn) Reset() {
	*x = Fixed32NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[128]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32NotIn) ProtoMessage() {}

func (x *Fixed32NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[128]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32NotIn.ProtoReflect.Descriptor instead.
func (*Fixed32NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{128}
}

func (x *Fixed32NotIn) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32LT) Reset() {
	*x = Fixed32LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[129]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32LT) ProtoMessage() {}

func (x *Fixed32LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[129]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32LT.ProtoReflect.Descriptor instead.
func (*Fixed32LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{129}
}

func (x *Fixed32LT) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32LTE) Reset() {
	*x = Fixed32LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[130]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32LTE) ProtoMessage() {}

func (x *Fixed32LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[130]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32LTE.ProtoReflect.Descriptor instead.
func (*Fixed32LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{130}
}

func (x *Fixed32LTE) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32GT) Reset() {
	*x = Fixed32GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[131]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32GT) ProtoMessage() {}

func (x *Fixed32GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[131]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32GT.ProtoReflect.Descriptor instead.
func (*Fixed32GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{131}
}

func (x *Fixed32GT) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32GTE) Reset() {
	*x = Fixed32GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[132]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32GTE) ProtoMessage() {}

func (x *Fixed32GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[132]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32GTE.ProtoReflect.Descriptor instead.
func (*Fixed32GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{132}
}

func (x *Fixed32GTE) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32GTLT) Reset() {
	*x = Fixed32GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[133]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32GTLT) ProtoMessage() {}

func (x *Fixed32GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[133]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32GTLT.ProtoReflect.Descriptor instead.
func (*Fixed32GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{133}
}

func (x *Fixed32GTLT) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32ExLTGT) Reset() {
	*x = Fixed32ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[134]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32ExLTGT) ProtoMessage() {}

func (x *Fixed32ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[134]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32ExLTGT.ProtoReflect.Descriptor instead.
func (*Fixed32ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{134}
}

func (x *Fixed32ExLTGT) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32GTELTE) Reset() {
	*x = Fixed32GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[135]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32GTELTE) ProtoMessage() {}

func (x *Fixed32GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[135]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32GTELTE.ProtoReflect.Descriptor instead.
func (*Fixed32GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{135}
}

func (x *Fixed32GTELTE) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32ExGTELTE) Reset() {
	*x = Fixed32ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[136]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32ExGTELTE) ProtoMessage() {}

func (x *Fixed32ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[136]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32ExGTELTE.ProtoReflect.Descriptor instead.
func (*Fixed32ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{136}
}

func (x *Fixed32ExGTELTE) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32Ignore) Reset() {
	*x = Fixed32Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[137]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32Ignore) ProtoMessage() {}

func (x *Fixed32Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[137]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32Ignore.ProtoReflect.Descriptor instead.
func (*Fixed32Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{137}
}

func (x *Fixed32Ignore) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32IncorrectType) Reset() {
	*x = Fixed32IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[138]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32IncorrectType) ProtoMessage() {}

func (x *Fixed32IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[138]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32IncorrectType.ProtoReflect.Descriptor instead.
func (*Fixed32IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{138}
}

func (x *Fixed32IncorrectType) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed32Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed32Example) Reset() {
	*x = Fixed32Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[139]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32Example) ProtoMessage() {}

func (x *Fixed32Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[139]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32Example.ProtoReflect.Descriptor instead.
func (*Fixed32Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{139}
}

func (x *Fixed32Example) GetVal() uint32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64None) Reset() {
	*x = Fixed64None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[140]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64None) ProtoMessage() {}

func (x *Fixed64None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[140]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64None.ProtoReflect.Descriptor instead.
func (*Fixed64None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{140}
}

func (x *Fixed64None) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64Const) Reset() {
	*x = Fixed64Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[141]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64Const) ProtoMessage() {}

func (x *Fixed64Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[141]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64Const.ProtoReflect.Descriptor instead.
func (*Fixed64Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{141}
}

func (x *Fixed64Const) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64In) Reset() {
	*x = Fixed64In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[142]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64In) ProtoMessage() {}

func (x *Fixed64In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[142]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64In.ProtoReflect.Descriptor instead.
func (*Fixed64In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{142}
}

func (x *Fixed64In) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64NotIn) Reset() {
	*x = Fixed64NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[143]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64NotIn) ProtoMessage() {}

func (x *Fixed64NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[143]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64NotIn.ProtoReflect.Descriptor instead.
func (*Fixed64NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{143}
}

func (x *Fixed64NotIn) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64LT) Reset() {
	*x = Fixed64LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[144]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64LT) ProtoMessage() {}

func (x *Fixed64LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[144]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64LT.ProtoReflect.Descriptor instead.
func (*Fixed64LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{144}
}

func (x *Fixed64LT) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64LTE) Reset() {
	*x = Fixed64LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[145]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64LTE) ProtoMessage() {}

func (x *Fixed64LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[145]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64LTE.ProtoReflect.Descriptor instead.
func (*Fixed64LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{145}
}

func (x *Fixed64LTE) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64GT) Reset() {
	*x = Fixed64GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[146]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64GT) ProtoMessage() {}

func (x *Fixed64GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[146]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64GT.ProtoReflect.Descriptor instead.
func (*Fixed64GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{146}
}

func (x *Fixed64GT) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64GTE) Reset() {
	*x = Fixed64GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[147]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64GTE) ProtoMessage() {}

func (x *Fixed64GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[147]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64GTE.ProtoReflect.Descriptor instead.
func (*Fixed64GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{147}
}

func (x *Fixed64GTE) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64GTLT) Reset() {
	*x = Fixed64GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[148]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64GTLT) ProtoMessage() {}

func (x *Fixed64GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[148]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64GTLT.ProtoReflect.Descriptor instead.
func (*Fixed64GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{148}
}

func (x *Fixed64GTLT) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64ExLTGT) Reset() {
	*x = Fixed64ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[149]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64ExLTGT) ProtoMessage() {}

func (x *Fixed64ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[149]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64ExLTGT.ProtoReflect.Descriptor instead.
func (*Fixed64ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{149}
}

func (x *Fixed64ExLTGT) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64GTELTE) Reset() {
	*x = Fixed64GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[150]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64GTELTE) ProtoMessage() {}

func (x *Fixed64GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[150]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64GTELTE.ProtoReflect.Descriptor instead.
func (*Fixed64GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{150}
}

func (x *Fixed64GTELTE) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64ExGTELTE) Reset() {
	*x = Fixed64ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[151]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64ExGTELTE) ProtoMessage() {}

func (x *Fixed64ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[151]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64ExGTELTE.ProtoReflect.Descriptor instead.
func (*Fixed64ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{151}
}

func (x *Fixed64ExGTELTE) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64Ignore) Reset() {
	*x = Fixed64Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[152]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64Ignore) ProtoMessage() {}

func (x *Fixed64Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[152]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64Ignore.ProtoReflect.Descriptor instead.
func (*Fixed64Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{152}
}

func (x *Fixed64Ignore) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64IncorrectType) Reset() {
	*x = Fixed64IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[153]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64IncorrectType) ProtoMessage() {}

func (x *Fixed64IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[153]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64IncorrectType.ProtoReflect.Descriptor instead.
func (*Fixed64IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{153}
}

func (x *Fixed64IncorrectType) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Fixed64Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val uint64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Fixed64Example) Reset() {
	*x = Fixed64Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[154]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64Example) ProtoMessage() {}

func (x *Fixed64Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[154]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64Example.ProtoReflect.Descriptor instead.
func (*Fixed64Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{154}
}

func (x *Fixed64Example) GetVal() uint64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32None) Reset() {
	*x = SFixed32None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[155]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32None) ProtoMessage() {}

func (x *SFixed32None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[155]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32None.ProtoReflect.Descriptor instead.
func (*SFixed32None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{155}
}

func (x *SFixed32None) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32Const) Reset() {
	*x = SFixed32Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[156]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32Const) ProtoMessage() {}

func (x *SFixed32Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[156]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32Const.ProtoReflect.Descriptor instead.
func (*SFixed32Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{156}
}

func (x *SFixed32Const) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32In) Reset() {
	*x = SFixed32In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[157]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32In) ProtoMessage() {}

func (x *SFixed32In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[157]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32In.ProtoReflect.Descriptor instead.
func (*SFixed32In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{157}
}

func (x *SFixed32In) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32NotIn) Reset() {
	*x = SFixed32NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[158]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32NotIn) ProtoMessage() {}

func (x *SFixed32NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[158]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32NotIn.ProtoReflect.Descriptor instead.
func (*SFixed32NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{158}
}

func (x *SFixed32NotIn) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32LT) Reset() {
	*x = SFixed32LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[159]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32LT) ProtoMessage() {}

func (x *SFixed32LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[159]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32LT.ProtoReflect.Descriptor instead.
func (*SFixed32LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{159}
}

func (x *SFixed32LT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32LTE) Reset() {
	*x = SFixed32LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[160]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32LTE) ProtoMessage() {}

func (x *SFixed32LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[160]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32LTE.ProtoReflect.Descriptor instead.
func (*SFixed32LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{160}
}

func (x *SFixed32LTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32GT) Reset() {
	*x = SFixed32GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[161]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32GT) ProtoMessage() {}

func (x *SFixed32GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[161]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32GT.ProtoReflect.Descriptor instead.
func (*SFixed32GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{161}
}

func (x *SFixed32GT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32GTE) Reset() {
	*x = SFixed32GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[162]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32GTE) ProtoMessage() {}

func (x *SFixed32GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[162]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32GTE.ProtoReflect.Descriptor instead.
func (*SFixed32GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{162}
}

func (x *SFixed32GTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32GTLT) Reset() {
	*x = SFixed32GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[163]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32GTLT) ProtoMessage() {}

func (x *SFixed32GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[163]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32GTLT.ProtoReflect.Descriptor instead.
func (*SFixed32GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{163}
}

func (x *SFixed32GTLT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32ExLTGT) Reset() {
	*x = SFixed32ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[164]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32ExLTGT) ProtoMessage() {}

func (x *SFixed32ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[164]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32ExLTGT.ProtoReflect.Descriptor instead.
func (*SFixed32ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{164}
}

func (x *SFixed32ExLTGT) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32GTELTE) Reset() {
	*x = SFixed32GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[165]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32GTELTE) ProtoMessage() {}

func (x *SFixed32GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[165]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32GTELTE.ProtoReflect.Descriptor instead.
func (*SFixed32GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{165}
}

func (x *SFixed32GTELTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32ExGTELTE) Reset() {
	*x = SFixed32ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[166]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32ExGTELTE) ProtoMessage() {}

func (x *SFixed32ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[166]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32ExGTELTE.ProtoReflect.Descriptor instead.
func (*SFixed32ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{166}
}

func (x *SFixed32ExGTELTE) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32Ignore) Reset() {
	*x = SFixed32Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[167]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32Ignore) ProtoMessage() {}

func (x *SFixed32Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[167]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32Ignore.ProtoReflect.Descriptor instead.
func (*SFixed32Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{167}
}

func (x *SFixed32Ignore) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32IncorrectType) Reset() {
	*x = SFixed32IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[168]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32IncorrectType) ProtoMessage() {}

func (x *SFixed32IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[168]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32IncorrectType.ProtoReflect.Descriptor instead.
func (*SFixed32IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{168}
}

func (x *SFixed32IncorrectType) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed32Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"fixed32,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed32Example) Reset() {
	*x = SFixed32Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[169]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32Example) ProtoMessage() {}

func (x *SFixed32Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[169]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32Example.ProtoReflect.Descriptor instead.
func (*SFixed32Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{169}
}

func (x *SFixed32Example) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64None struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64None) Reset() {
	*x = SFixed64None{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[170]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64None) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64None) ProtoMessage() {}

func (x *SFixed64None) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[170]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64None.ProtoReflect.Descriptor instead.
func (*SFixed64None) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{170}
}

func (x *SFixed64None) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64Const struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64Const) Reset() {
	*x = SFixed64Const{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[171]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64Const) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64Const) ProtoMessage() {}

func (x *SFixed64Const) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[171]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64Const.ProtoReflect.Descriptor instead.
func (*SFixed64Const) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{171}
}

func (x *SFixed64Const) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64In struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64In) Reset() {
	*x = SFixed64In{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[172]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64In) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64In) ProtoMessage() {}

func (x *SFixed64In) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[172]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64In.ProtoReflect.Descriptor instead.
func (*SFixed64In) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{172}
}

func (x *SFixed64In) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64NotIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64NotIn) Reset() {
	*x = SFixed64NotIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[173]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64NotIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64NotIn) ProtoMessage() {}

func (x *SFixed64NotIn) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[173]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64NotIn.ProtoReflect.Descriptor instead.
func (*SFixed64NotIn) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{173}
}

func (x *SFixed64NotIn) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64LT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64LT) Reset() {
	*x = SFixed64LT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[174]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64LT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64LT) ProtoMessage() {}

func (x *SFixed64LT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[174]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64LT.ProtoReflect.Descriptor instead.
func (*SFixed64LT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{174}
}

func (x *SFixed64LT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64LTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64LTE) Reset() {
	*x = SFixed64LTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[175]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64LTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64LTE) ProtoMessage() {}

func (x *SFixed64LTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[175]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64LTE.ProtoReflect.Descriptor instead.
func (*SFixed64LTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{175}
}

func (x *SFixed64LTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64GT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64GT) Reset() {
	*x = SFixed64GT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[176]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64GT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64GT) ProtoMessage() {}

func (x *SFixed64GT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[176]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64GT.ProtoReflect.Descriptor instead.
func (*SFixed64GT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{176}
}

func (x *SFixed64GT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64GTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64GTE) Reset() {
	*x = SFixed64GTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[177]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64GTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64GTE) ProtoMessage() {}

func (x *SFixed64GTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[177]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64GTE.ProtoReflect.Descriptor instead.
func (*SFixed64GTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{177}
}

func (x *SFixed64GTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64GTLT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64GTLT) Reset() {
	*x = SFixed64GTLT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[178]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64GTLT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64GTLT) ProtoMessage() {}

func (x *SFixed64GTLT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[178]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64GTLT.ProtoReflect.Descriptor instead.
func (*SFixed64GTLT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{178}
}

func (x *SFixed64GTLT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64ExLTGT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64ExLTGT) Reset() {
	*x = SFixed64ExLTGT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[179]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64ExLTGT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64ExLTGT) ProtoMessage() {}

func (x *SFixed64ExLTGT) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[179]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64ExLTGT.ProtoReflect.Descriptor instead.
func (*SFixed64ExLTGT) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{179}
}

func (x *SFixed64ExLTGT) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64GTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64GTELTE) Reset() {
	*x = SFixed64GTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[180]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64GTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64GTELTE) ProtoMessage() {}

func (x *SFixed64GTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[180]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64GTELTE.ProtoReflect.Descriptor instead.
func (*SFixed64GTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{180}
}

func (x *SFixed64GTELTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64ExGTELTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64ExGTELTE) Reset() {
	*x = SFixed64ExGTELTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[181]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64ExGTELTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64ExGTELTE) ProtoMessage() {}

func (x *SFixed64ExGTELTE) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[181]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64ExGTELTE.ProtoReflect.Descriptor instead.
func (*SFixed64ExGTELTE) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{181}
}

func (x *SFixed64ExGTELTE) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64Ignore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64Ignore) Reset() {
	*x = SFixed64Ignore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[182]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64Ignore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64Ignore) ProtoMessage() {}

func (x *SFixed64Ignore) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[182]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64Ignore.ProtoReflect.Descriptor instead.
func (*SFixed64Ignore) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{182}
}

func (x *SFixed64Ignore) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64IncorrectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64IncorrectType) Reset() {
	*x = SFixed64IncorrectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[183]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64IncorrectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64IncorrectType) ProtoMessage() {}

func (x *SFixed64IncorrectType) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[183]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64IncorrectType.ProtoReflect.Descriptor instead.
func (*SFixed64IncorrectType) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{183}
}

func (x *SFixed64IncorrectType) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type SFixed64Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int64 `protobuf:"fixed64,1,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *SFixed64Example) Reset() {
	*x = SFixed64Example{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[184]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64Example) ProtoMessage() {}

func (x *SFixed64Example) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[184]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64Example.ProtoReflect.Descriptor instead.
func (*SFixed64Example) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{184}
}

func (x *SFixed64Example) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

type Int64LTEOptional struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int64 `protobuf:"varint,1,opt,name=val,proto3,oneof" json:"val,omitempty"`
}

func (x *Int64LTEOptional) Reset() {
	*x = Int64LTEOptional{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[185]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64LTEOptional) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64LTEOptional) ProtoMessage() {}

func (x *Int64LTEOptional) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_numbers_proto_msgTypes[185]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64LTEOptional.ProtoReflect.Descriptor instead.
func (*Int64LTEOptional) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP(), []int{185}
}

func (x *Int64LTEOptional) GetVal() int64 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

var File_buf_validate_conformance_cases_numbers_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_numbers_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x1b,
	0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1d, 0x0a, 0x09, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0a, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x0d, 0xa4, 0x70, 0x9d,
	0x3f, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x07, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x49,
	0x6e, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0f,
	0xba, 0x48, 0x0c, 0x0a, 0x0a, 0x35, 0x85, 0xeb, 0x91, 0x40, 0x35, 0xe1, 0x7a, 0xfc, 0x40, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0a, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4e, 0x6f, 0x74,
	0x49, 0x6e, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42,
	0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x3d, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x27, 0x0a, 0x07, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x54, 0x12, 0x1c, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x15,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x08, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x4c, 0x54, 0x45, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x1d, 0x00, 0x00, 0x80, 0x42, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x07, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x47, 0x54, 0x12, 0x1c,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0xba, 0x48, 0x07,
	0x0a, 0x05, 0x25, 0x00, 0x00, 0x80, 0x41, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x08,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x47, 0x54, 0x45, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x2d, 0x00, 0x00, 0x00,
	0x41, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x09, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x47,
	0x54, 0x4c, 0x54, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02,
	0x42, 0x0f, 0xba, 0x48, 0x0c, 0x0a, 0x0a, 0x15, 0x00, 0x00, 0x20, 0x41, 0x25, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x30, 0x0a, 0x0b, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45,
	0x78, 0x4c, 0x54, 0x47, 0x54, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x0a, 0x0a, 0x15, 0x00, 0x00, 0x00, 0x00, 0x25, 0x00,
	0x00, 0x20, 0x41, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x30, 0x0a, 0x0b, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x0a, 0x0a, 0x1d, 0x00, 0x00, 0x80, 0x43,
	0x2d, 0x00, 0x00, 0x00, 0x43, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x32, 0x0a, 0x0d, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x45, 0x78, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x21, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x0a, 0x0a, 0x1d,
	0x00, 0x00, 0x00, 0x43, 0x2d, 0x00, 0x00, 0x80, 0x43, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28,
	0x0a, 0x0b, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x46, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x07, 0xba, 0x48, 0x04, 0x0a,
	0x02, 0x40, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0e, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x4e, 0x6f, 0x74, 0x46, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x07, 0xba, 0x48, 0x04, 0x0a, 0x02, 0x40, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x0b, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x12, 0x24, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x12, 0xba, 0x48, 0x0f, 0xd8, 0x01, 0x01, 0x0a, 0x0a, 0x1d, 0x00, 0x00, 0x80, 0x43,
	0x2d, 0x00, 0x00, 0x00, 0x43, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x36, 0x0a, 0x12, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0e, 0xba,
	0x48, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x78, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42,
	0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x4d, 0x00, 0x00, 0x00, 0x41, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x1e, 0x0a, 0x0a, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x2f, 0x0a, 0x0b, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xba, 0x48,
	0x0b, 0x12, 0x09, 0x09, 0xae, 0x47, 0xe1, 0x7a, 0x14, 0xae, 0xf3, 0x3f, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x35, 0x0a, 0x08, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x12, 0x29, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xba, 0x48, 0x14, 0x12,
	0x12, 0x31, 0x3d, 0x0a, 0xd7, 0xa3, 0x70, 0x3d, 0x12, 0x40, 0x31, 0x8f, 0xc2, 0xf5, 0x28, 0x5c,
	0x8f, 0x1f, 0x40, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0b, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x12, 0x09, 0x39, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x08, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x4c, 0x54, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x12, 0x09, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x09, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x4c, 0x54, 0x45, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x12, 0x09, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50,
	0x40, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x08, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x47, 0x54, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42,
	0x0e, 0xba, 0x48, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x09, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x47, 0x54,
	0x45, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e,
	0xba, 0x48, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x40, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x37, 0x0a, 0x0a, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x47, 0x54, 0x4c,
	0x54, 0x12, 0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17,
	0xba, 0x48, 0x14, 0x12, 0x12, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, 0x21, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x39, 0x0a, 0x0c,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x4c, 0x54, 0x47, 0x54, 0x12, 0x29, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xba, 0x48, 0x14, 0x12, 0x12,
	0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x24, 0x40, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x39, 0x0a, 0x0c, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xba, 0x48, 0x14, 0x12, 0x12, 0x19, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x70, 0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x40, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x3b, 0x0a, 0x0e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x47, 0x54,
	0x45, 0x4c, 0x54, 0x45, 0x12, 0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x42, 0x17, 0xba, 0x48, 0x14, 0x12, 0x12, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60,
	0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x40, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x29, 0x0a, 0x0c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x12,
	0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x12, 0x02, 0x40, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0f, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x46, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x07, 0xba, 0x48, 0x04, 0x12,
	0x02, 0x40, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3c, 0x0a, 0x0c, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x2c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x1a, 0xba, 0x48, 0x17, 0xd8, 0x01, 0x01, 0x12, 0x12, 0x19,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60,
	0x40, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x13, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a,
	0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x31, 0x0a, 0x0d, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x12, 0x09,
	0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x1d,
	0x0a, 0x09, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a,
	0x0a, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x08,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x26, 0x0a, 0x07, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49,
	0x6e, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09,
	0xba, 0x48, 0x06, 0x1a, 0x04, 0x30, 0x02, 0x30, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27,
	0x0a, 0x0a, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x19, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02,
	0x38, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x24, 0x0a, 0x07, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x4c, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x10, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a,
	0x08, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x54, 0x45, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x18, 0x40, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x24, 0x0a, 0x07, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x47, 0x54, 0x12,
	0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x1a, 0x02, 0x20, 0x10, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x47, 0x54, 0x45, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x28, 0x08, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x28, 0x0a, 0x09, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x47, 0x54, 0x4c, 0x54, 0x12, 0x1b,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xba, 0x48, 0x06,
	0x1a, 0x04, 0x10, 0x0a, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0b, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x4c, 0x54, 0x47, 0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xba, 0x48, 0x06, 0x1a, 0x04, 0x10, 0x00,
	0x20, 0x0a, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x1a, 0x06, 0x18, 0x80, 0x02, 0x28, 0x80, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0d, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78,
	0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x1a, 0x06, 0x18, 0x80, 0x01, 0x28, 0x80, 0x02,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0xd8, 0x01, 0x01, 0x1a, 0x06, 0x18, 0x80, 0x02, 0x28, 0x80,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x32, 0x0a, 0x12, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49,
	0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05,
	0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x29, 0x0a, 0x0c, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x40, 0x0a,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x1d, 0x0a, 0x09, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4e, 0x6f,
	0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x0a, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x43, 0x6f, 0x6e,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x08, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x26, 0x0a,
	0x07, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x30, 0x02, 0x30, 0x03,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x0a, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4e, 0x6f,
	0x74, 0x49, 0x6e, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x38, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x24,
	0x0a, 0x07, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x10, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x54, 0x45,
	0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x22, 0x02, 0x18, 0x40, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x24, 0x0a, 0x07, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20, 0x10, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x25, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x45, 0x12, 0x19, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22,
	0x02, 0x28, 0x08, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x09, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x47, 0x54, 0x4c, 0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x10, 0x0a, 0x20, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x4c, 0x54, 0x47,
	0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09,
	0xba, 0x48, 0x06, 0x22, 0x04, 0x10, 0x00, 0x20, 0x0a, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c,
	0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x22,
	0x06, 0x18, 0x80, 0x02, 0x28, 0x80, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0d,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x22,
	0x06, 0x18, 0x80, 0x01, 0x28, 0x80, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0b,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0xd8, 0x01, 0x01,
	0x22, 0x06, 0x18, 0x80, 0x02, 0x28, 0x80, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x8c, 0x04,
	0x0a, 0x13, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x42, 0x69, 0x67, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72,
	0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x06, 0x6c, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x22, 0x06, 0x10, 0xa6, 0xdd, 0x87,
	0xa4, 0x14, 0x52, 0x05, 0x6c, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x27, 0x0a, 0x06, 0x6c, 0x74, 0x5f,
	0x6e, 0x65, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x22, 0x0b,
	0x10, 0xda, 0xa2, 0xf8, 0xdb, 0xeb, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x05, 0x6c, 0x74, 0x4e,
	0x65, 0x67, 0x12, 0x22, 0x0a, 0x06, 0x67, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x22, 0x06, 0x20, 0xa6, 0xdd, 0x87, 0xa4, 0x14, 0x52,
	0x05, 0x67, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x27, 0x0a, 0x06, 0x67, 0x74, 0x5f, 0x6e, 0x65, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x22, 0x0b, 0x20, 0xda, 0xa2,
	0xf8, 0xdb, 0xeb, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x05, 0x67, 0x74, 0x4e, 0x65, 0x67, 0x12,
	0x24, 0x0a, 0x07, 0x6c, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0b, 0xba, 0x48, 0x08, 0x22, 0x06, 0x18, 0xa6, 0xdd, 0x87, 0xa4, 0x14, 0x52, 0x06, 0x6c,
	0x74, 0x65, 0x50, 0x6f, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x6c, 0x74, 0x65, 0x5f, 0x6e, 0x65, 0x67,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x22, 0x0b, 0x18, 0xda, 0xa2,
	0xf8, 0xdb, 0xeb, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x06, 0x6c, 0x74, 0x65, 0x4e, 0x65, 0x67,
	0x12, 0x24, 0x0a, 0x07, 0x67, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x22, 0x06, 0x28, 0xa6, 0xdd, 0x87, 0xa4, 0x14, 0x52, 0x06,
	0x67, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x67, 0x74, 0x65, 0x5f, 0x6e, 0x65,
	0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x22, 0x0b, 0x28, 0xda,
	0xa2, 0xf8, 0xdb, 0xeb, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x06, 0x67, 0x74, 0x65, 0x4e, 0x65,
	0x67, 0x12, 0x2e, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x70, 0x6f,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x22, 0x06, 0x08, 0xa6,
	0xdd, 0x87, 0xa4, 0x14, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x50, 0x6f,
	0x73, 0x12, 0x33, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x65,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x22, 0x0b, 0x08, 0xda,
	0xa2, 0xf8, 0xdb, 0xeb, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x4e, 0x65, 0x67, 0x12, 0x26, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x16, 0xba, 0x48, 0x13, 0x22, 0x11, 0x30, 0xa6, 0xdd, 0x87, 0xa4, 0x14, 0x30,
	0xda, 0xa2, 0xf8, 0xdb, 0xeb, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x2c,
	0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x69, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x16, 0xba,
	0x48, 0x13, 0x22, 0x11, 0x38, 0xa6, 0xdd, 0x87, 0xa4, 0x14, 0x38, 0xda, 0xa2, 0xf8, 0xdb, 0xeb,
	0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x69, 0x6e, 0x22, 0x32, 0x0a, 0x12,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x29, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x22, 0x02, 0x48, 0x0a, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x1e, 0x0a, 0x0a, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x0b, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xba, 0x48, 0x04, 0x2a, 0x02, 0x08, 0x01,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x08, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49,
	0x6e, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09,
	0xba, 0x48, 0x06, 0x2a, 0x04, 0x30, 0x02, 0x30, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28,
	0x0a, 0x0b, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x19, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xba, 0x48, 0x04, 0x2a,
	0x02, 0x38, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x55, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x4c, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x07, 0xba, 0x48, 0x04, 0x2a, 0x02, 0x10, 0x05, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x26, 0x0a, 0x09, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x54, 0x45, 0x12, 0x19, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xba, 0x48, 0x04, 0x2a, 0x02,
	0x18, 0x40, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x55, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x47, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x2a, 0x02, 0x20, 0x10, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x26,
	0x0a, 0x09, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x47, 0x54, 0x45, 0x12, 0x19, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xba, 0x48, 0x04, 0x2a, 0x02, 0x28,
	0x08, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x29, 0x0a, 0x0a, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x47, 0x54, 0x4c, 0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x09, 0xba, 0x48, 0x06, 0x2a, 0x04, 0x10, 0x0a, 0x20, 0x05, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x2b, 0x0a, 0x0c, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x4c, 0x54, 0x47,
	0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09,
	0xba, 0x48, 0x06, 0x2a, 0x04, 0x10, 0x05, 0x20, 0x0a, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d,
	0x0a, 0x0c, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xba, 0x48, 0x08,
	0x2a, 0x06, 0x18, 0x80, 0x02, 0x28, 0x80, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a,
	0x0e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12,
	0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xba, 0x48,
	0x08, 0x2a, 0x06, 0x18, 0x80, 0x01, 0x28, 0x80, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x30,
	0x0a, 0x0c, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x20,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0e, 0xba, 0x48, 0x0b,
	0xd8, 0x01, 0x01, 0x2a, 0x06, 0x18, 0x80, 0x02, 0x28, 0x80, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x33, 0x0a, 0x13, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x25, 0x00, 0x00, 0x00, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0d, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45,
	0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x07, 0xba, 0x48, 0x04, 0x2a, 0x02, 0x40, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x1e, 0x0a, 0x0a, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4e, 0x6f, 0x6e, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x28, 0x0a, 0x0b, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x43, 0x6f, 0x6e, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x32, 0x02, 0x08, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x08, 0x55,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x09, 0xba, 0x48, 0x06, 0x32, 0x04, 0x30, 0x02, 0x30, 0x03, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x0b, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4e, 0x6f,
	0x74, 0x49, 0x6e, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x38, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25,
	0x0a, 0x08, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x10, 0x05,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x26, 0x0a, 0x09, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c,
	0x54, 0x45, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x18, 0x40, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a,
	0x08, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x20, 0x10, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x26, 0x0a, 0x09, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54,
	0x45, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07,
	0xba, 0x48, 0x04, 0x32, 0x02, 0x28, 0x08, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x29, 0x0a, 0x0a,
	0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x4c, 0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x09, 0xba, 0x48, 0x06, 0x32, 0x04, 0x10, 0x0a,
	0x20, 0x05, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0c, 0x55, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x45, 0x78, 0x4c, 0x54, 0x47, 0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x09, 0xba, 0x48, 0x06, 0x32, 0x04, 0x10, 0x05, 0x20, 0x0a, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x0c, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54,
	0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x32, 0x06, 0x18, 0x80, 0x02, 0x28, 0x80, 0x01, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x47,
	0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x32, 0x06, 0x18, 0x80, 0x01, 0x28, 0x80, 0x02, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x30, 0x0a, 0x0c, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0xd8, 0x01, 0x01, 0x32, 0x06, 0x18, 0x80, 0x02, 0x28, 0x80,
	0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x13, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a,
	0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0d, 0x55,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02,
	0x40, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x1e, 0x0a, 0x0a, 0x53, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x11, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x0b, 0x53, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x11, 0x42, 0x07, 0xba, 0x48, 0x04, 0x3a, 0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x27, 0x0a, 0x08, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x12, 0x1b, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x09, 0xba, 0x48, 0x06, 0x3a,
	0x04, 0x30, 0x04, 0x30, 0x06, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28, 0x0a, 0x0b, 0x53, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x07, 0xba, 0x48, 0x04, 0x3a, 0x02, 0x38, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x54,
	0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x3a, 0x02, 0x10, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x09, 0x53,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x54, 0x45, 0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x08, 0xba, 0x48, 0x05, 0x3a, 0x03, 0x18, 0x80, 0x01, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x47, 0x54,
	0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x3a, 0x02, 0x20, 0x20, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x26, 0x0a, 0x09, 0x53,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x47, 0x54, 0x45, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x07, 0xba, 0x48, 0x04, 0x3a, 0x02, 0x28, 0x10, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x29, 0x0a, 0x0a, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x47, 0x54, 0x4c,
	0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x09,
	0xba, 0x48, 0x06, 0x3a, 0x04, 0x10, 0x14, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b,
	0x0a, 0x0c, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x4c, 0x54, 0x47, 0x54, 0x12, 0x1b,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x09, 0xba, 0x48, 0x06,
	0x3a, 0x04, 0x10, 0x00, 0x20, 0x14, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x0c, 0x53,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x3a, 0x06, 0x18,
	0x80, 0x04, 0x28, 0x80, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0e, 0x53, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x3a, 0x06,
	0x18, 0x80, 0x02, 0x28, 0x80, 0x04, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x30, 0x0a, 0x0c, 0x53,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0xd8, 0x01, 0x01,
	0x3a, 0x06, 0x18, 0x80, 0x04, 0x28, 0x80, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a,
	0x13, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x11, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0d, 0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x3a, 0x02, 0x40, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x1e,
	0x0a, 0x0a, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x28,
	0x0a, 0x0b, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x07, 0xba, 0x48, 0x04, 0x42,
	0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x27, 0x0a, 0x08, 0x53, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x49, 0x6e, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x12, 0x42, 0x09, 0xba, 0x48, 0x06, 0x42, 0x04, 0x30, 0x04, 0x30, 0x06, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x28, 0x0a, 0x0b, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4e, 0x6f, 0x74, 0x49, 0x6e,
	0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x42, 0x02, 0x38, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x53,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x12, 0x42, 0x07, 0xba, 0x48, 0x04, 0x42, 0x02, 0x10, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x27, 0x0a, 0x09, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x54, 0x45, 0x12,
	0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x08, 0xba, 0x48,
	0x05, 0x42, 0x03, 0x18, 0x80, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x25, 0x0a, 0x08, 0x53,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x12, 0x42, 0x07, 0xba, 0x48, 0x04, 0x42, 0x02, 0x20, 0x20, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x26, 0x0a, 0x09, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x45, 0x12,
	0x19, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x42, 0x02, 0x28, 0x10, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x29, 0x0a, 0x0a, 0x53, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x4c, 0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x09, 0xba, 0x48, 0x06, 0x42, 0x04, 0x10, 0x14, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0c, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45,
	0x78, 0x4c, 0x54, 0x47, 0x54, 0x12, 0x1b, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x12, 0x42, 0x09, 0xba, 0x48, 0x06, 0x42, 0x04, 0x10, 0x00, 0x20, 0x14, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x0c, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x47, 0x54, 0x45, 0x4c,
	0x54, 0x45, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42,
	0x0b, 0xba, 0x48, 0x08, 0x42, 0x06, 0x18, 0x80, 0x04, 0x28, 0x80, 0x02, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x2f, 0x0a, 0x0e, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x47, 0x54, 0x45,
	0x4c, 0x54, 0x45, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12,
	0x42, 0x0b, 0xba, 0x48, 0x08, 0x42, 0x06, 0x18, 0x80, 0x02, 0x28, 0x80, 0x04, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x30, 0x0a, 0x0c, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42,
	0x0e, 0xba, 0x48, 0x0b, 0xd8, 0x01, 0x01, 0x42, 0x06, 0x18, 0x80, 0x04, 0x28, 0x80, 0x02, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x13, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e,
	0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x25,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0d, 0x53, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x42, 0x07, 0xba, 0x48, 0x04, 0x42, 0x02, 0x40, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x1f, 0x0a, 0x0b, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x07, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x07, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x4a, 0x05, 0x0d, 0x01, 0x00, 0x00, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x09, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x49,
	0x6e, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x0f,
	0xba, 0x48, 0x0c, 0x4a, 0x0a, 0x35, 0x02, 0x00, 0x00, 0x00, 0x35, 0x03, 0x00, 0x00, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x4e,
	0x6f, 0x74, 0x49, 0x6e, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x07, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x4a, 0x05, 0x3d, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x29, 0x0a, 0x09, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x4c, 0x54, 0x12,
	0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x0a, 0xba, 0x48,
	0x07, 0x4a, 0x05, 0x15, 0x05, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a,
	0x0a, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x4c, 0x54, 0x45, 0x12, 0x1c, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x4a, 0x05, 0x1d,
	0x40, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x29, 0x0a, 0x09, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x47, 0x54, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x07, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x4a, 0x05, 0x25, 0x10, 0x00, 0x00, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0a, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x47,
	0x54, 0x45, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42,
	0x0a, 0xba, 0x48, 0x07, 0x4a, 0x05, 0x2d, 0x08, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x30, 0x0a, 0x0b, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x47, 0x54, 0x4c, 0x54, 0x12,
	0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x0f, 0xba, 0x48,
	0x0c, 0x4a, 0x0a, 0x15, 0x0a, 0x00, 0x00, 0x00, 0x25, 0x05, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x32, 0x0a, 0x0d, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x78, 0x4c,
	0x54, 0x47, 0x54, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07,
	0x42, 0x0f, 0xba, 0x48, 0x0c, 0x4a, 0x0a, 0x15, 0x05, 0x00, 0x00, 0x00, 0x25, 0x0a, 0x00, 0x00,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x32, 0x0a, 0x0d, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x07, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x4a, 0x0a, 0x1d, 0x00, 0x01, 0x00, 0x00,
	0x2d, 0x80, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x34, 0x0a, 0x0f, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x78, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x21, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x4a,
	0x0a, 0x1d, 0x80, 0x00, 0x00, 0x00, 0x2d, 0x00, 0x01, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x35, 0x0a, 0x0d, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x12, 0x24, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x12,
	0xba, 0x48, 0x0f, 0xd8, 0x01, 0x01, 0x4a, 0x0a, 0x1d, 0x00, 0x01, 0x00, 0x00, 0x2d, 0x80, 0x00,
	0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x34, 0x0a, 0x14, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x0a, 0xba, 0x48,
	0x07, 0x0a, 0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a,
	0x0e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12,
	0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x42, 0x0a, 0xba, 0x48,
	0x07, 0x4a, 0x05, 0x45, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x1f, 0x0a,
	0x0b, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x30,
	0x0a, 0x0c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x0e, 0xba, 0x48, 0x0b,
	0x52, 0x09, 0x09, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x36, 0x0a, 0x09, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x49, 0x6e, 0x12, 0x29, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x17, 0xba, 0x48, 0x14, 0x52,
	0x12, 0x31, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x03, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x30, 0x0a, 0x0c, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x52, 0x09, 0x39, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x09, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x4c, 0x54, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x06, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x52, 0x09, 0x11, 0x05, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0a, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x4c, 0x54, 0x45, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x06, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x52, 0x09, 0x19, 0x40, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x09, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x47, 0x54, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x06, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x52, 0x09, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0a, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x47, 0x54, 0x45, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x06, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x52, 0x09, 0x29, 0x08, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x0b, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x47, 0x54, 0x4c, 0x54, 0x12, 0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x06, 0x42, 0x17, 0xba, 0x48, 0x14, 0x52, 0x12, 0x11, 0x0a, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x21, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x3a, 0x0a, 0x0d, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x4c,
	0x54, 0x47, 0x54, 0x12, 0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06,
	0x42, 0x17, 0xba, 0x48, 0x14, 0x52, 0x12, 0x11, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x21, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3a,
	0x0a, 0x0d, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12,
	0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x17, 0xba, 0x48,
	0x14, 0x52, 0x12, 0x19, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x80, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3c, 0x0a, 0x0f, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x29, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x17, 0xba, 0x48, 0x14, 0x52,
	0x12, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x00, 0x01, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3d, 0x0a, 0x0d, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x2c, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x1a, 0xba, 0x48, 0x17, 0xd8, 0x01, 0x01, 0x52, 0x12,
	0x19, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x34, 0x0a, 0x14, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x0a, 0xba, 0x48,
	0x07, 0x0a, 0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x32, 0x0a,
	0x0e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12,
	0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x0e, 0xba, 0x48,
	0x0b, 0x52, 0x09, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x20, 0x0a, 0x0c, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x4e, 0x6f, 0x6e,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x0d, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0f, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x5a, 0x05, 0x0d, 0x01, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0a, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x49, 0x6e,
	0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x0f, 0xba,
	0x48, 0x0c, 0x5a, 0x0a, 0x35, 0x02, 0x00, 0x00, 0x00, 0x35, 0x03, 0x00, 0x00, 0x00, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x2d, 0x0a, 0x0d, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x4e,
	0x6f, 0x74, 0x49, 0x6e, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0f, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x5a, 0x05, 0x3d, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0a, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x4c, 0x54,
	0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x0a, 0xba,
	0x48, 0x07, 0x5a, 0x05, 0x15, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b,
	0x0a, 0x0b, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x4c, 0x54, 0x45, 0x12, 0x1c, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x5a,
	0x05, 0x1d, 0x40, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x0a, 0x53,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x47, 0x54, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x5a, 0x05, 0x25, 0x10, 0x00,
	0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2b, 0x0a, 0x0b, 0x53, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x47, 0x54, 0x45, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0f, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x5a, 0x05, 0x2d, 0x08, 0x00, 0x00, 0x00, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x22, 0x31, 0x0a, 0x0c, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x47, 0x54, 0x4c, 0x54, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0f, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x5a, 0x0a, 0x15, 0x0a, 0x00, 0x00, 0x00, 0x25, 0x00, 0x00,
	0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x0e, 0x53, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x45, 0x78, 0x4c, 0x54, 0x47, 0x54, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x5a, 0x0a, 0x15, 0x00, 0x00,
	0x00, 0x00, 0x25, 0x0a, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x0e,
	0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x47, 0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x21,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x0f, 0xba, 0x48, 0x0c,
	0x5a, 0x0a, 0x1d, 0x00, 0x01, 0x00, 0x00, 0x2d, 0x80, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0x35, 0x0a, 0x10, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x78, 0x47,
	0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0f, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x5a, 0x0a, 0x1d, 0x80, 0x00, 0x00, 0x00, 0x2d, 0x00,
	0x01, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x36, 0x0a, 0x0e, 0x53, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x24, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x12, 0xba, 0x48, 0x0f, 0xd8, 0x01, 0x01, 0x5a,
	0x0a, 0x1d, 0x00, 0x01, 0x00, 0x00, 0x2d, 0x80, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x35, 0x0a, 0x15, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x49, 0x6e, 0x63, 0x6f,
	0x72, 0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x25, 0x00, 0x00,
	0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0f, 0x53, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x5a, 0x05, 0x45, 0x00,
	0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x20, 0x0a, 0x0c, 0x53, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x10, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x31, 0x0a, 0x0d, 0x53, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x62, 0x09, 0x09,
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x37, 0x0a,
	0x0a, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x49, 0x6e, 0x12, 0x29, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x17, 0xba, 0x48, 0x14, 0x62, 0x12, 0x31,
	0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x31, 0x0a, 0x0d, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x10, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x62, 0x09, 0x39, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0a, 0x53, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x4c, 0x54, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x10, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x62, 0x09, 0x11, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0b, 0x53, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x4c, 0x54, 0x45, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x62, 0x09, 0x19, 0x40, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0a, 0x53, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x47, 0x54, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x62, 0x09, 0x21, 0x10, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x2f, 0x0a, 0x0b, 0x53, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x47, 0x54, 0x45, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x62, 0x09, 0x29, 0x08, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x39, 0x0a, 0x0c, 0x53,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x47, 0x54, 0x4c, 0x54, 0x12, 0x29, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x17, 0xba, 0x48, 0x14, 0x62, 0x12, 0x11,
	0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3b, 0x0a, 0x0e, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x45, 0x78, 0x4c, 0x54, 0x47, 0x54, 0x12, 0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x17, 0xba, 0x48, 0x14, 0x62, 0x12, 0x11, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x3b, 0x0a, 0x0e, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x47,
	0x54, 0x45, 0x4c, 0x54, 0x45, 0x12, 0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x10, 0x42, 0x17, 0xba, 0x48, 0x14, 0x62, 0x12, 0x19, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x29, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x3d, 0x0a, 0x10, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x47, 0x54,
	0x45, 0x4c, 0x54, 0x45, 0x12, 0x29, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x10, 0x42, 0x17, 0xba, 0x48, 0x14, 0x62, 0x12, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x29, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x3e, 0x0a, 0x0e, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x12, 0x2c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x1a,
	0xba, 0x48, 0x17, 0xd8, 0x01, 0x01, 0x62, 0x12, 0x19, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x29, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x35, 0x0a, 0x15, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x49, 0x6e, 0x63, 0x6f, 0x72,
	0x72, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x0a, 0x05, 0x25, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x33, 0x0a, 0x0f, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42, 0x0e, 0xba, 0x48, 0x0b, 0x62, 0x09, 0x41, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3a, 0x0a, 0x10, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x4c, 0x54, 0x45, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x12,
	0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x22, 0x02, 0x18, 0x40, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42,
	0x06, 0x0a, 0x04, 0x5f, 0x76, 0x61, 0x6c, 0x42, 0xa3, 0x02, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x42, 0x0c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x53,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x75, 0x66, 0x62, 0x75,
	0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61,
	0x73, 0x65, 0x73, 0xa2, 0x02, 0x04, 0x42, 0x56, 0x43, 0x43, 0xaa, 0x02, 0x1e, 0x42, 0x75, 0x66,
	0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x73, 0xca, 0x02, 0x1e, 0x42, 0x75,
	0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0xe2, 0x02, 0x2a, 0x42,
	0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x5c, 0x47, 0x50,
	0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x21, 0x42, 0x75, 0x66, 0x3a,
	0x3a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_buf_validate_conformance_cases_numbers_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_numbers_proto_rawDescData = file_buf_validate_conformance_cases_numbers_proto_rawDesc
)

func file_buf_validate_conformance_cases_numbers_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_numbers_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_numbers_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_numbers_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_numbers_proto_rawDescData
}

var file_buf_validate_conformance_cases_numbers_proto_msgTypes = make([]protoimpl.MessageInfo, 186)
var file_buf_validate_conformance_cases_numbers_proto_goTypes = []any{
	(*FloatNone)(nil),             // 0: buf.validate.conformance.cases.FloatNone
	(*FloatConst)(nil),            // 1: buf.validate.conformance.cases.FloatConst
	(*FloatIn)(nil),               // 2: buf.validate.conformance.cases.FloatIn
	(*FloatNotIn)(nil),            // 3: buf.validate.conformance.cases.FloatNotIn
	(*FloatLT)(nil),               // 4: buf.validate.conformance.cases.FloatLT
	(*FloatLTE)(nil),              // 5: buf.validate.conformance.cases.FloatLTE
	(*FloatGT)(nil),               // 6: buf.validate.conformance.cases.FloatGT
	(*FloatGTE)(nil),              // 7: buf.validate.conformance.cases.FloatGTE
	(*FloatGTLT)(nil),             // 8: buf.validate.conformance.cases.FloatGTLT
	(*FloatExLTGT)(nil),           // 9: buf.validate.conformance.cases.FloatExLTGT
	(*FloatGTELTE)(nil),           // 10: buf.validate.conformance.cases.FloatGTELTE
	(*FloatExGTELTE)(nil),         // 11: buf.validate.conformance.cases.FloatExGTELTE
	(*FloatFinite)(nil),           // 12: buf.validate.conformance.cases.FloatFinite
	(*FloatNotFinite)(nil),        // 13: buf.validate.conformance.cases.FloatNotFinite
	(*FloatIgnore)(nil),           // 14: buf.validate.conformance.cases.FloatIgnore
	(*FloatIncorrectType)(nil),    // 15: buf.validate.conformance.cases.FloatIncorrectType
	(*FloatExample)(nil),          // 16: buf.validate.conformance.cases.FloatExample
	(*DoubleNone)(nil),            // 17: buf.validate.conformance.cases.DoubleNone
	(*DoubleConst)(nil),           // 18: buf.validate.conformance.cases.DoubleConst
	(*DoubleIn)(nil),              // 19: buf.validate.conformance.cases.DoubleIn
	(*DoubleNotIn)(nil),           // 20: buf.validate.conformance.cases.DoubleNotIn
	(*DoubleLT)(nil),              // 21: buf.validate.conformance.cases.DoubleLT
	(*DoubleLTE)(nil),             // 22: buf.validate.conformance.cases.DoubleLTE
	(*DoubleGT)(nil),              // 23: buf.validate.conformance.cases.DoubleGT
	(*DoubleGTE)(nil),             // 24: buf.validate.conformance.cases.DoubleGTE
	(*DoubleGTLT)(nil),            // 25: buf.validate.conformance.cases.DoubleGTLT
	(*DoubleExLTGT)(nil),          // 26: buf.validate.conformance.cases.DoubleExLTGT
	(*DoubleGTELTE)(nil),          // 27: buf.validate.conformance.cases.DoubleGTELTE
	(*DoubleExGTELTE)(nil),        // 28: buf.validate.conformance.cases.DoubleExGTELTE
	(*DoubleFinite)(nil),          // 29: buf.validate.conformance.cases.DoubleFinite
	(*DoubleNotFinite)(nil),       // 30: buf.validate.conformance.cases.DoubleNotFinite
	(*DoubleIgnore)(nil),          // 31: buf.validate.conformance.cases.DoubleIgnore
	(*DoubleIncorrectType)(nil),   // 32: buf.validate.conformance.cases.DoubleIncorrectType
	(*DoubleExample)(nil),         // 33: buf.validate.conformance.cases.DoubleExample
	(*Int32None)(nil),             // 34: buf.validate.conformance.cases.Int32None
	(*Int32Const)(nil),            // 35: buf.validate.conformance.cases.Int32Const
	(*Int32In)(nil),               // 36: buf.validate.conformance.cases.Int32In
	(*Int32NotIn)(nil),            // 37: buf.validate.conformance.cases.Int32NotIn
	(*Int32LT)(nil),               // 38: buf.validate.conformance.cases.Int32LT
	(*Int32LTE)(nil),              // 39: buf.validate.conformance.cases.Int32LTE
	(*Int32GT)(nil),               // 40: buf.validate.conformance.cases.Int32GT
	(*Int32GTE)(nil),              // 41: buf.validate.conformance.cases.Int32GTE
	(*Int32GTLT)(nil),             // 42: buf.validate.conformance.cases.Int32GTLT
	(*Int32ExLTGT)(nil),           // 43: buf.validate.conformance.cases.Int32ExLTGT
	(*Int32GTELTE)(nil),           // 44: buf.validate.conformance.cases.Int32GTELTE
	(*Int32ExGTELTE)(nil),         // 45: buf.validate.conformance.cases.Int32ExGTELTE
	(*Int32Ignore)(nil),           // 46: buf.validate.conformance.cases.Int32Ignore
	(*Int32IncorrectType)(nil),    // 47: buf.validate.conformance.cases.Int32IncorrectType
	(*Int32Example)(nil),          // 48: buf.validate.conformance.cases.Int32Example
	(*Int64None)(nil),             // 49: buf.validate.conformance.cases.Int64None
	(*Int64Const)(nil),            // 50: buf.validate.conformance.cases.Int64Const
	(*Int64In)(nil),               // 51: buf.validate.conformance.cases.Int64In
	(*Int64NotIn)(nil),            // 52: buf.validate.conformance.cases.Int64NotIn
	(*Int64LT)(nil),               // 53: buf.validate.conformance.cases.Int64LT
	(*Int64LTE)(nil),              // 54: buf.validate.conformance.cases.Int64LTE
	(*Int64GT)(nil),               // 55: buf.validate.conformance.cases.Int64GT
	(*Int64GTE)(nil),              // 56: buf.validate.conformance.cases.Int64GTE
	(*Int64GTLT)(nil),             // 57: buf.validate.conformance.cases.Int64GTLT
	(*Int64ExLTGT)(nil),           // 58: buf.validate.conformance.cases.Int64ExLTGT
	(*Int64GTELTE)(nil),           // 59: buf.validate.conformance.cases.Int64GTELTE
	(*Int64ExGTELTE)(nil),         // 60: buf.validate.conformance.cases.Int64ExGTELTE
	(*Int64Ignore)(nil),           // 61: buf.validate.conformance.cases.Int64Ignore
	(*Int64BigConstraints)(nil),   // 62: buf.validate.conformance.cases.Int64BigConstraints
	(*Int64IncorrectType)(nil),    // 63: buf.validate.conformance.cases.Int64IncorrectType
	(*Int64Example)(nil),          // 64: buf.validate.conformance.cases.Int64Example
	(*UInt32None)(nil),            // 65: buf.validate.conformance.cases.UInt32None
	(*UInt32Const)(nil),           // 66: buf.validate.conformance.cases.UInt32Const
	(*UInt32In)(nil),              // 67: buf.validate.conformance.cases.UInt32In
	(*UInt32NotIn)(nil),           // 68: buf.validate.conformance.cases.UInt32NotIn
	(*UInt32LT)(nil),              // 69: buf.validate.conformance.cases.UInt32LT
	(*UInt32LTE)(nil),             // 70: buf.validate.conformance.cases.UInt32LTE
	(*UInt32GT)(nil),              // 71: buf.validate.conformance.cases.UInt32GT
	(*UInt32GTE)(nil),             // 72: buf.validate.conformance.cases.UInt32GTE
	(*UInt32GTLT)(nil),            // 73: buf.validate.conformance.cases.UInt32GTLT
	(*UInt32ExLTGT)(nil),          // 74: buf.validate.conformance.cases.UInt32ExLTGT
	(*UInt32GTELTE)(nil),          // 75: buf.validate.conformance.cases.UInt32GTELTE
	(*UInt32ExGTELTE)(nil),        // 76: buf.validate.conformance.cases.UInt32ExGTELTE
	(*UInt32Ignore)(nil),          // 77: buf.validate.conformance.cases.UInt32Ignore
	(*UInt32IncorrectType)(nil),   // 78: buf.validate.conformance.cases.UInt32IncorrectType
	(*UInt32Example)(nil),         // 79: buf.validate.conformance.cases.UInt32Example
	(*UInt64None)(nil),            // 80: buf.validate.conformance.cases.UInt64None
	(*UInt64Const)(nil),           // 81: buf.validate.conformance.cases.UInt64Const
	(*UInt64In)(nil),              // 82: buf.validate.conformance.cases.UInt64In
	(*UInt64NotIn)(nil),           // 83: buf.validate.conformance.cases.UInt64NotIn
	(*UInt64LT)(nil),              // 84: buf.validate.conformance.cases.UInt64LT
	(*UInt64LTE)(nil),             // 85: buf.validate.conformance.cases.UInt64LTE
	(*UInt64GT)(nil),              // 86: buf.validate.conformance.cases.UInt64GT
	(*UInt64GTE)(nil),             // 87: buf.validate.conformance.cases.UInt64GTE
	(*UInt64GTLT)(nil),            // 88: buf.validate.conformance.cases.UInt64GTLT
	(*UInt64ExLTGT)(nil),          // 89: buf.validate.conformance.cases.UInt64ExLTGT
	(*UInt64GTELTE)(nil),          // 90: buf.validate.conformance.cases.UInt64GTELTE
	(*UInt64ExGTELTE)(nil),        // 91: buf.validate.conformance.cases.UInt64ExGTELTE
	(*UInt64Ignore)(nil),          // 92: buf.validate.conformance.cases.UInt64Ignore
	(*UInt64IncorrectType)(nil),   // 93: buf.validate.conformance.cases.UInt64IncorrectType
	(*UInt64Example)(nil),         // 94: buf.validate.conformance.cases.UInt64Example
	(*SInt32None)(nil),            // 95: buf.validate.conformance.cases.SInt32None
	(*SInt32Const)(nil),           // 96: buf.validate.conformance.cases.SInt32Const
	(*SInt32In)(nil),              // 97: buf.validate.conformance.cases.SInt32In
	(*SInt32NotIn)(nil),           // 98: buf.validate.conformance.cases.SInt32NotIn
	(*SInt32LT)(nil),              // 99: buf.validate.conformance.cases.SInt32LT
	(*SInt32LTE)(nil),             // 100: buf.validate.conformance.cases.SInt32LTE
	(*SInt32GT)(nil),              // 101: buf.validate.conformance.cases.SInt32GT
	(*SInt32GTE)(nil),             // 102: buf.validate.conformance.cases.SInt32GTE
	(*SInt32GTLT)(nil),            // 103: buf.validate.conformance.cases.SInt32GTLT
	(*SInt32ExLTGT)(nil),          // 104: buf.validate.conformance.cases.SInt32ExLTGT
	(*SInt32GTELTE)(nil),          // 105: buf.validate.conformance.cases.SInt32GTELTE
	(*SInt32ExGTELTE)(nil),        // 106: buf.validate.conformance.cases.SInt32ExGTELTE
	(*SInt32Ignore)(nil),          // 107: buf.validate.conformance.cases.SInt32Ignore
	(*SInt32IncorrectType)(nil),   // 108: buf.validate.conformance.cases.SInt32IncorrectType
	(*SInt32Example)(nil),         // 109: buf.validate.conformance.cases.SInt32Example
	(*SInt64None)(nil),            // 110: buf.validate.conformance.cases.SInt64None
	(*SInt64Const)(nil),           // 111: buf.validate.conformance.cases.SInt64Const
	(*SInt64In)(nil),              // 112: buf.validate.conformance.cases.SInt64In
	(*SInt64NotIn)(nil),           // 113: buf.validate.conformance.cases.SInt64NotIn
	(*SInt64LT)(nil),              // 114: buf.validate.conformance.cases.SInt64LT
	(*SInt64LTE)(nil),             // 115: buf.validate.conformance.cases.SInt64LTE
	(*SInt64GT)(nil),              // 116: buf.validate.conformance.cases.SInt64GT
	(*SInt64GTE)(nil),             // 117: buf.validate.conformance.cases.SInt64GTE
	(*SInt64GTLT)(nil),            // 118: buf.validate.conformance.cases.SInt64GTLT
	(*SInt64ExLTGT)(nil),          // 119: buf.validate.conformance.cases.SInt64ExLTGT
	(*SInt64GTELTE)(nil),          // 120: buf.validate.conformance.cases.SInt64GTELTE
	(*SInt64ExGTELTE)(nil),        // 121: buf.validate.conformance.cases.SInt64ExGTELTE
	(*SInt64Ignore)(nil),          // 122: buf.validate.conformance.cases.SInt64Ignore
	(*SInt64IncorrectType)(nil),   // 123: buf.validate.conformance.cases.SInt64IncorrectType
	(*SInt64Example)(nil),         // 124: buf.validate.conformance.cases.SInt64Example
	(*Fixed32None)(nil),           // 125: buf.validate.conformance.cases.Fixed32None
	(*Fixed32Const)(nil),          // 126: buf.validate.conformance.cases.Fixed32Const
	(*Fixed32In)(nil),             // 127: buf.validate.conformance.cases.Fixed32In
	(*Fixed32NotIn)(nil),          // 128: buf.validate.conformance.cases.Fixed32NotIn
	(*Fixed32LT)(nil),             // 129: buf.validate.conformance.cases.Fixed32LT
	(*Fixed32LTE)(nil),            // 130: buf.validate.conformance.cases.Fixed32LTE
	(*Fixed32GT)(nil),             // 131: buf.validate.conformance.cases.Fixed32GT
	(*Fixed32GTE)(nil),            // 132: buf.validate.conformance.cases.Fixed32GTE
	(*Fixed32GTLT)(nil),           // 133: buf.validate.conformance.cases.Fixed32GTLT
	(*Fixed32ExLTGT)(nil),         // 134: buf.validate.conformance.cases.Fixed32ExLTGT
	(*Fixed32GTELTE)(nil),         // 135: buf.validate.conformance.cases.Fixed32GTELTE
	(*Fixed32ExGTELTE)(nil),       // 136: buf.validate.conformance.cases.Fixed32ExGTELTE
	(*Fixed32Ignore)(nil),         // 137: buf.validate.conformance.cases.Fixed32Ignore
	(*Fixed32IncorrectType)(nil),  // 138: buf.validate.conformance.cases.Fixed32IncorrectType
	(*Fixed32Example)(nil),        // 139: buf.validate.conformance.cases.Fixed32Example
	(*Fixed64None)(nil),           // 140: buf.validate.conformance.cases.Fixed64None
	(*Fixed64Const)(nil),          // 141: buf.validate.conformance.cases.Fixed64Const
	(*Fixed64In)(nil),             // 142: buf.validate.conformance.cases.Fixed64In
	(*Fixed64NotIn)(nil),          // 143: buf.validate.conformance.cases.Fixed64NotIn
	(*Fixed64LT)(nil),             // 144: buf.validate.conformance.cases.Fixed64LT
	(*Fixed64LTE)(nil),            // 145: buf.validate.conformance.cases.Fixed64LTE
	(*Fixed64GT)(nil),             // 146: buf.validate.conformance.cases.Fixed64GT
	(*Fixed64GTE)(nil),            // 147: buf.validate.conformance.cases.Fixed64GTE
	(*Fixed64GTLT)(nil),           // 148: buf.validate.conformance.cases.Fixed64GTLT
	(*Fixed64ExLTGT)(nil),         // 149: buf.validate.conformance.cases.Fixed64ExLTGT
	(*Fixed64GTELTE)(nil),         // 150: buf.validate.conformance.cases.Fixed64GTELTE
	(*Fixed64ExGTELTE)(nil),       // 151: buf.validate.conformance.cases.Fixed64ExGTELTE
	(*Fixed64Ignore)(nil),         // 152: buf.validate.conformance.cases.Fixed64Ignore
	(*Fixed64IncorrectType)(nil),  // 153: buf.validate.conformance.cases.Fixed64IncorrectType
	(*Fixed64Example)(nil),        // 154: buf.validate.conformance.cases.Fixed64Example
	(*SFixed32None)(nil),          // 155: buf.validate.conformance.cases.SFixed32None
	(*SFixed32Const)(nil),         // 156: buf.validate.conformance.cases.SFixed32Const
	(*SFixed32In)(nil),            // 157: buf.validate.conformance.cases.SFixed32In
	(*SFixed32NotIn)(nil),         // 158: buf.validate.conformance.cases.SFixed32NotIn
	(*SFixed32LT)(nil),            // 159: buf.validate.conformance.cases.SFixed32LT
	(*SFixed32LTE)(nil),           // 160: buf.validate.conformance.cases.SFixed32LTE
	(*SFixed32GT)(nil),            // 161: buf.validate.conformance.cases.SFixed32GT
	(*SFixed32GTE)(nil),           // 162: buf.validate.conformance.cases.SFixed32GTE
	(*SFixed32GTLT)(nil),          // 163: buf.validate.conformance.cases.SFixed32GTLT
	(*SFixed32ExLTGT)(nil),        // 164: buf.validate.conformance.cases.SFixed32ExLTGT
	(*SFixed32GTELTE)(nil),        // 165: buf.validate.conformance.cases.SFixed32GTELTE
	(*SFixed32ExGTELTE)(nil),      // 166: buf.validate.conformance.cases.SFixed32ExGTELTE
	(*SFixed32Ignore)(nil),        // 167: buf.validate.conformance.cases.SFixed32Ignore
	(*SFixed32IncorrectType)(nil), // 168: buf.validate.conformance.cases.SFixed32IncorrectType
	(*SFixed32Example)(nil),       // 169: buf.validate.conformance.cases.SFixed32Example
	(*SFixed64None)(nil),          // 170: buf.validate.conformance.cases.SFixed64None
	(*SFixed64Const)(nil),         // 171: buf.validate.conformance.cases.SFixed64Const
	(*SFixed64In)(nil),            // 172: buf.validate.conformance.cases.SFixed64In
	(*SFixed64NotIn)(nil),         // 173: buf.validate.conformance.cases.SFixed64NotIn
	(*SFixed64LT)(nil),            // 174: buf.validate.conformance.cases.SFixed64LT
	(*SFixed64LTE)(nil),           // 175: buf.validate.conformance.cases.SFixed64LTE
	(*SFixed64GT)(nil),            // 176: buf.validate.conformance.cases.SFixed64GT
	(*SFixed64GTE)(nil),           // 177: buf.validate.conformance.cases.SFixed64GTE
	(*SFixed64GTLT)(nil),          // 178: buf.validate.conformance.cases.SFixed64GTLT
	(*SFixed64ExLTGT)(nil),        // 179: buf.validate.conformance.cases.SFixed64ExLTGT
	(*SFixed64GTELTE)(nil),        // 180: buf.validate.conformance.cases.SFixed64GTELTE
	(*SFixed64ExGTELTE)(nil),      // 181: buf.validate.conformance.cases.SFixed64ExGTELTE
	(*SFixed64Ignore)(nil),        // 182: buf.validate.conformance.cases.SFixed64Ignore
	(*SFixed64IncorrectType)(nil), // 183: buf.validate.conformance.cases.SFixed64IncorrectType
	(*SFixed64Example)(nil),       // 184: buf.validate.conformance.cases.SFixed64Example
	(*Int64LTEOptional)(nil),      // 185: buf.validate.conformance.cases.Int64LTEOptional
}
var file_buf_validate_conformance_cases_numbers_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_numbers_proto_init() }
func file_buf_validate_conformance_cases_numbers_proto_init() {
	if File_buf_validate_conformance_cases_numbers_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*FloatNone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*FloatConst); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*FloatIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*FloatNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*FloatLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*FloatLTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*FloatGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*FloatGTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*FloatGTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*FloatExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*FloatGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*FloatExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*FloatFinite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*FloatNotFinite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*FloatIgnore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*FloatIncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*FloatExample); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleNone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleConst); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleNotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleLTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleGTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleGTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleFinite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleNotFinite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleIgnore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleIncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*DoubleExample); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*Int32None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*Int32Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*Int32In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*Int32NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*Int32LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*Int32LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*Int32GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*Int32GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*Int32GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*Int32ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*Int32GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*Int32ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*Int32Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*Int32IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*Int32Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*Int64None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*Int64Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*Int64In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[52].Exporter = func(v any, i int) any {
			switch v := v.(*Int64NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[53].Exporter = func(v any, i int) any {
			switch v := v.(*Int64LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[54].Exporter = func(v any, i int) any {
			switch v := v.(*Int64LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[55].Exporter = func(v any, i int) any {
			switch v := v.(*Int64GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[56].Exporter = func(v any, i int) any {
			switch v := v.(*Int64GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[57].Exporter = func(v any, i int) any {
			switch v := v.(*Int64GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[58].Exporter = func(v any, i int) any {
			switch v := v.(*Int64ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[59].Exporter = func(v any, i int) any {
			switch v := v.(*Int64GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[60].Exporter = func(v any, i int) any {
			switch v := v.(*Int64ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[61].Exporter = func(v any, i int) any {
			switch v := v.(*Int64Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[62].Exporter = func(v any, i int) any {
			switch v := v.(*Int64BigConstraints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[63].Exporter = func(v any, i int) any {
			switch v := v.(*Int64IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[64].Exporter = func(v any, i int) any {
			switch v := v.(*Int64Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[65].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[66].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[67].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[68].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[69].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[70].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[71].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[72].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[73].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[74].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[75].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[76].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[77].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[78].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[79].Exporter = func(v any, i int) any {
			switch v := v.(*UInt32Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[80].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[81].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[82].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[83].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[84].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[85].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[86].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[87].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[88].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[89].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[90].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[91].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[92].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[93].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[94].Exporter = func(v any, i int) any {
			switch v := v.(*UInt64Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[95].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[96].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[97].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[98].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[99].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[100].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[101].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[102].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[103].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[104].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[105].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[106].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[107].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[108].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[109].Exporter = func(v any, i int) any {
			switch v := v.(*SInt32Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[110].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[111].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[112].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[113].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[114].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[115].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[116].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[117].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[118].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[119].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[120].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[121].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[122].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[123].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[124].Exporter = func(v any, i int) any {
			switch v := v.(*SInt64Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[125].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[126].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[127].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[128].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[129].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[130].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[131].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[132].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[133].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[134].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[135].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[136].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[137].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[138].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[139].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed32Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[140].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[141].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[142].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[143].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[144].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[145].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[146].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[147].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[148].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[149].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[150].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[151].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[152].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[153].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[154].Exporter = func(v any, i int) any {
			switch v := v.(*Fixed64Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[155].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[156].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[157].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[158].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[159].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[160].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[161].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[162].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[163].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[164].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[165].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[166].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[167].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[168].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[169].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed32Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[170].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64None); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[171].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64Const); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[172].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64In); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[173].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64NotIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[174].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64LT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[175].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64LTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[176].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64GT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[177].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64GTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[178].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64GTLT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[179].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64ExLTGT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[180].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64GTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[181].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64ExGTELTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[182].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64Ignore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[183].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64IncorrectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[184].Exporter = func(v any, i int) any {
			switch v := v.(*SFixed64Example); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_numbers_proto_msgTypes[185].Exporter = func(v any, i int) any {
			switch v := v.(*Int64LTEOptional); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_numbers_proto_msgTypes[185].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_numbers_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   186,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_numbers_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_numbers_proto_depIdxs,
		MessageInfos:      file_buf_validate_conformance_cases_numbers_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_numbers_proto = out.File
	file_buf_validate_conformance_cases_numbers_proto_rawDesc = nil
	file_buf_validate_conformance_cases_numbers_proto_goTypes = nil
	file_buf_validate_conformance_cases_numbers_proto_depIdxs = nil
}
