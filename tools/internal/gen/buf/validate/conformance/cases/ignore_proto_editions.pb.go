// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: buf/validate/conformance/cases/ignore_proto_editions.proto

package cases

import (
	_ "github.com/bufbuild/protovalidate/tools/internal/gen/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EditionsScalarExplicitPresenceIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarExplicitPresenceIgnoreUnspecified) Reset() {
	*x = EditionsScalarExplicitPresenceIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarExplicitPresenceIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarExplicitPresenceIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsScalarExplicitPresenceIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarExplicitPresenceIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsScalarExplicitPresenceIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{0}
}

func (x *EditionsScalarExplicitPresenceIgnoreUnspecified) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,def=-42" json:"val,omitempty"`
}

// Default values for EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault fields.
const (
	Default_EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault_Val = int32(-42)
)

func (x *EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault) Reset() {
	*x = EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault) ProtoMessage() {}

func (x *EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{1}
}

func (x *EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault_Val
}

type EditionsScalarExplicitPresenceIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarExplicitPresenceIgnoreEmpty) Reset() {
	*x = EditionsScalarExplicitPresenceIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarExplicitPresenceIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarExplicitPresenceIgnoreEmpty) ProtoMessage() {}

func (x *EditionsScalarExplicitPresenceIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarExplicitPresenceIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsScalarExplicitPresenceIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{2}
}

func (x *EditionsScalarExplicitPresenceIgnoreEmpty) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type EditionsScalarExplicitPresenceIgnoreEmptyWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,def=-42" json:"val,omitempty"`
}

// Default values for EditionsScalarExplicitPresenceIgnoreEmptyWithDefault fields.
const (
	Default_EditionsScalarExplicitPresenceIgnoreEmptyWithDefault_Val = int32(-42)
)

func (x *EditionsScalarExplicitPresenceIgnoreEmptyWithDefault) Reset() {
	*x = EditionsScalarExplicitPresenceIgnoreEmptyWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarExplicitPresenceIgnoreEmptyWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarExplicitPresenceIgnoreEmptyWithDefault) ProtoMessage() {}

func (x *EditionsScalarExplicitPresenceIgnoreEmptyWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarExplicitPresenceIgnoreEmptyWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarExplicitPresenceIgnoreEmptyWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{3}
}

func (x *EditionsScalarExplicitPresenceIgnoreEmptyWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_EditionsScalarExplicitPresenceIgnoreEmptyWithDefault_Val
}

type EditionsScalarExplicitPresenceIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarExplicitPresenceIgnoreDefault) Reset() {
	*x = EditionsScalarExplicitPresenceIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarExplicitPresenceIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarExplicitPresenceIgnoreDefault) ProtoMessage() {}

func (x *EditionsScalarExplicitPresenceIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarExplicitPresenceIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarExplicitPresenceIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{4}
}

func (x *EditionsScalarExplicitPresenceIgnoreDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type EditionsScalarExplicitPresenceIgnoreDefaultWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,opt,name=val,def=-42" json:"val,omitempty"`
}

// Default values for EditionsScalarExplicitPresenceIgnoreDefaultWithDefault fields.
const (
	Default_EditionsScalarExplicitPresenceIgnoreDefaultWithDefault_Val = int32(-42)
)

func (x *EditionsScalarExplicitPresenceIgnoreDefaultWithDefault) Reset() {
	*x = EditionsScalarExplicitPresenceIgnoreDefaultWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarExplicitPresenceIgnoreDefaultWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarExplicitPresenceIgnoreDefaultWithDefault) ProtoMessage() {}

func (x *EditionsScalarExplicitPresenceIgnoreDefaultWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarExplicitPresenceIgnoreDefaultWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarExplicitPresenceIgnoreDefaultWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{5}
}

func (x *EditionsScalarExplicitPresenceIgnoreDefaultWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_EditionsScalarExplicitPresenceIgnoreDefaultWithDefault_Val
}

type EditionsScalarImplicitPresenceIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarImplicitPresenceIgnoreUnspecified) Reset() {
	*x = EditionsScalarImplicitPresenceIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarImplicitPresenceIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarImplicitPresenceIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsScalarImplicitPresenceIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarImplicitPresenceIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsScalarImplicitPresenceIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{6}
}

func (x *EditionsScalarImplicitPresenceIgnoreUnspecified) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type EditionsScalarImplicitPresenceIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarImplicitPresenceIgnoreEmpty) Reset() {
	*x = EditionsScalarImplicitPresenceIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarImplicitPresenceIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarImplicitPresenceIgnoreEmpty) ProtoMessage() {}

func (x *EditionsScalarImplicitPresenceIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarImplicitPresenceIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsScalarImplicitPresenceIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{7}
}

func (x *EditionsScalarImplicitPresenceIgnoreEmpty) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type EditionsScalarImplicitPresenceIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val int32 `protobuf:"varint,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarImplicitPresenceIgnoreDefault) Reset() {
	*x = EditionsScalarImplicitPresenceIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarImplicitPresenceIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarImplicitPresenceIgnoreDefault) ProtoMessage() {}

func (x *EditionsScalarImplicitPresenceIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarImplicitPresenceIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarImplicitPresenceIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{8}
}

func (x *EditionsScalarImplicitPresenceIgnoreDefault) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type EditionsScalarLegacyRequiredIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarLegacyRequiredIgnoreUnspecified) Reset() {
	*x = EditionsScalarLegacyRequiredIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarLegacyRequiredIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarLegacyRequiredIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsScalarLegacyRequiredIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarLegacyRequiredIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsScalarLegacyRequiredIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{9}
}

func (x *EditionsScalarLegacyRequiredIgnoreUnspecified) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val,def=-42" json:"val,omitempty"`
}

// Default values for EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault fields.
const (
	Default_EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault_Val = int32(-42)
)

func (x *EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault) Reset() {
	*x = EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault) ProtoMessage() {}

func (x *EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{10}
}

func (x *EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault_Val
}

type EditionsScalarLegacyRequiredIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarLegacyRequiredIgnoreEmpty) Reset() {
	*x = EditionsScalarLegacyRequiredIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarLegacyRequiredIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarLegacyRequiredIgnoreEmpty) ProtoMessage() {}

func (x *EditionsScalarLegacyRequiredIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarLegacyRequiredIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsScalarLegacyRequiredIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{11}
}

func (x *EditionsScalarLegacyRequiredIgnoreEmpty) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type EditionsScalarLegacyRequiredIgnoreEmptyWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val,def=-42" json:"val,omitempty"`
}

// Default values for EditionsScalarLegacyRequiredIgnoreEmptyWithDefault fields.
const (
	Default_EditionsScalarLegacyRequiredIgnoreEmptyWithDefault_Val = int32(-42)
)

func (x *EditionsScalarLegacyRequiredIgnoreEmptyWithDefault) Reset() {
	*x = EditionsScalarLegacyRequiredIgnoreEmptyWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarLegacyRequiredIgnoreEmptyWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarLegacyRequiredIgnoreEmptyWithDefault) ProtoMessage() {}

func (x *EditionsScalarLegacyRequiredIgnoreEmptyWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarLegacyRequiredIgnoreEmptyWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarLegacyRequiredIgnoreEmptyWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{12}
}

func (x *EditionsScalarLegacyRequiredIgnoreEmptyWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_EditionsScalarLegacyRequiredIgnoreEmptyWithDefault_Val
}

type EditionsScalarLegacyRequiredIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val" json:"val,omitempty"`
}

func (x *EditionsScalarLegacyRequiredIgnoreDefault) Reset() {
	*x = EditionsScalarLegacyRequiredIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarLegacyRequiredIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarLegacyRequiredIgnoreDefault) ProtoMessage() {}

func (x *EditionsScalarLegacyRequiredIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarLegacyRequiredIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarLegacyRequiredIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{13}
}

func (x *EditionsScalarLegacyRequiredIgnoreDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return 0
}

type EditionsScalarLegacyRequiredIgnoreDefaultWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *int32 `protobuf:"varint,1,req,name=val,def=-42" json:"val,omitempty"`
}

// Default values for EditionsScalarLegacyRequiredIgnoreDefaultWithDefault fields.
const (
	Default_EditionsScalarLegacyRequiredIgnoreDefaultWithDefault_Val = int32(-42)
)

func (x *EditionsScalarLegacyRequiredIgnoreDefaultWithDefault) Reset() {
	*x = EditionsScalarLegacyRequiredIgnoreDefaultWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsScalarLegacyRequiredIgnoreDefaultWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsScalarLegacyRequiredIgnoreDefaultWithDefault) ProtoMessage() {}

func (x *EditionsScalarLegacyRequiredIgnoreDefaultWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsScalarLegacyRequiredIgnoreDefaultWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsScalarLegacyRequiredIgnoreDefaultWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{14}
}

func (x *EditionsScalarLegacyRequiredIgnoreDefaultWithDefault) GetVal() int32 {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return Default_EditionsScalarLegacyRequiredIgnoreDefaultWithDefault_Val
}

type EditionsMessageExplicitPresenceIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageExplicitPresenceIgnoreUnspecified_Msg `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceIgnoreUnspecified) Reset() {
	*x = EditionsMessageExplicitPresenceIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{15}
}

func (x *EditionsMessageExplicitPresenceIgnoreUnspecified) GetVal() *EditionsMessageExplicitPresenceIgnoreUnspecified_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg `protobuf:"group,1,opt,name=Msg,json=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified) Reset() {
	*x = EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{16}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified) GetVal() *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageExplicitPresenceIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageExplicitPresenceIgnoreEmpty_Msg `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceIgnoreEmpty) Reset() {
	*x = EditionsMessageExplicitPresenceIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceIgnoreEmpty) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{17}
}

func (x *EditionsMessageExplicitPresenceIgnoreEmpty) GetVal() *EditionsMessageExplicitPresenceIgnoreEmpty_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageExplicitPresenceDelimitedIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg `protobuf:"group,1,opt,name=Msg,json=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty) Reset() {
	*x = EditionsMessageExplicitPresenceDelimitedIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceDelimitedIgnoreEmpty) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceDelimitedIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceDelimitedIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{18}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty) GetVal() *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageExplicitPresenceIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageExplicitPresenceIgnoreDefault_Msg `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceIgnoreDefault) Reset() {
	*x = EditionsMessageExplicitPresenceIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceIgnoreDefault) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{19}
}

func (x *EditionsMessageExplicitPresenceIgnoreDefault) GetVal() *EditionsMessageExplicitPresenceIgnoreDefault_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageExplicitPresenceDelimitedIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg `protobuf:"group,1,opt,name=Msg,json=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreDefault) Reset() {
	*x = EditionsMessageExplicitPresenceDelimitedIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceDelimitedIgnoreDefault) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceDelimitedIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceDelimitedIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{20}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreDefault) GetVal() *EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageLegacyRequiredIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageLegacyRequiredIgnoreUnspecified_Msg `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredIgnoreUnspecified) Reset() {
	*x = EditionsMessageLegacyRequiredIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{21}
}

func (x *EditionsMessageLegacyRequiredIgnoreUnspecified) GetVal() *EditionsMessageLegacyRequiredIgnoreUnspecified_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg `protobuf:"group,1,req,name=Msg,json=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified) Reset() {
	*x = EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{22}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified) GetVal() *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageLegacyRequiredIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageLegacyRequiredIgnoreEmpty_Msg `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredIgnoreEmpty) Reset() {
	*x = EditionsMessageLegacyRequiredIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredIgnoreEmpty) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{23}
}

func (x *EditionsMessageLegacyRequiredIgnoreEmpty) GetVal() *EditionsMessageLegacyRequiredIgnoreEmpty_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageLegacyRequiredDelimitedIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg `protobuf:"group,1,req,name=Msg,json=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty) Reset() {
	*x = EditionsMessageLegacyRequiredDelimitedIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredDelimitedIgnoreEmpty) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredDelimitedIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredDelimitedIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{24}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty) GetVal() *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageLegacyRequiredIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageLegacyRequiredIgnoreDefault_Msg `protobuf:"bytes,1,req,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredIgnoreDefault) Reset() {
	*x = EditionsMessageLegacyRequiredIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredIgnoreDefault) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{25}
}

func (x *EditionsMessageLegacyRequiredIgnoreDefault) GetVal() *EditionsMessageLegacyRequiredIgnoreDefault_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageLegacyRequiredDelimitedIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg `protobuf:"group,1,req,name=Msg,json=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreDefault) Reset() {
	*x = EditionsMessageLegacyRequiredDelimitedIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredDelimitedIgnoreDefault) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredDelimitedIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredDelimitedIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{26}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreDefault) GetVal() *EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsOneofIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*EditionsOneofIgnoreUnspecified_Val
	O isEditionsOneofIgnoreUnspecified_O `protobuf_oneof:"o"`
}

func (x *EditionsOneofIgnoreUnspecified) Reset() {
	*x = EditionsOneofIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsOneofIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsOneofIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsOneofIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsOneofIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsOneofIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{27}
}

func (m *EditionsOneofIgnoreUnspecified) GetO() isEditionsOneofIgnoreUnspecified_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *EditionsOneofIgnoreUnspecified) GetVal() int32 {
	if x, ok := x.GetO().(*EditionsOneofIgnoreUnspecified_Val); ok {
		return x.Val
	}
	return 0
}

type isEditionsOneofIgnoreUnspecified_O interface {
	isEditionsOneofIgnoreUnspecified_O()
}

type EditionsOneofIgnoreUnspecified_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof"`
}

func (*EditionsOneofIgnoreUnspecified_Val) isEditionsOneofIgnoreUnspecified_O() {}

type EditionsOneofIgnoreUnspecifiedWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*EditionsOneofIgnoreUnspecifiedWithDefault_Val
	O isEditionsOneofIgnoreUnspecifiedWithDefault_O `protobuf_oneof:"o"`
}

// Default values for EditionsOneofIgnoreUnspecifiedWithDefault fields.
const (
	Default_EditionsOneofIgnoreUnspecifiedWithDefault_Val = int32(-42)
)

func (x *EditionsOneofIgnoreUnspecifiedWithDefault) Reset() {
	*x = EditionsOneofIgnoreUnspecifiedWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsOneofIgnoreUnspecifiedWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsOneofIgnoreUnspecifiedWithDefault) ProtoMessage() {}

func (x *EditionsOneofIgnoreUnspecifiedWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsOneofIgnoreUnspecifiedWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsOneofIgnoreUnspecifiedWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{28}
}

func (m *EditionsOneofIgnoreUnspecifiedWithDefault) GetO() isEditionsOneofIgnoreUnspecifiedWithDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *EditionsOneofIgnoreUnspecifiedWithDefault) GetVal() int32 {
	if x, ok := x.GetO().(*EditionsOneofIgnoreUnspecifiedWithDefault_Val); ok {
		return x.Val
	}
	return Default_EditionsOneofIgnoreUnspecifiedWithDefault_Val
}

type isEditionsOneofIgnoreUnspecifiedWithDefault_O interface {
	isEditionsOneofIgnoreUnspecifiedWithDefault_O()
}

type EditionsOneofIgnoreUnspecifiedWithDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof,def=-42"`
}

func (*EditionsOneofIgnoreUnspecifiedWithDefault_Val) isEditionsOneofIgnoreUnspecifiedWithDefault_O() {
}

type EditionsOneofIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*EditionsOneofIgnoreEmpty_Val
	O isEditionsOneofIgnoreEmpty_O `protobuf_oneof:"o"`
}

func (x *EditionsOneofIgnoreEmpty) Reset() {
	*x = EditionsOneofIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsOneofIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsOneofIgnoreEmpty) ProtoMessage() {}

func (x *EditionsOneofIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsOneofIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsOneofIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{29}
}

func (m *EditionsOneofIgnoreEmpty) GetO() isEditionsOneofIgnoreEmpty_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *EditionsOneofIgnoreEmpty) GetVal() int32 {
	if x, ok := x.GetO().(*EditionsOneofIgnoreEmpty_Val); ok {
		return x.Val
	}
	return 0
}

type isEditionsOneofIgnoreEmpty_O interface {
	isEditionsOneofIgnoreEmpty_O()
}

type EditionsOneofIgnoreEmpty_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof"`
}

func (*EditionsOneofIgnoreEmpty_Val) isEditionsOneofIgnoreEmpty_O() {}

type EditionsOneofIgnoreEmptyWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*EditionsOneofIgnoreEmptyWithDefault_Val
	O isEditionsOneofIgnoreEmptyWithDefault_O `protobuf_oneof:"o"`
}

// Default values for EditionsOneofIgnoreEmptyWithDefault fields.
const (
	Default_EditionsOneofIgnoreEmptyWithDefault_Val = int32(-42)
)

func (x *EditionsOneofIgnoreEmptyWithDefault) Reset() {
	*x = EditionsOneofIgnoreEmptyWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsOneofIgnoreEmptyWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsOneofIgnoreEmptyWithDefault) ProtoMessage() {}

func (x *EditionsOneofIgnoreEmptyWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsOneofIgnoreEmptyWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsOneofIgnoreEmptyWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{30}
}

func (m *EditionsOneofIgnoreEmptyWithDefault) GetO() isEditionsOneofIgnoreEmptyWithDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *EditionsOneofIgnoreEmptyWithDefault) GetVal() int32 {
	if x, ok := x.GetO().(*EditionsOneofIgnoreEmptyWithDefault_Val); ok {
		return x.Val
	}
	return Default_EditionsOneofIgnoreEmptyWithDefault_Val
}

type isEditionsOneofIgnoreEmptyWithDefault_O interface {
	isEditionsOneofIgnoreEmptyWithDefault_O()
}

type EditionsOneofIgnoreEmptyWithDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof,def=-42"`
}

func (*EditionsOneofIgnoreEmptyWithDefault_Val) isEditionsOneofIgnoreEmptyWithDefault_O() {}

type EditionsOneofIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*EditionsOneofIgnoreDefault_Val
	O isEditionsOneofIgnoreDefault_O `protobuf_oneof:"o"`
}

func (x *EditionsOneofIgnoreDefault) Reset() {
	*x = EditionsOneofIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsOneofIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsOneofIgnoreDefault) ProtoMessage() {}

func (x *EditionsOneofIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsOneofIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsOneofIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{31}
}

func (m *EditionsOneofIgnoreDefault) GetO() isEditionsOneofIgnoreDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *EditionsOneofIgnoreDefault) GetVal() int32 {
	if x, ok := x.GetO().(*EditionsOneofIgnoreDefault_Val); ok {
		return x.Val
	}
	return 0
}

type isEditionsOneofIgnoreDefault_O interface {
	isEditionsOneofIgnoreDefault_O()
}

type EditionsOneofIgnoreDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof"`
}

func (*EditionsOneofIgnoreDefault_Val) isEditionsOneofIgnoreDefault_O() {}

type EditionsOneofIgnoreDefaultWithDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to O:
	//
	//	*EditionsOneofIgnoreDefaultWithDefault_Val
	O isEditionsOneofIgnoreDefaultWithDefault_O `protobuf_oneof:"o"`
}

// Default values for EditionsOneofIgnoreDefaultWithDefault fields.
const (
	Default_EditionsOneofIgnoreDefaultWithDefault_Val = int32(-42)
)

func (x *EditionsOneofIgnoreDefaultWithDefault) Reset() {
	*x = EditionsOneofIgnoreDefaultWithDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsOneofIgnoreDefaultWithDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsOneofIgnoreDefaultWithDefault) ProtoMessage() {}

func (x *EditionsOneofIgnoreDefaultWithDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsOneofIgnoreDefaultWithDefault.ProtoReflect.Descriptor instead.
func (*EditionsOneofIgnoreDefaultWithDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{32}
}

func (m *EditionsOneofIgnoreDefaultWithDefault) GetO() isEditionsOneofIgnoreDefaultWithDefault_O {
	if m != nil {
		return m.O
	}
	return nil
}

func (x *EditionsOneofIgnoreDefaultWithDefault) GetVal() int32 {
	if x, ok := x.GetO().(*EditionsOneofIgnoreDefaultWithDefault_Val); ok {
		return x.Val
	}
	return Default_EditionsOneofIgnoreDefaultWithDefault_Val
}

type isEditionsOneofIgnoreDefaultWithDefault_O interface {
	isEditionsOneofIgnoreDefaultWithDefault_O()
}

type EditionsOneofIgnoreDefaultWithDefault_Val struct {
	Val int32 `protobuf:"varint,1,opt,name=val,oneof,def=-42"`
}

func (*EditionsOneofIgnoreDefaultWithDefault_Val) isEditionsOneofIgnoreDefaultWithDefault_O() {}

type EditionsRepeatedIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedIgnoreUnspecified) Reset() {
	*x = EditionsRepeatedIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsRepeatedIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{33}
}

func (x *EditionsRepeatedIgnoreUnspecified) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedExpandedIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedExpandedIgnoreUnspecified) Reset() {
	*x = EditionsRepeatedExpandedIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedExpandedIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedExpandedIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsRepeatedExpandedIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedExpandedIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedExpandedIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{34}
}

func (x *EditionsRepeatedExpandedIgnoreUnspecified) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedIgnoreEmpty) Reset() {
	*x = EditionsRepeatedIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedIgnoreEmpty) ProtoMessage() {}

func (x *EditionsRepeatedIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{35}
}

func (x *EditionsRepeatedIgnoreEmpty) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedExpandedIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedExpandedIgnoreEmpty) Reset() {
	*x = EditionsRepeatedExpandedIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedExpandedIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedExpandedIgnoreEmpty) ProtoMessage() {}

func (x *EditionsRepeatedExpandedIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedExpandedIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedExpandedIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{36}
}

func (x *EditionsRepeatedExpandedIgnoreEmpty) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedIgnoreDefault) Reset() {
	*x = EditionsRepeatedIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedIgnoreDefault) ProtoMessage() {}

func (x *EditionsRepeatedIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{37}
}

func (x *EditionsRepeatedIgnoreDefault) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedExpandedIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedExpandedIgnoreDefault) Reset() {
	*x = EditionsRepeatedExpandedIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedExpandedIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedExpandedIgnoreDefault) ProtoMessage() {}

func (x *EditionsRepeatedExpandedIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedExpandedIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedExpandedIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{38}
}

func (x *EditionsRepeatedExpandedIgnoreDefault) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapIgnoreUnspecified) Reset() {
	*x = EditionsMapIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsMapIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsMapIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{39}
}

func (x *EditionsMapIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapIgnoreEmpty) Reset() {
	*x = EditionsMapIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapIgnoreEmpty) ProtoMessage() {}

func (x *EditionsMapIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsMapIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{40}
}

func (x *EditionsMapIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapIgnoreDefault) Reset() {
	*x = EditionsMapIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapIgnoreDefault) ProtoMessage() {}

func (x *EditionsMapIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsMapIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{41}
}

func (x *EditionsMapIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedItemIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedItemIgnoreUnspecified) Reset() {
	*x = EditionsRepeatedItemIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedItemIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedItemIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsRepeatedItemIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedItemIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedItemIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{42}
}

func (x *EditionsRepeatedItemIgnoreUnspecified) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedExpandedItemIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedExpandedItemIgnoreUnspecified) Reset() {
	*x = EditionsRepeatedExpandedItemIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedExpandedItemIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedExpandedItemIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsRepeatedExpandedItemIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedExpandedItemIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedExpandedItemIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{43}
}

func (x *EditionsRepeatedExpandedItemIgnoreUnspecified) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedItemIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedItemIgnoreEmpty) Reset() {
	*x = EditionsRepeatedItemIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedItemIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedItemIgnoreEmpty) ProtoMessage() {}

func (x *EditionsRepeatedItemIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedItemIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedItemIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{44}
}

func (x *EditionsRepeatedItemIgnoreEmpty) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedExpandedItemIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedExpandedItemIgnoreEmpty) Reset() {
	*x = EditionsRepeatedExpandedItemIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedExpandedItemIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedExpandedItemIgnoreEmpty) ProtoMessage() {}

func (x *EditionsRepeatedExpandedItemIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedExpandedItemIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedExpandedItemIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{45}
}

func (x *EditionsRepeatedExpandedItemIgnoreEmpty) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedItemIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,packed,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedItemIgnoreDefault) Reset() {
	*x = EditionsRepeatedItemIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedItemIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedItemIgnoreDefault) ProtoMessage() {}

func (x *EditionsRepeatedItemIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedItemIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedItemIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{46}
}

func (x *EditionsRepeatedItemIgnoreDefault) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsRepeatedExpandedItemIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val []int32 `protobuf:"varint,1,rep,name=val" json:"val,omitempty"`
}

func (x *EditionsRepeatedExpandedItemIgnoreDefault) Reset() {
	*x = EditionsRepeatedExpandedItemIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsRepeatedExpandedItemIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsRepeatedExpandedItemIgnoreDefault) ProtoMessage() {}

func (x *EditionsRepeatedExpandedItemIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsRepeatedExpandedItemIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsRepeatedExpandedItemIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{47}
}

func (x *EditionsRepeatedExpandedItemIgnoreDefault) GetVal() []int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapKeyIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapKeyIgnoreUnspecified) Reset() {
	*x = EditionsMapKeyIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapKeyIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapKeyIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsMapKeyIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapKeyIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsMapKeyIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{48}
}

func (x *EditionsMapKeyIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapKeyIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapKeyIgnoreEmpty) Reset() {
	*x = EditionsMapKeyIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapKeyIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapKeyIgnoreEmpty) ProtoMessage() {}

func (x *EditionsMapKeyIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapKeyIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsMapKeyIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{49}
}

func (x *EditionsMapKeyIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapKeyIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapKeyIgnoreDefault) Reset() {
	*x = EditionsMapKeyIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapKeyIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapKeyIgnoreDefault) ProtoMessage() {}

func (x *EditionsMapKeyIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapKeyIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsMapKeyIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{50}
}

func (x *EditionsMapKeyIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapValueIgnoreUnspecified struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapValueIgnoreUnspecified) Reset() {
	*x = EditionsMapValueIgnoreUnspecified{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapValueIgnoreUnspecified) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapValueIgnoreUnspecified) ProtoMessage() {}

func (x *EditionsMapValueIgnoreUnspecified) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapValueIgnoreUnspecified.ProtoReflect.Descriptor instead.
func (*EditionsMapValueIgnoreUnspecified) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{51}
}

func (x *EditionsMapValueIgnoreUnspecified) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapValueIgnoreEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapValueIgnoreEmpty) Reset() {
	*x = EditionsMapValueIgnoreEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapValueIgnoreEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapValueIgnoreEmpty) ProtoMessage() {}

func (x *EditionsMapValueIgnoreEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapValueIgnoreEmpty.ProtoReflect.Descriptor instead.
func (*EditionsMapValueIgnoreEmpty) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{52}
}

func (x *EditionsMapValueIgnoreEmpty) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMapValueIgnoreDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val map[int32]int32 `protobuf:"bytes,1,rep,name=val" json:"val,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (x *EditionsMapValueIgnoreDefault) Reset() {
	*x = EditionsMapValueIgnoreDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMapValueIgnoreDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMapValueIgnoreDefault) ProtoMessage() {}

func (x *EditionsMapValueIgnoreDefault) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMapValueIgnoreDefault.ProtoReflect.Descriptor instead.
func (*EditionsMapValueIgnoreDefault) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{53}
}

func (x *EditionsMapValueIgnoreDefault) GetVal() map[int32]int32 {
	if x != nil {
		return x.Val
	}
	return nil
}

type EditionsMessageExplicitPresenceIgnoreUnspecified_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceIgnoreUnspecified_Msg) Reset() {
	*x = EditionsMessageExplicitPresenceIgnoreUnspecified_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceIgnoreUnspecified_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceIgnoreUnspecified_Msg) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceIgnoreUnspecified_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceIgnoreUnspecified_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceIgnoreUnspecified_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{15, 0}
}

func (x *EditionsMessageExplicitPresenceIgnoreUnspecified_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg) Reset() {
	*x = EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{16, 0}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageExplicitPresenceIgnoreEmpty_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceIgnoreEmpty_Msg) Reset() {
	*x = EditionsMessageExplicitPresenceIgnoreEmpty_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceIgnoreEmpty_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceIgnoreEmpty_Msg) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceIgnoreEmpty_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceIgnoreEmpty_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceIgnoreEmpty_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{17, 0}
}

func (x *EditionsMessageExplicitPresenceIgnoreEmpty_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg) Reset() {
	*x = EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{18, 0}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageExplicitPresenceIgnoreDefault_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceIgnoreDefault_Msg) Reset() {
	*x = EditionsMessageExplicitPresenceIgnoreDefault_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceIgnoreDefault_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceIgnoreDefault_Msg) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceIgnoreDefault_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceIgnoreDefault_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceIgnoreDefault_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{19, 0}
}

func (x *EditionsMessageExplicitPresenceIgnoreDefault_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg) Reset() {
	*x = EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg) ProtoMessage() {}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{20, 0}
}

func (x *EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageLegacyRequiredIgnoreUnspecified_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredIgnoreUnspecified_Msg) Reset() {
	*x = EditionsMessageLegacyRequiredIgnoreUnspecified_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredIgnoreUnspecified_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredIgnoreUnspecified_Msg) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredIgnoreUnspecified_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredIgnoreUnspecified_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredIgnoreUnspecified_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{21, 0}
}

func (x *EditionsMessageLegacyRequiredIgnoreUnspecified_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg) Reset() {
	*x = EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{22, 0}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageLegacyRequiredIgnoreEmpty_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredIgnoreEmpty_Msg) Reset() {
	*x = EditionsMessageLegacyRequiredIgnoreEmpty_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredIgnoreEmpty_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredIgnoreEmpty_Msg) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredIgnoreEmpty_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredIgnoreEmpty_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredIgnoreEmpty_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{23, 0}
}

func (x *EditionsMessageLegacyRequiredIgnoreEmpty_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg) Reset() {
	*x = EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{24, 0}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageLegacyRequiredIgnoreDefault_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredIgnoreDefault_Msg) Reset() {
	*x = EditionsMessageLegacyRequiredIgnoreDefault_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredIgnoreDefault_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredIgnoreDefault_Msg) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredIgnoreDefault_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredIgnoreDefault_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredIgnoreDefault_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{25, 0}
}

func (x *EditionsMessageLegacyRequiredIgnoreDefault_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val *string `protobuf:"bytes,1,opt,name=val" json:"val,omitempty"`
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg) Reset() {
	*x = EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg) ProtoMessage() {}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg) ProtoReflect() protoreflect.Message {
	mi := &file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg.ProtoReflect.Descriptor instead.
func (*EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg) Descriptor() ([]byte, []int) {
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP(), []int{26, 0}
}

func (x *EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

var File_buf_validate_conformance_cases_ignore_proto_editions_proto protoreflect.FileDescriptor

var file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x5f, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x1b, 0x62, 0x75,
	0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4c, 0x0a, 0x2f, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x69,
	0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5c, 0x0a, 0x3a, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69,
	0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x49, 0x0a, 0x29, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0x59, 0x0a, 0x34, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c,
	0x61, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x57, 0x69, 0x74,
	0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8,
	0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4b, 0x0a, 0x2b, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45, 0x78, 0x70,
	0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a,
	0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5b, 0x0a, 0x36, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63,
	0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a,
	0x03, 0x2d, 0x34, 0x32, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x51, 0x0a, 0x2f, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x49, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0c, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01,
	0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4e, 0x0a, 0x29, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x49, 0x6d, 0x70, 0x6c, 0x69, 0x63,
	0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01,
	0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x50, 0x0a, 0x2b, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x49, 0x6d, 0x70, 0x6c, 0x69, 0x63,
	0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00,
	0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4f, 0x0a, 0x2d, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4c, 0x65, 0x67, 0x61,
	0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0c, 0xba, 0x48, 0x04, 0x1a, 0x02, 0x20,
	0x00, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5f, 0x0a, 0x38, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4c, 0x65, 0x67,
	0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x23, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x0c, 0xba, 0x48, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4c, 0x0a, 0x27,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4c, 0x65,
	0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00,
	0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5c, 0x0a, 0x32, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4c, 0x65, 0x67, 0x61,
	0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x12, 0x26, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d,
	0x34, 0x32, 0x42, 0x0f, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4e, 0x0a, 0x29, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x5e, 0x0a, 0x34, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x12, 0x26, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d,
	0x34, 0x32, 0x42, 0x0f, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xfc, 0x01, 0x0a, 0x30, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0xae, 0x01,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x4d, 0x73,
	0x67, 0x42, 0x46, 0xba, 0x48, 0x43, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06,
	0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c,
	0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17,
	0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x93, 0x02, 0x0a, 0x39, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69,
	0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0xbc, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x5d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x4d,
	0x73, 0x67, 0x42, 0x4b, 0xba, 0x48, 0x43, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61,
	0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xf3, 0x01,
	0x0a, 0x2a, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0xab, 0x01, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69,
	0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x49, 0xba, 0x48, 0x46, 0xba,
	0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a,
	0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f,
	0x6f, 0x27, 0xd8, 0x01, 0x01, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73,
	0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x8a, 0x02, 0x0a, 0x33, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0xb9, 0x01, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x57, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63,
	0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x4d,
	0x73, 0x67, 0x42, 0x4e, 0xba, 0x48, 0x46, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61,
	0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x01, 0xaa, 0x01, 0x02,
	0x28, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0xf7, 0x01, 0x0a, 0x2c, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x63, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x12, 0xad, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x50, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x4d, 0x73,
	0x67, 0x42, 0x49, 0xba, 0x48, 0x46, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06,
	0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c,
	0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x02, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x8e, 0x02, 0x0a, 0x35, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78,
	0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x12, 0xbb, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x59, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x4e, 0xba,
	0x48, 0x46, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62,
	0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20,
	0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x02, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xfd, 0x01, 0x0a, 0x2e,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c,
	0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0xb1,
	0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67,
	0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x4d, 0x73, 0x67,
	0x42, 0x4b, 0xba, 0x48, 0x43, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66,
	0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20,
	0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x91, 0x02, 0x0a, 0x37,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c,
	0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0xbc, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x4d,
	0x73, 0x67, 0x42, 0x4d, 0xba, 0x48, 0x43, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61,
	0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xaa, 0x01, 0x04, 0x08, 0x03, 0x28,
	0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0xf4, 0x01, 0x0a, 0x28, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0xae, 0x01, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x4e, 0xba, 0x48, 0x46, 0xba, 0x01, 0x40,
	0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74,
	0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27,
	0xd8, 0x01, 0x01, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a,
	0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x88, 0x02, 0x0a, 0x31, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65,
	0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0xb9, 0x01, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x62, 0x75, 0x66,
	0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x4d, 0x73,
	0x67, 0x42, 0x50, 0xba, 0x48, 0x46, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06,
	0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c,
	0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x01, 0xaa, 0x01, 0x04, 0x08,
	0x03, 0x28, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x22, 0xf8, 0x01, 0x0a, 0x2a, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x12, 0xb0, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c,
	0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x4e,
	0xba, 0x48, 0x46, 0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f,
	0x62, 0x61, 0x72, 0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d,
	0x20, 0x27, 0x66, 0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x02, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x8c, 0x02, 0x0a,
	0x33, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x12, 0xbb, 0x01, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x57, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x44, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x42, 0x50, 0xba, 0x48, 0x46,
	0xba, 0x01, 0x40, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x69, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x06, 0x66, 0x6f, 0x6f, 0x62, 0x61, 0x72,
	0x1a, 0x11, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x76, 0x61, 0x6c, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x66,
	0x6f, 0x6f, 0x27, 0xd8, 0x01, 0x02, 0xaa, 0x01, 0x04, 0x08, 0x03, 0x28, 0x02, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x1a, 0x17, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x42, 0x0a, 0x1e, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1b, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xba, 0x48, 0x04, 0x1a,
	0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22,
	0x52, 0x0a, 0x29, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4f, 0x6e, 0x65, 0x6f, 0x66,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x20, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x07,
	0xba, 0x48, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03,
	0x0a, 0x01, 0x6f, 0x22, 0x3f, 0x0a, 0x18, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4f,
	0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xba, 0x48,
	0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42,
	0x03, 0x0a, 0x01, 0x6f, 0x22, 0x4f, 0x0a, 0x23, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x23, 0x0a, 0x03, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x2d, 0x34, 0x32, 0x42, 0x0a, 0xba,
	0x48, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x41, 0x0a, 0x1a, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x51, 0x0a, 0x25, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x12, 0x23, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03,
	0x2d, 0x34, 0x32, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x42, 0x03, 0x0a, 0x01, 0x6f, 0x22, 0x3f, 0x0a, 0x21, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x12, 0x1a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x08, 0xba,
	0x48, 0x05, 0x92, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x4c, 0x0a, 0x29,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x45, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0d, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02, 0x08, 0x03,
	0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x3c, 0x0a, 0x1b, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x92, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x49, 0x0a, 0x23, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x70, 0x61,
	0x6e, 0x64, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x22, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x10, 0xba, 0x48,
	0x08, 0xd8, 0x01, 0x01, 0x92, 0x01, 0x02, 0x08, 0x03, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x3e, 0x0a, 0x1d, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x05, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x02, 0x92, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x4b, 0x0a, 0x25, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x22, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x10, 0xba, 0x48, 0x08, 0xd8, 0x01,
	0x02, 0x92, 0x01, 0x02, 0x08, 0x03, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0xb9, 0x01, 0x0a, 0x1c, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x12, 0x61, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45,
	0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x56, 0x61, 0x6c,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x08, 0xba, 0x48, 0x05, 0x9a, 0x01, 0x02, 0x08, 0x03, 0x52,
	0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb0, 0x01, 0x0a,
	0x16, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61,
	0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x9a, 0x01, 0x02,
	0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xb4, 0x01, 0x0a, 0x18, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x60, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0b, 0xba, 0x48,
	0x08, 0xd8, 0x01, 0x02, 0x9a, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36,
	0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x47, 0x0a, 0x25, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12,
	0x1e, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0c, 0xba, 0x48,
	0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x54, 0x0a, 0x2d, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x45, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x12, 0x23, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x11, 0xba,
	0x48, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01, 0x02, 0x18, 0x02,
	0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x44, 0x0a, 0x1f, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0xd8,
	0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x51, 0x0a, 0x27, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45,
	0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x05, 0x42, 0x14, 0xba, 0x48, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x01,
	0x1a, 0x02, 0x20, 0x00, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x46,
	0x0a, 0x21, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05,
	0x42, 0x0f, 0xba, 0x48, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0x53, 0x0a, 0x29, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x70, 0x61, 0x6e, 0x64,
	0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x12, 0x26, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05,
	0x42, 0x14, 0xba, 0x48, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02, 0x20,
	0x00, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22, 0xc3, 0x01, 0x0a, 0x1f,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12,
	0x68, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x56, 0x61,
	0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xba, 0x48, 0x09, 0x9a, 0x01, 0x06, 0x22, 0x04,
	0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xba, 0x01, 0x0a, 0x19, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61,
	0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x65, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x62,
	0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x42, 0x0f, 0xba, 0x48, 0x0c, 0x9a, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20,
	0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbe,
	0x01, 0x0a, 0x1b, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x4b, 0x65,
	0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x67,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x62, 0x75,
	0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x4b, 0x65, 0x79, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x9a, 0x01, 0x09, 0x22, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xc7, 0x01, 0x0a, 0x21, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x6a, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c,
	0xba, 0x48, 0x09, 0x9a, 0x01, 0x06, 0x2a, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76, 0x61,
	0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbe, 0x01, 0x0a, 0x1b, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x67, 0x0a, 0x03, 0x76, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0f, 0xba, 0x48,
	0x0c, 0x9a, 0x01, 0x09, 0x2a, 0x07, 0xd8, 0x01, 0x01, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x03, 0x76,
	0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc2, 0x01, 0x0a, 0x1d, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x69, 0x0a, 0x03,
	0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x62, 0x75, 0x66, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x42, 0x0f, 0xba, 0x48, 0x0c, 0x9a, 0x01, 0x09, 0x2a, 0x07, 0xd8, 0x01, 0x02, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x1a, 0x36, 0x0a, 0x08, 0x56, 0x61, 0x6c, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42,
	0xaf, 0x02, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x75, 0x66, 0x2e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x42, 0x18, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x50, 0x01, 0x5a, 0x53, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62,
	0x75, 0x66, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0xa2, 0x02, 0x04, 0x42, 0x56, 0x43, 0x43, 0xaa, 0x02,
	0x1e, 0x42, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x73, 0xca,
	0x02, 0x1e, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c, 0x43,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65, 0x73,
	0xe2, 0x02, 0x2a, 0x42, 0x75, 0x66, 0x5c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5c,
	0x43, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5c, 0x43, 0x61, 0x73, 0x65,
	0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x21,
	0x42, 0x75, 0x66, 0x3a, 0x3a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x3a, 0x43,
	0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x3a, 0x3a, 0x43, 0x61, 0x73, 0x65,
	0x73, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescOnce sync.Once
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescData = file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDesc
)

func file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescGZIP() []byte {
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescOnce.Do(func() {
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescData = protoimpl.X.CompressGZIP(file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescData)
	})
	return file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDescData
}

var file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes = make([]protoimpl.MessageInfo, 75)
var file_buf_validate_conformance_cases_ignore_proto_editions_proto_goTypes = []any{
	(*EditionsScalarExplicitPresenceIgnoreUnspecified)(nil),               // 0: buf.validate.conformance.cases.EditionsScalarExplicitPresenceIgnoreUnspecified
	(*EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault)(nil),    // 1: buf.validate.conformance.cases.EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault
	(*EditionsScalarExplicitPresenceIgnoreEmpty)(nil),                     // 2: buf.validate.conformance.cases.EditionsScalarExplicitPresenceIgnoreEmpty
	(*EditionsScalarExplicitPresenceIgnoreEmptyWithDefault)(nil),          // 3: buf.validate.conformance.cases.EditionsScalarExplicitPresenceIgnoreEmptyWithDefault
	(*EditionsScalarExplicitPresenceIgnoreDefault)(nil),                   // 4: buf.validate.conformance.cases.EditionsScalarExplicitPresenceIgnoreDefault
	(*EditionsScalarExplicitPresenceIgnoreDefaultWithDefault)(nil),        // 5: buf.validate.conformance.cases.EditionsScalarExplicitPresenceIgnoreDefaultWithDefault
	(*EditionsScalarImplicitPresenceIgnoreUnspecified)(nil),               // 6: buf.validate.conformance.cases.EditionsScalarImplicitPresenceIgnoreUnspecified
	(*EditionsScalarImplicitPresenceIgnoreEmpty)(nil),                     // 7: buf.validate.conformance.cases.EditionsScalarImplicitPresenceIgnoreEmpty
	(*EditionsScalarImplicitPresenceIgnoreDefault)(nil),                   // 8: buf.validate.conformance.cases.EditionsScalarImplicitPresenceIgnoreDefault
	(*EditionsScalarLegacyRequiredIgnoreUnspecified)(nil),                 // 9: buf.validate.conformance.cases.EditionsScalarLegacyRequiredIgnoreUnspecified
	(*EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault)(nil),      // 10: buf.validate.conformance.cases.EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault
	(*EditionsScalarLegacyRequiredIgnoreEmpty)(nil),                       // 11: buf.validate.conformance.cases.EditionsScalarLegacyRequiredIgnoreEmpty
	(*EditionsScalarLegacyRequiredIgnoreEmptyWithDefault)(nil),            // 12: buf.validate.conformance.cases.EditionsScalarLegacyRequiredIgnoreEmptyWithDefault
	(*EditionsScalarLegacyRequiredIgnoreDefault)(nil),                     // 13: buf.validate.conformance.cases.EditionsScalarLegacyRequiredIgnoreDefault
	(*EditionsScalarLegacyRequiredIgnoreDefaultWithDefault)(nil),          // 14: buf.validate.conformance.cases.EditionsScalarLegacyRequiredIgnoreDefaultWithDefault
	(*EditionsMessageExplicitPresenceIgnoreUnspecified)(nil),              // 15: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreUnspecified
	(*EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified)(nil),     // 16: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified
	(*EditionsMessageExplicitPresenceIgnoreEmpty)(nil),                    // 17: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreEmpty
	(*EditionsMessageExplicitPresenceDelimitedIgnoreEmpty)(nil),           // 18: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreEmpty
	(*EditionsMessageExplicitPresenceIgnoreDefault)(nil),                  // 19: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreDefault
	(*EditionsMessageExplicitPresenceDelimitedIgnoreDefault)(nil),         // 20: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreDefault
	(*EditionsMessageLegacyRequiredIgnoreUnspecified)(nil),                // 21: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreUnspecified
	(*EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified)(nil),       // 22: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified
	(*EditionsMessageLegacyRequiredIgnoreEmpty)(nil),                      // 23: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreEmpty
	(*EditionsMessageLegacyRequiredDelimitedIgnoreEmpty)(nil),             // 24: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreEmpty
	(*EditionsMessageLegacyRequiredIgnoreDefault)(nil),                    // 25: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreDefault
	(*EditionsMessageLegacyRequiredDelimitedIgnoreDefault)(nil),           // 26: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreDefault
	(*EditionsOneofIgnoreUnspecified)(nil),                                // 27: buf.validate.conformance.cases.EditionsOneofIgnoreUnspecified
	(*EditionsOneofIgnoreUnspecifiedWithDefault)(nil),                     // 28: buf.validate.conformance.cases.EditionsOneofIgnoreUnspecifiedWithDefault
	(*EditionsOneofIgnoreEmpty)(nil),                                      // 29: buf.validate.conformance.cases.EditionsOneofIgnoreEmpty
	(*EditionsOneofIgnoreEmptyWithDefault)(nil),                           // 30: buf.validate.conformance.cases.EditionsOneofIgnoreEmptyWithDefault
	(*EditionsOneofIgnoreDefault)(nil),                                    // 31: buf.validate.conformance.cases.EditionsOneofIgnoreDefault
	(*EditionsOneofIgnoreDefaultWithDefault)(nil),                         // 32: buf.validate.conformance.cases.EditionsOneofIgnoreDefaultWithDefault
	(*EditionsRepeatedIgnoreUnspecified)(nil),                             // 33: buf.validate.conformance.cases.EditionsRepeatedIgnoreUnspecified
	(*EditionsRepeatedExpandedIgnoreUnspecified)(nil),                     // 34: buf.validate.conformance.cases.EditionsRepeatedExpandedIgnoreUnspecified
	(*EditionsRepeatedIgnoreEmpty)(nil),                                   // 35: buf.validate.conformance.cases.EditionsRepeatedIgnoreEmpty
	(*EditionsRepeatedExpandedIgnoreEmpty)(nil),                           // 36: buf.validate.conformance.cases.EditionsRepeatedExpandedIgnoreEmpty
	(*EditionsRepeatedIgnoreDefault)(nil),                                 // 37: buf.validate.conformance.cases.EditionsRepeatedIgnoreDefault
	(*EditionsRepeatedExpandedIgnoreDefault)(nil),                         // 38: buf.validate.conformance.cases.EditionsRepeatedExpandedIgnoreDefault
	(*EditionsMapIgnoreUnspecified)(nil),                                  // 39: buf.validate.conformance.cases.EditionsMapIgnoreUnspecified
	(*EditionsMapIgnoreEmpty)(nil),                                        // 40: buf.validate.conformance.cases.EditionsMapIgnoreEmpty
	(*EditionsMapIgnoreDefault)(nil),                                      // 41: buf.validate.conformance.cases.EditionsMapIgnoreDefault
	(*EditionsRepeatedItemIgnoreUnspecified)(nil),                         // 42: buf.validate.conformance.cases.EditionsRepeatedItemIgnoreUnspecified
	(*EditionsRepeatedExpandedItemIgnoreUnspecified)(nil),                 // 43: buf.validate.conformance.cases.EditionsRepeatedExpandedItemIgnoreUnspecified
	(*EditionsRepeatedItemIgnoreEmpty)(nil),                               // 44: buf.validate.conformance.cases.EditionsRepeatedItemIgnoreEmpty
	(*EditionsRepeatedExpandedItemIgnoreEmpty)(nil),                       // 45: buf.validate.conformance.cases.EditionsRepeatedExpandedItemIgnoreEmpty
	(*EditionsRepeatedItemIgnoreDefault)(nil),                             // 46: buf.validate.conformance.cases.EditionsRepeatedItemIgnoreDefault
	(*EditionsRepeatedExpandedItemIgnoreDefault)(nil),                     // 47: buf.validate.conformance.cases.EditionsRepeatedExpandedItemIgnoreDefault
	(*EditionsMapKeyIgnoreUnspecified)(nil),                               // 48: buf.validate.conformance.cases.EditionsMapKeyIgnoreUnspecified
	(*EditionsMapKeyIgnoreEmpty)(nil),                                     // 49: buf.validate.conformance.cases.EditionsMapKeyIgnoreEmpty
	(*EditionsMapKeyIgnoreDefault)(nil),                                   // 50: buf.validate.conformance.cases.EditionsMapKeyIgnoreDefault
	(*EditionsMapValueIgnoreUnspecified)(nil),                             // 51: buf.validate.conformance.cases.EditionsMapValueIgnoreUnspecified
	(*EditionsMapValueIgnoreEmpty)(nil),                                   // 52: buf.validate.conformance.cases.EditionsMapValueIgnoreEmpty
	(*EditionsMapValueIgnoreDefault)(nil),                                 // 53: buf.validate.conformance.cases.EditionsMapValueIgnoreDefault
	(*EditionsMessageExplicitPresenceIgnoreUnspecified_Msg)(nil),          // 54: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreUnspecified.Msg
	(*EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg)(nil), // 55: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified.Msg
	(*EditionsMessageExplicitPresenceIgnoreEmpty_Msg)(nil),                // 56: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreEmpty.Msg
	(*EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg)(nil),       // 57: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreEmpty.Msg
	(*EditionsMessageExplicitPresenceIgnoreDefault_Msg)(nil),              // 58: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreDefault.Msg
	(*EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg)(nil),     // 59: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreDefault.Msg
	(*EditionsMessageLegacyRequiredIgnoreUnspecified_Msg)(nil),            // 60: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreUnspecified.Msg
	(*EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg)(nil),   // 61: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified.Msg
	(*EditionsMessageLegacyRequiredIgnoreEmpty_Msg)(nil),                  // 62: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreEmpty.Msg
	(*EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg)(nil),         // 63: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreEmpty.Msg
	(*EditionsMessageLegacyRequiredIgnoreDefault_Msg)(nil),                // 64: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreDefault.Msg
	(*EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg)(nil),       // 65: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreDefault.Msg
	nil, // 66: buf.validate.conformance.cases.EditionsMapIgnoreUnspecified.ValEntry
	nil, // 67: buf.validate.conformance.cases.EditionsMapIgnoreEmpty.ValEntry
	nil, // 68: buf.validate.conformance.cases.EditionsMapIgnoreDefault.ValEntry
	nil, // 69: buf.validate.conformance.cases.EditionsMapKeyIgnoreUnspecified.ValEntry
	nil, // 70: buf.validate.conformance.cases.EditionsMapKeyIgnoreEmpty.ValEntry
	nil, // 71: buf.validate.conformance.cases.EditionsMapKeyIgnoreDefault.ValEntry
	nil, // 72: buf.validate.conformance.cases.EditionsMapValueIgnoreUnspecified.ValEntry
	nil, // 73: buf.validate.conformance.cases.EditionsMapValueIgnoreEmpty.ValEntry
	nil, // 74: buf.validate.conformance.cases.EditionsMapValueIgnoreDefault.ValEntry
}
var file_buf_validate_conformance_cases_ignore_proto_editions_proto_depIdxs = []int32{
	54, // 0: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreUnspecified.Msg
	55, // 1: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified.Msg
	56, // 2: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreEmpty.Msg
	57, // 3: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreEmpty.Msg
	58, // 4: buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreDefault.val:type_name -> buf.validate.conformance.cases.EditionsMessageExplicitPresenceIgnoreDefault.Msg
	59, // 5: buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreDefault.val:type_name -> buf.validate.conformance.cases.EditionsMessageExplicitPresenceDelimitedIgnoreDefault.Msg
	60, // 6: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreUnspecified.Msg
	61, // 7: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified.Msg
	62, // 8: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreEmpty.Msg
	63, // 9: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreEmpty.Msg
	64, // 10: buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreDefault.val:type_name -> buf.validate.conformance.cases.EditionsMessageLegacyRequiredIgnoreDefault.Msg
	65, // 11: buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreDefault.val:type_name -> buf.validate.conformance.cases.EditionsMessageLegacyRequiredDelimitedIgnoreDefault.Msg
	66, // 12: buf.validate.conformance.cases.EditionsMapIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.EditionsMapIgnoreUnspecified.ValEntry
	67, // 13: buf.validate.conformance.cases.EditionsMapIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.EditionsMapIgnoreEmpty.ValEntry
	68, // 14: buf.validate.conformance.cases.EditionsMapIgnoreDefault.val:type_name -> buf.validate.conformance.cases.EditionsMapIgnoreDefault.ValEntry
	69, // 15: buf.validate.conformance.cases.EditionsMapKeyIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.EditionsMapKeyIgnoreUnspecified.ValEntry
	70, // 16: buf.validate.conformance.cases.EditionsMapKeyIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.EditionsMapKeyIgnoreEmpty.ValEntry
	71, // 17: buf.validate.conformance.cases.EditionsMapKeyIgnoreDefault.val:type_name -> buf.validate.conformance.cases.EditionsMapKeyIgnoreDefault.ValEntry
	72, // 18: buf.validate.conformance.cases.EditionsMapValueIgnoreUnspecified.val:type_name -> buf.validate.conformance.cases.EditionsMapValueIgnoreUnspecified.ValEntry
	73, // 19: buf.validate.conformance.cases.EditionsMapValueIgnoreEmpty.val:type_name -> buf.validate.conformance.cases.EditionsMapValueIgnoreEmpty.ValEntry
	74, // 20: buf.validate.conformance.cases.EditionsMapValueIgnoreDefault.val:type_name -> buf.validate.conformance.cases.EditionsMapValueIgnoreDefault.ValEntry
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_buf_validate_conformance_cases_ignore_proto_editions_proto_init() }
func file_buf_validate_conformance_cases_ignore_proto_editions_proto_init() {
	if File_buf_validate_conformance_cases_ignore_proto_editions_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarExplicitPresenceIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarExplicitPresenceIgnoreUnspecifiedWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarExplicitPresenceIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarExplicitPresenceIgnoreEmptyWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarExplicitPresenceIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarExplicitPresenceIgnoreDefaultWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarImplicitPresenceIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarImplicitPresenceIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarImplicitPresenceIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarLegacyRequiredIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarLegacyRequiredIgnoreUnspecifiedWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarLegacyRequiredIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarLegacyRequiredIgnoreEmptyWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarLegacyRequiredIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsScalarLegacyRequiredIgnoreDefaultWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceDelimitedIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceDelimitedIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredDelimitedIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredDelimitedIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsOneofIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsOneofIgnoreUnspecifiedWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsOneofIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsOneofIgnoreEmptyWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsOneofIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsOneofIgnoreDefaultWithDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedExpandedIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedExpandedIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedExpandedIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedItemIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedExpandedItemIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedItemIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedExpandedItemIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedItemIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsRepeatedExpandedItemIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapKeyIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapKeyIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapKeyIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapValueIgnoreUnspecified); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[52].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapValueIgnoreEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[53].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMapValueIgnoreDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[54].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceIgnoreUnspecified_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[55].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceDelimitedIgnoreUnspecified_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[56].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceIgnoreEmpty_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[57].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceDelimitedIgnoreEmpty_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[58].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceIgnoreDefault_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[59].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageExplicitPresenceDelimitedIgnoreDefault_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[60].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredIgnoreUnspecified_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[61].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredDelimitedIgnoreUnspecified_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[62].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredIgnoreEmpty_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[63].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredDelimitedIgnoreEmpty_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[64].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredIgnoreDefault_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[65].Exporter = func(v any, i int) any {
			switch v := v.(*EditionsMessageLegacyRequiredDelimitedIgnoreDefault_Msg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[27].OneofWrappers = []any{
		(*EditionsOneofIgnoreUnspecified_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[28].OneofWrappers = []any{
		(*EditionsOneofIgnoreUnspecifiedWithDefault_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[29].OneofWrappers = []any{
		(*EditionsOneofIgnoreEmpty_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[30].OneofWrappers = []any{
		(*EditionsOneofIgnoreEmptyWithDefault_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[31].OneofWrappers = []any{
		(*EditionsOneofIgnoreDefault_Val)(nil),
	}
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes[32].OneofWrappers = []any{
		(*EditionsOneofIgnoreDefaultWithDefault_Val)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   75,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_buf_validate_conformance_cases_ignore_proto_editions_proto_goTypes,
		DependencyIndexes: file_buf_validate_conformance_cases_ignore_proto_editions_proto_depIdxs,
		MessageInfos:      file_buf_validate_conformance_cases_ignore_proto_editions_proto_msgTypes,
	}.Build()
	File_buf_validate_conformance_cases_ignore_proto_editions_proto = out.File
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_rawDesc = nil
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_goTypes = nil
	file_buf_validate_conformance_cases_ignore_proto_editions_proto_depIdxs = nil
}
