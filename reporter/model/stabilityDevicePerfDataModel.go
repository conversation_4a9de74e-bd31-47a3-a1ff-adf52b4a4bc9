package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ StabilityDevicePerfDataModel = (*customStabilityDevicePerfDataModel)(nil)

	stabilityDevicePerfDataInsertFields = stringx.Remove(stabilityDevicePerfDataFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// StabilityDevicePerfDataModel is an interface to be customized, add more methods here,
	// and implement the added methods in customStabilityDevicePerfDataModel.
	StabilityDevicePerfDataModel interface {
		stabilityDevicePerfDataModel
		types.DBModel

		withSession(session sqlx.Session) StabilityDevicePerfDataModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *StabilityDevicePerfData) squirrel.InsertBuilder
		UpdateBuilder(data *StabilityDevicePerfData) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityDevicePerfData, error)

		FindByTaskID(ctx context.Context, taskID, projectID, dataType string) ([]*StabilityDevicePerfData, error)
		FindByUDID(ctx context.Context, taskID, projectID, udid, dataType string) ([]*StabilityDevicePerfData, error)

		FindOneByX(
			ctx context.Context, taskID, executeID, projectID, udid, dataType, series, x string,
		) (*StabilityDevicePerfData, error)
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID string) error
	}

	customStabilityDevicePerfDataModel struct {
		*defaultStabilityDevicePerfDataModel

		conn sqlx.SqlConn
	}
)

// NewStabilityDevicePerfDataModel returns a model for the database table.
func NewStabilityDevicePerfDataModel(conn sqlx.SqlConn) StabilityDevicePerfDataModel {
	return &customStabilityDevicePerfDataModel{
		defaultStabilityDevicePerfDataModel: newStabilityDevicePerfDataModel(conn),
		conn:                                conn,
	}
}

func (m *customStabilityDevicePerfDataModel) withSession(session sqlx.Session) StabilityDevicePerfDataModel {
	return NewStabilityDevicePerfDataModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customStabilityDevicePerfDataModel) Table() string {
	return m.table
}

func (m *customStabilityDevicePerfDataModel) Fields() []string {
	return stabilityDevicePerfDataFieldNames
}

func (m *customStabilityDevicePerfDataModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customStabilityDevicePerfDataModel) InsertBuilder(data *StabilityDevicePerfData) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(stabilityDevicePerfDataInsertFields...).Values()
}

func (m *customStabilityDevicePerfDataModel) UpdateBuilder(data *StabilityDevicePerfData) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customStabilityDevicePerfDataModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(stabilityDevicePerfDataFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDevicePerfDataModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDevicePerfDataModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customStabilityDevicePerfDataModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityDevicePerfData, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*StabilityDevicePerfData
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customStabilityDevicePerfDataModel) FindByTaskID(
	ctx context.Context, taskID, projectID, dataType string,
) ([]*StabilityDevicePerfData, error) {
	sb := m.SelectBuilder().Where(
		"`task_id` = ? AND `project_id` = ? AND `data_type` = ?",
		taskID, projectID, dataType,
	).OrderBy("`udid`", "`series`", "`x`")

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customStabilityDevicePerfDataModel) FindByUDID(
	ctx context.Context, taskID, projectID, udid, dataType string,
) ([]*StabilityDevicePerfData, error) {
	sb := m.SelectBuilder().Where(
		"`task_id` = ? AND `project_id` = ? AND `udid` = ? AND `data_type` = ?",
		taskID, projectID, udid, dataType,
	).OrderBy("`series`", "`x`")

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customStabilityDevicePerfDataModel) FindOneByX(
	ctx context.Context, taskID, executeID, projectID, udid, dataType, series, x string,
) (*StabilityDevicePerfData, error) {
	r, err := m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().
			Where(
				"`task_id` = ? AND `execute_id` = ? AND `project_id` = ? AND `udid` = ? AND `data_type` = ? AND `series` = ? AND `x` = ?",
				taskID, executeID, projectID, udid, dataType, series, x,
			),
	)
	if err != nil {
		return nil, err
	} else if len(r) == 0 {
		return nil, ErrNotFound
	}
	return r[0], nil
}

func (m *customStabilityDevicePerfDataModel) DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID string) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ?", taskID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}
