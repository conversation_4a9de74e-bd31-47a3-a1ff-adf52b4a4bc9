package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ StabilityDeviceActivityRecordModel = (*customStabilityDeviceActivityRecordModel)(nil)

	stabilityDeviceActivityRecordInsertFields = stringx.Remove(
		stabilityDeviceActivityRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// StabilityDeviceActivityRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customStabilityDeviceActivityRecordModel.
	StabilityDeviceActivityRecordModel interface {
		stabilityDeviceActivityRecordModel
		types.DBModel

		withSession(session sqlx.Session) StabilityDeviceActivityRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *StabilityDeviceActivityRecord) squirrel.InsertBuilder
		UpdateBuilder(data *StabilityDeviceActivityRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*StabilityDeviceActivityRecord, error,
		)

		FindByProjectIDTaskIDUdid(
			ctx context.Context, projectID, taskID, udid string,
		) ([]*StabilityDeviceActivityRecord, error)
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID string) error
	}

	customStabilityDeviceActivityRecordModel struct {
		*defaultStabilityDeviceActivityRecordModel

		conn sqlx.SqlConn
	}
)

// NewStabilityDeviceActivityRecordModel returns a model for the database table.
func NewStabilityDeviceActivityRecordModel(conn sqlx.SqlConn) StabilityDeviceActivityRecordModel {
	return &customStabilityDeviceActivityRecordModel{
		defaultStabilityDeviceActivityRecordModel: newStabilityDeviceActivityRecordModel(conn),
		conn: conn,
	}
}

func (m *customStabilityDeviceActivityRecordModel) withSession(session sqlx.Session) StabilityDeviceActivityRecordModel {
	return NewStabilityDeviceActivityRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customStabilityDeviceActivityRecordModel) Table() string {
	return m.table
}

func (m *customStabilityDeviceActivityRecordModel) Fields() []string {
	return stabilityDeviceActivityRecordFieldNames
}

func (m *customStabilityDeviceActivityRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customStabilityDeviceActivityRecordModel) InsertBuilder(data *StabilityDeviceActivityRecord) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(stabilityDeviceActivityRecordInsertFields...).Values()
}

func (m *customStabilityDeviceActivityRecordModel) UpdateBuilder(data *StabilityDeviceActivityRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customStabilityDeviceActivityRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(stabilityDeviceActivityRecordFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customStabilityDeviceActivityRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDeviceActivityRecordModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customStabilityDeviceActivityRecordModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*StabilityDeviceActivityRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*StabilityDeviceActivityRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customStabilityDeviceActivityRecordModel) FindByProjectIDTaskIDUdid(
	ctx context.Context, projectID, taskID, udid string,
) ([]*StabilityDeviceActivityRecord, error) {
	sb := m.SelectBuilder().
		Where("`project_id` = ? AND `task_id` = ? AND `udid` = ?", projectID, taskID, udid).
		OrderBy("`covered` DESC", "`name` ASC")

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customStabilityDeviceActivityRecordModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ?", taskID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}
