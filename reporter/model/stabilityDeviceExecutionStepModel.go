package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ StabilityDeviceExecutionStepModel = (*customStabilityDeviceExecutionStepModel)(nil)

	stabilityDeviceExecutionStepInsertFields = stringx.Remove(stabilityDeviceExecutionStepFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// StabilityDeviceExecutionStepModel is an interface to be customized, add more methods here,
	// and implement the added methods in customStabilityDeviceExecutionStepModel.
	StabilityDeviceExecutionStepModel interface {
		stabilityDeviceExecutionStepModel
		types.DBModel

		withSession(session sqlx.Session) StabilityDeviceExecutionStepModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *StabilityDeviceExecutionStep) squirrel.InsertBuilder
		UpdateBuilder(data *StabilityDeviceExecutionStep) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityDeviceExecutionStep, error)

		FindByProjectIDTaskIDUdid(
			ctx context.Context, projectID, taskID, udid string,
		) ([]*StabilityDeviceExecutionStep, error)
		FindByStageIndex(
			ctx context.Context, taskID, executeID, projectID string, stage, index int64,
		) (*StabilityDeviceExecutionStep, error)
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID string) error
	}

	customStabilityDeviceExecutionStepModel struct {
		*defaultStabilityDeviceExecutionStepModel

		conn sqlx.SqlConn
	}
)

// NewStabilityDeviceExecutionStepModel returns a model for the database table.
func NewStabilityDeviceExecutionStepModel(conn sqlx.SqlConn) StabilityDeviceExecutionStepModel {
	return &customStabilityDeviceExecutionStepModel{
		defaultStabilityDeviceExecutionStepModel: newStabilityDeviceExecutionStepModel(conn),
		conn:                                     conn,
	}
}

func (m *customStabilityDeviceExecutionStepModel) withSession(session sqlx.Session) StabilityDeviceExecutionStepModel {
	return NewStabilityDeviceExecutionStepModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customStabilityDeviceExecutionStepModel) Table() string {
	return m.table
}

func (m *customStabilityDeviceExecutionStepModel) Fields() []string {
	return stabilityDeviceExecutionStepFieldNames
}

func (m *customStabilityDeviceExecutionStepModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customStabilityDeviceExecutionStepModel) InsertBuilder(data *StabilityDeviceExecutionStep) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(stabilityDeviceExecutionStepInsertFields...).Values()
}

func (m *customStabilityDeviceExecutionStepModel) UpdateBuilder(data *StabilityDeviceExecutionStep) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customStabilityDeviceExecutionStepModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(stabilityDeviceExecutionStepFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDeviceExecutionStepModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDeviceExecutionStepModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customStabilityDeviceExecutionStepModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityDeviceExecutionStep, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*StabilityDeviceExecutionStep
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customStabilityDeviceExecutionStepModel) FindByProjectIDTaskIDUdid(
	ctx context.Context, projectID, taskID, udid string,
) ([]*StabilityDeviceExecutionStep, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `task_id` = ? AND `udid` = ?", projectID, taskID, udid,
	).OrderBy("`stage` ASC", "`index` ASC")
	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customStabilityDeviceExecutionStepModel) FindByStageIndex(
	ctx context.Context, taskID, executeID, projectID string, stage, index int64,
) (*StabilityDeviceExecutionStep, error) {
	r, err := m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().
			Where(
				"`task_id` = ? AND `execute_id` = ? AND `project_id` = ? AND `stage` = ? AND `index` = ?",
				taskID, executeID, projectID, stage, index,
			),
	)
	if err != nil {
		return nil, err
	} else if len(r) == 0 {
		return nil, ErrNotFound
	}
	return r[0], nil
}

func (m *customStabilityDeviceExecutionStepModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ?", taskID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}
