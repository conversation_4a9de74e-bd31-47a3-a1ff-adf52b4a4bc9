package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ UiDevicePerfDataModel = (*customUiDevicePerfDataModel)(nil)

	uiDevicePerfDataInsertFields = stringx.Remove(
		uiDevicePerfDataFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// UiDevicePerfDataModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiDevicePerfDataModel.
	UiDevicePerfDataModel interface {
		uiDevicePerfDataModel
		types.DBModel

		withSession(session sqlx.Session) UiDevicePerfDataModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiDevicePerfData) squirrel.InsertBuilder
		UpdateBuilder(data *UiDevicePerfData) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*UiDevicePerfData, error)

		FindByTaskID(ctx context.Context, taskID, projectID, dataType string) ([]*UiDevicePerfData, error)
		FindByUDID(ctx context.Context, taskID, projectID, udid, dataType string) ([]*UiDevicePerfData, error)
		FindOneByX(
			ctx context.Context, taskID, executeID, projectID, udid, dataType, series, x string,
		) (*UiDevicePerfData, error)
		DeleteByTaskID(ctx context.Context, session sqlx.Session, taskID string) error
	}

	customUiDevicePerfDataModel struct {
		*defaultUiDevicePerfDataModel

		conn sqlx.SqlConn
	}
)

// NewUiDevicePerfDataModel returns a model for the database table.
func NewUiDevicePerfDataModel(conn sqlx.SqlConn) UiDevicePerfDataModel {
	return &customUiDevicePerfDataModel{
		defaultUiDevicePerfDataModel: newUiDevicePerfDataModel(conn),
		conn:                         conn,
	}
}

func (m *customUiDevicePerfDataModel) withSession(session sqlx.Session) UiDevicePerfDataModel {
	return NewUiDevicePerfDataModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customUiDevicePerfDataModel) Table() string {
	return m.table
}

func (m *customUiDevicePerfDataModel) Fields() []string {
	return uiDevicePerfDataFieldNames
}

func (m *customUiDevicePerfDataModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customUiDevicePerfDataModel) InsertBuilder(data *UiDevicePerfData) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiDevicePerfDataInsertFields...).Values(
		data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.DataType, data.Interval, data.Series, data.Unit,
		data.X, data.Y, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customUiDevicePerfDataModel) UpdateBuilder(data *UiDevicePerfData) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`interval`":   data.Interval,
		"`unit`":       data.Unit,
		"`y`":          data.Y,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiDevicePerfDataModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiDevicePerfDataFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiDevicePerfDataModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiDevicePerfDataModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiDevicePerfDataModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiDevicePerfData, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiDevicePerfData
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiDevicePerfDataModel) FindByTaskID(
	ctx context.Context, taskID, projectID, dataType string,
) ([]*UiDevicePerfData, error) {
	return m.FindNoCacheByQuery(
		ctx,
		m.SelectBuilder().
			Where("`task_id` = ? AND `project_id` = ? AND `data_type` = ?", taskID, projectID, dataType).
			OrderBy("`udid`", "`series`", "`x`"),
	)
}

func (m *customUiDevicePerfDataModel) FindByUDID(
	ctx context.Context, taskID, projectID, udid, dataType string,
) ([]*UiDevicePerfData, error) {
	return m.FindNoCacheByQuery(
		ctx,
		m.SelectBuilder().
			Where(
				"`task_id` = ? AND `project_id` = ? AND `udid` = ? AND `data_type` = ?",
				taskID, projectID, udid, dataType,
			).
			OrderBy("`series`", "`x`"),
	)
}

func (m *customUiDevicePerfDataModel) FindOneByX(
	ctx context.Context, taskID, executeID, projectID, udid, dataType, series, x string,
) (*UiDevicePerfData, error) {
	r, err := m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().
			Where(
				"`task_id` = ? AND `execute_id` = ? AND `project_id` = ? AND `udid` = ? AND `data_type` = ? AND `series` = ? AND `x` = ?",
				taskID, executeID, projectID, udid, dataType, series, x,
			),
	)
	if err != nil {
		return nil, err
	} else if len(r) == 0 {
		return nil, ErrNotFound
	}
	return r[0], nil
}

func (m *customUiDevicePerfDataModel) DeleteByTaskID(ctx context.Context, session sqlx.Session, taskID string) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ?", taskID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}
