package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_                                   UiCaseExecutionRecordModel = (*customUiCaseExecutionRecordModel)(nil)
	VirtualUIDeviceExecutionRecordModel types.DBModel              = (*virtualUIDeviceExecutionRecordModel)(nil)

	uiCaseExecutionRecordFields = stringx.Remove(
		uiCaseExecutionRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
	uiCaseExecutionRecordRowsExpectAutoSet2 = strings.Join(
		stringx.Remove(
			uiCaseExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), ",",
	)
	uiCaseExecutionRecordRowsWithPlaceHolder2 = strings.Join(
		stringx.Remove(
			uiCaseExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), "=?,",
	) + "=?"

	virtualUIDeviceExecutionRecordFieldNames = builder.RawFieldNames(&SearchUIDeviceExecutionRecordItem{})
)

type (
	// UiCaseExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiCaseExecutionRecordModel.
	UiCaseExecutionRecordModel interface {
		uiCaseExecutionRecordModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiCaseExecutionRecord) squirrel.InsertBuilder
		UpdateBuilder(data *UiCaseExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectBuilderWithEmptyContent() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*UiCaseExecutionRecord, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *UiCaseExecutionRecord) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *UiCaseExecutionRecord) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error

		InsertRecordWithDefaultCreatedAt(ctx context.Context, data *UiCaseExecutionRecord) (sql.Result, error)
		UpdateRecordWithDefaultUpdatedAt(ctx context.Context, data *UiCaseExecutionRecord) error
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID, projectID string) error

		FindOneByTaskIDExecuteIDProjectID(
			ctx context.Context, taskID, executeID, projectID string,
		) (*UiCaseExecutionRecord, error)

		FindCountUICaseExecutionRecords(
			ctx context.Context, req SearchUICaseExecutionRecordReq,
		) (int64, error)
		FindUICaseExecutionRecords(
			ctx context.Context, req SearchUICaseExecutionRecordReq,
		) ([]*SearchUICaseExecutionRecordItem, error)

		FindCountUIDeviceExecutionRecords(ctx context.Context, req SearchUIDeviceExecutionRecordReq) (int64, error)
		FindUIDeviceExecutionRecords(
			ctx context.Context, req SearchUIDeviceExecutionRecordReq,
		) ([]*SearchUIDeviceExecutionRecordItem, error)
	}

	customUiCaseExecutionRecordModel struct {
		*defaultUiCaseExecutionRecordModel
	}
)

// NewUiCaseExecutionRecordModel returns a model for the database table.
func NewUiCaseExecutionRecordModel(conn sqlx.SqlConn) UiCaseExecutionRecordModel {
	return &customUiCaseExecutionRecordModel{
		defaultUiCaseExecutionRecordModel: newUiCaseExecutionRecordModel(conn),
	}
}

func (m *customUiCaseExecutionRecordModel) Table() string {
	return m.table
}

func (m *customUiCaseExecutionRecordModel) Fields() []string {
	return uiCaseExecutionRecordFieldNames
}

func (m *customUiCaseExecutionRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customUiCaseExecutionRecordModel) InsertBuilder(data *UiCaseExecutionRecord) squirrel.InsertBuilder {
	if data.CreatedAt == zeroTime {
		return squirrel.Insert(m.table).Columns(uiCaseExecutionRecordFields...).Values(
			data.TaskId, data.ProjectId, data.ExecuteId, data.CaseId, data.CaseName, data.SuiteExecuteId, data.Udid,
			data.Status, data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback,
			data.Cleaned,
		)
	} else {
		return squirrel.Insert(m.table).Columns(uiCaseExecutionRecordFields...).Columns("`created_at`").Values(
			data.TaskId, data.ProjectId, data.ExecuteId, data.CaseId, data.CaseName, data.SuiteExecuteId, data.Udid,
			data.Status, data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback,
			data.CreatedAt, data.Cleaned,
		)
	}
}

func (m *customUiCaseExecutionRecordModel) UpdateBuilder(data *UiCaseExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`case_name`":   data.CaseName,
		"`udid`":        data.Udid,
		"`status`":      data.Status,
		"`content`":     data.Content,
		"`executed_by`": data.ExecutedBy,
		"`started_at`":  data.StartedAt,
		"`ended_at`":    data.EndedAt,
		"`cost_time`":   data.CostTime,
		"`callback`":    data.Callback,
		"`cleaned`":     data.Cleaned,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiCaseExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiCaseExecutionRecordFieldNames...).From(m.table)
}

func (m *customUiCaseExecutionRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	fieldNames := stringx.Remove(uiCaseExecutionRecordFieldNames, "`content`")
	fieldNames = append(fieldNames, "'' as content")
	return squirrel.Select(fieldNames...).From(m.table)
}

func (m *customUiCaseExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *customUiCaseExecutionRecordModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *UiCaseExecutionRecord,
) (sql.Result, error) {
	query, values, err := m.InsertBuilder(data).ToSql()
	if err != nil {
		return nil, err
	}

	if session != nil {
		return session.ExecCtx(ctx, query, values...)
	}

	return m.conn.ExecCtx(ctx, query, values...)
}

func (m *customUiCaseExecutionRecordModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *UiCaseExecutionRecord,
) (sql.Result, error) {
	query, values, err := m.UpdateBuilder(newData).ToSql()
	if err != nil {
		return nil, err
	}

	if session != nil {
		return session.ExecCtx(ctx, query, values...)
	}

	return m.conn.ExecCtx(ctx, query, values...)
}

func (m *customUiCaseExecutionRecordModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}

	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *customUiCaseExecutionRecordModel) InsertRecordWithDefaultCreatedAt(
	ctx context.Context, data *UiCaseExecutionRecord,
) (sql.Result, error) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, uiCaseExecutionRecordRowsExpectAutoSet2,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.CaseId, data.CaseName,
		data.SuiteExecuteId, data.Status, data.Content, data.ExecutedBy, data.StartedAt,
		data.EndedAt, data.CostTime, data.Callback, data.Cleaned,
	)
	return ret, err
}

func (m *customUiCaseExecutionRecordModel) UpdateRecordWithDefaultUpdatedAt(
	ctx context.Context, data *UiCaseExecutionRecord,
) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiCaseExecutionRecordRowsWithPlaceHolder2)
	_, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.CaseId, data.CaseName,
		data.SuiteExecuteId, data.Status, data.Content, data.ExecutedBy, data.StartedAt,
		data.EndedAt, data.CostTime, data.Callback, data.Cleaned,
		data.Id,
	)
	return err
}

func (m *customUiCaseExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiCaseExecutionRecordModel) FindByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*UiCaseExecutionRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiCaseExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiCaseExecutionRecordModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}

func (m *customUiCaseExecutionRecordModel) FindOneByTaskIDExecuteIDProjectID(
	ctx context.Context, taskID, executeID, projectID string,
) (*UiCaseExecutionRecord, error) {
	sb := m.SelectBuilder().Where(
		"`task_id` = ? AND `execute_id` = ? AND `project_id` = ?", taskID, executeID, projectID,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

type SearchUICaseExecutionRecordItem struct {
	UiCaseExecutionRecord
}

func (m *customUiCaseExecutionRecordModel) FindCountUICaseExecutionRecords(
	ctx context.Context, req SearchUICaseExecutionRecordReq,
) (int64, error) {
	/*
		SQL:
		SELECT COUNT(*)
		FROM `ui_case_execution_record`
		WHERE `task_id` = ?
		  AND `suite_execute_id` = ?
		  AND `project_id` = ?
	*/

	sb := squirrel.Select("COUNT(*)").
		From(m.table).
		Where("`task_id` = ? AND `suite_execute_id` = ? AND `project_id` = ?", req.TaskID, req.ExecuteID, req.ProjectID)
	if req.UDID != "" {
		sb = sb.Where("`udid` = ?", req.UDID)
	}
	sb = sqlbuilder.SearchOptions(sb, sqlbuilder.WithCondition(m, req.Condition))

	return m.FindCount(ctx, sb)
}

func (m *customUiCaseExecutionRecordModel) FindUICaseExecutionRecords(
	ctx context.Context, req SearchUICaseExecutionRecordReq,
) ([]*SearchUICaseExecutionRecordItem, error) {
	/*
		SQL:
		SELECT *
		FROM `ui_case_execution_record`
		WHERE `task_id` = ?
		  AND `suite_execute_id` = ?
		  AND `project_id` = ?
	*/

	sb := squirrel.Select(uiCaseExecutionRecordFieldNames...).
		From(m.table).
		Where("`task_id` = ? AND `suite_execute_id` = ? AND `project_id` = ?", req.TaskID, req.ExecuteID, req.ProjectID)
	if req.UDID != "" {
		sb = sb.Where("`udid` = ?", req.UDID)
	}
	sb = sqlbuilder.SearchOptions(
		sb,
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)

	var (
		resp []*SearchUICaseExecutionRecordItem
		err  error
	)

	err = utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

type (
	virtualUIDeviceExecutionRecordModel struct{}

	SearchUIDeviceExecutionRecordItem struct {
		TaskId        string         `db:"task_id"`         // 任务ID
		PlanExecuteId string         `db:"plan_execute_id"` // UI计划执行ID
		ProjectId     string         `db:"project_id"`      // 项目ID
		Udid          string         `db:"udid"`            // 设备编号
		TotalCase     int64          `db:"total_case"`      // 集合总用例个数
		SuccessCase   int64          `db:"success_case"`    // 执行成功用例个数
		FailureCase   int64          `db:"failure_case"`    // 执行失败用例个数
		Status        sql.NullString `db:"status"`          // 执行状态（结果）
		ExecutedBy    string         `db:"executed_by"`     // 执行人
		StartedAt     int64          `db:"started_at"`      // 开始执行的时间(戳)
		EndedAt       sql.NullInt64  `db:"ended_at"`        // 结束执行的时间(戳)
		CostTime      int64          `db:"cost_time"`       // 执行耗时(毫秒)
	}
)

func (m *virtualUIDeviceExecutionRecordModel) Table() string {
	return "virtual_ui_device_execution_record"
}

func (m *virtualUIDeviceExecutionRecordModel) Fields() []string {
	return virtualUIDeviceExecutionRecordFieldNames
}

func (m *customUiCaseExecutionRecordModel) FindCountUIDeviceExecutionRecords(
	ctx context.Context, req SearchUIDeviceExecutionRecordReq,
) (int64, error) {
	/*
		SQL:
		SELECT COUNT(*)
		FROM (SELECT t.`task_id`,
		             t.`plan_execute_id`,
		             t.`project_id`,
		             t.`udid`,
		             IF(t.`total_case` = t.`success_case`, 'Success', 'Failure') AS `status`,
		             t.`total_case`,
		             t.`success_case`,
		             t.`failure_case`,
		             t.`started_at`,
		             t.`ended_at`,
		             t.`ended_at` - t.`started_at`                               AS `cost_time`,
		             t.`executed_by`
		      FROM (SELECT t1.`task_id`,
		                   t1.`execute_id`                         AS `plan_execute_id`,
		                   t1.`project_id`,
		                   t2.`udid`,
		                   COUNT(t2.`id`)                          AS `total_case`,
		                   SUM(IF(t2.`status` = 'Success', 1, 0))  AS `success_case`,
		                   SUM(IF(t2.`status` != 'Success', 1, 0)) AS `failure_case`,
		                   MIN(t2.`started_at`)                    AS `started_at`,
		                   MAX(t2.`ended_at`)                      AS `ended_at`,
		                   t1.`executed_by`
		            FROM `ui_plan_execution_record` AS t1
		                INNER JOIN `ui_case_execution_record` AS t2
		                    ON t1.`task_id` = t2.`task_id` AND
		                       t1.`project_id` = t2.`project_id`
		            WHERE t1.`task_id` = ?
		              AND t1.`project_id` = ?
		              AND t1.`execute_id` = ?
		            GROUP BY t2.`udid`) AS t) AS t;
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1"
		aliasT2 = "t2"

		fields = []string{
			aliasT + ".`task_id`",
			aliasT + ".`plan_execute_id`",
			aliasT + ".`project_id`",
			aliasT + ".`udid`",
			fmt.Sprintf(
				"IF(%s.`total_case` = %s.`success_case`, '%s', '%s') AS `status`",
				aliasT, aliasT, common.Success, common.Failure,
			),
			aliasT + ".`total_case`",
			aliasT + ".`success_case`",
			aliasT + ".`failure_case`",
			aliasT + ".`started_at`",
			aliasT + ".`ended_at`",
			fmt.Sprintf("%s.`ended_at` - %s.`started_at` AS `cost_time`", aliasT, aliasT),
			aliasT + ".`executed_by`",
		}
		subFields = []string{
			aliasT1 + ".`task_id`",
			aliasT1 + ".`execute_id` AS `plan_execute_id`",
			aliasT1 + ".`project_id`",
			aliasT2 + ".`udid`",
			fmt.Sprintf("COUNT(%s.`id`) AS `total_case`", aliasT2),
			fmt.Sprintf("SUM(IF(%s.`status` = '%s', 1, 0)) AS `success_case`", aliasT2, common.Success),
			fmt.Sprintf("SUM(IF(%s.`status` != '%s', 1, 0)) AS `failure_case`", aliasT2, common.Success),
			fmt.Sprintf("MIN(%s.`started_at`) AS `started_at`", aliasT2),
			fmt.Sprintf("MAX(%s.`ended_at`) AS `ended_at`", aliasT2),
			aliasT1 + ".`executed_by`",
		}

		tmp = VirtualUIDeviceExecutionRecordModel
	)

	sb := squirrel.Select(fields...).
		FromSelect(
			squirrel.Select(subFields...).
				From(uiPlanExecutionRecordTableName+" AS "+aliasT1).
				InnerJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`task_id` = %s.`task_id` AND %s.`project_id` = %s.`project_id`",
						m.table, aliasT2, aliasT1, aliasT2, aliasT1, aliasT2,
					),
				).
				Where(
					fmt.Sprintf(
						"%s.`task_id` = ? AND %s.`project_id` = ? AND %s.`execute_id` = ?",
						aliasT1, aliasT1, aliasT1,
					),
					req.TaskID, req.ProjectID, req.ExecuteID,
				).GroupBy(aliasT2+".`udid`"), aliasT,
		)

	sb = squirrel.Select("COUNT(*)").
		FromSelect(sqlbuilder.SearchOptionsWithAlias(sb, aliasT, sqlbuilder.WithCondition(tmp, req.Condition)), aliasT)

	return m.FindCount(ctx, sb)
}

func (m *customUiCaseExecutionRecordModel) FindUIDeviceExecutionRecords(
	ctx context.Context, req SearchUIDeviceExecutionRecordReq,
) ([]*SearchUIDeviceExecutionRecordItem, error) {
	/*
		SQL:
		SELECT t.`task_id`,
		       t.`plan_execute_id`,
		       t.`project_id`,
		       t.`udid`,
		       IF(t.`total_case` = t.`success_case`, 'Success', 'Failure') AS `status`,
		       t.`total_case`,
		       t.`success_case`,
		       t.`failure_case`,
		       t.`started_at`,
		       t.`ended_at`,
		       IFNULL(t.`ended_at` - t.`started_at`, 0)                    AS `cost_time`,
		       t.`executed_by`
		FROM (SELECT t1.`task_id`,
		             t1.`execute_id`                         AS `plan_execute_id`,
		             t1.`project_id`,
		             t2.`udid`,
		             COUNT(t2.`id`)                          AS `total_case`,
		             SUM(IF(t2.`status` = 'Success', 1, 0))  AS `success_case`,
		             SUM(IF(t2.`status` != 'Success', 1, 0)) AS `failure_case`,
		             MIN(t2.`started_at`)                    AS `started_at`,
		             MAX(t2.`ended_at`)                      AS `ended_at`,
		             t1.`executed_by`
		      FROM `ui_plan_execution_record` AS t1
		          INNER JOIN `ui_case_execution_record` AS t2
		              ON t1.`task_id` = t2.`task_id` AND
		                 t1.`project_id` = t2.`project_id`
		      WHERE t1.`task_id` = ?
		        AND t1.`project_id` = ?
		        AND t1.`execute_id` = ?
		      GROUP BY t2.`udid`) AS t
		ORDER BY `status` DESC, `udid`;
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1"
		aliasT2 = "t2"

		fields = []string{
			aliasT + ".`task_id`",
			aliasT + ".`plan_execute_id`",
			aliasT + ".`project_id`",
			aliasT + ".`udid`",
			fmt.Sprintf(
				"IF(%s.`total_case` = %s.`success_case`, '%s', '%s') AS `status`",
				aliasT, aliasT, common.Success, common.Failure,
			),
			aliasT + ".`total_case`",
			aliasT + ".`success_case`",
			aliasT + ".`failure_case`",
			aliasT + ".`started_at`",
			aliasT + ".`ended_at`",
			fmt.Sprintf("IFNULL(%s.`ended_at` - %s.`started_at`, 0) AS `cost_time`", aliasT, aliasT),
			aliasT + ".`executed_by`",
		}
		subFields = []string{
			aliasT1 + ".`task_id`",
			aliasT1 + ".`execute_id` AS `plan_execute_id`",
			aliasT1 + ".`project_id`",
			aliasT2 + ".`udid`",
			fmt.Sprintf("COUNT(%s.`id`) AS `total_case`", aliasT2),
			fmt.Sprintf("SUM(IF(%s.`status` = '%s', 1, 0)) AS `success_case`", aliasT2, common.Success),
			fmt.Sprintf("SUM(IF(%s.`status` != '%s', 1, 0)) AS `failure_case`", aliasT2, common.Success),
			fmt.Sprintf("MIN(%s.`started_at`) AS `started_at`", aliasT2),
			fmt.Sprintf("MAX(%s.`ended_at`) AS `ended_at`", aliasT2),
			aliasT1 + ".`executed_by`",
		}

		tmp = VirtualUIDeviceExecutionRecordModel
	)

	sb := squirrel.Select(fields...).
		FromSelect(
			squirrel.Select(subFields...).
				From(uiPlanExecutionRecordTableName+" AS "+aliasT1).
				InnerJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`task_id` = %s.`task_id` AND %s.`project_id` = %s.`project_id`",
						m.table, aliasT2, aliasT1, aliasT2, aliasT1, aliasT2,
					),
				).
				Where(
					fmt.Sprintf(
						"%s.`task_id` = ? AND %s.`project_id` = ? AND %s.`execute_id` = ?",
						aliasT1, aliasT1, aliasT1,
					),
					req.TaskID, req.ProjectID, req.ExecuteID,
				).GroupBy(aliasT2+".`udid`"), aliasT,
		).OrderBy("`status` DESC", "`udid`")

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT,
		sqlbuilder.WithCondition(tmp, req.Condition),
		sqlbuilder.WithPagination(tmp, req.Pagination),
		sqlbuilder.WithSort(tmp, req.Sort),
	)

	var (
		resp []*SearchUIDeviceExecutionRecordItem
		err  error
	)
	err = utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}
