package model

import (
	"context"
	"database/sql"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ PerfCaseExecutionRecordModel = (*customPerfCaseExecutionRecordModel)(nil)

	perfCaseExecutionRecordInsertFields = stringx.Remove(
		perfCaseExecutionRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfCaseExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfCaseExecutionRecordModel.
	PerfCaseExecutionRecordModel interface {
		perfCaseExecutionRecordModel
		types.DBModel

		withSession(session sqlx.Session) PerfCaseExecutionRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfCaseExecutionRecord) squirrel.InsertBuilder
		UpdateBuilder(data *PerfCaseExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*PerfCaseExecutionRecord, error,
		)

		FindCountByReq(ctx context.Context, req SearchPerfCaseExecutionRecordReq) (int64, error)
		FindAllByReq(ctx context.Context, req SearchPerfCaseExecutionRecordReq) (
			[]*SearchPerfCaseExecutionRecordItem, error,
		)

		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID, projectID string) error
	}

	customPerfCaseExecutionRecordModel struct {
		*defaultPerfCaseExecutionRecordModel

		conn sqlx.SqlConn
	}
)

// NewPerfCaseExecutionRecordModel returns a model for the database table.
func NewPerfCaseExecutionRecordModel(conn sqlx.SqlConn) PerfCaseExecutionRecordModel {
	return &customPerfCaseExecutionRecordModel{
		defaultPerfCaseExecutionRecordModel: newPerfCaseExecutionRecordModel(conn),
		conn:                                conn,
	}
}

func (m *customPerfCaseExecutionRecordModel) withSession(session sqlx.Session) PerfCaseExecutionRecordModel {
	return NewPerfCaseExecutionRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customPerfCaseExecutionRecordModel) Table() string {
	return m.table
}

func (m *customPerfCaseExecutionRecordModel) Fields() []string {
	return perfCaseExecutionRecordFieldNames
}

func (m *customPerfCaseExecutionRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customPerfCaseExecutionRecordModel) InsertBuilder(data *PerfCaseExecutionRecord) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfCaseExecutionRecordInsertFields...).Values(
		data.TaskId, data.ExecuteId, data.SuiteExecuteId, data.ProjectId, data.CaseId, data.CaseName, data.Steps,
		data.PerfData, data.LoadGenerator, data.Status, data.CostTime, data.ExecutedBy, data.StartedAt, data.EndedAt,
		data.ApiMetrics, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfCaseExecutionRecordModel) UpdateBuilder(data *PerfCaseExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`status`":      data.Status,
		"`cost_time`":   data.CostTime,
		"`ended_at`":    data.EndedAt,
		"`api_metrics`": data.ApiMetrics,
		"`err_msg`":     data.ErrMsg,
		"`cleaned`":     data.Cleaned,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfCaseExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfCaseExecutionRecordFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPerfCaseExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfCaseExecutionRecordModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfCaseExecutionRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfCaseExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

type SearchPerfCaseExecutionRecordItem struct {
	TaskId         string         `db:"task_id"`          // 任务ID
	ExecuteId      string         `db:"execute_id"`       // 执行ID
	SuiteExecuteId string         `db:"suite_execute_id"` // 集合执行ID
	PlanExecuteId  string         `db:"plan_execute_id"`  // 计划执行ID
	ProjectId      string         `db:"project_id"`       // 项目ID
	PlanId         string         `db:"plan_id"`          // 计划ID
	PlanName       string         `db:"plan_name"`        // 计划名称
	SuiteId        string         `db:"suite_id"`         // 集合ID
	SuiteName      string         `db:"suite_name"`       // 集合名称
	CaseId         string         `db:"case_id"`          // 用例ID
	CaseName       string         `db:"case_name"`        // 用例名称
	Steps          sql.NullString `db:"steps"`            // 用例步骤
	PerfData       sql.NullString `db:"perf_data"`        // 压测数据
	LoadGenerator  sql.NullString `db:"load_generator"`   // 施压机资源
	Status         sql.NullString `db:"status"`           // 执行状态（结果）
	CostTime       int64          `db:"cost_time"`        // 执行耗时（单位为毫秒）
	ExecutedBy     string         `db:"executed_by"`      // 执行者的用户ID
	StartedAt      sql.NullTime   `db:"started_at"`       // 开始时间
	EndedAt        sql.NullTime   `db:"ended_at"`         // 结束时间
	ApiMetrics     sql.NullString `db:"api_metrics"`      // 用例涉及的接口的指标信息
	ErrMsg         sql.NullString `db:"err_msg"`          // 用例执行错误信息
	CreatedBy      string         `db:"created_by"`       // 创建者的用户ID
	UpdatedBy      string         `db:"updated_by"`       // 最近一次更新者的用户ID
	CreatedAt      time.Time      `db:"created_at"`       // 创建时间
	UpdatedAt      time.Time      `db:"updated_at"`       // 更新时间
}

func (m *customPerfCaseExecutionRecordModel) FindCountByReq(
	ctx context.Context, req SearchPerfCaseExecutionRecordReq,
) (int64, error) {
	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS t1").
		InnerJoin(perfSuiteExecutionRecordTableName+" AS t2 ON t1.`task_id` = t2.`task_id` AND t1.`suite_execute_id` = t2.`execute_id` AND t1.`project_id` = t2.`project_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(perfPlanExecutionRecordTableName+" AS t3 ON t1.`task_id` = t3.`task_id` AND t2.`plan_execute_id` = t3.`execute_id` AND t1.`project_id` = t3.`project_id` AND t1.`deleted` = t3.`deleted`").
		Where(
			"t1.`task_id` = ? AND t3.`execute_id` = ? AND t1.`project_id` = ? AND t1.`deleted` = ?",
			req.TaskID, req.ExecuteID, req.ProjectID, constants.NotDeleted,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(sb, "t1", sqlbuilder.WithCondition(m, req.Condition))
	return m.FindCount(ctx, sb)
}

func (m *customPerfCaseExecutionRecordModel) FindAllByReq(
	ctx context.Context, req SearchPerfCaseExecutionRecordReq,
) ([]*SearchPerfCaseExecutionRecordItem, error) {
	/*
			SQL:
			SELECT t1.`task_id`,
		       t1.`execute_id`,
		       t1.`suite_execute_id`,
		       t3.`execute_id` AS `plan_execute_id`,
		       t1.`project_id`,
		       t3.`plan_id`,
		       t3.`plan_name`,
		       t2.`suite_id`,
		       t2.`suite_name`,
		       t1.`case_id`,
		       t1.`case_name`,
		       t1.`steps`,
		       t1.`perf_data`,
		       t1.`load_generator`,
		       t1.`status`,
		       t1.`cost_time`,
		       t1.`executed_by`,
		       t1.`started_at`,
		       t1.`ended_at`,
		       t1.`api_metrics`,
		       t1.`err_msg`,
		       t1.`created_by`,
		       t1.`updated_by`,
		       t1.`created_at`,
		       t1.`updated_at`
		FROM `perf_case_execution_record` AS t1
		    INNER JOIN `perf_suite_execution_record` AS t2 ON
		        t1.`task_id` = t2.`task_id` AND
		        t1.`suite_execute_id` = t2.`execute_id` AND
		        t1.`project_id` = t2.`project_id` AND
		        t1.`deleted` = t2.`deleted`
		    INNER JOIN `perf_plan_execution_record` AS t3 ON
		        t1.`task_id` = t3.`task_id` AND
		        t2.`plan_execute_id` = t3.`execute_id` AND
		        t1.`project_id` = t3.`project_id` AND
		        t1.`deleted` = t3.`deleted`
		WHERE t1.`task_id` = ?
		  AND t3.`execute_id` = ?
		  AND t1.`project_id` = ?
		  AND t1.`deleted` = ?;
	*/

	fields := []string{
		"t1.`task_id`",
		"t1.`execute_id`",
		"t1.`suite_execute_id`",
		"t3.`execute_id` AS `plan_execute_id`",
		"t1.`project_id`",
		"t3.`plan_id`",
		"t3.`plan_name`",
		"t2.`suite_id`",
		"t2.`suite_name`",
		"t1.`case_id`",
		"t1.`case_name`",
		"t1.`steps`",
		"t1.`perf_data`",
		"t1.`load_generator`",
		"t1.`status`",
		"t1.`cost_time`",
		"t1.`executed_by`",
		"t1.`started_at`",
		"t1.`ended_at`",
		"t1.`api_metrics`",
		"t1.`err_msg`",
		"t1.`created_by`",
		"t1.`updated_by`",
		"t1.`created_at`",
		"t1.`updated_at`",
	}
	sb := squirrel.Select(fields...).
		From(m.table+" AS t1").
		InnerJoin(perfSuiteExecutionRecordTableName+" AS t2 ON t1.`task_id` = t2.`task_id` AND t1.`suite_execute_id` = t2.`execute_id` AND t1.`project_id` = t2.`project_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(perfPlanExecutionRecordTableName+" AS t3 ON t1.`task_id` = t3.`task_id` AND t2.`plan_execute_id` = t3.`execute_id` AND t1.`project_id` = t3.`project_id` AND t1.`deleted` = t3.`deleted`").
		Where(
			"t1.`task_id` = ? AND t3.`execute_id` = ? AND t1.`project_id` = ? AND t1.`deleted` = ?",
			req.TaskID, req.ExecuteID, req.ProjectID, constants.NotDeleted,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t1",
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)

	var resp []*SearchPerfCaseExecutionRecordItem
	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customPerfCaseExecutionRecordModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}
