package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_ UiCaseExecutionStepContentModel = (*customUiCaseExecutionStepContentModel)(nil)

	uiCaseExecutionStepContentInsertFields = stringx.Remove(
		uiCaseExecutionStepContentFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// UiCaseExecutionStepContentModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiCaseExecutionStepContentModel.
	UiCaseExecutionStepContentModel interface {
		uiCaseExecutionStepContentModel
		types.DBModel

		withSession(session sqlx.Session) UiCaseExecutionStepContentModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiCaseExecutionStepContent) squirrel.InsertBuilder
		UpdateBuilder(data *UiCaseExecutionStepContent) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*UiCaseExecutionStepContent, error,
		)

		FindByTaskIDStepID(ctx context.Context, taskID, stepID string) ([]*UiCaseExecutionStepContent, error)
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID string) error
	}

	customUiCaseExecutionStepContentModel struct {
		*defaultUiCaseExecutionStepContentModel

		conn sqlx.SqlConn
	}
)

// NewUiCaseExecutionStepContentModel returns a model for the database table.
func NewUiCaseExecutionStepContentModel(conn sqlx.SqlConn) UiCaseExecutionStepContentModel {
	return &customUiCaseExecutionStepContentModel{
		defaultUiCaseExecutionStepContentModel: newUiCaseExecutionStepContentModel(conn),
		conn:                                   conn,
	}
}

func (m *customUiCaseExecutionStepContentModel) withSession(session sqlx.Session) UiCaseExecutionStepContentModel {
	return NewUiCaseExecutionStepContentModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customUiCaseExecutionStepContentModel) Table() string {
	return m.table
}

func (m *customUiCaseExecutionStepContentModel) Fields() []string {
	return uiCaseExecutionStepContentFieldNames
}

func (m *customUiCaseExecutionStepContentModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customUiCaseExecutionStepContentModel) InsertBuilder(data *UiCaseExecutionStepContent) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiCaseExecutionStepContentInsertFields...).Values(
		data.TaskId, data.StepId, data.Content, data.Index, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customUiCaseExecutionStepContentModel) UpdateBuilder(data *UiCaseExecutionStepContent) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`content`":    data.Content,
		"`index`":      data.Index,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiCaseExecutionStepContentModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiCaseExecutionStepContentFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customUiCaseExecutionStepContentModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiCaseExecutionStepContentModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiCaseExecutionStepContentModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiCaseExecutionStepContent, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiCaseExecutionStepContent
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiCaseExecutionStepContentModel) FindByTaskIDStepID(
	ctx context.Context, taskID, stepID string,
) ([]*UiCaseExecutionStepContent, error) {
	sb := m.SelectBuilder().Where("`task_id` = ? AND `step_id` = ?", taskID, stepID).OrderBy("`index` ASC")
	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customUiCaseExecutionStepContentModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID string,
) error {
	// DELETE FROM `ui_case_execution_step_content` WHERE `task_id` = ?
	deleteBuilder := squirrel.Delete(m.table).Where("`task_id` = ?", taskID)
	stmt, values, err := deleteBuilder.ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	// DELETE FROM `ui_case_execution_step_content` WHERE `task_id` = ? LIMIT 500
	count := int64(common.ConstDefaultMaxDeleteItems)
	stmt, values, err = deleteBuilder.Limit(uint64(count)).ToSql()
	if err != nil {
		return err
	}

	for count >= common.ConstDefaultMaxDeleteItems {
		result, err := m.conn.ExecCtx(ctx, stmt, values...)
		if err != nil {
			return err
		}

		count, _ = result.RowsAffected()
	}

	return nil
}
