package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/Masterminds/squirrel"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_ UiSuiteExecutionRecordModel = (*customUiSuiteExecutionRecordModel)(nil)

	uiSuiteExecutionRecordRowsExpectAutoSet2 = strings.Join(
		stringx.Remove(
			uiSuiteExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), ",",
	)
	uiSuiteExecutionRecordRowsWithPlaceHolder2 = strings.Join(
		stringx.Remove(
			uiSuiteExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), "=?,",
	) + "=?"
)

type (
	// UiSuiteExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiSuiteExecutionRecordModel.
	UiSuiteExecutionRecordModel interface {
		uiSuiteExecutionRecordModel
		types.DBModel

		SelectBuilder() squirrel.SelectBuilder
		SelectBuilderWithEmptyContent() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder

		InsertRecordWithDefaultCreatedAt(ctx context.Context, data *UiSuiteExecutionRecord) (sql.Result, error)
		UpdateRecordWithDefaultUpdatedAt(ctx context.Context, data *UiSuiteExecutionRecord) error
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID, projectID string) error

		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*UiSuiteExecutionRecord, error)

		FindCountUISuiteExecutionRecords(
			ctx context.Context, req SearchUISuiteExecutionRecordReq,
		) (int64, error)
		FindUISuiteExecutionRecords(
			ctx context.Context, req SearchUISuiteExecutionRecordReq,
		) ([]*SearchUISuiteExecutionRecordItem, error)
	}

	customUiSuiteExecutionRecordModel struct {
		*defaultUiSuiteExecutionRecordModel
	}
)

// NewUiSuiteExecutionRecordModel returns a model for the database table.
func NewUiSuiteExecutionRecordModel(conn sqlx.SqlConn) UiSuiteExecutionRecordModel {
	return &customUiSuiteExecutionRecordModel{
		defaultUiSuiteExecutionRecordModel: newUiSuiteExecutionRecordModel(conn),
	}
}

func (m *customUiSuiteExecutionRecordModel) Table() string {
	return m.table
}

func (m *customUiSuiteExecutionRecordModel) Fields() []string {
	return uiSuiteExecutionRecordFieldNames
}

func (m *customUiSuiteExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiSuiteExecutionRecordFieldNames...).From(m.table)
}

func (m *customUiSuiteExecutionRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	fieldNames := stringx.Remove(uiSuiteExecutionRecordFieldNames, "`content`")
	fieldNames = append(fieldNames, "'' as content")
	return squirrel.Select(fieldNames...).From(m.table)
}

func (m *customUiSuiteExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *customUiSuiteExecutionRecordModel) InsertRecordWithDefaultCreatedAt(
	ctx context.Context, data *UiSuiteExecutionRecord,
) (sql.Result, error) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, uiSuiteExecutionRecordRowsExpectAutoSet2,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.SuiteId, data.SuiteName,
		data.PlanExecuteId, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status,
		data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime,
		data.Callback, data.Cleaned,
	)
	return ret, err
}

func (m *customUiSuiteExecutionRecordModel) UpdateRecordWithDefaultUpdatedAt(
	ctx context.Context, data *UiSuiteExecutionRecord,
) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiSuiteExecutionRecordRowsWithPlaceHolder2)
	_, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.SuiteId, data.SuiteName,
		data.PlanExecuteId, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status,
		data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime,
		data.Callback, data.Cleaned,
		data.Id,
	)
	return err
}

func (m *customUiSuiteExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiSuiteExecutionRecordModel) FindByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*UiSuiteExecutionRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiSuiteExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiSuiteExecutionRecordModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}

type SearchUISuiteExecutionRecordItem struct {
	UiSuiteExecutionRecord
}

func (m *customUiSuiteExecutionRecordModel) FindCountUISuiteExecutionRecords(
	ctx context.Context, req SearchUISuiteExecutionRecordReq,
) (int64, error) {
	/*
		SQL:
		SELECT COUNT(*)
		FROM `ui_suite_execution_record`
		WHERE `task_id` = ?
		  AND `plan_execute_id` = ?
		  AND `project_id` = ?

		OR

		SELECT COUNT(*)
		FROM (SELECT t.`id`,
		             t.`task_id`,
		             t.`project_id`,
		             t.`execute_id`,
		             t.`suite_id`,
		             t.`suite_name`,
		             t.`plan_execute_id`,
		             t.`total_case`,
		             t.`success_case`,
		             t.`failure_case`,
		             IF(t.`total_case` = t.`success_case`, 'Success', 'Failure') AS `status`,
		             t.`content`,
		             t.`executed_by`,
		             t.`started_at`,
		             t.`ended_at`,
		             IFNULL(t.`ended_at` - t.`started_at`, 0)                    AS `cost_time`,
		             t.`callback`,
		             t.`created_at`,
		             t.`updated_at`,
		             t.`cleaned`
		      FROM (SELECT t1.`id`,
		                   t1.`task_id`,
		                   t1.`project_id`,
		                   t1.`execute_id`,
		                   t1.`suite_id`,
		                   t1.`suite_name`,
		                   t1.`plan_execute_id`,
		                   COUNT(t2.`id`)                          AS `total_case`,
		                   SUM(IF(t2.`status` = 'Success', 1, 0))  AS `success_case`,
		                   SUM(IF(t2.`status` != 'Success', 1, 0)) AS `failure_case`,
		                   t1.`content`,
		                   t1.`executed_by`,
		                   MIN(t1.`started_at`)                    AS `started_at`,
		                   MAX(t1.`ended_at`)                      AS `ended_at`,
		                   t1.`callback`,
		                   MIN(t1.`created_at`)                    AS `created_at`,
		                   MAX(t1.`updated_at`)                    AS `updated_at`,
		                   t1.`cleaned`
		            FROM `ui_suite_execution_record` AS t1
		                INNER JOIN `ui_case_execution_record` AS t2
		                    ON t1.`task_id` = t2.`task_id` AND
		                       t1.`project_id` = t2.`project_id` AND
		                       t1.`execute_id` = t2.`suite_execute_id`
		            WHERE t1.`task_id` = ?
		              AND t1.`plan_execute_id` = ?
		              AND t1.`project_id` = ?
		              AND t2.`udid` = ?
		            GROUP BY t1.`execute_id`) AS t) AS t
	*/

	var sb squirrel.SelectBuilder

	if req.UDID == "" {
		sb = sqlbuilder.SearchOptions(
			squirrel.Select("COUNT(*)").
				From(m.table).
				Where(
					"`task_id` = ? AND `plan_execute_id` = ? AND `project_id` = ?",
					req.TaskID, req.ExecuteID, req.ProjectID,
				),
			sqlbuilder.WithCondition(m, req.Condition),
		)
	} else {
		var (
			aliasT  = "t"
			aliasT1 = "t1"
			aliasT2 = "t2"

			fields = []string{
				aliasT + ".`id`",
				aliasT + ".`task_id`",
				aliasT + ".`project_id`",
				aliasT + ".`execute_id`",
				aliasT + ".`suite_id`",
				aliasT + ".`suite_name`",
				aliasT + ".`plan_execute_id`",
				aliasT + ".`total_case`",
				aliasT + ".`success_case`",
				aliasT + ".`failure_case`",
				fmt.Sprintf(
					"IF(%s.`total_case` = %s.`success_case`, '%s', '%s') AS `status`", aliasT, aliasT, common.Success,
					common.Failure,
				),
				aliasT + ".`content`",
				aliasT + ".`executed_by`",
				aliasT + ".`started_at`",
				aliasT + ".`ended_at`",
				fmt.Sprintf("IFNULL(%s.`ended_at` - %s.`started_at`, 0) AS `cost_time`", aliasT, aliasT),
				aliasT + ".`callback`",
				aliasT + ".`created_at`",
				aliasT + ".`updated_at`",
				aliasT + ".`cleaned`",
			}
			subFields = []string{
				aliasT1 + ".`id`",
				aliasT1 + ".`task_id`",
				aliasT1 + ".`project_id`",
				aliasT1 + ".`execute_id`",
				aliasT1 + ".`suite_id`",
				aliasT1 + ".`suite_name`",
				aliasT1 + ".`plan_execute_id`",
				fmt.Sprintf("COUNT(%s.`id`) AS `total_case`", aliasT2),
				fmt.Sprintf("SUM(IF(%s.`status` = '%s', 1, 0)) AS `success_case`", aliasT2, common.Success),
				fmt.Sprintf("SUM(IF(%s.`status` != '%s', 1, 0)) AS `failure_case`", aliasT2, common.Success),
				aliasT1 + ".`content`",
				aliasT1 + ".`executed_by`",
				fmt.Sprintf("MIN(%s.`started_at`) AS `started_at`", aliasT1),
				fmt.Sprintf("MAX(%s.`ended_at`) AS `ended_at`", aliasT1),
				aliasT1 + ".`callback`",
				fmt.Sprintf("MIN(%s.`created_at`) AS `created_at`", aliasT1),
				fmt.Sprintf("MAX(%s.`updated_at`) AS `updated_at`", aliasT1),
				aliasT1 + ".`cleaned`",
			}
		)

		sb = squirrel.Select("COUNT(*)").
			FromSelect(
				squirrel.Select(fields...).
					FromSelect(
						sqlbuilder.SearchOptionsWithAlias(
							squirrel.Select(subFields...).
								From(m.table+" AS "+aliasT1).
								InnerJoin(
									fmt.Sprintf(
										"%s AS %s ON %s.`task_id` = %s.`task_id` AND %s.`project_id` = %s.`project_id` AND %s.`execute_id` = %s.`suite_execute_id`",
										uiCaseExecutionRecordTableName, aliasT2,
										aliasT1, aliasT2,
										aliasT1, aliasT2,
										aliasT1, aliasT2,
									),
								).
								Where(
									fmt.Sprintf(
										"%s.`task_id` = ? AND %s.`plan_execute_id` = ? AND %s.`project_id` = ? AND %s.`udid` = ?",
										aliasT1, aliasT1, aliasT1, aliasT2,
									),
									req.TaskID, req.ExecuteID, req.ProjectID, req.UDID,
								).
								GroupBy(aliasT1+".`execute_id`"),
							aliasT1,
							sqlbuilder.WithCondition(m, req.Condition),
						), aliasT,
					), aliasT,
			)
	}

	return m.FindCount(ctx, sb)
}

func (m *customUiSuiteExecutionRecordModel) FindUISuiteExecutionRecords(
	ctx context.Context, req SearchUISuiteExecutionRecordReq,
) ([]*SearchUISuiteExecutionRecordItem, error) {
	/*
		SQL:
		SELECT *
		FROM `ui_suite_execution_record`
		WHERE `task_id` = ?
		  AND `plan_execute_id` = ?
		  AND `project_id` = ?

		OR

		SELECT t.`id`,
		       t.`task_id`,
		       t.`project_id`,
		       t.`execute_id`,
		       t.`suite_id`,
		       t.`suite_name`,
		       t.`plan_execute_id`,
		       t.`total_case`,
		       t.`success_case`,
		       t.`failure_case`,
		       IF(t.`total_case` = t.`success_case`, 'Success', 'Failure') AS `status`,
		       t.`content`,
		       t.`executed_by`,
		       t.`started_at`,
		       t.`ended_at`,
		       IFNULL(t.`ended_at` - t.`started_at`, 0)                    AS `cost_time`,
		       t.`callback`,
		       t.`created_at`,
		       t.`updated_at`,
		       t.`cleaned`
		FROM (SELECT t1.`id`,
		             t1.`task_id`,
		             t1.`project_id`,
		             t1.`execute_id`,
		             t1.`suite_id`,
		             t1.`suite_name`,
		             t1.`plan_execute_id`,
		             COUNT(t2.`id`)                          AS `total_case`,
		             SUM(IF(t2.`status` = 'Success', 1, 0))  AS `success_case`,
		             SUM(IF(t2.`status` != 'Success', 1, 0)) AS `failure_case`,
		             t1.`content`,
		             t1.`executed_by`,
		             MIN(t1.`started_at`)                    AS `started_at`,
		             MAX(t1.`ended_at`)                      AS `ended_at`,
		             t1.`callback`,
		             MIN(t1.`created_at`)                    AS `created_at`,
		             MAX(t1.`updated_at`)                    AS `updated_at`,
		             t1.`cleaned`
		      FROM `ui_suite_execution_record` AS t1
		          INNER JOIN `ui_case_execution_record` AS t2
		              ON t1.`task_id` = t2.`task_id` AND
		                 t1.`project_id` = t2.`project_id` AND
		                 t1.`execute_id` = t2.`suite_execute_id`
		      WHERE t1.`task_id` = ?
		        AND t1.`plan_execute_id` = ?
		        AND t1.`project_id` = ?
		        AND t2.`udid` = ?
		      GROUP BY t1.`execute_id`) AS t
	*/

	var sb squirrel.SelectBuilder

	if req.UDID == "" {
		sb = sqlbuilder.SearchOptions(
			squirrel.Select(uiSuiteExecutionRecordFieldNames...).
				From(m.table).
				Where(
					"`task_id` = ? AND `plan_execute_id` = ? AND `project_id` = ?",
					req.TaskID, req.ExecuteID, req.ProjectID,
				),
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		)
	} else {
		var (
			aliasT  = "t"
			aliasT1 = "t1"
			aliasT2 = "t2"

			fields = []string{
				aliasT + ".`id`",
				aliasT + ".`task_id`",
				aliasT + ".`project_id`",
				aliasT + ".`execute_id`",
				aliasT + ".`suite_id`",
				aliasT + ".`suite_name`",
				aliasT + ".`plan_execute_id`",
				aliasT + ".`total_case`",
				aliasT + ".`success_case`",
				aliasT + ".`failure_case`",
				fmt.Sprintf(
					"IF(%s.`total_case` = %s.`success_case`, '%s', '%s') AS `status`", aliasT, aliasT, common.Success,
					common.Failure,
				),
				aliasT + ".`content`",
				aliasT + ".`executed_by`",
				aliasT + ".`started_at`",
				aliasT + ".`ended_at`",
				fmt.Sprintf("IFNULL(%s.`ended_at` - %s.`started_at`, 0) AS `cost_time`", aliasT, aliasT),
				aliasT + ".`callback`",
				aliasT + ".`created_at`",
				aliasT + ".`updated_at`",
				aliasT + ".`cleaned`",
			}
			subFields = []string{
				aliasT1 + ".`id`",
				aliasT1 + ".`task_id`",
				aliasT1 + ".`project_id`",
				aliasT1 + ".`execute_id`",
				aliasT1 + ".`suite_id`",
				aliasT1 + ".`suite_name`",
				aliasT1 + ".`plan_execute_id`",
				fmt.Sprintf("COUNT(%s.`id`) AS `total_case`", aliasT2),
				fmt.Sprintf("SUM(IF(%s.`status` = '%s', 1, 0)) AS `success_case`", aliasT2, common.Success),
				fmt.Sprintf("SUM(IF(%s.`status` != '%s', 1, 0)) AS `failure_case`", aliasT2, common.Success),
				aliasT1 + ".`content`",
				aliasT1 + ".`executed_by`",
				fmt.Sprintf("MIN(%s.`started_at`) AS `started_at`", aliasT1),
				fmt.Sprintf("MAX(%s.`ended_at`) AS `ended_at`", aliasT1),
				aliasT1 + ".`callback`",
				fmt.Sprintf("MIN(%s.`created_at`) AS `created_at`", aliasT1),
				fmt.Sprintf("MAX(%s.`updated_at`) AS `updated_at`", aliasT1),
				aliasT1 + ".`cleaned`",
			}
		)

		sb = squirrel.Select(fields...).
			FromSelect(
				sqlbuilder.SearchOptionsWithAlias(
					squirrel.Select(subFields...).
						From(m.table+" AS "+aliasT1).
						InnerJoin(
							fmt.Sprintf(
								"%s AS %s ON %s.`task_id` = %s.`task_id` AND %s.`project_id` = %s.`project_id` AND %s.`execute_id` = %s.`suite_execute_id`",
								uiCaseExecutionRecordTableName, aliasT2,
								aliasT1, aliasT2,
								aliasT1, aliasT2,
								aliasT1, aliasT2,
							),
						).
						Where(
							fmt.Sprintf(
								"%s.`task_id` = ? AND %s.`plan_execute_id` = ? AND %s.`project_id` = ? AND %s.`udid` = ?",
								aliasT1, aliasT1, aliasT1, aliasT2,
							),
							req.TaskID, req.ExecuteID, req.ProjectID, req.UDID,
						).
						GroupBy(aliasT1+".`execute_id`"),
					aliasT1,
					sqlbuilder.WithCondition(m, req.Condition),
					sqlbuilder.WithPagination(m, req.Pagination),
					sqlbuilder.WithSort(m, req.Sort),
				), aliasT,
			)
	}

	var (
		resp []*SearchUISuiteExecutionRecordItem
		err  error
	)

	err = utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}
