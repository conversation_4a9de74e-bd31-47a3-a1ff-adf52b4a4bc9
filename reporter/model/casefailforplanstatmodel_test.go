package model

import (
	"context"
	"fmt"
	"testing"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func TestName(t *testing.T) {
	data := new(CaseFailForPlanStat)
	sql, args, _ := squirrel.Insert("table").Columns(caseFailForPlanStatInsertFields...).Values(
		data.ProjectId, data.TaskId, data.PlanId, data.CaseId, data.ExecuteId, data.CaseType, data.FailReasonUrl,
		data.Deleted, data.CreatedBy, data.UpdatedBy,
	).ToSql()
	fmt.Println(sql)
	fmt.Println(args)
}

func MockMysql() sqlx.SqlConn {
	source := "probe:Quwan@2020_TTinternation@tcp(************:3306)/reporter?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"
	return sqlx.NewMysql(source)
}

func MockRedisConf() redis.RedisConf {
	return redis.RedisConf{
		Host: "***************:6379",
		Type: "node",
		DB:   2,
	}
}

func MockCache() cache.CacheConf {
	return cache.CacheConf{
		{
			RedisConf: MockRedisConf(),
			Weight:    100,
		},
	}
}

var (
	converters []utils.TypeConverter
	str        = `{
  "project_id": "project_id:B3HxxLu4Hngh4LLAG1lYu",
  "case_id": "case_id:UVIx08SffOdvLRKgrldXd",
  "case_type": "API_CASE",
  "pagination": {
    "current_page": 1,
    "page_size": 20
  },
  "condition": {
    "single": null,
    "group": {
      "relationship": "AND",
      "conditions": [
        {
          "single": {
            "field": "name",
            "compare": "LIKE",
            "other": {
              "value": "test"
            }
          },
          "group": null
        },
        {
          "single": {
            "field": "maintained_by",
            "compare": "IN",
            "in": [
              "T4440"
            ]
          },
          "group": null
        }
      ]
    }
  },
  "sort": [
    {
      "field": "updated_at",
      "order": "DESC"
    }
  ]
}`
)

func TestGenerateApiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(t *testing.T) {
	model := NewCaseFailForPlanStatModel(MockMysql())
	req := new(reporterpb.ListFailCaseRecordForPlanRequest)
	err := protobuf.UnmarshalJSON([]byte(str), req)
	if err != nil {
		fmt.Println(err)
		return
	}

	query, countBuilder := model.GenerateListFailCaseForPlanRecordQuery(
		SearchCaseFailForPlanStatReq{
			ProjectId:  req.GetProjectId(),
			CaseId:     req.GetCaseId(),
			CaseType:   req.GetCaseType(),
			Pagination: req.GetPagination(),
		},
	)
	fmt.Println(query.MustSql())
	fmt.Println(countBuilder.MustSql())
	byQuery, err := model.FindJoinPlanNoCacheByQuery(context.Background(), query.SelectBuilder)
	fmt.Println(byQuery)
	cout, err := model.FindCount(context.Background(), countBuilder.SelectBuilder)
	fmt.Println(cout)
	resp := &reporterpb.ListFailCaseRecordForPlanResponse{}

	resp.Items = make([]*reporterpb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord, 0, len(byQuery))
	for _, _Case := range byQuery {
		item := &reporterpb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord{}
		if err = utils.Copy(item, _Case, converters...); err != nil {
			fmt.Println(err)
			return
		}
		resp.Items = append(resp.Items, item)
	}
	fmt.Println(resp)
}

func TestSelectBuilder(t *testing.T) {
	query, values, err := squirrel.Select("COUNT(*) AS `count`", "MAX(`updated_at`) AS `last_updated_at`").
		From("`case_fail_for_plan_stat`").
		Where(
			"`project_id` = ? AND `case_id` = ? AND `case_type` = ? AND `updated_at` >= DATE_SUB(CURDATE(), INTERVAL ? DAY) AND `deleted` = ?",
			"1", "2", "API_CASE", 10, constants.NotDeleted,
		).
		ToSql()
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("query: %s\nvalues: %s", query, jsonx.MarshalIgnoreError(values))
}
