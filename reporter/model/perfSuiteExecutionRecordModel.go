package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ PerfSuiteExecutionRecordModel = (*customPerfSuiteExecutionRecordModel)(nil)

	perfSuiteExecutionRecordInsertFields = stringx.Remove(
		perfSuiteExecutionRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfSuiteExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfSuiteExecutionRecordModel.
	PerfSuiteExecutionRecordModel interface {
		perfSuiteExecutionRecordModel
		types.DBModel

		withSession(session sqlx.Session) PerfSuiteExecutionRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfSuiteExecutionRecord) squirrel.InsertBuilder
		UpdateBuilder(data *PerfSuiteExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*PerfSuiteExecutionRecord, error,
		)

		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID, projectID string) error
	}

	customPerfSuiteExecutionRecordModel struct {
		*defaultPerfSuiteExecutionRecordModel

		conn sqlx.SqlConn
	}
)

// NewPerfSuiteExecutionRecordModel returns a model for the database table.
func NewPerfSuiteExecutionRecordModel(conn sqlx.SqlConn) PerfSuiteExecutionRecordModel {
	return &customPerfSuiteExecutionRecordModel{
		defaultPerfSuiteExecutionRecordModel: newPerfSuiteExecutionRecordModel(conn),
		conn:                                 conn,
	}
}

func (m *customPerfSuiteExecutionRecordModel) withSession(session sqlx.Session) PerfSuiteExecutionRecordModel {
	return NewPerfSuiteExecutionRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customPerfSuiteExecutionRecordModel) Table() string {
	return m.table
}

func (m *customPerfSuiteExecutionRecordModel) Fields() []string {
	return perfSuiteExecutionRecordFieldNames
}

func (m *customPerfSuiteExecutionRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customPerfSuiteExecutionRecordModel) InsertBuilder(data *PerfSuiteExecutionRecord) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfSuiteExecutionRecordInsertFields...).Values(
		data.TaskId, data.ExecuteId, data.PlanExecuteId, data.ProjectId, data.SuiteId, data.SuiteName, data.Status,
		data.CostTime, data.ExecutedBy, data.StartedAt, data.EndedAt, data.ErrMsg, data.Cleaned, data.Deleted,
		data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfSuiteExecutionRecordModel) UpdateBuilder(data *PerfSuiteExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`status`":     data.Status,
		"`cost_time`":  data.CostTime,
		"`ended_at`":   data.EndedAt,
		"`err_msg`":    data.ErrMsg,
		"`cleaned`":    data.Cleaned,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfSuiteExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfSuiteExecutionRecordFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPerfSuiteExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfSuiteExecutionRecordModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfSuiteExecutionRecordModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfSuiteExecutionRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfSuiteExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfSuiteExecutionRecordModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}
