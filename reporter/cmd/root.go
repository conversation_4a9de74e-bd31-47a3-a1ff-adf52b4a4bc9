package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"
)

const (
	rootCmdUse   = "reporter"
	rootCmdShort = "Reporter is one of the microservices of the Quality Platform"
	rootCmdLong  = `Reporter is one of the microservices of the Quality Platform. 
The main function is the management of the test resources, such as test cases, test suites, test plans...`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
