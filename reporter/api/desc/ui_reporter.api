syntax = "v1"

import (
	"ui_types.api"
)

@server (
	prefix: reporter/v1
	group: uiReporter
)

service reporter {
	// 计划执行记录列表
    @handler ListUiPlanRecordHandler
	post /record/ui_plan/list (ListUIPlanRecordReq) returns (ListUIPlanRecordResp)

	//@doc "get ui plan record"
	@handler GetUIPlanRecord
	get /record/ui_plan/get (GetUIPlanRecordReq) returns (GetUIPlanRecordResp)

	//@doc "search ui suite record"
	@handler SearchUISuiteRecord
	post /record/ui_plan/suite/search (SearchUISuiteRecordReq) returns (SearchUISuiteRecordResp)

	//@doc "search ui case record"
	@handler SearchUICaseRecord
	post /record/ui_suite/case/search (SearchUICaseRecordReq) returns (SearchUICaseRecordResp)

	//@doc "get ui case record"
	@handler GetUICaseRecord
	get /record/ui_case/get (GetUICaseRecordReq) returns (GetUICaseRecordResp)

	//@doc "get ui case step list"
	@handler ListUICaseStepList
	post /record/ui_case/step/list (ListUICaseStepReq) returns (ListUICaseStepResp)

	//@doc "get ui case step"
	@handler GetUICaseStep
	get /record/ui_case/step/get (GetUICaseStepReq) returns (GetUICaseStepResp)

	@handler SearchUIDeviceRecord
	post /record/ui_plan/device/search (SearchUIDeviceRecordReq) returns (SearchUIDeviceRecordResp)

    @handler GetUIDevicePerfData
	get /record/ui_plan/device/perf_data/get (GetUIDevicePerfDataReq) returns (GetUIDevicePerfDataResp)

	@doc "获取SLA对比数据"
	@handler CompareSlaData
	post /record/sla_data/compare (CompareSlaDataReq) returns (CompareSlaDataResp)

	@doc "获取SLA详细数据"
	@handler ListSlaData
	post /record/sla_data/list (ListSlaDataReq) returns (ListSlaDataResp)

	@doc "获取指定分支的SLA版本列表"
	@handler ListSlaVersion
	post /record/sla_version/list (ListSlaVersionReq) returns (ListSlaVersionResp)
}
