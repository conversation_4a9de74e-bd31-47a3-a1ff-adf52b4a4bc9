syntax = "v1"

import (
	"types.api"
)

@server (
	prefix: reporter/v1
	group: reporter
)

service reporter {
	// 组件执行记录详情
	@handler ViewComponentRecordHandler
	get /record/component/view (viewComponentRecordReq) returns (viewComponentRecordResp)
	
	// 组件组执行记录列表
	@handler ListComponentGroupRecordHandler
	post /record/component_group/list (listComponentGroupRecordReq) returns (listComponentGroupRecordResp)
	
	// 组件组执行记录详情
	@handler GetComponentGroupRecordHandler
	get /record/component_group/get (GetComponentGroupRecordReq) returns (GetComponentGroupRecordResp)
	
	// 用例执行记录列表
	@handler ListCaseRecordHandler
	post /record/case/list (ListCaseRecordReq) returns (ListCaseRecordResp)
	
	// 用例执行记录详情
	@handler GetCaseRecordHandler
	get /record/case/get (GetCaseRecordReq) returns (GetCaseRecordResp)
	
	// 接口执行记录列表
	@handler ListInterfaceRecordHandler
	post /record/interface/list (ListInterfaceRecordReq) returns (ListInterfaceRecordResp)
	
	// 接口执行记录详情
	@handler GetInterfaceRecordHandler
	get /record/interface/get (GetInterfaceRecordReq) returns (GetInterfaceRecordResp)
	
	// 集合执行记录列表
	@handler ListSuiteRecordHandler
	post /record/suite/list (ListSuiteRecordReq) returns (ListSuiteRecordResp)
	
	// 集合执行记录详情
	@handler GetSuiteRecordHandler
	get /record/suite/get (GetSuiteRecordReq) returns (GetSuiteRecordResp)

	// 精准测试服务执行记录列表
    @handler ListServiceRecordHandler
    post /record/service/list (ListServiceRecordReq) returns (ListServiceRecordResp)

    // 精准测试执行记录详情
    @handler GetServiceRecordHandler
    get /record/service/get (GetServiceRecordReq) returns (GetServiceRecordResp)

	// 计划执行记录列表
	@handler ListPlanRecordHandler
	post /record/plan/list (ListPlanRecordReq) returns (ListPlanRecordResp)
	
	// 计划执行记录详情
	@handler GetPlanRecordHandler
	get /record/plan/get (GetPlanRecordReq) returns (GetPlanRecordResp)
	
	// 计划执行详情查看其下各集合用例执行时间刻度信息
	@handler GetPlanTimeScaleHandler
	get /record/plan/get_time_scale (GetPlanTimeScaleReq) returns (GetPlanTimeScaleResp)
	
	// 获取测试计划执行报告（ci/cd专用）
	@handler GetPlanSummaryHandler
	get /record/plan/getSummary (GetPlanSummaryReq) returns (GetPlanSummaryResp)

    // 计划执行记录列表
    @handler ListFailCaseForPlanRecordHandler
    post /record/case/fail/for_plan/list (ListFailCaseRecordForPlanReq) returns (ListFailCaseRecordForPlanResp)
}