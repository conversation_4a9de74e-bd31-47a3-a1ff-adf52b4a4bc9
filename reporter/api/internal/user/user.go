package user

import (
	"context"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	userservice "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"
)

type Cache = map[string]*userinfo.FullUserInfo

var admin = userinfo.Admin()

func GetUser(ctx context.Context, client userservice.UserService, account string, cache Cache) (*userinfo.FullUserInfo, error) {
	if cache == nil {
		cache = make(Cache)
	}

	if account == "" {
		return &userinfo.FullUserInfo{}, nil
	} else if account == "empty" || account == "1" {
		// handle the user id which is hard code
		return &userinfo.FullUserInfo{
			TokenUserInfo: userinfo.TokenUserInfo{
				Account:  admin.Account,
				Fullname: admin.Fullname,
				DeptName: admin.DeptName,
				Email:    admin.Email,
				Mobile:   admin.Mobile,
			},
			DeptId:       admin.DeptId,
			FullDeptName: admin.FullDeptName,
			Photo:        admin.Photo,
			Enabled:      admin.Enabled,
		}, nil
	}

	// check whether the user is in the cache
	if ui, ok := cache[account]; ok {
		return ui, nil
	}

	// get user info by user id
	resp, err := client.GetUser(ctx, &userpb.GetUserReq{Account: account}, grpc.UseCompressor(gzip.Name))
	if err != nil {
		if errors.As(err, &sqlc.ErrNotFound) {
			return &userinfo.FullUserInfo{}, nil
		}
		return nil, err
	}

	userInfo := &userinfo.FullUserInfo{}
	_ = copier.Copy(userInfo, resp.GetUserInfo())

	// save user info into cache
	cache[account] = userInfo
	return userInfo, nil
}

//func GetUserList(userRpc userservice.UserService, ctx context.Context, account string) (*userinfo.UserInfo, error) {
//	// 组装查询用户的筛选条件
//	condition := sqlBuilderRpc.Condition{
//		Single: &sqlBuilderRpc.SingleCondition{
//			Field:   "account",
//			Compare: "EQ",
//			Other: &sqlBuilderRpc.Other{
//				Value: account,
//			},
//		},
//	}
//
//	// 查询用户信息
//	resp, err := userRpc.UserList(ctx, &userpb.UserListReq{Condition: &condition}, grpc.UseCompressor(gzip.Name))
//	if err != nil {
//		return nil, err
//	}
//
//	var userInfo *userinfo.UserInfo
//	_ = copier.Copy(&userInfo, resp.Items[0])
//
//	return userInfo, nil
//}
