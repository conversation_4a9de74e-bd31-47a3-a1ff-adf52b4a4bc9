package internal

import "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/middlewares"

var skipUrls = []string{
	"/reporter/v1/record/component/view",
	"/reporter/v1/record/component_group/list",
	"/reporter/v1/record/component_group/get",
	"/reporter/v1/record/case/list",
	"/reporter/v1/record/case/get",
	"/reporter/v1/record/interface/list",
	"/reporter/v1/record/interface/get",
	"/reporter/v1/record/suite/list",
	"/reporter/v1/record/suite/get",
	"/reporter/v1/record/plan/list",
	"/reporter/v1/record/plan/get",
	"/reporter/v1/record/plan/get_time_scale",
}

func RegisterSkipUrls() {
	for _, url := range skipUrls {
		middlewares.SkipForUrl(url)
	}
}
