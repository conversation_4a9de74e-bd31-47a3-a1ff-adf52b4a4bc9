package perfReporter

import (
	"net/http"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/logic/perfReporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
)

func SearchPerfPlanRecordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SearchPerfPlanRecordReq
		if err := httpx.Parse(r, &req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ParseParamError, err.Error()), "failed to parse parameters, error: %+v", err))
			return
		}

		if err := svcCtx.Validator.Validate.StructCtx(r.Context(), req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ValidateParamError, svcCtx.Validator.Translate(err)), "failed to validate parameters, error: %+v", err))
			return
		}

		l := perfReporter.NewSearchPerfPlanRecordLogic(r.Context(), svcCtx)
		resp, err := l.SearchPerfPlanRecord(&req)
		response.MakeHttpResponse(r, w, resp, err)
	}
}
