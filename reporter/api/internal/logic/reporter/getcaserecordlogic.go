package reporter

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commonTypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/user"
)

type GetCaseRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCaseRecordLogic {
	return &GetCaseRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCaseRecordLogic) GetCaseRecord(req *types.GetCaseRecordReq) (resp *types.GetCaseRecordResp, err error) {
	resp = &types.GetCaseRecordResp{}

	defer func() {
		if err1 := recover(); err1 != nil {
			// 处理错误信息
			msg := fmt.Sprintf(
				"component group record detail with task_id[%s], project_id[%s] case_execute_id[%s] and times[%d]",
				req.TaskId, req.ProjectId, req.CaseExecuteId, 1,
			)
			err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err1))
		}
	}()

	// 获取用例执行信息
	selectBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilder()
	selectBuilder = selectBuilder.Where(
		"`task_id` = ? AND `project_id` = ? AND `execute_id` = ? AND `component_execute_id` = ? ",
		req.TaskId, req.ProjectId, req.ExecuteId, req.CaseExecuteId,
	)

	caseRecords, err := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil || len(caseRecords) == 0 {
		panic(err)
	} else {
		componentType := caseRecords[0].ComponentType
		if (componentType != "API_CASE") && (componentType != "INTERFACE_CASE") {
			err = errors.New(
				fmt.Sprintf(
					"传递的component_execute_id[%s]不是一个有效的case_executeId", req.CaseExecuteId,
				),
			)
			panic(err)
		}
	}
	caseRecord := caseRecords[0]

	var (
		generalConfig commonTypes.ApiGeneralConfig
		accountConfig []commonTypes.ApiAccountConfig
	)

	_ = json.Unmarshal([]byte(caseRecord.GeneralConfig.String), &generalConfig)
	_ = json.Unmarshal([]byte(caseRecord.AccountConfig.String), &accountConfig)

	// 获取用例内部组件执行信息
	componentGroupRecordStruct := ComponentGroupRecordStruct{}

	ll := &GetComponentGroupRecordLogic{
		ctx:    l.ctx,
		svcCtx: l.svcCtx,
	}
	GetComponentGroupRecordByParams(&componentGroupRecordStruct, ll, req.TaskId, req.ProjectId, req.CaseExecuteId, 1)

	executor, userErr := user.GetUser(l.ctx, l.svcCtx.UserRPC, caseRecord.ExecutedBy, nil)
	if userErr != nil {
		err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
		return nil, err
	}

	var items []*types.CaseAllLayerComponentRecordItem

	for _, record := range componentGroupRecordStruct.InnerExecutionRecord {
		item := &types.CaseAllLayerComponentRecordItem{
			TaskId:                   record.TaskId,
			ProjectId:                record.ProjectId,
			ExecuteId:                record.ExecuteId,
			ComponentId:              record.ComponentId,
			ComponentExecuteId:       record.ComponentExecuteId,
			ParentComponentId:        record.ParentComponentId.String,
			ParentComponentExecuteId: record.ParentComponentExecuteId.String,
			Version:                  record.Version,
			ExecutedBy:               executor,
			StartedAt:                record.StartedAt,
			EndedAt:                  record.EndedAt.Int64,
			CostTime:                 record.CostTime,
			Status:                   record.Status.String,
		}
		items = append(items, item)
	}

	resp.ExecuteSummary = types.ExecuteSummary{
		ExecutedBy: executor,
		StartedAt:  caseRecord.StartedAt,
		EndedAt:    caseRecord.EndedAt.Int64,
		CostTime:   caseRecord.CostTime,
		Status:     caseRecord.Status.String,
	}
	resp.GeneralConfig = generalConfig
	resp.AccountConfig = accountConfig

	if items == nil {
		// 避免返回给前端时为null
		resp.Items = make([]*types.CaseAllLayerComponentRecordItem, 0)
	} else {
		resp.Items = items
	}

	return resp, err
}
