package reporter

import (
	"context"
	cryptorand "crypto/rand"
	"fmt"
	"math/big"
	mathrand "math/rand"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/discov"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	uiReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uireporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UiReporterService struct {
	ctx  context.Context
	conf zrpc.RpcClientConf

	Name string
	Cli  uiReporter.UIReporter
}

func NewUiReporterRpc(conf zrpc.RpcClientConf) *UiReporterService {
	ctx := context.Background()
	cli := &UiReporterService{
		ctx:  ctx,
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  uiReporter.NewUIReporter(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}

	return cli
}

func (l *UiReporterService) createUiPlanRecord(taskId, projectId string,
	planId, planName string, totalSuite, startedAt int64,
) (*pb.CreateUIPlanRecordResponse, error) {
	in := &pb.PutUIPlanRecordRequest{
		ProjectId:  projectId,
		PlanId:     planId,
		PlanName:   planName,
		TaskId:     taskId,
		Status:     "started",
		ExecutedBy: "T2384",
		StartedAt:  startedAt,
		TotalSuite: totalSuite,
	}
	resp, err := l.Cli.CreateUIPlanRecord(l.ctx, in)

	return resp, err
}

func (l *UiReporterService) modifyUiPlanRecord(taskId, executeId, projectId string,
	planId, planName string, totalSuite, finishedSuite int64,
	successSuite, startedAt int64,
) (*pb.ModifyUIPlanRecordResponse, error) {
	var status string
	if successSuite == totalSuite {
		status = "success"
	} else {
		status = "failure"
	}

	endedAt := time.Now().UnixMilli()

	in := &pb.PutUIPlanRecordRequest{
		TaskId:        taskId,
		ExecuteId:     executeId,
		ProjectId:     projectId,
		PlanId:        planId,
		PlanName:      planName,
		Status:        status,
		ExecutedBy:    "T2384",
		StartedAt:     startedAt,
		EndedAt:       endedAt,
		TotalSuite:    totalSuite,
		FinishedSuite: finishedSuite,
		SuccessSuite:  successSuite,
		Finished:      1,
	}
	resp, err := l.Cli.ModifyUIPlanRecord(l.ctx, in)

	return resp, err
}

func (l *UiReporterService) createUiSuiteRecord(taskId, projectId string,
	suiteId, suiteName, planExecuteId string, totalCase int64,
	suiteStartedAt int64,
) (*pb.CreateUISuiteRecordResponse, error) {
	in := &pb.PutUISuiteRecordRequest{
		TaskId:        taskId,
		ProjectId:     projectId,
		SuiteId:       suiteId,
		SuiteName:     suiteName,
		PlanExecuteId: planExecuteId,
		TotalCase:     totalCase,
		ExecutedBy:    "T2384",
		StartedAt:     suiteStartedAt,
	}
	resp, err := l.Cli.CreateUISuiteRecord(l.ctx, in)

	return resp, err
}

func (l *UiReporterService) modifyUiSuiteRecord(taskId, projectId string,
	suiteId, suiteName, planExecuteId, suiteExecuteId string, totalCase int64,
	successCase, failureCase int64, suiteStatus string, suiteStartedAt int64,
) (*pb.ModifyUISuiteRecordResponse, error) {
	endedAt := time.Now().UnixMilli()

	in := &pb.PutUISuiteRecordRequest{
		TaskId:        taskId,
		ProjectId:     projectId,
		ExecuteId:     suiteExecuteId,
		SuiteId:       suiteId,
		SuiteName:     suiteName,
		PlanExecuteId: planExecuteId,
		TotalCase:     totalCase,
		SuccessCase:   successCase,
		FailureCase:   failureCase,
		Status:        suiteStatus,
		ExecutedBy:    "T2384",
		StartedAt:     suiteStartedAt,
		EndedAt:       endedAt,
		Finished:      1,
	}
	resp, err := l.Cli.ModifyUISuiteRecord(l.ctx, in)

	return resp, err
}

func (l *UiReporterService) createUiCaseRecord(taskId, projectId string,
	caseId, caseName, suiteExecuteId string, startedAt int64,
) (*pb.CreateUICaseRecordResponse, error) {
	in := &pb.PutUICaseRecordRequest{
		TaskId:         taskId,
		ProjectId:      projectId,
		CaseId:         caseId,
		CaseName:       caseName,
		SuiteExecuteId: suiteExecuteId,
		ExecutedBy:     "T2384",
		StartedAt:      startedAt,
	}
	resp, err := l.Cli.CreateUICaseRecord(l.ctx, in)

	return resp, err
}

func (l *UiReporterService) modifyUiCaseRecord(taskId, projectId string,
	caseId, caseName, suiteExecuteId, caseStatus, caseExecuteId string, caseStartedAt int64,
) (*pb.ModifyUICaseRecordResponse, error) {
	endedAt := time.Now().UnixMilli()

	in := &pb.PutUICaseRecordRequest{
		TaskId:         taskId,
		ProjectId:      projectId,
		ExecuteId:      caseExecuteId,
		CaseId:         caseId,
		CaseName:       caseName,
		SuiteExecuteId: suiteExecuteId,
		Status:         caseStatus,
		ExecutedBy:     "T2384",
		StartedAt:      caseStartedAt,
		EndedAt:        endedAt,
	}
	resp, err := l.Cli.ModifyUICaseRecord(l.ctx, in)

	return resp, err
}

func random(max int64) int64 {
	n, err := cryptorand.Int(cryptorand.Reader, big.NewInt(max))
	if err != nil {
		return mathrand.Int63n(max)
	}

	return n.Int64()
}

func (l *UiReporterService) createSuiteRelatedRecord(planOrder, suiteOrder int64, suiteStatus, taskId, projectId, planExecuteId string, t *testing.T) {
	suiteId := utils.GenSuiteId()
	suiteName := fmt.Sprintf("集合%d", suiteOrder)

	suiteStartedAt := time.Now().UnixMilli()

	totalCase := random(8)
	if totalCase <= 0 {
		totalCase = 1
	}

	successCase := random(totalCase)
	if successCase <= 0 {
		successCase = 1
	}

	// 创建集合执行记录
	respSuite, errSuite := l.createUiSuiteRecord(taskId, projectId, suiteId, suiteName, planExecuteId, totalCase, suiteStartedAt)
	if errSuite != nil {
		fmt.Println(fmt.Sprintf("create %d.%d ui_suite_execution_record encounter error:%s", planOrder, suiteOrder, errSuite))
		t.FailNow()
	}
	suiteExecuteId := respSuite.GetExecuteId()

	// 创建并更新用例执行记录
	i := int64(0)
	for {
		i += 1
		if i > totalCase {
			break
		}
		caseId := utils.GenCaseId()
		caseName := fmt.Sprintf("用例%d", i)

		caseStartedAt := time.Now().UnixMilli()
		respCase, errCase := l.createUiCaseRecord(taskId, projectId, caseId, caseName, suiteExecuteId, caseStartedAt)
		if errCase != nil {
			fmt.Println(fmt.Sprintf("create %d.%d.%d ui_suite_execution_record encounter error:%s", planOrder, suiteOrder, i, errCase))
			t.FailNow()
		}
		caseExecuteId := respCase.GetExecuteId()

		time.Sleep(1 * time.Second)

		var caseStatus string
		if i <= successCase {
			caseStatus = "success"
		} else {
			caseStatus = "failure"
		}

		_, errCase_ := l.modifyUiCaseRecord(taskId, projectId, caseId, caseName, suiteExecuteId, caseStatus, caseExecuteId, caseStartedAt)
		if errCase_ != nil {
			fmt.Println(fmt.Sprintf("modify %d.%d.%d ui_case_execution_record encounter error:%s", planOrder, suiteOrder, i, errCase_))
			t.FailNow()
		}
	}

	// 更新集合的执行记录
	_, errSuite_ := l.modifyUiSuiteRecord(taskId, projectId, suiteId, suiteName, planExecuteId, suiteExecuteId, totalCase, successCase, totalCase-successCase, suiteStatus, suiteStartedAt)
	if errSuite_ != nil {
		fmt.Println(fmt.Sprintf("modify %d.%d ui_suite_execution_record encounter error:%s", planOrder, suiteOrder, errSuite_))
		t.FailNow()
	}
}

func (l *UiReporterService) createPlanRelatedRecord(planOrder int64, projectId string,
	planId, planName string, totalSuite, finishedSuite, successSuite int64, t *testing.T,
) {
	taskId := utils.GenTaskId()
	planStartedAt := time.Now().UnixMilli()

	// 创建计划执行记录
	respPlan, errPlan := l.createUiPlanRecord(taskId, projectId, planId, planName, totalSuite, planStartedAt)
	if errPlan != nil {
		fmt.Println(fmt.Sprintf("create %d. ui_plan_execution_record encounter error:%s", planOrder, errPlan))
		t.FailNow()
	}
	planExecuteId := respPlan.GetExecuteId()

	var suiteStatus string

	i := int64(0)
	for {
		i += 1
		if i > totalSuite {
			break
		}
		if i <= successSuite {
			suiteStatus = "success"
		} else {
			suiteStatus = "failure"
		}
		l.createSuiteRelatedRecord(planOrder, i, suiteStatus, taskId, projectId, planExecuteId, t)
	}

	// 更新计划执行记录
	_, errPlan_ := l.modifyUiPlanRecord(taskId, planExecuteId, projectId, planId, planName, totalSuite, finishedSuite, successSuite, planStartedAt)
	if errPlan_ != nil {
		fmt.Println(fmt.Sprintf("modify %d. ui_plan_execution_record encounter error:%s", planOrder, errPlan_))
		t.FailNow()
	}
}

func TestCreateAllRecord(t *testing.T) {
	conf := zrpc.RpcClientConf{
		Etcd: discov.EtcdConf{
			Hosts: []string{"127.0.0.1:2379"},
			Key:   "rpc.reporter",
		},
	}

	l := NewUiReporterRpc(conf)

	projectId := "project_id:Kqllt5-9fA-I5UOdhjA5d"
	planId := "plan_id:GTYVVS_ZZSXxNrMnr3bkC"
	planName := "测试计划~"

	l.createPlanRelatedRecord(1, projectId, planId, planName, 4, 2, 1, t)

	l.createPlanRelatedRecord(2, projectId, planId, planName, 5, 5, 5, t)

	l.createPlanRelatedRecord(3, projectId, planId, planName, 6, 3, 3, t)

	l.createPlanRelatedRecord(4, projectId, planId, planName, 7, 2, 1, t)
}
