package reporter

import (
	"context"
	"fmt"
	"math"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/user"
)

type ListCaseRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListCaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListCaseRecordLogic {
	return &ListCaseRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListCaseRecordLogic) ListCaseRecord(req *types.ListCaseRecordReq) (*types.ListCaseRecordResp, error) {
	resp := &types.ListCaseRecordResp{}

	// validate the project_id in req

	var executeType string
	if strings.HasPrefix(req.CaseId, "case_id") {
		executeType = "API_CASE"
	} else if strings.HasPrefix(req.CaseId, "interface_case_id") {
		executeType = "INTERFACE_CASE"
	}

	var countBuilder squirrel.SelectBuilder
	if req.Version == "" {
		countBuilder = l.svcCtx.ExecutionRecordModel.SelectCountBuilder().Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = ? AND `is_root` = 1",
			req.ProjectId, req.CaseId, executeType,
		)
	} else {
		countBuilder = l.svcCtx.ExecutionRecordModel.SelectCountBuilder().Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = ? AND `is_root` = 1 AND `version` = ?",
			req.ProjectId, req.CaseId, executeType, req.Version,
		)
	}

	count, err := l.svcCtx.ExecutionRecordModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		var msg string
		if req.Version == "" {
			msg = fmt.Sprintf(
				"failed to count case rceord with project_id[%s]、case_id[%s], error: %+v",
				req.ProjectId, req.CaseId, err,
			)
		} else {
			msg = fmt.Sprintf(
				"failed to count case rceord with project_id[%s]、case_id[%s], version[%s], error: %+v",
				req.ProjectId, req.CaseId, req.Version, err,
			)
		}
		return nil, errorx.Err(errorx.DBError, msg)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	var selectBuilder squirrel.SelectBuilder
	if req.Version == "" {
		selectBuilder = l.svcCtx.ExecutionRecordModel.SelectBuilder().Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = ? AND `is_root` = 1",
			req.ProjectId, req.CaseId, executeType,
		)
	} else {
		selectBuilder = l.svcCtx.ExecutionRecordModel.SelectBuilder().Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = ? AND `is_root` = 1 AND `version` = ?",
			req.ProjectId, req.CaseId, executeType, req.Version,
		)
	}

	if len(req.Sort) == 0 {
		sortField := api.SortField{
			Field: "created_at",
			Order: "desc",
		}
		req.Sort = append(req.Sort, &sortField)
	}

	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithPagination(l.svcCtx.ExecutionRecordModel, req.Pagination),
		sqlbuilder.WithSort(l.svcCtx.ExecutionRecordModel, req.Sort),
	)
	caseRecords, err := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil {
		var msg string
		if req.Version == "" {
			msg = fmt.Sprintf(
				"failed to find case record with project_id[%s]、case_id[%s], error: %+v",
				req.ProjectId, req.CaseId, err,
			)
		} else {
			msg = fmt.Sprintf(
				"failed to find case record with project_id[%s]、case_id[%s], version[%s], error: %+v",
				req.ProjectId, req.CaseId, req.Version, err,
			)
		}
		return nil, errorx.Err(errorx.DBError, msg)
	}

	// 用户映射
	cache := make(user.Cache)

	// 避免返回给前端时为null
	resp.Items = make([]*types.CaseRecordItem, 0, len(caseRecords))
	for _, caseRecord := range caseRecords {
		executor, userErr := user.GetUser(l.ctx, l.svcCtx.UserRPC, caseRecord.ExecutedBy, cache)
		if userErr != nil {
			err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
			return nil, err
		}

		r := &types.CaseRecordItem{
			TaskId:        caseRecord.TaskId,
			ProjectId:     caseRecord.ProjectId,
			ExecuteId:     caseRecord.ExecuteId,
			CaseExecuteId: caseRecord.ComponentExecuteId,
			Version:       caseRecord.Version,
			ExecutedBy:    executor,
			StartedAt:     caseRecord.StartedAt,
			EndedAt:       caseRecord.EndedAt.Int64,
			CostTime:      caseRecord.CostTime,
			Status:        caseRecord.Status.String,
			Cleaned:       caseRecord.Cleaned,
		}
		resp.Items = append(resp.Items, r)
	}

	if req.Pagination != nil {
		resp.CurrentPage = req.Pagination.CurrentPage
		resp.PageSize = req.Pagination.PageSize
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}
	return resp, nil
}
