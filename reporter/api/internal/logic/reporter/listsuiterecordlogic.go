package reporter

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListSuiteRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListSuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListSuiteRecordLogic {
	return &ListSuiteRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListSuiteRecordLogic) ListSuiteRecord(req *types.ListSuiteRecordReq) (
	resp *types.ListSuiteRecordResp, err error,
) {
	in := &pb.ListSuiteRecordRequest{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ReporterRPC.ListSuiteRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ListSuiteRecordResp{}
	if err = utils.Copy(resp, out); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	items := out.GetItems()

	// 用户映射
	cache := make(user.Cache)

	for i := range resp.Items {
		executor, userErr := user.GetUser(l.ctx, l.svcCtx.UserRPC, items[i].ExecutedBy, cache)
		if userErr != nil {
			err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
			return nil, err
		}

		resp.Items[i].ExecutedBy = executor
	}

	return resp, nil
}
