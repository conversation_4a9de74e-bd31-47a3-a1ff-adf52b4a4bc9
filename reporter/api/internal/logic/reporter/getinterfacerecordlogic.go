package reporter

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetInterfaceRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInterfaceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInterfaceRecordLogic {
	return &GetInterfaceRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInterfaceRecordLogic) GetInterfaceRecord(req *types.GetInterfaceRecordReq) (
	resp *types.GetInterfaceRecordResp, err error,
) {
	in := &pb.GetInterfaceRecordRequest{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ReporterRPC.GetInterfaceRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.GetInterfaceRecordResp{}
	if err = utils.Copy(resp, out); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	executor, userErr := user.GetUser(l.ctx, l.svcCtx.UserRPC, out.ExecutedBy, nil)
	if userErr != nil {
		err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
		return nil, err
	}

	resp.ExecutedBy = executor
	for i := range resp.CaseItems {
		resp.CaseItems[i].ExecutedBy = executor
		maintainer, userErr := user.GetUser(l.ctx, l.svcCtx.UserRPC, out.CaseItems[i].MaintainedBy, nil)
		if userErr != nil {
			err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
			return nil, err
		}
		resp.CaseItems[i].MaintainedBy = maintainer
	}

	return resp, nil
}
