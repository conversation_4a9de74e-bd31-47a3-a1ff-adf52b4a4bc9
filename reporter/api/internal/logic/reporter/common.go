package reporter

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
)

type ComponentGroupRecordStruct struct {
	InnerExecutionRecord []*model.ComponentExecuteRecord
}

func QueryComponentGroupRecord(l *GetComponentGroupRecordLogic, taskId, projectId, componentExecuteId string, times int64) []*model.ComponentExecuteRecord {
	componentRecords, err := l.svcCtx.ExecutionRecordModel.FindComponentExecuteRecords(l.ctx, taskId, projectId, componentExecuteId, times)
	if err != nil {
		panic(err)
	}

	return componentRecords
}

func GetComponentGroupRecordByParams(cGRS *ComponentGroupRecordStruct, l *GetComponentGroupRecordLogic,
	taskId, projectId, componentExecuteId string, times int64,
) {
	records := QueryComponentGroupRecord(l, taskId, projectId, componentExecuteId, times)

	cGRS.InnerExecutionRecord = append(cGRS.InnerExecutionRecord, records...)

	for _, record := range records {
		// 引用的不需要查找下一层
		//switch record.ComponentType {
		//case constants.SETUP, constants.TEARDOWN, constants.SINGLE, constants.GROUP, constants.CONDITION:
		//	GetComponentGroupRecordByParams(
		//		cGRS,
		//		l,
		//		record.TaskId,
		//		record.ProjectId,
		//		"",
		//		record.ParentComponentId.String,
		//		record.Times)
		//}

		GetComponentGroupRecordByParams(
			cGRS,
			l,
			record.TaskId,
			record.ProjectId,
			record.ComponentExecuteId,
			record.Times)
	}
}
