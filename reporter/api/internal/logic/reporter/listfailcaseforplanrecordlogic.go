package reporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListFailCaseForPlanRecordLogic struct {
	*BaseLogic
}

func NewListFailCaseForPlanRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ListFailCaseForPlanRecordLogic {
	return &ListFailCaseForPlanRecordLogic{
		newBaseLogic(ctx, svcCtx),
	}
}

func (l *ListFailCaseForPlanRecordLogic) ListFailCaseForPlanRecord(req *types.ListFailCaseRecordForPlanReq) (
	resp *types.ListFailCaseRecordForPlanResp, err error,
) {
	in := &pb.ListFailCaseRecordForPlanRequest{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ReporterRPC.ListFailCaseForPlanRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.ListFailCaseRecordForPlanResp{Items: []*types.FailCaseItemForPlanRecord{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
