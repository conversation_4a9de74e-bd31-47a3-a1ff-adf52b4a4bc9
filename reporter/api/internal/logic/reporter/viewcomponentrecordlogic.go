package reporter

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
)

type ViewComponentRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewViewComponentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewComponentRecordLogic {
	return &ViewComponentRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ViewComponentRecordLogic) ViewComponentRecord(req *types.ViewComponentRecordReq) (*types.ViewComponentRecordResp, error) {
	resp := &types.ViewComponentRecordResp{}

	if req.Times == 0 {
		req.Times = 1
	}

	selectBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilder()
	// 暂未过滤 组件类型!
	selectBuilder = selectBuilder.Where(
		"`task_id` = ? AND `project_id` = ? AND `execute_id` = ? AND `component_execute_id` = ? AND times = ?",
		req.TaskId, req.ProjectId, req.ExecuteId, req.ComponentExecuteId, req.Times).Limit(1)

	result, err := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)

	msg := fmt.Sprintf(
		"component record detail with task_id[%s], project_id[%s], component_execute_id[%s] and times[%d]",
		req.TaskId, req.ProjectId, req.ComponentExecuteId, req.Times)

	if err != nil || result == nil {
		return nil, errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err))
	}

	resp.Content = result[0].Content.String

	return resp, nil
}
