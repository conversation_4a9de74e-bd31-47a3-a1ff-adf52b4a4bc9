package reporter

import (
	"context"
	"fmt"
	"math"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/user"
)

type ListComponentGroupRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListComponentGroupRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListComponentGroupRecordLogic {
	return &ListComponentGroupRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListComponentGroupRecordLogic) ListComponentGroupRecord(req *types.ListComponentGroupRecordReq) (
	*types.ListComponentGroupRecordResp, error,
) {
	resp := &types.ListComponentGroupRecordResp{}

	// validate the project_id in req

	var countBuilder squirrel.SelectBuilder
	if req.Version == "" {
		countBuilder = l.svcCtx.ExecutionRecordModel.SelectCountBuilder().Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = 'API_COMPONENT_GROUP' AND `is_root` = 1",
			req.ProjectId, req.ComponentGroupId,
		)
	} else {
		countBuilder = l.svcCtx.ExecutionRecordModel.SelectCountBuilder().Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = 'API_COMPONENT_GROUP' AND `is_root` = 1 AND `version` = ?",
			req.ProjectId, req.ComponentGroupId, req.Version,
		)
	}

	count, err := l.svcCtx.ExecutionRecordModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		var msg string
		if req.Version == "" {
			msg = fmt.Sprintf(
				"failed to count component group record with project_id[%s]、component_group_id[%s], error: %+v",
				req.ProjectId, req.ComponentGroupId, err,
			)
		} else {
			msg = fmt.Sprintf(
				"failed to count component group record with project_id[%s]、component_group_id[%s]、version[%s], error: %+v",
				req.ProjectId, req.ComponentGroupId, req.Version, err,
			)
		}
		return nil, errorx.Err(errorx.DBError, msg)
	}

	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	var selectBuilder squirrel.SelectBuilder
	if req.Version == "" {
		selectBuilder = l.svcCtx.ExecutionRecordModel.SelectBuilder().Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = 'API_COMPONENT_GROUP' AND `is_root` = 1",
			req.ProjectId, req.ComponentGroupId,
		)
	} else {
		selectBuilder = l.svcCtx.ExecutionRecordModel.SelectBuilder().Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = 'API_COMPONENT_GROUP' AND `is_root` = 1 AND `version` = ?",
			req.ProjectId, req.ComponentGroupId, req.Version,
		)
	}

	if len(req.Sort) == 0 {
		sortField := api.SortField{
			Field: "created_at",
			Order: "desc",
		}
		req.Sort = append(req.Sort, &sortField)
	}

	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithPagination(l.svcCtx.ExecutionRecordModel, req.Pagination),
		sqlbuilder.WithSort(l.svcCtx.ExecutionRecordModel, req.Sort),
	)
	componentGroupRecords, err := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil {
		var msg string
		if req.Version == "" {
			msg = fmt.Sprintf(
				"failed to find component group record with project_id[%s]、component_group_id[%s], error: %+v",
				req.ProjectId, req.ComponentGroupId, err,
			)
		} else {
			msg = fmt.Sprintf(
				"failed to find component group record with project_id[%s]、component_group_id[%s], version[%s], error: %+v",
				req.ProjectId, req.ComponentGroupId, req.Version, err,
			)
		}
		return nil, errors.Wrap(errorx.Err(errorx.DBError, err.Error()), msg)
	}

	// 用户映射
	cache := make(user.Cache)

	// 避免返回给前端时为null
	resp.Items = make([]*types.ComponentGroupRecordItem, 0, len(componentGroupRecords))
	for _, componentGroupRecord := range componentGroupRecords {
		executor, userErr := user.GetUser(l.ctx, l.svcCtx.UserRPC, componentGroupRecord.ExecutedBy, cache)
		if userErr != nil {
			err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
			return nil, err
		}

		item := &types.ComponentGroupRecordItem{
			TaskId:                  componentGroupRecord.TaskId,
			ProjectId:               componentGroupRecord.ProjectId,
			ExecuteId:               componentGroupRecord.ExecuteId,
			ComponentGroupExecuteId: componentGroupRecord.ComponentExecuteId,
			Times:                   componentGroupRecord.Times,
			Version:                 componentGroupRecord.Version,
			ExecutedBy:              executor,
			StartedAt:               componentGroupRecord.StartedAt,
			EndedAt:                 componentGroupRecord.EndedAt.Int64,
			CostTime:                componentGroupRecord.CostTime,
			Status:                  componentGroupRecord.Status.String,
			CreatedAt:               componentGroupRecord.CreatedAt.UnixMilli(),
			Cleaned:                 componentGroupRecord.Cleaned,
		}
		resp.Items = append(resp.Items, item)
	}

	if req.Pagination != nil {
		resp.CurrentPage = req.Pagination.CurrentPage
		resp.PageSize = req.Pagination.PageSize
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}
	return resp, nil
}
