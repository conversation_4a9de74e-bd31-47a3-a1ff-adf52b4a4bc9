package reporter

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/user"
)

type GetComponentGroupRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetComponentGroupRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetComponentGroupRecordLogic {
	return &GetComponentGroupRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetComponentGroupRecordLogic) GetComponentGroupRecord(req *types.GetComponentGroupRecordReq) (
	resp *types.GetComponentGroupRecordResp, err error,
) {
	resp = &types.GetComponentGroupRecordResp{}

	defer func() {
		if err1 := recover(); err1 != nil {
			// 处理错误信息
			msg := fmt.Sprintf(
				"component group record detail with task_id[%s], project_id[%s] component_execute_id[%s] and times[%d]",
				req.TaskId, req.ProjectId, req.ComponentExecuteId, req.Times,
			)
			err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err1))
		}
	}()

	records, err := l.svcCtx.ExecutionRecordModel.FindComponentSelfRecord(
		l.ctx, req.TaskId, req.ProjectId, req.ExecuteId, req.ComponentExecuteId, req.Times,
	)
	if err != nil {
		panic(err.Error())
	}
	if len(records) != 1 {
		panic("找到的组件组调试记录条数不为1条")
	}
	componentSelfRecord := records[0]

	// 递归查找组件组内层组件（组）执行记录
	componentGroupRecordStruct := ComponentGroupRecordStruct{}
	GetComponentGroupRecordByParams(
		&componentGroupRecordStruct, l, req.TaskId, req.ProjectId, req.ComponentExecuteId, req.Times,
	)

	executor, userErr := user.GetUser(l.ctx, l.svcCtx.UserRPC, componentSelfRecord.ExecutedBy, nil)
	if userErr != nil {
		err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
		return nil, err
	}

	var items []*types.ComponentRecordItem

	for _, componentRecord := range componentGroupRecordStruct.InnerExecutionRecord {
		item := &types.ComponentRecordItem{
			TaskId:                   componentRecord.TaskId,
			ProjectId:                componentRecord.ProjectId,
			ExecuteId:                componentRecord.ExecuteId,
			ComponentId:              componentRecord.ComponentId,
			ComponentExecuteId:       componentRecord.ComponentExecuteId,
			ParentComponentId:        componentRecord.ParentComponentId.String,
			ParentComponentExecuteId: componentRecord.ParentComponentExecuteId.String,
			Version:                  componentRecord.Version,
			Times:                    componentRecord.Times,
			ExecutedBy:               executor,
			StartedAt:                componentRecord.StartedAt,
			EndedAt:                  componentRecord.EndedAt.Int64,
			CostTime:                 componentRecord.CostTime,
			Status:                   componentRecord.Status.String,
		}
		items = append(items, item)
	}

	resp.ExecuteSummary = types.ExecuteSummary{
		ExecutedBy: executor,
		StartedAt:  componentSelfRecord.StartedAt,
		EndedAt:    componentSelfRecord.EndedAt.Int64,
		CostTime:   componentSelfRecord.CostTime,
		Status:     componentSelfRecord.Status.String,
	}

	if items == nil {
		// 避免返回给前端时为null
		resp.Items = make([]*types.ComponentRecordItem, 0)
	} else {
		resp.Items = items
	}

	return resp, err
}
