package stabilityReporter_test

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

func TestSearchStabilityDeviceRecord(t *testing.T) {
	resp := &pb.SearchStabilityDeviceRecordResp{
		CurrentPage: 1,
		PageSize:    20,
		TotalCount:  100,
		TotalPage:   5,
		Items: []*pb.StabilityDeviceRecordItem{
			{
				ProjectId: "pid:xxxx",
				Udid:      "udid:dddd",
				Device: &pb1.Device{
					Udid: "udid:xxxx",
					Name: "dname:xxxx",
				},
			},
		},
	}
	t.Logf("%+v", resp)

	converters := []utils.TypeConverter{
		commonpb.StringToTriggerMode(),
	}

	out := &types.SearchStabilityDeviceRecordResp{}
	if err := utils.Copy(out, resp, converters...); err != nil {
		t.Fatalf(
			"failed to copy resp to out, resp: %s, error: %+v",
			jsonx.MarshalIgnoreError(resp), err,
		)
	}
	t.Logf("%+v", out)
	t.Logf("%+v", out.Items[0])
	t.Logf("%+v", out.Items[0].Device)
}
