package uiReporter

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/hashicorp/golang-lru/v2/expirable"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sla"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	common "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
)

var deviceCache = expirable.NewLRU[string, *common.Device](
	constants.ConstDefaultMakeMapSize, onEvict, 24*time.Hour,
)

type CompareSlaDataLogic struct {
	*BaseLogic
}

// 获取SLA对比数据
func NewCompareSlaDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CompareSlaDataLogic {
	return &CompareSlaDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CompareSlaDataLogic) CompareSlaData(req *types.CompareSlaDataReq) (resp *types.CompareSlaDataResp, err error) {
	var (
		platform    = platformToString(req.Platform)
		testVersion = formatVersion(platform, req.TestVersion)
		baseVersion = formatVersion(platform, req.BaseVersion)
		baseLine    = req.BaseLine

		finishLaunchedMaps = make(map[string]lang.PlaceholderType)
		autoLoginMaps      = make(map[string]lang.PlaceholderType)
		newHomePageMaps    = make(map[string]lang.PlaceholderType)
	)
	if baseLine == nil {
		bt := inferBranchType(platform, testVersion)
		rsp, err := l.svcCtx.ManagerSlaRPC.GetSlaThreshold(l.ctx, &pb.GetSlaThresholdReq{
			ProjectId: req.ProjectId,
		})
		if err != nil {
			return nil, err
		}
		baseLine = fetchBaseLine(rsp.Thresholds, commonpb.PlatformType(req.Platform), bt)
	}

	data, err := l.svcCtx.SLAClient.GetComparisonData(
		&sla.GetComparisonDataReq{
			Platform:    platform,
			TaskID:      req.TaskId,
			BaseTaskID:  req.BaseTaskId,
			TestVersion: testVersion,
			BaseVersion: baseVersion,
			BaseLine:    baseLine,
		},
	)
	if err != nil {
		return resp, err
	}

	ipon := data.DeviceIsPassedOrNotData
	if len(ipon.FinishLaunchedTimeFailList) > 0 {
		for _, udid := range ipon.FinishLaunchedTimeFailList {
			finishLaunchedMaps[udid] = lang.Placeholder
		}
	}
	if len(ipon.AutoLoginFailList) > 0 {
		for _, udid := range ipon.AutoLoginFailList {
			autoLoginMaps[udid] = lang.Placeholder
		}
	}
	if len(ipon.NewHomePageFailList) > 0 {
		for _, udid := range ipon.NewHomePageFailList {
			newHomePageMaps[udid] = lang.Placeholder
		}
	}

	size := len(data.DeviceComparisonData)/2 + 1
	resp = &types.CompareSlaDataResp{Items: make([]*types.CompareSlaDataRespItem, 0, size)}
	records := make(map[string]*types.CompareSlaDataRespItem, size)
	for _, dt := range data.DeviceComparisonData {
		var metaData *sla.DeviceComparisonMetaData
		if dt.TestData != nil {
			metaData = dt.TestData
		} else if dt.BaseData != nil {
			metaData = dt.BaseData
		} else {
			continue
		}
		udid := metaData.DeviceName
		item, ok := records[udid]
		if !ok {
			device, ok := deviceCache.Get(udid)
			if !ok {
				device = &common.Device{UDID: udid}
				_device, err := l.svcCtx.DeviceHubRPC.GetDevice(l.ctx, &devicehubpb.GetDeviceReq{
					Udid: udid,
				})
				if err != nil {
					l.Errorf("failed to fetch device by udid: %s, error: %+v", udid, err)
				} else {
					if err = utils.Copy(device, _device.GetDevice(), l.converters...); err != nil {
						l.Errorf(
							"failed to copy device from pb, data: %s, error: %+v",
							protobuf.MarshalJSONIgnoreError(_device.GetDevice()), err,
						)
					} else {
						deviceCache.Add(udid, device)
					}
				}
			}
			item = &types.CompareSlaDataRespItem{Device: device}
			records[udid] = item
			resp.Items = append(resp.Items, item)
		}
		if dt.TestData != nil {
			// 测试版本
			item.Version.TestValue = metaData.Version
			item.Counts.TestValue = strconv.Itoa(metaData.Counts)
			item.AvgFinishLaunchedTime.TestValue = strconv.Itoa(metaData.AvgFinishLaunchedTime)
			item.AvgFinishLaunchedTime.RateValue = metaData.FinishLaunchedPercent
			_, item.AvgFinishLaunchedTime.OverLimit = finishLaunchedMaps[udid]
			item.AvgAutoLoginTime.TestValue = strconv.Itoa(metaData.AvgAutoLogin)
			item.AvgAutoLoginTime.RateValue = metaData.AutoLoginPercent
			_, item.AvgAutoLoginTime.OverLimit = autoLoginMaps[udid]
			item.AvgNewHomePageTime.TestValue = strconv.Itoa(metaData.AvgNewHomePage)
			item.AvgNewHomePageTime.RateValue = metaData.NewHomePagePercent
			_, item.AvgNewHomePageTime.OverLimit = newHomePageMaps[udid]
		} else if dt.BaseData != nil {
			// 基准版本
			item.Version.BaseValue = metaData.Version
			item.Counts.BaseValue = strconv.Itoa(metaData.Counts)
			item.AvgFinishLaunchedTime.BaseValue = strconv.Itoa(metaData.AvgFinishLaunchedTime)
			item.AvgAutoLoginTime.BaseValue = strconv.Itoa(metaData.AvgAutoLogin)
			item.AvgNewHomePageTime.BaseValue = strconv.Itoa(metaData.AvgNewHomePage)
		}
	}

	return resp, nil
}

func onEvict(key string, value *common.Device) {
	logx.Debugf("evict from lru, key: %s, value: %s", key, protobuf.MarshalJSONIgnoreError(value))
}

func platformToString(platform int8) string {
	var platform_str string
	switch platform {
	case 1:
		platform_str = "Android"
	case 2:
		platform_str = "IOS"
	}
	return platform_str
}

func formatVersion(platform, version string) string {
	if strings.Contains(version, "-") {
		switch platform {
		case "Android":
			// 6.67.5-18809 => 6.67.5 18809
			version = strings.ReplaceAll(version, "-", " ")
		case "IOS":
			// 6.68.0-3917062 => Ver_6.68.0/build_3917062
			version = strings.ReplaceAll("Ver_"+version, "-", "/build_")
		}
	}
	return version
}

func inferBranchType(platform, version string) commonpb.BranchType {
	bt := commonpb.BranchType_BranchType_RELEASE
	switch platform {
	case "Android":
		vb := strings.Split(version, " ")
		if len(vb) > 1 {
			build, err := strconv.Atoi(vb[1])
			if err == nil && build > 30000 {
				bt = commonpb.BranchType_BranchType_TESTING
			}
		}
	case "IOS":
		// IOS只有生产包
	}
	return bt
}

func fetchBaseLine(thresholds []*pb.SlaThreshold, pt commonpb.PlatformType, bt commonpb.BranchType) *commontypes.SLABaseLine {
	baseLine := &commontypes.SLABaseLine{}
	for _, threshold := range thresholds {
		if threshold.PlatformType == pt && threshold.BranchType == bt {
			switch threshold.GetName() {
			case string(constants.SLA_Threshold_Finish_Launched):
				baseLine.FinishLaunchedLine = threshold.GetValue()
			case string(constants.SLA_Threshold_Auto_Login):
				baseLine.AutoLoginLine = threshold.GetValue()
			case string(constants.SLA_Threshold_New_Home_Page):
				baseLine.NewHomePageLine = threshold.GetValue()
			}
		}
	}
	return baseLine
}
