package uiReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sla"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
)

type ListSlaDataLogic struct {
	*BaseLogic
}

// 获取SLA详细数据
func NewListSlaDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListSlaDataLogic {
	return &ListSlaDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ListSlaDataLogic) ListSlaData(req *types.ListSlaDataReq) (resp *types.ListSlaDataResp, err error) {
	var (
		platform = platformToString(req.Platform)
		version  = formatVersion(platform, req.Version)
	)

	items, err := l.svcCtx.SLAClient.GetDetailsData(&sla.GetDetailsDataReq{
		Platform:   platform,
		Version:    version,
		DeviceName: req.Udid,
		TaskId:     req.TaskId,
	})
	if err != nil {
		return resp, err
	}

	resp = &types.ListSlaDataResp{Items: make([]*types.ListSlaDataRespItem, 0, len(items))}
	for _, item := range items {
		rspt := &types.ListSlaDataRespItem{}
		if err = utils.Copy(rspt, item, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data to response, data: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(item), err,
			)
		}
		resp.Items = append(resp.Items, rspt)
	}

	return resp, nil
}
