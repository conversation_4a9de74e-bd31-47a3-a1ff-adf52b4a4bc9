package types

import (
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type ViewComponentRecordReq struct {
	TaskId             string `form:"task_id"`
	ProjectId          string `form:"project_id"`
	ExecuteId          string `form:"execute_id"`
	ComponentExecuteId string `form:"component_execute_id"`
	Times              int64  `form:"times"`
}

type ViewComponentRecordResp struct {
	Content string `json:"content"`
}

type ListComponentGroupRecordReq struct {
	ProjectId        string           `json:"project_id"`
	ComponentGroupId string           `json:"component_group_id"`
	Version          string           `json:"version"`
	Pagination       *api.Pagination  `json:"pagination,omitempty,optional"`
	Sort             []*api.SortField `json:"sort,omitempty,optional"`
}

type ListComponentGroupRecordResp struct {
	CurrentPage uint64                      `json:"current_page"`
	PageSize    uint64                      `json:"page_size"`
	TotalCount  uint64                      `json:"total_count"`
	TotalPage   uint64                      `json:"total_page"`
	Items       []*ComponentGroupRecordItem `json:"items"`
}

type ComponentGroupRecordItem struct {
	TaskId                  string                 `json:"task_id"`
	ProjectId               string                 `json:"project_id"`
	ExecuteId               string                 `json:"execute_id"`
	ComponentGroupExecuteId string                 `json:"component_group_execute_id"`
	Times                   int64                  `json:"times"`
	Version                 string                 `json:"version"`
	ExecutedBy              *userinfo.FullUserInfo `json:"executed_by"`
	StartedAt               int64                  `json:"started_at"`
	EndedAt                 int64                  `json:"ended_at"`
	CostTime                int64                  `json:"cost_time"`
	Status                  string                 `json:"status"`
	CreatedAt               int64                  `json:"created_at"`
	Cleaned                 int64                  `json:"cleaned"`
}

type ExecuteSummary struct {
	ExecutedBy *userinfo.FullUserInfo `json:"executed_by"`
	StartedAt  int64                  `json:"started_at"`
	EndedAt    int64                  `json:"ended_at"`
	CostTime   int64                  `json:"cost_time"`
	Status     string                 `json:"status"`
}

type GetComponentGroupRecordReq struct {
	TaskId             string `form:"task_id"`
	ProjectId          string `form:"project_id"`
	ExecuteId          string `form:"execute_id"`
	ComponentExecuteId string `form:"component_execute_id"`
	Times              int64  `form:"times"`
}

type GetComponentGroupRecordResp struct {
	ExecuteSummary ExecuteSummary         `json:"execute_summary"`
	Items          []*ComponentRecordItem `json:"items"`
}

type ComponentRecordItem struct {
	TaskId                   string                 `json:"task_id"`
	ProjectId                string                 `json:"project_id"`
	ExecuteId                string                 `json:"execute_id"`
	ComponentId              string                 `json:"component_id"`
	ComponentExecuteId       string                 `json:"component_execute_id"`
	ParentComponentId        string                 `json:"parent_component_id"`
	ParentComponentExecuteId string                 `json:"parent_component_execute_id"`
	Version                  string                 `json:"version"`
	Times                    int64                  `json:"times"`
	ExecutedBy               *userinfo.FullUserInfo `json:"executed_by"`
	StartedAt                int64                  `json:"started_at"`
	EndedAt                  int64                  `json:"ended_at"`
	CostTime                 int64                  `json:"cost_time"`
	Status                   string                 `json:"status"`
}

type ListCaseRecordReq struct {
	ProjectId  string           `json:"project_id"`
	CaseId     string           `json:"case_id"`
	Version    string           `json:"version"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional"`
	Sort       []*api.SortField `json:"sort,omitempty,optional"`
}

type ListCaseRecordResp struct {
	CurrentPage uint64            `json:"current_page"`
	PageSize    uint64            `json:"page_size"`
	TotalCount  uint64            `json:"total_count"`
	TotalPage   uint64            `json:"total_page"`
	Items       []*CaseRecordItem `json:"items"`
}

type CaseRecordItem struct {
	TaskId        string                 `json:"task_id"`
	ProjectId     string                 `json:"project_id"`
	ExecuteId     string                 `json:"execute_id"`
	CaseExecuteId string                 `json:"case_execute_id"`
	Version       string                 `json:"version"`
	ExecutedBy    *userinfo.FullUserInfo `json:"executed_by"`
	StartedAt     int64                  `json:"started_at"`
	EndedAt       int64                  `json:"ended_at"`
	CostTime      int64                  `json:"cost_time"`
	Status        string                 `json:"status"`
	Cleaned       int64                  `json:"cleaned"`
}

type GetCaseRecordReq struct {
	TaskId        string `form:"task_id"`
	ProjectId     string `form:"project_id"`
	ExecuteId     string `form:"execute_id"`
	CaseExecuteId string `form:"case_execute_id"`
}

type GetCaseRecordResp struct {
	ExecuteSummary ExecuteSummary                     `json:"execute_summary"`
	GeneralConfig  types.ApiGeneralConfig             `json:"general_config"`
	AccountConfig  []types.ApiAccountConfig           `json:"account_config"`
	Items          []*CaseAllLayerComponentRecordItem `json:"items"`
}

type CaseAllLayerComponentRecordItem struct {
	TaskId                   string                 `json:"task_id"`
	ProjectId                string                 `json:"project_id"`
	ExecuteId                string                 `json:"execute_id"`
	ComponentId              string                 `json:"component_id"`
	ComponentExecuteId       string                 `json:"component_execute_id"`
	ParentComponentId        string                 `json:"parent_component_id"`
	ParentComponentExecuteId string                 `json:"parent_component_execute_id"`
	Version                  string                 `json:"version"`
	ExecutedBy               *userinfo.FullUserInfo `json:"executed_by"`
	StartedAt                int64                  `json:"started_at"`
	EndedAt                  int64                  `json:"ended_at"`
	CostTime                 int64                  `json:"cost_time"`
	Status                   string                 `json:"status"`
}

type ListInterfaceRecordReq struct {
	ProjectId   string           `json:"project_id"`
	InterfaceId string           `json:"interface_id"`
	Pagination  *api.Pagination  `json:"pagination,omitempty,optional"`
	Sort        []*api.SortField `json:"sort,omitempty,optional"`
}

type ListInterfaceRecordResp struct {
	CurrentPage uint64                     `json:"current_page"`
	PageSize    uint64                     `json:"page_size"`
	TotalCount  uint64                     `json:"total_count"`
	TotalPage   uint64                     `json:"total_page"`
	Items       []*InterfaceCaseRecordItem `json:"items"`
}

type InterfaceCaseRecordItem struct {
	TaskId             string                 `json:"task_id"`
	ExecuteId          string                 `json:"execute_id"`
	ProjectId          string                 `json:"project_id"`
	InterfaceExecuteId string                 `json:"interface_execute_id"`
	InterfaceId        string                 `json:"interface_id"`
	InterfaceName      string                 `json:"interface_name"`
	ExecutedBy         *userinfo.FullUserInfo `json:"executed_by"`
	StartedAt          int64                  `json:"started_at"`
	EndedAt            int64                  `json:"ended_at"`
	CostTime           int64                  `json:"cost_time"`
	TotalCase          int64                  `json:"total_case"`
	SuccessCase        int64                  `json:"success_case"`
	FailureCase        int64                  `json:"failure_case"`
	Status             string                 `json:"status"`
	Cleaned            int64                  `json:"cleaned"`
}

type GetInterfaceRecordReq struct {
	TaskId             string `form:"task_id"`
	ProjectId          string `form:"project_id"`
	ExecuteId          string `form:"execute_id"`
	InterfaceExecuteId string `form:"interface_execute_id"`
}

type GetInterfaceRecordResp struct {
	CostTime      int64                       `json:"cost_time"`
	ExecutedBy    *userinfo.FullUserInfo      `json:"executed_by"`
	StartedAt     int64                       `json:"started_at"`
	EndedAt       int64                       `json:"ended_at"`
	TotalCase     int64                       `json:"total_case"`
	SuccessCase   int64                       `json:"success_case"`
	FailureCase   int64                       `json:"failure_case"`
	InterfaceId   string                      `json:"interface_id"`
	InterfaceName string                      `json:"interface_name"`
	Content       structpb.Struct             `json:"content"`
	GeneralConfig types.ApiGeneralConfig      `json:"general_config"`
	AccountConfig []types.ApiAccountConfig    `json:"account_config"`
	CaseItems     []*InterfaceCaseRecordItem2 `json:"case_items"`
}

type InterfaceCaseRecordItem2 struct {
	TaskId        string                 `json:"task_id"`
	ExecuteId     string                 `json:"execute_id"`
	ProjectId     string                 `json:"project_id"`
	CaseExecuteId string                 `json:"case_execute_id"`
	ExecutedBy    *userinfo.FullUserInfo `json:"executed_by"`
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`
	CaseId        string                 `json:"case_id"`
	CaseName      string                 `json:"case_name"`
	Version       string                 `json:"version"`
	StartedAt     int64                  `json:"started_at"`
	EndedAt       int64                  `json:"ended_at"`
	CostTime      int64                  `json:"cost_time"`
	Status        string                 `json:"status"`
}

type ListSuiteRecordReq struct {
	ProjectId  string           `json:"project_id"`
	SuiteId    string           `json:"suite_id"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional"`
	Sort       []*api.SortField `json:"sort,omitempty,optional"`
}

type ListSuiteRecordResp struct {
	CurrentPage uint64                 `json:"current_page"`
	PageSize    uint64                 `json:"page_size"`
	TotalCount  uint64                 `json:"total_count"`
	TotalPage   uint64                 `json:"total_page"`
	Items       []*SuiteCaseRecordItem `json:"items"`
}

type SuiteCaseRecordItem struct {
	TaskId         string                 `json:"task_id"`
	ExecuteId      string                 `json:"execute_id"`
	ProjectId      string                 `json:"project_id"`
	SuiteExecuteId string                 `json:"suite_execute_id"`
	SuiteId        string                 `json:"suite_id"`
	SuiteName      string                 `json:"suite_name"`
	ExecutedBy     *userinfo.FullUserInfo `json:"executed_by"`
	StartedAt      int64                  `json:"started_at"`
	EndedAt        int64                  `json:"ended_at"`
	CostTime       int64                  `json:"cost_time"`
	TotalCase      int64                  `json:"total_case"`
	SuccessCase    int64                  `json:"success_case"`
	FailureCase    int64                  `json:"failure_case"`
	Status         string                 `json:"status"`
	Cleaned        int64                  `json:"cleaned"`
}

type GetSuiteRecordReq struct {
	TaskId         string `form:"task_id"`
	ProjectId      string `form:"project_id"`
	ExecuteId      string `form:"execute_id"`
	SuiteExecuteId string `form:"suite_execute_id"`
}

type GetSuiteRecordResp struct {
	CostTime      int64                    `json:"cost_time"`
	ExecutedBy    *userinfo.FullUserInfo   `json:"executed_by"`
	StartedAt     int64                    `json:"started_at"`
	EndedAt       int64                    `json:"ended_at"`
	TotalCase     int64                    `json:"total_case"`
	SuccessCase   int64                    `json:"success_case"`
	FailureCase   int64                    `json:"failure_case"`
	SuiteId       string                   `json:"suite_id"`
	SuiteName     string                   `json:"suite_name"`
	Content       structpb.Struct          `json:"content"`
	GeneralConfig types.ApiGeneralConfig   `json:"general_config"`
	AccountConfig []types.ApiAccountConfig `json:"account_config"`
	CaseItems     []*SuiteCaseRecordItem2  `json:"case_items"`
}

type SuiteCaseRecordItem2 struct {
	TaskId        string                 `json:"task_id"`
	ExecuteId     string                 `json:"execute_id"`
	ProjectId     string                 `json:"project_id"`
	CaseExecuteId string                 `json:"case_execute_id"`
	ExecutedBy    *userinfo.FullUserInfo `json:"executed_by"`
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`
	CaseType      string                 `json:"case_type"`
	CaseId        string                 `json:"case_id"`
	CaseName      string                 `json:"case_name"`
	Version       string                 `json:"version"`
	StartedAt     int64                  `json:"started_at"`
	EndedAt       int64                  `json:"ended_at"`
	CostTime      int64                  `json:"cost_time"`
	Status        string                 `json:"status"`
}

type ListPlanRecordReq struct {
	ProjectId  string           `json:"project_id"`
	PlanId     string           `json:"plan_id"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional"`
	Sort       []*api.SortField `json:"sort,omitempty,optional"`
}

type ListPlanRecordResp struct {
	CurrentPage uint64            `json:"current_page"`
	PageSize    uint64            `json:"page_size"`
	TotalCount  uint64            `json:"total_count"`
	TotalPage   uint64            `json:"total_page"`
	Items       []*PlanRecordItem `json:"items"`
}

type PlanRecordItem struct {
	TaskId        string                 `json:"task_id"`
	ExecuteId     string                 `json:"execute_id"`
	ProjectId     string                 `json:"project_id"`
	PlanExecuteId string                 `json:"plan_execute_id"`
	Version       string                 `json:"version"`
	ExecutedBy    *userinfo.FullUserInfo `json:"executed_by"`
	TotalSuite    int64                  `json:"total_suite"`
	SuccessSuite  int64                  `json:"success_suite"`
	FailureSuite  int64                  `json:"failure_suite"`
	TotalCase     int64                  `json:"total_case"`
	SuccessCase   int64                  `json:"success_case"`
	FailureCase   int64                  `json:"failure_case"`
	StartedAt     int64                  `json:"started_at"`
	EndedAt       int64                  `json:"ended_at"`
	CostTime      int64                  `json:"cost_time"`
	Status        string                 `json:"status"`
	Cleaned       int64                  `json:"cleaned"`
	PurposeType   string                 `json:"purpose_type"`
	Type          string                 `json:"type"`
}

type GetPlanRecordReq struct {
	TaskId        string `form:"task_id"`
	ProjectId     string `form:"project_id"`
	ExecuteId     string `form:"execute_id"`
	PlanExecuteId string `form:"plan_execute_id"`
}

type GetPlanRecordResp struct {
	CostTime               int64                    `json:"cost_time"`
	ExecutedBy             *userinfo.FullUserInfo   `json:"executed_by"`
	StartedAt              int64                    `json:"started_at"`
	EndedAt                int64                    `json:"ended_at"`
	TotalSuite             int64                    `json:"total_suite"`
	SuccessSuite           int64                    `json:"success_suite"`
	FailureSuite           int64                    `json:"failure_suite"`
	TotalCase              int64                    `json:"total_case"`
	SuccessCase            int64                    `json:"success_case"`
	FailureCase            int64                    `json:"failure_case"`
	PlanId                 string                   `json:"plan_id"`
	PlanName               string                   `json:"plan_name"`
	PurposeType            string                   `json:"purpose_type"`
	Content                structpb.Struct          `json:"content"`
	GeneralConfig          types.ApiGeneralConfig   `json:"general_config"`
	AccountConfig          []types.ApiAccountConfig `json:"account_config"`
	SuiteItems             []*SuiteItem             `json:"suite_items"`
	InterfaceDocumentItems []*InterfaceDocumentItem `json:"interface_document_items"`
	ServiceItems           []*ServiceItem           `json:"service_items"`
	Type                   string                   `json:"type"`
}

type SuiteItem struct {
	TaskId         string `json:"task_id"`
	ExecuteId      string `json:"execute_id"`
	ProjectId      string `json:"project_id"`
	SuiteExecuteId string `json:"suite_execute_id"`
	SuiteId        string `json:"suite_id"`
	SuiteName      string `json:"suite_name"`
	TotalCase      int64  `json:"total_case"`
	SuccessCase    int64  `json:"success_case"`
	FailureCase    int64  `json:"failure_case"`
	StartedAt      int64  `json:"started_at"`
	EndedAt        int64  `json:"ended_at"`
	CostTime       int64  `json:"cost_time"`
	Status         string `json:"status"`
}

type InterfaceDocumentItem struct {
	TaskId             string `json:"task_id"`
	ExecuteId          string `json:"execute_id"`
	ProjectId          string `json:"project_id"`
	InterfaceExecuteId string `json:"interface_execute_id"`
	InterfaceId        string `json:"interface_id"`
	InterfaceName      string `json:"interface_name"`
	TotalCase          int64  `json:"total_case"`
	SuccessCase        int64  `json:"success_case"`
	FailureCase        int64  `json:"failure_case"`
	StartedAt          int64  `json:"started_at"`
	EndedAt            int64  `json:"ended_at"`
	CostTime           int64  `json:"cost_time"`
	Status             string `json:"status"`
}

type GetPlanTimeScaleReq struct {
	TaskId        string `form:"task_id"`
	ProjectId     string `form:"project_id"`
	ExecuteId     string `form:"execute_id"`
	PlanExecuteId string `form:"plan_execute_id"`
}

type GetPlanTimeScaleResp struct {
	SuiteItems []*SuiteItem2 `json:"suite_items"`
}

type SuiteItem2 struct {
	SuiteId   string       `json:"suite_id"`
	SuiteName string       `json:"suite_name"`
	CaseItems []*CaseItem2 `json:"case_items"`
}

type CaseItem2 struct {
	CaseId    string `json:"case_id"`
	CaseName  string `json:"case_name"`
	StartedAt int64  `json:"started_at"`
	EndedAt   int64  `json:"ended_at"`
	CostTime  int64  `json:"cost_time"`
	Status    string `json:"status"`
}

type GetPlanSummaryReq struct {
	ProjectId string `form:"project_id"`
	TaskId    string `form:"task_id"`
	ExecuteId string `form:"execute_id"`
	PlanType  int64  `form:"plan_type,omitempty,optional,default=4" zh:"执行对象的类型"`
}

type GetPlanSummaryResp struct {
	Status string  `json:"status"`
	Record *Record `json:"record"`
}

type Record struct {
	TotalCase   int64  `json:"total_case"`
	SuccessCase int64  `json:"success_case"`
	PassRate    string `json:"pass_rate"`
	PassingRate string `json:"passing_rate"`
	ReportPath  string `json:"report_path"`
}

type ListServiceRecordReq struct {
	ProjectId  string           `json:"project_id"`
	ServiceId  string           `json:"service_id"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional"`
	Sort       []*api.SortField `json:"sort,omitempty,optional"`
}

type ListServiceRecordResp struct {
	CurrentPage uint64                   `json:"current_page"`
	PageSize    uint64                   `json:"page_size"`
	TotalCount  uint64                   `json:"total_count"`
	TotalPage   uint64                   `json:"total_page"`
	Items       []*ServiceCaseRecordItem `json:"items"`
}

type ServiceCaseRecordItem struct {
	TaskId           string                 `json:"task_id"`
	ExecuteId        string                 `json:"execute_id"`
	ProjectId        string                 `json:"project_id"`
	ServiceExecuteId string                 `json:"service_execute_id"`
	ServiceId        string                 `json:"service_id"`
	ServiceName      string                 `json:"service_name"`
	ExecutedBy       *userinfo.FullUserInfo `json:"executed_by"`
	StartedAt        int64                  `json:"started_at"`
	EndedAt          int64                  `json:"ended_at"`
	CostTime         int64                  `json:"cost_time"`
	TotalCase        int64                  `json:"total_case"`
	SuccessCase      int64                  `json:"success_case"`
	FailureCase      int64                  `json:"failure_case"`
	Status           string                 `json:"status"`
	Cleaned          int64                  `json:"cleaned"`
}

type GetServiceRecordReq struct {
	TaskId           string `form:"task_id"`
	ProjectId        string `form:"project_id"`
	ExecuteId        string `form:"execute_id"`
	ServiceExecuteId string `form:"service_execute_id"`
}

type GetServiceRecordResp struct {
	CostTime      int64                     `json:"cost_time"`
	ExecutedBy    *userinfo.FullUserInfo    `json:"executed_by"`
	StartedAt     int64                     `json:"started_at"`
	EndedAt       int64                     `json:"ended_at"`
	TotalCase     int64                     `json:"total_case"`
	SuccessCase   int64                     `json:"success_case"`
	FailureCase   int64                     `json:"failure_case"`
	ServiceId     string                    `json:"service_id"`
	ServiceName   string                    `json:"service_name"`
	Content       string                    `json:"content"`
	GeneralConfig string                    `json:"general_config"`
	AccountConfig string                    `json:"account_config"`
	CaseItems     []*ServiceCaseRecordItem2 `json:"case_items"`
}

type ServiceCaseRecordItem2 struct {
	TaskId        string                 `json:"task_id"`
	ExecuteId     string                 `json:"execute_id"`
	ProjectId     string                 `json:"project_id"`
	CaseExecuteId string                 `json:"case_execute_id"`
	ExecutedBy    *userinfo.FullUserInfo `json:"executed_by"`
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`
	CaseId        string                 `json:"case_id"`
	CaseName      string                 `json:"case_name"`
	Version       string                 `json:"version"`
	StartedAt     int64                  `json:"started_at"`
	EndedAt       int64                  `json:"ended_at"`
	CostTime      int64                  `json:"cost_time"`
	Status        string                 `json:"status"`
}

type ServiceItem struct {
	TaskId           string `json:"task_id"`
	ExecuteId        string `json:"execute_id"`
	ProjectId        string `json:"project_id"`
	ServiceExecuteId string `json:"service_execute_id"`
	ServiceId        string `json:"service_id"`
	ServiceName      string `json:"service_name"`
	TotalCase        int64  `json:"total_case"`
	SuccessCase      int64  `json:"success_case"`
	FailureCase      int64  `json:"failure_case"`
	StartedAt        int64  `json:"started_at"`
	EndedAt          int64  `json:"ended_at"`
	CostTime         int64  `json:"cost_time"`
	Status           string `json:"status"`
}

type ListFailCaseRecordForPlanReq struct {
	ProjectId  string          `json:"project_id"`
	CaseId     string          `json:"case_id"`
	CaseType   string          `json:"case_type"`
	Pagination *api.Pagination `json:"pagination,omitempty,optional"`
}

type ListFailCaseRecordForPlanResp struct {
	CurrentPage uint64                       `json:"current_page"`
	PageSize    uint64                       `json:"page_size"`
	TotalCount  uint64                       `json:"total_count"`
	TotalPage   uint64                       `json:"total_page"`
	Items       []*FailCaseItemForPlanRecord `json:"items"`
}

type FailCaseItemForPlanRecord struct {
	TaskId        string                 `json:"task_id"`
	ExecuteId     string                 `json:"execute_id"`
	ProjectId     string                 `json:"project_id"`
	PlanId        string                 `json:"plan_id"`
	PlanName      string                 `json:"plan_name"`
	PlanExecuteId string                 `json:"plan_execute_id"`
	Version       string                 `json:"version"`
	ExecutedBy    *userinfo.FullUserInfo `json:"executed_by"`
	TotalSuite    int64                  `json:"total_suite"`
	SuccessSuite  int64                  `json:"success_suite"`
	FailureSuite  int64                  `json:"failure_suite"`
	TotalCase     int64                  `json:"total_case"`
	SuccessCase   int64                  `json:"success_case"`
	FailureCase   int64                  `json:"failure_case"`
	StartedAt     int64                  `json:"started_at"`
	EndedAt       int64                  `json:"ended_at"`
	CostTime      int64                  `json:"cost_time"`
	Status        string                 `json:"status"`
	Cleaned       int64                  `json:"cleaned"`
	PurposeType   string                 `json:"purpose_type"`
	Type          string                 `json:"type"`
}
