DROP TABLE IF EXISTS `execution_record`;
CREATE TABLE `execution_record` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `task_id` varchar(64) NOT NULL COMMENT '任务id',
    `project_id` varchar(64) NOT NULL COMMENT '项目id',
    `execute_id` varchar(64) NOT NULL COMMENT '执行id',
    `execute_type` varchar(64) NOT NULL COMMENT '执行类型',
    `general_config` json DEFAULT NULL COMMENT '通用配置',
    `account_config` json DEFAULT NULL COMMENT '池账号配置',
    `component_id` varchar(64) NOT NULL COMMENT '组件id',
    `component_name` varchar(64) NOT NULL COMMENT '组件名称',
    `component_type` varchar(64) NOT NULL COMMENT '组件类型',
    `component_execute_id` varchar(64) NOT NULL COMMENT '组件执行id',
    `parent_component_id` varchar(64) DEFAULT NULL COMMENT '父组件id',
    `parent_component_execute_id` varchar(64) DEFAULT NULL COMMENT '父组件执行id',
    `version` varchar(64) NOT NULL COMMENT '组件版本',
    `times` int NOT NULL DEFAULT '1' COMMENT '第几次执行（兼容循环组件）',
    `status` varchar(64) DEFAULT NULL COMMENT '执行状态（结果）',
    `content` json DEFAULT NULL COMMENT '执行数据详情',
    `is_root` int NOT NULL DEFAULT '0' COMMENT '是否是根节点, 0:不是根节点； 1:是跟节点',
    `execute_user_id` varchar(64) NOT NULL COMMENT '执行人id',
    `started_at` bigint NOT NULL COMMENT '开始执行的时间(戳)',
    `ended_at` bigint DEFAULT NULL COMMENT '结束执行的时间(戳)',
    `cost_time` bigint DEFAULT '0' COMMENT '执行耗时(毫秒)',
    `callback` json DEFAULT NULL COMMENT 'callback日志',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_record_index` (`task_id`,`project_id`,`execute_id`,`component_execute_id`,`times`,`component_type`) USING BTREE,
    KEY `unique_parent_record_index` (`task_id`,`project_id`,`parent_component_execute_id`,`times`) USING BTREE,
    KEY `component_id_index` (`project_id`,`component_type`,`component_id`) USING BTREE,
    KEY `is_root_index` (`is_root`,`project_id`,`component_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14390 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='执行记录表';


DROP TABLE IF EXISTS `interface_execution_record`;
CREATE TABLE `interface_execution_record` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `task_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务id',
    `project_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目id',
    `execute_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行id',
    `execute_type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行类型',
    `general_config` json DEFAULT NULL COMMENT '通用配置',
    `account_config` json DEFAULT NULL COMMENT '池账号配置',
    `interface_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口id',
    `interface_execute_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口执行id, 作为用例执行表的parent_component_execute_id',
    `interface_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口名称',
    `plan_execute_id` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划执行id, 关联表plan_execution_record的plan_execute_id',
    `total_case` int NOT NULL COMMENT '接口总用例个数',
    `success_case` int NOT NULL DEFAULT '0' COMMENT '执行成功用例个数',
    `failure_case` int NOT NULL DEFAULT '0' COMMENT '执行失败用例个数',
    `status` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行状态（结果）',
    `content` json DEFAULT NULL COMMENT '执行数据详情',
    `execute_user_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行人id',
    `started_at` bigint NOT NULL COMMENT '开始执行的时间(戳)',
    `ended_at` bigint DEFAULT NULL COMMENT '结束执行的时间(戳)',
    `cost_time` bigint DEFAULT '0' COMMENT '执行耗时(毫秒)',
    `callback` json DEFAULT NULL COMMENT 'callback日志',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_record_index` (`task_id`,`project_id`,`execute_id`,`interface_execute_id`) USING BTREE,
    KEY `ix_interface_id` (`project_id`,`interface_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='接口执行记录表';

DROP TABLE IF EXISTS `suite_execution_record`;
CREATE TABLE `suite_execution_record` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `task_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务id',
    `project_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目id',
    `execute_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行id',
    `execute_type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行类型',
    `general_config` json DEFAULT NULL COMMENT '通用配置',
    `account_config` json DEFAULT NULL COMMENT '池账号配置',
    `suite_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '集合id',
    `suite_execute_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '集合执行id, 作为用例执行表的parent_component_execute_id',
    `suite_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '集合名称',
    `plan_execute_id` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划执行id, 关联表plan_execution_record的plan_execute_id',
    `total_case` int NOT NULL COMMENT '集合总用例个数',
    `success_case` int NOT NULL DEFAULT '0' COMMENT '执行成功用例个数',
    `failure_case` int NOT NULL DEFAULT '0' COMMENT '执行失败用例个数',
    `status` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行状态（结果）',
    `content` json DEFAULT NULL COMMENT '执行数据详情',
    `execute_user_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行人id',
    `started_at` bigint NOT NULL COMMENT '开始执行的时间(戳)',
    `ended_at` bigint DEFAULT NULL COMMENT '结束执行的时间(戳)',
    `cost_time` bigint DEFAULT '0' COMMENT '执行耗时(毫秒)',
    `callback` json DEFAULT NULL COMMENT 'callback日志',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_record_index` (`task_id`,`project_id`,`execute_id`,`suite_execute_id`) USING BTREE,
    KEY `ix_suite_id` (`project_id`,`suite_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='集合执行记录表';


DROP TABLE IF EXISTS `plan_execution_record`;
CREATE TABLE `plan_execution_record` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `task_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务id',
    `project_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目id',
    `execute_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行id',
    `execute_type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行类型',
    `general_config` json DEFAULT NULL COMMENT '通用配置',
    `account_config` json DEFAULT NULL COMMENT '池账号配置',
    `plan_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划id',
    `plan_execute_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划执行id, 作为接口执行表或集合执行表的parent_component_execute_id',
    `plan_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划名称',
    `total_suite` int NOT NULL COMMENT '计划总集合（或接口）个数',
    `success_suite` int NOT NULL DEFAULT '0' COMMENT '执行成功集合（或接口）个数',
    `failure_suite` int NOT NULL DEFAULT '0' COMMENT '执行失败集合（或接口）个数',
    `status` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行状态（结果）',
    `content` json DEFAULT NULL COMMENT '执行数据详情',
    `execute_user_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行人id',
    `started_at` bigint NOT NULL COMMENT '开始执行的时间(戳)',
    `ended_at` bigint DEFAULT NULL COMMENT '结束执行的时间(戳)',
    `cost_time` bigint DEFAULT '0' COMMENT '执行耗时(毫秒)',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_record_index` (`task_id`,`project_id`,`execute_id`,`plan_execute_id`) USING BTREE,
    KEY `ix_plan_id` (`project_id`,`plan_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='计划执行记录表';