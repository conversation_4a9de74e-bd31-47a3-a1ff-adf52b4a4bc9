alter table reporter.ui_plan_execution_record
    add total_case int default 0 not null comment '测试用例总数' after success_suite;

alter table reporter.ui_plan_execution_record
    add finished_case int default 0 not null comment '执行完成的测试用例数' after total_case;

alter table reporter.ui_plan_execution_record
    add success_case int default 0 not null comment '执行成功的测试用例数' after finished_case;

alter table reporter.plan_execution_record
    add total_case int default 0 not null comment '测试用例总数' after failure_suite;

alter table reporter.plan_execution_record
    add success_case int default 0 not null comment '执行成功的测试用例数' after total_case;

alter table reporter.plan_execution_record
    add failure_case int default 0 not null comment '执行完成的测试用例数' after success_case;

alter table reporter.ui_plan_execution_record
    add execute_data json null comment '执行数据' after finished;

