-- 优化执行记录清理逻辑
-- plan_execution_record
DROP INDEX `ix_per_created_at_cleaned` ON `plan_execution_record`;
CREATE INDEX `ix_per_cleaned_created_at` ON `plan_execution_record` (`cleaned`, `created_at`);

-- interface_execution_record
DROP INDEX `ix_interface_id` ON `interface_execution_record`;
CREATE INDEX `ix_ier_project_id_interface_id` ON `interface_execution_record` (`project_id`, `interface_id`);

DROP INDEX `idx_suite_day_index` ON `interface_execution_record`;
CREATE INDEX `ix_ier_project_id_started_at` ON `interface_execution_record` (`project_id`, `started_at`);

DROP INDEX `interface_execution_record_execute_type_index` ON `interface_execution_record`;
DROP INDEX `idx_interface_execution_record_execute_type_index` ON `interface_execution_record`;
CREATE INDEX `ix_ier_execute_type` ON `interface_execution_record` (`execute_type`);

CREATE INDEX `ix_ier_cleaned_created_at` ON `interface_execution_record` (`cleaned`, `created_at`);

-- suite_execution_record
DROP INDEX `ix_suite_id` ON `suite_execution_record`;
CREATE INDEX `ix_ser_project_id_suite_id` ON `suite_execution_record` (`project_id`, `suite_id`);

DROP INDEX `idx_suite_day_index` ON `suite_execution_record`;
CREATE INDEX `ix_ser_project_id_started_at` ON `suite_execution_record` (`project_id`, `started_at`);

DROP INDEX `idx_suite_execution_record_execute_type_index` ON `suite_execution_record`;
CREATE INDEX `ix_ser_execute_type` ON `suite_execution_record` (`execute_type`);

CREATE INDEX `ix_ser_cleaned_created_at` ON `suite_execution_record` (`cleaned`, `created_at`);

-- service_execution_record
DROP INDEX `ix_service_id` ON `service_execution_record`;
CREATE INDEX `ix_ser_project_id_service_id` ON `service_execution_record` (`project_id`, `service_id`);

DROP INDEX `idx_service_day_index` ON `service_execution_record`;
CREATE INDEX `ix_ser_project_id_started_at` ON `service_execution_record` (`project_id`, `started_at`);

DROP INDEX `idx_service_execution_record_execute_type_index` ON `service_execution_record`;
CREATE INDEX `ix_ser_execute_type` ON `service_execution_record` (`execute_type`);

CREATE INDEX `ix_ser_cleaned_created_at` ON `service_execution_record` (`cleaned`, `created_at`);

-- execution_record
CREATE INDEX `ix_er_cleaned_created_at` ON `execution_record` (`cleaned`, `created_at`);

DROP INDEX `idx_case_day_index` ON `execution_record`;
CREATE INDEX `ix_er_project_type_root_started` ON `execution_record` (`project_id`, `execute_type`, `is_root`, `started_at`);

DROP INDEX `is_root_index` ON `execution_record`;
CREATE INDEX `ix_er_project_component_type_root_version` ON `execution_record` (`project_id`, `component_id`, `execute_type`, `is_root`, `version`);

DROP INDEX `component_id_index` ON `execution_record`;
CREATE INDEX `ix_er_project_component` ON `execution_record` (`project_id`, `component_type`, `component_id`);

DROP INDEX `idx_execution_record_execute_type_index` ON `execution_record`;
CREATE INDEX `ix_er_type_root_cleaned_created` ON `execution_record` (`execute_type`, `is_root`, `cleaned`, `created_at`);


-- perf_plan_execution_record
CREATE INDEX `ix_pper_cleaned_created_at` ON `perf_plan_execution_record` (`cleaned`, `created_at`);

-- perf_suite_execution_record
CREATE INDEX `ix_pser_cleaned_created_at` ON `perf_suite_execution_record` (`cleaned`, `created_at`);

-- perf_case_execution_record
CREATE INDEX `ix_pcer_cleaned_created_at` ON `perf_case_execution_record` (`cleaned`, `created_at`);

-- ui_plan_execution_record
DROP INDEX `ix_plan_id` ON `ui_plan_execution_record`;
CREATE INDEX `ix_uper_project_id_plan_id` ON `ui_plan_execution_record` (`project_id`, `plan_id`);

CREATE INDEX `ix_uper_cleaned_created_at` ON `ui_plan_execution_record` (`cleaned`, `created_at`);

-- ui_suite_execution_record
DROP INDEX `ix_suite_id` ON `ui_suite_execution_record`;
CREATE INDEX `ix_user_project_id_suite_id` ON `ui_suite_execution_record` (`project_id`, `suite_id`);

CREATE INDEX `ix_user_cleaned_created_at` ON `ui_suite_execution_record` (`cleaned`, `created_at`);

-- ui_case_execution_record
DROP INDEX `case_id_index` ON `ui_case_execution_record`;
CREATE INDEX `ix_ucer_project_id_case_id` ON `ui_case_execution_record` (`project_id`, `case_id`);

CREATE INDEX `ix_ucer_cleaned_created_at` ON `ui_case_execution_record` (`cleaned`, `created_at`);


-- 精准测试通知增加审批人信息
ALTER TABLE `plan_execution_record` ADD `approved_by` JSON NULL COMMENT '审批人' AFTER `executed_by`;

-- 接口测试报告增加负责人信息
ALTER TABLE `execution_record` ADD `maintained_by` VARCHAR(64) NOT NULL COMMENT '负责人' AFTER `executed_by`;