DROP TABLE IF EXISTS `ui_case_execution_record`;
CREATE TABLE `ui_case_execution_record` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `task_id` varchar(64) NOT NULL COMMENT '任务id',
    `project_id` varchar(64) NOT NULL COMMENT '项目id',
    `execute_id` varchar(64) NOT NULL COMMENT '用例执行id',
    `case_id` varchar(64) NOT NULL COMMENT '用例id',
    `case_name` varchar(64) NOT NULL COMMENT '用例名称',
    `suite_execute_id` varchar(64) NOT NULL COMMENT '所属集合执行id',
    `status` varchar(64) DEFAULT NULL COMMENT '执行状态（结果）',
    `content` json DEFAULT NULL COMMENT '执行数据详情',
    `executed_by` varchar(64) NOT NULL COMMENT '执行人',
    `started_at` bigint NOT NULL COMMENT '开始执行的时间(戳)',
    `ended_at` bigint DEFAULT NULL COMMENT '结束执行的时间(戳)',
    `cost_time` bigint DEFAULT '0' COMMENT '执行耗时(毫秒)',
    `callback` json DEFAULT NULL COMMENT 'callback日志',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    `cleaned` tinyint(1) DEFAULT '0' COMMENT '是否已清理过记录',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_record_index` (
        `task_id`,
        `project_id`,
        `execute_id`
        ) USING BTREE,
    KEY `unique_parent_record_index` (
        `task_id`,
        `project_id`,
        `suite_execute_id`
        ) USING BTREE,
    KEY `case_id_index` (`project_id`, `case_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'UI用例执行记录表' ROW_FORMAT = DYNAMIC;

DROP TABLE IF EXISTS `ui_suite_execution_record`;
CREATE TABLE `ui_suite_execution_record` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务id',
    `project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目id',
    `execute_id` varchar(64) NOT NULL COMMENT '集合执行id, 作为用例执行表的suite_execute_id',
    `suite_id` varchar(64) NOT NULL COMMENT '集合id',
    `suite_name` varchar(64) NOT NULL COMMENT '集合名称',
    `plan_execute_id` varchar(64) NOT NULL COMMENT '计划执行id, 关联表ui_plan_execution_record的execute_id',
    `total_case` int NOT NULL COMMENT '集合总用例个数',
    `success_case` int NOT NULL DEFAULT '0' COMMENT '执行成功用例个数',
    `failure_case` int NOT NULL DEFAULT '0' COMMENT '执行失败用例个数',
    `status` varchar(64) DEFAULT NULL COMMENT '执行状态（结果）',
    `content` json DEFAULT NULL COMMENT '执行数据详情',
    `executed_by` varchar(64) NOT NULL COMMENT '执行人',
    `started_at` bigint NOT NULL COMMENT '开始执行的时间(戳)',
    `ended_at` bigint DEFAULT NULL COMMENT '结束执行的时间(戳)',
    `cost_time` bigint DEFAULT '0' COMMENT '执行耗时(毫秒)',
    `callback` json DEFAULT NULL COMMENT 'callback日志',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    `cleaned` tinyint DEFAULT '0' COMMENT '是否已清理过记录',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_record_index` (
     `task_id`,
     `project_id`,
     `execute_id`
     ) USING BTREE,
    KEY `ix_suite_id` (`project_id`, `suite_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46138 DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = 'UI集合执行记录表';

DROP TABLE IF EXISTS `ui_plan_execution_record`;
CREATE TABLE `reporter`.`ui_plan_execution_record`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目id',
    `plan_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划id',
    `plan_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划名称',
    `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务id',
    `execute_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行id',
    `status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行状态（结果）',
    `cost_time` bigint NULL DEFAULT 0 COMMENT '执行耗时(豪秒)',
    `executed_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行人id',
    `started_at` bigint NOT NULL COMMENT '开始执行的时间(戳)',
    `ended_at` bigint NULL DEFAULT NULL COMMENT '结束执行的时间(戳)',
    `total_suite` int NOT NULL COMMENT '测试集合总数',
    `finished_suite` int NOT NULL DEFAULT 0 COMMENT '执行完的测试集合数',
    `success_suite` int NOT NULL DEFAULT 0 COMMENT '执行成功的测试集合数',
    `content` json NULL COMMENT '执行数据详情',
    `finished` tinyint(1) NULL DEFAULT 0 COMMENT '计划是否执行完，0表示未执行完，1表示执行完',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    `cleaned` tinyint DEFAULT 0 COMMENT '是否已清理过记录',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `unique_record_index`(
        `task_id`,
        `project_id`,
        `execute_id`
        ) USING BTREE,
    INDEX `ix_plan_id`(`project_id`, `plan_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'UI计划执行记录表' ROW_FORMAT = DYNAMIC;