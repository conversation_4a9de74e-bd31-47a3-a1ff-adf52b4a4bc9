USE
`reporter`;

CREATE TABLE IF NOT EXISTS `case_fail_for_plan_stat`
(
    `id`              int         NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`      varchar(64) NOT NULL COMMENT '项目ID',
    `task_id`         varchar(64) NOT NULL COMMENT '任务ID',
    `plan_id`         varchar(64) NOT NULL COMMENT '计划ID',
    `case_id`         varchar(64) NOT NULL COMMENT '用例ID',
    `execute_id`      varchar(64) NOT NULL COMMENT '执行ID',
    `case_type`       varchar(64) NOT NULL COMMENT '计划类型[API_CASE，INTERFACE_CASE]',
    `fail_reason_url` varchar(255)         DEFAULT NULL COMMENT '失败原因-path，上传文件服务器后的地址',
    `deleted`         tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      varchar(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      varchar(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      varchar(64)          DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      timestamp NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY               `idx_cfs_pipici` (`project_id`,`plan_id`,`case_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用例失败计划统计表【一个计划可能会出现多个case_id】'

