USE
    `reporter`;
/*
select status,count(status) from ui_plan_execution_record  group by status;

Success	223
Stop	295
Started	16
Failure	1811
Panic 	10

execute_status tinyint default 0 null comment '0排队中,1执行中,2已完成,3已停止',
executed_result tinyint default 0 null comment '执行结果(1成功,2失败,3异常)',
*/

update ui_plan_execution_record
set execute_status  = 2,
    executed_result =1
where status = 'Success';

update ui_plan_execution_record
set execute_status  = 3,
    executed_result =3
where status = 'Stop';

update ui_plan_execution_record
set execute_status  = 2,
    executed_result =2
where status = 'Failure';

update ui_plan_execution_record
set execute_status  = 3,
    executed_result =3
where status = 'Panic';

update ui_plan_execution_record
set execute_status  = 1,
    executed_result =0
where status = 'Started';