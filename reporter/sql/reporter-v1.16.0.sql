CREATE TABLE `service_execution_record`
(
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `task_id` varchar(64) NOT NULL COMMENT '任务id',
    `project_id` varchar(64) NOT NULL COMMENT '项目id',
    `execute_id` varchar(64) NOT NULL COMMENT '执行id',
    `execute_type` varchar(64) NOT NULL COMMENT '执行类型',
    `general_config` json DEFAULT NULL COMMENT '通用配置',
    `account_config` json DEFAULT NULL COMMENT '池账号配置',
    `service_id` varchar(64) NOT NULL COMMENT '服务id',
    `service_execute_id` varchar(64) NOT NULL COMMENT '服务执行id, 作为用例执行表的parent_component_execute_id',
    `service_name` varchar(64) NOT NULL COMMENT '服务名称',
    `plan_execute_id` varchar(64) DEFAULT NULL COMMENT '计划执行id, 关联表plan_execution_record的plan_execute_id',
    `total_case` int NOT NULL COMMENT '集合总用例个数',
    `success_case` int NOT NULL DEFAULT '0' COMMENT '执行成功用例个数',
    `failure_case` int NOT NULL DEFAULT '0' COMMENT '执行失败用例个数',
    `status` varchar(64) DEFAULT NULL COMMENT '执行状态（结果）',
    `content` json DEFAULT NULL COMMENT '执行数据详情',
    `executed_by` varchar(64) NOT NULL COMMENT '执行人',
    `started_at` bigint NOT NULL COMMENT '开始执行的时间(戳)',
    `ended_at` bigint DEFAULT NULL COMMENT '结束执行的时间(戳)',
    `cost_time` bigint DEFAULT '0' COMMENT '执行耗时(毫秒)',
    `callback` json DEFAULT NULL COMMENT 'callback日志',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    `cleaned` tinyint DEFAULT '0' COMMENT '是否已清理过记录',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_record_index` (`task_id`,`project_id`,`execute_id`,`service_execute_id`) USING BTREE,
    KEY `ix_service_id` (`project_id`,`service_id`) USING BTREE,
    KEY `idx_service_execution_record_execute_type_index` (`execute_type`),
    KEY `idx_service_day_index` (`project_id`,`started_at`)
) ENGINE=InnoDB AUTO_INCREMENT=108916 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='服务执行记录表';


alter table plan_execution_record
    add trigger_mode varchar(64) NOT NULL DEFAULT '' comment '触发模式' after plan_name;
alter table ui_plan_execution_record
    add trigger_mode varchar(64) NOT NULL DEFAULT '' comment '触发模式' after plan_name;
