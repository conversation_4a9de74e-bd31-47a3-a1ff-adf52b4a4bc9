alter table execution_record change execute_user_id executed_by var<PERSON><PERSON>(64) not null comment '执行人';
alter table interface_execution_record change execute_user_id executed_by var<PERSON><PERSON>(64) not null comment '执行人';
alter table suite_execution_record change execute_user_id executed_by var<PERSON><PERSON>(64) not null comment '执行人';
alter table plan_execution_record change execute_user_id executed_by var<PERSON>r(64) not null comment '执行人';

CREATE INDEX `interface_execution_record_execute_type_index`
    ON `reporter`.`interface_execution_record` (execute_type);

CREATE INDEX `suite_execution_record_execute_type_index`
    ON `reporter`.`suite_execution_record` (execute_type);

CREATE INDEX `execution_record_execute_type_index`
    ON `reporter`.`execution_record` (execute_type);