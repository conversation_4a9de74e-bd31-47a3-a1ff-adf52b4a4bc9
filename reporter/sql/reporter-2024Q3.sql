-- 压力测试平台化
CREATE TABLE IF NOT EXISTS `perf_case_execution_record`
(
    `id`               INT         NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`          VARCHAR(64) NOT NULL COMMENT '任务ID',
    `execute_id`       VARCHAR(64) NOT NULL COMMENT '执行ID',
    `suite_execute_id` VARCHAR(64) NOT NULL COMMENT '集合执行ID',
    `project_id`       VARCHAR(64) NOT NULL COMMENT '项目ID',
    `case_id`          VARCHAR(64) NOT NULL COMMENT '用例ID',
    `case_name`        VARCHAR(64) NOT NULL COMMENT '用例名称',
    `status`           VARCHAR(64) NULL COMMENT '执行状态（结果）',
    `cost_time`        BIGINT      NULL     DEFAULT 0 COMMENT '执行耗时（单位为毫秒）',
    `executed_by`      VARCHAR(64) NOT NULL COMMENT '执行者的用户ID',
    `started_at`       TIMESTAMP   NULL COMMENT '开始时间',
    `ended_at`         TIMESTAMP   NULL COMMENT '结束时间',
    `api_metrics`      JSON        NULL COMMENT '用例涉及的接口的指标信息',
    `err_msg`          JSON        NULL COMMENT '用例执行错误信息',
    `cleaned`          TINYINT     NOT NULL DEFAULT 0 COMMENT '清理标识（未清理、已清理）',
    `deleted`          TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`       VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`       VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`       VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pcer_task_id_execute_id_project_id` (`task_id`, `execute_id`, `project_id`),
    KEY `ix_pcer_project_id_suite_execute_id_deleted` (`project_id`, `suite_execute_id`, `deleted`),
    KEY `ix_pcer_project_id_case_id_deleted` (`project_id`, `case_id`, `deleted`),
    KEY `ix_pcer_project_id_case_name_deleted` (`project_id`, `case_name`, `deleted`),
    KEY `ix_pcer_project_id_status_deleted` (`project_id`, `status`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '压测用例执行记录表';

CREATE TABLE IF NOT EXISTS `perf_suite_execution_record`
(
    `id`              INT         NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`         VARCHAR(64) NOT NULL COMMENT '任务ID',
    `execute_id`      VARCHAR(64) NOT NULL COMMENT '执行ID',
    `plan_execute_id` VARCHAR(64) NOT NULL COMMENT '计划执行ID',
    `project_id`      VARCHAR(64) NOT NULL COMMENT '项目ID',
    `suite_id`        VARCHAR(64) NOT NULL COMMENT '集合ID',
    `suite_name`      VARCHAR(64) NOT NULL COMMENT '集合名称',
    `status`          VARCHAR(64) NULL COMMENT '执行状态（结果）',
    `cost_time`       BIGINT      NULL     DEFAULT 0 COMMENT '执行耗时（单位为毫秒）',
    `executed_by`     VARCHAR(64) NOT NULL COMMENT '执行者的用户ID',
    `started_at`      TIMESTAMP   NULL COMMENT '开始时间',
    `ended_at`        TIMESTAMP   NULL COMMENT '结束时间',
    `err_msg`         JSON        NULL COMMENT '集合执行错误信息',
    `cleaned`         TINYINT     NOT NULL DEFAULT 0 COMMENT '清理标识（未清理、已清理）',
    `deleted`         TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pser_task_id_execute_id_project_id` (`task_id`, `execute_id`, `project_id`),
    KEY `ix_pser_project_id_plan_execute_id_deleted` (`project_id`, `plan_execute_id`, `deleted`),
    KEY `ix_pser_project_id_suite_id_deleted` (`project_id`, `suite_id`, `deleted`),
    KEY `ix_pser_project_id_suite_name_deleted` (`project_id`, `suite_name`, `deleted`),
    KEY `ix_pser_project_id_status_deleted` (`project_id`, `status`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '压测集合执行记录表';

CREATE TABLE IF NOT EXISTS `perf_plan_execution_record`
(
    `id`              INT           NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`         VARCHAR(64)   NOT NULL COMMENT '任务ID',
    `execute_id`      VARCHAR(64)   NOT NULL COMMENT '执行ID',
    `project_id`      VARCHAR(64)   NOT NULL COMMENT '项目ID',
    `plan_id`         VARCHAR(64)   NOT NULL COMMENT '计划ID',
    `plan_name`       VARCHAR(64)   NOT NULL COMMENT '计划名称',
    `trigger_mode`    VARCHAR(64)   NULL     DEFAULT '' COMMENT '触发模式',
    `target_max_rps`  INT           NOT NULL DEFAULT 1 COMMENT '目标最大的RPS',
    `target_duration` INT           NOT NULL COMMENT '目标压测持续时长（单位为秒）',
    `target_env`      VARCHAR(32)   NOT NULL COMMENT '目标环境（开发环境、测试环境、预发布环境、灰度环境、生产环境）',
    `status`          VARCHAR(64)   NULL COMMENT '执行状态（结果）',
    `task_type`       VARCHAR(16)   NOT NULL COMMENT '任务类型（执行、调试）',
    `execution_mode`  VARCHAR(16)   NOT NULL COMMENT '执行方式（按时长、按次数）',
    `cost_time`       BIGINT        NULL     DEFAULT 0 COMMENT '执行耗时（单位为毫秒）',
    `monitor_url`     VARCHAR(2083) NULL     DEFAULT '' COMMENT '监控面板地址',
    `executed_by`     VARCHAR(64)   NOT NULL COMMENT '执行者的用户ID',
    `started_at`      TIMESTAMP     NULL COMMENT '开始时间',
    `ended_at`        TIMESTAMP     NULL COMMENT '结束时间',
    `api_metrics`     JSON          NULL COMMENT '计划涉及的接口的指标信息',
    `err_msg`         JSON          NULL COMMENT '计划执行错误信息',
    `cleaned`         TINYINT       NOT NULL DEFAULT 0 COMMENT '清理标识（未清理、已清理）',
    `deleted`         TINYINT       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64)   NULL COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP     NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pper_task_id_execute_id_project_id` (`task_id`, `execute_id`, `project_id`),
    KEY `ix_pper_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_pper_project_id_plan_name_deleted` (`project_id`, `plan_name`, `deleted`),
    KEY `ix_pper_project_id_status_deleted` (`project_id`, `status`, `deleted`),
    KEY `ix_pper_project_executed_by_deleted` (`project_id`, `executed_by`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '压测计划执行记录表';
