# execution_record
DROP INDEX `unique_record_index` ON `reporter`.`execution_record`;
ALTER TABLE `reporter`.`execution_record` ADD UNIQUE INDEX `unique_record_index`(`task_id`, `project_id`, `execute_id`, `component_execute_id`, `times`, `component_type`) USING BTREE;

DROP INDEX `unique_parent_record_index` ON `reporter`.`execution_record`;
ALTER TABLE `reporter`.`execution_record` ADD INDEX `unique_parent_record_index`(`task_id`, `project_id`, `parent_component_execute_id`, `times`) USING BTREE;

DROP INDEX `component_id_index` ON `reporter`.`execution_record`;
ALTER TABLE `reporter`.`execution_record` ADD INDEX `component_id_index`(`project_id`, `component_type`, `component_id`) USING BTREE;

DROP INDEX `is_root_index` ON `reporter`.`execution_record`;
ALTER TABLE `reporter`.`execution_record` ADD INDEX `is_root_index`(`project_id`, `component_id`, `execute_type`, `is_root`, `version`) USING BTREE;


# suite_execution_record
DROP INDEX `unique_record_index` ON `reporter`.`suite_execution_record`;
ALTER TABLE `reporter`.`suite_execution_record` ADD UNIQUE INDEX `unique_record_index`(`task_id`, `project_id`, `execute_id`, `suite_execute_id`) USING BTREE;

DROP INDEX `ix_suite_id` ON `reporter`.`suite_execution_record`;
ALTER TABLE `reporter`.`suite_execution_record` ADD INDEX `ix_suite_id`(`project_id`, `suite_id`) USING BTREE;


# interface_execution_record
DROP INDEX `unique_record_index` ON `reporter`.`interface_execution_record`;
ALTER TABLE `reporter`.`interface_execution_record` ADD UNIQUE INDEX `unique_record_index`(`task_id`, `project_id`, `execute_id`, `interface_execute_id`) USING BTREE;

DROP INDEX `ix_interface_id` ON `reporter`.`interface_execution_record`;
ALTER TABLE `reporter`.`interface_execution_record` ADD INDEX `ix_interface_id`(`project_id`, `interface_id`) USING BTREE;


# plan_execution_record
DROP INDEX `unique_record_index` ON `reporter`.`plan_execution_record`;
ALTER TABLE `reporter`.`plan_execution_record` ADD UNIQUE INDEX `unique_record_index`(`task_id`, `project_id`, `execute_id`, `plan_execute_id`) USING BTREE;

DROP INDEX `ix_plan_id` ON `reporter`.`plan_execution_record`;
ALTER TABLE `reporter`.`plan_execution_record` ADD INDEX `ix_plan_id`(`project_id`, `plan_id`) USING BTREE;