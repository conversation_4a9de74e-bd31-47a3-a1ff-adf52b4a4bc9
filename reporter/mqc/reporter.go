package main

import (
	"fmt"

	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/cmd"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/server"
)

var ConfigFile = new(string)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		s, err := server.NewConsumeServer(*ConfigFile)
		if err != nil {
			return err
		}

		defer s.Stop()

		fmt.Printf("Starting mqc server...\n")
		s.Start()

		return nil
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.StringVarP(ConfigFile, "mqc-config", "f", "etc/reporter.yaml", "the config file of service")

	cobra.CheckErr(root.Execute())
}
