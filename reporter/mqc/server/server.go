package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/handler"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
)

// NewConsumeServer for single server startup
func NewConsumeServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new mqc server, cause by the config[%T] isn't a mqc config", c)
	}

	err := cc.ServiceConf.SetUp()
	if err != nil {
		return nil, errors.Errorf("failed to setup service config, error: %+v", err)
	}

	ctx := svc.NewServiceContext(cc)
	err = handler.RegisterHandlers(ctx.ConsumerV2, ctx)
	if err != nil {
		return nil, errors.Errorf("failed to register handler, error: %+v", err)
	}

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	internal.InitOperations(ctx)

	// 服务启动时，注册定时清理执行记录信号任务
	//l := splitcleantaskslogic.NewRegisterSplitCleanTaskLogic(context.Background(), ctx) // ctx done?
	//_, err = l.RegisterCheckTask(ctx)
	//if err != nil {
	//	l.Logger.Warnf("failed to register task, error: %+v", err)
	//}

	return ctx.ConsumerV2, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c)

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceFunc(NewServer),
	}
}
