package casefailstat

import (
	"context"
	"runtime/debug"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/logic/casefailstat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
)

type Processor struct {
	svcCtx *svc.ServiceContext
}

func (processor *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v, stack: %s", r, debug.Stack())
		}
	}()

	l := casefailstat.NewPlanLogic(ctx, processor.svcCtx)
	if err = l.Handler(task); err != nil {
		logger.Errorf("failed to execute the case_fail_stat task, error: %+v", err)
		return []byte(constants.FAILURE), err
	}

	return []byte(constants.SUCCESS), nil
}

func NewProcessor(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &Processor{
		svcCtx: svcCtx,
	}
}
