package setcleanedtask

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/logic/setcleanedtask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/types"
)

// Deprecated: use new api.
func CreateHandler(svcCtx *svc.ServiceContext) func(taskId, projectId string, cleanType constants.CleanType) (
	string, error,
) {
	return func(taskId, projectId string, cleanType constants.CleanType) (string, error) {
		logic := setcleanedtask.NewSetCleanedTasksLogic(context.Background(), svcCtx)
		err := logic.SetRecordsToCleaned(
			&types.SetCleanedTask{
				TaskId:    taskId,
				ProjectId: projectId,
				CleanType: cleanType,
			},
		)
		if err != nil {
			return constants.FAILURE, err
		}

		return constants.SUCCESS, nil
	}
}

type Processor struct {
	svcCtx *svc.ServiceContext
}

func (p *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v, stack: %s", r, debug.Stack())
		}
	}()

	var data types.SetCleanedTask
	if err = jsonx.Unmarshal(task.Payload, &data); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			err, "failed to unmarshal task payload, payload: %s", task.Payload,
		)
	}

	logic := setcleanedtask.NewSetCleanedTasksLogic(ctx, p.svcCtx)
	err = logic.SetRecordsToCleaned(&data)
	if err != nil {
		logger.Errorf(
			"failed to set records to cleaned, project_id: %s, task_id: %s, clean_type: %s, error: %+v",
			data.ProjectId, data.TaskId, data.CleanType, err,
		)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}

func NewProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &Processor{
		svcCtx: svcCtx,
	}
}
