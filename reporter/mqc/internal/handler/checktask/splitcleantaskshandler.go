package checktask

import (
	"context"
	"runtime/debug"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	splitcleantaskslogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/logic/splitcleantasks"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
)

// CreateHandler 检查定时清理任务状态
// Deprecated: use new api.
func CreateHandler(svcCtx *svc.ServiceContext) func() (string, error) {
	return func() (string, error) {
		l := splitcleantaskslogic.NewRegisterSplitCleanTaskLogic(context.Background(), svcCtx) // ctx done
		err := l.CheckPeriodicCleanTaskStatus()
		if err != nil {
			logx.Errorf("failed to check periodic clean task status: %+v", err)
			return constants.FAILURE, err
		}

		return constants.SUCCESS, nil
	}
}

type Processor struct {
	svcCtx *svc.ServiceContext
}

func (processor *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v, stack: %s", r, debug.Stack())
		}
	}()

	l := splitcleantaskslogic.NewRegisterSplitCleanTaskLogic(ctx, processor.svcCtx)
	err = l.CheckPeriodicCleanTaskStatus()
	if err != nil {
		logger.Errorf("failed to check periodic clean task status: %+v", err)
		return []byte(constants.FAILURE), err
	}
	return []byte(constants.SUCCESS), nil
}

func NewProcessor(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &Processor{
		svcCtx: svcCtx,
	}
}
