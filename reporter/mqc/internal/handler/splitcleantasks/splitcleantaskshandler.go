package splitcleantasks

import (
	"context"
	"runtime/debug"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	splitcleantaskslogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/logic/splitcleantasks"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
)

// Deprecated: use new api.
func CreateHandler(svcCtx *svc.ServiceContext) func(data []byte) (string, error) {
	return func(data []byte) (string, error) {
		l := splitcleantaskslogic.NewSplitCleanTasksLogic(context.Background(), svcCtx) // ctx done
		l.SplitCleanTasks(data)

		return constants.SUCCESS, nil
	}
}

type Processor struct {
	svcCtx *svc.ServiceContext
}

func (processor *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v, stack: %s", r, debug.Stack())
		}
	}()

	l := splitcleantaskslogic.NewSplitCleanTasksLogic(ctx, processor.svcCtx)
	l.SplitCleanTasks(task.Payload)
	return []byte(constants.SUCCESS), nil
}

func NewProcessor(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &Processor{
		svcCtx: svcCtx,
	}
}
