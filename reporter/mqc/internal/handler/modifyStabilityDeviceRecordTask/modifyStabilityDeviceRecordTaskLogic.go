package modifyStabilityDeviceRecordTask

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/logic/modifyStabilityDeviceRecordTask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type Processor struct {
	svcCtx *svc.ServiceContext
}

func NewProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &Processor{
		svcCtx: svcCtx,
	}
}

func (p *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v, stack: %s", r, debug.Stack())
		}
	}()

	logger.Infof("modify stability device record task playload: %s", task.Payload)

	var info pb.PutStabilityDeviceRecordReq
	if err = protobuf.UnmarshalJSON(task.Payload, &info); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			err, "failed to unmarshal task payload, type: %s, payload: %s", task.Typename, task.Payload,
		)
	}

	ctx = updateContext(ctx, info.GetTaskId(), info.GetExecuteId(), info.GetExecutedBy())
	if err = modifyStabilityDeviceRecordTask.NewLogic(ctx, p.svcCtx).ModifyStabilityDeviceRecord(&info); err != nil {
		logger.Errorf(
			"failed to modify stability device record: %s, error: %+v", protobuf.MarshalJSONIgnoreError(&info), err,
		)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}
