package handler

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/handler/casefailstat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/handler/delcleanedtask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/handler/modifyStabilityDeviceRecordTask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/handler/saveDevicePerfDataTask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/handler/saveDeviceStepTask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/handler/setcleanedtask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
)

func RegisterHandlers(server *consumer.Consumer, serverCtx *svc.ServiceContext) error {
	return server.RegisterHandlers(
		// 定时消息
		// consumer.NewTaskHandlerOjb(constants.MQTaskTypeReporterCheckTask, checktask.NewProcessor(serverCtx)),
		// consumer.NewTaskHandlerOjb(constants.MQTaskTypeReporterSplitCleanTask, splitcleantasks.NewProcessor(serverCtx)),
		// 普通消息
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeReporterSetCleanedTask, setcleanedtask.NewProcessor(serverCtx)),
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeReporterDelCleanedTask, delcleanedtask.NewProcessor(serverCtx)),
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeReporterCaseFailLogResult, casefailstat.NewProcessor(serverCtx)),
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeReporterSaveDevicePerfDataTask, saveDevicePerfDataTask.NewProcessor(serverCtx),
		),
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeReporterSaveDeviceStepTask, saveDeviceStepTask.NewProcessor(serverCtx),
		),
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeReporterModifyStabilityDeviceRecordTask, modifyStabilityDeviceRecordTask.NewProcessor(serverCtx),
		),
	)
}
