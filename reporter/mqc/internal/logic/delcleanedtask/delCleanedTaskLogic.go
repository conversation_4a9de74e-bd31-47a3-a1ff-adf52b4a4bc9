package delcleanedtask

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/types"
)

type DelCleanedTaskLogic struct {
	*BaseLogic
}

func NewDelCleanedTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DelCleanedTaskLogic {
	return &DelCleanedTaskLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *DelCleanedTaskLogic) DeleteCleanedRecords(task *types.DelCleanedTask) (err error) {
	switch task.CleanType {
	case constants.CleanTypeApiPlan:
		err = l.svcCtx.PlanExecutionRecordModel.DeleteBeforeNDaysRecords(l.ctx, nil, task.KeepDays)
	case constants.CleanTypeUiPlan:
		err = l.svcCtx.UIPlanExecutionRecordModel.DeleteBeforeNDaysRecords(l.ctx, nil, task.KeepDays)
	case constants.CleanTypePerfPlan:
		err = l.svcCtx.PerfPlanExecutionRecordModel.DeleteBeforeNDaysRecords(l.ctx, nil, task.KeepDays)
	case constants.CleanTypeStabilityPlan:
		err = l.svcCtx.StabilityPlanExecutionRecordModel.DeleteBeforeNDaysRecords(l.ctx, nil, task.KeepDays)
	case constants.CleanTypeApiSuite:
		err = l.svcCtx.SuiteExecutionRecordModel.DeleteBeforeNDaysRecords(l.ctx, nil, task.KeepDays)
	case constants.CleanTypeInterfaceDocument:
		err = l.svcCtx.InterfaceExecutionRecordModel.DeleteBeforeNDaysRecords(l.ctx, nil, task.KeepDays)
	case constants.CleanTypeApiCase, constants.CleanTypeInterfaceCase, constants.CleanTypeApiComponentGroup:
		err = l.svcCtx.ExecutionRecordModel.DeleteBeforeNDaysRecords(l.ctx, nil, task.KeepDays)
	case constants.CleanTypeUIAgentComponent:
		err = l.svcCtx.UiAgentComponentExecutionRecordModel.DeleteBeforeNDaysRecords(l.ctx, nil, task.KeepDays)
	default:
		l.Errorf("invalid clean type: %s", task.CleanType)
		return nil
	}

	return err
}
