package casefailstat

import (
	"context"
	"database/sql"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PlanLogic struct {
	*BaseLogic
}

func NewPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PlanLogic {
	return &PlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (processor *PlanLogic) Handler(task *base.Task) error {
	processor.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(processor.ctx), trace.SpanIDFromContext(processor.ctx), task.Typename,
	)

	req := new(reporterpb.CaseFailForPlanStatForMq)
	err := protobuf.UnmarshalJSON(task.Payload, req)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of case fail stat plan result, payload: %s, error: %+v",
			task.Payload, err,
		)
	}

	var (
		projectID     = req.GetProjectId()
		taskID        = req.GetTaskId()
		failReasonURL = req.GetFailReasonUrl()

		user = userinfo.System()
	)

	// 查询计划
	planLog, err := processor.svcCtx.PlanExecutionRecordModel.FindOneByTaskIdProjectId(
		processor.ctx, taskID, projectID,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find plan execution record, project_id: %s, task_id: %s, error: %+v",
			projectID, taskID, err,
		)
	}
	if planLog != nil {
		req.PlanId = planLog.PlanId
	} else {
		return errorx.Errorf(
			errorx.NotExists, "not found the plan execution record, project_id: %s, task_id: %s", projectID, taskID,
		)
	}

	// 保存数据
	return processor.svcCtx.CaseFailForPlanStatModel.Trans(
		processor.ctx, func(context context.Context, session sqlx.Session) error {
			_, err := processor.svcCtx.CaseFailForPlanStatModel.InsertTX(
				context, session, &model.CaseFailForPlanStat{
					ProjectId: projectID,
					TaskId:    taskID,
					PlanId:    req.GetPlanId(),
					CaseId:    req.GetCaseId(),
					ExecuteId: req.GetExecuteId(),
					CaseType:  req.GetCaseType(),
					FailReasonUrl: sql.NullString{
						String: failReasonURL,
						Valid:  failReasonURL != "",
					},
					CreatedBy: user.Account,
					UpdatedBy: user.Account,
				},
			)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to create case fail plan stat record, project_id: %s, task_id: %s, plan_id: %s, case_id: %s, case_type: %s, error: %+v",
					projectID, taskID, req.GetPlanId(), req.GetCaseId(), req.GetCaseType(), err,
				)
			}
			return nil
		},
	)
}
