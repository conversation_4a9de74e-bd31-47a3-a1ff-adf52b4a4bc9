package setcleanedtask

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	discoverypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
)

const defaultReportsPath = "/app/data/reports"

type SetCleanedTasksLogic struct {
	*BaseLogic
}

func NewSetCleanedTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetCleanedTasksLogic {
	return &SetCleanedTasksLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SetCleanedTasksLogic) SetRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	var fn func(cleanTask *types.SetCleanedTask) error

	switch task.CleanType {
	case constants.CleanTypeApiPlan:
		fn = l.setAPIPlanRecordsToCleaned
	case constants.CleanTypeUiPlan:
		fn = l.setUIPlanRecordsToCleaned
	case constants.CleanTypePerfPlan:
		fn = l.setPerfPlanRecordsToCleaned
	case constants.CleanTypeStabilityPlan:
		fn = l.setStabilityPlanRecordsToCleaned
	case constants.CleanTypeApiSuite:
		fn = l.setAPISuiteRecordsToCleaned
	case constants.CleanTypeInterfaceDocument:
		fn = l.setInterfaceDocumentRecordsToCleaned
	case constants.CleanTypeApiCase, constants.CleanTypeInterfaceCase, constants.CleanTypeApiComponentGroup:
		fn = l.setExecutionRecordsToCleaned
	case constants.CleanTypeUiPackage:
		fn = l.setUIPackageRecordsToCleaned
	case constants.CleanTypeUIAgentComponent:
		fn = l.setUIAgentComponentRecordsToCleaned
	default:
		l.Errorf("invalid clean type: %s", task.CleanType)
		return nil
	}

	return fn(task)
}

func (l *SetCleanedTasksLogic) setAPIPlanRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	if err = l.svcCtx.ExecutionRecordModel.DeleteRecordByTaskId(
		l.ctx, nil, task.TaskId, task.ProjectId, task.CleanType,
	); err != nil {
		return err
	}

	if err = l.svcCtx.ServiceExecutionRecordModel.DeleteRecordByTaskId(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	if err = l.svcCtx.InterfaceExecutionRecordModel.DeleteRecordByTaskId(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	if err = l.svcCtx.SuiteExecutionRecordModel.DeleteRecordByTaskId(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	if err = l.svcCtx.PlanExecutionRecordModel.SetRecordHasCleaned(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	return nil
}

func (l *SetCleanedTasksLogic) setUIPlanRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	if err = l.svcCtx.UIDevicePerfDataModel.DeleteByTaskID(l.ctx, nil, task.TaskId); err != nil {
		return err
	}

	if err = l.svcCtx.UICaseExecutionStepContentModel.DeleteRecordByTaskID(l.ctx, nil, task.TaskId); err != nil {
		return err
	}

	if err = l.svcCtx.UICaseExecutionStepModel.DeleteRecordByTaskID(l.ctx, nil, task.TaskId); err != nil {
		return err
	}

	if err = l.svcCtx.UICaseExecutionRecordModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	if err = l.svcCtx.UISuiteExecutionRecordModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	if err = l.svcCtx.UIPlanExecutionRecordModel.SetRecordHasCleaned(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	return nil
}

func (l *SetCleanedTasksLogic) setPerfPlanRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	if err = l.svcCtx.PerfCaseExecutionRecordModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	if err = l.svcCtx.PerfSuiteExecutionRecordModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	if err = l.svcCtx.PerfPlanExecutionRecordModel.SetRecordHasCleaned(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	return nil
}

func (l *SetCleanedTasksLogic) setStabilityPlanRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	// delete activity records
	if err = l.svcCtx.StabilityDeviceActivityRecordModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId,
	); err != nil {
		return err
	}

	// delete perf_data records
	if err = l.svcCtx.StabilityDevicePerfDataModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId,
	); err != nil {
		return err
	}

	// delete step_content records
	if err = l.svcCtx.StabilityDeviceExecutionStepContentModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId,
	); err != nil {
		return err
	}

	// delete step records
	if err = l.svcCtx.StabilityDeviceExecutionStepModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId,
	); err != nil {
		return err
	}

	// delete device records
	if err = l.svcCtx.StabilityDeviceExecutionRecordModel.DeleteRecordByTaskID(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	// set plan records cleaned
	if err = l.svcCtx.StabilityPlanExecutionRecordModel.SetRecordHasCleaned(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	// delete report files
	l.deleteStabilityReports(task.TaskId)
	return nil
}

func (l *SetCleanedTasksLogic) deleteStabilityReports(taskID string) {
	reportsPath := defaultReportsPath
	if l.svcCtx.Config.LocalPath != "" {
		reportsPath = l.svcCtx.Config.LocalPath
	}

	// delete app files
	shortTaskID := strings.TrimPrefix(taskID, utils.ConstTaskIdPrefix)
	appFiles := filepath.Join(
		reportsPath, constants.ConstStoragePathOfStabilityTest, constants.ConstStoragePathOfApps, shortTaskID+".*",
	)
	files, err := filepath.Glob(appFiles)
	if err != nil {
		l.Errorf("failed to find matching files, pattern: %s, error: %+v", appFiles, err)
	} else {
		for _, file := range files {
			if err = os.Remove(file); err != nil {
				l.Errorf("failed to remove file, path: %s, error: %+v", file, err)
			}
		}
	}

	if passed := l.svcCtx.DiscoveryRPC.HealthCheck(); passed == nil && l.svcCtx.DiscoveryRPC.IsDiscovery() {
		// try to remove app files from discovery
		for _, suffix := range []string{constants.ConstSuffixOfApk, constants.ConstSuffixOfIpa} {
			_, _ = l.svcCtx.DiscoveryRPC.RemoveApp(
				l.ctx, &discoverypb.RemoveAppReq{
					Filename: shortTaskID + suffix,
				},
			)
		}
	}

	// delete report files
	reportFiles := filepath.Join(
		reportsPath, constants.ConstStoragePathOfStabilityTest, constants.ConstStoragePathOfReports, taskID,
	)
	if err = os.RemoveAll(reportFiles); err != nil {
		l.Errorf("failed to remove reports, path: %s, error: %+v", reportFiles, err)
	}
}

func (l *SetCleanedTasksLogic) setAPISuiteRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	if err = l.svcCtx.ExecutionRecordModel.DeleteRecordByTaskId(
		l.ctx, nil, task.TaskId, task.ProjectId, task.CleanType,
	); err != nil {
		return err
	}

	if err = l.svcCtx.SuiteExecutionRecordModel.SetRecordHasCleaned(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	return nil
}

func (l *SetCleanedTasksLogic) setInterfaceDocumentRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	if err = l.svcCtx.ExecutionRecordModel.DeleteRecordByTaskId(
		l.ctx, nil, task.TaskId, task.ProjectId, task.CleanType,
	); err != nil {
		return err
	}

	if err = l.svcCtx.InterfaceExecutionRecordModel.SetRecordHasCleaned(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	return nil
}

func (l *SetCleanedTasksLogic) setExecutionRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	if err = l.svcCtx.ExecutionRecordModel.DeleteRecordByTaskId(
		l.ctx, nil, task.TaskId, task.ProjectId, task.CleanType,
	); err != nil {
		return err
	}

	if err = l.svcCtx.ExecutionRecordModel.SetRecordHasCleaned(l.ctx, nil, task.TaskId, task.ProjectId); err != nil {
		return err
	}

	return nil
}

func (l *SetCleanedTasksLogic) setUIPackageRecordsToCleaned(_ *types.SetCleanedTask) (err error) {
	lockKey := "lock::ui::package::cleanup"
	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, lockKey, redislock.WithExpire(10*time.Minute),
	)
	if err != nil {
		l.Errorf("failed to acquire global cleanup lock: %+v", err)
		return err
	}
	defer lock.Release()

	pattern := fmt.Sprintf("%s*", commonconsts.FailedInstallFailedAppPrefix)
	keys, err := l.svcCtx.Redis.KeysCtx(l.ctx, pattern)
	if err != nil {
		l.Errorf("failed to get keys with pattern %s: %+v", pattern, err)
		return err
	}

	const exceededTime = 24 * 60 * 60
	for _, key := range keys {
		data, err := l.svcCtx.Redis.GetCtx(l.ctx, key)
		if err != nil {
			l.Errorf("failed to get value of key %s: %+v", key, err)
			continue
		}

		detail := commonconsts.FailedInstallFailedAppDetail{}
		if err = json.Unmarshal([]byte(data), &detail); err != nil {
			l.Errorf("failed to unmarshal value of key %s: %+v", key, err)
			continue
		}

		if time.Now().Unix()-detail.Timestamp < exceededTime {
			continue
		}

		if err = os.RemoveAll(detail.FilePath); err != nil {
			l.Errorf("failed to remove file %s: %+v", detail.FilePath, err)
		}
		if _, err = l.svcCtx.Redis.DelCtx(l.ctx, key); err != nil {
			l.Errorf("failed to delete key %s: %+v", key, err)
		}
	}

	return nil
}
func (l *SetCleanedTasksLogic) setUIAgentComponentRecordsToCleaned(task *types.SetCleanedTask) (err error) {
	if err = l.svcCtx.UiAgentComponentExecutionRecordModel.SetRecordHasCleaned(
		l.ctx, nil, task.TaskId, task.ProjectId,
	); err != nil {
		return err
	}

	l.deleteUIAgentReports(task.TaskId, task.ExecuteId)
	return nil
}

func (l *SetCleanedTasksLogic) deleteUIAgentReports(taskID, executeID string) {
	shortTaskID := strings.TrimPrefix(taskID, utils.ConstTaskIdPrefix)

	var byDiscovery bool
	if passed := l.svcCtx.DiscoveryRPC.HealthCheck(); passed == nil && l.svcCtx.DiscoveryRPC.IsDiscovery() {
		byDiscovery = true
	}

	for _, suffix := range []string{constants.ConstSuffixOfApk, constants.ConstSuffixOfIpa} {
		filename := fmt.Sprintf("%s%s", shortTaskID, suffix)
		// app path: /app/data/reports/ui_agent_test/apps/${short_task_id}${suffix}
		_ = os.Remove(
			filepath.Join(
				l.svcCtx.Config.LocalPath,
				constants.ConstStoragePathOfUIAgentTest,
				constants.ConstStoragePathOfApps,
				filename,
			),
		)

		if byDiscovery {
			_, _ = l.svcCtx.DiscoveryRPC.RemoveApp(
				l.ctx, &discoverypb.RemoveAppReq{
					Filename: filename,
				},
			)
		}
	}

	// report path: /app/data/reports/ui_agent_test/reports/${task_id}
	_ = os.RemoveAll(
		filepath.Join(
			l.svcCtx.Config.LocalPath,
			constants.ConstStoragePathOfUIAgentTest,
			constants.ConstStoragePathOfReports,
			taskID,
		),
	)

	if err := l.svcCtx.ClickPilotClient.DeleteTask(executeID); err != nil { // 执行ID作为Agent的任务ID
		l.Errorf("failed to delete task by ClickPilot, execute_id: %s, error: %+v", executeID, err)
	}
}
