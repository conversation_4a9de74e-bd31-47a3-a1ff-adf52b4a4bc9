package svc

import (
	"time"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/cronscheduler"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common/zrpc/discovery"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	DB        sqlx.SqlConn
	Redis     *redis.Redis
	Scheduler *cronscheduler.Scheduler

	DiscoveryRPC discovery.IClient // 注意：为`*discovery.NoopClient`时，则无需通过接口删除App文件

	// ConsumerV1       *consumerv1.Consumer
	ConsumerV2       *consumer.Consumer
	ReporterProducer *producer.Producer

	ExecutionRecordModel                     model.ExecutionRecordModel
	ServiceExecutionRecordModel              model.ServiceExecutionRecordModel
	InterfaceExecutionRecordModel            model.InterfaceExecutionRecordModel
	SuiteExecutionRecordModel                model.SuiteExecutionRecordModel
	PlanExecutionRecordModel                 model.PlanExecutionRecordModel
	CaseFailForPlanStatModel                 model.CaseFailForPlanStatModel
	UIDevicePerfDataModel                    model.UiDevicePerfDataModel
	UICaseExecutionStepModel                 model.UiCaseExecutionStepModel
	UICaseExecutionStepContentModel          model.UiCaseExecutionStepContentModel
	UICaseExecutionRecordModel               model.UiCaseExecutionRecordModel
	UISuiteExecutionRecordModel              model.UiSuiteExecutionRecordModel
	UIPlanExecutionRecordModel               model.UiPlanExecutionRecordModel
	PerfCaseExecutionRecordModel             model.PerfCaseExecutionRecordModel
	PerfSuiteExecutionRecordModel            model.PerfSuiteExecutionRecordModel
	PerfPlanExecutionRecordModel             model.PerfPlanExecutionRecordModel
	StabilityPlanExecutionRecordModel        model.StabilityPlanExecutionRecordModel
	StabilityDevicePerfDataModel             model.StabilityDevicePerfDataModel
	StabilityDeviceExecutionStepModel        model.StabilityDeviceExecutionStepModel
	StabilityDeviceExecutionStepContentModel model.StabilityDeviceExecutionStepContentModel
	StabilityDeviceActivityRecordModel       model.StabilityDeviceActivityRecordModel
	StabilityDeviceExecutionRecordModel      model.StabilityDeviceExecutionRecordModel
	UiAgentComponentExecutionRecordModel     model.UiAgentComponentExecutionRecordModel

	ClickPilotClient clickPilot.IClient

	MQCName            string
	SplitCleanTaskList []string
}

func NewServiceContext(conf config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(conf.DB.DataSource)
	scheduler := cronscheduler.NewScheduler()
	scheduler.Scheduler.ChangeLocation(time.Local) // change to local timezone

	return &ServiceContext{
		Config: conf,

		DB:        sqlConn,
		Redis:     redis.MustNewRedis(conf.Redis, redis.WithDB(conf.Redis.DB)),
		Scheduler: scheduler,

		DiscoveryRPC: discovery.NewRPCClient(conf.Discovery),

		ConsumerV2:       consumer.NewConsumer(conf.Consumer),
		ReporterProducer: producer.NewProducer(conf.ReporterProducer),

		ExecutionRecordModel:                     model.NewExecutionRecordModel(sqlConn),
		ServiceExecutionRecordModel:              model.NewServiceExecutionRecordModel(sqlConn),
		InterfaceExecutionRecordModel:            model.NewInterfaceExecutionRecordModel(sqlConn),
		SuiteExecutionRecordModel:                model.NewSuiteExecutionRecordModel(sqlConn),
		PlanExecutionRecordModel:                 model.NewPlanExecutionRecordModel(sqlConn),
		CaseFailForPlanStatModel:                 model.NewCaseFailForPlanStatModel(sqlConn),
		UIDevicePerfDataModel:                    model.NewUiDevicePerfDataModel(sqlConn),
		UICaseExecutionStepModel:                 model.NewUiCaseExecutionStepModel(sqlConn),
		UICaseExecutionStepContentModel:          model.NewUiCaseExecutionStepContentModel(sqlConn),
		UICaseExecutionRecordModel:               model.NewUiCaseExecutionRecordModel(sqlConn),
		UISuiteExecutionRecordModel:              model.NewUiSuiteExecutionRecordModel(sqlConn),
		UIPlanExecutionRecordModel:               model.NewUiPlanExecutionRecordModel(sqlConn),
		PerfCaseExecutionRecordModel:             model.NewPerfCaseExecutionRecordModel(sqlConn),
		PerfSuiteExecutionRecordModel:            model.NewPerfSuiteExecutionRecordModel(sqlConn),
		PerfPlanExecutionRecordModel:             model.NewPerfPlanExecutionRecordModel(sqlConn),
		StabilityPlanExecutionRecordModel:        model.NewStabilityPlanExecutionRecordModel(sqlConn),
		StabilityDevicePerfDataModel:             model.NewStabilityDevicePerfDataModel(sqlConn),
		StabilityDeviceExecutionStepModel:        model.NewStabilityDeviceExecutionStepModel(sqlConn),
		StabilityDeviceExecutionStepContentModel: model.NewStabilityDeviceExecutionStepContentModel(sqlConn),
		StabilityDeviceActivityRecordModel:       model.NewStabilityDeviceActivityRecordModel(sqlConn),
		StabilityDeviceExecutionRecordModel:      model.NewStabilityDeviceExecutionRecordModel(sqlConn),
		UiAgentComponentExecutionRecordModel:     model.NewUiAgentComponentExecutionRecordModel(sqlConn),

		ClickPilotClient: clickPilot.NewClient(conf.ClickPilot),

		MQCName:            utils.GenReporterMQCName(),
		SplitCleanTaskList: make([]string, 1),
	}
}
