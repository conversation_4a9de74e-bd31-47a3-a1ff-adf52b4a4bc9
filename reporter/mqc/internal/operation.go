package internal

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	splitcleantaskslogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/logic/splitcleantasks"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
)

const (
	lockKeyOfClearXExecutionRecord = "lock:x_execution_record:delete"
	lockKeyOfClearFailedCase       = "lock:case_fail_for_plan_stat:delete"

	expireOfClearXExecutionRecordTask = 30 * time.Minute
	expireOfClearFailedCaseTask       = 5 * time.Minute
)

func InitOperations(svcCtx *svc.ServiceContext) {
	setupOperation(svcCtx)
	teardownOperation(svcCtx)
}

func setupOperation(svcCtx *svc.ServiceContext) {
	registerScheduleTasks(svcCtx)
}

func teardownOperation(svcCtx *svc.ServiceContext) {
	// stop the cron scheduler when shutdown
	proc.AddShutdownListener(svcCtx.Scheduler.Stop)
}

func registerScheduleTasks(svcCtx *svc.ServiceContext) {
	// register the task of clear failure case statistics records
	if err := svcCtx.Scheduler.RegisterTasks(
		map[string]func(){
			svcCtx.Config.ReporterCleaner.CleanCron: func() {
				if err := clearExecutionRecords(svcCtx); err != nil {
					logx.Errorf("failed to clear the execution records, error: %+v", err)
				}
			},
			svcCtx.Config.FailedCaseCleaner.CronExpression: func() {
				if err := clearFailedCaseRecords(svcCtx, svcCtx.Config.FailedCaseCleaner.KeepDays); err != nil {
					logx.Errorf("failed to clear the failed case records, error: %+v", err)
				}
			},
		},
	); err != nil {
		logx.Errorf("failed to register scheduled tasks, error: %+v", err)
		return
	}

	// start the cron scheduler
	svcCtx.Scheduler.Start()
}

func clearExecutionRecords(svcCtx *svc.ServiceContext) error {
	var (
		ctx    context.Context
		cancel context.CancelFunc
	)

	ctx, cancel = context.WithTimeout(context.Background(), expireOfClearXExecutionRecordTask)
	defer cancel()

	logger := logx.WithContext(ctx)

	if err := caller.LockDo(
		svcCtx.Redis, lockKeyOfClearXExecutionRecord, func() error {
			splitcleantaskslogic.NewSplitCleanTasksLogic(ctx, svcCtx).SplitCleanTasksV2()
			return nil
		},
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			logger.Errorf(
				"failed to acquire the redis lock with key[%s], error: %+v", lockKeyOfClearXExecutionRecord, err,
			)
			return err
		}

		logger.Infof(
			"another service is executing the task of clear execution records, key: %s", lockKeyOfClearXExecutionRecord,
		)
	} else {
		logger.Infof("finished to clear execution records, key: %s", lockKeyOfClearXExecutionRecord)
	}

	return nil
}

func clearFailedCaseRecords(svcCtx *svc.ServiceContext, days int) error {
	var (
		ctx    context.Context
		cancel context.CancelFunc
	)

	ctx, cancel = context.WithTimeout(context.Background(), expireOfClearFailedCaseTask)
	defer cancel()

	logger := logx.WithContext(ctx)

	if err := caller.LockDo(
		svcCtx.Redis, lockKeyOfClearFailedCase, func() error {
			return svcCtx.CaseFailForPlanStatModel.DeleteBeforeNDaysRecords(ctx, nil, days)
		},
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			logger.Errorf("failed to acquire the redis lock with key[%s], error: %+v", lockKeyOfClearFailedCase, err)
			return err
		}

		logger.Infof(
			"another service is executing the task of clear failed case records, key: %s", lockKeyOfClearFailedCase,
		)
	} else {
		logger.Infof("finished to clear failed case records, key: %s, days: %d", lockKeyOfClearFailedCase, days)
	}

	return nil
}
