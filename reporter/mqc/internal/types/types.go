package types

import "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"

type CleanConfig struct {
	Type     string `json:"type"`
	Mode     string `json:"mode"`
	Value    string `json:"value"`
	KeepDays int    `json:"keep_days"`
}

type CleanConfigs struct {
	Items []*CleanConfig `json:"items,omitempty"`
}

type SetCleanedTask struct {
	TaskId    string              `json:"task_id"`
	ExecuteId string              `json:"execute_id"`
	ProjectId string              `json:"project_id"`
	CleanType constants.CleanType `json:"clean_type"`
}

type DelCleanedTask struct {
	CleanType constants.CleanType `json:"clean_type"`
	KeepDays  int                 `json:"keep_days"`
}
