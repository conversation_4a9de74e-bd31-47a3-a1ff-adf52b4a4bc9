package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis *redis.Redis

	ExecutionRecordModel                     model.ExecutionRecordModel
	InterfaceExecutionRecordModel            model.InterfaceExecutionRecordModel
	SuiteExecutionRecordModel                model.SuiteExecutionRecordModel
	PlanExecutionRecordModel                 model.PlanExecutionRecordModel
	ServiceExecutionRecordModel              model.ServiceExecutionRecordModel
	CaseFailForPlanStatModel                 model.CaseFailForPlanStatModel
	UIDevicePerfDataModel                    model.UiDevicePerfDataModel
	UICaseExecutionStepModel                 model.UiCaseExecutionStepModel
	UICaseExecutionStepContentModel          model.UiCaseExecutionStepContentModel
	UICaseExecutionRecordModel               model.UiCaseExecutionRecordModel
	UISuiteExecutionRecordModel              model.UiSuiteExecutionRecordModel
	UIPlanExecutionRecordModel               model.UiPlanExecutionRecordModel
	PerfCaseExecutionRecordModel             model.PerfCaseExecutionRecordModel
	PerfSuiteExecutionRecordModel            model.PerfSuiteExecutionRecordModel
	PerfPlanExecutionRecordModel             model.PerfPlanExecutionRecordModel
	StabilityPlanExecutionRecordModel        model.StabilityPlanExecutionRecordModel
	StabilityDeviceExecutionRecordModel      model.StabilityDeviceExecutionRecordModel
	StabilityDeviceExecutionStepModel        model.StabilityDeviceExecutionStepModel
	StabilityDeviceExecutionStepContentModel model.StabilityDeviceExecutionStepContentModel
	StabilityDevicePerfDataModel             model.StabilityDevicePerfDataModel
	StabilityDeviceActivityRecordModel       model.StabilityDeviceActivityRecordModel
	UiAgentComponentExecutionRecordModel     model.UiAgentComponentExecutionRecordModel

	Consumer *consumer.Consumer

	ProbeDomain string
	Monitors    map[string]config.MonitorConfig
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	monitors := make(map[string]config.MonitorConfig)
	for _, monitor := range c.Monitors {
		monitors[monitor.Env] = monitor
	}

	return &ServiceContext{
		Config: c,

		Redis: redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.DB)),

		ExecutionRecordModel:                     model.NewExecutionRecordModel(sqlConn),
		InterfaceExecutionRecordModel:            model.NewInterfaceExecutionRecordModel(sqlConn),
		SuiteExecutionRecordModel:                model.NewSuiteExecutionRecordModel(sqlConn),
		PlanExecutionRecordModel:                 model.NewPlanExecutionRecordModel(sqlConn),
		ServiceExecutionRecordModel:              model.NewServiceExecutionRecordModel(sqlConn),
		CaseFailForPlanStatModel:                 model.NewCaseFailForPlanStatModel(sqlConn),
		UIDevicePerfDataModel:                    model.NewUiDevicePerfDataModel(sqlConn),
		UICaseExecutionStepModel:                 model.NewUiCaseExecutionStepModel(sqlConn),
		UICaseExecutionStepContentModel:          model.NewUiCaseExecutionStepContentModel(sqlConn),
		UICaseExecutionRecordModel:               model.NewUiCaseExecutionRecordModel(sqlConn),
		UISuiteExecutionRecordModel:              model.NewUiSuiteExecutionRecordModel(sqlConn),
		UIPlanExecutionRecordModel:               model.NewUiPlanExecutionRecordModel(sqlConn),
		PerfCaseExecutionRecordModel:             model.NewPerfCaseExecutionRecordModel(sqlConn),
		PerfSuiteExecutionRecordModel:            model.NewPerfSuiteExecutionRecordModel(sqlConn),
		PerfPlanExecutionRecordModel:             model.NewPerfPlanExecutionRecordModel(sqlConn),
		StabilityPlanExecutionRecordModel:        model.NewStabilityPlanExecutionRecordModel(sqlConn),
		StabilityDeviceExecutionRecordModel:      model.NewStabilityDeviceExecutionRecordModel(sqlConn),
		StabilityDeviceExecutionStepModel:        model.NewStabilityDeviceExecutionStepModel(sqlConn),
		StabilityDeviceExecutionStepContentModel: model.NewStabilityDeviceExecutionStepContentModel(sqlConn),
		StabilityDevicePerfDataModel:             model.NewStabilityDevicePerfDataModel(sqlConn),
		StabilityDeviceActivityRecordModel:       model.NewStabilityDeviceActivityRecordModel(sqlConn),
		UiAgentComponentExecutionRecordModel:     model.NewUiAgentComponentExecutionRecordModel(sqlConn),

		Consumer: consumer.NewConsumer(c.Consumer),

		ProbeDomain: c.ProbeDomain,
		Monitors:    monitors,
	}
}
