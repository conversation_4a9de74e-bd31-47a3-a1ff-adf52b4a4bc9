package reporterlogic

import (
	"context"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetExecuteRecordLogic struct {
	*BaseLogic
}

func NewGetExecuteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExecuteRecordLogic {
	return &GetExecuteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetExecuteRecordLogic) GetExecuteRecord(in *pb.GetExecuteRecordRequest) (*pb.GetExecuteRecordResponse, error) {
	// todo: add your logic here and delete this line
	fmt.Println(in)

	return &pb.GetExecuteRecordResponse{}, nil
}
