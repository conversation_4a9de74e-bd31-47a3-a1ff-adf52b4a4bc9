package reporterlogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CountFailedCaseInLastNDaysLogic struct {
	*BaseLogic
}

func NewCountFailedCaseInLastNDaysLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CountFailedCaseInLastNDaysLogic {
	return &CountFailedCaseInLastNDaysLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CountFailedCaseInLastNDays 统计最近N天指定用例的失败数
func (l *CountFailedCaseInLastNDaysLogic) CountFailedCaseInLastNDays(in *pb.CountFailedCaseInLastNDaysReq) (
	out *pb.CountFailedCaseInLastNDaysResp, err error,
) {
	result, err := l.svcCtx.CaseFailForPlanStatModel.CountInLastNDaysRecords(
		l.ctx, in.GetProjectId(), in.GetCaseId(), in.GetCaseType(), int(in.GetDays()),
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count case failures in last n days, project_id: %s, case_id: %s, case_type: %s, days: %d, error: %+v",
			in.GetProjectId(), in.GetCaseId(), in.GetCaseType(), in.GetDays(), err,
		)
	}

	return &pb.CountFailedCaseInLastNDaysResp{
		Count:         result.Count,
		LastUpdatedAt: result.LastUpdatedAt.Time.UnixMilli(),
	}, nil
}
