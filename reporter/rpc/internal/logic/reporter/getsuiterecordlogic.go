package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	structPb "google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonPb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetSuiteRecordLogic struct {
	*BaseLogic
}

func NewGetSuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSuiteRecordLogic {
	return &GetSuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 集合执行记录详情
func (l *GetSuiteRecordLogic) GetSuiteRecord(in *pb.GetSuiteRecordRequest) (resp *pb.GetSuiteRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	selectBuilder := l.svcCtx.SuiteExecutionRecordModel.SelectBuilder()
	selectBuilder = selectBuilder.Where("`task_id` = ?  AND `project_id` = ? AND `execute_id` = ? AND `suite_execute_id` = ?",
		in.TaskId, in.ProjectId, in.SuiteExecuteId, in.SuiteExecuteId).Limit(1)

	suiteRecords, err2 := l.svcCtx.SuiteExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)

	msg := fmt.Sprintf("suite suiteRecord detail with task_id[%s] project_id[%s] and suite_execute_id[%s]",
		in.TaskId, in.ProjectId, in.SuiteExecuteId)

	if (err2 != nil) || (len(suiteRecords) == 0) {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
		return nil, err
	}

	var (
		successCase, failureCase int64
		caseItems                []*pb.GetSuiteRecordResponse_CaseItem
	)

	// 获取关联测试用例执行情况
	selectCaseRecordBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `parent_component_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.SuiteExecuteId)
	caseRecords, err3 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectCaseRecordBuilder)

	if err3 != nil {
		err = err3
		return nil, err
	}

	for _, caseRecord := range caseRecords {
		if caseRecord.Status.String == "Success" {
			successCase += 1
		} else if caseRecord.Status.String == "Failure" {
			failureCase += 1
		}
		item := pb.GetSuiteRecordResponse_CaseItem{
			TaskId:        caseRecord.TaskId,
			ExecuteId:     caseRecord.ExecuteId,
			ProjectId:     caseRecord.ProjectId,
			CaseExecuteId: caseRecord.ComponentExecuteId,
			CaseId:        caseRecord.ComponentId,
			CaseName:      caseRecord.ComponentName,
			Version:       caseRecord.Version,
			StartedAt:     caseRecord.StartedAt,
			EndedAt:       caseRecord.EndedAt.Int64,
			CostTime:      caseRecord.CostTime,
			Status:        caseRecord.Status.String,
			MaintainedBy:  caseRecord.MaintainedBy,
			CaseType:      caseRecord.ComponentType,
		}
		caseItems = append(caseItems, &item)
	}

	suiteRecord := suiteRecords[0]

	var content structPb.Struct
	_ = protobuf.UnmarshalJSON([]byte(suiteRecord.Content.String), &content)

	var generalConfig *commonPb.GeneralConfig
	var accountConfig []*commonPb.AccountConfig
	_ = json.Unmarshal([]byte(suiteRecord.GeneralConfig.String), &generalConfig)
	_ = json.Unmarshal([]byte(suiteRecord.AccountConfig.String), &accountConfig)

	resp = &pb.GetSuiteRecordResponse{
		CostTime:      suiteRecord.CostTime,
		ExecutedBy:    suiteRecord.ExecutedBy,
		StartedAt:     suiteRecord.StartedAt,
		EndedAt:       suiteRecord.EndedAt.Int64,
		TotalCase:     suiteRecord.TotalCase,
		SuccessCase:   suiteRecord.SuccessCase,
		FailureCase:   suiteRecord.FailureCase,
		SuiteId:       suiteRecord.SuiteId,
		SuiteName:     suiteRecord.SuiteName,
		Content:       &content,
		GeneralConfig: generalConfig,
		AccountConfig: accountConfig,
	}

	if caseItems == nil {
		resp.CaseItems = make([]*pb.GetSuiteRecordResponse_CaseItem, 0)
	} else {
		resp.CaseItems = caseItems
	}

	return resp, nil
}
