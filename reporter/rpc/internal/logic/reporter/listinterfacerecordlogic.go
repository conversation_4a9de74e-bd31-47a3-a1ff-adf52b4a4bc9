package reporterlogic

import (
	"context"
	"fmt"
	"math"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListInterfaceRecordLogic struct {
	*BaseLogic
}

func NewListInterfaceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListInterfaceRecordLogic {
	return &ListInterfaceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 接口`调试`记录列表
func (l *ListInterfaceRecordLogic) ListInterfaceRecord(in *pb.ListInterfaceRecordRequest) (resp *pb.ListInterfaceRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	countBuilder := l.svcCtx.InterfaceExecutionRecordModel.SelectCountBuilder().Where(
		"`project_id` = ? AND `interface_id` = ? AND `execute_type` = 'INTERFACE_DOCUMENT'",
		in.ProjectId, in.InterfaceId)

	count, err2 := l.svcCtx.InterfaceExecutionRecordModel.FindCount(l.ctx, countBuilder)
	if err2 != nil {
		msg := fmt.Sprintf(
			"faile to count interface record with project_id[%s]、interface_id[%s], error: %+v",
			in.ProjectId, in.InterfaceId, err2)
		err = errorx.Err(errorx.DBError, msg)
		return nil, err
	}

	resp = &pb.ListInterfaceRecordResponse{
		TotalCount: uint64(count),
		TotalPage:  1,
	}

	selectBuilder := l.svcCtx.InterfaceExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `interface_id` = ? AND `execute_type` = 'INTERFACE_DOCUMENT'",
		in.ProjectId, in.InterfaceId)

	if len(in.Sort) == 0 {
		sortField := rpc.SortField{
			Field: "started_at",
			Order: "desc",
		}
		in.Sort = append(in.Sort, &sortField)
	}

	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithPagination(l.svcCtx.InterfaceExecutionRecordModel, in.Pagination),
		sqlbuilder.WithSort(l.svcCtx.InterfaceExecutionRecordModel, in.Sort),
	)
	interfaceRecords, err3 := l.svcCtx.InterfaceExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err3 != nil {
		msg := fmt.Sprintf(
			"failed to find interface record with project_id[%s]、interface_id[%s], error: %+v",
			in.ProjectId, in.InterfaceId, err3)
		err = errorx.Err(errorx.DBError, msg)
		return nil, err
	}

	// 避免返回null给调用者
	resp.Items = make([]*pb.ListInterfaceRecordResponse_InterfaceRecord, 0, len(interfaceRecords))
	for _, interfaceRecord := range interfaceRecords {
		r := &pb.ListInterfaceRecordResponse_InterfaceRecord{
			TaskId:             interfaceRecord.TaskId,
			ExecuteId:          interfaceRecord.ExecuteId,
			ProjectId:          interfaceRecord.ProjectId,
			InterfaceExecuteId: interfaceRecord.InterfaceExecuteId,
			InterfaceId:        interfaceRecord.InterfaceId,
			InterfaceName:      interfaceRecord.InterfaceName,
			ExecutedBy:         interfaceRecord.ExecutedBy,
			StartedAt:          interfaceRecord.StartedAt,
			EndedAt:            interfaceRecord.EndedAt.Int64,
			CostTime:           interfaceRecord.CostTime,
			TotalCase:          interfaceRecord.TotalCase,
			SuccessCase:        interfaceRecord.SuccessCase,
			FailureCase:        interfaceRecord.FailureCase,
			Status:             interfaceRecord.Status.String,
			Cleaned:            interfaceRecord.Cleaned,
		}
		resp.Items = append(resp.Items, r)
	}

	if in.Pagination != nil {
		resp.CurrentPage = in.Pagination.CurrentPage
		resp.PageSize = in.Pagination.PageSize
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
