package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	structPb "google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonPb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetInterfaceRecordLogic struct {
	*BaseLogic
}

func NewGetInterfaceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInterfaceRecordLogic {
	return &GetInterfaceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 接口执行记录详情
func (l *GetInterfaceRecordLogic) GetInterfaceRecord(in *pb.GetInterfaceRecordRequest) (resp *pb.GetInterfaceRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	selectBuilder := l.svcCtx.InterfaceExecutionRecordModel.SelectBuilder()
	selectBuilder = selectBuilder.Where("`task_id` = ?  AND `project_id` = ? AND `execute_id` = ? AND `interface_execute_id` = ?",
		in.TaskId, in.ProjectId, in.InterfaceExecuteId, in.InterfaceExecuteId).Limit(1)

	interfaceRecords, err2 := l.svcCtx.InterfaceExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)

	msg := fmt.Sprintf("interface interfaceRecord detail with task_id[%s] project_id[%s] and interface_execute_id[%s]",
		in.TaskId, in.ProjectId, in.InterfaceExecuteId)

	if (err2 != nil) || (len(interfaceRecords) == 0) {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
		return nil, err
	}

	var (
		successCase, failureCase int64
		caseItems                []*pb.GetInterfaceRecordResponse_CaseItem
	)

	// 获取关联测试用例执行情况
	selectCaseRecordBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `parent_component_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.InterfaceExecuteId)
	caseRecords, err3 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectCaseRecordBuilder)

	if err3 != nil {
		err = err3
		return nil, err
	}

	for _, caseRecord := range caseRecords {
		if caseRecord.Status.String == "Success" {
			successCase += 1
		} else if caseRecord.Status.String == "Failure" {
			failureCase += 1
		}
		item := pb.GetInterfaceRecordResponse_CaseItem{
			TaskId:        caseRecord.TaskId,
			ExecuteId:     caseRecord.ExecuteId,
			ProjectId:     caseRecord.ProjectId,
			CaseExecuteId: caseRecord.ComponentExecuteId,
			CaseId:        caseRecord.ComponentId,
			CaseName:      caseRecord.ComponentName,
			Version:       caseRecord.Version,
			StartedAt:     caseRecord.StartedAt,
			EndedAt:       caseRecord.EndedAt.Int64,
			CostTime:      caseRecord.CostTime,
			Status:        caseRecord.Status.String,
			MaintainedBy:  caseRecord.MaintainedBy,
		}
		caseItems = append(caseItems, &item)
	}

	interfaceRecord := interfaceRecords[0]

	var content structPb.Struct
	_ = protobuf.UnmarshalJSON([]byte(interfaceRecord.Content.String), &content)

	var generalConfig *commonPb.GeneralConfig
	var accountConfig []*commonPb.AccountConfig
	_ = json.Unmarshal([]byte(interfaceRecord.GeneralConfig.String), &generalConfig)
	_ = json.Unmarshal([]byte(interfaceRecord.AccountConfig.String), &accountConfig)

	resp = &pb.GetInterfaceRecordResponse{
		CostTime:      interfaceRecord.CostTime,
		ExecutedBy:    interfaceRecord.ExecutedBy,
		StartedAt:     interfaceRecord.StartedAt,
		EndedAt:       interfaceRecord.EndedAt.Int64,
		TotalCase:     interfaceRecord.TotalCase,
		SuccessCase:   interfaceRecord.SuccessCase,
		FailureCase:   interfaceRecord.FailureCase,
		InterfaceId:   interfaceRecord.InterfaceId,
		InterfaceName: interfaceRecord.InterfaceName,
		Content:       &content,
		GeneralConfig: generalConfig,
		AccountConfig: accountConfig,
	}

	if caseItems == nil {
		resp.CaseItems = make([]*pb.GetInterfaceRecordResponse_CaseItem, 0)
	} else {
		resp.CaseItems = caseItems
	}

	return resp, nil
}
