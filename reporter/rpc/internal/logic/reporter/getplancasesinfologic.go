package reporterlogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPlanCasesInfoLogic struct {
	*BaseLogic
}

func NewGetPlanCasesInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPlanCasesInfoLogic {
	return &GetPlanCasesInfoLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetPlanCasesInfo 获取UI计划关联用例信息
func (l *GetPlanCasesInfoLogic) GetPlanCasesInfo(in *pb.GetPlanCasesInfoRequest) (resp *pb.GetPlanCasesInfoResponse, err error) {
	totalCase, failureCase, successCase, err := l.getPlanCaseInfoByCalculate(in.ProjectId, in.TaskId, in.ExecuteId)
	if err != nil {
		return nil, err
	}
	return &pb.GetPlanCasesInfoResponse{TotalCase: totalCase, FailureCase: failureCase, SuccessCase: successCase}, nil
}
