package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyServiceRecordLogic struct {
	*BaseLogic
}

func NewModifyServiceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyServiceRecordLogic {
	return &ModifyServiceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 修改精准测试服务记录
func (l *ModifyServiceRecordLogic) ModifyServiceRecord(in *pb.PutServiceRecordRequest) (resp *pb.ModifyServiceRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	msg := fmt.Sprintf(
		"rpc component pb with task_id[%s] and project_id[%s] and execute_id[%s] and service_execute_id[%s]",
		in.TaskId, in.ProjectId, in.ExecuteId, in.ServiceExecuteId)

	redisKey := fmt.Sprintf("%s%s_%s_%s_%s", common.ConstRedisServiceRecord, in.TaskId, in.ProjectId, in.ExecuteId, in.ServiceExecuteId)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)

	// 获取之前创建的执行记录
	record := &model.ServiceExecutionRecord{}
	if redisErr == nil && recordStr != "" {
		_ = json.Unmarshal([]byte(recordStr), record)
	} else {
		selectBuilder := l.svcCtx.ServiceExecutionRecordModel.SelectBuilder()
		selectBuilder = selectBuilder.Where(
			"`task_id` = ? AND `project_id` = ? AND `execute_id` = ? AND `service_execute_id` = ?",
			in.TaskId, in.ProjectId, in.ExecuteId, in.ServiceExecuteId).Limit(1)
		result, err2 := l.svcCtx.ServiceExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
		if err2 != nil || len(result) == 0 {
			err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
			return nil, err
		}
		record = result[0]
	}

	record = ConvertServiceModel(in, record)
	err = l.svcCtx.ServiceExecutionRecordModel.UpdateRecordWithDefaultUpdatedAt(l.ctx, record)
	if err != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to update %s, error: %+v", msg, err))
		return nil, err
	}

	_, _ = l.svcCtx.Redis.Del(redisKey)

	if !l.isFinishedStatus(record.Status.String) {
		executionRecordBytes, _ := json.Marshal(record)
		executionRecordStr := string(executionRecordBytes)

		redisErr = l.svcCtx.Redis.Setex(redisKey, executionRecordStr, 600) // 10分钟
		if redisErr != nil {
			redisErr = errorx.Err(errorx.RedisError, fmt.Sprintf("modify record success, but encounter redis error: %+v", redisErr))
			l.Logger.Warnf(redisErr.Error())
		}
	}

	return &pb.ModifyServiceRecordResponse{}, nil
}
