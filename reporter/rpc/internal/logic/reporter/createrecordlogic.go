package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateRecordLogic struct {
	*BaseLogic
}

func NewCreateRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateRecordLogic {
	return &CreateRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateRecordLogic) CreateRecord(in *pb.PutRecordRequest) (*pb.CreateRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.<PERSON><PERSON>.Errorf(err.Error())
		}
	}()

	err = CheckNotNull(in)
	if err != nil {
		return nil, err
	}

	record := ConvertCaseModel(in, nil)
	result, insertErr := l.svcCtx.ExecutionRecordModel.InsertRecordWithDefaultCreatedAt(
		l.ctx,
		record,
	)

	if insertErr != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to create record, error: %+v", insertErr))
		return nil, err
	}

	recordId, _ := result.LastInsertId()
	record.Id = recordId

	executionRecordBytes, _ := json.Marshal(record)
	executionRecordStr := string(executionRecordBytes)

	redisKey := fmt.Sprintf("%s%s_%s_%s_%s_%d", common.ConstRedisRecord, in.TaskId, in.ProjectId, in.ExecuteId, record.ComponentExecuteId, in.Times)
	redisErr := l.svcCtx.Redis.Setex(redisKey, executionRecordStr, 300) // 5分钟
	if redisErr != nil {
		redisErr = errorx.Err(errorx.RedisError, fmt.Sprintf("create record success, but encounter redis error: %+v", redisErr))
		l.Logger.Warnf(redisErr.Error())
	}

	resp := &pb.CreateRecordResponse{
		ComponentExecuteId: record.ComponentExecuteId,
	}

	return resp, nil
}
