package reporterlogic

import (
	"fmt"
	"strings"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func CheckNotNull(in *pb.PutRecordRequest) error {
	var nullParams []string
	if in.TaskId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyTaskID)
	}
	if in.ProjectId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyProjectID)
	}
	if in.ExecuteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyExecuteID)
	}
	if in.ExecuteType == "" {
		nullParams = append(nullParams, common.ReqFieldKeyExecuteType)
	}
	if in.ComponentId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyComponentID)
	}
	if in.ComponentType == "" {
		nullParams = append(nullParams, common.ReqFieldKeyComponentType)
	}

	if len(nullParams) != 0 {
		nullParamStr := strings.Join(nullParams, "、")
		err := fmt.Sprintf("以下参数不可为空：%s", nullParamStr)
		return errorx.Err(errorx.ValidateParamError, fmt.Sprintf("failed to create record, error: %+v", err))
	}

	return nil
}

func CheckNotNullInterface(in *pb.PutInterfaceRecordRequest) error {
	var nullParams []string
	if in.TaskId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyTaskID)
	}
	if in.ProjectId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyProjectID)
	}
	if in.ExecuteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyExecuteID)
	}
	if in.InterfaceId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyInterfaceID)
	}
	if in.InterfaceExecuteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyInterfaceExecuteID)
	}

	if len(nullParams) != 0 {
		nullParamStr := strings.Join(nullParams, "、")
		err := fmt.Sprintf("以下参数不可为空：%s", nullParamStr)
		return errorx.Err(errorx.ValidateParamError, fmt.Sprintf("failed to create record, error: %+v", err))
	}

	return nil
}

func CheckNotNullSuite(in *pb.PutSuiteRecordRequest) error {
	var nullParams []string
	if in.TaskId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyTaskID)
	}
	if in.ProjectId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyProjectID)
	}
	if in.ExecuteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyExecuteID)
	}
	if in.SuiteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeySuiteID)
	}
	if in.SuiteExecuteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeySuiteExecuteID)
	}

	if len(nullParams) != 0 {
		nullParamStr := strings.Join(nullParams, "、")
		err := fmt.Sprintf("以下参数不可为空：%s", nullParamStr)
		return errorx.Err(errorx.ValidateParamError, fmt.Sprintf("failed to create record, error: %+v", err))
	}

	return nil
}

func CheckNotNullPlan(in *pb.PutPlanRecordRequest, flag string) error {
	var nullParams []string
	if in.TaskId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyTaskID)
	}
	if in.ProjectId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyProjectID)
	}
	if in.ExecuteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyExecuteID)
	}
	if in.PlanId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyPlanID)
	}

	if len(nullParams) != 0 {
		nullParamStr := strings.Join(nullParams, "、")
		err := fmt.Sprintf("以下参数不可为空：%s", nullParamStr)
		return errorx.Err(errorx.ValidateParamError, fmt.Sprintf("failed to create record, error: %+v", err))
	}

	return nil
}
