package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreatePlanRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

type TestInfo struct {
	NoCaseServiceS []string
}

func NewCreatePlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePlanRecordLogic {
	return &CreatePlanRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreatePlanRecordLogic) CreatePlanRecord(in *pb.PutPlanRecordRequest) (*pb.CreatePlanRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	err = CheckNotNullPlan(in, " create")
	if err != nil {
		return nil, err
	}

	record := ConvertPlanModel(in, nil)
	result, insertErr := l.svcCtx.PlanExecutionRecordModel.InsertRecordWithDefaultCreatedAt(l.ctx, record)

	if insertErr != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to create record, error: %+v", insertErr))
		return nil, err
	}

	recordId, _ := result.LastInsertId()
	record.Id = recordId

	executionRecordBytes, _ := json.Marshal(record)
	executionRecordStr := string(executionRecordBytes)

	redisKey := fmt.Sprintf("%s%s_%s_%s_%s", common.ConstRedisPlanRecord, in.TaskId, in.ProjectId, in.ExecuteId, record.PlanExecuteId)
	redisErr := l.svcCtx.Redis.Setex(redisKey, executionRecordStr, 600) // 10分钟
	if redisErr != nil {
		redisErr = errorx.Err(errorx.RedisError, fmt.Sprintf("create record success, but encounter redis error: %+v", redisErr))
		l.Logger.Warnf(redisErr.Error())
	}

	resp := &pb.CreatePlanRecordResponse{
		PlanExecuteId: record.PlanExecuteId,
	}

	return resp, nil
}
