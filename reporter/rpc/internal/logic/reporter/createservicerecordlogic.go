package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateServiceRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateServiceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateServiceRecordLogic {
	return &CreateServiceRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// `精准测试服务`
func (l *CreateServiceRecordLogic) CreateServiceRecord(in *pb.PutServiceRecordRequest) (out *pb.CreateServiceRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	record := ConvertServiceModel(in, nil)
	result, insertErr := l.svcCtx.ServiceExecutionRecordModel.InsertRecordWithDefaultCreatedAt(l.ctx, record)

	if insertErr != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to create record, error: %+v", insertErr))
		return nil, err
	}

	recordId, _ := result.LastInsertId()
	record.Id = recordId

	executionRecordBytes, _ := json.Marshal(record)
	executionRecordStr := string(executionRecordBytes)

	redisKey := fmt.Sprintf("%s%s_%s_%s_%s", common.ConstRedisServiceRecord, in.TaskId, in.ProjectId, in.ExecuteId, record.ServiceExecuteId)
	redisErr := l.svcCtx.Redis.Setex(redisKey, executionRecordStr, 600) // 10分钟
	if redisErr != nil {
		redisErr = errorx.Err(errorx.RedisError, fmt.Sprintf("create record success, but encounter redis error: %+v", redisErr))
		l.Logger.Warnf(redisErr.Error())
	}

	resp := &pb.CreateServiceRecordResponse{
		ServiceExecuteId: record.ServiceExecuteId,
	}

	return resp, nil
}
