package reporterlogic

import (
	"database/sql"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func ConvertPlanModel(in *pb.PutPlanRecordRequest, old *model.PlanExecutionRecord) (out *model.PlanExecutionRecord) {
	if old == nil {
		old = &model.PlanExecutionRecord{}
	}

	out = &model.PlanExecutionRecord{
		Id:          old.Id,
		TaskId:      StringIfOr(old.TaskId, in.GetTaskId()),
		ProjectId:   StringIfOr(old.ProjectId, in.GetProjectId()),
		ExecuteId:   StringIfOr(old.ExecuteId, in.GetExecuteId()),
		ExecuteType: "API_PLAN",
		GeneralConfig: sql.NullString{
			String: StringIfOr(in.GetGeneralConfig(), old.GeneralConfig.String),
			Valid:  StringIfOr(in.GetGeneralConfig(), old.GeneralConfig.String) != "",
		},
		AccountConfig: sql.NullString{
			String: StringIfOr(in.GetAccountConfig(), old.AccountConfig.String),
			Valid:  StringIfOr(in.GetAccountConfig(), old.GeneralConfig.String) != "",
		},
		PlanId:        StringIfOr(old.PlanId, in.GetPlanId()),
		PlanExecuteId: StringIfOr(old.PlanExecuteId, StringIfOr(in.GetPlanExecuteId(), utils.GenExecuteId())),
		PlanName:      StringIfOr(in.GetPlanName(), old.PlanName),
		TriggerMode:   StringIfOr(in.GetTriggerMode(), old.TriggerMode),
		PlanPurpose:   StringIfOr(in.GetPlanPurpose(), old.PlanPurpose),
		TotalSuite:    Int64IfOr(in.GetTotalSuite(), old.TotalSuite),
		SuccessSuite:  Int64IfOr(in.GetSuccessSuite(), old.SuccessSuite),
		FailureSuite:  Int64IfOr(in.GetFailureSuite(), old.FailureSuite),
		TotalCase:     Int64IfOr(in.GetTotalCase(), old.TotalCase),
		SuccessCase:   Int64IfOr(in.GetSuccessCase(), old.SuccessCase),
		FailureCase:   Int64IfOr(in.GetFailureCase(), old.FailureCase),
		Status: sql.NullString{
			String: StringIfOr(in.GetStatus(), old.Status.String),
			Valid:  StringIfOr(in.GetStatus(), old.Status.String) != "",
		},
		Content: sql.NullString{
			String: StringIfOr(in.GetContent(), old.Content.String),
			Valid:  StringIfOr(in.GetContent(), old.Content.String) != "",
		},
		ServiceCasesContent: sql.NullString{
			String: StringIfOr(
				in.GetServiceCasesContent(), old.ServiceCasesContent.String,
			), Valid: StringIfOr(in.GetServiceCasesContent(), old.ServiceCasesContent.String) != "",
		},
		ExecutedBy: StringIfOr(in.GetExecutedBy(), old.ExecutedBy),
		ApprovedBy: sql.NullString{
			String: StringIfOr(in.GetApprovedBy(), old.ApprovedBy.String),
			Valid:  StringIfOr(in.GetApprovedBy(), old.ApprovedBy.String) != "",
		},
		StartedAt: Int64IfOr(in.GetStartedAt(), old.StartedAt),
		EndedAt: sql.NullInt64{
			Int64: Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64),
			Valid: Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64) > 0,
		},
		CreatedAt: old.CreatedAt,
		UpdatedAt: old.UpdatedAt,
		Cleaned:   old.Cleaned,
	}

	if out.EndedAt.Int64 > out.StartedAt {
		out.CostTime = time.UnixMilli(out.EndedAt.Int64).Sub(time.UnixMilli(out.StartedAt)).Milliseconds()
	}

	return out
}
