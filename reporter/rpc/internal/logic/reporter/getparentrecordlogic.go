package reporterlogic

import (
	"context"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetParentRecordLogic struct {
	*BaseLogic
}

func NewGetParentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetParentRecordLogic {
	return &GetParentRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetParentRecordLogic) GetParentRecord(in *pb.GetParentRecordRequest) (*pb.GetParentRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.<PERSON>.<PERSON>rrorf(err.<PERSON><PERSON>r())
		}
	}()

	msg := fmt.Sprintf("record detail with task_id[%s] project_id[%s] execute_type[%s] component_type[%s] "+
		"and component_execute_id[%s]", in.TaskId, in.ProjectId, in.ExecuteType, in.ComponentType, in.ComponentExecuteId)

	// 先实现`接口`和`集合`
	var parentExecuteId string

	if in.ComponentType == "INTERFACE_DOCUMENT" {
		selectBuilder := l.svcCtx.InterfaceExecutionRecordModel.SelectBuilder()
		selectBuilder = selectBuilder.Where("`task_id` = ?  AND `project_id` = ? AND `execute_id` = ? AND `interface_execute_id` = ?",
			in.TaskId, in.ProjectId, in.ExecuteId, in.ComponentExecuteId).Limit(1)
		interfaceRecords, err2 := l.svcCtx.InterfaceExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
		if err2 != nil {
			err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
			return nil, err
		}
		if len(interfaceRecords) == 0 {
			err = errorx.Err(errorx.NotExists, fmt.Sprintf("not found %s", msg))
			return nil, err
		}
		parentExecuteId = interfaceRecords[0].PlanExecuteId.String
	} else if in.ComponentType == "API_SUITE" {
		selectBuilder := l.svcCtx.SuiteExecutionRecordModel.SelectBuilder()
		selectBuilder = selectBuilder.Where("`task_id` = ?  AND `project_id` = ? AND `execute_id` = ? AND `suite_execute_id` = ?",
			in.TaskId, in.ProjectId, in.ComponentExecuteId, in.ComponentExecuteId).Limit(1)
		interfaceRecords, err2 := l.svcCtx.SuiteExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
		if err2 != nil {
			err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
			return nil, err
		}
		if len(interfaceRecords) == 0 {
			err = errorx.Err(errorx.NotExists, fmt.Sprintf("not found %s", msg))
			return nil, err
		}
		parentExecuteId = interfaceRecords[0].PlanExecuteId.String
	} else if in.ComponentType == "API_SERVICE" {
		selectBuilder := l.svcCtx.ServiceExecutionRecordModel.SelectBuilder()
		selectBuilder = selectBuilder.Where("`task_id` = ?  AND `project_id` = ? AND `execute_id` = ? AND `service_execute_id` = ?",
			in.TaskId, in.ProjectId, in.ComponentExecuteId, in.ComponentExecuteId).Limit(1)
		serviceRecords, err2 := l.svcCtx.ServiceExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
		if err2 != nil {
			err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
			return nil, err
		}
		if len(serviceRecords) == 0 {
			err = errorx.Err(errorx.NotExists, fmt.Sprintf("not found %s", msg))
			return nil, err
		}
		parentExecuteId = serviceRecords[0].PlanExecuteId.String
	}

	if parentExecuteId == "" {
		err = errorx.Err(errorx.NotExists, "no parent record")
		return nil, err
	}

	selectBuilder := l.svcCtx.PlanExecutionRecordModel.SelectBuilder()
	selectBuilder = selectBuilder.Where("`task_id` = ?  AND `project_id` = ? AND `execute_id` = ? AND `plan_execute_id` = ?",
		in.TaskId, in.ProjectId, parentExecuteId, parentExecuteId).Limit(1)

	planRecords, err3 := l.svcCtx.PlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err3 != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find parent %s, error: %+v", msg, err3))
		return nil, err
	}
	if len(planRecords) == 0 {
		err = errorx.Err(errorx.NotExists, fmt.Sprintf("not found parent %s", msg))
		return nil, err
	}

	parentRecord := planRecords[0]

	return &pb.GetParentRecordResponse{
		TaskId:                   parentRecord.TaskId,
		ExecuteId:                parentRecord.ExecuteId,
		ProjectId:                parentRecord.ProjectId,
		ExecuteType:              parentRecord.ExecuteType,
		ComponentId:              parentRecord.PlanId,
		ComponentName:            parentRecord.PlanName,
		ComponentType:            "API_PLAN",
		ComponentExecuteId:       parentRecord.PlanExecuteId,
		ParentComponentExecuteId: "",
		Version:                  "",
		Times:                    1,
		Status:                   parentRecord.Status.String,
		IsRoot:                   1,
		ExecutedBy:               parentRecord.ExecutedBy,
		StartedAt:                parentRecord.StartedAt,
		EndedAt:                  parentRecord.EndedAt.Int64,
		CostTime:                 parentRecord.CostTime,
	}, nil
}
