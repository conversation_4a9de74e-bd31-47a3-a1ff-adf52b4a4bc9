package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyInterfaceRecordLogic struct {
	*BaseLogic
}

func NewModifyInterfaceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceRecordLogic {
	return &ModifyInterfaceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyInterfaceRecordLogic) ModifyInterfaceRecord(in *pb.PutInterfaceRecordRequest) (*pb.ModifyInterfaceRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	err = CheckNotNullInterface(in)
	if err != nil {
		return nil, err
	}

	msg := fmt.Sprintf(
		"rpc component pb with task_id[%s] and project_id[%s] and execute_id[%s] and interface_execute_id[%s]",
		in.TaskId, in.ProjectId, in.ExecuteId, in.InterfaceExecuteId)

	redisKey := fmt.Sprintf("%s%s_%s_%s_%s", common.ConstRedisInterfaceRecord, in.TaskId, in.ProjectId, in.ExecuteId, in.InterfaceExecuteId)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)

	// 获取之前创建的执行记录
	record := &model.InterfaceExecutionRecord{}
	if redisErr == nil && recordStr != "" {
		_ = json.Unmarshal([]byte(recordStr), record)
	} else {
		selectBuilder := l.svcCtx.InterfaceExecutionRecordModel.SelectBuilder()
		selectBuilder = selectBuilder.Where(
			"`task_id` = ? AND `project_id` = ? AND `execute_id` = ? AND `interface_execute_id` = ?",
			in.TaskId, in.ProjectId, in.ExecuteId, in.InterfaceExecuteId).Limit(1)
		result, err2 := l.svcCtx.InterfaceExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
		if err2 != nil || len(result) == 0 {
			err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
			return nil, err
		}
		record = result[0]
	}

	record = ConvertInterfaceModel(in, record)
	err = l.svcCtx.InterfaceExecutionRecordModel.UpdateRecordWithDefaultUpdatedAt(l.ctx, record)
	if err != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to update %s, error: %+v", msg, err))
		return nil, err
	}

	_, _ = l.svcCtx.Redis.Del(redisKey)

	if !l.isFinishedStatus(record.Status.String) {
		executionRecordBytes, _ := json.Marshal(record)
		executionRecordStr := string(executionRecordBytes)

		redisErr = l.svcCtx.Redis.Setex(redisKey, executionRecordStr, 600) // 10分钟
		if redisErr != nil {
			redisErr = errorx.Err(errorx.RedisError, fmt.Sprintf("modify record success, but encounter redis error: %+v", redisErr))
			l.Logger.Warnf(redisErr.Error())
		}
	}

	return &pb.ModifyInterfaceRecordResponse{}, nil
}
