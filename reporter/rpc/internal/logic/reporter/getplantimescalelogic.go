package reporterlogic

import (
	"context"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPlanTimeScaleLogic struct {
	*BaseLogic
}

func NewGetPlanTimeScaleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPlanTimeScaleLogic {
	return &GetPlanTimeScaleLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 计划执行详情查看其下各集合用例执行时间刻度信息
func (l *GetPlanTimeScaleLogic) GetPlanTimeScale(in *pb.GetPlanTimeScaleRequest) (resp *pb.GetPlanTimeScaleResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	selectBuilder := l.svcCtx.PlanExecutionRecordModel.SelectBuilderWithEmptyContent()
	selectBuilder = selectBuilder.Where("`task_id` = ?  AND `project_id` = ? AND `execute_id`= ? AND `plan_execute_id` = ?",
		in.TaskId, in.ProjectId, in.PlanExecuteId, in.PlanExecuteId).Limit(1)

	planRecords, err2 := l.svcCtx.PlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)

	msg := fmt.Sprintf("plan record detail with task_id[%s] project_id[%s] and plan_execute_id[%s]",
		in.TaskId, in.ProjectId, in.PlanExecuteId)

	if (err2 != nil) || (len(planRecords) == 0) {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
		return nil, err
	}

	var suiteItems []*pb.GetPlanTimeScaleResponse_SuiteRecord

	// 获取关联`集合`执行情况
	selectSuiteRecordBuilder := l.svcCtx.SuiteExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `execute_id`= ? AND `plan_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.PlanExecuteId, in.PlanExecuteId)
	suiteRecords, err3 := l.svcCtx.SuiteExecutionRecordModel.FindByQuery(l.ctx, selectSuiteRecordBuilder)

	if err3 != nil {
		err = err3
		return nil, err
	}

	// 获取关联`接口`执行情况
	selectInterfaceRecordBuilder := l.svcCtx.InterfaceExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `execute_id`= ? AND `plan_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.PlanExecuteId, in.PlanExecuteId)
	interfaceRecords, err4 := l.svcCtx.InterfaceExecutionRecordModel.FindByQuery(l.ctx, selectInterfaceRecordBuilder)
	if err4 != nil {
		err = err4
		return nil, err
	}

	// 获取关联`服务`执行情况
	selectServiceRecordBuilder := l.svcCtx.ServiceExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `execute_id`= ? AND `plan_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.PlanExecuteId, in.PlanExecuteId)
	serviceRecords, err5 := l.svcCtx.ServiceExecutionRecordModel.FindByQuery(l.ctx, selectServiceRecordBuilder)
	if err5 != nil {
		err = err5
		return nil, err
	}

	for _, suiteRecord := range suiteRecords {
		selectCaseRecordBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
			"`task_id` = ? AND `project_id` = ? AND `parent_component_execute_id` = ? ",
			suiteRecord.TaskId, suiteRecord.ProjectId, suiteRecord.SuiteExecuteId)
		caseRecords, err5 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectCaseRecordBuilder)
		if err5 != nil {
			err = err5
			return nil, err
		}

		var caseItems []*pb.GetPlanTimeScaleResponse_CaseRecord
		for _, caseRecord := range caseRecords {
			caseItem := pb.GetPlanTimeScaleResponse_CaseRecord{
				CaseId:    caseRecord.ComponentId,
				CaseName:  caseRecord.ComponentName,
				StartedAt: caseRecord.StartedAt,
				EndedAt:   caseRecord.EndedAt.Int64,
				CostTime:  caseRecord.CostTime,
				Status:    caseRecord.Status.String,
			}
			caseItems = append(caseItems, &caseItem)
		}

		item := pb.GetPlanTimeScaleResponse_SuiteRecord{
			SuiteId:   suiteRecord.SuiteId,
			SuiteName: suiteRecord.SuiteName,
			CaseItems: caseItems,
		}
		suiteItems = append(suiteItems, &item)
	}

	for _, interfaceRecord := range interfaceRecords {
		selectCaseRecordBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
			"`task_id` = ? AND `project_id` = ? AND `parent_component_execute_id` = ? ",
			interfaceRecord.TaskId, interfaceRecord.ProjectId, interfaceRecord.InterfaceExecuteId)
		caseRecords, err6 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectCaseRecordBuilder)

		if err6 != nil {
			err = err6
			return nil, err
		}

		var caseItems []*pb.GetPlanTimeScaleResponse_CaseRecord
		for _, caseRecord := range caseRecords {
			caseItem := pb.GetPlanTimeScaleResponse_CaseRecord{
				CaseId:    caseRecord.ComponentId,
				CaseName:  caseRecord.ComponentName,
				StartedAt: caseRecord.StartedAt,
				EndedAt:   caseRecord.EndedAt.Int64,
				CostTime:  caseRecord.CostTime,
				Status:    caseRecord.Status.String,
			}
			caseItems = append(caseItems, &caseItem)
		}

		item := pb.GetPlanTimeScaleResponse_SuiteRecord{
			SuiteId:   interfaceRecord.InterfaceId,
			SuiteName: interfaceRecord.InterfaceName,
			CaseItems: caseItems,
		}
		suiteItems = append(suiteItems, &item)
	}

	for _, serviceRecord := range serviceRecords {
		selectCaseRecordBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
			"`task_id` = ? AND `project_id` = ? AND `parent_component_execute_id` = ? ",
			serviceRecord.TaskId, serviceRecord.ProjectId, serviceRecord.ServiceExecuteId)
		caseRecords, err7 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectCaseRecordBuilder)
		if err7 != nil {
			err = err7
			return nil, err
		}

		var caseItems []*pb.GetPlanTimeScaleResponse_CaseRecord
		for _, caseRecord := range caseRecords {
			caseItem := pb.GetPlanTimeScaleResponse_CaseRecord{
				CaseId:    caseRecord.ComponentId,
				CaseName:  caseRecord.ComponentName,
				StartedAt: caseRecord.StartedAt,
				EndedAt:   caseRecord.EndedAt.Int64,
				CostTime:  caseRecord.CostTime,
				Status:    caseRecord.Status.String,
			}
			caseItems = append(caseItems, &caseItem)
		}

		item := pb.GetPlanTimeScaleResponse_SuiteRecord{
			SuiteId:   serviceRecord.ServiceId,
			SuiteName: serviceRecord.ServiceName,
			CaseItems: caseItems,
		}
		suiteItems = append(suiteItems, &item)
	}

	resp = &pb.GetPlanTimeScaleResponse{
		SuiteItems: suiteItems,
	}

	return resp, nil
}
