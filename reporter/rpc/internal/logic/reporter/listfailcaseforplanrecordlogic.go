package reporterlogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListFailCaseForPlanRecordLogic struct {
	*BaseLogic
}

func NewListFailCaseForPlanRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ListFailCaseForPlanRecordLogic {
	return &ListFailCaseForPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ListFailCaseForPlanRecordLogic) ListFailCaseForPlanRecord(in *pb.ListFailCaseRecordForPlanRequest) (
	resp *pb.ListFailCaseRecordForPlanResponse, err error,
) {
	resp = &pb.ListFailCaseRecordForPlanResponse{}

	req := model.SearchCaseFailForPlanStatReq{
		ProjectId:  in.GetProjectId(),
		CaseId:     in.GetCaseId(),
		CaseType:   in.GetCaseType(),
		Pagination: in.GetPagination(),
	}

	selectBuilder, countBuilder := l.svcCtx.CaseFailForPlanStatModel.GenerateListFailCaseForPlanRecordQuery(req)

	count, err := l.svcCtx.CaseFailForPlanStatModel.FindCount(l.ctx, countBuilder.SelectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to count case with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	cases, err := l.svcCtx.CaseFailForPlanStatModel.FindJoinPlanNoCacheByQuery(l.ctx, selectBuilder.SelectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find case with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	pagination := in.GetPagination()

	resp.Items = make([]*pb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord, 0, len(cases))
	for _, _Case := range cases {
		item := &pb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord{}
		if err = utils.Copy(item, _Case, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy case[%+v] to response, error: %+v",
				_Case, err,
			)
		}

		resp.Items = append(resp.Items, item)
	}

	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
