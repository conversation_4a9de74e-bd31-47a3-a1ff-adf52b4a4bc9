package reporterlogic

import (
	"context"
	"fmt"
	"math"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListPlanRecordLogic struct {
	*BaseLogic
}

func NewListPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListPlanRecordLogic {
	return &ListPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ListPlanRecord 计划执行记录列表
func (l *ListPlanRecordLogic) ListPlanRecord(in *pb.ListPlanRecordRequest) (resp *pb.ListPlanRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	countBuilder := l.svcCtx.PlanExecutionRecordModel.SelectCountBuilder().Where(
		"`project_id` = ? AND `plan_id` = ?",
		in.ProjectId, in.PlanId)

	count, err2 := l.svcCtx.PlanExecutionRecordModel.FindCount(l.ctx, countBuilder)
	if err2 != nil {
		msg := fmt.Sprintf(
			"faile to count plan record with project_id[%s]、plan_id[%s] error: %+v",
			in.ProjectId, in.PlanId, err2)
		err = errorx.Err(errorx.DBError, msg)
		return nil, err
	}
	resp = &pb.ListPlanRecordResponse{
		TotalCount: uint64(count),
		TotalPage:  1,
	}

	selectBuilder := l.svcCtx.PlanExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `plan_id` = ?", in.ProjectId, in.PlanId)

	if len(in.Sort) == 0 {
		sortField := rpc.SortField{
			Field: "started_at",
			Order: "desc",
		}
		in.Sort = append(in.Sort, &sortField)
	}

	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithPagination(l.svcCtx.PlanExecutionRecordModel, in.Pagination),
		sqlbuilder.WithSort(l.svcCtx.PlanExecutionRecordModel, in.Sort),
	)
	planRecords, err3 := l.svcCtx.PlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err3 != nil {
		msg := fmt.Sprintf(
			"failed to find plan record with project_id[%s]、plan_id[%s] error: %+v",
			in.ProjectId, in.PlanId, err3)
		err = errorx.Err(errorx.DBError, msg)
		return nil, err
	}

	// 避免返回null给调用者
	resp.Items = make([]*pb.ListPlanRecordResponse_PlanRecord, 0, len(planRecords))
	for _, planRecord := range planRecords {
		r := &pb.ListPlanRecordResponse_PlanRecord{
			TaskId:        planRecord.TaskId,
			ProjectId:     planRecord.ProjectId,
			ExecuteId:     planRecord.ExecuteId,
			PlanExecuteId: planRecord.PlanExecuteId,
			ExecutedBy:    planRecord.ExecutedBy,
			StartedAt:     planRecord.StartedAt,
			EndedAt:       planRecord.EndedAt.Int64,
			CostTime:      planRecord.CostTime,
			Status:        planRecord.Status.String,
			TotalSuite:    planRecord.TotalSuite,
			SuccessSuite:  planRecord.SuccessSuite,
			FailureSuite:  planRecord.FailureSuite,
			TotalCase:     planRecord.TotalCase,
			SuccessCase:   planRecord.SuccessCase,
			FailureCase:   planRecord.FailureCase,
			Cleaned:       planRecord.Cleaned,
			PurposeType:   planRecord.PlanPurpose,
			TriggerMode:   planRecord.TriggerMode,
			Type:          planRecord.TriggerMode,
		}
		resp.Items = append(resp.Items, r)
	}

	if in.Pagination != nil {
		resp.CurrentPage = in.Pagination.CurrentPage
		resp.PageSize = in.Pagination.PageSize
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
