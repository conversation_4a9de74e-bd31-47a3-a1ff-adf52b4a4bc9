package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyRecordLogic struct {
	*BaseLogic
}

func NewModifyRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyRecordLogic {
	return &ModifyRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyRecordLogic) ModifyRecord(in *pb.PutRecordRequest) (*pb.ModifyRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.<PERSON>.<PERSON>(err.Error())
		}
	}()

	err = CheckNotNull(in)
	if err != nil {
		return nil, err
	}

	if in.Times == 0 {
		in.Times = 1
	}

	msg := fmt.Sprintf(
		"rpc component pb with task_id[%s] and project_id[%s] and execute_id[%s] and component_execute_id[%s] and times[%d] and component_type[%s]",
		in.TaskId, in.ProjectId, in.ExecuteId, in.ComponentExecuteId, in.Times, in.ComponentType)

	redisKey := fmt.Sprintf("%s%s_%s_%s_%s_%d", common.ConstRedisRecord, in.TaskId, in.ProjectId, in.ExecuteId, in.ComponentExecuteId, in.Times)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)

	// 获取之前创建的执行记录
	record := &model.ExecutionRecord{}
	if redisErr == nil && recordStr != "" {
		_ = json.Unmarshal([]byte(recordStr), record)
	} else {
		selectBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilder()
		selectBuilder = selectBuilder.Where(
			"`task_id` = ? AND `project_id` = ? AND `execute_id` = ? AND `component_execute_id` = ? AND `times` = ?",
			in.TaskId, in.ProjectId, in.ExecuteId, in.ComponentExecuteId, in.Times).Limit(1)
		result, err2 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
		if err2 != nil || len(result) == 0 {
			err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
			return nil, err
		} else {
			if result[0].ComponentType != in.ComponentType {
				err = errors.New(fmt.Sprintf("传递的component_type[%s]不正确", in.ComponentType))
				return nil, err
			}
		}
		record = result[0]
	}

	record = ConvertCaseModel(in, record)
	err = l.svcCtx.ExecutionRecordModel.UpdateRecordWithDefaultUpdatedAt(l.ctx, record)
	if err != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to update %s, error: %+v", msg, err))
		return nil, err
	}

	_, _ = l.svcCtx.Redis.Del(redisKey)

	if !l.isFinishedStatus(record.Status.String) {
		executionRecordBytes, _ := json.Marshal(record)
		executionRecordStr := string(executionRecordBytes)

		redisErr = l.svcCtx.Redis.Setex(redisKey, executionRecordStr, 300) // 5分钟
		if redisErr != nil {
			redisErr = errorx.Err(errorx.RedisError, fmt.Sprintf("modify record success, but encounter redis error: %+v", redisErr))
			l.Logger.Warnf(redisErr.Error())
		}
	}

	return &pb.ModifyRecordResponse{}, nil
}
