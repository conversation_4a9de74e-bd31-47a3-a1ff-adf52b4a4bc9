package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateInterfaceRecordLogic struct {
	*BaseLogic
}

func NewCreateInterfaceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceRecordLogic {
	return &CreateInterfaceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 接口相关的RPC接口
func (l *CreateInterfaceRecordLogic) CreateInterfaceRecord(in *pb.PutInterfaceRecordRequest) (*pb.CreateInterfaceRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.Logger.<PERSON>rrorf(err.Error())
		}
	}()

	err = CheckNotNullInterface(in)
	if err != nil {
		return nil, err
	}

	record := ConvertInterfaceModel(in, nil)
	result, insertErr := l.svcCtx.InterfaceExecutionRecordModel.InsertRecordWithDefaultCreatedAt(l.ctx, record)

	if insertErr != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to create record, error: %+v", insertErr))
		return nil, err
	}

	recordId, _ := result.LastInsertId()
	record.Id = recordId

	executionRecordBytes, _ := json.Marshal(record)
	executionRecordStr := string(executionRecordBytes)

	redisKey := fmt.Sprintf("%s%s_%s_%s_%s", common.ConstRedisInterfaceRecord, in.TaskId, in.ProjectId, in.ExecuteId, record.InterfaceExecuteId)
	redisErr := l.svcCtx.Redis.Setex(redisKey, executionRecordStr, 600) // 10分钟
	if redisErr != nil {
		redisErr = errorx.Err(errorx.RedisError, fmt.Sprintf("create record success, but encounter redis error: %+v", redisErr))
		l.Logger.Warnf(redisErr.Error())
	}

	resp := &pb.CreateInterfaceRecordResponse{
		InterfaceExecuteId: record.InterfaceExecuteId,
	}

	return resp, nil
}
