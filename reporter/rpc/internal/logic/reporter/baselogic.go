package reporterlogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"

	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
)

var finishedStatusMap = map[common.RecordStatus]lang.PlaceholderType{
	common.Success: lang.Placeholder,
	common.Skip:    lang.Placeholder,
	common.Failure: lang.Placeholder,
	common.Panic:   lang.Placeholder,
	common.Stop:    lang.Placeholder,
	common.Invalid: lang.Placeholder,
}

type BaseLogic struct {
	logx.Logger
	ctx        context.Context
	svcCtx     *svc.ServiceContext
	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger:     logx.WithContext(ctx),
		ctx:        ctx,
		svcCtx:     svcCtx,
		converters: []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) getPlanCaseInfoByCalculate(projectId, taskId, planExecuteId string) (int64, int64, int64, error) {
	selectBuilder := l.svcCtx.SuiteExecutionRecordModel.SelectBuilder().Where(
		"`task_id` = ? AND `project_id` = ? AND `plan_execute_id` = ?",
		taskId, projectId, planExecuteId,
	)
	SuiteRecords, err := l.svcCtx.SuiteExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil {
		return 0, 0, 0, err
	}

	selectInterfaceRecordBuilder := l.svcCtx.InterfaceExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `plan_execute_id` = ? ",
		taskId, projectId, planExecuteId,
	)

	interfaceRecords, err := l.svcCtx.InterfaceExecutionRecordModel.FindByQuery(l.ctx, selectInterfaceRecordBuilder)
	if err != nil {
		return 0, 0, 0, err
	}

	selectServiceRecordBuilder := l.svcCtx.ServiceExecutionRecordModel.SelectBuilder().Where(
		"`task_id` = ? AND `project_id` = ? AND `plan_execute_id` = ?",
		taskId, projectId, planExecuteId,
	)
	serviceRecords, err := l.svcCtx.ServiceExecutionRecordModel.FindByQuery(l.ctx, selectServiceRecordBuilder)
	if err != nil {
		return 0, 0, 0, err
	}

	var totalCase, failureCase, successCase int64

	for _, SuiteRecord := range SuiteRecords {
		totalCase += SuiteRecord.TotalCase
		failureCase += SuiteRecord.FailureCase
		successCase += SuiteRecord.SuccessCase
	}

	for _, interfaceRecord := range interfaceRecords {
		totalCase += interfaceRecord.TotalCase
		failureCase += interfaceRecord.FailureCase
		successCase += interfaceRecord.SuccessCase
	}

	for _, serviceRecord := range serviceRecords {
		totalCase += serviceRecord.TotalCase
		failureCase += serviceRecord.FailureCase
		successCase += serviceRecord.SuccessCase
	}

	return totalCase, failureCase, successCase, nil
}

func (l *BaseLogic) isFinishedStatus(status string) bool {
	_, exist := finishedStatusMap[status]
	return exist
}

func StringIfOr(a, b string) string {
	if a != "" {
		return a
	}
	return b
}

func Int64IfOr(a, b int64) int64 {
	if a > 0 {
		return a
	}
	return b
}
