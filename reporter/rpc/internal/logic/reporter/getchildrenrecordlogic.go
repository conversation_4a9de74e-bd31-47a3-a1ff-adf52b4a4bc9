package reporterlogic

import (
	"context"
	"fmt"
	"strings"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetChildrenRecordLogic struct {
	*BaseLogic
}

func NewGetChildrenRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetChildrenRecordLogic {
	return &GetChildrenRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func CheckInParam(in *pb.GetChildrenRecordRequest) error {
	var nullParams []string
	if in.TaskId == "" {
		nullParams = append(nullParams, "task_id")
	}
	if in.ExecuteId == "" {
		nullParams = append(nullParams, "execute_id")
	}
	if in.ProjectId == "" {
		nullParams = append(nullParams, "project_id")
	}
	if in.ExecuteType == "" {
		nullParams = append(nullParams, "execute_type")
	}
	if in.ComponentId == "" {
		nullParams = append(nullParams, "component_id")
	}
	if in.ComponentType == "" {
		nullParams = append(nullParams, "component_type")
	}
	if in.ComponentExecuteId == "" {
		nullParams = append(nullParams, "component_execute_id")
	}

	if len(nullParams) != 0 {
		nullParamStr := strings.Join(nullParams, "、")
		err := fmt.Sprintf("以下参数不可为空：%s", nullParamStr)
		return errorx.Err(errorx.ValidateParamError, fmt.Sprintf("failed to create record, error: %+v", err))
	}

	return nil
}

// 查询Children的RPC接口
func (l *GetChildrenRecordLogic) GetChildrenRecord(in *pb.GetChildrenRecordRequest) (*pb.GetChildrenRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	err = CheckInParam(in)
	if err != nil {
		return nil, err
	}

	var resp pb.GetChildrenRecordResponse
	var childRecordArray []*pb.GetChildrenRecordResponse_ChildRecord

	if in.ComponentType == "INTERFACE_CASE" {
		// 获取关联测试用例执行情况
		selectCaseRecordBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
			"`task_id` = ? AND `project_id` = ? AND `parent_component_execute_id` = ? ",
			in.TaskId, in.ProjectId, in.ComponentExecuteId)
		caseRecords, err2 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectCaseRecordBuilder)

		if err2 != nil {
			err = err2
			return nil, err
		}

		for _, caseRecord := range caseRecords {
			item := pb.GetChildrenRecordResponse_ChildRecord{
				TaskId:                   caseRecord.TaskId,
				ExecuteId:                caseRecord.ExecuteId,
				ExecuteType:              caseRecord.ExecuteType,
				ComponentId:              caseRecord.ComponentId,
				ComponentName:            caseRecord.ComponentName,
				ComponentType:            caseRecord.ComponentType,
				ComponentExecuteId:       caseRecord.ComponentExecuteId,
				ParentComponentId:        caseRecord.ParentComponentId.String,
				ParentComponentExecuteId: caseRecord.ParentComponentExecuteId.String,
				Version:                  caseRecord.Version,
				Times:                    caseRecord.Times,
				Status:                   caseRecord.Status.String,
				IsRoot:                   caseRecord.IsRoot,
				ExecutedBy:               caseRecord.ExecutedBy,
				StartedAt:                caseRecord.StartedAt,
				EndedAt:                  caseRecord.EndedAt.Int64,
				CostTime:                 caseRecord.CostTime,
			}
			childRecordArray = append(childRecordArray, &item)
		}
	}

	resp.ChildRecordArray = childRecordArray

	return &resp, nil
}
