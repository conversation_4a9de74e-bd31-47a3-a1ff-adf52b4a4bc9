package reporterlogic

import (
	"context"
	"fmt"

	"github.com/shopspring/decimal"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPlanSummaryLogic struct {
	*BaseLogic
}

func NewGetPlanSummaryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPlanSummaryLogic {
	return &GetPlanSummaryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetPlanSummary 获取测试计划执行报告（ci/cd专用）
func (l *GetPlanSummaryLogic) GetPlanSummary(in *pb.GetPlanSummaryRequest) (
	resp *pb.GetPlanSummaryResponse, err error,
) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	var (
		projectID = in.GetProjectId()
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()

		PassRate    string
		PassingRate string
		planType    = managerpb.ApiExecutionDataType(in.GetPlanType())
	)

	switch planType {
	case managerpb.ApiExecutionDataType_API_PLAN:
		selectBuilder := l.svcCtx.PlanExecutionRecordModel.SelectBuilderWithEmptyContent()
		selectBuilder = selectBuilder.Where(
			"`task_id` = ?  AND `project_id` = ? AND `execute_id`= ? AND `plan_execute_id` = ?",
			taskID, projectID, executeID, executeID,
		).Limit(1)

		planRecords, err2 := l.svcCtx.PlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)

		if err2 != nil {
			return nil, errorx.Errorf(
				errorx.DBError,
				"failed to find the api plan record detail, project_id: %s, task_id: %s, execute_id: %s, error: %+v",
				projectID, taskID, executeID, err2,
			)
		} else if len(planRecords) == 0 {
			return nil, errorx.Errorf(
				errorx.NotExists,
				"not found the api plan record detail, project_id: %s, task_id: %s, execute_id: %s",
				projectID, taskID, executeID,
			)
		}

		planRecord := planRecords[0]
		status := planRecord.Status.String

		resp = &pb.GetPlanSummaryResponse{
			Status: status,
		}
		if status != "Success" && status != "Failure" {
			return resp, nil
		}

		if planRecord.TotalCase == 0 {
			PassRate = "0.0"
			PassingRate = "0/0"
		} else {
			proportion := float64(planRecord.SuccessCase) / float64(planRecord.TotalCase)
			PassRateFloat, _ := decimal.NewFromFloat(proportion * 100).RoundFloor(2).Float64()
			PassRate = fmt.Sprintf("%.2f%s", PassRateFloat, "%")
			PassingRate = fmt.Sprintf("%d/%d", planRecord.SuccessCase, planRecord.TotalCase)
		}

		probeDomain := l.svcCtx.Config.ProbeDomain
		reportPath := fmt.Sprintf(
			"%s/#/api-test/testplan/reporter?plan_id=%s&task_id=%s&plan_execute_id=%s&project_id=%s&execute_id=%s",
			probeDomain, planRecord.PlanId, in.TaskId, in.ExecuteId, in.ProjectId, in.ExecuteId,
		)

		resp.Record = &pb.GetPlanSummaryResponse_Record{
			TotalCase:   planRecord.TotalCase,
			SuccessCase: planRecord.SuccessCase,
			PassRate:    PassRate,
			PassingRate: PassingRate,
			ReportPath:  reportPath,
		}

		return resp, nil
	case managerpb.ApiExecutionDataType_UI_PLAN:
		selectBuilder := l.svcCtx.UIPlanExecutionRecordModel.SelectBuilderWithEmptyContent()
		selectBuilder = selectBuilder.Where(
			"`task_id` = ?  AND `project_id` = ? AND `execute_id`= ? ",
			taskID, projectID, executeID,
		).Limit(1)

		planRecords, err2 := l.svcCtx.UIPlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)

		if err2 != nil {
			return nil, errorx.Errorf(
				errorx.DBError,
				"failed to find the ui plan record detail, project_id: %s, task_id: %s, execute_id: %s, error: %+v",
				projectID, taskID, executeID, err2,
			)
		} else if len(planRecords) == 0 {
			return nil, errorx.Errorf(
				errorx.NotExists,
				"not found the ui plan record detail, project_id: %s, task_id: %s, execute_id: %s",
				projectID, taskID, executeID,
			)
		}

		planRecord := planRecords[0]
		status := planRecord.Status.String

		resp = &pb.GetPlanSummaryResponse{
			Status: status,
		}
		if status != "Success" && status != "Failure" {
			return resp, nil
		}

		if planRecord.TotalCase == 0 {
			PassRate = "0.0"
			PassingRate = "0/0"
		} else {
			proportion := float64(planRecord.SuccessCase) / float64(planRecord.TotalCase)
			PassRateFloat, _ := decimal.NewFromFloat(proportion * 100).RoundFloor(2).Float64()
			PassRate = fmt.Sprintf("%.2f%s", PassRateFloat, "%")
			PassingRate = fmt.Sprintf("%d/%d", planRecord.SuccessCase, planRecord.TotalCase)
		}

		reportPath := ""

		if planRecord.Content.String != "" {
			var contentMap map[string]any
			err = jsonx.UnmarshalFromString(planRecord.Content.String, &contentMap)
			if err != nil {
				return nil, err
			}
			reportViewUrl_ := contentMap["report_view_url"]

			if reportViewUrl_ != nil {
				reportPath = reportViewUrl_.(string)
			}
		}

		resp.Record = &pb.GetPlanSummaryResponse_Record{
			TotalCase:   planRecord.TotalCase,
			SuccessCase: planRecord.SuccessCase,
			PassRate:    PassRate,
			PassingRate: PassingRate,
			ReportPath:  reportPath,
		}

		return resp, nil
	default:
		return nil, errorx.Errorf(
			errorx.DoesNotSupport, "invalid plan type, expected: %d or %d, but got: %d",
			managerpb.ApiExecutionDataType_API_PLAN, managerpb.ApiExecutionDataType_UI_PLAN, in.GetPlanType(),
		)
	}
}
