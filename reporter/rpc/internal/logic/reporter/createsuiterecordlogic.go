package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateSuiteRecordLogic struct {
	*BaseLogic
}

func NewCreateSuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateSuiteRecordLogic {
	return &CreateSuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateSuiteRecordLogic) CreateSuiteRecord(in *pb.PutSuiteRecordRequest) (*pb.CreateSuiteRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.<PERSON><PERSON>.<PERSON>(err.Error())
		}
	}()

	err = CheckNotNullSuite(in)
	if err != nil {
		return nil, err
	}

	record := ConvertSuiteModel(in, nil)
	result, insertErr := l.svcCtx.SuiteExecutionRecordModel.InsertRecordWithDefaultCreatedAt(l.ctx, record)

	if insertErr != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to create record, error: %+v", insertErr))
		return nil, err
	}

	recordId, _ := result.LastInsertId()
	record.Id = recordId

	executionRecordBytes, _ := json.Marshal(record)
	executionRecordStr := string(executionRecordBytes)

	redisKey := fmt.Sprintf("%s%s_%s_%s_%s", common.ConstRedisSuiteRecord, in.TaskId, in.ProjectId, in.ExecuteId, record.SuiteExecuteId)
	redisErr := l.svcCtx.Redis.Setex(redisKey, executionRecordStr, 600) // 10分钟
	if redisErr != nil {
		redisErr = errorx.Err(errorx.RedisError, fmt.Sprintf("create record success, but encounter redis error: %+v", redisErr))
		l.Logger.Warnf(redisErr.Error())
	}

	resp := &pb.CreateSuiteRecordResponse{
		SuiteExecuteId: record.SuiteExecuteId,
	}

	return resp, nil
}
