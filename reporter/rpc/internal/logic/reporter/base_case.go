package reporterlogic

import (
	"database/sql"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func ConvertCaseModel(in *pb.PutRecordRequest, old *model.ExecutionRecord) (out *model.ExecutionRecord) {
	if old == nil {
		old = &model.ExecutionRecord{}
	}

	out = &model.ExecutionRecord{
		Id:                       old.Id,
		TaskId:                   StringIfOr(old.TaskId, in.GetTaskId()),
		ProjectId:                StringIfOr(old.ProjectId, in.GetProjectId()),
		ExecuteId:                StringIfOr(old.ExecuteId, in.GetExecuteId()),
		ExecuteType:              StringIfOr(old.ExecuteType, in.GetExecuteType()),
		GeneralConfig:            sql.NullString{String: StringIfOr(in.GetGeneralConfig(), old.GeneralConfig.String), Valid: StringIfOr(in.GetGeneralConfig(), old.GeneralConfig.String) != ""},
		AccountConfig:            sql.NullString{String: StringIfOr(in.GetAccountConfig(), old.AccountConfig.String), Valid: StringIfOr(in.GetAccountConfig(), old.GeneralConfig.String) != ""},
		ComponentId:              StringIfOr(old.ComponentId, in.GetComponentId()),
		ComponentName:            StringIfOr(in.GetComponentName(), old.ComponentName),
		ComponentType:            StringIfOr(in.GetComponentType(), old.ComponentType),
		ComponentExecuteId:       StringIfOr(old.ComponentExecuteId, StringIfOr(in.GetComponentExecuteId(), utils.GenExecuteId())),
		ParentComponentId:        sql.NullString{String: StringIfOr(in.GetParentComponentId(), old.ParentComponentId.String), Valid: StringIfOr(in.GetParentComponentId(), old.ParentComponentId.String) != ""},
		ParentComponentExecuteId: sql.NullString{String: StringIfOr(in.GetParentComponentExecuteId(), old.ParentComponentExecuteId.String), Valid: StringIfOr(in.GetParentComponentExecuteId(), old.ParentComponentExecuteId.String) != ""},
		Version:                  StringIfOr(in.GetVersion(), old.Version),
		Times:                    Int64IfOr(Int64IfOr(in.GetTimes(), old.Times), 1),
		Status:                   sql.NullString{String: StringIfOr(in.GetStatus(), old.Status.String), Valid: StringIfOr(in.GetStatus(), old.Status.String) != ""},
		Content:                  sql.NullString{String: StringIfOr(in.GetContent(), old.Content.String), Valid: StringIfOr(in.GetContent(), old.Content.String) != ""},
		IsRoot:                   Int64IfOr(in.GetIsRoot(), old.IsRoot),
		ExecutedBy:               StringIfOr(in.GetExecutedBy(), old.ExecutedBy),
		MaintainedBy:             StringIfOr(in.GetMaintainedBy(), old.MaintainedBy),
		StartedAt:                Int64IfOr(in.GetStartedAt(), old.StartedAt),
		EndedAt:                  sql.NullInt64{Int64: Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64), Valid: Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64) > 0},
		Callback:                 sql.NullString{String: StringIfOr(in.GetCallback(), old.Callback.String), Valid: StringIfOr(in.GetCallback(), old.Callback.String) != ""},
		CreatedAt:                old.CreatedAt,
		UpdatedAt:                old.UpdatedAt,
		Cleaned:                  old.Cleaned,
	}

	if out.EndedAt.Int64 > out.StartedAt {
		out.CostTime = time.UnixMilli(out.EndedAt.Int64).Sub(time.UnixMilli(out.StartedAt)).Milliseconds()
	}

	return out
}
