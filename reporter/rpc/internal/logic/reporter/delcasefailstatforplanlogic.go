package reporterlogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DelCaseFailStatForPlanLogic struct {
	*BaseLogic
}

func NewDelCaseFailStatForPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DelCaseFailStatForPlanLogic {
	return &DelCaseFailStatForPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *DelCaseFailStatForPlanLogic) DelCaseFailStatForPlan(in *pb.DelCaseFailStatForPlanReq) (
	out *pb.DelCaseFailStatForPlanResp, err error,
) {
	return &pb.DelCaseFailStatForPlanResp{}, l.svcCtx.CaseFailForPlanStatModel.DeleteByProjectIdCaseIdCaseType(
		l.ctx, nil, in.GetProjectId(), in.GetCaseId(), in.GetCaseType(),
	)
}
