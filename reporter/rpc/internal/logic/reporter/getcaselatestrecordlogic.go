package reporterlogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetCaseLatestRecordLogic struct {
	*BaseLogic
}

func NewGetCaseLatestRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCaseLatestRecordLogic {
	return &GetCaseLatestRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetCaseLatestRecordLogic) GetCaseLatestRecord(in *pb.GetCaseLatestRecordRequest) (*pb.GetCaseLatestRecordResponse, error) {
	var err error

	defer func() {
		if err != nil {
			l.<PERSON><PERSON>.<PERSON>(err.Error())
		}
	}()

	if (in.ProjectId == "") || (len(in.CaseIdArray) == 0) {
		err = errorx.Err(errorx.ParseParamError, "project_id和case_id_array均不能为空！")
		return nil, err
	}

	var caseRecordArray []*pb.GetCaseLatestRecordResponse_RecordCaseRecord

	for _, caseId := range in.CaseIdArray {
		var latestRecord *model.ExecutionRecord

		selectBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilder()

		selectApiCaseBuilder := selectBuilder.Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = 'API_CASE' AND `is_root` = 1",
			in.ProjectId, caseId).OrderBy("started_at DESC").Limit(1)

		resultApiCase, err2 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectApiCaseBuilder)
		if err2 != nil {
			err = err2
			return nil, err
		}

		if len(resultApiCase) == 1 {
			latestRecord = resultApiCase[0]
		}

		selectInterfaceCaseBuilder := selectBuilder.Where(
			"`project_id` = ? AND `component_id` = ? AND `execute_type` = 'INTERFACE_CASE' AND `is_root` = 1",
			in.ProjectId, caseId).OrderBy("started_at DESC").Limit(1)
		resultInterfaceCase, err3 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectInterfaceCaseBuilder)
		if err3 != nil {
			err = err3
			return nil, err
		}

		if len(resultInterfaceCase) == 1 {
			if latestRecord != nil {
				if resultInterfaceCase[0].StartedAt > latestRecord.StartedAt {
					latestRecord = resultInterfaceCase[0]
				}
			} else {
				latestRecord = resultInterfaceCase[0]
			}
		}

		if latestRecord != nil {
			caseRecord := pb.GetCaseLatestRecordResponse_RecordCaseRecord{
				TaskId:    latestRecord.TaskId,
				ExecuteId: latestRecord.ExecuteId,
				ProjectId: latestRecord.ProjectId,
				CaseId:    latestRecord.ComponentId,
				Status:    latestRecord.Status.String,
				Content:   latestRecord.Content.String,
				StartedAt: latestRecord.StartedAt,
				EndedAt:   latestRecord.EndedAt.Int64,
			}
			caseRecordArray = append(caseRecordArray, &caseRecord)
		}
	}

	return &pb.GetCaseLatestRecordResponse{CaseRecordArray: caseRecordArray}, nil
}
