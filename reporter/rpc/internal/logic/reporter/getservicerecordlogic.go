package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	structPb "google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonPb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetServiceRecordLogic struct {
	*BaseLogic
}

func NewGetServiceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetServiceRecordLogic {
	return &GetServiceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 精准测试服务执行记录详情
func (l *GetServiceRecordLogic) GetServiceRecord(in *pb.GetServiceRecordRequest) (resp *pb.GetServiceRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	selectBuilder := l.svcCtx.ServiceExecutionRecordModel.SelectBuilder()
	selectBuilder = selectBuilder.Where("`task_id` = ?  AND `project_id` = ? AND `execute_id` = ? AND `service_execute_id` = ?",
		in.TaskId, in.ProjectId, in.ServiceExecuteId, in.ServiceExecuteId).Limit(1)

	serviceRecords, err2 := l.svcCtx.ServiceExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)

	msg := fmt.Sprintf("service serviceRecord detail with task_id[%s] project_id[%s] and service_execute_id[%s]",
		in.TaskId, in.ProjectId, in.ServiceExecuteId)

	if (err2 != nil) || (len(serviceRecords) == 0) {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err2))
		return nil, err
	}

	var (
		successCase, failureCase int64
		caseItems                []*pb.GetServiceRecordResponse_CaseItem
	)

	// 获取关联测试用例执行情况
	selectCaseRecordBuilder := l.svcCtx.ExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `parent_component_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.ServiceExecuteId)
	caseRecords, err3 := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, selectCaseRecordBuilder)

	if err3 != nil {
		err = err3
		return nil, err
	}

	for _, caseRecord := range caseRecords {
		if caseRecord.Status.String == "Success" {
			successCase += 1
		} else if caseRecord.Status.String == "Failure" {
			failureCase += 1
		}
		item := pb.GetServiceRecordResponse_CaseItem{
			TaskId:        caseRecord.TaskId,
			ExecuteId:     caseRecord.ExecuteId,
			ProjectId:     caseRecord.ProjectId,
			CaseExecuteId: caseRecord.ComponentExecuteId,
			CaseId:        caseRecord.ComponentId,
			CaseName:      caseRecord.ComponentName,
			Version:       caseRecord.Version,
			StartedAt:     caseRecord.StartedAt,
			EndedAt:       caseRecord.EndedAt.Int64,
			CostTime:      caseRecord.CostTime,
			Status:        caseRecord.Status.String,
			MaintainedBy:  caseRecord.MaintainedBy,
		}
		caseItems = append(caseItems, &item)
	}

	serviceRecord := serviceRecords[0]

	var content structPb.Struct
	_ = protobuf.UnmarshalJSON([]byte(serviceRecord.Content.String), &content)

	var generalConfig *commonPb.GeneralConfig
	var accountConfig []*commonPb.AccountConfig
	_ = json.Unmarshal([]byte(serviceRecord.GeneralConfig.String), &generalConfig)
	_ = json.Unmarshal([]byte(serviceRecord.AccountConfig.String), &accountConfig)

	resp = &pb.GetServiceRecordResponse{
		CostTime:      serviceRecord.CostTime,
		ExecutedBy:    serviceRecord.ExecutedBy,
		StartedAt:     serviceRecord.StartedAt,
		EndedAt:       serviceRecord.EndedAt.Int64,
		TotalCase:     serviceRecord.TotalCase,
		SuccessCase:   serviceRecord.SuccessCase,
		FailureCase:   serviceRecord.FailureCase,
		ServiceId:     serviceRecord.ServiceId,
		ServiceName:   serviceRecord.ServiceName,
		Content:       &content,
		GeneralConfig: generalConfig,
		AccountConfig: accountConfig,
	}

	if caseItems == nil {
		resp.CaseItems = make([]*pb.GetServiceRecordResponse_CaseItem, 0)
	} else {
		resp.CaseItems = caseItems
	}

	return resp, nil
}
