package reporterlogic

import (
	"context"
	"fmt"
	"math"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListServiceRecordLogic struct {
	*BaseLogic
}

func NewListServiceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListServiceRecordLogic {
	return &ListServiceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 精准测试服务`调试`记录列表
func (l *ListServiceRecordLogic) ListServiceRecord(in *pb.ListServiceRecordRequest) (resp *pb.ListServiceRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	countBuilder := l.svcCtx.ServiceExecutionRecordModel.SelectCountBuilder().Where(
		"`project_id` = ? AND `service_id` = ? AND `execute_type` = 'API_SERVICE'",
		in.ProjectId, in.ServiceId)

	count, err2 := l.svcCtx.ServiceExecutionRecordModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		msg := fmt.Sprintf(
			"faile to count service record with project_id[%s]、service_id[%s], error: %+v",
			in.ProjectId, in.ServiceId, err2)
		err = errorx.Err(errorx.DBError, msg)
		return nil, err
	}
	resp = &pb.ListServiceRecordResponse{
		TotalCount: uint64(count),
		TotalPage:  1,
	}

	selectBuilder := l.svcCtx.ServiceExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `service_id` = ? AND `execute_type` = 'API_SERVICE'",
		in.ProjectId, in.ServiceId)

	if len(in.Sort) == 0 {
		sortField := rpc.SortField{
			Field: "started_at",
			Order: "desc",
		}
		in.Sort = append(in.Sort, &sortField)
	}

	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithPagination(l.svcCtx.ServiceExecutionRecordModel, in.Pagination),
		sqlbuilder.WithSort(l.svcCtx.ServiceExecutionRecordModel, in.Sort),
	)
	serviceRecords, err3 := l.svcCtx.ServiceExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err3 != nil {
		msg := fmt.Sprintf(
			"failed to find service record with project_id[%s]、service_id[%s], error: %+v",
			in.ProjectId, in.ServiceId, err3)
		err = errorx.Err(errorx.DBError, msg)
		return nil, err
	}

	// 避免返回null给调用者
	resp.Items = make([]*pb.ListServiceRecordResponse_ServiceRecord, 0, len(serviceRecords))
	for _, serviceRecord := range serviceRecords {
		r := &pb.ListServiceRecordResponse_ServiceRecord{
			TaskId:           serviceRecord.TaskId,
			ExecuteId:        serviceRecord.ExecuteId,
			ProjectId:        serviceRecord.ProjectId,
			ServiceExecuteId: serviceRecord.ServiceExecuteId,
			ServiceId:        serviceRecord.ServiceId,
			ServiceName:      serviceRecord.ServiceName,
			ExecutedBy:       serviceRecord.ExecutedBy,
			StartedAt:        serviceRecord.StartedAt,
			EndedAt:          serviceRecord.EndedAt.Int64,
			CostTime:         serviceRecord.CostTime,
			TotalCase:        serviceRecord.TotalCase,
			SuccessCase:      serviceRecord.SuccessCase,
			FailureCase:      serviceRecord.FailureCase,
			Status:           serviceRecord.Status.String,
			Cleaned:          serviceRecord.Cleaned,
		}
		resp.Items = append(resp.Items, r)
	}

	if in.Pagination != nil {
		resp.CurrentPage = in.Pagination.CurrentPage
		resp.PageSize = in.Pagination.PageSize
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
