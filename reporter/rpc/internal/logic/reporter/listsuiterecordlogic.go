package reporterlogic

import (
	"context"
	"fmt"
	"math"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListSuiteRecordLogic struct {
	*BaseLogic
}

func NewListSuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListSuiteRecordLogic {
	return &ListSuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// 集合`调试`记录列表
func (l *ListSuiteRecordLogic) ListSuiteRecord(in *pb.ListSuiteRecordRequest) (resp *pb.ListSuiteRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	countBuilder := l.svcCtx.SuiteExecutionRecordModel.SelectCountBuilder().Where(
		"`project_id` = ? AND `suite_id` = ? AND `execute_type` = 'API_SUITE'",
		in.ProjectId, in.SuiteId)

	count, err2 := l.svcCtx.SuiteExecutionRecordModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		msg := fmt.Sprintf(
			"faile to count suite record with project_id[%s]、suite_id[%s], error: %+v",
			in.ProjectId, in.SuiteId, err2)
		err = errorx.Err(errorx.DBError, msg)
		return nil, err
	}
	resp = &pb.ListSuiteRecordResponse{
		TotalCount: uint64(count),
		TotalPage:  1,
	}

	selectBuilder := l.svcCtx.SuiteExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `suite_id` = ? AND `execute_type` = 'API_SUITE'",
		in.ProjectId, in.SuiteId)

	if len(in.Sort) == 0 {
		sortField := rpc.SortField{
			Field: "started_at",
			Order: "desc",
		}
		in.Sort = append(in.Sort, &sortField)
	}

	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithPagination(l.svcCtx.SuiteExecutionRecordModel, in.Pagination),
		sqlbuilder.WithSort(l.svcCtx.SuiteExecutionRecordModel, in.Sort),
	)
	suiteRecords, err3 := l.svcCtx.SuiteExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err3 != nil {
		msg := fmt.Sprintf(
			"failed to find suite record with project_id[%s]、suite_id[%s], error: %+v",
			in.ProjectId, in.SuiteId, err3)
		err = errorx.Err(errorx.DBError, msg)
		return nil, err
	}

	// 避免返回null给调用者
	resp.Items = make([]*pb.ListSuiteRecordResponse_SuiteRecord, 0, len(suiteRecords))
	for _, suiteRecord := range suiteRecords {
		r := &pb.ListSuiteRecordResponse_SuiteRecord{
			TaskId:         suiteRecord.TaskId,
			ExecuteId:      suiteRecord.ExecuteId,
			ProjectId:      suiteRecord.ProjectId,
			SuiteExecuteId: suiteRecord.SuiteExecuteId,
			SuiteId:        suiteRecord.SuiteId,
			SuiteName:      suiteRecord.SuiteName,
			ExecutedBy:     suiteRecord.ExecutedBy,
			StartedAt:      suiteRecord.StartedAt,
			EndedAt:        suiteRecord.EndedAt.Int64,
			CostTime:       suiteRecord.CostTime,
			TotalCase:      suiteRecord.TotalCase,
			SuccessCase:    suiteRecord.SuccessCase,
			FailureCase:    suiteRecord.FailureCase,
			Status:         suiteRecord.Status.String,
			Cleaned:        suiteRecord.Cleaned,
		}
		resp.Items = append(resp.Items, r)
	}

	if in.Pagination != nil {
		resp.CurrentPage = in.Pagination.CurrentPage
		resp.PageSize = in.Pagination.PageSize
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
