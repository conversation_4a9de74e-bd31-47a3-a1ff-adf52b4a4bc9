package reporterlogic

import (
	"database/sql"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func ConvertSuiteModel(in *pb.PutSuiteRecordRequest, old *model.SuiteExecutionRecord) (out *model.SuiteExecutionRecord) {
	if old == nil {
		old = &model.SuiteExecutionRecord{}
	}

	out = &model.SuiteExecutionRecord{
		Id:             old.Id,
		TaskId:         StringIfOr(old.TaskId, in.GetTaskId()),
		ProjectId:      StringIfOr(old.ProjectId, in.GetProjectId()),
		ExecuteId:      StringIfOr(old.ExecuteId, in.GetExecuteId()),
		ExecuteType:    StringIfOr(old.ExecuteType, in.GetExecuteType()),
		GeneralConfig:  sql.NullString{String: StringIfOr(in.GetGeneralConfig(), old.GeneralConfig.String), Valid: StringIfOr(in.GetGeneralConfig(), old.GeneralConfig.String) != ""},
		AccountConfig:  sql.NullString{String: StringIfOr(in.GetAccountConfig(), old.AccountConfig.String), Valid: StringIfOr(in.GetAccountConfig(), old.GeneralConfig.String) != ""},
		SuiteId:        StringIfOr(old.SuiteId, in.GetSuiteId()),
		SuiteExecuteId: StringIfOr(old.SuiteExecuteId, StringIfOr(in.GetSuiteExecuteId(), utils.GenExecuteId())),
		SuiteName:      StringIfOr(in.GetSuiteName(), old.SuiteName),
		PlanExecuteId:  sql.NullString{String: StringIfOr(in.GetPlanExecuteId(), old.PlanExecuteId.String), Valid: StringIfOr(in.GetPlanExecuteId(), old.PlanExecuteId.String) != ""},
		TotalCase:      Int64IfOr(in.GetTotalCase(), old.TotalCase),
		SuccessCase:    Int64IfOr(in.GetSuccessCase(), old.SuccessCase),
		FailureCase:    Int64IfOr(in.GetFailureCase(), old.FailureCase),
		Status:         sql.NullString{String: StringIfOr(in.GetStatus(), old.Status.String), Valid: StringIfOr(in.GetStatus(), old.Status.String) != ""},
		Content:        sql.NullString{String: StringIfOr(in.GetContent(), old.Content.String), Valid: StringIfOr(in.GetContent(), old.Content.String) != ""},
		ExecutedBy:     StringIfOr(in.GetExecutedBy(), old.ExecutedBy),
		StartedAt:      Int64IfOr(in.GetStartedAt(), old.StartedAt),
		EndedAt:        sql.NullInt64{Int64: Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64), Valid: Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64) > 0},
		Callback:       sql.NullString{String: StringIfOr(in.GetCallback(), old.Callback.String), Valid: StringIfOr(in.GetCallback(), old.Callback.String) != ""},
		CreatedAt:      old.CreatedAt,
		UpdatedAt:      old.UpdatedAt,
		Cleaned:        old.Cleaned,
	}

	if out.EndedAt.Int64 > out.StartedAt {
		out.CostTime = time.UnixMilli(out.EndedAt.Int64).Sub(time.UnixMilli(out.StartedAt)).Milliseconds()
	}

	return out
}
