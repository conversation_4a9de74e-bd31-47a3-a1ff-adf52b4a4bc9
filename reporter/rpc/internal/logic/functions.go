package logic

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var (
	services                    = ([]*commonpb.PerfServiceMetaData)(nil)
	monitorUrls                 = ([]*pb.MonitorUrl)(nil)
	perfCaseSteps               = ([]*pb.PerfCaseStepInfo)(nil)
	perfDataInfo                = (*pb.PerfDataInfo)(nil)
	loadGenerator               = (*commonpb.LoadGenerator)(nil)
	apiMetrics                  = ([]*pb.APIMetric)(nil)
	errMsg                      = (*pb.ErrorMessage)(nil)
	applicationConfig           = (*commonpb.ApplicationConfig)(nil)
	uiAgentComponentSteps       = ([]*commonpb.UIAgentComponentStep)(nil)
	uiAgentComponentExpectation = (*commonpb.UIAgentComponentExpectation)(nil)
	variables                   = ([]*commonpb.GeneralConfigVar)(nil)
	uiAgentDevice               = (*commonpb.UIAgentDevice)(nil)
)

func SQLNullStringToPerfServiceMetaDatas() utils.TypeConverter {
	return commonutils.NullStringToApiStructOrRpcMessage(services, nil)
}

func SQLNullStringToMonitorUrls() utils.TypeConverter {
	return commonutils.NullStringToApiStructOrRpcMessage(monitorUrls, nil)
}

func SQLNullStringToPerfCaseSteps() utils.TypeConverter {
	return commonutils.NullStringToApiStructOrRpcMessage(perfCaseSteps, nil)
}

func SQLNullStringToPerfDataInfo() utils.TypeConverter {
	return commonutils.NullStringToApiStructOrRpcMessage(perfDataInfo, nil)
}

func SQLNullStringToLoadGenerator() utils.TypeConverter {
	return commonutils.NullStringToApiStructOrRpcMessage(loadGenerator, nil)
}

func SQLNullStringToAPIMetrics() utils.TypeConverter {
	return commonutils.NullStringToApiStructOrRpcMessage(apiMetrics, nil)
}

func SQLNullStringToErrorMessage() utils.TypeConverter {
	return commonutils.NullStringToApiStructOrRpcMessage(errMsg, nil)
}

func StringToApplicationConfig() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(applicationConfig, nil)
}

func StringToUIAgentComponentSteps() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(uiAgentComponentSteps, nil)
}

func StringToUIAgentComponentExpectation() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(uiAgentComponentExpectation, nil)
}

func StringToVariables() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(variables, nil)
}

func StringToUIAgentDevice() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(uiAgentDevice, nil)
}
