package perfreporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPerfCaseRecordLogic struct {
	*BaseLogic
}

func NewGetPerfCaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPerfCaseRecordLogic {
	return &GetPerfCaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetPerfCaseRecord 获取压测用例执行记录
func (l *GetPerfCaseRecordLogic) GetPerfCaseRecord(in *pb.GetPerfCaseRecordReq) (
	out *pb.GetPerfCaseRecordResp, err error,
) {
	record, err := model.CheckPerfCaseExecutionRecordByExecuteID(
		l.ctx, l.svcCtx.PerfCaseExecutionRecordModel, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.GetPerfCaseRecordResp{Record: &pb.PerfCaseRecord{}}
	if err = utils.Copy(out.Record, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf case execution record to response, record: %s, error: %+v",
			jsonx.MarshalIgnoreError(record), err,
		)
	}

	return out, nil
}
