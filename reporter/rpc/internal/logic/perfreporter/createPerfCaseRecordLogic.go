package perfreporterlogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreatePerfCaseRecordLogic struct {
	*BaseLogic
}

func NewCreatePerfCaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfCaseRecordLogic {
	return &CreatePerfCaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePerfCaseRecord 创建压测用例执行记录
func (l *CreatePerfCaseRecordLogic) CreatePerfCaseRecord(in *pb.CreatePerfCaseRecordReq) (
	out *pb.CreatePerfCaseRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockPerfCaseRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)
	fn := func() error {
		_, err = model.CheckPerfCaseExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.PerfCaseExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			if re, ok := errorx.RootError(err); ok && re.Code() != errorx.NotExists {
				return err
			}
		} else {
			return errorx.Errorf(
				errorx.AlreadyExists,
				"the perf case execution record already exists, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}

		var (
			steps, perfData, loadGenerator sql.NullString

			now        = time.Now()
			startedAt  = now
			executedBy = in.GetExecutedBy()
		)

		if len(in.GetSteps()) > 0 {
			steps.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetSteps())
			steps.Valid = true
		}
		if in.GetPerfData() != nil {
			perfData.String = protobuf.MarshalJSONToStringIgnoreError(in.GetPerfData())
			perfData.Valid = true
		}
		if in.GetLoadGenerator() != nil {
			loadGenerator.String = protobuf.MarshalJSONToStringIgnoreError(in.GetLoadGenerator())
			loadGenerator.Valid = true
		}

		if in.GetStartedAt() != nil && in.GetStartedAt().IsValid() && in.GetStartedAt().GetSeconds() > 0 {
			if startTime := in.GetStartedAt().AsTime(); !startTime.IsZero() {
				startedAt = startTime
			}
		}

		if executedBy == "" {
			executedBy = l.currentUser.Account
		}

		record := &model.PerfCaseExecutionRecord{
			TaskId:         in.GetTaskId(),
			ExecuteId:      in.GetExecuteId(),
			SuiteExecuteId: in.GetSuiteExecuteId(),
			ProjectId:      in.GetProjectId(),
			CaseId:         in.GetCaseId(),
			CaseName:       in.GetCaseName(),
			Steps:          steps,
			PerfData:       perfData,
			LoadGenerator:  loadGenerator,
			Status: sql.NullString{
				String: in.GetStatus(),
				Valid:  in.GetStatus() != "",
			},
			ExecutedBy: executedBy,
			StartedAt: sql.NullTime{
				Time:  startedAt,
				Valid: true,
			},
			CreatedBy: l.currentUser.Account,
			UpdatedBy: l.currentUser.Account,
			CreatedAt: now,
			UpdatedAt: now,
		}

		return l.svcCtx.PerfCaseExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.PerfCaseExecutionRecordModel.Insert(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to create perf case execution record, task_id: %s, execute_id: %s, suite_execute_id: %s, project_id: %s, case_id: %s, error: %+v",
						taskID, executeID, in.GetSuiteExecuteId(), projectID, in.GetCaseId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.CreatePerfCaseRecordResp{}, nil
}
