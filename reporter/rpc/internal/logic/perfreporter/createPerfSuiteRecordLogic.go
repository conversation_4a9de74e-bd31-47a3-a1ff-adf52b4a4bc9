package perfreporterlogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreatePerfSuiteRecordLogic struct {
	*BaseLogic
}

func NewCreatePerfSuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfSuiteRecordLogic {
	return &CreatePerfSuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePerfSuiteRecord 创建压测集合执行记录
func (l *CreatePerfSuiteRecordLogic) CreatePerfSuiteRecord(in *pb.CreatePerfSuiteRecordReq) (
	out *pb.CreatePerfSuiteRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockPerfSuiteRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)
	fn := func() error {
		_, err = model.CheckPerfSuiteExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.PerfSuiteExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			if re, ok := errorx.RootError(err); ok && re.Code() != errorx.NotExists {
				return err
			}
		} else {
			return errorx.Errorf(
				errorx.AlreadyExists,
				"the perf suite execution record already exists, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}

		now := time.Now()
		startedAt := now
		if in.GetStartedAt() != nil && in.GetStartedAt().IsValid() && in.GetStartedAt().GetSeconds() > 0 {
			if startTime := in.GetStartedAt().AsTime(); !startTime.IsZero() {
				startedAt = startTime
			}
		}

		executedBy := in.GetExecutedBy()
		if executedBy == "" {
			executedBy = l.currentUser.Account
		}

		record := &model.PerfSuiteExecutionRecord{
			TaskId:        taskID,
			ExecuteId:     executeID,
			PlanExecuteId: in.GetPlanExecuteId(),
			ProjectId:     projectID,
			SuiteId:       in.GetSuiteId(),
			SuiteName:     in.GetSuiteName(),
			Status: sql.NullString{
				String: in.GetStatus(),
				Valid:  in.GetStatus() != "",
			},
			ExecutedBy: executedBy,
			StartedAt: sql.NullTime{
				Time:  startedAt,
				Valid: true,
			},
			CreatedBy: l.currentUser.Account,
			UpdatedBy: l.currentUser.Account,
			CreatedAt: now,
			UpdatedAt: now,
		}

		return l.svcCtx.PerfSuiteExecutionRecordModel.Trans(
			l.ctx,
			func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.PerfSuiteExecutionRecordModel.Insert(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to create perf suite execution record, task_id: %s, execute_id: %s, plan_suite_id: %s, project_id: %s, suite_id: %s, error: %+v",
						taskID, executeID, in.GetPlanExecuteId(), projectID, in.GetSuiteId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.CreatePerfSuiteRecordResp{}, nil
}
