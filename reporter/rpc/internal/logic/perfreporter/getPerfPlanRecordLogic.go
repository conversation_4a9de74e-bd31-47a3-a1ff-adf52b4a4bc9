package perfreporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPerfPlanRecordLogic struct {
	*BaseLogic
}

func NewGetPerfPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPerfPlanRecordLogic {
	return &GetPerfPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetPerfPlanRecord 获取压测计划执行记录
func (l *GetPerfPlanRecordLogic) GetPerfPlanRecord(in *pb.GetPerfPlanRecordReq) (
	out *pb.GetPerfPlanRecordResp, err error,
) {
	record, err := model.CheckPerfPlanExecutionRecordByExecuteID(
		l.ctx, l.svcCtx.PerfPlanExecutionRecordModel, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.GetPerfPlanRecordResp{Record: &pb.PerfPlanRecord{}}
	if err = utils.Copy(out.Record, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan execution record to response, record: %s, error: %+v",
			jsonx.MarshalIgnoreError(record), err,
		)
	}

	return out, nil
}
