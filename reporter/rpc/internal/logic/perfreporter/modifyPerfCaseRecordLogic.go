package perfreporterlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyPerfCaseRecordLogic struct {
	*BaseLogic
}

func NewModifyPerfCaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfCaseRecordLogic {
	return &ModifyPerfCaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPerfCaseRecord 修改压测用例执行记录
func (l *ModifyPerfCaseRecordLogic) ModifyPerfCaseRecord(in *pb.ModifyPerfCaseRecordReq) (
	out *pb.ModifyPerfCaseRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockPerfCaseRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)
	fn := func() error {
		origin, err := model.CheckPerfCaseExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.PerfCaseExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			return err
		}

		var (
			status     = origin.Status
			costTime   = origin.CostTime
			endedAt    = origin.EndedAt
			apiMetrics = origin.ApiMetrics
			errMsg     = origin.ErrMsg

			now = time.Now()
		)

		if in.GetStatus() != "" {
			status.String = in.GetStatus()
			status.Valid = true
		}

		if in.GetEndedAt() != nil && in.GetEndedAt().IsValid() && in.GetEndedAt().GetSeconds() > 0 {
			if endTime := in.GetEndedAt().AsTime(); !endTime.IsZero() {
				costTime = endTime.Sub(origin.StartedAt.Time).Milliseconds()
				endedAt.Time = endTime
				endedAt.Valid = true
			}
		}

		if len(in.GetApiMetrics()) != 0 {
			s, err := protobuf.MarshalJSONWithMessagesToString(in.GetApiMetrics())
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.SerializationError, err.Error()),
					"failed to unmarshal the api metrics of perf case execution record, task_id: %s, execute_id: %s, project_id: %s, api_metrics: %+v, error: %+v",
					taskID, executeID, projectID, in.GetApiMetrics(), err,
				)
			}

			apiMetrics.String = s
			apiMetrics.Valid = s != ""
		}

		if in.GetErrMsg() != nil {
			s, err := protobuf.MarshalJSONToString(in.GetErrMsg())
			if err != nil {
				l.Errorf(
					"failed to unmarshal the error message of perf case execution record, task_id: %s, execute_id: %s, project_id: %s, error_message: %+v, error: %+v",
					taskID, executeID, projectID, in.GetErrMsg(), err,
				)
			} else {
				errMsg.String = s
				errMsg.Valid = s != ""
			}
		}

		record := &model.PerfCaseExecutionRecord{
			Id:             origin.Id,
			TaskId:         origin.TaskId,
			ExecuteId:      origin.ExecuteId,
			SuiteExecuteId: origin.SuiteExecuteId,
			ProjectId:      origin.ProjectId,
			CaseId:         origin.CaseId,
			CaseName:       origin.CaseName,
			Steps:          origin.Steps,
			PerfData:       origin.PerfData,
			LoadGenerator:  origin.LoadGenerator,
			Status:         status,
			CostTime:       costTime,
			ExecutedBy:     origin.ExecutedBy,
			StartedAt:      origin.StartedAt,
			EndedAt:        endedAt,
			ApiMetrics:     apiMetrics,
			ErrMsg:         errMsg,
			Cleaned:        origin.Cleaned,
			Deleted:        origin.Deleted,
			CreatedBy:      origin.CreatedBy,
			UpdatedBy:      origin.UpdatedBy,
			CreatedAt:      origin.CreatedAt,
			UpdatedAt:      now,
		}

		return l.svcCtx.PerfCaseExecutionRecordModel.Trans(
			l.ctx,
			func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.PerfCaseExecutionRecordModel.Update(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify perf case execution record, task_id: %s, execute_id: %s, project_id: %s, case_id: %s, error: %+v",
						taskID, executeID, projectID, in.GetCaseId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.ModifyPerfCaseRecordResp{}, nil
}
