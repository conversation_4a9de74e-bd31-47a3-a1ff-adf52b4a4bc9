package perfreporterlogic

import (
	"database/sql"
	"fmt"
	"net/url"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"
)

func TestGenerateCmdAndGrpcPathMonitorURL(t *testing.T) {
	const baseURL = "https://yw-monitor.ttyuyin.com/d/yaKGumVIk/andgrpc?orgId=7&refresh=10s"
	startedAt := time.Now().Add(-30 * time.Minute)

	type args struct {
		monitorURL string
		startedAt  sql.NullTime
		endedAt    sql.NullTime
		commands   []uint32
		grpcPaths  []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "with empty monitorURL",
			args: args{
				monitorURL: "",
				startedAt:  sql.NullTime{},
				endedAt:    sql.NullTime{},
				commands:   []uint32{10, 74},
				grpcPaths:  []string{"/ga.api.auth.AuthLogic/Auth", "/ga.api.sync.SyncGoLogic/CheckSyncKey"},
			},
			want: "https://yw-monitor.ttyuyin.com/d/yaKGumVIk/andgrpc?orgId=7&refresh=10s&var-cmd=10&var-cmd=74&var-grpc_path=%2Fga.api.auth.AuthLogic%2FAuth&var-grpc_path=%2Fga.api.sync.SyncGoLogic%2FCheckSyncKey",
		},
		{
			name: "with not empty monitorURL and repeated parameters",
			args: args{
				monitorURL: "https://yw-monitor.ttyuyin.com/d/yaKGumVIk/andgrpc?orgId=7&refresh=10s&var-cmd=10&var-grpc_path=%2Fga.api.auth.AuthLogic%2FAuth",
				startedAt:  sql.NullTime{},
				endedAt:    sql.NullTime{},
				commands:   []uint32{10, 74},
				grpcPaths:  []string{"/ga.api.auth.AuthLogic/Auth", "/ga.api.sync.SyncGoLogic/CheckSyncKey"},
			},
			want: "https://yw-monitor.ttyuyin.com/d/yaKGumVIk/andgrpc?orgId=7&refresh=10s&var-cmd=10&var-cmd=74&var-grpc_path=%2Fga.api.auth.AuthLogic%2FAuth&var-grpc_path=%2Fga.api.sync.SyncGoLogic%2FCheckSyncKey",
		},
		{
			name: "with valid startedAt",
			args: args{
				monitorURL: "",
				startedAt: sql.NullTime{
					Time: func() time.Time {
						return startedAt
					}(),
					Valid: true,
				},
				endedAt:   sql.NullTime{},
				commands:  []uint32{10, 74},
				grpcPaths: []string{"/ga.api.auth.AuthLogic/Auth", "/ga.api.sync.SyncGoLogic/CheckSyncKey"},
			},
			want: fmt.Sprintf(
				"https://yw-monitor.ttyuyin.com/d/yaKGumVIk/andgrpc?from=%d&orgId=7&refresh=10s&var-cmd=10&var-cmd=74&var-grpc_path=%%2Fga.api.auth.AuthLogic%%2FAuth&var-grpc_path=%%2Fga.api.sync.SyncGoLogic%%2FCheckSyncKey",
				startedAt.UnixMilli(),
			),
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				monitorURL := tt.args.monitorURL
				if monitorURL == "" {
					monitorURL = baseURL
				}

				u, err := url.Parse(monitorURL)
				if err != nil {
					t.Fatalf("failed to parse the monitor url, error: %+v", err)
				}

				values := u.Query()
				if _, ok := values[fromOfQueryParams]; !ok && tt.args.startedAt.Valid && !tt.args.startedAt.Time.IsZero() {
					values[fromOfQueryParams] = []string{strconv.FormatInt(tt.args.startedAt.Time.UnixMilli(), 10)}
				}
				if _, ok := values[toOfQueryParams]; !ok && tt.args.endedAt.Valid && !tt.args.endedAt.Time.IsZero() {
					values[toOfQueryParams] = []string{strconv.FormatInt(tt.args.endedAt.Time.UnixMilli(), 10)}
				}

				commandSet := set.NewHashset(uint64(len(tt.args.commands)), generic.Equals[string], generic.HashString)
				for _, cmd := range tt.args.commands {
					commandSet.Put(strconv.FormatUint(uint64(cmd), 10))
				}
				for _, v := range values[cmdOfQueryParams] {
					commandSet.Put(v)
				}
				values[cmdOfQueryParams] = commandSet.Keys()

				grpcPathSet := set.NewHashset(
					uint64(len(tt.args.grpcPaths)), generic.Equals[string], generic.HashString, tt.args.grpcPaths...,
				)
				for _, v := range values[grpcPathOfQueryParams] {
					grpcPathSet.Put(v)
				}
				values[grpcPathOfQueryParams] = grpcPathSet.Keys()

				u.RawQuery = values.Encode()
				monitorURL = u.String()
				t.Logf("monitor url: %s", monitorURL)

				assert.Equal(t, tt.want, monitorURL)
			},
		)
	}
}

func TestGenerateHttpServiceMonitorURL(t *testing.T) {
	const baseURL = "https://tt-telemetry.ttyuyin.com/#/application-dashboard"

	type args struct {
		service string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "",
			args: args{
				service: "wwxs-2024.activity-production",
			},
			want: "https://tt-telemetry.ttyuyin.com/#/application-dashboard?cluster=&name=wwxs-2024.activity-production",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				//u, err := url.ParseRequestURI(baseURL)
				//if err != nil {
				//	t.Fatal(err)
				//}
				//
				//values := u.Query()
				//values[clusterOfQueryParams] = []string{""}
				//values[nameOfQueryParams] = []string{tt.args.service}
				//
				//u.RawQuery = values.Encode()
				//monitorURL := u.String()
				monitorURL := fmt.Sprintf(
					"%s?%s=&%s=%s", baseURL, clusterOfQueryParams, nameOfQueryParams, tt.args.service,
				)
				t.Logf("monitor url: %s", monitorURL)

				assert.Equal(t, tt.want, monitorURL)
			},
		)
	}
}
