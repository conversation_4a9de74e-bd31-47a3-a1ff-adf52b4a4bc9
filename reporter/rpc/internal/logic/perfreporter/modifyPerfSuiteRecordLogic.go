package perfreporterlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyPerfSuiteRecordLogic struct {
	*BaseLogic
}

func NewModifyPerfSuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfSuiteRecordLogic {
	return &ModifyPerfSuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPerfSuiteRecord 修改压测集合执行记录
func (l *ModifyPerfSuiteRecordLogic) ModifyPerfSuiteRecord(in *pb.ModifyPerfSuiteRecordReq) (
	out *pb.ModifyPerfSuiteRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockPerfSuiteRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)
	fn := func() error {
		origin, err := model.CheckPerfSuiteExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.PerfSuiteExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			return err
		}

		var (
			status   = origin.Status
			costTime = origin.CostTime
			endedAt  = origin.EndedAt
			errMsg   = origin.ErrMsg

			now = time.Now()
		)

		if in.GetStatus() != "" {
			status.String = in.GetStatus()
			status.Valid = true
		}

		if in.GetEndedAt() != nil && in.GetEndedAt().IsValid() && in.GetEndedAt().GetSeconds() > 0 {
			if endTime := in.GetEndedAt().AsTime(); !endTime.IsZero() {
				costTime = endTime.Sub(origin.StartedAt.Time).Milliseconds()
				endedAt.Time = endTime
				endedAt.Valid = true
			}
		}

		if in.GetErrMsg() != nil {
			s, err := protobuf.MarshalJSONToString(in.GetErrMsg())
			if err != nil {
				l.Errorf(
					"failed to unmarshal the error message of perf suite execution record, task_id: %s, execute_id: %s, project_id: %s, error_message: %+v, error: %+v",
					taskID, executeID, projectID, in.GetErrMsg(), err,
				)
			} else {
				errMsg.String = s
				errMsg.Valid = s != ""
			}
		}

		record := &model.PerfSuiteExecutionRecord{
			Id:            origin.Id,
			TaskId:        origin.TaskId,
			ExecuteId:     origin.ExecuteId,
			PlanExecuteId: origin.PlanExecuteId,
			ProjectId:     origin.ProjectId,
			SuiteId:       origin.SuiteId,
			SuiteName:     origin.SuiteName,
			Status:        status,
			CostTime:      costTime,
			ExecutedBy:    origin.ExecutedBy,
			StartedAt:     origin.StartedAt,
			EndedAt:       endedAt,
			ErrMsg:        errMsg,
			Cleaned:       origin.Cleaned,
			Deleted:       origin.Deleted,
			CreatedBy:     origin.CreatedBy,
			UpdatedBy:     origin.UpdatedBy,
			CreatedAt:     origin.CreatedAt,
			UpdatedAt:     now,
		}

		return l.svcCtx.PerfSuiteExecutionRecordModel.Trans(
			l.ctx,
			func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.PerfSuiteExecutionRecordModel.Update(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify perf suite execution record, task_id: %s, execute_id: %s, project_id: %s, suite_id: %s, error: %+v",
						taskID, executeID, projectID, in.GetSuiteId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.ModifyPerfSuiteRecordResp{}, nil
}
