package perfreporterlogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchPerfPlanRecordLogic struct {
	*BaseLogic
}

func NewSearchPerfPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfPlanRecordLogic {
	return &SearchPerfPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfPlanRecord 搜索压测计划执行记录
func (l *SearchPerfPlanRecordLogic) SearchPerfPlanRecord(in *pb.SearchPerfPlanRecordReq) (
	out *pb.SearchPerfPlanRecordResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		planID     = in.GetPlanId()
		pagination = in.GetPagination()
	)

	out = &pb.SearchPerfPlanRecordResp{}

	selectBuilder, countBuilder := l.svcCtx.PerfPlanExecutionRecordModel.GenerateSearchPerfPlanExecutionRecordSqlBuilder(
		model.SearchPerfPlanExecutionRecordReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID:  projectID,
				Condition:  in.GetCondition(),
				Pagination: pagination,
				Sort:       rpc.ConvertSortFields(in.GetSort()),
			},
			PlanID: planID,
		},
	)

	count, err := l.svcCtx.PerfPlanExecutionRecordModel.FindCountPerfPlanExecutionRecords(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf plan execution records, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	records, err := l.svcCtx.PerfPlanExecutionRecordModel.FindPerfPlanExecutionRecords(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan execution records, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	out.Items = make([]*pb.SearchPerfPlanRecordItem, 0, len(records))
	for _, record := range records {
		item := &pb.SearchPerfPlanRecordItem{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf plan execution record to response, record: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(record), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
