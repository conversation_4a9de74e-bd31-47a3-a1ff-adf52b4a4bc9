package perfreporterlogic

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/syncx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"
	"golang.org/x/exp/slices"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const (
	sep = "|"

	lockTimeout = 5 * time.Second

	cmdAndGrpcDashboardName = "cmd & grpc 接口监控"

	fromOfQueryParams     = "from"
	toOfQueryParams       = "to"
	cmdOfQueryParams      = "var-cmd"
	grpcPathOfQueryParams = "var-grpc_path"
	clusterOfQueryParams  = "cluster"
	nameOfQueryParams     = "name"
)

var singleFlight = syncx.NewSingleFlight()

type UpdateMonitorURLOfPerfPlanRecordLogic struct {
	*BaseLogic
}

func NewUpdateMonitorURLOfPerfPlanRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateMonitorURLOfPerfPlanRecordLogic {
	return &UpdateMonitorURLOfPerfPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// UpdateMonitorURLOfPerfPlanRecord 更新压测计划执行记录的`metric_url`字段
func (l *UpdateMonitorURLOfPerfPlanRecordLogic) UpdateMonitorURLOfPerfPlanRecord(in *pb.UpdateMonitorURLOfPerfPlanRecordReq) (
	out *pb.UpdateMonitorURLOfPerfPlanRecordResp, err error,
) {
	val, err := singleFlight.Do(
		makeKeyByUpdateMonitorURLReq(in), func() (any, error) {
			var (
				taskID    = in.GetTaskId()
				executeID = in.GetExecuteId()
				projectID = in.GetProjectId()

				record  *model.PerfPlanExecutionRecord
				targets []*pb.MonitorUrl
			)

			record, err = model.CheckPerfPlanExecutionRecordByExecuteID(
				l.ctx, l.svcCtx.PerfPlanExecutionRecordModel, taskID, executeID, projectID,
			)
			if err != nil {
				return nil, err
			}

			key := fmt.Sprintf(
				"%s:%s:%s:%s", common.ConstLockPerfPlanRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID,
				projectID,
			)
			fn := func() error {
				// get the latest record after acquire the lock
				record, err = model.CheckPerfPlanExecutionRecordByExecuteID(
					l.ctx, l.svcCtx.PerfPlanExecutionRecordModel, taskID, executeID, projectID,
				)
				if err != nil {
					return err
				}

				conf, ok := l.svcCtx.Monitors[record.TargetEnv]
				if !ok {
					l.Warnf("not found the monitor url config by target env, env: %s", record.TargetEnv)
					return nil
				}

				var sources []*pb.MonitorUrl
				if record.MonitorUrls.Valid && record.MonitorUrls.String != "" {
					if err = protobuf.UnmarshalJSONWithMessagesFromString(
						record.MonitorUrls.String, &sources,
					); err != nil {
						return errors.Wrapf(
							errorx.Err(errorx.SerializationError, err.Error()),
							"failed to unmarshal the monitor urls of perf plan execution record, urls: %s, error: %+v",
							record.MonitorUrls.String, err,
						)
					}
				}

				targets = l.generateMonitorURLs(in, conf, sources)
				if len(targets) > 0 {
					record.MonitorUrls.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(targets)
					record.MonitorUrls.Valid = true
				} else {
					record.MonitorUrls.Valid = false
				}

				return l.svcCtx.PerfPlanExecutionRecordModel.Trans(
					l.ctx, func(context context.Context, session sqlx.Session) error {
						if _, err = l.svcCtx.PerfPlanExecutionRecordModel.Update(context, session, record); err != nil {
							return errors.Wrapf(
								errorx.Err(errorx.DBError, err.Error()),
								"failed to modify the monitor url of perf plan execution record, task_id: %s, execute_id: %s, project_id: %s, monitor_urls: %d => %d, error: %+v",
								taskID, executeID, projectID, len(sources), len(targets), err,
							)
						}

						l.Infof(
							"modify the monitor url of perf plan execution record successfully, task_id: %s, execute_id: %s, project_id: %s, monitor_urls: %d => %d",
							taskID, executeID, projectID, len(sources), len(targets),
						)
						return nil
					},
				)
			}
			if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
				return nil, err
			}

			return &pb.UpdateMonitorURLOfPerfPlanRecordResp{
				MonitorUrls: targets,
			}, nil
		},
	)
	if err != nil {
		return nil, err
	}

	return val.(*pb.UpdateMonitorURLOfPerfPlanRecordResp), nil
}

func (l *UpdateMonitorURLOfPerfPlanRecordLogic) generateMonitorURLs(
	in *pb.UpdateMonitorURLOfPerfPlanRecordReq, conf config.MonitorConfig, sources []*pb.MonitorUrl,
) []*pb.MonitorUrl {
	var (
		commands     = in.GetCommands()
		grpcPaths    = in.GetGrpcPaths()
		httpServices = in.GetHttpServices()

		commandsStr  = jsonx.MarshalIgnoreError(commands)
		grpcPathsStr = jsonx.MarshalIgnoreError(grpcPaths)

		number  = utils.Max(len(sources), 1+len(httpServices))
		targets = make([]*pb.MonitorUrl, 0, number)
		cache   = make(map[string]lang.PlaceholderType, number)
	)

	if len(commands) > 0 || len(grpcPaths) > 0 {
		cache[cmdAndGrpcDashboardName] = lang.Placeholder
	}
	for _, service := range httpServices {
		cache[service] = lang.Placeholder
	}

	for _, source := range sources {
		switch source.GetType() {
		case commonpb.MonitorUrlType_MUT_GRAFANA:
			mu, err := l.generateGrafanaMonitorURL(source.GetUrl(), commands, grpcPaths)
			if err != nil {
				l.Errorf(
					"failed to generate the grafana monitor url, url: %s, commands: %s, grpc_paths: %s, error: %+v",
					source.GetUrl(), commandsStr, grpcPathsStr, err,
				)
				continue
			}

			targets = append(targets, mu)
			delete(cache, cmdAndGrpcDashboardName)
		case commonpb.MonitorUrlType_MUT_APP_INSIGHT:
			targets = append(targets, source)
			delete(cache, source.GetName())
		default:
			l.Warnf("invalid monitor url type: %s", protobuf.GetEnumStringOf(source.GetType()))
			continue
		}
	}

	for name := range cache {
		if name == cmdAndGrpcDashboardName {
			mu, err := l.generateGrafanaMonitorURL(conf.GrafanaBaseURL, commands, grpcPaths)
			if err != nil {
				l.Errorf(
					"failed to generate the grafana monitor url, url: %s, commands: %s, grpc_paths: %s, error: %+v",
					conf.GrafanaBaseURL, commandsStr, grpcPathsStr, err,
				)
				continue
			}

			targets = append(targets, mu)
		} else {
			targets = append(targets, l.generateAppInsightMonitorURL(conf.AppInsightBaseURL, name))
		}
	}

	return targets
}

func (l *UpdateMonitorURLOfPerfPlanRecordLogic) generateGrafanaMonitorURL(
	baseURL string, commands []uint32, grpcPaths []string,
) (*pb.MonitorUrl, error) {
	u, err := url.Parse(baseURL)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()),
			"failed to parse the monitor url, url: %s, error: %+v",
			baseURL, err,
		)
	}

	values := u.Query()
	// [2024-10-18] not set the	`from` and `to` query params, set these query params by frontend
	//if _, ok := values[fromOfQueryParams]; !ok && record.StartedAt.Valid && !record.StartedAt.Time.IsZero() {
	//	values[fromOfQueryParams] = []string{strconv.FormatInt(record.StartedAt.Time.UnixMilli(), 10)}
	//}
	//if _, ok := values[toOfQueryParams]; !ok && record.EndedAt.Valid && !record.EndedAt.Time.IsZero() {
	//	values[toOfQueryParams] = []string{strconv.FormatInt(record.EndedAt.Time.UnixMilli(), 10)}
	//}

	// remove duplicates from the command set
	commandSet := set.NewHashset(uint64(len(commands)), generic.Equals[string], generic.HashString)
	for _, cmd := range commands {
		commandSet.Put(strconv.FormatUint(uint64(cmd), 10))
	}
	for _, v := range values[cmdOfQueryParams] {
		commandSet.Put(v)
	}
	values[cmdOfQueryParams] = commandSet.Keys()

	// remove duplicates from the grpc path set
	grpcPathSet := set.NewHashset(
		uint64(len(grpcPaths)), generic.Equals[string], generic.HashString, grpcPaths...,
	)
	for _, v := range values[grpcPathOfQueryParams] {
		grpcPathSet.Put(v)
	}
	values[grpcPathOfQueryParams] = grpcPathSet.Keys()

	u.RawQuery = values.Encode()

	return &pb.MonitorUrl{
		Name: cmdAndGrpcDashboardName,
		Type: commonpb.MonitorUrlType_MUT_GRAFANA,
		Url:  u.String(),
	}, nil
}

func (l *UpdateMonitorURLOfPerfPlanRecordLogic) generateAppInsightMonitorURL(baseURL, service string) *pb.MonitorUrl {
	return &pb.MonitorUrl{
		Name: service,
		Type: commonpb.MonitorUrlType_MUT_APP_INSIGHT,
		Url:  fmt.Sprintf("%s?%s=&%s=%s", baseURL, clusterOfQueryParams, nameOfQueryParams, service),
	}
}

func makeKeyByUpdateMonitorURLReq(in *pb.UpdateMonitorURLOfPerfPlanRecordReq) string {
	commands := make([]string, 0, len(in.GetCommands()))
	for _, cmd := range in.GetCommands() {
		commands = append(commands, strconv.FormatUint(uint64(cmd), 10))
	}
	slices.Sort(commands)

	grpcPaths := make([]string, 0, len(in.GetGrpcPaths()))
	grpcPaths = append(grpcPaths, in.GetGrpcPaths()...)
	slices.Sort(grpcPaths)

	httpServices := make([]string, 0, len(in.GetHttpServices()))
	httpServices = append(httpServices, in.GetHttpServices()...)
	slices.Sort(httpServices)

	return strings.Join(
		[]string{
			in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
			strings.Join(commands, sep), strings.Join(grpcPaths, sep), strings.Join(httpServices, sep),
		}, sep,
	)
}
