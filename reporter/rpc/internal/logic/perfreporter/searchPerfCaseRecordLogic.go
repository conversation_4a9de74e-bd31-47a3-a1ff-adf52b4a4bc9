package perfreporterlogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchPerfCaseRecordLogic struct {
	*BaseLogic
}

func NewSearchPerfCaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfCaseRecordLogic {
	return &SearchPerfCaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfCaseRecord 搜索压测计划执行记录下的压测用例执行记录
func (l *SearchPerfCaseRecordLogic) SearchPerfCaseRecord(in *pb.SearchPerfCaseRecordReq) (
	out *pb.SearchPerfCaseRecordResp, err error,
) {
	out = &pb.SearchPerfCaseRecordResp{}

	req := model.SearchPerfCaseExecutionRecordReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		TaskID:    in.GetTaskId(),
		ExecuteID: in.GetExecuteId(),
	}
	count, err := l.svcCtx.PerfCaseExecutionRecordModel.FindCountByReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf case execution records, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
			in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	records, err := l.svcCtx.PerfCaseExecutionRecordModel.FindAllByReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf case execution records, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
			in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.PerfCaseRecord, 0, len(records))
	for _, record := range records {
		item := &pb.PerfCaseRecord{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf case execution record to response, record: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(record), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = uint64(math.Ceil(float64(out.TotalCount) / float64(out.PageSize)))
	}

	return out, nil
}
