package perfreporterlogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreatePerfPlanRecordLogic struct {
	*BaseLogic
}

func NewCreatePerfPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfPlanRecordLogic {
	return &CreatePerfPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePerfPlanRecord 创建压测计划执行记录
func (l *CreatePerfPlanRecordLogic) CreatePerfPlanRecord(in *pb.CreatePerfPlanRecordReq) (
	out *pb.CreatePerfPlanRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockPerfPlanRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)
	fn := func() error {
		_, err = model.CheckPerfPlanExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.PerfPlanExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			if re, ok := errorx.RootError(err); ok && re.Code() != errorx.NotExists {
				return err
			}
		} else {
			return errorx.Errorf(
				errorx.AlreadyExists,
				"the perf plan execution record already exists, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}

		now := time.Now()
		startedAt := now
		if in.GetStartedAt() != nil && in.GetStartedAt().IsValid() && in.GetStartedAt().GetSeconds() > 0 {
			if startTime := in.GetStartedAt().AsTime(); !startTime.IsZero() {
				startedAt = startTime
			}
		}

		services := ""
		if len(in.GetServices()) > 0 {
			services = protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetServices())
		}

		executedBy := in.GetExecutedBy()
		if executedBy == "" {
			executedBy = l.currentUser.Account
		}

		record := &model.PerfPlanExecutionRecord{
			TaskId:         taskID,
			ExecuteId:      executeID,
			ProjectId:      projectID,
			PlanId:         in.GetPlanId(),
			PlanName:       in.GetPlanName(),
			TriggerMode:    protobuf.GetEnumStringOf(in.GetTriggerMode()),
			TargetMaxRps:   in.GetTargetMaxRps(),
			TargetDuration: int64(in.GetTargetDuration()),
			Protocol:       protobuf.GetEnumStringOf(in.GetProtocol()),
			TargetEnv:      protobuf.GetEnumStringOf(in.GetTargetEnv()),
			Status: sql.NullString{
				String: in.GetStatus(),
				Valid:  in.GetStatus() != "",
			},
			TaskType:      protobuf.GetEnumStringOf(in.GetTaskType()),
			ExecutionMode: protobuf.GetEnumStringOf(in.GetExecutionMode()),
			Services: sql.NullString{
				String: services,
				Valid:  services != "",
			},
			ExecutedBy: executedBy,
			StartedAt: sql.NullTime{
				Time:  startedAt,
				Valid: true,
			},
			CreatedBy: l.currentUser.Account,
			UpdatedBy: l.currentUser.Account,
			CreatedAt: now,
			UpdatedAt: now,
		}

		return l.svcCtx.PerfPlanExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.PerfPlanExecutionRecordModel.Insert(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to create perf plan execution record, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
						taskID, executeID, projectID, in.GetPlanId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.CreatePerfPlanRecordResp{}, nil
}
