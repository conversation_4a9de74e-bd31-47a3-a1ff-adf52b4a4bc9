package perfreporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPerfSuiteRecordLogic struct {
	*BaseLogic
}

func NewGetPerfSuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPerfSuiteRecordLogic {
	return &GetPerfSuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetPerfSuiteRecord 获取压测集合执行记录
func (l *GetPerfSuiteRecordLogic) GetPerfSuiteRecord(in *pb.GetPerfSuiteRecordReq) (
	out *pb.GetPerfSuiteRecordResp, err error,
) {
	record, err := model.CheckPerfSuiteExecutionRecordByExecuteID(
		l.ctx, l.svcCtx.PerfSuiteExecutionRecordModel, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.GetPerfSuiteRecordResp{Record: &pb.PerfSuiteRecord{}}
	if err = utils.Copy(out.Record, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf suite execution record to response, record: %s, error: %+v",
			jsonx.MarshalIgnoreError(record), err,
		)
	}

	return out, nil
}
