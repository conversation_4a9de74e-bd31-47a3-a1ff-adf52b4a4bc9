package uireporterlogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUICaseRecordLogic struct {
	*BaseLogic
}

func NewGetUICaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUICaseRecordLogic {
	return &GetUICaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetUICaseRecord 获取UI用例执行记录
func (l *GetUICaseRecordLogic) GetUICaseRecord(in *pb.GetUICaseRecordReq) (out *pb.GetUICaseRecordResp, err error) {
	record, err := l.svcCtx.UICaseExecutionRecordModel.FindOneByTaskIDExecuteIDProjectID(
		l.ctx, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find ui case execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the ui case execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
					in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
				),
			)
		}
	}

	out = &pb.GetUICaseRecordResp{Record: &pb.UICaseRecord{}}
	if err = utils.Copy(out.Record, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui case execution record to response, record: %+v, error: %+v",
			record, err,
		)
	}

	return out, nil
}
