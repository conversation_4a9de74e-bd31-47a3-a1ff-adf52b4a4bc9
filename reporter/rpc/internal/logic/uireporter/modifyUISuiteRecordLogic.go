package uireporterlogic

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyUISuiteRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewModifyUISuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyUISuiteRecordLogic {
	return &ModifyUISuiteRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ModifyUISuiteRecord 修改UI集合执行记录
func (l *ModifyUISuiteRecordLogic) ModifyUISuiteRecord(in *pb.PutUISuiteRecordRequest) (
	*pb.ModifyUISuiteRecordResponse, error,
) {
	err := CheckNotNullUISuite(in, modify)
	if err != nil {
		return nil, err
	}

	msg := fmt.Sprintf(
		"ui_suite_execution_record with task_id[%s] and project_id[%s] and execute_id[%s]",
		in.TaskId, in.ProjectId, in.ExecuteId,
	)

	redisKey := fmt.Sprintf("ui_%s_%s_%s", in.TaskId, in.ProjectId, in.ExecuteId)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)

	// 获取之前创建的执行记录
	record := &model.UiSuiteExecutionRecord{}
	if redisErr == nil && recordStr != "" {
		_, _ = l.svcCtx.Redis.Del(redisKey)
		_ = json.Unmarshal([]byte(recordStr), &record)
	} else {
		selectBuilder := l.svcCtx.UISuiteExecutionRecordModel.SelectBuilder()
		selectBuilder = selectBuilder.Where(
			"`task_id` = ? AND `project_id` = ? AND `execute_id` = ?",
			in.TaskId, in.ProjectId, in.ExecuteId,
		).Limit(1)
		result, err2 := l.svcCtx.UISuiteExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
		if err2 != nil || len(result) == 0 {
			return nil, errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err))
		}
		record = result[0]
	}

	record = ConvertUISuiteModel(in, record)
	err = l.svcCtx.UISuiteExecutionRecordModel.UpdateRecordWithDefaultUpdatedAt(l.ctx, record)
	if err != nil {
		return nil, errorx.Err(errorx.DBError, fmt.Sprintf("failed to update %s, error: %+v", msg, err))
	}

	return &pb.ModifyUISuiteRecordResponse{}, nil
}
