package uireporterlogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func ConvertUIPlanModel(
	ctx context.Context, in *pb.PutUIPlanRecordRequest, old *model.UiPlanExecutionRecord,
) (out *model.UiPlanExecutionRecord) {
	if old == nil {
		old = &model.UiPlanExecutionRecord{}
	}

	var (
		status      = StringIfOr(in.GetStatus(), old.Status.String)
		content     = StringIfOr(in.GetContent(), old.Content.String)
		endedAt     = Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64)
		executeData = StringIfOr(in.GetExecuteData(), old.ExecuteData.String)
	)

	out = &model.UiPlanExecutionRecord{
		Id:            old.Id,
		ProjectId:     StringIfOr(old.ProjectId, in.GetProjectId()),
		PlanId:        StringIfOr(old.PlanId, in.GetPlanId()),
		PlanName:      StringIfOr(old.PlanName, in.GetPlanName()),
		TriggerMode:   StringIfOr(old.TriggerMode, in.GetTriggerMode()),
		TaskId:        StringIfOr(old.TaskId, in.GetTaskId()),
		ExecuteId:     StringIfOr(old.ExecuteId, in.GetExecuteId()),
		TotalSuite:    Int64IfOr(in.GetTotalSuite(), old.TotalSuite),
		FinishedSuite: Int64IfOr(in.GetFinishedSuite(), old.FinishedSuite),
		SuccessSuite:  Int64IfOr(in.GetSuccessSuite(), old.SuccessSuite),
		TotalCase:     Int64IfOr(in.GetTotalCase(), old.TotalCase),
		CostTime:      Int64IfOr(0, old.CostTime),
		FinishedCase:  Int64IfOr(in.GetFinishedCase(), old.FinishedCase),
		SuccessCase:   Int64IfOr(in.GetSuccessCase(), old.SuccessCase),
		Finished:      Int64IfOr(in.GetFinished(), old.Finished),
		Status: sql.NullString{
			String: status,
			Valid:  status != "",
		},
		Content: sql.NullString{
			String: content,
			Valid:  content != "",
		},
		ExecutedBy: StringIfOr(old.ExecutedBy, in.GetExecutedBy()),
		StartedAt:  Int64IfOr(old.StartedAt, in.GetStartedAt()),
		EndedAt: sql.NullInt64{
			Int64: endedAt,
			Valid: endedAt > 0,
		},
		ExecuteData: sql.NullString{
			String: executeData,
			Valid:  executeData != "",
		},
		CreatedAt:      old.CreatedAt,
		UpdatedAt:      old.UpdatedAt,
		Cleaned:        old.Cleaned,
		ExecuteStatus:  old.ExecuteStatus,
		ExecutedResult: old.ExecutedResult,
		WaitTime:       old.WaitTime,
		PriorityType:   Int64IfOr(old.PriorityType, int64(in.GetPriorityType())),
		UpdateAt: sql.NullInt64{
			Int64: time.Now().UnixMilli(),
			Valid: true,
		},
	}

	switch in.GetExecuteStatus() {
	case commonpb.ExecuteStatus_TES_EXECUTING:
		logx.Debugf("task ConvertUIPlanModel ExecuteStatus_TES_EXECUTING info1: %s", in)
		if old.ExecuteStatus == int64(commonpb.ExecuteStatus_TES_INIT) {
			logx.Debugf("task ConvertUIPlanModel ExecuteStatus_TES_EXECUTING info2: %s", in)
			out.ExecuteStatus = int64(commonpb.ExecuteStatus_TES_EXECUTING)
			out.StartedAt = in.GetStartedAt()
			out.WaitTime = in.GetStartedAt() - old.CreatedAt.UnixMilli()
		}
	}

	convertComponentState2ExecuteXStatus(ctx, out, old)
	return out
}
