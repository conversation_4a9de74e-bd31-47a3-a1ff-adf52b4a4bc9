package uireporterlogic

import (
	"database/sql"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func ConvertUICaseModel(
	in *pb.PutUICaseRecordRequest, old *model.UiCaseExecutionRecord,
) (out *model.UiCaseExecutionRecord) {
	if old == nil {
		old = &model.UiCaseExecutionRecord{}
	}

	var (
		status   = StringIfOr(in.GetStatus(), old.Status.String)
		content  = StringIfOr(in.GetContent(), old.Content.String)
		endedAt  = Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64)
		callback = StringIfOr(in.GetCallback(), old.Callback.String)
	)

	out = &model.UiCaseExecutionRecord{
		Id:             old.Id,
		TaskId:         StringIfOr(old.TaskId, in.GetTaskId()),
		ProjectId:      StringIfOr(old.ProjectId, in.GetProjectId()),
		ExecuteId:      StringIfOr(old.ExecuteId, in.GetExecuteId()),
		CaseId:         StringIfOr(old.CaseId, in.GetCaseId()),
		CaseName:       StringIfOr(in.GetCaseName(), old.CaseName),
		SuiteExecuteId: StringIfOr(old.SuiteExecuteId, in.GetSuiteExecuteId()),
		Udid:           StringIfOr(in.GetUdid(), old.Udid),
		Status: sql.NullString{
			String: status,
			Valid:  status != "",
		},
		Content: sql.NullString{
			String: content,
			Valid:  content != "",
		},
		ExecutedBy: StringIfOr(in.GetExecutedBy(), old.ExecutedBy),
		StartedAt:  Int64IfOr(in.GetStartedAt(), old.StartedAt),
		EndedAt: sql.NullInt64{
			Int64: endedAt,
			Valid: endedAt > 0,
		},
		Callback: sql.NullString{
			String: callback,
			Valid:  callback != "",
		},
		CreatedAt: old.CreatedAt,
		UpdatedAt: old.UpdatedAt,
		Cleaned:   old.Cleaned,
	}

	if out.EndedAt.Int64 > out.StartedAt {
		out.CostTime = time.UnixMilli(out.EndedAt.Int64).Sub(time.UnixMilli(out.StartedAt)).Milliseconds()
	}

	return out
}

func GenerateUICaseCacheKey(in *pb.PutUICaseRecordRequest) string {
	return fmt.Sprintf(
		"ui_%s_%s_%s_%s", in.GetTaskId(), in.GetProjectId(), in.GetSuiteExecuteId(), in.GetExecuteId(),
	)
}

func generateUIDevicePerfDataLockKey(in *pb.SaveUIDevicePerfDataReq) string {
	return fmt.Sprintf(
		"%s:%s:%s:%s:%s:%s",
		common.ConstLockUIDevicePerfDataTaskIDExecuteIDProjectIDUDIDDataTypePrefix,
		in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), in.GetUdid(), protobuf.GetEnumStringOf(in.GetDataType()),
	)
}
