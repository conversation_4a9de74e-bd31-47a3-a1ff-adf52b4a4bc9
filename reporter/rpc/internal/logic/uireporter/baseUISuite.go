package uireporterlogic

import (
	"database/sql"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func ConvertUISuiteModel(
	in *pb.PutUISuiteRecordRequest, old *model.UiSuiteExecutionRecord,
) (out *model.UiSuiteExecutionRecord) {
	if old == nil {
		old = &model.UiSuiteExecutionRecord{}
	}

	var (
		status   = StringIfOr(in.GetStatus(), old.Status.String)
		content  = StringIfOr(in.GetContent(), old.Content.String)
		endedAt  = Int64IfOr(in.GetEndedAt(), old.EndedAt.Int64)
		callback = StringIfOr(in.GetCallback(), old.Callback.String)
	)

	out = &model.UiSuiteExecutionRecord{
		Id:            old.Id,
		TaskId:        StringIfOr(old.TaskId, in.GetTaskId()),
		ProjectId:     StringIfOr(old.ProjectId, in.GetProjectId()),
		ExecuteId:     StringIfOr(old.ExecuteId, in.GetExecuteId()),
		SuiteId:       StringIfOr(old.SuiteId, in.GetSuiteId()),
		SuiteName:     StringIfOr(in.GetSuiteName(), old.SuiteName),
		PlanExecuteId: StringIfOr(in.GetPlanExecuteId(), old.PlanExecuteId),
		TotalCase:     Int64IfOr(in.GetTotalCase(), old.TotalCase),
		SuccessCase:   Int64IfOr(in.GetSuccessCase(), old.SuccessCase),
		FailureCase:   Int64IfOr(in.GetFailureCase(), old.FailureCase),
		Status: sql.NullString{
			String: status,
			Valid:  status != "",
		},
		Content: sql.NullString{
			String: content,
			Valid:  content != "",
		},
		ExecutedBy: StringIfOr(in.GetExecutedBy(), old.ExecutedBy),
		StartedAt:  Int64IfOr(in.GetStartedAt(), old.StartedAt),
		EndedAt: sql.NullInt64{
			Int64: endedAt,
			Valid: endedAt > 0,
		},
		Callback: sql.NullString{
			String: callback,
			Valid:  callback != "",
		},
		CreatedAt: old.CreatedAt,
		UpdatedAt: old.UpdatedAt,
		Cleaned:   old.Cleaned,
	}

	if out.EndedAt.Int64 > out.StartedAt {
		out.CostTime = time.UnixMilli(out.EndedAt.Int64).Sub(time.UnixMilli(out.StartedAt)).Milliseconds()
	}

	return out
}
