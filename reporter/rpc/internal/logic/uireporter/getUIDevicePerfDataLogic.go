package uireporterlogic

import (
	"context"
	"regexp"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/collector"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const zeroOfY = "0"

var iosUDIDRegex = regexp.MustCompile(`^[0-9A-F]{8}-[0-9A-F]{16}$`)

type GetUIDevicePerfDataLogic struct {
	*BaseLogic
}

func NewGetUIDevicePerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIDevicePerfDataLogic {
	return &GetUIDevicePerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetUIDevicePerfData 获取UI测试设备性能数据
func (l *GetUIDevicePerfDataLogic) GetUIDevicePerfData(in *pb.GetUIDevicePerfDataReq) (
	out *pb.GetUIDevicePerfDataResp, err error,
) {
	out = &pb.GetUIDevicePerfDataResp{}

	if in.GetUdid() == "" {
		out.Data, err = l.getByTask(in)
	} else {
		out.Data, err = l.getByUDID(in)
	}

	if err != nil {
		return nil, err
	}

	return out, nil
}

func (l *GetUIDevicePerfDataLogic) getByTask(in *pb.GetUIDevicePerfDataReq) (*pb.PerfData, error) {
	var (
		taskID      = in.GetTaskId()
		projectID   = in.GetProjectId()
		dataType    = in.GetDataType()
		dataTypeStr = protobuf.GetEnumStringOf(dataType)

		out = &pb.PerfData{
			DataType: dataType,
			Unit:     "",
			X:        make([]string, 0),
			Series:   make([]*pb.PerfData_Series, 0),
		}
	)

	records, err := l.svcCtx.UIDevicePerfDataModel.FindByTaskID(l.ctx, taskID, projectID, dataTypeStr)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui device perf data, task_id: %s, project_id: %s, data_type: %s, error: %+v",
			taskID, projectID, dataTypeStr, err,
		)
	}

	if len(records) == 0 {
		return out, nil
	}

	var (
		// record.udid -> record.x -> record
		cache           = make(map[string]map[string]*model.UiDevicePerfData, len(records))
		interval        time.Duration
		tmp, minX, maxX time.Time
	)

	// 获取数据类型下默认的指标名称
	series := collector.DefaultSeries(collector.DataType(dataTypeStr))

	for _, record := range records {
		if record.Series != string(series) {
			continue
		}

		if _, ok := cache[record.Udid]; !ok {
			cache[record.Udid] = make(map[string]*model.UiDevicePerfData, len(records))
		}

		// 同一数据类型下，采集间隔不变，因此取第一个记录的采集间隔
		if interval == 0 {
			interval = time.Duration(record.Interval) * time.Millisecond
		}

		tmp, err = time.Parse(time.DateTime, record.X)
		if err != nil {
			continue
		}

		// 按采集间隔对时间取整
		tmp = tmp.Round(interval)

		if tmp.Before(minX) {
			minX = tmp
		}
		if tmp.After(maxX) {
			maxX = tmp
		}
		cache[record.Udid][tmp.Format(time.DateTime)] = record
	}

	if len(cache) == 0 || interval == 0 || minX.IsZero() || maxX.IsZero() {
		return out, nil
	}

	var (
		capacity = maxX.Sub(minX)/interval + 1
		index    = -1
	)
	out.X = make([]string, 0, capacity)
	out.Series = make([]*pb.PerfData_Series, 0, len(cache))

	for u, m := range cache {
		index += 1
		out.Series = append(
			out.Series, &pb.PerfData_Series{
				Name: u,
				Y:    make([]string, 0, capacity),
			},
		)

		for x := minX; x.Before(maxX); x = x.Add(interval) {
			t := x.Format(time.DateTime)
			if index == 0 {
				out.X = append(out.X, t)
			}

			v, ok := m[t]
			if !ok {
				out.Series[index].Y = append(out.Series[index].Y, zeroOfY)
			} else {
				if out.Unit == "" && out.Unit != v.Unit {
					out.Unit = v.Unit
				}
				out.Series[index].Y = append(out.Series[index].Y, v.Y)
			}
		}
	}

	return out, nil
}

func (l *GetUIDevicePerfDataLogic) getByUDID(in *pb.GetUIDevicePerfDataReq) (*pb.PerfData, error) {
	var (
		taskID      = in.GetTaskId()
		projectID   = in.GetProjectId()
		udid        = in.GetUdid()
		dataType    = in.GetDataType()
		dataTypeStr = protobuf.GetEnumStringOf(dataType)

		out = &pb.PerfData{
			DataType: dataType,
			Unit:     "",
			X:        make([]string, 0),
			Series:   make([]*pb.PerfData_Series, 0),
		}
	)

	records, err := l.svcCtx.UIDevicePerfDataModel.FindByUDID(l.ctx, taskID, projectID, udid, dataTypeStr)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui device perf data, task_id: %s, project_id: %s, udid: %s, data_type: %s, error: %+v",
			taskID, projectID, udid, dataTypeStr, err,
		)
	}

	if len(records) == 0 {
		return out, nil
	}

	var (
		// record.series -> record.x -> record
		cache           = make(map[string]map[string]*model.UiDevicePerfData, len(records))
		interval        time.Duration
		tmp, minX, maxX time.Time
	)

	for _, record := range records {
		if _, ok := cache[record.Series]; !ok {
			cache[record.Series] = make(map[string]*model.UiDevicePerfData, len(records))
		}

		// 同一数据类型下，采集间隔不变，因此取第一个记录的采集间隔
		if interval == 0 {
			interval = time.Duration(record.Interval) * time.Millisecond
		}

		tmp, err = time.Parse(time.DateTime, record.X)
		if err != nil {
			continue
		}

		// 按采集间隔对时间取整
		tmp = tmp.Round(interval)

		if minX.IsZero() || tmp.Before(minX) {
			minX = tmp
		}
		if maxX.IsZero() || tmp.After(maxX) {
			maxX = tmp
		}
		cache[record.Series][tmp.Format(time.DateTime)] = record
	}

	if len(cache) == 0 || interval == 0 || minX.IsZero() || maxX.IsZero() {
		return out, nil
	}

	var (
		capacity = maxX.Sub(minX)/interval + 1
		index    = -1
	)
	out.X = make([]string, 0, capacity)
	out.Series = make([]*pb.PerfData_Series, 0, len(cache))

	for s, m := range cache {
		index += 1
		out.Series = append(
			out.Series, &pb.PerfData_Series{
				Name: s,
				Y:    make([]string, 0, capacity),
			},
		)

		for x := minX; x.Before(maxX); x = x.Add(interval) {
			t := x.Format(time.DateTime)
			if index == 0 {
				out.X = append(out.X, t)
			}

			v, ok := m[t]
			if !ok {
				out.Series[index].Y = append(out.Series[index].Y, zeroOfY)
			} else {
				if out.Unit == "" && out.Unit != v.Unit {
					out.Unit = v.Unit
				}
				out.Series[index].Y = append(out.Series[index].Y, v.Y)
			}
		}
	}

	return out, nil
}
