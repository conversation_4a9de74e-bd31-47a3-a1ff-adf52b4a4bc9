package uireporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		converters: []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) generateStepID() (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenStepID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.UICaseExecutionStepModel.FindOneByStepId(l.ctx, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	stepID := g.Next()
	if stepID == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate step id, please try it later",
			),
		)
	}

	return stepID, nil
}

func (l *BaseLogic) getPlanCaseInfoByCalculate(projectId, taskId, planExecuteId string) (int64, int64, int64, error) {
	selectBuilder := l.svcCtx.UISuiteExecutionRecordModel.SelectBuilder().Where(
		"`task_id` = ? AND `project_id` = ? AND `plan_execute_id` = ?",
		taskId, projectId, planExecuteId,
	)
	uiSuiteRecords, err := l.svcCtx.UISuiteExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil {
		return 0, 0, 0, err
	}

	var totalCase, finishedCase, successCase int64

	for _, uiSuiteRecord := range uiSuiteRecords {
		totalCase += uiSuiteRecord.TotalCase
		finishedCase += uiSuiteRecord.SuccessCase + uiSuiteRecord.FailureCase
		successCase += uiSuiteRecord.SuccessCase
	}

	return totalCase, finishedCase, successCase, nil
}

func StringIfOr(a, b string) string {
	if a != "" {
		return a
	}
	return b
}

func Int64IfOr(a, b int64) int64 {
	if a > 0 {
		return a
	}
	return b
}
