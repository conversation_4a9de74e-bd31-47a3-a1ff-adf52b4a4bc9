package uireporterlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateUIPlanRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateUIPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUIPlanRecordLogic {
	return &CreateUIPlanRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// CreateUIPlanRecord 创建UI计划执行记录
func (l *CreateUIPlanRecordLogic) CreateUIPlanRecord(in *pb.PutUIPlanRecordRequest) (
	*pb.CreateUIPlanRecordResponse, error,
) {
	err := CheckNotNullUIPlan(in, create)
	if err != nil {
		return nil, err
	}

	/*if in.StartedAt <= 0 {
		return nil, errorx.Err(errorx.ValidateParamError, "开始执行的时间戳必须大于0")
	}*/

	record := ConvertUIPlanModel(l.ctx, in, nil)
	result, insertErr := l.svcCtx.UIPlanExecutionRecordModel.InsertRecordWithDefaultCreatedAt(l.ctx, record)

	if insertErr != nil {
		return nil, errorx.Err(
			errorx.DBError, fmt.Sprintf("failed to create ui_plan_execution_record, error: %+v", insertErr),
		)
	}

	recordId, _ := result.LastInsertId()
	record.Id = recordId
	executionRecordBytes, _ := json.Marshal(record)
	executionRecordStr := string(executionRecordBytes)

	redisKey := fmt.Sprintf("ui_%s_%s_%s", in.TaskId, in.ProjectId, record.ExecuteId)
	_, err = redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, redisKey, redislock.WithValue(executionRecordStr), redislock.WithExpire(3600*time.Second),
	)
	if err != nil {
		return nil, errorx.Err(
			errorx.RedisError,
			fmt.Sprintf("create ui_plan_execution_record success, but encounter redis error: %+v", err),
		)
	}

	resp := &pb.CreateUIPlanRecordResponse{
		ExecuteId: record.ExecuteId,
	}

	return resp, nil
}
