package uireporterlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateUISuiteRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateUISuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUISuiteRecordLogic {
	return &CreateUISuiteRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// CreateUISuiteRecord 创建UI集合执行记录
func (l *CreateUISuiteRecordLogic) CreateUISuiteRecord(in *pb.PutUISuiteRecordRequest) (
	*pb.CreateUISuiteRecordResponse, error,
) {
	err := CheckNotNullUISuite(in, create)
	if err != nil {
		return nil, err
	}

	if in.StartedAt <= 0 {
		return nil, errorx.Err(errorx.ValidateParamError, "开始执行的时间戳必须大于0")
	}

	record := ConvertUISuiteModel(in, nil)
	result, insertErr := l.svcCtx.UISuiteExecutionRecordModel.InsertRecordWithDefaultCreatedAt(l.ctx, record)

	if insertErr != nil {
		return nil, errorx.Err(
			errorx.DBError, fmt.Sprintf("failed to create ui_suite_execution_record, error: %+v", insertErr),
		)
	}

	recordId, _ := result.LastInsertId()
	record.Id = recordId

	executionRecordBytes, _ := json.Marshal(record)
	executionRecordStr := string(executionRecordBytes)

	redisKey := fmt.Sprintf("ui_%s_%s_%s_%s", in.TaskId, in.ProjectId, in.PlanExecuteId, record.ExecuteId)
	_, err = redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, redisKey, redislock.WithValue(executionRecordStr), redislock.WithExpire(1800*time.Second),
	)
	if err != nil {
		return nil, errorx.Err(
			errorx.RedisError,
			fmt.Sprintf("create ui_suite_execution_record success, but encounter redis error: %+v", err),
		)
	}

	resp := &pb.CreateUISuiteRecordResponse{
		ExecuteId: record.ExecuteId,
	}

	return resp, nil
}
