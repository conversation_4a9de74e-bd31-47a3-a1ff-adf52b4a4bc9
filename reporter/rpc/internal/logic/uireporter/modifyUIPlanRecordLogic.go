package uireporterlogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyUIPlanRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewModifyUIPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyUIPlanRecordLogic {
	return &ModifyUIPlanRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ModifyUIPlanRecord 修改UI计划执行记录
func (l *ModifyUIPlanRecordLogic) ModifyUIPlanRecord(in *pb.PutUIPlanRecordRequest) (
	*pb.ModifyUIPlanRecordResponse, error,
) {
	err := CheckNotNullUIPlan(in, modify)
	if err != nil {
		return nil, err
	}

	msg := fmt.Sprintf(
		"ui_plan_execution_record with task_id[%s] and project_id[%s] and execute_id[%s]",
		in.TaskId, in.ProjectId, in.ExecuteId,
	)

	selectBuilder := l.svcCtx.UIPlanExecutionRecordModel.SelectBuilder()
	selectBuilder = selectBuilder.Where(
		"`project_id` = ? AND `plan_id` = ? AND `task_id` = ? ",
		in.ProjectId, in.PlanId, in.TaskId,
	).Limit(1)
	result, err := l.svcCtx.UIPlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil || len(result) == 0 {
		return nil, errorx.Err(errorx.DBError, fmt.Sprintf("failed to find %s, error: %+v", msg, err))
	}
	record := result[0]

	record = ConvertUIPlanModel(l.ctx, in, record)
	err = l.svcCtx.UIPlanExecutionRecordModel.UpdateRecordWithDefaultUpdatedAt(l.ctx, record)
	if err != nil {
		return nil, errorx.Err(errorx.DBError, fmt.Sprintf("failed to update %s, error: %+v", msg, err))
	}

	return &pb.ModifyUIPlanRecordResponse{}, nil
}

// convertComponentState2ExecuteXStatus
func convertComponentState2ExecuteXStatus(
	ctx context.Context, target, source *model.UiPlanExecutionRecord,
) {
	/*
		1.旧状态转换成 新的两个状态
		    -结果(异常)
		    -结果(失败)
		    -结果(成功)
		    -执行状态(执行中)
		    -执行状态(停止)
		    -执行状态(完成)
		    -并且更新等待时间 执行时间
		2.同步第一次的开始时间和执行中
		    - 更新等待时间
		    - 更新执行中
	*/
	var state dispatcherpb.ComponentState
	sTmp, ok := dispatcherpb.ComponentState_value[target.Status.String]
	if ok {
		state = dispatcherpb.ComponentState(sTmp)
	}
	endTime := target.EndedAt.Int64
	costTime := endTime - target.CreatedAt.UnixMilli()
	if target.StartedAt > 0 {
		costTime = endTime - target.StartedAt
	}
	logx.WithContext(ctx).Debugf(
		"convertComponentState2ExecuteXStatus：开始时间[%d]，结束时间[%d]，创建时间[%d]", target.StartedAt, endTime,
		target.CreatedAt.UnixMilli(),
	)
	logx.WithContext(ctx).Debugf(
		"convertComponentState2ExecuteXStatus：costTime[%d]", costTime,
	)
	if endTime > 0 { // 存在结束时间
		target.EndedAt = sql.NullInt64{
			Int64: endTime,
			Valid: true,
		}
		target.CostTime = costTime
		// target.WaitTime = costTime
		if target.WaitTime <= 0 {
			target.WaitTime = costTime
		}
	}
	switch state {
	case dispatcherpb.ComponentState_Stop:
		fallthrough
	case dispatcherpb.ComponentState_Panic:
		if source.ExecuteStatus != int64(commonpb.ExecuteStatus_TES_STOP) {
			target.ExecuteStatus = int64(commonpb.ExecuteStatus_TES_STOP)
		}
		if source.ExecutedResult != int64(commonpb.ExecutedResult_TER_PANIC) {
			target.ExecutedResult = int64(commonpb.ExecutedResult_TER_PANIC)
		}

	case dispatcherpb.ComponentState_Failure:
		if source.ExecuteStatus != int64(commonpb.ExecuteStatus_TES_FINISH) {
			target.ExecuteStatus = int64(commonpb.ExecuteStatus_TES_FINISH)
		}
		if source.ExecutedResult != int64(commonpb.ExecutedResult_TER_FAILURE) {
			target.ExecutedResult = int64(commonpb.ExecutedResult_TER_FAILURE)
		}
	case dispatcherpb.ComponentState_Success:
		if source.ExecuteStatus != int64(commonpb.ExecuteStatus_TES_FINISH) {
			target.ExecuteStatus = int64(commonpb.ExecuteStatus_TES_FINISH)
		}
		if source.ExecutedResult != int64(commonpb.ExecutedResult_TER_SUCCESS) {
			target.ExecutedResult = int64(commonpb.ExecutedResult_TER_SUCCESS)
		}
	default:
		return
	}
}
