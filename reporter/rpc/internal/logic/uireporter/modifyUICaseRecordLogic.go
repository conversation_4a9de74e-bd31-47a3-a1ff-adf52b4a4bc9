package uireporterlogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const defaultContentSliceLength = 1024

type ModifyUICaseRecordLogic struct {
	*BaseLogic
}

func NewModifyUICaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyUICaseRecordLogic {
	return &ModifyUICaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

type contentSlice struct {
	content string
	index   int64
}

// ModifyUICaseRecord 修改UI用例执行记录
func (l *ModifyUICaseRecordLogic) ModifyUICaseRecord(in *pb.PutUICaseRecordRequest) (
	*pb.ModifyUICaseRecordResponse, error,
) {
	var (
		taskID    = in.GetTaskId()
		projectID = in.GetProjectId()
		executeID = in.GetExecuteId()
	)

	err := CheckNotNullUICase(in, modify)
	if err != nil {
		return nil, err
	}

	record := &model.UiCaseExecutionRecord{}

	redisKey := GenerateUICaseCacheKey(in)
	recordStr, err := l.svcCtx.Redis.Get(redisKey)
	if err == nil && recordStr != "" {
		_, _ = l.svcCtx.Redis.Del(redisKey)

		//_ = json.Unmarshal([]byte(recordStr), &record)
		if err = jsonx.UnmarshalFromString(recordStr, record); err != nil {
			l.Errorf("failed to unmarshal the cache data of ui case record, data: %s, error: %+v", recordStr, err)
		}
	}
	if err != nil || record.Id == 0 {
		record, err = l.svcCtx.UICaseExecutionRecordModel.FindOneByTaskIDExecuteIDProjectID(
			l.ctx, taskID, executeID, projectID,
		)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return nil, errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find ui case execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
					taskID, executeID, projectID, err,
				)
			} else {
				return nil, errors.WithStack(
					errorx.Errorf(
						errorx.NotExists,
						"the ui case execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
						taskID, executeID, projectID,
					),
				)
			}
		}
	}

	record = ConvertUICaseModel(in, record)
	var content string
	if record.Content.Valid {
		content = record.Content.String
		record.Content = sql.NullString{}
	}

	_, err = l.svcCtx.UICaseExecutionRecordModel.UpdateTX(l.ctx, nil, record)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update table[%s] with values[%+v], error: %+v",
			l.svcCtx.UICaseExecutionRecordModel.Table(), record, err,
		)
	}

	if content == "" {
		l.Warnf(
			"the content of ui case execution record is empty, task_id: %s, execute_id: %s, project_id: %s",
			taskID, executeID, projectID,
		)
	} else {
		var steps []*pb.UICaseStep
		if err = protobuf.UnmarshalJSONWithMessagesFromString(content, &steps); err != nil {
			l.Errorf("failed to unmarshal the content of ui case record, content: %s, error: %+v", content, err)
			return &pb.ModifyUICaseRecordResponse{}, nil
		}

		if err = mr.MapReduceVoid[*pb.UICaseStep, any](
			func(source chan<- *pb.UICaseStep) {
				for _, step := range steps {
					source <- step
				}
			}, func(item *pb.UICaseStep, writer mr.Writer[any], cancel func(error)) {
				var (
					stepID string
					err    error

					now = time.Now()
				)
				defer func() {
					if err != nil {
						cancel(err)
					}
				}()

				if item == nil {
					return
				}
				if item.GetStepId() == "" {
					stepID, err = l.generateStepID()
					if err != nil {
						l.Errorf(
							"failed to generate step id, task_id: %s, execute_id: %s, error: %+v",
							taskID, executeID, err,
						)
						return
					}

					item.StepId = stepID
				}

				data := &model.UiCaseExecutionStep{
					TaskId:    taskID,
					ExecuteId: executeID,
					ProjectId: projectID,
					CaseId:    in.GetCaseId(),
					StepId:    item.GetStepId(),
					Stage:     int64(item.GetStage().Number()),
					Index:     item.GetIndex(),
					Name:      item.GetName(),
					Status:    item.GetStatus(),
					StartedAt: time.UnixMilli(item.GetStartedAt()),
					EndedAt:   time.UnixMilli(item.GetEndedAt()),
					CreatedBy: in.GetExecutedBy(),
					UpdatedBy: in.GetExecutedBy(),
					CreatedAt: now,
					UpdatedAt: now,
				}
				if _, err = l.svcCtx.UICaseExecutionStepModel.Insert(l.ctx, nil, data); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to insert table[%s] with values[%+v], error: %+v",
						l.svcCtx.UICaseExecutionStepModel.Table(), data, err,
					)
					return
				}

				err = l.svcCtx.UICaseExecutionStepContentModel.Trans(
					l.ctx,
					func(context context.Context, session sqlx.Session) error {
						return mr.MapReduceVoid[contentSlice, any](
							func(source chan<- contentSlice) {
								var (
									_content = []rune(item.GetContent())
									_length  = len(_content)
									_index   = 1
								)

								for i := 0; i < _length; i += defaultContentSliceLength {
									end := i + defaultContentSliceLength
									if end > _length {
										end = _length
									}

									source <- contentSlice{
										content: string(_content[i:end]),
										index:   int64(_index),
									}
									_index += 1
								}
							}, func(item contentSlice, writer mr.Writer[any], cancel func(error)) {
								var (
									_err error

									_now = time.Now()
								)
								defer func() {
									if _err != nil {
										cancel(_err)
									}
								}()

								_data := &model.UiCaseExecutionStepContent{
									TaskId: taskID,
									StepId: stepID,
									Content: sql.NullString{
										String: item.content,
										Valid:  true,
									},
									Index:     item.index,
									CreatedBy: in.GetExecutedBy(),
									UpdatedBy: in.GetExecutedBy(),
									CreatedAt: _now,
									UpdatedAt: _now,
								}
								if _, _err = l.svcCtx.UICaseExecutionStepContentModel.Insert(
									l.ctx, session, _data,
								); _err != nil {
									_err = errors.Wrapf(
										errorx.Err(errorx.DBError, err.Error()),
										"failed to insert table[%s] with values[%+v], error: %+v",
										l.svcCtx.UICaseExecutionStepContentModel.Table(), _data, _err,
									)
									return
								}
							}, func(pipe <-chan any, cancel func(error)) {
							}, mr.WithContext(context),
						)
					},
				)
			}, func(pipe <-chan any, cancel func(error)) {
			},
		); err != nil {
			l.Errorf(
				"failed to save the step contents of ui case record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				taskID, executeID, projectID, err,
			)
		}
	}

	return &pb.ModifyUICaseRecordResponse{}, nil
}
