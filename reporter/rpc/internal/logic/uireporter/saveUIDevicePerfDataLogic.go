package uireporterlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SaveUIDevicePerfDataLogic struct {
	*BaseLogic
}

func NewSaveUIDevicePerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveUIDevicePerfDataLogic {
	return &SaveUIDevicePerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SaveUIDevicePerfData 保存UI测试设备性能数据
func (l *SaveUIDevicePerfDataLogic) SaveUIDevicePerfData(in *pb.SaveUIDevicePerfDataReq) (
	out *pb.SaveUIDevicePerfDataResp, err error,
) {
	key := generateUIDevicePerfDataLockKey(in)
	fn := func() error {
		record, err := l.svcCtx.UIDevicePerfDataModel.FindOneByX(
			l.ctx, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), in.GetUdid(),
			protobuf.GetEnumStringOf(in.GetDataType()), in.GetSeries(), in.GetX(),
		)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find ui device perf data, in: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(in), err,
			)
		}

		now := time.Now()
		if record == nil {
			record = &model.UiDevicePerfData{
				TaskId:    in.GetTaskId(),
				ExecuteId: in.GetExecuteId(),
				ProjectId: in.GetProjectId(),
				Udid:      in.GetUdid(),
				DataType:  protobuf.GetEnumStringOf(in.GetDataType()),
				Interval:  in.GetInterval(),
				Series:    in.GetSeries(),
				Unit:      in.GetUnit(),
				X:         in.GetX(),
				Y:         in.GetY(),
				CreatedBy: in.GetExecutedBy(),
				UpdatedBy: in.GetExecutedBy(),
				CreatedAt: now,
				UpdatedAt: now,
			}
			if _, err = l.svcCtx.UIDevicePerfDataModel.Insert(l.ctx, nil, record); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIDevicePerfDataModel.Table(), jsonx.MarshalIgnoreError(record), err,
				)
			}
		} else {
			record.Interval = in.GetInterval()
			record.Unit = in.GetUnit()
			record.Y = in.GetY()
			record.UpdatedBy = in.GetExecutedBy()
			record.UpdatedAt = now
			if _, err = l.svcCtx.UIDevicePerfDataModel.Update(l.ctx, nil, record); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIDevicePerfDataModel.Table(), jsonx.MarshalIgnoreError(record), err,
				)
			}
		}

		return nil
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &pb.SaveUIDevicePerfDataResp{}, nil
}
