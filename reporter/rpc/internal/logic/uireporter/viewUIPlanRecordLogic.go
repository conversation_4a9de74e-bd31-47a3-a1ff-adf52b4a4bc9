package uireporterlogic

import (
	"context"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ViewUIPlanRecordLogic struct {
	*BaseLogic
}

func NewViewUIPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewUIPlanRecordLogic {
	return &ViewUIPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewUIPlanRecord 查看UI计划执行记录
func (l *ViewUIPlanRecordLogic) ViewUIPlanRecord(in *pb.ViewUIPlanRecordRequest) (
	resp *pb.ViewUIPlanRecordResponse, err error,
) {
	selectBuilder := l.svcCtx.UIPlanExecutionRecordModel.SelectBuilder().Where(
		"`task_id` = ? AND `project_id` = ? AND `execute_id` = ?",
		in.TaskId, in.ProjectId, in.ExecuteId,
	)

	planRecords, err := l.svcCtx.UIPlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil {
		msg := fmt.Sprintf(
			"failed to find plan record with task_id[%s], project_id[%s], execute_id[%s], error: %+v",
			in.TaskId, in.ProjectId, in.ExecuteId, err,
		)
		return nil, errorx.Err(errorx.DBError, msg)
	} else if len(planRecords) == 0 {
		msg := fmt.Sprintf(
			"failed to find plan record with task_id[%s], project_id[%s], execute_id[%s]",
			in.TaskId, in.ProjectId, in.ExecuteId,
		)
		return nil, errorx.Err(errorx.NotExists, msg)
	}

	planRecord := planRecords[0]

	var totalCase, finishedCase, successCase int64
	if planRecord.Finished == int64(constants.Finished) {
		totalCase, finishedCase, successCase = planRecord.TotalCase, planRecord.FinishedCase, planRecord.SuccessCase
	} else {
		totalCase, finishedCase, successCase, err = l.getPlanCaseInfoByCalculate(
			planRecord.ProjectId, planRecord.TaskId, planRecord.ExecuteId,
		)
		if err != nil {
			return nil, err
		}
	}

	priorityType, err := utils.NumberConvertToPBEnum(planRecord.PriorityType, commonpb.PriorityType_Default)
	if err != nil {
		priorityType = commonpb.PriorityType(planRecord.PriorityType)
	}
	executeStatus, err := utils.NumberConvertToPBEnum(planRecord.ExecuteStatus, commonpb.ExecuteStatus_TES_INIT)
	if err != nil {
		executeStatus = commonpb.ExecuteStatus(planRecord.ExecuteStatus)
	}
	executeResult, err := utils.NumberConvertToPBEnum(planRecord.ExecutedResult, commonpb.ExecutedResult_TER_INIT)
	if err != nil {
		executeResult = commonpb.ExecutedResult(planRecord.ExecutedResult)
	}

	resp = &pb.ViewUIPlanRecordResponse{
		ProjectId:      planRecord.ProjectId,
		PlanId:         planRecord.PlanId,
		PlanName:       planRecord.PlanName,
		TaskId:         planRecord.TaskId,
		ExecuteId:      planRecord.ExecuteId,
		Status:         planRecord.Status.String,
		CostTime:       planRecord.CostTime,
		ExecutedBy:     planRecord.ExecutedBy,
		StartedAt:      planRecord.StartedAt,
		EndedAt:        planRecord.EndedAt.Int64,
		TotalSuite:     planRecord.TotalSuite,
		FinishedSuite:  planRecord.FinishedSuite,
		SuccessSuite:   planRecord.SuccessSuite,
		TotalCase:      totalCase,
		FinishedCase:   finishedCase,
		SuccessCase:    successCase,
		Content:        planRecord.Content.String,
		Finished:       planRecord.Finished,
		Cleaned:        planRecord.Cleaned,
		ExecuteData:    planRecord.ExecuteData.String,
		TriggerMode:    planRecord.TriggerMode,
		Type:           planRecord.TriggerMode,
		ExecuteStatus:  executeStatus.(commonpb.ExecuteStatus),
		PriorityType:   priorityType.(commonpb.PriorityType),
		ExecutedResult: executeResult.(commonpb.ExecutedResult),
		WaitTime:       planRecord.WaitTime,
		UpdateAt:       planRecord.UpdateAt.Int64,
	}

	return resp, nil
}
