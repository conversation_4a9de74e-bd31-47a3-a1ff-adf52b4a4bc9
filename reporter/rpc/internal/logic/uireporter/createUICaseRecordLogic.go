package uireporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateUICaseRecordLogic struct {
	*BaseLogic
}

func NewCreateUICaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUICaseRecordLogic {
	return &CreateUICaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateUICaseRecord 创建UI用例执行记录
func (l *CreateUICaseRecordLogic) CreateUICaseRecord(in *pb.PutUICaseRecordRequest) (
	*pb.CreateUICaseRecordResponse, error,
) {
	err := CheckNotNullUICase(in, create)
	if err != nil {
		return nil, err
	}

	if in.GetStartedAt() <= 0 {
		return nil, errorx.Err(errorx.ValidateParamError, "开始执行的时间戳必须大于0")
	}

	record := ConvertUICaseModel(in, nil)
	result, err := l.svcCtx.UICaseExecutionRecordModel.InsertTX(l.ctx, nil, record)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to insert values to table, table: %s, values: %s, error: %+v",
			l.svcCtx.UICaseExecutionRecordModel.Table(), jsonx.MarshalIgnoreError(record), err,
		)
	}

	recordId, _ := result.LastInsertId()
	record.Id = recordId

	err = l.svcCtx.Redis.SetexCtx(l.ctx, GenerateUICaseCacheKey(in), jsonx.MarshalToStringIgnoreError(record), 1800)
	if err != nil {
		l.Errorf(
			"failed to cache the ui case record, task_id: %s, execute_id: %s, error: %+v",
			in.GetTaskId(), in.GetExecuteId(), err,
		)
	}

	return &pb.CreateUICaseRecordResponse{
		ExecuteId: record.ExecuteId,
	}, nil
}
