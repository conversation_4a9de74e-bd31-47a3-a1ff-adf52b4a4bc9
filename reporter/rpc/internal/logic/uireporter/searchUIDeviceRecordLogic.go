package uireporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchUIDeviceRecordLogic struct {
	*BaseLogic
}

func NewSearchUIDeviceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUIDeviceRecordLogic {
	return &SearchUIDeviceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchUIDeviceRecord 搜索UI计划执行记录下的设备记录
func (l *SearchUIDeviceRecordLogic) SearchUIDeviceRecord(in *pb.SearchUIDeviceRecordReq) (
	out *pb.SearchUIDeviceRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	req := model.SearchUIDeviceExecutionRecordReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  projectID,
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		TaskID:    taskID,
		ExecuteID: executeID,
	}

	count, err := l.svcCtx.UICaseExecutionRecordModel.FindCountUIDeviceExecutionRecords(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count ui device execution records, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
			taskID, executeID, projectID, err,
		)
	}

	records, err := l.svcCtx.UICaseExecutionRecordModel.FindUIDeviceExecutionRecords(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui device execution records, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
			taskID, executeID, projectID, err,
		)
	}

	out = &pb.SearchUIDeviceRecordResp{
		CurrentPage: 1,
		PageSize:    uint64(count),
		TotalCount:  uint64(count),
		TotalPage:   1,
		Items:       make([]*pb.UIDeviceRecord, 0, len(records)),
	}
	for _, record := range records {
		item := &pb.UIDeviceRecord{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy ui device execution record to response, record: %s, error: %+v",
				jsonx.MarshalIgnoreError(record), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
