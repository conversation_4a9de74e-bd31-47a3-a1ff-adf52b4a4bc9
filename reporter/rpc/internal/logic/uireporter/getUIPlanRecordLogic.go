package uireporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const keyOfMetaData = "meta_data"

type GetUIPlanRecordLogic struct {
	*BaseLogic
}

func NewGetUIPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIPlanRecordLogic {
	return &GetUIPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetUIPlanRecord 获取UI计划执行记录
func (l *GetUIPlanRecordLogic) GetUIPlanRecord(in *pb.GetUIPlanRecordReq) (out *pb.GetUIPlanRecordResp, err error) {
	record, err := l.svcCtx.UIPlanExecutionRecordModel.FindOneByTaskIDExecuteIDProjectID(
		l.ctx, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find ui plan execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the ui plan execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
					in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
				),
			)
		}
	}

	return &pb.GetUIPlanRecordResp{
		Record: l.convert(record),
	}, nil
}

func (l *GetUIPlanRecordLogic) convert(in *model.UiPlanExecutionRecord) (out *pb.UIPlanRecord) {
	triggerMode, _ := utils.StringConvertToPBEnum(in.TriggerMode, commonpb.TriggerMode_NULL)
	priorityType, _ := utils.NumberConvertToPBEnum(in.PriorityType, commonpb.PriorityType_Default)

	out = &pb.UIPlanRecord{}

	defer func() {
		if out == nil {
			return
		}

		out.TaskId = in.TaskId
		out.ExecuteId = in.ExecuteId
		out.ProjectId = in.ProjectId
		out.PlanId = in.PlanId
		out.PlanName = in.PlanName
		out.Type = triggerMode.(commonpb.TriggerMode)
		out.PriorityType = priorityType.(commonpb.PriorityType)

		out.Status = in.Status.String
		out.TotalSuite = in.TotalSuite
		out.SuccessSuite = in.SuccessSuite
		out.FailureSuite = in.TotalSuite - in.SuccessSuite
		out.TotalCase = in.TotalCase
		out.SuccessCase = in.SuccessCase
		out.FailureCase = in.TotalCase - in.SuccessCase
		out.CostTime = in.CostTime
		out.StartedAt = in.StartedAt
		out.EndedAt = in.EndedAt.Int64
		out.ExecutedBy = in.ExecutedBy
	}()

	if in.ExecuteData.Valid && in.ExecuteData.String != "" {
		var m map[string]any
		if err := jsonx.UnmarshalFromString(in.ExecuteData.String, &m); err != nil {
			l.Errorf(
				"failed to unmarshal the `execute_data` of ui plan execution record to map, task_id: %s, project_id: %s, execute_id: %s, error: %+v",
				in.TaskId, in.ProjectId, in.ExecuteId, err,
			)

			return out
		}

		v, ok := m[keyOfMetaData]
		if !ok {
			l.Warnf(
				"the `execute_data` of ui plan execution record doesn't contain the key `%s`, task_id: %s, project_id: %s, execute_id: %s",
				keyOfMetaData, in.TaskId, in.ProjectId, in.ExecuteId,
			)

			return out
		}

		m1, ok := v.(map[string]any)
		if !ok {
			l.Warnf(
				"the value of the key `%s` of the `execute_data` of ui plan execution record is not a map, task_id: %s, project_id: %s, execute_id: %s",
				keyOfMetaData, in.TaskId, in.ProjectId, in.ExecuteId,
			)

			return out
		}

		for key, value := range m1 {
			m[key] = value
		}
		delete(m, keyOfMetaData)

		if err := protobuf.UnmarshalJSON(jsonx.MarshalIgnoreError(m), out); err != nil {
			l.Errorf(
				"failed to unmarshal the `execute_data` of ui plan execution record to %T, task_id: %s, project_id: %s, execute_id: %s, error: %+v",
				out, in.TaskId, in.ProjectId, in.ExecuteId, err,
			)

			return out
		}
	}

	return out
}
