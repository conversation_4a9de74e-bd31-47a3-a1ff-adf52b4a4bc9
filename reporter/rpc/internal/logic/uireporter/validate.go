package uireporterlogic

import (
	"fmt"
	"strings"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

func CheckNotNullUICase(in *pb.PutUICaseRecordRequest, tp operationType) error {
	var nullParams []string

	if in.TaskId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyTaskID)
	}
	if in.ProjectId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyProjectID)
	}
	if in.CaseId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyCaseID)
	}

	if tp == modify {
		if in.ExecuteId == "" {
			nullParams = append(nullParams, common.ReqFieldKeyExecuteID)
		}
	}

	if len(nullParams) != 0 {
		nullParamStr := strings.Join(nullParams, "、")
		err := fmt.Sprintf("以下参数不可为空：%s", nullParamStr)
		return errorx.Err(errorx.ValidateParamError, fmt.Sprintf("failed to create record, error: %+v", err))
	}

	return nil
}

func CheckNotNullUISuite(in *pb.PutUISuiteRecordRequest, tp operationType) error {
	var nullParams []string

	if in.TaskId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyTaskID)
	}
	if in.ProjectId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyProjectID)
	}
	if in.SuiteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeySuiteID)
	}
	if in.PlanExecuteId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyPlanExecuteID)
	}

	if tp == modify {
		if in.ExecuteId == "" {
			nullParams = append(nullParams, common.ReqFieldKeyExecuteID)
		}
	}

	if len(nullParams) != 0 {
		nullParamStr := strings.Join(nullParams, "、")
		err := fmt.Sprintf("以下参数不可为空：%s", nullParamStr)
		return errorx.Err(errorx.ValidateParamError, fmt.Sprintf("failed to create record, error: %+v", err))
	}

	return nil
}

func CheckNotNullUIPlan(in *pb.PutUIPlanRecordRequest, tp operationType) error {
	var nullParams []string

	if in.TaskId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyTaskID)
	}
	if in.ProjectId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyProjectID)
	}
	if in.PlanId == "" {
		nullParams = append(nullParams, common.ReqFieldKeyPlanID)
	}

	if tp == modify {
		if in.ExecuteId == "" {
			nullParams = append(nullParams, common.ReqFieldKeyExecuteID)
		}
	}

	if len(nullParams) != 0 {
		nullParamStr := strings.Join(nullParams, "、")
		err := fmt.Sprintf("以下参数不可为空：%s", nullParamStr)
		return errorx.Err(errorx.ValidateParamError, fmt.Sprintf("failed to create record, error: %+v", err))
	}

	return nil
}
