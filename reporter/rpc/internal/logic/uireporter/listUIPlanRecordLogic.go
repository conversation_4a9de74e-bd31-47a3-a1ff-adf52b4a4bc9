package uireporterlogic

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListUIPlanRecordLogic struct {
	*BaseLogic
}

func NewListUIPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListUIPlanRecordLogic {
	return &ListUIPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ListUIPlanRecord UI计划执行记录列表
func (l *ListUIPlanRecordLogic) ListUIPlanRecord(in *pb.ListUIPlanRecordRequest) (
	resp *pb.ListUIPlanRecordResponse, err error,
) {
	var (
		projectID  = in.GetProjectId()
		planID     = in.GetPlanId()
		pagination = in.GetPagination()
		sort       = in.GetSort()
	)

	countBuilder := l.svcCtx.UIPlanExecutionRecordModel.SelectCountBuilder().Where(
		"`project_id` = ? AND `plan_id` = ?", projectID, planID,
	)
	count, err := l.svcCtx.UIPlanExecutionRecordModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		msg := fmt.Sprintf(
			"faile to count ui_plan_execution_record with project_id[%s]、plan_id[%s], error: %+v",
			projectID, planID, err,
		)
		return nil, errorx.Err(errorx.DBError, msg)
	}
	resp = &pb.ListUIPlanRecordResponse{
		TotalCount: uint64(count),
		TotalPage:  1,
	}

	selectBuilder := l.svcCtx.UIPlanExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `plan_id` = ?", projectID, planID,
	)

	if len(sort) == 0 {
		sort = append(
			sort, &rpc.SortField{
				Field: "`created_at`",
				Order: constants.DESC,
			},
		)
	}

	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithPagination(l.svcCtx.UIPlanExecutionRecordModel, pagination),
		sqlbuilder.WithSort(l.svcCtx.UIPlanExecutionRecordModel, sort),
	)
	planRecords, err := l.svcCtx.UIPlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errorx.Errorf(
			errorx.DBError, "failed to find plan record with project_id[%s]、plan_id[%s], error: %+v",
			projectID, planID, err,
		)
	}

	// 避免返回null给调用者
	resp.Items = make([]*pb.ListUIPlanRecordResponse_PlanRecord, 0, len(planRecords))
	for _, planRecord := range planRecords {
		var reportViewUrl string
		var reportDownloadUrl string
		if planRecord.Content.String != "" {
			var contentMap map[string]any
			if err = jsonx.UnmarshalFromString(planRecord.Content.String, &contentMap); err == nil {
				reportViewUrl_ := contentMap["report_view_url"]
				if reportViewUrl_ != nil {
					reportViewUrl = reportViewUrl_.(string)
				}
				reportDownloadUrl_ := contentMap["report_download_url"]
				if reportDownloadUrl_ != nil {
					reportDownloadUrl = reportDownloadUrl_.(string)
				}
			} else {
				l.Errorf(
					"invalid ui plan record content, task_id: %s, content: %s, err: %+v",
					planRecord.TaskId, planRecord.Content.String, err,
				)
			}
		}

		var totalCase, finishedCase, successCase int64
		if planRecord.Finished == int64(constants.Finished) {
			totalCase, finishedCase, successCase = planRecord.TotalCase, planRecord.FinishedCase, planRecord.SuccessCase
		} else {
			totalCase, finishedCase, successCase, err = l.getPlanCaseInfoByCalculate(
				planRecord.ProjectId, planRecord.TaskId, planRecord.ExecuteId,
			)
			if err != nil {
				return nil, err
			}
		}

		/*【UI计划 -> 执行记录，任务结束了，排队耗时一直在增长】https://www.tapd.cn/66310153/bugtrace/bugs/view?bug_id=1166310153001125644*/
		if planRecord.WaitTime == 0 {
			if planRecord.ExecutedResult == int64(commonpb.ExecutedResult_TER_INIT) {
				if planRecord.StartedAt <= 0 {
					planRecord.WaitTime = time.Now().UnixMilli() - planRecord.CreatedAt.UnixMilli()
				} else {
					planRecord.WaitTime = time.Now().UnixMilli() - planRecord.StartedAt
				}
			}
		}
		if planRecord.CostTime == 0 {
			if planRecord.EndedAt.Int64 == 0 {
				planRecord.CostTime = time.Now().UnixMilli() - planRecord.CreatedAt.UnixMilli()
			} else {
				planRecord.CostTime = planRecord.EndedAt.Int64 - planRecord.CreatedAt.UnixMilli()
			}
		}

		item := &pb.ListUIPlanRecordResponse_PlanRecord{
			ProjectId:         planRecord.ProjectId,
			PlanId:            planRecord.PlanId,
			PlanName:          planRecord.PlanName,
			TaskId:            planRecord.TaskId,
			ExecuteId:         planRecord.ExecuteId,
			Status:            planRecord.Status.String,
			CostTime:          planRecord.CostTime,
			ExecutedBy:        planRecord.ExecutedBy,
			StartedAt:         planRecord.StartedAt,
			EndedAt:           planRecord.EndedAt.Int64,
			TotalSuite:        planRecord.TotalSuite,
			FinishedSuite:     planRecord.FinishedSuite,
			SuccessSuite:      planRecord.SuccessSuite,
			TotalCase:         totalCase,
			FinishedCase:      finishedCase,
			SuccessCase:       successCase,
			Content:           planRecord.Content.String,
			ReportViewUrl:     reportViewUrl,
			ReportDownloadUrl: reportDownloadUrl,
			Finished:          planRecord.Finished,
			Cleaned:           planRecord.Cleaned,
			TriggerMode:       planRecord.TriggerMode,
			Type:              planRecord.TriggerMode,
			ExecuteStatus:     commonpb.ExecuteStatus(planRecord.ExecuteStatus),
			PriorityType:      commonpb.PriorityType(planRecord.PriorityType),
			ExecutedResult:    commonpb.ExecutedResult(planRecord.ExecutedResult),
			WaitTime:          planRecord.WaitTime,
			UpdateAt:          planRecord.UpdateAt.Int64,
		}
		resp.Items = append(resp.Items, item)
	}

	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
