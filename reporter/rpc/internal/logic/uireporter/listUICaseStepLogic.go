package uireporterlogic

import (
	"context"
	"sort"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListUICaseStepLogic struct {
	*BaseLogic
}

func NewListUICaseStepLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListUICaseStepLogic {
	return &ListUICaseStepLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ListUICaseStep 获取UI用例执行步骤列表
func (l *ListUICaseStepLogic) ListUICaseStep(in *pb.ListUICaseStepReq) (out *pb.ListUICaseStepResp, err error) {
	records, err := l.svcCtx.UICaseExecutionStepModel.FindByTaskIDExecuteIDProjectID(
		l.ctx, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui case execution step records, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
			in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), err,
		)
	}

	out = &pb.ListUICaseStepResp{
		TotalCount: uint64(len(records)),
		Items:      make([]*pb.UICaseStep, 0, len(records)),
	}
	if err = mr.MapReduceVoid[*model.UiCaseExecutionStep, *pb.UICaseStep](
		func(source chan<- *model.UiCaseExecutionStep) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		}, func(item *model.UiCaseExecutionStep, writer mr.Writer[*pb.UICaseStep], cancel func(error)) {
			step := &pb.UICaseStep{}
			if err := utils.Copy(step, item, l.converters...); err != nil {
				l.Errorf("failed to copy ui case execution step record to %T, record: %+v, error: %+v", step, item, err)
				return
			}

			if in.GetWithContent() {
				rs, err := l.svcCtx.UICaseExecutionStepContentModel.FindByTaskIDStepID(l.ctx, item.TaskId, item.StepId)
				if err != nil {
					l.Errorf(
						"failed to find ui case execution step record contents, step_id: %s, error: %+v", item.StepId,
						err,
					)
				} else {
					var b strings.Builder
					for _, r := range rs {
						if r.Content.Valid && r.Content.String != "" {
							b.WriteString(r.Content.String)
						}
					}

					step.Content = b.String()
				}
			}

			writer.Write(step)
		}, func(pipe <-chan *pb.UICaseStep, cancel func(error)) {
			for step := range pipe {
				out.Items = append(out.Items, step)
			}

			sort.SliceStable(
				out.Items, func(i, j int) bool {
					if out.Items[i].GetStage() != out.Items[j].GetStage() {
						return out.Items[i].GetStage() < out.Items[j].GetStage()
					}

					return out.Items[i].GetIndex() < out.Items[j].GetIndex()
				},
			)
		}, mr.WithContext(l.ctx), mr.WithWorkers(len(records)),
	); err != nil {
		return nil, err
	}

	out.TotalCount = uint64(len(out.Items))
	return out, nil
}
