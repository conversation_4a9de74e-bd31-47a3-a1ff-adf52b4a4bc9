package uireporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchUICaseRecordLogic struct {
	*BaseLogic
}

func NewSearchUICaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUICaseRecordLogic {
	return &SearchUICaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchUICaseRecord 搜索UI集合执行记录下的UI用例执行记录
func (l *SearchUICaseRecordLogic) SearchUICaseRecord(in *pb.SearchUICaseRecordReq) (
	out *pb.SearchUICaseRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
		udid      = in.GetUdid()
	)

	req := model.SearchUICaseExecutionRecordReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  projectID,
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		TaskID:    taskID,
		ExecuteID: executeID,
		UDID:      udid,
	}

	count, err := l.svcCtx.UICaseExecutionRecordModel.FindCountUICaseExecutionRecords(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count ui case execution records, task_id: %s, execute_id: %s, project_id: %s, udid: %s, error: %+v",
			taskID, executeID, projectID, udid, err,
		)
	}

	records, err := l.svcCtx.UICaseExecutionRecordModel.FindUICaseExecutionRecords(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui case execution records, task_id: %s, execute_id: %s, project_id: %s, udid: %s, error: %+v",
			taskID, executeID, projectID, udid, err,
		)
	}

	out = &pb.SearchUICaseRecordResp{
		CurrentPage: 1,
		PageSize:    uint64(count),
		TotalCount:  uint64(count),
		TotalPage:   1,
		Items:       make([]*pb.UICaseRecord, 0, len(records)),
	}
	for _, record := range records {
		item := &pb.UICaseRecord{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy ui case execution record to response, record: %s, error: %+v",
				jsonx.MarshalIgnoreError(record), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
