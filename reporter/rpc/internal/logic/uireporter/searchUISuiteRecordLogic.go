package uireporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchUISuiteRecordLogic struct {
	*BaseLogic
}

func NewSearchUISuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUISuiteRecordLogic {
	return &SearchUISuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchUISuiteRecord 搜索UI计划执行记录下的UI集合执行记录
func (l *SearchUISuiteRecordLogic) SearchUISuiteRecord(in *pb.SearchUISuiteRecordReq) (
	out *pb.SearchUISuiteRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
		udid      = in.GetUdid()
	)

	req := model.SearchUISuiteExecutionRecordReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  projectID,
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		TaskID:    taskID,
		ExecuteID: executeID,
		UDID:      udid,
	}

	count, err := l.svcCtx.UISuiteExecutionRecordModel.FindCountUISuiteExecutionRecords(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count ui suite execution records, task_id: %s, execute_id: %s, project_id: %s, udid: %s, error: %+v",
			taskID, executeID, projectID, udid, err,
		)
	}

	records, err := l.svcCtx.UISuiteExecutionRecordModel.FindUISuiteExecutionRecords(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui suite execution records, task_id: %s, execute_id: %s, project_id: %s, udid: %s,error: %+v",
			taskID, executeID, projectID, udid, err,
		)
	}

	out = &pb.SearchUISuiteRecordResp{
		CurrentPage: 1,
		PageSize:    uint64(count),
		TotalCount:  uint64(count),
		TotalPage:   1,
		Items:       make([]*pb.UISuiteRecord, 0, len(records)),
	}
	for _, record := range records {
		item := &pb.UISuiteRecord{Udid: udid}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy ui suite execution record to response, record: %s, error: %+v",
				jsonx.MarshalIgnoreError(record), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
