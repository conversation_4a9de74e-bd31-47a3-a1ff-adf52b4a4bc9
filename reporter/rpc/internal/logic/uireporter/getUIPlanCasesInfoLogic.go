package uireporterlogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUIPlanCasesInfoLogic struct {
	*BaseLogic
}

func NewGetUIPlanCasesInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIPlanCasesInfoLogic {
	return &GetUIPlanCasesInfoLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetUIPlanCasesInfo 获取UI计划关联用例信息
func (l *GetUIPlanCasesInfoLogic) GetUIPlanCasesInfo(in *pb.GetUIPlanCasesInfoRequest) (resp *pb.GetUIPlanCasesInfoResponse, err error) {
	totalCase, finishedCase, successCase, err := l.getPlanCaseInfoByCalculate(in.ProjectId, in.TaskId, in.ExecuteId)
	if err != nil {
		return nil, err
	}
	return &pb.GetUIPlanCasesInfoResponse{TotalCase: totalCase, FinishedCase: finishedCase, SuccessCase: successCase}, nil
}
