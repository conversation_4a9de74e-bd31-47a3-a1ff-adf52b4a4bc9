package stabilityreporterlogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchStabilityDeviceRecordLogic struct {
	*BaseLogic
}

func NewSearchStabilityDeviceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchStabilityDeviceRecordLogic {
	return &SearchStabilityDeviceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchStabilityDeviceRecord 搜索稳测执行报告的总览设备
func (l *SearchStabilityDeviceRecordLogic) SearchStabilityDeviceRecord(in *pb.SearchStabilityDeviceRecordReq) (
	out *pb.SearchStabilityDeviceRecordResp, err error,
) {
	out = &pb.SearchStabilityDeviceRecordResp{}

	selectBuilder, countBuilder := l.svcCtx.StabilityDeviceExecutionRecordModel.GenerateSearchStabilityDeviceExecutionRecordSqlBuilder(
		model.SearchStabilityDeviceExecutionRecordReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID:  in.GetProjectId(),
				Condition:  in.GetCondition(),
				Pagination: in.GetPagination(),
				Sort:       rpc.ConvertSortFields(in.GetSort()),
			},
			TaskID:    in.GetTaskId(),
			ExecuteID: in.GetExecuteId(),
		},
	)

	count, err := l.svcCtx.StabilityDeviceExecutionRecordModel.FindCountStabilityDeviceExecutionRecords(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count stability device execution records, task_id: %s, plan_execute_id: %s, project_id: %s, error: %+v",
			in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	records, err := l.svcCtx.StabilityDeviceExecutionRecordModel.FindStabilityDeviceExecutionRecords(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find stability device execution records, task_id: %s, plan_execute_id: %s, project_id: %s, error: %+v",
			in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.StabilityDeviceRecordItem, 0, len(records))
	for _, record := range records {
		item := &pb.StabilityDeviceRecordItem{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy stability device execution record to response, record: %+v, error: %+v",
				record, err,
			)
		}

		if record.Device.Valid {
			var device devicehubpb.Device
			if err = protobuf.UnmarshalJSONFromString(record.Device.String, &device); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to unmarshal stability device info, device_info: %s, error: %+v",
					record.Device.String, err,
				)
			}
			if err = utils.Copy(item.Device, &device, l.converters...); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy stability device info to response, device_info: %+v, error: %+v",
					&device, err,
				)
			}
		}

		if record.Result.Valid {
			var result commonpb.StabilityResult
			if err = protobuf.UnmarshalJSONFromString(record.Result.String, &result); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to unmarshal stability device result, result: %s, error: %+v",
					record.Result.String, err,
				)
			}
			item.Artifacts = make([]string, 0, len(result.Artifacts))
			for _, artifact := range result.Artifacts {
				item.Artifacts = append(item.Artifacts, artifact.FileName)
			}
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = uint64(math.Ceil(float64(out.TotalCount) / float64(out.PageSize)))
	}

	return out, nil
}
