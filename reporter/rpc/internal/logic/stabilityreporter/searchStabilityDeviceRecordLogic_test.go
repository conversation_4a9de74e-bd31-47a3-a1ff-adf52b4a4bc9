package stabilityreporterlogic_test

import (
	"testing"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

func TestSearchStabilityDeviceRecordLogic(t *testing.T) {
	device1 := devicehubpb.Device{
		Udid:          "PXUYD22628002359",
		Name:          "CTR-AL00",
		Type:          commonpb.DeviceType_REAL_PHONE,
		Platform:      commonpb.PlatformType_ANDROID,
		Brand:         "HUAWEI",
		Model:         "CTR-AL00",
		Version:       "12",
		Serial:        "PXUYD22628002359",
		Provider:      "http://*************:3500",
		ProviderType:  devicehubpb.ProviderType_ATX,
		RemoteAddress: "*************:20321",
		State:         devicehubpb.DeviceState_IDLE,
		StartedAt:     **********,
		Duration:      666666,
		CreatedBy:     "probe-system",
		UpdatedBy:     "probe-system",
		CreatedAt:     **********,
		UpdatedAt:     **********,
	}

	device2 := devicehubpb.Device{
		Udid:          "LZYTYLZT9HFI6DLN",
		Name:          "PJG110",
		Type:          commonpb.DeviceType_REAL_PHONE,
		Platform:      commonpb.PlatformType_ANDROID,
		Brand:         "OPPO",
		Model:         "PJG110",
		Version:       "13",
		Serial:        "LZYTYLZT9HFI6DLN",
		Provider:      "http://************:3500",
		ProviderType:  devicehubpb.ProviderType_ATX,
		RemoteAddress: "************:20023",
		State:         devicehubpb.DeviceState_IDLE,
		StartedAt:     **********,
		Duration:      777777,
		CreatedBy:     "probe-system",
		UpdatedBy:     "probe-system",
		CreatedAt:     **********,
		UpdatedAt:     **********,
	}

	s, err := protobuf.MarshalJSONToString(&device1)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(s)

	s, err = protobuf.MarshalJSONToString(&device2)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(s)
}

func TestSearchStabilityDeviceRecordResult(t *testing.T) {
	result := commonpb.StabilityResult{
		Artifacts: []*commonpb.Artifact{
			{
				Type:        commonpb.ArtifactType_ArtifactType_LOG,
				FileName:    "crash-dump.log",
				FilePath:    "https://testing-dev-quality.ttyuyin.com/reports/crash-dump.log",
				Description: "filenameOfCrashDumpLog",
			},
			{
				Type:        commonpb.ArtifactType_ArtifactType_LOG,
				FileName:    "oom-traces.log",
				FilePath:    "https://testing-dev-quality.ttyuyin.com/reports/oom-traces.log",
				Description: "filenameOfOOMTracesLog",
			},
		},
	}
	s, err := protobuf.MarshalJSONToString(&result)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(s)
}
