package stabilityreporterlogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyStabilityPlanRecordLogic struct {
	*BaseLogic
}

func NewModifyStabilityPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyStabilityPlanRecordLogic {
	return &ModifyStabilityPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyStabilityPlanRecord 修改稳测执行记录
func (l *ModifyStabilityPlanRecordLogic) ModifyStabilityPlanRecord(in *pb.PutStabilityPlanRecordReq) (out *pb.ModifyStabilityPlanRecordResp, err error) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()

		status = in.GetStatus()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockStabilityPlanRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)

	fn := func() error {
		origin, err := l.svcCtx.StabilityPlanExecutionRecordModel.FindOneByTaskIDExecuteIDProjectID(
			l.ctx, taskID, executeID, projectID,
		)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return errorx.Errorf(
					errorx.DBError,
					"failed to find stability plan execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
					taskID, executeID, projectID, err,
				)
			} else {
				return errorx.Errorf(
					errorx.NotExists,
					"the stability plan execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
					taskID, executeID, projectID,
				)
			}
		}

		record := l.ConvertStabilityPlanModel(in, origin)
		if status != dispatcherpb.ComponentState_Started.String() {
			executeData, err := l.GeneratePlanRecordExecuteData(l.ctx, record, in.GetAppInfo())
			if err != nil {
				l.Errorf(
					"failed to generate stability plan execution record execute data, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
					taskID, executeID, projectID, err,
				)
			} else {
				record.ExecuteData = sql.NullString{
					String: executeData,
					Valid:  true,
				}
			}

			selectBuilder, _ := l.svcCtx.StabilityDeviceExecutionRecordModel.GenerateSearchStabilityDeviceExecutionRecordSqlBuilder(
				model.SearchStabilityDeviceExecutionRecordReq{
					BaseSearchReq: model.BaseSearchReq{
						ProjectID: projectID,
					},
					TaskID:    taskID,
					ExecuteID: executeID,
				},
			)
			deviceRecords, err := l.svcCtx.StabilityDeviceExecutionRecordModel.FindStabilityDeviceExecutionRecords(l.ctx, selectBuilder)
			if err != nil {
				return errorx.Errorf(
					errorx.DBError,
					"failed to find stability device execution records, task_id: %s, plan_execute_id: %s, project_id: %s, error: %+v",
					taskID, executeID, projectID, err,
				)
			}

			var successDevice, failureDevice int64
			for _, deviceRecord := range deviceRecords {
				switch deviceRecord.Status.String {
				case dispatcherpb.ComponentState_Success.String():
					successDevice++
				case dispatcherpb.ComponentState_Failure.String(), dispatcherpb.ComponentState_Panic.String():
					failureDevice++
				}
			}
			record.SuccessDevice = successDevice
			record.FailureDevice = failureDevice

			if status == dispatcherpb.ComponentState_Success.String() {
				if failureDevice > 0 {
					record.Status = sql.NullString{
						String: dispatcherpb.ComponentState_Failure.String(),
						Valid:  true,
					}
				} else if successDevice == 0 {
					record.Status = sql.NullString{
						String: dispatcherpb.ComponentState_Skip.String(),
						Valid:  true,
					}
				}
			}
		}

		return l.svcCtx.StabilityPlanExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.StabilityPlanExecutionRecordModel.Update(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify stability plan execution record, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
						taskID, executeID, projectID, record.PlanId, err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}
	return &pb.ModifyStabilityPlanRecordResp{}, nil
}
