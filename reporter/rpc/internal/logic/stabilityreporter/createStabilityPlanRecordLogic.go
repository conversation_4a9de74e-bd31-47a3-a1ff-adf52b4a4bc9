package stabilityreporterlogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateStabilityPlanRecordLogic struct {
	*BaseLogic
}

func NewCreateStabilityPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateStabilityPlanRecordLogic {
	return &CreateStabilityPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateStabilityPlanRecord 创建稳测的执行记录
func (l *CreateStabilityPlanRecordLogic) CreateStabilityPlanRecord(in *pb.PutStabilityPlanRecordReq) (out *pb.CreateStabilityPlanRecordResp, err error) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockStabilityPlanRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)

	fn := func() error {
		_, err := l.svcCtx.StabilityPlanExecutionRecordModel.FindOneByTaskIDExecuteIDProjectID(
			l.ctx, taskID, executeID, projectID,
		)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return err
			}
		} else {
			return errorx.Errorf(
				errorx.AlreadyExists,
				"the stability plan execution record already exists, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}

		record := l.ConvertStabilityPlanModel(in, nil)
		return l.svcCtx.StabilityPlanExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.StabilityPlanExecutionRecordModel.Insert(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to create stability plan execution record, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
						taskID, executeID, projectID, record.PlanId, err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.CreateStabilityPlanRecordResp{}, nil
}
