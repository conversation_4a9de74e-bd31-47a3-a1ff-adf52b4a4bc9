package stabilityreporterlogic

import (
	"context"
	"sort"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListStabilityDeviceStepLogic struct {
	*BaseLogic
}

func NewListStabilityDeviceStepLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListStabilityDeviceStepLogic {
	return &ListStabilityDeviceStepLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ListStabilityDeviceStep 获取稳测执行报告的设备步骤日志
func (l *ListStabilityDeviceStepLogic) ListStabilityDeviceStep(in *pb.ListStabilityDeviceStepReq) (out *pb.ListStabilityDeviceStepResp, err error) {
	steps, err := l.svcCtx.StabilityDeviceExecutionStepModel.FindByProjectIDTaskIDUdid(
		l.ctx, in.GetProjectId(), in.GetTaskId(), in.GetUdid(),
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find stability device steps, project_id: %s, task_id: %s, udid: %s, error: %+v",
			in.GetProjectId(), in.GetTaskId(), in.GetUdid(), err,
		)
	}

	out = &pb.ListStabilityDeviceStepResp{
		TotalCount: uint64(len(steps)),
		Items:      make([]*pb.StabilityDeviceStep, 0, len(steps)),
	}
	if err = mr.MapReduceVoid[*model.StabilityDeviceExecutionStep, *pb.StabilityDeviceStep](
		func(source chan<- *model.StabilityDeviceExecutionStep) {
			for _, step := range steps {
				if step == nil {
					continue
				}

				source <- step
			}
		}, func(item *model.StabilityDeviceExecutionStep, writer mr.Writer[*pb.StabilityDeviceStep], cancel func(error)) {
			step := &pb.StabilityDeviceStep{}
			if err := utils.Copy(step, item, l.converters...); err != nil {
				l.Errorf("failed to copy stability device execution step to %T, record: %+v, error: %+v", step, item, err)
				return
			}

			if item.Artifacts.Valid {
				var artifacts []*commonpb.Artifact
				if err := protobuf.UnmarshalJSONWithMessagesFromString(item.Artifacts.String, &artifacts); err != nil {
					l.Errorf("failed to unmarshal artifacts, record: %+v, error: %+v", item, err)
				} else {
					step.ShotCount = int64(len(artifacts))
				}
			}

			if in.GetWithContent() {
				rs, err := l.svcCtx.StabilityDeviceExecutionStepContentModel.FindByTaskIDStepID(l.ctx, item.TaskId, item.StepId)
				if err != nil {
					l.Errorf(
						"failed to find stability device execution step contents, step_id: %s, error: %+v", item.StepId,
						err,
					)
				} else {
					var b strings.Builder
					for _, r := range rs {
						if r.Content.Valid && r.Content.String != "" {
							b.WriteString(r.Content.String)
						}
					}

					step.Content = b.String()
				}
			}

			writer.Write(step)
		}, func(pipe <-chan *pb.StabilityDeviceStep, cancel func(error)) {
			for step := range pipe {
				out.Items = append(out.Items, step)
			}

			sort.SliceStable(
				out.Items, func(i, j int) bool {
					if out.Items[i].GetStage() != out.Items[j].GetStage() {
						return out.Items[i].GetStage() < out.Items[j].GetStage()
					}

					return out.Items[i].GetIndex() < out.Items[j].GetIndex()
				},
			)
		}, mr.WithContext(l.ctx), mr.WithWorkers(len(steps)),
	); err != nil {
		return nil, err
	}

	out.TotalCount = uint64(len(out.Items))
	return out, nil
}
