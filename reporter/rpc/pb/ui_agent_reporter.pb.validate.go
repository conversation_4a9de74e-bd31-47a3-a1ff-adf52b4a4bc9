// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: reporter/ui_agent_reporter.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on UIAgentComponentRecordItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponentRecordItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponentRecordItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentRecordItemMultiError, or nil if none found.
func (m *UIAgentComponentRecordItem) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponentRecordItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for ParentExecuteId

	// no validation rules for ProjectId

	// no validation rules for ComponentId

	// no validation rules for ComponentName

	// no validation rules for TriggerMode

	// no validation rules for ExecuteType

	if all {
		switch v := interface{}(m.GetApplicationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentRecordItemValidationError{
					field:  "ApplicationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentRecordItemValidationError{
					field:  "ApplicationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentRecordItemValidationError{
				field:  "ApplicationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StepByStep

	for idx, item := range m.GetSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentRecordItemValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentRecordItemValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentRecordItemValidationError{
					field:  fmt.Sprintf("Steps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExpectation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentRecordItemValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentRecordItemValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentRecordItemValidationError{
				field:  "Expectation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVariables() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentRecordItemValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentRecordItemValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentRecordItemValidationError{
					field:  fmt.Sprintf("Variables[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentRecordItemValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentRecordItemValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentRecordItemValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reinstall

	// no validation rules for Restart

	// no validation rules for Status

	// no validation rules for ExecutedBy

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	// no validation rules for CostTime

	if all {
		switch v := interface{}(m.GetErrMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentRecordItemValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentRecordItemValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentRecordItemValidationError{
				field:  "ErrMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Cleaned

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return UIAgentComponentRecordItemMultiError(errors)
	}

	return nil
}

// UIAgentComponentRecordItemMultiError is an error wrapping multiple
// validation errors returned by UIAgentComponentRecordItem.ValidateAll() if
// the designated constraints aren't met.
type UIAgentComponentRecordItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentRecordItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentRecordItemMultiError) AllErrors() []error { return m }

// UIAgentComponentRecordItemValidationError is the validation error returned
// by UIAgentComponentRecordItem.Validate if the designated constraints aren't met.
type UIAgentComponentRecordItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentRecordItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentRecordItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentRecordItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentRecordItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentRecordItemValidationError) ErrorName() string {
	return "UIAgentComponentRecordItemValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentComponentRecordItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponentRecordItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentRecordItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentRecordItemValidationError{}
