package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"
)

const (
	rootCmdUse   = "relation"
	rootCmdShort = "Relation is one of the microservices of the Quality Platform"
	rootCmdLong  = `Relation is one of the microservices of the Quality Platform`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
