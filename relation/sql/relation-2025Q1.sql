-- 接口覆盖率统计
CREATE TABLE IF NOT EXISTS `service_team_relation`
(
    `id`                INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `service`           VARCHAR(64)  NOT NULL COMMENT '服务名称',
    `namespace`         VARCHAR(64)  NOT NULL COMMENT '命名空间',
    `team_name`         VARCHAR(64)  NOT NULL COMMENT '团队名称',
    `team_description`  VARCHAR(255) NULL COMMENT '团队描述',
    `deleted`           TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`        VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`        VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`        VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_str_service_namespace_team_name` (`service`, `namespace`, `team_name`),
    KEY `ix_str_service_namespace_deleted` (`service`, `namespace`, `deleted`),
    KEY `ix_str_team_name_deleted` (`team_name`, `deleted`),
    KEY `ix_str_updated_at` (`updated_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='服务与团队关系表';

-- 添加查询天相的请求路径
ALTER TABLE `service_method_relation` ADD `query_path` VARCHAR(512) NULL COMMENT '请求路径（查询天相）' AFTER `req_path`;