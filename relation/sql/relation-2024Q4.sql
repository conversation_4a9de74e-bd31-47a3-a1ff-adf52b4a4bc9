-- 常态化压测
CREATE TABLE IF NOT EXISTS `service_method_relation`
(
    `id`         INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `service`    VARCHAR(64)  NOT NULL COMMENT '服务名称',
    `namespace`  VARCHAR(64)  NOT NULL COMMENT '命名空间',
    `cmd`        INT          NOT NULL COMMENT '命令号',
    `method`     VARCHAR(512) NOT NULL COMMENT '接口名称',
    `grpc_path`  VARCHAR(512) NOT NULL COMMENT 'grpc路径',
    `deprecated` TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已弃用（未弃用、已弃用）',
    `deleted`    TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_smr_service_namespace_method` (`service`, `namespace`, `method`),
    KEY `ix_smr_service_deleted` (`service`, `deleted`),
    KEY `ix_smr_namespace_deleted` (`namespace`, `deleted`),
    KEY `ix_smr_cmd_deleted` (`cmd`, `deleted`),
    KEY `ix_smr_method_deleted` (`method`, `deleted`),
    KEY `ix_smr_grpc_path_deleted` (`grpc_path`, `deleted`),
    KEY `ix_smr_updated_at` (`updated_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='服务与接口关系表';


-- 优化绑定服务与测试用例关系
CREATE TABLE IF NOT EXISTS `service_case_relation_v2`
(
    `id`                INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`        VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `general_config_id` VARCHAR(64)  NOT NULL COMMENT '通用配置ID',
    `service`           VARCHAR(64)  NOT NULL COMMENT '服务名称',
    `namespace`         VARCHAR(64)  NOT NULL COMMENT '命名空间',
    `cmd`               INT          NOT NULL COMMENT '命令号',
    `method`            VARCHAR(512) NOT NULL COMMENT '接口名称',
    `grpc_path`         VARCHAR(512) NOT NULL COMMENT 'grpc路径',
    `to_service`        VARCHAR(64)  NOT NULL COMMENT '经过的服务名称',
    `document_id`       VARCHAR(64)  NULL COMMENT '接口文档ID',
    `case_id`           VARCHAR(64)  NOT NULL COMMENT '用例ID',
    `deleted`           TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`        VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`        VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`        VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_scr_grpc_path_case_id` (`project_id`, `general_config_id`, `grpc_path`, `case_id`),
    KEY `ix_scr_service` (`project_id`, `general_config_id`, `service`),
    KEY `ix_scr_to_service` (`project_id`, `general_config_id`, `to_service`),
    KEY `ix_scr_case_id` (`project_id`, `general_config_id`, `case_id`),
    KEY `ix_scr_updated_at` (`updated_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='服务与用例关系表';


-- 支持`TT`的`HTTP`服务的精准测试
ALTER TABLE `service_method_relation` CHANGE `grpc_path` `req_path` VARCHAR(512) NOT NULL COMMENT '请求路径（gRPC路径、HTTP路径）';
ALTER TABLE `service_method_relation` MODIFY `cmd` INT NULL COMMENT '命令号' AFTER `req_path`;
DROP INDEX `ix_smr_grpc_path_deleted` ON `service_method_relation`;
CREATE INDEX `ix_smr_req_path_deleted` ON `service_method_relation` (`req_path`, `deleted`);
ALTER TABLE `service_method_relation` ADD `type` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '接口类型（gRPC、HTTP）' AFTER `namespace`;
UPDATE `service_method_relation` SET `type` = 'gRPC', `updated_at` = `updated_at` WHERE `deleted` = 0;

ALTER TABLE `service_case_relation_v2` CHANGE `grpc_path` `req_path` VARCHAR(512) NOT NULL COMMENT '请求路径（gRPC路径、HTTP路径）';
ALTER TABLE `service_case_relation_v2` MODIFY `cmd` INT NULL COMMENT '命令号' AFTER `req_path`;
DROP INDEX `ix_scr_grpc_path_case_id` ON `service_case_relation_v2`;
CREATE INDEX `ix_scr_req_path_case_id` ON `service_case_relation_v2` (`project_id`, `general_config_id`, `req_path`, `case_id`);
