CREATE DATABASE IF NOT EXISTS `relation` DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_general_ci;

USE `relation`;

CREATE TABLE `relation`.`service_case_relation`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目ID',
    `general_config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '入口通配环境id',
    `service` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '入口服务名称',
    `interface_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '入口服务接口路径',
    `cmd` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '入口md命令号',
    `relation_service` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联的服务名称（包含入口服务）',
    `case_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '测试用例id',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `unique_record_index`(`project_id`, `general_config_id`, `interface_path`, `case_id`) USING BTREE COMMENT 'grpc接口关联的所有service',
    INDEX `ix_service`(`project_id`, `general_config_id`, `relation_service`) USING BTREE COMMENT 'service对应的所有测试用例'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '服务与测试用例关联表' ROW_FORMAT = Dynamic;