package main

import (
	"github.com/spf13/cobra"
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	api "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/server"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/cmd"
	mqc "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/server"
	rpc "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/server"
)

var (
	apiConfigFile = new(string)
	rpcConfigFile = new(string)
	mqcConfigFile = new(string)
)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		sg := service.NewServiceGroup()
		defer sg.Stop()

		ss, err := server.NewCombineServer([][]server.Option{
			api.Options(*apiConfigFile),
			rpc.Options(*rpcConfigFile),
			mqc.Options(*mqcConfigFile),
		})
		if err != nil {
			return err
		}

		for _, s := range ss {
			sg.Add(s)
		}

		sg.Start()

		return nil
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false

	flags.StringVar(apiConfigFile, "api-config", "api/etc/relation.yaml", "the config file of api service")
	flags.StringVar(rpcConfigFile, "rpc-config", "rpc/etc/relation.yaml", "the config file of rpc service")
	flags.StringVar(mqcConfigFile, "mqc-config", "mqc/etc/relation.yaml", "the config file of mqc service")

	cobra.CheckErr(root.Execute())
}
