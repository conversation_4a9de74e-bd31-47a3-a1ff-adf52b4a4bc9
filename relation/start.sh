#!/bin/sh

path="/app/relation"
project="relation"

# api
mkdir -p ${path}/api/etc/
curl -X GET -s -H "Content-Type:application/json;charset=UTF-8" \
 -H "Authorization:${APOLLO_AUTH_TOKEN}" \
  http://${APOLLO_ADDR}/openapi/v1/envs/dev/apps/probe-backend/clusters/${project}/namespaces/api.yaml | jq .items[0].value | sed 's/^"\|"$//g' | sed ':a;:N;$!ba;s/\\n/\n/g' \
   > ${path}/api/etc/${project}.yaml

# rpc
mkdir -p ${path}/rpc/etc/
curl -X GET -s -H "Content-Type:application/json;charset=UTF-8" \
 -H "Authorization:${APOLLO_AUTH_TOKEN}" \
  http://${APOLLO_ADDR}/openapi/v1/envs/dev/apps/probe-backend/clusters/${project}/namespaces/rpc.yaml | jq .items[0].value | sed 's/^"\|"$//g' | sed ':a;:N;$!ba;s/\\n/\n/g' \
   > ${path}/rpc/etc/${project}.yaml

nohup ${path}/${project}.linux --api-config ${path}/api/etc/${project}.yaml --rpc-config ${path}/rpc/etc/${project}.yaml >> /app/logs/${project}/stdout.log 2>&1 &
