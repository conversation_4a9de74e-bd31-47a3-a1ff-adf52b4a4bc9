package model

import (
	"context"
	"database/sql"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ ServiceCaseRelationModel = (*customServiceCaseRelationModel)(nil)

	serviceCaseRelationInsertFields = stringx.Remove(serviceCaseRelationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// ServiceCaseRelationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customServiceCaseRelationModel.
	ServiceCaseRelationModel interface {
		serviceCaseRelationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ServiceCaseRelation) squirrel.InsertBuilder
		UpdateBuilder(data *ServiceCaseRelation) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ServiceCaseRelation, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ServiceCaseRelation) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ServiceCaseRelation) (sql.Result, error)
	}

	customServiceCaseRelationModel struct {
		*defaultServiceCaseRelationModel
	}
)

// NewServiceCaseRelationModel returns a model for the database table.
func NewServiceCaseRelationModel(conn sqlx.SqlConn, c cache.CacheConf) ServiceCaseRelationModel {
	return &customServiceCaseRelationModel{
		defaultServiceCaseRelationModel: newServiceCaseRelationModel(conn, c),
	}
}

func (m *customServiceCaseRelationModel) Table() string {
	return m.table
}

func (m *customServiceCaseRelationModel) Fields() []string {
	return serviceCaseRelationFieldNames
}

func (m *customServiceCaseRelationModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customServiceCaseRelationModel) InsertBuilder(data *ServiceCaseRelation) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(serviceCaseRelationInsertFields...).Values(
		data.ProjectId,
		data.GeneralConfigId,
		data.Service,
		data.InterfacePath,
		data.Cmd,
		data.RelationService,
		data.CaseId,
	)
}

func (m *customServiceCaseRelationModel) UpdateBuilder(data *ServiceCaseRelation) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`updated_at`": data.UpdatedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customServiceCaseRelationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(serviceCaseRelationFieldNames...).From(m.table)
}

func (m *customServiceCaseRelationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").From(m.table)
}

func (m *customServiceCaseRelationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customServiceCaseRelationModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ServiceCaseRelation, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ServiceCaseRelation
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customServiceCaseRelationModel) InsertTX(ctx context.Context, session sqlx.Session, data *ServiceCaseRelation) (sql.Result, error) {
	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query, values, err := m.InsertBuilder(data).ToSql()
		if err != nil {
			return nil, err
		}
		if session != nil {
			return session.ExecCtx(ctx, query, values...)
		}
		return conn.ExecCtx(ctx, query, values...)
	})
}

func (m *customServiceCaseRelationModel) UpdateTX(ctx context.Context, session sqlx.Session, data *ServiceCaseRelation) (sql.Result, error) {
	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query, values, err := m.UpdateBuilder(data).ToSql()
		if err != nil {
			return nil, err
		}
		if session != nil {
			return session.ExecCtx(ctx, query, values...)
		}
		return conn.ExecCtx(ctx, query, values...)
	})
}
