package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ ServiceMethodRelationModel = (*customServiceMethodRelationModel)(nil)

	serviceMethodRelationInsertFields = stringx.Remove(
		serviceMethodRelationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// ServiceMethodRelationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customServiceMethodRelationModel.
	ServiceMethodRelationModel interface {
		serviceMethodRelationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ServiceMethodRelation) squirrel.InsertBuilder
		UpdateBuilder(data *ServiceMethodRelation) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ServiceMethodRelation, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ServiceMethodRelation) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ServiceMethodRelation) (sql.Result, error)

		FindAll(ctx context.Context) ([]*ServiceMethodRelation, error)
		FindByService(ctx context.Context, service, namespace string) ([]*ServiceMethodRelation, error)
		FindByMethod(ctx context.Context, method string) ([]*ServiceMethodRelation, error)
		FindByUpdatedAt(ctx context.Context, updatedAt time.Time) ([]*ServiceMethodRelation, error)
		DeleteByUpdatedAt(ctx context.Context, session sqlx.Session, updatedAt time.Time) error
	}

	customServiceMethodRelationModel struct {
		*defaultServiceMethodRelationModel

		conn sqlx.SqlConn
	}
)

// NewServiceMethodRelationModel returns a model for the database table.
func NewServiceMethodRelationModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) ServiceMethodRelationModel {
	return &customServiceMethodRelationModel{
		defaultServiceMethodRelationModel: newServiceMethodRelationModel(conn, c, opts...),
		conn:                              conn,
	}
}

func (m *customServiceMethodRelationModel) Table() string {
	return m.table
}

func (m *customServiceMethodRelationModel) Fields() []string {
	return serviceMethodRelationFieldNames
}

func (m *customServiceMethodRelationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customServiceMethodRelationModel) InsertBuilder(data *ServiceMethodRelation) squirrel.InsertBuilder {
	ib := squirrel.Insert(m.table).Columns(serviceMethodRelationInsertFields...)
	values := []any{
		data.Service,
		data.Namespace,
		data.Type,
		data.Method,
		data.ReqPath,
		data.QueryPath,
		data.Cmd,
		data.Deprecated,
		data.Deleted,
		data.CreatedBy,
		data.UpdatedBy,
	}
	if !data.CreatedAt.IsZero() {
		ib = ib.Columns("`created_at`")
		values = append(values, data.CreatedAt)
	}
	if !data.UpdatedAt.IsZero() {
		ib = ib.Columns("`updated_at`")
		values = append(values, data.UpdatedAt)
	}

	return ib.Values(values...)
}

func (m *customServiceMethodRelationModel) UpdateBuilder(data *ServiceMethodRelation) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`req_path`":   data.ReqPath,
		"`query_path`": data.QueryPath,
		"`cmd`":        data.Cmd,
		"`deprecated`": data.Deprecated,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	if !data.UpdatedAt.IsZero() {
		eq["`updated_at`"] = data.UpdatedAt
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customServiceMethodRelationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(serviceMethodRelationFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customServiceMethodRelationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customServiceMethodRelationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customServiceMethodRelationModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ServiceMethodRelation, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ServiceMethodRelation
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customServiceMethodRelationModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *ServiceMethodRelation,
) (sql.Result, error) {
	relationServiceMethodRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, data.Id)
	relationServiceMethodRelationServiceNamespaceMethodKey := fmt.Sprintf(
		"%s%v:%v:%v",
		cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix, data.Service, data.Namespace, data.Method,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, relationServiceMethodRelationIdKey, relationServiceMethodRelationServiceNamespaceMethodKey,
	)
}

func (m *customServiceMethodRelationModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *ServiceMethodRelation,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	relationServiceMethodRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, data.Id)
	relationServiceMethodRelationServiceNamespaceMethodKey := fmt.Sprintf(
		"%s%v:%v:%v",
		cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix, data.Service, data.Namespace, data.Method,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, relationServiceMethodRelationIdKey, relationServiceMethodRelationServiceNamespaceMethodKey,
	)
}

func (m *customServiceMethodRelationModel) FindAll(ctx context.Context) ([]*ServiceMethodRelation, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}

func (m *customServiceMethodRelationModel) FindByService(
	ctx context.Context, service, namespace string,
) ([]*ServiceMethodRelation, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`service` = ? AND `namespace` = ?", service, namespace))
}

func (m *customServiceMethodRelationModel) FindByMethod(ctx context.Context, method string) (
	[]*ServiceMethodRelation, error,
) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`method` = ?", method))
}

func (m *customServiceMethodRelationModel) FindByUpdatedAt(
	ctx context.Context, updatedAt time.Time,
) ([]*ServiceMethodRelation, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`updated_at` <= ?", updatedAt))
}

func (m *customServiceMethodRelationModel) DeleteByUpdatedAt(
	ctx context.Context, session sqlx.Session, updatedAt time.Time,
) error {
	keys := m.getKeysByUpdatedAt(ctx, updatedAt)

	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			/*
				SQL:
				DELETE `service_method_relation`
				WHERE `updated_at` <= ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`updated_at` <= ?", updatedAt).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
	return err
}

func (m *customServiceMethodRelationModel) getKeysByUpdatedAt(ctx context.Context, updatedAt time.Time) []string {
	cs, err := m.FindByUpdatedAt(ctx, updatedAt)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v",
				cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix, c.Service, c.Namespace, c.Method,
			),
		)
	}

	return keys
}
