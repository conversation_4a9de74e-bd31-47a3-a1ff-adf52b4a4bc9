// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	serviceTeamRelationTableName           = "`service_team_relation`"
	serviceTeamRelationFieldNames          = builder.RawFieldNames(&ServiceTeamRelation{})
	serviceTeamRelationRows                = strings.Join(serviceTeamRelationFieldNames, ",")
	serviceTeamRelationRowsExpectAutoSet   = strings.Join(stringx.Remove(serviceTeamRelationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	serviceTeamRelationRowsWithPlaceHolder = strings.Join(stringx.Remove(serviceTeamRelationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheRelationServiceTeamRelationIdPrefix                       = "cache:relation:serviceTeamRelation:id:"
	cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix = "cache:relation:serviceTeamRelation:service:namespace:teamName:"
)

type (
	serviceTeamRelationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ServiceTeamRelation) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ServiceTeamRelation, error)
		FindOneByServiceNamespaceTeamName(ctx context.Context, service string, namespace string, teamName string) (*ServiceTeamRelation, error)
		Update(ctx context.Context, session sqlx.Session, data *ServiceTeamRelation) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultServiceTeamRelationModel struct {
		sqlc.CachedConn
		table string
	}

	ServiceTeamRelation struct {
		Id              int64          `db:"id"`               // 自增ID
		Service         string         `db:"service"`          // 服务名称
		Namespace       string         `db:"namespace"`        // 命名空间
		TeamName        string         `db:"team_name"`        // 团队名称
		TeamDescription sql.NullString `db:"team_description"` // 团队描述
		Deleted         int64          `db:"deleted"`          // 逻辑删除标识（未删除、已删除）
		CreatedBy       string         `db:"created_by"`       // 创建者的用户ID
		UpdatedBy       string         `db:"updated_by"`       // 最近一次更新者的用户ID
		DeletedBy       sql.NullString `db:"deleted_by"`       // 删除者的用户ID
		CreatedAt       time.Time      `db:"created_at"`       // 创建时间
		UpdatedAt       time.Time      `db:"updated_at"`       // 更新时间
		DeletedAt       sql.NullTime   `db:"deleted_at"`       // 删除时间
	}
)

func newServiceTeamRelationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultServiceTeamRelationModel {
	return &defaultServiceTeamRelationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`service_team_relation`",
	}
}

func (m *defaultServiceTeamRelationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	relationServiceTeamRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, id)
	relationServiceTeamRelationServiceNamespaceTeamNameKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix, data.Service, data.Namespace, data.TeamName)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, relationServiceTeamRelationIdKey, relationServiceTeamRelationServiceNamespaceTeamNameKey)
	return err
}

func (m *defaultServiceTeamRelationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	relationServiceTeamRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, id)
	relationServiceTeamRelationServiceNamespaceTeamNameKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix, data.Service, data.Namespace, data.TeamName)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, relationServiceTeamRelationIdKey, relationServiceTeamRelationServiceNamespaceTeamNameKey)
	return err
}

func (m *defaultServiceTeamRelationModel) FindOne(ctx context.Context, id int64) (*ServiceTeamRelation, error) {
	relationServiceTeamRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, id)
	var resp ServiceTeamRelation
	err := m.QueryRowCtx(ctx, &resp, relationServiceTeamRelationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", serviceTeamRelationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultServiceTeamRelationModel) FindOneByServiceNamespaceTeamName(ctx context.Context, service string, namespace string, teamName string) (*ServiceTeamRelation, error) {
	relationServiceTeamRelationServiceNamespaceTeamNameKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix, service, namespace, teamName)
	var resp ServiceTeamRelation
	err := m.QueryRowIndexCtx(ctx, &resp, relationServiceTeamRelationServiceNamespaceTeamNameKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `service` = ? and `namespace` = ? and `team_name` = ? and `deleted` = ? limit 1", serviceTeamRelationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, service, namespace, teamName, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultServiceTeamRelationModel) Insert(ctx context.Context, session sqlx.Session, data *ServiceTeamRelation) (sql.Result, error) {
	relationServiceTeamRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, data.Id)
	relationServiceTeamRelationServiceNamespaceTeamNameKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix, data.Service, data.Namespace, data.TeamName)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, serviceTeamRelationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.Service, data.Namespace, data.TeamName, data.TeamDescription, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.Service, data.Namespace, data.TeamName, data.TeamDescription, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, relationServiceTeamRelationIdKey, relationServiceTeamRelationServiceNamespaceTeamNameKey)
}

func (m *defaultServiceTeamRelationModel) Update(ctx context.Context, session sqlx.Session, newData *ServiceTeamRelation) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	relationServiceTeamRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, data.Id)
	relationServiceTeamRelationServiceNamespaceTeamNameKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix, data.Service, data.Namespace, data.TeamName)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, serviceTeamRelationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.Service, newData.Namespace, newData.TeamName, newData.TeamDescription, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.Service, newData.Namespace, newData.TeamName, newData.TeamDescription, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, relationServiceTeamRelationIdKey, relationServiceTeamRelationServiceNamespaceTeamNameKey)
}

func (m *defaultServiceTeamRelationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, primary)
}

func (m *defaultServiceTeamRelationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", serviceTeamRelationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultServiceTeamRelationModel) tableName() string {
	return m.table
}
