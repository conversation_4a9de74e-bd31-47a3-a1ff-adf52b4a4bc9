// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	serviceMethodRelationTableName           = "`service_method_relation`"
	serviceMethodRelationFieldNames          = builder.RawFieldNames(&ServiceMethodRelation{})
	serviceMethodRelationRows                = strings.Join(serviceMethodRelationFieldNames, ",")
	serviceMethodRelationRowsExpectAutoSet   = strings.Join(stringx.Remove(serviceMethodRelationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	serviceMethodRelationRowsWithPlaceHolder = strings.Join(stringx.Remove(serviceMethodRelationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheRelationServiceMethodRelationIdPrefix                     = "cache:relation:serviceMethodRelation:id:"
	cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix = "cache:relation:serviceMethodRelation:service:namespace:method:"
)

type (
	serviceMethodRelationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ServiceMethodRelation) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ServiceMethodRelation, error)
		FindOneByServiceNamespaceMethod(ctx context.Context, service string, namespace string, method string) (*ServiceMethodRelation, error)
		Update(ctx context.Context, session sqlx.Session, data *ServiceMethodRelation) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultServiceMethodRelationModel struct {
		sqlc.CachedConn
		table string
	}

	ServiceMethodRelation struct {
		Id         int64          `db:"id"`         // 自增ID
		Service    string         `db:"service"`    // 服务名称
		Namespace  string         `db:"namespace"`  // 命名空间
		Type       string         `db:"type"`       // 接口类型（gRPC、HTTP）
		Method     string         `db:"method"`     // 接口名称
		ReqPath    string         `db:"req_path"`   // 请求路径（gRPC路径、HTTP路径）
		QueryPath  sql.NullString `db:"query_path"` // 请求路径（查询天相）
		Cmd        sql.NullInt64  `db:"cmd"`        // 命令号
		Deprecated int64          `db:"deprecated"` // 是否已弃用（未弃用、已弃用
		Deleted    int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy  string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy  string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy  sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt  time.Time      `db:"created_at"` // 创建时间
		UpdatedAt  time.Time      `db:"updated_at"` // 更新时间
		DeletedAt  sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newServiceMethodRelationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultServiceMethodRelationModel {
	return &defaultServiceMethodRelationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`service_method_relation`",
	}
}

func (m *defaultServiceMethodRelationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	relationServiceMethodRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, id)
	relationServiceMethodRelationServiceNamespaceMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix, data.Service, data.Namespace, data.Method)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, relationServiceMethodRelationIdKey, relationServiceMethodRelationServiceNamespaceMethodKey)
	return err
}

func (m *defaultServiceMethodRelationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	relationServiceMethodRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, id)
	relationServiceMethodRelationServiceNamespaceMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix, data.Service, data.Namespace, data.Method)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, relationServiceMethodRelationIdKey, relationServiceMethodRelationServiceNamespaceMethodKey)
	return err
}

func (m *defaultServiceMethodRelationModel) FindOne(ctx context.Context, id int64) (*ServiceMethodRelation, error) {
	relationServiceMethodRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, id)
	var resp ServiceMethodRelation
	err := m.QueryRowCtx(ctx, &resp, relationServiceMethodRelationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", serviceMethodRelationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultServiceMethodRelationModel) FindOneByServiceNamespaceMethod(ctx context.Context, service string, namespace string, method string) (*ServiceMethodRelation, error) {
	relationServiceMethodRelationServiceNamespaceMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix, service, namespace, method)
	var resp ServiceMethodRelation
	err := m.QueryRowIndexCtx(ctx, &resp, relationServiceMethodRelationServiceNamespaceMethodKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `service` = ? and `namespace` = ? and `method` = ? and `deleted` = ? limit 1", serviceMethodRelationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, service, namespace, method, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultServiceMethodRelationModel) Insert(ctx context.Context, session sqlx.Session, data *ServiceMethodRelation) (sql.Result, error) {
	relationServiceMethodRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, data.Id)
	relationServiceMethodRelationServiceNamespaceMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix, data.Service, data.Namespace, data.Method)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, serviceMethodRelationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.Service, data.Namespace, data.Type, data.Method, data.ReqPath, data.QueryPath, data.Cmd, data.Deprecated, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.Service, data.Namespace, data.Type, data.Method, data.ReqPath, data.QueryPath, data.Cmd, data.Deprecated, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, relationServiceMethodRelationIdKey, relationServiceMethodRelationServiceNamespaceMethodKey)
}

func (m *defaultServiceMethodRelationModel) Update(ctx context.Context, session sqlx.Session, newData *ServiceMethodRelation) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	relationServiceMethodRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, data.Id)
	relationServiceMethodRelationServiceNamespaceMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheRelationServiceMethodRelationServiceNamespaceMethodPrefix, data.Service, data.Namespace, data.Method)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, serviceMethodRelationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.Service, newData.Namespace, newData.Type, newData.Method, newData.ReqPath, newData.QueryPath, newData.Cmd, newData.Deprecated, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.Service, newData.Namespace, newData.Type, newData.Method, newData.ReqPath, newData.QueryPath, newData.Cmd, newData.Deprecated, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, relationServiceMethodRelationIdKey, relationServiceMethodRelationServiceNamespaceMethodKey)
}

func (m *defaultServiceMethodRelationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheRelationServiceMethodRelationIdPrefix, primary)
}

func (m *defaultServiceMethodRelationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", serviceMethodRelationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultServiceMethodRelationModel) tableName() string {
	return m.table
}
