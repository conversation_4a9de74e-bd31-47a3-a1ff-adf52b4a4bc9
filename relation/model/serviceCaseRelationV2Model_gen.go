// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	serviceCaseRelationV2TableName           = "`service_case_relation_v2`"
	serviceCaseRelationV2FieldNames          = builder.RawFieldNames(&ServiceCaseRelationV2{})
	serviceCaseRelationV2Rows                = strings.Join(serviceCaseRelationV2FieldNames, ",")
	serviceCaseRelationV2RowsExpectAutoSet   = strings.Join(stringx.Remove(serviceCaseRelationV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	serviceCaseRelationV2RowsWithPlaceHolder = strings.Join(stringx.Remove(serviceCaseRelationV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheRelationServiceCaseRelationV2IdPrefix = "cache:relation:serviceCaseRelationV2:id:"
)

type (
	serviceCaseRelationV2Model interface {
		Insert(ctx context.Context, session sqlx.Session, data *ServiceCaseRelationV2) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ServiceCaseRelationV2, error)
		Update(ctx context.Context, session sqlx.Session, data *ServiceCaseRelationV2) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultServiceCaseRelationV2Model struct {
		sqlc.CachedConn
		table string
	}

	ServiceCaseRelationV2 struct {
		Id              int64          `db:"id"`                // 自增ID
		ProjectId       string         `db:"project_id"`        // 项目ID
		GeneralConfigId string         `db:"general_config_id"` // 通用配置ID
		Service         string         `db:"service"`           // 服务名称
		Namespace       string         `db:"namespace"`         // 命名空间
		Method          string         `db:"method"`            // 接口名称
		ReqPath         string         `db:"req_path"`          // 请求路径（gRPC路径、HTTP路径）
		Cmd             sql.NullInt64  `db:"cmd"`               // 命令号
		ToService       string         `db:"to_service"`        // 经过的服务名称
		DocumentId      sql.NullString `db:"document_id"`       // 接口文档ID
		CaseId          string         `db:"case_id"`           // 用例ID
		Deleted         int64          `db:"deleted"`           // 逻辑删除标识（未删除、已删除）
		CreatedBy       string         `db:"created_by"`        // 创建者的用户ID
		UpdatedBy       string         `db:"updated_by"`        // 最近一次更新者的用户ID
		DeletedBy       sql.NullString `db:"deleted_by"`        // 删除者的用户ID
		CreatedAt       time.Time      `db:"created_at"`        // 创建时间
		UpdatedAt       time.Time      `db:"updated_at"`        // 更新时间
		DeletedAt       sql.NullTime   `db:"deleted_at"`        // 删除时间
	}
)

func newServiceCaseRelationV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultServiceCaseRelationV2Model {
	return &defaultServiceCaseRelationV2Model{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`service_case_relation_v2`",
	}
}

func (m *defaultServiceCaseRelationV2Model) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	relationServiceCaseRelationV2IdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, relationServiceCaseRelationV2IdKey)
	return err
}

func (m *defaultServiceCaseRelationV2Model) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	relationServiceCaseRelationV2IdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, relationServiceCaseRelationV2IdKey)
	return err
}

func (m *defaultServiceCaseRelationV2Model) FindOne(ctx context.Context, id int64) (*ServiceCaseRelationV2, error) {
	relationServiceCaseRelationV2IdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, id)
	var resp ServiceCaseRelationV2
	err := m.QueryRowCtx(ctx, &resp, relationServiceCaseRelationV2IdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", serviceCaseRelationV2Rows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultServiceCaseRelationV2Model) Insert(ctx context.Context, session sqlx.Session, data *ServiceCaseRelationV2) (sql.Result, error) {
	relationServiceCaseRelationV2IdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, serviceCaseRelationV2RowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.GeneralConfigId, data.Service, data.Namespace, data.Method, data.ReqPath, data.Cmd, data.ToService, data.DocumentId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.GeneralConfigId, data.Service, data.Namespace, data.Method, data.ReqPath, data.Cmd, data.ToService, data.DocumentId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, relationServiceCaseRelationV2IdKey)
}

func (m *defaultServiceCaseRelationV2Model) Update(ctx context.Context, session sqlx.Session, data *ServiceCaseRelationV2) (sql.Result, error) {

	relationServiceCaseRelationV2IdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, serviceCaseRelationV2RowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.GeneralConfigId, data.Service, data.Namespace, data.Method, data.ReqPath, data.Cmd, data.ToService, data.DocumentId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.GeneralConfigId, data.Service, data.Namespace, data.Method, data.ReqPath, data.Cmd, data.ToService, data.DocumentId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, relationServiceCaseRelationV2IdKey)
}

func (m *defaultServiceCaseRelationV2Model) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, primary)
}

func (m *defaultServiceCaseRelationV2Model) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", serviceCaseRelationV2Rows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultServiceCaseRelationV2Model) tableName() string {
	return m.table
}
