package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ ServiceCaseRelationV2Model = (*customServiceCaseRelationV2Model)(nil)

	serviceCaseRelationV2InsertFields = stringx.Remove(
		serviceCaseRelationV2FieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// ServiceCaseRelationV2Model is an interface to be customized, add more methods here,
	// and implement the added methods in customServiceCaseRelationV2Model.
	ServiceCaseRelationV2Model interface {
		serviceCaseRelationV2Model
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ServiceCaseRelationV2) squirrel.InsertBuilder
		UpdateBuilder(data *ServiceCaseRelationV2) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ServiceCaseRelationV2, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ServiceCaseRelationV2) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ServiceCaseRelationV2) (sql.Result, error)

		FindByReqPathAndCaseID(
			ctx context.Context, projectID, generalConfigID, reqPath, caseID string,
		) ([]*ServiceCaseRelationV2, error)
		FindByToService(ctx context.Context, projectID, generalConfigID, toService string, updatedAt time.Time) (
			[]*ServiceCaseRelationV2, error,
		)

		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customServiceCaseRelationV2Model struct {
		*defaultServiceCaseRelationV2Model

		conn sqlx.SqlConn
	}
)

// NewServiceCaseRelationV2Model returns a model for the database table.
func NewServiceCaseRelationV2Model(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) ServiceCaseRelationV2Model {
	return &customServiceCaseRelationV2Model{
		defaultServiceCaseRelationV2Model: newServiceCaseRelationV2Model(conn, c, opts...),
		conn:                              conn,
	}
}

func (m *customServiceCaseRelationV2Model) Table() string {
	return m.table
}

func (m *customServiceCaseRelationV2Model) Fields() []string {
	return serviceCaseRelationV2FieldNames
}

func (m *customServiceCaseRelationV2Model) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customServiceCaseRelationV2Model) InsertBuilder(data *ServiceCaseRelationV2) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(serviceCaseRelationV2InsertFields...).Values(
		data.ProjectId, data.GeneralConfigId, data.Service, data.Namespace, data.Method, data.ReqPath, data.Cmd,
		data.ToService, data.DocumentId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customServiceCaseRelationV2Model) UpdateBuilder(data *ServiceCaseRelationV2) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`service`":     data.Service,
		"`namespace`":   data.Namespace,
		"`req_path`":    data.ReqPath,
		"`cmd`":         data.Cmd,
		"`to_service`":  data.ToService,
		"`document_id`": data.DocumentId,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`updated_at`":  data.UpdatedAt,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customServiceCaseRelationV2Model) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(serviceCaseRelationV2FieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customServiceCaseRelationV2Model) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customServiceCaseRelationV2Model) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customServiceCaseRelationV2Model) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ServiceCaseRelationV2, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ServiceCaseRelationV2
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customServiceCaseRelationV2Model) InsertTX(
	ctx context.Context, session sqlx.Session, data *ServiceCaseRelationV2,
) (sql.Result, error) {
	relationServiceCaseRelationV2IdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, relationServiceCaseRelationV2IdKey,
	)
}

func (m *customServiceCaseRelationV2Model) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *ServiceCaseRelationV2,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	relationServiceCaseRelationV2IdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, relationServiceCaseRelationV2IdKey,
	)
}

func (m *customServiceCaseRelationV2Model) FindByReqPathAndCaseID(
	ctx context.Context, projectID, generalConfigID, reqPath, caseID string,
) ([]*ServiceCaseRelationV2, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where(
			"`project_id` = ? AND `general_config_id` = ? AND `req_path` = ? AND `case_id` = ?",
			projectID, generalConfigID, reqPath, caseID,
		),
	)
}

func (m *customServiceCaseRelationV2Model) FindByToService(
	ctx context.Context, projectID, generalConfigID, toService string, updatedAt time.Time,
) (
	[]*ServiceCaseRelationV2, error,
) {
	sb := m.SelectBuilder().
		Where(
			"`project_id` = ? AND `to_service` = ?", projectID, toService,
		)
	if len(generalConfigID) > 0 {
		sb.Where("`general_config_id` = ?", generalConfigID)
	}
	if !updatedAt.IsZero() {
		sb.Where("`updated_at` > ?", updatedAt)
	}

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customServiceCaseRelationV2Model) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	keys := m.getKeysOfBeforeNDaysRecords(ctx, days)

	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			stmt, values, err := squirrel.Delete(m.table).
				Where("`updated_at` < ?", time.Now().AddDate(0, 0, -days)).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}

			return m.conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
	return err
}

func (m *customServiceCaseRelationV2Model) getKeysOfBeforeNDaysRecords(ctx context.Context, days int) []string {
	rs, err := m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`updated_at` < ?", time.Now().AddDate(0, 0, -days)),
	)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(rs))
	for _, r := range rs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationV2IdPrefix, r.Id))
	}

	return keys
}
