package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ ServiceTeamRelationModel = (*customServiceTeamRelationModel)(nil)

	serviceTeamRelationInsertFields = stringx.Remove(
		serviceTeamRelationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// ServiceTeamRelationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customServiceTeamRelationModel.
	ServiceTeamRelationModel interface {
		serviceTeamRelationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ServiceTeamRelation) squirrel.InsertBuilder
		UpdateBuilder(data *ServiceTeamRelation) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ServiceTeamRelation, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ServiceTeamRelation) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ServiceTeamRelation) (sql.Result, error)

		FindAll(ctx context.Context) ([]*ServiceTeamRelation, error)
		FindByService(ctx context.Context, service string) ([]*ServiceTeamRelation, error)
		FindByTeam(ctx context.Context, team string) ([]*ServiceTeamRelation, error)
		FindByUpdatedAt(ctx context.Context, updatedAt time.Time) ([]*ServiceTeamRelation, error)
		DeleteByUpdatedAt(ctx context.Context, session sqlx.Session, updatedAt time.Time) error
	}

	customServiceTeamRelationModel struct {
		*defaultServiceTeamRelationModel

		conn sqlx.SqlConn
	}
)

// NewServiceTeamRelationModel returns a model for the database table.
func NewServiceTeamRelationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) ServiceTeamRelationModel {
	return &customServiceTeamRelationModel{
		defaultServiceTeamRelationModel: newServiceTeamRelationModel(conn, c, opts...),
		conn:                            conn,
	}
}

func (m *customServiceTeamRelationModel) Table() string {
	return m.table
}

func (m *customServiceTeamRelationModel) Fields() []string {
	return serviceTeamRelationFieldNames
}

func (m *customServiceTeamRelationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customServiceTeamRelationModel) InsertBuilder(data *ServiceTeamRelation) squirrel.InsertBuilder {
	ib := squirrel.Insert(m.table).Columns(serviceTeamRelationInsertFields...)
	values := []any{
		data.Service,
		data.Namespace,
		data.TeamName,
		data.TeamDescription,
		data.Deleted,
		data.CreatedBy,
		data.UpdatedBy,
	}
	if !data.CreatedAt.IsZero() {
		ib = ib.Columns("`created_at`")
		values = append(values, data.CreatedAt)
	}
	if !data.UpdatedAt.IsZero() {
		ib = ib.Columns("`updated_at`")
		values = append(values, data.UpdatedAt)
	}

	return ib.Values(values...)
}

func (m *customServiceTeamRelationModel) UpdateBuilder(data *ServiceTeamRelation) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`team_name`":        data.TeamName,
		"`team_description`": data.TeamDescription,
		"`deleted`":          data.Deleted,
		"`updated_by`":       data.UpdatedBy,
		"`deleted_by`":       data.DeletedBy,
		"`deleted_at`":       data.DeletedAt,
	}
	if !data.UpdatedAt.IsZero() {
		eq["`updated_at`"] = data.UpdatedAt
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customServiceTeamRelationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(serviceTeamRelationFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customServiceTeamRelationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customServiceTeamRelationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customServiceTeamRelationModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ServiceTeamRelation, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ServiceTeamRelation
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customServiceTeamRelationModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *ServiceTeamRelation,
) (sql.Result, error) {
	relationServiceTeamRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, data.Id)
	relationServiceTeamRelationServiceNamespaceTeamNameKey := fmt.Sprintf(
		"%s%v:%v:%v",
		cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix, data.Service, data.Namespace, data.TeamName,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, relationServiceTeamRelationIdKey, relationServiceTeamRelationServiceNamespaceTeamNameKey,
	)
}

func (m *customServiceTeamRelationModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *ServiceTeamRelation,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	relationServiceTeamRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, data.Id)
	relationServiceTeamRelationServiceNamespaceTeamNameKey := fmt.Sprintf(
		"%s%v:%v:%v",
		cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix, data.Service, data.Namespace, data.TeamName,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, relationServiceTeamRelationIdKey, relationServiceTeamRelationServiceNamespaceTeamNameKey,
	)
}

func (m *customServiceTeamRelationModel) FindAll(ctx context.Context) ([]*ServiceTeamRelation, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}

func (m *customServiceTeamRelationModel) FindByService(ctx context.Context, service string) (
	[]*ServiceTeamRelation, error,
) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`service` = ?", service))
}

func (m *customServiceTeamRelationModel) FindByTeam(ctx context.Context, team string) ([]*ServiceTeamRelation, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`team_name` = ?", team))
}

func (m *customServiceTeamRelationModel) FindByUpdatedAt(
	ctx context.Context, updatedAt time.Time,
) ([]*ServiceTeamRelation, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`updated_at` <= ?", updatedAt))
}

func (m *customServiceTeamRelationModel) DeleteByUpdatedAt(
	ctx context.Context, session sqlx.Session, updatedAt time.Time,
) error {
	keys := m.getKeysByUpdatedAt(ctx, updatedAt)

	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			/*
				SQL:
				DELETE `service_team_relation`
				WHERE `updated_at` <= ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`updated_at` <= ?", updatedAt).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
	return err
}

func (m *customServiceTeamRelationModel) getKeysByUpdatedAt(ctx context.Context, updatedAt time.Time) []string {
	cs, err := m.FindByUpdatedAt(ctx, updatedAt)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheRelationServiceTeamRelationIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v",
				cacheRelationServiceTeamRelationServiceNamespaceTeamNamePrefix, c.Service, c.Namespace, c.TeamName,
			),
		)
	}

	return keys
}
