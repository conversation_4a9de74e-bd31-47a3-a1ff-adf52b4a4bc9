syntax = "v1"

info (
	title: "质量平台-关系管理服务"
	desc: "目前用于精准测试维护服务与测试用例关系"
	author: "heji<PERSON><PERSON>"
	email: "he<PERSON><PERSON><PERSON>@52tt.com"
	version: "0.1.0"
)

import (
	"types.api"
)

@server (
	prefix: relation/v1
	group: precisiontesting
)

service relation {
	// 精准测试say hello
	@handler sayHelloService
	get /precision_testing/say_hello (SayHelloReq) returns (SayHelloResp)
	
	// 将测试用例id绑定到被测试服务
	@handler bindTestCaseToService
	post /precision_testing/bind_test_case_to_service (BindTestCaseToServiceReq) returns (BindTestCaseToServiceResp)
	
	// 获取被测试服务绑定的测试用例id
	@handler getServiceBindTestCase
	get /precision_testing/get_service_bind_test_case (GetServiceBindTestCaseReq) returns (GetServiceBindTestCaseResp)
}