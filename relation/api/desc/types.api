syntax = "v1"

info (
    title: "质量平台关系管理服务类型定义"
    desc: "质量平台关系管理服务中API接口涉及的类型定义"
    author: "heji<PERSON><PERSON>"
    email: "<EMAIL>"
    version: "0.1.0"
)

type SayHelloReq {

}

type SayHelloResp {
    Info    string `json:"info"`
}

type BindTestCaseToServiceReq {
    ProjectId   string  `json:"project_id" validate:"required" zh:"项目ID"`
    GeneralConfigId string  `json:"general_config_id" validate:"required" zh:"通用配置ID"`
    ServiceName string  `json:"service_name,omitempty,optional" zh:"服务名称"`
    InterfacePath   string `json:"interface_path,omitempty,optional" zh:"接口路径"`
    Cmd string `json:"cmd,omitempty,optional" zh:"命令号"`
    CaseId  string `json:"case_id" validate:"required" zh:"测试用例id"`
}

type BindTestCaseToServiceResp {}

type GetServiceBindTestCaseReq {
    ProjectId   string  `form:"project_id" validate:"required" zh:"项目ID"`
    GeneralConfigId string `form:"general_config_id" validate:"required" zh:"通用配置ID"`
    ServiceName string  `form:"service_name" validate:"required" zh:"服务名称"`
}

type GetServiceBindTestCaseResp {
    CaseIdS []string    `json:"case_id_s"`
}