// Code generated by goctl. DO NOT EDIT.
package types

type SayHelloReq struct {
}

type SayHelloResp struct {
	Info string `json:"info"`
}

type BindTestCaseToServiceReq struct {
	ProjectId       string `json:"project_id" validate:"required" zh:"项目ID"`
	GeneralConfigId string `json:"general_config_id" validate:"required" zh:"通用配置ID"`
	ServiceName     string `json:"service_name,omitempty,optional" zh:"服务名称"`
	InterfacePath   string `json:"interface_path,omitempty,optional" zh:"接口路径"`
	Cmd             string `json:"cmd,omitempty,optional" zh:"命令号"`
	CaseId          string `json:"case_id" validate:"required" zh:"测试用例id"`
}

type BindTestCaseToServiceResp struct {
}

type GetServiceBindTestCaseReq struct {
	ProjectId       string `form:"project_id" validate:"required" zh:"项目ID"`
	GeneralConfigId string `form:"general_config_id" validate:"required" zh:"通用配置ID"`
	ServiceName     string `form:"service_name" validate:"required" zh:"服务名称"`
}

type GetServiceBindTestCaseResp struct {
	CaseIdS []string `json:"case_id_s"`
}
