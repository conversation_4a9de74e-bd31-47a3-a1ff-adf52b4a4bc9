// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	precisiontesting "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/handler/precisiontesting"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/precision_testing/say_hello",
				Handler: precisiontesting.SayHelloServiceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/precision_testing/bind_test_case_to_service",
				Handler: precisiontesting.BindTestCaseToServiceHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/precision_testing/get_service_bind_test_case",
				Handler: precisiontesting.GetServiceBindTestCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/relation/v1"),
	)
}
