package svc

import (
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/client/relationservice"
)

type ServiceContext struct {
	Config config.Config

	Validator   *utils.Validator
	RelationRPC relationservice.RelationService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,

		Validator: utils.NewValidator(c.Validator.Locale),
		RelationRPC: relationservice.NewRelationService(
			zrpc.MustNewClient(
				c.Relation, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}
