package precisiontesting

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type GetServiceBindTestCaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetServiceBindTestCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetServiceBindTestCaseLogic {
	return &GetServiceBindTestCaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetServiceBindTestCaseLogic) GetServiceBindTestCase(req *types.GetServiceBindTestCaseReq) (
	resp *types.GetServiceBindTestCaseResp, err error,
) {
	in := &pb.GetServiceBindTestCaseRequest{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.RelationRPC.GetServiceBindTestCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.GetServiceBindTestCaseResp{}
	if err = utils.Copy(resp, out); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return
}
