package precisiontesting

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type BindTestCaseToServiceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBindTestCaseToServiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BindTestCaseToServiceLogic {
	return &BindTestCaseToServiceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BindTestCaseToServiceLogic) BindTestCaseToService(req *types.BindTestCaseToServiceReq) (
	resp *types.BindTestCaseToServiceResp, err error,
) {
	// req.ServiceName和req.InterfacePath组合的优先级高于req.Cmd
	if req.Cmd == "" {
		if req.ServiceName == "" || req.InterfacePath == "" {
			err = errors.Errorf(
				"传递的参数service_name「%s」、interface_path「%s」和cmd「%s」不能同时为空",
				req.ServiceName, req.InterfacePath, req.Cmd,
			)
			return nil, errors.Wrapf(
				errorx.Err(errorx.ValidateParamError, err.Error()),
				"参数校验发生错误：[%+v]", err,
			)
		}
	}

	in := &pb.BindTestCaseToServiceRequest{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.RelationRPC.BindTestCaseToService(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.BindTestCaseToServiceResp{}
	if err = utils.Copy(resp, out); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
