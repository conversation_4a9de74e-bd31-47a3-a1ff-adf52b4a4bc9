package precisiontesting

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SayHelloServiceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSayHelloServiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SayHelloServiceLogic {
	return &SayHelloServiceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SayHelloServiceLogic) SayHelloService(req *types.SayHelloReq) (resp *types.SayHelloResp, err error) {
	resp = &types.SayHelloResp{
		Info: "hello world!",
	}

	return resp, nil
}
