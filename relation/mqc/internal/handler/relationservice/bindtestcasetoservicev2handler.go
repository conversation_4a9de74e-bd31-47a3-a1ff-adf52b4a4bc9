package relationservice

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"
	"google.golang.org/protobuf/proto"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/logic/relationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

// Deprecated: use new api.
func CreateV2Handler(svcCtx *svc.ServiceContext) func(data []byte) (string, error) {
	return func(data []byte) (string, error) {
		taskData := &pb.BindTestCaseToServiceV2Request{}
		err := proto.Unmarshal(data, taskData)
		if err != nil {
			logx.Errorf("订阅源数据反序列化失败：%s", err)
			return "fail", err
		}

		logic := relationservice.NewServiceBindCaseV2Logic(context.Background(), svcCtx) // ctx done
		err = logic.BindTestCaseToService(taskData)
		if err != nil {
			logx.Errorf("bind test case to service fail: %s", err)
			return "fail", err
		}
		return "success", nil
	}
}

type ProcessorV2 struct {
	svcCtx *svc.ServiceContext
}

func (processor *ProcessorV2) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	taskData := &pb.BindTestCaseToServiceV2Request{}
	err = protobuf.UnmarshalJSON(task.Payload, taskData)
	if err != nil {
		logger.Errorf("failed to unmarshal the payload of task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of task, payload: %s, error: %+v", task.Payload, err,
		)
	}

	logic := relationservice.NewServiceBindCaseV2Logic(ctx, processor.svcCtx)
	err = logic.BindTestCaseToService(taskData)
	if err != nil {
		logger.Errorf("failed to bind test case to service, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), err
	}

	return []byte(constants.SUCCESS), nil
}

func NewProcessorV2(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorV2{
		svcCtx: svcCtx,
	}
}
