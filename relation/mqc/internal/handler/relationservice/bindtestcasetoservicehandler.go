package relationservice

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"
	"google.golang.org/protobuf/proto"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/logic/relationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

func CreateHandler(svcCtx *svc.ServiceContext) func(data []byte) (string, error) {
	return func(data []byte) (string, error) {
		taskData := &pb.BindTestCaseToServiceRequest{}
		err := proto.Unmarshal(data, taskData)
		if err != nil {
			logx.Errorf("订阅源数据反序列化失败：%s", err)
			return "fail", err
		}

		logic := relationservice.NewServiceBindCaseLogic(context.Background(), svcCtx) // ctx done
		err = logic.BindTestCaseToService(taskData)
		if err != nil {
			logx.Errorf("bind test case to service fail: %s", err)
			return "fail", err
		}
		return "success", nil
	}
}

type Processor struct {
	svcCtx *svc.ServiceContext
}

func (processor *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logx.WithContext(ctx).Debug(
		fmt.Sprintf(
			"processor TraceID:%s, task name:%s", trace.TraceIDFromContext(ctx), task.Typename,
		),
	)
	logx.WithContext(ctx).Debug(fmt.Sprintf("processor SpanID:%s", trace.SpanIDFromContext(ctx)))

	taskData := &pb.BindTestCaseToServiceRequest{}
	err = proto.Unmarshal(task.Payload, taskData)
	if err != nil {
		logx.Errorf("订阅源数据反序列化失败：%s", err)
		return []byte("fail"), err
	}

	logic := relationservice.NewServiceBindCaseLogic(ctx, processor.svcCtx)
	err = logic.BindTestCaseToService(taskData)
	if err != nil {
		logx.Errorf("bind test case to service fail: %s", err)
		return []byte("fail"), err
	}

	return []byte("success"), nil
}

func NewProcessor(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &Processor{
		svcCtx: svcCtx,
	}
}
