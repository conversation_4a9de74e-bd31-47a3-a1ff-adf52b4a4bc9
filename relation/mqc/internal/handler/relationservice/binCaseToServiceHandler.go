package relationservice

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/logic/relationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type ProcessorBindCaseToService struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorBindCaseToService(svcCtx *svc.ServiceContext) base.Handler {
	return &ProcessorBindCaseToService{
		svcCtx: svcCtx,
	}
}

func (p *ProcessorBindCaseToService) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	if task.Payload == nil {
		return []byte(constants.SUCCESS), nil
	}
	logger.Debugf("the payload of bind case to service task: %s", task.Payload)

	var in pb.BindCaseToServiceReq
	if err = protobuf.UnmarshalJSON(task.Payload, &in); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of bind case to service, payload: %s, error: %+v",
			task.Payload, err,
		)
	}

	_, err = relationservice.NewBindCaseToServiceLogic(ctx, p.svcCtx).BindCaseToService(&in)
	if err != nil {
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}
