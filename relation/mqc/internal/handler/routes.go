// Code generated by goctl. DO NOT EDIT.
package handler

import (
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/handler/relationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/svc"
)

func RegisterHandlers(server *consumerv2.Consumer, serverCtx *svc.ServiceContext) error {
	return server.RegisterHandlers(
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeRelationBindTestCaseToService, relationservice.NewProcessor(serverCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeRelationBindTestCaseToServiceV2, relationservice.NewProcessorV2(serverCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeRelationBindCaseToService, relationservice.NewProcessorBindCaseToService(serverCtx),
		),
	)
}
