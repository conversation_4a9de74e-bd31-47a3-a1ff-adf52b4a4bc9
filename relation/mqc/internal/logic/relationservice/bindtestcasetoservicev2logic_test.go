package relationservice

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/config"
)

func TestGetServiceNameAndInterfacePath(t *testing.T) {
	ctx := context.Background()

	svCtx := svc.NewServiceContext(
		config.Config{
			Redis: redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: "node",
				Pass: "Quwan@2020",
				DB:   15,
			},
			Cache: []cache.NodeConf{
				{
					RedisConf: redis.RedisConf{
						Host: "127.0.0.1:6379",
						Type: "node",
						Pass: "Quwan@2020",
						DB:   15,
					},
					Weight: 100,
				},
			},
			DB: types.DBConfig{
				DataSource: "probe:Quwan@2020_TTinternation@tcp(************:3306)/relation?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai",
			},
			TTServiceUrl: "http://testing-internal-api.ttyuyin.com/topology-trace-api/query",
		},
	)

	l := NewServiceBindCaseV2Logic(ctx, svCtx)

	in := &pb.BindTestCaseToServiceV2Request{}

	body := `{"request_url":"/router/v1/common/api/call","precision_info":"eyJwcm9iZV9wcm9qZWN0X2lkIjoiIiwicHJvYmVfZ2VuZXJhbF9jb25maWdfaWQiOiIiLCJwcm9iZV90ZXN0X2Nhc2VfaWQiOiIifQ==","request_body":"ewogICAgImNpZCI6ICJjbGllbnRfaWQ6OUhqN0tnc1k4SHItVUp4eHlucjU1IiwKICAgICJib2R5IjogewogICAgICAgICJ1aWQiOiAyNTA4NjEzCiAgICB9LAogICAgIm1ldGhvZCI6ICJnYS5hcGkuY29sbGVjdGlvbi5Db2xsZWN0aW9uTG9naWMuRGVsVm9pY2VXYXRlcm1hcmsiCn0=","response_body":"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}`
	err := jsonx.UnmarshalFromString(body, in)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	out, err := l.genBindTestCaseToServiceReq(in)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	t.Log("out =", jsonx.MarshalToStringIgnoreError(out))

}
