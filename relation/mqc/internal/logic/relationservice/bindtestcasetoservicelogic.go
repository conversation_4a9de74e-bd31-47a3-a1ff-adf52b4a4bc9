package relationservice

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type Properties struct {
	Level     string
	Namespace string
}

type Entity struct {
	Label      string
	Name       string
	Properties *Properties
}

type TTResult struct {
	Entities []*Entity
}

type CmdServiceInfo struct {
	ProjectId     string
	ServiceName   string
	InterfacePath string
}

// Deprecated: use `ServiceBindCaseV2Logic` instead.
type ServiceBindCaseLogic struct {
	*BaseLogic

	RelationServices []string
	ExistRelationMap map[string]*model.ServiceCaseRelation
	UpdateRelations  []*model.ServiceCaseRelation
	InsertRelations  []*model.ServiceCaseRelation
}

func NewServiceBindCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ServiceBindCaseLogic {
	return &ServiceBindCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ServiceBindCaseLogic) getServiceNameAndInterfacePath(in *pb.BindTestCaseToServiceRequest) error {
	var serviceName, interfacePath string
	var req *http.Request
	var res *http.Response
	var err error

	client := &http.Client{}

	if in.Cmd == "" && (in.ServiceName == "" || in.InterfacePath == "") {
		err = errors.New(
			fmt.Sprintf(
				"传递的参数service_name「%s」、interface_path「%s」和cmd「%s」不能同时为空",
				in.ServiceName, in.InterfacePath, in.Cmd,
			),
		)
		return err
	} else {
		var cmdServiceInfo CmdServiceInfo

		redisKey := fmt.Sprintf("relation_cmd_%s_%s", in.ProjectId, in.Cmd)
		cmdServiceInfoStr, redisErr := l.svcCtx.Redis.Get(redisKey)

		if redisErr == nil && cmdServiceInfoStr != "" {
			err = json.Unmarshal([]byte(cmdServiceInfoStr), &cmdServiceInfo)
			if err == nil {
				in.ServiceName = cmdServiceInfo.ServiceName
				in.InterfacePath = cmdServiceInfo.InterfacePath
			}
		} else {
			url := fmt.Sprintf("%s?name=relation_cmd_bind_method&params=%s", l.svcCtx.TTServiceUrl, in.Cmd)
			req, err = http.NewRequest("GET", url, nil)
			if err != nil {
				return err
			}
			req = req.WithContext(l.ctx)

			defer func() {
				result := recover()
				if result != nil {
					logx.Errorf("relation precision testing recover result: %+v", result)
					err = errors.New(fmt.Sprintf("调用TTServiceUrl发生异常: %+v", result))
				} else {
					if res != nil && res.StatusCode == 200 {
						var data []byte

						defer res.Body.Close()
						data, err = io.ReadAll(res.Body)

						if err == nil {
							var tTResult TTResult
							err = json.Unmarshal(data, &tTResult)

							if len(tTResult.Entities) != 0 {
								interfacePath = tTResult.Entities[0].Name
								serviceName = tTResult.Entities[1].Name

								in.ServiceName = serviceName
								in.InterfacePath = interfacePath

								cmdServiceInfo = CmdServiceInfo{
									ProjectId:     in.ProjectId,
									ServiceName:   serviceName,
									InterfacePath: interfacePath,
								}
								cmdServiceInfoBytes, _ := json.Marshal(cmdServiceInfo)
								cmdServiceInfoStr = string(cmdServiceInfoBytes)

								_, redisErr = redislock.NewRedisLockAndAcquire(
									l.svcCtx.Redis, redisKey, redislock.WithValue(cmdServiceInfoStr),
									redislock.WithExpire(24*time.Hour),
								)
								if redisErr != nil {
									logx.Errorf("getServiceNameAndInterfacePath encounter redis error: %+v", redisErr)
								}
							} else {
								err = errors.New("没有找到服务的相关信息")
							}
						}
					}
				}
			}()

			retry := 0
			for { // 最多尝试3次，间隔1秒
				retry += 1
				if retry > 3 {
					break
				}

				res, err = client.Do(req)
				if err == nil {
					break
				}

				time.Sleep(1 * time.Second)
			}
		}

		return err
	}
}

func (l *ServiceBindCaseLogic) getInterfacePathRelationService(in *pb.BindTestCaseToServiceRequest) error {
	var relationServices []string
	var req *http.Request
	var res *http.Response
	var err error

	client := &http.Client{}

	redisKey := fmt.Sprintf(
		"relation_interface_path_%s_%s_%s_%s", in.ProjectId, in.ServiceName, in.InterfacePath, in.GeneralConfigId,
	)
	relationServiceStr, redisErr := l.svcCtx.Redis.Get(redisKey)

	if redisErr == nil && relationServiceStr != "" {
		err = json.Unmarshal([]byte(relationServiceStr), &relationServices)
		if err == nil {
			l.RelationServices = relationServices
		}
	} else {
		url := fmt.Sprintf(
			"%s?name=relation_method_to_workload&params=%s&params=%s",
			l.svcCtx.TTServiceUrl, in.ServiceName, in.InterfacePath,
		)
		req, err = http.NewRequest("GET", url, nil)
		req = req.WithContext(l.ctx)
		if err != nil {
			return err
		}

		defer func() {
			result := recover()
			if result != nil {
				logx.Errorf("relation precision testing recover result: %+v", result)
				err = errors.New(fmt.Sprintf("调用TTServiceUrl发生异常: %+v", result))
			} else {
				if res != nil && res.StatusCode == 200 {
					var data []byte

					defer res.Body.Close()
					data, err = io.ReadAll(res.Body)

					if err == nil {
						var tTResult TTResult
						err = json.Unmarshal(data, &tTResult)

						if err == nil {
							for _, entity := range tTResult.Entities {
								serviceName := entity.Name
								relationServices = append(relationServices, serviceName)
							}
							relationServiceMap := make(map[string]string)
							for _, s := range relationServices {
								relationServiceMap[s] = s
							}
							_, exist := relationServiceMap[in.ServiceName]
							if !exist {
								relationServices = append(relationServices, in.ServiceName)
							}
							l.RelationServices = relationServices

							relationServiceBytes, _ := json.Marshal(relationServices)
							relationServiceStr = string(relationServiceBytes)

							_, redisErr = redislock.NewRedisLockAndAcquire(
								l.svcCtx.Redis, redisKey, redislock.WithValue(relationServiceStr),
								redislock.WithExpire(24*time.Hour),
							)
							if redisErr != nil {
								logx.Errorf("getInterfacePathRelationService encounter redis error: %+v", redisErr)
							}
						}
					}
				}
			}
		}()

		retry := 0
		for { // 最多尝试3次，间隔1秒
			retry += 1
			if retry > 3 {
				break
			}

			res, err = client.Do(req)
			if err == nil {
				break
			}

			time.Sleep(1 * time.Second)
		}
	}

	return err
}

func (l *ServiceBindCaseLogic) getExistRelationMap(in *pb.BindTestCaseToServiceRequest) error {
	selectBuilder := l.svcCtx.ServiceCaseRelationModel.SelectBuilder()
	selectBuilder = selectBuilder.Where(
		"`project_id` = ? AND `general_config_id` = ? AND `interface_path` = ? AND `case_id` = ?",
		in.ProjectId, in.GeneralConfigId, in.InterfacePath, in.CaseId,
	)
	existRelations, err := l.svcCtx.ServiceCaseRelationModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return errorx.Err(errorx.DBError, fmt.Sprintf("failed to find exist service case relation, error: %+v", err))
	}
	existRelationMap := make(map[string]*model.ServiceCaseRelation, len(existRelations))
	for _, existRelation := range existRelations {
		existRelationMap[existRelation.RelationService] = existRelation
	}

	l.ExistRelationMap = existRelationMap

	return nil
}

func (l *ServiceBindCaseLogic) getAllRelations(in *pb.BindTestCaseToServiceRequest) {
	var updateRelations, insertRelations []*model.ServiceCaseRelation

	// 遍历所有关联的服务，存在则更新，不存在则插入
	for _, relationService := range l.RelationServices {
		relation, flag := l.ExistRelationMap[relationService]
		if flag { // 存在则更新
			relation.UpdatedAt = time.Now()
			updateRelations = append(updateRelations, relation)
		} else { // 不存在则插入
			insertRelation := &model.ServiceCaseRelation{
				ProjectId:       in.ProjectId,
				GeneralConfigId: in.GeneralConfigId,
				Service:         in.ServiceName,
				InterfacePath:   in.InterfacePath,
				Cmd: sql.NullString{
					String: in.Cmd,
					Valid:  in.Cmd != "",
				},
				CaseId:          in.CaseId,
				RelationService: relationService,
			}
			insertRelations = append(insertRelations, insertRelation)
		}
	}

	l.UpdateRelations = updateRelations
	l.InsertRelations = insertRelations
}

func (l *ServiceBindCaseLogic) realBindTestCaseToService() error {
	err := l.svcCtx.ServiceCaseRelationModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			for _, relation := range l.UpdateRelations {
				_, updateErr := l.svcCtx.ServiceCaseRelationModel.UpdateTX(context, session, relation)
				if updateErr != nil {
					return updateErr
				}
			}
			for _, relation := range l.InsertRelations {
				_, insertErr := l.svcCtx.ServiceCaseRelationModel.InsertTX(context, session, relation)
				if insertErr != nil {
					return insertErr
				}
			}
			return nil
		},
	)

	return err
}

func (l *ServiceBindCaseLogic) BindTestCaseToService(in *pb.BindTestCaseToServiceRequest) error {
	// 获取serviceName 和 interfacePath
	err := l.getServiceNameAndInterfacePath(in)
	if err != nil {
		return err
	}

	// 获取interfacePath背后调用的所有service(包含入口service)
	err = l.getInterfacePathRelationService(in)
	if err != nil {
		return err
	}

	// 获取所有已经存在的绑定
	err = l.getExistRelationMap(in)
	if err != nil {
		return err
	}

	// 算出需要更新绑定的服务列表、需要绑定的服务列表
	l.getAllRelations(in)

	// 执行真正的绑定或更新绑定操作
	err = l.realBindTestCaseToService()

	return err
}
