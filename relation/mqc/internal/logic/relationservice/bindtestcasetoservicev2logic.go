package relationservice

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

const (
	ApiProxyCommonCallUrl                  = "/router/v1/common/api/call"
	RedisKeyPrefixServiceNameInterfacePath = "service_name_interface_path"
	RedisKeyPrefixRelationInterfacePath    = "relation_interface_path"
)

type InterfacePathServiceInfo struct {
	ProjectId     string
	ServiceName   string
	InterfacePath string
}

// Deprecated: use “BindCaseToServiceLogic instead.
type ServiceBindCaseV2Logic struct {
	*BaseLogic

	RelationServices []string
	ExistRelationMap map[string]*model.ServiceCaseRelation
	UpdateRelations  []*model.ServiceCaseRelation
	InsertRelations  []*model.ServiceCaseRelation
}

func NewServiceBindCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ServiceBindCaseV2Logic {
	return &ServiceBindCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

type BindTestCaseToServiceReq struct {
	ClientType             string
	ProjectId              string
	GeneralConfigId        string
	ServiceName            string
	DeliveredInterfacePath string
	InterfacePath          string
	Cmd                    string
	CaseId                 string
}

func (l *ServiceBindCaseV2Logic) genBindTestCaseToServiceReq(in *pb.BindTestCaseToServiceV2Request) (
	*BindTestCaseToServiceReq, error,
) {
	// 请求url不是ApiProxy业务接口调用的直接跳过
	requestUrl := in.RequestUrl
	if !strings.Contains(requestUrl, ApiProxyCommonCallUrl) {
		return nil, nil
	}

	// 精准测试信息
	precisionInfoMap := make(map[string]string)
	err := json.Unmarshal(in.PrecisionInfo, &precisionInfoMap)
	if err != nil {
		err = fmt.Errorf("解析精准测试信息发生错误：%+v", err)
		return nil, err
	}

	probeProjectId := precisionInfoMap["probe_project_id"]
	probeGeneralConfigId := precisionInfoMap["probe_general_config_id"]
	probeTestCaseId := precisionInfoMap["probe_test_case_id"]
	// 没有传递必要的维护字段直接跳过
	if probeProjectId == "" || probeGeneralConfigId == "" || probeTestCaseId == "" {
		return nil, nil
	}

	requestBodyMap := make(map[string]any)
	err = json.Unmarshal(in.RequestBody, &requestBodyMap)
	// 请求体不是application/json
	if err != nil {
		err = fmt.Errorf("解析http请求体发生错误：%+v", err)
		return nil, err
	}

	// 请求体method填写为形如"ga.api.channel.ChannelLogic.GetUserAdminChannelList"， 即至少有2个.以上才是grpc path, 才需要处理
	methodAny, ok := requestBodyMap["method"]
	if !ok || methodAny == nil {
		return nil, nil
	}
	requestMethod, ok := methodAny.(string)
	if !ok || strings.Count(requestMethod, ".") < 2 {
		return nil, nil
	}

	// 响应体
	responseBodyMap := make(map[string]any)
	err = json.Unmarshal(in.ResponseBody, &responseBodyMap)
	if err != nil {
		err = fmt.Errorf("解析http请求响应体发生错误：%+v", err)
		return nil, err
	}

	// 需要判断响应体结构
	var clientType string
	data, flag := responseBodyMap["data"].(map[string]any)
	if flag && data != nil {
		clientInfo, flag2 := data["client_info"].(map[string]any)
		if flag2 && clientInfo != nil {
			clientType = clientInfo["type"].(string)
		}
	}

	var isTT bool
	if clientType == "tt" {
		isTT = true
	} else if strings.HasPrefix(clientType, "tt_") {
		isTT = true
	}
	if !isTT {
		return nil, nil
	}

	index := strings.LastIndex(requestMethod, ".")
	serviceFullName := requestMethod[:index]
	methodName := requestMethod[index+1:]
	deliveredInterfacePath := fmt.Sprintf("/%s/%s", serviceFullName, methodName)

	bindTestCaseToServiceReq := &BindTestCaseToServiceReq{
		ClientType:             clientType,
		ProjectId:              probeProjectId,
		GeneralConfigId:        probeGeneralConfigId,
		ServiceName:            "",
		DeliveredInterfacePath: deliveredInterfacePath,
		Cmd:                    "",
		CaseId:                 probeTestCaseId,
	}

	return bindTestCaseToServiceReq, nil
}

func (l *ServiceBindCaseV2Logic) getServiceNameAndInterfacePath(in *BindTestCaseToServiceReq) error {
	var serviceName, interfacePath string
	var req *http.Request
	var res *http.Response
	var err error

	client := &http.Client{}

	var interfacePathServiceInfo InterfacePathServiceInfo

	redisKey := fmt.Sprintf("%s_%s_%s", RedisKeyPrefixServiceNameInterfacePath, in.ProjectId, in.DeliveredInterfacePath)
	interfacePathServiceInfoStr, redisErr := l.svcCtx.Redis.Get(redisKey)

	if redisErr == nil && interfacePathServiceInfoStr != "" {
		err = json.Unmarshal([]byte(interfacePathServiceInfoStr), &interfacePathServiceInfo)
		if err == nil {
			in.ServiceName = interfacePathServiceInfo.ServiceName
			in.InterfacePath = interfacePathServiceInfo.InterfacePath
		}
	} else {
		url := fmt.Sprintf(
			"%s?name=relation_rewrite_bind_method&params=%s", l.svcCtx.TTServiceUrl, in.DeliveredInterfacePath,
		)
		req, err = http.NewRequest("GET", url, nil)
		if err != nil {
			return err
		}
		req = req.WithContext(l.ctx)

		defer func() {
			result := recover()
			if result != nil {
				logx.Errorf("relation precision testing recover result: %+v", result)
				err = errors.New(fmt.Sprintf("调用TTServiceUrl发生异常: %+v", result))
			} else {
				if res != nil && res.StatusCode == 200 {
					var data []byte

					defer res.Body.Close()
					data, err = io.ReadAll(res.Body)

					if err == nil {
						var tTResult TTResult
						err = json.Unmarshal(data, &tTResult)

						if len(tTResult.Entities) != 0 {
							interfacePath = tTResult.Entities[0].Name
							serviceName = tTResult.Entities[1].Name

							in.ServiceName = serviceName
							in.InterfacePath = interfacePath

							interfacePathServiceInfo = InterfacePathServiceInfo{
								ProjectId:     in.ProjectId,
								ServiceName:   serviceName,
								InterfacePath: interfacePath,
							}
							interfacePathServiceInfoBytes, _ := json.Marshal(interfacePathServiceInfo)
							interfacePathServiceInfoStr = string(interfacePathServiceInfoBytes)

							_, redisErr = redislock.NewRedisLockAndAcquire(
								l.svcCtx.Redis, redisKey, redislock.WithValue(interfacePathServiceInfoStr),
								redislock.WithExpire(24*time.Hour),
							)
							if redisErr != nil {
								logx.Errorf("getServiceNameAndInterfacePath encounter redis error: %+v", redisErr)
							}
						}
					}
				}
			}
		}()

		retry := 0
		for { // 最多尝试3次，间隔1秒
			retry += 1
			if retry > 3 {
				break
			}

			res, err = client.Do(req)
			if err == nil {
				break
			}

			time.Sleep(1 * time.Second)
		}
	}

	return err
}

func (l *ServiceBindCaseV2Logic) getServiceNameAndInterfacePath2(in *BindTestCaseToServiceReq) error {
	var req *http.Request
	var res *http.Response
	var err error

	client := &http.Client{}

	var interfacePathServiceInfo InterfacePathServiceInfo

	redisKey := fmt.Sprintf(
		"%s_%s_%s_%s", RedisKeyPrefixServiceNameInterfacePath,
		in.ProjectId, in.DeliveredInterfacePath, in.GeneralConfigId,
	)
	interfacePathServiceInfoStr, redisErr := l.svcCtx.Redis.Get(redisKey)

	if redisErr == nil && interfacePathServiceInfoStr != "" {
		err = json.Unmarshal([]byte(interfacePathServiceInfoStr), &interfacePathServiceInfo)
		if err == nil {
			in.ServiceName = interfacePathServiceInfo.ServiceName
			in.InterfacePath = interfacePathServiceInfo.InterfacePath
		}
	} else {
		url := fmt.Sprintf(
			"%s?name=relation_method_bind_workload&params=%s", l.svcCtx.TTServiceUrl, in.DeliveredInterfacePath,
		)
		req, err = http.NewRequest("GET", url, nil)
		req = req.WithContext(l.ctx)
		if err != nil {
			return err
		}

		defer func() {
			result := recover()
			if result != nil {
				logx.Errorf("relation precision testing recover result: %+v", result)
				err = errors.New(fmt.Sprintf("调用TTServiceUrl发生异常: %+v", result))
			} else {
				if res != nil && res.StatusCode == 200 {
					var data []byte

					defer res.Body.Close()
					data, err = io.ReadAll(res.Body)

					if err == nil {
						var tTResult TTResult
						err = json.Unmarshal(data, &tTResult)

						if err == nil {
							var shortestServiceName string
							for idx, entity := range tTResult.Entities {
								if idx == 0 {
									shortestServiceName = entity.Name
								} else {
									if len(entity.Name) < len(shortestServiceName) {
										shortestServiceName = entity.Name
									}
								}
							}

							in.ServiceName = shortestServiceName
							in.InterfacePath = in.DeliveredInterfacePath

							interfacePathServiceInfo = InterfacePathServiceInfo{
								ProjectId:     in.ProjectId,
								ServiceName:   shortestServiceName,
								InterfacePath: in.DeliveredInterfacePath,
							}
							interfacePathServiceInfoBytes, _ := json.Marshal(interfacePathServiceInfo)
							interfacePathServiceInfoStr = string(interfacePathServiceInfoBytes)

							_, redisErr = redislock.NewRedisLockAndAcquire(
								l.svcCtx.Redis, redisKey, redislock.WithValue(interfacePathServiceInfoStr),
								redislock.WithExpire(24*time.Hour),
							)
							if redisErr != nil {
								logx.Errorf("getServiceNameAndInterfacePath encounter redis error: %+v", redisErr)
							}
						}
					}
				}
			}
		}()

		retry := 0
		for { // 最多尝试3次，间隔1秒
			retry += 1
			if retry > 3 {
				break
			}

			res, err = client.Do(req)
			if err == nil {
				break
			}

			time.Sleep(1 * time.Second)
		}
	}

	return err
}

func (l *ServiceBindCaseV2Logic) getInterfacePathRelationService(in *BindTestCaseToServiceReq) error {
	var relationServices []string
	var req *http.Request
	var res *http.Response
	var err error

	client := &http.Client{}

	redisKey := fmt.Sprintf(
		"%s_%s_%s_%s_%s", RedisKeyPrefixRelationInterfacePath,
		in.ProjectId, in.ServiceName, in.InterfacePath, in.GeneralConfigId,
	)
	relationServiceStr, redisErr := l.svcCtx.Redis.Get(redisKey)

	if redisErr == nil && relationServiceStr != "" {
		err = json.Unmarshal([]byte(relationServiceStr), &relationServices)
		if err == nil {
			l.RelationServices = relationServices
		}
	} else {
		url := fmt.Sprintf(
			"%s?name=relation_method_to_workload&params=%s&params=%s",
			l.svcCtx.TTServiceUrl, in.ServiceName, in.InterfacePath,
		)
		req, err = http.NewRequest("GET", url, nil)
		req = req.WithContext(l.ctx)
		if err != nil {
			return err
		}

		defer func() {
			result := recover()
			if result != nil {
				logx.Errorf("relation precision testing recover result: %+v", result)
				err = errors.New(fmt.Sprintf("调用TTServiceUrl发生异常: %+v", result))
			} else {
				if res != nil && res.StatusCode == 200 {
					var data []byte

					defer res.Body.Close()
					data, err = io.ReadAll(res.Body)

					if err == nil {
						var tTResult TTResult
						err = json.Unmarshal(data, &tTResult)

						if err == nil {
							for _, entity := range tTResult.Entities {
								serviceName := entity.Name
								relationServices = append(relationServices, serviceName)
							}
							relationServiceMap := make(map[string]string)
							for _, s := range relationServices {
								relationServiceMap[s] = s
							}
							_, exist := relationServiceMap[in.ServiceName]
							if !exist {
								relationServices = append(relationServices, in.ServiceName)
							}
							l.RelationServices = relationServices

							relationServiceBytes, _ := json.Marshal(relationServices)
							relationServiceStr = string(relationServiceBytes)

							_, redisErr = redislock.NewRedisLockAndAcquire(
								l.svcCtx.Redis, redisKey, redislock.WithValue(relationServiceStr),
								redislock.WithExpire(24*time.Hour),
							)
							if redisErr != nil {
								logx.Errorf("getServiceNameAndInterfacePath2 encounter redis error: %+v", redisErr)
							}
						}
					}
				}
			}
		}()

		retry := 0
		for { // 最多尝试3次，间隔1秒
			retry += 1
			if retry > 3 {
				break
			}

			res, err = client.Do(req)
			if err == nil {
				break
			}

			time.Sleep(1 * time.Second)
		}
	}

	return err
}

func (l *ServiceBindCaseV2Logic) getExistRelationMap(in *BindTestCaseToServiceReq) error {
	selectBuilder := l.svcCtx.ServiceCaseRelationModel.SelectBuilder()
	selectBuilder = selectBuilder.Where(
		"`project_id` = ? AND `general_config_id` = ? AND `interface_path` = ? AND `case_id` = ?",
		in.ProjectId, in.GeneralConfigId, in.InterfacePath, in.CaseId,
	)
	existRelations, err := l.svcCtx.ServiceCaseRelationModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return errorx.Err(errorx.DBError, fmt.Sprintf("failed to find exist service case relation, error: %+v", err))
	}
	existRelationMap := make(map[string]*model.ServiceCaseRelation, len(existRelations))
	for _, existRelation := range existRelations {
		existRelationMap[existRelation.RelationService] = existRelation
	}

	l.ExistRelationMap = existRelationMap

	return nil
}

func (l *ServiceBindCaseV2Logic) getAllRelations(in *BindTestCaseToServiceReq) {
	var updateRelations, insertRelations []*model.ServiceCaseRelation

	// 遍历所有关联的服务，存在则更新，不存在则插入
	for _, relationService := range l.RelationServices {
		relation, flag := l.ExistRelationMap[relationService]
		if flag { // 存在则更新
			relation.UpdatedAt = time.Now()
			updateRelations = append(updateRelations, relation)
		} else { // 不存在则插入
			insertRelation := &model.ServiceCaseRelation{
				ProjectId:       in.ProjectId,
				GeneralConfigId: in.GeneralConfigId,
				Service:         in.ServiceName,
				InterfacePath:   in.InterfacePath,
				Cmd: sql.NullString{
					String: in.Cmd,
					Valid:  in.Cmd != "",
				},
				CaseId:          in.CaseId,
				RelationService: relationService,
			}
			insertRelations = append(insertRelations, insertRelation)
		}
	}

	l.UpdateRelations = updateRelations
	l.InsertRelations = insertRelations
}

func (l *ServiceBindCaseV2Logic) realBindTestCaseToService() error {
	err := l.svcCtx.ServiceCaseRelationModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			for _, relation := range l.UpdateRelations {
				_, updateErr := l.svcCtx.ServiceCaseRelationModel.UpdateTX(context, session, relation)
				if updateErr != nil {
					return updateErr
				}
			}
			for _, relation := range l.InsertRelations {
				_, insertErr := l.svcCtx.ServiceCaseRelationModel.InsertTX(context, session, relation)
				if insertErr != nil {
					return insertErr
				}
			}
			return nil
		},
	)

	return err
}

func (l *ServiceBindCaseV2Logic) BindTestCaseToService(in *pb.BindTestCaseToServiceV2Request) error {
	l.Infof("bind test case to service, payload: %s", protobuf.MarshalJSONIgnoreError(in))

	// 从请求参数生成BindTestCaseToServiceReq类型对象
	bindTestCaseToServiceReq, err := l.genBindTestCaseToServiceReq(in)
	if err != nil {
		return err
	}

	l.Infof("bind test case to service, req: %s", jsonx.MarshalIgnoreError(bindTestCaseToServiceReq))

	if bindTestCaseToServiceReq == nil {
		return nil
	}

	// 尝试获取serviceName和interfacePath，并赋值到bindTestCaseToServiceReq
	err = l.getServiceNameAndInterfacePath(bindTestCaseToServiceReq)
	if err != nil {
		return err
	}

	// 换个接口获取serviceName和interfacePath，并赋值到bindTestCaseToServiceReq
	if bindTestCaseToServiceReq.ServiceName == "" || bindTestCaseToServiceReq.InterfacePath == "" { // 说明deliveredInterfacePath其实就是interfacePath, 需要再获取serviceName
		err = l.getServiceNameAndInterfacePath2(bindTestCaseToServiceReq)
		if err != nil {
			return err
		}
	}

	// 还是没能获取到serviceName和interfacePath
	if bindTestCaseToServiceReq.ServiceName == "" || bindTestCaseToServiceReq.InterfacePath == "" {
		return nil
	}

	// 获取interfacePath背后调用的所有service(包含入口service)
	err = l.getInterfacePathRelationService(bindTestCaseToServiceReq)
	if err != nil {
		return err
	}

	// 获取所有已经存在的绑定
	err = l.getExistRelationMap(bindTestCaseToServiceReq)
	if err != nil {
		return err
	}

	// 算出需要更新绑定的服务列表、需要绑定的服务列表
	l.getAllRelations(bindTestCaseToServiceReq)

	// 执行真正的绑定或更新绑定操作
	err = l.realBindTestCaseToService()

	return err
}
