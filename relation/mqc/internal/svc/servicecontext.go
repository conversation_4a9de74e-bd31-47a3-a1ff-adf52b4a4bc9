package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/tlink"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis        *redis.Redis
	TTServiceUrl string // Deprecated: use `TLinkClient` instead.
	Consumer     *consumerv2.Consumer

	TLinkClient *tlink.Client

	ServiceCaseRelationModel   model.ServiceCaseRelationModel // Deprecated: use `ServiceCaseRelationV2Model` instead.
	ServiceCaseRelationV2Model model.ServiceCaseRelationV2Model
	ServiceMethodRelationModel model.ServiceMethodRelationModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	return &ServiceContext{
		Config: c,

		Redis:        redis.MustNewRedis(c.Redis, redis.WithDB(c.Redis.DB)),
		TTServiceUrl: c.TTServiceUrl,
		Consumer:     consumerv2.NewConsumer(c.Consumer),

		ServiceCaseRelationModel:   model.NewServiceCaseRelationModel(sqlConn, c.Cache),
		ServiceCaseRelationV2Model: model.NewServiceCaseRelationV2Model(sqlConn, c.Cache),
		ServiceMethodRelationModel: model.NewServiceMethodRelationModel(sqlConn, c.Cache),

		TLinkClient: tlink.NewClient(c.TLink),
	}
}
