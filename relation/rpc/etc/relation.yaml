Name: rpc.relation
Mode: test
ListenOn: 127.0.0.1:20711
Timeout: 0

Log:
  ServiceName: rpc.relation
  Encoding: plain
  Level: info
  Path: /app/logs/relation

#Prometheus:
#  Host: 0.0.0.0
#  Port: 20722
#  Path: /metrics
#
#Telemetry:
#  Name: rpc.relation
#  Endpoint: http://127.0.0.1:14268/api/traces
#  Sampler: 1.0
#  Batcher: jaeger

DevServer:
  Enabled: true
  Port: 20732

Redis:
  Key: rpc.relation
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 16

Cache:
  - Host: 127.0.0.1:6379
    Pass:
    DB: 16

DB:
#  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/relation?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
  DataSource: probe:Quwan@2020_TTinternation@tcp(************:3306)/relation?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Producer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:relation
  Db: 16

GitLab:
  Token: ********************

TLink:
  BaseURL: https://testing-internal-api.ttyuyin.com

Sentinel:
  BaseURL: https://cicd.ttyuyin.com
  Token: 'cXVhbGl0eQ==.51448796d22758fce4a7928d613fcef3fdcd604aa60259227d9ac0fc0dcb0f5c'

AppInsight:
  BaseURL: http://tt-telemetry-web.ttyuyin.com

TTAppProto:
  GitURL: https://gitlab.ttyuyin.com/tt-protocols/app
  Branch: develop
  TargetPath: /app/data/protos/tt/develop
  UpdateCron: "0 6,12,19 * * MON-FRI" # every weekday 6:00,12:00,19:00

ServiceCaseCleaner:
  CronExpression: "30 2 * * ?" # every day 2:30
  KeepDays: 14
