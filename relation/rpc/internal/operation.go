package internal

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/serverinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
)

var (
	noNeedUserMethods = []string{
		"/relation.RelationService/BindTestCaseToService",
		"/relation.RelationService/BindTestCaseToServiceV2",
		"/relation.RelationService/GetServiceBindTestCase",
		"/relation.RelationService/GetServiceByMethod",
	}
)

func InitOperation(svcCtx *svc.ServiceContext) error {
	// register no need user full methods for user info server interceptor
	registerNoNeedUserFullMethod()

	// register scheduled tasks
	if err := registerScheduledTasks(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register scheduled tasks")
	}

	return nil
}

func registerNoNeedUserFullMethod() {
	for _, fullMethod := range noNeedUserMethods {
		serverinterceptors.NoNeedUserForFullMethod(fullMethod)
	}
}

func registerScheduledTasks(svcCtx *svc.ServiceContext) error {
	if err := svcCtx.Scheduler.RegisterTasks(
		map[string]func(){
			svcCtx.Config.TTAppProto.UpdateCron: func() {
				if err := newUpdateRelationHandler(svcCtx).Update(); err != nil {
					logx.Errorf("failed to update relation, error: %+v", err)
				}
			},
			svcCtx.Config.ServiceCaseCleaner.CronExpression: func() {
				if err := newCleanRelationHandler(svcCtx).Clean(); err != nil {
					logx.Errorf("failed to clean relation, error: %+v", err)
				}
			},
		},
	); err != nil {
		return err
	}

	svcCtx.Scheduler.Start()
	proc.AddShutdownListener(svcCtx.Scheduler.Stop)

	return nil
}
