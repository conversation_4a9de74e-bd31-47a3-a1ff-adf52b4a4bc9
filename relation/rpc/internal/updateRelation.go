package internal

import (
	"context"
	"database/sql"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/tt"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
)

const (
	lockKeyOfUpdate = "lock:relation:updateRelation:update"
	chanKeyOfUpdate = "chan:relation:updateRelation:update"

	expireOfUpdateTask = 10 * time.Minute

	constSlash         = "/"
	constDotGit        = ".git"
	constUpdateWorkers = 100
	constMRMaxWorkers  = 16
)

var systemUser = userinfo.System()

type (
	UpdateRelationHandler struct {
		logx.Logger
		ctx    context.Context
		svcCtx *svc.ServiceContext

		product protobuf.Product
		pm      *protobuf.ProtoManager
	}

	serviceNamespace struct {
		service   string
		namespace string
	}
)

func newUpdateRelationHandler(svcCtx *svc.ServiceContext) *UpdateRelationHandler {
	ctx := context.Background()
	return &UpdateRelationHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (h *UpdateRelationHandler) Update() error {
	var cancel context.CancelFunc
	h.ctx, cancel = context.WithTimeout(h.ctx, expireOfUpdateTask)
	defer cancel()

	key := lockKeyOfUpdate
	fn := func() error {
		if err := h.cloneOrUpdateProto(); err != nil {
			h.Errorf("failed to clone or update proto, key: %s, error: %+v", key, err)
		}

		if err := h.updateRelations(); err != nil {
			return err
		}

		return nil
	}
	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(expireOfUpdateTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to update service and method relation, key: %s, error: %+v", key, err)
			return err
		}

		h.Infof("another service is executing the update task of TT services and methods, key: %s", key)
	} else {
		h.Infof("finished to update service and method relation, key: %s", key)
	}

	return nil
}

func (h *UpdateRelationHandler) cloneOrUpdateProto() error {
	var (
		token  = h.svcCtx.Config.GitLab.Token
		gitURL = h.svcCtx.Config.TTAppProto.GitURL
		branch = h.svcCtx.Config.TTAppProto.Branch
		target = h.svcCtx.Config.TTAppProto.TargetPath

		u      *url.URL
		commit *object.Commit
		err    error
	)

	u, err = url.Parse(gitURL)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"failed to parse the git url, url: %s, error: %+v",
			gitURL, err,
		)
	} else if _, ok := u.User.Password(); !ok || u.User.Username() == "" {
		u.User = url.UserPassword(constants.ConstDefaultGitUsername, token)
	}
	gitURLWithAuth := u.String()

	path := filepath.Join(target, getLastPathFromURL(u))
	_, err = os.Stat(path)
	if err != nil && !os.IsNotExist(err) {
		// if it is not a 'not exist' error, return directly
		return errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"failed to get the FileInfo of the path, path: %s, error: %+v",
			path, err,
		)
	} else if os.IsNotExist(err) {
		// target path doesn't exist, clone the git repo from remote
		h.Infof("begin to clone the git repo, git: %s, path: %s, branch: %s", gitURLWithAuth, path, branch)
		commit, err = utils.CloneWithContext(h.ctx, gitURLWithAuth, path, branch, utils.WithCloneUseCommand())
	} else {
		// target path exists, pull the git repo from remote
		h.Infof("begin to pull the git repo, git: %s, path: %s, branch: %s", gitURLWithAuth, path, branch)
		commit, err = utils.PullWithContext(h.ctx, path, utils.WithPullUseCommand())
	}
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.GitOperationFailure, err.Error()),
			"failed to clone or pull the git repo, git: %s, branch: %s, path: %s, error: %+v",
			gitURL, branch, path, err,
		)
	}

	h.Infof(
		"finish to clone or pull the git repo, git: %s, path: %s, branch: %s, commit: %q",
		gitURL, path, branch, commit.String(),
	)

	h.product = protobuf.Product{
		Name:   "",
		Branch: branch,
		Projects: []protobuf.Project{
			{
				Name:   filepath.Base(path),
				Branch: branch,
				Path:   path,
			},
		},
	}

	return nil
}

func (h *UpdateRelationHandler) updateRelations() error {
	var (
		now      = time.Now()
		lastDate = now.Add(-24 * time.Hour)
	)

	if err := h.newProtobufManager(); err != nil {
		return err
	}

	services, err := h.updateServiceMethodRelations(now)
	if err != nil {
		return err
	}

	if err = h.updateServiceTeamRelations(services, now); err != nil {
		return err
	}

	h.deletedRelationsByLastDate(lastDate)
	return nil
}

func (h *UpdateRelationHandler) newProtobufManager() error {
	var err error
	h.pm, err = protobuf.NewProtoManager(protobuf.WithProducts(h.product))
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.NewObjectFailure, err.Error()),
			"failed to new a protobuf manager, protobuf product: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(h.product), err,
		)
	}

	return nil
}

func (h *UpdateRelationHandler) updateServiceMethodRelations(now time.Time) (map[string]*serviceNamespace, error) {
	return mr.MapReduce[protoreflect.MethodDescriptor, *model.ServiceMethodRelation, map[string]*serviceNamespace](
		func(source chan<- protoreflect.MethodDescriptor) {
			if err := h.pm.RangeMethodDescriptors(
				func(md protoreflect.MethodDescriptor) bool {
					source <- md
					return true
				}, protobuf.WithProductBranch(h.svcCtx.Config.TTAppProto.Branch),
			); err != nil {
				h.Errorf("failed to range the method descriptors, error: %+v", err)
			}
		},
		func(item protoreflect.MethodDescriptor, writer mr.Writer[*model.ServiceMethodRelation], cancel func(error)) {
			var (
				reqMethod   string
				queryMethod string
				service     string
				namespace   string

				lo = tt.GetLogicOptions(item)
			)

			for _, method := range []string{lo.OriginMethod, lo.RewriteMethod} {
				if len(reqMethod) > 0 && len(queryMethod) > 0 {
					break
				}

				entities, err := h.svcCtx.TLinkClient.QueryServicesByMethod(constants.MethodTypeOfGRPC, method)
				if err != nil {
					h.Errorf("failed to query the service by method, method: %s, error: %+v", method, err)
					continue
				}

				for _, entity := range entities {
					if entity.Name == "" || entity.Properties.Namespace == "" {
						h.Errorf(
							"the service associated with the method is empty, method: %s, entity: %s",
							method, jsonx.MarshalIgnoreError(entity),
						)
						continue
					}

					if len(reqMethod) == 0 {
						reqMethod = method
						service = entity.Name
						namespace = entity.Properties.Namespace
					}

					rsp, err := h.svcCtx.AppInsightClient.QueryRangeMetrics(
						&appInsight.QueryRangeMetricsReq{
							Metric:    appInsight.MetricTypeOfQPS,
							Workload:  entity.Name,
							Namespace: entity.Properties.Namespace,
							Method:    method,
							StartedAt: now.Add(-5 * time.Minute),
							EndedAt:   now,
						},
					)
					if err != nil {
						h.Errorf("failed to query the metrics by method, method: %s, error: %+v", method, err)
						continue
					}

					if len(rsp.Points) > 0 {
						queryMethod = method
						service = entity.Name
						namespace = entity.Properties.Namespace
						break
					}
				}
			}
			if len(reqMethod) > 0 && len(service) > 0 && len(namespace) > 0 {
				smr, err := h.generateServiceMethodRelation(
					service, namespace, reqMethod, string(item.FullName()), queryMethod, lo, now,
				)
				if err == nil && smr != nil {
					writer.Write(smr)
				}
			}
		},
		func(
			pipe <-chan *model.ServiceMethodRelation, writer mr.Writer[map[string]*serviceNamespace],
			cancel func(error),
		) {
			services := make(map[string]*serviceNamespace, constants.ConstDefaultMakeMapSize)
			for item := range pipe {
				if item == nil {
					continue
				}

				key := item.Service + "." + item.Namespace
				if _, ok := services[key]; !ok {
					services[key] = &serviceNamespace{
						service:   item.Service,
						namespace: item.Namespace,
					}
				}

				var err error
				if item.Id == 0 {
					_, err = h.svcCtx.ServiceMethodRelationModel.InsertTX(h.ctx, nil, item)
				} else {
					_, err = h.svcCtx.ServiceMethodRelationModel.UpdateTX(h.ctx, nil, item)
				}
				if err != nil {
					h.Errorf(
						"failed to save the service method relation, service: %s, namespace: %s, method: %s, error: %+v",
						item.Service, item.Namespace, item.Method, err,
					)
				}
			}

			writer.Write(services)
		},
		mr.WithContext(h.ctx), mr.WithWorkers(constMRMaxWorkers),
	)
}

func (h *UpdateRelationHandler) updateServiceTeamRelations(
	services map[string]*serviceNamespace, now time.Time,
) error {
	return mr.MapReduceVoid[*serviceNamespace, *model.ServiceTeamRelation](
		func(source chan<- *serviceNamespace) {
			for _, v := range services {
				source <- v
			}
		},
		func(item *serviceNamespace, writer mr.Writer[*model.ServiceTeamRelation], cancel func(error)) {
			if item == nil {
				return
			}

			str, err := h.generateServiceTeamRelation(item.service, item.namespace, now)
			if err == nil && str != nil {
				writer.Write(str)
			}
		},
		func(pipe <-chan *model.ServiceTeamRelation, cancel func(error)) {
			for item := range pipe {
				if item == nil {
					continue
				}

				var err error
				if item.Id == 0 {
					_, err = h.svcCtx.ServiceTeamRelationModel.InsertTX(h.ctx, nil, item)
				} else {
					_, err = h.svcCtx.ServiceTeamRelationModel.UpdateTX(h.ctx, nil, item)
				}
				if err != nil {
					h.Errorf(
						"failed to save the service team relation, service: %s, namespace: %s, team: %s, error: %+v",
						item.Service, item.Namespace, item.TeamName, err,
					)
				}
			}
		},
		mr.WithContext(h.ctx),
	)
}

func (h *UpdateRelationHandler) generateServiceMethodRelation(
	service, namespace, method, methodFullName, queryMethod string, lo tt.LogicOptions, now time.Time,
) (*model.ServiceMethodRelation, error) {
	r, err := h.svcCtx.ServiceMethodRelationModel.FindOneByServiceNamespaceMethod(
		h.ctx, service, namespace, methodFullName,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		h.Errorf(
			"failed to find the service method relation, service: %s, namespace: %s, method: %s, error: %+v",
			service, namespace, methodFullName, err,
		)
		return nil, err
	} else if errors.Is(err, model.ErrNotFound) {
		r = &model.ServiceMethodRelation{
			Service:   service,
			Namespace: namespace,
			Type:      string(constants.MethodTypeOfGRPC),
			Method:    methodFullName,
			ReqPath:   method,
			QueryPath: sql.NullString{
				String: queryMethod,
				Valid:  queryMethod != "",
			},
			Cmd: sql.NullInt64{
				Int64: int64(lo.MethodOptions.ID),
				Valid: lo.MethodOptions.ID > 0,
			},
			Deprecated: cast.ToInt64(lo.MethodOptions.Deprecated),
			CreatedBy:  systemUser.Account,
			UpdatedBy:  systemUser.Account,
			CreatedAt:  now,
			UpdatedAt:  now,
		}
	} else if r != nil {
		r.ReqPath = method
		r.QueryPath = sql.NullString{
			String: queryMethod,
			Valid:  queryMethod != "",
		}
		r.Cmd = sql.NullInt64{
			Int64: int64(lo.MethodOptions.ID),
			Valid: lo.MethodOptions.ID > 0,
		}
		r.Deprecated = cast.ToInt64(lo.MethodOptions.Deprecated)
		r.UpdatedBy = systemUser.Account
		r.UpdatedAt = now
	}

	return r, nil
}

func (h *UpdateRelationHandler) generateServiceTeamRelation(
	service, namespace string, now time.Time,
) (*model.ServiceTeamRelation, error) {
	// query sentinel project by service
	apps, err := h.svcCtx.SentinelClient.GetAppsByName(service)
	if err != nil {
		return nil, err
	} else if len(apps) == 0 {
		return nil, errorx.Errorf(errorx.NotExists, "not found any apps by name, service: %s", service)
	} else if len(apps) > 1 {
		h.Warnf("got more than one app by name, service: %s, apps: %s", service, jsonx.MarshalIgnoreError(apps))
	}

	app := apps[0]
	r, err := h.svcCtx.ServiceTeamRelationModel.FindOneByServiceNamespaceTeamName(
		h.ctx, service, namespace, app.ProjectName,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		h.Errorf(
			"failed to find the service team relation, service: %s, namespace: %s, team_name: %s, error: %+v",
			service, namespace, app.ProjectName, err,
		)
		return nil, err
	} else if errors.Is(err, model.ErrNotFound) {
		r = &model.ServiceTeamRelation{
			Service:   service,
			Namespace: namespace,
			TeamName:  app.ProjectName,
			TeamDescription: sql.NullString{
				String: app.ProjectDescription,
				Valid:  app.ProjectDescription != "",
			},
			CreatedBy: systemUser.Account,
			UpdatedBy: systemUser.Account,
			CreatedAt: now,
			UpdatedAt: now,
		}
	} else if r != nil {
		r.TeamName = app.ProjectName
		r.TeamDescription = sql.NullString{
			String: app.ProjectDescription,
			Valid:  app.ProjectDescription != "",
		}
		r.UpdatedBy = systemUser.Account
		r.UpdatedAt = now
	}

	return r, nil
}

func (h *UpdateRelationHandler) deletedRelationsByLastDate(lastDate time.Time) {
	if err := h.svcCtx.ServiceMethodRelationModel.DeleteByUpdatedAt(h.ctx, nil, lastDate); err != nil {
		h.Errorf(
			"failed to delete the service method relation by updated at, updated_at: %s, error: %+v",
			lastDate.Format("2006-01-02 15:04:05"), err,
		)
	}
	if err := h.svcCtx.ServiceTeamRelationModel.DeleteByUpdatedAt(h.ctx, nil, lastDate); err != nil {
		h.Errorf(
			"failed to delete the service team relation by updated at, updated_at: %s, error: %+v",
			lastDate.Format("2006-01-02 15:04:05"), err,
		)
	}
}

func getLastPathFromURL(u *url.URL) string {
	path := u.Path

	// 移除开头的斜杠
	path = strings.TrimLeft(path, constSlash)

	// 移除`.git`扩展名
	path = strings.TrimSuffix(path, constDotGit)

	// 分割路径，获取最后一个部分，即目录名称
	parts := strings.Split(path, constSlash)
	return parts[len(parts)-1]
}
