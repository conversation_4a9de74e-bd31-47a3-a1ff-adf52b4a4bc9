package svc

import (
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/config"
)

func Mock_ServiceContext() *ServiceContext {
	mock.Mock_Log()

	source := "probe:Quwan@2020_TTinternation@tcp(10.64.240.41:3306)/relation?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"
	mysqlCli := sqlx.NewMysql(source)

	conf := config.Config{
		RpcServerConf: zrpc.RpcServerConf{
			ServiceConf: service.ServiceConf{
				Name: "rpc.relation",
			},
		},
		Cache: cache.CacheConf{
			{
				RedisConf: mock.Mock_RedisConf(),
				Weight:    100,
			},
		},
	}

	svc := &ServiceContext{
		Config: conf,

		Redis:                      redis.MustNewRedis(mock.Mock_RedisConf(), redis.WithDB(mock.Mock_RedisConf().DB)),
		ServiceCaseRelationModel:   model.NewServiceCaseRelationModel(mysqlCli, conf.Cache),
		ServiceCaseRelationV2Model: model.NewServiceCaseRelationV2Model(mysqlCli, conf.Cache),
		ServiceMethodRelationModel: model.NewServiceMethodRelationModel(mysqlCli, conf.Cache),
	}

	return svc
}
