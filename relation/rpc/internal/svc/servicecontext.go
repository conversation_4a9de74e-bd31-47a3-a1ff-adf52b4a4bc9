package svc

import (
	"time"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/cronscheduler"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sentinel"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/tlink"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis     *redis.Redis
	Scheduler *cronscheduler.Scheduler

	ServiceCaseRelationModel   model.ServiceCaseRelationModel // Deprecated: use `ServiceCaseRelationV2Model` instead.
	ServiceCaseRelationV2Model model.ServiceCaseRelationV2Model
	ServiceMethodRelationModel model.ServiceMethodRelationModel
	ServiceTeamRelationModel   model.ServiceTeamRelationModel

	Producer *producer.Producer

	TLinkClient      *tlink.Client
	SentinelClient   *sentinel.Client
	AppInsightClient *appInsight.Client
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	scheduler := cronscheduler.NewScheduler()
	scheduler.Scheduler.ChangeLocation(time.Local) // change to local timezone

	return &ServiceContext{
		Config: c,

		Redis:     redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.DB)),
		Scheduler: scheduler,

		ServiceCaseRelationModel:   model.NewServiceCaseRelationModel(sqlConn, c.Cache),
		ServiceCaseRelationV2Model: model.NewServiceCaseRelationV2Model(sqlConn, c.Cache),
		ServiceMethodRelationModel: model.NewServiceMethodRelationModel(sqlConn, c.Cache),
		ServiceTeamRelationModel:   model.NewServiceTeamRelationModel(sqlConn, c.Cache),

		Producer: producer.NewProducer(c.Producer),

		TLinkClient:      tlink.NewClient(c.TLink),
		SentinelClient:   sentinel.NewClient(c.Sentinel),
		AppInsightClient: appInsight.NewClient(c.AppInsight),
	}
}
