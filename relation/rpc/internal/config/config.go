package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qettypes "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sentinel"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/tlink"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type Config struct {
	zrpc.RpcServerConf

	DB    qettypes.DBConfig
	Cache cache.CacheConf

	Producer producer.Config

	GitLab             qettypes.GitLabConfig
	TLink              tlink.Config
	Sentinel           sentinel.Config
	AppInsight         appInsight.Config
	TTAppProto         TTAppProto
	ServiceCaseCleaner commontypes.ClearStrategy
}

type TTAppProto struct {
	GitURL     string
	Branch     string `json:",default=main"`
	TargetPath string
	UpdateCron string
}

func (c Config) ListenOn() string {
	return c.RpcServerConf.ListenOn
}

func (c Config) LogConfig() logx.LogConf {
	return c.RpcServerConf.ServiceConf.Log
}
