package logic

import (
	"database/sql"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

func MethodTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.MethodType_MT_UNKNOWN)
}

func StringToMethodType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.MethodType_MT_UNKNOWN)
}

func SQLNullInt64ToCmd() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: utils.SQLNullInt64,
		DstType: utils.Uint32,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullInt64); !ok || !s.Valid {
				return uint32(0), nil
			} else {
				return uint32(s.Int64), nil
			}
		},
	}
}
