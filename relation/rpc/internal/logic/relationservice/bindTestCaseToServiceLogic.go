package relationservicelogic

import (
	"context"
	"fmt"

	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

// Deprecated: use `BindTestCaseToServiceV2Logic` instead.
type BindTestCaseToServiceLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewBindTestCaseToServiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BindTestCaseToServiceLogic {
	return &BindTestCaseToServiceLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *BindTestCaseToServiceLogic) BindTestCaseToService(in *pb.BindTestCaseToServiceRequest) (
	out *pb.BindTestCaseToServiceResponse, err error,
) {
	arg, err := protobuf.MarshalMessage(in)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "绑定服务与测试用例任务参数序列化失败, error: %s", err,
		)
	}

	taskSign := &tasks.Signature{
		UUID:       l.getUUID(),
		RoutingKey: l.svcCtx.Config.Producer.Queue,
		Name:       constants.MQTaskTypeRelationBindTestCaseToService,
		Args: []tasks.Arg{
			{Value: arg, Type: "[]byte"},
		},
	}

	// 发布任务
	_, err = l.svcCtx.Producer.AsyncPush(l.ctx, taskSign, l.svcCtx.Config.Name)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.BindTestCaseToServiceResponse{}, nil
}

func (l *BindTestCaseToServiceLogic) getUUID() string {
	return fmt.Sprintf("precise_test::%s", uuid.New().String())
}
