package relationservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetAllServiceMethodsLogic struct {
	*BaseLogic
}

func NewGetAllServiceMethodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAllServiceMethodsLogic {
	return &GetAllServiceMethodsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetAllServiceMethods 获取全部服务与接口关系
func (l *GetAllServiceMethodsLogic) GetAllServiceMethods(in *pb.GetAllServiceMethodsReq) (out *pb.GetAllServiceMethodsResp, err error) {
	relations, err := l.svcCtx.ServiceMethodRelationModel.FindAll(l.ctx)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find service method relation, error: %+v",
			err,
		)
	} else if len(relations) == 0 {
		l.Warn("not found any service method relations")
		return &pb.GetAllServiceMethodsResp{Relations: []*pb.ServiceMethodRelation{}}, nil
	}

	out = &pb.GetAllServiceMethodsResp{Relations: make([]*pb.ServiceMethodRelation, 0, len(relations))}
	for _, relation := range relations {
		item := &pb.ServiceMethodRelation{}
		if err = utils.Copy(item, relation, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to copy service method relation, relation: %s, error: %+v",
				jsonx.MarshalIgnoreError(relation), err,
			)
		}

		out.Relations = append(out.Relations, item)
	}

	return out, nil
}
