package relationservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type GetServiceByMethodLogic struct {
	*BaseLogic
}

func NewGetServiceByMethodLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetServiceByMethodLogic {
	return &GetServiceByMethodLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetServiceByMethodLogic) GetServiceByMethod(in *pb.GetServiceByMethodReq) (
	out *pb.GetServiceByMethodResp, err error,
) {
	method := in.GetMethod()

	relations, err := l.svcCtx.ServiceMethodRelationModel.FindByMethod(l.ctx, method)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find service method relation, method: %s, error: %+v",
			method, err,
		)
	} else if len(relations) == 0 {
		l.Warnf("not found any service method relations, method: %s", method)
		return &pb.GetServiceByMethodResp{Relations: []*pb.ServiceMethodRelation{}}, nil
	}

	out = &pb.GetServiceByMethodResp{Relations: make([]*pb.ServiceMethodRelation, 0, len(relations))}
	for _, relation := range relations {
		item := &pb.ServiceMethodRelation{}
		if err = utils.Copy(item, relation, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy service method relation, relation: %s, error: %+v",
				jsonx.MarshalIgnoreError(relation), err,
			)
		}

		out.Relations = append(out.Relations, item)
	}

	return out, nil
}
