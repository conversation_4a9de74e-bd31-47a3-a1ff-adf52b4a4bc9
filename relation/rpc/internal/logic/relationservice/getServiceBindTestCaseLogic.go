package relationservicelogic

import (
	"context"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

// Deprecated: use `GetCaseByServiceLogic` instead.
type GetServiceBindTestCaseLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetServiceBindTestCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetServiceBindTestCaseLogic {
	return &GetServiceBindTestCaseLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetServiceBindTestCaseLogic) GetServiceBindTestCase(in *pb.GetServiceBindTestCaseRequest) (
	out *pb.GetServiceBindTestCaseResponse, err error,
) {
	selectBuilder := l.svcCtx.ServiceCaseRelationModel.SelectBuilder()
	condition := squirrel.And{
		squirrel.Eq{
			"project_id":        in.GetProjectId(),
			"general_config_id": in.GetGeneralConfigId(),
			"relation_service":  in.GetServiceNames(),
		},
		squirrel.Gt{
			"updated_at": time.Now().Add(-24 * time.Hour),
		},
	}
	selectBuilder = selectBuilder.Where(condition)
	selectBuilder = selectBuilder.OrderBy("`updated_at` DESC")
	relations, err := l.svcCtx.ServiceCaseRelationModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errorx.Errorf(
			errorx.DBError,
			"failed to find service case relation, project_id: %s, general_config_id: %s, service: %s, error: %+v",
			in.GetProjectId(), in.GetGeneralConfigId(), in.GetServiceNames(), err,
		)
	}

	out = &pb.GetServiceBindTestCaseResponse{
		Relations: make([]*pb.Relation, 0, len(relations)),
	}
	if len(relations) == 0 {
		l.Warnf(
			"no service bind test case found, project_id: %s, general_config_id: %s, service: %s",
			in.GetProjectId(), in.GetGeneralConfigId(), in.GetServiceNames(),
		)
		return out, nil
	}

	for _, r := range relations {
		out.Relations = append(
			out.Relations, &pb.Relation{
				ProjectId:       r.ProjectId,
				GeneralConfigId: r.GeneralConfigId,
				ServiceName:     r.RelationService,
				InterfacePath:   r.InterfacePath,
				Cmd:             r.Cmd.String,
				CaseId:          r.CaseId,
			},
		)
	}

	return out, nil
}
