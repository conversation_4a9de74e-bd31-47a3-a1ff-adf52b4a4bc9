package relationservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type GetAllServiceTeamsLogic struct {
	*BaseLogic
}

func NewGetAllServiceTeamsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAllServiceTeamsLogic {
	return &GetAllServiceTeamsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetAllServiceTeams 获取全部服务与团队关系
func (l *GetAllServiceTeamsLogic) GetAllServiceTeams(_ *pb.GetAllServiceTeamsReq) (
	out *pb.GetAllServiceTeamsResp, err error,
) {
	relations, err := l.svcCtx.ServiceTeamRelationModel.FindAll(l.ctx)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find service team relation, error: %+v",
			err,
		)
	} else if len(relations) == 0 {
		l.Warn("not found any service team relations")
		return &pb.GetAllServiceTeamsResp{Relations: []*pb.ServiceTeamRelation{}}, nil
	}

	out = &pb.GetAllServiceTeamsResp{Relations: make([]*pb.ServiceTeamRelation, 0, len(relations))}
	for _, relation := range relations {
		item := &pb.ServiceTeamRelation{}
		if err = utils.Copy(item, relation, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to copy service team relation, relation: %s, error: %+v",
				jsonx.MarshalIgnoreError(relation), err,
			)
		}

		out.Relations = append(out.Relations, item)
	}

	return out, nil
}
