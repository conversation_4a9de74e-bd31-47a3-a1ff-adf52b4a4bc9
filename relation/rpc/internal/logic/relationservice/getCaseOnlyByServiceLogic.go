package relationservicelogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetCaseOnlyByServiceLogic struct {
	*BaseLogic
}

func NewGetCaseOnlyByServiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCaseOnlyByServiceLogic {
	return &GetCaseOnlyByServiceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetCaseOnlyByService 只通过服务名称获取关联的测试用例
func (l *GetCaseOnlyByServiceLogic) GetCaseOnlyByService(in *pb.GetCaseOnlyByServiceReq) (out *pb.GetCaseOnlyByServiceResp, err error) {
	var (
		projectID = in.GetProjectId()
		service   = in.GetService()
	)

	relations, err := l.svcCtx.ServiceCaseRelationV2Model.FindByToService(
		l.ctx, projectID, "", service, time.Now().Add(24*time.Hour),
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find service case relation, project_id: %s, service: %s, error: %+v",
			projectID, service, err,
		)
	} else if len(relations) == 0 {
		l.Warnf(
			"not found any service case relations, project_id: %s, service: %s",
			projectID, service,
		)
		return &pb.GetCaseOnlyByServiceResp{Relations: []*pb.ServiceCaseRelation{}}, nil
	}

	out = &pb.GetCaseOnlyByServiceResp{Relations: make([]*pb.ServiceCaseRelation, 0, len(relations))}
	for _, relation := range relations {
		item := &pb.ServiceCaseRelation{}
		if err = utils.Copy(item, relation, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy service case relation, relation: %s, error: %+v",
				jsonx.MarshalIgnoreError(relation), err,
			)
		}

		out.Relations = append(out.Relations, item)
	}

	return out, nil
}
