package relationservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type GetTeamByServiceLogic struct {
	*BaseLogic
}

func NewGetTeamByServiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTeamByServiceLogic {
	return &GetTeamByServiceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetTeamByService 通过服务名称获取关联的团队
func (l *GetTeamByServiceLogic) GetTeamByService(in *pb.GetTeamByServiceReq) (out *pb.GetTeamByServiceResp, err error) {
	service := in.GetService()

	relations, err := l.svcCtx.ServiceTeamRelationModel.FindByService(l.ctx, service)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find service team relation, service: %s, error: %+v",
			service, err,
		)
	} else if len(relations) == 0 {
		l.Warnf("not found any service team relations, service: %s", service)
		return &pb.GetTeamByServiceResp{Relations: []*pb.ServiceTeamRelation{}}, nil
	}

	out = &pb.GetTeamByServiceResp{Relations: make([]*pb.ServiceTeamRelation, 0, len(relations))}
	for _, relation := range relations {
		item := &pb.ServiceTeamRelation{}
		if err = utils.Copy(item, relation, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to copy service team relation, relation: %s, error: %+v",
				jsonx.MarshalIgnoreError(relation), err,
			)
		}

		out.Relations = append(out.Relations, item)
	}

	return out, nil
}
