package relationservicelogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type BindCaseToServiceLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewBindCaseToServiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BindCaseToServiceLogic {
	return &BindCaseToServiceLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *BindCaseToServiceLogic) BindCaseToService(in *pb.BindCaseToServiceReq) (
	out *pb.BindCaseToServiceResp, err error,
) {
	payload := protobuf.MarshalJSONIgnoreError(in)

	_, err = l.svcCtx.Producer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeRelationBindCaseToService,
			payload,
			base.WithRetentionOptions(5*time.Minute),
			base.WithMaxRetryOptions(0),
		), base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()),
			"failed to send task to mq, type: %s, payload: %s, error: %+v",
			constants.MQTaskTypeRelationBindCaseToService, payload, err,
		)
	}

	return &pb.BindCaseToServiceResp{}, nil
}
