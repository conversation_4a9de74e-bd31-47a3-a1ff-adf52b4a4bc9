package relationservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

// Deprecated: use `BindCaseToServiceLogic` instead.
type BindTestCaseToServiceV2Logic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewBindTestCaseToServiceV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *BindTestCaseToServiceV2Logic {
	return &BindTestCaseToServiceV2Logic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *BindTestCaseToServiceV2Logic) BindTestCaseToServiceV2(in *pb.BindTestCaseToServiceV2Request) (
	out *pb.BindTestCaseToServiceV2Response, err error,
) {
	payload, err := protobuf.MarshalJSON(in)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"绑定服务与测试用例任务参数序列化失败, payload: %+v, error: %+v",
			in, err,
		)
	}

	task := base.NewTask(
		constants.MQTaskTypeRelationBindTestCaseToServiceV2,
		payload,
		base.WithRetentionOptions(0),
		base.WithMaxRetryOptions(0),
	)
	_, err = l.svcCtx.Producer.Send(l.ctx, task, base.QueuePriorityDefault)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}
	return &pb.BindTestCaseToServiceV2Response{}, nil
}
