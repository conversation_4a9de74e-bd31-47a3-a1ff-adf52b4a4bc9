package internal

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
)

const (
	lockKeyOfClean = "lock:relation:cleanRelation:clean"

	expireOfCleanTask = 5 * time.Minute
)

type CleanRelationHandler struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func newCleanRelationHandler(svcCtx *svc.ServiceContext) *CleanRelationHandler {
	ctx := context.Background()
	return &CleanRelationHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (h *CleanRelationHandler) Clean() error {
	var cancel context.CancelFunc
	h.ctx, cancel = context.WithTimeout(h.ctx, expireOfCleanTask)
	defer cancel()

	days := h.svcCtx.Config.ServiceCaseCleaner.KeepDays
	key := lockKeyOfClean
	fn := func() error {
		return h.svcCtx.ServiceCaseRelationV2Model.DeleteBeforeNDaysRecords(h.ctx, nil, days)
	}
	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(expireOfCleanTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to clean service and case relation, key: %s, error: %+v", key, err)
			return err
		}

		h.Infof("another service is executing the clean task of TT services and cases, key: %s", key)
	} else {
		h.Infof("finished to clean service and case relation, key: %s, days: %d", key, days)
	}

	return nil
}
