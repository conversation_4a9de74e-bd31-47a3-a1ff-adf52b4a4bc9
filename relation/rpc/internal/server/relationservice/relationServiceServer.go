// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: relation.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	relationservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/logic/relationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type RelationServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedRelationServiceServer
}

func NewRelationServiceServer(svcCtx *svc.ServiceContext) *RelationServiceServer {
	return &RelationServiceServer{
		svcCtx: svcCtx,
	}
}

// Deprecated: use `BindTestCaseToServiceV2` instead.
func (s *RelationServiceServer) BindTestCaseToService(ctx context.Context, in *pb.BindTestCaseToServiceRequest) (*pb.BindTestCaseToServiceResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewBindTestCaseToServiceLogic(ctx, s.svcCtx)

	return l.BindTestCaseToService(in)
}

// Deprecated: use `BindCaseToService` instead.
func (s *RelationServiceServer) BindTestCaseToServiceV2(ctx context.Context, in *pb.BindTestCaseToServiceV2Request) (*pb.BindTestCaseToServiceV2Response, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewBindTestCaseToServiceV2Logic(ctx, s.svcCtx)

	return l.BindTestCaseToServiceV2(in)
}

// Deprecated: use `GetCaseByService` instead.
func (s *RelationServiceServer) GetServiceBindTestCase(ctx context.Context, in *pb.GetServiceBindTestCaseRequest) (*pb.GetServiceBindTestCaseResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewGetServiceBindTestCaseLogic(ctx, s.svcCtx)

	return l.GetServiceBindTestCase(in)
}

// BindCaseToService 绑定服务与测试用例
func (s *RelationServiceServer) BindCaseToService(ctx context.Context, in *pb.BindCaseToServiceReq) (*pb.BindCaseToServiceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewBindCaseToServiceLogic(ctx, s.svcCtx)

	return l.BindCaseToService(in)
}

// GetServiceByMethod 通过方法名称获取关联的服务
func (s *RelationServiceServer) GetServiceByMethod(ctx context.Context, in *pb.GetServiceByMethodReq) (*pb.GetServiceByMethodResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewGetServiceByMethodLogic(ctx, s.svcCtx)

	return l.GetServiceByMethod(in)
}

// GetCaseByService 通过服务名称获取关联的测试用例
func (s *RelationServiceServer) GetCaseByService(ctx context.Context, in *pb.GetCaseByServiceReq) (*pb.GetCaseByServiceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewGetCaseByServiceLogic(ctx, s.svcCtx)

	return l.GetCaseByService(in)
}

// GetTeamByService 通过服务名称获取关联的团队
func (s *RelationServiceServer) GetTeamByService(ctx context.Context, in *pb.GetTeamByServiceReq) (*pb.GetTeamByServiceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewGetTeamByServiceLogic(ctx, s.svcCtx)

	return l.GetTeamByService(in)
}

// GetAllServiceTeams 获取全部服务与团队关系
func (s *RelationServiceServer) GetAllServiceTeams(ctx context.Context, in *pb.GetAllServiceTeamsReq) (*pb.GetAllServiceTeamsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewGetAllServiceTeamsLogic(ctx, s.svcCtx)

	return l.GetAllServiceTeams(in)
}

// GetAllServiceMethods 获取全部服务与接口关系
func (s *RelationServiceServer) GetAllServiceMethods(ctx context.Context, in *pb.GetAllServiceMethodsReq) (*pb.GetAllServiceMethodsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewGetAllServiceMethodsLogic(ctx, s.svcCtx)

	return l.GetAllServiceMethods(in)
}

// GetCaseOnlyByService 只通过服务名称获取关联的测试用例
func (s *RelationServiceServer) GetCaseOnlyByService(ctx context.Context, in *pb.GetCaseOnlyByServiceReq) (*pb.GetCaseOnlyByServiceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := relationservicelogic.NewGetCaseOnlyByServiceLogic(ctx, s.svcCtx)

	return l.GetCaseOnlyByService(in)
}
