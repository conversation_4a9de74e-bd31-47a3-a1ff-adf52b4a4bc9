// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: relation.proto

package relationservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type (
	BindCaseToServiceReq            = pb.BindCaseToServiceReq
	BindCaseToServiceResp           = pb.BindCaseToServiceResp
	BindTestCaseToServiceRequest    = pb.BindTestCaseToServiceRequest
	BindTestCaseToServiceResponse   = pb.BindTestCaseToServiceResponse
	BindTestCaseToServiceV2Request  = pb.BindTestCaseToServiceV2Request
	BindTestCaseToServiceV2Response = pb.BindTestCaseToServiceV2Response
	GetAllServiceMethodsReq         = pb.GetAllServiceMethodsReq
	GetAllServiceMethodsResp        = pb.GetAllServiceMethodsResp
	GetAllServiceTeamsReq           = pb.GetAllServiceTeamsReq
	GetAllServiceTeamsResp          = pb.GetAllServiceTeamsResp
	GetCaseByServiceReq             = pb.GetCaseByServiceReq
	GetCaseByServiceResp            = pb.GetCaseByServiceResp
	GetCaseOnlyByServiceReq         = pb.GetCaseOnlyByServiceReq
	GetCaseOnlyByServiceResp        = pb.GetCaseOnlyByServiceResp
	GetServiceBindTestCaseRequest   = pb.GetServiceBindTestCaseRequest
	GetServiceBindTestCaseResponse  = pb.GetServiceBindTestCaseResponse
	GetServiceByMethodReq           = pb.GetServiceByMethodReq
	GetServiceByMethodResp          = pb.GetServiceByMethodResp
	GetTeamByServiceReq             = pb.GetTeamByServiceReq
	GetTeamByServiceResp            = pb.GetTeamByServiceResp
	PrecisionInfo                   = pb.PrecisionInfo
	Relation                        = pb.Relation
	ServiceCaseRelation             = pb.ServiceCaseRelation
	ServiceMethodRelation           = pb.ServiceMethodRelation
	ServiceTeamRelation             = pb.ServiceTeamRelation

	RelationService interface {
		// Deprecated: use `BindTestCaseToServiceV2` instead.
		BindTestCaseToService(ctx context.Context, in *BindTestCaseToServiceRequest, opts ...grpc.CallOption) (*BindTestCaseToServiceResponse, error)
		// Deprecated: use `BindCaseToService` instead.
		BindTestCaseToServiceV2(ctx context.Context, in *BindTestCaseToServiceV2Request, opts ...grpc.CallOption) (*BindTestCaseToServiceV2Response, error)
		// Deprecated: use `GetCaseByService` instead.
		GetServiceBindTestCase(ctx context.Context, in *GetServiceBindTestCaseRequest, opts ...grpc.CallOption) (*GetServiceBindTestCaseResponse, error)
		// BindCaseToService 绑定服务与测试用例
		BindCaseToService(ctx context.Context, in *BindCaseToServiceReq, opts ...grpc.CallOption) (*BindCaseToServiceResp, error)
		// GetServiceByMethod 通过方法名称获取关联的服务
		GetServiceByMethod(ctx context.Context, in *GetServiceByMethodReq, opts ...grpc.CallOption) (*GetServiceByMethodResp, error)
		// GetCaseByService 通过服务名称获取关联的测试用例
		GetCaseByService(ctx context.Context, in *GetCaseByServiceReq, opts ...grpc.CallOption) (*GetCaseByServiceResp, error)
		// GetTeamByService 通过服务名称获取关联的团队
		GetTeamByService(ctx context.Context, in *GetTeamByServiceReq, opts ...grpc.CallOption) (*GetTeamByServiceResp, error)
		// GetAllServiceTeams 获取全部服务与团队关系
		GetAllServiceTeams(ctx context.Context, in *GetAllServiceTeamsReq, opts ...grpc.CallOption) (*GetAllServiceTeamsResp, error)
		// GetAllServiceMethods 获取全部服务与接口关系
		GetAllServiceMethods(ctx context.Context, in *GetAllServiceMethodsReq, opts ...grpc.CallOption) (*GetAllServiceMethodsResp, error)
		// GetCaseOnlyByService 只通过服务名称获取关联的测试用例
		GetCaseOnlyByService(ctx context.Context, in *GetCaseOnlyByServiceReq, opts ...grpc.CallOption) (*GetCaseOnlyByServiceResp, error)
	}

	defaultRelationService struct {
		cli zrpc.Client
	}
)

func NewRelationService(cli zrpc.Client) RelationService {
	return &defaultRelationService{
		cli: cli,
	}
}

// Deprecated: use `BindTestCaseToServiceV2` instead.
func (m *defaultRelationService) BindTestCaseToService(ctx context.Context, in *BindTestCaseToServiceRequest, opts ...grpc.CallOption) (*BindTestCaseToServiceResponse, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.BindTestCaseToService(ctx, in, opts...)
}

// Deprecated: use `BindCaseToService` instead.
func (m *defaultRelationService) BindTestCaseToServiceV2(ctx context.Context, in *BindTestCaseToServiceV2Request, opts ...grpc.CallOption) (*BindTestCaseToServiceV2Response, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.BindTestCaseToServiceV2(ctx, in, opts...)
}

// Deprecated: use `GetCaseByService` instead.
func (m *defaultRelationService) GetServiceBindTestCase(ctx context.Context, in *GetServiceBindTestCaseRequest, opts ...grpc.CallOption) (*GetServiceBindTestCaseResponse, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.GetServiceBindTestCase(ctx, in, opts...)
}

// BindCaseToService 绑定服务与测试用例
func (m *defaultRelationService) BindCaseToService(ctx context.Context, in *BindCaseToServiceReq, opts ...grpc.CallOption) (*BindCaseToServiceResp, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.BindCaseToService(ctx, in, opts...)
}

// GetServiceByMethod 通过方法名称获取关联的服务
func (m *defaultRelationService) GetServiceByMethod(ctx context.Context, in *GetServiceByMethodReq, opts ...grpc.CallOption) (*GetServiceByMethodResp, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.GetServiceByMethod(ctx, in, opts...)
}

// GetCaseByService 通过服务名称获取关联的测试用例
func (m *defaultRelationService) GetCaseByService(ctx context.Context, in *GetCaseByServiceReq, opts ...grpc.CallOption) (*GetCaseByServiceResp, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.GetCaseByService(ctx, in, opts...)
}

// GetTeamByService 通过服务名称获取关联的团队
func (m *defaultRelationService) GetTeamByService(ctx context.Context, in *GetTeamByServiceReq, opts ...grpc.CallOption) (*GetTeamByServiceResp, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.GetTeamByService(ctx, in, opts...)
}

// GetAllServiceTeams 获取全部服务与团队关系
func (m *defaultRelationService) GetAllServiceTeams(ctx context.Context, in *GetAllServiceTeamsReq, opts ...grpc.CallOption) (*GetAllServiceTeamsResp, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.GetAllServiceTeams(ctx, in, opts...)
}

// GetAllServiceMethods 获取全部服务与接口关系
func (m *defaultRelationService) GetAllServiceMethods(ctx context.Context, in *GetAllServiceMethodsReq, opts ...grpc.CallOption) (*GetAllServiceMethodsResp, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.GetAllServiceMethods(ctx, in, opts...)
}

// GetCaseOnlyByService 只通过服务名称获取关联的测试用例
func (m *defaultRelationService) GetCaseOnlyByService(ctx context.Context, in *GetCaseOnlyByServiceReq, opts ...grpc.CallOption) (*GetCaseOnlyByServiceResp, error) {
	client := pb.NewRelationServiceClient(m.cli.Conn())
	return client.GetCaseOnlyByService(ctx, in, opts...)
}
