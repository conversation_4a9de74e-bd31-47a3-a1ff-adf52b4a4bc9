// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: relation/relation.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MethodType int32

const (
	MethodType_MT_UNKNOWN MethodType = 0
	MethodType_GRPC       MethodType = 1
	MethodType_HTTP       MethodType = 2
)

// Enum value maps for MethodType.
var (
	MethodType_name = map[int32]string{
		0: "MT_UNKNOWN",
		1: "GRPC",
		2: "HTTP",
	}
	MethodType_value = map[string]int32{
		"MT_UNKNOWN": 0,
		"GRPC":       1,
		"HTTP":       2,
	}
)

func (x MethodType) Enum() *MethodType {
	p := new(MethodType)
	*p = x
	return p
}

func (x MethodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MethodType) Descriptor() protoreflect.EnumDescriptor {
	return file_relation_relation_proto_enumTypes[0].Descriptor()
}

func (MethodType) Type() protoreflect.EnumType {
	return &file_relation_relation_proto_enumTypes[0]
}

func (x MethodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MethodType.Descriptor instead.
func (MethodType) EnumDescriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{0}
}

type BindTestCaseToServiceRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                     // 项目id，不可为空
	GeneralConfigId string                 `protobuf:"bytes,2,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"` // 通用配置ID
	ServiceName     string                 `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`               // 服务名称
	InterfacePath   string                 `protobuf:"bytes,4,opt,name=interface_path,json=interfacePath,proto3" json:"interface_path,omitempty"`         // 接口路径
	Cmd             string                 `protobuf:"bytes,5,opt,name=cmd,proto3" json:"cmd,omitempty"`                                                  // 命令号，可不传递
	CaseId          string                 `protobuf:"bytes,6,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                              // 测试用例id
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BindTestCaseToServiceRequest) Reset() {
	*x = BindTestCaseToServiceRequest{}
	mi := &file_relation_relation_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindTestCaseToServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindTestCaseToServiceRequest) ProtoMessage() {}

func (x *BindTestCaseToServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindTestCaseToServiceRequest.ProtoReflect.Descriptor instead.
func (*BindTestCaseToServiceRequest) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{0}
}

func (x *BindTestCaseToServiceRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *BindTestCaseToServiceRequest) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

func (x *BindTestCaseToServiceRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *BindTestCaseToServiceRequest) GetInterfacePath() string {
	if x != nil {
		return x.InterfacePath
	}
	return ""
}

func (x *BindTestCaseToServiceRequest) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *BindTestCaseToServiceRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

type BindTestCaseToServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindTestCaseToServiceResponse) Reset() {
	*x = BindTestCaseToServiceResponse{}
	mi := &file_relation_relation_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindTestCaseToServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindTestCaseToServiceResponse) ProtoMessage() {}

func (x *BindTestCaseToServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindTestCaseToServiceResponse.ProtoReflect.Descriptor instead.
func (*BindTestCaseToServiceResponse) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{1}
}

type BindTestCaseToServiceV2Request struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestUrl    string                 `protobuf:"bytes,1,opt,name=request_url,json=requestUrl,proto3" json:"request_url,omitempty"`
	PrecisionInfo []byte                 `protobuf:"bytes,2,opt,name=precision_info,json=precisionInfo,proto3" json:"precision_info,omitempty"`
	RequestBody   []byte                 `protobuf:"bytes,3,opt,name=request_body,json=requestBody,proto3" json:"request_body,omitempty"`
	ResponseBody  []byte                 `protobuf:"bytes,4,opt,name=response_body,json=responseBody,proto3" json:"response_body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindTestCaseToServiceV2Request) Reset() {
	*x = BindTestCaseToServiceV2Request{}
	mi := &file_relation_relation_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindTestCaseToServiceV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindTestCaseToServiceV2Request) ProtoMessage() {}

func (x *BindTestCaseToServiceV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindTestCaseToServiceV2Request.ProtoReflect.Descriptor instead.
func (*BindTestCaseToServiceV2Request) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{2}
}

func (x *BindTestCaseToServiceV2Request) GetRequestUrl() string {
	if x != nil {
		return x.RequestUrl
	}
	return ""
}

func (x *BindTestCaseToServiceV2Request) GetPrecisionInfo() []byte {
	if x != nil {
		return x.PrecisionInfo
	}
	return nil
}

func (x *BindTestCaseToServiceV2Request) GetRequestBody() []byte {
	if x != nil {
		return x.RequestBody
	}
	return nil
}

func (x *BindTestCaseToServiceV2Request) GetResponseBody() []byte {
	if x != nil {
		return x.ResponseBody
	}
	return nil
}

type BindTestCaseToServiceV2Response struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindTestCaseToServiceV2Response) Reset() {
	*x = BindTestCaseToServiceV2Response{}
	mi := &file_relation_relation_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindTestCaseToServiceV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindTestCaseToServiceV2Response) ProtoMessage() {}

func (x *BindTestCaseToServiceV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindTestCaseToServiceV2Response.ProtoReflect.Descriptor instead.
func (*BindTestCaseToServiceV2Response) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{3}
}

type Relation struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                     // 项目id，不可为空
	GeneralConfigId string                 `protobuf:"bytes,2,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"` // 通用配置ID
	ServiceName     string                 `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`               // 服务名称
	InterfacePath   string                 `protobuf:"bytes,4,opt,name=interface_path,json=interfacePath,proto3" json:"interface_path,omitempty"`         // 接口路径
	Cmd             string                 `protobuf:"bytes,5,opt,name=cmd,proto3" json:"cmd,omitempty"`                                                  // 命令号，可不传递
	CaseId          string                 `protobuf:"bytes,6,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                              // 测试用例id
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Relation) Reset() {
	*x = Relation{}
	mi := &file_relation_relation_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Relation) ProtoMessage() {}

func (x *Relation) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Relation.ProtoReflect.Descriptor instead.
func (*Relation) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{4}
}

func (x *Relation) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Relation) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

func (x *Relation) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *Relation) GetInterfacePath() string {
	if x != nil {
		return x.InterfacePath
	}
	return ""
}

func (x *Relation) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *Relation) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

type GetServiceBindTestCaseRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                     // 项目id，不可为空
	ServiceNames    []string               `protobuf:"bytes,2,rep,name=service_names,json=serviceNames,proto3" json:"service_names,omitempty"`            // 服务名称，不可为空
	GeneralConfigId string                 `protobuf:"bytes,3,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"` // 通用配置ID
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetServiceBindTestCaseRequest) Reset() {
	*x = GetServiceBindTestCaseRequest{}
	mi := &file_relation_relation_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceBindTestCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceBindTestCaseRequest) ProtoMessage() {}

func (x *GetServiceBindTestCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceBindTestCaseRequest.ProtoReflect.Descriptor instead.
func (*GetServiceBindTestCaseRequest) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{5}
}

func (x *GetServiceBindTestCaseRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GetServiceBindTestCaseRequest) GetServiceNames() []string {
	if x != nil {
		return x.ServiceNames
	}
	return nil
}

func (x *GetServiceBindTestCaseRequest) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

type GetServiceBindTestCaseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relations     []*Relation            `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceBindTestCaseResponse) Reset() {
	*x = GetServiceBindTestCaseResponse{}
	mi := &file_relation_relation_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceBindTestCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceBindTestCaseResponse) ProtoMessage() {}

func (x *GetServiceBindTestCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceBindTestCaseResponse.ProtoReflect.Descriptor instead.
func (*GetServiceBindTestCaseResponse) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{6}
}

func (x *GetServiceBindTestCaseResponse) GetRelations() []*Relation {
	if x != nil {
		return x.Relations
	}
	return nil
}

type PrecisionInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	GeneralConfigId string                 `protobuf:"bytes,2,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"`
	DocumentId      string                 `protobuf:"bytes,3,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`
	CaseId          string                 `protobuf:"bytes,4,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PrecisionInfo) Reset() {
	*x = PrecisionInfo{}
	mi := &file_relation_relation_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrecisionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrecisionInfo) ProtoMessage() {}

func (x *PrecisionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrecisionInfo.ProtoReflect.Descriptor instead.
func (*PrecisionInfo) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{7}
}

func (x *PrecisionInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PrecisionInfo) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

func (x *PrecisionInfo) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *PrecisionInfo) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

type BindCaseToServiceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestUrl    string                 `protobuf:"bytes,1,opt,name=request_url,json=requestUrl,proto3" json:"request_url,omitempty"`
	PrecisionInfo *PrecisionInfo         `protobuf:"bytes,2,opt,name=precision_info,json=precisionInfo,proto3" json:"precision_info,omitempty"`
	RequestBody   string                 `protobuf:"bytes,3,opt,name=request_body,json=requestBody,proto3" json:"request_body,omitempty"`
	ResponseBody  string                 `protobuf:"bytes,4,opt,name=response_body,json=responseBody,proto3" json:"response_body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindCaseToServiceReq) Reset() {
	*x = BindCaseToServiceReq{}
	mi := &file_relation_relation_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindCaseToServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindCaseToServiceReq) ProtoMessage() {}

func (x *BindCaseToServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindCaseToServiceReq.ProtoReflect.Descriptor instead.
func (*BindCaseToServiceReq) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{8}
}

func (x *BindCaseToServiceReq) GetRequestUrl() string {
	if x != nil {
		return x.RequestUrl
	}
	return ""
}

func (x *BindCaseToServiceReq) GetPrecisionInfo() *PrecisionInfo {
	if x != nil {
		return x.PrecisionInfo
	}
	return nil
}

func (x *BindCaseToServiceReq) GetRequestBody() string {
	if x != nil {
		return x.RequestBody
	}
	return ""
}

func (x *BindCaseToServiceReq) GetResponseBody() string {
	if x != nil {
		return x.ResponseBody
	}
	return ""
}

type BindCaseToServiceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindCaseToServiceResp) Reset() {
	*x = BindCaseToServiceResp{}
	mi := &file_relation_relation_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindCaseToServiceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindCaseToServiceResp) ProtoMessage() {}

func (x *BindCaseToServiceResp) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindCaseToServiceResp.ProtoReflect.Descriptor instead.
func (*BindCaseToServiceResp) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{9}
}

type ServiceMethodRelation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Service       string                 `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`                        // 服务名称
	Namespace     string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`                    // 命名空间
	Type          MethodType             `protobuf:"varint,3,opt,name=type,proto3,enum=relation.MethodType" json:"type,omitempty"`    // 接口类型
	Method        string                 `protobuf:"bytes,4,opt,name=method,proto3" json:"method,omitempty"`                          // 接口名称
	ReqPath       string                 `protobuf:"bytes,11,opt,name=req_path,json=reqPath,proto3" json:"req_path,omitempty"`        // 请求路径
	Cmd           uint32                 `protobuf:"varint,12,opt,name=cmd,proto3" json:"cmd,omitempty"`                              // 命令号
	Deprecated    bool                   `protobuf:"varint,13,opt,name=deprecated,proto3" json:"deprecated,omitempty"`                // 是否已弃用
	QueryPath     string                 `protobuf:"bytes,14,opt,name=query_path,json=queryPath,proto3" json:"query_path,omitempty"`  // 请求路径（查询天相）
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`  // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`  // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceMethodRelation) Reset() {
	*x = ServiceMethodRelation{}
	mi := &file_relation_relation_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceMethodRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceMethodRelation) ProtoMessage() {}

func (x *ServiceMethodRelation) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceMethodRelation.ProtoReflect.Descriptor instead.
func (*ServiceMethodRelation) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{10}
}

func (x *ServiceMethodRelation) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *ServiceMethodRelation) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ServiceMethodRelation) GetType() MethodType {
	if x != nil {
		return x.Type
	}
	return MethodType_MT_UNKNOWN
}

func (x *ServiceMethodRelation) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ServiceMethodRelation) GetReqPath() string {
	if x != nil {
		return x.ReqPath
	}
	return ""
}

func (x *ServiceMethodRelation) GetCmd() uint32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *ServiceMethodRelation) GetDeprecated() bool {
	if x != nil {
		return x.Deprecated
	}
	return false
}

func (x *ServiceMethodRelation) GetQueryPath() string {
	if x != nil {
		return x.QueryPath
	}
	return ""
}

func (x *ServiceMethodRelation) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ServiceMethodRelation) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ServiceMethodRelation) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ServiceMethodRelation) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type GetServiceByMethodReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Method        string                 `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceByMethodReq) Reset() {
	*x = GetServiceByMethodReq{}
	mi := &file_relation_relation_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceByMethodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceByMethodReq) ProtoMessage() {}

func (x *GetServiceByMethodReq) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceByMethodReq.ProtoReflect.Descriptor instead.
func (*GetServiceByMethodReq) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{11}
}

func (x *GetServiceByMethodReq) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

type GetServiceByMethodResp struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Relations     []*ServiceMethodRelation `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceByMethodResp) Reset() {
	*x = GetServiceByMethodResp{}
	mi := &file_relation_relation_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceByMethodResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceByMethodResp) ProtoMessage() {}

func (x *GetServiceByMethodResp) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceByMethodResp.ProtoReflect.Descriptor instead.
func (*GetServiceByMethodResp) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{12}
}

func (x *GetServiceByMethodResp) GetRelations() []*ServiceMethodRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

type ServiceCaseRelation struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                     // 项目ID
	GeneralConfigId string                 `protobuf:"bytes,2,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"` // 通用配置ID
	Service         string                 `protobuf:"bytes,3,opt,name=service,proto3" json:"service,omitempty"`                                          // 服务名称
	Namespace       string                 `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`                                      // 命名空间
	Method          string                 `protobuf:"bytes,5,opt,name=method,proto3" json:"method,omitempty"`                                            // 接口名称
	ReqPath         string                 `protobuf:"bytes,6,opt,name=req_path,json=reqPath,proto3" json:"req_path,omitempty"`                           // 请求路径
	Cmd             uint32                 `protobuf:"varint,7,opt,name=cmd,proto3" json:"cmd,omitempty"`                                                 // 命令号
	ToService       string                 `protobuf:"bytes,8,opt,name=to_service,json=toService,proto3" json:"to_service,omitempty"`                     // 经过的服务名称
	DocumentId      string                 `protobuf:"bytes,9,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`                  // 接口文档ID
	CaseId          string                 `protobuf:"bytes,10,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                             // 用例ID
	CreatedBy       string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                    // 创建者
	UpdatedBy       string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                    // 更新者
	CreatedAt       int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                   // 创建时间
	UpdatedAt       int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                   // 更新时间
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServiceCaseRelation) Reset() {
	*x = ServiceCaseRelation{}
	mi := &file_relation_relation_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCaseRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCaseRelation) ProtoMessage() {}

func (x *ServiceCaseRelation) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCaseRelation.ProtoReflect.Descriptor instead.
func (*ServiceCaseRelation) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{13}
}

func (x *ServiceCaseRelation) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ServiceCaseRelation) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

func (x *ServiceCaseRelation) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *ServiceCaseRelation) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ServiceCaseRelation) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ServiceCaseRelation) GetReqPath() string {
	if x != nil {
		return x.ReqPath
	}
	return ""
}

func (x *ServiceCaseRelation) GetCmd() uint32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *ServiceCaseRelation) GetToService() string {
	if x != nil {
		return x.ToService
	}
	return ""
}

func (x *ServiceCaseRelation) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *ServiceCaseRelation) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ServiceCaseRelation) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ServiceCaseRelation) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ServiceCaseRelation) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ServiceCaseRelation) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type GetCaseByServiceReq struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                     // 项目ID
	GeneralConfigId string                 `protobuf:"bytes,2,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"` // 通用配置ID
	Service         string                 `protobuf:"bytes,3,opt,name=service,proto3" json:"service,omitempty"`                                          // 服务名称列表
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetCaseByServiceReq) Reset() {
	*x = GetCaseByServiceReq{}
	mi := &file_relation_relation_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCaseByServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseByServiceReq) ProtoMessage() {}

func (x *GetCaseByServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseByServiceReq.ProtoReflect.Descriptor instead.
func (*GetCaseByServiceReq) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{14}
}

func (x *GetCaseByServiceReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GetCaseByServiceReq) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

func (x *GetCaseByServiceReq) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

type GetCaseByServiceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relations     []*ServiceCaseRelation `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCaseByServiceResp) Reset() {
	*x = GetCaseByServiceResp{}
	mi := &file_relation_relation_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCaseByServiceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseByServiceResp) ProtoMessage() {}

func (x *GetCaseByServiceResp) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseByServiceResp.ProtoReflect.Descriptor instead.
func (*GetCaseByServiceResp) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{15}
}

func (x *GetCaseByServiceResp) GetRelations() []*ServiceCaseRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

type GetCaseOnlyByServiceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	Service       string                 `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`                      // 服务名称列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCaseOnlyByServiceReq) Reset() {
	*x = GetCaseOnlyByServiceReq{}
	mi := &file_relation_relation_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCaseOnlyByServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseOnlyByServiceReq) ProtoMessage() {}

func (x *GetCaseOnlyByServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseOnlyByServiceReq.ProtoReflect.Descriptor instead.
func (*GetCaseOnlyByServiceReq) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{16}
}

func (x *GetCaseOnlyByServiceReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GetCaseOnlyByServiceReq) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

type GetCaseOnlyByServiceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relations     []*ServiceCaseRelation `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCaseOnlyByServiceResp) Reset() {
	*x = GetCaseOnlyByServiceResp{}
	mi := &file_relation_relation_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCaseOnlyByServiceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseOnlyByServiceResp) ProtoMessage() {}

func (x *GetCaseOnlyByServiceResp) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseOnlyByServiceResp.ProtoReflect.Descriptor instead.
func (*GetCaseOnlyByServiceResp) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{17}
}

func (x *GetCaseOnlyByServiceResp) GetRelations() []*ServiceCaseRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

type ServiceTeamRelation struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Service         string                 `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`                                        // 服务名称
	Namespace       string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`                                    // 命名空间
	TeamName        string                 `protobuf:"bytes,3,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`                      // 团队名称
	TeamDescription string                 `protobuf:"bytes,4,opt,name=team_description,json=teamDescription,proto3" json:"team_description,omitempty"` // 团队描述
	CreatedBy       string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                  // 创建者
	UpdatedBy       string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                  // 更新者
	CreatedAt       int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                 // 创建时间
	UpdatedAt       int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                 // 更新时间
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServiceTeamRelation) Reset() {
	*x = ServiceTeamRelation{}
	mi := &file_relation_relation_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceTeamRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTeamRelation) ProtoMessage() {}

func (x *ServiceTeamRelation) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTeamRelation.ProtoReflect.Descriptor instead.
func (*ServiceTeamRelation) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{18}
}

func (x *ServiceTeamRelation) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *ServiceTeamRelation) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ServiceTeamRelation) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *ServiceTeamRelation) GetTeamDescription() string {
	if x != nil {
		return x.TeamDescription
	}
	return ""
}

func (x *ServiceTeamRelation) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ServiceTeamRelation) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ServiceTeamRelation) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ServiceTeamRelation) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type GetTeamByServiceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Service       string                 `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"` // 服务名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTeamByServiceReq) Reset() {
	*x = GetTeamByServiceReq{}
	mi := &file_relation_relation_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTeamByServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTeamByServiceReq) ProtoMessage() {}

func (x *GetTeamByServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTeamByServiceReq.ProtoReflect.Descriptor instead.
func (*GetTeamByServiceReq) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{19}
}

func (x *GetTeamByServiceReq) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

type GetTeamByServiceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relations     []*ServiceTeamRelation `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTeamByServiceResp) Reset() {
	*x = GetTeamByServiceResp{}
	mi := &file_relation_relation_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTeamByServiceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTeamByServiceResp) ProtoMessage() {}

func (x *GetTeamByServiceResp) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTeamByServiceResp.ProtoReflect.Descriptor instead.
func (*GetTeamByServiceResp) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{20}
}

func (x *GetTeamByServiceResp) GetRelations() []*ServiceTeamRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

type GetAllServiceTeamsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllServiceTeamsReq) Reset() {
	*x = GetAllServiceTeamsReq{}
	mi := &file_relation_relation_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllServiceTeamsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllServiceTeamsReq) ProtoMessage() {}

func (x *GetAllServiceTeamsReq) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllServiceTeamsReq.ProtoReflect.Descriptor instead.
func (*GetAllServiceTeamsReq) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{21}
}

type GetAllServiceTeamsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relations     []*ServiceTeamRelation `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllServiceTeamsResp) Reset() {
	*x = GetAllServiceTeamsResp{}
	mi := &file_relation_relation_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllServiceTeamsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllServiceTeamsResp) ProtoMessage() {}

func (x *GetAllServiceTeamsResp) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllServiceTeamsResp.ProtoReflect.Descriptor instead.
func (*GetAllServiceTeamsResp) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{22}
}

func (x *GetAllServiceTeamsResp) GetRelations() []*ServiceTeamRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

type GetAllServiceMethodsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllServiceMethodsReq) Reset() {
	*x = GetAllServiceMethodsReq{}
	mi := &file_relation_relation_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllServiceMethodsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllServiceMethodsReq) ProtoMessage() {}

func (x *GetAllServiceMethodsReq) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllServiceMethodsReq.ProtoReflect.Descriptor instead.
func (*GetAllServiceMethodsReq) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{23}
}

type GetAllServiceMethodsResp struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Relations     []*ServiceMethodRelation `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllServiceMethodsResp) Reset() {
	*x = GetAllServiceMethodsResp{}
	mi := &file_relation_relation_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllServiceMethodsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllServiceMethodsResp) ProtoMessage() {}

func (x *GetAllServiceMethodsResp) ProtoReflect() protoreflect.Message {
	mi := &file_relation_relation_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllServiceMethodsResp.ProtoReflect.Descriptor instead.
func (*GetAllServiceMethodsResp) Descriptor() ([]byte, []int) {
	return file_relation_relation_proto_rawDescGZIP(), []int{24}
}

func (x *GetAllServiceMethodsResp) GetRelations() []*ServiceMethodRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

var File_relation_relation_proto protoreflect.FileDescriptor

var file_relation_relation_proto_rawDesc = []byte{
	0x0a, 0x17, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xff, 0x02, 0x0a, 0x1c, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x73,
	0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19,
	0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x21, 0xfa, 0x42, 0x1e, 0x72, 0x1c, 0x32, 0x1a, 0x28, 0x3f, 0x3a, 0x5e, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b,
	0x3f, 0x29, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07,
	0x10, 0x01, 0x18, 0x40, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xfa, 0x42,
	0x0a, 0x72, 0x08, 0x10, 0x01, 0x18, 0x80, 0x02, 0xd0, 0x01, 0x01, 0x52, 0x0d, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x03, 0x63, 0x6d,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x10, 0x01,
	0x18, 0x40, 0xd0, 0x01, 0x01, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x4a, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0xfa, 0x42, 0x2e,
	0x72, 0x2c, 0x32, 0x27, 0x28, 0x3f, 0x3a, 0x5e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x3a,
	0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0xd0, 0x01, 0x01, 0x52, 0x06,
	0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0x1f, 0x0a, 0x1d, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65,
	0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x1e, 0x42, 0x69, 0x6e, 0x64,
	0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x70,
	0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x6f,
	0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x22, 0x21, 0x0a, 0x1f, 0x42, 0x69,
	0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xeb, 0x02,
	0x0a, 0x08, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e,
	0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x11, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0xfa, 0x42, 0x1e, 0x72, 0x1c, 0x32, 0x1a, 0x28, 0x3f, 0x3a,
	0x5e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c,
	0xfa, 0x42, 0x09, 0x72, 0x07, 0x10, 0x01, 0x18, 0x40, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x0e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x72, 0x08, 0x10, 0x01, 0x18, 0x80, 0x02, 0xd0, 0x01, 0x01,
	0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x1e, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42,
	0x09, 0x72, 0x07, 0x10, 0x01, 0x18, 0x40, 0xd0, 0x01, 0x01, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12,
	0x4a, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x31, 0xfa, 0x42, 0x2e, 0x72, 0x2c, 0x32, 0x27, 0x28, 0x3f, 0x3a, 0x5e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29,
	0xd0, 0x01, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0xd2, 0x01, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65,
	0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24,
	0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x12, 0x4d, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0xfa, 0x42,
	0x1e, 0x72, 0x1c, 0x32, 0x1a, 0x28, 0x3f, 0x3a, 0x5e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52,
	0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64,
	0x22, 0x52, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb1, 0x02, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72,
	0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x21, 0xfa, 0x42, 0x1e, 0x72, 0x1c, 0x32, 0x1a, 0x28, 0x3f, 0x3a, 0x5e, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x3a, 0x2e,
	0x2b, 0x3f, 0x29, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x28, 0xfa, 0x42, 0x25, 0x72, 0x23,
	0x32, 0x1e, 0x28, 0x3f, 0x3a, 0x5e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29,
	0xd0, 0x01, 0x01, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x47, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2e, 0xfa, 0x42, 0x2b, 0x72, 0x29, 0x32, 0x27, 0x28, 0x3f, 0x3a, 0x5e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29,
	0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0xd2, 0x01, 0x0a, 0x14, 0x42, 0x69, 0x6e,
	0x64, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x28, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x02, 0x52,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x48, 0x0a, 0x0e, 0x70,
	0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50,
	0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x22, 0x17, 0x0a,
	0x15, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0xf9, 0x02, 0x0a, 0x15, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65,
	0x71, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65,
	0x71, 0x50, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x72, 0x65,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x2f, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x79, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x22, 0x57, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x42, 0x79, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3d, 0x0a,
	0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb2, 0x03, 0x0a,
	0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x71, 0x50, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0xc6, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x42, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa,
	0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x21, 0xfa, 0x42, 0x1e, 0x72, 0x1c, 0x32, 0x1a, 0x28, 0x3f, 0x3a, 0x5e,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x53, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x73, 0x65, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x3b, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x7b, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x42, 0x79,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e,
	0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x57, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x42, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x91, 0x02, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x65,
	0x61, 0x6d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x38, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x54, 0x65, 0x61, 0x6d, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x21, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x22, 0x53, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x42, 0x79,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x09, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x17, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65,
	0x71, 0x22, 0x55, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x09, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x22, 0x59, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x3d, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2a, 0x3a,
	0x0a, 0x0a, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a,
	0x4d, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x04,
	0x47, 0x52, 0x50, 0x43, 0x10, 0x01, 0x1a, 0x08, 0x82, 0xb5, 0x18, 0x04, 0x67, 0x52, 0x50, 0x43,
	0x12, 0x08, 0x0a, 0x04, 0x48, 0x54, 0x54, 0x50, 0x10, 0x02, 0x32, 0xc4, 0x07, 0x0a, 0x0f, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x68,
	0x0a, 0x15, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x26, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x54,
	0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x17, 0x42, 0x69, 0x6e, 0x64,
	0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x32, 0x12, 0x28, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42,
	0x69, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x73,
	0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x32,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61,
	0x73, 0x65, 0x12, 0x27, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74,
	0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x11, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x61, 0x73,
	0x65, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x2e, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x61, 0x73, 0x65, 0x54, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x1f, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52,
	0x65, 0x71, 0x1a, 0x20, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x42,
	0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x42, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x65,
	0x61, 0x6d, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x42, 0x79,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x42, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x73,
	0x12, 0x1f, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x5d, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x12, 0x21, 0x2e, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x22,
	0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x5d, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x4f, 0x6e, 0x6c,
	0x79, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x4f, 0x6e, 0x6c,
	0x79, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65,
	0x4f, 0x6e, 0x6c, 0x79, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x42, 0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_relation_relation_proto_rawDescOnce sync.Once
	file_relation_relation_proto_rawDescData = file_relation_relation_proto_rawDesc
)

func file_relation_relation_proto_rawDescGZIP() []byte {
	file_relation_relation_proto_rawDescOnce.Do(func() {
		file_relation_relation_proto_rawDescData = protoimpl.X.CompressGZIP(file_relation_relation_proto_rawDescData)
	})
	return file_relation_relation_proto_rawDescData
}

var file_relation_relation_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_relation_relation_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_relation_relation_proto_goTypes = []any{
	(MethodType)(0),                         // 0: relation.MethodType
	(*BindTestCaseToServiceRequest)(nil),    // 1: relation.BindTestCaseToServiceRequest
	(*BindTestCaseToServiceResponse)(nil),   // 2: relation.BindTestCaseToServiceResponse
	(*BindTestCaseToServiceV2Request)(nil),  // 3: relation.BindTestCaseToServiceV2Request
	(*BindTestCaseToServiceV2Response)(nil), // 4: relation.BindTestCaseToServiceV2Response
	(*Relation)(nil),                        // 5: relation.Relation
	(*GetServiceBindTestCaseRequest)(nil),   // 6: relation.GetServiceBindTestCaseRequest
	(*GetServiceBindTestCaseResponse)(nil),  // 7: relation.GetServiceBindTestCaseResponse
	(*PrecisionInfo)(nil),                   // 8: relation.PrecisionInfo
	(*BindCaseToServiceReq)(nil),            // 9: relation.BindCaseToServiceReq
	(*BindCaseToServiceResp)(nil),           // 10: relation.BindCaseToServiceResp
	(*ServiceMethodRelation)(nil),           // 11: relation.ServiceMethodRelation
	(*GetServiceByMethodReq)(nil),           // 12: relation.GetServiceByMethodReq
	(*GetServiceByMethodResp)(nil),          // 13: relation.GetServiceByMethodResp
	(*ServiceCaseRelation)(nil),             // 14: relation.ServiceCaseRelation
	(*GetCaseByServiceReq)(nil),             // 15: relation.GetCaseByServiceReq
	(*GetCaseByServiceResp)(nil),            // 16: relation.GetCaseByServiceResp
	(*GetCaseOnlyByServiceReq)(nil),         // 17: relation.GetCaseOnlyByServiceReq
	(*GetCaseOnlyByServiceResp)(nil),        // 18: relation.GetCaseOnlyByServiceResp
	(*ServiceTeamRelation)(nil),             // 19: relation.ServiceTeamRelation
	(*GetTeamByServiceReq)(nil),             // 20: relation.GetTeamByServiceReq
	(*GetTeamByServiceResp)(nil),            // 21: relation.GetTeamByServiceResp
	(*GetAllServiceTeamsReq)(nil),           // 22: relation.GetAllServiceTeamsReq
	(*GetAllServiceTeamsResp)(nil),          // 23: relation.GetAllServiceTeamsResp
	(*GetAllServiceMethodsReq)(nil),         // 24: relation.GetAllServiceMethodsReq
	(*GetAllServiceMethodsResp)(nil),        // 25: relation.GetAllServiceMethodsResp
}
var file_relation_relation_proto_depIdxs = []int32{
	5,  // 0: relation.GetServiceBindTestCaseResponse.relations:type_name -> relation.Relation
	8,  // 1: relation.BindCaseToServiceReq.precision_info:type_name -> relation.PrecisionInfo
	0,  // 2: relation.ServiceMethodRelation.type:type_name -> relation.MethodType
	11, // 3: relation.GetServiceByMethodResp.relations:type_name -> relation.ServiceMethodRelation
	14, // 4: relation.GetCaseByServiceResp.relations:type_name -> relation.ServiceCaseRelation
	14, // 5: relation.GetCaseOnlyByServiceResp.relations:type_name -> relation.ServiceCaseRelation
	19, // 6: relation.GetTeamByServiceResp.relations:type_name -> relation.ServiceTeamRelation
	19, // 7: relation.GetAllServiceTeamsResp.relations:type_name -> relation.ServiceTeamRelation
	11, // 8: relation.GetAllServiceMethodsResp.relations:type_name -> relation.ServiceMethodRelation
	1,  // 9: relation.RelationService.BindTestCaseToService:input_type -> relation.BindTestCaseToServiceRequest
	3,  // 10: relation.RelationService.BindTestCaseToServiceV2:input_type -> relation.BindTestCaseToServiceV2Request
	6,  // 11: relation.RelationService.GetServiceBindTestCase:input_type -> relation.GetServiceBindTestCaseRequest
	9,  // 12: relation.RelationService.BindCaseToService:input_type -> relation.BindCaseToServiceReq
	12, // 13: relation.RelationService.GetServiceByMethod:input_type -> relation.GetServiceByMethodReq
	15, // 14: relation.RelationService.GetCaseByService:input_type -> relation.GetCaseByServiceReq
	20, // 15: relation.RelationService.GetTeamByService:input_type -> relation.GetTeamByServiceReq
	22, // 16: relation.RelationService.GetAllServiceTeams:input_type -> relation.GetAllServiceTeamsReq
	24, // 17: relation.RelationService.GetAllServiceMethods:input_type -> relation.GetAllServiceMethodsReq
	17, // 18: relation.RelationService.GetCaseOnlyByService:input_type -> relation.GetCaseOnlyByServiceReq
	2,  // 19: relation.RelationService.BindTestCaseToService:output_type -> relation.BindTestCaseToServiceResponse
	4,  // 20: relation.RelationService.BindTestCaseToServiceV2:output_type -> relation.BindTestCaseToServiceV2Response
	7,  // 21: relation.RelationService.GetServiceBindTestCase:output_type -> relation.GetServiceBindTestCaseResponse
	10, // 22: relation.RelationService.BindCaseToService:output_type -> relation.BindCaseToServiceResp
	13, // 23: relation.RelationService.GetServiceByMethod:output_type -> relation.GetServiceByMethodResp
	16, // 24: relation.RelationService.GetCaseByService:output_type -> relation.GetCaseByServiceResp
	21, // 25: relation.RelationService.GetTeamByService:output_type -> relation.GetTeamByServiceResp
	23, // 26: relation.RelationService.GetAllServiceTeams:output_type -> relation.GetAllServiceTeamsResp
	25, // 27: relation.RelationService.GetAllServiceMethods:output_type -> relation.GetAllServiceMethodsResp
	18, // 28: relation.RelationService.GetCaseOnlyByService:output_type -> relation.GetCaseOnlyByServiceResp
	19, // [19:29] is the sub-list for method output_type
	9,  // [9:19] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_relation_relation_proto_init() }
func file_relation_relation_proto_init() {
	if File_relation_relation_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_relation_relation_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_relation_relation_proto_goTypes,
		DependencyIndexes: file_relation_relation_proto_depIdxs,
		EnumInfos:         file_relation_relation_proto_enumTypes,
		MessageInfos:      file_relation_relation_proto_msgTypes,
	}.Build()
	File_relation_relation_proto = out.File
	file_relation_relation_proto_rawDesc = nil
	file_relation_relation_proto_goTypes = nil
	file_relation_relation_proto_depIdxs = nil
}
