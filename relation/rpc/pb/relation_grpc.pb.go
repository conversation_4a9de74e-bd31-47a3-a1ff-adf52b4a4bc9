// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: relation/relation.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RelationService_BindTestCaseToService_FullMethodName   = "/relation.RelationService/BindTestCaseToService"
	RelationService_BindTestCaseToServiceV2_FullMethodName = "/relation.RelationService/BindTestCaseToServiceV2"
	RelationService_GetServiceBindTestCase_FullMethodName  = "/relation.RelationService/GetServiceBindTestCase"
	RelationService_BindCaseToService_FullMethodName       = "/relation.RelationService/BindCaseToService"
	RelationService_GetServiceByMethod_FullMethodName      = "/relation.RelationService/GetServiceByMethod"
	RelationService_GetCaseByService_FullMethodName        = "/relation.RelationService/GetCaseByService"
	RelationService_GetTeamByService_FullMethodName        = "/relation.RelationService/GetTeamByService"
	RelationService_GetAllServiceTeams_FullMethodName      = "/relation.RelationService/GetAllServiceTeams"
	RelationService_GetAllServiceMethods_FullMethodName    = "/relation.RelationService/GetAllServiceMethods"
	RelationService_GetCaseOnlyByService_FullMethodName    = "/relation.RelationService/GetCaseOnlyByService"
)

// RelationServiceClient is the client API for RelationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RelationServiceClient interface {
	// Deprecated: use `BindTestCaseToServiceV2` instead.
	BindTestCaseToService(ctx context.Context, in *BindTestCaseToServiceRequest, opts ...grpc.CallOption) (*BindTestCaseToServiceResponse, error)
	// Deprecated: use `BindCaseToService` instead.
	BindTestCaseToServiceV2(ctx context.Context, in *BindTestCaseToServiceV2Request, opts ...grpc.CallOption) (*BindTestCaseToServiceV2Response, error)
	// Deprecated: use `GetCaseByService` instead.
	GetServiceBindTestCase(ctx context.Context, in *GetServiceBindTestCaseRequest, opts ...grpc.CallOption) (*GetServiceBindTestCaseResponse, error)
	// BindCaseToService 绑定服务与测试用例
	BindCaseToService(ctx context.Context, in *BindCaseToServiceReq, opts ...grpc.CallOption) (*BindCaseToServiceResp, error)
	// GetServiceByMethod 通过方法名称获取关联的服务
	GetServiceByMethod(ctx context.Context, in *GetServiceByMethodReq, opts ...grpc.CallOption) (*GetServiceByMethodResp, error)
	// GetCaseByService 通过服务名称获取关联的测试用例
	GetCaseByService(ctx context.Context, in *GetCaseByServiceReq, opts ...grpc.CallOption) (*GetCaseByServiceResp, error)
	// GetTeamByService 通过服务名称获取关联的团队
	GetTeamByService(ctx context.Context, in *GetTeamByServiceReq, opts ...grpc.CallOption) (*GetTeamByServiceResp, error)
	// GetAllServiceTeams 获取全部服务与团队关系
	GetAllServiceTeams(ctx context.Context, in *GetAllServiceTeamsReq, opts ...grpc.CallOption) (*GetAllServiceTeamsResp, error)
	// GetAllServiceMethods 获取全部服务与接口关系
	GetAllServiceMethods(ctx context.Context, in *GetAllServiceMethodsReq, opts ...grpc.CallOption) (*GetAllServiceMethodsResp, error)
	// GetCaseOnlyByService 只通过服务名称获取关联的测试用例
	GetCaseOnlyByService(ctx context.Context, in *GetCaseOnlyByServiceReq, opts ...grpc.CallOption) (*GetCaseOnlyByServiceResp, error)
}

type relationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRelationServiceClient(cc grpc.ClientConnInterface) RelationServiceClient {
	return &relationServiceClient{cc}
}

func (c *relationServiceClient) BindTestCaseToService(ctx context.Context, in *BindTestCaseToServiceRequest, opts ...grpc.CallOption) (*BindTestCaseToServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BindTestCaseToServiceResponse)
	err := c.cc.Invoke(ctx, RelationService_BindTestCaseToService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) BindTestCaseToServiceV2(ctx context.Context, in *BindTestCaseToServiceV2Request, opts ...grpc.CallOption) (*BindTestCaseToServiceV2Response, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BindTestCaseToServiceV2Response)
	err := c.cc.Invoke(ctx, RelationService_BindTestCaseToServiceV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) GetServiceBindTestCase(ctx context.Context, in *GetServiceBindTestCaseRequest, opts ...grpc.CallOption) (*GetServiceBindTestCaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceBindTestCaseResponse)
	err := c.cc.Invoke(ctx, RelationService_GetServiceBindTestCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) BindCaseToService(ctx context.Context, in *BindCaseToServiceReq, opts ...grpc.CallOption) (*BindCaseToServiceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BindCaseToServiceResp)
	err := c.cc.Invoke(ctx, RelationService_BindCaseToService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) GetServiceByMethod(ctx context.Context, in *GetServiceByMethodReq, opts ...grpc.CallOption) (*GetServiceByMethodResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceByMethodResp)
	err := c.cc.Invoke(ctx, RelationService_GetServiceByMethod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) GetCaseByService(ctx context.Context, in *GetCaseByServiceReq, opts ...grpc.CallOption) (*GetCaseByServiceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCaseByServiceResp)
	err := c.cc.Invoke(ctx, RelationService_GetCaseByService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) GetTeamByService(ctx context.Context, in *GetTeamByServiceReq, opts ...grpc.CallOption) (*GetTeamByServiceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTeamByServiceResp)
	err := c.cc.Invoke(ctx, RelationService_GetTeamByService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) GetAllServiceTeams(ctx context.Context, in *GetAllServiceTeamsReq, opts ...grpc.CallOption) (*GetAllServiceTeamsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllServiceTeamsResp)
	err := c.cc.Invoke(ctx, RelationService_GetAllServiceTeams_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) GetAllServiceMethods(ctx context.Context, in *GetAllServiceMethodsReq, opts ...grpc.CallOption) (*GetAllServiceMethodsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllServiceMethodsResp)
	err := c.cc.Invoke(ctx, RelationService_GetAllServiceMethods_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relationServiceClient) GetCaseOnlyByService(ctx context.Context, in *GetCaseOnlyByServiceReq, opts ...grpc.CallOption) (*GetCaseOnlyByServiceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCaseOnlyByServiceResp)
	err := c.cc.Invoke(ctx, RelationService_GetCaseOnlyByService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RelationServiceServer is the server API for RelationService service.
// All implementations must embed UnimplementedRelationServiceServer
// for forward compatibility.
type RelationServiceServer interface {
	// Deprecated: use `BindTestCaseToServiceV2` instead.
	BindTestCaseToService(context.Context, *BindTestCaseToServiceRequest) (*BindTestCaseToServiceResponse, error)
	// Deprecated: use `BindCaseToService` instead.
	BindTestCaseToServiceV2(context.Context, *BindTestCaseToServiceV2Request) (*BindTestCaseToServiceV2Response, error)
	// Deprecated: use `GetCaseByService` instead.
	GetServiceBindTestCase(context.Context, *GetServiceBindTestCaseRequest) (*GetServiceBindTestCaseResponse, error)
	// BindCaseToService 绑定服务与测试用例
	BindCaseToService(context.Context, *BindCaseToServiceReq) (*BindCaseToServiceResp, error)
	// GetServiceByMethod 通过方法名称获取关联的服务
	GetServiceByMethod(context.Context, *GetServiceByMethodReq) (*GetServiceByMethodResp, error)
	// GetCaseByService 通过服务名称获取关联的测试用例
	GetCaseByService(context.Context, *GetCaseByServiceReq) (*GetCaseByServiceResp, error)
	// GetTeamByService 通过服务名称获取关联的团队
	GetTeamByService(context.Context, *GetTeamByServiceReq) (*GetTeamByServiceResp, error)
	// GetAllServiceTeams 获取全部服务与团队关系
	GetAllServiceTeams(context.Context, *GetAllServiceTeamsReq) (*GetAllServiceTeamsResp, error)
	// GetAllServiceMethods 获取全部服务与接口关系
	GetAllServiceMethods(context.Context, *GetAllServiceMethodsReq) (*GetAllServiceMethodsResp, error)
	// GetCaseOnlyByService 只通过服务名称获取关联的测试用例
	GetCaseOnlyByService(context.Context, *GetCaseOnlyByServiceReq) (*GetCaseOnlyByServiceResp, error)
	mustEmbedUnimplementedRelationServiceServer()
}

// UnimplementedRelationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRelationServiceServer struct{}

func (UnimplementedRelationServiceServer) BindTestCaseToService(context.Context, *BindTestCaseToServiceRequest) (*BindTestCaseToServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindTestCaseToService not implemented")
}
func (UnimplementedRelationServiceServer) BindTestCaseToServiceV2(context.Context, *BindTestCaseToServiceV2Request) (*BindTestCaseToServiceV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindTestCaseToServiceV2 not implemented")
}
func (UnimplementedRelationServiceServer) GetServiceBindTestCase(context.Context, *GetServiceBindTestCaseRequest) (*GetServiceBindTestCaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceBindTestCase not implemented")
}
func (UnimplementedRelationServiceServer) BindCaseToService(context.Context, *BindCaseToServiceReq) (*BindCaseToServiceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindCaseToService not implemented")
}
func (UnimplementedRelationServiceServer) GetServiceByMethod(context.Context, *GetServiceByMethodReq) (*GetServiceByMethodResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceByMethod not implemented")
}
func (UnimplementedRelationServiceServer) GetCaseByService(context.Context, *GetCaseByServiceReq) (*GetCaseByServiceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCaseByService not implemented")
}
func (UnimplementedRelationServiceServer) GetTeamByService(context.Context, *GetTeamByServiceReq) (*GetTeamByServiceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTeamByService not implemented")
}
func (UnimplementedRelationServiceServer) GetAllServiceTeams(context.Context, *GetAllServiceTeamsReq) (*GetAllServiceTeamsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllServiceTeams not implemented")
}
func (UnimplementedRelationServiceServer) GetAllServiceMethods(context.Context, *GetAllServiceMethodsReq) (*GetAllServiceMethodsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllServiceMethods not implemented")
}
func (UnimplementedRelationServiceServer) GetCaseOnlyByService(context.Context, *GetCaseOnlyByServiceReq) (*GetCaseOnlyByServiceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCaseOnlyByService not implemented")
}
func (UnimplementedRelationServiceServer) mustEmbedUnimplementedRelationServiceServer() {}
func (UnimplementedRelationServiceServer) testEmbeddedByValue()                         {}

// UnsafeRelationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RelationServiceServer will
// result in compilation errors.
type UnsafeRelationServiceServer interface {
	mustEmbedUnimplementedRelationServiceServer()
}

func RegisterRelationServiceServer(s grpc.ServiceRegistrar, srv RelationServiceServer) {
	// If the following call pancis, it indicates UnimplementedRelationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RelationService_ServiceDesc, srv)
}

func _RelationService_BindTestCaseToService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindTestCaseToServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).BindTestCaseToService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_BindTestCaseToService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).BindTestCaseToService(ctx, req.(*BindTestCaseToServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_BindTestCaseToServiceV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindTestCaseToServiceV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).BindTestCaseToServiceV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_BindTestCaseToServiceV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).BindTestCaseToServiceV2(ctx, req.(*BindTestCaseToServiceV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_GetServiceBindTestCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceBindTestCaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).GetServiceBindTestCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_GetServiceBindTestCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).GetServiceBindTestCase(ctx, req.(*GetServiceBindTestCaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_BindCaseToService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindCaseToServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).BindCaseToService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_BindCaseToService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).BindCaseToService(ctx, req.(*BindCaseToServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_GetServiceByMethod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceByMethodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).GetServiceByMethod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_GetServiceByMethod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).GetServiceByMethod(ctx, req.(*GetServiceByMethodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_GetCaseByService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCaseByServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).GetCaseByService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_GetCaseByService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).GetCaseByService(ctx, req.(*GetCaseByServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_GetTeamByService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamByServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).GetTeamByService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_GetTeamByService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).GetTeamByService(ctx, req.(*GetTeamByServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_GetAllServiceTeams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllServiceTeamsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).GetAllServiceTeams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_GetAllServiceTeams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).GetAllServiceTeams(ctx, req.(*GetAllServiceTeamsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_GetAllServiceMethods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllServiceMethodsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).GetAllServiceMethods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_GetAllServiceMethods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).GetAllServiceMethods(ctx, req.(*GetAllServiceMethodsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelationService_GetCaseOnlyByService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCaseOnlyByServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelationServiceServer).GetCaseOnlyByService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelationService_GetCaseOnlyByService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelationServiceServer).GetCaseOnlyByService(ctx, req.(*GetCaseOnlyByServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RelationService_ServiceDesc is the grpc.ServiceDesc for RelationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RelationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "relation.RelationService",
	HandlerType: (*RelationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BindTestCaseToService",
			Handler:    _RelationService_BindTestCaseToService_Handler,
		},
		{
			MethodName: "BindTestCaseToServiceV2",
			Handler:    _RelationService_BindTestCaseToServiceV2_Handler,
		},
		{
			MethodName: "GetServiceBindTestCase",
			Handler:    _RelationService_GetServiceBindTestCase_Handler,
		},
		{
			MethodName: "BindCaseToService",
			Handler:    _RelationService_BindCaseToService_Handler,
		},
		{
			MethodName: "GetServiceByMethod",
			Handler:    _RelationService_GetServiceByMethod_Handler,
		},
		{
			MethodName: "GetCaseByService",
			Handler:    _RelationService_GetCaseByService_Handler,
		},
		{
			MethodName: "GetTeamByService",
			Handler:    _RelationService_GetTeamByService_Handler,
		},
		{
			MethodName: "GetAllServiceTeams",
			Handler:    _RelationService_GetAllServiceTeams_Handler,
		},
		{
			MethodName: "GetAllServiceMethods",
			Handler:    _RelationService_GetAllServiceMethods_Handler,
		},
		{
			MethodName: "GetCaseOnlyByService",
			Handler:    _RelationService_GetCaseOnlyByService_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "relation/relation.proto",
}
