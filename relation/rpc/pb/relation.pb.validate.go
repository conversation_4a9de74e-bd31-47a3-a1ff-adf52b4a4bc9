// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: relation/relation.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on BindTestCaseToServiceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindTestCaseToServiceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindTestCaseToServiceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BindTestCaseToServiceRequestMultiError, or nil if none found.
func (m *BindTestCaseToServiceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BindTestCaseToServiceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_BindTestCaseToServiceRequest_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := BindTestCaseToServiceRequestValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_BindTestCaseToServiceRequest_GeneralConfigId_Pattern.MatchString(m.GetGeneralConfigId()) {
		err := BindTestCaseToServiceRequestValidationError{
			field:  "GeneralConfigId",
			reason: "value does not match regex pattern \"(?:^general_config_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetServiceName() != "" {

		if l := utf8.RuneCountInString(m.GetServiceName()); l < 1 || l > 64 {
			err := BindTestCaseToServiceRequestValidationError{
				field:  "ServiceName",
				reason: "value length must be between 1 and 64 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetInterfacePath() != "" {

		if l := utf8.RuneCountInString(m.GetInterfacePath()); l < 1 || l > 256 {
			err := BindTestCaseToServiceRequestValidationError{
				field:  "InterfacePath",
				reason: "value length must be between 1 and 256 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetCmd() != "" {

		if l := utf8.RuneCountInString(m.GetCmd()); l < 1 || l > 64 {
			err := BindTestCaseToServiceRequestValidationError{
				field:  "Cmd",
				reason: "value length must be between 1 and 64 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetCaseId() != "" {

		if !_BindTestCaseToServiceRequest_CaseId_Pattern.MatchString(m.GetCaseId()) {
			err := BindTestCaseToServiceRequestValidationError{
				field:  "CaseId",
				reason: "value does not match regex pattern \"(?:^case_id:.+?|^interface_case_id:.+?)\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return BindTestCaseToServiceRequestMultiError(errors)
	}

	return nil
}

// BindTestCaseToServiceRequestMultiError is an error wrapping multiple
// validation errors returned by BindTestCaseToServiceRequest.ValidateAll() if
// the designated constraints aren't met.
type BindTestCaseToServiceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindTestCaseToServiceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindTestCaseToServiceRequestMultiError) AllErrors() []error { return m }

// BindTestCaseToServiceRequestValidationError is the validation error returned
// by BindTestCaseToServiceRequest.Validate if the designated constraints
// aren't met.
type BindTestCaseToServiceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindTestCaseToServiceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindTestCaseToServiceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindTestCaseToServiceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindTestCaseToServiceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindTestCaseToServiceRequestValidationError) ErrorName() string {
	return "BindTestCaseToServiceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BindTestCaseToServiceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindTestCaseToServiceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindTestCaseToServiceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindTestCaseToServiceRequestValidationError{}

var _BindTestCaseToServiceRequest_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _BindTestCaseToServiceRequest_GeneralConfigId_Pattern = regexp.MustCompile("(?:^general_config_id:.+?)")

var _BindTestCaseToServiceRequest_CaseId_Pattern = regexp.MustCompile("(?:^case_id:.+?|^interface_case_id:.+?)")

// Validate checks the field values on BindTestCaseToServiceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindTestCaseToServiceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindTestCaseToServiceResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BindTestCaseToServiceResponseMultiError, or nil if none found.
func (m *BindTestCaseToServiceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BindTestCaseToServiceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BindTestCaseToServiceResponseMultiError(errors)
	}

	return nil
}

// BindTestCaseToServiceResponseMultiError is an error wrapping multiple
// validation errors returned by BindTestCaseToServiceResponse.ValidateAll()
// if the designated constraints aren't met.
type BindTestCaseToServiceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindTestCaseToServiceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindTestCaseToServiceResponseMultiError) AllErrors() []error { return m }

// BindTestCaseToServiceResponseValidationError is the validation error
// returned by BindTestCaseToServiceResponse.Validate if the designated
// constraints aren't met.
type BindTestCaseToServiceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindTestCaseToServiceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindTestCaseToServiceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindTestCaseToServiceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindTestCaseToServiceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindTestCaseToServiceResponseValidationError) ErrorName() string {
	return "BindTestCaseToServiceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BindTestCaseToServiceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindTestCaseToServiceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindTestCaseToServiceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindTestCaseToServiceResponseValidationError{}

// Validate checks the field values on BindTestCaseToServiceV2Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindTestCaseToServiceV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindTestCaseToServiceV2Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BindTestCaseToServiceV2RequestMultiError, or nil if none found.
func (m *BindTestCaseToServiceV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *BindTestCaseToServiceV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestUrl

	// no validation rules for PrecisionInfo

	// no validation rules for RequestBody

	// no validation rules for ResponseBody

	if len(errors) > 0 {
		return BindTestCaseToServiceV2RequestMultiError(errors)
	}

	return nil
}

// BindTestCaseToServiceV2RequestMultiError is an error wrapping multiple
// validation errors returned by BindTestCaseToServiceV2Request.ValidateAll()
// if the designated constraints aren't met.
type BindTestCaseToServiceV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindTestCaseToServiceV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindTestCaseToServiceV2RequestMultiError) AllErrors() []error { return m }

// BindTestCaseToServiceV2RequestValidationError is the validation error
// returned by BindTestCaseToServiceV2Request.Validate if the designated
// constraints aren't met.
type BindTestCaseToServiceV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindTestCaseToServiceV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindTestCaseToServiceV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindTestCaseToServiceV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindTestCaseToServiceV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindTestCaseToServiceV2RequestValidationError) ErrorName() string {
	return "BindTestCaseToServiceV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e BindTestCaseToServiceV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindTestCaseToServiceV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindTestCaseToServiceV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindTestCaseToServiceV2RequestValidationError{}

// Validate checks the field values on BindTestCaseToServiceV2Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindTestCaseToServiceV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindTestCaseToServiceV2Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BindTestCaseToServiceV2ResponseMultiError, or nil if none found.
func (m *BindTestCaseToServiceV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *BindTestCaseToServiceV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BindTestCaseToServiceV2ResponseMultiError(errors)
	}

	return nil
}

// BindTestCaseToServiceV2ResponseMultiError is an error wrapping multiple
// validation errors returned by BindTestCaseToServiceV2Response.ValidateAll()
// if the designated constraints aren't met.
type BindTestCaseToServiceV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindTestCaseToServiceV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindTestCaseToServiceV2ResponseMultiError) AllErrors() []error { return m }

// BindTestCaseToServiceV2ResponseValidationError is the validation error
// returned by BindTestCaseToServiceV2Response.Validate if the designated
// constraints aren't met.
type BindTestCaseToServiceV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindTestCaseToServiceV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindTestCaseToServiceV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindTestCaseToServiceV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindTestCaseToServiceV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindTestCaseToServiceV2ResponseValidationError) ErrorName() string {
	return "BindTestCaseToServiceV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BindTestCaseToServiceV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindTestCaseToServiceV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindTestCaseToServiceV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindTestCaseToServiceV2ResponseValidationError{}

// Validate checks the field values on Relation with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Relation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Relation with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RelationMultiError, or nil
// if none found.
func (m *Relation) ValidateAll() error {
	return m.validate(true)
}

func (m *Relation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_Relation_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := RelationValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Relation_GeneralConfigId_Pattern.MatchString(m.GetGeneralConfigId()) {
		err := RelationValidationError{
			field:  "GeneralConfigId",
			reason: "value does not match regex pattern \"(?:^general_config_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetServiceName() != "" {

		if l := utf8.RuneCountInString(m.GetServiceName()); l < 1 || l > 64 {
			err := RelationValidationError{
				field:  "ServiceName",
				reason: "value length must be between 1 and 64 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetInterfacePath() != "" {

		if l := utf8.RuneCountInString(m.GetInterfacePath()); l < 1 || l > 256 {
			err := RelationValidationError{
				field:  "InterfacePath",
				reason: "value length must be between 1 and 256 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetCmd() != "" {

		if l := utf8.RuneCountInString(m.GetCmd()); l < 1 || l > 64 {
			err := RelationValidationError{
				field:  "Cmd",
				reason: "value length must be between 1 and 64 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetCaseId() != "" {

		if !_Relation_CaseId_Pattern.MatchString(m.GetCaseId()) {
			err := RelationValidationError{
				field:  "CaseId",
				reason: "value does not match regex pattern \"(?:^case_id:.+?|^interface_case_id:.+?)\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return RelationMultiError(errors)
	}

	return nil
}

// RelationMultiError is an error wrapping multiple validation errors returned
// by Relation.ValidateAll() if the designated constraints aren't met.
type RelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelationMultiError) AllErrors() []error { return m }

// RelationValidationError is the validation error returned by
// Relation.Validate if the designated constraints aren't met.
type RelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelationValidationError) ErrorName() string { return "RelationValidationError" }

// Error satisfies the builtin error interface
func (e RelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelationValidationError{}

var _Relation_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _Relation_GeneralConfigId_Pattern = regexp.MustCompile("(?:^general_config_id:.+?)")

var _Relation_CaseId_Pattern = regexp.MustCompile("(?:^case_id:.+?|^interface_case_id:.+?)")

// Validate checks the field values on GetServiceBindTestCaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceBindTestCaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceBindTestCaseRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceBindTestCaseRequestMultiError, or nil if none found.
func (m *GetServiceBindTestCaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceBindTestCaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetServiceBindTestCaseRequest_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := GetServiceBindTestCaseRequestValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetServiceBindTestCaseRequest_GeneralConfigId_Pattern.MatchString(m.GetGeneralConfigId()) {
		err := GetServiceBindTestCaseRequestValidationError{
			field:  "GeneralConfigId",
			reason: "value does not match regex pattern \"(?:^general_config_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetServiceBindTestCaseRequestMultiError(errors)
	}

	return nil
}

// GetServiceBindTestCaseRequestMultiError is an error wrapping multiple
// validation errors returned by GetServiceBindTestCaseRequest.ValidateAll()
// if the designated constraints aren't met.
type GetServiceBindTestCaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceBindTestCaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceBindTestCaseRequestMultiError) AllErrors() []error { return m }

// GetServiceBindTestCaseRequestValidationError is the validation error
// returned by GetServiceBindTestCaseRequest.Validate if the designated
// constraints aren't met.
type GetServiceBindTestCaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceBindTestCaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceBindTestCaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceBindTestCaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceBindTestCaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceBindTestCaseRequestValidationError) ErrorName() string {
	return "GetServiceBindTestCaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceBindTestCaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceBindTestCaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceBindTestCaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceBindTestCaseRequestValidationError{}

var _GetServiceBindTestCaseRequest_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _GetServiceBindTestCaseRequest_GeneralConfigId_Pattern = regexp.MustCompile("(?:^general_config_id:.+?)")

// Validate checks the field values on GetServiceBindTestCaseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceBindTestCaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceBindTestCaseResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceBindTestCaseResponseMultiError, or nil if none found.
func (m *GetServiceBindTestCaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceBindTestCaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetServiceBindTestCaseResponseValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetServiceBindTestCaseResponseValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetServiceBindTestCaseResponseValidationError{
					field:  fmt.Sprintf("Relations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetServiceBindTestCaseResponseMultiError(errors)
	}

	return nil
}

// GetServiceBindTestCaseResponseMultiError is an error wrapping multiple
// validation errors returned by GetServiceBindTestCaseResponse.ValidateAll()
// if the designated constraints aren't met.
type GetServiceBindTestCaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceBindTestCaseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceBindTestCaseResponseMultiError) AllErrors() []error { return m }

// GetServiceBindTestCaseResponseValidationError is the validation error
// returned by GetServiceBindTestCaseResponse.Validate if the designated
// constraints aren't met.
type GetServiceBindTestCaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceBindTestCaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceBindTestCaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceBindTestCaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceBindTestCaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceBindTestCaseResponseValidationError) ErrorName() string {
	return "GetServiceBindTestCaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceBindTestCaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceBindTestCaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceBindTestCaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceBindTestCaseResponseValidationError{}

// Validate checks the field values on PrecisionInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PrecisionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrecisionInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PrecisionInfoMultiError, or
// nil if none found.
func (m *PrecisionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PrecisionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_PrecisionInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := PrecisionInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PrecisionInfo_GeneralConfigId_Pattern.MatchString(m.GetGeneralConfigId()) {
		err := PrecisionInfoValidationError{
			field:  "GeneralConfigId",
			reason: "value does not match regex pattern \"(?:^general_config_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDocumentId() != "" {

		if !_PrecisionInfo_DocumentId_Pattern.MatchString(m.GetDocumentId()) {
			err := PrecisionInfoValidationError{
				field:  "DocumentId",
				reason: "value does not match regex pattern \"(?:^interface_document_id:.+?)\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if !_PrecisionInfo_CaseId_Pattern.MatchString(m.GetCaseId()) {
		err := PrecisionInfoValidationError{
			field:  "CaseId",
			reason: "value does not match regex pattern \"(?:^case_id:.+?|^interface_case_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PrecisionInfoMultiError(errors)
	}

	return nil
}

// PrecisionInfoMultiError is an error wrapping multiple validation errors
// returned by PrecisionInfo.ValidateAll() if the designated constraints
// aren't met.
type PrecisionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrecisionInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrecisionInfoMultiError) AllErrors() []error { return m }

// PrecisionInfoValidationError is the validation error returned by
// PrecisionInfo.Validate if the designated constraints aren't met.
type PrecisionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrecisionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrecisionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrecisionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrecisionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrecisionInfoValidationError) ErrorName() string { return "PrecisionInfoValidationError" }

// Error satisfies the builtin error interface
func (e PrecisionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrecisionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrecisionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrecisionInfoValidationError{}

var _PrecisionInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _PrecisionInfo_GeneralConfigId_Pattern = regexp.MustCompile("(?:^general_config_id:.+?)")

var _PrecisionInfo_DocumentId_Pattern = regexp.MustCompile("(?:^interface_document_id:.+?)")

var _PrecisionInfo_CaseId_Pattern = regexp.MustCompile("(?:^case_id:.+?|^interface_case_id:.+?)")

// Validate checks the field values on BindCaseToServiceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindCaseToServiceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindCaseToServiceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BindCaseToServiceReqMultiError, or nil if none found.
func (m *BindCaseToServiceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BindCaseToServiceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetRequestUrl()) < 2 {
		err := BindCaseToServiceReqValidationError{
			field:  "RequestUrl",
			reason: "value length must be at least 2 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPrecisionInfo() == nil {
		err := BindCaseToServiceReqValidationError{
			field:  "PrecisionInfo",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPrecisionInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BindCaseToServiceReqValidationError{
					field:  "PrecisionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BindCaseToServiceReqValidationError{
					field:  "PrecisionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrecisionInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BindCaseToServiceReqValidationError{
				field:  "PrecisionInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestBody

	// no validation rules for ResponseBody

	if len(errors) > 0 {
		return BindCaseToServiceReqMultiError(errors)
	}

	return nil
}

// BindCaseToServiceReqMultiError is an error wrapping multiple validation
// errors returned by BindCaseToServiceReq.ValidateAll() if the designated
// constraints aren't met.
type BindCaseToServiceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindCaseToServiceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindCaseToServiceReqMultiError) AllErrors() []error { return m }

// BindCaseToServiceReqValidationError is the validation error returned by
// BindCaseToServiceReq.Validate if the designated constraints aren't met.
type BindCaseToServiceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindCaseToServiceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindCaseToServiceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindCaseToServiceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindCaseToServiceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindCaseToServiceReqValidationError) ErrorName() string {
	return "BindCaseToServiceReqValidationError"
}

// Error satisfies the builtin error interface
func (e BindCaseToServiceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindCaseToServiceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindCaseToServiceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindCaseToServiceReqValidationError{}

// Validate checks the field values on BindCaseToServiceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindCaseToServiceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindCaseToServiceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BindCaseToServiceRespMultiError, or nil if none found.
func (m *BindCaseToServiceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BindCaseToServiceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BindCaseToServiceRespMultiError(errors)
	}

	return nil
}

// BindCaseToServiceRespMultiError is an error wrapping multiple validation
// errors returned by BindCaseToServiceResp.ValidateAll() if the designated
// constraints aren't met.
type BindCaseToServiceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindCaseToServiceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindCaseToServiceRespMultiError) AllErrors() []error { return m }

// BindCaseToServiceRespValidationError is the validation error returned by
// BindCaseToServiceResp.Validate if the designated constraints aren't met.
type BindCaseToServiceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindCaseToServiceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindCaseToServiceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindCaseToServiceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindCaseToServiceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindCaseToServiceRespValidationError) ErrorName() string {
	return "BindCaseToServiceRespValidationError"
}

// Error satisfies the builtin error interface
func (e BindCaseToServiceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindCaseToServiceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindCaseToServiceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindCaseToServiceRespValidationError{}

// Validate checks the field values on ServiceMethodRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceMethodRelation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceMethodRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceMethodRelationMultiError, or nil if none found.
func (m *ServiceMethodRelation) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceMethodRelation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Service

	// no validation rules for Namespace

	// no validation rules for Type

	// no validation rules for Method

	// no validation rules for ReqPath

	// no validation rules for Cmd

	// no validation rules for Deprecated

	// no validation rules for QueryPath

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return ServiceMethodRelationMultiError(errors)
	}

	return nil
}

// ServiceMethodRelationMultiError is an error wrapping multiple validation
// errors returned by ServiceMethodRelation.ValidateAll() if the designated
// constraints aren't met.
type ServiceMethodRelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceMethodRelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceMethodRelationMultiError) AllErrors() []error { return m }

// ServiceMethodRelationValidationError is the validation error returned by
// ServiceMethodRelation.Validate if the designated constraints aren't met.
type ServiceMethodRelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceMethodRelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceMethodRelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceMethodRelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceMethodRelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceMethodRelationValidationError) ErrorName() string {
	return "ServiceMethodRelationValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceMethodRelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceMethodRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceMethodRelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceMethodRelationValidationError{}

// Validate checks the field values on GetServiceByMethodReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceByMethodReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceByMethodReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetServiceByMethodReqMultiError, or nil if none found.
func (m *GetServiceByMethodReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceByMethodReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Method

	if len(errors) > 0 {
		return GetServiceByMethodReqMultiError(errors)
	}

	return nil
}

// GetServiceByMethodReqMultiError is an error wrapping multiple validation
// errors returned by GetServiceByMethodReq.ValidateAll() if the designated
// constraints aren't met.
type GetServiceByMethodReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceByMethodReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceByMethodReqMultiError) AllErrors() []error { return m }

// GetServiceByMethodReqValidationError is the validation error returned by
// GetServiceByMethodReq.Validate if the designated constraints aren't met.
type GetServiceByMethodReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceByMethodReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceByMethodReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceByMethodReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceByMethodReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceByMethodReqValidationError) ErrorName() string {
	return "GetServiceByMethodReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceByMethodReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceByMethodReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceByMethodReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceByMethodReqValidationError{}

// Validate checks the field values on GetServiceByMethodResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceByMethodResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceByMethodResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetServiceByMethodRespMultiError, or nil if none found.
func (m *GetServiceByMethodResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceByMethodResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetServiceByMethodRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetServiceByMethodRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetServiceByMethodRespValidationError{
					field:  fmt.Sprintf("Relations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetServiceByMethodRespMultiError(errors)
	}

	return nil
}

// GetServiceByMethodRespMultiError is an error wrapping multiple validation
// errors returned by GetServiceByMethodResp.ValidateAll() if the designated
// constraints aren't met.
type GetServiceByMethodRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceByMethodRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceByMethodRespMultiError) AllErrors() []error { return m }

// GetServiceByMethodRespValidationError is the validation error returned by
// GetServiceByMethodResp.Validate if the designated constraints aren't met.
type GetServiceByMethodRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceByMethodRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceByMethodRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceByMethodRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceByMethodRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceByMethodRespValidationError) ErrorName() string {
	return "GetServiceByMethodRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceByMethodRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceByMethodResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceByMethodRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceByMethodRespValidationError{}

// Validate checks the field values on ServiceCaseRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceCaseRelation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceCaseRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceCaseRelationMultiError, or nil if none found.
func (m *ServiceCaseRelation) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceCaseRelation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for GeneralConfigId

	// no validation rules for Service

	// no validation rules for Namespace

	// no validation rules for Method

	// no validation rules for ReqPath

	// no validation rules for Cmd

	// no validation rules for ToService

	// no validation rules for DocumentId

	// no validation rules for CaseId

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return ServiceCaseRelationMultiError(errors)
	}

	return nil
}

// ServiceCaseRelationMultiError is an error wrapping multiple validation
// errors returned by ServiceCaseRelation.ValidateAll() if the designated
// constraints aren't met.
type ServiceCaseRelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceCaseRelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceCaseRelationMultiError) AllErrors() []error { return m }

// ServiceCaseRelationValidationError is the validation error returned by
// ServiceCaseRelation.Validate if the designated constraints aren't met.
type ServiceCaseRelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceCaseRelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceCaseRelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceCaseRelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceCaseRelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceCaseRelationValidationError) ErrorName() string {
	return "ServiceCaseRelationValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceCaseRelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceCaseRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceCaseRelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceCaseRelationValidationError{}

// Validate checks the field values on GetCaseByServiceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseByServiceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseByServiceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseByServiceReqMultiError, or nil if none found.
func (m *GetCaseByServiceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseByServiceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetCaseByServiceReq_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := GetCaseByServiceReqValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetCaseByServiceReq_GeneralConfigId_Pattern.MatchString(m.GetGeneralConfigId()) {
		err := GetCaseByServiceReqValidationError{
			field:  "GeneralConfigId",
			reason: "value does not match regex pattern \"(?:^general_config_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetService()) < 1 {
		err := GetCaseByServiceReqValidationError{
			field:  "Service",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCaseByServiceReqMultiError(errors)
	}

	return nil
}

// GetCaseByServiceReqMultiError is an error wrapping multiple validation
// errors returned by GetCaseByServiceReq.ValidateAll() if the designated
// constraints aren't met.
type GetCaseByServiceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseByServiceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseByServiceReqMultiError) AllErrors() []error { return m }

// GetCaseByServiceReqValidationError is the validation error returned by
// GetCaseByServiceReq.Validate if the designated constraints aren't met.
type GetCaseByServiceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseByServiceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseByServiceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseByServiceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseByServiceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseByServiceReqValidationError) ErrorName() string {
	return "GetCaseByServiceReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseByServiceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseByServiceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseByServiceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseByServiceReqValidationError{}

var _GetCaseByServiceReq_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _GetCaseByServiceReq_GeneralConfigId_Pattern = regexp.MustCompile("(?:^general_config_id:.+?)")

// Validate checks the field values on GetCaseByServiceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseByServiceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseByServiceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseByServiceRespMultiError, or nil if none found.
func (m *GetCaseByServiceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseByServiceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCaseByServiceRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCaseByServiceRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCaseByServiceRespValidationError{
					field:  fmt.Sprintf("Relations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCaseByServiceRespMultiError(errors)
	}

	return nil
}

// GetCaseByServiceRespMultiError is an error wrapping multiple validation
// errors returned by GetCaseByServiceResp.ValidateAll() if the designated
// constraints aren't met.
type GetCaseByServiceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseByServiceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseByServiceRespMultiError) AllErrors() []error { return m }

// GetCaseByServiceRespValidationError is the validation error returned by
// GetCaseByServiceResp.Validate if the designated constraints aren't met.
type GetCaseByServiceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseByServiceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseByServiceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseByServiceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseByServiceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseByServiceRespValidationError) ErrorName() string {
	return "GetCaseByServiceRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseByServiceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseByServiceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseByServiceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseByServiceRespValidationError{}

// Validate checks the field values on GetCaseOnlyByServiceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseOnlyByServiceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseOnlyByServiceReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseOnlyByServiceReqMultiError, or nil if none found.
func (m *GetCaseOnlyByServiceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseOnlyByServiceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetCaseOnlyByServiceReq_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := GetCaseOnlyByServiceReqValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetService()) < 1 {
		err := GetCaseOnlyByServiceReqValidationError{
			field:  "Service",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCaseOnlyByServiceReqMultiError(errors)
	}

	return nil
}

// GetCaseOnlyByServiceReqMultiError is an error wrapping multiple validation
// errors returned by GetCaseOnlyByServiceReq.ValidateAll() if the designated
// constraints aren't met.
type GetCaseOnlyByServiceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseOnlyByServiceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseOnlyByServiceReqMultiError) AllErrors() []error { return m }

// GetCaseOnlyByServiceReqValidationError is the validation error returned by
// GetCaseOnlyByServiceReq.Validate if the designated constraints aren't met.
type GetCaseOnlyByServiceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseOnlyByServiceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseOnlyByServiceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseOnlyByServiceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseOnlyByServiceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseOnlyByServiceReqValidationError) ErrorName() string {
	return "GetCaseOnlyByServiceReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseOnlyByServiceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseOnlyByServiceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseOnlyByServiceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseOnlyByServiceReqValidationError{}

var _GetCaseOnlyByServiceReq_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

// Validate checks the field values on GetCaseOnlyByServiceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseOnlyByServiceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseOnlyByServiceResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseOnlyByServiceRespMultiError, or nil if none found.
func (m *GetCaseOnlyByServiceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseOnlyByServiceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCaseOnlyByServiceRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCaseOnlyByServiceRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCaseOnlyByServiceRespValidationError{
					field:  fmt.Sprintf("Relations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCaseOnlyByServiceRespMultiError(errors)
	}

	return nil
}

// GetCaseOnlyByServiceRespMultiError is an error wrapping multiple validation
// errors returned by GetCaseOnlyByServiceResp.ValidateAll() if the designated
// constraints aren't met.
type GetCaseOnlyByServiceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseOnlyByServiceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseOnlyByServiceRespMultiError) AllErrors() []error { return m }

// GetCaseOnlyByServiceRespValidationError is the validation error returned by
// GetCaseOnlyByServiceResp.Validate if the designated constraints aren't met.
type GetCaseOnlyByServiceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseOnlyByServiceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseOnlyByServiceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseOnlyByServiceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseOnlyByServiceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseOnlyByServiceRespValidationError) ErrorName() string {
	return "GetCaseOnlyByServiceRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseOnlyByServiceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseOnlyByServiceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseOnlyByServiceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseOnlyByServiceRespValidationError{}

// Validate checks the field values on ServiceTeamRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceTeamRelation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceTeamRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceTeamRelationMultiError, or nil if none found.
func (m *ServiceTeamRelation) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceTeamRelation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Service

	// no validation rules for Namespace

	// no validation rules for TeamName

	// no validation rules for TeamDescription

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return ServiceTeamRelationMultiError(errors)
	}

	return nil
}

// ServiceTeamRelationMultiError is an error wrapping multiple validation
// errors returned by ServiceTeamRelation.ValidateAll() if the designated
// constraints aren't met.
type ServiceTeamRelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceTeamRelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceTeamRelationMultiError) AllErrors() []error { return m }

// ServiceTeamRelationValidationError is the validation error returned by
// ServiceTeamRelation.Validate if the designated constraints aren't met.
type ServiceTeamRelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceTeamRelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceTeamRelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceTeamRelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceTeamRelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceTeamRelationValidationError) ErrorName() string {
	return "ServiceTeamRelationValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceTeamRelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceTeamRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceTeamRelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceTeamRelationValidationError{}

// Validate checks the field values on GetTeamByServiceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTeamByServiceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTeamByServiceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTeamByServiceReqMultiError, or nil if none found.
func (m *GetTeamByServiceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTeamByServiceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetService()) < 1 {
		err := GetTeamByServiceReqValidationError{
			field:  "Service",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTeamByServiceReqMultiError(errors)
	}

	return nil
}

// GetTeamByServiceReqMultiError is an error wrapping multiple validation
// errors returned by GetTeamByServiceReq.ValidateAll() if the designated
// constraints aren't met.
type GetTeamByServiceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTeamByServiceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTeamByServiceReqMultiError) AllErrors() []error { return m }

// GetTeamByServiceReqValidationError is the validation error returned by
// GetTeamByServiceReq.Validate if the designated constraints aren't met.
type GetTeamByServiceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTeamByServiceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTeamByServiceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTeamByServiceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTeamByServiceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTeamByServiceReqValidationError) ErrorName() string {
	return "GetTeamByServiceReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetTeamByServiceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTeamByServiceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTeamByServiceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTeamByServiceReqValidationError{}

// Validate checks the field values on GetTeamByServiceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTeamByServiceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTeamByServiceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTeamByServiceRespMultiError, or nil if none found.
func (m *GetTeamByServiceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTeamByServiceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTeamByServiceRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTeamByServiceRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTeamByServiceRespValidationError{
					field:  fmt.Sprintf("Relations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTeamByServiceRespMultiError(errors)
	}

	return nil
}

// GetTeamByServiceRespMultiError is an error wrapping multiple validation
// errors returned by GetTeamByServiceResp.ValidateAll() if the designated
// constraints aren't met.
type GetTeamByServiceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTeamByServiceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTeamByServiceRespMultiError) AllErrors() []error { return m }

// GetTeamByServiceRespValidationError is the validation error returned by
// GetTeamByServiceResp.Validate if the designated constraints aren't met.
type GetTeamByServiceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTeamByServiceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTeamByServiceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTeamByServiceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTeamByServiceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTeamByServiceRespValidationError) ErrorName() string {
	return "GetTeamByServiceRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetTeamByServiceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTeamByServiceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTeamByServiceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTeamByServiceRespValidationError{}

// Validate checks the field values on GetAllServiceTeamsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllServiceTeamsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllServiceTeamsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllServiceTeamsReqMultiError, or nil if none found.
func (m *GetAllServiceTeamsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllServiceTeamsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAllServiceTeamsReqMultiError(errors)
	}

	return nil
}

// GetAllServiceTeamsReqMultiError is an error wrapping multiple validation
// errors returned by GetAllServiceTeamsReq.ValidateAll() if the designated
// constraints aren't met.
type GetAllServiceTeamsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllServiceTeamsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllServiceTeamsReqMultiError) AllErrors() []error { return m }

// GetAllServiceTeamsReqValidationError is the validation error returned by
// GetAllServiceTeamsReq.Validate if the designated constraints aren't met.
type GetAllServiceTeamsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllServiceTeamsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllServiceTeamsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllServiceTeamsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllServiceTeamsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllServiceTeamsReqValidationError) ErrorName() string {
	return "GetAllServiceTeamsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllServiceTeamsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllServiceTeamsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllServiceTeamsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllServiceTeamsReqValidationError{}

// Validate checks the field values on GetAllServiceTeamsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllServiceTeamsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllServiceTeamsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllServiceTeamsRespMultiError, or nil if none found.
func (m *GetAllServiceTeamsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllServiceTeamsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllServiceTeamsRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllServiceTeamsRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllServiceTeamsRespValidationError{
					field:  fmt.Sprintf("Relations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllServiceTeamsRespMultiError(errors)
	}

	return nil
}

// GetAllServiceTeamsRespMultiError is an error wrapping multiple validation
// errors returned by GetAllServiceTeamsResp.ValidateAll() if the designated
// constraints aren't met.
type GetAllServiceTeamsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllServiceTeamsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllServiceTeamsRespMultiError) AllErrors() []error { return m }

// GetAllServiceTeamsRespValidationError is the validation error returned by
// GetAllServiceTeamsResp.Validate if the designated constraints aren't met.
type GetAllServiceTeamsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllServiceTeamsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllServiceTeamsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllServiceTeamsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllServiceTeamsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllServiceTeamsRespValidationError) ErrorName() string {
	return "GetAllServiceTeamsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllServiceTeamsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllServiceTeamsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllServiceTeamsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllServiceTeamsRespValidationError{}

// Validate checks the field values on GetAllServiceMethodsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllServiceMethodsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllServiceMethodsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllServiceMethodsReqMultiError, or nil if none found.
func (m *GetAllServiceMethodsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllServiceMethodsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAllServiceMethodsReqMultiError(errors)
	}

	return nil
}

// GetAllServiceMethodsReqMultiError is an error wrapping multiple validation
// errors returned by GetAllServiceMethodsReq.ValidateAll() if the designated
// constraints aren't met.
type GetAllServiceMethodsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllServiceMethodsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllServiceMethodsReqMultiError) AllErrors() []error { return m }

// GetAllServiceMethodsReqValidationError is the validation error returned by
// GetAllServiceMethodsReq.Validate if the designated constraints aren't met.
type GetAllServiceMethodsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllServiceMethodsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllServiceMethodsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllServiceMethodsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllServiceMethodsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllServiceMethodsReqValidationError) ErrorName() string {
	return "GetAllServiceMethodsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllServiceMethodsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllServiceMethodsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllServiceMethodsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllServiceMethodsReqValidationError{}

// Validate checks the field values on GetAllServiceMethodsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllServiceMethodsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllServiceMethodsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllServiceMethodsRespMultiError, or nil if none found.
func (m *GetAllServiceMethodsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllServiceMethodsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllServiceMethodsRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllServiceMethodsRespValidationError{
						field:  fmt.Sprintf("Relations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllServiceMethodsRespValidationError{
					field:  fmt.Sprintf("Relations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllServiceMethodsRespMultiError(errors)
	}

	return nil
}

// GetAllServiceMethodsRespMultiError is an error wrapping multiple validation
// errors returned by GetAllServiceMethodsResp.ValidateAll() if the designated
// constraints aren't met.
type GetAllServiceMethodsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllServiceMethodsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllServiceMethodsRespMultiError) AllErrors() []error { return m }

// GetAllServiceMethodsRespValidationError is the validation error returned by
// GetAllServiceMethodsResp.Validate if the designated constraints aren't met.
type GetAllServiceMethodsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllServiceMethodsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllServiceMethodsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllServiceMethodsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllServiceMethodsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllServiceMethodsRespValidationError) ErrorName() string {
	return "GetAllServiceMethodsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllServiceMethodsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllServiceMethodsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllServiceMethodsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllServiceMethodsRespValidationError{}
