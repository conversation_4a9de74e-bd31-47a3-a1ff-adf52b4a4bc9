# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "uidetection_proto",
    srcs = [
        "ui_detection.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
    ],
)

proto_library_with_info(
    name = "uidetection_proto_with_info",
    deps = [
        ":uidetection_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "uidetection_java_proto",
    deps = [":uidetection_proto"],
)

java_grpc_library(
    name = "uidetection_java_grpc",
    srcs = [":uidetection_proto"],
    deps = [":uidetection_java_proto"],
)

java_gapic_library(
    name = "uidetection_java_gapic",
    srcs = [":uidetection_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "ui_detection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosuidetection_v1.yaml",
    test_deps = [
        ":uidetection_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":uidetection_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "uidetection_java_gapic_test_suite",
    test_classes = [
        "com.google.chromeos.uidetection.v1.UiDetectionServiceClientHttpJsonTest",
        "com.google.chromeos.uidetection.v1.UiDetectionServiceClientTest",
    ],
    runtime_deps = [":uidetection_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-chromeos-uidetection-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":uidetection_java_gapic",
        ":uidetection_java_grpc",
        ":uidetection_java_proto",
        ":uidetection_proto",
    ],
)

go_proto_library(
    name = "uidetection_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/chromeos/uidetection/v1",
    protos = [":uidetection_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "uidetection_go_gapic",
    srcs = [":uidetection_proto_with_info"],
    grpc_service_config = "ui_detection_grpc_service_config.json",
    importpath = "google.golang.org/google/chromeos/uidetection/v1;uidetection",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "chromeosuidetection_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":uidetection_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-chromeos-uidetection-v1-go",
    deps = [
        ":uidetection_go_gapic",
        ":uidetection_go_gapic_srcjar-metadata.srcjar",
        ":uidetection_go_gapic_srcjar-snippets.srcjar",
        ":uidetection_go_gapic_srcjar-test.srcjar",
        ":uidetection_go_proto",
    ],
)

py_gapic_library(
    name = "uidetection_py_gapic",
    srcs = [":uidetection_proto"],
    grpc_service_config = "ui_detection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosuidetection_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "uidetection_py_gapic_test",
    srcs = [
        "uidetection_py_gapic_pytest.py",
        "uidetection_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":uidetection_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "chromeos-uidetection-v1-py",
    deps = [
        ":uidetection_py_gapic",
    ],
)

php_proto_library(
    name = "uidetection_php_proto",
    deps = [":uidetection_proto"],
)

php_gapic_library(
    name = "uidetection_php_gapic",
    srcs = [":uidetection_proto_with_info"],
    grpc_service_config = "ui_detection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosuidetection_v1.yaml",
    transport = "grpc+rest",
    deps = [":uidetection_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-chromeos-uidetection-v1-php",
    deps = [
        ":uidetection_php_gapic",
        ":uidetection_php_proto",
    ],
)

nodejs_gapic_library(
    name = "uidetection_nodejs_gapic",
    package_name = "@google-cloud/uidetection",
    src = ":uidetection_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "ui_detection_grpc_service_config.json",
    package = "google.chromeos.uidetection.v1",
    rest_numeric_enums = True,
    service_yaml = "chromeosuidetection_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "chromeos-uidetection-v1-nodejs",
    deps = [
        ":uidetection_nodejs_gapic",
        ":uidetection_proto",
    ],
)

ruby_proto_library(
    name = "uidetection_ruby_proto",
    deps = [":uidetection_proto"],
)

ruby_grpc_library(
    name = "uidetection_ruby_grpc",
    srcs = [":uidetection_proto"],
    deps = [":uidetection_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "uidetection_ruby_gapic",
    srcs = [":uidetection_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-chromeos-uidetection-v1",
    ],
    grpc_service_config = "ui_detection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosuidetection_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":uidetection_ruby_grpc",
        ":uidetection_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-chromeos-uidetection-v1-ruby",
    deps = [
        ":uidetection_ruby_gapic",
        ":uidetection_ruby_grpc",
        ":uidetection_ruby_proto",
    ],
)

csharp_proto_library(
    name = "uidetection_csharp_proto",
    deps = [":uidetection_proto"],
)

csharp_grpc_library(
    name = "uidetection_csharp_grpc",
    srcs = [":uidetection_proto"],
    deps = [":uidetection_csharp_proto"],
)

csharp_gapic_library(
    name = "uidetection_csharp_gapic",
    srcs = [":uidetection_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "ui_detection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosuidetection_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":uidetection_csharp_grpc",
        ":uidetection_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-chromeos-uidetection-v1-csharp",
    deps = [
        ":uidetection_csharp_gapic",
        ":uidetection_csharp_grpc",
        ":uidetection_csharp_proto",
    ],
)

cc_proto_library(
    name = "uidetection_cc_proto",
    deps = [":uidetection_proto"],
)

cc_grpc_library(
    name = "uidetection_cc_grpc",
    srcs = [":uidetection_proto"],
    grpc_only = True,
    deps = [":uidetection_cc_proto"],
)
