type: google.api.Service
config_version: 3
name: chromeosuidetection.googleapis.com
title: ChromeOS UI Detection API

apis:
- name: google.chromeos.uidetection.v1.UiDetectionService

documentation:
  summary: |-
    ChromeOS UI Detection API allows image-based UI detection in E2E testing
    frameworks of ChromeOS. It will be used by ChromeOS labs and ChromeOS
    partners.

backend:
  rules:
  - selector: google.chromeos.uidetection.v1.UiDetectionService.ExecuteDetection
    deadline: 30.0
