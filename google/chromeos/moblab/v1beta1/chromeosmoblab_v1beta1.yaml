type: google.api.Service
config_version: 3
name: chromeosmoblab.googleapis.com
title: Chrome OS Moblab API

control:
  environment: servicecontrol.googleapis.com

apis:
- name: google.chromeos.moblab.v1beta1.BuildService

backend:
  rules:
  - selector: 'google.chromeos.moblab.v1beta1.BuildService.*'
    deadline: 30.0

documentation:
  summary: |-
    Moblab API is an external-facing API that Moblabs deployed in remote external labs of Chrome OS
    partners can communicate with for various testing needs.

authentication:
  rules:
  - selector: 'google.chromeos.moblab.v1beta1.BuildService.*'
    oauth:
      canonical_scopes: https://www.googleapis.com/auth/moblabapi
