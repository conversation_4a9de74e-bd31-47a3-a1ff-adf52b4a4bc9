# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "moblab_proto",
    srcs = [
        "build_service.proto",
        "resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "moblab_proto_with_info",
    deps = [
        ":moblab_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "moblab_java_proto",
    deps = [":moblab_proto"],
)

java_grpc_library(
    name = "moblab_java_grpc",
    srcs = [":moblab_proto"],
    deps = [":moblab_java_proto"],
)

java_gapic_library(
    name = "moblab_java_gapic",
    srcs = [":moblab_proto_with_info"],
    gapic_yaml = "chromeosmoblab_gapic.yaml",
    grpc_service_config = "moblab_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosmoblab_v1beta1.yaml",
    test_deps = [
        ":moblab_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":moblab_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "moblab_java_gapic_test_suite",
    test_classes = [
        "com.google.chromeos.moblab.v1beta1.BuildServiceClientHttpJsonTest",
        "com.google.chromeos.moblab.v1beta1.BuildServiceClientTest",
    ],
    runtime_deps = [":moblab_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-chromeos-moblab-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":moblab_java_gapic",
        ":moblab_java_grpc",
        ":moblab_java_proto",
        ":moblab_proto",
    ],
)

go_proto_library(
    name = "moblab_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/chromeos/moblab/v1beta1",
    protos = [":moblab_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "moblab_go_gapic",
    srcs = [":moblab_proto_with_info"],
    grpc_service_config = "moblab_grpc_service_config.json",
    importpath = "google.golang.org/google/chromeos/moblab/v1beta1;moblab",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "chromeosmoblab_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":moblab_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-chromeos-moblab-v1beta1-go",
    deps = [
        ":moblab_go_gapic",
        ":moblab_go_gapic_srcjar-metadata.srcjar",
        ":moblab_go_gapic_srcjar-snippets.srcjar",
        ":moblab_go_gapic_srcjar-test.srcjar",
        ":moblab_go_proto",
    ],
)

py_gapic_library(
    name = "moblab_py_gapic",
    srcs = [":moblab_proto"],
    grpc_service_config = "moblab_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosmoblab_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "moblab_py_gapic_test",
    srcs = [
        "moblab_py_gapic_pytest.py",
        "moblab_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":moblab_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "chromeos-moblab-v1beta1-py",
    deps = [
        ":moblab_py_gapic",
    ],
)

php_proto_library(
    name = "moblab_php_proto",
    deps = [":moblab_proto"],
)

php_gapic_library(
    name = "moblab_php_gapic",
    srcs = [":moblab_proto_with_info"],
    grpc_service_config = "moblab_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosmoblab_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":moblab_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-chromeos-moblab-v1beta1-php",
    deps = [
        ":moblab_php_gapic",
        ":moblab_php_proto",
    ],
)

nodejs_gapic_library(
    name = "moblab_nodejs_gapic",
    package_name = "@google-cloud/moblab",
    src = ":moblab_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "moblab_grpc_service_config.json",
    package = "google.chromeos.moblab.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "chromeosmoblab_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "chromeos-moblab-v1beta1-nodejs",
    deps = [
        ":moblab_nodejs_gapic",
        ":moblab_proto",
    ],
)

ruby_proto_library(
    name = "moblab_ruby_proto",
    deps = [":moblab_proto"],
)

ruby_grpc_library(
    name = "moblab_ruby_grpc",
    srcs = [":moblab_proto"],
    deps = [":moblab_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "moblab_ruby_gapic",
    srcs = [":moblab_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-chromeos-moblab-v1beta1"],
    grpc_service_config = "moblab_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosmoblab_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":moblab_ruby_grpc",
        ":moblab_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-chromeos-moblab-v1beta1-ruby",
    deps = [
        ":moblab_ruby_gapic",
        ":moblab_ruby_grpc",
        ":moblab_ruby_proto",
    ],
)

csharp_proto_library(
    name = "moblab_csharp_proto",
    deps = [":moblab_proto"],
)

csharp_grpc_library(
    name = "moblab_csharp_grpc",
    srcs = [":moblab_proto"],
    deps = [":moblab_csharp_proto"],
)

csharp_gapic_library(
    name = "moblab_csharp_gapic",
    srcs = [":moblab_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "moblab_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chromeosmoblab_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":moblab_csharp_grpc",
        ":moblab_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-chromeos-moblab-v1beta1-csharp",
    deps = [
        ":moblab_csharp_gapic",
        ":moblab_csharp_grpc",
        ":moblab_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
