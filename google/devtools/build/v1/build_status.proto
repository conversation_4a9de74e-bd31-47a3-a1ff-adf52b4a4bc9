// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.build.v1;

import "google/protobuf/any.proto";
import "google/protobuf/wrappers.proto";

option cc_enable_arenas = true;
option go_package = "google.golang.org/genproto/googleapis/devtools/build/v1;build";
option java_multiple_files = true;
option java_outer_classname = "BuildStatusProto";
option java_package = "com.google.devtools.build.v1";
option php_namespace = "Google\\Cloud\\Build\\V1";

// Status used for both invocation attempt and overall build completion.
message BuildStatus {
  // The end result of the Build.
  enum Result {
    // Unspecified or unknown.
    UNKNOWN_STATUS = 0;

    // Build was successful and tests (if requested) all pass.
    COMMAND_SUCCEEDED = 1;

    // Build error and/or test failure.
    COMMAND_FAILED = 2;

    // Unable to obtain a result due to input provided by the user.
    USER_ERROR = 3;

    // Unable to obtain a result due to a failure within the build system.
    SYSTEM_ERROR = 4;

    // Build required too many resources, such as build tool RAM.
    RESOURCE_EXHAUSTED = 5;

    // An invocation attempt time exceeded its deadline.
    INVOCATION_DEADLINE_EXCEEDED = 6;

    // Build request time exceeded the request_deadline
    REQUEST_DEADLINE_EXCEEDED = 8;

    // The build was cancelled by a call to CancelBuild.
    CANCELLED = 7;
  }

  // The end result.
  Result result = 1;

  // Final invocation ID of the build, if there was one.
  // This field is only set on a status in BuildFinished event.
  string final_invocation_id = 3;

  // Build tool exit code. Integer value returned by the executed build tool.
  // Might not be available in some cases, e.g., a build timeout.
  google.protobuf.Int32Value build_tool_exit_code = 4;

  // Human-readable error message. Do not use for programmatic purposes.
  string error_message = 5;

  // Fine-grained diagnostic information to complement the status.
  google.protobuf.Any details = 2;
}
