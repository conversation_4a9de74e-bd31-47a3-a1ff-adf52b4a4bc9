# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "clouddebugger_proto",
    srcs = [
        "controller.proto",
        "data.proto",
        "debugger.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/devtools/source/v1:source_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "clouddebugger_proto_with_info",
    deps = [
        ":clouddebugger_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "clouddebugger_java_proto",
    deps = [":clouddebugger_proto"],
)

java_grpc_library(
    name = "clouddebugger_java_grpc",
    srcs = [":clouddebugger_proto"],
    deps = [":clouddebugger_java_proto"],
)

java_gapic_library(
    name = "clouddebugger_java_gapic",
    srcs = [":clouddebugger_proto_with_info"],
    gapic_yaml = "clouddebugger_gapic.yaml",
    grpc_service_config = "clouddebugger_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "clouddebugger_v2.yaml",
    test_deps = [
        ":clouddebugger_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":clouddebugger_java_proto",
    ],
)

java_gapic_test(
    name = "clouddebugger_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.debugger.v2.Controller2ClientHttpJsonTest",
        "com.google.cloud.debugger.v2.Controller2ClientTest",
        "com.google.cloud.debugger.v2.Debugger2ClientHttpJsonTest",
        "com.google.cloud.debugger.v2.Debugger2ClientTest",
    ],
    runtime_deps = [":clouddebugger_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-devtools-clouddebugger-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":clouddebugger_java_gapic",
        ":clouddebugger_java_grpc",
        ":clouddebugger_java_proto",
        ":clouddebugger_proto",
    ],
)

go_proto_library(
    name = "clouddebugger_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/debugger/apiv2/debuggerpb",
    protos = [":clouddebugger_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/devtools/source/v1:source_go_proto",
    ],
)

go_gapic_library(
    name = "clouddebugger_go_gapic",
    srcs = [":clouddebugger_proto_with_info"],
    grpc_service_config = "clouddebugger_grpc_service_config.json",
    importpath = "cloud.google.com/go/debugger/apiv2;debugger",
    rest_numeric_enums = True,
    service_yaml = "clouddebugger_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":clouddebugger_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-devtools-clouddebugger-v2-go",
    deps = [
        ":clouddebugger_go_gapic",
        ":clouddebugger_go_gapic_srcjar-snippets.srcjar",
        ":clouddebugger_go_gapic_srcjar-test.srcjar",
        ":clouddebugger_go_proto",
    ],
)

py_gapic_library(
    name = "clouddebugger_py_gapic",
    srcs = [":clouddebugger_proto"],
    grpc_service_config = "clouddebugger_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-debugger-client",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=debugger",
    ],
    rest_numeric_enums = True,
    service_yaml = "clouddebugger_v2.yaml",
    transport = "grpc+rest",
)

# Uncomment once https://github.com/googleapis/gapic-generator-python/issues/1376 is fixed
#py_test(
#    name = "clouddebugger_py_gapic_test",
#    srcs = [
#        "clouddebugger_py_gapic_pytest.py",
#        "clouddebugger_py_gapic_test.py",
#    ],
#    legacy_create_init = False,
#    deps = [":clouddebugger_py_gapic"],
#)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "devtools-clouddebugger-v2-py",
    deps = [
        ":clouddebugger_py_gapic",
        "//google/devtools/source/v1:google-cloud-source-v1-py",
    ],
)

php_proto_library(
    name = "clouddebugger_php_proto",
    deps = [":clouddebugger_proto"],
)

php_gapic_library(
    name = "clouddebugger_php_gapic",
    srcs = [":clouddebugger_proto_with_info"],
    grpc_service_config = "clouddebugger_grpc_service_config.json",
    migration_mode = "MIGRATING",
    rest_numeric_enums = True,
    service_yaml = "clouddebugger_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":clouddebugger_php_proto",
        "//google/devtools/source/v1:source_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-devtools-clouddebugger-v2-php",
    deps = [
        ":clouddebugger_php_gapic",
        ":clouddebugger_php_proto",
        "//google/devtools/source/v1:source_php_proto",
    ],
)

nodejs_gapic_library(
    name = "clouddebugger_nodejs_gapic",
    src = ":clouddebugger_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "clouddebugger_grpc_service_config.json",
    package = "google.devtools.clouddebugger.v2",
    rest_numeric_enums = True,
    service_yaml = "clouddebugger_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "devtools-clouddebugger-v2-nodejs",
    deps = [
        ":clouddebugger_nodejs_gapic",
        ":clouddebugger_proto",
        "//google/devtools/source/v1:source_proto",
    ],
)

ruby_proto_library(
    name = "clouddebugger_ruby_proto",
    deps = [":clouddebugger_proto"],
)

ruby_grpc_library(
    name = "clouddebugger_ruby_grpc",
    srcs = [":clouddebugger_proto"],
    deps = [":clouddebugger_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "clouddebugger_ruby_gapic",
    srcs = [":clouddebugger_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-debugger-v2",
        "ruby-cloud-env-prefix=DEBUGGER",
        "ruby-cloud-product-url=https://cloud.google.com/debugger",
        "ruby-cloud-api-id=clouddebugger.googleapis.com",
        "ruby-cloud-api-shortname=clouddebugger",
        "ruby-cloud-service-override=Controller2=Controller;Debugger2=Debugger",
    ],
    grpc_service_config = "clouddebugger_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Cloud Debugger API allows applications to interact with the Google Cloud Debugger backends. It provides two interfaces: the Debugger interface and the Controller interface. The Controller interface allows you to implement an agent that sends state data -- for example, the value of program variables and the call stack -- to Cloud Debugger when the application is running. The Debugger interface allows you to implement a Cloud Debugger client that allows users to set and delete the breakpoints at which the state data is collected, as well as read the data that is captured.",
    ruby_cloud_title = "Cloud Debugger V2",
    service_yaml = "clouddebugger_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":clouddebugger_ruby_grpc",
        ":clouddebugger_ruby_proto",
        "//google/devtools/source/v1:source_ruby_grpc",
        "//google/devtools/source/v1:source_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-devtools-clouddebugger-v2-ruby",
    deps = [
        ":clouddebugger_ruby_gapic",
        ":clouddebugger_ruby_grpc",
        ":clouddebugger_ruby_proto",
        "//google/devtools/source/v1:source_ruby_grpc",
        "//google/devtools/source/v1:source_ruby_proto",
    ],
)

csharp_proto_library(
    name = "clouddebugger_csharp_proto",
    deps = [":clouddebugger_proto"],
)

csharp_grpc_library(
    name = "clouddebugger_csharp_grpc",
    srcs = [":clouddebugger_proto"],
    deps = [":clouddebugger_csharp_proto"],
)

csharp_gapic_library(
    name = "clouddebugger_csharp_gapic",
    srcs = [":clouddebugger_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "clouddebugger_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "clouddebugger_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":clouddebugger_csharp_grpc",
        ":clouddebugger_csharp_proto",
        "//google/devtools/source/v1:source_csharp_grpc",
        "//google/devtools/source/v1:source_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-devtools-clouddebugger-v2-csharp",
    deps = [
        ":clouddebugger_csharp_gapic",
        ":clouddebugger_csharp_grpc",
        ":clouddebugger_csharp_proto",
    ],
)

cc_proto_library(
    name = "clouddebugger_cc_proto",
    deps = [":clouddebugger_proto"],
)

cc_grpc_library(
    name = "clouddebugger_cc_grpc",
    srcs = [":clouddebugger_proto"],
    grpc_only = True,
    deps = [":clouddebugger_cc_proto"],
)
