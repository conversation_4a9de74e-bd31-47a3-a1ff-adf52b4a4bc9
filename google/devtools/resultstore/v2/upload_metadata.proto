// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.resultstore.v2;

import "google/api/resource.proto";

option go_package = "google.golang.org/genproto/googleapis/devtools/resultstore/v2;resultstore";
option java_multiple_files = true;
option java_outer_classname = "UploadMetadataProto";
option java_package = "com.google.devtools.resultstore.v2";

// The upload metadata for an invocation
message UploadMetadata {
  option (google.api.resource) = {
    type: "resultstore.googleapis.com/UploadMetadata"
    pattern: "invocations/{invocation}/uploadMetadata"
  };

  // The name of the upload metadata.  Its format will be:
  // invocations/${INVOCATION_ID}/uploadMetadata
  string name = 1;

  // The resume token of the last batch that was committed in the most recent
  // batch upload.
  // More information with resume_token could be found in
  // resultstore_upload.proto
  string resume_token = 2;

  // Client-specific data used to resume batch upload if an error occurs and
  // retry action is needed.
  bytes uploader_state = 3;
}
