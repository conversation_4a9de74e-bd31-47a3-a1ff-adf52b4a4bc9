// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.resultstore.v2;

import "google/api/resource.proto";
import "google/devtools/resultstore/v2/file.proto";

option go_package = "google.golang.org/genproto/googleapis/devtools/resultstore/v2;resultstore";
option java_multiple_files = true;
option java_outer_classname = "FileSetProto";
option java_package = "com.google.devtools.resultstore.v2";

// This resource represents a set of Files and other (nested) FileSets.
// A FileSet is a node in the graph, and the file_sets field represents the
// outgoing edges. A resource may reference various nodes in the graph to
// represent the transitive closure of all files from those nodes.
// The FileSets must form a directed acyclic graph. The Upload API is unable to
// enforce that the graph is acyclic at write time, and if cycles are written,
// it may cause issues at read time.
//
// A FileSet may be referenced by other resources in conjunction with Files.
//
// Clients should prefer using Files directly under resources. Clients should
// not use FileSets unless their usecase requires a directed acyclic graph of
// Files.
message FileSet {
  option (google.api.resource) = {
    type: "resultstore.googleapis.com/FileSet"
    pattern: "invocations/{invocation}/fileSets/{file_set}"
  };

  // The resource ID components that identify the FileSet.
  message Id {
    // The Invocation ID.
    string invocation_id = 1;

    // The FileSet ID.
    string file_set_id = 2;
  }

  // The format of this FileSet resource name must be:
  // invocations/${INVOCATION_ID}/fileSets/${url_encode(FILE_SET_ID)}
  string name = 1;

  // The resource ID components that identify the file set. They must match the
  // resource name after proper encoding.
  Id id = 2;

  // List of names of other file sets that are referenced from this one.
  // Each name must point to a file set under the same invocation. The name
  // format must be: invocations/${INVOCATION_ID}/fileSets/${FILE_SET_ID}
  repeated string file_sets = 3;

  // Files that are contained within this file set.
  // The uid field in the file should be unique for the Invocation.
  repeated File files = 4;
}
