// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.resultstore.v2;

import "google/api/resource.proto";
import "google/devtools/resultstore/v2/common.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/devtools/resultstore/v2;resultstore";
option java_multiple_files = true;
option java_outer_classname = "DownloadMetadataProto";
option java_package = "com.google.devtools.resultstore.v2";

// The download metadata for an invocation
message DownloadMetadata {
  option (google.api.resource) = {
    type: "resultstore.googleapis.com/DownloadMetadata"
    pattern: "invocations/{invocation}/downloadMetadata"
  };

  // The name of the download metadata.  Its format will be:
  // invocations/${INVOCATION_ID}/downloadMetadata
  string name = 1;

  // Indicates the upload status of the invocation, whether it is
  // post-processing, or immutable, etc.
  UploadStatus upload_status = 2;

  // If populated, the time when CreateInvocation is called.
  // This does not necessarily line up with the start time of the invocation.
  // Please use invocation.timing.start_time for that purpose.
  google.protobuf.Timestamp create_time = 3;

  // If populated, the time when FinalizeInvocation is called or when invocation
  // is automatically finalized. This field is populated when upload_status
  // becomes POST_PROCESSING.
  google.protobuf.Timestamp finalize_time = 4;

  // If populated, the time when all post processing is done and the invocation
  // is marked as immutable. This field is populated when upload_status becomes
  // IMMUTABLE.
  google.protobuf.Timestamp immutable_time = 5;
}
