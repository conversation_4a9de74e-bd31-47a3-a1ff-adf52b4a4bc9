// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.resultstore.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/devtools/resultstore/v2/action.proto";
import "google/devtools/resultstore/v2/configuration.proto";
import "google/devtools/resultstore/v2/configured_target.proto";
import "google/devtools/resultstore/v2/download_metadata.proto";
import "google/devtools/resultstore/v2/file_set.proto";
import "google/devtools/resultstore/v2/invocation.proto";
import "google/devtools/resultstore/v2/target.proto";

option go_package = "google.golang.org/genproto/googleapis/devtools/resultstore/v2;resultstore";
option java_multiple_files = true;
option java_outer_classname = "ResultStoreDownloadProto";
option java_package = "com.google.devtools.resultstore.v2";

// This is the interface used to download information from the ResultStore
// database.
//
// Clients are encourage to use ExportInvocation for most traffic.
//
// Most APIs require setting a response FieldMask via the 'fields' URL query
// parameter or the X-Goog-FieldMask HTTP/gRPC header.
service ResultStoreDownload {
  option (google.api.default_host) = "resultstore.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Exports the invocation with the given name and its child resources.
  //
  // The order in which resources are returned is defined as follows,
  // invocation; download_metadata; configurations; targets interleaving
  // configured_targets and actions; file_sets.
  //
  // - Invocation
  // - DownloadMetadata
  // - Configurations
  // - Targets
  //   └─ ConfiguredTargets
  //      └─Actions
  // - FileSets
  //
  // All child resources will be returned before the next parent
  // resource is returned. For example, all actions under a configured_target
  // will be returned before the next configured_target is returned.
  // The order in which results within a given resource type are returned is
  // undefined, but stable.
  //
  // An error will be reported in the following cases:
  // - If the invocation is not found.
  // - If the given invocation name is badly formatted.
  // - If no field mask was given.
  rpc ExportInvocation(ExportInvocationRequest)
      returns (ExportInvocationResponse) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*}:export"
    };
  }

  // Retrieves the invocation with the given name.
  //
  // An error will be reported in the following cases:
  // - If the invocation is not found.
  // - If the given invocation name is badly formatted.
  // - If no field mask was given.
  rpc GetInvocation(GetInvocationRequest) returns (Invocation) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Searches for invocations matching the given query parameters. Results will
  // be ordered by timing.start_time with most recent first, but total ordering
  // of results is not guaranteed when difference in timestamps is very small.
  // Results may be stale. Results may be omitted.
  //
  //
  // An error will be reported in the following cases:
  // - If a query string is not provided
  // - If no field mask was given.
  rpc SearchInvocations(SearchInvocationsRequest)
      returns (SearchInvocationsResponse) {
    option (google.api.http) = {
      get: "/v2/invocations:search"
    };
  }

  // Retrieves the metadata for an invocation with the given name.
  //
  // An error will be reported in the following cases:
  // - If the invocation is not found.
  // - If the given invocation name is badly formatted.
  rpc GetInvocationDownloadMetadata(GetInvocationDownloadMetadataRequest)
      returns (DownloadMetadata) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*/downloadMetadata}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves the configuration with the given name.
  //
  // An error will be reported in the following cases:
  // - If the configuration or its parent invocation is not found.
  // - If the given configuration name is badly formatted.
  // - If no field mask was given.
  rpc GetConfiguration(GetConfigurationRequest) returns (Configuration) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*/configs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves all configurations for a parent invocation.
  // This might be limited by user or server,
  // in which case a continuation token is provided.
  // The order in which results are returned is undefined, but stable.
  //
  // An error will be reported in the following cases:
  // - If the parent invocation is not found.
  // - If the given parent invocation name is badly formatted.
  // - If no field mask was given.
  rpc ListConfigurations(ListConfigurationsRequest)
      returns (ListConfigurationsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=invocations/*}/configs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the target with the given name.
  //
  // An error will be reported in the following cases:
  // - If the target or its parent invocation is not found.
  // - If the given target name is badly formatted.
  // - If no field mask was given.
  rpc GetTarget(GetTargetRequest) returns (Target) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*/targets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves all targets for a parent invocation.  This might be limited by
  // user or server, in which case a continuation token is provided.
  // The order in which results are returned is undefined, but stable.
  //
  // An error will be reported in the following cases:
  // - If the parent is not found.
  // - If the given parent name is badly formatted.
  // - If no field mask was given.
  rpc ListTargets(ListTargetsRequest) returns (ListTargetsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=invocations/*}/targets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the configured target with the given name.
  //
  // An error will be reported in the following cases:
  // - If the configured target is not found.
  // - If the given name is badly formatted.
  // - If no field mask was given.
  rpc GetConfiguredTarget(GetConfiguredTargetRequest)
      returns (ConfiguredTarget) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*/targets/*/configuredTargets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves all configured targets for a parent invocation/target.
  // This might be limited by user or server, in which case a continuation
  // token is provided. Supports '-' for targetId meaning all targets.
  // The order in which results are returned is undefined, but stable and
  // consistent with ListTargets and ListConfigurations.
  //
  // An error will be reported in the following cases:
  // - If the parent is not found.
  // - If the given parent name is badly formatted.
  // - If no field mask was given.
  rpc ListConfiguredTargets(ListConfiguredTargetsRequest)
      returns (ListConfiguredTargetsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=invocations/*/targets/*}/configuredTargets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Searches for ConfiguredTargets matching the given query parameters. Results
  // will be ordered by timing.start_time with most recent first, but total
  // ordering of results is not guaranteed when difference in timestamps is
  // very small. Results may be stale. Results may be omitted.
  //
  //
  // Field masks are supported for only these fields and their subfields:
  // - configured_targets.name
  // - configured_targets.id
  // - configured_targets.status_attributes
  // - configured_targets.timing
  // - next_page_token
  //
  // An error will be reported in the following cases:
  // - If a query string is not provided
  // - If no field mask was given.
  rpc SearchConfiguredTargets(SearchConfiguredTargetsRequest)
      returns (SearchConfiguredTargetsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=invocations/*/targets/*}/configuredTargets:search"
    };
  }

  // Retrieves the action with the given name.
  //
  // An error will be reported in the following cases:
  // - If the action is not found.
  // - If the given name is badly formatted.
  // - If no field mask was given.
  rpc GetAction(GetActionRequest) returns (Action) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*/targets/*/configuredTargets/*/actions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves all actions for a parent invocation/target/configuration.
  // This might be limited by user or server, in which case a continuation
  // token is provided. Supports '-' for configurationId to mean all
  // actions for all configurations for a target, or '-' for targetId and
  // configurationId to mean all actions for all configurations and all targets.
  // Does not support targetId '-' with a specified configuration.
  // The order in which results are returned is undefined, but stable and
  // consistent with ListConfiguredTargets.
  //
  // An error will be reported in the following cases:
  // - If the parent is not found.
  // - If the given parent name is badly formatted.
  // - If no field mask was given.
  rpc ListActions(ListActionsRequest) returns (ListActionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=invocations/*/targets/*/configuredTargets/*}/actions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a list of actions for a parent invocation or multiple parents
  // target/configuration. This might be limited by user or server, in which
  // case a continuation token is provided. The order in which results are
  // returned is undefined, but stable and consistent with
  // ListConfiguredTargets.
  //
  // An error will be reported in the following cases:
  // - If the given parent name is badly formatted.
  // - If no field mask was given.
  rpc BatchListActions(BatchListActionsRequest)
      returns (BatchListActionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=invocations/*}/actions:batchList"
    };
  }

  // Retrieves the file set with the given name.
  //
  // An error will be reported in the following cases:
  // - If the file set or its parent invocation is not found.
  // - If the given file set name is badly formatted.
  // - If no field mask was given.
  rpc GetFileSet(GetFileSetRequest) returns (FileSet) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*/fileSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves all file sets for a parent invocation.
  // This might be limited by user or server,
  // in which case a continuation token is provided.
  // The order in which results are returned is undefined, but stable.
  //
  // An error will be reported in the following cases:
  // - If the parent invocation is not found.
  // - If the given parent invocation name is badly formatted.
  // - If no field mask was given.
  rpc ListFileSets(ListFileSetsRequest) returns (ListFileSetsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=invocations/*}/fileSets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the transitive closure of FileSets. This might be limited by user
  // or server, in which case a continuation token is provided.
  // The order in which results are returned is undefined, and unstable.
  //
  // An error will be reported in the following cases:
  // - If page_token is too large to continue the calculation.
  // - If the resource is not found.
  // - If the given resource name is badly formatted.
  // - If no field mask was given.
  rpc TraverseFileSets(TraverseFileSetsRequest)
      returns (TraverseFileSetsResponse) {
    option (google.api.http) = {
      get: "/v2/{name=invocations/*/fileSets/*}:traverseFileSets"
      additional_bindings {
        get: "/v2/{name=invocations/*/targets/*/configuredTargets/*/actions/*}:traverseFileSets"
      }
    };
  }
}

// Request passed into GetInvocation
message GetInvocationRequest {
  // Required. The name of the invocation to retrieve. It must match this
  // format: invocations/${INVOCATION_ID} where INVOCATION_ID must be an RFC
  // 4122-compliant UUID.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Invocation"
    }
  ];
}

// Request passed into SearchInvocations
message SearchInvocationsRequest {
  // The maximum number of items to return. Zero means all, but may be capped by
  // the server.
  int32 page_size = 1;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous Search request, if
    // any.
    string page_token = 2;

    // Absolute number of results to skip. May be rejected if too high.
    int64 offset = 3;
  }

  // A filtering query string.
  //
  // Only a limited number of fields and operators are supported. Not every
  // field supports every operator.
  //
  // Fields that support equals ("=") restrictions:
  //
  // id.invocation_id
  // name
  // status_attributes.status
  // workspace_info.hostname
  // download_metadata.upload_status
  //
  // Fields that support contains (":") restrictions:
  //
  // invocation_attributes.users
  // invocation_attributes.labels
  //
  // Fields that support comparison ("<", "<=", ">", ">=") restrictions;
  //
  // timing.start_time
  //
  // Supported custom function global restrictions:
  //
  // propertyEquals("key", "value")
  string query = 4;

  // The project id to search under.
  string project_id = 5;

  // If true, all equals or contains restrictions on string fields in query will
  // require exact match. Otherwise, a string field restriction may ignore case
  // and punctuation.
  bool exact_match = 7;
}

// Response from calling SearchInvocations
message SearchInvocationsResponse {
  // Invocations matching the search, possibly capped at request.page_size or a
  // server limit.
  repeated Invocation invocations = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results.
  string next_page_token = 2;
}

// Request passed into ExportInvocationRequest
message ExportInvocationRequest {
  // Required. The name of the invocation to retrieve. It must match this
  // format: invocations/${INVOCATION_ID} where INVOCATION_ID must be an RFC
  // 4122-compliant UUID.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Invocation"
    }
  ];

  // The maximum number of items to return. Zero means all, but may be capped by
  // the server.
  int32 page_size = 2;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous export request, if
    // any.
    string page_token = 3;

    // Absolute number of results to skip.
    int64 offset = 4;
  }

  // Filters Targets, ConfiguredTargets, and Actions returned
  //
  // Only id.target_id field with single equals ("=") restriction supported
  string targets_filter = 6;

  // Requires targets_filter to be populated
  // Filters ConfiguredTargets and Actions returned
  //
  // Only id.configuration_id field with single equals ("=") restriction
  // supported
  string configured_targets_filter = 7;

  // Requires both targets_filter and configured_targets_filter to be populated
  // Filters Actions returned
  //
  // Only id.action_id field with single equals ("=") restriction supported
  string actions_filter = 8;
}

// Response from calling ExportInvocationResponse.
// Possibly capped at request.page_size or a server limit.
message ExportInvocationResponse {
  // Parent Invocation resource.
  Invocation invocation = 1;

  // download metadata of request invocation
  // download_metadata and invocation count towards page_size once.
  DownloadMetadata download_metadata = 8;

  // Targets matching the request invocation.
  repeated Target targets = 2;

  // Configurations matching the request invocation.
  repeated Configuration configurations = 3;

  // ConfiguredTargets matching the request invocation.
  repeated ConfiguredTarget configured_targets = 4;

  // Actions matching the request invocation.
  repeated Action actions = 5;

  // FileSets matching the request invocation.
  repeated FileSet file_sets = 6;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 7;
}

// Request passed into GetInvocationDownloadMetadata
message GetInvocationDownloadMetadataRequest {
  // Required. The name of the download metadata to retrieve. It must match this
  // format: invocations/${INVOCATION_ID}/downloadMetadata where INVOCATION_ID
  // must be an RFC 4122-compliant UUID.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/DownloadMetadata"
    }
  ];
}

// Request passed into GetConfiguration
message GetConfigurationRequest {
  // Required. The name of the configuration to retrieve. It must match this
  // format: invocations/${INVOCATION_ID}/configs/${CONFIGURATION_ID}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Configuration"
    }
  ];
}

// Request passed into ListConfigurations
message ListConfigurationsRequest {
  // Required. The invocation name of the configurations to retrieve.
  // It must match this format: invocations/${INVOCATION_ID}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Invocation"
    }
  ];

  // The maximum number of items to return.
  // Zero means all, but may be capped by the server.
  int32 page_size = 2;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous List request, if any.
    string page_token = 3;

    // Absolute number of results to skip.
    int64 offset = 4;
  }

  // A filter to return only resources that match it.
  // Any fields used in the filter must be also specified in the field mask.
  // May cause pages with 0 results and a next_page_token to be returned.
  string filter = 5;
}

// Response from calling ListConfigurations
message ListConfigurationsResponse {
  // Configurations matching the request invocation,
  // possibly capped at request.page_size or a server limit.
  repeated Configuration configurations = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request passed into GetTarget
message GetTargetRequest {
  // Required. The name of the target to retrieve. It must match this format:
  // invocations/${INVOCATION_ID}/targets/${url_encode(TARGET_ID)}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Target"
    }
  ];
}

// Request passed into ListTargets
message ListTargetsRequest {
  // Required. The invocation name of the targets to retrieve. It must match
  // this format: invocations/${INVOCATION_ID}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Invocation"
    }
  ];

  // The maximum number of items to return.
  // Zero means all, but may be capped by the server.
  int32 page_size = 2;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous List request, if any.
    string page_token = 3;

    // Absolute number of results to skip.
    int64 offset = 4;
  }

  // A filter to return only resources that match it.
  // Any fields used in the filter must be also specified in the field mask.
  // May cause pages with 0 results and a next_page_token to be returned.
  string filter = 5;
}

// Response from calling ListTargetsResponse
message ListTargetsResponse {
  // Targets matching the request invocation,
  // possibly capped at request.page_size or a server limit.
  repeated Target targets = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request passed into GetConfiguredTarget
message GetConfiguredTargetRequest {
  // Required. The name of the configured target to retrieve. It must match this
  // format:
  // invocations/${INVOCATION_ID}/targets/${url_encode(TARGET_ID)}/configuredTargets/${CONFIGURATION_ID}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/ConfiguredTarget"
    }
  ];
}

// Request passed into ListConfiguredTargets
message ListConfiguredTargetsRequest {
  // Required. The invocation and target name of the configured targets to
  // retrieve. It must match this format:
  // invocations/${INVOCATION_ID}/targets/${url_encode(TARGET_ID)}
  // Supports '-' for ${TARGET_ID} meaning all targets.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Target"
    }
  ];

  // The maximum number of items to return.
  // Zero means all, but may be capped by the server.
  int32 page_size = 2;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous List request, if any.
    string page_token = 3;

    // Absolute number of results to skip.
    int64 offset = 4;
  }

  // A filter to return only resources that match it.
  // Any fields used in the filter must be also specified in the field mask.
  // May cause pages with 0 results and a next_page_token to be returned.
  string filter = 5;
}

// Response from calling ListConfiguredTargets
message ListConfiguredTargetsResponse {
  // ConfiguredTargets matching the request,
  // possibly capped at request.page_size or a server limit.
  repeated ConfiguredTarget configured_targets = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request passed into SearchConfiguredTargets
message SearchConfiguredTargetsRequest {
  // Required. Must be set to invocations/-/targets/-
  // This only supports searching all ConfiguredTargets across all Invocations.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Target"
    }
  ];

  // The maximum number of items to return. Zero means all, but may be capped by
  // the server.
  int32 page_size = 2;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous Search request, if
    // any.
    string page_token = 3;

    // Absolute number of results to skip. May be rejected if too high.
    int64 offset = 4;
  }

  // A filtering query string.
  //
  // Only a limited number of fields and operators are supported. Not every
  // field supports every operator. Access to parent resources is provided
  // via synthetic fields ‘invocation’, ‘configuration’, and ‘target’.
  //
  // Any search must contain an equals restriction on id.target_id.
  //
  // Fields that support equals ("=") restrictions:
  //
  // id.target_id
  // status_attributes.status
  //
  // target.target_attributes.type
  // target.target_attributes.language
  // target.test_attributes.size
  //
  // configuration.configuration_attributes.cpu
  //
  // invocation.workspace_info.hostname
  //
  // Fields that support contains (":") restrictions:
  //
  // target.target_attributes.tags
  //
  // invocation.invocation_attributes.users
  // invocation.invocation_attributes.labels
  //
  // Fields that support comparison ("<", "<=", ">", ">=") restrictions;
  //
  // timing.start_time
  // coalesced_start_time
  // Supported custom function global restrictions:
  //
  // invocationPropertyEquals("key", "value")
  // targetPropertyEquals("key", "value")
  // configurationPropertyEquals("key", "value")
  // configuredTargetPropertyEquals("key", "value")
  string query = 5;

  // The project id to search under.
  string project_id = 6;

  // Unimplemented
  bool exact_match = 7;
}

// Response from calling SearchConfiguredTargets
message SearchConfiguredTargetsResponse {
  // ConfiguredTargets matching the search, possibly capped at request.page_size
  // or a server limit.
  repeated ConfiguredTarget configured_targets = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results.
  string next_page_token = 2;
}

// Request passed into GetAction
message GetActionRequest {
  // Required. The name of the action to retrieve. It must match this format:
  // invocations/${INVOCATION_ID}/targets/${url_encode(TARGET_ID)}/configuredTargets/${CONFIGURATION_ID}/actions/${ACTION_ID}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Action"
    }
  ];
}

// Request passed into ListActions
message ListActionsRequest {
  // Required. The invocation, target, and configuration name of the action to
  // retrieve. It must match this format:
  // invocations/${INVOCATION_ID}/targets/${url_encode(TARGET_ID)}/configuredTargets/${CONFIGURATION_ID}
  // Supports '-' for ${CONFIGURATION_ID} to mean all Actions for all
  // Configurations for a Target, or '-' for ${TARGET_ID} and
  // ${CONFIGURATION_ID} to mean all Actions for all Configurations and all
  // Targets. Does not support ${TARGET_ID} '-' with a specified configuration.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/ConfiguredTarget"
    }
  ];

  // The maximum number of items to return.
  // Zero means all, but may be capped by the server.
  int32 page_size = 2;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous List request, if any.
    string page_token = 3;

    // Absolute number of results to skip.
    int64 offset = 4;
  }

  // A filter to return only resources that match it.
  // Any fields used in the filter must be also specified in the field mask.
  // May cause pages with 0 results and a next_page_token to be returned.
  string filter = 5;
}

// Response from calling ListActions
message ListActionsResponse {
  // Actions matching the request,
  // possibly capped at request.page_size or a server limit.
  repeated Action actions = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request passed into BatchListActionsRequest
message BatchListActionsRequest {
  // Required. The invocation name of the actions to retrieve. It must match
  // this format: invocations/${INVOCATION_ID}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Invocation"
    }
  ];

  // The names of the configured targets to retrieve.
  // It must match this format:
  // invocations/${INVOCATION_ID}/targets/${url_encode(TARGET_ID)}/configuredTargets/${CONFIGURATION_ID}
  repeated string configured_targets = 2;

  // The maximum number of items to return.
  // Zero means all, but may be capped by the server.
  int32 page_size = 3;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous List request, if any.
    // Page tokens will become larger with every page returned, and if a page
    // token becomes too large, it will no longer be possible to continue to
    // calculate the transitive dependencies. The API will return a 400
    // Bad request (HTTPS), or a INVALID_ARGUMENT (gRPC ) when
    // this happens.
    string page_token = 4;

    // Absolute number of results to skip.
    // Not yet implemented. 0 for default.
    int64 offset = 5;
  }

  // A filter to return only resources that match it.
  // Any fields used in the filter must be also specified in the field mask.
  // May cause pages with 0 results and a next_page_token to be returned.
  string filter = 6;
}

// Response from calling BatchListActionsResponse
message BatchListActionsResponse {
  // Actions matching the request,
  // possibly capped at request.page_size or a server limit.
  repeated Action actions = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;

  // Not found configured target names.
  repeated string not_found = 3;
}

// Request passed into GetFileSet
message GetFileSetRequest {
  // Required. The name of the file set to retrieve. It must match this format:
  // invocations/${INVOCATION_ID}/fileSets/${FILE_SET_ID}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/FileSet"
    }
  ];
}

// Request passed into ListFileSets
message ListFileSetsRequest {
  // Required. The invocation name of the file sets to retrieve.
  // It must match this format: invocations/${INVOCATION_ID}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "resultstore.googleapis.com/Invocation"
    }
  ];

  // The maximum number of items to return.
  // Zero means all, but may be capped by the server.
  int32 page_size = 2;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous List request, if any.
    string page_token = 3;

    // Absolute number of results to skip.
    int64 offset = 4;
  }

  // A filter to return only resources that match it.
  // Any fields used in the filter must be also specified in the field mask.
  // May cause pages with 0 results and a next_page_token to be returned.
  string filter = 5;
}

// Response from calling ListFileSets
message ListFileSetsResponse {
  // File sets matching the request,
  // possibly capped at request.page_size or a server limit.
  repeated FileSet file_sets = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request passed into TraverseFileSets
message TraverseFileSetsRequest {
  // Required. The name of the resource to traverse.
  // It must match one of the following formats:
  //
  // invocations/${INVOCATION_ID}/fileSets/${FILE_SET_ID}
  // This returns the transitive closure of FileSets referenced by the given
  // FileSet, including itself.
  //
  // invocations/${INVOCATION_ID}/targets/${url_encode(TARGET_ID)}/configuredTargets/${CONFIGURATION_ID}/actions/${ACTION_ID}
  // This returns the transitive closure of FileSets referenced by the given
  // Action. If ${ACTION_ID} is "-", this returns the transitive closure of
  // FileSets referenced by all Actions under the given ConfiguredTarget.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // The maximum number of items to return.
  // Zero means all, but may be capped by the server.
  int32 page_size = 2;

  // Options for pagination.
  oneof page_start {
    // The next_page_token value returned from a previous List request, if any.
    // Page tokens will become larger with every page returned, and if a page
    // token becomes too large, it will no longer be possible to continue to
    // calculate the transitive dependencies. The API will return a 400
    // Bad request (HTTPS), or a INVALID_ARGUMENT (gRPC ) when
    // this happens.
    string page_token = 3;

    // Absolute number of results to skip.
    // Not yet implemented. 0 for default.
    int64 offset = 4;
  }
}

// Response from calling TraverseFileSets
message TraverseFileSetsResponse {
  // File sets matching the request.
  // The order in which results are returned is undefined, but stable.
  repeated FileSet file_sets = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}
