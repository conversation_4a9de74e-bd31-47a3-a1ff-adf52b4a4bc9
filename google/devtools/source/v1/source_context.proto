// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.devtools.source.v1;

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.DevTools.Source.V1";
option go_package = "google.golang.org/genproto/googleapis/devtools/source/v1;source";
option java_multiple_files = true;
option java_outer_classname = "SourceContextProto";
option java_package = "com.google.devtools.source.v1";
option php_namespace = "Google\\Cloud\\DevTools\\Source\\V1";

// A SourceContext is a reference to a tree of files. A SourceContext together
// with a path point to a unique revision of a single file or directory.
message SourceContext {
  // A SourceContext can refer any one of the following types of repositories.
  oneof context {
    // A SourceContext referring to a revision in a cloud repo.
    CloudRepoSourceContext cloud_repo = 1;

    // A SourceContext referring to a snapshot in a cloud workspace.
    CloudWorkspaceSourceContext cloud_workspace = 2;

    // A SourceContext referring to a Gerrit project.
    GerritSourceContext gerrit = 3;

    // A SourceContext referring to any third party Git repo (e.g. GitHub).
    GitSourceContext git = 6;
  }
}

// An ExtendedSourceContext is a SourceContext combined with additional
// details describing the context.
message ExtendedSourceContext {
  // Any source context.
  SourceContext context = 1;

  // Labels with user defined metadata.
  map<string, string> labels = 2;
}

// An alias to a repo revision.
message AliasContext {
  // The type of an Alias.
  enum Kind {
    // Do not use.
    ANY = 0;

    // Git tag
    FIXED = 1;

    // Git branch
    MOVABLE = 2;

    // OTHER is used to specify non-standard aliases, those not of the kinds
    // above. For example, if a Git repo has a ref named "refs/foo/bar", it
    // is considered to be of kind OTHER.
    OTHER = 4;
  }

  // The alias kind.
  Kind kind = 1;

  // The alias name.
  string name = 2;
}

// A CloudRepoSourceContext denotes a particular revision in a cloud
// repo (a repo hosted by the Google Cloud Platform).
message CloudRepoSourceContext {
  // The ID of the repo.
  RepoId repo_id = 1;

  // A revision in a cloud repository can be identified by either its revision
  // ID or its Alias.
  oneof revision {
    // A revision ID.
    string revision_id = 2;

    // The name of an alias (branch, tag, etc.).
    string alias_name = 3 [deprecated = true];

    // An alias, which may be a branch or tag.
    AliasContext alias_context = 4;
  }
}

// A CloudWorkspaceSourceContext denotes a workspace at a particular snapshot.
message CloudWorkspaceSourceContext {
  // The ID of the workspace.
  CloudWorkspaceId workspace_id = 1;

  // The ID of the snapshot.
  // An empty snapshot_id refers to the most recent snapshot.
  string snapshot_id = 2;
}

// A SourceContext referring to a Gerrit project.
message GerritSourceContext {
  // The URI of a running Gerrit instance.
  string host_uri = 1;

  // The full project name within the host. Projects may be nested, so
  // "project/subproject" is a valid project name.
  // The "repo name" is hostURI/project.
  string gerrit_project = 2;

  // A revision in a Gerrit project can be identified by either its revision ID
  // or its alias.
  oneof revision {
    // A revision (commit) ID.
    string revision_id = 3;

    // The name of an alias (branch, tag, etc.).
    string alias_name = 4 [deprecated = true];

    // An alias, which may be a branch or tag.
    AliasContext alias_context = 5;
  }
}

// A GitSourceContext denotes a particular revision in a third party Git
// repository (e.g. GitHub).
message GitSourceContext {
  // Git repository URL.
  string url = 1;

  // Git commit hash.
  // required.
  string revision_id = 2;
}

// A unique identifier for a cloud repo.
message RepoId {
  // A cloud repository can be identified by either its project ID and
  // repository name combination, or its globally unique identifier.
  oneof id {
    // A combination of a project ID and a repo name.
    ProjectRepoId project_repo_id = 1;

    // A server-assigned, globally unique identifier.
    string uid = 2;
  }
}

// Selects a repo using a Google Cloud Platform project ID
// (e.g. winged-cargo-31) and a repo name within that project.
message ProjectRepoId {
  // The ID of the project.
  string project_id = 1;

  // The name of the repo. Leave empty for the default repo.
  string repo_name = 2;
}

// A CloudWorkspaceId is a unique identifier for a cloud workspace.
// A cloud workspace is a place associated with a repo where modified files
// can be stored before they are committed.
message CloudWorkspaceId {
  // The ID of the repo containing the workspace.
  RepoId repo_id = 1;

  // The unique name of the workspace within the repo.  This is the name
  // chosen by the client in the Source API's CreateWorkspace method.
  string name = 2;
}
