// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.cloudtrace.v1;

import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/api/annotations.proto";

option csharp_namespace = "Google.Cloud.Trace.V1";
option go_package = "cloud.google.com/go/trace/apiv1/tracepb;tracepb";
option java_multiple_files = true;
option java_outer_classname = "TraceProto";
option java_package = "com.google.devtools.cloudtrace.v1";
option php_namespace = "Google\\Cloud\\Trace\\V1";
option ruby_package = "Google::Cloud::Trace::V1";

// This file describes an API for collecting and viewing traces and spans
// within a trace.  A Trace is a collection of spans corresponding to a single
// operation or set of operations for an application. A span is an individual
// timed event which forms a node of the trace tree. Spans for a single trace
// may span multiple services.
service TraceService {
  option (google.api.default_host) = "cloudtrace.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/trace.append,"
      "https://www.googleapis.com/auth/trace.readonly";

  // Returns of a list of traces that match the specified filter conditions.
  rpc ListTraces(ListTracesRequest) returns (ListTracesResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project_id}/traces"
    };
    option (google.api.method_signature) = "project_id";
  }

  // Gets a single trace by its ID.
  rpc GetTrace(GetTraceRequest) returns (Trace) {
    option (google.api.http) = {
      get: "/v1/projects/{project_id}/traces/{trace_id}"
    };
    option (google.api.method_signature) = "project_id,trace_id";
  }

  // Sends new traces to Stackdriver Trace or updates existing traces. If the ID
  // of a trace that you send matches that of an existing trace, any fields
  // in the existing trace and its spans are overwritten by the provided values,
  // and any new fields provided are merged with the existing trace data. If the
  // ID does not match, a new trace is created.
  rpc PatchTraces(PatchTracesRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      patch: "/v1/projects/{project_id}/traces"
      body: "traces"
    };
    option (google.api.method_signature) = "project_id,traces";
  }
}

// A trace describes how long it takes for an application to perform an
// operation. It consists of a set of spans, each of which represent a single
// timed event within the operation.
message Trace {
  // Project ID of the Cloud project where the trace data is stored.
  string project_id = 1;

  // Globally unique identifier for the trace. This identifier is a 128-bit
  // numeric value formatted as a 32-byte hex string. For example,
  // `382d4f4c6b7bb2f4a972559d9085001d`.
  string trace_id = 2;

  // Collection of spans in the trace.
  repeated TraceSpan spans = 3;
}

// List of new or updated traces.
message Traces {
  // List of traces.
  repeated Trace traces = 1;
}

// A span represents a single timed event within a trace. Spans can be nested
// and form a trace tree. Often, a trace contains a root span that describes the
// end-to-end latency of an operation and, optionally, one or more subspans for
// its suboperations. Spans do not need to be contiguous. There may be gaps
// between spans in a trace.
message TraceSpan {
  // Type of span. Can be used to specify additional relationships between spans
  // in addition to a parent/child relationship.
  enum SpanKind {
    // Unspecified.
    SPAN_KIND_UNSPECIFIED = 0;

    // Indicates that the span covers server-side handling of an RPC or other
    // remote network request.
    RPC_SERVER = 1;

    // Indicates that the span covers the client-side wrapper around an RPC or
    // other remote request.
    RPC_CLIENT = 2;
  }

  // Identifier for the span. Must be a 64-bit integer other than 0 and
  // unique within a trace. For example, `2205310701640571284`.
  fixed64 span_id = 1;

  // Distinguishes between spans generated in a particular context. For example,
  // two spans with the same name may be distinguished using `RPC_CLIENT`
  // and `RPC_SERVER` to identify queueing latency associated with the span.
  SpanKind kind = 2;

  // Name of the span. Must be less than 128 bytes. The span name is sanitized
  // and displayed in the Stackdriver Trace tool in the
  // Google Cloud Platform Console.
  // The name may be a method name or some other per-call site name.
  // For the same executable and the same call point, a best practice is
  // to use a consistent name, which makes it easier to correlate
  // cross-trace spans.
  string name = 3;

  // Start time of the span in nanoseconds from the UNIX epoch.
  google.protobuf.Timestamp start_time = 4;

  // End time of the span in nanoseconds from the UNIX epoch.
  google.protobuf.Timestamp end_time = 5;

  // Optional. ID of the parent span, if any.
  fixed64 parent_span_id = 6 [(google.api.field_behavior) = OPTIONAL];

  // Collection of labels associated with the span. Label keys must be less than
  // 128 bytes. Label values must be less than 16 kilobytes (10MB for
  // `/stacktrace` values).
  //
  // Some predefined label keys exist, or you may create your own. When creating
  // your own, we recommend the following formats:
  //
  // * `/category/product/key` for agents of well-known products (e.g.
  //   `/db/mongodb/read_size`).
  // * `short_host/path/key` for domain-specific keys (e.g.
  //   `foo.com/myproduct/bar`)
  //
  // Predefined labels include:
  //
  // *   `/agent`
  // *   `/component`
  // *   `/error/message`
  // *   `/error/name`
  // *   `/http/client_city`
  // *   `/http/client_country`
  // *   `/http/client_protocol`
  // *   `/http/client_region`
  // *   `/http/host`
  // *   `/http/method`
  // *   `/http/path`
  // *   `/http/redirected_url`
  // *   `/http/request/size`
  // *   `/http/response/size`
  // *   `/http/route`
  // *   `/http/status_code`
  // *   `/http/url`
  // *   `/http/user_agent`
  // *   `/pid`
  // *   `/stacktrace`
  // *   `/tid`
  map<string, string> labels = 7;
}

// The request message for the `ListTraces` method. All fields are required
// unless specified.
message ListTracesRequest {
  // Type of data returned for traces in the list.
  enum ViewType {
    // Default is `MINIMAL` if unspecified.
    VIEW_TYPE_UNSPECIFIED = 0;

    // Minimal view of the trace record that contains only the project
    // and trace IDs.
    MINIMAL = 1;

    // Root span view of the trace record that returns the root spans along
    // with the minimal trace data.
    ROOTSPAN = 2;

    // Complete view of the trace record that contains the actual trace data.
    // This is equivalent to calling the REST `get` or RPC `GetTrace` method
    // using the ID of each listed trace.
    COMPLETE = 3;
  }

  // Required. ID of the Cloud project where the trace data is stored.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Type of data returned for traces in the list. Default is
  // `MINIMAL`.
  ViewType view = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of traces to return. If not specified or <= 0, the
  // implementation selects a reasonable value.  The implementation may
  // return fewer traces than the requested page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Token identifying the page of results to return. If provided, use the
  // value of the `next_page_token` field from a previous request.
  string page_token = 4;

  // Start of the time interval (inclusive) during which the trace data was
  // collected from the application.
  google.protobuf.Timestamp start_time = 5;

  // End of the time interval (inclusive) during which the trace data was
  // collected from the application.
  google.protobuf.Timestamp end_time = 6;

  // Optional. A filter against labels for the request.
  //
  // By default, searches use prefix matching. To specify exact match, prepend
  // a plus symbol (`+`) to the search term.
  // Multiple terms are ANDed. Syntax:
  //
  // *   `root:NAME_PREFIX` or `NAME_PREFIX`: Return traces where any root
  //     span starts with `NAME_PREFIX`.
  // *   `+root:NAME` or `+NAME`: Return traces where any root span's name is
  //     exactly `NAME`.
  // *   `span:NAME_PREFIX`: Return traces where any span starts with
  //     `NAME_PREFIX`.
  // *   `+span:NAME`: Return traces where any span's name is exactly
  //     `NAME`.
  // *   `latency:DURATION`: Return traces whose overall latency is
  //     greater or equal to than `DURATION`. Accepted units are nanoseconds
  //     (`ns`), milliseconds (`ms`), and seconds (`s`). Default is `ms`. For
  //     example, `latency:24ms` returns traces whose overall latency
  //     is greater than or equal to 24 milliseconds.
  // *   `label:LABEL_KEY`: Return all traces containing the specified
  //     label key (exact match, case-sensitive) regardless of the key:value
  //     pair's value (including empty values).
  // *   `LABEL_KEY:VALUE_PREFIX`: Return all traces containing the specified
  //     label key (exact match, case-sensitive) whose value starts with
  //     `VALUE_PREFIX`. Both a key and a value must be specified.
  // *   `+LABEL_KEY:VALUE`: Return all traces containing a key:value pair
  //     exactly matching the specified text. Both a key and a value must be
  //     specified.
  // *   `method:VALUE`: Equivalent to `/http/method:VALUE`.
  // *   `url:VALUE`: Equivalent to `/http/url:VALUE`.
  string filter = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field used to sort the returned traces.
  // Can be one of the following:
  //
  // *   `trace_id`
  // *   `name` (`name` field of root span in the trace)
  // *   `duration` (difference between `end_time` and `start_time` fields of
  //      the root span)
  // *   `start` (`start_time` field of the root span)
  //
  // Descending order can be specified by appending `desc` to the sort field
  // (for example, `name desc`).
  //
  // Only one sort field is permitted.
  string order_by = 8 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for the `ListTraces` method.
message ListTracesResponse {
  // List of trace records as specified by the view parameter.
  repeated Trace traces = 1;

  // If defined, indicates that there are more traces that match the request
  // and that this value should be passed to the next request to continue
  // retrieving additional traces.
  string next_page_token = 2;
}

// The request message for the `GetTrace` method.
message GetTraceRequest {
  // Required. ID of the Cloud project where the trace data is stored.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the trace to return.
  string trace_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for the `PatchTraces` method.
message PatchTracesRequest {
  // Required. ID of the Cloud project where the trace data is stored.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The body of the message.
  Traces traces = 2 [(google.api.field_behavior) = REQUIRED];
}
