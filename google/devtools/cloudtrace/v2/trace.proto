// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.cloudtrace.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Trace.V2";
option go_package = "cloud.google.com/go/trace/apiv2/tracepb;tracepb";
option java_multiple_files = true;
option java_outer_classname = "TraceProto";
option java_package = "com.google.devtools.cloudtrace.v2";
option php_namespace = "Google\\Cloud\\Trace\\V2";
option ruby_package = "Google::Cloud::Trace::V2";

// A span represents a single operation within a trace. Spans can be
// nested to form a trace tree. Often, a trace contains a root span
// that describes the end-to-end latency, and one or more subspans for
// its sub-operations.
//
// A trace can also contain multiple root spans, or none at all.
// Spans do not need to be contiguous. There might be
// gaps or overlaps between spans in a trace.
message Span {
  option (google.api.resource) = {
    type: "cloudtrace.googleapis.com/Span"
    pattern: "projects/{project}/traces/{trace}/spans/{span}"
  };

  // A set of attributes as key-value pairs.
  message Attributes {
    // A set of attributes. Each attribute's key can be up to 128 bytes
    // long. The value can be a string up to 256 bytes, a signed 64-bit integer,
    // or the boolean values `true` or `false`. For example:
    //
    //     "/instance_id": { "string_value": { "value": "my-instance" } }
    //     "/http/request_bytes": { "int_value": 300 }
    //     "abc.com/myattribute": { "bool_value": false }
    map<string, AttributeValue> attribute_map = 1;

    // The number of attributes that were discarded. Attributes can be discarded
    // because their keys are too long or because there are too many attributes.
    // If this value is 0 then all attributes are valid.
    int32 dropped_attributes_count = 2;
  }

  // A time-stamped annotation or message event in the Span.
  message TimeEvent {
    // Text annotation with a set of attributes.
    message Annotation {
      // A user-supplied message describing the event. The maximum length for
      // the description is 256 bytes.
      TruncatableString description = 1;

      // A set of attributes on the annotation. You can have up to 4 attributes
      // per Annotation.
      Attributes attributes = 2;
    }

    // An event describing a message sent/received between Spans.
    message MessageEvent {
      // Indicates whether the message was sent or received.
      enum Type {
        // Unknown event type.
        TYPE_UNSPECIFIED = 0;

        // Indicates a sent message.
        SENT = 1;

        // Indicates a received message.
        RECEIVED = 2;
      }

      // Type of MessageEvent. Indicates whether the message was sent or
      // received.
      Type type = 1;

      // An identifier for the MessageEvent's message that can be used to match
      // `SENT` and `RECEIVED` MessageEvents.
      int64 id = 2;

      // The number of uncompressed bytes sent or received.
      int64 uncompressed_size_bytes = 3;

      // The number of compressed bytes sent or received. If missing, the
      // compressed size is assumed to be the same size as the uncompressed
      // size.
      int64 compressed_size_bytes = 4;
    }

    // The timestamp indicating the time the event occurred.
    google.protobuf.Timestamp time = 1;

    // A `TimeEvent` can contain either an `Annotation` object or a
    // `MessageEvent` object, but not both.
    oneof value {
      // Text annotation with a set of attributes.
      Annotation annotation = 2;

      // An event describing a message sent/received between Spans.
      MessageEvent message_event = 3;
    }
  }

  // A collection of `TimeEvent`s. A `TimeEvent` is a time-stamped annotation
  // on the span, consisting of either user-supplied key:value pairs, or
  // details of a message sent/received between Spans.
  message TimeEvents {
    // A collection of `TimeEvent`s.
    repeated TimeEvent time_event = 1;

    // The number of dropped annotations in all the included time events.
    // If the value is 0, then no annotations were dropped.
    int32 dropped_annotations_count = 2;

    // The number of dropped message events in all the included time events.
    // If the value is 0, then no message events were dropped.
    int32 dropped_message_events_count = 3;
  }

  // A pointer from the current span to another span in the same trace or in a
  // different trace. For example, this can be used in batching operations,
  // where a single batch handler processes multiple requests from different
  // traces or when the handler receives a request from a different project.
  message Link {
    // The relationship of the current span relative to the linked span: child,
    // parent, or unspecified.
    enum Type {
      // The relationship of the two spans is unknown.
      TYPE_UNSPECIFIED = 0;

      // The linked span is a child of the current span.
      CHILD_LINKED_SPAN = 1;

      // The linked span is a parent of the current span.
      PARENT_LINKED_SPAN = 2;
    }

    // The `[TRACE_ID]` for a trace within a project.
    string trace_id = 1;

    // The `[SPAN_ID]` for a span within a trace.
    string span_id = 2;

    // The relationship of the current span relative to the linked span.
    Type type = 3;

    // A set of attributes on the link. Up to 32 attributes can be
    // specified per link.
    Attributes attributes = 4;
  }

  // A collection of links, which are references from this span to a span
  // in the same or different trace.
  message Links {
    // A collection of links.
    repeated Link link = 1;

    // The number of dropped links after the maximum size was enforced. If
    // this value is 0, then no links were dropped.
    int32 dropped_links_count = 2;
  }

  // Type of span. Can be used to specify additional relationships between spans
  // in addition to a parent/child relationship.
  enum SpanKind {
    // Unspecified. Do NOT use as default.
    // Implementations MAY assume SpanKind.INTERNAL to be default.
    SPAN_KIND_UNSPECIFIED = 0;

    // Indicates that the span is used internally. Default value.
    INTERNAL = 1;

    // Indicates that the span covers server-side handling of an RPC or other
    // remote network request.
    SERVER = 2;

    // Indicates that the span covers the client-side wrapper around an RPC or
    // other remote request.
    CLIENT = 3;

    // Indicates that the span describes producer sending a message to a broker.
    // Unlike client and  server, there is no direct critical path latency
    // relationship between producer and consumer spans (e.g. publishing a
    // message to a pubsub service).
    PRODUCER = 4;

    // Indicates that the span describes consumer receiving a message from a
    // broker. Unlike client and  server, there is no direct critical path
    // latency relationship between producer and consumer spans (e.g. receiving
    // a message from a pubsub service subscription).
    CONSUMER = 5;
  }

  // Required. The resource name of the span in the following format:
  //
  //  * `projects/[PROJECT_ID]/traces/[TRACE_ID]/spans/[SPAN_ID]`
  //
  // `[TRACE_ID]` is a unique identifier for a trace within a project;
  // it is a 32-character hexadecimal encoding of a 16-byte array. It should
  // not be zero.
  //
  // `[SPAN_ID]` is a unique identifier for a span within a trace; it
  // is a 16-character hexadecimal encoding of an 8-byte array. It should not
  // be zero.
  // .
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The `[SPAN_ID]` portion of the span's resource name.
  string span_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The `[SPAN_ID]` of this span's parent span. If this is a root span,
  // then this field must be empty.
  string parent_span_id = 3;

  // Required. A description of the span's operation (up to 128 bytes).
  // Cloud Trace displays the description in the
  // Cloud console.
  // For example, the display name can be a qualified method name or a file name
  // and a line number where the operation is called. A best practice is to use
  // the same display name within an application and at the same call point.
  // This makes it easier to correlate spans in different traces.
  TruncatableString display_name = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The start time of the span. On the client side, this is the time
  // kept by the local machine where the span execution starts. On the server
  // side, this is the time when the server's application handler starts
  // running.
  google.protobuf.Timestamp start_time = 5
      [(google.api.field_behavior) = REQUIRED];

  // Required. The end time of the span. On the client side, this is the time
  // kept by the local machine where the span execution ends. On the server
  // side, this is the time when the server application handler stops running.
  google.protobuf.Timestamp end_time = 6
      [(google.api.field_behavior) = REQUIRED];

  // A set of attributes on the span. You can have up to 32 attributes per
  // span.
  Attributes attributes = 7;

  // Stack trace captured at the start of the span.
  StackTrace stack_trace = 8;

  // A set of time events. You can have up to 32 annotations and 128 message
  // events per span.
  TimeEvents time_events = 9;

  // Links associated with the span. You can have up to 128 links per Span.
  Links links = 10;

  // Optional. The final status for this span.
  google.rpc.Status status = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set this parameter to indicate whether this span is in
  // the same process as its parent. If you do not set this parameter,
  // Trace is unable to take advantage of this helpful information.
  google.protobuf.BoolValue same_process_as_parent_span = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of child spans that were generated while this span
  // was active. If set, allows implementation to detect missing child spans.
  google.protobuf.Int32Value child_span_count = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Distinguishes between spans generated in a particular context.
  // For example, two spans with the same name may be distinguished using
  // `CLIENT` (caller) and `SERVER` (callee) to identify an RPC call.
  SpanKind span_kind = 14 [(google.api.field_behavior) = OPTIONAL];
}

// The allowed types for `[VALUE]` in a `[KEY]:[VALUE]` attribute.
message AttributeValue {
  // The type of the value.
  oneof value {
    // A string up to 256 bytes long.
    TruncatableString string_value = 1;

    // A 64-bit signed integer.
    int64 int_value = 2;

    // A Boolean value represented by `true` or `false`.
    bool bool_value = 3;
  }
}

// A call stack appearing in a trace.
message StackTrace {
  // Represents a single stack frame in a stack trace.
  message StackFrame {
    // The fully-qualified name that uniquely identifies the function or
    // method that is active in this frame (up to 1024 bytes).
    TruncatableString function_name = 1;

    // An un-mangled function name, if `function_name` is mangled.
    // To get information about name mangling, run
    // [this search](https://www.google.com/search?q=cxx+name+mangling).
    // The name can be fully-qualified (up to 1024 bytes).
    TruncatableString original_function_name = 2;

    // The name of the source file where the function call appears (up to 256
    // bytes).
    TruncatableString file_name = 3;

    // The line number in `file_name` where the function call appears.
    int64 line_number = 4;

    // The column number where the function call appears, if available.
    // This is important in JavaScript because of its anonymous functions.
    int64 column_number = 5;

    // The binary module from where the code was loaded.
    Module load_module = 6;

    // The version of the deployed source code (up to 128 bytes).
    TruncatableString source_version = 7;
  }

  // A collection of stack frames, which can be truncated.
  message StackFrames {
    // Stack frames in this call stack.
    repeated StackFrame frame = 1;

    // The number of stack frames that were dropped because there
    // were too many stack frames.
    // If this value is 0, then no stack frames were dropped.
    int32 dropped_frames_count = 2;
  }

  // Stack frames in this stack trace. A maximum of 128 frames are allowed.
  StackFrames stack_frames = 1;

  // The hash ID is used to conserve network bandwidth for duplicate
  // stack traces within a single trace.
  //
  // Often multiple spans will have identical stack traces.
  // The first occurrence of a stack trace should contain both the
  // `stackFrame` content and a value in `stackTraceHashId`.
  //
  // Subsequent spans within the same request can refer
  // to that stack trace by only setting `stackTraceHashId`.
  int64 stack_trace_hash_id = 2;
}

// Binary module.
message Module {
  // For example: main binary, kernel modules, and dynamic libraries
  // such as libc.so, sharedlib.so (up to 256 bytes).
  TruncatableString module = 1;

  // A unique identifier for the module, usually a hash of its
  // contents (up to 128 bytes).
  TruncatableString build_id = 2;
}

// Represents a string that might be shortened to a specified length.
message TruncatableString {
  // The shortened string. For example, if the original string is 500
  // bytes long and the limit of the string is 128 bytes, then
  // `value` contains the first 128 bytes of the 500-byte string.
  //
  // Truncation always happens on a UTF8 character boundary. If there
  // are multi-byte characters in the string, then the length of the
  // shortened string might be less than the size limit.
  string value = 1;

  // The number of bytes removed from the original string. If this
  // value is 0, then the string was not shortened.
  int32 truncated_byte_count = 2;
}
