// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.clouderrorreporting.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/devtools/clouderrorreporting/v1beta1/common.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.ErrorReporting.V1Beta1";
option go_package = "cloud.google.com/go/errorreporting/apiv1beta1/errorreportingpb;errorreportingpb";
option java_multiple_files = true;
option java_outer_classname = "ErrorGroupServiceProto";
option java_package = "com.google.devtools.clouderrorreporting.v1beta1";
option php_namespace = "Google\\Cloud\\ErrorReporting\\V1beta1";
option ruby_package = "Google::Cloud::ErrorReporting::V1beta1";

// Service for retrieving and updating individual error groups.
service ErrorGroupService {
  option (google.api.default_host) = "clouderrorreporting.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Get the specified group.
  rpc GetGroup(GetGroupRequest) returns (ErrorGroup) {
    option (google.api.http) = {
      get: "/v1beta1/{group_name=projects/*/groups/*}"
      additional_bindings {
        get: "/v1beta1/{group_name=projects/*/locations/*/groups/*}"
      }
    };
    option (google.api.method_signature) = "group_name";
  }

  // Replace the data for the specified group.
  // Fails if the group does not exist.
  rpc UpdateGroup(UpdateGroupRequest) returns (ErrorGroup) {
    option (google.api.http) = {
      put: "/v1beta1/{group.name=projects/*/groups/*}"
      body: "group"
      additional_bindings {
        put: "/v1beta1/{group.name=projects/*/locations/*/groups/*}"
        body: "group"
      }
    };
    option (google.api.method_signature) = "group";
  }
}

// A request to return an individual group.
message GetGroupRequest {
  // Required. The group resource name. Written as either
  // `projects/{projectID}/groups/{group_id}` or
  // `projects/{projectID}/locations/{location}/groups/{group_id}`. Call
  // [groupStats.list]
  // [google.devtools.clouderrorreporting.v1beta1.ErrorStatsService.ListGroupStats]
  // to return a list of groups belonging to this project.
  //
  // Examples: `projects/my-project-123/groups/my-group`,
  // `projects/my-project-123/locations/global/groups/my-group`
  //
  // In the group resource name, the `group_id` is a unique identifier for a
  // particular error group. The identifier is derived from key parts of the
  // error-log content and is treated as Service Data. For information about
  // how Service Data is handled, see [Google Cloud Privacy
  // Notice](https://cloud.google.com/terms/cloud-privacy-notice).
  //
  // For a list of supported locations, see [Supported
  // Regions](https://cloud.google.com/logging/docs/region-support). `global` is
  // the default when unspecified.
  string group_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouderrorreporting.googleapis.com/ErrorGroup"
    }
  ];
}

// A request to replace the existing data for the given group.
message UpdateGroupRequest {
  // Required. The group which replaces the resource on the server.
  ErrorGroup group = 1 [(google.api.field_behavior) = REQUIRED];
}
