// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.testing.v1;

option go_package = "google.golang.org/genproto/googleapis/devtools/testing/v1;testing";
option java_multiple_files = true;
option java_outer_classname = "AdbServiceProto";
option java_package = "com.google.devtools.testing.v1";

// API-facing proto equivalent to the internal ADB Service proto. In general,
// this proto should be equivalent of the messages defined in the internal
// ADB Device Forwarder, with the caveat that these support a self-sufficient
// API, which is the best practice at the time of writing.

// A message returned from a device.
message DeviceMessage {
  oneof contents {
    // Information about the device's state.
    StatusUpdate status_update = 1;

    // The result of a device stream from ADB.
    StreamStatus stream_status = 2;

    // Data from an open stream.
    StreamData stream_data = 3;
  }
}

// A message to an ADB server.
message AdbMessage {
  oneof contents {
    // Open a new stream.
    Open open = 1;

    // Send data to a stream.
    StreamData stream_data = 2;
  }
}

// A StatusUpdate message given over the ADB protocol for the device state.
message StatusUpdate {
  // The state displayed with the ADB Device when running "adb devices"
  enum DeviceState {
    // The device state is unknown.
    DEVICE_STATE_UNSPECIFIED = 0;

    // The ADB device is in the "device" status.
    DEVICE = 1;

    // The ADB device is in the "recovery" status.
    RECOVERY = 2;

    // The ADB device is in the "rescue" status.
    RESCUE = 3;

    // The ADB device is in the "sideload" status.
    SIDELOAD = 4;

    // The ADB device is in the "missing" status.
    MISSING = 10;

    // The ADB device is in the "offline" status.
    OFFLINE = 11;

    // The ADB device is in the "unauthorized" status.
    UNAUTHORIZED = 12;

    // The ADB device is in the "authorizing" status.
    AUTHORIZING = 13;

    // The ADB device is in the "connecting" status.
    CONNECTING = 14;
  }

  // The device's state
  DeviceState state = 1;

  // A map of properties with information about this device.
  map<string, string> properties = 2;

  // A comma-separated list of "features" that this device supports.
  string features = 3;
}

// The result of a stream.
message StreamStatus {
  // The unique ID of this stream, assigned by the client.
  int32 stream_id = 1;

  // The result of the stream. Either "Okay" for success or "Fail" for failure.
  oneof status {
    // Okay for success.
    Okay okay = 2;

    // Fail for failure.
    Fail fail = 3;
  }
}

// Message for opening a new stream.
message Open {
  // The unique ID that will be used to talk to this stream. This should
  // probably just be a number that increments for each new Open request.
  int32 stream_id = 1;

  // An ADB service to use in the new stream.
  string service = 2;
}

// Data for a stream.
message StreamData {
  // The unique ID of this stream, assigned by the client.
  int32 stream_id = 1;

  // The data of the stream, either bytes or "Close", indicating that the stream
  // is done.
  oneof contents {
    // Data in the stream.
    bytes data = 2;

    // The stream is closing. EOF.
    Close close = 3;
  }
}

// Message signifying that the stream is open
message Okay {}

// Message signifying that the stream failed to open
message Fail {
  // A user-displayable failure reason.
  string reason = 1;
}

// Message signifying that the stream closed.
message Close {}
