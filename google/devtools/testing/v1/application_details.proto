// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.testing.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/devtools/testing/v1/test_execution.proto";

option go_package = "google.golang.org/genproto/googleapis/devtools/testing/v1;testing";
option java_multiple_files = true;
option java_outer_classname = "ApplicationDetailProto";
option java_package = "com.google.devtools.testing.v1";

// A service which parses input applications and returns details that can be
// useful in the context of testing.
service ApplicationDetailService {
  option (google.api.default_host) = "testing.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets the details of an Android application APK.
  rpc GetApkDetails(GetApkDetailsRequest) returns (GetApkDetailsResponse) {
    option (google.api.http) = {
      post: "/v1/applicationDetailService/getApkDetails"
      body: "location"
    };
  }
}

// Android application details based on application manifest and archive
// contents.
message ApkDetail {
  ApkManifest apk_manifest = 1;
}

// An Android app manifest. See
// http://developer.android.com/guide/topics/manifest/manifest-intro.html
message ApkManifest {
  // Full Java-style package name for this application, e.g.
  // "com.example.foo".
  string package_name = 1;

  // Minimum API level required for the application to run.
  int32 min_sdk_version = 2;

  // Maximum API level on which the application is designed to run.
  int32 max_sdk_version = 3;

  // Specifies the API Level on which the application is designed to run.
  int32 target_sdk_version = 6;

  // User-readable name for the application.
  string application_label = 4;

  repeated IntentFilter intent_filters = 5;

  // Permissions declared to be used by the application
  repeated UsesPermissionTag uses_permission_tags = 13;

  repeated string uses_permission = 7;

  // Version number used internally by the app.
  int64 version_code = 8;

  // Version number shown to users.
  string version_name = 9;

  // Meta-data tags defined in the manifest.
  repeated Metadata metadata = 10;

  // Feature usage tags defined in the manifest.
  repeated UsesFeature uses_feature = 11;

  // Services contained in the <application> tag.
  repeated Service services = 12;
}

// The <uses-permission> tag within a manifest.
// https://developer.android.com/guide/topics/manifest/uses-permission-element.html
message UsesPermissionTag {
  // The android:name value
  string name = 1;

  // The android:name value
  int32 max_sdk_version = 2;
}

// The <service> section of an <application> tag.
// https://developer.android.com/guide/topics/manifest/service-element
message Service {
  // The android:name value
  string name = 1;

  // Intent filters in the service
  repeated IntentFilter intent_filter = 2;
}

// The <intent-filter> section of an <activity> tag.
// https://developer.android.com/guide/topics/manifest/intent-filter-element.html
message IntentFilter {
  // The android:name value of the <action> tag.
  repeated string action_names = 1;

  // The android:name value of the <category> tag.
  repeated string category_names = 2;

  // The android:mimeType value of the <data> tag.
  string mime_type = 3;
}

// A <meta-data> tag within a manifest.
// https://developer.android.com/guide/topics/manifest/meta-data-element.html
message Metadata {
  // The android:name value
  string name = 1;

  // The android:value value
  string value = 2;
}

// A <uses-feature> tag within a manifest.
// https://developer.android.com/guide/topics/manifest/uses-feature-element.html
message UsesFeature {
  // The android:name value
  string name = 1;

  // The android:required value
  bool is_required = 2;
}

// A request to get the details of an Android application.
message GetApkDetailsRequest {
  // Optional. The APK to be parsed for details.
  FileReference location = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The App Bundle to be parsed for details.
  FileReference bundle_location = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response containing the details of the specified Android application.
message GetApkDetailsResponse {
  // Details of the Android App.
  ApkDetail apk_detail = 1;
}
