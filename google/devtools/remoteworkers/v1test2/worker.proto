// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.devtools.remoteworkers.v1test2;

option csharp_namespace = "Google.DevTools.RemoteWorkers.V1Test2";
option go_package = "google.golang.org/genproto/googleapis/devtools/remoteworkers/v1test2;remoteworkers";
option java_multiple_files = true;
option java_outer_classname = "RemoteWorkersWorker";
option java_package = "com.google.devtools.remoteworkers.v1test2";
option php_namespace = "Google\\Cloud\\Remoteworkers\\V1test2";
option objc_class_prefix = "RW";

// Describes a worker, which is a list of one or more devices and the
// connections between them. A device could be a computer, a phone, or even an
// accelerator like a GPU; it's up to the farm administrator to decide how to
// model their farm. For example, if a farm only has one type of GPU, the GPU
// could be modelled as a "has_gpu" property on its host computer; if it has
// many subproperties itself, it might be better to model it as a separate
// device.
//
// The first device in the worker is the "primary device" - that is, the device
// running a bot and which is responsible for actually executing commands. All
// other devices are considered to be attached devices, and must be controllable
// by the primary device.
//
// This message (and all its submessages) can be used in two contexts:
//
// * Status: sent by the bot to report the current capabilities of the device to
// allow reservation matching.
// * Request: sent by a client to request a device with certain capabilities in
// a reservation.
//
// Several of the fields in this message have different semantics depending on
// which of which of these contexts it is used. These semantics are described
// below.
//
// Several messages in Worker and its submessages have the concept of keys and
// values, such as `Worker.Property` and `Device.Property`. All keys are simple
// strings, but certain keys are "standard" keys and should be broadly supported
// across farms and implementations; these are listed below each relevant
// message. Bot implementations or farm admins may add *additional* keys, but
// these SHOULD all begin with an underscore so they do not conflict with
// standard keys that may be added in the future.
//
// Keys are not context sensitive.
//
// See http://goo.gl/NurY8g for more information on the Worker message.
message Worker {
  // A global property; see the `properties` field for more information.
  message Property {
    // For general information on keys, see the documentation to `Worker`.
    //
    // The current set of standard keys are:
    //
    // * pool: different workers can be reserved for different purposes. For
    // example, an admin might want to segregate long-running integration tests
    // from short-running unit tests, so unit tests will always get some
    // throughput. To support this, the server can assign different values for
    // `pool` (such as "itest" and "utest") to different workers, and then have
    // jobs request workers from those pools.
    string key = 1;

    // The property's value.
    string value = 2;
  }

  // A configuration request or report; see the `configs` field for more
  // information.
  message Config {
    // For general information on keys, see the documentation to `Worker`.
    //
    // The current set of standard keys are:
    //
    // * DockerImage: the image of the container. When being reported by the
    // bot, the empty value should always be included if the bot is able to pull
    // its own images; the bot may optionally *also* report images that are
    // present in its cache. When being requested in a lease, the value is the
    // URI of the image (eg `gcr.io/user/image@sha256:hash`).
    string key = 1;

    // The configuration's value.
    string value = 2;
  }

  // A list of devices; the first device is the primary device. See the `Device`
  // message for more information.
  repeated Device devices = 1;

  // A worker may contain "global" properties. For example, certain machines
  // might be reserved for certain types of jobs, like short-running compilation
  // versus long-running integration tests. This property is known as a "pool"
  // and is not related to any one device within the worker; rather, it applies
  // to the worker as a whole.
  //
  // The behaviour of repeated keys is identical to that of Device.Property.
  repeated Property properties = 2;

  // Bots can be configured in certain ways when accepting leases. For example,
  // many leases are executed inside a Docker container. To support this, the
  // bot needs to be able to report that it has Docker installed (and knows how
  // to execute something inside a container), and the task submitter needs to
  // specify which image should be used to start the container. Similarly, a
  // lease may be able to run as one of several users on the worker; in such
  // cases, the bot needs to report what users are available, and the submitter
  // needs to choose one.
  //
  // Therefore, when this message is reported by the bot to the service, each
  // key represents a *type* of configuration that the bot knows how to set,
  // while each *value* represents a legal value for that configuration (the
  // empty string is interpretted as a wildcard, such as for Docker images).
  // When this message is sent by the server to the bot in the context of a
  // lease, it represents a command to the bot to apply the setting. Keys may
  // be repeated during reporting but not in a lease.
  repeated Config configs = 3;
}

// Any device, including computers, phones, accelerators (e.g. GPUs), etc. All
// names must be unique.
message Device {
  // A device property; see `properties` for more information.
  message Property {
    // For general information on keys, see the documentation to `Worker`.
    //
    // The current set of standard keys are:
    //
    // * os: a human-readable description of the OS. Examples include `linux`,
    // `ubuntu` and `ubuntu 14.04` (note that a bot may advertise itself as more
    // than one). This will be replaced in the future by more well-structured
    // keys and values to represent OS variants.
    //
    // * has-docker: "true" if the bot has Docker installed. This will be
    // replaced in the future by a more structured message for Docker support.
    string key = 1;

    // The property's value.
    string value = 2;
  }

  // The handle can be thought of as the "name" of the device, and must be
  // unique within a Worker.
  //
  // In the Status context, the handle should be some human-understandable name,
  // perhaps corresponding to a label physically written on the device to make
  // it easy to locate. In the Request context, the name should be the
  // *logical* name expected by the task. The bot is responsible for mapping the
  // logical name expected by the task to a machine-readable name that the task
  // can actually use, such as a USB address. The method by which this mapping
  // is communicated to the task is not covered in this API.
  string handle = 1;

  // Properties of this device that don't change based on the tasks that are
  // running on it, e.g. OS, CPU architecture, etc.
  //
  // Keys may be repeated, and have the following interpretation:
  //
  //    * Status context: the device can support *any* the listed values. For
  //    example, an "ISA" property might include "x86", "x86-64" and "sse4".
  //
  //    * Request context: the device *must* support *all* of the listed values.
  repeated Property properties = 2;
}
