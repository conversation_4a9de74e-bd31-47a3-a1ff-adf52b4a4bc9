# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "remoteworkers_proto",
    srcs = [
        "bots.proto",
        "command.proto",
        "worker.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "remoteworkers_proto_with_info",
    deps = [
        ":remoteworkers_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "remoteworkers_java_proto",
    deps = [":remoteworkers_proto"],
)

java_grpc_library(
    name = "remoteworkers_java_grpc",
    srcs = [":remoteworkers_proto"],
    deps = [":remoteworkers_java_proto"],
)

java_gapic_library(
    name = "remoteworkers_java_gapic",
    srcs = [":remoteworkers_proto_with_info"],
    gapic_yaml = "remoteworkers_gapic.yaml",
    grpc_service_config = "remoteworkers_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "remoteworkers_v1beta2.yaml",
    test_deps = [
        ":remoteworkers_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":remoteworkers_java_proto",
    ],
)

java_gapic_test(
    name = "remoteworkers_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.devtools.remoteworkers.v1test2.BotsClientHttpJsonTest",
        "com.google.cloud.devtools.remoteworkers.v1test2.BotsClientTest",
    ],
    runtime_deps = [":remoteworkers_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-devtools-remoteworkers-v1test2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":remoteworkers_java_gapic",
        ":remoteworkers_java_grpc",
        ":remoteworkers_java_proto",
        ":remoteworkers_proto",
    ],
)

go_proto_library(
    name = "remoteworkers_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/devtools/remoteworkers/v1test2",
    protos = [":remoteworkers_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "remoteworkers_go_gapic",
    srcs = [":remoteworkers_proto_with_info"],
    grpc_service_config = "remoteworkers_grpc_service_config.json",
    importpath = "cloud.google.com/go/devtools/remoteworkers/apiv1test2;remoteworkers",
    rest_numeric_enums = False,
    service_yaml = "remoteworkers_v1beta2.yaml",
    transport = "grpc",
    deps = [
        ":remoteworkers_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-devtools-remoteworkers-v1test2-go",
    deps = [
        ":remoteworkers_go_gapic",
        ":remoteworkers_go_gapic_srcjar-snippets.srcjar",
        ":remoteworkers_go_gapic_srcjar-test.srcjar",
        ":remoteworkers_go_proto",
    ],
)

py_gapic_library(
    name = "remoteworkers_py_gapic",
    srcs = [":remoteworkers_proto"],
    grpc_service_config = "remoteworkers_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "remoteworkers_v1beta2.yaml",
    transport = "grpc",
)

py_test(
    name = "remoteworkers_py_gapic_test",
    srcs = [
        "remoteworkers_py_gapic_pytest.py",
        "remoteworkers_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":remoteworkers_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "devtools-remoteworkers-v1test2-py",
    deps = [
        ":remoteworkers_py_gapic",
    ],
)

php_proto_library(
    name = "remoteworkers_php_proto",
    deps = [":remoteworkers_proto"],
)

php_gapic_library(
    name = "remoteworkers_php_gapic",
    srcs = [":remoteworkers_proto_with_info"],
    grpc_service_config = "remoteworkers_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "remoteworkers_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [":remoteworkers_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-devtools-remoteworkers-v1test2-php",
    deps = [
        ":remoteworkers_php_gapic",
        ":remoteworkers_php_proto",
    ],
)

nodejs_gapic_library(
    name = "remoteworkers_nodejs_gapic",
    src = ":remoteworkers_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "remoteworkers_grpc_service_config.json",
    package = "google.devtools.remoteworkers.v1test2",
    rest_numeric_enums = False,
    service_yaml = "remoteworkers_v1beta2.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "devtools-remoteworkers-v1test2-nodejs",
    deps = [
        ":remoteworkers_nodejs_gapic",
        ":remoteworkers_proto",
    ],
)

ruby_proto_library(
    name = "remoteworkers_ruby_proto",
    deps = [":remoteworkers_proto"],
)

ruby_grpc_library(
    name = "remoteworkers_ruby_grpc",
    srcs = [":remoteworkers_proto"],
    deps = [":remoteworkers_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "remoteworkers_ruby_gapic",
    srcs = [":remoteworkers_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-remoteworkers-v1test2"],
    rest_numeric_enums = False,
    service_yaml = "remoteworkers_v1beta2.yaml",
    deps = [
        ":remoteworkers_ruby_grpc",
        ":remoteworkers_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-devtools-remoteworkers-v1test2-ruby",
    deps = [
        ":remoteworkers_ruby_gapic",
        ":remoteworkers_ruby_grpc",
        ":remoteworkers_ruby_proto",
    ],
)

csharp_proto_library(
    name = "remoteworkers_csharp_proto",
    deps = [":remoteworkers_proto"],
)

csharp_grpc_library(
    name = "remoteworkers_csharp_grpc",
    srcs = [":remoteworkers_proto"],
    deps = [":remoteworkers_csharp_proto"],
)

csharp_gapic_library(
    name = "remoteworkers_csharp_gapic",
    srcs = [":remoteworkers_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "remoteworkers_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "remoteworkers_v1beta2.yaml",
    deps = [
        ":remoteworkers_csharp_grpc",
        ":remoteworkers_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-devtools-remoteworkers-v1test2-csharp",
    deps = [
        ":remoteworkers_csharp_gapic",
        ":remoteworkers_csharp_grpc",
        ":remoteworkers_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
