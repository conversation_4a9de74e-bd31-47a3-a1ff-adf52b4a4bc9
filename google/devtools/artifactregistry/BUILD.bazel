# This build file includes a target for the Ruby wrapper library for
# google-cloud-artifact_registry.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for artifactregistry.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "artifactregistry_ruby_wrapper",
    srcs = ["//google/devtools/artifactregistry/v1:artifactregistry_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-artifact_registry",
        "ruby-cloud-env-prefix=ARTIFACT_REGISTRY",
        "ruby-cloud-wrapper-of=v1:0.12;v1beta2:0.11",
        "ruby-cloud-product-url=https://cloud.google.com/artifact-registry/",
        "ruby-cloud-api-id=artifactregistry.googleapis.com",
        "ruby-cloud-api-shortname=artifactregistry",
    ],
    ruby_cloud_description = "Artifact Registry stores and manages build artifacts in a scalable and integrated service built on Google infrastructure.",
    ruby_cloud_title = "Artifact Registry",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-artifactregistry-ruby",
    deps = [
        ":artifactregistry_ruby_wrapper",
    ],
)
