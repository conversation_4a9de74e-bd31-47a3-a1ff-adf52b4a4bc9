// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1beta2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/devtools/artifactregistry/v1beta2/apt_artifact.proto";
import "google/devtools/artifactregistry/v1beta2/file.proto";
import "google/devtools/artifactregistry/v1beta2/package.proto";
import "google/devtools/artifactregistry/v1beta2/repository.proto";
import "google/devtools/artifactregistry/v1beta2/settings.proto";
import "google/devtools/artifactregistry/v1beta2/tag.proto";
import "google/devtools/artifactregistry/v1beta2/version.proto";
import "google/devtools/artifactregistry/v1beta2/yum_artifact.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1Beta2";
option go_package = "cloud.google.com/go/artifactregistry/apiv1beta2/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.devtools.artifactregistry.v1beta2";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1beta2";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1beta2";

// The Artifact Registry API service.
//
// Artifact Registry is an artifact management system for storing artifacts
// from different package management systems.
//
// The resources managed by this API are:
//
// * Repositories, which group packages and their data.
// * Packages, which group versions and their tags.
// * Versions, which are specific forms of a package.
// * Tags, which represent alternative names for versions.
// * Files, which contain content and are optionally associated with a Package
//   or Version.
service ArtifactRegistry {
  option (google.api.default_host) = "artifactregistry.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Imports Apt artifacts. The returned Operation will complete once the
  // resources are imported. Package, Version, and File resources are created
  // based on the imported artifacts. Imported artifacts that conflict with
  // existing resources are ignored.
  rpc ImportAptArtifacts(ImportAptArtifactsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta2/{parent=projects/*/locations/*/repositories/*}/aptArtifacts:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.devtools.artifactregistry.v1beta2.ImportAptArtifactsResponse"
      metadata_type: "google.devtools.artifactregistry.v1beta2.ImportAptArtifactsMetadata"
    };
  }

  // Imports Yum (RPM) artifacts. The returned Operation will complete once the
  // resources are imported. Package, Version, and File resources are created
  // based on the imported artifacts. Imported artifacts that conflict with
  // existing resources are ignored.
  rpc ImportYumArtifacts(ImportYumArtifactsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta2/{parent=projects/*/locations/*/repositories/*}/yumArtifacts:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.devtools.artifactregistry.v1beta2.ImportYumArtifactsResponse"
      metadata_type: "google.devtools.artifactregistry.v1beta2.ImportYumArtifactsMetadata"
    };
  }

  // Lists repositories.
  rpc ListRepositories(ListRepositoriesRequest) returns (ListRepositoriesResponse) {
    option (google.api.http) = {
      get: "/v1beta2/{parent=projects/*/locations/*}/repositories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a repository.
  rpc GetRepository(GetRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      get: "/v1beta2/{name=projects/*/locations/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a repository. The returned Operation will finish once the
  // repository has been created. Its response will be the created Repository.
  rpc CreateRepository(CreateRepositoryRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta2/{parent=projects/*/locations/*}/repositories"
      body: "repository"
    };
    option (google.api.method_signature) = "parent,repository,repository_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.devtools.artifactregistry.v1beta2.Repository"
      metadata_type: "google.devtools.artifactregistry.v1beta2.OperationMetadata"
    };
  }

  // Updates a repository.
  rpc UpdateRepository(UpdateRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      patch: "/v1beta2/{repository.name=projects/*/locations/*/repositories/*}"
      body: "repository"
    };
    option (google.api.method_signature) = "repository,update_mask";
  }

  // Deletes a repository and all of its contents. The returned Operation will
  // finish once the repository has been deleted. It will not have any Operation
  // metadata and will return a google.protobuf.Empty response.
  rpc DeleteRepository(DeleteRepositoryRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta2/{name=projects/*/locations/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1beta2.OperationMetadata"
    };
  }

  // Lists packages.
  rpc ListPackages(ListPackagesRequest) returns (ListPackagesResponse) {
    option (google.api.http) = {
      get: "/v1beta2/{parent=projects/*/locations/*/repositories/*}/packages"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a package.
  rpc GetPackage(GetPackageRequest) returns (Package) {
    option (google.api.http) = {
      get: "/v1beta2/{name=projects/*/locations/*/repositories/*/packages/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a package and all of its versions and tags. The returned operation
  // will complete once the package has been deleted.
  rpc DeletePackage(DeletePackageRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta2/{name=projects/*/locations/*/repositories/*/packages/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1beta2.OperationMetadata"
    };
  }

  // Lists versions.
  rpc ListVersions(ListVersionsRequest) returns (ListVersionsResponse) {
    option (google.api.http) = {
      get: "/v1beta2/{parent=projects/*/locations/*/repositories/*/packages/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a version
  rpc GetVersion(GetVersionRequest) returns (Version) {
    option (google.api.http) = {
      get: "/v1beta2/{name=projects/*/locations/*/repositories/*/packages/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a version and all of its content. The returned operation will
  // complete once the version has been deleted.
  rpc DeleteVersion(DeleteVersionRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta2/{name=projects/*/locations/*/repositories/*/packages/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1beta2.OperationMetadata"
    };
  }

  // Lists files.
  rpc ListFiles(ListFilesRequest) returns (ListFilesResponse) {
    option (google.api.http) = {
      get: "/v1beta2/{parent=projects/*/locations/*/repositories/*}/files"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a file.
  rpc GetFile(GetFileRequest) returns (File) {
    option (google.api.http) = {
      get: "/v1beta2/{name=projects/*/locations/*/repositories/*/files/**}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists tags.
  rpc ListTags(ListTagsRequest) returns (ListTagsResponse) {
    option (google.api.http) = {
      get: "/v1beta2/{parent=projects/*/locations/*/repositories/*/packages/*}/tags"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a tag.
  rpc GetTag(GetTagRequest) returns (Tag) {
    option (google.api.http) = {
      get: "/v1beta2/{name=projects/*/locations/*/repositories/*/packages/*/tags/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a tag.
  rpc CreateTag(CreateTagRequest) returns (Tag) {
    option (google.api.http) = {
      post: "/v1beta2/{parent=projects/*/locations/*/repositories/*/packages/*}/tags"
      body: "tag"
    };
    option (google.api.method_signature) = "parent,tag,tag_id";
  }

  // Updates a tag.
  rpc UpdateTag(UpdateTagRequest) returns (Tag) {
    option (google.api.http) = {
      patch: "/v1beta2/{tag.name=projects/*/locations/*/repositories/*/packages/*/tags/*}"
      body: "tag"
    };
    option (google.api.method_signature) = "tag,update_mask";
  }

  // Deletes a tag.
  rpc DeleteTag(DeleteTagRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta2/{name=projects/*/locations/*/repositories/*/packages/*/tags/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the IAM policy for a given resource.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta2/{resource=projects/*/locations/*/repositories/*}:setIamPolicy"
      body: "*"
    };
  }

  // Gets the IAM policy for a given resource.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      get: "/v1beta2/{resource=projects/*/locations/*/repositories/*}:getIamPolicy"
    };
  }

  // Tests if the caller has a list of permissions on a resource.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest) returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1beta2/{resource=projects/*/locations/*/repositories/*}:testIamPermissions"
      body: "*"
    };
  }

  // Retrieves the Settings for the Project.
  rpc GetProjectSettings(GetProjectSettingsRequest) returns (ProjectSettings) {
    option (google.api.http) = {
      get: "/v1beta2/{name=projects/*/projectSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the Settings for the Project.
  rpc UpdateProjectSettings(UpdateProjectSettingsRequest) returns (ProjectSettings) {
    option (google.api.http) = {
      patch: "/v1beta2/{project_settings.name=projects/*/projectSettings}"
      body: "project_settings"
    };
    option (google.api.method_signature) = "project_settings,update_mask";
  }
}

// Metadata type for longrunning-operations, currently empty.
message OperationMetadata {

}
