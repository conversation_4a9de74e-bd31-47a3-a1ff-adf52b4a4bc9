// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1beta2;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1Beta2";
option go_package = "cloud.google.com/go/artifactregistry/apiv1beta2/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "PackageProto";
option java_package = "com.google.devtools.artifactregistry.v1beta2";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1beta2";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1beta2";

// Packages are named collections of versions.
message Package {
  // The name of the package, for example:
  // "projects/p1/locations/us-central1/repositories/repo1/packages/pkg1".
  string name = 1;

  // The display name of the package.
  string display_name = 2;

  // The time when the package was created.
  google.protobuf.Timestamp create_time = 5;

  // The time when the package was last updated. This includes publishing a new
  // version of the package.
  google.protobuf.Timestamp update_time = 6;
}

// The request to list packages.
message ListPackagesRequest {
  // The name of the parent resource whose packages will be listed.
  string parent = 1;

  // The maximum number of packages to return.
  // Maximum page size is 10,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;
}

// The response from listing packages.
message ListPackagesResponse {
  // The packages returned.
  repeated Package packages = 1;

  // The token to retrieve the next page of packages, or empty if there are no
  // more packages to return.
  string next_page_token = 2;
}

// The request to retrieve a package.
message GetPackageRequest {
  // The name of the package to retrieve.
  string name = 1;
}

// The request to delete a package.
message DeletePackageRequest {
  // The name of the package to delete.
  string name = 1;
}
