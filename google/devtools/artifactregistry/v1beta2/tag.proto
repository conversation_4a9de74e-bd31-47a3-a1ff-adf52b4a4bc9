// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1beta2;

import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1Beta2";
option go_package = "cloud.google.com/go/artifactregistry/apiv1beta2/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "TagProto";
option java_package = "com.google.devtools.artifactregistry.v1beta2";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1beta2";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1beta2";

// Tags point to a version and represent an alternative name that can be used
// to access the version.
message Tag {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/Tag"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/packages/{package}/tags/{tag}"
  };

  // The name of the tag, for example:
  // "projects/p1/locations/us-central1/repositories/repo1/packages/pkg1/tags/tag1".
  // If the package part contains slashes, the slashes are escaped.
  // The tag part can only have characters in [a-zA-Z0-9\-._~:@], anything else
  // must be URL encoded.
  string name = 1;

  // The name of the version the tag refers to, for example:
  // "projects/p1/locations/us-central1/repositories/repo1/packages/pkg1/versions/sha256:5243811"
  // If the package or version ID parts contain slashes, the slashes are
  // escaped.
  string version = 2;
}

// The request to list tags.
message ListTagsRequest {
  // The name of the parent resource whose tags will be listed.
  string parent = 1;

  // An expression for filtering the results of the request. Filter rules are
  // case insensitive. The fields eligible for filtering are:
  //
  //   * `version`
  //
  //  An example of using a filter:
  //
  //   * `version="projects/p1/locations/us-central1/repositories/repo1/packages/pkg1/versions/1.0"`
  //   --> Tags that are applied to the version `1.0` in package `pkg1`.
  string filter = 4;

  // The maximum number of tags to return. Maximum page size is 10,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;
}

// The response from listing tags.
message ListTagsResponse {
  // The tags returned.
  repeated Tag tags = 1;

  // The token to retrieve the next page of tags, or empty if there are no
  // more tags to return.
  string next_page_token = 2;
}

// The request to retrieve a tag.
message GetTagRequest {
  // The name of the tag to retrieve.
  string name = 1;
}

// The request to create a new tag.
message CreateTagRequest {
  // The name of the parent resource where the tag will be created.
  string parent = 1;

  // The tag id to use for this repository.
  string tag_id = 2;

  // The tag to be created.
  Tag tag = 3;
}

// The request to create or update a tag.
message UpdateTagRequest {
  // The tag that replaces the resource on the server.
  Tag tag = 1;

  // The update mask applies to the resource. For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2;
}

// The request to delete a tag.
message DeleteTagRequest {
  // The name of the tag to delete.
  string name = 1;
}
