// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1beta2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1Beta2";
option go_package = "cloud.google.com/go/artifactregistry/apiv1beta2/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "SettingsProto";
option java_package = "com.google.devtools.artifactregistry.v1beta2";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1beta2";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1beta2";

// The Artifact Registry settings that apply to a Project.
message ProjectSettings {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/ProjectSettings"
    pattern: "projects/{project}/projectSettings"
  };

  // The possible redirection states for legacy repositories.
  enum RedirectionState {
    // No redirection status has been set.
    REDIRECTION_STATE_UNSPECIFIED = 0;

    // Redirection is disabled.
    REDIRECTION_FROM_GCR_IO_DISABLED = 1;

    // Redirection is enabled.
    REDIRECTION_FROM_GCR_IO_ENABLED = 2;

    // Redirection is enabled, and has been finalized so cannot be reverted.
    REDIRECTION_FROM_GCR_IO_FINALIZED = 3;
  }

  // The name of the project's settings.
  //
  // Always of the form:
  // projects/{project-id}/projectSettings
  //
  // In update request: never set
  // In response: always set
  string name = 1;

  // The redirection state of the legacy repositories in this project.
  RedirectionState legacy_redirection_state = 2;
}

// Gets the redirection status for a project.
message GetProjectSettingsRequest {
  // Required. The name of the projectSettings resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/ProjectSettings"
    }
  ];
}

// Sets the settings of the project.
message UpdateProjectSettingsRequest {
  // The project settings.
  ProjectSettings project_settings = 2;

  // Field mask to support partial updates.
  google.protobuf.FieldMask update_mask = 3;
}
