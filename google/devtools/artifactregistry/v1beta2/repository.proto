// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1beta2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1Beta2";
option go_package = "cloud.google.com/go/artifactregistry/apiv1beta2/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "RepositoryProto";
option java_package = "com.google.devtools.artifactregistry.v1beta2";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1beta2";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1beta2";

// A Repository for storing artifacts with a specific format.
message Repository {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/Repository"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}"
  };

  // MavenRepositoryConfig is maven related repository details.
  // Provides additional configuration details for repositories of the maven
  // format type.
  message MavenRepositoryConfig {
    // VersionPolicy is the version policy for the repository.
    enum VersionPolicy {
      // VERSION_POLICY_UNSPECIFIED - the version policy is not defined.
      // When the version policy is not defined, no validation is performed
      // for the versions.
      VERSION_POLICY_UNSPECIFIED = 0;

      // RELEASE - repository will accept only Release versions.
      RELEASE = 1;

      // SNAPSHOT - repository will accept only Snapshot versions.
      SNAPSHOT = 2;
    }

    // The repository with this flag will allow publishing
    // the same snapshot versions.
    bool allow_snapshot_overwrites = 1;

    // Version policy defines the versions that the registry will accept.
    VersionPolicy version_policy = 2;
  }

  // A package format.
  enum Format {
    // Unspecified package format.
    FORMAT_UNSPECIFIED = 0;

    // Docker package format.
    DOCKER = 1;

    // Maven package format.
    MAVEN = 2;

    // NPM package format.
    NPM = 3;

    // APT package format.
    APT = 5;

    // YUM package format.
    YUM = 6;

    // Python package format.
    PYTHON = 8;
  }

  // Repository-specific configurations.
  oneof format_config {
    // Maven repository config contains repository level configuration
    // for the repositories of maven type.
    MavenRepositoryConfig maven_config = 9;
  }

  // The name of the repository, for example:
  // "projects/p1/locations/us-central1/repositories/repo1".
  string name = 1;

  // The format of packages that are stored in the repository.
  Format format = 2;

  // The user-provided description of the repository.
  string description = 3;

  // Labels with user-defined metadata.
  // This field may contain up to 64 entries. Label keys and values may be no
  // longer than 63 characters. Label keys must begin with a lowercase letter
  // and may only contain lowercase letters, numeric characters, underscores,
  // and dashes.
  map<string, string> labels = 4;

  // The time when the repository was created.
  google.protobuf.Timestamp create_time = 5;

  // The time when the repository was last updated.
  google.protobuf.Timestamp update_time = 6;

  // The Cloud KMS resource name of the customer managed encryption key that’s
  // used to encrypt the contents of the Repository. Has the form:
  // `projects/my-project/locations/my-region/keyRings/my-kr/cryptoKeys/my-key`.
  // This value may not be changed after the Repository has been created.
  string kms_key_name = 8;
}

// The request to list repositories.
message ListRepositoriesRequest {
  // Required. The name of the parent resource whose repositories will be listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/Repository"
    }
  ];

  // The maximum number of repositories to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;
}

// The response from listing repositories.
message ListRepositoriesResponse {
  // The repositories returned.
  repeated Repository repositories = 1;

  // The token to retrieve the next page of repositories, or empty if there are
  // no more repositories to return.
  string next_page_token = 2;
}

// The request to retrieve a repository.
message GetRepositoryRequest {
  // Required. The name of the repository to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Repository"
    }
  ];
}

// The request to create a new repository.
message CreateRepositoryRequest {
  // Required. The name of the parent resource where the repository will be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/Repository"
    }
  ];

  // The repository id to use for this repository.
  string repository_id = 2;

  // The repository to be created.
  Repository repository = 3;
}

// The request to update a repository.
message UpdateRepositoryRequest {
  // The repository that replaces the resource on the server.
  Repository repository = 1;

  // The update mask applies to the resource. For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2;
}

// The request to delete a repository.
message DeleteRepositoryRequest {
  // Required. The name of the repository to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Repository"
    }
  ];
}
