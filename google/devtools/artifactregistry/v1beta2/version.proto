// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1beta2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/devtools/artifactregistry/v1beta2/tag.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1Beta2";
option go_package = "cloud.google.com/go/artifactregistry/apiv1beta2/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "VersionProto";
option java_package = "com.google.devtools.artifactregistry.v1beta2";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1beta2";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1beta2";

// The view, which determines what version information is returned in a
// response.
enum VersionView {
  // The default / unset value.
  // The API will default to the BASIC view.
  VERSION_VIEW_UNSPECIFIED = 0;

  // Includes basic information about the version, but not any related tags.
  BASIC = 1;

  // Include everything.
  FULL = 2;
}

// The body of a version resource. A version resource represents a
// collection of components, such as files and other data. This may correspond
// to a version in many package management schemes.
message Version {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/Version"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/packages/{package}/versions/{version}"
  };

  // The name of the version, for example:
  // "projects/p1/locations/us-central1/repositories/repo1/packages/pkg1/versions/art1".
  // If the package or version ID parts contain slashes, the slashes are
  // escaped.
  string name = 1;

  // Optional. Description of the version, as specified in its metadata.
  string description = 3;

  // The time when the version was created.
  google.protobuf.Timestamp create_time = 5;

  // The time when the version was last updated.
  google.protobuf.Timestamp update_time = 6;

  // Output only. A list of related tags. Will contain up to 100 tags that
  // reference this version.
  repeated Tag related_tags = 7;

  // Output only. Repository-specific Metadata stored against this version.
  // The fields returned are defined by the underlying repository-specific
  // resource. Currently, the only resource in use is
  // [DockerImage][google.devtools.artifactregistry.v1.DockerImage]
  google.protobuf.Struct metadata = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request to list versions.
message ListVersionsRequest {
  // The name of the parent resource whose versions will be listed.
  string parent = 1;

  // The maximum number of versions to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;

  // The view that should be returned in the response.
  VersionView view = 4;

  // Optional. The field to order the results by.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response from listing versions.
message ListVersionsResponse {
  // The versions returned.
  repeated Version versions = 1;

  // The token to retrieve the next page of versions, or empty if there are no
  // more versions to return.
  string next_page_token = 2;
}

// The request to retrieve a version.
message GetVersionRequest {
  // The name of the version to retrieve.
  string name = 1;

  // The view that should be returned in the response.
  VersionView view = 2;
}

// The request to delete a version.
message DeleteVersionRequest {
  // The name of the version to delete.
  string name = 1;

  // By default, a version that is tagged may not be deleted. If force=true, the
  // version and any tags pointing to the version are deleted.
  bool force = 2;
}
