// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "VPCSCConfigProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// The Artifact Registry VPC SC config that apply to a Project.
message VPCSCConfig {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/VpcscConfig"
    pattern: "projects/{project}/locations/{location}/vpcscConfig"
  };

  // VPCSCPolicy is the VPC SC policy for project and location.
  enum VPCSCPolicy {
    // VPCSC_POLICY_UNSPECIFIED - the VPS SC policy is not defined.
    // When VPS SC policy is not defined - the Service will use the default
    // behavior (VPCSC_DENY).
    VPCSC_POLICY_UNSPECIFIED = 0;

    // VPCSC_DENY - repository will block the requests to the Upstreams for the
    // Remote Repositories if the resource is in the perimeter.
    DENY = 1;

    // VPCSC_ALLOW - repository will allow the requests to the Upstreams for the
    // Remote Repositories if the resource is in the perimeter.
    ALLOW = 2;
  }

  // The name of the project's VPC SC Config.
  //
  // Always of the form:
  // projects/{projectID}/locations/{location}/vpcscConfig
  //
  // In update request: never set
  // In response: always set
  string name = 1;

  // The project per location VPC SC policy that defines the VPC SC behavior for
  // the Remote Repository (Allow/Deny).
  VPCSCPolicy vpcsc_policy = 2;
}

// Gets the VPC SC config for a project.
message GetVPCSCConfigRequest {
  // Required. The name of the VPCSCConfig resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/VpcscConfig"
    }
  ];
}

// Sets the VPCSC config of the project.
message UpdateVPCSCConfigRequest {
  // The project config.
  VPCSCConfig vpcsc_config = 1;

  // Field mask to support partial updates.
  google.protobuf.FieldMask update_mask = 2;
}
