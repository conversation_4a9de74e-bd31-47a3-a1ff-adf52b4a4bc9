// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/devtools/artifactregistry/v1/tag.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "VersionProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// The view, which determines what version information is returned in a
// response.
enum VersionView {
  // The default / unset value.
  // The API will default to the BASIC view.
  VERSION_VIEW_UNSPECIFIED = 0;

  // Includes basic information about the version, but not any related tags.
  BASIC = 1;

  // Include everything.
  FULL = 2;
}

// The body of a version resource. A version resource represents a
// collection of components, such as files and other data. This may correspond
// to a version in many package management schemes.
message Version {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/Version"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/packages/{package}/versions/{version}"
  };

  // The name of the version, for example:
  // `projects/p1/locations/us-central1/repositories/repo1/packages/pkg1/versions/art1`.
  // If the package or version ID parts contain slashes, the slashes are
  // escaped.
  string name = 1;

  // Optional. Description of the version, as specified in its metadata.
  string description = 3;

  // The time when the version was created.
  google.protobuf.Timestamp create_time = 5;

  // The time when the version was last updated.
  google.protobuf.Timestamp update_time = 6;

  // Output only. A list of related tags. Will contain up to 100 tags that
  // reference this version.
  repeated Tag related_tags = 7;

  // Output only. Repository-specific Metadata stored against this version.
  // The fields returned are defined by the underlying repository-specific
  // resource. Currently, the resources could be:
  // [DockerImage][google.devtools.artifactregistry.v1.DockerImage]
  // [MavenArtifact][google.devtools.artifactregistry.v1.MavenArtifact]
  google.protobuf.Struct metadata = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Client specified annotations.
  map<string, string> annotations = 9 [(google.api.field_behavior) = OPTIONAL];
}

// The request to list versions.
message ListVersionsRequest {
  // The name of the parent resource whose versions will be listed.
  string parent = 1;

  // The maximum number of versions to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;

  // The view that should be returned in the response.
  VersionView view = 4;

  // Optional. The field to order the results by.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression for filtering the results of the request. Filter
  // rules are case insensitive. The fields eligible for filtering are:
  //
  //   * `name`
  //   * `annotations`
  //
  //  Examples of using a filter:
  //
  //  To filter the results of your request to versions with the name
  //  `my-version` in project `my-project` in the `us-central` region, in
  //  repository `my-repo`, append the following filter expression to your
  //  request:
  //
  //   * `name="projects/my-project/locations/us-central1/repositories/my-repo/packages/my-package/versions/my-version"`
  //
  //  You can also use wildcards to match any number of characters before or
  //  after the value:
  //
  //   * `name="projects/my-project/locations/us-central1/repositories/my-repo/packages/my-package/versions/*version"`
  //   * `name="projects/my-project/locations/us-central1/repositories/my-repo/packages/my-package/versions/my*"`
  //   * `name="projects/my-project/locations/us-central1/repositories/my-repo/packages/my-package/versions/*version*"`
  //
  //  To filter the results of your request to versions with the annotation
  //  key-value pair [`external_link`: `external_link_value`], append the
  //  following filter expression to your request:
  //
  //   * `"annotations.external_link:external_link_value"`
  //
  //  To filter just for a specific annotation key `external_link`, append the
  //  following filter expression to your request:
  //
  //   * `"annotations.external_link"`
  //
  //  If the annotation key or value contains special characters, you can escape
  //  them by surrounding the value with backticks. For example, to filter the
  //  results of your request to versions with the annotation key-value pair
  //  [`external.link`:`https://example.com/my-version`], append the following
  //  filter expression to your request:
  //
  //   * `` "annotations.`external.link`:`https://example.com/my-version`" ``
  //
  //  You can also filter with annotations with a wildcard to
  //  match any number of characters before or after the value:
  //
  //   * `` "annotations.*_link:`*example.com*`" ``
  string filter = 6 [(google.api.field_behavior) = OPTIONAL];
}

// The response from listing versions.
message ListVersionsResponse {
  // The versions returned.
  repeated Version versions = 1;

  // The token to retrieve the next page of versions, or empty if there are no
  // more versions to return.
  string next_page_token = 2;
}

// The request to retrieve a version.
message GetVersionRequest {
  // The name of the version to retrieve.
  string name = 1;

  // The view that should be returned in the response.
  VersionView view = 2;
}

// The request to delete a version.
message DeleteVersionRequest {
  // The name of the version to delete.
  string name = 1;

  // By default, a version that is tagged may not be deleted. If force=true, the
  // version and any tags pointing to the version are deleted.
  bool force = 2;
}

// The request to delete multiple versions across a repository.
message BatchDeleteVersionsRequest {
  // The name of the repository holding all requested versions.
  string parent = 1 [(google.api.resource_reference) = {
    child_type: "artifactregistry.googleapis.com/Version"
  }];

  // Required. The names of the versions to delete.
  // A maximum of 10000 versions can be deleted in a batch.
  repeated string names = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Version"
    }
  ];

  // If true, the request is performed without deleting data, following AIP-163.
  bool validate_only = 3;
}

// The metadata of an LRO from deleting multiple versions.
message BatchDeleteVersionsMetadata {
  // The versions the operation failed to delete.
  repeated string failed_versions = 2;
}

// The request to update a version.
message UpdateVersionRequest {
  // Required. The Version that replaces the resource on the server.
  Version version = 1 [(google.api.field_behavior) = REQUIRED];

  // The update mask applies to the resource. For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2;
}
