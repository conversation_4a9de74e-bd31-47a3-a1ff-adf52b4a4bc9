// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "PackageProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// Packages are named collections of versions.
message Package {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/Package"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/packages/{package}"
  };

  // The name of the package, for example:
  // `projects/p1/locations/us-central1/repositories/repo1/packages/pkg1`.
  // If the package ID part contains slashes, the slashes are escaped.
  string name = 1;

  // The display name of the package.
  string display_name = 2;

  // The time when the package was created.
  google.protobuf.Timestamp create_time = 5;

  // The time when the package was last updated. This includes publishing a new
  // version of the package.
  google.protobuf.Timestamp update_time = 6;

  // Optional. Client specified annotations.
  map<string, string> annotations = 7 [(google.api.field_behavior) = OPTIONAL];
}

// The request to list packages.
message ListPackagesRequest {
  // Required. The name of the parent resource whose packages will be listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/Package"
    }
  ];

  // The maximum number of packages to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;

  // Optional. An expression for filtering the results of the request. Filter
  // rules are case insensitive. The fields eligible for filtering are:
  //
  //   * `name`
  //   * `annotations`
  //
  // Examples of using a filter:
  //
  //  To filter the results of your request to packages with the name
  //  `my-package` in project `my-project` in the `us-central` region, in
  //  repository `my-repo`, append the following filter expression to your
  //  request:
  //
  //   * `name="projects/my-project/locations/us-central1/repositories/my-repo/packages/my-package"`
  //
  //  You can also use wildcards to match any number of characters before or
  //  after the value:
  //
  //   * `name="projects/my-project/locations/us-central1/repositories/my-repo/packages/my-*"`
  //   * `name="projects/my-project/locations/us-central1/repositories/my-repo/packages/*package"`
  //   * `name="projects/my-project/locations/us-central1/repositories/my-repo/packages/*pack*"`
  //
  //  To filter the results of your request to packages with the annotation
  //  key-value pair [`external_link`: `external_link_value`], append the
  //  following filter expression to your request":
  //
  //   * `"annotations.external_link:external_link_value"`
  //
  //  To filter the results just for a specific annotation key `external_link`,
  //  append the following filter expression to your request:
  //
  //   * `"annotations.external_link"`
  //
  //  If the annotation key or value contains special characters, you can escape
  //  them by surrounding the value with backticks. For example, to filter the
  //  results of your request to packages with the annotation key-value pair
  //  [`external.link`:`https://example.com/my-package`], append the following
  //  filter expression to your request:
  //
  //   * `` "annotations.`external.link`:`https://example.com/my-package`" ``
  //
  //  You can also filter with annotations with a wildcard to
  //  match any number of characters before or after the value:
  //
  //   * `` "annotations.*_link:`*example.com*`" ``
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The field to order the results by.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response from listing packages.
message ListPackagesResponse {
  // The packages returned.
  repeated Package packages = 1;

  // The token to retrieve the next page of packages, or empty if there are no
  // more packages to return.
  string next_page_token = 2;
}

// The request to retrieve a package.
message GetPackageRequest {
  // Required. The name of the package to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Package"
    }
  ];
}

// The request to delete a package.
message DeletePackageRequest {
  // Required. The name of the package to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Package"
    }
  ];
}

// The request to update a package.
message UpdatePackageRequest {
  // The package that replaces the resource on the server.
  Package package = 1;

  // The update mask applies to the resource. For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2;
}
