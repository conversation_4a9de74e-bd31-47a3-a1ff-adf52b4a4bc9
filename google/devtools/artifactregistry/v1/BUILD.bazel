# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "artifactregistry_proto",
    srcs = [
        "apt_artifact.proto",
        "artifact.proto",
        "attachment.proto",
        "file.proto",
        "generic.proto",
        "package.proto",
        "repository.proto",
        "rule.proto",
        "service.proto",
        "settings.proto",
        "tag.proto",
        "version.proto",
        "vpcsc_config.proto",
        "yum_artifact.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "artifactregistry_proto_with_info",
    deps = [
        ":artifactregistry_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "artifactregistry_java_proto",
    deps = [":artifactregistry_proto"],
)

java_grpc_library(
    name = "artifactregistry_java_grpc",
    srcs = [":artifactregistry_proto"],
    deps = [":artifactregistry_java_proto"],
)

java_gapic_library(
    name = "artifactregistry_java_gapic",
    srcs = [":artifactregistry_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "artifactregistry_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "artifactregistry_v1.yaml",
    test_deps = [
        ":artifactregistry_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":artifactregistry_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "artifactregistry_java_gapic_test_suite",
    test_classes = [
        "com.google.devtools.artifactregistry.v1.ArtifactRegistryClientHttpJsonTest",
        "com.google.devtools.artifactregistry.v1.ArtifactRegistryClientTest",
    ],
    runtime_deps = [":artifactregistry_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-devtools-artifactregistry-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":artifactregistry_java_gapic",
        ":artifactregistry_java_grpc",
        ":artifactregistry_java_proto",
        ":artifactregistry_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "artifactregistry_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb",
    protos = [":artifactregistry_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "artifactregistry_go_gapic",
    srcs = [":artifactregistry_proto_with_info"],
    grpc_service_config = "artifactregistry_grpc_service_config.json",
    importpath = "cloud.google.com/go/artifactregistry/apiv1;artifactregistry",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "artifactregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":artifactregistry_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-devtools-artifactregistry-v1-go",
    deps = [
        ":artifactregistry_go_gapic",
        ":artifactregistry_go_gapic_srcjar-metadata.srcjar",
        ":artifactregistry_go_gapic_srcjar-snippets.srcjar",
        ":artifactregistry_go_gapic_srcjar-test.srcjar",
        ":artifactregistry_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "artifactregistry_py_gapic",
    srcs = [":artifactregistry_proto"],
    grpc_service_config = "artifactregistry_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=artifactregistry",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-artifact-registry",
    ],
    rest_numeric_enums = True,
    service_yaml = "artifactregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "artifactregistry_py_gapic_test",
    srcs = [
        "artifactregistry_py_gapic_pytest.py",
        "artifactregistry_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":artifactregistry_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "devtools-artifactregistry-v1-py",
    deps = [
        ":artifactregistry_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "artifactregistry_php_proto",
    deps = [":artifactregistry_proto"],
)

php_gapic_library(
    name = "artifactregistry_php_gapic",
    srcs = [":artifactregistry_proto_with_info"],
    grpc_service_config = "artifactregistry_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "artifactregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":artifactregistry_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-devtools-artifactregistry-v1-php",
    deps = [
        ":artifactregistry_php_gapic",
        ":artifactregistry_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "artifactregistry_nodejs_gapic",
    package_name = "@google-cloud/artifact-registry",
    src = ":artifactregistry_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "artifactregistry_grpc_service_config.json",
    package = "google.devtools.artifactregistry.v1",
    rest_numeric_enums = True,
    service_yaml = "artifactregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "devtools-artifactregistry-v1-nodejs",
    deps = [
        ":artifactregistry_nodejs_gapic",
        ":artifactregistry_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "artifactregistry_ruby_proto",
    deps = [":artifactregistry_proto"],
)

ruby_grpc_library(
    name = "artifactregistry_ruby_grpc",
    srcs = [":artifactregistry_proto"],
    deps = [":artifactregistry_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "artifactregistry_ruby_gapic",
    srcs = [":artifactregistry_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=artifactregistry.googleapis.com",
        "ruby-cloud-api-shortname=artifactregistry",
        "ruby-cloud-env-prefix=ARTIFACT_REGISTRY",
        "ruby-cloud-gem-name=google-cloud-artifact_registry-v1",
        "ruby-cloud-product-url=https://cloud.google.com/artifact-registry/",
    ],
    grpc_service_config = "artifactregistry_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Artifact Registry stores and manages build artifacts in a scalable and integrated service built on Google infrastructure.",
    ruby_cloud_title = "Artifact Registry V1",
    service_yaml = "artifactregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":artifactregistry_ruby_grpc",
        ":artifactregistry_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-devtools-artifactregistry-v1-ruby",
    deps = [
        ":artifactregistry_ruby_gapic",
        ":artifactregistry_ruby_grpc",
        ":artifactregistry_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "artifactregistry_csharp_proto",
    extra_opts = [],
    deps = [":artifactregistry_proto"],
)

csharp_grpc_library(
    name = "artifactregistry_csharp_grpc",
    srcs = [":artifactregistry_proto"],
    deps = [":artifactregistry_csharp_proto"],
)

csharp_gapic_library(
    name = "artifactregistry_csharp_gapic",
    srcs = [":artifactregistry_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "artifactregistry_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "artifactregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":artifactregistry_csharp_grpc",
        ":artifactregistry_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-devtools-artifactregistry-v1-csharp",
    deps = [
        ":artifactregistry_csharp_gapic",
        ":artifactregistry_csharp_grpc",
        ":artifactregistry_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "artifactregistry_cc_proto",
    deps = [":artifactregistry_proto"],
)

cc_grpc_library(
    name = "artifactregistry_cc_grpc",
    srcs = [":artifactregistry_proto"],
    grpc_only = True,
    deps = [":artifactregistry_cc_proto"],
)
