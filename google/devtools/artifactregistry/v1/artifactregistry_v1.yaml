type: google.api.Service
config_version: 3
name: artifactregistry.googleapis.com
title: Artifact Registry API

apis:
- name: google.cloud.location.Locations
- name: google.devtools.artifactregistry.v1.ArtifactRegistry
- name: google.longrunning.Operations

types:
- name: google.devtools.artifactregistry.v1.BatchDeleteVersionsMetadata
- name: google.devtools.artifactregistry.v1.GenericArtifact
- name: google.devtools.artifactregistry.v1.ImportAptArtifactsMetadata
- name: google.devtools.artifactregistry.v1.ImportAptArtifactsResponse
- name: google.devtools.artifactregistry.v1.ImportYumArtifactsMetadata
- name: google.devtools.artifactregistry.v1.ImportYumArtifactsResponse
- name: google.devtools.artifactregistry.v1.OperationMetadata

documentation:
  summary: |-
    Store and manage build artifacts in a scalable and integrated service built
    on Google infrastructure.
  overview: |-
    With Artifact Registry you can store and manage your build artifacts (e.g.
    Docker images, Maven packages, npm packages), in a scalable and integrated
    repository service built on Google infrastructure. You can manage
    repository access
    with IAM and interact with repositories via gcloud, Cloud Console, and
    native package format tools. The service can also be integrated with Cloud
    Build and other CI/CD systems. Artifact Registry abstracts away
    infrastructure management, so you can focus on what matters most —
    delivering value to the users of your services and applications. Note:
    Enabling the Artifact Registry API will not affect your use of Container
    Registry in the same project.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.devtools.artifactregistry.v1.ArtifactRegistry.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.BatchDeleteVersions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.CreateAttachment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.CreateRepository
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.CreateRule
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.CreateTag
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.DeleteAttachment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.DeleteFile
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.DeletePackage
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.DeleteRepository
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.DeleteRule
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.DeleteTag
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.DeleteVersion
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.ImportAptArtifacts
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.ImportYumArtifacts
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.SetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.UpdateFile
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.UpdatePackage
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.UpdateProjectSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.UpdateRepository
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.UpdateRule
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.UpdateTag
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.UpdateVPCSCConfig
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.artifactregistry.v1.ArtifactRegistry.UpdateVersion
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
