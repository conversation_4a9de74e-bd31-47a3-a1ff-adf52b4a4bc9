// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "AptArtifactProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// A detailed representation of an Apt artifact. Information in the record
// is derived from the archive's control file.
// See https://www.debian.org/doc/debian-policy/ch-controlfields.html
message AptArtifact {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/AptArtifact"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/aptArtifacts/{apt_artifact}"
  };

  // Package type is either binary or source.
  enum PackageType {
    // Package type is not specified.
    PACKAGE_TYPE_UNSPECIFIED = 0;

    // Binary package.
    BINARY = 1;

    // Source package.
    SOURCE = 2;
  }

  // Output only. The Artifact Registry resource name of the artifact.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Apt package name of the artifact.
  string package_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An artifact is a binary or source package.
  PackageType package_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Operating system architecture of the artifact.
  string architecture = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Repository component of the artifact.
  string component = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Contents of the artifact's control metadata file.
  bytes control_file = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Google Cloud Storage location where the artifacts currently reside.
message ImportAptArtifactsGcsSource {
  // Cloud Storage paths URI (e.g., gs://my_bucket//my_object).
  repeated string uris = 1;

  // Supports URI wildcards for matching multiple objects from a single URI.
  bool use_wildcards = 2;
}

// The request to import new apt artifacts.
message ImportAptArtifactsRequest {
  // The source location of the package binaries.
  oneof source {
    // Google Cloud Storage location where input content is located.
    ImportAptArtifactsGcsSource gcs_source = 2;
  }

  // The name of the parent resource where the artifacts will be imported.
  string parent = 1;
}

// Error information explaining why a package was not imported.
message ImportAptArtifactsErrorInfo {
  // The source that was not imported.
  oneof source {
    // Google Cloud Storage location requested.
    ImportAptArtifactsGcsSource gcs_source = 1;
  }

  // The detailed error status.
  google.rpc.Status error = 2;
}

// The response message from importing APT artifacts.
message ImportAptArtifactsResponse {
  // The Apt artifacts imported.
  repeated AptArtifact apt_artifacts = 1;

  // Detailed error info for packages that were not imported.
  repeated ImportAptArtifactsErrorInfo errors = 2;
}

// The operation metadata for importing artifacts.
message ImportAptArtifactsMetadata {}
