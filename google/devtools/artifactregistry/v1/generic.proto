// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "GenericProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// GenericArtifact represents a generic artifact
message GenericArtifact {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/GenericArtifact"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/genericArtifacts/{generic_artifact}"
  };

  // Resource name of the generic artifact.
  // project, location, repository, package_id and version_id
  // create a unique generic artifact.
  // i.e. "projects/test-project/locations/us-west4/repositories/test-repo/
  // genericArtifacts/package_id:version_id"
  string name = 1;

  // The version of the generic artifact.
  string version = 2;

  // Output only. The time when the Generic module is created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the Generic module is updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
