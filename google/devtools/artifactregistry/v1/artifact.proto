// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "ArtifactProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// DockerImage represents a docker artifact.
// The following fields are returned as untyped metadata in the Version
// resource, using camelcase keys (i.e. metadata.imageSizeBytes):
// * imageSizeBytes
// * mediaType
// * buildTime
message DockerImage {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/DockerImage"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/dockerImages/{docker_image}"
  };

  // Required. registry_location, project_id, repository_name and image id forms
  // a unique image
  // name:`projects/<project_id>/locations/<location>/repository/<repository_name>/dockerImages/<docker_image>`.
  // For example,
  // "projects/test-project/locations/us-west4/repositories/test-repo/dockerImages/
  // nginx@sha256:e9954c1fc875017be1c3e36eca16be2d9e9bccc4bf072163515467d6a823c7cf",
  // where "us-west4" is the registry_location, "test-project" is the
  // project_id, "test-repo" is the repository_name and
  // "nginx@sha256:e9954c1fc875017be1c3e36eca16be2d9e9bccc4bf072163515467d6a823c7cf"
  // is the image's digest.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. URL to access the image.
  // Example:
  // us-west4-docker.pkg.dev/test-project/test-repo/nginx@sha256:e9954c1fc875017be1c3e36eca16be2d9e9bccc4bf072163515467d6a823c7cf
  string uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Tags attached to this image.
  repeated string tags = 3;

  // Calculated size of the image.
  // This field is returned as the 'metadata.imageSizeBytes' field in the
  // Version resource.
  int64 image_size_bytes = 4;

  // Time the image was uploaded.
  google.protobuf.Timestamp upload_time = 5;

  // Media type of this image, e.g.
  // "application/vnd.docker.distribution.manifest.v2+json".
  // This field is returned as the 'metadata.mediaType' field in the
  // Version resource.
  string media_type = 6;

  // The time this image was built.
  // This field is returned as the 'metadata.buildTime' field in the
  // Version resource.
  // The build time is returned to the client as an RFC 3339 string, which can
  // be easily used with the JavaScript Date constructor.
  google.protobuf.Timestamp build_time = 7;

  // Output only. The time when the docker image was last updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request to list docker images.
message ListDockerImagesRequest {
  // Required. The name of the parent resource whose docker images will be
  // listed.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of artifacts to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;

  // The field to order the results by.
  string order_by = 4;
}

// The response from listing docker images.
message ListDockerImagesResponse {
  // The docker images returned.
  repeated DockerImage docker_images = 1;

  // The token to retrieve the next page of artifacts, or empty if there are no
  // more artifacts to return.
  string next_page_token = 2;
}

// The request to get docker images.
message GetDockerImageRequest {
  // Required. The name of the docker images.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/DockerImage"
    }
  ];
}

// MavenArtifact represents a maven artifact.
message MavenArtifact {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/MavenArtifact"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/mavenArtifacts/{maven_artifact}"
  };

  // Required. registry_location, project_id, repository_name and maven_artifact
  // forms a unique artifact For example,
  // "projects/test-project/locations/us-west4/repositories/test-repo/mavenArtifacts/
  // com.google.guava:guava:31.0-jre",
  // where "us-west4" is the registry_location, "test-project" is the
  // project_id, "test-repo" is the repository_name and
  // "com.google.guava:guava:31.0-jre"
  // is the maven artifact.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. URL to access the pom file of the artifact.
  // Example:
  // us-west4-maven.pkg.dev/test-project/test-repo/com/google/guava/guava/31.0/guava-31.0.pom
  string pom_uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Group ID for the artifact.
  // Example:
  // com.google.guava
  string group_id = 3;

  // Artifact ID for the artifact.
  string artifact_id = 4;

  // Version of this artifact.
  string version = 5;

  // Output only. Time the artifact was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time the artifact was updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request to list maven artifacts.
message ListMavenArtifactsRequest {
  // Required. The name of the parent resource whose maven artifacts will be
  // listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/MavenArtifact"
    }
  ];

  // The maximum number of artifacts to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;
}

// The response from listing maven artifacts.
message ListMavenArtifactsResponse {
  // The maven artifacts returned.
  repeated MavenArtifact maven_artifacts = 1;

  // The token to retrieve the next page of artifacts, or empty if there are no
  // more artifacts to return.
  string next_page_token = 2;
}

// The request to get maven artifacts.
message GetMavenArtifactRequest {
  // Required. The name of the maven artifact.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/MavenArtifact"
    }
  ];
}

// NpmPackage represents an npm artifact.
message NpmPackage {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/NpmPackage"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/npmPackages/{npm_package}"
  };

  // Required. registry_location, project_id, repository_name and npm_package
  // forms a unique package For example,
  // "projects/test-project/locations/us-west4/repositories/test-repo/npmPackages/
  // npm_test:1.0.0",
  // where "us-west4" is the registry_location, "test-project" is the
  // project_id, "test-repo" is the repository_name and
  // npm_test:1.0.0" is the npm package.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Package for the artifact.
  string package_name = 3;

  // Version of this package.
  string version = 4;

  // Tags attached to this package.
  repeated string tags = 5;

  // Output only. Time the package was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time the package was updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request to list npm packages.
message ListNpmPackagesRequest {
  // Required. The name of the parent resource whose npm packages will be
  // listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/NpmPackage"
    }
  ];

  // The maximum number of artifacts to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;
}

// The response from listing npm packages.
message ListNpmPackagesResponse {
  // The npm packages returned.
  repeated NpmPackage npm_packages = 1;

  // The token to retrieve the next page of artifacts, or empty if there are no
  // more artifacts to return.
  string next_page_token = 2;
}

// The request to get npm packages.
message GetNpmPackageRequest {
  // Required. The name of the npm package.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/NpmPackage"
    }
  ];
}

// PythonPackage represents a python artifact.
message PythonPackage {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/PythonPackage"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/pythonPackages/{python_package}"
  };

  // Required. registry_location, project_id, repository_name and python_package
  // forms a unique package
  // name:`projects/<project_id>/locations/<location>/repository/<repository_name>/pythonPackages/<python_package>`.
  // For example,
  // "projects/test-project/locations/us-west4/repositories/test-repo/pythonPackages/
  // python_package:1.0.0",
  // where "us-west4" is the registry_location, "test-project" is the
  // project_id, "test-repo" is the repository_name and
  // python_package:1.0.0" is the python package.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. URL to access the package.
  // Example:
  // us-west4-python.pkg.dev/test-project/test-repo/python_package/file-name-1.0.0.tar.gz
  string uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Package for the artifact.
  string package_name = 3;

  // Version of this package.
  string version = 4;

  // Output only. Time the package was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time the package was updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request to list python packages.
message ListPythonPackagesRequest {
  // Required. The name of the parent resource whose python packages will be
  // listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/PythonPackage"
    }
  ];

  // The maximum number of artifacts to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;
}

// The response from listing python packages.
message ListPythonPackagesResponse {
  // The python packages returned.
  repeated PythonPackage python_packages = 1;

  // The token to retrieve the next page of artifacts, or empty if there are no
  // more artifacts to return.
  string next_page_token = 2;
}

// The request to get python packages.
message GetPythonPackageRequest {
  // Required. The name of the python package.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/PythonPackage"
    }
  ];
}
