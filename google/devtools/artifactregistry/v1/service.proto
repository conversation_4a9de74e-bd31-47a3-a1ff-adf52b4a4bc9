// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/devtools/artifactregistry/v1/apt_artifact.proto";
import "google/devtools/artifactregistry/v1/artifact.proto";
import "google/devtools/artifactregistry/v1/attachment.proto";
import "google/devtools/artifactregistry/v1/file.proto";
import "google/devtools/artifactregistry/v1/package.proto";
import "google/devtools/artifactregistry/v1/repository.proto";
import "google/devtools/artifactregistry/v1/rule.proto";
import "google/devtools/artifactregistry/v1/settings.proto";
import "google/devtools/artifactregistry/v1/tag.proto";
import "google/devtools/artifactregistry/v1/version.proto";
import "google/devtools/artifactregistry/v1/vpcsc_config.proto";
import "google/devtools/artifactregistry/v1/yum_artifact.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// The Artifact Registry API service.
//
// Artifact Registry is an artifact management system for storing artifacts
// from different package management systems.
//
// The resources managed by this API are:
//
// * Repositories, which group packages and their data.
// * Packages, which group versions and their tags.
// * Versions, which are specific forms of a package.
// * Tags, which represent alternative names for versions.
// * Files, which contain content and are optionally associated with a Package
//   or Version.
service ArtifactRegistry {
  option (google.api.default_host) = "artifactregistry.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists docker images.
  rpc ListDockerImages(ListDockerImagesRequest)
      returns (ListDockerImagesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/dockerImages"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a docker image.
  rpc GetDockerImage(GetDockerImageRequest) returns (DockerImage) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/dockerImages/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists maven artifacts.
  rpc ListMavenArtifacts(ListMavenArtifactsRequest)
      returns (ListMavenArtifactsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/mavenArtifacts"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a maven artifact.
  rpc GetMavenArtifact(GetMavenArtifactRequest) returns (MavenArtifact) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/mavenArtifacts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists npm packages.
  rpc ListNpmPackages(ListNpmPackagesRequest)
      returns (ListNpmPackagesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/npmPackages"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a npm package.
  rpc GetNpmPackage(GetNpmPackageRequest) returns (NpmPackage) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/npmPackages/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists python packages.
  rpc ListPythonPackages(ListPythonPackagesRequest)
      returns (ListPythonPackagesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/pythonPackages"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a python package.
  rpc GetPythonPackage(GetPythonPackageRequest) returns (PythonPackage) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/pythonPackages/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Imports Apt artifacts. The returned Operation will complete once the
  // resources are imported. Package, Version, and File resources are created
  // based on the imported artifacts. Imported artifacts that conflict with
  // existing resources are ignored.
  rpc ImportAptArtifacts(ImportAptArtifactsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/repositories/*}/aptArtifacts:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.devtools.artifactregistry.v1.ImportAptArtifactsResponse"
      metadata_type: "google.devtools.artifactregistry.v1.ImportAptArtifactsMetadata"
    };
  }

  // Imports Yum (RPM) artifacts. The returned Operation will complete once the
  // resources are imported. Package, Version, and File resources are created
  // based on the imported artifacts. Imported artifacts that conflict with
  // existing resources are ignored.
  rpc ImportYumArtifacts(ImportYumArtifactsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/repositories/*}/yumArtifacts:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.devtools.artifactregistry.v1.ImportYumArtifactsResponse"
      metadata_type: "google.devtools.artifactregistry.v1.ImportYumArtifactsMetadata"
    };
  }

  // Lists repositories.
  rpc ListRepositories(ListRepositoriesRequest)
      returns (ListRepositoriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/repositories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a repository.
  rpc GetRepository(GetRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a repository. The returned Operation will finish once the
  // repository has been created. Its response will be the created Repository.
  rpc CreateRepository(CreateRepositoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/repositories"
      body: "repository"
    };
    option (google.api.method_signature) = "parent,repository,repository_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.devtools.artifactregistry.v1.Repository"
      metadata_type: "google.devtools.artifactregistry.v1.OperationMetadata"
    };
  }

  // Updates a repository.
  rpc UpdateRepository(UpdateRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      patch: "/v1/{repository.name=projects/*/locations/*/repositories/*}"
      body: "repository"
    };
    option (google.api.method_signature) = "repository,update_mask";
  }

  // Deletes a repository and all of its contents. The returned Operation will
  // finish once the repository has been deleted. It will not have any Operation
  // metadata and will return a google.protobuf.Empty response.
  rpc DeleteRepository(DeleteRepositoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1.OperationMetadata"
    };
  }

  // Lists packages.
  rpc ListPackages(ListPackagesRequest) returns (ListPackagesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/packages"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a package.
  rpc GetPackage(GetPackageRequest) returns (Package) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/packages/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a package and all of its versions and tags. The returned operation
  // will complete once the package has been deleted.
  rpc DeletePackage(DeletePackageRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*/packages/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1.OperationMetadata"
    };
  }

  // Lists versions.
  rpc ListVersions(ListVersionsRequest) returns (ListVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*/packages/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a version
  rpc GetVersion(GetVersionRequest) returns (Version) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/packages/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a version and all of its content. The returned operation will
  // complete once the version has been deleted.
  rpc DeleteVersion(DeleteVersionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*/packages/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1.OperationMetadata"
    };
  }

  // Deletes multiple versions across a repository. The returned operation will
  // complete once the versions have been deleted.
  rpc BatchDeleteVersions(BatchDeleteVersionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/repositories/*/packages/*}/versions:batchDelete"
      body: "*"
    };
    option (google.api.method_signature) = "parent,names";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1.BatchDeleteVersionsMetadata"
    };
  }

  // Updates a version.
  rpc UpdateVersion(UpdateVersionRequest) returns (Version) {
    option (google.api.http) = {
      patch: "/v1/{version.name=projects/*/locations/*/repositories/*/packages/*/versions/*}"
      body: "version"
    };
    option (google.api.method_signature) = "version,update_mask";
  }

  // Lists files.
  rpc ListFiles(ListFilesRequest) returns (ListFilesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/files"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a file.
  rpc GetFile(GetFileRequest) returns (File) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/files/**}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a file and all of its content. It is only allowed on generic
  // repositories. The returned operation will complete once the file has been
  // deleted.
  rpc DeleteFile(DeleteFileRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*/files/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1.OperationMetadata"
    };
  }

  // Updates a file.
  rpc UpdateFile(UpdateFileRequest) returns (File) {
    option (google.api.http) = {
      patch: "/v1/{file.name=projects/*/locations/*/repositories/*/files/*}"
      body: "file"
    };
    option (google.api.method_signature) = "file,update_mask";
  }

  // Lists tags.
  rpc ListTags(ListTagsRequest) returns (ListTagsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*/packages/*}/tags"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a tag.
  rpc GetTag(GetTagRequest) returns (Tag) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/packages/*/tags/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a tag.
  rpc CreateTag(CreateTagRequest) returns (Tag) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/repositories/*/packages/*}/tags"
      body: "tag"
    };
    option (google.api.method_signature) = "parent,tag,tag_id";
  }

  // Updates a tag.
  rpc UpdateTag(UpdateTagRequest) returns (Tag) {
    option (google.api.http) = {
      patch: "/v1/{tag.name=projects/*/locations/*/repositories/*/packages/*/tags/*}"
      body: "tag"
    };
    option (google.api.method_signature) = "tag,update_mask";
  }

  // Deletes a tag.
  rpc DeleteTag(DeleteTagRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*/packages/*/tags/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a rule.
  rpc CreateRule(CreateRuleRequest) returns (Rule) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/repositories/*}/rules"
      body: "rule"
    };
    option (google.api.method_signature) = "parent,rule,rule_id";
  }

  // Lists rules.
  rpc ListRules(ListRulesRequest) returns (ListRulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/rules"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a rule.
  rpc GetRule(GetRuleRequest) returns (Rule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/rules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a rule.
  rpc UpdateRule(UpdateRuleRequest) returns (Rule) {
    option (google.api.http) = {
      patch: "/v1/{rule.name=projects/*/locations/*/repositories/*/rules/*}"
      body: "rule"
    };
    option (google.api.method_signature) = "rule,update_mask";
  }

  // Deletes a rule.
  rpc DeleteRule(DeleteRuleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*/rules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the IAM policy for a given resource.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/repositories/*}:setIamPolicy"
      body: "*"
    };
  }

  // Gets the IAM policy for a given resource.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      get: "/v1/{resource=projects/*/locations/*/repositories/*}:getIamPolicy"
    };
  }

  // Tests if the caller has a list of permissions on a resource.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/repositories/*}:testIamPermissions"
      body: "*"
    };
  }

  // Retrieves the Settings for the Project.
  rpc GetProjectSettings(GetProjectSettingsRequest) returns (ProjectSettings) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/projectSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the Settings for the Project.
  rpc UpdateProjectSettings(UpdateProjectSettingsRequest)
      returns (ProjectSettings) {
    option (google.api.http) = {
      patch: "/v1/{project_settings.name=projects/*/projectSettings}"
      body: "project_settings"
    };
    option (google.api.method_signature) = "project_settings,update_mask";
  }

  // Retrieves the VPCSC Config for the Project.
  rpc GetVPCSCConfig(GetVPCSCConfigRequest) returns (VPCSCConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/vpcscConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the VPCSC Config for the Project.
  rpc UpdateVPCSCConfig(UpdateVPCSCConfigRequest) returns (VPCSCConfig) {
    option (google.api.http) = {
      patch: "/v1/{vpcsc_config.name=projects/*/locations/*/vpcscConfig}"
      body: "vpcsc_config"
    };
    option (google.api.method_signature) = "vpcsc_config,update_mask";
  }

  // Updates a package.
  rpc UpdatePackage(UpdatePackageRequest) returns (Package) {
    option (google.api.http) = {
      patch: "/v1/{package.name=projects/*/locations/*/repositories/*/packages/*}"
      body: "package"
    };
    option (google.api.method_signature) = "package,update_mask";
  }

  // Lists attachments.
  rpc ListAttachments(ListAttachmentsRequest)
      returns (ListAttachmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/attachments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets an attachment.
  rpc GetAttachment(GetAttachmentRequest) returns (Attachment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/attachments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an attachment. The returned Operation will finish once the
  // attachment has been created. Its response will be the created attachment.
  rpc CreateAttachment(CreateAttachmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/repositories/*}/attachments"
      body: "attachment"
    };
    option (google.api.method_signature) = "parent,attachment,attachment_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.devtools.artifactregistry.v1.Attachment"
      metadata_type: "google.devtools.artifactregistry.v1.OperationMetadata"
    };
  }

  // Deletes an attachment. The returned Operation will
  // finish once the attachments has been deleted. It will not have any
  // Operation metadata and will return a `google.protobuf.Empty` response.
  rpc DeleteAttachment(DeleteAttachmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*/attachments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.artifactregistry.v1.OperationMetadata"
    };
  }
}

// Metadata type for longrunning-operations, currently empty.
message OperationMetadata {}
