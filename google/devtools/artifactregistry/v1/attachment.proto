// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "AttachmentProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// An Attachment refers to additional metadata that can be attached to
// artifacts in Artifact Registry. An attachment consists of one or more files.
message Attachment {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/Attachment"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/attachments/{attachment}"
  };

  // The name of the attachment. E.g.
  // `projects/p1/locations/us/repositories/repo/attachments/sbom`.
  string name = 1;

  // Required. The target the attachment is for, can be a Version, Package or
  // Repository. E.g.
  // `projects/p1/locations/us-central1/repositories/repo1/packages/p1/versions/v1`.
  string target = 2 [(google.api.field_behavior) = REQUIRED];

  // Type of attachment.
  // E.g. `application/vnd.spdx+json`
  string type = 3;

  // The namespace this attachment belongs to.
  // E.g. If an attachment is created by artifact analysis, namespace is set
  // to `artifactanalysis.googleapis.com`.
  string attachment_namespace = 4;

  // Optional. User annotations. These attributes can only be set and used by
  // the user, and not by Artifact Registry. See
  // https://google.aip.dev/128#annotations for more details such as format and
  // size limitations.
  map<string, string> annotations = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time when the attachment was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the attachment was last updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The files that belong to this attachment.
  // If the file ID part contains slashes, they are escaped. E.g.
  // `projects/p1/locations/us-central1/repositories/repo1/files/sha:<sha-of-file>`.
  repeated string files = 8 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/File"
    }
  ];

  // Output only. The name of the OCI version that this attachment created. Only
  // populated for Docker attachments. E.g.
  // `projects/p1/locations/us-central1/repositories/repo1/packages/p1/versions/v1`.
  string oci_version_name = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request to list attachments.
message ListAttachmentsRequest {
  // Required. The name of the parent resource whose attachments will be listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/Attachment"
    }
  ];

  // Optional. An expression for filtering the results of the request. Filter
  // rules are case insensitive. The fields eligible for filtering are:
  //
  //   * `target`
  //   * `type`
  //   * `attachment_namespace`
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // The maximum number of attachments to return. Maximum page size is 1,000.
  int32 page_size = 3;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 4;
}

// The response from listing attachments.
message ListAttachmentsResponse {
  // The attachments returned.
  repeated Attachment attachments = 1;

  // The token to retrieve the next page of attachments, or empty if there are
  // no more attachments to return.
  string next_page_token = 2;
}

// The request to retrieve an attachment.
message GetAttachmentRequest {
  // Required. The name of the attachment to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Attachment"
    }
  ];
}

// The request to create a new attachment.
message CreateAttachmentRequest {
  // Required. The name of the parent resource where the attachment will be
  // created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/Attachment"
    }
  ];

  // Required. The attachment id to use for this attachment.
  string attachment_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The attachment to be created.
  Attachment attachment = 3 [(google.api.field_behavior) = REQUIRED];
}

// The request to delete an attachment.
message DeleteAttachmentRequest {
  // Required. The name of the attachment to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Attachment"
    }
  ];
}
