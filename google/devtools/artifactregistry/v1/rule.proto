// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/type/expr.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1";
option go_package = "cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "RuleProto";
option java_package = "com.google.devtools.artifactregistry.v1";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1";

// A rule defines the deny or allow action of the operation it applies to and
// the conditions required for the rule to apply. You can set one rule for an
// entire repository and one rule for each package within.
message Rule {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/Rule"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/rules/{rule}"
  };

  // Defines the action of the rule.
  enum Action {
    // Action not specified.
    ACTION_UNSPECIFIED = 0;

    // Allow the operation.
    ALLOW = 1;

    // Deny the operation.
    DENY = 2;
  }

  // The operation the rule applies to.
  enum Operation {
    // Operation not specified.
    OPERATION_UNSPECIFIED = 0;

    // Download operation.
    DOWNLOAD = 1;
  }

  // The name of the rule, for example:
  // `projects/p1/locations/us-central1/repositories/repo1/rules/rule1`.
  string name = 1;

  // The action this rule takes.
  Action action = 2;

  Operation operation = 3;

  // Optional. A CEL expression for conditions that must be met in order for the
  // rule to apply. If not provided, the rule matches all objects.
  google.type.Expr condition = 4 [(google.api.field_behavior) = OPTIONAL];

  // The package ID the rule applies to.
  // If empty, this rule applies to all packages inside the repository.
  string package_id = 5;
}

// The request to list rules.
message ListRulesRequest {
  // Required. The name of the parent repository whose rules will be listed.
  // For example:
  // `projects/p1/locations/us-central1/repositories/repo1`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/Rule"
    }
  ];

  // The maximum number of rules to return. Maximum page size is 1,000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;
}

// The response from listing rules.
message ListRulesResponse {
  // The rules returned.
  repeated Rule rules = 1;

  // The token to retrieve the next page of rules, or empty if there are no
  // more rules to return.
  string next_page_token = 2;
}

// The request to retrieve a rule.
message GetRuleRequest {
  // Required. The name of the rule to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Rule"
    }
  ];
}

// The request to create a new rule.
message CreateRuleRequest {
  // Required. The name of the parent resource where the rule will be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "artifactregistry.googleapis.com/Rule"
    }
  ];

  // The rule id to use for this repository.
  string rule_id = 2;

  // The rule to be created.
  Rule rule = 3;
}

// The request to update a rule.
message UpdateRuleRequest {
  // The rule that replaces the resource on the server.
  Rule rule = 1;

  // The update mask applies to the resource. For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2;
}

// The request to delete a rule.
message DeleteRuleRequest {
  // Required. The name of the rule to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "artifactregistry.googleapis.com/Rule"
    }
  ];
}
