// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.containeranalysis.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";

option go_package = "cloud.google.com/go/containeranalysis/apiv1beta1/containeranalysispb;containeranalysispb";
option java_multiple_files = true;
option java_package = "com.google.containeranalysis.v1beta1";
option objc_class_prefix = "GCA";

// Retrieves analysis results of Cloud components such as Docker container
// images. The Container Analysis API is an implementation of the
// [Grafeas](https://grafeas.io) API.
//
// Analysis results are stored as a series of occurrences. An `Occurrence`
// contains information about a specific analysis instance on a resource. An
// occurrence refers to a `Note`. A note contains details describing the
// analysis and is generally stored in a separate project, called a `Provider`.
// Multiple occurrences can refer to the same note.
//
// For example, an SSL vulnerability could affect multiple images. In this case,
// there would be one note for the vulnerability and an occurrence for each
// image with the vulnerability referring to that note.
service ContainerAnalysisV1Beta1 {
  option (google.api.default_host) = "containeranalysis.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Sets the access control policy on the specified note or occurrence.
  // Requires `containeranalysis.notes.setIamPolicy` or
  // `containeranalysis.occurrences.setIamPolicy` permission if the resource is
  // a note or an occurrence, respectively.
  //
  // The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for
  // notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for
  // occurrences.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/notes/*}:setIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/occurrences/*}:setIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Gets the access control policy for a note or an occurrence resource.
  // Requires `containeranalysis.notes.setIamPolicy` or
  // `containeranalysis.occurrences.setIamPolicy` permission if the resource is
  // a note or occurrence, respectively.
  //
  // The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for
  // notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for
  // occurrences.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/notes/*}:getIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/occurrences/*}:getIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource";
  }

  // Returns the permissions that a caller has on the specified note or
  // occurrence. Requires list permission on the project (for example,
  // `containeranalysis.notes.list`).
  //
  // The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for
  // notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for
  // occurrences.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/notes/*}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/occurrences/*}:testIamPermissions"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource,permissions";
  }

  // Gets a summary of the packages within a given resource.
  rpc GeneratePackagesSummary(GeneratePackagesSummaryRequest)
      returns (PackagesSummaryResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/resources/**}:generatePackagesSummary"
      body: "*"
    };
  }

  // Generates an SBOM and other dependency information for the given resource.
  rpc ExportSBOM(ExportSBOMRequest) returns (ExportSBOMResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/resources/**}:exportSBOM"
      body: "*"
    };
  }
}

// GeneratePackagesSummaryRequest is the request body for the
// GeneratePackagesSummary API method. It just takes a single name argument,
// referring to the resource.
message GeneratePackagesSummaryRequest {
  // Required. The name of the resource to get a packages summary for in the
  // form of `projects/[PROJECT_ID]/resources/[RESOURCE_URL]`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// A summary of the packages found within the given resource.
message PackagesSummaryResponse {
  // Per license count
  message LicensesSummary {
    // The license of the package. Note that the format of this value is not
    // guaranteed. It may be nil, an empty string, a boolean value (A | B), a
    // differently formed boolean value (A OR B), etc...
    string license = 1;

    // The number of fixable vulnerabilities associated with this resource.
    int64 count = 2;
  }

  // The unique URL of the image or the container for which this summary
  // applies.
  string resource_url = 1;

  // A listing by license name of each of the licenses and their counts.
  repeated LicensesSummary licenses_summary = 2;
}

// The request to a call of ExportSBOM
message ExportSBOMRequest {
  // Required. The name of the resource in the form of
  // `projects/[PROJECT_ID]/resources/[RESOURCE_URL]`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The response from a call to ExportSBOM
message ExportSBOMResponse {
  // The name of the discovery occurrence in the form
  // "projects/{project_id}/occurrences/{OCCURRENCE_ID}
  // It can be used to track the progression of the SBOM export.
  string discovery_occurrence_id = 1;
}
