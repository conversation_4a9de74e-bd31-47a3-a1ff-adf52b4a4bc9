// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.cloudbuild.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/api/resource.proto";
import "google/api/routing.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CloudBuild.V1";
option go_package = "cloud.google.com/go/cloudbuild/apiv1/v2/cloudbuildpb;cloudbuildpb";
option java_multiple_files = true;
option java_package = "com.google.cloudbuild.v1";
option objc_class_prefix = "GCB";
option php_namespace = "Google\\Cloud\\Build\\V1";
option ruby_package = "Google::Cloud::Build::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};
option (google.api.resource_definition) = {
  type: "iam.googleapis.com/ServiceAccount"
  pattern: "projects/{project}/serviceAccounts/{service_account}"
};
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/Secret"
  pattern: "projects/{project}/secrets/{secret}"
};
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/SecretVersion"
  pattern: "projects/{project}/secrets/{secret}/versions/{version}"
};
option (google.api.resource_definition) = {
  type: "gkehub.googleapis.com/Membership"
  pattern: "projects/{project}/locations/{location}/memberships/{cluster_name}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{keyring}/cryptoKeys/{key}"
};
option (google.api.resource_definition) = {
  type: "pubsub.googleapis.com/Subscription"
  pattern: "projects/{project}/subscriptions/{subscription}"
};
option (google.api.resource_definition) = {
  type: "pubsub.googleapis.com/Topic"
  pattern: "projects/{project}/topics/{topic}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/NetworkAttachment"
  pattern: "projects/{project}/regions/{region}/networkAttachments/{networkattachment}"
};
option (google.api.resource_definition) = {
  type: "cloudbuild.googleapis.com/Repository"
  pattern: "projects/{project}/locations/{location}/connections/{connection}/repositories/{repository}"
};

// Creates and manages builds on Google Cloud Platform.
//
// The main concept used by this API is a `Build`, which describes the location
// of the source to build, how to build the source, and where to store the
// built artifacts, if any.
//
// A user can list previously-requested builds or get builds by their ID to
// determine the status of the build.
service CloudBuild {
  option (google.api.default_host) = "cloudbuild.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Starts a build with the specified configuration.
  //
  // This method returns a long-running `Operation`, which includes the build
  // ID. Pass the build ID to `GetBuild` to determine the build status (such as
  // `SUCCESS` or `FAILURE`).
  rpc CreateBuild(CreateBuildRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}/builds"
      body: "build"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*}/builds"
        body: "build"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "project_id,build";
    option (google.longrunning.operation_info) = {
      response_type: "Build"
      metadata_type: "BuildOperationMetadata"
    };
  }

  // Returns information about a previously requested build.
  //
  // The `Build` that is returned includes its status (such as `SUCCESS`,
  // `FAILURE`, or `WORKING`), and timing information.
  rpc GetBuild(GetBuildRequest) returns (Build) {
    option (google.api.http) = {
      get: "/v1/projects/{project_id}/builds/{id}"
      additional_bindings { get: "/v1/{name=projects/*/locations/*/builds/*}" }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/builds/*"
      }
    };
    option (google.api.method_signature) = "project_id,id";
  }

  // Lists previously requested builds.
  //
  // Previously requested builds may still be in-progress, or may have finished
  // successfully or unsuccessfully.
  rpc ListBuilds(ListBuildsRequest) returns (ListBuildsResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project_id}/builds"
      additional_bindings { get: "/v1/{parent=projects/*/locations/*}/builds" }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "project_id,filter";
  }

  // Cancels a build in progress.
  rpc CancelBuild(CancelBuildRequest) returns (Build) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}/builds/{id}:cancel"
      body: "*"
      additional_bindings {
        post: "/v1/{name=projects/*/locations/*/builds/*}:cancel"
        body: "*"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/builds/*"
      }
    };
    option (google.api.method_signature) = "project_id,id";
  }

  // Creates a new build based on the specified build.
  //
  // This method creates a new build using the original build request, which may
  // or may not result in an identical build.
  //
  // For triggered builds:
  //
  // * Triggered builds resolve to a precise revision; therefore a retry of a
  // triggered build will result in a build that uses the same revision.
  //
  // For non-triggered builds that specify `RepoSource`:
  //
  // * If the original build built from the tip of a branch, the retried build
  // will build from the tip of that branch, which may not be the same revision
  // as the original build.
  // * If the original build specified a commit sha or revision ID, the retried
  // build will use the identical source.
  //
  // For builds that specify `StorageSource`:
  //
  // * If the original build pulled source from Cloud Storage without
  // specifying the generation of the object, the new build will use the current
  // object, which may be different from the original build source.
  // * If the original build pulled source from Cloud Storage and specified the
  // generation of the object, the new build will attempt to use the same
  // object, which may or may not be available depending on the bucket's
  // lifecycle management settings.
  rpc RetryBuild(RetryBuildRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}/builds/{id}:retry"
      body: "*"
      additional_bindings {
        post: "/v1/{name=projects/*/locations/*/builds/*}:retry"
        body: "*"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/builds/*"
      }
    };
    option (google.api.method_signature) = "project_id,id";
    option (google.longrunning.operation_info) = {
      response_type: "Build"
      metadata_type: "BuildOperationMetadata"
    };
  }

  // Approves or rejects a pending build.
  //
  // If approved, the returned LRO will be analogous to the LRO returned from
  // a CreateBuild call.
  //
  // If rejected, the returned LRO will be immediately done.
  rpc ApproveBuild(ApproveBuildRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/builds/*}:approve"
      body: "*"
      additional_bindings {
        post: "/v1/{name=projects/*/locations/*/builds/*}:approve"
        body: "*"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/builds/*"
      }
    };
    option (google.api.method_signature) = "name,approval_result";
    option (google.longrunning.operation_info) = {
      response_type: "Build"
      metadata_type: "BuildOperationMetadata"
    };
  }

  // Creates a new `BuildTrigger`.
  //
  // This API is experimental.
  rpc CreateBuildTrigger(CreateBuildTriggerRequest) returns (BuildTrigger) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}/triggers"
      body: "trigger"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*}/triggers"
        body: "trigger"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "project_id,trigger";
  }

  // Returns information about a `BuildTrigger`.
  //
  // This API is experimental.
  rpc GetBuildTrigger(GetBuildTriggerRequest) returns (BuildTrigger) {
    option (google.api.http) = {
      get: "/v1/projects/{project_id}/triggers/{trigger_id}"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/triggers/*}"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/triggers/*"
      }
    };
    option (google.api.method_signature) = "project_id,trigger_id";
  }

  // Lists existing `BuildTrigger`s.
  //
  // This API is experimental.
  rpc ListBuildTriggers(ListBuildTriggersRequest)
      returns (ListBuildTriggersResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project_id}/triggers"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*}/triggers"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "project_id";
  }

  // Deletes a `BuildTrigger` by its project ID and trigger ID.
  //
  // This API is experimental.
  rpc DeleteBuildTrigger(DeleteBuildTriggerRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/projects/{project_id}/triggers/{trigger_id}"
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/triggers/*}"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/triggers/*"
      }
    };
    option (google.api.method_signature) = "project_id,trigger_id";
  }

  // Updates a `BuildTrigger` by its project ID and trigger ID.
  //
  // This API is experimental.
  rpc UpdateBuildTrigger(UpdateBuildTriggerRequest) returns (BuildTrigger) {
    option (google.api.http) = {
      patch: "/v1/projects/{project_id}/triggers/{trigger_id}"
      body: "trigger"
      additional_bindings {
        patch: "/v1/{trigger.resource_name=projects/*/locations/*/triggers/*}"
        body: "trigger"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "trigger.resource_name"
        path_template: "projects/*/locations/{location=*}/triggers/*"
      }
    };
    option (google.api.method_signature) = "project_id,trigger_id,trigger";
  }

  // Runs a `BuildTrigger` at a particular source revision.
  //
  // To run a regional or global trigger, use the POST request
  // that includes the location endpoint in the path (ex.
  // v1/projects/{projectId}/locations/{region}/triggers/{triggerId}:run). The
  // POST request that does not include the location endpoint in the path can
  // only be used when running global triggers.
  rpc RunBuildTrigger(RunBuildTriggerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}/triggers/{trigger_id}:run"
      body: "source"
      additional_bindings {
        post: "/v1/{name=projects/*/locations/*/triggers/*}:run"
        body: "*"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/triggers/*"
      }
    };
    option (google.api.method_signature) = "project_id,trigger_id,source";
    option (google.longrunning.operation_info) = {
      response_type: "Build"
      metadata_type: "BuildOperationMetadata"
    };
  }

  // ReceiveTriggerWebhook [Experimental] is called when the API receives a
  // webhook request targeted at a specific trigger.
  rpc ReceiveTriggerWebhook(ReceiveTriggerWebhookRequest)
      returns (ReceiveTriggerWebhookResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}/triggers/{trigger}:webhook"
      body: "body"
      additional_bindings {
        post: "/v1/{name=projects/*/locations/*/triggers/*}:webhook"
        body: "body"
      }
    };
  }

  // Creates a `WorkerPool`.
  rpc CreateWorkerPool(CreateWorkerPoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/workerPools"
      body: "worker_pool"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "parent,worker_pool,worker_pool_id";
    option (google.longrunning.operation_info) = {
      response_type: "WorkerPool"
      metadata_type: "CreateWorkerPoolOperationMetadata"
    };
  }

  // Returns details of a `WorkerPool`.
  rpc GetWorkerPool(GetWorkerPoolRequest) returns (WorkerPool) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/workerPools/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/workerPools/*"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a `WorkerPool`.
  rpc DeleteWorkerPool(DeleteWorkerPoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/workerPools/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/workerPools/*"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteWorkerPoolOperationMetadata"
    };
  }

  // Updates a `WorkerPool`.
  rpc UpdateWorkerPool(UpdateWorkerPoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{worker_pool.name=projects/*/locations/*/workerPools/*}"
      body: "worker_pool"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "worker_pool.name"
        path_template: "projects/*/locations/{location=*}/workerPools/*"
      }
    };
    option (google.api.method_signature) = "worker_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "WorkerPool"
      metadata_type: "UpdateWorkerPoolOperationMetadata"
    };
  }

  // Lists `WorkerPool`s.
  rpc ListWorkerPools(ListWorkerPoolsRequest)
      returns (ListWorkerPoolsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/workerPools"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// Specifies a build to retry.
message RetryBuildRequest {
  // The name of the `Build` to retry.
  // Format: `projects/{project}/locations/{location}/builds/{build}`
  string name = 3 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/Build"
  }];

  // Required. ID of the project.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Build ID of the original build.
  string id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Specifies a build trigger to run and the source to use.
message RunBuildTriggerRequest {
  // The name of the `Trigger` to run.
  // Format: `projects/{project}/locations/{location}/triggers/{trigger}`
  string name = 4 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/BuildTrigger"
  }];

  // Required. ID of the project.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the trigger.
  string trigger_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Source to build against this trigger.
  // Branch and tag names cannot consist of regular expressions.
  RepoSource source = 3;
}

// Location of the source in an archive file in Cloud Storage.
message StorageSource {
  // Cloud Storage bucket containing the source (see
  // [Bucket Name
  // Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).
  string bucket = 1;

  // Cloud Storage object containing the source.
  //
  // This object must be a zipped (`.zip`) or gzipped archive file (`.tar.gz`)
  // containing source to build.
  string object = 2;

  // Cloud Storage generation for the object. If the generation is
  // omitted, the latest generation will be used.
  int64 generation = 3;

  // Specifies the tool to fetch the source file for the build.
  enum SourceFetcher {
    // Unspecified. Defaults to GSUTIL.
    SOURCE_FETCHER_UNSPECIFIED = 0;

    // Use the "gsutil" tool to download the source file.
    GSUTIL = 1;

    // Use the Cloud Storage Fetcher tool to download the source file.
    GCS_FETCHER = 2;
  }

  // Option to specify the tool to fetch the source file for the build.
  SourceFetcher source_fetcher = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Location of the source in any accessible Git repository.
message GitSource {
  // Location of the Git repo to build.
  //
  // This will be used as a `git remote`, see
  // https://git-scm.com/docs/git-remote.
  string url = 1;

  // Directory, relative to the source root, in which to run the build.
  //
  // This must be a relative path. If a step's `dir` is specified and is an
  // absolute path, this value is ignored for that step's execution.
  string dir = 5;

  // The revision to fetch from the Git repository such as a branch, a tag, a
  // commit SHA, or any Git ref.
  //
  // Cloud Build uses `git fetch` to fetch the revision from the Git
  // repository; therefore make sure that the string you provide for `revision`
  // is parsable  by the command. For information on string values accepted by
  // `git fetch`, see
  // https://git-scm.com/docs/gitrevisions#_specifying_revisions. For
  // information on `git fetch`, see https://git-scm.com/docs/git-fetch.
  string revision = 6;
}

// Location of the source in a Google Cloud Source Repository.
message RepoSource {
  // ID of the project that owns the Cloud Source Repository. If omitted, the
  // project ID requesting the build is assumed.
  string project_id = 1;

  // Name of the Cloud Source Repository.
  string repo_name = 2;

  // A revision within the Cloud Source Repository must be specified in
  // one of these ways.
  oneof revision {
    // Regex matching branches to build.
    //
    // The syntax of the regular expressions accepted is the syntax accepted by
    // RE2 and described at https://github.com/google/re2/wiki/Syntax
    string branch_name = 3;

    // Regex matching tags to build.
    //
    // The syntax of the regular expressions accepted is the syntax accepted by
    // RE2 and described at https://github.com/google/re2/wiki/Syntax
    string tag_name = 4;

    // Explicit commit SHA to build.
    string commit_sha = 5;
  }

  // Directory, relative to the source root, in which to run the build.
  //
  // This must be a relative path. If a step's `dir` is specified and is an
  // absolute path, this value is ignored for that step's execution.
  string dir = 7;

  // Only trigger a build if the revision regex does NOT match the revision
  // regex.
  bool invert_regex = 8;

  // Substitutions to use in a triggered build.
  // Should only be used with RunBuildTrigger
  map<string, string> substitutions = 9;
}

// Location of the source manifest in Cloud Storage.
// This feature is in Preview; see description
// [here](https://github.com/GoogleCloudPlatform/cloud-builders/tree/master/gcs-fetcher).
message StorageSourceManifest {
  // Cloud Storage bucket containing the source manifest (see [Bucket
  // Name
  // Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).
  string bucket = 1;

  // Cloud Storage object containing the source manifest.
  //
  // This object must be a JSON file.
  string object = 2;

  // Cloud Storage generation for the object. If the generation is
  // omitted, the latest generation will be used.
  int64 generation = 3;
}

// Location of the source in a supported storage service.
message Source {
  // Location of source.
  oneof source {
    // If provided, get the source from this location in Cloud Storage.
    StorageSource storage_source = 2;

    // If provided, get the source from this location in a Cloud Source
    // Repository.
    RepoSource repo_source = 3;

    // If provided, get the source from this Git repository.
    GitSource git_source = 5;

    // If provided, get the source from this manifest in Cloud Storage.
    // This feature is in Preview; see description
    // [here](https://github.com/GoogleCloudPlatform/cloud-builders/tree/master/gcs-fetcher).
    StorageSourceManifest storage_source_manifest = 8;
  }
}

// An image built by the pipeline.
message BuiltImage {
  // Name used to push the container image to Google Container Registry, as
  // presented to `docker push`.
  string name = 1;

  // Docker Registry 2.0 digest.
  string digest = 3;

  // Output only. Stores timing information for pushing the specified image.
  TimeSpan push_timing = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Artifact uploaded using the PythonPackage directive.
message UploadedPythonPackage {
  // URI of the uploaded artifact.
  string uri = 1;

  // Hash types and values of the Python Artifact.
  FileHashes file_hashes = 2;

  // Output only. Stores timing information for pushing the specified artifact.
  TimeSpan push_timing = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A Maven artifact uploaded using the MavenArtifact directive.
message UploadedMavenArtifact {
  // URI of the uploaded artifact.
  string uri = 1;

  // Hash types and values of the Maven Artifact.
  FileHashes file_hashes = 2;

  // Output only. Stores timing information for pushing the specified artifact.
  TimeSpan push_timing = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A Go module artifact uploaded to Artifact Registry using the GoModule
// directive.
message UploadedGoModule {
  // URI of the uploaded artifact.
  string uri = 1;

  // Hash types and values of the Go Module Artifact.
  FileHashes file_hashes = 2;

  // Output only. Stores timing information for pushing the specified artifact.
  TimeSpan push_timing = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An npm package uploaded to Artifact Registry using the NpmPackage
// directive.
message UploadedNpmPackage {
  // URI of the uploaded npm package.
  string uri = 1;

  // Hash types and values of the npm package.
  FileHashes file_hashes = 2;

  // Output only. Stores timing information for pushing the specified artifact.
  TimeSpan push_timing = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A step in the build pipeline.
message BuildStep {
  // Required. The name of the container image that will run this particular
  // build step.
  //
  // If the image is available in the host's Docker daemon's cache, it
  // will be run directly. If not, the host will attempt to pull the image
  // first, using the builder service account's credentials if necessary.
  //
  // The Docker daemon's cache will already have the latest versions of all of
  // the officially supported build steps
  // ([https://github.com/GoogleCloudPlatform/cloud-builders](https://github.com/GoogleCloudPlatform/cloud-builders)).
  // The Docker daemon will also have cached many of the layers for some popular
  // images, like "ubuntu", "debian", but they will be refreshed at the time you
  // attempt to use them.
  //
  // If you built an image in a previous build step, it will be stored in the
  // host's Docker daemon's cache and is available to use as the name for a
  // later build step.
  string name = 1;

  // A list of environment variable definitions to be used when running a step.
  //
  // The elements are of the form "KEY=VALUE" for the environment variable "KEY"
  // being given the value "VALUE".
  repeated string env = 2;

  // A list of arguments that will be presented to the step when it is started.
  //
  // If the image used to run the step's container has an entrypoint, the `args`
  // are used as arguments to that entrypoint. If the image does not define
  // an entrypoint, the first element in args is used as the entrypoint,
  // and the remainder will be used as arguments.
  repeated string args = 3;

  // Working directory to use when running this step's container.
  //
  // If this value is a relative path, it is relative to the build's working
  // directory. If this value is absolute, it may be outside the build's working
  // directory, in which case the contents of the path may not be persisted
  // across build step executions, unless a `volume` for that path is specified.
  //
  // If the build specifies a `RepoSource` with `dir` and a step with a `dir`,
  // which specifies an absolute path, the `RepoSource` `dir` is ignored for
  // the step's execution.
  string dir = 4;

  // Unique identifier for this build step, used in `wait_for` to
  // reference this build step as a dependency.
  string id = 5;

  // The ID(s) of the step(s) that this build step depends on.
  // This build step will not start until all the build steps in `wait_for`
  // have completed successfully. If `wait_for` is empty, this build step will
  // start when all previous build steps in the `Build.Steps` list have
  // completed successfully.
  repeated string wait_for = 6;

  // Entrypoint to be used instead of the build step image's default entrypoint.
  // If unset, the image's default entrypoint is used.
  string entrypoint = 7;

  // A list of environment variables which are encrypted using a Cloud Key
  // Management Service crypto key. These values must be specified in the
  // build's `Secret`.
  repeated string secret_env = 8;

  // List of volumes to mount into the build step.
  //
  // Each volume is created as an empty volume prior to execution of the
  // build step. Upon completion of the build, volumes and their contents are
  // discarded.
  //
  // Using a named volume in only one step is not valid as it is indicative
  // of a build request with an incorrect configuration.
  repeated Volume volumes = 9;

  // Output only. Stores timing information for executing this build step.
  TimeSpan timing = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Stores timing information for pulling this build step's
  // builder image only.
  TimeSpan pull_timing = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Time limit for executing this build step. If not defined, the step has no
  // time limit and will be allowed to continue to run until either it completes
  // or the build itself times out.
  google.protobuf.Duration timeout = 11;

  // Output only. Status of the build step. At this time, build step status is
  // only updated on build completion; step status is not updated in real-time
  // as the build progresses.
  Build.Status status = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Allow this build step to fail without failing the entire build.
  //
  // If false, the entire build will fail if this step fails. Otherwise, the
  // build will succeed, but this step will still have a failure status.
  // Error information will be reported in the failure_detail field.
  bool allow_failure = 14;

  // Output only. Return code from running the step.
  int32 exit_code = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Allow this build step to fail without failing the entire build if and
  // only if the exit code is one of the specified codes. If allow_failure
  // is also specified, this field will take precedence.
  repeated int32 allow_exit_codes = 18;

  // A shell script to be executed in the step.
  //
  // When script is provided, the user cannot specify the entrypoint or args.
  string script = 19;

  // Option to include built-in and custom substitutions as env variables
  // for this build step. This option will override the global option
  // in BuildOption.
  optional bool automap_substitutions = 20;
}

// Volume describes a Docker container volume which is mounted into build steps
// in order to persist files across build step execution.
message Volume {
  // Name of the volume to mount.
  //
  // Volume names must be unique per build step and must be valid names for
  // Docker volumes. Each named volume must be used by at least two build steps.
  string name = 1;

  // Path at which to mount the volume.
  //
  // Paths must be absolute and cannot conflict with other volume paths on the
  // same build step or with certain reserved volume paths.
  string path = 2;
}

// Artifacts created by the build pipeline.
message Results {
  // Container images that were built as a part of the build.
  repeated BuiltImage images = 2;

  // List of build step digests, in the order corresponding to build step
  // indices.
  repeated string build_step_images = 3;

  // Path to the artifact manifest for non-container artifacts uploaded to Cloud
  // Storage. Only populated when artifacts are uploaded to Cloud Storage.
  string artifact_manifest = 4;

  // Number of non-container artifacts uploaded to Cloud Storage. Only populated
  // when artifacts are uploaded to Cloud Storage.
  int64 num_artifacts = 5;

  // List of build step outputs, produced by builder images, in the order
  // corresponding to build step indices.
  //
  // [Cloud Builders](https://cloud.google.com/cloud-build/docs/cloud-builders)
  // can produce this output by writing to `$BUILDER_OUTPUT/output`.
  // Only the first 4KB of data is stored.
  repeated bytes build_step_outputs = 6;

  // Time to push all non-container artifacts to Cloud Storage.
  TimeSpan artifact_timing = 7;

  // Python artifacts uploaded to Artifact Registry at the end of the build.
  repeated UploadedPythonPackage python_packages = 8;

  // Maven artifacts uploaded to Artifact Registry at the end of the build.
  repeated UploadedMavenArtifact maven_artifacts = 9;

  // Optional. Go module artifacts uploaded to Artifact Registry at the end of
  // the build.
  repeated UploadedGoModule go_modules = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Npm packages uploaded to Artifact Registry at the end of the build.
  repeated UploadedNpmPackage npm_packages = 12;
}

// An artifact that was uploaded during a build. This
// is a single record in the artifact manifest JSON file.
message ArtifactResult {
  // The path of an artifact in a Cloud Storage bucket, with the
  // generation number. For example,
  // `gs://mybucket/path/to/output.jar#generation`.
  string location = 1;

  // The file hash of the artifact.
  repeated FileHashes file_hash = 2;
}

// A build resource in the Cloud Build API.
//
// At a high level, a `Build` describes where to find source code, how to build
// it (for example, the builder image to run on the source), and where to store
// the built artifacts.
//
// Fields can include the following variables, which will be expanded when the
// build is created:
//
// - $PROJECT_ID: the project ID of the build.
// - $PROJECT_NUMBER: the project number of the build.
// - $LOCATION: the location/region of the build.
// - $BUILD_ID: the autogenerated ID of the build.
// - $REPO_NAME: the source repository name specified by RepoSource.
// - $BRANCH_NAME: the branch name specified by RepoSource.
// - $TAG_NAME: the tag name specified by RepoSource.
// - $REVISION_ID or $COMMIT_SHA: the commit SHA specified by RepoSource or
//   resolved from the specified branch or tag.
// - $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.
message Build {
  option (google.api.resource) = {
    type: "cloudbuild.googleapis.com/Build"
    pattern: "projects/{project}/builds/{build}"
    pattern: "projects/{project}/locations/{location}/builds/{build}"
  };

  // Possible status of a build or build step.
  enum Status {
    // Status of the build is unknown.
    STATUS_UNKNOWN = 0;

    // Build has been created and is pending execution and queuing. It has not
    // been queued.
    PENDING = 10;

    // Build or step is queued; work has not yet begun.
    QUEUED = 1;

    // Build or step is being executed.
    WORKING = 2;

    // Build or step finished successfully.
    SUCCESS = 3;

    // Build or step failed to complete successfully.
    FAILURE = 4;

    // Build or step failed due to an internal cause.
    INTERNAL_ERROR = 5;

    // Build or step took longer than was allowed.
    TIMEOUT = 6;

    // Build or step was canceled by a user.
    CANCELLED = 7;

    // Build was enqueued for longer than the value of `queue_ttl`.
    EXPIRED = 9;
  }

  // A non-fatal problem encountered during the execution of the build.
  message Warning {
    // The relative importance of this warning.
    enum Priority {
      // Should not be used.
      PRIORITY_UNSPECIFIED = 0;

      // e.g. deprecation warnings and alternative feature highlights.
      INFO = 1;

      // e.g. automated detection of possible issues with the build.
      WARNING = 2;

      // e.g. alerts that a feature used in the build is pending removal
      ALERT = 3;
    }

    // Explanation of the warning generated.
    string text = 1;

    // The priority for this warning.
    Priority priority = 2;
  }

  // A fatal problem encountered during the execution of the build.
  message FailureInfo {
    // The name of a fatal problem encountered during the execution of the
    // build.
    enum FailureType {
      // Type unspecified
      FAILURE_TYPE_UNSPECIFIED = 0;

      // Unable to push the image to the repository.
      PUSH_FAILED = 1;

      // Final image not found.
      PUSH_IMAGE_NOT_FOUND = 2;

      // Unauthorized push of the final image.
      PUSH_NOT_AUTHORIZED = 3;

      // Backend logging failures. Should retry.
      LOGGING_FAILURE = 4;

      // A build step has failed.
      USER_BUILD_STEP = 5;

      // The source fetching has failed.
      FETCH_SOURCE_FAILED = 6;
    }

    // The name of the failure.
    FailureType type = 1;

    // Explains the failure issue in more detail using hard-coded text.
    string detail = 2;
  }

  // Output only. The 'Build' name with format:
  // `projects/{project}/locations/{location}/builds/{build}`, where {build}
  // is a unique identifier generated by the service.
  string name = 45 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unique identifier of the build.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the project.
  string project_id = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of the build.
  Status status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Customer-readable message about the current status.
  string status_detail = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The location of the source files to build.
  Source source = 3;

  // Required. The operations to be performed on the workspace.
  repeated BuildStep steps = 11;

  // Output only. Results of the build.
  Results results = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the request to create the build was received.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which execution of the build was started.
  google.protobuf.Timestamp start_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which execution of the build was finished.
  //
  // The difference between finish_time and start_time is the duration of the
  // build's execution.
  google.protobuf.Timestamp finish_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Amount of time that this build should be allowed to run, to second
  // granularity. If this amount of time elapses, work on the build will cease
  // and the build status will be `TIMEOUT`.
  //
  // `timeout` starts ticking from `startTime`.
  //
  // Default time is 60 minutes.
  google.protobuf.Duration timeout = 12;

  // A list of images to be pushed upon the successful completion of all build
  // steps.
  //
  // The images are pushed using the builder service account's credentials.
  //
  // The digests of the pushed images will be stored in the `Build` resource's
  // results field.
  //
  // If any of the images fail to be pushed, the build status is marked
  // `FAILURE`.
  repeated string images = 13;

  // TTL in queue for this build. If provided and the build is enqueued longer
  // than this value, the build will expire and the build status will be
  // `EXPIRED`.
  //
  // The TTL starts ticking from create_time.
  google.protobuf.Duration queue_ttl = 40;

  // Artifacts produced by the build that should be uploaded upon
  // successful completion of all build steps.
  Artifacts artifacts = 37;

  // Cloud Storage bucket where logs should be written (see
  // [Bucket Name
  // Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).
  // Logs file names will be of the format `${logs_bucket}/log-${build_id}.txt`.
  string logs_bucket = 19;

  // Output only. A permanent fixed identifier for source.
  SourceProvenance source_provenance = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the `BuildTrigger` that triggered this build, if it
  // was triggered automatically.
  string build_trigger_id = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Special options for this build.
  BuildOptions options = 23;

  // Output only. URL to logs for this build in Google Cloud Console.
  string log_url = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Substitutions data for `Build` resource.
  map<string, string> substitutions = 29;

  // Tags for annotation of a `Build`. These are not docker tags.
  repeated string tags = 31;

  // Secrets to decrypt using Cloud Key Management Service.
  // Note: Secret Manager is the recommended technique
  // for managing sensitive data with Cloud Build. Use `available_secrets` to
  // configure builds to access secrets from Secret Manager. For instructions,
  // see: https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets
  repeated Secret secrets = 32;

  // Output only. Stores timing information for phases of the build. Valid keys
  // are:
  //
  // * BUILD: time to execute all build steps.
  // * PUSH: time to push all artifacts including docker images and non docker
  // artifacts.
  // * FETCHSOURCE: time to fetch source.
  // * SETUPBUILD: time to set up build.
  //
  // If the build does not specify source or images,
  // these keys will not be included.
  map<string, TimeSpan> timing = 33 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Describes this build's approval configuration, status,
  // and result.
  BuildApproval approval = 44 [(google.api.field_behavior) = OUTPUT_ONLY];

  // IAM service account whose credentials will be used at build runtime.
  // Must be of the format `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // ACCOUNT can be email address or uniqueId of the service account.
  //
  string service_account = 42 [(google.api.resource_reference) = {
    type: "iam.googleapis.com/ServiceAccount"
  }];

  // Secrets and secret environment variables.
  Secrets available_secrets = 47;

  // Output only. Non-fatal problems encountered during the execution of the
  // build.
  repeated Warning warnings = 49 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Contains information about the build when status=FAILURE.
  FailureInfo failure_info = 51 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Artifacts produced by a build that should be uploaded upon
// successful completion of all build steps.
message Artifacts {
  // Files in the workspace to upload to Cloud Storage upon successful
  // completion of all build steps.
  message ArtifactObjects {
    // Cloud Storage bucket and optional object path, in the form
    // "gs://bucket/path/to/somewhere/". (see [Bucket Name
    // Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).
    //
    // Files in the workspace matching any path pattern will be uploaded to
    // Cloud Storage with this location as a prefix.
    string location = 1;

    // Path globs used to match files in the build's workspace.
    repeated string paths = 2;

    // Output only. Stores timing information for pushing all artifact objects.
    TimeSpan timing = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // A Maven artifact to upload to Artifact Registry upon successful completion
  // of all build steps.
  message MavenArtifact {
    // Artifact Registry repository, in the form
    // "https://$REGION-maven.pkg.dev/$PROJECT/$REPOSITORY"
    //
    // Artifact in the workspace specified by path will be uploaded to
    // Artifact Registry with this location as a prefix.
    string repository = 1;

    // Path to an artifact in the build's workspace to be uploaded to
    // Artifact Registry.
    // This can be either an absolute path,
    // e.g. /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar
    // or a relative path from /workspace,
    // e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.
    string path = 2;

    // Maven `artifactId` value used when uploading the artifact to Artifact
    // Registry.
    string artifact_id = 3;

    // Maven `groupId` value used when uploading the artifact to Artifact
    // Registry.
    string group_id = 4;

    // Maven `version` value used when uploading the artifact to Artifact
    // Registry.
    string version = 5;
  }

  // Go module to upload to Artifact Registry upon successful completion of all
  // build steps. A module refers to all dependencies in a go.mod file.
  message GoModule {
    // Optional. Artifact Registry repository name.
    //
    // Specified Go modules will be zipped and uploaded to Artifact Registry
    // with this location as a prefix.
    // e.g. my-go-repo
    string repository_name = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Location of the Artifact Registry repository. i.e. us-east1
    // Defaults to the build’s location.
    string repository_location = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Project ID of the Artifact Registry repository.
    // Defaults to the build project.
    string repository_project_id = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Source path of the go.mod file in the build's workspace. If not
    // specified, this will default to the current directory.
    // e.g. ~/code/go/mypackage
    string source_path = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The Go module's "module path".
    // e.g. example.com/foo/v2
    string module_path = 5 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The Go module's semantic version in the form vX.Y.Z. e.g.
    // v0.1.1 Pre-release identifiers can also be added by appending a dash and
    // dot separated ASCII alphanumeric characters and hyphens.
    // e.g. v0.2.3-alpha.x.12m.5
    string module_version = 6 [(google.api.field_behavior) = OPTIONAL];
  }

  // Python package to upload to Artifact Registry upon successful completion
  // of all build steps. A package can encapsulate multiple objects to be
  // uploaded to a single repository.
  message PythonPackage {
    // Artifact Registry repository, in the form
    // "https://$REGION-python.pkg.dev/$PROJECT/$REPOSITORY"
    //
    // Files in the workspace matching any path pattern will be uploaded to
    // Artifact Registry with this location as a prefix.
    string repository = 1;

    // Path globs used to match files in the build's workspace. For Python/
    // Twine, this is usually `dist/*`, and sometimes additionally an `.asc`
    // file.
    repeated string paths = 2;
  }

  // Npm package to upload to Artifact Registry upon successful completion
  // of all build steps.
  message NpmPackage {
    // Artifact Registry repository, in the form
    // "https://$REGION-npm.pkg.dev/$PROJECT/$REPOSITORY"
    //
    // Npm package in the workspace specified by path will be zipped and
    // uploaded to Artifact Registry with this location as a prefix.
    string repository = 1;

    // Path to the package.json.
    // e.g. workspace/path/to/package
    string package_path = 2;
  }

  // A list of images to be pushed upon the successful completion of all build
  // steps.
  //
  // The images will be pushed using the builder service account's credentials.
  //
  // The digests of the pushed images will be stored in the Build resource's
  // results field.
  //
  // If any of the images fail to be pushed, the build is marked FAILURE.
  repeated string images = 1;

  // A list of objects to be uploaded to Cloud Storage upon successful
  // completion of all build steps.
  //
  // Files in the workspace matching specified paths globs will be uploaded to
  // the specified Cloud Storage location using the builder service account's
  // credentials.
  //
  // The location and generation of the uploaded objects will be stored in the
  // Build resource's results field.
  //
  // If any objects fail to be pushed, the build is marked FAILURE.
  ArtifactObjects objects = 2;

  // A list of Maven artifacts to be uploaded to Artifact Registry upon
  // successful completion of all build steps.
  //
  // Artifacts in the workspace matching specified paths globs will be uploaded
  // to the specified Artifact Registry repository using the builder service
  // account's credentials.
  //
  // If any artifacts fail to be pushed, the build is marked FAILURE.
  repeated MavenArtifact maven_artifacts = 3;

  // Optional. A list of Go modules to be uploaded to Artifact Registry upon
  // successful completion of all build steps.
  //
  // If any objects fail to be pushed, the build is marked FAILURE.
  repeated GoModule go_modules = 4 [(google.api.field_behavior) = OPTIONAL];

  // A list of Python packages to be uploaded to Artifact Registry upon
  // successful completion of all build steps.
  //
  // The build service account credentials will be used to perform the upload.
  //
  // If any objects fail to be pushed, the build is marked FAILURE.
  repeated PythonPackage python_packages = 5;

  // A list of npm packages to be uploaded to Artifact Registry upon
  // successful completion of all build steps.
  //
  // Npm packages in the specified paths will be uploaded
  // to the specified Artifact Registry repository using the builder service
  // account's credentials.
  //
  // If any packages fail to be pushed, the build is marked FAILURE.
  repeated NpmPackage npm_packages = 6;
}

// Start and end times for a build execution phase.
message TimeSpan {
  // Start of time span.
  google.protobuf.Timestamp start_time = 1;

  // End of time span.
  google.protobuf.Timestamp end_time = 2;
}

// Metadata for build operations.
message BuildOperationMetadata {
  // The build that the operation is tracking.
  Build build = 1;
}

// Provenance of the source. Ways to find the original source, or verify that
// some source was used for this build.
message SourceProvenance {
  // A copy of the build's `source.storage_source`, if exists, with any
  // generations resolved.
  StorageSource resolved_storage_source = 3;

  // A copy of the build's `source.repo_source`, if exists, with any
  // revisions resolved.
  RepoSource resolved_repo_source = 6;

  // A copy of the build's `source.storage_source_manifest`, if exists, with any
  // revisions resolved.
  // This feature is in Preview.
  StorageSourceManifest resolved_storage_source_manifest = 9;

  // Output only. Hash(es) of the build source, which can be used to verify that
  // the original source integrity was maintained in the build. Note that
  // `FileHashes` will only be populated if `BuildOptions` has requested a
  // `SourceProvenanceHash`.
  //
  // The keys to this map are file paths used as build source and the values
  // contain the hash values for those files.
  //
  // If the build source came in a single package such as a gzipped tarfile
  // (`.tar.gz`), the `FileHash` will be for the single path to that file.
  map<string, FileHashes> file_hashes = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Container message for hashes of byte content of files, used in
// SourceProvenance messages to verify integrity of source input to the build.
message FileHashes {
  // Collection of file hashes.
  repeated Hash file_hash = 1;
}

// Container message for hash values.
message Hash {
  // Specifies the hash algorithm, if any.
  enum HashType {
    // No hash requested.
    NONE = 0;

    // Use a sha256 hash.
    SHA256 = 1;

    // Use a md5 hash.
    MD5 = 2;

    // Dirhash of a Go module's source code which is then hex-encoded.
    GO_MODULE_H1 = 3;

    // Use a sha512 hash.
    SHA512 = 4;
  }

  // The type of hash that was performed.
  HashType type = 1;

  // The hash value.
  bytes value = 2;
}

// Secrets and secret environment variables.
message Secrets {
  // Secrets in Secret Manager and associated secret environment variable.
  repeated SecretManagerSecret secret_manager = 1;

  // Secrets encrypted with KMS key and the associated secret environment
  // variable.
  repeated InlineSecret inline = 2;
}

// Pairs a set of secret environment variables mapped to encrypted
// values with the Cloud KMS key to use to decrypt the value.
message InlineSecret {
  // Resource name of Cloud KMS crypto key to decrypt the encrypted value.
  // In format: projects/*/locations/*/keyRings/*/cryptoKeys/*
  string kms_key_name = 1 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // Map of environment variable name to its encrypted value.
  //
  // Secret environment variables must be unique across all of a build's
  // secrets, and must be used by at least one build step. Values can be at most
  // 64 KB in size. There can be at most 100 secret values across all of a
  // build's secrets.
  map<string, bytes> env_map = 2;
}

// Pairs a secret environment variable with a SecretVersion in Secret Manager.
message SecretManagerSecret {
  // Resource name of the SecretVersion. In format:
  // projects/*/secrets/*/versions/*
  string version_name = 1 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];

  // Environment variable name to associate with the secret.
  // Secret environment variables must be unique across all of a build's
  // secrets, and must be used by at least one build step.
  string env = 2;
}

// Pairs a set of secret environment variables containing encrypted
// values with the Cloud KMS key to use to decrypt the value.
// Note: Use `kmsKeyName` with  `available_secrets` instead of using
// `kmsKeyName` with `secret`. For instructions see:
// https://cloud.google.com/cloud-build/docs/securing-builds/use-encrypted-credentials.
message Secret {
  // Cloud KMS key name to use to decrypt these envs.
  string kms_key_name = 1;

  // Map of environment variable name to its encrypted value.
  //
  // Secret environment variables must be unique across all of a build's
  // secrets, and must be used by at least one build step. Values can be at most
  // 64 KB in size. There can be at most 100 secret values across all of a
  // build's secrets.
  map<string, bytes> secret_env = 3;
}

// Request to create a new build.
message CreateBuildRequest {
  // The parent resource where this build will be created.
  // Format: `projects/{project}/locations/{location}`
  string parent = 4 [(google.api.resource_reference) = {
    child_type: "cloudbuild.googleapis.com/Build"
  }];

  // Required. ID of the project.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Build resource to create.
  Build build = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to get a build.
message GetBuildRequest {
  // The name of the `Build` to retrieve.
  // Format: `projects/{project}/locations/{location}/builds/{build}`
  string name = 4 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/Build"
  }];

  // Required. ID of the project.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the build.
  string id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to list builds.
message ListBuildsRequest {
  // The parent of the collection of `Builds`.
  // Format: `projects/{project}/locations/{location}`
  string parent = 9 [(google.api.resource_reference) = {
    child_type: "cloudbuild.googleapis.com/Build"
  }];

  // Required. ID of the project.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Number of results to return in the list.
  int32 page_size = 2;

  // The page token for the next page of Builds.
  //
  // If unspecified, the first page of results is returned.
  //
  // If the token is rejected for any reason, INVALID_ARGUMENT will be thrown.
  // In this case, the token should be discarded, and pagination should be
  // restarted from the first page of results.
  //
  // See https://google.aip.dev/158 for more.
  string page_token = 3;

  // The raw filter text to constrain the results.
  string filter = 8;
}

// Response including listed builds.
message ListBuildsResponse {
  // Builds will be sorted by `create_time`, descending.
  repeated Build builds = 1;

  // Token to receive the next page of results.
  // This will be absent if the end of the response list has been reached.
  string next_page_token = 2;
}

// Request to cancel an ongoing build.
message CancelBuildRequest {
  // The name of the `Build` to cancel.
  // Format: `projects/{project}/locations/{location}/builds/{build}`
  string name = 4 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/Build"
  }];

  // Required. ID of the project.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the build.
  string id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to approve or reject a pending build.
message ApproveBuildRequest {
  // Required. Name of the target build.
  // For example: "projects/{$project_id}/builds/{$build_id}"
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Approval decision and metadata.
  ApprovalResult approval_result = 2;
}

// BuildApproval describes a build's approval configuration, state, and
// result.
message BuildApproval {
  // Specifies the current state of a build's approval.
  enum State {
    // Default enum type. This should not be used.
    STATE_UNSPECIFIED = 0;

    // Build approval is pending.
    PENDING = 1;

    // Build approval has been approved.
    APPROVED = 2;

    // Build approval has been rejected.
    REJECTED = 3;

    // Build was cancelled while it was still pending approval.
    CANCELLED = 5;
  }

  // Output only. The state of this build's approval.
  State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Configuration for manual approval of this build.
  ApprovalConfig config = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Result of manual approval for this Build.
  ApprovalResult result = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ApprovalConfig describes configuration for manual approval of a build.
message ApprovalConfig {
  // Whether or not approval is needed. If this is set on a build, it will
  // become pending when created, and will need to be explicitly approved
  // to start.
  bool approval_required = 1;
}

// ApprovalResult describes the decision and associated metadata of a manual
// approval of a build.
message ApprovalResult {
  // Specifies whether or not this manual approval result is to approve
  // or reject a build.
  enum Decision {
    // Default enum type. This should not be used.
    DECISION_UNSPECIFIED = 0;

    // Build is approved.
    APPROVED = 1;

    // Build is rejected.
    REJECTED = 2;
  }

  // Output only. Email of the user that called the ApproveBuild API to
  // approve or reject a build at the time that the API was called.
  string approver_account = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the approval decision was made.
  google.protobuf.Timestamp approval_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The decision of this manual approval.
  Decision decision = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional comment for this manual approval result.
  string comment = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional URL tied to this manual approval result. This field
  // is essentially the same as comment, except that it will be rendered by the
  // UI differently. An example use case is a link to an external job that
  // approved this Build.
  string url = 6 [(google.api.field_behavior) = OPTIONAL];
}

// GitRepoSource describes a repo and ref of a code repository.
message GitRepoSource {
  // The URI of the repo (e.g. https://github.com/user/repo.git).
  // Either `uri` or `repository` can be specified and is required.
  string uri = 1;

  // The source of the SCM repo.
  oneof source {
    // The connected repository resource name, in the format
    // `projects/*/locations/*/connections/*/repositories/*`. Either `uri` or
    // `repository` can be specified and is required.
    string repository = 6 [(google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Repository"
    }];
  }

  // The branch or tag to use. Must start with "refs/" (required).
  string ref = 2;

  // See RepoType below.
  GitFileSource.RepoType repo_type = 3;

  // The resource name of the enterprise config that should be applied
  // to this source.
  oneof enterprise_config {
    // The full resource name of the github enterprise config.
    // Format:
    // `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.
    // `projects/{project}/githubEnterpriseConfigs/{id}`.
    string github_enterprise_config = 4 [(google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/GithubEnterpriseConfig"
    }];
  }
}

// GitFileSource describes a file within a (possibly remote) code repository.
message GitFileSource {
  // The type of the repo, since it may not be explicit from the `repo` field
  // (e.g from a URL).
  enum RepoType {
    // The default, unknown repo type. Don't use it, instead use one of
    // the other repo types.
    UNKNOWN = 0;

    // A Google Cloud Source Repositories-hosted repo.
    CLOUD_SOURCE_REPOSITORIES = 1;

    // A GitHub-hosted repo not necessarily on "github.com" (i.e. GitHub
    // Enterprise).
    GITHUB = 2;

    // A Bitbucket Server-hosted repo.
    BITBUCKET_SERVER = 3;

    // A GitLab-hosted repo.
    GITLAB = 4;
  }

  // The path of the file, with the repo root as the root of the path.
  string path = 1;

  // The URI of the repo.
  // Either uri or repository can be specified.
  // If unspecified, the repo from which the trigger invocation originated is
  // assumed to be the repo from which to read the specified path.
  string uri = 2;

  // The source of the SCM repo.
  oneof source {
    // The fully qualified resource name of the Repos API repository.
    // Either URI or repository can be specified.
    // If unspecified, the repo from which the trigger invocation originated is
    // assumed to be the repo from which to read the specified path.
    string repository = 7 [(google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Repository"
    }];
  }

  // See RepoType above.
  RepoType repo_type = 3;

  // The branch, tag, arbitrary ref, or SHA version of the repo to use when
  // resolving the filename (optional).
  // This field respects the same syntax/resolution as described here:
  // https://git-scm.com/docs/gitrevisions
  // If unspecified, the revision from which the trigger invocation originated
  // is assumed to be the revision from which to read the specified path.
  string revision = 4;

  // The resource name of the enterprise config that should be applied
  // to this source.
  oneof enterprise_config {
    // The full resource name of the github enterprise config.
    // Format:
    // `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.
    // `projects/{project}/githubEnterpriseConfigs/{id}`.
    string github_enterprise_config = 5 [(google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/GithubEnterpriseConfig"
    }];
  }
}

// Configuration for an automated build in response to source repository
// changes.
message BuildTrigger {
  option (google.api.resource) = {
    type: "cloudbuild.googleapis.com/BuildTrigger"
    pattern: "projects/{project}/triggers/{trigger}"
    pattern: "projects/{project}/locations/{location}/triggers/{trigger}"
    plural: "triggers"
    singular: "trigger"
  };

  // The `Trigger` name with format:
  // `projects/{project}/locations/{location}/triggers/{trigger}`, where
  // {trigger} is a unique identifier generated by the service.
  string resource_name = 34;

  // Output only. Unique identifier of the trigger.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Human-readable description of this trigger.
  string description = 10;

  // User-assigned name of the trigger. Must be unique within the project.
  // Trigger names must meet the following requirements:
  //
  // + They must contain only alphanumeric characters and dashes.
  // + They can be 1-64 characters long.
  // + They must begin and end with an alphanumeric character.
  string name = 21;

  // Tags for annotation of a `BuildTrigger`
  repeated string tags = 19;

  // Template describing the types of source changes to trigger a build.
  //
  // Branch and tag names in trigger templates are interpreted as regular
  // expressions. Any branch or tag change that matches that regular expression
  // will trigger a build.
  //
  // Mutually exclusive with `github`.
  RepoSource trigger_template = 7;

  // GitHubEventsConfig describes the configuration of a trigger that creates
  // a build whenever a GitHub event is received.
  //
  // Mutually exclusive with `trigger_template`.
  GitHubEventsConfig github = 13;

  // PubsubConfig describes the configuration of a trigger that
  // creates a build whenever a Pub/Sub message is published.
  PubsubConfig pubsub_config = 29;

  // WebhookConfig describes the configuration of a trigger that
  // creates a build whenever a webhook is sent to a trigger's webhook URL.
  WebhookConfig webhook_config = 31;

  // Template describing the Build request to make when the trigger is matched.
  // At least one of the template fields must be provided.
  oneof build_template {
    // Autodetect build configuration.  The following precedence is used (case
    // insensitive):
    //
    // 1. cloudbuild.yaml
    // 2. cloudbuild.yml
    // 3. cloudbuild.json
    // 4. Dockerfile
    //
    // Currently only available for GitHub App Triggers.
    bool autodetect = 18;

    // Contents of the build template.
    Build build = 4;

    // Path, from the source root, to the build configuration file
    // (i.e. cloudbuild.yaml).
    string filename = 8;

    // The file source describing the local or remote Build template.
    GitFileSource git_file_source = 24;
  }

  // Output only. Time when the trigger was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // If true, the trigger will never automatically execute a build.
  bool disabled = 9;

  // Substitutions for Build resource. The keys must match the following
  // regular expression: `^_[A-Z0-9_]+$`.
  map<string, string> substitutions = 11;

  // ignored_files and included_files are file glob matches using
  // https://golang.org/pkg/path/filepath/#Match extended with support for "**".
  //
  // If ignored_files and changed files are both empty, then they are
  // not used to determine whether or not to trigger a build.
  //
  // If ignored_files is not empty, then we ignore any files that match
  // any of the ignored_file globs. If the change has no files that are
  // outside of the ignored_files globs, then we do not trigger a build.
  repeated string ignored_files = 15;

  // If any of the files altered in the commit pass the ignored_files
  // filter and included_files is empty, then as far as this filter is
  // concerned, we should trigger the build.
  //
  // If any of the files altered in the commit pass the ignored_files
  // filter and included_files is not empty, then we make sure that at
  // least one of those files matches a included_files glob. If not,
  // then we do not trigger a build.
  repeated string included_files = 16;

  // Optional. A Common Expression Language string.
  string filter = 30 [(google.api.field_behavior) = OPTIONAL];

  // The repo and ref of the repository from which to build. This field
  // is used only for those triggers that do not respond to SCM events.
  // Triggers that respond to such events build source at whatever commit
  // caused the event.
  // This field is currently only used by Webhook, Pub/Sub, Manual, and Cron
  // triggers.
  GitRepoSource source_to_build = 26;

  // The service account used for all user-controlled operations including
  // UpdateBuildTrigger, RunBuildTrigger, CreateBuild, and CancelBuild.
  // If no service account is set, then the standard Cloud Build service account
  // ([PROJECT_NUM]@system.gserviceaccount.com) will be used instead.
  // Format: `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT_ID_OR_EMAIL}`
  string service_account = 33 [(google.api.resource_reference) = {
    type: "iam.googleapis.com/ServiceAccount"
  }];

  // The configuration of a trigger that creates a build whenever an event from
  // Repo API is received.
  RepositoryEventConfig repository_event_config = 39;
}

// The configuration of a trigger that creates a build whenever an event from
// Repo API is received.
message RepositoryEventConfig {
  // All possible SCM repo types from Repo API.
  enum RepositoryType {
    // If unspecified, RepositoryType defaults to GITHUB.
    REPOSITORY_TYPE_UNSPECIFIED = 0;

    // The SCM repo is GITHUB.
    GITHUB = 1;

    // The SCM repo is GITHUB Enterprise.
    GITHUB_ENTERPRISE = 2;

    // The SCM repo is GITLAB Enterprise.
    GITLAB_ENTERPRISE = 3;
  }

  // The resource name of the Repo API resource.
  string repository = 1 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/Repository"
  }];

  // Output only. The type of the SCM vendor the repository points to.
  RepositoryType repository_type = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The types of filter to trigger a build.
  oneof filter {
    // Filter to match changes in pull requests.
    PullRequestFilter pull_request = 3;

    // Filter to match changes in refs like branches, tags.
    PushFilter push = 4;
  }
}

// GitHubEventsConfig describes the configuration of a trigger that creates a
// build whenever a GitHub event is received.
message GitHubEventsConfig {
  // The installationID that emits the GitHub event.
  int64 installation_id = 1 [deprecated = true];

  // Owner of the repository. For example: The owner for
  // https://github.com/googlecloudplatform/cloud-builders is
  // "googlecloudplatform".
  string owner = 6;

  // Name of the repository. For example: The name for
  // https://github.com/googlecloudplatform/cloud-builders is "cloud-builders".
  string name = 7;

  // Filter describing the types of events to trigger a build.
  // Currently supported event types: push, pull_request.
  oneof event {
    // filter to match changes in pull requests.
    PullRequestFilter pull_request = 4;

    // filter to match changes in refs like branches, tags.
    PushFilter push = 5;
  }
}

// PubsubConfig describes the configuration of a trigger that
// creates a build whenever a Pub/Sub message is published.
message PubsubConfig {
  // Enumerates potential issues with the underlying Pub/Sub subscription
  // configuration.
  enum State {
    // The subscription configuration has not been checked.
    STATE_UNSPECIFIED = 0;

    // The Pub/Sub subscription is properly configured.
    OK = 1;

    // The subscription has been deleted.
    SUBSCRIPTION_DELETED = 2;

    // The topic has been deleted.
    TOPIC_DELETED = 3;

    // Some of the subscription's field are misconfigured.
    SUBSCRIPTION_MISCONFIGURED = 4;
  }

  // Output only. Name of the subscription. Format is
  // `projects/{project}/subscriptions/{subscription}`.
  string subscription = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];

  // The name of the topic from which this subscription is receiving messages.
  // Format is `projects/{project}/topics/{topic}`.
  string topic = 2 [
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];

  // Service account that will make the push request.
  string service_account_email = 3 [(google.api.resource_reference) = {
    type: "iam.googleapis.com/ServiceAccount"
  }];

  // Potential issues with the underlying Pub/Sub subscription configuration.
  // Only populated on get requests.
  State state = 4;
}

// WebhookConfig describes the configuration of a trigger that
// creates a build whenever a webhook is sent to a trigger's webhook URL.
message WebhookConfig {
  // Enumerates potential issues with the Secret Manager secret provided by the
  // user.
  enum State {
    // The webhook auth configuration not been checked.
    STATE_UNSPECIFIED = 0;

    // The auth configuration is properly setup.
    OK = 1;

    // The secret provided in auth_method has been deleted.
    SECRET_DELETED = 2;
  }

  // Auth method specifies how the webhook authenticates with GCP.
  oneof auth_method {
    // Required. Resource name for the secret required as a URL parameter.
    string secret = 3 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "secretmanager.googleapis.com/SecretVersion"
      }
    ];
  }

  // Potential issues with the underlying Pub/Sub subscription configuration.
  // Only populated on get requests.
  State state = 4;
}

// PullRequestFilter contains filter properties for matching GitHub Pull
// Requests.
message PullRequestFilter {
  // Controls behavior of Pull Request comments.
  enum CommentControl {
    // Do not require comments on Pull Requests before builds are triggered.
    COMMENTS_DISABLED = 0;

    // Enforce that repository owners or collaborators must comment on Pull
    // Requests before builds are triggered.
    COMMENTS_ENABLED = 1;

    // Enforce that repository owners or collaborators must comment on external
    // contributors' Pull Requests before builds are triggered.
    COMMENTS_ENABLED_FOR_EXTERNAL_CONTRIBUTORS_ONLY = 2;
  }

  // Target refs to match.
  // A target ref is the git reference where the pull request will be applied.
  oneof git_ref {
    // Regex of branches to match.
    //
    // The syntax of the regular expressions accepted is the syntax accepted by
    // RE2 and described at https://github.com/google/re2/wiki/Syntax
    string branch = 2;
  }

  // Configure builds to run whether a repository owner or collaborator need to
  // comment `/gcbrun`.
  CommentControl comment_control = 5;

  // If true, branches that do NOT match the git_ref will trigger a build.
  bool invert_regex = 6;
}

// Push contains filter properties for matching GitHub git pushes.
message PushFilter {
  // Modified refs to match.
  // A modified refs are the refs modified by a git push operation.
  oneof git_ref {
    // Regexes matching branches to build.
    //
    // The syntax of the regular expressions accepted is the syntax accepted by
    // RE2 and described at https://github.com/google/re2/wiki/Syntax
    string branch = 2;

    // Regexes matching tags to build.
    //
    // The syntax of the regular expressions accepted is the syntax accepted by
    // RE2 and described at https://github.com/google/re2/wiki/Syntax
    string tag = 3;
  }

  // When true, only trigger a build if the revision regex does NOT match the
  // git_ref regex.
  bool invert_regex = 4;
}

// Request to create a new `BuildTrigger`.
message CreateBuildTriggerRequest {
  // The parent resource where this trigger will be created.
  // Format: `projects/{project}/locations/{location}`
  string parent = 3 [(google.api.resource_reference) = {
    child_type: "cloudbuild.googleapis.com/BuildTrigger"
  }];

  // Required. ID of the project for which to configure automatic builds.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. `BuildTrigger` to create.
  BuildTrigger trigger = 2 [(google.api.field_behavior) = REQUIRED];
}

// Returns the `BuildTrigger` with the specified ID.
message GetBuildTriggerRequest {
  // The name of the `Trigger` to retrieve.
  // Format: `projects/{project}/locations/{location}/triggers/{trigger}`
  string name = 3 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/BuildTrigger"
  }];

  // Required. ID of the project that owns the trigger.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Identifier (`id` or `name`) of the `BuildTrigger` to get.
  string trigger_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to list existing `BuildTriggers`.
message ListBuildTriggersRequest {
  // The parent of the collection of `Triggers`.
  // Format: `projects/{project}/locations/{location}`
  string parent = 4 [(google.api.resource_reference) = {
    child_type: "cloudbuild.googleapis.com/BuildTrigger"
  }];

  // Required. ID of the project for which to list BuildTriggers.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Number of results to return in the list.
  int32 page_size = 2;

  // Token to provide to skip to a particular spot in the list.
  string page_token = 3;
}

// Response containing existing `BuildTriggers`.
message ListBuildTriggersResponse {
  // `BuildTriggers` for the project, sorted by `create_time` descending.
  repeated BuildTrigger triggers = 1;

  // Token to receive the next page of results.
  string next_page_token = 2;
}

// Request to delete a `BuildTrigger`.
message DeleteBuildTriggerRequest {
  // The name of the `Trigger` to delete.
  // Format: `projects/{project}/locations/{location}/triggers/{trigger}`
  string name = 3 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/BuildTrigger"
  }];

  // Required. ID of the project that owns the trigger.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the `BuildTrigger` to delete.
  string trigger_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to update an existing `BuildTrigger`.
message UpdateBuildTriggerRequest {
  // Required. ID of the project that owns the trigger.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the `BuildTrigger` to update.
  string trigger_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. `BuildTrigger` to update.
  BuildTrigger trigger = 3 [(google.api.field_behavior) = REQUIRED];

  // Update mask for the resource. If this is set,
  // the server will only update the fields specified in the field mask.
  // Otherwise, a full update of the mutable resource fields will be performed.
  google.protobuf.FieldMask update_mask = 5;
}

// Optional arguments to enable specific features of builds.
message BuildOptions {
  // Specifies the manner in which the build should be verified, if at all.
  //
  // If a verified build is requested, and any part of the process to generate
  // and upload provenance fails, the build will also fail.
  //
  // If the build does not request verification then that process may occur, but
  // is not guaranteed to. If it does occur and fails, the build will not fail.
  //
  // For more information, see [Viewing Build
  // Provenance](https://cloud.google.com/build/docs/securing-builds/view-build-provenance).
  enum VerifyOption {
    // Not a verifiable build (the default).
    NOT_VERIFIED = 0;

    // Build must be verified.
    VERIFIED = 1;
  }

  // Supported Compute Engine machine types.
  // For more information, see [Machine
  // types](https://cloud.google.com/compute/docs/machine-types).
  enum MachineType {
    // Standard machine type.
    UNSPECIFIED = 0;

    // Highcpu machine with 8 CPUs.
    N1_HIGHCPU_8 = 1;

    // Highcpu machine with 32 CPUs.
    N1_HIGHCPU_32 = 2;

    // Highcpu e2 machine with 8 CPUs.
    E2_HIGHCPU_8 = 5;

    // Highcpu e2 machine with 32 CPUs.
    E2_HIGHCPU_32 = 6;

    // E2 machine with 1 CPU.
    E2_MEDIUM = 7;
  }

  // Specifies the behavior when there is an error in the substitution checks.
  enum SubstitutionOption {
    // Fails the build if error in substitutions checks, like missing
    // a substitution in the template or in the map.
    MUST_MATCH = 0;

    // Do not fail the build if error in substitutions checks.
    ALLOW_LOOSE = 1;
  }

  // Specifies the behavior when writing build logs to Cloud Storage.
  enum LogStreamingOption {
    // Service may automatically determine build log streaming behavior.
    STREAM_DEFAULT = 0;

    // Build logs should be streamed to Cloud Storage.
    STREAM_ON = 1;

    // Build logs should not be streamed to Cloud Storage; they will be
    // written when the build is completed.
    STREAM_OFF = 2;
  }

  // Details about how a build should be executed on a `WorkerPool`.
  //
  // See [running builds in a private
  // pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-private-pool)
  // for more information.
  message PoolOption {
    // The `WorkerPool` resource to execute the build on.
    // You must have `cloudbuild.workerpools.use` on the project hosting the
    // WorkerPool.
    //
    // Format projects/{project}/locations/{location}/workerPools/{workerPoolId}
    string name = 1 [(google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }];
  }

  // Specifies the logging mode.
  enum LoggingMode {
    // The service determines the logging mode. The default is `LEGACY`. Do not
    // rely on the default logging behavior as it may change in the future.
    LOGGING_UNSPECIFIED = 0;

    // Build logs are stored in Cloud Logging and Cloud Storage.
    LEGACY = 1;

    // Build logs are stored in Cloud Storage.
    GCS_ONLY = 2;

    // This option is the same as CLOUD_LOGGING_ONLY.
    STACKDRIVER_ONLY = 3 [deprecated = true];

    // Build logs are stored in Cloud Logging. Selecting this option will not
    // allow [logs
    // streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).
    CLOUD_LOGGING_ONLY = 5;

    // Turn off all logging. No build logs will be captured.
    NONE = 4;
  }

  // Default Cloud Storage log bucket behavior options.
  enum DefaultLogsBucketBehavior {
    // Unspecified.
    DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED = 0;

    // Bucket is located in user-owned project in the same region as the
    // build. The builder service account must have access to create and write
    // to Cloud Storage buckets in the build project.
    REGIONAL_USER_OWNED_BUCKET = 1;

    // Bucket is located in a Google-owned project and is not regionalized.
    LEGACY_BUCKET = 2;
  }

  // Requested hash for SourceProvenance.
  repeated Hash.HashType source_provenance_hash = 1;

  // Requested verifiability options.
  VerifyOption requested_verify_option = 2;

  // Compute Engine machine type on which to run the build.
  MachineType machine_type = 3;

  // Requested disk size for the VM that runs the build. Note that this is *NOT*
  // "disk free"; some of the space will be used by the operating system and
  // build utilities. Also note that this is the minimum disk size that will be
  // allocated for the build -- the build may run with a larger disk than
  // requested. At present, the maximum disk size is 2000GB; builds that request
  // more than the maximum are rejected with an error.
  int64 disk_size_gb = 6;

  // Option to specify behavior when there is an error in the substitution
  // checks.
  //
  // NOTE: this is always set to ALLOW_LOOSE for triggered builds and cannot
  // be overridden in the build configuration file.
  SubstitutionOption substitution_option = 4;

  // Option to specify whether or not to apply bash style string
  // operations to the substitutions.
  //
  // NOTE: this is always enabled for triggered builds and cannot be
  // overridden in the build configuration file.
  bool dynamic_substitutions = 17;

  // Option to include built-in and custom substitutions as env variables
  // for all build steps.
  bool automap_substitutions = 22;

  // Option to define build log streaming behavior to Cloud
  // Storage.
  LogStreamingOption log_streaming_option = 5;

  // This field deprecated; please use `pool.name` instead.
  string worker_pool = 7 [deprecated = true];

  // Optional. Specification for execution on a `WorkerPool`.
  //
  // See [running builds in a private
  // pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-private-pool)
  // for more information.
  PoolOption pool = 19 [(google.api.field_behavior) = OPTIONAL];

  // Option to specify the logging mode, which determines if and where build
  // logs are stored.
  LoggingMode logging = 11;

  // A list of global environment variable definitions that will exist for all
  // build steps in this build. If a variable is defined in both globally and in
  // a build step, the variable will use the build step value.
  //
  // The elements are of the form "KEY=VALUE" for the environment variable "KEY"
  // being given the value "VALUE".
  repeated string env = 12;

  // A list of global environment variables, which are encrypted using a Cloud
  // Key Management Service crypto key. These values must be specified in the
  // build's `Secret`. These variables will be available to all build steps
  // in this build.
  repeated string secret_env = 13;

  // Global list of volumes to mount for ALL build steps
  //
  // Each volume is created as an empty volume prior to starting the build
  // process. Upon completion of the build, volumes and their contents are
  // discarded. Global volume names and paths cannot conflict with the volumes
  // defined a build step.
  //
  // Using a global volume in a build with only one step is not valid as
  // it is indicative of a build request with an incorrect configuration.
  repeated Volume volumes = 14;

  // Optional. Option to specify how default logs buckets are setup.
  DefaultLogsBucketBehavior default_logs_bucket_behavior = 21
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Option to specify whether structured logging is enabled.
  //
  // If true, JSON-formatted logs are parsed as structured logs.
  bool enable_structured_logging = 23 [(google.api.field_behavior) = OPTIONAL];
}

// ReceiveTriggerWebhookRequest [Experimental] is the request object accepted by
// the ReceiveTriggerWebhook method.
message ReceiveTriggerWebhookRequest {
  // The name of the `ReceiveTriggerWebhook` to retrieve.
  // Format: `projects/{project}/locations/{location}/triggers/{trigger}`
  string name = 5;

  // HTTP request body.
  google.api.HttpBody body = 1;

  // Project in which the specified trigger lives
  string project_id = 2;

  // Name of the trigger to run the payload against
  string trigger = 3;

  // Secret token used for authorization if an OAuth token isn't provided.
  string secret = 4;
}

// ReceiveTriggerWebhookResponse [Experimental] is the response object for the
// ReceiveTriggerWebhook method.
message ReceiveTriggerWebhookResponse {}

message GitHubEnterpriseConfig {
  option (google.api.resource) = {
    type: "cloudbuild.googleapis.com/GithubEnterpriseConfig"
    pattern: "projects/{project}/githubEnterpriseConfigs/{config}"
    pattern: "projects/{project}/locations/{location}/githubEnterpriseConfigs/{config}"
  };

  // Optional. The full resource name for the GitHubEnterpriseConfig
  // For example:
  // "projects/{$project_id}/locations/{$location_id}/githubEnterpriseConfigs/{$config_id}"
  string name = 7 [(google.api.field_behavior) = OPTIONAL];

  // The URL of the github enterprise host the configuration is for.
  string host_url = 3;

  // Required. The GitHub app id of the Cloud Build app on the GitHub Enterprise
  // server.
  int64 app_id = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. Time when the installation was associated with the project.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The key that should be attached to webhook calls to the ReceiveWebhook
  // endpoint.
  string webhook_key = 8;

  // Optional. The network to be used when reaching out to the GitHub
  // Enterprise server. The VPC network must be enabled for private
  // service connection. This should be set if the GitHub Enterprise server is
  // hosted on-premises and not reachable by public internet.
  // If this field is left empty, no network peering will occur and calls to
  // the GitHub Enterprise server will be made over the public internet.
  // Must be in the format
  // `projects/{project}/global/networks/{network}`, where {project}
  // is a project number or id and {network} is the name of a
  // VPC network in the project.
  string peered_network = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Names of secrets in Secret Manager.
  GitHubEnterpriseSecrets secrets = 10;

  // Name to display for this config.
  string display_name = 11;

  // Optional. SSL certificate to use for requests to GitHub Enterprise.
  string ssl_ca = 12 [(google.api.field_behavior) = OPTIONAL];
}

// GitHubEnterpriseSecrets represents the names of all necessary secrets in
// Secret Manager for a GitHub Enterprise server.
// Format is: projects/<project number>/secrets/<secret name>.
message GitHubEnterpriseSecrets {
  // The resource name for the private key secret version.
  string private_key_version_name = 5 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];

  // The resource name for the webhook secret secret version in Secret Manager.
  string webhook_secret_version_name = 6 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];

  // The resource name for the OAuth secret secret version in Secret Manager.
  string oauth_secret_version_name = 7 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];

  // The resource name for the OAuth client ID secret version in Secret Manager.
  string oauth_client_id_version_name = 8 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];
}

// Configuration for a `WorkerPool`.
//
// Cloud Build owns and maintains a pool of workers for general use and have no
// access to a project's private network. By default, builds submitted to
// Cloud Build will use a worker from this pool.
//
// If your build needs access to resources on a private network,
// create and use a `WorkerPool` to run your builds. Private `WorkerPool`s give
// your builds access to any single VPC network that you
// administer, including any on-prem resources connected to that VPC
// network. For an overview of private pools, see
// [Private pools
// overview](https://cloud.google.com/build/docs/private-pools/private-pools-overview).
message WorkerPool {
  option (google.api.resource) = {
    type: "cloudbuild.googleapis.com/WorkerPool"
    pattern: "projects/{project}/locations/{location}/workerPools/{worker_pool}"
    plural: "workerPools"
    singular: "workerPool"
    style: DECLARATIVE_FRIENDLY
  };

  // State of the `WorkerPool`.
  enum State {
    // State of the `WorkerPool` is unknown.
    STATE_UNSPECIFIED = 0;

    // `WorkerPool` is being created.
    CREATING = 1;

    // `WorkerPool` is running.
    RUNNING = 2;

    // `WorkerPool` is being deleted: cancelling builds and draining workers.
    DELETING = 3;

    // `WorkerPool` is deleted.
    DELETED = 4;

    // `WorkerPool` is being updated; new builds cannot be run.
    UPDATING = 5;
  }

  // Output only. The resource name of the `WorkerPool`, with format
  // `projects/{project}/locations/{location}/workerPools/{worker_pool}`.
  // The value of `{worker_pool}` is provided by `worker_pool_id` in
  // `CreateWorkerPool` request and the value of `{location}` is determined by
  // the endpoint accessed.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A user-specified, human-readable name for the `WorkerPool`. If provided,
  // this value must be 1-63 characters.
  string display_name = 2;

  // Output only. A unique identifier for the `WorkerPool`.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User specified annotations. See https://google.aip.dev/128#annotations
  // for more details such as format and size limitations.
  map<string, string> annotations = 4;

  // Output only. Time at which the request to create the `WorkerPool` was
  // received.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the request to update the `WorkerPool` was
  // received.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the request to delete the `WorkerPool` was
  // received.
  google.protobuf.Timestamp delete_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. `WorkerPool` state.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration for the `WorkerPool`.
  oneof config {
    // Legacy Private Pool configuration.
    PrivatePoolV1Config private_pool_v1_config = 12;
  }

  // Output only. Checksum computed by the server. May be sent on update and
  // delete requests to ensure that the client has an up-to-date value before
  // proceeding.
  string etag = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for a V1 `PrivatePool`.
message PrivatePoolV1Config {
  // Defines the configuration to be used for creating workers in
  // the pool.
  message WorkerConfig {
    // Machine type of a worker, such as `e2-medium`.
    // See [Worker pool config
    // file](https://cloud.google.com/build/docs/private-pools/worker-pool-config-file-schema).
    // If left blank, Cloud Build will use a sensible default.
    string machine_type = 1;

    // Size of the disk attached to the worker, in GB.
    // See [Worker pool config
    // file](https://cloud.google.com/build/docs/private-pools/worker-pool-config-file-schema).
    // Specify a value of up to 2000. If `0` is specified, Cloud Build will use
    // a standard disk size.
    int64 disk_size_gb = 2;
  }

  // Defines the network configuration for the pool.
  message NetworkConfig {
    // Defines the egress option for the pool.
    enum EgressOption {
      // If set, defaults to PUBLIC_EGRESS.
      EGRESS_OPTION_UNSPECIFIED = 0;

      // If set, workers are created without any public address, which prevents
      // network egress to public IPs unless a network proxy is configured.
      NO_PUBLIC_EGRESS = 1;

      // If set, workers are created with a public address which allows for
      // public internet egress.
      PUBLIC_EGRESS = 2;
    }

    // Required. Immutable. The network definition that the workers are peered
    // to. If this section is left empty, the workers will be peered to
    // `WorkerPool.project_id` on the service producer network. Must be in the
    // format `projects/{project}/global/networks/{network}`, where `{project}`
    // is a project number, such as `12345`, and `{network}` is the name of a
    // VPC network in the project. See
    // [Understanding network configuration
    // options](https://cloud.google.com/build/docs/private-pools/set-up-private-pool-environment)
    string peered_network = 1 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/Network"
      }
    ];

    // Option to configure network egress for the workers.
    EgressOption egress_option = 2;

    // Immutable. Subnet IP range within the peered network. This is specified
    // in CIDR notation with a slash and the subnet prefix size. You can
    // optionally specify an IP address before the subnet prefix value. e.g.
    // `***********/29` would specify an IP range starting at *********** with a
    // prefix size of 29 bits.
    // `/16` would specify a prefix size of 16 bits, with an automatically
    // determined IP within the peered VPC.
    // If unspecified, a value of `/24` will be used.
    string peered_network_ip_range = 3
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // Defines the Private Service Connect network configuration for the pool.
  message PrivateServiceConnect {
    // Required. Immutable. The network attachment that the worker network
    // interface is peered to. Must be in the format
    // `projects/{project}/regions/{region}/networkAttachments/{networkAttachment}`.
    // The region of network attachment must be the same as the worker pool.
    // See [Network
    // Attachments](https://cloud.google.com/vpc/docs/about-network-attachments)
    string network_attachment = 1 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/NetworkAttachment"
      }
    ];

    // Required. Immutable. Disable public IP on the primary network interface.
    //
    // If true, workers are created without any public address, which prevents
    // network egress to public IPs unless a network proxy is configured.
    // If false, workers are created with a public address which allows for
    // public internet egress. The public address only applies to traffic
    // through the primary network interface.
    // If `route_all_traffic` is set to true, all traffic will go through the
    // non-primary network interface, this boolean has no effect.
    bool public_ip_address_disabled = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.field_behavior) = IMMUTABLE
    ];

    // Immutable. Route all traffic through PSC interface. Enable this if you
    // want full control of traffic in the private pool. Configure Cloud NAT for
    // the subnet of network attachment if you need to access public Internet.
    //
    // If false, Only route private IPs, e.g. 10.0.0.0/8, **********/12, and
    // ***********/16 through PSC interface.
    bool route_all_traffic = 3 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Machine configuration for the workers in the pool.
  WorkerConfig worker_config = 1;

  // Network configuration for the pool.
  NetworkConfig network_config = 2;

  // Immutable. Private Service Connect(PSC) Network configuration for the pool.
  PrivateServiceConnect private_service_connect = 5
      [(google.api.field_behavior) = IMMUTABLE];
}

// Request to create a new `WorkerPool`.
message CreateWorkerPoolRequest {
  // Required. The parent resource where this worker pool will be created.
  // Format: `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. `WorkerPool` resource to create.
  WorkerPool worker_pool = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. The ID to use for the `WorkerPool`, which will become
  // the final component of the resource name.
  //
  // This value should be 1-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  string worker_pool_id = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // If set, validate the request and preview the response, but do not actually
  // post it.
  bool validate_only = 4;
}

// Request to get a `WorkerPool` with the specified name.
message GetWorkerPoolRequest {
  // Required. The name of the `WorkerPool` to retrieve.
  // Format: `projects/{project}/locations/{location}/workerPools/{workerPool}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }
  ];
}

// Request to delete a `WorkerPool`.
message DeleteWorkerPoolRequest {
  // Required. The name of the `WorkerPool` to delete.
  // Format:
  // `projects/{project}/locations/{location}/workerPools/{workerPool}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }
  ];

  // Optional. If provided, it must match the server's etag on the workerpool
  // for the request to be processed.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // If set to true, and the `WorkerPool` is not found, the request will succeed
  // but no action will be taken on the server.
  bool allow_missing = 3;

  // If set, validate the request and preview the response, but do not actually
  // post it.
  bool validate_only = 4;
}

// Request to update a `WorkerPool`.
message UpdateWorkerPoolRequest {
  // Required. The `WorkerPool` to update.
  //
  // The `name` field is used to identify the `WorkerPool` to update.
  // Format: `projects/{project}/locations/{location}/workerPools/{workerPool}`.
  WorkerPool worker_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // A mask specifying which fields in `worker_pool` to update.
  google.protobuf.FieldMask update_mask = 2;

  // If set, validate the request and preview the response, but do not actually
  // post it.
  bool validate_only = 4;
}

// Request to list `WorkerPool`s.
message ListWorkerPoolsRequest {
  // Required. The parent of the collection of `WorkerPools`.
  // Format: `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of `WorkerPool`s to return. The service may return
  // fewer than this value. If omitted, the server will use a sensible default.
  int32 page_size = 2;

  // A page token, received from a previous `ListWorkerPools` call. Provide this
  // to retrieve the subsequent page.
  string page_token = 3;
}

// Response containing existing `WorkerPools`.
message ListWorkerPoolsResponse {
  // `WorkerPools` for the specified project.
  repeated WorkerPool worker_pools = 1;

  // Continuation token used to page through large result sets. Provide this
  // value in a subsequent ListWorkerPoolsRequest to return the next page of
  // results.
  string next_page_token = 2;
}

// Metadata for the `CreateWorkerPool` operation.
message CreateWorkerPoolOperationMetadata {
  // The resource name of the `WorkerPool` to create.
  // Format:
  // `projects/{project}/locations/{location}/workerPools/{worker_pool}`.
  string worker_pool = 1 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/WorkerPool"
  }];

  // Time the operation was created.
  google.protobuf.Timestamp create_time = 2;

  // Time the operation was completed.
  google.protobuf.Timestamp complete_time = 3;
}

// Metadata for the `UpdateWorkerPool` operation.
message UpdateWorkerPoolOperationMetadata {
  // The resource name of the `WorkerPool` being updated.
  // Format:
  // `projects/{project}/locations/{location}/workerPools/{worker_pool}`.
  string worker_pool = 1 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/WorkerPool"
  }];

  // Time the operation was created.
  google.protobuf.Timestamp create_time = 2;

  // Time the operation was completed.
  google.protobuf.Timestamp complete_time = 3;
}

// Metadata for the `DeleteWorkerPool` operation.
message DeleteWorkerPoolOperationMetadata {
  // The resource name of the `WorkerPool` being deleted.
  // Format:
  // `projects/{project}/locations/{location}/workerPools/{worker_pool}`.
  string worker_pool = 1 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/WorkerPool"
  }];

  // Time the operation was created.
  google.protobuf.Timestamp create_time = 2;

  // Time the operation was completed.
  google.protobuf.Timestamp complete_time = 3;
}
