// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.cloudbuild.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/api/resource.proto";
import "google/devtools/cloudbuild/v2/cloudbuild.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CloudBuild.V2";
option go_package = "cloud.google.com/go/cloudbuild/apiv2/cloudbuildpb;cloudbuildpb";
option java_multiple_files = true;
option java_outer_classname = "RepositoryManagerProto";
option java_package = "com.google.cloudbuild.v2";
option objc_class_prefix = "GCB";
option php_namespace = "Google\\Cloud\\Build\\V2";
option ruby_package = "Google::Cloud::Build::V2";
option (google.api.resource_definition) = {
  type: "servicedirectory.googleapis.com/Service"
  pattern: "projects/{project}/locations/{location}/namespaces/{namespace}/services/{service}"
};

// Manages connections to source code repositories.
service RepositoryManager {
  option (google.api.default_host) = "cloudbuild.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Connection.
  rpc CreateConnection(CreateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/connections"
      body: "connection"
    };
    option (google.api.method_signature) = "parent,connection,connection_id";
    option (google.longrunning.operation_info) = {
      response_type: "Connection"
      metadata_type: "google.devtools.cloudbuild.v2.OperationMetadata"
    };
  }

  // Gets details of a single connection.
  rpc GetConnection(GetConnectionRequest) returns (Connection) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/connections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Connections in a given project and location.
  rpc ListConnections(ListConnectionsRequest)
      returns (ListConnectionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/connections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a single connection.
  rpc UpdateConnection(UpdateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{connection.name=projects/*/locations/*/connections/*}"
      body: "connection"
    };
    option (google.api.method_signature) = "connection,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Connection"
      metadata_type: "google.devtools.cloudbuild.v2.OperationMetadata"
    };
  }

  // Deletes a single connection.
  rpc DeleteConnection(DeleteConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/connections/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.cloudbuild.v2.OperationMetadata"
    };
  }

  // Creates a Repository.
  rpc CreateRepository(CreateRepositoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*/connections/*}/repositories"
      body: "repository"
    };
    option (google.api.method_signature) = "parent,repository,repository_id";
    option (google.longrunning.operation_info) = {
      response_type: "Repository"
      metadata_type: "google.devtools.cloudbuild.v2.OperationMetadata"
    };
  }

  // Creates multiple repositories inside a connection.
  rpc BatchCreateRepositories(BatchCreateRepositoriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*/connections/*}/repositories:batchCreate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
    option (google.longrunning.operation_info) = {
      response_type: "BatchCreateRepositoriesResponse"
      metadata_type: "google.devtools.cloudbuild.v2.OperationMetadata"
    };
  }

  // Gets details of a single repository.
  rpc GetRepository(GetRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/connections/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Repositories in a given connection.
  rpc ListRepositories(ListRepositoriesRequest)
      returns (ListRepositoriesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/connections/*}/repositories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a single repository.
  rpc DeleteRepository(DeleteRepositoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/connections/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.devtools.cloudbuild.v2.OperationMetadata"
    };
  }

  // Fetches read/write token of a given repository.
  rpc FetchReadWriteToken(FetchReadWriteTokenRequest)
      returns (FetchReadWriteTokenResponse) {
    option (google.api.http) = {
      post: "/v2/{repository=projects/*/locations/*/connections/*/repositories/*}:accessReadWriteToken"
      body: "*"
    };
    option (google.api.method_signature) = "repository";
  }

  // Fetches read token of a given repository.
  rpc FetchReadToken(FetchReadTokenRequest) returns (FetchReadTokenResponse) {
    option (google.api.http) = {
      post: "/v2/{repository=projects/*/locations/*/connections/*/repositories/*}:accessReadToken"
      body: "*"
    };
    option (google.api.method_signature) = "repository";
  }

  // FetchLinkableRepositories get repositories from SCM that are
  // accessible and could be added to the connection.
  rpc FetchLinkableRepositories(FetchLinkableRepositoriesRequest)
      returns (FetchLinkableRepositoriesResponse) {
    option (google.api.http) = {
      get: "/v2/{connection=projects/*/locations/*/connections/*}:fetchLinkableRepositories"
    };
  }

  // Fetch the list of branches or tags for a given repository.
  rpc FetchGitRefs(FetchGitRefsRequest) returns (FetchGitRefsResponse) {
    option (google.api.http) = {
      get: "/v2/{repository=projects/*/locations/*/connections/*/repositories/*}:fetchGitRefs"
    };
    option (google.api.method_signature) = "repository";
  }
}

// A connection to a SCM like GitHub, GitHub Enterprise, Bitbucket Data Center,
// Bitbucket Cloud or GitLab.
message Connection {
  option (google.api.resource) = {
    type: "cloudbuild.googleapis.com/Connection"
    pattern: "projects/{project}/locations/{location}/connections/{connection}"
    plural: "connections"
    singular: "connection"
    style: DECLARATIVE_FRIENDLY
  };

  // Immutable. The resource name of the connection, in the format
  // `projects/{project}/locations/{location}/connections/{connection_id}`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Server assigned timestamp for when the connection was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server assigned timestamp for when the connection was updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration for the connection depending on the type of provider.
  oneof connection_config {
    // Configuration for connections to github.com.
    GitHubConfig github_config = 5;

    // Configuration for connections to an instance of GitHub Enterprise.
    GitHubEnterpriseConfig github_enterprise_config = 6;

    // Configuration for connections to gitlab.com or an instance of GitLab
    // Enterprise.
    GitLabConfig gitlab_config = 7;

    // Configuration for connections to Bitbucket Data Center.
    BitbucketDataCenterConfig bitbucket_data_center_config = 8;

    // Configuration for connections to Bitbucket Cloud.
    BitbucketCloudConfig bitbucket_cloud_config = 9;
  }

  // Output only. Installation state of the Connection.
  InstallationState installation_state = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // If disabled is set to true, functionality is disabled for this connection.
  // Repository based API methods and webhooks processing for repositories in
  // this connection will be disabled.
  bool disabled = 13;

  // Output only. Set to true when the connection is being set up or updated in
  // the background.
  bool reconciling = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Allows clients to store small amounts of arbitrary data.
  map<string, string> annotations = 15;

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 16;
}

// Describes stage and necessary actions to be taken by the
// user to complete the installation. Used for GitHub and GitHub Enterprise
// based connections.
message InstallationState {
  // Stage of the installation process.
  enum Stage {
    // No stage specified.
    STAGE_UNSPECIFIED = 0;

    // Only for GitHub Enterprise. An App creation has been requested.
    // The user needs to confirm the creation in their GitHub enterprise host.
    PENDING_CREATE_APP = 1;

    // User needs to authorize the GitHub (or Enterprise) App via OAuth.
    PENDING_USER_OAUTH = 2;

    // User needs to follow the link to install the GitHub (or Enterprise) App.
    PENDING_INSTALL_APP = 3;

    // Installation process has been completed.
    COMPLETE = 10;
  }

  // Output only. Current step of the installation process.
  Stage stage = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Message of what the user should do next to continue the
  // installation. Empty string if the installation is already complete.
  string message = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Link to follow for next action. Empty string if the
  // installation is already complete.
  string action_uri = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for FetchLinkableRepositories.
message FetchLinkableRepositoriesRequest {
  // Required. The name of the Connection.
  // Format: `projects/*/locations/*/connections/*`.
  string connection = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Connection"
    }
  ];

  // Number of results to return in the list. Default to 20.
  int32 page_size = 2;

  // Page start.
  string page_token = 3;
}

// Response message for FetchLinkableRepositories.
message FetchLinkableRepositoriesResponse {
  // repositories ready to be created.
  repeated Repository repositories = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// Configuration for connections to github.com.
message GitHubConfig {
  // OAuth credential of the account that authorized the Cloud Build GitHub App.
  // It is recommended to use a robot account instead of a human user account.
  // The OAuth token must be tied to the Cloud Build GitHub App.
  OAuthCredential authorizer_credential = 1;

  // GitHub App installation id.
  int64 app_installation_id = 2;
}

// Configuration for connections to an instance of GitHub Enterprise.
message GitHubEnterpriseConfig {
  // Required. The URI of the GitHub Enterprise host this connection is for.
  string host_uri = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. API Key used for authentication of webhook events.
  string api_key = 12 [(google.api.field_behavior) = REQUIRED];

  // Id of the GitHub App created from the manifest.
  int64 app_id = 2;

  // The URL-friendly name of the GitHub App.
  string app_slug = 13;

  // SecretManager resource containing the private key of the GitHub App,
  // formatted as `projects/*/secrets/*/versions/*`.
  string private_key_secret_version = 4 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];

  // SecretManager resource containing the webhook secret of the GitHub App,
  // formatted as `projects/*/secrets/*/versions/*`.
  string webhook_secret_secret_version = 5 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];

  // ID of the installation of the GitHub App.
  int64 app_installation_id = 9;

  // Configuration for using Service Directory to privately connect to a GitHub
  // Enterprise server. This should only be set if the GitHub Enterprise server
  // is hosted on-premises and not reachable by public internet. If this field
  // is left empty, calls to the GitHub Enterprise server will be made over the
  // public internet.
  ServiceDirectoryConfig service_directory_config = 10;

  // SSL certificate to use for requests to GitHub Enterprise.
  string ssl_ca = 11;

  // Output only. GitHub Enterprise version installed at the host_uri.
  string server_version = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for connections to gitlab.com or an instance of GitLab
// Enterprise.
message GitLabConfig {
  // The URI of the GitLab Enterprise host this connection is for.
  // If not specified, the default value is https://gitlab.com.
  string host_uri = 1;

  // Required. Immutable. SecretManager resource containing the webhook secret
  // of a GitLab Enterprise project, formatted as
  // `projects/*/secrets/*/versions/*`.
  string webhook_secret_secret_version = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Required. A GitLab personal access token with the minimum `read_api` scope
  // access.
  UserCredential read_authorizer_credential = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. A GitLab personal access token with the `api` scope access.
  UserCredential authorizer_credential = 4
      [(google.api.field_behavior) = REQUIRED];

  // Configuration for using Service Directory to privately connect to a GitLab
  // Enterprise server. This should only be set if the GitLab Enterprise server
  // is hosted on-premises and not reachable by public internet. If this field
  // is left empty, calls to the GitLab Enterprise server will be made over the
  // public internet.
  ServiceDirectoryConfig service_directory_config = 5;

  // SSL certificate to use for requests to GitLab Enterprise.
  string ssl_ca = 6;

  // Output only. Version of the GitLab Enterprise server running on the
  // `host_uri`.
  string server_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for connections to Bitbucket Data Center.
message BitbucketDataCenterConfig {
  // Required. The URI of the Bitbucket Data Center instance or cluster this
  // connection is for.
  string host_uri = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. SecretManager resource containing the webhook secret
  // used to verify webhook events, formatted as
  // `projects/*/secrets/*/versions/*`.
  string webhook_secret_secret_version = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Required. A http access token with the `REPO_READ` access.
  UserCredential read_authorizer_credential = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. A http access token with the `REPO_ADMIN` scope access.
  UserCredential authorizer_credential = 4
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Configuration for using Service Directory to privately connect to
  // a Bitbucket Data Center. This should only be set if the Bitbucket Data
  // Center is hosted on-premises and not reachable by public internet. If this
  // field is left empty, calls to the Bitbucket Data Center will be made over
  // the public internet.
  ServiceDirectoryConfig service_directory_config = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. SSL certificate to use for requests to the Bitbucket Data Center.
  string ssl_ca = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Version of the Bitbucket Data Center running on the
  // `host_uri`.
  string server_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for connections to Bitbucket Cloud.
message BitbucketCloudConfig {
  // Required. The Bitbucket Cloud Workspace ID to be connected to Google Cloud
  // Platform.
  string workspace = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. SecretManager resource containing the webhook secret used to
  // verify webhook events, formatted as `projects/*/secrets/*/versions/*`.
  string webhook_secret_secret_version = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Required. An access token with the `repository` access. It can be either a
  // workspace, project or repository access token. It's recommended to use a
  // system account to generate the credentials.
  UserCredential read_authorizer_credential = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. An access token with the `webhook`, `repository`,
  // `repository:admin` and `pullrequest` scope access. It can be either a
  // workspace, project or repository access token. It's recommended to use a
  // system account to generate these credentials.
  UserCredential authorizer_credential = 4
      [(google.api.field_behavior) = REQUIRED];
}

// ServiceDirectoryConfig represents Service Directory configuration for a
// connection.
message ServiceDirectoryConfig {
  // Required. The Service Directory service name.
  // Format:
  // projects/{project}/locations/{location}/namespaces/{namespace}/services/{service}.
  string service = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "servicedirectory.googleapis.com/Service"
    }
  ];
}

// A repository associated to a parent connection.
message Repository {
  option (google.api.resource) = {
    type: "cloudbuild.googleapis.com/Repository"
    pattern: "projects/{project}/locations/{location}/connections/{connection}/repositories/{repository}"
    plural: "repositories"
    singular: "repository"
    style: DECLARATIVE_FRIENDLY
  };

  // Immutable. Resource name of the repository, in the format
  // `projects/*/locations/*/connections/*/repositories/*`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. Git Clone HTTPS URI.
  string remote_uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Server assigned timestamp for when the connection was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server assigned timestamp for when the connection was updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Allows clients to store small amounts of arbitrary data.
  map<string, string> annotations = 6;

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 7;

  // Output only. External ID of the webhook created for the repository.
  string webhook_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents an OAuth token of the account that authorized the Connection,
// and associated metadata.
message OAuthCredential {
  // A SecretManager resource containing the OAuth token that authorizes
  // the Cloud Build connection. Format: `projects/*/secrets/*/versions/*`.
  string oauth_token_secret_version = 1 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];

  // Output only. The username associated to this token.
  string username = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents a personal access token that authorized the Connection,
// and associated metadata.
message UserCredential {
  // Required. A SecretManager resource containing the user token that
  // authorizes the Cloud Build connection. Format:
  // `projects/*/secrets/*/versions/*`.
  string user_token_secret_version = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Output only. The username associated to this token.
  string username = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for creating a Connection
message CreateConnectionRequest {
  // Required. Project and location where the connection will be created.
  // Format: `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudbuild.googleapis.com/Connection"
    }
  ];

  // Required. The Connection to create.
  Connection connection = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the Connection, which will become the final
  // component of the Connection's resource name. Names must be unique
  // per-project per-location. Allows alphanumeric characters and any of
  // -._~%!$&'()*+,;=@.
  string connection_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Message for getting the details of a Connection.
message GetConnectionRequest {
  // Required. The name of the Connection to retrieve.
  // Format: `projects/*/locations/*/connections/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Connection"
    }
  ];
}

// Message for requesting list of Connections.
message ListConnectionsRequest {
  // Required. The parent, which owns this collection of Connections.
  // Format: `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudbuild.googleapis.com/Connection"
    }
  ];

  // Number of results to return in the list.
  int32 page_size = 2;

  // Page start.
  string page_token = 3;
}

// Message for response to listing Connections.
message ListConnectionsResponse {
  // The list of Connections.
  repeated Connection connections = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// Message for updating a Connection.
message UpdateConnectionRequest {
  // Required. The Connection to update.
  Connection connection = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;

  // If set to true, and the connection is not found a new connection
  // will be created. In this situation `update_mask` is ignored.
  // The creation will succeed only if the input connection has all the
  // necessary information (e.g a github_config with both  user_oauth_token and
  // installation_id properties).
  bool allow_missing = 3;

  // The current etag of the connection.
  // If an etag is provided and does not match the current etag of the
  // connection, update will be blocked and an ABORTED error will be returned.
  string etag = 4;
}

// Message for deleting a Connection.
message DeleteConnectionRequest {
  // Required. The name of the Connection to delete.
  // Format: `projects/*/locations/*/connections/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Connection"
    }
  ];

  // The current etag of the connection.
  // If an etag is provided and does not match the current etag of the
  // connection, deletion will be blocked and an ABORTED error will be returned.
  string etag = 2;

  // If set, validate the request, but do not actually post it.
  bool validate_only = 3;
}

// Message for creating a Repository.
message CreateRepositoryRequest {
  // Required. The connection to contain the repository. If the request is part
  // of a BatchCreateRepositoriesRequest, this field should be empty or match
  // the parent specified there.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Connection"
    }
  ];

  // Required. The repository to create.
  Repository repository = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the repository, which will become the final
  // component of the repository's resource name. This ID should be unique in
  // the connection. Allows alphanumeric characters and any of
  // -._~%!$&'()*+,;=@.
  string repository_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Message for creating repositoritories in batch.
message BatchCreateRepositoriesRequest {
  // Required. The connection to contain all the repositories being created.
  // Format: projects/*/locations/*/connections/*
  // The parent field in the CreateRepositoryRequest messages
  // must either be empty or match this field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Connection"
    }
  ];

  // Required. The request messages specifying the repositories to create.
  repeated CreateRepositoryRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Message for response of creating repositories in batch.
message BatchCreateRepositoriesResponse {
  // Repository resources created.
  repeated Repository repositories = 1;
}

// Message for getting the details of a Repository.
message GetRepositoryRequest {
  // Required. The name of the Repository to retrieve.
  // Format: `projects/*/locations/*/connections/*/repositories/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Repository"
    }
  ];
}

// Message for requesting list of Repositories.
message ListRepositoriesRequest {
  // Required. The parent, which owns this collection of Repositories.
  // Format: `projects/*/locations/*/connections/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudbuild.googleapis.com/Repository"
    }
  ];

  // Number of results to return in the list.
  int32 page_size = 2;

  // Page start.
  string page_token = 3;

  // A filter expression that filters resources listed in the response.
  // Expressions must follow API improvement proposal
  // [AIP-160](https://google.aip.dev/160). e.g.
  // `remote_uri:"https://github.com*"`.
  string filter = 4;
}

// Message for response to listing Repositories.
message ListRepositoriesResponse {
  // The list of Repositories.
  repeated Repository repositories = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// Message for deleting a Repository.
message DeleteRepositoryRequest {
  // Required. The name of the Repository to delete.
  // Format: `projects/*/locations/*/connections/*/repositories/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Repository"
    }
  ];

  // The current etag of the repository.
  // If an etag is provided and does not match the current etag of the
  // repository, deletion will be blocked and an ABORTED error will be returned.
  string etag = 2;

  // If set, validate the request, but do not actually post it.
  bool validate_only = 3;
}

// Message for fetching SCM read/write token.
message FetchReadWriteTokenRequest {
  // Required. The resource name of the repository in the format
  // `projects/*/locations/*/connections/*/repositories/*`.
  string repository = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Repository"
    }
  ];
}

// Message for fetching SCM read token.
message FetchReadTokenRequest {
  // Required. The resource name of the repository in the format
  // `projects/*/locations/*/connections/*/repositories/*`.
  string repository = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Repository"
    }
  ];
}

// Message for responding to get read token.
message FetchReadTokenResponse {
  // The token content.
  string token = 1;

  // Expiration timestamp. Can be empty if unknown or non-expiring.
  google.protobuf.Timestamp expiration_time = 2;
}

// Message for responding to get read/write token.
message FetchReadWriteTokenResponse {
  // The token content.
  string token = 1;

  // Expiration timestamp. Can be empty if unknown or non-expiring.
  google.protobuf.Timestamp expiration_time = 2;
}

// RPC request object accepted by the ProcessWebhook RPC method.
message ProcessWebhookRequest {
  // Required. Project and location where the webhook will be received.
  // Format: `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudbuild.googleapis.com/Connection"
    }
  ];

  // HTTP request body.
  google.api.HttpBody body = 2;

  // Arbitrary additional key to find the maching repository for a webhook event
  // if needed.
  string webhook_key = 3;
}

// Request for fetching git refs
message FetchGitRefsRequest {
  // Type of refs
  enum RefType {
    // No type specified.
    REF_TYPE_UNSPECIFIED = 0;

    // To fetch tags.
    TAG = 1;

    // To fetch branches.
    BRANCH = 2;
  }

  // Required. The resource name of the repository in the format
  // `projects/*/locations/*/connections/*/repositories/*`.
  string repository = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Repository"
    }
  ];

  // Type of refs to fetch
  RefType ref_type = 2;
}

// Response for fetching git refs
message FetchGitRefsResponse {
  // Name of the refs fetched.
  repeated string ref_names = 1;
}
