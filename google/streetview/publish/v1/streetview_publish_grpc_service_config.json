{"methodConfig": [{"name": [{"service": "google.streetview.publish.v1.StreetViewPublishService", "method": "BatchUpdatePhotos"}, {"service": "google.streetview.publish.v1.StreetViewPublishService", "method": "CreatePhoto"}, {"service": "google.streetview.publish.v1.StreetViewPublishService", "method": "StartUpload"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": []}}, {"name": [{"service": "google.streetview.publish.v1.StreetViewPublishService", "method": "GetPhoto"}, {"service": "google.streetview.publish.v1.StreetViewPublishService", "method": "BatchGetPhotos"}, {"service": "google.streetview.publish.v1.StreetViewPublishService", "method": "ListPhotos"}, {"service": "google.streetview.publish.v1.StreetViewPublishService", "method": "UpdatePhoto"}, {"service": "google.streetview.publish.v1.StreetViewPublishService", "method": "DeletePhoto"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}