type: google.api.Service
config_version: 3
name: streetviewpublish.googleapis.com
title: Street View Publish API

apis:
- name: google.streetview.publish.v1.StreetViewPublishService

documentation:
  summary: |-
    Publishes 360 photos to Google Maps, along with position, orientation, and
    connectivity metadata. Apps can offer an interface for positioning,
    connecting, and uploading user-generated Street View images.
  overview: |-
    An API to contribute 360 photos to Google Street View. The API also allows
    the users to connect photos, update metadata of the photos, generate photo
    collections, and delete photos.

authentication:
  rules:
  - selector: 'google.streetview.publish.v1.StreetViewPublishService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/streetviewpublish

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=316970&template=1019235
  documentation_uri: https://developers.google.com/streetview/publish
  api_short_name: streetviewpublish
  github_label: 'api: streetviewpublish'
  doc_tag_prefix: streetviewpublish
  organization: GEO
  library_settings:
  - version: google.streetview.publish.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://developers.google.com/streetview/publish/reference/rest
