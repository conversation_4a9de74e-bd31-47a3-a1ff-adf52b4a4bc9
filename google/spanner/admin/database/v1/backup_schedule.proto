// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.spanner.admin.database.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/spanner/admin/database/v1/backup.proto";

option csharp_namespace = "Google.Cloud.Spanner.Admin.Database.V1";
option go_package = "cloud.google.com/go/spanner/admin/database/apiv1/databasepb;databasepb";
option java_multiple_files = true;
option java_outer_classname = "BackupScheduleProto";
option java_package = "com.google.spanner.admin.database.v1";
option php_namespace = "Google\\Cloud\\Spanner\\Admin\\Database\\V1";
option ruby_package = "Google::Cloud::Spanner::Admin::Database::V1";

// Defines specifications of the backup schedule.
message BackupScheduleSpec {
  // Required.
  oneof schedule_spec {
    // Cron style schedule specification.
    CrontabSpec cron_spec = 1;
  }
}

// BackupSchedule expresses the automated backup creation specification for a
// Spanner database.
// Next ID: 10
message BackupSchedule {
  option (google.api.resource) = {
    type: "spanner.googleapis.com/BackupSchedule"
    pattern: "projects/{project}/instances/{instance}/databases/{database}/backupSchedules/{schedule}"
    plural: "backupSchedules"
    singular: "backupSchedule"
  };

  // Identifier. Output only for the
  // [CreateBackupSchedule][DatabaseAdmin.CreateBackupSchededule] operation.
  // Required for the
  // [UpdateBackupSchedule][google.spanner.admin.database.v1.DatabaseAdmin.UpdateBackupSchedule]
  // operation. A globally unique identifier for the backup schedule which
  // cannot be changed. Values are of the form
  // `projects/<project>/instances/<instance>/databases/<database>/backupSchedules/[a-z][a-z0-9_\-]*[a-z0-9]`
  // The final segment of the name must be between 2 and 60 characters in
  // length.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The schedule specification based on which the backup creations
  // are triggered.
  BackupScheduleSpec spec = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The retention duration of a backup that must be at least 6 hours
  // and at most 366 days. The backup is eligible to be automatically deleted
  // once the retention period has elapsed.
  google.protobuf.Duration retention_duration = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The encryption configuration that will be used to encrypt the
  // backup. If this field is not specified, the backup will use the same
  // encryption configuration as the database.
  CreateBackupEncryptionConfig encryption_config = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Backup type spec determines the type of backup that is created by
  // the backup schedule. Currently, only full backups are supported.
  oneof backup_type_spec {
    // The schedule creates only full backups.
    FullBackupSpec full_backup_spec = 7;

    // The schedule creates incremental backup chains.
    IncrementalBackupSpec incremental_backup_spec = 8;
  }

  // Output only. The timestamp at which the schedule was last updated.
  // If the schedule has never been updated, this field contains the timestamp
  // when the schedule was first created.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CrontabSpec can be used to specify the version time and frequency at
// which the backup should be created.
message CrontabSpec {
  // Required. Textual representation of the crontab. User can customize the
  // backup frequency and the backup version time using the cron
  // expression. The version time must be in UTC timezone.
  //
  // The backup will contain an externally consistent copy of the
  // database at the version time. Allowed frequencies are 12 hour, 1 day,
  // 1 week and 1 month. Examples of valid cron specifications:
  //   * `0 2/12 * * * ` : every 12 hours at (2, 14) hours past midnight in UTC.
  //   * `0 2,14 * * * ` : every 12 hours at (2,14) hours past midnight in UTC.
  //   * `0 2 * * * `    : once a day at 2 past midnight in UTC.
  //   * `0 2 * * 0 `    : once a week every Sunday at 2 past midnight in UTC.
  //   * `0 2 8 * * `    : once a month on 8th day at 2 past midnight in UTC.
  string text = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The time zone of the times in `CrontabSpec.text`. Currently
  // only UTC is supported.
  string time_zone = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Schedule backups will contain an externally consistent copy
  // of the database at the version time specified in
  // `schedule_spec.cron_spec`. However, Spanner may not initiate the creation
  // of the scheduled backups at that version time. Spanner will initiate
  // the creation of scheduled backups within the time window bounded by the
  // version_time specified in `schedule_spec.cron_spec` and version_time +
  // `creation_window`.
  google.protobuf.Duration creation_window = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request for
// [CreateBackupSchedule][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackupSchedule].
message CreateBackupScheduleRequest {
  // Required. The name of the database that this backup schedule applies to.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Database"
    }
  ];

  // Required. The Id to use for the backup schedule. The `backup_schedule_id`
  // appended to `parent` forms the full backup schedule name of the form
  // `projects/<project>/instances/<instance>/databases/<database>/backupSchedules/<backup_schedule_id>`.
  string backup_schedule_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The backup schedule to create.
  BackupSchedule backup_schedule = 3 [(google.api.field_behavior) = REQUIRED];
}

// The request for
// [GetBackupSchedule][google.spanner.admin.database.v1.DatabaseAdmin.GetBackupSchedule].
message GetBackupScheduleRequest {
  // Required. The name of the schedule to retrieve.
  // Values are of the form
  // `projects/<project>/instances/<instance>/databases/<database>/backupSchedules/<backup_schedule_id>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/BackupSchedule"
    }
  ];
}

// The request for
// [DeleteBackupSchedule][google.spanner.admin.database.v1.DatabaseAdmin.DeleteBackupSchedule].
message DeleteBackupScheduleRequest {
  // Required. The name of the schedule to delete.
  // Values are of the form
  // `projects/<project>/instances/<instance>/databases/<database>/backupSchedules/<backup_schedule_id>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/BackupSchedule"
    }
  ];
}

// The request for
// [ListBackupSchedules][google.spanner.admin.database.v1.DatabaseAdmin.ListBackupSchedules].
message ListBackupSchedulesRequest {
  // Required. Database is the parent resource whose backup schedules should be
  // listed. Values are of the form
  // projects/<project>/instances/<instance>/databases/<database>
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Database"
    }
  ];

  // Optional. Number of backup schedules to be returned in the response. If 0
  // or less, defaults to the server's maximum allowed page size.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If non-empty, `page_token` should contain a
  // [next_page_token][google.spanner.admin.database.v1.ListBackupSchedulesResponse.next_page_token]
  // from a previous
  // [ListBackupSchedulesResponse][google.spanner.admin.database.v1.ListBackupSchedulesResponse]
  // to the same `parent`.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response for
// [ListBackupSchedules][google.spanner.admin.database.v1.DatabaseAdmin.ListBackupSchedules].
message ListBackupSchedulesResponse {
  // The list of backup schedules for a database.
  repeated BackupSchedule backup_schedules = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListBackupSchedules][google.spanner.admin.database.v1.DatabaseAdmin.ListBackupSchedules]
  // call to fetch more of the schedules.
  string next_page_token = 2;
}

// The request for
// [UpdateBackupScheduleRequest][google.spanner.admin.database.v1.DatabaseAdmin.UpdateBackupSchedule].
message UpdateBackupScheduleRequest {
  // Required. The backup schedule to update. `backup_schedule.name`, and the
  // fields to be updated as specified by `update_mask` are required. Other
  // fields are ignored.
  BackupSchedule backup_schedule = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying which fields in the BackupSchedule resource
  // should be updated. This mask is relative to the BackupSchedule resource,
  // not to the request message. The field mask must always be
  // specified; this prevents any future fields from being erased
  // accidentally.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}
