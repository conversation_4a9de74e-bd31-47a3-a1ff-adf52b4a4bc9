type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.spanner.admin.database.v1
interfaces:
- name: google.spanner.admin.database.v1.DatabaseAdmin
  methods:
  - name: CreateDatabase
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 86400000
  - name: UpdateDatabaseDdl
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 86400000
  - name: CreateBackup
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 172800000
  - name: RestoreDatabase
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 86400000
