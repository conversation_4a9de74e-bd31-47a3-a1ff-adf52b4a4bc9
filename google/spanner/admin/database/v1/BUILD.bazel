# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "database_proto",
    srcs = [
        "backup.proto",
        "backup_schedule.proto",
        "common.proto",
        "spanner_database_admin.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "database_proto_with_info",
    deps = [
        ":database_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "database_java_proto",
    deps = [":database_proto"],
)

java_grpc_library(
    name = "database_java_grpc",
    srcs = [":database_proto"],
    deps = [":database_java_proto"],
)

java_gapic_library(
    name = "database_java_gapic",
    srcs = [":database_proto_with_info"],
    gapic_yaml = "spanner_admin_database_gapic.yaml",
    grpc_service_config = "spanner_admin_database_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    test_deps = [
        ":database_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":database_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "database_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.spanner.admin.database.v1.DatabaseAdminClientHttpJsonTest",
        "com.google.cloud.spanner.admin.database.v1.DatabaseAdminClientTest",
    ],
    runtime_deps = [":database_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-admin-database-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":database_java_gapic",
        ":database_java_grpc",
        ":database_java_proto",
        ":database_proto",
    ],
)

go_proto_library(
    name = "database_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/spanner/admin/database/apiv1/databasepb",
    protos = [":database_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "database_go_gapic",
    srcs = [":database_proto_with_info"],
    grpc_service_config = "spanner_admin_database_grpc_service_config.json",
    importpath = "cloud.google.com/go/spanner/admin/database/apiv1;database",
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    deps = [
        ":database_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-admin-database-v1-go",
    deps = [
        ":database_go_gapic",
        ":database_go_gapic_srcjar-snippets.srcjar",
        ":database_go_gapic_srcjar-test.srcjar",
        ":database_go_proto",
    ],
)

py_gapic_library(
    name = "database_py_gapic",
    srcs = [":database_proto"],
    grpc_service_config = "spanner_admin_database_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=spanner_admin_database",
    ],
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "database_py_gapic_test",
    srcs = [
        "database_py_gapic_pytest.py",
        "database_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":database_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "admin-database-v1-py",
    deps = [
        ":database_py_gapic",
    ],
)

php_proto_library(
    name = "database_php_proto",
    deps = [":database_proto"],
)

php_gapic_library(
    name = "database_php_gapic",
    srcs = [":database_proto_with_info"],
    gapic_yaml = "spanner_admin_database_gapic.yaml",
    grpc_service_config = "spanner_admin_database_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    migration_mode = "MIGRATING",
    deps = [":database_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-admin-database-v1-php",
    deps = [
        ":database_php_gapic",
        ":database_php_proto",
    ],
)

nodejs_gapic_library(
    name = "database_nodejs_gapic",
    package_name = "@google-cloud/spanner",
    src = ":database_proto_with_info",
    extra_protoc_parameters = [
        "metadata",
        "template=typescript_gapic",
    ],
    grpc_service_config = "spanner_admin_database_grpc_service_config.json",
    handwritten_layer = True,
    package = "google.spanner.admin.database.v1",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "admin-database-v1-nodejs",
    deps = [
        ":database_nodejs_gapic",
        ":database_proto",
    ],
)

ruby_proto_library(
    name = "database_ruby_proto",
    deps = [":database_proto"],
)

ruby_grpc_library(
    name = "database_ruby_grpc",
    srcs = [":database_proto"],
    deps = [":database_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "database_ruby_gapic",
    srcs = [":database_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-spanner-admin-database-v1",
        "ruby-cloud-env-prefix=SPANNER",
        "ruby-cloud-product-url=https://cloud.google.com/spanner",
        "ruby-cloud-api-id=spanner.googleapis.com",
        "ruby-cloud-api-shortname=spanner",
        "ruby-cloud-wrapper-gem-override=google-cloud-spanner",
    ],
    grpc_service_config = "spanner_admin_database_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Spanner is a managed, mission-critical, globally consistent and scalable relational database service.",
    ruby_cloud_title = "Cloud Spanner Database Admin V1",
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    deps = [
        ":database_ruby_grpc",
        ":database_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-admin-database-v1-ruby",
    deps = [
        ":database_ruby_gapic",
        ":database_ruby_grpc",
        ":database_ruby_proto",
    ],
)

csharp_proto_library(
    name = "database_csharp_proto",
    deps = [":database_proto"],
)

csharp_grpc_library(
    name = "database_csharp_grpc",
    srcs = [":database_proto"],
    deps = [":database_csharp_proto"],
)

csharp_gapic_library(
    name = "database_csharp_gapic",
    srcs = [":database_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "spanner_admin_database_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    deps = [
        ":database_csharp_grpc",
        ":database_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-admin-database-v1-csharp",
    deps = [
        ":database_csharp_gapic",
        ":database_csharp_grpc",
        ":database_csharp_proto",
    ],
)

cc_proto_library(
    name = "database_cc_proto",
    deps = [":database_proto"],
)

cc_grpc_library(
    name = "database_cc_grpc",
    srcs = [":database_proto"],
    grpc_only = True,
    deps = [":database_cc_proto"],
)
