// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.spanner.admin.database.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Spanner.Admin.Database.V1";
option go_package = "cloud.google.com/go/spanner/admin/database/apiv1/databasepb;databasepb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.spanner.admin.database.v1";
option php_namespace = "Google\\Cloud\\Spanner\\Admin\\Database\\V1";
option ruby_package = "Google::Cloud::Spanner::Admin::Database::V1";
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKeyVersion"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}/cryptoKeyVersions/{crypto_key_version}"
};

// Encapsulates progress related information for a Cloud Spanner long
// running operation.
message OperationProgress {
  // Percent completion of the operation.
  // Values are between 0 and 100 inclusive.
  int32 progress_percent = 1;

  // Time the request was received.
  google.protobuf.Timestamp start_time = 2;

  // If set, the time at which this operation failed or was completed
  // successfully.
  google.protobuf.Timestamp end_time = 3;
}

// Encryption configuration for a Cloud Spanner database.
message EncryptionConfig {
  // The Cloud KMS key to be used for encrypting and decrypting
  // the database. Values are of the form
  // `projects/<project>/locations/<location>/keyRings/<key_ring>/cryptoKeys/<kms_key_name>`.
  string kms_key_name = 2 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // Specifies the KMS configuration for the one or more keys used to encrypt
  // the database. Values are of the form
  // `projects/<project>/locations/<location>/keyRings/<key_ring>/cryptoKeys/<kms_key_name>`.
  //
  // The keys referenced by kms_key_names must fully cover all
  // regions of the database instance configuration. Some examples:
  // * For single region database instance configs, specify a single regional
  // location KMS key.
  // * For multi-regional database instance configs of type GOOGLE_MANAGED,
  // either specify a multi-regional location KMS key or multiple regional
  // location KMS keys that cover all regions in the instance config.
  // * For a database instance config of type USER_MANAGED, please specify only
  // regional location KMS keys to cover each region in the instance config.
  // Multi-regional location KMS keys are not supported for USER_MANAGED
  // instance configs.
  repeated string kms_key_names = 3 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];
}

// Encryption information for a Cloud Spanner database or backup.
message EncryptionInfo {
  // Possible encryption types.
  enum Type {
    // Encryption type was not specified, though data at rest remains encrypted.
    TYPE_UNSPECIFIED = 0;

    // The data is encrypted at rest with a key that is
    // fully managed by Google. No key version or status will be populated.
    // This is the default state.
    GOOGLE_DEFAULT_ENCRYPTION = 1;

    // The data is encrypted at rest with a key that is
    // managed by the customer. The active version of the key. `kms_key_version`
    // will be populated, and `encryption_status` may be populated.
    CUSTOMER_MANAGED_ENCRYPTION = 2;
  }

  // Output only. The type of encryption.
  Type encryption_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If present, the status of a recent encrypt/decrypt call on
  // underlying data for this database or backup. Regardless of status, data is
  // always encrypted at rest.
  google.rpc.Status encryption_status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A Cloud KMS key version that is being used to protect the
  // database or backup.
  string kms_key_version = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];
}

// Indicates the dialect type of a database.
enum DatabaseDialect {
  // Default value. This value will create a database with the
  // GOOGLE_STANDARD_SQL dialect.
  DATABASE_DIALECT_UNSPECIFIED = 0;

  // GoogleSQL supported SQL.
  GOOGLE_STANDARD_SQL = 1;

  // PostgreSQL supported SQL.
  POSTGRESQL = 2;
}
