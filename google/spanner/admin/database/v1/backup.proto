// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.spanner.admin.database.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/spanner/admin/database/v1/common.proto";

option csharp_namespace = "Google.Cloud.Spanner.Admin.Database.V1";
option go_package = "cloud.google.com/go/spanner/admin/database/apiv1/databasepb;databasepb";
option java_multiple_files = true;
option java_outer_classname = "BackupProto";
option java_package = "com.google.spanner.admin.database.v1";
option php_namespace = "Google\\Cloud\\Spanner\\Admin\\Database\\V1";
option ruby_package = "Google::Cloud::Spanner::Admin::Database::V1";

// A backup of a Cloud Spanner database.
message Backup {
  option (google.api.resource) = {
    type: "spanner.googleapis.com/Backup"
    pattern: "projects/{project}/instances/{instance}/backups/{backup}"
  };

  // Indicates the current state of the backup.
  enum State {
    // Not specified.
    STATE_UNSPECIFIED = 0;

    // The pending backup is still being created. Operations on the
    // backup may fail with `FAILED_PRECONDITION` in this state.
    CREATING = 1;

    // The backup is complete and ready for use.
    READY = 2;
  }

  // Required for the
  // [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup]
  // operation. Name of the database from which this backup was created. This
  // needs to be in the same instance as the backup. Values are of the form
  // `projects/<project>/instances/<instance>/databases/<database>`.
  string database = 2 [(google.api.resource_reference) = {
    type: "spanner.googleapis.com/Database"
  }];

  // The backup will contain an externally consistent copy of the database at
  // the timestamp specified by `version_time`. If `version_time` is not
  // specified, the system will set `version_time` to the `create_time` of the
  // backup.
  google.protobuf.Timestamp version_time = 9;

  // Required for the
  // [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup]
  // operation. The expiration time of the backup, with microseconds
  // granularity that must be at least 6 hours and at most 366 days
  // from the time the CreateBackup request is processed. Once the `expire_time`
  // has passed, the backup is eligible to be automatically deleted by Cloud
  // Spanner to free the resources used by the backup.
  google.protobuf.Timestamp expire_time = 3;

  // Output only for the
  // [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup]
  // operation. Required for the
  // [UpdateBackup][google.spanner.admin.database.v1.DatabaseAdmin.UpdateBackup]
  // operation.
  //
  // A globally unique identifier for the backup which cannot be
  // changed. Values are of the form
  // `projects/<project>/instances/<instance>/backups/[a-z][a-z0-9_\-]*[a-z0-9]`
  // The final segment of the name must be between 2 and 60 characters
  // in length.
  //
  // The backup is stored in the location(s) specified in the instance
  // configuration of the instance containing the backup, identified
  // by the prefix of the backup name of the form
  // `projects/<project>/instances/<instance>`.
  string name = 1;

  // Output only. The time the
  // [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup]
  // request is received. If the request does not specify `version_time`, the
  // `version_time` of the backup will be equivalent to the `create_time`.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Size of the backup in bytes.
  int64 size_bytes = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of bytes that will be freed by deleting this
  // backup. This value will be zero if, for example, this backup is part of an
  // incremental backup chain and younger backups in the chain require that we
  // keep its data. For backups not in an incremental backup chain, this is
  // always the size of the backup. This value may change if backups on the same
  // chain get created, deleted or expired.
  int64 freeable_size_bytes = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a backup in an incremental backup chain, this is the
  // storage space needed to keep the data that has changed since the previous
  // backup. For all other backups, this is always the size of the backup. This
  // value may change if backups on the same chain get deleted or expired.
  //
  // This field can be used to calculate the total storage space used by a set
  // of backups. For example, the total space used by all backups of a database
  // can be computed by summing up this field.
  int64 exclusive_size_bytes = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the backup.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of the restored databases that reference the backup.
  // The database names are of
  // the form `projects/<project>/instances/<instance>/databases/<database>`.
  // Referencing databases may exist in different instances. The existence of
  // any referencing database prevents the backup from being deleted. When a
  // restored database from the backup enters the `READY` state, the reference
  // to the backup is removed.
  repeated string referencing_databases = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Database"
    }
  ];

  // Output only. The encryption information for the backup.
  EncryptionInfo encryption_info = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The encryption information for the backup, whether it is
  // protected by one or more KMS keys. The information includes all Cloud
  // KMS key versions used to encrypt the backup. The `encryption_status' field
  // inside of each `EncryptionInfo` is not populated. At least one of the key
  // versions must be available for the backup to be restored. If a key version
  // is revoked in the middle of a restore, the restore behavior is undefined.
  repeated EncryptionInfo encryption_information = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The database dialect information for the backup.
  DatabaseDialect database_dialect = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of the destination backups being created by copying
  // this source backup. The backup names are of the form
  // `projects/<project>/instances/<instance>/backups/<backup>`.
  // Referencing backups may exist in different instances. The existence of
  // any referencing backup prevents the backup from being deleted. When the
  // copy operation is done (either successfully completed or cancelled or the
  // destination backup is deleted), the reference to the backup is removed.
  repeated string referencing_backups = 11 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "spanner.googleapis.com/Backup" }
  ];

  // Output only. The max allowed expiration time of the backup, with
  // microseconds granularity. A backup's expiration time can be configured in
  // multiple APIs: CreateBackup, UpdateBackup, CopyBackup. When updating or
  // copying an existing backup, the expiration time specified must be
  // less than `Backup.max_expire_time`.
  google.protobuf.Timestamp max_expire_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of backup schedule URIs that are associated with
  // creating this backup. This is only applicable for scheduled backups, and
  // is empty for on-demand backups.
  //
  // To optimize for storage, whenever possible, multiple schedules are
  // collapsed together to create one backup. In such cases, this field captures
  // the list of all backup schedule URIs that are associated with creating
  // this backup. If collapsing is not done, then this field captures the
  // single backup schedule URI associated with creating this backup.
  repeated string backup_schedules = 14 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/BackupSchedule"
    }
  ];

  // Output only. Populated only for backups in an incremental backup chain.
  // Backups share the same chain id if and only if they belong to the same
  // incremental backup chain. Use this field to determine which backups are
  // part of the same incremental backup chain. The ordering of backups in the
  // chain can be determined by ordering the backup `version_time`.
  string incremental_backup_chain_id = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Data deleted at a time older than this is guaranteed not to be
  // retained in order to support this backup. For a backup in an incremental
  // backup chain, this is the version time of the oldest backup that exists or
  // ever existed in the chain. For all other backups, this is the version time
  // of the backup. This field can be used to understand what data is being
  // retained by the backup system.
  google.protobuf.Timestamp oldest_version_time = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request for
// [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup].
message CreateBackupRequest {
  // Required. The name of the instance in which the backup will be
  // created. This must be the same instance that contains the database the
  // backup will be created from. The backup will be stored in the
  // location(s) specified in the instance configuration of this
  // instance. Values are of the form
  // `projects/<project>/instances/<instance>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // Required. The id of the backup to be created. The `backup_id` appended to
  // `parent` forms the full backup name of the form
  // `projects/<project>/instances/<instance>/backups/<backup_id>`.
  string backup_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The backup to create.
  Backup backup = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The encryption configuration used to encrypt the backup. If this
  // field is not specified, the backup will use the same encryption
  // configuration as the database by default, namely
  // [encryption_type][google.spanner.admin.database.v1.CreateBackupEncryptionConfig.encryption_type]
  // = `USE_DATABASE_ENCRYPTION`.
  CreateBackupEncryptionConfig encryption_config = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Metadata type for the operation returned by
// [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup].
message CreateBackupMetadata {
  // The name of the backup being created.
  string name = 1 [
    (google.api.resource_reference) = { type: "spanner.googleapis.com/Backup" }
  ];

  // The name of the database the backup is created from.
  string database = 2 [(google.api.resource_reference) = {
    type: "spanner.googleapis.com/Database"
  }];

  // The progress of the
  // [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup]
  // operation.
  OperationProgress progress = 3;

  // The time at which cancellation of this operation was received.
  // [Operations.CancelOperation][google.longrunning.Operations.CancelOperation]
  // starts asynchronous cancellation on a long-running operation. The server
  // makes a best effort to cancel the operation, but success is not guaranteed.
  // Clients can use
  // [Operations.GetOperation][google.longrunning.Operations.GetOperation] or
  // other methods to check whether the cancellation succeeded or whether the
  // operation completed despite cancellation. On successful cancellation,
  // the operation is not deleted; instead, it becomes an operation with
  // an [Operation.error][google.longrunning.Operation.error] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  google.protobuf.Timestamp cancel_time = 4;
}

// The request for
// [CopyBackup][google.spanner.admin.database.v1.DatabaseAdmin.CopyBackup].
message CopyBackupRequest {
  // Required. The name of the destination instance that will contain the backup
  // copy. Values are of the form: `projects/<project>/instances/<instance>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // Required. The id of the backup copy.
  // The `backup_id` appended to `parent` forms the full backup_uri of the form
  // `projects/<project>/instances/<instance>/backups/<backup>`.
  string backup_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The source backup to be copied.
  // The source backup needs to be in READY state for it to be copied.
  // Once CopyBackup is in progress, the source backup cannot be deleted or
  // cleaned up on expiration until CopyBackup is finished.
  // Values are of the form:
  // `projects/<project>/instances/<instance>/backups/<backup>`.
  string source_backup = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "spanner.googleapis.com/Backup" }
  ];

  // Required. The expiration time of the backup in microsecond granularity.
  // The expiration time must be at least 6 hours and at most 366 days
  // from the `create_time` of the source backup. Once the `expire_time` has
  // passed, the backup is eligible to be automatically deleted by Cloud Spanner
  // to free the resources used by the backup.
  google.protobuf.Timestamp expire_time = 4
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The encryption configuration used to encrypt the backup. If this
  // field is not specified, the backup will use the same encryption
  // configuration as the source backup by default, namely
  // [encryption_type][google.spanner.admin.database.v1.CopyBackupEncryptionConfig.encryption_type]
  // = `USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION`.
  CopyBackupEncryptionConfig encryption_config = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Metadata type for the operation returned by
// [CopyBackup][google.spanner.admin.database.v1.DatabaseAdmin.CopyBackup].
message CopyBackupMetadata {
  // The name of the backup being created through the copy operation.
  // Values are of the form
  // `projects/<project>/instances/<instance>/backups/<backup>`.
  string name = 1 [
    (google.api.resource_reference) = { type: "spanner.googleapis.com/Backup" }
  ];

  // The name of the source backup that is being copied.
  // Values are of the form
  // `projects/<project>/instances/<instance>/backups/<backup>`.
  string source_backup = 2 [
    (google.api.resource_reference) = { type: "spanner.googleapis.com/Backup" }
  ];

  // The progress of the
  // [CopyBackup][google.spanner.admin.database.v1.DatabaseAdmin.CopyBackup]
  // operation.
  OperationProgress progress = 3;

  // The time at which cancellation of CopyBackup operation was received.
  // [Operations.CancelOperation][google.longrunning.Operations.CancelOperation]
  // starts asynchronous cancellation on a long-running operation. The server
  // makes a best effort to cancel the operation, but success is not guaranteed.
  // Clients can use
  // [Operations.GetOperation][google.longrunning.Operations.GetOperation] or
  // other methods to check whether the cancellation succeeded or whether the
  // operation completed despite cancellation. On successful cancellation,
  // the operation is not deleted; instead, it becomes an operation with
  // an [Operation.error][google.longrunning.Operation.error] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  google.protobuf.Timestamp cancel_time = 4;
}

// The request for
// [UpdateBackup][google.spanner.admin.database.v1.DatabaseAdmin.UpdateBackup].
message UpdateBackupRequest {
  // Required. The backup to update. `backup.name`, and the fields to be updated
  // as specified by `update_mask` are required. Other fields are ignored.
  // Update is only supported for the following fields:
  //  * `backup.expire_time`.
  Backup backup = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying which fields (e.g. `expire_time`) in the
  // Backup resource should be updated. This mask is relative to the Backup
  // resource, not to the request message. The field mask must always be
  // specified; this prevents any future fields from being erased accidentally
  // by clients that do not know about them.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request for
// [GetBackup][google.spanner.admin.database.v1.DatabaseAdmin.GetBackup].
message GetBackupRequest {
  // Required. Name of the backup.
  // Values are of the form
  // `projects/<project>/instances/<instance>/backups/<backup>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "spanner.googleapis.com/Backup" }
  ];
}

// The request for
// [DeleteBackup][google.spanner.admin.database.v1.DatabaseAdmin.DeleteBackup].
message DeleteBackupRequest {
  // Required. Name of the backup to delete.
  // Values are of the form
  // `projects/<project>/instances/<instance>/backups/<backup>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "spanner.googleapis.com/Backup" }
  ];
}

// The request for
// [ListBackups][google.spanner.admin.database.v1.DatabaseAdmin.ListBackups].
message ListBackupsRequest {
  // Required. The instance to list backups from.  Values are of the
  // form `projects/<project>/instances/<instance>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // An expression that filters the list of returned backups.
  //
  // A filter expression consists of a field name, a comparison operator, and a
  // value for filtering.
  // The value must be a string, a number, or a boolean. The comparison operator
  // must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`.
  // Colon `:` is the contains operator. Filter rules are not case sensitive.
  //
  // The following fields in the
  // [Backup][google.spanner.admin.database.v1.Backup] are eligible for
  // filtering:
  //
  //   * `name`
  //   * `database`
  //   * `state`
  //   * `create_time`  (and values are of the format YYYY-MM-DDTHH:MM:SSZ)
  //   * `expire_time`  (and values are of the format YYYY-MM-DDTHH:MM:SSZ)
  //   * `version_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ)
  //   * `size_bytes`
  //   * `backup_schedules`
  //
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. By default, expressions are combined with AND logic, but
  // you can specify AND, OR, and NOT logic explicitly.
  //
  // Here are a few examples:
  //
  //   * `name:Howl` - The backup's name contains the string "howl".
  //   * `database:prod`
  //          - The database's name contains the string "prod".
  //   * `state:CREATING` - The backup is pending creation.
  //   * `state:READY` - The backup is fully created and ready for use.
  //   * `(name:howl) AND (create_time < \"2018-03-28T14:50:00Z\")`
  //          - The backup name contains the string "howl" and `create_time`
  //              of the backup is before 2018-03-28T14:50:00Z.
  //   * `expire_time < \"2018-03-28T14:50:00Z\"`
  //          - The backup `expire_time` is before 2018-03-28T14:50:00Z.
  //   * `size_bytes > 10000000000` - The backup's size is greater than 10GB
  //   * `backup_schedules:daily`
  //          - The backup is created from a schedule with "daily" in its name.
  string filter = 2;

  // Number of backups to be returned in the response. If 0 or
  // less, defaults to the server's maximum allowed page size.
  int32 page_size = 3;

  // If non-empty, `page_token` should contain a
  // [next_page_token][google.spanner.admin.database.v1.ListBackupsResponse.next_page_token]
  // from a previous
  // [ListBackupsResponse][google.spanner.admin.database.v1.ListBackupsResponse]
  // to the same `parent` and with the same `filter`.
  string page_token = 4;
}

// The response for
// [ListBackups][google.spanner.admin.database.v1.DatabaseAdmin.ListBackups].
message ListBackupsResponse {
  // The list of matching backups. Backups returned are ordered by `create_time`
  // in descending order, starting from the most recent `create_time`.
  repeated Backup backups = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListBackups][google.spanner.admin.database.v1.DatabaseAdmin.ListBackups]
  // call to fetch more of the matching backups.
  string next_page_token = 2;
}

// The request for
// [ListBackupOperations][google.spanner.admin.database.v1.DatabaseAdmin.ListBackupOperations].
message ListBackupOperationsRequest {
  // Required. The instance of the backup operations. Values are of
  // the form `projects/<project>/instances/<instance>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // An expression that filters the list of returned backup operations.
  //
  // A filter expression consists of a field name, a
  // comparison operator, and a value for filtering.
  // The value must be a string, a number, or a boolean. The comparison operator
  // must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`.
  // Colon `:` is the contains operator. Filter rules are not case sensitive.
  //
  // The following fields in the [operation][google.longrunning.Operation]
  // are eligible for filtering:
  //
  //   * `name` - The name of the long-running operation
  //   * `done` - False if the operation is in progress, else true.
  //   * `metadata.@type` - the type of metadata. For example, the type string
  //      for
  //      [CreateBackupMetadata][google.spanner.admin.database.v1.CreateBackupMetadata]
  //      is
  //      `type.googleapis.com/google.spanner.admin.database.v1.CreateBackupMetadata`.
  //   * `metadata.<field_name>` - any field in metadata.value.
  //      `metadata.@type` must be specified first if filtering on metadata
  //      fields.
  //   * `error` - Error associated with the long-running operation.
  //   * `response.@type` - the type of response.
  //   * `response.<field_name>` - any field in response.value.
  //
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. By default, expressions are combined with AND logic, but
  // you can specify AND, OR, and NOT logic explicitly.
  //
  // Here are a few examples:
  //
  //   * `done:true` - The operation is complete.
  //   * `(metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CreateBackupMetadata) AND` \
  //      `metadata.database:prod` - Returns operations where:
  //      * The operation's metadata type is
  //      [CreateBackupMetadata][google.spanner.admin.database.v1.CreateBackupMetadata].
  //      * The source database name of backup contains the string "prod".
  //   * `(metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CreateBackupMetadata) AND` \
  //     `(metadata.name:howl) AND` \
  //     `(metadata.progress.start_time < \"2018-03-28T14:50:00Z\") AND` \
  //     `(error:*)` - Returns operations where:
  //     * The operation's metadata type is
  //     [CreateBackupMetadata][google.spanner.admin.database.v1.CreateBackupMetadata].
  //     * The backup name contains the string "howl".
  //     * The operation started before 2018-03-28T14:50:00Z.
  //     * The operation resulted in an error.
  //   * `(metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CopyBackupMetadata) AND` \
  //     `(metadata.source_backup:test) AND` \
  //     `(metadata.progress.start_time < \"2022-01-18T14:50:00Z\") AND` \
  //     `(error:*)` - Returns operations where:
  //     * The operation's metadata type is
  //     [CopyBackupMetadata][google.spanner.admin.database.v1.CopyBackupMetadata].
  //     * The source backup name contains the string "test".
  //     * The operation started before 2022-01-18T14:50:00Z.
  //     * The operation resulted in an error.
  //   * `((metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CreateBackupMetadata) AND` \
  //     `(metadata.database:test_db)) OR` \
  //     `((metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CopyBackupMetadata)
  //     AND` \
  //     `(metadata.source_backup:test_bkp)) AND` \
  //     `(error:*)` - Returns operations where:
  //     * The operation's metadata matches either of criteria:
  //       * The operation's metadata type is
  //       [CreateBackupMetadata][google.spanner.admin.database.v1.CreateBackupMetadata]
  //       AND the source database name of the backup contains the string
  //       "test_db"
  //       * The operation's metadata type is
  //       [CopyBackupMetadata][google.spanner.admin.database.v1.CopyBackupMetadata]
  //       AND the source backup name contains the string "test_bkp"
  //     * The operation resulted in an error.
  string filter = 2;

  // Number of operations to be returned in the response. If 0 or
  // less, defaults to the server's maximum allowed page size.
  int32 page_size = 3;

  // If non-empty, `page_token` should contain a
  // [next_page_token][google.spanner.admin.database.v1.ListBackupOperationsResponse.next_page_token]
  // from a previous
  // [ListBackupOperationsResponse][google.spanner.admin.database.v1.ListBackupOperationsResponse]
  // to the same `parent` and with the same `filter`.
  string page_token = 4;
}

// The response for
// [ListBackupOperations][google.spanner.admin.database.v1.DatabaseAdmin.ListBackupOperations].
message ListBackupOperationsResponse {
  // The list of matching backup [long-running
  // operations][google.longrunning.Operation]. Each operation's name will be
  // prefixed by the backup's name. The operation's
  // [metadata][google.longrunning.Operation.metadata] field type
  // `metadata.type_url` describes the type of the metadata. Operations returned
  // include those that are pending or have completed/failed/canceled within the
  // last 7 days. Operations returned are ordered by
  // `operation.metadata.value.progress.start_time` in descending order starting
  // from the most recently started operation.
  repeated google.longrunning.Operation operations = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListBackupOperations][google.spanner.admin.database.v1.DatabaseAdmin.ListBackupOperations]
  // call to fetch more of the matching metadata.
  string next_page_token = 2;
}

// Information about a backup.
message BackupInfo {
  // Name of the backup.
  string backup = 1 [
    (google.api.resource_reference) = { type: "spanner.googleapis.com/Backup" }
  ];

  // The backup contains an externally consistent copy of `source_database` at
  // the timestamp specified by `version_time`. If the
  // [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup]
  // request did not specify `version_time`, the `version_time` of the backup is
  // equivalent to the `create_time`.
  google.protobuf.Timestamp version_time = 4;

  // The time the
  // [CreateBackup][google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup]
  // request was received.
  google.protobuf.Timestamp create_time = 2;

  // Name of the database the backup was created from.
  string source_database = 3 [(google.api.resource_reference) = {
    type: "spanner.googleapis.com/Database"
  }];
}

// Encryption configuration for the backup to create.
message CreateBackupEncryptionConfig {
  // Encryption types for the backup.
  enum EncryptionType {
    // Unspecified. Do not use.
    ENCRYPTION_TYPE_UNSPECIFIED = 0;

    // Use the same encryption configuration as the database. This is the
    // default option when
    // [encryption_config][google.spanner.admin.database.v1.CreateBackupEncryptionConfig]
    // is empty. For example, if the database is using
    // `Customer_Managed_Encryption`, the backup will be using the same Cloud
    // KMS key as the database.
    USE_DATABASE_ENCRYPTION = 1;

    // Use Google default encryption.
    GOOGLE_DEFAULT_ENCRYPTION = 2;

    // Use customer managed encryption. If specified, `kms_key_name`
    // must contain a valid Cloud KMS key.
    CUSTOMER_MANAGED_ENCRYPTION = 3;
  }

  // Required. The encryption type of the backup.
  EncryptionType encryption_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The Cloud KMS key that will be used to protect the backup.
  // This field should be set only when
  // [encryption_type][google.spanner.admin.database.v1.CreateBackupEncryptionConfig.encryption_type]
  // is `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the form
  // `projects/<project>/locations/<location>/keyRings/<key_ring>/cryptoKeys/<kms_key_name>`.
  string kms_key_name = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Optional. Specifies the KMS configuration for the one or more keys used to
  // protect the backup. Values are of the form
  // `projects/<project>/locations/<location>/keyRings/<key_ring>/cryptoKeys/<kms_key_name>`.
  //
  // The keys referenced by kms_key_names must fully cover all
  // regions of the backup's instance configuration. Some examples:
  // * For single region instance configs, specify a single regional
  // location KMS key.
  // * For multi-regional instance configs of type GOOGLE_MANAGED,
  // either specify a multi-regional location KMS key or multiple regional
  // location KMS keys that cover all regions in the instance config.
  // * For an instance config of type USER_MANAGED, please specify only
  // regional location KMS keys to cover each region in the instance config.
  // Multi-regional location KMS keys are not supported for USER_MANAGED
  // instance configs.
  repeated string kms_key_names = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];
}

// Encryption configuration for the copied backup.
message CopyBackupEncryptionConfig {
  // Encryption types for the backup.
  enum EncryptionType {
    // Unspecified. Do not use.
    ENCRYPTION_TYPE_UNSPECIFIED = 0;

    // This is the default option for
    // [CopyBackup][google.spanner.admin.database.v1.DatabaseAdmin.CopyBackup]
    // when
    // [encryption_config][google.spanner.admin.database.v1.CopyBackupEncryptionConfig]
    // is not specified. For example, if the source backup is using
    // `Customer_Managed_Encryption`, the backup will be using the same Cloud
    // KMS key as the source backup.
    USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION = 1;

    // Use Google default encryption.
    GOOGLE_DEFAULT_ENCRYPTION = 2;

    // Use customer managed encryption. If specified, either `kms_key_name` or
    // `kms_key_names` must contain valid Cloud KMS key(s).
    CUSTOMER_MANAGED_ENCRYPTION = 3;
  }

  // Required. The encryption type of the backup.
  EncryptionType encryption_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The Cloud KMS key that will be used to protect the backup.
  // This field should be set only when
  // [encryption_type][google.spanner.admin.database.v1.CopyBackupEncryptionConfig.encryption_type]
  // is `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the form
  // `projects/<project>/locations/<location>/keyRings/<key_ring>/cryptoKeys/<kms_key_name>`.
  string kms_key_name = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Optional. Specifies the KMS configuration for the one or more keys used to
  // protect the backup. Values are of the form
  // `projects/<project>/locations/<location>/keyRings/<key_ring>/cryptoKeys/<kms_key_name>`.
  // Kms keys specified can be in any order.
  //
  // The keys referenced by kms_key_names must fully cover all
  // regions of the backup's instance configuration. Some examples:
  // * For single region instance configs, specify a single regional
  // location KMS key.
  // * For multi-regional instance configs of type GOOGLE_MANAGED,
  // either specify a multi-regional location KMS key or multiple regional
  // location KMS keys that cover all regions in the instance config.
  // * For an instance config of type USER_MANAGED, please specify only
  // regional location KMS keys to cover each region in the instance config.
  // Multi-regional location KMS keys are not supported for USER_MANAGED
  // instance configs.
  repeated string kms_key_names = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];
}

// The specification for full backups.
// A full backup stores the entire contents of the database at a given
// version time.
message FullBackupSpec {}

// The specification for incremental backup chains.
// An incremental backup stores the delta of changes between a previous
// backup and the database contents at a given version time. An
// incremental backup chain consists of a full backup and zero or more
// successive incremental backups. The first backup created for an
// incremental backup chain is always a full backup.
message IncrementalBackupSpec {}
