{"methodConfig": [{"name": [{"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "ListDatabases"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "GetDatabase"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "UpdateDatabase"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "UpdateDatabaseDdl"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "DropDatabase"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "GetDatabaseDdl"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "GetBackup"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "ListBackups"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "DeleteBackup"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "UpdateBackup"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "ListDatabaseOperations"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "ListBackupOperations"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "ListDatabaseRoles"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "CreateBackupSchedule"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "GetBackupSchedule"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "UpdateBackupSchedule"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "DeleteBackupSchedule"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "ListBackupSchedules"}], "timeout": "3600s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "32s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "GetIamPolicy"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "32s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "CreateDatabase"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "CreateBackup"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "CopyBackup"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "RestoreDatabase"}], "timeout": "3600s"}, {"name": [{"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "SetIamPolicy"}, {"service": "google.spanner.admin.database.v1.DatabaseAdmin", "method": "TestIamPermissions"}], "timeout": "30s"}]}