type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.spanner.admin.instance.v1
interfaces:
- name: google.spanner.admin.instance.v1.InstanceAdmin
  methods:
  - name: CreateInstance
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 86400000
  - name: UpdateInstance
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 86400000
