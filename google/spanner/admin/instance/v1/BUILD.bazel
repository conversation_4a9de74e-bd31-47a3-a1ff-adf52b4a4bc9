# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "instance_proto",
    srcs = [
        "common.proto",
        "spanner_instance_admin.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "instance_proto_with_info",
    deps = [
        ":instance_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "instance_java_proto",
    deps = [":instance_proto"],
)

java_grpc_library(
    name = "instance_java_grpc",
    srcs = [":instance_proto"],
    deps = [":instance_java_proto"],
)

java_gapic_library(
    name = "instance_java_gapic",
    srcs = [":instance_proto_with_info"],
    gapic_yaml = "spanner_admin_instance_gapic.yaml",
    grpc_service_config = "spanner_admin_instance_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "spanner_admin_instance.yaml",
    test_deps = [
        ":instance_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":instance_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "instance_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.spanner.admin.instance.v1.InstanceAdminClientHttpJsonTest",
        "com.google.cloud.spanner.admin.instance.v1.InstanceAdminClientTest",
    ],
    runtime_deps = [":instance_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-admin-instance-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":instance_java_gapic",
        ":instance_java_grpc",
        ":instance_java_proto",
        ":instance_proto",
    ],
)

go_proto_library(
    name = "instance_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/spanner/admin/instance/apiv1/instancepb",
    protos = [":instance_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "instance_go_gapic",
    srcs = [":instance_proto_with_info"],
    grpc_service_config = "spanner_admin_instance_grpc_service_config.json",
    importpath = "cloud.google.com/go/spanner/admin/instance/apiv1;instance",
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "spanner_admin_instance.yaml",
    transport = "grpc+rest",
    deps = [
        ":instance_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-admin-instance-v1-go",
    deps = [
        ":instance_go_gapic",
        ":instance_go_gapic_srcjar-snippets.srcjar",
        ":instance_go_gapic_srcjar-test.srcjar",
        ":instance_go_proto",
    ],
)

py_gapic_library(
    name = "instance_py_gapic",
    srcs = [":instance_proto"],
    grpc_service_config = "spanner_admin_instance_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=spanner_admin_instance",
    ],
    rest_numeric_enums = True,
    service_yaml = "spanner_admin_instance.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "instance_py_gapic_test",
    srcs = [
        "instance_py_gapic_pytest.py",
        "instance_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":instance_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "admin-instance-v1-py",
    deps = [
        ":instance_py_gapic",
    ],
)

php_proto_library(
    name = "instance_php_proto",
    deps = [":instance_proto"],
)

php_gapic_library(
    name = "instance_php_gapic",
    srcs = [":instance_proto_with_info"],
    gapic_yaml = "spanner_admin_instance_gapic.yaml",
    grpc_service_config = "spanner_admin_instance_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "spanner_admin_instance.yaml",
    transport = "grpc+rest",
    migration_mode = "MIGRATING",
    deps = [":instance_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-admin-instance-v1-php",
    deps = [
        ":instance_php_gapic",
        ":instance_php_proto",
    ],
)

nodejs_gapic_library(
    name = "instance_nodejs_gapic",
    package_name = "@google-cloud/spanner",
    src = ":instance_proto_with_info",
    extra_protoc_parameters = [
        "metadata",
        "template=typescript_gapic",
    ],
    grpc_service_config = "spanner_admin_instance_grpc_service_config.json",
    handwritten_layer = True,
    package = "google.spanner.admin.instance.v1",
    rest_numeric_enums = True,
    service_yaml = "spanner_admin_instance.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "admin-instance-v1-nodejs",
    deps = [
        ":instance_nodejs_gapic",
        ":instance_proto",
    ],
)

ruby_proto_library(
    name = "instance_ruby_proto",
    deps = [":instance_proto"],
)

ruby_grpc_library(
    name = "instance_ruby_grpc",
    srcs = [":instance_proto"],
    deps = [":instance_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "instance_ruby_gapic",
    srcs = [":instance_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-spanner-admin-instance-v1",
        "ruby-cloud-env-prefix=SPANNER",
        "ruby-cloud-product-url=https://cloud.google.com/spanner",
        "ruby-cloud-api-id=spanner.googleapis.com",
        "ruby-cloud-api-shortname=spanner",
        "ruby-cloud-wrapper-gem-override=google-cloud-spanner",
    ],
    grpc_service_config = "spanner_admin_instance_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Spanner is a managed, mission-critical, globally consistent and scalable relational database service.",
    ruby_cloud_title = "Cloud Spanner Instance Admin V1",
    service_yaml = "spanner_admin_instance.yaml",
    transport = "grpc+rest",
    deps = [
        ":instance_ruby_grpc",
        ":instance_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-admin-instance-v1-ruby",
    deps = [
        ":instance_ruby_gapic",
        ":instance_ruby_grpc",
        ":instance_ruby_proto",
    ],
)

csharp_proto_library(
    name = "instance_csharp_proto",
    deps = [":instance_proto"],
)

csharp_grpc_library(
    name = "instance_csharp_grpc",
    srcs = [":instance_proto"],
    deps = [":instance_csharp_proto"],
)

csharp_gapic_library(
    name = "instance_csharp_gapic",
    srcs = [":instance_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "spanner_admin_instance_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "spanner_admin_instance.yaml",
    transport = "grpc+rest",
    deps = [
        ":instance_csharp_grpc",
        ":instance_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-admin-instance-v1-csharp",
    deps = [
        ":instance_csharp_gapic",
        ":instance_csharp_grpc",
        ":instance_csharp_proto",
    ],
)

cc_proto_library(
    name = "instance_cc_proto",
    deps = [":instance_proto"],
)

cc_grpc_library(
    name = "instance_cc_grpc",
    srcs = [":instance_proto"],
    grpc_only = True,
    deps = [":instance_cc_proto"],
)
