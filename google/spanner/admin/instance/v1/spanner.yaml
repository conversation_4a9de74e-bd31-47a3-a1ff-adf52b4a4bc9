type: google.api.Service
config_version: 3
name: spanner.googleapis.com
title: Cloud Spanner API

apis:
- name: google.longrunning.Operations
- name: google.spanner.admin.instance.v1.InstanceAdmin

types:
- name: google.spanner.admin.instance.v1.CreateInstanceConfigMetadata
- name: google.spanner.admin.instance.v1.CreateInstanceMetadata
- name: google.spanner.admin.instance.v1.CreateInstancePartitionMetadata
- name: google.spanner.admin.instance.v1.UpdateInstanceConfigMetadata
- name: google.spanner.admin.instance.v1.UpdateInstanceMetadata
- name: google.spanner.admin.instance.v1.UpdateInstancePartitionMetadata

documentation:
  summary: |-
    Cloud Spanner is a managed, mission-critical, globally consistent and
    scalable relational database service.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/instances/*/databases/*/operations/*}:cancel'
    additional_bindings:
    - post: '/v1/{name=projects/*/instances/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/instances/*/backups/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/instances/*/instancePartitions/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/instanceConfigs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/instanceConfigs/*/ssdCaches/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/instances/*/databases/*/operations/*}'
    additional_bindings:
    - delete: '/v1/{name=projects/*/instances/*/operations/*}'
    - delete: '/v1/{name=projects/*/instances/*/backups/*/operations/*}'
    - delete: '/v1/{name=projects/*/instances/*/instancePartitions/*/operations/*}'
    - delete: '/v1/{name=projects/*/instanceConfigs/*/operations/*}'
    - delete: '/v1/{name=projects/*/instanceConfigs/*/ssdCaches/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/instances/*/databases/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=projects/*/instances/*/operations/*}'
    - get: '/v1/{name=projects/*/instances/*/backups/*/operations/*}'
    - get: '/v1/{name=projects/*/instances/*/instancePartitions/*/operations/*}'
    - get: '/v1/{name=projects/*/instanceConfigs/*/operations/*}'
    - get: '/v1/{name=projects/*/instanceConfigs/*/ssdCaches/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/instances/*/databases/*/operations}'
    additional_bindings:
    - get: '/v1/{name=projects/*/instances/*/operations}'
    - get: '/v1/{name=projects/*/instances/*/backups/*/operations}'
    - get: '/v1/{name=projects/*/instances/*/instancePartitions/*/operations}'
    - get: '/v1/{name=projects/*/instanceConfigs/*/operations}'
    - get: '/v1/{name=projects/*/instanceConfigs/*/ssdCaches/*/operations}'

authentication:
  rules:
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/spanner.admin
  - selector: 'google.spanner.admin.instance.v1.InstanceAdmin.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/spanner.admin

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=190851&template=0
  documentation_uri: https://cloud.google.com/spanner/
