// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.spanner.admin.instance.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/spanner/admin/instance/v1/common.proto";

option csharp_namespace = "Google.Cloud.Spanner.Admin.Instance.V1";
option go_package = "cloud.google.com/go/spanner/admin/instance/apiv1/instancepb;instancepb";
option java_multiple_files = true;
option java_outer_classname = "SpannerInstanceAdminProto";
option java_package = "com.google.spanner.admin.instance.v1";
option php_namespace = "Google\\Cloud\\Spanner\\Admin\\Instance\\V1";
option ruby_package = "Google::Cloud::Spanner::Admin::Instance::V1";

// Cloud Spanner Instance Admin API
//
// The Cloud Spanner Instance Admin API can be used to create, delete,
// modify and list instances. Instances are dedicated Cloud Spanner serving
// and storage resources to be used by Cloud Spanner databases.
//
// Each instance has a "configuration", which dictates where the
// serving resources for the Cloud Spanner instance are located (e.g.,
// US-central, Europe). Configurations are created by Google based on
// resource availability.
//
// Cloud Spanner billing is based on the instances that exist and their
// sizes. After an instance exists, there are no additional
// per-database or per-operation charges for use of the instance
// (though there may be additional network bandwidth charges).
// Instances offer isolation: problems with databases in one instance
// will not affect other instances. However, within an instance
// databases can affect each other. For example, if one database in an
// instance receives a lot of requests and consumes most of the
// instance resources, fewer resources are available for other
// databases in that instance, and their performance may suffer.
service InstanceAdmin {
  option (google.api.default_host) = "spanner.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/spanner.admin";

  // Lists the supported instance configurations for a given project.
  //
  // Returns both Google-managed configurations and user-managed
  // configurations.
  rpc ListInstanceConfigs(ListInstanceConfigsRequest)
      returns (ListInstanceConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/instanceConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets information about a particular instance configuration.
  rpc GetInstanceConfig(GetInstanceConfigRequest) returns (InstanceConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/instanceConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an instance configuration and begins preparing it to be used. The
  // returned long-running operation
  // can be used to track the progress of preparing the new
  // instance configuration. The instance configuration name is assigned by the
  // caller. If the named instance configuration already exists,
  // `CreateInstanceConfig` returns `ALREADY_EXISTS`.
  //
  // Immediately after the request returns:
  //
  //   * The instance configuration is readable via the API, with all requested
  //     attributes. The instance configuration's
  //     [reconciling][google.spanner.admin.instance.v1.InstanceConfig.reconciling]
  //     field is set to true. Its state is `CREATING`.
  //
  // While the operation is pending:
  //
  //   * Cancelling the operation renders the instance configuration immediately
  //     unreadable via the API.
  //   * Except for deleting the creating resource, all other attempts to modify
  //     the instance configuration are rejected.
  //
  // Upon completion of the returned operation:
  //
  //   * Instances can be created using the instance configuration.
  //   * The instance configuration's
  //   [reconciling][google.spanner.admin.instance.v1.InstanceConfig.reconciling]
  //   field becomes false. Its state becomes `READY`.
  //
  // The returned long-running operation will
  // have a name of the format
  // `<instance_config_name>/operations/<operation_id>` and can be used to track
  // creation of the instance configuration. The
  // metadata field type is
  // [CreateInstanceConfigMetadata][google.spanner.admin.instance.v1.CreateInstanceConfigMetadata].
  // The response field type is
  // [InstanceConfig][google.spanner.admin.instance.v1.InstanceConfig], if
  // successful.
  //
  // Authorization requires `spanner.instanceConfigs.create` permission on
  // the resource
  // [parent][google.spanner.admin.instance.v1.CreateInstanceConfigRequest.parent].
  rpc CreateInstanceConfig(CreateInstanceConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/instanceConfigs"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,instance_config,instance_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.spanner.admin.instance.v1.InstanceConfig"
      metadata_type: "google.spanner.admin.instance.v1.CreateInstanceConfigMetadata"
    };
  }

  // Updates an instance configuration. The returned
  // long-running operation can be used to track
  // the progress of updating the instance. If the named instance configuration
  // does not exist, returns `NOT_FOUND`.
  //
  // Only user-managed configurations can be updated.
  //
  // Immediately after the request returns:
  //
  //   * The instance configuration's
  //     [reconciling][google.spanner.admin.instance.v1.InstanceConfig.reconciling]
  //     field is set to true.
  //
  // While the operation is pending:
  //
  //   * Cancelling the operation sets its metadata's
  //     [cancel_time][google.spanner.admin.instance.v1.UpdateInstanceConfigMetadata.cancel_time].
  //     The operation is guaranteed to succeed at undoing all changes, after
  //     which point it terminates with a `CANCELLED` status.
  //   * All other attempts to modify the instance configuration are rejected.
  //   * Reading the instance configuration via the API continues to give the
  //     pre-request values.
  //
  // Upon completion of the returned operation:
  //
  //   * Creating instances using the instance configuration uses the new
  //     values.
  //   * The new values of the instance configuration are readable via the API.
  //   * The instance configuration's
  //   [reconciling][google.spanner.admin.instance.v1.InstanceConfig.reconciling]
  //   field becomes false.
  //
  // The returned long-running operation will
  // have a name of the format
  // `<instance_config_name>/operations/<operation_id>` and can be used to track
  // the instance configuration modification.  The
  // metadata field type is
  // [UpdateInstanceConfigMetadata][google.spanner.admin.instance.v1.UpdateInstanceConfigMetadata].
  // The response field type is
  // [InstanceConfig][google.spanner.admin.instance.v1.InstanceConfig], if
  // successful.
  //
  // Authorization requires `spanner.instanceConfigs.update` permission on
  // the resource [name][google.spanner.admin.instance.v1.InstanceConfig.name].
  rpc UpdateInstanceConfig(UpdateInstanceConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{instance_config.name=projects/*/instanceConfigs/*}"
      body: "*"
    };
    option (google.api.method_signature) = "instance_config,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "google.spanner.admin.instance.v1.InstanceConfig"
      metadata_type: "google.spanner.admin.instance.v1.UpdateInstanceConfigMetadata"
    };
  }

  // Deletes the instance configuration. Deletion is only allowed when no
  // instances are using the configuration. If any instances are using
  // the configuration, returns `FAILED_PRECONDITION`.
  //
  // Only user-managed configurations can be deleted.
  //
  // Authorization requires `spanner.instanceConfigs.delete` permission on
  // the resource [name][google.spanner.admin.instance.v1.InstanceConfig.name].
  rpc DeleteInstanceConfig(DeleteInstanceConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/instanceConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the user-managed instance configuration long-running
  // operations in the given project. An instance
  // configuration operation has a name of the form
  // `projects/<project>/instanceConfigs/<instance_config>/operations/<operation>`.
  // The long-running operation
  // metadata field type
  // `metadata.type_url` describes the type of the metadata. Operations returned
  // include those that have completed/failed/canceled within the last 7 days,
  // and pending operations. Operations returned are ordered by
  // `operation.metadata.value.start_time` in descending order starting
  // from the most recently started operation.
  rpc ListInstanceConfigOperations(ListInstanceConfigOperationsRequest)
      returns (ListInstanceConfigOperationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/instanceConfigOperations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all instances in the given project.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all instance partitions for the given instance.
  rpc ListInstancePartitions(ListInstancePartitionsRequest)
      returns (ListInstancePartitionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/instances/*}/instancePartitions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets information about a particular instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an instance and begins preparing it to begin serving. The
  // returned long-running operation
  // can be used to track the progress of preparing the new
  // instance. The instance name is assigned by the caller. If the
  // named instance already exists, `CreateInstance` returns
  // `ALREADY_EXISTS`.
  //
  // Immediately upon completion of this request:
  //
  //   * The instance is readable via the API, with all requested attributes
  //     but no allocated resources. Its state is `CREATING`.
  //
  // Until completion of the returned operation:
  //
  //   * Cancelling the operation renders the instance immediately unreadable
  //     via the API.
  //   * The instance can be deleted.
  //   * All other attempts to modify the instance are rejected.
  //
  // Upon completion of the returned operation:
  //
  //   * Billing for all successfully-allocated resources begins (some types
  //     may have lower than the requested levels).
  //   * Databases can be created in the instance.
  //   * The instance's allocated resource levels are readable via the API.
  //   * The instance's state becomes `READY`.
  //
  // The returned long-running operation will
  // have a name of the format `<instance_name>/operations/<operation_id>` and
  // can be used to track creation of the instance.  The
  // metadata field type is
  // [CreateInstanceMetadata][google.spanner.admin.instance.v1.CreateInstanceMetadata].
  // The response field type is
  // [Instance][google.spanner.admin.instance.v1.Instance], if successful.
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/instances"
      body: "*"
    };
    option (google.api.method_signature) = "parent,instance_id,instance";
    option (google.longrunning.operation_info) = {
      response_type: "google.spanner.admin.instance.v1.Instance"
      metadata_type: "google.spanner.admin.instance.v1.CreateInstanceMetadata"
    };
  }

  // Updates an instance, and begins allocating or releasing resources
  // as requested. The returned long-running operation can be used to track the
  // progress of updating the instance. If the named instance does not
  // exist, returns `NOT_FOUND`.
  //
  // Immediately upon completion of this request:
  //
  //   * For resource types for which a decrease in the instance's allocation
  //     has been requested, billing is based on the newly-requested level.
  //
  // Until completion of the returned operation:
  //
  //   * Cancelling the operation sets its metadata's
  //     [cancel_time][google.spanner.admin.instance.v1.UpdateInstanceMetadata.cancel_time],
  //     and begins restoring resources to their pre-request values. The
  //     operation is guaranteed to succeed at undoing all resource changes,
  //     after which point it terminates with a `CANCELLED` status.
  //   * All other attempts to modify the instance are rejected.
  //   * Reading the instance via the API continues to give the pre-request
  //     resource levels.
  //
  // Upon completion of the returned operation:
  //
  //   * Billing begins for all successfully-allocated resources (some types
  //     may have lower than the requested levels).
  //   * All newly-reserved resources are available for serving the instance's
  //     tables.
  //   * The instance's new resource levels are readable via the API.
  //
  // The returned long-running operation will
  // have a name of the format `<instance_name>/operations/<operation_id>` and
  // can be used to track the instance modification.  The
  // metadata field type is
  // [UpdateInstanceMetadata][google.spanner.admin.instance.v1.UpdateInstanceMetadata].
  // The response field type is
  // [Instance][google.spanner.admin.instance.v1.Instance], if successful.
  //
  // Authorization requires `spanner.instances.update` permission on
  // the resource [name][google.spanner.admin.instance.v1.Instance.name].
  rpc UpdateInstance(UpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{instance.name=projects/*/instances/*}"
      body: "*"
    };
    option (google.api.method_signature) = "instance,field_mask";
    option (google.longrunning.operation_info) = {
      response_type: "google.spanner.admin.instance.v1.Instance"
      metadata_type: "google.spanner.admin.instance.v1.UpdateInstanceMetadata"
    };
  }

  // Deletes an instance.
  //
  // Immediately upon completion of the request:
  //
  //   * Billing ceases for all of the instance's reserved resources.
  //
  // Soon afterward:
  //
  //   * The instance and *all of its databases* immediately and
  //     irrevocably disappear from the API. All data in the databases
  //     is permanently deleted.
  rpc DeleteInstance(DeleteInstanceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Sets the access control policy on an instance resource. Replaces any
  // existing policy.
  //
  // Authorization requires `spanner.instances.setIamPolicy` on
  // [resource][google.iam.v1.SetIamPolicyRequest.resource].
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/instances/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Gets the access control policy for an instance resource. Returns an empty
  // policy if an instance exists but does not have a policy set.
  //
  // Authorization requires `spanner.instances.getIamPolicy` on
  // [resource][google.iam.v1.GetIamPolicyRequest.resource].
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/instances/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Returns permissions that the caller has on the specified instance resource.
  //
  // Attempting this RPC on a non-existent Cloud Spanner instance resource will
  // result in a NOT_FOUND error if the user has `spanner.instances.list`
  // permission on the containing Google Cloud Project. Otherwise returns an
  // empty set of permissions.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/instances/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }

  // Gets information about a particular instance partition.
  rpc GetInstancePartition(GetInstancePartitionRequest)
      returns (InstancePartition) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/instances/*/instancePartitions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an instance partition and begins preparing it to be used. The
  // returned long-running operation
  // can be used to track the progress of preparing the new instance partition.
  // The instance partition name is assigned by the caller. If the named
  // instance partition already exists, `CreateInstancePartition` returns
  // `ALREADY_EXISTS`.
  //
  // Immediately upon completion of this request:
  //
  //   * The instance partition is readable via the API, with all requested
  //     attributes but no allocated resources. Its state is `CREATING`.
  //
  // Until completion of the returned operation:
  //
  //   * Cancelling the operation renders the instance partition immediately
  //     unreadable via the API.
  //   * The instance partition can be deleted.
  //   * All other attempts to modify the instance partition are rejected.
  //
  // Upon completion of the returned operation:
  //
  //   * Billing for all successfully-allocated resources begins (some types
  //     may have lower than the requested levels).
  //   * Databases can start using this instance partition.
  //   * The instance partition's allocated resource levels are readable via the
  //     API.
  //   * The instance partition's state becomes `READY`.
  //
  // The returned long-running operation will
  // have a name of the format
  // `<instance_partition_name>/operations/<operation_id>` and can be used to
  // track creation of the instance partition.  The
  // metadata field type is
  // [CreateInstancePartitionMetadata][google.spanner.admin.instance.v1.CreateInstancePartitionMetadata].
  // The response field type is
  // [InstancePartition][google.spanner.admin.instance.v1.InstancePartition], if
  // successful.
  rpc CreateInstancePartition(CreateInstancePartitionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/instances/*}/instancePartitions"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,instance_partition,instance_partition_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.spanner.admin.instance.v1.InstancePartition"
      metadata_type: "google.spanner.admin.instance.v1.CreateInstancePartitionMetadata"
    };
  }

  // Deletes an existing instance partition. Requires that the
  // instance partition is not used by any database or backup and is not the
  // default instance partition of an instance.
  //
  // Authorization requires `spanner.instancePartitions.delete` permission on
  // the resource
  // [name][google.spanner.admin.instance.v1.InstancePartition.name].
  rpc DeleteInstancePartition(DeleteInstancePartitionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/instances/*/instancePartitions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an instance partition, and begins allocating or releasing resources
  // as requested. The returned long-running operation can be used to track the
  // progress of updating the instance partition. If the named instance
  // partition does not exist, returns `NOT_FOUND`.
  //
  // Immediately upon completion of this request:
  //
  //   * For resource types for which a decrease in the instance partition's
  //   allocation has been requested, billing is based on the newly-requested
  //   level.
  //
  // Until completion of the returned operation:
  //
  //   * Cancelling the operation sets its metadata's
  //     [cancel_time][google.spanner.admin.instance.v1.UpdateInstancePartitionMetadata.cancel_time],
  //     and begins restoring resources to their pre-request values. The
  //     operation is guaranteed to succeed at undoing all resource changes,
  //     after which point it terminates with a `CANCELLED` status.
  //   * All other attempts to modify the instance partition are rejected.
  //   * Reading the instance partition via the API continues to give the
  //     pre-request resource levels.
  //
  // Upon completion of the returned operation:
  //
  //   * Billing begins for all successfully-allocated resources (some types
  //     may have lower than the requested levels).
  //   * All newly-reserved resources are available for serving the instance
  //     partition's tables.
  //   * The instance partition's new resource levels are readable via the API.
  //
  // The returned long-running operation will
  // have a name of the format
  // `<instance_partition_name>/operations/<operation_id>` and can be used to
  // track the instance partition modification. The
  // metadata field type is
  // [UpdateInstancePartitionMetadata][google.spanner.admin.instance.v1.UpdateInstancePartitionMetadata].
  // The response field type is
  // [InstancePartition][google.spanner.admin.instance.v1.InstancePartition], if
  // successful.
  //
  // Authorization requires `spanner.instancePartitions.update` permission on
  // the resource
  // [name][google.spanner.admin.instance.v1.InstancePartition.name].
  rpc UpdateInstancePartition(UpdateInstancePartitionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{instance_partition.name=projects/*/instances/*/instancePartitions/*}"
      body: "*"
    };
    option (google.api.method_signature) = "instance_partition,field_mask";
    option (google.longrunning.operation_info) = {
      response_type: "google.spanner.admin.instance.v1.InstancePartition"
      metadata_type: "google.spanner.admin.instance.v1.UpdateInstancePartitionMetadata"
    };
  }

  // Lists instance partition long-running operations in the given instance.
  // An instance partition operation has a name of the form
  // `projects/<project>/instances/<instance>/instancePartitions/<instance_partition>/operations/<operation>`.
  // The long-running operation
  // metadata field type
  // `metadata.type_url` describes the type of the metadata. Operations returned
  // include those that have completed/failed/canceled within the last 7 days,
  // and pending operations. Operations returned are ordered by
  // `operation.metadata.value.start_time` in descending order starting from the
  // most recently started operation.
  //
  // Authorization requires `spanner.instancePartitionOperations.list`
  // permission on the resource
  // [parent][google.spanner.admin.instance.v1.ListInstancePartitionOperationsRequest.parent].
  rpc ListInstancePartitionOperations(ListInstancePartitionOperationsRequest)
      returns (ListInstancePartitionOperationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/instances/*}/instancePartitionOperations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Moves an instance to the target instance configuration. You can use the
  // returned long-running operation to track
  // the progress of moving the instance.
  //
  // `MoveInstance` returns `FAILED_PRECONDITION` if the instance meets any of
  // the following criteria:
  //
  //   * Is undergoing a move to a different instance configuration
  //   * Has backups
  //   * Has an ongoing update
  //   * Contains any CMEK-enabled databases
  //   * Is a free trial instance
  //
  // While the operation is pending:
  //
  //   * All other attempts to modify the instance, including changes to its
  //     compute capacity, are rejected.
  //   * The following database and backup admin operations are rejected:
  //
  //     * `DatabaseAdmin.CreateDatabase`
  //     * `DatabaseAdmin.UpdateDatabaseDdl` (disabled if default_leader is
  //        specified in the request.)
  //     * `DatabaseAdmin.RestoreDatabase`
  //     * `DatabaseAdmin.CreateBackup`
  //     * `DatabaseAdmin.CopyBackup`
  //
  //   * Both the source and target instance configurations are subject to
  //     hourly compute and storage charges.
  //   * The instance might experience higher read-write latencies and a higher
  //     transaction abort rate. However, moving an instance doesn't cause any
  //     downtime.
  //
  // The returned long-running operation has
  // a name of the format
  // `<instance_name>/operations/<operation_id>` and can be used to track
  // the move instance operation. The
  // metadata field type is
  // [MoveInstanceMetadata][google.spanner.admin.instance.v1.MoveInstanceMetadata].
  // The response field type is
  // [Instance][google.spanner.admin.instance.v1.Instance],
  // if successful.
  // Cancelling the operation sets its metadata's
  // [cancel_time][google.spanner.admin.instance.v1.MoveInstanceMetadata.cancel_time].
  // Cancellation is not immediate because it involves moving any data
  // previously moved to the target instance configuration back to the original
  // instance configuration. You can use this operation to track the progress of
  // the cancellation. Upon successful completion of the cancellation, the
  // operation terminates with `CANCELLED` status.
  //
  // If not cancelled, upon completion of the returned operation:
  //
  //   * The instance successfully moves to the target instance
  //     configuration.
  //   * You are billed for compute and storage in target instance
  //   configuration.
  //
  // Authorization requires the `spanner.instances.update` permission on
  // the resource [instance][google.spanner.admin.instance.v1.Instance].
  //
  // For more details, see
  // [Move an instance](https://cloud.google.com/spanner/docs/move-instance).
  rpc MoveInstance(MoveInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/instances/*}:move"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.spanner.admin.instance.v1.MoveInstanceResponse"
      metadata_type: "google.spanner.admin.instance.v1.MoveInstanceMetadata"
    };
  }
}

message ReplicaInfo {
  // Indicates the type of replica.  See the [replica types
  // documentation](https://cloud.google.com/spanner/docs/replication#replica_types)
  // for more details.
  enum ReplicaType {
    // Not specified.
    TYPE_UNSPECIFIED = 0;

    // Read-write replicas support both reads and writes. These replicas:
    //
    // * Maintain a full copy of your data.
    // * Serve reads.
    // * Can vote whether to commit a write.
    // * Participate in leadership election.
    // * Are eligible to become a leader.
    READ_WRITE = 1;

    // Read-only replicas only support reads (not writes). Read-only replicas:
    //
    // * Maintain a full copy of your data.
    // * Serve reads.
    // * Do not participate in voting to commit writes.
    // * Are not eligible to become a leader.
    READ_ONLY = 2;

    // Witness replicas don't support reads but do participate in voting to
    // commit writes. Witness replicas:
    //
    // * Do not maintain a full copy of data.
    // * Do not serve reads.
    // * Vote whether to commit writes.
    // * Participate in leader election but are not eligible to become leader.
    WITNESS = 3;
  }

  // The location of the serving resources, e.g., "us-central1".
  string location = 1;

  // The type of replica.
  ReplicaType type = 2;

  // If true, this location is designated as the default leader location where
  // leader replicas are placed. See the [region types
  // documentation](https://cloud.google.com/spanner/docs/instances#region_types)
  // for more details.
  bool default_leader_location = 3;
}

// A possible configuration for a Cloud Spanner instance. Configurations
// define the geographic placement of nodes and their replication.
message InstanceConfig {
  option (google.api.resource) = {
    type: "spanner.googleapis.com/InstanceConfig"
    pattern: "projects/{project}/instanceConfigs/{instance_config}"
    plural: "instanceConfigs"
    singular: "instanceConfig"
  };

  // The type of this configuration.
  enum Type {
    // Unspecified.
    TYPE_UNSPECIFIED = 0;

    // Google-managed configuration.
    GOOGLE_MANAGED = 1;

    // User-managed configuration.
    USER_MANAGED = 2;
  }

  // Indicates the current state of the instance configuration.
  enum State {
    // Not specified.
    STATE_UNSPECIFIED = 0;

    // The instance configuration is still being created.
    CREATING = 1;

    // The instance configuration is fully created and ready to be used to
    // create instances.
    READY = 2;
  }

  // Describes the availability for free instances to be created in an instance
  // configuration.
  enum FreeInstanceAvailability {
    // Not specified.
    FREE_INSTANCE_AVAILABILITY_UNSPECIFIED = 0;

    // Indicates that free instances are available to be created in this
    // instance configuration.
    AVAILABLE = 1;

    // Indicates that free instances are not supported in this instance
    // configuration.
    UNSUPPORTED = 2;

    // Indicates that free instances are currently not available to be created
    // in this instance configuration.
    DISABLED = 3;

    // Indicates that additional free instances cannot be created in this
    // instance configuration because the project has reached its limit of free
    // instances.
    QUOTA_EXCEEDED = 4;
  }

  // Indicates the quorum type of this instance configuration.
  enum QuorumType {
    // Quorum type not specified.
    QUORUM_TYPE_UNSPECIFIED = 0;

    // An instance configuration tagged with `REGION` quorum type forms a write
    // quorum in a single region.
    REGION = 1;

    // An instance configuration tagged with the `DUAL_REGION` quorum type forms
    // a write quorum with exactly two read-write regions in a multi-region
    // configuration.
    //
    // This instance configuration requires failover in the event of
    // regional failures.
    DUAL_REGION = 2;

    // An instance configuration tagged with the `MULTI_REGION` quorum type
    // forms a write quorum from replicas that are spread across more than one
    // region in a multi-region configuration.
    MULTI_REGION = 3;
  }

  // A unique identifier for the instance configuration.  Values
  // are of the form
  // `projects/<project>/instanceConfigs/[a-z][-a-z0-9]*`.
  //
  // User instance configuration must start with `custom-`.
  string name = 1;

  // The name of this instance configuration as it appears in UIs.
  string display_name = 2;

  // Output only. Whether this instance configuration is a Google-managed or
  // user-managed configuration.
  Type config_type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The geographic placement of nodes in this instance configuration and their
  // replication properties.
  //
  // To create user-managed configurations, input
  // `replicas` must include all replicas in `replicas` of the `base_config`
  // and include one or more replicas in the `optional_replicas` of the
  // `base_config`.
  repeated ReplicaInfo replicas = 3;

  // Output only. The available optional replicas to choose from for
  // user-managed configurations. Populated for Google-managed configurations.
  repeated ReplicaInfo optional_replicas = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Base configuration name, e.g. projects/<project_name>/instanceConfigs/nam3,
  // based on which this configuration is created. Only set for user-managed
  // configurations. `base_config` must refer to a configuration of type
  // `GOOGLE_MANAGED` in the same project as this configuration.
  string base_config = 7 [(google.api.resource_reference) = {
    type: "spanner.googleapis.com/InstanceConfig"
  }];

  // Cloud Labels are a flexible and lightweight mechanism for organizing cloud
  // resources into groups that reflect a customer's organizational needs and
  // deployment strategies. Cloud Labels can be used to filter collections of
  // resources. They can be used to control how resource metrics are aggregated.
  // And they can be used as arguments to policy management rules (e.g. route,
  // firewall, load balancing, etc.).
  //
  //  * Label keys must be between 1 and 63 characters long and must conform to
  //    the following regular expression: `[a-z][a-z0-9_-]{0,62}`.
  //  * Label values must be between 0 and 63 characters long and must conform
  //    to the regular expression `[a-z0-9_-]{0,63}`.
  //  * No more than 64 labels can be associated with a given resource.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  //
  // If you plan to use labels in your own code, please note that additional
  // characters may be allowed in the future. Therefore, you are advised to use
  // an internal label representation, such as JSON, which doesn't rely upon
  // specific characters being disallowed.  For example, representing labels
  // as the string:  name + "_" + value  would prove problematic if we were to
  // allow "_" in a future release.
  map<string, string> labels = 8;

  // etag is used for optimistic concurrency control as a way
  // to help prevent simultaneous updates of a instance configuration from
  // overwriting each other. It is strongly suggested that systems make use of
  // the etag in the read-modify-write cycle to perform instance configuration
  // updates in order to avoid race conditions: An etag is returned in the
  // response which contains instance configurations, and systems are expected
  // to put that etag in the request to update instance configuration to ensure
  // that their change is applied to the same version of the instance
  // configuration. If no etag is provided in the call to update the instance
  // configuration, then the existing instance configuration is overwritten
  // blindly.
  string etag = 9;

  // Allowed values of the "default_leader" schema option for databases in
  // instances that use this instance configuration.
  repeated string leader_options = 4;

  // Output only. If true, the instance configuration is being created or
  // updated. If false, there are no ongoing operations for the instance
  // configuration.
  bool reconciling = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current instance configuration state. Applicable only for
  // `USER_MANAGED` configurations.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Describes whether free instances are available to be created
  // in this instance configuration.
  FreeInstanceAvailability free_instance_availability = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The `QuorumType` of the instance configuration.
  QuorumType quorum_type = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The storage limit in bytes per processing unit.
  int64 storage_limit_per_processing_unit = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ReplicaComputeCapacity describes the amount of server resources that are
// allocated to each replica identified by the replica selection.
message ReplicaComputeCapacity {
  // Required. Identifies replicas by specified properties.
  // All replicas in the selection have the same amount of compute capacity.
  ReplicaSelection replica_selection = 1
      [(google.api.field_behavior) = REQUIRED];

  // Compute capacity allocated to each replica identified by the specified
  // selection.
  // The unit is selected based on the unit used to specify the instance size
  // for non-autoscaling instances, or the unit used in autoscaling limit for
  // autoscaling instances.
  oneof compute_capacity {
    // The number of nodes allocated to each replica.
    //
    // This may be zero in API responses for instances that are not yet in
    // state `READY`.
    int32 node_count = 2;

    // The number of processing units allocated to each replica.
    //
    // This may be zero in API responses for instances that are not yet in
    // state `READY`.
    int32 processing_units = 3;
  }
}

// Autoscaling configuration for an instance.
message AutoscalingConfig {
  // The autoscaling limits for the instance. Users can define the minimum and
  // maximum compute capacity allocated to the instance, and the autoscaler will
  // only scale within that range. Users can either use nodes or processing
  // units to specify the limits, but should use the same unit to set both the
  // min_limit and max_limit.
  message AutoscalingLimits {
    // The minimum compute capacity for the instance.
    oneof min_limit {
      // Minimum number of nodes allocated to the instance. If set, this number
      // should be greater than or equal to 1.
      int32 min_nodes = 1;

      // Minimum number of processing units allocated to the instance. If set,
      // this number should be multiples of 1000.
      int32 min_processing_units = 2;
    }

    // The maximum compute capacity for the instance. The maximum compute
    // capacity should be less than or equal to 10X the minimum compute
    // capacity.
    oneof max_limit {
      // Maximum number of nodes allocated to the instance. If set, this number
      // should be greater than or equal to min_nodes.
      int32 max_nodes = 3;

      // Maximum number of processing units allocated to the instance. If set,
      // this number should be multiples of 1000 and be greater than or equal to
      // min_processing_units.
      int32 max_processing_units = 4;
    }
  }

  // The autoscaling targets for an instance.
  message AutoscalingTargets {
    // Required. The target high priority cpu utilization percentage that the
    // autoscaler should be trying to achieve for the instance. This number is
    // on a scale from 0 (no utilization) to 100 (full utilization). The valid
    // range is [10, 90] inclusive.
    int32 high_priority_cpu_utilization_percent = 1
        [(google.api.field_behavior) = REQUIRED];

    // Required. The target storage utilization percentage that the autoscaler
    // should be trying to achieve for the instance. This number is on a scale
    // from 0 (no utilization) to 100 (full utilization). The valid range is
    // [10, 99] inclusive.
    int32 storage_utilization_percent = 2
        [(google.api.field_behavior) = REQUIRED];
  }

  // AsymmetricAutoscalingOption specifies the scaling of replicas identified by
  // the given selection.
  message AsymmetricAutoscalingOption {
    // Overrides the top-level autoscaling configuration for the replicas
    // identified by `replica_selection`. All fields in this message are
    // optional. Any unspecified fields will use the corresponding values from
    // the top-level autoscaling configuration.
    message AutoscalingConfigOverrides {
      // Optional. If specified, overrides the min/max limit in the top-level
      // autoscaling configuration for the selected replicas.
      AutoscalingLimits autoscaling_limits = 1
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. If specified, overrides the autoscaling target
      // high_priority_cpu_utilization_percent in the top-level autoscaling
      // configuration for the selected replicas.
      int32 autoscaling_target_high_priority_cpu_utilization_percent = 2
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Required. Selects the replicas to which this AsymmetricAutoscalingOption
    // applies. Only read-only replicas are supported.
    ReplicaSelection replica_selection = 1
        [(google.api.field_behavior) = REQUIRED];

    // Optional. Overrides applied to the top-level autoscaling configuration
    // for the selected replicas.
    AutoscalingConfigOverrides overrides = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Autoscaling limits for an instance.
  AutoscalingLimits autoscaling_limits = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The autoscaling targets for an instance.
  AutoscalingTargets autoscaling_targets = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Optional asymmetric autoscaling options.
  // Replicas matching the replica selection criteria will be autoscaled
  // independently from other replicas. The autoscaler will scale the replicas
  // based on the utilization of replicas identified by the replica selection.
  // Replica selections should not overlap with each other.
  //
  // Other replicas (those do not match any replica selection) will be
  // autoscaled together and will have the same compute capacity allocated to
  // them.
  repeated AsymmetricAutoscalingOption asymmetric_autoscaling_options = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// An isolated set of Cloud Spanner resources on which databases can be hosted.
message Instance {
  option (google.api.resource) = {
    type: "spanner.googleapis.com/Instance"
    pattern: "projects/{project}/instances/{instance}"
    plural: "instances"
    singular: "instance"
  };

  // Indicates the current state of the instance.
  enum State {
    // Not specified.
    STATE_UNSPECIFIED = 0;

    // The instance is still being created. Resources may not be
    // available yet, and operations such as database creation may not
    // work.
    CREATING = 1;

    // The instance is fully created and ready to do work such as
    // creating databases.
    READY = 2;
  }

  // The type of this instance. The type can be used to distinguish product
  // variants, that can affect aspects like: usage restrictions, quotas and
  // billing. Currently this is used to distinguish FREE_INSTANCE vs PROVISIONED
  // instances.
  enum InstanceType {
    // Not specified.
    INSTANCE_TYPE_UNSPECIFIED = 0;

    // Provisioned instances have dedicated resources, standard usage limits and
    // support.
    PROVISIONED = 1;

    // Free instances provide no guarantee for dedicated resources,
    // [node_count, processing_units] should be 0. They come
    // with stricter usage limits and limited support.
    FREE_INSTANCE = 2;
  }

  // The edition selected for this instance. Different editions provide
  // different capabilities at different price points.
  enum Edition {
    // Edition not specified.
    EDITION_UNSPECIFIED = 0;

    // Standard edition.
    STANDARD = 1;

    // Enterprise edition.
    ENTERPRISE = 2;

    // Enterprise Plus edition.
    ENTERPRISE_PLUS = 3;
  }

  // Indicates the
  // [default backup
  // schedule](https://cloud.google.com/spanner/docs/backup#default-backup-schedules)
  // behavior for new databases within the instance.
  enum DefaultBackupScheduleType {
    // Not specified.
    DEFAULT_BACKUP_SCHEDULE_TYPE_UNSPECIFIED = 0;

    // A default backup schedule isn't created automatically when a new database
    // is created in the instance.
    NONE = 1;

    // A default backup schedule is created automatically when a new database
    // is created in the instance. The default backup schedule creates a full
    // backup every 24 hours. These full backups are retained for 7 days.
    // You can edit or delete the default backup schedule once it's created.
    AUTOMATIC = 2;
  }

  // Required. A unique identifier for the instance, which cannot be changed
  // after the instance is created. Values are of the form
  // `projects/<project>/instances/[a-z][-a-z0-9]*[a-z0-9]`. The final
  // segment of the name must be between 2 and 64 characters in length.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the instance's configuration. Values are of the form
  // `projects/<project>/instanceConfigs/<configuration>`. See
  // also [InstanceConfig][google.spanner.admin.instance.v1.InstanceConfig] and
  // [ListInstanceConfigs][google.spanner.admin.instance.v1.InstanceAdmin.ListInstanceConfigs].
  string config = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/InstanceConfig"
    }
  ];

  // Required. The descriptive name for this instance as it appears in UIs.
  // Must be unique per project and between 4 and 30 characters in length.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // The number of nodes allocated to this instance. At most, one of either
  // `node_count` or `processing_units` should be present in the message.
  //
  // Users can set the `node_count` field to specify the target number of nodes
  // allocated to the instance.
  //
  // If autoscaling is enabled, `node_count` is treated as an `OUTPUT_ONLY`
  // field and reflects the current number of nodes allocated to the instance.
  //
  // This might be zero in API responses for instances that are not yet in the
  // `READY` state.
  //
  //
  // For more information, see
  // [Compute capacity, nodes, and processing
  // units](https://cloud.google.com/spanner/docs/compute-capacity).
  int32 node_count = 5;

  // The number of processing units allocated to this instance. At most, one of
  // either `processing_units` or `node_count` should be present in the message.
  //
  // Users can set the `processing_units` field to specify the target number of
  // processing units allocated to the instance.
  //
  // If autoscaling is enabled, `processing_units` is treated as an
  // `OUTPUT_ONLY` field and reflects the current number of processing units
  // allocated to the instance.
  //
  // This might be zero in API responses for instances that are not yet in the
  // `READY` state.
  //
  //
  // For more information, see
  // [Compute capacity, nodes and processing
  // units](https://cloud.google.com/spanner/docs/compute-capacity).
  int32 processing_units = 9;

  // Output only. Lists the compute capacity per ReplicaSelection. A replica
  // selection identifies a set of replicas with common properties. Replicas
  // identified by a ReplicaSelection are scaled with the same compute capacity.
  repeated ReplicaComputeCapacity replica_compute_capacity = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The autoscaling configuration. Autoscaling is enabled if this
  // field is set. When autoscaling is enabled, node_count and processing_units
  // are treated as OUTPUT_ONLY fields and reflect the current compute capacity
  // allocated to the instance.
  AutoscalingConfig autoscaling_config = 17
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current instance state. For
  // [CreateInstance][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstance],
  // the state must be either omitted or set to `CREATING`. For
  // [UpdateInstance][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstance],
  // the state must be either omitted or set to `READY`.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Cloud Labels are a flexible and lightweight mechanism for organizing cloud
  // resources into groups that reflect a customer's organizational needs and
  // deployment strategies. Cloud Labels can be used to filter collections of
  // resources. They can be used to control how resource metrics are aggregated.
  // And they can be used as arguments to policy management rules (e.g. route,
  // firewall, load balancing, etc.).
  //
  //  * Label keys must be between 1 and 63 characters long and must conform to
  //    the following regular expression: `[a-z][a-z0-9_-]{0,62}`.
  //  * Label values must be between 0 and 63 characters long and must conform
  //    to the regular expression `[a-z0-9_-]{0,63}`.
  //  * No more than 64 labels can be associated with a given resource.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  //
  // If you plan to use labels in your own code, please note that additional
  // characters may be allowed in the future. And so you are advised to use an
  // internal label representation, such as JSON, which doesn't rely upon
  // specific characters being disallowed.  For example, representing labels
  // as the string:  name + "_" + value  would prove problematic if we were to
  // allow "_" in a future release.
  map<string, string> labels = 7;

  // The `InstanceType` of the current instance.
  InstanceType instance_type = 10;

  // Deprecated. This field is not populated.
  repeated string endpoint_uris = 8;

  // Output only. The time at which the instance was created.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the instance was most recently updated.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Free instance metadata. Only populated for free instances.
  FreeInstanceMetadata free_instance_metadata = 13;

  // Optional. The `Edition` of the current instance.
  Edition edition = 20 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Controls the default backup schedule behavior for new databases
  // within the instance. By default, a backup schedule is created automatically
  // when a new database is created in a new instance.
  //
  // Note that the `AUTOMATIC` value isn't permitted for free instances,
  // as backups and backup schedules aren't supported for free instances.
  //
  // In the `GetInstance` or `ListInstances` response, if the value of
  // `default_backup_schedule_type` isn't set, or set to `NONE`, Spanner doesn't
  // create a default backup schedule for new databases in the instance.
  DefaultBackupScheduleType default_backup_schedule_type = 23
      [(google.api.field_behavior) = OPTIONAL];
}

// The request for
// [ListInstanceConfigs][google.spanner.admin.instance.v1.InstanceAdmin.ListInstanceConfigs].
message ListInstanceConfigsRequest {
  // Required. The name of the project for which a list of supported instance
  // configurations is requested. Values are of the form
  // `projects/<project>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Number of instance configurations to be returned in the response. If 0 or
  // less, defaults to the server's maximum allowed page size.
  int32 page_size = 2;

  // If non-empty, `page_token` should contain a
  // [next_page_token][google.spanner.admin.instance.v1.ListInstanceConfigsResponse.next_page_token]
  // from a previous
  // [ListInstanceConfigsResponse][google.spanner.admin.instance.v1.ListInstanceConfigsResponse].
  string page_token = 3;
}

// The response for
// [ListInstanceConfigs][google.spanner.admin.instance.v1.InstanceAdmin.ListInstanceConfigs].
message ListInstanceConfigsResponse {
  // The list of requested instance configurations.
  repeated InstanceConfig instance_configs = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListInstanceConfigs][google.spanner.admin.instance.v1.InstanceAdmin.ListInstanceConfigs]
  // call to fetch more of the matching instance configurations.
  string next_page_token = 2;
}

// The request for
// [GetInstanceConfigRequest][google.spanner.admin.instance.v1.InstanceAdmin.GetInstanceConfig].
message GetInstanceConfigRequest {
  // Required. The name of the requested instance configuration. Values are of
  // the form `projects/<project>/instanceConfigs/<config>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/InstanceConfig"
    }
  ];
}

// The request for
// [CreateInstanceConfig][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstanceConfig].
message CreateInstanceConfigRequest {
  // Required. The name of the project in which to create the instance
  // configuration. Values are of the form `projects/<project>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The ID of the instance configuration to create. Valid identifiers
  // are of the form `custom-[-a-z0-9]*[a-z0-9]` and must be between 2 and 64
  // characters in length. The `custom-` prefix is required to avoid name
  // conflicts with Google-managed configurations.
  string instance_config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The `InstanceConfig` proto of the configuration to create.
  // `instance_config.name` must be
  // `<parent>/instanceConfigs/<instance_config_id>`.
  // `instance_config.base_config` must be a Google-managed configuration name,
  // e.g. <parent>/instanceConfigs/us-east1, <parent>/instanceConfigs/nam3.
  InstanceConfig instance_config = 3 [(google.api.field_behavior) = REQUIRED];

  // An option to validate, but not actually execute, a request,
  // and provide the same response.
  bool validate_only = 4;
}

// The request for
// [UpdateInstanceConfig][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstanceConfig].
message UpdateInstanceConfigRequest {
  // Required. The user instance configuration to update, which must always
  // include the instance configuration name. Otherwise, only fields mentioned
  // in
  // [update_mask][google.spanner.admin.instance.v1.UpdateInstanceConfigRequest.update_mask]
  // need be included. To prevent conflicts of concurrent updates,
  // [etag][google.spanner.admin.instance.v1.InstanceConfig.reconciling] can
  // be used.
  InstanceConfig instance_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying which fields in
  // [InstanceConfig][google.spanner.admin.instance.v1.InstanceConfig] should be
  // updated. The field mask must always be specified; this prevents any future
  // fields in [InstanceConfig][google.spanner.admin.instance.v1.InstanceConfig]
  // from being erased accidentally by clients that do not know about them. Only
  // display_name and labels can be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // An option to validate, but not actually execute, a request,
  // and provide the same response.
  bool validate_only = 3;
}

// The request for
// [DeleteInstanceConfig][google.spanner.admin.instance.v1.InstanceAdmin.DeleteInstanceConfig].
message DeleteInstanceConfigRequest {
  // Required. The name of the instance configuration to be deleted.
  // Values are of the form
  // `projects/<project>/instanceConfigs/<instance_config>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/InstanceConfig"
    }
  ];

  // Used for optimistic concurrency control as a way to help prevent
  // simultaneous deletes of an instance configuration from overwriting each
  // other. If not empty, the API
  // only deletes the instance configuration when the etag provided matches the
  // current status of the requested instance configuration. Otherwise, deletes
  // the instance configuration without checking the current status of the
  // requested instance configuration.
  string etag = 2;

  // An option to validate, but not actually execute, a request,
  // and provide the same response.
  bool validate_only = 3;
}

// The request for
// [ListInstanceConfigOperations][google.spanner.admin.instance.v1.InstanceAdmin.ListInstanceConfigOperations].
message ListInstanceConfigOperationsRequest {
  // Required. The project of the instance configuration operations.
  // Values are of the form `projects/<project>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // An expression that filters the list of returned operations.
  //
  // A filter expression consists of a field name, a
  // comparison operator, and a value for filtering.
  // The value must be a string, a number, or a boolean. The comparison operator
  // must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`.
  // Colon `:` is the contains operator. Filter rules are not case sensitive.
  //
  // The following fields in the Operation are eligible for filtering:
  //
  //   * `name` - The name of the long-running operation
  //   * `done` - False if the operation is in progress, else true.
  //   * `metadata.@type` - the type of metadata. For example, the type string
  //      for
  //      [CreateInstanceConfigMetadata][google.spanner.admin.instance.v1.CreateInstanceConfigMetadata]
  //      is
  //      `type.googleapis.com/google.spanner.admin.instance.v1.CreateInstanceConfigMetadata`.
  //   * `metadata.<field_name>` - any field in metadata.value.
  //      `metadata.@type` must be specified first, if filtering on metadata
  //      fields.
  //   * `error` - Error associated with the long-running operation.
  //   * `response.@type` - the type of response.
  //   * `response.<field_name>` - any field in response.value.
  //
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. By default, expressions are combined with AND logic. However,
  // you can specify AND, OR, and NOT logic explicitly.
  //
  // Here are a few examples:
  //
  //   * `done:true` - The operation is complete.
  //   * `(metadata.@type=` \
  //     `type.googleapis.com/google.spanner.admin.instance.v1.CreateInstanceConfigMetadata)
  //     AND` \
  //     `(metadata.instance_config.name:custom-config) AND` \
  //     `(metadata.progress.start_time < \"2021-03-28T14:50:00Z\") AND` \
  //     `(error:*)` - Return operations where:
  //     * The operation's metadata type is
  //     [CreateInstanceConfigMetadata][google.spanner.admin.instance.v1.CreateInstanceConfigMetadata].
  //     * The instance configuration name contains "custom-config".
  //     * The operation started before 2021-03-28T14:50:00Z.
  //     * The operation resulted in an error.
  string filter = 2;

  // Number of operations to be returned in the response. If 0 or
  // less, defaults to the server's maximum allowed page size.
  int32 page_size = 3;

  // If non-empty, `page_token` should contain a
  // [next_page_token][google.spanner.admin.instance.v1.ListInstanceConfigOperationsResponse.next_page_token]
  // from a previous
  // [ListInstanceConfigOperationsResponse][google.spanner.admin.instance.v1.ListInstanceConfigOperationsResponse]
  // to the same `parent` and with the same `filter`.
  string page_token = 4;
}

// The response for
// [ListInstanceConfigOperations][google.spanner.admin.instance.v1.InstanceAdmin.ListInstanceConfigOperations].
message ListInstanceConfigOperationsResponse {
  // The list of matching instance configuration long-running operations. Each
  // operation's name will be
  // prefixed by the name of the instance configuration. The operation's
  // metadata field type
  // `metadata.type_url` describes the type of the metadata.
  repeated google.longrunning.Operation operations = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListInstanceConfigOperations][google.spanner.admin.instance.v1.InstanceAdmin.ListInstanceConfigOperations]
  // call to fetch more of the matching metadata.
  string next_page_token = 2;
}

// The request for
// [GetInstance][google.spanner.admin.instance.v1.InstanceAdmin.GetInstance].
message GetInstanceRequest {
  // Required. The name of the requested instance. Values are of the form
  // `projects/<project>/instances/<instance>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // If field_mask is present, specifies the subset of
  // [Instance][google.spanner.admin.instance.v1.Instance] fields that should be
  // returned. If absent, all
  // [Instance][google.spanner.admin.instance.v1.Instance] fields are returned.
  google.protobuf.FieldMask field_mask = 2;
}

// The request for
// [CreateInstance][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstance].
message CreateInstanceRequest {
  // Required. The name of the project in which to create the instance. Values
  // are of the form `projects/<project>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The ID of the instance to create.  Valid identifiers are of the
  // form `[a-z][-a-z0-9]*[a-z0-9]` and must be between 2 and 64 characters in
  // length.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The instance to create.  The name may be omitted, but if
  // specified must be `<parent>/instances/<instance_id>`.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];
}

// The request for
// [ListInstances][google.spanner.admin.instance.v1.InstanceAdmin.ListInstances].
message ListInstancesRequest {
  // Required. The name of the project for which a list of instances is
  // requested. Values are of the form `projects/<project>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Number of instances to be returned in the response. If 0 or less, defaults
  // to the server's maximum allowed page size.
  int32 page_size = 2;

  // If non-empty, `page_token` should contain a
  // [next_page_token][google.spanner.admin.instance.v1.ListInstancesResponse.next_page_token]
  // from a previous
  // [ListInstancesResponse][google.spanner.admin.instance.v1.ListInstancesResponse].
  string page_token = 3;

  // An expression for filtering the results of the request. Filter rules are
  // case insensitive. The fields eligible for filtering are:
  //
  //   * `name`
  //   * `display_name`
  //   * `labels.key` where key is the name of a label
  //
  // Some examples of using filters are:
  //
  //   * `name:*` --> The instance has a name.
  //   * `name:Howl` --> The instance's name contains the string "howl".
  //   * `name:HOWL` --> Equivalent to above.
  //   * `NAME:howl` --> Equivalent to above.
  //   * `labels.env:*` --> The instance has the label "env".
  //   * `labels.env:dev` --> The instance has the label "env" and the value of
  //                        the label contains the string "dev".
  //   * `name:howl labels.env:dev` --> The instance's name contains "howl" and
  //                                  it has the label "env" with its value
  //                                  containing "dev".
  string filter = 4;

  // Deadline used while retrieving metadata for instances.
  // Instances whose metadata cannot be retrieved within this deadline will be
  // added to
  // [unreachable][google.spanner.admin.instance.v1.ListInstancesResponse.unreachable]
  // in
  // [ListInstancesResponse][google.spanner.admin.instance.v1.ListInstancesResponse].
  google.protobuf.Timestamp instance_deadline = 5;
}

// The response for
// [ListInstances][google.spanner.admin.instance.v1.InstanceAdmin.ListInstances].
message ListInstancesResponse {
  // The list of requested instances.
  repeated Instance instances = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListInstances][google.spanner.admin.instance.v1.InstanceAdmin.ListInstances]
  // call to fetch more of the matching instances.
  string next_page_token = 2;

  // The list of unreachable instances.
  // It includes the names of instances whose metadata could not be retrieved
  // within
  // [instance_deadline][google.spanner.admin.instance.v1.ListInstancesRequest.instance_deadline].
  repeated string unreachable = 3;
}

// The request for
// [UpdateInstance][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstance].
message UpdateInstanceRequest {
  // Required. The instance to update, which must always include the instance
  // name.  Otherwise, only fields mentioned in
  // [field_mask][google.spanner.admin.instance.v1.UpdateInstanceRequest.field_mask]
  // need be included.
  Instance instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying which fields in
  // [Instance][google.spanner.admin.instance.v1.Instance] should be updated.
  // The field mask must always be specified; this prevents any future fields in
  // [Instance][google.spanner.admin.instance.v1.Instance] from being erased
  // accidentally by clients that do not know about them.
  google.protobuf.FieldMask field_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request for
// [DeleteInstance][google.spanner.admin.instance.v1.InstanceAdmin.DeleteInstance].
message DeleteInstanceRequest {
  // Required. The name of the instance to be deleted. Values are of the form
  // `projects/<project>/instances/<instance>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];
}

// Metadata type for the operation returned by
// [CreateInstance][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstance].
message CreateInstanceMetadata {
  // The instance being created.
  Instance instance = 1;

  // The time at which the
  // [CreateInstance][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstance]
  // request was received.
  google.protobuf.Timestamp start_time = 2;

  // The time at which this operation was cancelled. If set, this operation is
  // in the process of undoing itself (which is guaranteed to succeed) and
  // cannot be cancelled again.
  google.protobuf.Timestamp cancel_time = 3;

  // The time at which this operation failed or was completed successfully.
  google.protobuf.Timestamp end_time = 4;

  // The expected fulfillment period of this create operation.
  FulfillmentPeriod expected_fulfillment_period = 5;
}

// Metadata type for the operation returned by
// [UpdateInstance][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstance].
message UpdateInstanceMetadata {
  // The desired end state of the update.
  Instance instance = 1;

  // The time at which
  // [UpdateInstance][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstance]
  // request was received.
  google.protobuf.Timestamp start_time = 2;

  // The time at which this operation was cancelled. If set, this operation is
  // in the process of undoing itself (which is guaranteed to succeed) and
  // cannot be cancelled again.
  google.protobuf.Timestamp cancel_time = 3;

  // The time at which this operation failed or was completed successfully.
  google.protobuf.Timestamp end_time = 4;

  // The expected fulfillment period of this update operation.
  FulfillmentPeriod expected_fulfillment_period = 5;
}

// Free instance specific metadata that is kept even after an instance has been
// upgraded for tracking purposes.
message FreeInstanceMetadata {
  // Allows users to change behavior when a free instance expires.
  enum ExpireBehavior {
    // Not specified.
    EXPIRE_BEHAVIOR_UNSPECIFIED = 0;

    // When the free instance expires, upgrade the instance to a provisioned
    // instance.
    FREE_TO_PROVISIONED = 1;

    // When the free instance expires, disable the instance, and delete it
    // after the grace period passes if it has not been upgraded.
    REMOVE_AFTER_GRACE_PERIOD = 2;
  }

  // Output only. Timestamp after which the instance will either be upgraded or
  // scheduled for deletion after a grace period. ExpireBehavior is used to
  // choose between upgrading or scheduling the free instance for deletion. This
  // timestamp is set during the creation of a free instance.
  google.protobuf.Timestamp expire_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If present, the timestamp at which the free instance was
  // upgraded to a provisioned instance.
  google.protobuf.Timestamp upgrade_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Specifies the expiration behavior of a free instance. The default of
  // ExpireBehavior is `REMOVE_AFTER_GRACE_PERIOD`. This can be modified during
  // or after creation, and before expiration.
  ExpireBehavior expire_behavior = 3;
}

// Metadata type for the operation returned by
// [CreateInstanceConfig][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstanceConfig].
message CreateInstanceConfigMetadata {
  // The target instance configuration end state.
  InstanceConfig instance_config = 1;

  // The progress of the
  // [CreateInstanceConfig][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstanceConfig]
  // operation.
  OperationProgress progress = 2;

  // The time at which this operation was cancelled.
  google.protobuf.Timestamp cancel_time = 3;
}

// Metadata type for the operation returned by
// [UpdateInstanceConfig][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstanceConfig].
message UpdateInstanceConfigMetadata {
  // The desired instance configuration after updating.
  InstanceConfig instance_config = 1;

  // The progress of the
  // [UpdateInstanceConfig][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstanceConfig]
  // operation.
  OperationProgress progress = 2;

  // The time at which this operation was cancelled.
  google.protobuf.Timestamp cancel_time = 3;
}

// An isolated set of Cloud Spanner resources that databases can define
// placements on.
message InstancePartition {
  option (google.api.resource) = {
    type: "spanner.googleapis.com/InstancePartition"
    pattern: "projects/{project}/instances/{instance}/instancePartitions/{instance_partition}"
    plural: "instancePartitions"
    singular: "instancePartition"
  };

  // Indicates the current state of the instance partition.
  enum State {
    // Not specified.
    STATE_UNSPECIFIED = 0;

    // The instance partition is still being created. Resources may not be
    // available yet, and operations such as creating placements using this
    // instance partition may not work.
    CREATING = 1;

    // The instance partition is fully created and ready to do work such as
    // creating placements and using in databases.
    READY = 2;
  }

  // Required. A unique identifier for the instance partition. Values are of the
  // form
  // `projects/<project>/instances/<instance>/instancePartitions/[a-z][-a-z0-9]*[a-z0-9]`.
  // The final segment of the name must be between 2 and 64 characters in
  // length. An instance partition's name cannot be changed after the instance
  // partition is created.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the instance partition's configuration. Values are of
  // the form `projects/<project>/instanceConfigs/<configuration>`. See also
  // [InstanceConfig][google.spanner.admin.instance.v1.InstanceConfig] and
  // [ListInstanceConfigs][google.spanner.admin.instance.v1.InstanceAdmin.ListInstanceConfigs].
  string config = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/InstanceConfig"
    }
  ];

  // Required. The descriptive name for this instance partition as it appears in
  // UIs. Must be unique per project and between 4 and 30 characters in length.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Compute capacity defines amount of server and storage resources that are
  // available to the databases in an instance partition. At most, one of either
  // `node_count` or` processing_units` should be present in the message. For
  // more information, see
  // [Compute capacity, nodes, and processing
  // units](https://cloud.google.com/spanner/docs/compute-capacity).
  oneof compute_capacity {
    // The number of nodes allocated to this instance partition.
    //
    // Users can set the `node_count` field to specify the target number of
    // nodes allocated to the instance partition.
    //
    // This may be zero in API responses for instance partitions that are not
    // yet in state `READY`.
    int32 node_count = 5;

    // The number of processing units allocated to this instance partition.
    //
    // Users can set the `processing_units` field to specify the target number
    // of processing units allocated to the instance partition.
    //
    // This might be zero in API responses for instance partitions that are not
    // yet in the `READY` state.
    int32 processing_units = 6;
  }

  // Output only. The current instance partition state.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the instance partition was created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the instance partition was most recently
  // updated.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of the databases that reference this
  // instance partition. Referencing databases should share the parent instance.
  // The existence of any referencing database prevents the instance partition
  // from being deleted.
  repeated string referencing_databases = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Deprecated: This field is not populated.
  // Output only. The names of the backups that reference this instance
  // partition. Referencing backups should share the parent instance. The
  // existence of any referencing backup prevents the instance partition from
  // being deleted.
  repeated string referencing_backups = 11
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Used for optimistic concurrency control as a way
  // to help prevent simultaneous updates of a instance partition from
  // overwriting each other. It is strongly suggested that systems make use of
  // the etag in the read-modify-write cycle to perform instance partition
  // updates in order to avoid race conditions: An etag is returned in the
  // response which contains instance partitions, and systems are expected to
  // put that etag in the request to update instance partitions to ensure that
  // their change will be applied to the same version of the instance partition.
  // If no etag is provided in the call to update instance partition, then the
  // existing instance partition is overwritten blindly.
  string etag = 12;
}

// Metadata type for the operation returned by
// [CreateInstancePartition][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstancePartition].
message CreateInstancePartitionMetadata {
  // The instance partition being created.
  InstancePartition instance_partition = 1;

  // The time at which the
  // [CreateInstancePartition][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstancePartition]
  // request was received.
  google.protobuf.Timestamp start_time = 2;

  // The time at which this operation was cancelled. If set, this operation is
  // in the process of undoing itself (which is guaranteed to succeed) and
  // cannot be cancelled again.
  google.protobuf.Timestamp cancel_time = 3;

  // The time at which this operation failed or was completed successfully.
  google.protobuf.Timestamp end_time = 4;
}

// The request for
// [CreateInstancePartition][google.spanner.admin.instance.v1.InstanceAdmin.CreateInstancePartition].
message CreateInstancePartitionRequest {
  // Required. The name of the instance in which to create the instance
  // partition. Values are of the form
  // `projects/<project>/instances/<instance>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // Required. The ID of the instance partition to create. Valid identifiers are
  // of the form `[a-z][-a-z0-9]*[a-z0-9]` and must be between 2 and 64
  // characters in length.
  string instance_partition_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The instance partition to create. The instance_partition.name may
  // be omitted, but if specified must be
  // `<parent>/instancePartitions/<instance_partition_id>`.
  InstancePartition instance_partition = 3
      [(google.api.field_behavior) = REQUIRED];
}

// The request for
// [DeleteInstancePartition][google.spanner.admin.instance.v1.InstanceAdmin.DeleteInstancePartition].
message DeleteInstancePartitionRequest {
  // Required. The name of the instance partition to be deleted.
  // Values are of the form
  // `projects/{project}/instances/{instance}/instancePartitions/{instance_partition}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/InstancePartition"
    }
  ];

  // Optional. If not empty, the API only deletes the instance partition when
  // the etag provided matches the current status of the requested instance
  // partition. Otherwise, deletes the instance partition without checking the
  // current status of the requested instance partition.
  string etag = 2;
}

// The request for
// [GetInstancePartition][google.spanner.admin.instance.v1.InstanceAdmin.GetInstancePartition].
message GetInstancePartitionRequest {
  // Required. The name of the requested instance partition. Values are of
  // the form
  // `projects/{project}/instances/{instance}/instancePartitions/{instance_partition}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/InstancePartition"
    }
  ];
}

// The request for
// [UpdateInstancePartition][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstancePartition].
message UpdateInstancePartitionRequest {
  // Required. The instance partition to update, which must always include the
  // instance partition name. Otherwise, only fields mentioned in
  // [field_mask][google.spanner.admin.instance.v1.UpdateInstancePartitionRequest.field_mask]
  // need be included.
  InstancePartition instance_partition = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying which fields in
  // [InstancePartition][google.spanner.admin.instance.v1.InstancePartition]
  // should be updated. The field mask must always be specified; this prevents
  // any future fields in
  // [InstancePartition][google.spanner.admin.instance.v1.InstancePartition]
  // from being erased accidentally by clients that do not know about them.
  google.protobuf.FieldMask field_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Metadata type for the operation returned by
// [UpdateInstancePartition][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstancePartition].
message UpdateInstancePartitionMetadata {
  // The desired end state of the update.
  InstancePartition instance_partition = 1;

  // The time at which
  // [UpdateInstancePartition][google.spanner.admin.instance.v1.InstanceAdmin.UpdateInstancePartition]
  // request was received.
  google.protobuf.Timestamp start_time = 2;

  // The time at which this operation was cancelled. If set, this operation is
  // in the process of undoing itself (which is guaranteed to succeed) and
  // cannot be cancelled again.
  google.protobuf.Timestamp cancel_time = 3;

  // The time at which this operation failed or was completed successfully.
  google.protobuf.Timestamp end_time = 4;
}

// The request for
// [ListInstancePartitions][google.spanner.admin.instance.v1.InstanceAdmin.ListInstancePartitions].
message ListInstancePartitionsRequest {
  // Required. The instance whose instance partitions should be listed. Values
  // are of the form `projects/<project>/instances/<instance>`. Use `{instance}
  // = '-'` to list instance partitions for all Instances in a project, e.g.,
  // `projects/myproject/instances/-`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // Number of instance partitions to be returned in the response. If 0 or less,
  // defaults to the server's maximum allowed page size.
  int32 page_size = 2;

  // If non-empty, `page_token` should contain a
  // [next_page_token][google.spanner.admin.instance.v1.ListInstancePartitionsResponse.next_page_token]
  // from a previous
  // [ListInstancePartitionsResponse][google.spanner.admin.instance.v1.ListInstancePartitionsResponse].
  string page_token = 3;

  // Optional. Deadline used while retrieving metadata for instance partitions.
  // Instance partitions whose metadata cannot be retrieved within this deadline
  // will be added to
  // [unreachable][google.spanner.admin.instance.v1.ListInstancePartitionsResponse.unreachable]
  // in
  // [ListInstancePartitionsResponse][google.spanner.admin.instance.v1.ListInstancePartitionsResponse].
  google.protobuf.Timestamp instance_partition_deadline = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// The response for
// [ListInstancePartitions][google.spanner.admin.instance.v1.InstanceAdmin.ListInstancePartitions].
message ListInstancePartitionsResponse {
  // The list of requested instancePartitions.
  repeated InstancePartition instance_partitions = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListInstancePartitions][google.spanner.admin.instance.v1.InstanceAdmin.ListInstancePartitions]
  // call to fetch more of the matching instance partitions.
  string next_page_token = 2;

  // The list of unreachable instances or instance partitions.
  // It includes the names of instances or instance partitions whose metadata
  // could not be retrieved within
  // [instance_partition_deadline][google.spanner.admin.instance.v1.ListInstancePartitionsRequest.instance_partition_deadline].
  repeated string unreachable = 3;
}

// The request for
// [ListInstancePartitionOperations][google.spanner.admin.instance.v1.InstanceAdmin.ListInstancePartitionOperations].
message ListInstancePartitionOperationsRequest {
  // Required. The parent instance of the instance partition operations.
  // Values are of the form `projects/<project>/instances/<instance>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // Optional. An expression that filters the list of returned operations.
  //
  // A filter expression consists of a field name, a
  // comparison operator, and a value for filtering.
  // The value must be a string, a number, or a boolean. The comparison operator
  // must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`.
  // Colon `:` is the contains operator. Filter rules are not case sensitive.
  //
  // The following fields in the Operation are eligible for filtering:
  //
  //   * `name` - The name of the long-running operation
  //   * `done` - False if the operation is in progress, else true.
  //   * `metadata.@type` - the type of metadata. For example, the type string
  //      for
  //      [CreateInstancePartitionMetadata][google.spanner.admin.instance.v1.CreateInstancePartitionMetadata]
  //      is
  //      `type.googleapis.com/google.spanner.admin.instance.v1.CreateInstancePartitionMetadata`.
  //   * `metadata.<field_name>` - any field in metadata.value.
  //      `metadata.@type` must be specified first, if filtering on metadata
  //      fields.
  //   * `error` - Error associated with the long-running operation.
  //   * `response.@type` - the type of response.
  //   * `response.<field_name>` - any field in response.value.
  //
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. By default, expressions are combined with AND logic. However,
  // you can specify AND, OR, and NOT logic explicitly.
  //
  // Here are a few examples:
  //
  //   * `done:true` - The operation is complete.
  //   * `(metadata.@type=` \
  //     `type.googleapis.com/google.spanner.admin.instance.v1.CreateInstancePartitionMetadata)
  //     AND` \
  //     `(metadata.instance_partition.name:custom-instance-partition) AND` \
  //     `(metadata.start_time < \"2021-03-28T14:50:00Z\") AND` \
  //     `(error:*)` - Return operations where:
  //     * The operation's metadata type is
  //     [CreateInstancePartitionMetadata][google.spanner.admin.instance.v1.CreateInstancePartitionMetadata].
  //     * The instance partition name contains "custom-instance-partition".
  //     * The operation started before 2021-03-28T14:50:00Z.
  //     * The operation resulted in an error.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of operations to be returned in the response. If 0 or
  // less, defaults to the server's maximum allowed page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If non-empty, `page_token` should contain a
  // [next_page_token][google.spanner.admin.instance.v1.ListInstancePartitionOperationsResponse.next_page_token]
  // from a previous
  // [ListInstancePartitionOperationsResponse][google.spanner.admin.instance.v1.ListInstancePartitionOperationsResponse]
  // to the same `parent` and with the same `filter`.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Deadline used while retrieving metadata for instance partition
  // operations. Instance partitions whose operation metadata cannot be
  // retrieved within this deadline will be added to
  // [unreachable_instance_partitions][google.spanner.admin.instance.v1.ListInstancePartitionOperationsResponse.unreachable_instance_partitions]
  // in
  // [ListInstancePartitionOperationsResponse][google.spanner.admin.instance.v1.ListInstancePartitionOperationsResponse].
  google.protobuf.Timestamp instance_partition_deadline = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// The response for
// [ListInstancePartitionOperations][google.spanner.admin.instance.v1.InstanceAdmin.ListInstancePartitionOperations].
message ListInstancePartitionOperationsResponse {
  // The list of matching instance partition long-running operations. Each
  // operation's name will be
  // prefixed by the instance partition's name. The operation's
  // metadata field type
  // `metadata.type_url` describes the type of the metadata.
  repeated google.longrunning.Operation operations = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListInstancePartitionOperations][google.spanner.admin.instance.v1.InstanceAdmin.ListInstancePartitionOperations]
  // call to fetch more of the matching metadata.
  string next_page_token = 2;

  // The list of unreachable instance partitions.
  // It includes the names of instance partitions whose operation metadata could
  // not be retrieved within
  // [instance_partition_deadline][google.spanner.admin.instance.v1.ListInstancePartitionOperationsRequest.instance_partition_deadline].
  repeated string unreachable_instance_partitions = 3;
}

// The request for
// [MoveInstance][google.spanner.admin.instance.v1.InstanceAdmin.MoveInstance].
message MoveInstanceRequest {
  // Required. The instance to move.
  // Values are of the form `projects/<project>/instances/<instance>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/Instance"
    }
  ];

  // Required. The target instance configuration where to move the instance.
  // Values are of the form `projects/<project>/instanceConfigs/<config>`.
  string target_config = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "spanner.googleapis.com/InstanceConfig"
    }
  ];
}

// The response for
// [MoveInstance][google.spanner.admin.instance.v1.InstanceAdmin.MoveInstance].
message MoveInstanceResponse {}

// Metadata type for the operation returned by
// [MoveInstance][google.spanner.admin.instance.v1.InstanceAdmin.MoveInstance].
message MoveInstanceMetadata {
  // The target instance configuration where to move the instance.
  // Values are of the form `projects/<project>/instanceConfigs/<config>`.
  string target_config = 1;

  // The progress of the
  // [MoveInstance][google.spanner.admin.instance.v1.InstanceAdmin.MoveInstance]
  // operation.
  // [progress_percent][google.spanner.admin.instance.v1.OperationProgress.progress_percent]
  // is reset when cancellation is requested.
  OperationProgress progress = 2;

  // The time at which this operation was cancelled.
  google.protobuf.Timestamp cancel_time = 3;
}
