# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "executor_proto",
    srcs = [
        "cloud_executor.proto",
    ],
    deps = [
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/spanner/admin/database/v1:database_proto",
        "//google/spanner/admin/instance/v1:instance_proto",
        "//google/spanner/v1:spanner_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "executor_proto_with_info",
    deps = [
        ":executor_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "executor_java_proto",
    deps = [":executor_proto"],
)

java_grpc_library(
    name = "executor_java_grpc",
    srcs = [":executor_proto"],
    deps = [":executor_java_proto"],
)

java_gapic_library(
    name = "executor_java_gapic",
    srcs = [":executor_proto_with_info"],
    gapic_yaml = "spanner_executor_gapic.yaml",
    grpc_service_config = "spanner_cloud_executor_grpc_service_config.json",
    service_yaml = "spanner_cloud_executor.yaml",
    test_deps = [
        ":executor_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":executor_java_proto",
        "//google/api:api_java_proto",
        "//google/spanner/admin/database/v1:database_java_proto",
    ],
)

java_gapic_test(
    name = "executor_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.spanner.executor.v1.SpannerExecutorProxyClientTest",
    ],
    runtime_deps = [":executor_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-spanner-executor-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":executor_java_gapic",
        ":executor_java_grpc",
        ":executor_java_proto",
        ":executor_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "executor_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/spanner/executor/apiv1/executorpb",
    protos = [":executor_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/spanner/admin/database/v1:database_go_proto",
        "//google/spanner/admin/instance/v1:instance_go_proto",
        "//google/spanner/v1:spanner_go_proto",
    ],
)

go_gapic_library(
    name = "executor_go_gapic",
    srcs = [":executor_proto_with_info"],
    grpc_service_config = "spanner_cloud_executor_grpc_service_config.json",
    importpath = "cloud.google.com/go/spanner/executor/apiv1;executor",
    metadata = True,
    release_level = "beta",
    service_yaml = "spanner_cloud_executor.yaml",
    transport = "grpc",
    deps = [
        ":executor_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/spanner/admin/database/v1:database_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-spanner-executor-v1-go",
    deps = [
        ":executor_go_gapic",
        ":executor_go_gapic_srcjar-test.srcjar",
        ":executor_go_gapic_srcjar-metadata.srcjar",
        ":executor_go_gapic_srcjar-snippets.srcjar",
        ":executor_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",
    "py_test",
)

py_proto_library(
    name = "executor_py_proto",
    deps = [":executor_proto"],
)

py_gapic_library(
    name = "executor_py_gapic",
    srcs = [":executor_proto"],
    grpc_service_config = "spanner_cloud_executor_grpc_service_config.json",
    service_yaml = "spanner_cloud_executor.yaml",
    transport = "grpc",
    deps = [
      ":executor_py_proto",
    ],
)

py_test(
    name = "executor_py_gapic_test",
    srcs = [
        "executor_py_gapic_pytest.py",
        "executor_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [
      ":executor_py_gapic",
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "spanner-executor-v1-py",
    deps = [
        ":executor_py_gapic",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "executor_nodejs_gapic",
    package_name = "@google-cloud/executor",
    src = ":executor_proto_with_info",
    extra_protoc_parameters = ["metadata","template=typescript_gapic",],
    grpc_service_config = "spanner_cloud_executor_grpc_service_config.json",
    package = "google.spanner.executor.v1",
    service_yaml = "spanner_cloud_executor.yaml",
    transport = "grpc",
    deps = [
    ],
)

nodejs_gapic_assembly_pkg(
    name = "spanner-executor-v1-nodejs",
    deps = [
        ":executor_nodejs_gapic",
        ":executor_proto",
        "//google/spanner/admin/database/v1:database_proto",
        "//google/spanner/admin/instance/v1:instance_proto",
        "//google/spanner/v1:spanner_proto",
    ],
)