{"methodConfig": [{"name": [{"service": "google.spanner.v1.<PERSON><PERSON>", "method": "ExecuteStreamingSql"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "StreamingRead"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "BatchWrite"}], "timeout": "3600s"}, {"name": [{"service": "google.spanner.v1.<PERSON><PERSON>", "method": "Commit"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "ListSessions"}], "timeout": "3600s", "retryPolicy": {"initialBackoff": "0.250s", "maxBackoff": "32s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}, {"name": [{"service": "google.spanner.v1.<PERSON><PERSON>", "method": "BatchCreateSessions"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.250s", "maxBackoff": "32s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}, {"name": [{"service": "google.spanner.v1.<PERSON><PERSON>", "method": "CreateSession"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "GetSession"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "DeleteSession"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "ExecuteSql"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "ExecuteBatchDml"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "Read"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "BeginTransaction"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "Rollback"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "PartitionQuery"}, {"service": "google.spanner.v1.<PERSON><PERSON>", "method": "PartitionRead"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "0.250s", "maxBackoff": "32s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}]}