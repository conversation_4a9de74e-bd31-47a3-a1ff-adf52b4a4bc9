# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "spanner_proto",
    srcs = [
        "commit_response.proto",
        "keys.proto",
        "mutation.proto",
        "query_plan.proto",
        "result_set.proto",
        "spanner.proto",
        "transaction.proto",
        "type.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "spanner_proto_with_info",
    deps = [
        ":spanner_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "spanner_java_proto",
    deps = [":spanner_proto"],
)

java_grpc_library(
    name = "spanner_java_grpc",
    srcs = [":spanner_proto"],
    deps = [":spanner_java_proto"],
)

java_gapic_library(
    name = "spanner_java_gapic",
    srcs = [":spanner_proto_with_info"],
    gapic_yaml = "spanner_gapic.yaml",
    grpc_service_config = "spanner_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    test_deps = [
        ":spanner_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":spanner_java_proto",
    ],
)

java_gapic_test(
    name = "spanner_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.spanner.v1.SpannerClientHttpJsonTest",
        "com.google.cloud.spanner.v1.SpannerClientTest",
    ],
    runtime_deps = [":spanner_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-spanner-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":spanner_java_gapic",
        ":spanner_java_grpc",
        ":spanner_java_proto",
        ":spanner_proto",
    ],
)

go_proto_library(
    name = "spanner_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/spanner/apiv1/spannerpb",
    protos = [":spanner_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "spanner_go_gapic",
    srcs = [":spanner_proto_with_info"],
    grpc_service_config = "spanner_grpc_service_config.json",
    importpath = "cloud.google.com/go/spanner/apiv1;spanner",
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    deps = [
        ":spanner_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-spanner-v1-go",
    deps = [
        ":spanner_go_gapic",
        ":spanner_go_gapic_srcjar-snippets.srcjar",
        ":spanner_go_gapic_srcjar-test.srcjar",
        ":spanner_go_proto",
    ],
)

py_gapic_library(
    name = "spanner_py_gapic",
    srcs = [":spanner_proto"],
    grpc_service_config = "spanner_grpc_service_config.json",
    opt_args = ["python-gapic-namespace=google.cloud"],
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "spanner_py_gapic_test",
    srcs = [
        "spanner_py_gapic_pytest.py",
        "spanner_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":spanner_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "spanner-v1-py",
    deps = [
        ":spanner_py_gapic",
    ],
)

php_proto_library(
    name = "spanner_php_proto",
    deps = [":spanner_proto"],
)

php_gapic_library(
    name = "spanner_php_gapic",
    srcs = [":spanner_proto_with_info"],
    grpc_service_config = "spanner_grpc_service_config.json",
    migration_mode = "MIGRATING",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    deps = [":spanner_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-spanner-v1-php",
    deps = [
        ":spanner_php_gapic",
        ":spanner_php_proto",
    ],
)

nodejs_gapic_library(
    name = "spanner_nodejs_gapic",
    package_name = "@google-cloud/spanner",
    src = ":spanner_proto_with_info",
    extra_protoc_parameters = [
        "metadata",
        "template=typescript_gapic",
    ],
    grpc_service_config = "spanner_grpc_service_config.json",
    handwritten_layer = True,
    main_service = "spanner",
    package = "google.spanner.v1",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "spanner-v1-nodejs",
    deps = [
        ":spanner_nodejs_gapic",
        ":spanner_proto",
    ],
)

ruby_proto_library(
    name = "spanner_ruby_proto",
    deps = [":spanner_proto"],
)

ruby_grpc_library(
    name = "spanner_ruby_grpc",
    srcs = [":spanner_proto"],
    deps = [":spanner_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "spanner_ruby_gapic",
    srcs = [":spanner_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-spanner-v1",
        "ruby-cloud-env-prefix=SPANNER",
        "ruby-cloud-product-url=https://cloud.google.com/spanner",
        "ruby-cloud-api-id=spanner.googleapis.com",
        "ruby-cloud-api-shortname=spanner",
    ],
    grpc_service_config = "spanner_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Spanner is a managed, mission-critical, globally consistent and scalable relational database service.",
    ruby_cloud_title = "Cloud Spanner V1",
    service_yaml = "spanner.yaml",
    deps = [
        ":spanner_ruby_grpc",
        ":spanner_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-spanner-v1-ruby",
    deps = [
        ":spanner_ruby_gapic",
        ":spanner_ruby_grpc",
        ":spanner_ruby_proto",
    ],
)

csharp_proto_library(
    name = "spanner_csharp_proto",
    deps = [":spanner_proto"],
)

csharp_grpc_library(
    name = "spanner_csharp_grpc",
    srcs = [":spanner_proto"],
    deps = [":spanner_csharp_proto"],
)

csharp_gapic_library(
    name = "spanner_csharp_gapic",
    srcs = [":spanner_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "spanner_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "spanner.yaml",
    deps = [
        ":spanner_csharp_grpc",
        ":spanner_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-spanner-v1-csharp",
    deps = [
        ":spanner_csharp_gapic",
        ":spanner_csharp_grpc",
        ":spanner_csharp_proto",
    ],
)

cc_proto_library(
    name = "spanner_cc_proto",
    deps = [":spanner_proto"],
)

cc_grpc_library(
    name = "spanner_cc_grpc",
    srcs = [":spanner_proto"],
    grpc_only = True,
    deps = [":spanner_cc_proto"],
)
