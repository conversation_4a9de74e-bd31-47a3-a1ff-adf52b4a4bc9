// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.spanner.v1;

import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.Spanner.V1";
option go_package = "cloud.google.com/go/spanner/apiv1/spannerpb;spannerpb";
option java_multiple_files = true;
option java_outer_classname = "QueryPlanProto";
option java_package = "com.google.spanner.v1";
option php_namespace = "Google\\Cloud\\Spanner\\V1";
option ruby_package = "Google::Cloud::Spanner::V1";

// Node information for nodes appearing in a [QueryPlan.plan_nodes][google.spanner.v1.QueryPlan.plan_nodes].
message PlanNode {
  // The kind of [PlanNode][google.spanner.v1.PlanNode]. Distinguishes between the two different kinds of
  // nodes that can appear in a query plan.
  enum Kind {
    // Not specified.
    KIND_UNSPECIFIED = 0;

    // Denotes a Relational operator node in the expression tree. Relational
    // operators represent iterative processing of rows during query execution.
    // For example, a `TableScan` operation that reads rows from a table.
    RELATIONAL = 1;

    // Denotes a Scalar node in the expression tree. Scalar nodes represent
    // non-iterable entities in the query plan. For example, constants or
    // arithmetic operators appearing inside predicate expressions or references
    // to column names.
    SCALAR = 2;
  }

  // Metadata associated with a parent-child relationship appearing in a
  // [PlanNode][google.spanner.v1.PlanNode].
  message ChildLink {
    // The node to which the link points.
    int32 child_index = 1;

    // The type of the link. For example, in Hash Joins this could be used to
    // distinguish between the build child and the probe child, or in the case
    // of the child being an output variable, to represent the tag associated
    // with the output variable.
    string type = 2;

    // Only present if the child node is [SCALAR][google.spanner.v1.PlanNode.Kind.SCALAR] and corresponds
    // to an output variable of the parent node. The field carries the name of
    // the output variable.
    // For example, a `TableScan` operator that reads rows from a table will
    // have child links to the `SCALAR` nodes representing the output variables
    // created for each column that is read by the operator. The corresponding
    // `variable` fields will be set to the variable names assigned to the
    // columns.
    string variable = 3;
  }

  // Condensed representation of a node and its subtree. Only present for
  // `SCALAR` [PlanNode(s)][google.spanner.v1.PlanNode].
  message ShortRepresentation {
    // A string representation of the expression subtree rooted at this node.
    string description = 1;

    // A mapping of (subquery variable name) -> (subquery node id) for cases
    // where the `description` string of this node references a `SCALAR`
    // subquery contained in the expression subtree rooted at this node. The
    // referenced `SCALAR` subquery may not necessarily be a direct child of
    // this node.
    map<string, int32> subqueries = 2;
  }

  // The `PlanNode`'s index in [node list][google.spanner.v1.QueryPlan.plan_nodes].
  int32 index = 1;

  // Used to determine the type of node. May be needed for visualizing
  // different kinds of nodes differently. For example, If the node is a
  // [SCALAR][google.spanner.v1.PlanNode.Kind.SCALAR] node, it will have a condensed representation
  // which can be used to directly embed a description of the node in its
  // parent.
  Kind kind = 2;

  // The display name for the node.
  string display_name = 3;

  // List of child node `index`es and their relationship to this parent.
  repeated ChildLink child_links = 4;

  // Condensed representation for [SCALAR][google.spanner.v1.PlanNode.Kind.SCALAR] nodes.
  ShortRepresentation short_representation = 5;

  // Attributes relevant to the node contained in a group of key-value pairs.
  // For example, a Parameter Reference node could have the following
  // information in its metadata:
  //
  //     {
  //       "parameter_reference": "param1",
  //       "parameter_type": "array"
  //     }
  google.protobuf.Struct metadata = 6;

  // The execution statistics associated with the node, contained in a group of
  // key-value pairs. Only present if the plan was returned as a result of a
  // profile query. For example, number of executions, number of rows/time per
  // execution etc.
  google.protobuf.Struct execution_stats = 7;
}

// Contains an ordered list of nodes appearing in the query plan.
message QueryPlan {
  // The nodes in the query plan. Plan nodes are returned in pre-order starting
  // with the plan root. Each [PlanNode][google.spanner.v1.PlanNode]'s `id` corresponds to its index in
  // `plan_nodes`.
  repeated PlanNode plan_nodes = 1;
}
