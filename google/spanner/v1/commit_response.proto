// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.spanner.v1;

import "google/protobuf/timestamp.proto";
import "google/spanner/v1/transaction.proto";

option csharp_namespace = "Google.Cloud.Spanner.V1";
option go_package = "cloud.google.com/go/spanner/apiv1/spannerpb;spannerpb";
option java_multiple_files = true;
option java_outer_classname = "CommitResponseProto";
option java_package = "com.google.spanner.v1";
option php_namespace = "Google\\Cloud\\Spanner\\V1";
option ruby_package = "Google::Cloud::Spanner::V1";

// The response for [Commit][google.spanner.v1.Spanner.Commit].
message CommitResponse {
  // Additional statistics about a commit.
  message CommitStats {
    // The total number of mutations for the transaction. Knowing the
    // `mutation_count` value can help you maximize the number of mutations
    // in a transaction and minimize the number of API round trips. You can
    // also monitor this value to prevent transactions from exceeding the system
    // [limit](https://cloud.google.com/spanner/quotas#limits_for_creating_reading_updating_and_deleting_data).
    // If the number of mutations exceeds the limit, the server returns
    // [INVALID_ARGUMENT](https://cloud.google.com/spanner/docs/reference/rest/v1/Code#ENUM_VALUES.INVALID_ARGUMENT).
    int64 mutation_count = 1;
  }

  // The Cloud Spanner timestamp at which the transaction committed.
  google.protobuf.Timestamp commit_timestamp = 1;

  // The statistics about this Commit. Not returned by default.
  // For more information, see
  // [CommitRequest.return_commit_stats][google.spanner.v1.CommitRequest.return_commit_stats].
  CommitStats commit_stats = 2;

  // Clients should examine and retry the commit if any of the following
  // reasons are populated.
  oneof MultiplexedSessionRetry {
    // If specified, transaction has not committed yet.
    // Clients must retry the commit with the new precommit token.
    MultiplexedSessionPrecommitToken precommit_token = 4;
  }
}
