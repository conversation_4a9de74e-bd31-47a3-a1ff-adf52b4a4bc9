type: google.api.Service
config_version: 3
name: spanner.googleapis.com
title: Cloud Spanner API

apis:
- name: google.spanner.v1.Spanner

documentation:
  summary: |-
    Cloud Spanner is a managed, mission-critical, globally consistent and
    scalable relational database service.

backend:
  rules:
  - selector: 'google.longrunning.Operations.*'
    deadline: 3600.0
  - selector: 'google.spanner.v1.Spanner.*'
    deadline: 3600.0

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/instances/*/databases/*/operations/*}:cancel'
    additional_bindings:
    - post: '/v1/{name=projects/*/instances/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/instances/*/backups/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/instanceConfigs/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/instances/*/databases/*/operations/*}'
    additional_bindings:
    - delete: '/v1/{name=projects/*/instances/*/operations/*}'
    - delete: '/v1/{name=projects/*/instances/*/backups/*/operations/*}'
    - delete: '/v1/{name=projects/*/instanceConfigs/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/instances/*/databases/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=projects/*/instances/*/operations/*}'
    - get: '/v1/{name=projects/*/instances/*/backups/*/operations/*}'
    - get: '/v1/{name=projects/*/instanceConfigs/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/instances/*/databases/*/operations}'
    additional_bindings:
    - get: '/v1/{name=projects/*/instances/*/operations}'
    - get: '/v1/{name=projects/*/instances/*/backups/*/operations}'
    - get: '/v1/{name=projects/*/instanceConfigs/*/operations}'

authentication:
  rules:
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/spanner.admin
  - selector: 'google.spanner.v1.Spanner.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/spanner.data

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=190851&template=0
  documentation_uri: https://cloud.google.com/spanner/
