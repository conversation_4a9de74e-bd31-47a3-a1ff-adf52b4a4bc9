type: google.api.Service
config_version: 3
name: storagetransfer.googleapis.com
title: Storage Transfer API

apis:
- name: google.longrunning.Operations
- name: google.storagetransfer.v1.StorageTransferService

types:
- name: google.storagetransfer.v1.TransferOperation

documentation:
  summary: |-
    Transfers data from external data sources to a Google Cloud Storage bucket
    or between Google Cloud Storage buckets.
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    description: |-
      Cancels a transfer. Use
      the [transferOperations.get][google.longrunning.Operations.GetOperation]
      method to check if the cancellation succeeded or if the operation
      completed despite the `cancel` request.

      When you cancel an operation, the currently running transfer is
      interrupted.  For recurring transfer jobs, the next instance of the
      transfer job will still run.  For example, if your job is configured
      to run every day at 1pm and you cancel Monday's operation at 1:05pm,
      Monday's transfer
      will stop. However, a transfer job will still be attempted on Tuesday.

      This applies only to currently running operations. If an operation is
      not currently running, `cancel` does nothing.

      <aside class="caution">
      <b>Caution:</b> Canceling a transfer job can leave your data in an
      unknown state. We recommend that you restore the state at both the
      destination and the source after the `cancel` request completes so
      that your data is in a consistent state. </aside>

      When you cancel a job, the next job computes a delta of files and may
      repair any inconsistent state. For instance, if you run a job every
      day, and today's job found 10 new files and transferred five files
      before you canceled the job, tomorrow's transfer operation will
      compute a new delta with the five files that were not copied today
      plus any new files discovered tomorrow.

  - selector: google.longrunning.Operations.ListOperations
    description: |-
      Lists transfer operations. Operations are ordered by their creation
      time in reverse chronological order.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=transferOperations/**}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=transferOperations/**}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=transferOperations}'

authentication:
  rules:
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.storagetransfer.v1.StorageTransferService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
