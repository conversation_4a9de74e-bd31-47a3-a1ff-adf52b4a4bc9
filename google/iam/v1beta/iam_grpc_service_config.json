{"methodConfig": [{"name": [{"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "ListWorkloadIdentityPools"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "GetWorkloadIdentityPool"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "CreateWorkloadIdentityPool"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "UpdateWorkloadIdentityPool"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "DeleteWorkloadIdentityPool"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "UndeleteWorkloadIdentityPool"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "ListWorkloadIdentityPoolProviders"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "GetWorkloadIdentityPoolProvider"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "CreateWorkloadIdentityPoolProvider"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "UpdateWorkloadIdentityPoolProvider"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "DeleteWorkloadIdentityPoolProvider"}, {"service": "google.iam.v1beta.WorkloadIdentityPools", "method": "UndeleteWorkloadIdentityPoolProvider"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}