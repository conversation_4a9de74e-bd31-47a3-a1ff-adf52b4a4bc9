type: google.api.Service
config_version: 2
name: iam.googleapis.com
title: Identity and Access Management (IAM) API

apis:
- name: google.iam.v1beta.WorkloadIdentityPools

documentation:
  summary: |-
    <p>Manages identity and access control for Google Cloud Platform resources,
    including the creation of service accounts, which you can use to
    authenticate to Google and make API calls.</p> <aside
    class="note"><b>Note:</b> This API is tied to the <a
    href="/iam/docs/reference/credentials/rest">IAM service account
    credentials API</a> (<code>iamcredentials.googleapis.com</code>). Enabling
    or disabling this API will also enable or disable the IAM service account
    credentials API.</aside>

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/operations/*}:cancel'
    body: '*'
    additional_bindings:
    - post: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*/operations/*}:cancel'
      body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/operations/*}'
    additional_bindings:
    - delete: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/operations/*}'
    additional_bindings:
    - get: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*}/operations'
    additional_bindings:
    - get: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*}/operations'

authentication:
  rules:
  - selector: 'google.iam.v1beta.WorkloadIdentityPools.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
