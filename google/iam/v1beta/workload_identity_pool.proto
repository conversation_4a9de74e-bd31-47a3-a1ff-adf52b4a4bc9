// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/iam/apiv1beta/iampb;iampb";
option java_multiple_files = true;
option java_outer_classname = "WorkloadIdentityPoolProto";
option java_package = "com.google.iam.v1beta";

// Manages WorkloadIdentityPools.
service WorkloadIdentityPools {
  option (google.api.default_host) = "iam.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists all non-deleted
  // [WorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPool]s in a
  // project. If `show_deleted` is set to `true`, then deleted pools are also
  // listed.
  rpc ListWorkloadIdentityPools(ListWorkloadIdentityPoolsRequest) returns (ListWorkloadIdentityPoolsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*}/workloadIdentityPools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets an individual
  // [WorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPool].
  rpc GetWorkloadIdentityPool(GetWorkloadIdentityPoolRequest) returns (WorkloadIdentityPool) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new
  // [WorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPool].
  //
  // You cannot reuse the name of a deleted pool until 30 days after deletion.
  rpc CreateWorkloadIdentityPool(CreateWorkloadIdentityPoolRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*}/workloadIdentityPools"
      body: "workload_identity_pool"
    };
    option (google.api.method_signature) = "parent,workload_identity_pool,workload_identity_pool_id";
    option (google.longrunning.operation_info) = {
      response_type: "WorkloadIdentityPool"
      metadata_type: "WorkloadIdentityPoolOperationMetadata"
    };
  }

  // Updates an existing
  // [WorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPool].
  rpc UpdateWorkloadIdentityPool(UpdateWorkloadIdentityPoolRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{workload_identity_pool.name=projects/*/locations/*/workloadIdentityPools/*}"
      body: "workload_identity_pool"
    };
    option (google.api.method_signature) = "workload_identity_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "WorkloadIdentityPool"
      metadata_type: "WorkloadIdentityPoolOperationMetadata"
    };
  }

  // Deletes a
  // [WorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPool].
  //
  // You cannot use a deleted pool to exchange external
  // credentials for Google Cloud credentials. However, deletion does
  // not revoke credentials that have already been issued.
  // Credentials issued for a deleted pool do not grant access to resources.
  // If the pool is undeleted, and the credentials are not expired, they
  // grant access again.
  // You can undelete a pool for 30 days. After 30 days, deletion is
  // permanent.
  // You cannot update deleted pools. However, you can view and list them.
  rpc DeleteWorkloadIdentityPool(DeleteWorkloadIdentityPoolRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "WorkloadIdentityPool"
      metadata_type: "WorkloadIdentityPoolOperationMetadata"
    };
  }

  // Undeletes a [WorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPool],
  // as long as it was deleted fewer than 30 days ago.
  rpc UndeleteWorkloadIdentityPool(UndeleteWorkloadIdentityPoolRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*}:undelete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "WorkloadIdentityPool"
      metadata_type: "WorkloadIdentityPoolOperationMetadata"
    };
  }

  // Lists all non-deleted
  // [WorkloadIdentityPoolProvider][google.iam.v1beta.WorkloadIdentityPoolProvider]s
  // in a [WorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPool].
  // If `show_deleted` is set to `true`, then deleted providers are also listed.
  rpc ListWorkloadIdentityPoolProviders(ListWorkloadIdentityPoolProvidersRequest) returns (ListWorkloadIdentityPoolProvidersResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/workloadIdentityPools/*}/providers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets an individual
  // [WorkloadIdentityPoolProvider][google.iam.v1beta.WorkloadIdentityPoolProvider].
  rpc GetWorkloadIdentityPoolProvider(GetWorkloadIdentityPoolProviderRequest) returns (WorkloadIdentityPoolProvider) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new
  // [WorkloadIdentityPoolProvider][google.iam.v1beta.WorkloadIdentityProvider]
  // in a [WorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPool].
  //
  // You cannot reuse the name of a deleted provider until 30 days after
  // deletion.
  rpc CreateWorkloadIdentityPoolProvider(CreateWorkloadIdentityPoolProviderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/workloadIdentityPools/*}/providers"
      body: "workload_identity_pool_provider"
    };
    option (google.api.method_signature) = "parent,workload_identity_pool_provider,workload_identity_pool_provider_id";
    option (google.longrunning.operation_info) = {
      response_type: "WorkloadIdentityPoolProvider"
      metadata_type: "WorkloadIdentityPoolProviderOperationMetadata"
    };
  }

  // Updates an existing
  // [WorkloadIdentityPoolProvider][google.iam.v1beta.WorkloadIdentityProvider].
  rpc UpdateWorkloadIdentityPoolProvider(UpdateWorkloadIdentityPoolProviderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{workload_identity_pool_provider.name=projects/*/locations/*/workloadIdentityPools/*/providers/*}"
      body: "workload_identity_pool_provider"
    };
    option (google.api.method_signature) = "workload_identity_pool_provider,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "WorkloadIdentityPoolProvider"
      metadata_type: "WorkloadIdentityPoolProviderOperationMetadata"
    };
  }

  // Deletes a
  // [WorkloadIdentityPoolProvider][google.iam.v1beta.WorkloadIdentityProvider].
  // Deleting a provider does not revoke credentials that have already been
  // issued; they continue to grant access.
  // You can undelete a provider for 30 days. After 30 days, deletion is
  // permanent.
  // You cannot update deleted providers. However, you can view and list them.
  rpc DeleteWorkloadIdentityPoolProvider(DeleteWorkloadIdentityPoolProviderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "WorkloadIdentityPoolProvider"
      metadata_type: "WorkloadIdentityPoolProviderOperationMetadata"
    };
  }

  // Undeletes a
  // [WorkloadIdentityPoolProvider][google.iam.v1beta.WorkloadIdentityProvider],
  // as long as it was deleted fewer than 30 days ago.
  rpc UndeleteWorkloadIdentityPoolProvider(UndeleteWorkloadIdentityPoolProviderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*}:undelete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "WorkloadIdentityPoolProvider"
      metadata_type: "WorkloadIdentityPoolProviderOperationMetadata"
    };
  }
}

// Represents a collection of external workload identities. You can define IAM
// policies to grant these identities access to Google Cloud resources.
message WorkloadIdentityPool {
  option (google.api.resource) = {
    type: "iam.googleapis.com/WorkloadIdentityPool"
    pattern: "projects/{project}/locations/{location}/workloadIdentityPools/{workload_identity_pool}"
  };

  // The current state of the pool.
  enum State {
    // State unspecified.
    STATE_UNSPECIFIED = 0;

    // The pool is active, and may be used in Google Cloud policies.
    ACTIVE = 1;

    // The pool is soft-deleted. Soft-deleted pools are permanently deleted
    // after approximately 30 days. You can restore a soft-deleted pool using
    // [UndeleteWorkloadIdentityPool][google.iam.v1beta.WorkloadIdentityPools.UndeleteWorkloadIdentityPool].
    //
    // You cannot reuse the ID of a soft-deleted pool until it is permanently
    // deleted.
    //
    // While a pool is deleted, you cannot use it to exchange tokens, or use
    // existing tokens to access resources. If the pool is undeleted, existing
    // tokens grant access again.
    DELETED = 2;
  }

  // Output only. The resource name of the pool.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A display name for the pool. Cannot exceed 32 characters.
  string display_name = 2;

  // A description of the pool. Cannot exceed 256 characters.
  string description = 3;

  // Output only. The state of the pool.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether the pool is disabled. You cannot use a disabled pool to exchange
  // tokens, or use existing tokens to access resources. If
  // the pool is re-enabled, existing tokens grant access again.
  bool disabled = 5;
}

// A configuration for an external identity provider.
message WorkloadIdentityPoolProvider {
  option (google.api.resource) = {
    type: "iam.googleapis.com/WorkloadIdentityPoolProvider"
    pattern: "projects/{project}/locations/{location}/workloadIdentityPools/{workload_identity_pool}/providers/{workload_identity_pool_provider}"
  };

  // Represents an Amazon Web Services identity provider.
  message Aws {
    // Required. The AWS account ID.
    string account_id = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Represents an OpenId Connect 1.0 identity provider.
  message Oidc {
    // Required. The OIDC issuer URL.
    string issuer_uri = 1 [(google.api.field_behavior) = REQUIRED];

    // Acceptable values for the `aud` field (audience) in the OIDC token. Token
    // exchange requests are rejected if the token audience does not match one
    // of the configured values. Each audience may be at most 256 characters. A
    // maximum of 10 audiences may be configured.
    //
    // If this list is empty, the OIDC token audience must be equal to
    // the full canonical resource name of the WorkloadIdentityPoolProvider,
    // with or without the HTTPS prefix. For example:
    //
    // ```
    // //iam.googleapis.com/projects/<project-number>/locations/<location>/workloadIdentityPools/<pool-id>/providers/<provider-id>
    // https://iam.googleapis.com/projects/<project-number>/locations/<location>/workloadIdentityPools/<pool-id>/providers/<provider-id>
    // ```
    repeated string allowed_audiences = 2;
  }

  // The current state of the provider.
  enum State {
    // State unspecified.
    STATE_UNSPECIFIED = 0;

    // The provider is active, and may be used to validate authentication
    // credentials.
    ACTIVE = 1;

    // The provider is soft-deleted. Soft-deleted providers are permanently
    // deleted after approximately 30 days. You can restore a soft-deleted
    // provider using
    // [UndeleteWorkloadIdentityPoolProvider][google.iam.v1beta.WorkloadIdentityPools.UndeleteWorkloadIdentityPoolProvider].
    //
    // You cannot reuse the ID of a soft-deleted provider until it is
    // permanently deleted.
    DELETED = 2;
  }

  // Output only. The resource name of the provider.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A display name for the provider. Cannot exceed 32 characters.
  string display_name = 2;

  // A description for the provider. Cannot exceed 256 characters.
  string description = 3;

  // Output only. The state of the provider.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether the provider is disabled. You cannot use a disabled provider to
  // exchange tokens. However, existing tokens still grant access.
  bool disabled = 5;

  // Maps attributes from authentication credentials issued by an external
  // identity provider to Google Cloud attributes, such as `subject` and
  // `segment`.
  //
  // Each key must be a string specifying the Google Cloud IAM attribute to
  // map to.
  //
  // The following keys are supported:
  //
  // * `google.subject`: The principal IAM is authenticating. You can reference
  //                     this value in IAM bindings. This is also the
  //                     subject that appears in Cloud Logging logs.
  //                     Cannot exceed 127 characters.
  //
  // * `google.groups`: Groups the external identity belongs to. You can grant
  //                    groups access to resources using an IAM `principalSet`
  //                    binding; access applies to all members of the group.
  //
  // You can also provide custom attributes by specifying
  // `attribute.{custom_attribute}`, where `{custom_attribute}` is the name of
  // the custom attribute to be mapped. You can define a maximum of 50 custom
  // attributes. The maximum length of a mapped attribute key is
  // 100 characters, and the key may only contain the characters [a-z0-9_].
  //
  // You can reference these attributes in IAM policies to define fine-grained
  // access for a workload to Google Cloud resources. For example:
  //
  // * `google.subject`:
  // `principal://iam.googleapis.com/projects/{project}/locations/{location}/workloadIdentityPools/{pool}/subject/{value}`
  //
  // * `google.groups`:
  // `principalSet://iam.googleapis.com/projects/{project}/locations/{location}/workloadIdentityPools/{pool}/group/{value}`
  //
  // * `attribute.{custom_attribute}`:
  // `principalSet://iam.googleapis.com/projects/{project}/locations/{location}/workloadIdentityPools/{pool}/attribute.{custom_attribute}/{value}`
  //
  // Each value must be a [Common Expression Language]
  // (https://opensource.google/projects/cel) function that maps an
  // identity provider credential to the normalized attribute specified by the
  // corresponding map key.
  //
  // You can use the `assertion` keyword in the expression to access a JSON
  // representation of the authentication credential issued by the provider.
  //
  // The maximum length of an attribute mapping expression is 2048 characters.
  // When evaluated, the total size of all mapped attributes must not exceed
  // 8KB.
  //
  // For AWS providers, the following rules apply:
  //
  // - If no attribute mapping is defined, the following default mapping
  //   applies:
  //
  //   ```
  //   {
  //     "google.subject":"assertion.arn",
  //     "attribute.aws_role":
  //         "assertion.arn.contains('assumed-role')"
  //         " ? assertion.arn.extract('{account_arn}assumed-role/')"
  //         "   + 'assumed-role/'"
  //         "   + assertion.arn.extract('assumed-role/{role_name}/')"
  //         " : assertion.arn",
  //   }
  //   ```
  //
  // - If any custom attribute mappings are defined, they must include a mapping
  //   to the `google.subject` attribute.
  //
  //
  // For OIDC providers, the following rules apply:
  //
  // - Custom attribute mappings must be defined, and must include a mapping to
  //   the `google.subject` attribute. For example, the following maps the
  //   `sub` claim of the incoming credential to the `subject` attribute on
  //   a Google token.
  //
  //   ```
  //   {"google.subject": "assertion.sub"}
  //   ```
  map<string, string> attribute_mapping = 6;

  // [A Common Expression Language](https://opensource.google/projects/cel)
  // expression, in plain text, to restrict what otherwise valid authentication
  // credentials issued by the provider should not be accepted.
  //
  // The expression must output a boolean representing whether to allow the
  // federation.
  //
  // The following keywords may be referenced in the expressions:
  //
  // * `assertion`: JSON representing the authentication credential issued by
  //                the provider.
  // * `google`: The Google attributes mapped from the assertion in the
  //             `attribute_mappings`.
  // * `attribute`: The custom attributes mapped from the assertion in the
  //                `attribute_mappings`.
  //
  // The maximum length of the attribute condition expression is 4096
  // characters. If unspecified, all valid authentication credential are
  // accepted.
  //
  // The following example shows how to only allow credentials with a mapped
  // `google.groups` value of `admins`:
  //
  // ```
  // "'admins' in google.groups"
  // ```
  string attribute_condition = 7;

  // Identity provider configuration types.
  oneof provider_config {
    // An Amazon Web Services identity provider.
    Aws aws = 8;

    // An OpenId Connect 1.0 identity provider.
    Oidc oidc = 9;
  }
}

// Request message for ListWorkloadIdentityPools.
message ListWorkloadIdentityPoolsRequest {
  // Required. The parent resource to list pools for.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // The maximum number of pools to return.
  // If unspecified, at most 50 pools are returned.
  // The maximum value is 1000; values above are 1000 truncated to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListWorkloadIdentityPools`
  // call. Provide this to retrieve the subsequent page.
  string page_token = 3;

  // Whether to return soft-deleted pools.
  bool show_deleted = 4;
}

// Response message for ListWorkloadIdentityPools.
message ListWorkloadIdentityPoolsResponse {
  // A list of pools.
  repeated WorkloadIdentityPool workload_identity_pools = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetWorkloadIdentityPool.
message GetWorkloadIdentityPoolRequest {
  // Required. The name of the pool to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/WorkloadIdentityPool"
    }
  ];
}

// Request message for CreateWorkloadIdentityPool.
message CreateWorkloadIdentityPoolRequest {
  // Required. The parent resource to create the pool in. The only supported
  // location is `global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The pool to create.
  WorkloadIdentityPool workload_identity_pool = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the pool, which becomes the
  // final component of the resource name. This value should be 4-32 characters,
  // and may contain the characters [a-z0-9-]. The prefix `gcp-` is
  // reserved for use by Google, and may not be specified.
  string workload_identity_pool_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateWorkloadIdentityPool.
message UpdateWorkloadIdentityPoolRequest {
  // Required. The pool to update. The `name` field is used to identify the pool.
  WorkloadIdentityPool workload_identity_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteWorkloadIdentityPool.
message DeleteWorkloadIdentityPoolRequest {
  // Required. The name of the pool to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/WorkloadIdentityPool"
    }
  ];
}

// Request message for UndeleteWorkloadIdentityPool.
message UndeleteWorkloadIdentityPoolRequest {
  // Required. The name of the pool to undelete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/WorkloadIdentityPool"
    }
  ];
}

// Request message for ListWorkloadIdentityPoolProviders.
message ListWorkloadIdentityPoolProvidersRequest {
  // Required. The pool to list providers for.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/WorkloadIdentityPool"
    }
  ];

  // The maximum number of providers to return.
  // If unspecified, at most 50 providers are returned.
  // The maximum value is 100; values above 100 are truncated to 100.
  int32 page_size = 2;

  // A page token, received from a previous
  // `ListWorkloadIdentityPoolProviders` call. Provide this to retrieve the
  // subsequent page.
  string page_token = 3;

  // Whether to return soft-deleted providers.
  bool show_deleted = 4;
}

// Response message for ListWorkloadIdentityPoolProviders.
message ListWorkloadIdentityPoolProvidersResponse {
  // A list of providers.
  repeated WorkloadIdentityPoolProvider workload_identity_pool_providers = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetWorkloadIdentityPoolProvider.
message GetWorkloadIdentityPoolProviderRequest {
  // Required. The name of the provider to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/WorkloadIdentityPoolProvider"
    }
  ];
}

// Request message for CreateWorkloadIdentityPoolProvider.
message CreateWorkloadIdentityPoolProviderRequest {
  // Required. The pool to create this provider in.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/WorkloadIdentityPool"
    }
  ];

  // Required. The provider to create.
  WorkloadIdentityPoolProvider workload_identity_pool_provider = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID for the provider, which becomes the
  // final component of the resource name. This value must be 4-32 characters,
  // and may contain the characters [a-z0-9-]. The prefix `gcp-` is
  // reserved for use by Google, and may not be specified.
  string workload_identity_pool_provider_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateWorkloadIdentityPoolProvider.
message UpdateWorkloadIdentityPoolProviderRequest {
  // Required. The provider to update.
  WorkloadIdentityPoolProvider workload_identity_pool_provider = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteWorkloadIdentityPoolProvider.
message DeleteWorkloadIdentityPoolProviderRequest {
  // Required. The name of the provider to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/WorkloadIdentityPoolProvider"
    }
  ];
}

// Request message for UndeleteWorkloadIdentityPoolProvider.
message UndeleteWorkloadIdentityPoolProviderRequest {
  // Required. The name of the provider to undelete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/WorkloadIdentityPoolProvider"
    }
  ];
}

// Metadata for long-running WorkloadIdentityPool operations.
message WorkloadIdentityPoolOperationMetadata {}

// Metadata for long-running WorkloadIdentityPoolProvider operations.
message WorkloadIdentityPoolProviderOperationMetadata {}
