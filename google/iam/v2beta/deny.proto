// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.v2beta;

import "google/type/expr.proto";

option csharp_namespace = "Google.Cloud.Iam.V2Beta";
option go_package = "cloud.google.com/go/iam/apiv2beta/iampb;iampb";
option java_multiple_files = true;
option java_outer_classname = "DenyRuleProto";
option java_package = "com.google.iam.v2beta";
option php_namespace = "Google\\Cloud\\Iam\\V2beta";

// A deny rule in an IAM deny policy.
message DenyRule {
  // The identities that are prevented from using one or more permissions on
  // Google Cloud resources. This field can contain the following values:
  //
  // * `principalSet://goog/public:all`: A special identifier that represents
  //   any principal that is on the internet, even if they do not have a Google
  //   Account or are not logged in.
  //
  // * `principal://goog/subject/{email_id}`: A specific Google Account.
  //   Includes Gmail, Cloud Identity, and Google Workspace user accounts. For
  //   example, `principal://goog/subject/<EMAIL>`.
  //
  // * `deleted:principal://goog/subject/{email_id}?uid={uid}`: A specific
  //   Google Account that was deleted recently. For example,
  //   `deleted:principal://goog/subject/<EMAIL>?uid=**********`. If
  //   the Google Account is recovered, this identifier reverts to the standard
  //   identifier for a Google Account.
  //
  // * `principalSet://goog/group/{group_id}`: A Google group. For example,
  //   `principalSet://goog/group/<EMAIL>`.
  //
  // * `deleted:principalSet://goog/group/{group_id}?uid={uid}`: A Google group
  //   that was deleted recently. For example,
  //   `deleted:principalSet://goog/group/<EMAIL>?uid=**********`. If
  //   the Google group is restored, this identifier reverts to the standard
  //   identifier for a Google group.
  //
  // * `principal://iam.googleapis.com/projects/-/serviceAccounts/{service_account_id}`:
  //   A Google Cloud service account. For example,
  //   `principal://iam.googleapis.com/projects/-/serviceAccounts/<EMAIL>`.
  //
  // * `deleted:principal://iam.googleapis.com/projects/-/serviceAccounts/{service_account_id}?uid={uid}`:
  //   A Google Cloud service account that was deleted recently. For example,
  //   `deleted:principal://iam.googleapis.com/projects/-/serviceAccounts/<EMAIL>?uid=**********`.
  //   If the service account is undeleted, this identifier reverts to the
  //   standard identifier for a service account.
  //
  // * `principalSet://goog/cloudIdentityCustomerId/{customer_id}`: All of the
  //   principals associated with the specified Google Workspace or Cloud
  //   Identity customer ID. For example,
  //   `principalSet://goog/cloudIdentityCustomerId/C01Abc35`.
  repeated string denied_principals = 1;

  // The identities that are excluded from the deny rule, even if they are
  // listed in the `denied_principals`. For example, you could add a Google
  // group to the `denied_principals`, then exclude specific users who belong to
  // that group.
  //
  // This field can contain the same values as the `denied_principals` field,
  // excluding `principalSet://goog/public:all`, which represents all users on
  // the internet.
  repeated string exception_principals = 2;

  // The permissions that are explicitly denied by this rule. Each permission
  // uses the format `{service_fqdn}/{resource}.{verb}`, where `{service_fqdn}`
  // is the fully qualified domain name for the service. For example,
  // `iam.googleapis.com/roles.list`.
  repeated string denied_permissions = 3;

  // Specifies the permissions that this rule excludes from the set of denied
  // permissions given by `denied_permissions`. If a permission appears in
  // `denied_permissions` _and_ in `exception_permissions` then it will _not_ be
  // denied.
  //
  // The excluded permissions can be specified using the same syntax as
  // `denied_permissions`.
  repeated string exception_permissions = 4;

  // The condition that determines whether this deny rule applies to a request.
  // If the condition expression evaluates to `true`, then the deny rule is
  // applied; otherwise, the deny rule is not applied.
  //
  // Each deny rule is evaluated independently. If this deny rule does not apply
  // to a request, other deny rules might still apply.
  //
  // The condition can use CEL functions that evaluate
  // [resource
  // tags](https://cloud.google.com/iam/help/conditions/resource-tags). Other
  // functions and operators are not supported.
  google.type.Expr denial_condition = 5;
}
