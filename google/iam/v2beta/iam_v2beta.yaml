type: google.api.Service
config_version: 3
name: iam.googleapis.com
title: Identity and Access Management (IAM) API

apis:
- name: google.iam.v2beta.Policies
- name: google.longrunning.Operations

types:
- name: google.iam.v2beta.PolicyOperationMetadata

documentation:
  summary: |-
    Manages identity and access control for Google Cloud Platform resources,
    including the creation of service accounts, which you can use to
    authenticate to Google and make API calls.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2beta/{name=policies/*/*/*/operations/*}'

authentication:
  rules:
  - selector: 'google.iam.v2beta.Policies.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
