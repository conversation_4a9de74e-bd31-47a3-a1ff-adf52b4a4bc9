{"methodConfig": [{"name": [{"service": "google.iam.v2beta.Policies", "method": "ListPolicies"}, {"service": "google.iam.v2beta.Policies", "method": "GetPolicy"}, {"service": "google.iam.v2beta.Policies", "method": "CreatePolicy"}, {"service": "google.iam.v2beta.Policies", "method": "UpdatePolicy"}, {"service": "google.iam.v2beta.Policies", "method": "DeletePolicy"}, {"service": "google.iam.v2beta.Policies", "method": "GetEffectivePolicies"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}