// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.v3beta;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Iam.V3Beta";
option go_package = "cloud.google.com/go/iam/apiv3beta/iampb;iampb";
option java_multiple_files = true;
option java_outer_classname = "PrincipalAccessBoundaryPolicyResourcesProto";
option java_package = "com.google.iam.v3beta";

// An IAM principal access boundary policy resource.
message PrincipalAccessBoundaryPolicy {
  option (google.api.resource) = {
    type: "iam.googleapis.com/PrincipalAccessBoundaryPolicy"
    pattern: "organizations/{organization}/locations/{location}/principalAccessBoundaryPolicies/{principal_access_boundary_policy}"
    plural: "principalAccessBoundaryPolicies"
    singular: "principalAccessBoundaryPolicy"
  };

  // Identifier. The resource name of the principal access boundary policy.
  //
  // The following format is supported:
  // `organizations/{organization_id}/locations/{location}/principalAccessBoundaryPolicies/{policy_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The globally unique ID of the principal access boundary
  // policy.
  string uid = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. The etag for the principal access boundary.
  // If this is provided on update, it must match the server's etag.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The description of the principal access boundary policy. Must be
  // less than or equal to 63 characters.
  string display_name = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User defined annotations. See
  // https://google.aip.dev/148#annotations for more details such as format and
  // size limitations
  map<string, string> annotations = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time when the principal access boundary policy was
  // created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the principal access boundary policy was most
  // recently updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The details for the principal access boundary policy.
  PrincipalAccessBoundaryPolicyDetails details = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Principal access boundary policy details
message PrincipalAccessBoundaryPolicyDetails {
  // Required. A list of principal access boundary policy rules. The number of
  // rules in a policy is limited to 500.
  repeated PrincipalAccessBoundaryPolicyRule rules = 1
      [(google.api.field_behavior) = REQUIRED];

  // Optional.
  // The version number that indicates which Google Cloud
  // services are included in the enforcement (e.g. "latest", "1", ...). If
  // empty, the PAB policy version will be set to the current latest version,
  // and this version won't get updated when new versions are released.
  string enforcement_version = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Principal access boundary policy rule that defines the resource boundary.
message PrincipalAccessBoundaryPolicyRule {
  // An effect to describe the access relationship.
  enum Effect {
    // Effect unspecified.
    EFFECT_UNSPECIFIED = 0;

    // Allows access to the resources in this rule.
    ALLOW = 1;
  }

  // Optional. The description of the principal access boundary policy rule.
  // Must be less than or equal to 256 characters.
  string description = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. A list of Cloud Resource Manager resources. The resource and all
  // the descendants are included. The number of resources in a policy is
  // limited to 500 across all rules.
  //
  // The following resource types are supported:
  //
  // * Organizations, such as
  // `//cloudresourcemanager.googleapis.com/organizations/123`.
  // * Folders, such as `//cloudresourcemanager.googleapis.com/folders/123`.
  // * Projects, such as `//cloudresourcemanager.googleapis.com/projects/123`
  //   or `//cloudresourcemanager.googleapis.com/projects/my-project-id`.
  repeated string resources = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The access relationship of principals to the resources in this
  // rule.
  Effect effect = 3 [(google.api.field_behavior) = REQUIRED];
}
