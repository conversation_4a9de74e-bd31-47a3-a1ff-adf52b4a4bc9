{"methodConfig": [{"name": [{"service": "google.iam.v2.Policies", "method": "ListPolicies"}, {"service": "google.iam.v2.Policies", "method": "GetPolicy"}, {"service": "google.iam.v2.Policies", "method": "CreatePolicy"}, {"service": "google.iam.v2.Policies", "method": "UpdatePolicy"}, {"service": "google.iam.v2.Policies", "method": "DeletePolicy"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}