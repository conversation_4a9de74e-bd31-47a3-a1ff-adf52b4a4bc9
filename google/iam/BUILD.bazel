# This build file includes a target for the Ruby wrapper library for
# google-iam.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for iam.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "iam_ruby_wrapper",
    srcs = ["//google/iam/v2:iam_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-iam",
        "ruby-cloud-wrapper-of=v2:0.5",
        "ruby-cloud-product-url=https://cloud.google.com/iam",
        "ruby-cloud-api-id=iam.googleapis.com",
        "ruby-cloud-api-shortname=iam",
    ],
    ruby_cloud_description = "Manages identity and access control policies for Google Cloud Platform resources.",
    ruby_cloud_title = "IAM",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-iam-ruby",
    deps = [
        ":iam_ruby_wrapper",
    ],
)
