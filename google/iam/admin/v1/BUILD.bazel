# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "admin_proto",
    srcs = [
        "audit_data.proto",
        "iam.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "admin_proto_with_info",
    deps = [
        ":admin_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "admin_java_proto",
    deps = [":admin_proto"],
)

java_grpc_library(
    name = "admin_java_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_java_proto"],
)

java_gapic_library(
    name = "admin_java_gapic",
    srcs = [":admin_proto_with_info"],
    # This was manually replaced - build_gen shouldn't have removed it.
    gapic_yaml = "iam_gapic.yaml",
    grpc_service_config = "iam_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iam.yaml",
    test_deps = [
        ":admin_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":admin_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "admin_java_gapic_test_suite",
    # This was manually named IAMClientTest - build_gen attempts to rename it to
    # IamClientTest.
    test_classes = [
        # Java package override from gapic.yaml.
        "com.google.cloud.iam.admin.v1.IAMClientTest",
    ],
    runtime_deps = [":admin_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-iam-admin-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":admin_java_gapic",
        ":admin_java_grpc",
        ":admin_java_proto",
        ":admin_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "admin_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/iam/admin/apiv1/adminpb",
    protos = [":admin_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "admin_go_gapic",
    srcs = [":admin_proto_with_info"],
    grpc_service_config = "iam_grpc_service_config.json",
    importpath = "cloud.google.com/go/iam/admin/apiv1;admin",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "iam.yaml",
    transport = "grpc",
    deps = [
        ":admin_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-iam-admin-v1-go",
    deps = [
        ":admin_go_gapic",
        ":admin_go_gapic_srcjar-metadata.srcjar",
        ":admin_go_gapic_srcjar-snippets.srcjar",
        ":admin_go_gapic_srcjar-test.srcjar",
        ":admin_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "admin_py_gapic",
    srcs = [":admin_proto"],
    grpc_service_config = "iam_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iam.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
    opt_args = [
        "python-gapic-name=iam_admin",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-iam",
    ],
)

py_test(
    name = "admin_py_gapic_test",
    srcs = [
        "admin_py_gapic_pytest.py",
        "admin_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":admin_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "iam-admin-v1-py",
    deps = [
        ":admin_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "admin_php_proto",
    deps = [":admin_proto"],
)

php_gapic_library(
    name = "admin_php_gapic",
    srcs = [":admin_proto_with_info"],
    grpc_service_config = "iam_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iam.yaml",
    transport = "grpc+rest",
    deps = [":admin_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-iam-admin-v1-php",
    deps = [
        ":admin_php_gapic",
        ":admin_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "admin_nodejs_gapic",
    package_name = "@google-cloud/admin",
    src = ":admin_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "iam_grpc_service_config.json",
    package = "google.iam.admin.v1",
    rest_numeric_enums = True,
    service_yaml = "iam.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "iam-admin-v1-nodejs",
    deps = [
        ":admin_nodejs_gapic",
        ":admin_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "admin_ruby_proto",
    deps = [":admin_proto"],
)

ruby_grpc_library(
    name = "admin_ruby_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "admin_ruby_gapic",
    srcs = [":admin_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-iam-admin-v1"],
    grpc_service_config = "iam_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iam.yaml",
    transport = "grpc",
    deps = [
        ":admin_ruby_grpc",
        ":admin_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-iam-admin-v1-ruby",
    deps = [
        ":admin_ruby_gapic",
        ":admin_ruby_grpc",
        ":admin_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "admin_csharp_proto",
    deps = [":admin_proto"],
)

csharp_grpc_library(
    name = "admin_csharp_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_csharp_proto"],
)

csharp_gapic_library(
    name = "admin_csharp_gapic",
    srcs = [":admin_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "iam_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iam.yaml",
    transport = "grpc",
    deps = [
        ":admin_csharp_grpc",
        ":admin_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-iam-admin-v1-csharp",
    deps = [
        ":admin_csharp_gapic",
        ":admin_csharp_grpc",
        ":admin_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "admin_cc_proto",
    deps = [":admin_proto"],
)

cc_grpc_library(
    name = "admin_cc_grpc",
    srcs = [":admin_proto"],
    grpc_only = True,
    deps = [":admin_cc_proto"],
)
