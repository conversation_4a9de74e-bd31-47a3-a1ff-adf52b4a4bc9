// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.admin.v1;

option csharp_namespace = "Google.Cloud.Iam.Admin.V1";
option go_package = "cloud.google.com/go/iam/admin/apiv1/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "AuditDataProto";
option java_package = "com.google.iam.admin.v1";
option php_namespace = "Google\\Cloud\\Iam\\Admin\\V1";

// Audit log information specific to Cloud IAM admin APIs. This message is
// serialized as an `Any` type in the `ServiceData` message of an
// `AuditLog` message.
message AuditData {
  // A PermissionDelta message to record the added_permissions and
  // removed_permissions inside a role.
  message PermissionDelta {
    // Added permissions.
    repeated string added_permissions = 1;

    // Removed permissions.
    repeated string removed_permissions = 2;
  }

  // The permission_delta when when creating or updating a Role.
  PermissionDelta permission_delta = 1;
}
