{"methodConfig": [{"name": [{"service": "google.iam.admin.v1.IAM", "method": "ListServiceAccounts"}, {"service": "google.iam.admin.v1.IAM", "method": "GetServiceAccount"}, {"service": "google.iam.admin.v1.IAM", "method": "UpdateServiceAccount"}, {"service": "google.iam.admin.v1.IAM", "method": "DeleteServiceAccount"}, {"service": "google.iam.admin.v1.IAM", "method": "ListServiceAccountKeys"}, {"service": "google.iam.admin.v1.IAM", "method": "GetServiceAccountKey"}, {"service": "google.iam.admin.v1.IAM", "method": "DeleteServiceAccountKey"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.iam.admin.v1.IAM", "method": "CreateServiceAccount"}, {"service": "google.iam.admin.v1.IAM", "method": "CreateServiceAccountKey"}, {"service": "google.iam.admin.v1.IAM", "method": "SignBlob"}, {"service": "google.iam.admin.v1.IAM", "method": "GetIamPolicy"}, {"service": "google.iam.admin.v1.IAM", "method": "SetIamPolicy"}, {"service": "google.iam.admin.v1.IAM", "method": "TestIamPermissions"}, {"service": "google.iam.admin.v1.IAM", "method": "QueryGrantableRoles"}, {"service": "google.iam.admin.v1.IAM", "method": "SignJwt"}], "timeout": "60s"}]}