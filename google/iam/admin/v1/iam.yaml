type: google.api.Service
config_version: 3
name: iam.googleapis.com
title: Identity and Access Management (IAM) API

apis:
- name: google.iam.admin.v1.IAM

types:
- name: google.iam.admin.v1.AuditData

documentation:
  summary: |-
    Manages identity and access control for Google Cloud Platform resources,
    including the creation of service accounts, which you can use to
    authenticate to Google and make API calls.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/operations/*}'
    additional_bindings:
    - get: '/v1beta/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/workloadIdentityPools/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/workloadIdentityPools/*/providers/*/operations/*}'

authentication:
  rules:
  - selector: 'google.iam.admin.v1.IAM.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
