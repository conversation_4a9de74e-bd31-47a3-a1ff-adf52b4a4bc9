type: google.api.Service
config_version: 3
name: iam.googleapis.com
title: Identity and Access Management (IAM) API

apis:
- name: google.cloud.location.Locations
- name: google.iam.v3.PolicyBindings
- name: google.iam.v3.PrincipalAccessBoundaryPolicies
- name: google.longrunning.Operations

types:
- name: google.iam.v3.OperationMetadata

documentation:
  summary: |-
    Manages identity and access control for Google Cloud resources, including
    the creation of service accounts, which you can use to authenticate to
    Google and make API calls. Enabling this API also enables the IAM Service
    Account Credentials API (iamcredentials.googleapis.com). However,
    disabling this API doesn't disable the IAM Service Account Credentials
    API.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v3/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v3/{name=folders/*/locations/*/operations/*}'
    - get: '/v3/{name=organizations/*/locations/*/operations/*}'

authentication:
  rules:
  - selector: 'google.iam.v3.PolicyBindings.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v3.PrincipalAccessBoundaryPolicies.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1077618
  documentation_uri: https://cloud.google.com/iam/docs/overview
  api_short_name: iam
  github_label: 'api: iam'
  doc_tag_prefix: iam
  organization: CLOUD
  proto_reference_documentation_uri: https://cloud.google.com/iam/docs/reference/rpc
