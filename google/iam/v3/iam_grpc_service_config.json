{"methodConfig": [{"name": [{"service": "google.iam.v3.PrincipalAccessBoundaryPolicies", "method": "GetPrincipalAccessBoundaryPolicy"}, {"service": "google.iam.v3.PrincipalAccessBoundaryPolicies", "method": "ListPrincipalAccessBoundaryPolicies"}, {"service": "google.iam.v3.PrincipalAccessBoundaryPolicies", "method": "SearchPrincipalAccessBoundaryPolicyBindings"}, {"service": "google.iam.v3.PolicyBindings", "method": "GetPolicyBinding"}, {"service": "google.iam.v3.PolicyBindings", "method": "ListPolicyBindings"}, {"service": "google.iam.v3.PolicyBindings", "method": "SearchTargetPolicyBindings"}, {"service": "google.longrunning.Operations", "method": "GetOperation"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.iam.v3.PrincipalAccessBoundaryPolicies", "method": "CreatePrincipalAccessBoundaryPolicy"}, {"service": "google.iam.v3.PrincipalAccessBoundaryPolicies", "method": "UpdatePrincipalAccessBoundaryPolicy"}, {"service": "google.iam.v3.PrincipalAccessBoundaryPolicies", "method": "DeletePrincipalAccessBoundaryPolicy"}, {"service": "google.iam.v3.PolicyBindings", "method": "CreatePolicyBinding"}, {"service": "google.iam.v3.PolicyBindings", "method": "UpdatePolicyBinding"}, {"service": "google.iam.v3.PolicyBindings", "method": "DeletePolicyBinding"}], "timeout": "30s"}]}