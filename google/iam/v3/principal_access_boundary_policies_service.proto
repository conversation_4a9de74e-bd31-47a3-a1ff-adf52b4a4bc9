// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v3/operation_metadata.proto";
import "google/iam/v3/policy_binding_resources.proto";
import "google/iam/v3/principal_access_boundary_policy_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Iam.V3";
option go_package = "cloud.google.com/go/iam/apiv3/iampb;iampb";
option java_multiple_files = true;
option java_outer_classname = "PrincipalAccessBoundaryPoliciesServiceProto";
option java_package = "com.google.iam.v3";

// Manages Identity and Access Management (IAM) principal access boundary
// policies.
service PrincipalAccessBoundaryPolicies {
  option (google.api.default_host) = "iam.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a principal access boundary policy, and returns a long running
  // operation.
  rpc CreatePrincipalAccessBoundaryPolicy(
      CreatePrincipalAccessBoundaryPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{parent=organizations/*/locations/*}/principalAccessBoundaryPolicies"
      body: "principal_access_boundary_policy"
    };
    option (google.api.method_signature) =
        "parent,principal_access_boundary_policy,principal_access_boundary_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "PrincipalAccessBoundaryPolicy"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets a principal access boundary policy.
  rpc GetPrincipalAccessBoundaryPolicy(GetPrincipalAccessBoundaryPolicyRequest)
      returns (PrincipalAccessBoundaryPolicy) {
    option (google.api.http) = {
      get: "/v3/{name=organizations/*/locations/*/principalAccessBoundaryPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a principal access boundary policy.
  rpc UpdatePrincipalAccessBoundaryPolicy(
      UpdatePrincipalAccessBoundaryPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v3/{principal_access_boundary_policy.name=organizations/*/locations/*/principalAccessBoundaryPolicies/*}"
      body: "principal_access_boundary_policy"
    };
    option (google.api.method_signature) =
        "principal_access_boundary_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "PrincipalAccessBoundaryPolicy"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a principal access boundary policy.
  rpc DeletePrincipalAccessBoundaryPolicy(
      DeletePrincipalAccessBoundaryPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v3/{name=organizations/*/locations/*/principalAccessBoundaryPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists principal access boundary policies.
  rpc ListPrincipalAccessBoundaryPolicies(
      ListPrincipalAccessBoundaryPoliciesRequest)
      returns (ListPrincipalAccessBoundaryPoliciesResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=organizations/*/locations/*}/principalAccessBoundaryPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns all policy bindings that bind a specific policy if a user has
  // searchPolicyBindings permission on that policy.
  rpc SearchPrincipalAccessBoundaryPolicyBindings(
      SearchPrincipalAccessBoundaryPolicyBindingsRequest)
      returns (SearchPrincipalAccessBoundaryPolicyBindingsResponse) {
    option (google.api.http) = {
      get: "/v3/{name=organizations/*/locations/*/principalAccessBoundaryPolicies/*}:searchPolicyBindings"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for CreatePrincipalAccessBoundaryPolicyRequest method.
message CreatePrincipalAccessBoundaryPolicyRequest {
  // Required. The parent resource where this principal access boundary policy
  // will be created. Only organization is supported now.
  //
  // Format:
  //   `organizations/{organization_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "iam.googleapis.com/PrincipalAccessBoundaryPolicy"
    }
  ];

  // Required. The ID to use for the principal access boundary policy, which
  // will become the final component of the principal access boundary policy's
  // resource name.
  //
  // This value must start with a lowercase letter followed by up to 62
  // lowercase letters, numbers, hyphens, or dots. Pattern,
  // /[a-z][a-z0-9-\.]{2,62}/.
  string principal_access_boundary_policy_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The principal access boundary policy to create.
  PrincipalAccessBoundaryPolicy principal_access_boundary_policy = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the creation, but do not
  // actually post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetPrincipalAccessBoundaryPolicy method.
message GetPrincipalAccessBoundaryPolicyRequest {
  // Required. The name of the principal access boundary policy to retrieve.
  //
  // Format:
  //   `organizations/{organization_id}/locations/{location}/principalAccessBoundaryPolicies/{principal_access_boundary_policy_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/PrincipalAccessBoundaryPolicy"
    }
  ];
}

// Request message for UpdatePrincipalAccessBoundaryPolicy method.
message UpdatePrincipalAccessBoundaryPolicyRequest {
  // Required. The principal access boundary policy to update.
  //
  // The principal access boundary policy's `name` field is used to identify the
  // policy to update.
  PrincipalAccessBoundaryPolicy principal_access_boundary_policy = 1
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the update, but do not
  // actually post it.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of fields to update
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for DeletePrincipalAccessBoundaryPolicy method.
message DeletePrincipalAccessBoundaryPolicyRequest {
  // Required. The name of the principal access boundary policy to delete.
  //
  // Format:
  //   `organizations/{organization_id}/locations/{location}/principalAccessBoundaryPolicies/{principal_access_boundary_policy_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/PrincipalAccessBoundaryPolicy"
    }
  ];

  // Optional. The etag of the principal access boundary policy.
  // If this is provided, it must match the server's etag.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the deletion, but do not
  // actually post it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request will force the deletion of the Policy
  // even if the Policy references PolicyBindings.
  bool force = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListPrincipalAccessBoundaryPolicies method.
message ListPrincipalAccessBoundaryPoliciesRequest {
  // Required. The parent resource, which owns the collection of principal
  // access boundary policies.
  //
  // Format:
  //   `organizations/{organization_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "iam.googleapis.com/PrincipalAccessBoundaryPolicy"
    }
  ];

  // Optional. The maximum number of principal access boundary policies to
  // return. The service may return fewer than this value.
  //
  // If unspecified, at most 50 principal access boundary policies will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListPrincipalAccessBoundaryPolicies` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListPrincipalAccessBoundaryPolicies` must match the call that provided the
  // page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

message ListPrincipalAccessBoundaryPoliciesResponse {
  // The principal access boundary policies from the specified parent.
  repeated PrincipalAccessBoundaryPolicy principal_access_boundary_policies = 1;

  // Optional. A token, which can be sent as `page_token` to retrieve the next
  // page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for SearchPrincipalAccessBoundaryPolicyBindings rpc.
message SearchPrincipalAccessBoundaryPolicyBindingsRequest {
  // Required. The name of the principal access boundary policy.
  // Format:
  //  `organizations/{organization_id}/locations/{location}/principalAccessBoundaryPolicies/{principal_access_boundary_policy_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/PrincipalAccessBoundaryPolicy"
    }
  ];

  // Optional. The maximum number of policy bindings to return. The service may
  // return fewer than this value.
  //
  // If unspecified, at most 50 policy bindings will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `SearchPrincipalAccessBoundaryPolicyBindingsRequest` call. Provide this to
  // retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `SearchPrincipalAccessBoundaryPolicyBindingsRequest` must match the call
  // that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for SearchPrincipalAccessBoundaryPolicyBindings rpc.
message SearchPrincipalAccessBoundaryPolicyBindingsResponse {
  // The policy bindings that reference the specified policy.
  repeated PolicyBinding policy_bindings = 1;

  // Optional. A token, which can be sent as `page_token` to retrieve the next
  // page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}
