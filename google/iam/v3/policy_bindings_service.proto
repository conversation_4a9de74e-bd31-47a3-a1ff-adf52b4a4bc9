// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v3/operation_metadata.proto";
import "google/iam/v3/policy_binding_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Iam.V3";
option go_package = "cloud.google.com/go/iam/apiv3/iampb;iampb";
option java_multiple_files = true;
option java_outer_classname = "PolicyBindingsServiceProto";
option java_package = "com.google.iam.v3";
option (google.api.resource_definition) = {
  type: "iam.googleapis.com/OrganizationLocation"
  pattern: "organizations/{organization}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "iam.googleapis.com/FolderLocation"
  pattern: "folders/{folder}/locations/{location}"
};

// An interface for managing Identity and Access Management (IAM) policy
// bindings.
service PolicyBindings {
  option (google.api.default_host) = "iam.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a policy binding and returns a long-running operation.
  // Callers will need the IAM permissions on both the policy and target.
  // Once the binding is created, the policy is applied to the target.
  rpc CreatePolicyBinding(CreatePolicyBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*}/policyBindings"
      body: "policy_binding"
      additional_bindings {
        post: "/v3/{parent=folders/*/locations/*}/policyBindings"
        body: "policy_binding"
      }
      additional_bindings {
        post: "/v3/{parent=organizations/*/locations/*}/policyBindings"
        body: "policy_binding"
      }
    };
    option (google.api.method_signature) =
        "parent,policy_binding,policy_binding_id";
    option (google.longrunning.operation_info) = {
      response_type: "PolicyBinding"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets a policy binding.
  rpc GetPolicyBinding(GetPolicyBindingRequest) returns (PolicyBinding) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/policyBindings/*}"
      additional_bindings {
        get: "/v3/{name=folders/*/locations/*/policyBindings/*}"
      }
      additional_bindings {
        get: "/v3/{name=organizations/*/locations/*/policyBindings/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a policy binding and returns a long-running operation.
  // Callers will need the IAM permissions on the policy and target in the
  // binding to update, and the IAM permission to remove the existing policy
  // from the binding. Target is immutable and cannot be updated. Once the
  // binding is updated, the new policy is applied to the target.
  rpc UpdatePolicyBinding(UpdatePolicyBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v3/{policy_binding.name=projects/*/locations/*/policyBindings/*}"
      body: "policy_binding"
      additional_bindings {
        patch: "/v3/{policy_binding.name=folders/*/locations/*/policyBindings/*}"
        body: "policy_binding"
      }
      additional_bindings {
        patch: "/v3/{policy_binding.name=organizations/*/locations/*/policyBindings/*}"
        body: "policy_binding"
      }
    };
    option (google.api.method_signature) = "policy_binding,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "PolicyBinding"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a policy binding and returns a long-running operation.
  // Callers will need the IAM permissions on both the policy and target.
  // Once the binding is deleted, the policy no longer applies to the target.
  rpc DeletePolicyBinding(DeletePolicyBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v3/{name=projects/*/locations/*/policyBindings/*}"
      additional_bindings {
        delete: "/v3/{name=folders/*/locations/*/policyBindings/*}"
      }
      additional_bindings {
        delete: "/v3/{name=organizations/*/locations/*/policyBindings/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists policy bindings.
  rpc ListPolicyBindings(ListPolicyBindingsRequest)
      returns (ListPolicyBindingsResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*}/policyBindings"
      additional_bindings {
        get: "/v3/{parent=folders/*/locations/*}/policyBindings"
      }
      additional_bindings {
        get: "/v3/{parent=organizations/*/locations/*}/policyBindings"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Search policy bindings by target. Returns all policy binding objects bound
  // directly to target.
  rpc SearchTargetPolicyBindings(SearchTargetPolicyBindingsRequest)
      returns (SearchTargetPolicyBindingsResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*}/policyBindings:searchTargetPolicyBindings"
      additional_bindings {
        get: "/v3/{parent=folders/*/locations/*}/policyBindings:searchTargetPolicyBindings"
      }
      additional_bindings {
        get: "/v3/{parent=organizations/*/locations/*}/policyBindings:searchTargetPolicyBindings"
      }
    };
    option (google.api.method_signature) = "parent,target";
  }
}

// Request message for CreatePolicyBinding method.
message CreatePolicyBindingRequest {
  // Required. The parent resource where this policy binding will be created.
  // The binding parent is the closest Resource Manager resource (Project,
  // Folder or Organization) to the binding target.
  //
  // Format:
  //
  // * `projects/{project_id}/locations/{location}`
  // * `projects/{project_number}/locations/{location}`
  // * `folders/{folder_id}/locations/{location}`
  // * `organizations/{organization_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "iam.googleapis.com/PolicyBinding"
    }
  ];

  // Required. The ID to use for the policy binding, which will become the final
  // component of the policy binding's resource name.
  //
  // This value must start with a lowercase letter followed by up to 62
  // lowercase letters, numbers, hyphens, or dots. Pattern,
  // /[a-z][a-z0-9-\.]{2,62}/.
  string policy_binding_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The policy binding to create.
  PolicyBinding policy_binding = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the creation, but do not
  // actually post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetPolicyBinding method.
message GetPolicyBindingRequest {
  // Required. The name of the policy binding to retrieve.
  //
  // Format:
  //
  // * `projects/{project_id}/locations/{location}/policyBindings/{policy_binding_id}`
  // * `projects/{project_number}/locations/{location}/policyBindings/{policy_binding_id}`
  // * `folders/{folder_id}/locations/{location}/policyBindings/{policy_binding_id}`
  // * `organizations/{organization_id}/locations/{location}/policyBindings/{policy_binding_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/PolicyBinding"
    }
  ];
}

// Request message for UpdatePolicyBinding method.
message UpdatePolicyBindingRequest {
  // Required. The policy binding to update.
  //
  // The policy binding's `name` field is used to identify the policy binding to
  // update.
  PolicyBinding policy_binding = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the update, but do not
  // actually post it.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of fields to update
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for DeletePolicyBinding method.
message DeletePolicyBindingRequest {
  // Required. The name of the policy binding to delete.
  //
  // Format:
  //
  // * `projects/{project_id}/locations/{location}/policyBindings/{policy_binding_id}`
  // * `projects/{project_number}/locations/{location}/policyBindings/{policy_binding_id}`
  // * `folders/{folder_id}/locations/{location}/policyBindings/{policy_binding_id}`
  // * `organizations/{organization_id}/locations/{location}/policyBindings/{policy_binding_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/PolicyBinding"
    }
  ];

  // Optional. The etag of the policy binding.
  // If this is provided, it must match the server's etag.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the deletion, but do not
  // actually post it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListPolicyBindings method.
message ListPolicyBindingsRequest {
  // Required. The parent resource, which owns the collection of policy
  // bindings.
  //
  // Format:
  //
  // * `projects/{project_id}/locations/{location}`
  // * `projects/{project_number}/locations/{location}`
  // * `folders/{folder_id}/locations/{location}`
  // * `organizations/{organization_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "iam.googleapis.com/PolicyBinding"
    }
  ];

  // Optional. The maximum number of policy bindings to return. The service may
  // return fewer than this value.
  //
  // If unspecified, at most 50 policy bindings will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListPolicyBindings` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListPolicyBindings` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression for filtering the results of the request. Filter
  // rules are case insensitive. Some eligible fields for filtering are:
  //
  // + `target`
  // + `policy`
  //
  // Some examples of filter queries:
  //
  // | Query            | Description                                         |
  // |------------------|-----------------------------------------------------|
  // | `target:ex*`     | The binding target's name starts with "ex".         |
  // | `target:example` | The binding target's name is `example`.             |
  // | `policy:example` | The binding policy's name is `example`.             |
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListPolicyBindings method.
message ListPolicyBindingsResponse {
  // The policy bindings from the specified parent.
  repeated PolicyBinding policy_bindings = 1;

  // Optional. A token, which can be sent as `page_token` to retrieve the next
  // page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for SearchTargetPolicyBindings method.
message SearchTargetPolicyBindingsRequest {
  // Required. The target resource, which is bound to the policy in the binding.
  //
  // Format:
  //
  // * `//iam.googleapis.com/locations/global/workforcePools/POOL_ID`
  // * `//iam.googleapis.com/projects/PROJECT_NUMBER/locations/global/workloadIdentityPools/POOL_ID`
  // * `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID`
  // * `//cloudresourcemanager.googleapis.com/projects/{project_number}`
  // * `//cloudresourcemanager.googleapis.com/folders/{folder_id}`
  // * `//cloudresourcemanager.googleapis.com/organizations/{organization_id}`
  string target = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of policy bindings to return. The service may
  // return fewer than this value.
  //
  // If unspecified, at most 50 policy bindings will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `SearchTargetPolicyBindingsRequest` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all other parameters provided to
  // `SearchTargetPolicyBindingsRequest` must match the call that provided the
  // page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. The parent resource where this search will be performed. This
  // should be the nearest Resource Manager resource (project, folder, or
  // organization) to the target.
  //
  // Format:
  //
  // * `projects/{project_id}/locations/{location}`
  // * `projects/{project_number}/locations/{location}`
  // * `folders/{folder_id}/locations/{location}`
  // * `organizations/{organization_id}/locations/{location}`
  string parent = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "iam.googleapis.com/PolicyBinding"
    }
  ];
}

// Response message for SearchTargetPolicyBindings method.
message SearchTargetPolicyBindingsResponse {
  // The policy bindings bound to the specified target.
  repeated PolicyBinding policy_bindings = 1;

  // Optional. A token, which can be sent as `page_token` to retrieve the next
  // page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}
