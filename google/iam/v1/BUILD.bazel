##############################################################################
# Common
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_grpc_library",
    "java_proto_library",
    "php_gapic_assembly_pkg",
    "php_proto_library",
    "proto_library_with_info",
    "py_proto_library",
    "py_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)
load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "iam_policy_proto",
    srcs = ["iam_policy.proto"],
    deps = [
        ":options_proto",
        ":policy_proto",
        ":resource_policy_member_proto",
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

proto_library(
    name = "policy_proto",
    srcs = ["policy.proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/type:expr_proto",
    ],
)

proto_library(
    name = "options_proto",
    srcs = ["options.proto"],
    deps = [
        "//google/api:annotations_proto",
    ],
)

proto_library(
    name = "resource_policy_member_proto",
    srcs = ["resource_policy_member.proto"],
    deps = [
        "//google/api:field_behavior_proto",
    ],
)

proto_library_with_info(
    name = "iam_proto_with_info",
    deps = [
        ":iam_policy_proto",
        ":options_proto",
        ":policy_proto",
        ":resource_policy_member_proto",
    ],
)

java_proto_library(
    name = "iam_java_proto",
    deps = [
        ":iam_policy_proto",
        ":options_proto",
        ":policy_proto",
        ":resource_policy_member_proto",
    ],
)

java_grpc_library(
    name = "iam_java_grpc",
    srcs = [":iam_policy_proto"],
    deps = [":iam_java_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-iam-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":iam_java_grpc",
        ":iam_java_proto",
        ":iam_policy_proto",
    ],
)

go_proto_library(
    name = "iam_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/iam/apiv1/iampb",
    protos = [
        ":iam_policy_proto",
        ":options_proto",
        ":policy_proto",
        ":resource_policy_member_proto",
    ],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:field_behavior_go_proto",
        "//google/type:expr_go_proto",
    ],
)

# Manual addition for owlbot migration.
go_gapic_library(
    name = "iam_go_gapic",
    srcs = [":iam_proto_with_info"],
    grpc_service_config = None,
    importpath = "cloud.google.com/go/iam/apiv1;iam",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "iam_meta_api.yaml",
    transport = "grpc+rest",
    deps = [":iam_go_proto"],
)

go_gapic_assembly_pkg(
    name = "gapi-cloud-iam-v1-go",
    deps = [
        ":iam_go_gapic",
        ":iam_go_gapic_srcjar-metadata.srcjar",
        ":iam_go_gapic_srcjar-snippets.srcjar",
        ":iam_go_gapic_srcjar-test.srcjar",
        ":iam_go_proto",
    ],
)

cc_proto_library(
    name = "iam_policy_cc_proto",
    deps = [":iam_policy_proto"],
)

cc_proto_library(
    name = "options_cc_proto",
    deps = [":options_proto"],
)

cc_proto_library(
    name = "policy_cc_proto",
    deps = [":policy_proto"],
)

cc_proto_library(
    name = "resource_policy_member_cc_proto",
    deps = [":resource_policy_member_proto"],
)

cc_grpc_library(
    name = "iam_cc_grpc",
    srcs = [":iam_policy_proto"],
    grpc_only = True,
    deps = [":iam_policy_cc_proto"],
)

py_proto_library(
    name = "iam_policy_py_proto",
    deps = [":iam_policy_proto"],
)

py_proto_library(
    name = "policy_py_proto",
    deps = [":policy_proto"],
)

py_proto_library(
    name = "options_py_proto",
    deps = [":options_proto"],
)

py_proto_library(
    name = "resource_policy_member_py_proto",
    deps = [":resource_policy_member_proto"],
)

py_gapic_assembly_pkg(
    name = "iam-v1-py",
    deps = [
        ":iam_policy_py_proto",
        ":policy_py_proto",
        ":options_py_proto",
        ":resource_policy_member_py_proto",
    ],
)

php_proto_library(
    name = "iam_php_proto",
    deps = [
        ":iam_policy_proto",
        ":options_proto",
        ":policy_proto",
        ":resource_policy_member_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-iam-v1-php",
    deps = [":iam_php_proto"],
)

ruby_proto_library(
    name = "iam_ruby_proto",
    deps = [
        ":iam_policy_proto",
        ":options_proto",
        ":policy_proto",
        ":resource_policy_member_proto",
        "//google/iam/v1/logging:audit_data_proto",
    ],
)

ruby_grpc_library(
    name = "iam_ruby_grpc",
    srcs = [":iam_policy_proto"],
    deps = [":iam_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "iam_ruby_gapic",
    srcs = [":iam_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-iam-v1",
    ],
    rest_numeric_enums = False,
    ruby_cloud_description = "An add-on interface used by some Google API clients to provide IAM Policy calls.",
    ruby_cloud_title = "IAM",
    service_yaml = "iam_meta_api.yaml",
    transport = "grpc+rest",
    deps = [
        ":iam_ruby_grpc",
        ":iam_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-iam-v1-ruby",
    deps = [
        ":iam_ruby_gapic",
        ":iam_ruby_grpc",
        ":iam_ruby_proto",
    ],
)

csharp_proto_library(
    name = "iam_csharp_proto",
    deps = [
        ":iam_policy_proto",
        ":options_proto",
        ":policy_proto",
        ":resource_policy_member_proto",
        "//google/iam/v1/logging:audit_data_proto",
    ],
)

csharp_grpc_library(
    name = "iam_csharp_grpc",
    srcs = [":iam_policy_proto"],
    deps = [":iam_csharp_proto"],
)

csharp_gapic_library(
    name = "iam_csharp_gapic",
    srcs = [":iam_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    rest_numeric_enums = False,
    service_yaml = "iam_meta_api.yaml",
    deps = [
        ":iam_csharp_grpc",
        ":iam_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-iam-v1-csharp",
    deps = [
        ":iam_csharp_gapic",
        ":iam_csharp_grpc",
        ":iam_csharp_proto",
    ],
)
