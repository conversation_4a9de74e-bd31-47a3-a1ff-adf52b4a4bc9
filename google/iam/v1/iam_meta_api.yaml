type: google.api.Service
config_version: 3
name: iam-meta-api.googleapis.com
title: IAM Meta API

apis:
- name: google.iam.v1.IAMPolicy

types:
- name: google.iam.v1.PolicyDelta

documentation:
  summary: Manages access control for Google Cloud Platform resources.
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    post: '/v1/{resource=**}:getIamPolicy'
    body: '*'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=**}:setIamPolicy'
    body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=**}:testIamPermissions'
    body: '*'
