load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "audit_data_proto",
    srcs = [
        "audit_data.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/iam/v1:policy_proto",
    ],
)

proto_library_with_info(
    name = "logging_proto_with_info",
    deps = [":audit_data_proto"],
)

##############################################################################
# Java
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "java_proto_library", "java_gapic_assembly_gradle_pkg")

java_proto_library(
    name = "logging_java_proto",
    deps = [":audit_data_proto"],
)

java_gapic_assembly_gradle_pkg(
    name = "google-iam-logging-v1-java",
    deps = [
        ":logging_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_proto_library",
)

go_proto_library(
    name = "logging_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/iam/apiv1/logging/loggingpb",
    protos = [":audit_data_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

go_gapic_assembly_pkg(
    name = "gapi-cloud-iam-admin-go",
    deps = [
        ":logging_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",
)

py_proto_library(
    name = "audit_data_py_proto",
    deps = [":audit_data_proto"],
)

py_gapic_library(
    name = "logging_py_gapic",
    srcs = [":audit_data_proto"],
    opt_args = [
        "warehouse-package-name=google-cloud-iam-logging",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=iam_logging",
    ],
    rest_numeric_enums = False,
    transport = "grpc",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-iam-logging-v1-py",
    deps = [
        ":logging_py_gapic",
    ],
)

py_gapic_assembly_pkg(
    name = "audit-data-py",
    deps = [
        ":audit_data_py_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "logging_php_proto",
    deps = [":audit_data_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-iam-v1-logging-php",
    deps = [":logging_php_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "logging_cc_proto",
    deps = [":audit_data_proto"],
)

cc_grpc_library(
    name = "logging_cc_grpc",
    srcs = [":audit_data_proto"],
    grpc_only = True,
    deps = [":logging_cc_proto"],
)
