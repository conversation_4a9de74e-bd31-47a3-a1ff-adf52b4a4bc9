{"methodConfig": [{"name": [{"service": "google.iam.credentials.v1.IAMCredentials", "method": "GenerateAccessToken"}, {"service": "google.iam.credentials.v1.IAMCredentials", "method": "GenerateIdToken"}, {"service": "google.iam.credentials.v1.IAMCredentials", "method": "SignBlob"}, {"service": "google.iam.credentials.v1.IAMCredentials", "method": "SignJwt"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}