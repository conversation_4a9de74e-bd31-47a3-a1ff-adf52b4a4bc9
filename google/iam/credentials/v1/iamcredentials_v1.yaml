type: google.api.Service
config_version: 3
name: iamcredentials.googleapis.com
title: IAM Service Account Credentials API

apis:
- name: google.iam.credentials.v1.IAMCredentials

documentation:
  summary: 'Creates short-lived, limited-privilege credentials for IAM service accounts.'

authentication:
  rules:
  - selector: 'google.iam.credentials.v1.IAMCredentials.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
