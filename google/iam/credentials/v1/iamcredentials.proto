// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.credentials.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/iam/credentials/v1/common.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Iam.Credentials.V1";
option go_package = "cloud.google.com/go/iam/credentials/apiv1/credentialspb;credentialspb";
option java_multiple_files = true;
option java_outer_classname = "IAMCredentialsProto";
option java_package = "com.google.cloud.iam.credentials.v1";
option php_namespace = "Google\\Cloud\\Iam\\Credentials\\V1";

// A service account is a special type of Google account that belongs to your
// application or a virtual machine (VM), instead of to an individual end user.
// Your application assumes the identity of the service account to call Google
// APIs, so that the users aren't directly involved.
//
// Service account credentials are used to temporarily assume the identity
// of the service account. Supported credential types include OAuth 2.0 access
// tokens, OpenID Connect ID tokens, self-signed JSON Web Tokens (JWTs), and
// more.
service IAMCredentials {
  option (google.api.default_host) = "iamcredentials.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Generates an OAuth 2.0 access token for a service account.
  rpc GenerateAccessToken(GenerateAccessTokenRequest) returns (GenerateAccessTokenResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:generateAccessToken"
      body: "*"
    };
    option (google.api.method_signature) = "name,delegates,scope,lifetime";
  }

  // Generates an OpenID Connect ID token for a service account.
  rpc GenerateIdToken(GenerateIdTokenRequest) returns (GenerateIdTokenResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:generateIdToken"
      body: "*"
    };
    option (google.api.method_signature) = "name,delegates,audience,include_email";
  }

  // Signs a blob using a service account's system-managed private key.
  rpc SignBlob(SignBlobRequest) returns (SignBlobResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:signBlob"
      body: "*"
    };
    option (google.api.method_signature) = "name,delegates,payload";
  }

  // Signs a JWT using a service account's system-managed private key.
  rpc SignJwt(SignJwtRequest) returns (SignJwtResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:signJwt"
      body: "*"
    };
    option (google.api.method_signature) = "name,delegates,payload";
  }
}
