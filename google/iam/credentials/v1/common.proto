// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.credentials.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Iam.Credentials.V1";
option go_package = "cloud.google.com/go/iam/credentials/apiv1/credentialspb;credentialspb";
option java_multiple_files = true;
option java_outer_classname = "IAMCredentialsCommonProto";
option java_package = "com.google.cloud.iam.credentials.v1";
option php_namespace = "Google\\Cloud\\Iam\\Credentials\\V1";
option (google.api.resource_definition) = {
  type: "iam.googleapis.com/ServiceAccount"
  pattern: "projects/{project}/serviceAccounts/{service_account}"
};

message GenerateAccessTokenRequest {
  // Required. The resource name of the service account for which the credentials
  // are requested, in the following format:
  // `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard
  // character is required; replacing it with a project ID is invalid.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // The sequence of service accounts in a delegation chain. Each service
  // account must be granted the `roles/iam.serviceAccountTokenCreator` role
  // on its next service account in the chain. The last service account in the
  // chain must be granted the `roles/iam.serviceAccountTokenCreator` role
  // on the service account that is specified in the `name` field of the
  // request.
  //
  // The delegates must have the following format:
  // `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard
  // character is required; replacing it with a project ID is invalid.
  repeated string delegates = 2;

  // Required. Code to identify the scopes to be included in the OAuth 2.0 access token.
  // See https://developers.google.com/identity/protocols/googlescopes for more
  // information.
  // At least one value required.
  repeated string scope = 4 [(google.api.field_behavior) = REQUIRED];

  // The desired lifetime duration of the access token in seconds.
  // Must be set to a value less than or equal to 3600 (1 hour). If a value is
  // not specified, the token's lifetime will be set to a default value of one
  // hour.
  google.protobuf.Duration lifetime = 7;
}

message GenerateAccessTokenResponse {
  // The OAuth 2.0 access token.
  string access_token = 1;

  // Token expiration time.
  // The expiration time is always set.
  google.protobuf.Timestamp expire_time = 3;
}

message SignBlobRequest {
  // Required. The resource name of the service account for which the credentials
  // are requested, in the following format:
  // `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard
  // character is required; replacing it with a project ID is invalid.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // The sequence of service accounts in a delegation chain. Each service
  // account must be granted the `roles/iam.serviceAccountTokenCreator` role
  // on its next service account in the chain. The last service account in the
  // chain must be granted the `roles/iam.serviceAccountTokenCreator` role
  // on the service account that is specified in the `name` field of the
  // request.
  //
  // The delegates must have the following format:
  // `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard
  // character is required; replacing it with a project ID is invalid.
  repeated string delegates = 3;

  // Required. The bytes to sign.
  bytes payload = 5 [(google.api.field_behavior) = REQUIRED];
}

message SignBlobResponse {
  // The ID of the key used to sign the blob.
  string key_id = 1;

  // The signed blob.
  bytes signed_blob = 4;
}

message SignJwtRequest {
  // Required. The resource name of the service account for which the credentials
  // are requested, in the following format:
  // `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard
  // character is required; replacing it with a project ID is invalid.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // The sequence of service accounts in a delegation chain. Each service
  // account must be granted the `roles/iam.serviceAccountTokenCreator` role
  // on its next service account in the chain. The last service account in the
  // chain must be granted the `roles/iam.serviceAccountTokenCreator` role
  // on the service account that is specified in the `name` field of the
  // request.
  //
  // The delegates must have the following format:
  // `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard
  // character is required; replacing it with a project ID is invalid.
  repeated string delegates = 3;

  // Required. The JWT payload to sign: a JSON object that contains a JWT Claims Set.
  string payload = 5 [(google.api.field_behavior) = REQUIRED];
}

message SignJwtResponse {
  // The ID of the key used to sign the JWT.
  string key_id = 1;

  // The signed JWT.
  string signed_jwt = 2;
}

message GenerateIdTokenRequest {
  // Required. The resource name of the service account for which the credentials
  // are requested, in the following format:
  // `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard
  // character is required; replacing it with a project ID is invalid.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // The sequence of service accounts in a delegation chain. Each service
  // account must be granted the `roles/iam.serviceAccountTokenCreator` role
  // on its next service account in the chain. The last service account in the
  // chain must be granted the `roles/iam.serviceAccountTokenCreator` role
  // on the service account that is specified in the `name` field of the
  // request.
  //
  // The delegates must have the following format:
  // `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard
  // character is required; replacing it with a project ID is invalid.
  repeated string delegates = 2;

  // Required. The audience for the token, such as the API or account that this token
  // grants access to.
  string audience = 3 [(google.api.field_behavior) = REQUIRED];

  // Include the service account email in the token. If set to `true`, the
  // token will contain `email` and `email_verified` claims.
  bool include_email = 4;
}

message GenerateIdTokenResponse {
  // The OpenId Connect ID token.
  string token = 1;
}
