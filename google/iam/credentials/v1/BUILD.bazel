# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "credentials_proto",
    srcs = [
        "common.proto",
        "iamcredentials.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "credentials_proto_with_info",
    deps = [
        ":credentials_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "credentials_java_proto",
    deps = [":credentials_proto"],
)

java_grpc_library(
    name = "credentials_java_grpc",
    srcs = [":credentials_proto"],
    deps = [":credentials_java_proto"],
)

java_gapic_library(
    name = "credentials_java_gapic",
    srcs = [":credentials_proto_with_info"],
    grpc_service_config = "iamcredentials_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iamcredentials_v1.yaml",
    test_deps = [
        ":credentials_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":credentials_java_proto",
    ],
)

java_gapic_test(
    name = "credentials_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.iam.credentials.v1.IAMCredentialsClientHttpJsonTest",
        "com.google.cloud.iam.credentials.v1.IAMCredentialsClientTest",
    ],
    runtime_deps = [":credentials_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-iam-credentials-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":credentials_java_gapic",
        ":credentials_java_grpc",
        ":credentials_java_proto",
        ":credentials_proto",
    ],
)

go_proto_library(
    name = "credentials_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/iam/credentials/apiv1/credentialspb",
    protos = [":credentials_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "credentials_go_gapic",
    srcs = [":credentials_proto_with_info"],
    grpc_service_config = "iamcredentials_grpc_service_config.json",
    importpath = "cloud.google.com/go/iam/credentials/apiv1;credentials",
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "iamcredentials_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":credentials_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-iam-credentials-v1-go",
    deps = [
        ":credentials_go_gapic",
        ":credentials_go_gapic_srcjar-snippets.srcjar",
        ":credentials_go_gapic_srcjar-test.srcjar",
        ":credentials_go_proto",
    ],
)

py_gapic_library(
    name = "credentials_py_gapic",
    srcs = [":credentials_proto"],
    grpc_service_config = "iamcredentials_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-iam",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=iam_credentials",
    ],
    rest_numeric_enums = True,
    service_yaml = "iamcredentials_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "credentials_py_gapic_test",
    srcs = [
        "credentials_py_gapic_pytest.py",
        "credentials_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":credentials_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "iam-credentials-v1-py",
    deps = [
        ":credentials_py_gapic",
    ],
)

php_proto_library(
    name = "credentials_php_proto",
    deps = [":credentials_proto"],
)

php_gapic_library(
    name = "credentials_php_gapic",
    srcs = [":credentials_proto_with_info"],
    grpc_service_config = "iamcredentials_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "iamcredentials_v1.yaml",
    transport = "grpc+rest",
    deps = [":credentials_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-iam-credentials-v1-php",
    deps = [
        ":credentials_php_gapic",
        ":credentials_php_proto",
    ],
)

nodejs_gapic_library(
    name = "credentials_nodejs_gapic",
    package_name = "@google-cloud/iam-credentials",
    src = ":credentials_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "iamcredentials_grpc_service_config.json",
    package = "google.iam.credentials.v1",
    rest_numeric_enums = True,
    service_yaml = "iamcredentials_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "iam-credentials-v1-nodejs",
    deps = [
        ":credentials_nodejs_gapic",
        ":credentials_proto",
    ],
)

ruby_proto_library(
    name = "credentials_ruby_proto",
    deps = [":credentials_proto"],
)

ruby_grpc_library(
    name = "credentials_ruby_grpc",
    srcs = [":credentials_proto"],
    deps = [":credentials_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "credentials_ruby_gapic",
    srcs = [":credentials_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-iam-credentials-v1",
        "ruby-cloud-env-prefix=IAM_CREDENTIALS",
        "ruby-cloud-product-url=https://cloud.google.com/iam",
        "ruby-cloud-api-id=iamcredentials.googleapis.com",
        "ruby-cloud-api-shortname=iamcredentials",
    ],
    grpc_service_config = "iamcredentials_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Service Account Credentials API creates short-lived credentials for Identity and Access Management (IAM) service accounts. You can also use this API to sign JSON Web Tokens (JWTs), as well as blobs of binary data that contain other types of tokens.",
    ruby_cloud_title = "IAM Service Account Credentials V1",
    service_yaml = "iamcredentials_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":credentials_ruby_grpc",
        ":credentials_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-iam-credentials-v1-ruby",
    deps = [
        ":credentials_ruby_gapic",
        ":credentials_ruby_grpc",
        ":credentials_ruby_proto",
    ],
)

csharp_proto_library(
    name = "credentials_csharp_proto",
    deps = [":credentials_proto"],
)

csharp_grpc_library(
    name = "credentials_csharp_grpc",
    srcs = [":credentials_proto"],
    deps = [":credentials_csharp_proto"],
)

csharp_gapic_library(
    name = "credentials_csharp_gapic",
    srcs = [":credentials_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "iamcredentials_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iamcredentials_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":credentials_csharp_grpc",
        ":credentials_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-iam-credentials-v1-csharp",
    deps = [
        ":credentials_csharp_gapic",
        ":credentials_csharp_grpc",
        ":credentials_csharp_proto",
    ],
)

cc_proto_library(
    name = "credentials_cc_proto",
    deps = [":credentials_proto"],
)

cc_grpc_library(
    name = "credentials_cc_grpc",
    srcs = [":credentials_proto"],
    grpc_only = True,
    deps = [":credentials_cc_proto"],
)
