type: google.api.Service
config_version: 3
name: analyticsdata.googleapis.com
title: Google Analytics Data API

apis:
- name: google.analytics.data.v1alpha.AlphaAnalyticsData
- name: google.longrunning.Operations

types:
- name: google.analytics.data.v1alpha.AudienceListMetadata

documentation:
  summary: |-
    Accesses report data in Google Analytics. Warning: Creating multiple
    Customer Applications, Accounts, or Projects to simulate or act as a
    single Customer Application, Account, or Project (respectively) or to
    circumvent Service-specific usage limits or quotas is a direct violation
    of Google Cloud Platform Terms of Service as well as Google APIs Terms of
    Service. These actions can result in immediate termination of your GCP
    project(s) without any warning.

authentication:
  rules:
  - selector: 'google.analytics.data.v1alpha.AlphaAnalyticsData.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.data.v1alpha.AlphaAnalyticsData.SheetExportAudienceList
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics,
        https://www.googleapis.com/auth/analytics.readonly,
        https://www.googleapis.com/auth/drive,
        https://www.googleapis.com/auth/drive.file,
        https://www.googleapis.com/auth/spreadsheets
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics,
        https://www.googleapis.com/auth/analytics.readonly
