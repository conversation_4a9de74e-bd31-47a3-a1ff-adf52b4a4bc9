// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.data.v1alpha;

import "google/analytics/data/v1alpha/data.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/analytics/data/v1alpha;data";
option java_multiple_files = true;
option java_outer_classname = "AnalyticsDataApiProto";
option java_package = "com.google.analytics.data.v1alpha";
option (google.api.resource_definition) = {
  type: "analyticsadmin.googleapis.com/Property"
  pattern: "properties/{property}"
};

// Google Analytics reporting data service.
service AlphaAnalyticsData {
  option (google.api.default_host) = "analyticsdata.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/analytics,"
      "https://www.googleapis.com/auth/analytics.readonly,"
      "https://www.googleapis.com/auth/drive,"
      "https://www.googleapis.com/auth/drive.file,"
      "https://www.googleapis.com/auth/spreadsheets";

  // Returns a customized funnel report of your Google Analytics event data. The
  // data returned from the API is as a table with columns for the requested
  // dimensions and metrics.
  //
  // Funnel exploration lets you visualize the steps your users take to complete
  // a task and quickly see how well they are succeeding or failing at each
  // step. For example, how do prospects become shoppers and then become buyers?
  // How do one time buyers become repeat buyers? With this information, you can
  // improve inefficient or abandoned customer journeys. To learn more, see [GA4
  // Funnel Explorations](https://support.google.com/analytics/answer/9327974).
  //
  // This method is introduced at alpha stability with the intention of
  // gathering feedback on syntax and capabilities before entering beta. To give
  // your feedback on this API, complete the [Google Analytics Data API Funnel
  // Reporting
  // Feedback](https://docs.google.com/forms/d/e/1FAIpQLSdwOlQDJAUoBiIgUZZ3S_Lwi8gr7Bb0k1jhvc-DEg7Rol3UjA/viewform).
  rpc RunFunnelReport(RunFunnelReportRequest)
      returns (RunFunnelReportResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{property=properties/*}:runFunnelReport"
      body: "*"
    };
  }

  // Creates an audience list for later retrieval. This method quickly returns
  // the audience list's resource name and initiates a long running asynchronous
  // request to form an audience list. To list the users in an audience list,
  // first create the audience list through this method and then send the
  // audience resource name to the `QueryAudienceList` method.
  //
  // See [Creating an Audience
  // List](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Lists with examples.
  //
  // An audience list is a snapshot of the users currently in the audience at
  // the time of audience list creation. Creating audience lists for one
  // audience on different days will return different results as users enter and
  // exit the audience.
  //
  // Audiences in Google Analytics 4 allow you to segment your users in the ways
  // that are important to your business. To learn more, see
  // https://support.google.com/analytics/answer/9267572. Audience lists contain
  // the users in each audience.
  //
  // This method is available at beta stability at
  // [audienceExports.create](https://developers.google.com/analytics/devguides/reporting/data/v1/rest/v1beta/properties.audienceExports/create).
  // To give your feedback on this API, complete the [Google Analytics Audience
  // Export API Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc CreateAudienceList(CreateAudienceListRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/audienceLists"
      body: "audience_list"
    };
    option (google.api.method_signature) = "parent,audience_list";
    option (google.longrunning.operation_info) = {
      response_type: "AudienceList"
      metadata_type: "AudienceListMetadata"
    };
  }

  // Retrieves an audience list of users. After creating an audience, the users
  // are not immediately available for listing. First, a request to
  // `CreateAudienceList` is necessary to create an audience list of users, and
  // then second, this method is used to retrieve the users in the audience
  // list.
  //
  // See [Creating an Audience
  // List](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Lists with examples.
  //
  // Audiences in Google Analytics 4 allow you to segment your users in the ways
  // that are important to your business. To learn more, see
  // https://support.google.com/analytics/answer/9267572.
  //
  // This method is available at beta stability at
  // [audienceExports.query](https://developers.google.com/analytics/devguides/reporting/data/v1/rest/v1beta/properties.audienceExports/query).
  // To give your feedback on this API, complete the [Google Analytics Audience
  // Export API Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc QueryAudienceList(QueryAudienceListRequest)
      returns (QueryAudienceListResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{name=properties/*/audienceLists/*}:query"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Exports an audience list of users to a Google Sheet. After creating an
  // audience, the users are not immediately available for listing. First, a
  // request to `CreateAudienceList` is necessary to create an audience list of
  // users, and then second, this method is used to export those users in the
  // audience list to a Google Sheet.
  //
  // See [Creating an Audience
  // List](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Lists with examples.
  //
  // Audiences in Google Analytics 4 allow you to segment your users in the ways
  // that are important to your business. To learn more, see
  // https://support.google.com/analytics/answer/9267572.
  //
  // This method is introduced at alpha stability with the intention of
  // gathering feedback on syntax and capabilities before entering beta. To give
  // your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc SheetExportAudienceList(SheetExportAudienceListRequest)
      returns (SheetExportAudienceListResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{name=properties/*/audienceLists/*}:exportSheet"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets configuration metadata about a specific audience list. This method
  // can be used to understand an audience list after it has been created.
  //
  // See [Creating an Audience
  // List](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Lists with examples.
  //
  // This method is available at beta stability at
  // [audienceExports.get](https://developers.google.com/analytics/devguides/reporting/data/v1/rest/v1beta/properties.audienceExports/get).
  // To give your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc GetAudienceList(GetAudienceListRequest) returns (AudienceList) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/audienceLists/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all audience lists for a property. This method can be used for you to
  // find and reuse existing audience lists rather than creating unnecessary new
  // audience lists. The same audience can have multiple audience lists that
  // represent the list of users that were in an audience on different days.
  //
  // See [Creating an Audience
  // List](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Lists with examples.
  //
  // This method is available at beta stability at
  // [audienceExports.list](https://developers.google.com/analytics/devguides/reporting/data/v1/rest/v1beta/properties.audienceExports/list).
  // To give your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc ListAudienceLists(ListAudienceListsRequest)
      returns (ListAudienceListsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/audienceLists"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a recurring audience list. Recurring audience lists produces new
  // audience lists each day. Audience lists are users in an audience at the
  // time of the list's creation.
  //
  // A recurring audience list ensures that you have audience list based on the
  // most recent data available for use each day. If you manually create
  // audience list, you don't know when an audience list based on an additional
  // day's data is available. This recurring audience list automates the
  // creation of an audience list when an additional day's data is available.
  // You will consume fewer quota tokens by using recurring audience list versus
  // manually creating audience list at various times of day trying to guess
  // when an additional day's data is ready.
  //
  // This method is introduced at alpha stability with the intention of
  // gathering feedback on syntax and capabilities before entering beta. To give
  // your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc CreateRecurringAudienceList(CreateRecurringAudienceListRequest)
      returns (RecurringAudienceList) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/recurringAudienceLists"
      body: "recurring_audience_list"
    };
    option (google.api.method_signature) = "parent,recurring_audience_list";
  }

  // Gets configuration metadata about a specific recurring audience list. This
  // method can be used to understand a recurring audience list's state after it
  // has been created. For example, a recurring audience list resource will
  // generate audience list instances for each day, and this method can be used
  // to get the resource name of the most recent audience list instance.
  //
  // This method is introduced at alpha stability with the intention of
  // gathering feedback on syntax and capabilities before entering beta. To give
  // your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc GetRecurringAudienceList(GetRecurringAudienceListRequest)
      returns (RecurringAudienceList) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/recurringAudienceLists/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all recurring audience lists for a property. This method can be used
  // for you to find and reuse existing recurring audience lists rather than
  // creating unnecessary new recurring audience lists. The same audience can
  // have multiple recurring audience lists that represent different dimension
  // combinations; for example, just the dimension `deviceId` or both the
  // dimensions `deviceId` and `userId`.
  //
  // This method is introduced at alpha stability with the intention of
  // gathering feedback on syntax and capabilities before entering beta. To give
  // your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc ListRecurringAudienceLists(ListRecurringAudienceListsRequest)
      returns (ListRecurringAudienceListsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/recurringAudienceLists"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get all property quotas organized by quota category for a given property.
  // This will charge 1 property quota from the category with the most quota.
  rpc GetPropertyQuotasSnapshot(GetPropertyQuotasSnapshotRequest)
      returns (PropertyQuotasSnapshot) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/propertyQuotasSnapshot}"
    };
    option (google.api.method_signature) = "name";
  }

  // Initiates the creation of a report task. This method quickly
  // returns a report task and initiates a long running
  // asynchronous request to form a customized report of your Google Analytics
  // event data.
  //
  // A report task will be retained and available for querying for 72 hours
  // after it has been created.
  //
  // A report task created by one user can be listed and queried by all users
  // who have access to the property.
  rpc CreateReportTask(CreateReportTaskRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/reportTasks"
      body: "report_task"
    };
    option (google.api.method_signature) = "parent,report_task";
    option (google.longrunning.operation_info) = {
      response_type: "ReportTask"
      metadata_type: "ReportTaskMetadata"
    };
  }

  // Retrieves a report task's content. After requesting the `CreateReportTask`,
  // you are able to retrieve the report content once the report is
  // ACTIVE. This method will return an error if the report task's state is not
  // `ACTIVE`. A query response will return the tabular row & column values of
  // the report.
  rpc QueryReportTask(QueryReportTaskRequest)
      returns (QueryReportTaskResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{name=properties/*/reportTasks/*}:query"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets report metadata about a specific report task. After creating a report
  // task, use this method to check its processing state or inspect its
  // report definition.
  rpc GetReportTask(GetReportTaskRequest) returns (ReportTask) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/reportTasks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all report tasks for a property.
  rpc ListReportTasks(ListReportTasksRequest)
      returns (ListReportTasksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/reportTasks"
    };
    option (google.api.method_signature) = "parent";
  }
}

// A request to create a new recurring audience list.
message CreateRecurringAudienceListRequest {
  // Required. The parent resource where this recurring audience list will be
  // created. Format: `properties/{property}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsdata.googleapis.com/RecurringAudienceList"
    }
  ];

  // Required. The recurring audience list to create.
  RecurringAudienceList recurring_audience_list = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A recurring audience list produces new audience lists each day. Audience
// lists are users in an audience at the time of the list's creation. A
// recurring audience list ensures that you have audience list based on the most
// recent data available for use each day.
message RecurringAudienceList {
  option (google.api.resource) = {
    type: "analyticsdata.googleapis.com/RecurringAudienceList"
    pattern: "properties/{property}/recurringAudienceLists/{recurring_audience_list}"
    plural: "recurringAudienceLists"
    singular: "recurringAudienceList"
  };

  // Output only. Identifier. The recurring audience list resource name assigned
  // during creation. This resource name identifies this
  // `RecurringAudienceList`.
  //
  // Format:
  // `properties/{property}/recurringAudienceLists/{recurring_audience_list}`
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Required. The audience resource name. This resource name identifies the
  // audience being listed and is shared between the Analytics Data & Admin
  // APIs.
  //
  // Format: `properties/{property}/audiences/{audience}`
  string audience = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The descriptive display name for this audience. For example,
  // "Purchasers".
  string audience_display_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The dimensions requested and displayed in the audience list
  // response.
  repeated AudienceDimension dimensions = 4
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The number of remaining days that a recurring audience export
  // will produce an audience list instance. This counter decreases by one each
  // day, and when it reaches zero, no new audience lists will be created.
  //
  // Recurring audience list request for Analytics 360 properties default to 180
  // days and have a maximum of 365 days. Requests for standard Analytics
  // properties default to 14 days and have a maximum of 30 days.
  //
  // The minimum value allowed during creation is 1. Requests above their
  // respective maximum will be coerced to their maximum.
  optional int32 active_days_remaining = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Audience list resource names for audience list instances
  // created for this recurring audience list. One audience list is created for
  // each day, and the audience list will be listed here.
  //
  // This list is ordered with the most recently created audience list first.
  repeated string audience_lists = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Configures webhook notifications to be sent from the Google
  // Analytics Data API to your webhook server. Use of webhooks is optional. If
  // unused, you'll need to poll this API to determine when a recurring audience
  // list creates new audience lists. Webhooks allow a notification to be sent
  // to your servers & avoid the need for polling.
  //
  // Two POST requests will be sent each time a recurring audience list creates
  // an audience list. This happens once per day until a recurring audience list
  // reaches 0 active days remaining. The first request will be sent showing a
  // newly created audience list in its CREATING state. The second request will
  // be sent after the audience list completes creation (either the ACTIVE or
  // FAILED state).
  optional WebhookNotification webhook_notification = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Configures a long-running operation resource to send a webhook notification
// from the Google Analytics Data API to your webhook server when the resource
// updates.
//
// Notification configurations contain private values & are only visible to your
// GCP project. Different GCP projects may attach different webhook
// notifications to the same long-running operation resource.
message WebhookNotification {
  // Optional. The web address that will receive the webhook notification. This
  // address will receive POST requests as the state of the long running
  // operation resource changes. The POST request will contain both a JSON
  // version of the long running operation resource in the body and a
  // `sentTimestamp` field. The sent timestamp will specify the unix
  // microseconds since the epoch that the request was sent; this lets you
  // identify replayed notifications.
  //
  // An example URI is
  // `https://us-central1-example-project-id.cloudfunctions.net/example-function-1`.
  //
  // The URI must use HTTPS and point to a site with a valid SSL certificate on
  // the web server. The URI must have a maximum string length of 128 characters
  // & use only the allowlisted characters from [RFC
  // 1738](https://www.rfc-editor.org/rfc/rfc1738).
  //
  // When your webhook server receives a notification, it is expected to reply
  // with an HTTP response status code of 200 within 5 seconds.
  //
  // A URI is required to use webhook notifications.
  //
  // Requests to this webhook server will contain an ID token authenticating the
  // service account
  // `<EMAIL>`. To learn
  // more about ID tokens, see
  // https://cloud.google.com/docs/authentication/token-types#id. For Google
  // Cloud Functions, this lets you configure your function to require
  // authentication. In Cloud IAM, you will need to grant the service account
  // permissions to the Cloud Run Invoker (`roles/run.invoker`) & Cloud
  // Functions Invoker (`roles/cloudfunctions.invoker`) roles for the webhook
  // post request to pass Google Cloud Functions authentication. This API can
  // send webhook notifications to arbitrary URIs; for webhook servers other
  // than Google Cloud Functions, this ID token in the authorization bearer
  // header should be ignored if it is not needed.
  optional string uri = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The channel token is an arbitrary string value and must have a
  // maximum string length of 64 characters. Channel tokens allow you to verify
  // the source of a webhook notification. This guards against the message being
  // spoofed. The channel token will be specified in the `X-Goog-Channel-Token`
  // HTTP header of the webhook POST request.
  //
  // A channel token is not required to use webhook notifications.
  optional string channel_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to retrieve configuration metadata about a specific recurring
// audience list.
message GetRecurringAudienceListRequest {
  // Required. The recurring audience list resource name.
  // Format:
  // `properties/{property}/recurringAudienceLists/{recurring_audience_list}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsdata.googleapis.com/RecurringAudienceList"
    }
  ];
}

// A request to list all recurring audience lists for a property.
message ListRecurringAudienceListsRequest {
  // Required. All recurring audience lists for this property will be listed in
  // the response. Format: `properties/{property}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsdata.googleapis.com/RecurringAudienceList"
    }
  ];

  // Optional. The maximum number of recurring audience lists to return. The
  // service may return fewer than this value. If unspecified, at most 200
  // recurring audience lists will be returned. The maximum value is 1000
  // (higher values will be coerced to the maximum).
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListRecurringAudienceLists` call. Provide this to retrieve the subsequent
  // page.
  //
  // When paginating, all other parameters provided to
  // `ListRecurringAudienceLists` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A list of all recurring audience lists for a property.
message ListRecurringAudienceListsResponse {
  // Each recurring audience list for a property.
  repeated RecurringAudienceList recurring_audience_lists = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  optional string next_page_token = 2;
}

// A request to return the PropertyQuotasSnapshot for a given category.
message GetPropertyQuotasSnapshotRequest {
  // Required. Quotas from this property will be listed in the response.
  // Format: `properties/{property}/propertyQuotasSnapshot`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/PropertyQuotasSnapshot"
    }
  ];
}

// Current state of all Property Quotas organized by quota category.
message PropertyQuotasSnapshot {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/PropertyQuotasSnapshot"
    pattern: "properties/{property}/propertyQuotasSnapshot"
    plural: "propertyQuotasSnapshots"
    singular: "propertyQuotasSnapshot"
  };

  // Identifier. The property quota snapshot resource name.
  string name = 4 [(google.api.field_behavior) = IDENTIFIER];

  // Property Quota for core property tokens
  PropertyQuota core_property_quota = 1;

  // Property Quota for realtime property tokens
  PropertyQuota realtime_property_quota = 2;

  // Property Quota for funnel property tokens
  PropertyQuota funnel_property_quota = 3;
}

// A request to retrieve configuration metadata about a specific audience list.
message GetAudienceListRequest {
  // Required. The audience list resource name.
  // Format: `properties/{property}/audienceLists/{audience_list}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsdata.googleapis.com/AudienceList"
    }
  ];
}

// A request to list all audience lists for a property.
message ListAudienceListsRequest {
  // Required. All audience lists for this property will be listed in the
  // response. Format: `properties/{property}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsdata.googleapis.com/AudienceList"
    }
  ];

  // Optional. The maximum number of audience lists to return. The service may
  // return fewer than this value. If unspecified, at most 200 audience lists
  // will be returned. The maximum value is 1000 (higher values will be coerced
  // to the maximum).
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListAudienceLists` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAudienceLists` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A list of all audience lists for a property.
message ListAudienceListsResponse {
  // Each audience list for a property.
  repeated AudienceList audience_lists = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  optional string next_page_token = 2;
}

// A request to create a new audience list.
message CreateAudienceListRequest {
  // Required. The parent resource where this audience list will be created.
  // Format: `properties/{property}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsdata.googleapis.com/AudienceList"
    }
  ];

  // Required. The audience list to create.
  AudienceList audience_list = 2 [(google.api.field_behavior) = REQUIRED];
}

// An audience list is a list of users in an audience at the time of the list's
// creation. One audience may have multiple audience lists created for different
// days.
message AudienceList {
  option (google.api.resource) = {
    type: "analyticsdata.googleapis.com/AudienceList"
    pattern: "properties/{property}/audienceLists/{audience_list}"
    plural: "audienceLists"
    singular: "audienceList"
  };

  // The AudienceList currently exists in this state.
  enum State {
    // Unspecified state will never be used.
    STATE_UNSPECIFIED = 0;

    // The AudienceList is currently creating and will be available in the
    // future. Creating occurs immediately after the CreateAudienceList call.
    CREATING = 1;

    // The AudienceList is fully created and ready for querying. An AudienceList
    // is updated to active asynchronously from a request; this occurs some
    // time (for example 15 minutes) after the initial create call.
    ACTIVE = 2;

    // The AudienceList failed to be created. It is possible that re-requesting
    // this audience list will succeed.
    FAILED = 3;
  }

  // Output only. Identifier. The audience list resource name assigned during
  // creation. This resource name identifies this `AudienceList`.
  //
  // Format: `properties/{property}/audienceLists/{audience_list}`
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Required. The audience resource name. This resource name identifies the
  // audience being listed and is shared between the Analytics Data & Admin
  // APIs.
  //
  // Format: `properties/{property}/audiences/{audience}`
  string audience = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The descriptive display name for this audience. For example,
  // "Purchasers".
  string audience_display_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The dimensions requested and displayed in the query response.
  repeated AudienceDimension dimensions = 4
      [(google.api.field_behavior) = REQUIRED];

  // Output only. The current state for this AudienceList.
  optional State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when CreateAudienceList was called and the
  // AudienceList began the `CREATING` state.
  optional google.protobuf.Timestamp begin_creating_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total quota tokens charged during creation of the
  // AudienceList. Because this token count is based on activity from the
  // `CREATING` state, this tokens charged will be fixed once an AudienceList
  // enters the `ACTIVE` or `FAILED` states.
  int32 creation_quota_tokens_charged = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total number of rows in the AudienceList result.
  optional int32 row_count = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Error message is populated when an audience list fails during
  // creation. A common reason for such a failure is quota exhaustion.
  optional string error_message = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The percentage completed for this audience export ranging
  // between 0 to 100.
  optional double percentage_completed = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The recurring audience list that created this audience list.
  // Recurring audience lists create audience lists daily.
  //
  // If audience lists are created directly, they will have no associated
  // recurring audience list, and this field will be blank.
  optional string recurring_audience_list = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Configures webhook notifications to be sent from the Google
  // Analytics Data API to your webhook server. Use of webhooks is optional. If
  // unused, you'll need to poll this API to determine when an audience list is
  // ready to be used. Webhooks allow a notification to be sent to your servers
  // & avoid the need for polling.
  //
  // Either one or two POST requests will be sent to the webhook. The first POST
  // request will be sent immediately showing the newly created audience list in
  // its CREATING state. The second POST request will be sent after the audience
  // list completes creation (either the ACTIVE or FAILED state).
  //
  // If identical audience lists are requested in quick succession, the second &
  // subsequent audience lists can be served from cache. In that case, the
  // audience list create method can return an audience list is already ACTIVE.
  // In this scenario, only one POST request will be sent to the webhook.
  optional WebhookNotification webhook_notification = 13
      [(google.api.field_behavior) = OPTIONAL];
}

// This metadata is currently blank.
message AudienceListMetadata {}

// A request to list users in an audience list.
message QueryAudienceListRequest {
  // Required. The name of the audience list to retrieve users from.
  // Format: `properties/{property}/audienceLists/{audience_list}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The row count of the start row. The first row is counted as row
  // 0.
  //
  // When paging, the first request does not specify offset; or equivalently,
  // sets offset to 0; the first request returns the first `limit` of rows. The
  // second request sets offset to the `limit` of the first request; the second
  // request returns the second `limit` of rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 offset = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of rows to return. If unspecified, 10,000 rows are
  // returned. The API returns a maximum of 250,000 rows per request, no matter
  // how many you ask for. `limit` must be positive.
  //
  // The API can also return fewer rows than the requested `limit`, if there
  // aren't as many dimension values as the `limit`.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 limit = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A list of users in an audience list.
message QueryAudienceListResponse {
  // Configuration data about AudienceList being queried. Returned to help
  // interpret the audience rows in this response. For example, the dimensions
  // in this AudienceList correspond to the columns in the AudienceRows.
  optional AudienceList audience_list = 1;

  // Rows for each user in an audience list. The number of rows in this
  // response will be less than or equal to request's page size.
  repeated AudienceRow audience_rows = 2;

  // The total number of rows in the AudienceList result. `rowCount` is
  // independent of the number of rows returned in the response, the `limit`
  // request parameter, and the `offset` request parameter. For example if a
  // query returns 175 rows and includes `limit` of 50 in the API request, the
  // response will contain `rowCount` of 175 but only 50 rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  optional int32 row_count = 3;
}

// A request to export users in an audience list to a Google Sheet.
message SheetExportAudienceListRequest {
  // Required. The name of the audience list to retrieve users from.
  // Format: `properties/{property}/audienceLists/{audience_list}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsdata.googleapis.com/AudienceList"
    }
  ];

  // Optional. The row count of the start row. The first row is counted as row
  // 0.
  //
  // When paging, the first request does not specify offset; or equivalently,
  // sets offset to 0; the first request returns the first `limit` of rows. The
  // second request sets offset to the `limit` of the first request; the second
  // request returns the second `limit` of rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 offset = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of rows to return. If unspecified, 10,000 rows are
  // returned. The API returns a maximum of 250,000 rows per request, no matter
  // how many you ask for. `limit` must be positive.
  //
  // The API can also return fewer rows than the requested `limit`, if there
  // aren't as many dimension values as the `limit`.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 limit = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The created Google Sheet with the list of users in an audience list.
message SheetExportAudienceListResponse {
  // A uri for you to visit in your browser to view the Google Sheet.
  optional string spreadsheet_uri = 1;

  // An ID that identifies the created Google Sheet resource.
  optional string spreadsheet_id = 2;

  // The total number of rows in the AudienceList result. `rowCount` is
  // independent of the number of rows returned in the response, the `limit`
  // request parameter, and the `offset` request parameter. For example if a
  // query returns 175 rows and includes `limit` of 50 in the API request, the
  // response will contain `rowCount` of 175 but only 50 rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  optional int32 row_count = 3;

  // Configuration data about AudienceList being exported. Returned to help
  // interpret the AudienceList in the Google Sheet of this response.
  //
  // For example, the AudienceList may have more rows than are present in the
  // Google Sheet, and in that case, you may want to send an additional sheet
  // export request with a different `offset` value to retrieve the next page of
  // rows in an additional Google Sheet.
  optional AudienceList audience_list = 4;
}

// Dimension value attributes for the audience user row.
message AudienceRow {
  // Each dimension value attribute for an audience user. One dimension value
  // will be added for each dimension column requested.
  repeated AudienceDimensionValue dimension_values = 1;
}

// An audience dimension is a user attribute. Specific user attributed are
// requested and then later returned in the `QueryAudienceListResponse`.
message AudienceDimension {
  // Optional. The API name of the dimension. See the [API
  // Dimensions](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-api-schema#dimensions)
  // for the list of dimension names.
  string dimension_name = 1 [(google.api.field_behavior) = OPTIONAL];
}

// The value of a dimension.
message AudienceDimensionValue {
  // One kind of dimension value.
  oneof one_value {
    // Value as a string if the dimension type is a string.
    string value = 1;
  }
}

// The request for a funnel report.
message RunFunnelReportRequest {
  // Controls the dimensions present in the funnel visualization sub report
  // response.
  enum FunnelVisualizationType {
    // Unspecified type.
    FUNNEL_VISUALIZATION_TYPE_UNSPECIFIED = 0;

    // A standard (stepped) funnel. The funnel visualization sub report in the
    // response will not contain date.
    STANDARD_FUNNEL = 1;

    // A trended (line chart) funnel. The funnel visualization sub report in the
    // response will contain the date dimension.
    TRENDED_FUNNEL = 2;
  }

  // Optional. A Google Analytics property identifier whose events are tracked.
  // Specified in the URL path and not the body. To learn more, see [where to
  // find your Property
  // ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id).
  // Within a batch request, this property should either be unspecified or
  // consistent with the batch-level property.
  //
  // Example: properties/1234
  string property = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Date ranges of data to read. If multiple date ranges are
  // requested, each response row will contain a zero based date range index. If
  // two date ranges overlap, the event data for the overlapping days is
  // included in the response rows for both date ranges.
  repeated DateRange date_ranges = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The configuration of this request's funnel. This funnel
  // configuration is required.
  Funnel funnel = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If specified, this breakdown adds a dimension to the funnel table
  // sub report response. This breakdown dimension expands each funnel step to
  // the unique values of the breakdown dimension. For example, a breakdown by
  // the `deviceCategory` dimension will create rows for `mobile`, `tablet`,
  // `desktop`, and the total.
  FunnelBreakdown funnel_breakdown = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If specified, next action adds a dimension to the funnel
  // visualization sub report response. This next action dimension expands each
  // funnel step to the unique values of the next action. For example a next
  // action of the `eventName` dimension will create rows for several events
  // (for example `session_start` & `click`) and the total.
  //
  // Next action only supports `eventName` and most Page / Screen dimensions
  // like `pageTitle` and `pagePath`.
  FunnelNextAction funnel_next_action = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The funnel visualization type controls the dimensions present in
  // the funnel visualization sub report response. If not specified,
  // `STANDARD_FUNNEL` is used.
  FunnelVisualizationType funnel_visualization_type = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The configurations of segments. Segments are subsets of a
  // property's data. In a funnel report with segments, the funnel is evaluated
  // in each segment.
  //
  // Each segment specified in this request
  // produces a separate row in the response; in the response, each segment
  // identified by its name.
  //
  // The segments parameter is optional. Requests are limited to 4 segments.
  repeated Segment segments = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of rows to return. If unspecified, 10,000 rows are
  // returned. The API returns a maximum of 250,000 rows per request, no matter
  // how many you ask for. `limit` must be positive.
  //
  // The API can also return fewer rows than the requested `limit`, if there
  // aren't as many dimension values as the `limit`.
  int64 limit = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Dimension filters allow you to ask for only specific dimension
  // values in the report. To learn more, see [Creating a Report: Dimension
  // Filters](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#dimension_filters)
  // for examples. Metrics cannot be used in this filter.
  FilterExpression dimension_filter = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Toggles whether to return the current state of this Analytics
  // Property's quota. Quota is returned in [PropertyQuota](#PropertyQuota).
  bool return_property_quota = 12 [(google.api.field_behavior) = OPTIONAL];
}

// The funnel report response contains two sub reports. The two sub reports are
// different combinations of dimensions and metrics.
message RunFunnelReportResponse {
  // The funnel table is a report with the funnel step, segment, breakdown
  // dimension, active users, completion rate, abandonments, and abandonments
  // rate.
  //
  // The segment dimension is only present in this response if a segment was
  // requested. The breakdown dimension is only present in this response if it
  // was requested.
  FunnelSubReport funnel_table = 1;

  // The funnel visualization is a report with the funnel step, segment, date,
  // next action dimension, and active users.
  //
  // The segment dimension is only present in this response if a segment was
  // requested. The date dimension is only present in this response if it was
  // requested through the `TRENDED_FUNNEL` funnel type. The next action
  // dimension is only present in the response if it was requested.
  FunnelSubReport funnel_visualization = 2;

  // This Analytics Property's quota state including this request.
  PropertyQuota property_quota = 3;

  // Identifies what kind of resource this message is. This `kind` is always the
  // fixed string "analyticsData#runFunnelReport". Useful to distinguish between
  // response types in JSON.
  string kind = 4;
}

// A specific report task configuration.
message ReportTask {
  option (google.api.resource) = {
    type: "analyticsdata.googleapis.com/ReportTask"
    pattern: "properties/{property}/reportTasks/{report_task}"
    plural: "reportTasks"
    singular: "reportTask"
  };

  // The definition of how a report should be run.
  message ReportDefinition {
    // Optional. The dimensions requested and displayed.
    repeated Dimension dimensions = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The metrics requested and displayed.
    repeated Metric metrics = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Date ranges of data to read. If multiple date ranges are
    // requested, each response row will contain a zero based date range index.
    // If two date ranges overlap, the event data for the overlapping days is
    // included in the response rows for both date ranges. In a cohort request,
    // this `dateRanges` must be unspecified.
    repeated DateRange date_ranges = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Dimension filters let you ask for only specific dimension
    // values in the report. To learn more, see [Fundamentals of Dimension
    // Filters](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#dimension_filters)
    // for examples. Metrics cannot be used in this filter.
    FilterExpression dimension_filter = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The filter clause of metrics. Applied after aggregating the
    // report's rows, similar to SQL having-clause. Dimensions cannot be used in
    // this filter.
    FilterExpression metric_filter = 6 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The row count of the start row from Google Analytics Storage.
    // The first row is counted as row 0.
    //
    // When creating a report task, the `offset` and `limit` parameters define
    // the subset of data rows from Google Analytics storage to be included in
    // the generated report. For example, if there are a total of 300,000 rows
    // in Google Analytics storage, the initial report task may have the
    // first 10,000 rows with a limit of 10,000 and an offset of 0.
    // Subsequently, another report task could cover the next 10,000 rows with a
    // limit of 10,000 and an offset of 10,000.
    int64 offset = 7 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The number of rows to return in the Report. If unspecified,
    // 10,000 rows are returned. The API returns a maximum of 250,000 rows per
    // request, no matter how many you ask for. `limit` must be positive.
    //
    // The API can also return fewer rows than the requested `limit`, if there
    // aren't as many dimension values as the `limit`. For instance, there are
    // fewer than 300 possible values for the dimension `country`, so when
    // reporting on only `country`, you can't get more than 300 rows, even if
    // you set `limit` to a higher value.
    int64 limit = 8 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Aggregation of metrics. Aggregated metric values will be shown
    // in rows where the dimension_values are set to
    // "RESERVED_(MetricAggregation)".
    repeated MetricAggregation metric_aggregations = 9
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specifies how rows are ordered in the response.
    repeated OrderBy order_bys = 10 [(google.api.field_behavior) = OPTIONAL];

    // Optional. A currency code in ISO4217 format, such as "AED", "USD", "JPY".
    // If the field is empty, the report uses the property's default currency.
    string currency_code = 11 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Cohort group associated with this request. If there is a cohort
    // group in the request the 'cohort' dimension must be present.
    CohortSpec cohort_spec = 12 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If false or unspecified, each row with all metrics equal to 0
    // will not be returned. If true, these rows will be returned if they are
    // not separately removed by a filter.
    //
    // Regardless of this `keep_empty_rows` setting, only data recorded by the
    // Google Analytics property can be displayed in a report.
    //
    // For example if a property never logs a `purchase` event, then a query for
    // the `eventName` dimension and  `eventCount` metric will not have a row
    // containing eventName: "purchase" and eventCount: 0.
    bool keep_empty_rows = 13 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The report's sampling level.
    optional SamplingLevel sampling_level = 14
        [(google.api.field_behavior) = OPTIONAL];
  }

  // The report metadata for a specific report task.
  message ReportMetadata {
    // The processing state.
    enum State {
      // Unspecified state will never be used.
      STATE_UNSPECIFIED = 0;

      // The report is currently creating and will be available in the
      // future. Creating occurs immediately after the CreateReport call.
      CREATING = 1;

      // The report is fully created and ready for querying.
      ACTIVE = 2;

      // The report failed to be created.
      FAILED = 3;
    }

    // Output only. The current state for this report task.
    optional State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when `CreateReportTask` was called and the report
    // began the `CREATING` state.
    optional google.protobuf.Timestamp begin_creating_time = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The total quota tokens charged during creation of the
    // report. Because this token count is based on activity from the `CREATING`
    // state, this tokens charge will be fixed once a report task enters the
    // `ACTIVE` or `FAILED` state.
    int32 creation_quota_tokens_charged = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The total number of rows in the report result. This field
    // will be populated when the state is active. You can utilize
    // `task_row_count` for pagination within the confines of their existing
    // report.
    optional int32 task_row_count = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Error message is populated if a report task fails during
    // creation.
    optional string error_message = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The total number of rows in Google Analytics storage. If you
    // want to query additional data rows beyond the current report, they can
    // initiate a new report task based on the `total_row_count`.
    //
    // The `task_row_count` represents the number of rows specifically
    // pertaining to the current report, whereas `total_row_count` encompasses
    // the total count of rows across all data retrieved from Google
    // Analytics storage.
    //
    // For example, suppose the current report's `task_row_count` is 20,
    // displaying the data from the first 20 rows. Simultaneously, the
    // `total_row_count` is 30, indicating the presence of data for all 30 rows.
    // The `task_row_count` can be utilizated to paginate through the initial 20
    // rows. To expand the report and include data from all 30 rows, a new
    // report task can be created using the total_row_count to access the full
    // set of 30 rows' worth of data.
    optional int32 total_row_count = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Identifier. The report task resource name assigned during
  // creation. Format: "properties/{property}/reportTasks/{report_task}"
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. A report definition to fetch report data, which describes the
  // structure of a report. It typically includes the fields that will be
  // included in the report and the criteria that will be used to filter the
  // data.
  ReportDefinition report_definition = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The report metadata for a specific report task, which provides
  // information about a report.  It typically includes the following
  // information: the resource name of the report, the state of the report, the
  // timestamp the report was created, etc,
  ReportMetadata report_metadata = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A request to create a report task.
message CreateReportTaskRequest {
  // Required. The parent resource where this report task will be created.
  // Format: `properties/{propertyId}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsdata.googleapis.com/ReportTask"
    }
  ];

  // Required. The report task configuration to create.
  ReportTask report_task = 2 [(google.api.field_behavior) = REQUIRED];
}

// Represents the metadata of a long-running operation. Currently, this metadata
// is blank.
message ReportTaskMetadata {}

// A request to fetch the report content for a report task.
message QueryReportTaskRequest {
  // Required. The report source name.
  // Format: `properties/{property}/reportTasks/{report}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The row count of the start row in the report. The first row is
  // counted as row 0.
  //
  // When paging, the first request does not specify offset; or equivalently,
  // sets offset to 0; the first request returns the first `limit` of rows. The
  // second request sets offset to the `limit` of the first request; the second
  // request returns the second `limit` of rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 offset = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of rows to return from the report. If unspecified,
  // 10,000 rows are returned. The API returns a maximum of 250,000 rows per
  // request, no matter how many you ask for. `limit` must be positive.
  //
  // The API can also return fewer rows than the requested `limit`, if there
  // aren't as many dimension values as the `limit`. The number of rows
  // available to a QueryReportTaskRequest is further limited by the limit of
  // the associated ReportTask. A query can retrieve at most ReportTask.limit
  // rows. For example, if the ReportTask has a limit of 1,000, then a
  // QueryReportTask request with offset=900 and limit=500 will return at most
  // 100 rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 limit = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The report content corresponding to a report task.
message QueryReportTaskResponse {
  // Describes dimension columns. The number of DimensionHeaders and ordering of
  // DimensionHeaders matches the dimensions present in rows.
  repeated DimensionHeader dimension_headers = 1;

  // Describes metric columns. The number of MetricHeaders and ordering of
  // MetricHeaders matches the metrics present in rows.
  repeated MetricHeader metric_headers = 2;

  // Rows of dimension value combinations and metric values in the report.
  repeated Row rows = 3;

  // If requested, the totaled values of metrics.
  repeated Row totals = 4;

  // If requested, the maximum values of metrics.
  repeated Row maximums = 5;

  // If requested, the minimum values of metrics.
  repeated Row minimums = 6;

  // The total number of rows in the query result.
  int32 row_count = 7;

  // Metadata for the report.
  ResponseMetaData metadata = 8;
}

// A request to retrieve configuration metadata about a specific report task.
message GetReportTaskRequest {
  // Required. The report task resource name.
  // Format: `properties/{property}/reportTasks/{report_task}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsdata.googleapis.com/ReportTask"
    }
  ];
}

// A request to list all report tasks for a property.
message ListReportTasksRequest {
  // Required. All report tasks for this property will be listed in the
  // response. Format: `properties/{property}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsdata.googleapis.com/ReportTask"
    }
  ];

  // Optional. The maximum number of report tasks to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListReportTasks` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A list of all report tasks for a property.
message ListReportTasksResponse {
  // Each report task for a property.
  repeated ReportTask report_tasks = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  optional string next_page_token = 2;
}
