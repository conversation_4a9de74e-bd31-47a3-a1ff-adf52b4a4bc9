# This build file includes a target for the Ruby wrapper library for
# google-analytics-data.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for analyticsdata.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta in this case.
ruby_cloud_gapic_library(
    name = "analyticsdata_ruby_wrapper",
    srcs = ["//google/analytics/data/v1beta:data_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-analytics-data",
        "ruby-cloud-env-prefix=ANALYTICS_DATA",
        "ruby-cloud-wrapper-of=v1beta:0.11",
        "ruby-cloud-product-url=https://developers.google.com/analytics/devguides/reporting/data/v1",
        "ruby-cloud-api-id=analyticsdata.googleapis.com",
        "ruby-cloud-api-shortname=analyticsdata",
        "ruby-cloud-service-override=BetaAnalyticsData=AnalyticsData",
    ],
    ruby_cloud_description = "The Google Analytics Data API provides programmatic methods to access report data in Google Analytics 4 (GA4) properties. Google Analytics 4 helps you understand how people use your web, iOS, or Android app.",
    ruby_cloud_title = "Google Analytics Data",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-analytics-data-ruby",
    deps = [
        ":analyticsdata_ruby_wrapper",
    ],
)
