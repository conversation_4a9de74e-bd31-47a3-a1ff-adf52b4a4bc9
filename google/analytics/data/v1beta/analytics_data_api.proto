// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.data.v1beta;

import "google/analytics/data/v1beta/data.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/analytics/data/v1beta;data";
option java_multiple_files = true;
option java_outer_classname = "AnalyticsDataApiProto";
option java_package = "com.google.analytics.data.v1beta";
option (google.api.resource_definition) = {
  type: "analyticsadmin.googleapis.com/Property"
  pattern: "properties/{property}"
};

// Google Analytics reporting data service.
service BetaAnalyticsData {
  option (google.api.default_host) = "analyticsdata.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/analytics,"
      "https://www.googleapis.com/auth/analytics.readonly";

  // Returns a customized report of your Google Analytics event data. Reports
  // contain statistics derived from data collected by the Google Analytics
  // tracking code. The data returned from the API is as a table with columns
  // for the requested dimensions and metrics. Metrics are individual
  // measurements of user activity on your property, such as active users or
  // event count. Dimensions break down metrics across some common criteria,
  // such as country or event name.
  //
  // For a guide to constructing requests & understanding responses, see
  // [Creating a
  // Report](https://developers.google.com/analytics/devguides/reporting/data/v1/basics).
  rpc RunReport(RunReportRequest) returns (RunReportResponse) {
    option (google.api.http) = {
      post: "/v1beta/{property=properties/*}:runReport"
      body: "*"
    };
  }

  // Returns a customized pivot report of your Google Analytics event data.
  // Pivot reports are more advanced and expressive formats than regular
  // reports. In a pivot report, dimensions are only visible if they are
  // included in a pivot. Multiple pivots can be specified to further dissect
  // your data.
  rpc RunPivotReport(RunPivotReportRequest) returns (RunPivotReportResponse) {
    option (google.api.http) = {
      post: "/v1beta/{property=properties/*}:runPivotReport"
      body: "*"
    };
  }

  // Returns multiple reports in a batch. All reports must be for the same
  // Google Analytics property.
  rpc BatchRunReports(BatchRunReportsRequest)
      returns (BatchRunReportsResponse) {
    option (google.api.http) = {
      post: "/v1beta/{property=properties/*}:batchRunReports"
      body: "*"
    };
  }

  // Returns multiple pivot reports in a batch. All reports must be for the same
  // Google Analytics property.
  rpc BatchRunPivotReports(BatchRunPivotReportsRequest)
      returns (BatchRunPivotReportsResponse) {
    option (google.api.http) = {
      post: "/v1beta/{property=properties/*}:batchRunPivotReports"
      body: "*"
    };
  }

  // Returns metadata for dimensions and metrics available in reporting methods.
  // Used to explore the dimensions and metrics. In this method, a Google
  // Analytics property identifier is specified in the request, and
  // the metadata response includes Custom dimensions and metrics as well as
  // Universal metadata.
  //
  // For example if a custom metric with parameter name `levels_unlocked` is
  // registered to a property, the Metadata response will contain
  // `customEvent:levels_unlocked`. Universal metadata are dimensions and
  // metrics applicable to any property such as `country` and `totalUsers`.
  rpc GetMetadata(GetMetadataRequest) returns (Metadata) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/metadata}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a customized report of realtime event data for your property.
  // Events appear in realtime reports seconds after they have been sent to
  // the Google Analytics. Realtime reports show events and usage data for the
  // periods of time ranging from the present moment to 30 minutes ago (up to
  // 60 minutes for Google Analytics 360 properties).
  //
  // For a guide to constructing realtime requests & understanding responses,
  // see [Creating a Realtime
  // Report](https://developers.google.com/analytics/devguides/reporting/data/v1/realtime-basics).
  rpc RunRealtimeReport(RunRealtimeReportRequest)
      returns (RunRealtimeReportResponse) {
    option (google.api.http) = {
      post: "/v1beta/{property=properties/*}:runRealtimeReport"
      body: "*"
    };
  }

  // This compatibility method lists dimensions and metrics that can be added to
  // a report request and maintain compatibility. This method fails if the
  // request's dimensions and metrics are incompatible.
  //
  // In Google Analytics, reports fail if they request incompatible dimensions
  // and/or metrics; in that case, you will need to remove dimensions and/or
  // metrics from the incompatible report until the report is compatible.
  //
  // The Realtime and Core reports have different compatibility rules. This
  // method checks compatibility for Core reports.
  rpc CheckCompatibility(CheckCompatibilityRequest)
      returns (CheckCompatibilityResponse) {
    option (google.api.http) = {
      post: "/v1beta/{property=properties/*}:checkCompatibility"
      body: "*"
    };
  }

  // Creates an audience export for later retrieval. This method quickly returns
  // the audience export's resource name and initiates a long running
  // asynchronous request to form an audience export. To export the users in an
  // audience export, first create the audience export through this method and
  // then send the audience resource name to the `QueryAudienceExport` method.
  //
  // See [Creating an Audience
  // Export](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Exports with examples.
  //
  // An audience export is a snapshot of the users currently in the audience at
  // the time of audience export creation. Creating audience exports for one
  // audience on different days will return different results as users enter and
  // exit the audience.
  //
  // Audiences in Google Analytics 4 allow you to segment your users in the ways
  // that are important to your business. To learn more, see
  // https://support.google.com/analytics/answer/9267572. Audience exports
  // contain the users in each audience.
  //
  // Audience Export APIs have some methods at alpha and other methods at beta
  // stability. The intention is to advance methods to beta stability after some
  // feedback and adoption. To give your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc CreateAudienceExport(CreateAudienceExportRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*}/audienceExports"
      body: "audience_export"
    };
    option (google.api.method_signature) = "parent,audience_export";
    option (google.longrunning.operation_info) = {
      response_type: "AudienceExport"
      metadata_type: "AudienceExportMetadata"
    };
  }

  // Retrieves an audience export of users. After creating an audience, the
  // users are not immediately available for exporting. First, a request to
  // `CreateAudienceExport` is necessary to create an audience export of users,
  // and then second, this method is used to retrieve the users in the audience
  // export.
  //
  // See [Creating an Audience
  // Export](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Exports with examples.
  //
  // Audiences in Google Analytics 4 allow you to segment your users in the ways
  // that are important to your business. To learn more, see
  // https://support.google.com/analytics/answer/9267572.
  //
  // Audience Export APIs have some methods at alpha and other methods at beta
  // stability. The intention is to advance methods to beta stability after some
  // feedback and adoption. To give your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc QueryAudienceExport(QueryAudienceExportRequest)
      returns (QueryAudienceExportResponse) {
    option (google.api.http) = {
      post: "/v1beta/{name=properties/*/audienceExports/*}:query"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets configuration metadata about a specific audience export. This method
  // can be used to understand an audience export after it has been created.
  //
  // See [Creating an Audience
  // Export](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Exports with examples.
  //
  // Audience Export APIs have some methods at alpha and other methods at beta
  // stability. The intention is to advance methods to beta stability after some
  // feedback and adoption. To give your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc GetAudienceExport(GetAudienceExportRequest) returns (AudienceExport) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/audienceExports/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all audience exports for a property. This method can be used for you
  // to find and reuse existing audience exports rather than creating
  // unnecessary new audience exports. The same audience can have multiple
  // audience exports that represent the export of users that were in an
  // audience on different days.
  //
  // See [Creating an Audience
  // Export](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics)
  // for an introduction to Audience Exports with examples.
  //
  // Audience Export APIs have some methods at alpha and other methods at beta
  // stability. The intention is to advance methods to beta stability after some
  // feedback and adoption. To give your feedback on this API, complete the
  // [Google Analytics Audience Export API
  // Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.
  rpc ListAudienceExports(ListAudienceExportsRequest)
      returns (ListAudienceExportsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*}/audienceExports"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The request for compatibility information for a report's dimensions and
// metrics. Check compatibility provides a preview of the compatibility of a
// report; fields shared with the `runReport` request should be the same values
// as in your `runReport` request.
message CheckCompatibilityRequest {
  // A Google Analytics property identifier whose events are tracked. To
  // learn more, see [where to find your Property
  // ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id).
  // `property` should be the same value as in your `runReport` request.
  //
  // Example: properties/1234
  string property = 1;

  // The dimensions in this report. `dimensions` should be the same value as in
  // your `runReport` request.
  repeated Dimension dimensions = 2;

  // The metrics in this report. `metrics` should be the same value as in your
  // `runReport` request.
  repeated Metric metrics = 3;

  // The filter clause of dimensions. `dimensionFilter` should be the same value
  // as in your `runReport` request.
  FilterExpression dimension_filter = 4;

  // The filter clause of metrics. `metricFilter` should be the same value as in
  // your `runReport` request
  FilterExpression metric_filter = 5;

  // Filters the dimensions and metrics in the response to just this
  // compatibility. Commonly used as `”compatibilityFilter”: “COMPATIBLE”`
  // to only return compatible dimensions & metrics.
  Compatibility compatibility_filter = 6;
}

// The compatibility response with the compatibility of each dimension & metric.
message CheckCompatibilityResponse {
  // The compatibility of each dimension.
  repeated DimensionCompatibility dimension_compatibilities = 1;

  // The compatibility of each metric.
  repeated MetricCompatibility metric_compatibilities = 2;
}

// The dimensions, metrics and comparisons currently accepted in reporting
// methods.
message Metadata {
  option (google.api.resource) = {
    type: "analyticsdata.googleapis.com/Metadata"
    pattern: "properties/{property}/metadata"
  };

  // Resource name of this metadata.
  string name = 3;

  // The dimension descriptions.
  repeated DimensionMetadata dimensions = 1;

  // The metric descriptions.
  repeated MetricMetadata metrics = 2;

  // The comparison descriptions.
  repeated ComparisonMetadata comparisons = 4;
}

// The request to generate a report.
message RunReportRequest {
  // A Google Analytics property identifier whose events are tracked.
  // Specified in the URL path and not the body. To learn more, see [where to
  // find your Property
  // ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id).
  // Within a batch request, this property should either be unspecified or
  // consistent with the batch-level property.
  //
  // Example: properties/1234
  string property = 1;

  // The dimensions requested and displayed.
  repeated Dimension dimensions = 2;

  // The metrics requested and displayed.
  repeated Metric metrics = 3;

  // Date ranges of data to read. If multiple date ranges are requested, each
  // response row will contain a zero based date range index. If two date
  // ranges overlap, the event data for the overlapping days is included in the
  // response rows for both date ranges. In a cohort request, this `dateRanges`
  // must be unspecified.
  repeated DateRange date_ranges = 4;

  // Dimension filters let you ask for only specific dimension values in
  // the report. To learn more, see [Fundamentals of Dimension
  // Filters](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#dimension_filters)
  // for examples. Metrics cannot be used in this filter.
  FilterExpression dimension_filter = 5;

  // The filter clause of metrics. Applied after aggregating the report's rows,
  // similar to SQL having-clause. Dimensions cannot be used in this filter.
  FilterExpression metric_filter = 6;

  // The row count of the start row. The first row is counted as row 0.
  //
  // When paging, the first request does not specify offset; or equivalently,
  // sets offset to 0; the first request returns the first `limit` of rows. The
  // second request sets offset to the `limit` of the first request; the second
  // request returns the second `limit` of rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 offset = 7;

  // The number of rows to return. If unspecified, 10,000 rows are returned. The
  // API returns a maximum of 250,000 rows per request, no matter how many you
  // ask for. `limit` must be positive.
  //
  // The API can also return fewer rows than the requested `limit`, if there
  // aren't as many dimension values as the `limit`. For instance, there are
  // fewer than 300 possible values for the dimension `country`, so when
  // reporting on only `country`, you can't get more than 300 rows, even if you
  // set `limit` to a higher value.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 limit = 8;

  // Aggregation of metrics. Aggregated metric values will be shown in rows
  // where the dimension_values are set to "RESERVED_(MetricAggregation)".
  // Aggregates including both comparisons and multiple date ranges will
  // be aggregated based on the date ranges.
  repeated MetricAggregation metric_aggregations = 9;

  // Specifies how rows are ordered in the response.
  // Requests including both comparisons and multiple date ranges will
  // have order bys applied on the comparisons.
  repeated OrderBy order_bys = 10;

  // A currency code in ISO4217 format, such as "AED", "USD", "JPY".
  // If the field is empty, the report uses the property's default currency.
  string currency_code = 11;

  // Cohort group associated with this request. If there is a cohort group
  // in the request the 'cohort' dimension must be present.
  CohortSpec cohort_spec = 12;

  // If false or unspecified, each row with all metrics equal to 0 will not be
  // returned. If true, these rows will be returned if they are not separately
  // removed by a filter.
  //
  // Regardless of this `keep_empty_rows` setting, only data recorded by the
  // Google Analytics property can be displayed in a report.
  //
  // For example if a property never logs a `purchase` event, then a query for
  // the `eventName` dimension and  `eventCount` metric will not have a row
  // eventName: "purchase" and eventCount: 0.
  bool keep_empty_rows = 13;

  // Toggles whether to return the current state of this Google Analytics
  // property's quota. Quota is returned in [PropertyQuota](#PropertyQuota).
  bool return_property_quota = 14;

  // Optional. The configuration of comparisons requested and displayed. The
  // request only requires a comparisons field in order to receive a comparison
  // column in the response.
  repeated Comparison comparisons = 15 [(google.api.field_behavior) = OPTIONAL];
}

// The response report table corresponding to a request.
message RunReportResponse {
  // Describes dimension columns. The number of DimensionHeaders and ordering of
  // DimensionHeaders matches the dimensions present in rows.
  repeated DimensionHeader dimension_headers = 1;

  // Describes metric columns. The number of MetricHeaders and ordering of
  // MetricHeaders matches the metrics present in rows.
  repeated MetricHeader metric_headers = 2;

  // Rows of dimension value combinations and metric values in the report.
  repeated Row rows = 3;

  // If requested, the totaled values of metrics.
  repeated Row totals = 4;

  // If requested, the maximum values of metrics.
  repeated Row maximums = 5;

  // If requested, the minimum values of metrics.
  repeated Row minimums = 6;

  // The total number of rows in the query result. `rowCount` is independent of
  // the number of rows returned in the response, the `limit` request
  // parameter, and the `offset` request parameter. For example if a query
  // returns 175 rows and includes `limit` of 50 in the API request, the
  // response will contain `rowCount` of 175 but only 50 rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int32 row_count = 7;

  // Metadata for the report.
  ResponseMetaData metadata = 8;

  // This Google Analytics property's quota state including this request.
  PropertyQuota property_quota = 9;

  // Identifies what kind of resource this message is. This `kind` is always the
  // fixed string "analyticsData#runReport". Useful to distinguish between
  // response types in JSON.
  string kind = 10;
}

// The request to generate a pivot report.
message RunPivotReportRequest {
  // A Google Analytics property identifier whose events are tracked.
  // Specified in the URL path and not the body. To learn more, see [where to
  // find your Property
  // ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id).
  // Within a batch request, this property should either be unspecified or
  // consistent with the batch-level property.
  //
  // Example: properties/1234
  string property = 1;

  // The dimensions requested. All defined dimensions must be used by one of the
  // following: dimension_expression, dimension_filter, pivots, order_bys.
  repeated Dimension dimensions = 2;

  // The metrics requested, at least one metric needs to be specified. All
  // defined metrics must be used by one of the following: metric_expression,
  // metric_filter, order_bys.
  repeated Metric metrics = 3;

  // The date range to retrieve event data for the report. If multiple date
  // ranges are specified, event data from each date range is used in the
  // report. A special dimension with field name "dateRange" can be included in
  // a Pivot's field names; if included, the report compares between date
  // ranges. In a cohort request, this `dateRanges` must be unspecified.
  repeated DateRange date_ranges = 4;

  // Describes the visual format of the report's dimensions in columns or rows.
  // The union of the fieldNames (dimension names) in all pivots must be a
  // subset of dimension names defined in Dimensions. No two pivots can share a
  // dimension. A dimension is only visible if it appears in a pivot.
  repeated Pivot pivots = 5;

  // The filter clause of dimensions. Dimensions must be requested to be used in
  // this filter. Metrics cannot be used in this filter.
  FilterExpression dimension_filter = 6;

  // The filter clause of metrics. Applied at post aggregation phase, similar to
  // SQL having-clause. Metrics must be requested to be used in this filter.
  // Dimensions cannot be used in this filter.
  FilterExpression metric_filter = 7;

  // A currency code in ISO4217 format, such as "AED", "USD", "JPY".
  // If the field is empty, the report uses the property's default currency.
  string currency_code = 8;

  // Cohort group associated with this request. If there is a cohort group
  // in the request the 'cohort' dimension must be present.
  CohortSpec cohort_spec = 9;

  // If false or unspecified, each row with all metrics equal to 0 will not be
  // returned. If true, these rows will be returned if they are not separately
  // removed by a filter.
  //
  // Regardless of this `keep_empty_rows` setting, only data recorded by the
  // Google Analytics property can be displayed in a report.
  //
  // For example if a property never logs a `purchase` event, then a query for
  // the `eventName` dimension and  `eventCount` metric will not have a row
  // eventName: "purchase" and eventCount: 0.
  bool keep_empty_rows = 10;

  // Toggles whether to return the current state of this Google Analytics
  // property's quota. Quota is returned in [PropertyQuota](#PropertyQuota).
  bool return_property_quota = 11;

  // Optional. The configuration of comparisons requested and displayed. The
  // request requires both a comparisons field and a comparisons dimension to
  // receive a comparison column in the response.
  repeated Comparison comparisons = 12 [(google.api.field_behavior) = OPTIONAL];
}

// The response pivot report table corresponding to a pivot request.
message RunPivotReportResponse {
  // Summarizes the columns and rows created by a pivot. Each pivot in the
  // request produces one header in the response. If we have a request like
  // this:
  //
  //     "pivots": [{
  //       "fieldNames": ["country",
  //         "city"]
  //     },
  //     {
  //       "fieldNames": "eventName"
  //     }]
  //
  // We will have the following `pivotHeaders` in the response:
  //
  //     "pivotHeaders" : [{
  //       "dimensionHeaders": [{
  //         "dimensionValues": [
  //            { "value": "United Kingdom" },
  //            { "value": "London" }
  //          ]
  //       },
  //       {
  //         "dimensionValues": [
  //         { "value": "Japan" },
  //         { "value": "Osaka" }
  //         ]
  //       }]
  //     },
  //     {
  //       "dimensionHeaders": [{
  //         "dimensionValues": [{ "value": "session_start" }]
  //       },
  //       {
  //         "dimensionValues": [{ "value": "scroll" }]
  //       }]
  //     }]
  repeated PivotHeader pivot_headers = 1;

  // Describes dimension columns. The number of DimensionHeaders and ordering of
  // DimensionHeaders matches the dimensions present in rows.
  repeated DimensionHeader dimension_headers = 2;

  // Describes metric columns. The number of MetricHeaders and ordering of
  // MetricHeaders matches the metrics present in rows.
  repeated MetricHeader metric_headers = 3;

  // Rows of dimension value combinations and metric values in the report.
  repeated Row rows = 4;

  // Aggregation of metric values. Can be totals, minimums, or maximums. The
  // returned aggregations are controlled by the metric_aggregations in the
  // pivot. The type of aggregation returned in each row is shown by the
  // dimension_values which are set to "RESERVED_<MetricAggregation>".
  repeated Row aggregates = 5;

  // Metadata for the report.
  ResponseMetaData metadata = 6;

  // This Google Analytics property's quota state including this request.
  PropertyQuota property_quota = 7;

  // Identifies what kind of resource this message is. This `kind` is always the
  // fixed string "analyticsData#runPivotReport". Useful to distinguish between
  // response types in JSON.
  string kind = 8;
}

// The batch request containing multiple report requests.
message BatchRunReportsRequest {
  // A Google Analytics property identifier whose events are tracked.
  // Specified in the URL path and not the body. To learn more, see [where to
  // find your Property
  // ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id).
  // This property must be specified for the batch. The property within
  // RunReportRequest may either be unspecified or consistent with this
  // property.
  //
  // Example: properties/1234
  string property = 1;

  // Individual requests. Each request has a separate report response. Each
  // batch request is allowed up to 5 requests.
  repeated RunReportRequest requests = 2;
}

// The batch response containing multiple reports.
message BatchRunReportsResponse {
  // Individual responses. Each response has a separate report request.
  repeated RunReportResponse reports = 1;

  // Identifies what kind of resource this message is. This `kind` is always the
  // fixed string "analyticsData#batchRunReports". Useful to distinguish between
  // response types in JSON.
  string kind = 2;
}

// The batch request containing multiple pivot report requests.
message BatchRunPivotReportsRequest {
  // A Google Analytics property identifier whose events are tracked.
  // Specified in the URL path and not the body. To learn more, see [where to
  // find your Property
  // ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id).
  // This property must be specified for the batch. The property within
  // RunPivotReportRequest may either be unspecified or consistent with this
  // property.
  //
  // Example: properties/1234
  string property = 1;

  // Individual requests. Each request has a separate pivot report response.
  // Each batch request is allowed up to 5 requests.
  repeated RunPivotReportRequest requests = 2;
}

// The batch response containing multiple pivot reports.
message BatchRunPivotReportsResponse {
  // Individual responses. Each response has a separate pivot report request.
  repeated RunPivotReportResponse pivot_reports = 1;

  // Identifies what kind of resource this message is. This `kind` is always the
  // fixed string "analyticsData#batchRunPivotReports". Useful to distinguish
  // between response types in JSON.
  string kind = 2;
}

// Request for a property's dimension and metric metadata.
message GetMetadataRequest {
  // Required. The resource name of the metadata to retrieve. This name field is
  // specified in the URL path and not URL parameters. Property is a numeric
  // Google Analytics property identifier. To learn more, see [where to find
  // your Property
  // ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id).
  //
  // Example: properties/1234/metadata
  //
  // Set the Property ID to 0 for dimensions and metrics common to all
  // properties. In this special mode, this method will not return custom
  // dimensions and metrics.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsdata.googleapis.com/Metadata"
    }
  ];
}

// The request to generate a realtime report.
message RunRealtimeReportRequest {
  // A Google Analytics property identifier whose events are tracked.
  // Specified in the URL path and not the body. To learn more, see [where to
  // find your Property
  // ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id).
  //
  // Example: properties/1234
  string property = 1;

  // The dimensions requested and displayed.
  repeated Dimension dimensions = 2;

  // The metrics requested and displayed.
  repeated Metric metrics = 3;

  // The filter clause of dimensions. Metrics cannot be used in this filter.
  FilterExpression dimension_filter = 4;

  // The filter clause of metrics. Applied at post aggregation phase, similar to
  // SQL having-clause. Dimensions cannot be used in this filter.
  FilterExpression metric_filter = 5;

  // The number of rows to return. If unspecified, 10,000 rows are returned. The
  // API returns a maximum of 250,000 rows per request, no matter how many you
  // ask for. `limit` must be positive.
  //
  // The API can also return fewer rows than the requested `limit`, if there
  // aren't as many dimension values as the `limit`. For instance, there are
  // fewer than 300 possible values for the dimension `country`, so when
  // reporting on only `country`, you can't get more than 300 rows, even if you
  // set `limit` to a higher value.
  int64 limit = 6;

  // Aggregation of metrics. Aggregated metric values will be shown in rows
  // where the dimension_values are set to "RESERVED_(MetricAggregation)".
  repeated MetricAggregation metric_aggregations = 7;

  // Specifies how rows are ordered in the response.
  repeated OrderBy order_bys = 8;

  // Toggles whether to return the current state of this Google Analytics
  // property's Realtime quota. Quota is returned in
  // [PropertyQuota](#PropertyQuota).
  bool return_property_quota = 9;

  // The minute ranges of event data to read. If unspecified, one minute range
  // for the last 30 minutes will be used. If multiple minute ranges are
  // requested, each response row will contain a zero based minute range index.
  // If two minute ranges overlap, the event data for the overlapping minutes is
  // included in the response rows for both minute ranges.
  repeated MinuteRange minute_ranges = 10;
}

// The response realtime report table corresponding to a request.
message RunRealtimeReportResponse {
  // Describes dimension columns. The number of DimensionHeaders and ordering of
  // DimensionHeaders matches the dimensions present in rows.
  repeated DimensionHeader dimension_headers = 1;

  // Describes metric columns. The number of MetricHeaders and ordering of
  // MetricHeaders matches the metrics present in rows.
  repeated MetricHeader metric_headers = 2;

  // Rows of dimension value combinations and metric values in the report.
  repeated Row rows = 3;

  // If requested, the totaled values of metrics.
  repeated Row totals = 4;

  // If requested, the maximum values of metrics.
  repeated Row maximums = 5;

  // If requested, the minimum values of metrics.
  repeated Row minimums = 6;

  // The total number of rows in the query result. `rowCount` is independent of
  // the number of rows returned in the response and the `limit` request
  // parameter. For example if a query returns 175 rows and includes `limit`
  // of 50 in the API request, the response will contain `rowCount` of 175 but
  // only 50 rows.
  int32 row_count = 7;

  // This Google Analytics property's Realtime quota state including this
  // request.
  PropertyQuota property_quota = 8;

  // Identifies what kind of resource this message is. This `kind` is always the
  // fixed string "analyticsData#runRealtimeReport". Useful to distinguish
  // between response types in JSON.
  string kind = 9;
}

// A request to retrieve configuration metadata about a specific audience
// export.
message GetAudienceExportRequest {
  // Required. The audience export resource name.
  // Format: `properties/{property}/audienceExports/{audience_export}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsdata.googleapis.com/AudienceExport"
    }
  ];
}

// A request to list all audience exports for a property.
message ListAudienceExportsRequest {
  // Required. All audience exports for this property will be listed in the
  // response. Format: `properties/{property}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsdata.googleapis.com/AudienceExport"
    }
  ];

  // Optional. The maximum number of audience exports to return. The service may
  // return fewer than this value. If unspecified, at most 200 audience exports
  // will be returned. The maximum value is 1000 (higher values will be coerced
  // to the maximum).
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListAudienceExports`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAudienceExports`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A list of all audience exports for a property.
message ListAudienceExportsResponse {
  // Each audience export for a property.
  repeated AudienceExport audience_exports = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  optional string next_page_token = 2;
}

// A request to create a new audience export.
message CreateAudienceExportRequest {
  // Required. The parent resource where this audience export will be created.
  // Format: `properties/{property}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsdata.googleapis.com/AudienceExport"
    }
  ];

  // Required. The audience export to create.
  AudienceExport audience_export = 2 [(google.api.field_behavior) = REQUIRED];
}

// An audience export is a list of users in an audience at the time of the
// list's creation. One audience may have multiple audience exports created for
// different days.
message AudienceExport {
  option (google.api.resource) = {
    type: "analyticsdata.googleapis.com/AudienceExport"
    pattern: "properties/{property}/audienceExports/{audience_export}"
    plural: "audienceExports"
    singular: "audienceExport"
  };

  // The AudienceExport currently exists in this state.
  enum State {
    // Unspecified state will never be used.
    STATE_UNSPECIFIED = 0;

    // The AudienceExport is currently creating and will be available in the
    // future. Creating occurs immediately after the CreateAudienceExport call.
    CREATING = 1;

    // The AudienceExport is fully created and ready for querying. An
    // AudienceExport is updated to active asynchronously from a request; this
    // occurs some time (for example 15 minutes) after the initial create call.
    ACTIVE = 2;

    // The AudienceExport failed to be created. It is possible that
    // re-requesting this audience export will succeed.
    FAILED = 3;
  }

  // Output only. Identifier. The audience export resource name assigned during
  // creation. This resource name identifies this `AudienceExport`.
  //
  // Format: `properties/{property}/audienceExports/{audience_export}`
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Required. The audience resource name. This resource name identifies the
  // audience being listed and is shared between the Analytics Data & Admin
  // APIs.
  //
  // Format: `properties/{property}/audiences/{audience}`
  string audience = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The descriptive display name for this audience. For example,
  // "Purchasers".
  string audience_display_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The dimensions requested and displayed in the query response.
  repeated AudienceDimension dimensions = 4
      [(google.api.field_behavior) = REQUIRED];

  // Output only. The current state for this AudienceExport.
  optional State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when CreateAudienceExport was called and the
  // AudienceExport began the `CREATING` state.
  optional google.protobuf.Timestamp begin_creating_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total quota tokens charged during creation of the
  // AudienceExport. Because this token count is based on activity from the
  // `CREATING` state, this tokens charged will be fixed once an AudienceExport
  // enters the `ACTIVE` or `FAILED` states.
  int32 creation_quota_tokens_charged = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total number of rows in the AudienceExport result.
  optional int32 row_count = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Error message is populated when an audience export fails
  // during creation. A common reason for such a failure is quota exhaustion.
  optional string error_message = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The percentage completed for this audience export ranging
  // between 0 to 100.
  optional double percentage_completed = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// This metadata is currently blank.
message AudienceExportMetadata {}

// A request to list users in an audience export.
message QueryAudienceExportRequest {
  // Required. The name of the audience export to retrieve users from.
  // Format: `properties/{property}/audienceExports/{audience_export}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The row count of the start row. The first row is counted as row
  // 0.
  //
  // When paging, the first request does not specify offset; or equivalently,
  // sets offset to 0; the first request returns the first `limit` of rows. The
  // second request sets offset to the `limit` of the first request; the second
  // request returns the second `limit` of rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 offset = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of rows to return. If unspecified, 10,000 rows are
  // returned. The API returns a maximum of 250,000 rows per request, no matter
  // how many you ask for. `limit` must be positive.
  //
  // The API can also return fewer rows than the requested `limit`, if there
  // aren't as many dimension values as the `limit`.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 limit = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A list of users in an audience export.
message QueryAudienceExportResponse {
  // Configuration data about AudienceExport being queried. Returned to help
  // interpret the audience rows in this response. For example, the dimensions
  // in this AudienceExport correspond to the columns in the AudienceRows.
  optional AudienceExport audience_export = 1;

  // Rows for each user in an audience export. The number of rows in this
  // response will be less than or equal to request's page size.
  repeated AudienceRow audience_rows = 2;

  // The total number of rows in the AudienceExport result. `rowCount` is
  // independent of the number of rows returned in the response, the `limit`
  // request parameter, and the `offset` request parameter. For example if a
  // query returns 175 rows and includes `limit` of 50 in the API request, the
  // response will contain `rowCount` of 175 but only 50 rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  optional int32 row_count = 3;
}

// Dimension value attributes for the audience user row.
message AudienceRow {
  // Each dimension value attribute for an audience user. One dimension value
  // will be added for each dimension column requested.
  repeated AudienceDimensionValue dimension_values = 1;
}

// An audience dimension is a user attribute. Specific user attributed are
// requested and then later returned in the `QueryAudienceExportResponse`.
message AudienceDimension {
  // Optional. The API name of the dimension. See the [API
  // Dimensions](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-api-schema#dimensions)
  // for the list of dimension names.
  string dimension_name = 1 [(google.api.field_behavior) = OPTIONAL];
}

// The value of a dimension.
message AudienceDimensionValue {
  // One kind of dimension value.
  oneof one_value {
    // Value as a string if the dimension type is a string.
    string value = 1;
  }
}
