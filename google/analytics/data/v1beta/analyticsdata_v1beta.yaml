type: google.api.Service
config_version: 3
name: analyticsdata.googleapis.com
title: Google Analytics Data API

apis:
- name: google.analytics.data.v1beta.BetaAnalyticsData
- name: google.longrunning.Operations

documentation:
  summary: |-
    Accesses report data in Google Analytics. Warning: Creating multiple
    Customer Applications, Accounts, or Projects to simulate or act as a
    single Customer Application, Account, or Project (respectively) or to
    circumvent Service-specific usage limits or quotas is a direct violation
    of Google Cloud Platform Terms of Service as well as Google APIs Terms of
    Service. These actions can result in immediate termination of your GCP
    project(s) without any warning.

authentication:
  rules:
  - selector: 'google.analytics.data.v1beta.BetaAnalyticsData.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics,
        https://www.googleapis.com/auth/analytics.readonly
