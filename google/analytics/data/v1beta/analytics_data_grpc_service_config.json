{"methodConfig": [{"name": [{"service": "google.analytics.data.v1beta.BetaAnalyticsData"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNKNOWN"]}}, {"name": [{"service": "google.analytics.data.v1beta.BetaAnalyticsData", "method": "RunReport"}, {"service": "google.analytics.data.v1beta.BetaAnalyticsData", "method": "RunPivotReport"}, {"service": "google.analytics.data.v1beta.BetaAnalyticsData", "method": "BatchRunReports"}, {"service": "google.analytics.data.v1beta.BetaAnalyticsData", "method": "BatchRunPivotReports"}, {"service": "google.analytics.data.v1beta.BetaAnalyticsData", "method": "RunRealtimeReport"}, {"service": "google.analytics.data.v1beta.BetaAnalyticsData", "method": "GetMetadata"}, {"service": "google.analytics.data.v1beta.BetaAnalyticsData", "method": "CheckCompatibility"}], "timeout": "60s"}]}