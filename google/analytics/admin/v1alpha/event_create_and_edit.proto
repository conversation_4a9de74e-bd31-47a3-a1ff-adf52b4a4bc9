// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.admin.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/analytics/admin/apiv1alpha/adminpb;adminpb";
option java_multiple_files = true;
option java_package = "com.google.analytics.admin.v1alpha";

// Defines an event parameter to mutate.
message ParameterMutation {
  // Required. The name of the parameter to mutate.
  // This value must:
  // * be less than 40 characters.
  // * be unique across across all mutations within the rule
  // * consist only of letters, digits or _ (underscores)
  // For event edit rules, the name may also be set to 'event_name' to modify
  // the event_name in place.
  string parameter = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The value mutation to perform.
  // * Must be less than 100 characters.
  // * To specify a constant value for the param, use the value's string.
  // * To copy value from another parameter, use syntax like
  // "[[other_parameter]]" For more details, see this [help center
  // article](https://support.google.com/analytics/answer/10085872#modify-an-event&zippy=%2Cin-this-article%2Cmodify-parameters).
  string parameter_value = 2 [(google.api.field_behavior) = REQUIRED];
}

// An Event Create Rule defines conditions that will trigger the creation
// of an entirely new event based upon matched criteria of a source event.
// Additional mutations of the parameters from the source event can be defined.
//
// Unlike Event Edit rules, Event Creation Rules have no defined order.  They
// will all be run independently.
//
// Event Edit and Event Create rules can't be used to modify an event created
// from an Event Create rule.
message EventCreateRule {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/EventCreateRule"
    pattern: "properties/{property}/dataStreams/{data_stream}/eventCreateRules/{event_create_rule}"
  };

  // Output only. Resource name for this EventCreateRule resource.
  // Format:
  // properties/{property}/dataStreams/{data_stream}/eventCreateRules/{event_create_rule}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The name of the new event to be created.
  //
  // This value must:
  // * be less than 40 characters
  // * consist only of letters, digits or _ (underscores)
  // * start with a letter
  string destination_event = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Must have at least one condition, and can have up to 10 max.
  // Conditions on the source event must match for this rule to be applied.
  repeated MatchingCondition event_conditions = 3
      [(google.api.field_behavior) = REQUIRED];

  // If true, the source parameters are copied to the new event.
  // If false, or unset, all non-internal parameters are not copied from the
  // source event. Parameter mutations are applied after the parameters have
  // been copied.
  bool source_copy_parameters = 4;

  // Parameter mutations define parameter behavior on the new event, and
  // are applied in order.
  // A maximum of 20 mutations can be applied.
  repeated ParameterMutation parameter_mutations = 5;
}

// An Event Edit Rule defines conditions that will trigger the creation of an
// entirely new event based upon matched criteria of a source event. Additional
// mutations of the parameters from the source event can be defined.
//
// Unlike Event Create rules, Event Edit Rules are applied in their defined
// order.
//
// Event Edit rules can't be used to modify an event created from an Event
// Create rule.
message EventEditRule {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/EventEditRule"
    pattern: "properties/{property}/dataStreams/{data_stream}/eventEditRules/{event_edit_rule}"
    plural: "eventEditRules"
    singular: "eventEditRule"
  };

  // Identifier. Resource name for this EventEditRule resource.
  // Format:
  // properties/{property}/dataStreams/{data_stream}/eventEditRules/{event_edit_rule}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The display name of this event edit rule. Maximum of 255
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Conditions on the source event must match for this rule to be
  // applied. Must have at least one condition, and can have up to 10 max.
  repeated MatchingCondition event_conditions = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. Parameter mutations define parameter behavior on the new event,
  // and are applied in order. A maximum of 20 mutations can be applied.
  repeated ParameterMutation parameter_mutations = 4
      [(google.api.field_behavior) = REQUIRED];

  // Output only. The order for which this rule will be processed. Rules with an
  // order value lower than this will be processed before this rule, rules with
  // an order value higher than this will be processed after this rule. New
  // event edit rules will be assigned an order value at the end of the order.
  //
  // This value does not apply to event create rules.
  int64 processing_order = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines a condition for when an Event Edit or Event Creation rule applies to
// an event.
message MatchingCondition {
  // Comparison type for matching condition
  enum ComparisonType {
    // Unknown
    COMPARISON_TYPE_UNSPECIFIED = 0;

    // Equals, case sensitive
    EQUALS = 1;

    // Equals, case insensitive
    EQUALS_CASE_INSENSITIVE = 2;

    // Contains, case sensitive
    CONTAINS = 3;

    // Contains, case insensitive
    CONTAINS_CASE_INSENSITIVE = 4;

    // Starts with, case sensitive
    STARTS_WITH = 5;

    // Starts with, case insensitive
    STARTS_WITH_CASE_INSENSITIVE = 6;

    // Ends with, case sensitive
    ENDS_WITH = 7;

    // Ends with, case insensitive
    ENDS_WITH_CASE_INSENSITIVE = 8;

    // Greater than
    GREATER_THAN = 9;

    // Greater than or equal
    GREATER_THAN_OR_EQUAL = 10;

    // Less than
    LESS_THAN = 11;

    // Less than or equal
    LESS_THAN_OR_EQUAL = 12;

    // regular expression. Only supported for web streams.
    REGULAR_EXPRESSION = 13;

    // regular expression, case insensitive. Only supported for web streams.
    REGULAR_EXPRESSION_CASE_INSENSITIVE = 14;
  }

  // Required. The name of the field that is compared against for the condition.
  // If 'event_name' is specified this condition will apply to the name of the
  // event.  Otherwise the condition will apply to a parameter with the
  // specified name.
  //
  // This value cannot contain spaces.
  string field = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of comparison to be applied to the value.
  ComparisonType comparison_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The value being compared against for this condition.  The runtime
  // implementation may perform type coercion of this value to evaluate this
  // condition based on the type of the parameter value.
  string value = 3 [(google.api.field_behavior) = REQUIRED];

  // Whether or not the result of the comparison should be negated. For example,
  // if `negated` is true, then 'equals' comparisons would function as 'not
  // equals'.
  bool negated = 4;
}
