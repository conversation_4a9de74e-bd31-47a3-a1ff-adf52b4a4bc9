type: google.api.Service
config_version: 3
name: analyticsadmin.googleapis.com
title: Google Analytics Admin API

apis:
- name: google.analytics.admin.v1alpha.AnalyticsAdminService

documentation:
  summary: |-
    Manage properties in Google Analytics.  Warning: Creating multiple Customer
    Applications, Accounts, or Projects to simulate or act as a single
    Customer Application, Account, or Project (respectively) or to circumvent
    Service-specific usage limits or quotas is a direct violation of Google
    Cloud Platform Terms of Service as well as Google APIs Terms of Service.
    These actions can result in immediate termination of your GCP project(s)
    without any warning.

authentication:
  rules:
  - selector: 'google.analytics.admin.v1alpha.AnalyticsAdminService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.BatchCreateAccessBindings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.BatchDeleteAccessBindings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.BatchGetAccessBindings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users,
        https://www.googleapis.com/auth/analytics.manage.users.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.BatchUpdateAccessBindings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.CreateAccessBinding
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.DeleteAccessBinding
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.FetchAutomatedGa4ConfigurationOptOut
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.FetchConnectedGa4Property
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetAccessBinding
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users,
        https://www.googleapis.com/auth/analytics.manage.users.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetAccount
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetAdSenseLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetAttributionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetAudience
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetBigQueryLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetCalculatedMetric
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetChannelGroup
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetConversionEvent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetCustomDimension
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetCustomMetric
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetDataRedactionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetDataRetentionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetDataSharingSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetDataStream
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetDisplayVideo360AdvertiserLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetDisplayVideo360AdvertiserLinkProposal
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetEnhancedMeasurementSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetEventCreateRule
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetEventEditRule
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetExpandedDataSet
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetGlobalSiteTag
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetGoogleSignalsSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetKeyEvent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetMeasurementProtocolSecret
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetProperty
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetRollupPropertySourceLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetSKAdNetworkConversionValueSchema
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetSearchAds360Link
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.GetSubpropertyEventFilter
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListAccessBindings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users,
        https://www.googleapis.com/auth/analytics.manage.users.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListAccountSummaries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListAccounts
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListAdSenseLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListAudiences
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListBigQueryLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListCalculatedMetrics
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListChannelGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListConnectedSiteTags
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListConversionEvents
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListCustomDimensions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListCustomMetrics
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListDataStreams
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListDisplayVideo360AdvertiserLinkProposals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListDisplayVideo360AdvertiserLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListEventCreateRules
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListEventEditRules
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListExpandedDataSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListFirebaseLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListGoogleAdsLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListKeyEvents
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListMeasurementProtocolSecrets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListProperties
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListRollupPropertySourceLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListSKAdNetworkConversionValueSchemas
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListSearchAds360Links
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.ListSubpropertyEventFilters
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.RunAccessReport
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1alpha.AnalyticsAdminService.UpdateAccessBinding
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.manage.users
