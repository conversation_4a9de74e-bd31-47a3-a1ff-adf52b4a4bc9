// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.admin.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/analytics/admin/apiv1alpha/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "ExpandedDataSetProto";
option java_package = "com.google.analytics.admin.v1alpha";

// A specific filter for a single dimension
message ExpandedDataSetFilter {
  // A filter for a string-type dimension that matches a particular pattern.
  message StringFilter {
    // The match type for the string filter.
    enum MatchType {
      // Unspecified
      MATCH_TYPE_UNSPECIFIED = 0;

      // Exact match of the string value.
      EXACT = 1;

      // Contains the string value.
      CONTAINS = 2;
    }

    // Required. The match type for the string filter.
    MatchType match_type = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The string value to be matched against.
    string value = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. If true, the match is case-sensitive. If false, the match is
    // case-insensitive.
    // Must be true when match_type is EXACT.
    // Must be false when match_type is CONTAINS.
    bool case_sensitive = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // A filter for a string dimension that matches a particular list of options.
  message InListFilter {
    // Required. The list of possible string values to match against. Must be
    // non-empty.
    repeated string values = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. If true, the match is case-sensitive. If false, the match is
    // case-insensitive.
    // Must be true.
    bool case_sensitive = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // One of the above filters.
  oneof one_filter {
    // A filter for a string-type dimension that matches a particular pattern.
    StringFilter string_filter = 2;

    // A filter for a string dimension that matches a particular list of
    // options.
    InListFilter in_list_filter = 3;
  }

  // Required. The dimension name to filter.
  string field_name = 1 [(google.api.field_behavior) = REQUIRED];
}

// A logical expression of EnhancedDataSet dimension filters.
message ExpandedDataSetFilterExpression {
  // The expression applied to a filter.
  oneof expr {
    // A list of expressions to be AND’ed together. It must contain a
    // ExpandedDataSetFilterExpression with either not_expression or
    // dimension_filter. This must be set for the top level
    // ExpandedDataSetFilterExpression.
    ExpandedDataSetFilterExpressionList and_group = 1;

    // A filter expression to be NOT'ed (that is, inverted, complemented). It
    // must include a dimension_filter. This cannot be set on the
    // top level ExpandedDataSetFilterExpression.
    ExpandedDataSetFilterExpression not_expression = 2;

    // A filter on a single dimension. This cannot be set on the top
    // level ExpandedDataSetFilterExpression.
    ExpandedDataSetFilter filter = 3;
  }
}

// A list of ExpandedDataSet filter expressions.
message ExpandedDataSetFilterExpressionList {
  // A list of ExpandedDataSet filter expressions.
  repeated ExpandedDataSetFilterExpression filter_expressions = 1;
}

// A resource message representing a GA4 ExpandedDataSet.
message ExpandedDataSet {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/ExpandedDataSet"
    pattern: "properties/{property}/expandedDataSets/{expanded_data_set}"
  };

  // Output only. The resource name for this ExpandedDataSet resource.
  // Format: properties/{property_id}/expandedDataSets/{expanded_data_set}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the ExpandedDataSet.
  // Max 200 chars.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the ExpandedDataSet.
  // Max 50 chars.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Immutable. The list of dimensions included in the ExpandedDataSet.
  // See the [API
  // Dimensions](https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema#dimensions)
  // for the list of dimension names.
  repeated string dimension_names = 4 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The list of metrics included in the ExpandedDataSet.
  // See the [API
  // Metrics](https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema#metrics)
  // for the list of dimension names.
  repeated string metric_names = 5 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. A logical expression of ExpandedDataSet filters applied to
  // dimension included in the ExpandedDataSet. This filter is used to reduce
  // the number of rows and thus the chance of encountering `other` row.
  ExpandedDataSetFilterExpression dimension_filter_expression = 6
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when expanded data set began (or will begin) collecing
  // data.
  google.protobuf.Timestamp data_collection_start_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
