// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.admin.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/analytics/admin/apiv1alpha/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "SubpropertyEventFilterProto";
option java_package = "com.google.analytics.admin.v1alpha";

// A specific filter expression
message SubpropertyEventFilterCondition {
  // A filter for a string-type dimension that matches a particular pattern.
  message StringFilter {
    // How the filter will be used to determine a match.
    enum MatchType {
      // Match type unknown or not specified.
      MATCH_TYPE_UNSPECIFIED = 0;

      // Exact match of the string value.
      EXACT = 1;

      // Begins with the string value.
      BEGINS_WITH = 2;

      // Ends with the string value.
      ENDS_WITH = 3;

      // Contains the string value.
      CONTAINS = 4;

      // Full regular expression matches with the string value.
      FULL_REGEXP = 5;

      // Partial regular expression matches with the string value.
      PARTIAL_REGEXP = 6;
    }

    // Required. The match type for the string filter.
    MatchType match_type = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The string value used for the matching.
    string value = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. If true, the string value is case sensitive. If false, the
    // match is case-insensitive.
    bool case_sensitive = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  oneof one_filter {
    // A filter for null values.
    bool null_filter = 2;

    // A filter for a string-type dimension that matches a particular pattern.
    StringFilter string_filter = 3;
  }

  // Required. The field that is being filtered.
  string field_name = 1 [(google.api.field_behavior) = REQUIRED];
}

// A logical expression of Subproperty event filters.
message SubpropertyEventFilterExpression {
  // The expression applied to a filter.
  oneof expr {
    // A list of expressions to OR’ed together. Must only contain
    // not_expression or filter_condition expressions.
    SubpropertyEventFilterExpressionList or_group = 1;

    // A filter expression to be NOT'ed (inverted, complemented). It can only
    // include a filter. This cannot be set on the top level
    // SubpropertyEventFilterExpression.
    SubpropertyEventFilterExpression not_expression = 2;

    // Creates a filter that matches a specific event. This cannot be set on the
    // top level SubpropertyEventFilterExpression.
    SubpropertyEventFilterCondition filter_condition = 3;
  }
}

// A list of Subproperty event filter expressions.
message SubpropertyEventFilterExpressionList {
  // Required. Unordered list. A list of Subproperty event filter expressions
  repeated SubpropertyEventFilterExpression filter_expressions = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = UNORDERED_LIST
  ];
}

// A clause for defining a filter. A filter may be inclusive (events satisfying
// the filter clause are included in the subproperty's data) or exclusive
// (events satisfying the filter clause are excluded from the subproperty's
// data).
message SubpropertyEventFilterClause {
  // Specifies whether this is an include or exclude filter clause.
  enum FilterClauseType {
    // Filter clause type unknown or not specified.
    FILTER_CLAUSE_TYPE_UNSPECIFIED = 0;

    // Events will be included in the Sub property if the filter clause is met.
    INCLUDE = 1;

    // Events will be excluded from the Sub property if the filter clause is
    // met.
    EXCLUDE = 2;
  }

  // Required. The type for the filter clause.
  FilterClauseType filter_clause_type = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The logical expression for what events are sent to the
  // subproperty.
  SubpropertyEventFilterExpression filter_expression = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A resource message representing a GA4 Subproperty event filter.
message SubpropertyEventFilter {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/SubpropertyEventFilter"
    pattern: "properties/{property}/subpropertyEventFilters/{sub_property_event_filter}"
    plural: "subpropertyEventFilters"
    singular: "subpropertyEventFilter"
  };

  // Output only. Format:
  // properties/{ordinary_property_id}/subpropertyEventFilters/{sub_property_event_filter}
  // Example: properties/1234/subpropertyEventFilters/5678
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Resource name of the Subproperty that uses this filter.
  optional string apply_to_property = 2
      [(google.api.field_behavior) = IMMUTABLE];

  // Required. Unordered list. Filter clauses that define the
  // SubpropertyEventFilter. All clauses are AND'ed together to determine what
  // data is sent to the subproperty.
  repeated SubpropertyEventFilterClause filter_clauses = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = UNORDERED_LIST
  ];
}
