type: google.api.Service
config_version: 3
name: analyticsadmin.googleapis.com
title: Google Analytics Admin API

apis:
- name: google.analytics.admin.v1beta.AnalyticsAdminService

documentation:
  summary: |-
    Manage properties in Google Analytics.  Warning: Creating multiple Customer
    Applications, Accounts, or Projects to simulate or act as a single
    Customer Application, Account, or Project (respectively) or to circumvent
    Service-specific usage limits or quotas is a direct violation of Google
    Cloud Platform Terms of Service as well as Google APIs Terms of Service.
    These actions can result in immediate termination of your GCP project(s)
    without any warning.

authentication:
  rules:
  - selector: 'google.analytics.admin.v1beta.AnalyticsAdminService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetAccount
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetConversionEvent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetCustomDimension
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetCustomMetric
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetDataRetentionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetDataSharingSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetDataStream
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetKeyEvent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetMeasurementProtocolSecret
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.GetProperty
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListAccountSummaries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListAccounts
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListConversionEvents
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListCustomDimensions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListCustomMetrics
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListDataStreams
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListFirebaseLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListGoogleAdsLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListKeyEvents
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListMeasurementProtocolSecrets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.ListProperties
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
  - selector: google.analytics.admin.v1beta.AnalyticsAdminService.RunAccessReport
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/analytics.edit,
        https://www.googleapis.com/auth/analytics.readonly
