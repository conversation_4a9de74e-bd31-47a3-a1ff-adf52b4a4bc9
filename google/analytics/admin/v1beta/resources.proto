// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.admin.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/analytics/admin/apiv1beta/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.analytics.admin.v1beta";
option (google.api.resource_definition) = {
  type: "marketingplatformadmin.googleapis.com/Organization"
  pattern: "organizations/{organization}"
};

// The category selected for this property, used for industry benchmarking.
enum IndustryCategory {
  // Industry category unspecified
  INDUSTRY_CATEGORY_UNSPECIFIED = 0;

  // Automotive
  AUTOMOTIVE = 1;

  // Business and industrial markets
  BUSINESS_AND_INDUSTRIAL_MARKETS = 2;

  // Finance
  FINANCE = 3;

  // Healthcare
  HEALTHCARE = 4;

  // Technology
  TECHNOLOGY = 5;

  // Travel
  TRAVEL = 6;

  // Other
  OTHER = 7;

  // Arts and entertainment
  ARTS_AND_ENTERTAINMENT = 8;

  // Beauty and fitness
  BEAUTY_AND_FITNESS = 9;

  // Books and literature
  BOOKS_AND_LITERATURE = 10;

  // Food and drink
  FOOD_AND_DRINK = 11;

  // Games
  GAMES = 12;

  // Hobbies and leisure
  HOBBIES_AND_LEISURE = 13;

  // Home and garden
  HOME_AND_GARDEN = 14;

  // Internet and telecom
  INTERNET_AND_TELECOM = 15;

  // Law and government
  LAW_AND_GOVERNMENT = 16;

  // News
  NEWS = 17;

  // Online communities
  ONLINE_COMMUNITIES = 18;

  // People and society
  PEOPLE_AND_SOCIETY = 19;

  // Pets and animals
  PETS_AND_ANIMALS = 20;

  // Real estate
  REAL_ESTATE = 21;

  // Reference
  REFERENCE = 22;

  // Science
  SCIENCE = 23;

  // Sports
  SPORTS = 24;

  // Jobs and education
  JOBS_AND_EDUCATION = 25;

  // Shopping
  SHOPPING = 26;
}

// Various levels of service for Google Analytics.
enum ServiceLevel {
  // Service level not specified or invalid.
  SERVICE_LEVEL_UNSPECIFIED = 0;

  // The standard version of Google Analytics.
  GOOGLE_ANALYTICS_STANDARD = 1;

  // The paid, premium version of Google Analytics.
  GOOGLE_ANALYTICS_360 = 2;
}

// Different kinds of actors that can make changes to Google Analytics
// resources.
enum ActorType {
  // Unknown or unspecified actor type.
  ACTOR_TYPE_UNSPECIFIED = 0;

  // Changes made by the user specified in actor_email.
  USER = 1;

  // Changes made by the Google Analytics system.
  SYSTEM = 2;

  // Changes made by Google Analytics support team staff.
  SUPPORT = 3;
}

// Types of actions that may change a resource.
enum ActionType {
  // Action type unknown or not specified.
  ACTION_TYPE_UNSPECIFIED = 0;

  // Resource was created in this change.
  CREATED = 1;

  // Resource was updated in this change.
  UPDATED = 2;

  // Resource was deleted in this change.
  DELETED = 3;
}

// Types of resources whose changes may be returned from change history.
enum ChangeHistoryResourceType {
  // Resource type unknown or not specified.
  CHANGE_HISTORY_RESOURCE_TYPE_UNSPECIFIED = 0;

  // Account resource
  ACCOUNT = 1;

  // Property resource
  PROPERTY = 2;

  // FirebaseLink resource
  FIREBASE_LINK = 6;

  // GoogleAdsLink resource
  GOOGLE_ADS_LINK = 7;

  // GoogleSignalsSettings resource
  GOOGLE_SIGNALS_SETTINGS = 8;

  // ConversionEvent resource
  CONVERSION_EVENT = 9;

  // MeasurementProtocolSecret resource
  MEASUREMENT_PROTOCOL_SECRET = 10;

  // DataRetentionSettings resource
  DATA_RETENTION_SETTINGS = 13;

  // DisplayVideo360AdvertiserLink resource
  DISPLAY_VIDEO_360_ADVERTISER_LINK = 14;

  // DisplayVideo360AdvertiserLinkProposal resource
  DISPLAY_VIDEO_360_ADVERTISER_LINK_PROPOSAL = 15;

  // DataStream resource
  DATA_STREAM = 18;

  // AttributionSettings resource
  ATTRIBUTION_SETTINGS = 20;
}

// Types of Property resources.
enum PropertyType {
  // Unknown or unspecified property type
  PROPERTY_TYPE_UNSPECIFIED = 0;

  // Ordinary GA4 property
  PROPERTY_TYPE_ORDINARY = 1;

  // GA4 subproperty
  PROPERTY_TYPE_SUBPROPERTY = 2;

  // GA4 rollup property
  PROPERTY_TYPE_ROLLUP = 3;
}

// A resource message representing a Google Analytics account.
message Account {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/Account"
    pattern: "accounts/{account}"
  };

  // Output only. Resource name of this account.
  // Format: accounts/{account}
  // Example: "accounts/100"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this account was originally created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when account payload fields were last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Human-readable display name for this account.
  string display_name = 4 [(google.api.field_behavior) = REQUIRED];

  // Country of business. Must be a Unicode CLDR region code.
  string region_code = 5;

  // Output only. Indicates whether this Account is soft-deleted or not. Deleted
  // accounts are excluded from List results unless specifically requested.
  bool deleted = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URI for a Google Marketing Platform organization resource.
  // Only set when this account is connected to a GMP organization.
  // Format: marketingplatformadmin.googleapis.com/organizations/{org_id}
  string gmp_organization = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "marketingplatformadmin.googleapis.com/Organization"
    }
  ];
}

// A resource message representing a Google Analytics GA4 property.
message Property {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/Property"
    pattern: "properties/{property}"
  };

  // Output only. Resource name of this property.
  // Format: properties/{property_id}
  // Example: "properties/1000"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The property type for this Property resource. When creating a
  // property, if the type is "PROPERTY_TYPE_UNSPECIFIED", then
  // "ORDINARY_PROPERTY" will be implied.
  PropertyType property_type = 14 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when the entity was originally created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when entity payload fields were last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Resource name of this property's logical parent.
  //
  // Note: The Property-Moving UI can be used to change the parent.
  // Format: accounts/{account}, properties/{property}
  // Example: "accounts/100", "properties/101"
  string parent = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Required. Human-readable display name for this property.
  //
  // The max allowed display name length is 100 UTF-16 code units.
  string display_name = 5 [(google.api.field_behavior) = REQUIRED];

  // Industry associated with this property
  // Example: AUTOMOTIVE, FOOD_AND_DRINK
  IndustryCategory industry_category = 6;

  // Required. Reporting Time Zone, used as the day boundary for reports,
  // regardless of where the data originates. If the time zone honors DST,
  // Analytics will automatically adjust for the changes.
  //
  // NOTE: Changing the time zone only affects data going forward, and is not
  // applied retroactively.
  //
  // Format: https://www.iana.org/time-zones
  // Example: "America/Los_Angeles"
  string time_zone = 7 [(google.api.field_behavior) = REQUIRED];

  // The currency type used in reports involving monetary values.
  //
  //
  // Format: https://en.wikipedia.org/wiki/ISO_4217
  // Examples: "USD", "EUR", "JPY"
  string currency_code = 8;

  // Output only. The Google Analytics service level that applies to this
  // property.
  ServiceLevel service_level = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set, the time at which this property was trashed. If not
  // set, then this property is not currently in the trash can.
  google.protobuf.Timestamp delete_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set, the time at which this trashed property will be
  // permanently deleted. If not set, then this property is not currently in the
  // trash can and is not slated to be deleted.
  google.protobuf.Timestamp expire_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The resource name of the parent account
  // Format: accounts/{account_id}
  // Example: "accounts/123"
  string account = 13 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];
}

// A resource message representing a data stream.
message DataStream {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DataStream"
    pattern: "properties/{property}/dataStreams/{data_stream}"
  };

  // Data specific to web streams.
  message WebStreamData {
    // Output only. Analytics Measurement ID.
    //
    // Example: "G-1A2BCD345E"
    string measurement_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. ID of the corresponding web app in Firebase, if any.
    // This ID can change if the web app is deleted and recreated.
    string firebase_app_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Domain name of the web app being measured, or empty.
    // Example: "http://www.google.com", "https://www.google.com"
    string default_uri = 3;
  }

  // Data specific to Android app streams.
  message AndroidAppStreamData {
    // Output only. ID of the corresponding Android app in Firebase, if any.
    // This ID can change if the Android app is deleted and recreated.
    string firebase_app_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. The package name for the app being measured.
    // Example: "com.example.myandroidapp"
    string package_name = 2 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Data specific to iOS app streams.
  message IosAppStreamData {
    // Output only. ID of the corresponding iOS app in Firebase, if any.
    // This ID can change if the iOS app is deleted and recreated.
    string firebase_app_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Required. Immutable. The Apple App Store Bundle ID for the app
    // Example: "com.example.myiosapp"
    string bundle_id = 2 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.field_behavior) = REQUIRED
    ];
  }

  // The type of the data stream.
  enum DataStreamType {
    // Type unknown or not specified.
    DATA_STREAM_TYPE_UNSPECIFIED = 0;

    // Web data stream.
    WEB_DATA_STREAM = 1;

    // Android app data stream.
    ANDROID_APP_DATA_STREAM = 2;

    // iOS app data stream.
    IOS_APP_DATA_STREAM = 3;
  }

  // Data for specific data stream types. The message that will be
  // set corresponds to the type of this stream.
  oneof stream_data {
    // Data specific to web streams. Must be populated if type is
    // WEB_DATA_STREAM.
    WebStreamData web_stream_data = 6;

    // Data specific to Android app streams. Must be populated if type is
    // ANDROID_APP_DATA_STREAM.
    AndroidAppStreamData android_app_stream_data = 7;

    // Data specific to iOS app streams. Must be populated if type is
    // IOS_APP_DATA_STREAM.
    IosAppStreamData ios_app_stream_data = 8;
  }

  // Output only. Resource name of this Data Stream.
  // Format: properties/{property_id}/dataStreams/{stream_id}
  // Example: "properties/1000/dataStreams/2000"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The type of this DataStream resource.
  DataStreamType type = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // Human-readable display name for the Data Stream.
  //
  // Required for web data streams.
  //
  // The max allowed display name length is 255 UTF-16 code units.
  string display_name = 3;

  // Output only. Time when this stream was originally created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when stream payload fields were last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A link between a GA4 property and a Firebase project.
message FirebaseLink {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/FirebaseLink"
    pattern: "properties/{property}/firebaseLinks/{firebase_link}"
  };

  // Output only. Example format: properties/1234/firebaseLinks/5678
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Firebase project resource name. When creating a FirebaseLink,
  // you may provide this resource name using either a project number or project
  // ID. Once this resource has been created, returned FirebaseLinks will always
  // have a project_name that contains a project number.
  //
  // Format: 'projects/{project number}'
  // Example: 'projects/1234'
  string project = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when this FirebaseLink was originally created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A link between a GA4 property and a Google Ads account.
message GoogleAdsLink {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/GoogleAdsLink"
    pattern: "properties/{property}/googleAdsLinks/{google_ads_link}"
  };

  // Output only. Format:
  // properties/{propertyId}/googleAdsLinks/{googleAdsLinkId}
  //
  // Note: googleAdsLinkId is not the Google Ads customer ID.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Google Ads customer ID.
  string customer_id = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. If true, this link is for a Google Ads manager account.
  bool can_manage_clients = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Enable personalized advertising features with this integration.
  // Automatically publish my Google Analytics audience lists and Google
  // Analytics remarketing events/parameters to the linked Google Ads account.
  // If this field is not set on create/update, it will be defaulted to true.
  google.protobuf.BoolValue ads_personalization_enabled = 5;

  // Output only. Time when this link was originally created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this link was last updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the user that created the link.
  // An empty string will be returned if the email address can't be retrieved.
  string creator_email_address = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A resource message representing data sharing settings of a Google Analytics
// account.
message DataSharingSettings {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DataSharingSettings"
    pattern: "accounts/{account}/dataSharingSettings"
  };

  // Output only. Resource name.
  // Format: accounts/{account}/dataSharingSettings
  // Example: "accounts/1000/dataSharingSettings"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Allows Google support to access the data in order to help troubleshoot
  // issues.
  bool sharing_with_google_support_enabled = 2;

  // Allows Google sales teams that are assigned to the customer to access the
  // data in order to suggest configuration changes to improve results.
  // Sales team restrictions still apply when enabled.
  bool sharing_with_google_assigned_sales_enabled = 3;

  // Allows any of Google sales to access the data in order to suggest
  // configuration changes to improve results.
  bool sharing_with_google_any_sales_enabled = 4;

  // Allows Google to use the data to improve other Google products or services.
  bool sharing_with_google_products_enabled = 5;

  // Allows Google to share the data anonymously in aggregate form with others.
  bool sharing_with_others_enabled = 6;
}

// A virtual resource representing an overview of an account and
// all its child GA4 properties.
message AccountSummary {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/AccountSummary"
    pattern: "accountSummaries/{account_summary}"
  };

  // Resource name for this account summary.
  // Format: accountSummaries/{account_id}
  // Example: "accountSummaries/1000"
  string name = 1;

  // Resource name of account referred to by this account summary
  // Format: accounts/{account_id}
  // Example: "accounts/1000"
  string account = 2 [(google.api.resource_reference) = {
    type: "analyticsadmin.googleapis.com/Account"
  }];

  // Display name for the account referred to in this account summary.
  string display_name = 3;

  // List of summaries for child accounts of this account.
  repeated PropertySummary property_summaries = 4;
}

// A virtual resource representing metadata for a GA4 property.
message PropertySummary {
  // Resource name of property referred to by this property summary
  // Format: properties/{property_id}
  // Example: "properties/1000"
  string property = 1 [(google.api.resource_reference) = {
    type: "analyticsadmin.googleapis.com/Property"
  }];

  // Display name for the property referred to in this property summary.
  string display_name = 2;

  // The property's property type.
  PropertyType property_type = 3;

  // Resource name of this property's logical parent.
  //
  // Note: The Property-Moving UI can be used to change the parent.
  // Format: accounts/{account}, properties/{property}
  // Example: "accounts/100", "properties/200"
  string parent = 4;
}

// A secret value used for sending hits to Measurement Protocol.
message MeasurementProtocolSecret {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    pattern: "properties/{property}/dataStreams/{data_stream}/measurementProtocolSecrets/{measurement_protocol_secret}"
  };

  // Output only. Resource name of this secret. This secret may be a child of
  // any type of stream. Format:
  // properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Human-readable display name for this secret.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The measurement protocol secret value. Pass this value to the
  // api_secret field of the Measurement Protocol API when sending hits to this
  // secret's parent property.
  string secret_value = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A set of changes within a Google Analytics account or its child properties
// that resulted from the same cause. Common causes would be updates made in the
// Google Analytics UI, changes from customer support, or automatic Google
// Analytics system changes.
message ChangeHistoryEvent {
  // ID of this change history event. This ID is unique across Google Analytics.
  string id = 1;

  // Time when change was made.
  google.protobuf.Timestamp change_time = 2;

  // The type of actor that made this change.
  ActorType actor_type = 3;

  // Email address of the Google account that made the change. This will be a
  // valid email address if the actor field is set to USER, and empty otherwise.
  // Google accounts that have been deleted will cause an error.
  string user_actor_email = 4;

  // If true, then the list of changes returned was filtered, and does not
  // represent all changes that occurred in this event.
  bool changes_filtered = 5;

  // A list of changes made in this change history event that fit the filters
  // specified in SearchChangeHistoryEventsRequest.
  repeated ChangeHistoryChange changes = 6;
}

// A description of a change to a single Google Analytics resource.
message ChangeHistoryChange {
  // A snapshot of a resource as before or after the result of a change in
  // change history.
  message ChangeHistoryResource {
    oneof resource {
      // A snapshot of an Account resource in change history.
      Account account = 1;

      // A snapshot of a Property resource in change history.
      Property property = 2;

      // A snapshot of a FirebaseLink resource in change history.
      FirebaseLink firebase_link = 6;

      // A snapshot of a GoogleAdsLink resource in change history.
      GoogleAdsLink google_ads_link = 7;

      // A snapshot of a ConversionEvent resource in change history.
      ConversionEvent conversion_event = 11;

      // A snapshot of a MeasurementProtocolSecret resource in change history.
      MeasurementProtocolSecret measurement_protocol_secret = 12;

      // A snapshot of a data retention settings resource in change history.
      DataRetentionSettings data_retention_settings = 15;

      // A snapshot of a DataStream resource in change history.
      DataStream data_stream = 18;
    }
  }

  // Resource name of the resource whose changes are described by this entry.
  string resource = 1;

  // The type of action that changed this resource.
  ActionType action = 2;

  // Resource contents from before the change was made. If this resource was
  // created in this change, this field will be missing.
  ChangeHistoryResource resource_before_change = 3;

  // Resource contents from after the change was made. If this resource was
  // deleted in this change, this field will be missing.
  ChangeHistoryResource resource_after_change = 4;
}

// A conversion event in a Google Analytics property.
message ConversionEvent {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/ConversionEvent"
    pattern: "properties/{property}/conversionEvents/{conversion_event}"
  };

  // Defines a default value/currency for a conversion event. Both value and
  // currency must be provided.
  message DefaultConversionValue {
    // This value will be used to populate the value for all conversions
    // of the specified event_name where the event "value" parameter is unset.
    optional double value = 1;

    // When a conversion event for this event_name has no set currency,
    // this currency will be applied as the default. Must be in ISO 4217
    // currency code format. See https://en.wikipedia.org/wiki/ISO_4217 for
    // more information.
    optional string currency_code = 2;
  }

  // The method by which conversions will be counted across multiple events
  // within a session.
  enum ConversionCountingMethod {
    // Counting method not specified.
    CONVERSION_COUNTING_METHOD_UNSPECIFIED = 0;

    // Each Event instance is considered a Conversion.
    ONCE_PER_EVENT = 1;

    // An Event instance is considered a Conversion at most once per session per
    // user.
    ONCE_PER_SESSION = 2;
  }

  // Output only. Resource name of this conversion event.
  // Format: properties/{property}/conversionEvents/{conversion_event}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The event name for this conversion event.
  // Examples: 'click', 'purchase'
  string event_name = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when this conversion event was created in the property.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set, this event can currently be deleted with
  // DeleteConversionEvent.
  bool deletable = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set to true, this conversion event refers to a custom
  // event.  If set to false, this conversion event refers to a default event in
  // GA. Default events typically have special meaning in GA. Default events are
  // usually created for you by the GA system, but in some cases can be created
  // by property admins. Custom events count towards the maximum number of
  // custom conversion events that may be created per property.
  bool custom = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The method by which conversions will be counted across multiple
  // events within a session. If this value is not provided, it will be set to
  // `ONCE_PER_EVENT`.
  ConversionCountingMethod counting_method = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines a default value/currency for a conversion event.
  optional DefaultConversionValue default_conversion_value = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// A key event in a Google Analytics property.
message KeyEvent {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/KeyEvent"
    pattern: "properties/{property}/keyEvents/{key_event}"
    plural: "keyEvents"
    singular: "keyEvent"
  };

  // Defines a default value/currency for a key event.
  message DefaultValue {
    // Required. This will be used to populate the "value" parameter for all
    // occurrences of this Key Event (specified by event_name) where that
    // parameter is unset.
    double numeric_value = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. When an occurrence of this Key Event (specified by event_name)
    // has no set currency this currency will be applied as the default. Must be
    // in ISO 4217 currency code format.
    //
    // See https://en.wikipedia.org/wiki/ISO_4217 for more information.
    string currency_code = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // The method by which Key Events will be counted across multiple events
  // within a session.
  enum CountingMethod {
    // Counting method not specified.
    COUNTING_METHOD_UNSPECIFIED = 0;

    // Each Event instance is considered a Key Event.
    ONCE_PER_EVENT = 1;

    // An Event instance is considered a Key Event at most once per session per
    // user.
    ONCE_PER_SESSION = 2;
  }

  // Output only. Resource name of this key event.
  // Format: properties/{property}/keyEvents/{key_event}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The event name for this key event.
  // Examples: 'click', 'purchase'
  string event_name = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when this key event was created in the property.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set to true, this event can be deleted.
  bool deletable = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set to true, this key event refers to a custom event.  If
  // set to false, this key event refers to a default event in GA. Default
  // events typically have special meaning in GA. Default events are usually
  // created for you by the GA system, but in some cases can be created by
  // property admins. Custom events count towards the maximum number of
  // custom key events that may be created per property.
  bool custom = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The method by which Key Events will be counted across multiple
  // events within a session.
  CountingMethod counting_method = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Defines a default value/currency for a key event.
  DefaultValue default_value = 7 [(google.api.field_behavior) = OPTIONAL];
}

// A definition for a CustomDimension.
message CustomDimension {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/CustomDimension"
    pattern: "properties/{property}/customDimensions/{custom_dimension}"
  };

  // Valid values for the scope of this dimension.
  enum DimensionScope {
    // Scope unknown or not specified.
    DIMENSION_SCOPE_UNSPECIFIED = 0;

    // Dimension scoped to an event.
    EVENT = 1;

    // Dimension scoped to a user.
    USER = 2;

    // Dimension scoped to eCommerce items
    ITEM = 3;
  }

  // Output only. Resource name for this CustomDimension resource.
  // Format: properties/{property}/customDimensions/{customDimension}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Tagging parameter name for this custom dimension.
  //
  // If this is a user-scoped dimension, then this is the user property name.
  // If this is an event-scoped dimension, then this is the event parameter
  // name.
  //
  // If this is an item-scoped dimension, then this is the parameter
  // name found in the eCommerce items array.
  //
  // May only contain alphanumeric and underscore characters, starting with a
  // letter. Max length of 24 characters for user-scoped dimensions, 40
  // characters for event-scoped dimensions.
  string parameter_name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Display name for this custom dimension as shown in the Analytics
  // UI. Max length of 82 characters, alphanumeric plus space and underscore
  // starting with a letter. Legacy system-generated display names may contain
  // square brackets, but updates to this field will never permit square
  // brackets.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description for this custom dimension. Max length of 150
  // characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. The scope of this dimension.
  DimensionScope scope = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. If set to true, sets this dimension as NPA and excludes it from
  // ads personalization.
  //
  // This is currently only supported by user-scoped custom dimensions.
  bool disallow_ads_personalization = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// A definition for a custom metric.
message CustomMetric {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/CustomMetric"
    pattern: "properties/{property}/customMetrics/{custom_metric}"
  };

  // Possible types of representing the custom metric's value.
  //
  // Currency representation may change in the future, requiring a breaking API
  // change.
  enum MeasurementUnit {
    // MeasurementUnit unspecified or missing.
    MEASUREMENT_UNIT_UNSPECIFIED = 0;

    // This metric uses default units.
    STANDARD = 1;

    // This metric measures a currency.
    CURRENCY = 2;

    // This metric measures feet.
    FEET = 3;

    // This metric measures meters.
    METERS = 4;

    // This metric measures kilometers.
    KILOMETERS = 5;

    // This metric measures miles.
    MILES = 6;

    // This metric measures milliseconds.
    MILLISECONDS = 7;

    // This metric measures seconds.
    SECONDS = 8;

    // This metric measures minutes.
    MINUTES = 9;

    // This metric measures hours.
    HOURS = 10;
  }

  // The scope of this metric.
  enum MetricScope {
    // Scope unknown or not specified.
    METRIC_SCOPE_UNSPECIFIED = 0;

    // Metric scoped to an event.
    EVENT = 1;
  }

  // Labels that mark the data in this custom metric as data that should be
  // restricted to specific users.
  enum RestrictedMetricType {
    // Type unknown or unspecified.
    RESTRICTED_METRIC_TYPE_UNSPECIFIED = 0;

    // Metric reports cost data.
    COST_DATA = 1;

    // Metric reports revenue data.
    REVENUE_DATA = 2;
  }

  // Output only. Resource name for this CustomMetric resource.
  // Format: properties/{property}/customMetrics/{customMetric}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Tagging name for this custom metric.
  //
  // If this is an event-scoped metric, then this is the event parameter
  // name.
  //
  // May only contain alphanumeric and underscore charactes, starting with a
  // letter. Max length of 40 characters for event-scoped metrics.
  string parameter_name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Display name for this custom metric as shown in the Analytics UI.
  // Max length of 82 characters, alphanumeric plus space and underscore
  // starting with a letter. Legacy system-generated display names may contain
  // square brackets, but updates to this field will never permit square
  // brackets.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description for this custom dimension.
  // Max length of 150 characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The type for the custom metric's value.
  MeasurementUnit measurement_unit = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. The scope of this custom metric.
  MetricScope scope = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Types of restricted data that this metric may contain. Required
  // for metrics with CURRENCY measurement unit. Must be empty for metrics with
  // a non-CURRENCY measurement unit.
  repeated RestrictedMetricType restricted_metric_type = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Settings values for data retention. This is a singleton resource.
message DataRetentionSettings {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DataRetentionSettings"
    pattern: "properties/{property}/dataRetentionSettings"
  };

  // Valid values for the data retention duration.
  enum RetentionDuration {
    // Data retention time duration is not specified.
    RETENTION_DURATION_UNSPECIFIED = 0;

    // The data retention time duration is 2 months.
    TWO_MONTHS = 1;

    // The data retention time duration is 14 months.
    FOURTEEN_MONTHS = 3;

    // The data retention time duration is 26 months.
    // Available to 360 properties only.
    TWENTY_SIX_MONTHS = 4;

    // The data retention time duration is 38 months.
    // Available to 360 properties only.
    THIRTY_EIGHT_MONTHS = 5;

    // The data retention time duration is 50 months.
    // Available to 360 properties only.
    FIFTY_MONTHS = 6;
  }

  // Output only. Resource name for this DataRetentionSetting resource.
  // Format: properties/{property}/dataRetentionSettings
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The length of time that event-level data is retained.
  RetentionDuration event_data_retention = 2;

  // If true, reset the retention period for the user identifier with every
  // event from that user.
  bool reset_user_data_on_new_activity = 3;
}
