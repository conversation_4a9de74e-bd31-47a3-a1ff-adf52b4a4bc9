// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.logging.v1;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/chat/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ChatAppLogEntryProto";
option java_package = "com.google.chat.logging.v1";

// JSON payload of error messages. If the Cloud Logging API is enabled, these
// error messages are logged to
// [Google Cloud Logging](https://cloud.google.com/logging/docs).
message ChatAppLogEntry {
  // The deployment that caused the error. For Chat bots built in Apps Script,
  // this is the deployment ID defined by Apps Script.
  string deployment = 1;

  // The error code and message.
  google.rpc.Status error = 2;

  // The unencrypted `callback_method` name that was running when the error was
  // encountered.
  string deployment_function = 3;
}
