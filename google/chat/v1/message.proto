// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/apps/card/v1/card.proto";
import "google/chat/v1/action_status.proto";
import "google/chat/v1/annotation.proto";
import "google/chat/v1/attachment.proto";
import "google/chat/v1/contextual_addon.proto";
import "google/chat/v1/deletion_metadata.proto";
import "google/chat/v1/matched_url.proto";
import "google/chat/v1/reaction.proto";
import "google/chat/v1/slash_command.proto";
import "google/chat/v1/space.proto";
import "google/chat/v1/user.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "MessageProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// A message in a Google Chat space.
message Message {
  option (google.api.resource) = {
    type: "chat.googleapis.com/Message"
    pattern: "spaces/{space}/messages/{message}"
  };

  // Identifier. Resource name of the message.
  //
  // Format: `spaces/{space}/messages/{message}`
  //
  //
  // Where `{space}` is the ID of the space where the message is posted and
  // `{message}` is a system-assigned ID for the message. For example,
  // `spaces/AAAAAAAAAAA/messages/BBBBBBBBBBB.BBBBBBBBBBB`.
  //
  // If you set a custom ID when you create a message, you can use this ID to
  // specify the message in a request by replacing `{message}` with the value
  // from the `clientAssignedMessageId` field. For example,
  // `spaces/AAAAAAAAAAA/messages/client-custom-name`. For details, see [Name
  // a
  // message](https://developers.google.com/workspace/chat/create-messages#name_a_created_message).
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The user who created the message.
  // If your Chat app [authenticates as a
  // user](https://developers.google.com/workspace/chat/authenticate-authorize-chat-user),
  // the output populates the
  // [user](https://developers.google.com/workspace/chat/api/reference/rest/v1/User)
  // `name` and `type`.
  User sender = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. For spaces created in Chat, the time at which the
  // message was created. This field is output only, except when used in import
  // mode spaces.
  //
  // For import mode spaces, set this field to the historical timestamp at which
  // the message was created in the source in order to preserve the original
  // creation time.
  google.protobuf.Timestamp create_time = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Output only. The time at which the message was last edited by a user. If
  // the message has never been edited, this field is empty.
  google.protobuf.Timestamp last_update_time = 23
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the message was deleted in
  // Google Chat. If the message is never deleted, this field is empty.
  google.protobuf.Timestamp delete_time = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Plain-text body of the message. The first link to an image,
  // video, or web page generates a [preview
  // chip](https://developers.google.com/workspace/chat/preview-links). You can
  // also [@mention a Google Chat
  // user](https://developers.google.com/workspace/chat/format-messages#messages-@mention),
  // or everyone in the space.
  //
  // To learn about creating text messages, see [Send a
  // message](https://developers.google.com/workspace/chat/create-messages).
  string text = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Contains the message `text` with markups added to communicate
  // formatting. This field might not capture all formatting visible in the UI,
  // but includes the following:
  //
  // * [Markup
  // syntax](https://developers.google.com/workspace/chat/format-messages)
  // for bold, italic, strikethrough, monospace, monospace block, and bulleted
  // list.
  //
  // * [User
  // mentions](https://developers.google.com/workspace/chat/format-messages#messages-@mention)
  // using the format `<users/{user}>`.
  //
  // * Custom hyperlinks using the format `<{url}|{rendered_text}>` where the
  // first string is the URL and the second is the rendered text—for example,
  // `<http://example.com|custom text>`.
  //
  // * Custom emoji using the format `:{emoji_name}:`—for example, `:smile:`.
  // This doesn't apply to Unicode emoji, such as `U+1F600` for a grinning
  // face emoji.
  //
  // For more information, see [View text formatting sent in a
  // message](https://developers.google.com/workspace/chat/format-messages#view_text_formatting_sent_in_a_message)
  string formatted_text = 43 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Deprecated: Use `cards_v2` instead.
  //
  // Rich, formatted, and interactive cards that you can use to display UI
  // elements such as: formatted texts, buttons, and clickable images. Cards are
  // normally displayed below the plain-text body of the message. `cards` and
  // `cards_v2` can have a maximum size of 32 KB.
  repeated ContextualAddOnMarkup.Card cards = 5 [deprecated = true];

  // Optional. An array of
  // [cards](https://developers.google.com/workspace/chat/api/reference/rest/v1/cards).
  //
  // Only Chat apps can create cards. If your Chat app [authenticates as a
  // user](https://developers.google.com/workspace/chat/authenticate-authorize-chat-user),
  // the messages can't contain cards.
  //
  // To learn how to create a message that contains cards, see [Send a
  // message](https://developers.google.com/workspace/chat/create-messages).
  //
  // [Card builder](https://addons.gsuite.google.com/uikit/builder)
  repeated CardWithId cards_v2 = 22 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Annotations associated with the `text` in this message.
  repeated Annotation annotations = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The thread the message belongs to. For example usage, see
  // [Start or reply to a message
  // thread](https://developers.google.com/workspace/chat/create-messages#create-message-thread).
  Thread thread = 11;

  // Output only. If your Chat app [authenticates as a
  // user](https://developers.google.com/workspace/chat/authenticate-authorize-chat-user),
  // the output only populates the
  // [space](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces)
  // `name`.
  Space space = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A plain-text description of the message's cards, used when the
  // actual cards can't be displayed—for example, mobile notifications.
  string fallback_text = 13 [(google.api.field_behavior) = OPTIONAL];

  // Input only. Parameters that a Chat app can use to configure how its
  // response is posted.
  ActionResponse action_response = 14
      [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Plain-text body of the message with all Chat app mentions
  // stripped out.
  string argument_text = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Slash command information, if applicable.
  SlashCommand slash_command = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User-uploaded attachment.
  repeated Attachment attachment = 18 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A URL in `spaces.messages.text` that matches a link preview
  // pattern. For more information, see [Preview
  // links](https://developers.google.com/workspace/chat/preview-links).
  MatchedUrl matched_url = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When `true`, the message is a response in a reply thread. When
  // `false`, the message is visible in the space's top-level conversation as
  // either the first message of a thread or a message with no threaded replies.
  //
  // If the space doesn't support reply in threads, this field is always
  // `false`.
  bool thread_reply = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A custom ID for the message. You can use field to identify a
  // message, or to get, delete, or update a message. To set a custom ID,
  // specify the
  // [`messageId`](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.messages/create#body.QUERY_PARAMETERS.message_id)
  // field when you create the message. For details, see [Name a
  // message](https://developers.google.com/workspace/chat/create-messages#name_a_created_message).
  string client_assigned_message_id = 32
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The list of emoji reaction summaries on the message.
  repeated EmojiReactionSummary emoji_reaction_summaries = 33
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. Input for creating a message, otherwise output only.
  // The user that can view the message. When set, the message is private and
  // only visible to the specified user and the Chat app. To include this field
  // in your request, you must call the Chat API using [app
  // authentication](https://developers.google.com/workspace/chat/authenticate-authorize-chat-app)
  // and omit the following:
  //
  // * [Attachments](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.messages.attachments)
  // * [Accessory
  // widgets](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.messages#Message.AccessoryWidget)
  //
  // For details, see [Send a message
  // privately](https://developers.google.com/workspace/chat/create-messages#private).
  User private_message_viewer = 36 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Output only. Information about a deleted message. A message is deleted when
  // `delete_time` is set.
  DeletionMetadata deletion_metadata = 38
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Information about a message that's quoted by a Google Chat
  // user in a space. Google Chat users can quote a message to reply to it.
  QuotedMessageMetadata quoted_message_metadata = 39
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. GIF images that are attached to the message.
  repeated AttachedGif attached_gifs = 42
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. One or more interactive widgets that appear at the bottom of a
  // message. You can add accessory widgets to messages that contain text,
  // cards, or both text and cards. Not supported for messages that contain
  // dialogs. For details, see [Add interactive widgets at the bottom of a
  // message](https://developers.google.com/workspace/chat/create-messages#add-accessory-widgets).
  //
  // Creating a message with accessory widgets requires [app
  // authentication]
  // (https://developers.google.com/workspace/chat/authenticate-authorize-chat-app).
  repeated AccessoryWidget accessory_widgets = 44
      [(google.api.field_behavior) = OPTIONAL];
}

// A GIF image that's specified by a URL.
message AttachedGif {
  // Output only. The URL that hosts the GIF image.
  string uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Information about a quoted message.
message QuotedMessageMetadata {
  option (google.api.resource) = {
    type: "chat.googleapis.com/QuotedMessageMetadata"
    pattern: "spaces/{space}/messages/{message}/quotedMessageMetadata/{quoted_message_metadata}"
  };

  // Output only. Resource name of the quoted message.
  //
  // Format: `spaces/{space}/messages/{message}`
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "chat.googleapis.com/Message" }
  ];

  // Output only. The timestamp when the quoted message was created or when the
  // quoted message was last updated.
  google.protobuf.Timestamp last_update_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A thread in a Google Chat space. For example usage, see
// [Start or reply to a message
// thread](https://developers.google.com/workspace/chat/create-messages#create-message-thread).
//
// If you specify a thread when creating a message, you can set the
// [`messageReplyOption`](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.messages/create#messagereplyoption)
// field to determine what happens if no matching thread is found.
message Thread {
  option (google.api.resource) = {
    type: "chat.googleapis.com/Thread"
    pattern: "spaces/{space}/threads/{thread}"
  };

  // Identifier. Resource name of the thread.
  //
  // Example: `spaces/{space}/threads/{thread}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Input for creating or updating a thread. Otherwise, output only.
  // ID for the thread. Supports up to 4000 characters.
  //
  // This ID is unique to the Chat app that sets it. For example, if
  // multiple Chat apps create a message using the same thread key,
  // the messages are posted in different threads. To reply in a
  // thread created by a person or another Chat app, specify the thread `name`
  // field instead.
  string thread_key = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Parameters that a Chat app can use to configure how its response is posted.
message ActionResponse {
  // The type of Chat app response.
  enum ResponseType {
    // Default type that's handled as `NEW_MESSAGE`.
    TYPE_UNSPECIFIED = 0;

    // Post as a new message in the topic.
    NEW_MESSAGE = 1;

    // Update the Chat app's message. This is only permitted on a `CARD_CLICKED`
    // event where the message sender type is `BOT`.
    UPDATE_MESSAGE = 2;

    // Update the cards on a user's message. This is only permitted as a
    // response to a `MESSAGE` event with a matched url, or a `CARD_CLICKED`
    // event where the message sender type is `HUMAN`. Text is ignored.
    UPDATE_USER_MESSAGE_CARDS = 6;

    // Privately ask the user for additional authentication or configuration.
    REQUEST_CONFIG = 3;

    // Presents a
    // [dialog](https://developers.google.com/workspace/chat/dialogs).
    DIALOG = 4;

    // Widget text autocomplete options query.
    UPDATE_WIDGET = 7;
  }

  // List of widget autocomplete results.
  message SelectionItems {
    // An array of the SelectionItem objects.
    repeated google.apps.card.v1.SelectionInput.SelectionItem items = 1;
  }

  // For `selectionInput` widgets, returns autocomplete suggestions for a
  // multiselect menu.
  message UpdatedWidget {
    // The widget updated in response to a user action.
    oneof updated_widget {
      // List of widget autocomplete results
      SelectionItems suggestions = 1;
    }

    // The ID of the updated widget. The ID must match the one for the
    // widget that triggered the update request.
    string widget = 2;
  }

  // Input only. The type of Chat app response.
  ResponseType type = 1 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. URL for users to authenticate or configure. (Only for
  // `REQUEST_CONFIG` response types.)
  string url = 2 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. A response to an interaction event related to a
  // [dialog](https://developers.google.com/workspace/chat/dialogs). Must be
  // accompanied by `ResponseType.Dialog`.
  DialogAction dialog_action = 3 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The response of the updated widget.
  UpdatedWidget updated_widget = 4 [(google.api.field_behavior) = INPUT_ONLY];
}

// One or more interactive widgets that appear at the bottom of a message. For
// details, see [Add interactive widgets at the bottom of a
// message](https://developers.google.com/workspace/chat/create-messages#add-accessory-widgets).
message AccessoryWidget {
  // The type of action.
  oneof action {
    // A list of buttons.
    google.apps.card.v1.ButtonList button_list = 1;
  }
}

// Request to get a message.
message GetMessageRequest {
  // Required. Resource name of the message.
  //
  // Format: `spaces/{space}/messages/{message}`
  //
  // If you've set a custom ID for your message, you can use the value from the
  // `clientAssignedMessageId` field for `{message}`. For details, see [Name a
  // message]
  // (https://developers.google.com/workspace/chat/create-messages#name_a_created_message).
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "chat.googleapis.com/Message" }
  ];
}

// Request to delete a message.
message DeleteMessageRequest {
  // Required. Resource name of the message.
  //
  // Format: `spaces/{space}/messages/{message}`
  //
  // If you've set a custom ID for your message, you can use the value from the
  // `clientAssignedMessageId` field for `{message}`. For details, see [Name a
  // message]
  // (https://developers.google.com/workspace/chat/create-messages#name_a_created_message).
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "chat.googleapis.com/Message" }
  ];

  // Optional. When `true`, deleting a message also deletes its threaded
  // replies. When `false`, if a message has threaded replies, deletion fails.
  //
  // Only applies when [authenticating as a
  // user](https://developers.google.com/workspace/chat/authenticate-authorize-chat-user).
  // Has no effect when [authenticating as a Chat app]
  // (https://developers.google.com/workspace/chat/authenticate-authorize-chat-app).
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request to update a message.
message UpdateMessageRequest {
  // Required. Message with fields updated.
  Message message = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The field paths to update. Separate multiple values with commas
  // or use `*` to update all field paths.
  //
  // Currently supported field paths:
  //
  // - `text`
  //
  // - `attachment`
  //
  // - `cards` (Requires [app
  // authentication](/chat/api/guides/auth/service-accounts).)
  //
  // - `cards_v2`  (Requires [app
  // authentication](/chat/api/guides/auth/service-accounts).)
  //
  // - `accessory_widgets`  (Requires [app
  // authentication](/chat/api/guides/auth/service-accounts).)
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If `true` and the message isn't found, a new message is created
  // and `updateMask` is ignored. The specified message ID must be
  // [client-assigned](https://developers.google.com/workspace/chat/create-messages#name_a_created_message)
  // or the request fails.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Creates a message.
message CreateMessageRequest {
  // Specifies how to reply to a message.
  // More states might be added in the future.
  enum MessageReplyOption {
    // Default. Starts a new thread. Using this option ignores any [thread
    // ID][google.chat.v1.Thread.name] or
    // [`thread_key`][google.chat.v1.Thread.thread_key] that's included.
    MESSAGE_REPLY_OPTION_UNSPECIFIED = 0;

    // Creates the message as a reply to the thread specified by [thread
    // ID][google.chat.v1.Thread.name] or
    // [`thread_key`][google.chat.v1.Thread.thread_key]. If it fails, the
    // message starts a new thread instead.
    REPLY_MESSAGE_FALLBACK_TO_NEW_THREAD = 1;

    // Creates the message as a reply to the thread specified by [thread
    // ID][google.chat.v1.Thread.name] or
    // [`thread_key`][google.chat.v1.Thread.thread_key]. If a new `thread_key`
    // is used, a new thread is created. If the message creation fails, a
    // `NOT_FOUND` error is returned instead.
    REPLY_MESSAGE_OR_FAIL = 2;
  }

  // Required. The resource name of the space in which to create a message.
  //
  // Format: `spaces/{space}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "chat.googleapis.com/Message"
    }
  ];

  // Required. Message body.
  Message message = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. Deprecated: Use
  // [thread.thread_key][google.chat.v1.Thread.thread_key] instead. ID for the
  // thread. Supports up to 4000 characters. To start or add to a thread, create
  // a message and specify a `threadKey` or the
  // [thread.name][google.chat.v1.Thread.name]. For example usage, see [Start or
  // reply to a message
  // thread](https://developers.google.com/workspace/chat/create-messages#create-message-thread).
  string thread_key = 6
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Optional. A unique request ID for this message. Specifying an existing
  // request ID returns the message created with that ID instead of creating a
  // new message.
  string request_id = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether a message starts a thread or replies to one.
  // Only supported in named spaces.
  //
  // When [responding to user
  // interactions](https://developers.google.com/workspace/chat/receive-respond-interactions),
  // this field is ignored. For interactions within a thread, the reply is
  // created in the same thread. Otherwise, the reply is created as a new
  // thread.
  MessageReplyOption message_reply_option = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A custom ID for a message. Lets Chat apps get, update, or delete
  // a message without needing to store the system-assigned ID in the message's
  // resource name (represented in the message `name` field).
  //
  // The value for this field must meet the following requirements:
  //
  // * Begins with `client-`. For example, `client-custom-name` is a valid
  //   custom ID, but `custom-name` is not.
  // * Contains up to 63 characters and only lowercase letters, numbers, and
  //   hyphens.
  // * Is unique within a space. A Chat app can't use the same custom ID for
  // different messages.
  //
  // For details, see [Name a
  // message](https://developers.google.com/workspace/chat/create-messages#name_a_created_message).
  string message_id = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Lists messages in the specified space, that the user is a member of.
message ListMessagesRequest {
  // Required. The resource name of the space to list messages from.
  //
  // Format: `spaces/{space}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "chat.googleapis.com/Message"
    }
  ];

  // Optional. The maximum number of messages returned. The service might return
  // fewer messages than this value.
  //
  // If unspecified, at most 25 are returned.
  //
  // The maximum value is 1000. If you use a value more than 1000, it's
  // automatically changed to 1000.
  //
  // Negative values return an `INVALID_ARGUMENT` error.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token received from a previous list messages call. Provide
  // this parameter to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided should match the call that
  // provided the page token. Passing different values to the other parameters
  // might lead to unexpected results.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A query filter.
  //
  // You can filter messages by date (`create_time`) and thread (`thread.name`).
  //
  // To filter messages by the date they were created, specify the `create_time`
  // with a timestamp in [RFC-3339](https://www.rfc-editor.org/rfc/rfc3339)
  // format and double quotation marks. For example,
  // `"2023-04-21T11:30:00-04:00"`. You can use the greater than operator `>` to
  // list messages that were created after a timestamp, or the less than
  // operator `<` to list messages that were created before a timestamp. To
  // filter messages within a time interval, use the `AND` operator between two
  // timestamps.
  //
  // To filter by thread, specify the `thread.name`, formatted as
  // `spaces/{space}/threads/{thread}`. You can only specify one
  // `thread.name` per query.
  //
  // To filter by both thread and date, use the `AND` operator in your query.
  //
  // For example, the following queries are valid:
  //
  // ```
  // create_time > "2012-04-21T11:30:00-04:00"
  //
  // create_time > "2012-04-21T11:30:00-04:00" AND
  //   thread.name = spaces/AAAAAAAAAAA/threads/123
  //
  // create_time > "2012-04-21T11:30:00+00:00" AND
  //
  // create_time < "2013-01-01T00:00:00+00:00" AND
  //   thread.name = spaces/AAAAAAAAAAA/threads/123
  //
  // thread.name = spaces/AAAAAAAAAAA/threads/123
  // ```
  //
  // Invalid queries are rejected by the server with an `INVALID_ARGUMENT`
  // error.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. How the list of messages is ordered. Specify a value to order by
  // an ordering operation. Valid ordering operation values are as follows:
  //
  // - `ASC` for ascending.
  //
  // - `DESC` for descending.
  //
  // The default ordering is `create_time ASC`.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether to include deleted messages. Deleted messages include
  // deleted time and metadata about their deletion, but message content is
  // unavailable.
  bool show_deleted = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for listing messages.
message ListMessagesResponse {
  // List of messages.
  repeated Message messages = 1;

  // You can send a token as `pageToken` to retrieve the next page of
  // results. If empty, there are no subsequent pages.
  string next_page_token = 2;
}

// Contains a
// [dialog](https://developers.google.com/workspace/chat/dialogs) and request
// status code.
message DialogAction {
  // Action to perform.
  oneof action {
    // Input only.
    // [Dialog](https://developers.google.com/workspace/chat/dialogs) for the
    // request.
    Dialog dialog = 1 [(google.api.field_behavior) = INPUT_ONLY];
  }

  // Input only. Status for a request to either invoke or submit a
  // [dialog](https://developers.google.com/workspace/chat/dialogs). Displays
  // a status and message to users, if necessary.
  // For example, in case of an error or success.
  ActionStatus action_status = 2 [(google.api.field_behavior) = INPUT_ONLY];
}

// Wrapper around the card body of the dialog.
message Dialog {
  // Input only. Body of the dialog, which is rendered in a modal.
  // Google Chat apps don't support the following card entities:
  // `DateTimePicker`, `OnChangeAction`.
  google.apps.card.v1.Card body = 1 [(google.api.field_behavior) = INPUT_ONLY];
}

// A
// [card](https://developers.google.com/workspace/chat/api/reference/rest/v1/cards)
// in a Google Chat message.
//
// Only Chat apps can create cards. If your Chat app [authenticates as a
// user](https://developers.google.com/workspace/chat/authenticate-authorize-chat-user),
// the message can't contain cards.
//
// [Card builder](https://addons.gsuite.google.com/uikit/builder)
message CardWithId {
  // Required if the message contains multiple cards. A unique identifier for
  // a card in a message.
  string card_id = 1;

  // A card. Maximum size is 32 KB.
  google.apps.card.v1.Card card = 2;
}
