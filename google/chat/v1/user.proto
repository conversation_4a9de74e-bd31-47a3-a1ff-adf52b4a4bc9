// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "UserProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// A user in Google Chat.
// When returned as an output from a request, if your Chat app [authenticates as
// a
// user](https://developers.google.com/workspace/chat/authenticate-authorize-chat-user),
// the output for a `User` resource only populates the user's `name` and `type`.
message User {
  enum Type {
    // Default value for the enum. DO NOT USE.
    TYPE_UNSPECIFIED = 0;

    // Human user.
    HUMAN = 1;

    // Chat app user.
    BOT = 2;
  }

  // Resource name for a Google Chat [user][google.chat.v1.User].
  //
  // Format: `users/{user}`. `users/app` can be used as an alias for the calling
  // app [bot][google.chat.v1.User.Type.BOT] user.
  //
  // For [human users][google.chat.v1.User.Type.HUMAN], `{user}` is the same
  // user identifier as:
  //
  // - the `id` for the
  // [Person](https://developers.google.com/people/api/rest/v1/people) in the
  // People API. For example, `users/123456789` in Chat API represents the same
  // person as the `123456789` Person profile ID in People API.
  //
  // - the `id` for a
  // [user](https://developers.google.com/admin-sdk/directory/reference/rest/v1/users)
  // in the Admin SDK Directory API.
  //
  // - the user's email address can be used as an alias for `{user}` in API
  // requests. For example, if the People API Person profile ID for
  // `<EMAIL>` is `123456789`, you can use `users/<EMAIL>` as
  // an alias to reference `users/123456789`. Only the canonical resource name
  // (for example `users/123456789`) will be returned from the API.
  string name = 1;

  // Output only. The user's display name.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Unique identifier of the user's Google Workspace domain.
  string domain_id = 6;

  // User type.
  Type type = 5;

  // Output only. When `true`, the user is deleted or their profile is not
  // visible.
  bool is_anonymous = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
