// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "SpaceReadStateProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// A user's read state within a space, used to identify read and unread
// messages.
message SpaceReadState {
  option (google.api.resource) = {
    type: "chat.googleapis.com/SpaceReadState"
    pattern: "users/{user}/spaces/{space}/spaceReadState"
    singular: "spaceReadState"
  };

  // Resource name of the space read state.
  //
  // Format: `users/{user}/spaces/{space}/spaceReadState`
  string name = 1;

  // Optional. The time when the user's space read state was updated. Usually
  // this corresponds with either the timestamp of the last read message, or a
  // timestamp specified by the user to mark the last read position in a space.
  google.protobuf.Timestamp last_read_time = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetSpaceReadState API.
message GetSpaceReadStateRequest {
  // Required. Resource name of the space read state to retrieve.
  //
  // Only supports getting read state for the calling user.
  //
  // To refer to the calling user, set one of the following:
  //
  // - The `me` alias. For example, `users/me/spaces/{space}/spaceReadState`.
  //
  // - Their Workspace email address. For example,
  // `users/<EMAIL>/spaces/{space}/spaceReadState`.
  //
  // - Their user id. For example,
  // `users/123456789/spaces/{space}/spaceReadState`.
  //
  // Format: users/{user}/spaces/{space}/spaceReadState
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "chat.googleapis.com/SpaceReadState"
    }
  ];
}

// Request message for UpdateSpaceReadState API.
message UpdateSpaceReadStateRequest {
  // Required. The space read state and fields to update.
  //
  // Only supports updating read state for the calling user.
  //
  // To refer to the calling user, set one of the following:
  //
  // - The `me` alias. For example, `users/me/spaces/{space}/spaceReadState`.
  //
  // - Their Workspace email address. For example,
  // `users/<EMAIL>/spaces/{space}/spaceReadState`.
  //
  // - Their user id. For example,
  // `users/123456789/spaces/{space}/spaceReadState`.
  //
  // Format: users/{user}/spaces/{space}/spaceReadState
  SpaceReadState space_read_state = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The field paths to update. Currently supported field paths:
  //
  // - `last_read_time`
  //
  // When the `last_read_time` is before the latest message create time, the
  // space appears as unread in the UI.
  //
  // To mark the space as read, set `last_read_time` to any value later (larger)
  // than the latest message create time. The `last_read_time` is coerced to
  // match the latest message create time. Note that the space read state only
  // affects the read state of messages that are visible in the space's
  // top-level conversation. Replies in threads are unaffected by this
  // timestamp, and instead rely on the thread read state.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}
