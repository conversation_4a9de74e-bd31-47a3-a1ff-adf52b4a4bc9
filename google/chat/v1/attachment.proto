// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "AttachmentProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// An attachment in Google Chat.
message Attachment {
  option (google.api.resource) = {
    type: "chat.googleapis.com/Attachment"
    pattern: "spaces/{space}/messages/{message}/attachments/{attachment}"
  };

  // The source of the attachment.
  enum Source {
    // Reserved.
    SOURCE_UNSPECIFIED = 0;

    // The file is a Google Drive file.
    DRIVE_FILE = 1;

    // The file is uploaded to Chat.
    UPLOADED_CONTENT = 2;
  }

  // Optional. Resource name of the attachment, in the form
  // `spaces/{space}/messages/{message}/attachments/{attachment}`.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The original file name for the content, not the full path.
  string content_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The content type (MIME type) of the file.
  string content_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The data reference to the attachment.
  oneof data_ref {
    // Optional. A reference to the attachment data. This field is used to
    // create or update messages with attachments, or with the media API to
    // download the attachment data.
    AttachmentDataRef attachment_data_ref = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Output only. A reference to the Google Drive attachment. This field is
    // used with the Google Drive API.
    DriveDataRef drive_data_ref = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The thumbnail URL which should be used to preview the
  // attachment to a human user. Chat apps shouldn't use this URL to download
  // attachment content.
  string thumbnail_uri = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The download URL which should be used to allow a human user to
  // download the attachment. Chat apps shouldn't use this URL to download
  // attachment content.
  string download_uri = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The source of the attachment.
  Source source = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A reference to the data of a drive attachment.
message DriveDataRef {
  // The ID for the drive file. Use with the Drive API.
  string drive_file_id = 2;
}

// A reference to the attachment data.
message AttachmentDataRef {
  // Optional. The resource name of the attachment data. This field is used with
  // the media API to download the attachment data.
  string resource_name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Opaque token containing a reference to an uploaded attachment.
  // Treated by clients as an opaque string and used to create or update Chat
  // messages with attachments.
  string attachment_upload_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request to get an attachment.
message GetAttachmentRequest {
  // Required. Resource name of the attachment, in the form
  // `spaces/{space}/messages/{message}/attachments/{attachment}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "chat.googleapis.com/Attachment" }
  ];
}

// Request to upload an attachment.
message UploadAttachmentRequest {
  // Required. Resource name of the Chat space in which the attachment is
  // uploaded. Format "spaces/{space}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "chat.googleapis.com/Message"
    }
  ];

  // Required. The filename of the attachment, including the file extension.
  string filename = 4 [(google.api.field_behavior) = REQUIRED];
}

// Response of uploading an attachment.
message UploadAttachmentResponse {
  // Reference to the uploaded attachment.
  AttachmentDataRef attachment_data_ref = 1;
}
