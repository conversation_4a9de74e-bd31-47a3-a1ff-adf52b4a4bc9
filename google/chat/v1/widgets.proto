// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "WidgetsProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// A widget is a UI element that presents text and images.
message WidgetMarkup {
  // A paragraph of text. Formatted text supported. For more information
  // about formatting text, see
  // [Formatting text in Google Chat
  // apps](https://developers.google.com/workspace/chat/format-messages#card-formatting)
  // and
  // [Formatting
  // text in Google Workspace
  // Add-ons](https://developers.google.com/apps-script/add-ons/concepts/widgets#text_formatting).
  message TextParagraph {
    string text = 1;
  }

  // The set of supported icons.
  enum Icon {
    ICON_UNSPECIFIED = 0;

    AIRPLANE = 1;

    BOOKMARK = 26;

    BUS = 25;

    CAR = 9;

    CLOCK = 2;

    CONFIRMATION_NUMBER_ICON = 12;

    DOLLAR = 14;

    DESCRIPTION = 27;

    EMAIL = 10;

    EVENT_PERFORMER = 20;

    EVENT_SEAT = 21;

    FLIGHT_ARRIVAL = 16;

    FLIGHT_DEPARTURE = 15;

    HOTEL = 6;

    HOTEL_ROOM_TYPE = 17;

    INVITE = 19;

    MAP_PIN = 3;

    MEMBERSHIP = 24;

    MULTIPLE_PEOPLE = 18;

    OFFER = 30;

    PERSON = 11;

    PHONE = 13;

    RESTAURANT_ICON = 7;

    SHOPPING_CART = 8;

    STAR = 5;

    STORE = 22;

    TICKET = 4;

    TRAIN = 23;

    VIDEO_CAMERA = 28;

    VIDEO_PLAY = 29;
  }

  // A button. Can be a text button or an image button.
  message Button {
    oneof type {
      // A button with text and `onclick` action.
      TextButton text_button = 1;

      // A button with image and `onclick` action.
      ImageButton image_button = 2;
    }
  }

  // A button with text and `onclick` action.
  message TextButton {
    // The text of the button.
    string text = 1;

    // The `onclick` action of the button.
    OnClick on_click = 2;
  }

  // A UI element contains a key (label) and a value (content). This
  // element can also contain some actions such as `onclick` button.
  message KeyValue {
    // At least one of icons, `top_label` and `bottom_label` must be defined.
    oneof icons {
      // An enum value that's replaced by the Chat API with the
      // corresponding icon image.
      Icon icon = 1;

      // The icon specified by a URL.
      string icon_url = 2;
    }

    // The text of the top label. Formatted text supported. For more information
    // about formatting text, see
    // [Formatting text in Google Chat
    // apps](https://developers.google.com/workspace/chat/format-messages#card-formatting)
    // and
    // [Formatting
    // text in Google Workspace
    // Add-ons](https://developers.google.com/apps-script/add-ons/concepts/widgets#text_formatting).
    string top_label = 3;

    // The text of the content. Formatted text supported and always required.
    // For more information
    // about formatting text, see
    // [Formatting text in Google Chat
    // apps](https://developers.google.com/workspace/chat/format-messages#card-formatting)
    // and
    // [Formatting
    // text in Google Workspace
    // Add-ons](https://developers.google.com/apps-script/add-ons/concepts/widgets#text_formatting).
    string content = 4;

    // If the content should be multiline.
    bool content_multiline = 9;

    // The text of the bottom label. Formatted text supported. For more
    // information about formatting text, see [Formatting text in Google Chat
    // apps](https://developers.google.com/workspace/chat/format-messages#card-formatting)
    // and
    // [Formatting
    // text in Google Workspace
    // Add-ons](https://developers.google.com/apps-script/add-ons/concepts/widgets#text_formatting).
    string bottom_label = 5;

    // The `onclick` action. Only the top label, bottom label, and content
    // region are clickable.
    OnClick on_click = 6;

    // A control widget. You can set either `button` or `switch_widget`,
    // but not both.
    oneof control {
      // A button that can be clicked to trigger an action.
      Button button = 7;
    }
  }

  // An image that's specified by a URL and can have an `onclick` action.
  message Image {
    // The URL of the image.
    string image_url = 1;

    // The `onclick` action.
    OnClick on_click = 2;

    // The aspect ratio of this image (width and height). This field lets you
    // reserve the right height for the image while waiting for it to load.
    // It's not meant to override the built-in aspect ratio of the image.
    // If unset, the server fills it by prefetching the image.
    double aspect_ratio = 3;
  }

  // An image button with an `onclick` action.
  message ImageButton {
    // The icon can be specified by an `Icon` `enum` or a URL.
    oneof icons {
      // The icon specified by an `enum` that indices to an icon provided by
      // Chat API.
      Icon icon = 1;

      // The icon specified by a URL.
      string icon_url = 3;
    }

    // The `onclick` action.
    OnClick on_click = 2;

    // The name of this `image_button` that's used for accessibility.
    // Default value is provided if this name isn't specified.
    string name = 4;
  }

  // An `onclick` action (for example, open a link).
  message OnClick {
    oneof data {
      // A form action is triggered by this `onclick` action if specified.
      FormAction action = 1;

      // This `onclick` action triggers an open link action if specified.
      OpenLink open_link = 2;
    }
  }

  // A link that opens a new window.
  message OpenLink {
    // The URL to open.
    string url = 1;
  }

  // A form action describes the behavior when the form is submitted.
  // For example, you can invoke Apps Script to handle the form.
  message FormAction {
    // List of string parameters to supply when the action method is invoked.
    // For example, consider three snooze buttons: snooze now, snooze one day,
    // snooze next week. You might use `action method = snooze()`, passing the
    // snooze type and snooze time in the list of string parameters.
    message ActionParameter {
      // The name of the parameter for the action script.
      string key = 1;

      // The value of the parameter.
      string value = 2;
    }

    // The method name is used to identify which part of the form triggered the
    // form submission. This information is echoed back to the Chat app as part
    // of the card click event. You can use the same method name for several
    // elements that trigger a common behavior.
    string action_method_name = 1;

    // List of action parameters.
    repeated ActionParameter parameters = 2;
  }

  // A `WidgetMarkup` can only have one of the following items. You can use
  // multiple `WidgetMarkup` fields to display more items.
  oneof data {
    // Display a text paragraph in this widget.
    TextParagraph text_paragraph = 1;

    // Display an image in this widget.
    Image image = 2;

    // Display a key value item in this widget.
    KeyValue key_value = 3;
  }

  // A list of buttons. Buttons is also `oneof data` and only one of these
  // fields should be set.
  repeated Button buttons = 6;
}
