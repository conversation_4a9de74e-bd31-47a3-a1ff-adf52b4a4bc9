// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/resource.proto";
import "google/chat/v1/attachment.proto";
import "google/chat/v1/user.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "AnnotationProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// Output only. Annotations associated with the plain-text body of the message.
// To add basic formatting to a text message, see
// [Format text
// messages](https://developers.google.com/workspace/chat/format-messages).
//
// Example plain-text message body:
// ```
// Hello @FooBot how are you!"
// ```
//
// The corresponding annotations metadata:
// ```
// "annotations":[{
//   "type":"USER_MENTION",
//   "startIndex":6,
//   "length":7,
//   "userMention": {
//     "user": {
//       "name":"users/{user}",
//       "displayName":"FooBot",
//       "avatarUrl":"https://goo.gl/aeDtrS",
//       "type":"BOT"
//     },
//     "type":"MENTION"
//    }
// }]
// ```
message Annotation {
  // The type of this annotation.
  AnnotationType type = 1;

  // Start index (0-based, inclusive) in the plain-text message body this
  // annotation corresponds to.
  optional int32 start_index = 2;

  // Length of the substring in the plain-text message body this annotation
  // corresponds to.
  int32 length = 3;

  // Additional metadata about the annotation.
  oneof metadata {
    // The metadata of user mention.
    UserMentionMetadata user_mention = 4;

    // The metadata for a slash command.
    SlashCommandMetadata slash_command = 5;

    // The metadata for a rich link.
    RichLinkMetadata rich_link_metadata = 6;
  }
}

// Annotation metadata for user mentions (@).
message UserMentionMetadata {
  enum Type {
    // Default value for the enum. Don't use.
    TYPE_UNSPECIFIED = 0;

    // Add user to space.
    ADD = 1;

    // Mention user in space.
    MENTION = 2;
  }

  // The user mentioned.
  User user = 1;

  // The type of user mention.
  Type type = 2;
}

// Annotation metadata for slash commands (/).
message SlashCommandMetadata {
  enum Type {
    // Default value for the enum. Don't use.
    TYPE_UNSPECIFIED = 0;

    // Add Chat app to space.
    ADD = 1;

    // Invoke slash command in space.
    INVOKE = 2;
  }

  // The Chat app whose command was invoked.
  User bot = 1;

  // The type of slash command.
  Type type = 2;

  // The name of the invoked slash command.
  string command_name = 3;

  // The command ID of the invoked slash command.
  int64 command_id = 4;

  // Indicates whether the slash command is for a dialog.
  bool triggers_dialog = 5;
}

// A rich link to a resource.
message RichLinkMetadata {
  // The rich link type. More types might be added in the future.
  enum RichLinkType {
    // Default value for the enum. Don't use.
    RICH_LINK_TYPE_UNSPECIFIED = 0;

    // A Google Drive rich link type.
    DRIVE_FILE = 1;

    // A Chat space rich link type. For example, a space smart chip.
    CHAT_SPACE = 2;
  }

  // The URI of this link.
  string uri = 1;

  // The rich link type.
  RichLinkType rich_link_type = 2;

  // Data for the linked resource.
  oneof data {
    // Data for a drive link.
    DriveLinkData drive_link_data = 3;

    // Data for a chat space link.
    ChatSpaceLinkData chat_space_link_data = 4;
  }
}

// Data for Google Drive links.
message DriveLinkData {
  // A
  // [DriveDataRef](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.messages.attachments#drivedataref)
  // which references a Google Drive file.
  DriveDataRef drive_data_ref = 1;

  // The mime type of the linked Google Drive resource.
  string mime_type = 2;
}

// Data for Chat space links.
message ChatSpaceLinkData {
  // The space of the linked Chat space resource.
  //
  // Format: `spaces/{space}`
  string space = 1
      [(google.api.resource_reference) = { type: "chat.googleapis.com/Space" }];

  // The thread of the linked Chat space resource.
  //
  // Format: `spaces/{space}/threads/{thread}`
  string thread = 2 [
    (google.api.resource_reference) = { type: "chat.googleapis.com/Thread" }
  ];

  // The message of the linked Chat space resource.
  //
  // Format: `spaces/{space}/messages/{message}`
  string message = 3 [
    (google.api.resource_reference) = { type: "chat.googleapis.com/Message" }
  ];
}

// Type of the annotation.
enum AnnotationType {
  // Default value for the enum. Don't use.
  ANNOTATION_TYPE_UNSPECIFIED = 0;

  // A user is mentioned.
  USER_MENTION = 1;

  // A slash command is invoked.
  SLASH_COMMAND = 2;

  // A rich link annotation.
  RICH_LINK = 3;
}
