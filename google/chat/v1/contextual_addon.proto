// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/chat/v1/widgets.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "ContextualAddOnProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// The markup for developers to specify the contents of a contextual AddOn.
message ContextualAddOnMarkup {
  // A card is a UI element that can contain UI widgets such as text and
  // images.
  message Card {
    message CardHeader {
      enum ImageStyle {
        IMAGE_STYLE_UNSPECIFIED = 0;

        // Square border.
        IMAGE = 1;

        // Circular border.
        AVATAR = 2;
      }

      // The title must be specified. The header has a fixed height: if both a
      // title and subtitle is specified, each takes up one line. If only the
      // title is specified, it takes up both lines.
      string title = 1;

      // The subtitle of the card header.
      string subtitle = 2;

      // The image's type (for example, square border or circular border).
      ImageStyle image_style = 3;

      // The URL of the image in the card header.
      string image_url = 4;
    }

    // A section contains a collection of widgets that are rendered
    // (vertically) in the order that they are specified. Across all platforms,
    // cards have a narrow fixed width, so
    // there's currently no need for layout properties (for example, float).
    message Section {
      // The header of the section. Formatted text is
      // supported. For more information
      // about formatting text, see
      // [Formatting text in Google Chat
      // apps](https://developers.google.com/workspace/chat/format-messages#card-formatting)
      // and
      // [Formatting
      // text in Google Workspace
      // Add-ons](https://developers.google.com/apps-script/add-ons/concepts/widgets#text_formatting).
      string header = 1;

      // A section must contain at least one widget.
      repeated WidgetMarkup widgets = 2;
    }

    // A card action is
    // the action associated with the card. For an invoice card, a
    // typical action would be: delete invoice, email invoice or open the
    // invoice in browser.
    //
    // Not supported by Google Chat apps.
    message CardAction {
      // The label used to be displayed in the action menu item.
      string action_label = 1;

      // The onclick action for this action item.
      WidgetMarkup.OnClick on_click = 2;
    }

    // The header of the card. A header usually contains a title and an image.
    CardHeader header = 1;

    // Sections are separated by a line divider.
    repeated Section sections = 2;

    // The actions of this card.
    repeated CardAction card_actions = 3;

    // Name of the card.
    string name = 4;
  }
}
