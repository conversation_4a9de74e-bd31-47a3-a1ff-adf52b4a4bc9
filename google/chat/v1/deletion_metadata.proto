// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "DeletionMetadataProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// Information about a deleted message. A message is deleted when `delete_time`
// is set.
message DeletionMetadata {
  // Who deleted the message and how it was deleted. More values may be added in
  // the future.
  enum DeletionType {
    // This value is unused.
    DELETION_TYPE_UNSPECIFIED = 0;

    // User deleted their own message.
    CREATOR = 1;

    // The space owner deleted the message.
    SPACE_OWNER = 2;

    // A Google Workspace admin deleted the message.
    ADMIN = 3;

    // A Chat app deleted its own message when it expired.
    APP_MESSAGE_EXPIRY = 4;

    // A Chat app deleted the message on behalf of the user.
    CREATOR_VIA_APP = 5;

    // A Chat app deleted the message on behalf of the space owner.
    SPACE_OWNER_VIA_APP = 6;
  }

  // Indicates who deleted the message.
  DeletionType deletion_type = 1;
}
