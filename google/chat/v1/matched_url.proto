// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "MatchedUrlProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// A matched URL in a Chat message. Chat apps can preview matched URLs. For more
// information, see [Preview
// links](https://developers.google.com/chat/how-tos/preview-links).
message MatchedUrl {
  // Output only. The URL that was matched.
  string url = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
