{"methodConfig": [{"name": [{"service": "google.chat.v1.ChatService", "method": "CreateMessage"}, {"service": "google.chat.v1.ChatService", "method": "UpdateMessage"}, {"service": "google.chat.v1.ChatService", "method": "GetMessage"}, {"service": "google.chat.v1.ChatService", "method": "ListMessages"}, {"service": "google.chat.v1.ChatService", "method": "DeleteMessage"}, {"service": "google.chat.v1.ChatService", "method": "CreateSpace"}, {"service": "google.chat.v1.ChatService", "method": "SetUpSpace"}, {"service": "google.chat.v1.ChatService", "method": "UpdateSpace"}, {"service": "google.chat.v1.ChatService", "method": "GetSpace"}, {"service": "google.chat.v1.ChatService", "method": "ListSpaces"}, {"service": "google.chat.v1.ChatService", "method": "DeleteSpace"}, {"service": "google.chat.v1.ChatService", "method": "SearchSpaces"}, {"service": "google.chat.v1.ChatService", "method": "CreateMembership"}, {"service": "google.chat.v1.ChatService", "method": "GetMembership"}, {"service": "google.chat.v1.ChatService", "method": "ListMemberships"}, {"service": "google.chat.v1.ChatService", "method": "DeleteMembership"}, {"service": "google.chat.v1.ChatService", "method": "CreateReaction"}, {"service": "google.chat.v1.ChatService", "method": "ListReactions"}, {"service": "google.chat.v1.ChatService", "method": "DeleteReaction"}, {"service": "google.chat.v1.ChatService", "method": "UploadAttachment"}, {"service": "google.chat.v1.ChatService", "method": "FindDirectMessage"}, {"service": "google.chat.v1.ChatService", "method": "CompleteImportSpace"}, {"service": "google.chat.v1.ChatService", "method": "UpdateMembership"}, {"service": "google.chat.v1.ChatService", "method": "UpdateSpaceReadState"}, {"service": "google.chat.v1.ChatService", "method": "GetSpaceReadState"}, {"service": "google.chat.v1.ChatService", "method": "GetThreadReadState"}, {"service": "google.chat.v1.ChatService", "method": "GetAttachment"}, {"service": "google.chat.v1.ChatService", "method": "GetSpaceEvent"}, {"service": "google.chat.v1.ChatService", "method": "ListSpaceEvents"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}