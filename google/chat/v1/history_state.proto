// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "HistoryStateProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// The history state for messages and spaces. Specifies how long messages and
// conversation threads are kept after creation.
enum HistoryState {
  // Default value. Do not use.
  HISTORY_STATE_UNSPECIFIED = 0;

  // History off. [Messages and threads are kept for 24
  // hours](https://support.google.com/chat/answer/7664687).
  HISTORY_OFF = 1;

  // History on. The organization's [Vault retention
  // rules](https://support.google.com/vault/answer/7657597) specify for
  // how long messages and threads are kept.
  HISTORY_ON = 2;
}
