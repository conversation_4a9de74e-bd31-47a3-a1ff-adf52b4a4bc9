type: google.api.Service
config_version: 3
name: chat.googleapis.com
title: Google Chat API

apis:
- name: google.chat.v1.ChatService

documentation:
  summary: |-
    The Google Chat API lets you build Chat apps to integrate your services
    with Google Chat and manage Chat resources such as spaces, members, and
    messages.

authentication:
  rules:
  - selector: google.chat.v1.ChatService.CompleteImportSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.import
  - selector: google.chat.v1.ChatService.CreateMembership
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.memberships,
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.app
  - selector: google.chat.v1.ChatService.CreateMessage
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.create
  - selector: google.chat.v1.ChatService.CreateReaction
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.create
  - selector: google.chat.v1.ChatService.CreateSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.create
  - selector: google.chat.v1.ChatService.DeleteMembership
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.memberships,
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.app
  - selector: google.chat.v1.ChatService.DeleteMessage
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.messages
  - selector: google.chat.v1.ChatService.DeleteReaction
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions
  - selector: google.chat.v1.ChatService.DeleteSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.delete,
        https://www.googleapis.com/auth/chat.delete,
        https://www.googleapis.com/auth/chat.import
  - selector: google.chat.v1.ChatService.FindDirectMessage
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly
  - selector: google.chat.v1.ChatService.GetAttachment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot
  - selector: google.chat.v1.ChatService.GetMembership
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.memberships,
        https://www.googleapis.com/auth/chat.admin.memberships.readonly,
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly
  - selector: google.chat.v1.ChatService.GetMessage
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.readonly
  - selector: google.chat.v1.ChatService.GetSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.spaces,
        https://www.googleapis.com/auth/chat.admin.spaces.readonly,
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly
  - selector: google.chat.v1.ChatService.GetSpaceEvent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.readonly,
        https://www.googleapis.com/auth/chat.messages.readonly,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly
  - selector: google.chat.v1.ChatService.GetSpaceReadState
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.users.readstate,
        https://www.googleapis.com/auth/chat.users.readstate.readonly
  - selector: google.chat.v1.ChatService.GetThreadReadState
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.users.readstate,
        https://www.googleapis.com/auth/chat.users.readstate.readonly
  - selector: google.chat.v1.ChatService.ListMemberships
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.memberships,
        https://www.googleapis.com/auth/chat.admin.memberships.readonly,
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly
  - selector: google.chat.v1.ChatService.ListMessages
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.readonly
  - selector: google.chat.v1.ChatService.ListReactions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.readonly,
        https://www.googleapis.com/auth/chat.messages.readonly
  - selector: google.chat.v1.ChatService.ListSpaceEvents
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.readonly,
        https://www.googleapis.com/auth/chat.messages.readonly,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly
  - selector: google.chat.v1.ChatService.ListSpaces
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly
  - selector: google.chat.v1.ChatService.SearchSpaces
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.spaces,
        https://www.googleapis.com/auth/chat.admin.spaces.readonly
  - selector: google.chat.v1.ChatService.SetUpSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.create
  - selector: google.chat.v1.ChatService.UpdateMembership
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.memberships,
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.memberships
  - selector: google.chat.v1.ChatService.UpdateMessage
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.messages
  - selector: google.chat.v1.ChatService.UpdateSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.admin.spaces,
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.spaces
  - selector: google.chat.v1.ChatService.UpdateSpaceReadState
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.users.readstate
  - selector: google.chat.v1.ChatService.UploadAttachment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.import,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.create

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=350158&template=1047346
  documentation_uri: https://developers.google.com/chat/concepts
  api_short_name: chat
  github_label: 'api: chat'
  doc_tag_prefix: chat
  organization: CLOUD
  library_settings:
  - version: google.chat.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://developers.google.com/chat/api/reference/rest
