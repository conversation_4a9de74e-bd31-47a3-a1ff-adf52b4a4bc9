// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/chat/v1/user.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "ReactionProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// A reaction to a message.
message Reaction {
  option (google.api.resource) = {
    type: "chat.googleapis.com/Reaction"
    pattern: "spaces/{space}/messages/{message}/reactions/{reaction}"
  };

  // Identifier. The resource name of the reaction.
  //
  // Format: `spaces/{space}/messages/{message}/reactions/{reaction}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The user who created the reaction.
  User user = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The emoji used in the reaction.
  Emoji emoji = 3 [(google.api.field_behavior) = REQUIRED];
}

// An emoji that is used as a reaction to a message.
message Emoji {
  // Required. The content of the emoji.
  oneof content {
    // Optional. A basic emoji represented by a unicode string.
    string unicode = 1 [(google.api.field_behavior) = OPTIONAL];

    // Output only. A custom emoji.
    CustomEmoji custom_emoji = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// Represents a custom emoji.
message CustomEmoji {
  // Output only. Unique key for the custom emoji resource.
  string uid = 1 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
}

// The number of people who reacted to a message with a specific emoji.
message EmojiReactionSummary {
  // Output only. Emoji associated with the reactions.
  Emoji emoji = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total number of reactions using the associated emoji.
  optional int32 reaction_count = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Creates a reaction to a message.
message CreateReactionRequest {
  // Required. The message where the reaction is created.
  //
  // Format: `spaces/{space}/messages/{message}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "chat.googleapis.com/Reaction"
    }
  ];

  // Required. The reaction to create.
  Reaction reaction = 2 [(google.api.field_behavior) = REQUIRED];
}

// Lists reactions to a message.
message ListReactionsRequest {
  // Required. The message users reacted to.
  //
  // Format: `spaces/{space}/messages/{message}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "chat.googleapis.com/Reaction"
    }
  ];

  // Optional. The maximum number of reactions returned. The service can return
  // fewer reactions than this value. If unspecified, the default value is 25.
  // The maximum value is 200; values above 200 are changed to 200.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. (If resuming from a previous query.)
  //
  // A page token received from a previous list reactions call. Provide this
  // to retrieve the subsequent page.
  //
  // When paginating, the filter value should match the call that provided the
  // page token. Passing a different value might lead to unexpected results.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A query filter.
  //
  // You can filter reactions by
  // [emoji](https://developers.google.com/workspace/chat/api/reference/rest/v1/Emoji)
  // (either `emoji.unicode` or `emoji.custom_emoji.uid`) and
  // [user](https://developers.google.com/workspace/chat/api/reference/rest/v1/User)
  // (`user.name`).
  //
  // To filter reactions for multiple emojis or users, join similar fields
  // with the `OR` operator, such as `emoji.unicode = "🙂" OR emoji.unicode =
  // "👍"` and `user.name = "users/AAAAAA" OR user.name = "users/BBBBBB"`.
  //
  // To filter reactions by emoji and user, use the `AND` operator, such as
  // `emoji.unicode = "🙂" AND user.name = "users/AAAAAA"`.
  //
  // If your query uses both `AND` and `OR`, group them with parentheses.
  //
  // For example, the following queries are valid:
  //
  // ```
  // user.name = "users/{user}"
  // emoji.unicode = "🙂"
  // emoji.custom_emoji.uid = "{uid}"
  // emoji.unicode = "🙂" OR emoji.unicode = "👍"
  // emoji.unicode = "🙂" OR emoji.custom_emoji.uid = "{uid}"
  // emoji.unicode = "🙂" AND user.name = "users/{user}"
  // (emoji.unicode = "🙂" OR emoji.custom_emoji.uid = "{uid}")
  // AND user.name = "users/{user}"
  // ```
  //
  // The following queries are invalid:
  //
  // ```
  // emoji.unicode = "🙂" AND emoji.unicode = "👍"
  // emoji.unicode = "🙂" AND emoji.custom_emoji.uid = "{uid}"
  // emoji.unicode = "🙂" OR user.name = "users/{user}"
  // emoji.unicode = "🙂" OR emoji.custom_emoji.uid = "{uid}" OR
  // user.name = "users/{user}"
  // emoji.unicode = "🙂" OR emoji.custom_emoji.uid = "{uid}"
  // AND user.name = "users/{user}"
  // ```
  //
  // Invalid queries are rejected by the server with an `INVALID_ARGUMENT`
  // error.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response to a list reactions request.
message ListReactionsResponse {
  // List of reactions in the requested (or first) page.
  repeated Reaction reactions = 1;

  // Continuation token to retrieve the next page of results. It's empty
  // for the last page of results.
  string next_page_token = 2;
}

// Deletes a reaction to a message.
message DeleteReactionRequest {
  // Required. Name of the reaction to delete.
  //
  // Format: `spaces/{space}/messages/{message}/reactions/{reaction}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "chat.googleapis.com/Reaction" }
  ];
}
