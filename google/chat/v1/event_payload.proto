// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";
import "google/chat/v1/membership.proto";
import "google/chat/v1/message.proto";
import "google/chat/v1/reaction.proto";
import "google/chat/v1/space.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "EventPayloadProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// Event payload for a new membership.
//
// Event type: `google.workspace.chat.membership.v1.created`.
message MembershipCreatedEventData {
  // The new membership.
  Membership membership = 1;
}

// Event payload for a deleted membership.
//
// Event type: `google.workspace.chat.membership.v1.deleted`
message MembershipDeletedEventData {
  // The deleted membership. Only the `name` and `state` fields are populated.
  Membership membership = 1;
}

// Event payload for an updated membership.
//
// Event type: `google.workspace.chat.membership.v1.updated`
message MembershipUpdatedEventData {
  // The updated membership.
  Membership membership = 1;
}

// Event payload for multiple new memberships.
//
// Event type: `google.workspace.chat.membership.v1.batchCreated`
message MembershipBatchCreatedEventData {
  // A list of new memberships.
  repeated MembershipCreatedEventData memberships = 1;
}

// Event payload for multiple updated memberships.
//
// Event type: `google.workspace.chat.membership.v1.batchUpdated`
message MembershipBatchUpdatedEventData {
  // A list of updated memberships.
  repeated MembershipUpdatedEventData memberships = 1;
}

// Event payload for multiple deleted memberships.
//
// Event type: `google.workspace.chat.membership.v1.batchDeleted`
message MembershipBatchDeletedEventData {
  // A list of deleted memberships.
  repeated MembershipDeletedEventData memberships = 1;
}

// Event payload for a new message.
//
// Event type: `google.workspace.chat.message.v1.created`
message MessageCreatedEventData {
  // The new message.
  Message message = 1;
}

// Event payload for an updated message.
//
// Event type: `google.workspace.chat.message.v1.updated`
message MessageUpdatedEventData {
  // The updated message.
  Message message = 1;
}

// Event payload for a deleted message.
//
// Event type: `google.workspace.chat.message.v1.deleted`
message MessageDeletedEventData {
  // The deleted message. Only the `name`, `createTime`, `deleteTime`, and
  // `deletionMetadata` fields are populated.
  Message message = 1;
}

// Event payload for multiple new messages.
//
// Event type: `google.workspace.chat.message.v1.batchCreated`
message MessageBatchCreatedEventData {
  // A list of new messages.
  repeated MessageCreatedEventData messages = 1;
}

// Event payload for multiple updated messages.
//
// Event type: `google.workspace.chat.message.v1.batchUpdated`
message MessageBatchUpdatedEventData {
  // A list of updated messages.
  repeated MessageUpdatedEventData messages = 1;
}

// Event payload for multiple deleted messages.
//
// Event type: `google.workspace.chat.message.v1.batchDeleted`
message MessageBatchDeletedEventData {
  // A list of deleted messages.
  repeated MessageDeletedEventData messages = 1;
}

// Event payload for an updated space.
//
// Event type: `google.workspace.chat.space.v1.updated`
message SpaceUpdatedEventData {
  // The updated space.
  Space space = 1;
}

// Event payload for multiple updates to a space.
//
// Event type: `google.workspace.chat.space.v1.batchUpdated`
message SpaceBatchUpdatedEventData {
  // A list of updated spaces.
  repeated SpaceUpdatedEventData spaces = 1;
}

// Event payload for a new reaction.
//
// Event type: `google.workspace.chat.reaction.v1.created`
message ReactionCreatedEventData {
  // The new reaction.
  Reaction reaction = 1;
}

// Event payload for a deleted reaction.
//
// Type: `google.workspace.chat.reaction.v1.deleted`
message ReactionDeletedEventData {
  // The deleted reaction.
  Reaction reaction = 1;
}

// Event payload for multiple new reactions.
//
// Event type: `google.workspace.chat.reaction.v1.batchCreated`
message ReactionBatchCreatedEventData {
  // A list of new reactions.
  repeated ReactionCreatedEventData reactions = 1;
}

// Event payload for multiple deleted reactions.
//
// Event type: `google.workspace.chat.reaction.v1.batchDeleted`
message ReactionBatchDeletedEventData {
  // A list of deleted reactions.
  repeated ReactionDeletedEventData reactions = 1;
}
