# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "chat_proto",
    srcs = [
        "action_status.proto",
        "annotation.proto",
        "attachment.proto",
        "chat_service.proto",
        "contextual_addon.proto",
        "deletion_metadata.proto",
        "event_payload.proto",
        "group.proto",
        "history_state.proto",
        "matched_url.proto",
        "membership.proto",
        "message.proto",
        "reaction.proto",
        "slash_command.proto",
        "space.proto",
        "space_event.proto",
        "space_read_state.proto",
        "space_setup.proto",
        "thread_read_state.proto",
        "user.proto",
        "widgets.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/apps/card/v1:card_proto",
        "//google/rpc:code_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "chat_proto_with_info",
    deps = [
        ":chat_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "chat_java_proto",
    deps = [":chat_proto"],
)

java_grpc_library(
    name = "chat_java_grpc",
    srcs = [":chat_proto"],
    deps = [":chat_java_proto"],
)

java_gapic_library(
    name = "chat_java_gapic",
    srcs = [":chat_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "chat_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chat_v1.yaml",
    test_deps = [
        ":chat_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":chat_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "chat_java_gapic_test_suite",
    test_classes = [
        "com.google.chat.v1.ChatServiceClientHttpJsonTest",
        "com.google.chat.v1.ChatServiceClientTest",
    ],
    runtime_deps = [":chat_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-chat-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":chat_java_gapic",
        ":chat_java_grpc",
        ":chat_java_proto",
        ":chat_proto",
        "//google/apps/card/v1:card_java_proto", # Added manually
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "chat_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/chat/apiv1/chatpb",
    protos = [":chat_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/apps/card/v1:card_go_proto",
        "//google/rpc:code_go_proto",
    ],
)

go_gapic_library(
    name = "chat_go_gapic",
    srcs = [":chat_proto_with_info"],
    grpc_service_config = "chat_grpc_service_config.json",
    importpath = "cloud.google.com/go/chat/apiv1;chat",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "chat_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":chat_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-chat-v1-go",
    deps = [
        ":chat_go_gapic",
        ":chat_go_gapic_srcjar-metadata.srcjar",
        ":chat_go_gapic_srcjar-snippets.srcjar",
        ":chat_go_gapic_srcjar-test.srcjar",
        ":chat_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "py_test",
)

py_import(
    name="card",
    srcs = [
        "//google/apps/card/v1:card_py_gapic",
    ],
)

py_gapic_library(
    name = "chat_py_gapic",
    srcs = [":chat_proto"],
    grpc_service_config = "chat_grpc_service_config.json",
    opt_args = [
        "proto-plus-deps=google.apps.card.v1",
        "python-gapic-namespace=google.apps",
        "warehouse-package-name=google-apps-chat",
    ],
    rest_numeric_enums = True,
    service_yaml = "chat_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":card",
    ],
)

py_test(
    name = "chat_py_gapic_test",
    srcs = [
        "chat_py_gapic_pytest.py",
        "chat_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":chat_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "chat-v1-py",
    deps = [
        ":chat_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "chat_php_proto",
    deps = [":chat_proto"],
)

php_gapic_library(
    name = "chat_php_gapic",
    srcs = [":chat_proto_with_info"],
    grpc_service_config = "chat_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "chat_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":chat_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-chat-v1-php",
    deps = [
        ":chat_php_gapic",
        ":chat_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "chat_nodejs_gapic",
    package_name = "@google-apps/chat",
    src = ":chat_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "chat_grpc_service_config.json",
    package = "google.chat.v1",
    rest_numeric_enums = True,
    service_yaml = "chat_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "chat-v1-nodejs",
    deps = [
        ":chat_nodejs_gapic",
        ":chat_proto",
        "//google/apps/card/v1:card_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "chat_ruby_proto",
    deps = [":chat_proto"],
)

ruby_grpc_library(
    name = "chat_ruby_grpc",
    srcs = [":chat_proto"],
    deps = [":chat_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "chat_ruby_gapic",
    srcs = [":chat_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-extra-dependencies=google-apps-card-v1=>0.0+<2.a",
        "ruby-cloud-gem-name=google-apps-chat-v1",
    ],
    grpc_service_config = "chat_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chat_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":chat_ruby_grpc",
        ":chat_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-chat-v1-ruby",
    deps = [
        ":chat_ruby_gapic",
        ":chat_ruby_grpc",
        ":chat_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "chat_csharp_proto",
    deps = [":chat_proto"],
)

csharp_grpc_library(
    name = "chat_csharp_grpc",
    srcs = [":chat_proto"],
    deps = [":chat_csharp_proto"],
)

csharp_gapic_library(
    name = "chat_csharp_gapic",
    srcs = [":chat_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "chat_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "chat_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":chat_csharp_grpc",
        ":chat_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-chat-v1-csharp",
    deps = [
        ":chat_csharp_gapic",
        ":chat_csharp_grpc",
        ":chat_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "chat_cc_proto",
    deps = [":chat_proto"],
)

cc_grpc_library(
    name = "chat_cc_grpc",
    srcs = [":chat_proto"],
    grpc_only = True,
    deps = [":chat_cc_proto"],
)
