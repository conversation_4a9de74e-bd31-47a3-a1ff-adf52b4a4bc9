// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "ThreadReadStateProto";
option java_package = "com.google.chat.v1";
option objc_class_prefix = "DYNAPIProto";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// A user's read state within a thread, used to identify read and unread
// messages.
message ThreadReadState {
  option (google.api.resource) = {
    type: "chat.googleapis.com/ThreadReadState"
    pattern: "users/{user}/spaces/{space}/threads/{thread}/threadReadState"
    singular: "threadReadState"
  };

  // Resource name of the thread read state.
  //
  // Format: `users/{user}/spaces/{space}/threads/{thread}/threadReadState`
  string name = 1;

  // The time when the user's thread read state was updated. Usually this
  // corresponds with the timestamp of the last read message in a thread.
  google.protobuf.Timestamp last_read_time = 2;
}

// Request message for GetThreadReadStateRequest API.
message GetThreadReadStateRequest {
  // Required. Resource name of the thread read state to retrieve.
  //
  // Only supports getting read state for the calling user.
  //
  // To refer to the calling user, set one of the following:
  //
  // - The `me` alias. For example,
  // `users/me/spaces/{space}/threads/{thread}/threadReadState`.
  //
  // - Their Workspace email address. For example,
  // `users/<EMAIL>/spaces/{space}/threads/{thread}/threadReadState`.
  //
  // - Their user id. For example,
  // `users/123456789/spaces/{space}/threads/{thread}/threadReadState`.
  //
  // Format: users/{user}/spaces/{space}/threads/{thread}/threadReadState
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "chat.googleapis.com/ThreadReadState"
    }
  ];
}
