// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chat.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/chat/v1/event_payload.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Chat.V1";
option go_package = "cloud.google.com/go/chat/apiv1/chatpb;chatpb";
option java_multiple_files = true;
option java_outer_classname = "SpaceEventProto";
option java_package = "com.google.chat.v1";
option php_namespace = "Google\\Apps\\Chat\\V1";
option ruby_package = "Google::Apps::Chat::V1";

// An event that represents a change or activity in a Google Chat space. To
// learn more, see [Work with events from Google
// Chat](https://developers.google.com/workspace/chat/events-overview).
message SpaceEvent {
  option (google.api.resource) = {
    type: "chat.googleapis.com/SpaceEvent"
    pattern: "spaces/{space}/spaceEvents/{space_event}"
  };

  // Resource name of the space event.
  //
  // Format: `spaces/{space}/spaceEvents/{spaceEvent}`
  string name = 1;

  // Time when the event occurred.
  google.protobuf.Timestamp event_time = 3;

  // Type of space event. Each event type has a batch version, which
  // represents multiple instances of the event type that occur in a short
  // period of time. For `spaceEvents.list()` requests, omit batch event types
  // in your query filter. By default, the server returns both event type and
  // its batch version.
  //
  // Supported event types for
  // [messages](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.messages):
  //
  //    * New message: `google.workspace.chat.message.v1.created`
  //    * Updated message: `google.workspace.chat.message.v1.updated`
  //    * Deleted message: `google.workspace.chat.message.v1.deleted`
  //    * Multiple new messages: `google.workspace.chat.message.v1.batchCreated`
  //    * Multiple updated messages:
  //    `google.workspace.chat.message.v1.batchUpdated`
  //    * Multiple deleted messages:
  //    `google.workspace.chat.message.v1.batchDeleted`
  //
  // Supported event types for
  // [memberships](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.members):
  //
  //   * New membership: `google.workspace.chat.membership.v1.created`
  //   * Updated membership: `google.workspace.chat.membership.v1.updated`
  //   * Deleted membership: `google.workspace.chat.membership.v1.deleted`
  //   * Multiple new memberships:
  //   `google.workspace.chat.membership.v1.batchCreated`
  //   * Multiple updated memberships:
  //   `google.workspace.chat.membership.v1.batchUpdated`
  //   * Multiple deleted memberships:
  //   `google.workspace.chat.membership.v1.batchDeleted`
  //
  // Supported event types for
  // [reactions](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.messages.reactions):
  //
  //   * New reaction: `google.workspace.chat.reaction.v1.created`
  //   * Deleted reaction: `google.workspace.chat.reaction.v1.deleted`
  //   * Multiple new reactions:
  //   `google.workspace.chat.reaction.v1.batchCreated`
  //   * Multiple deleted reactions:
  //   `google.workspace.chat.reaction.v1.batchDeleted`
  //
  // Supported event types about the
  // [space](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces):
  //
  //   * Updated space: `google.workspace.chat.space.v1.updated`
  //   * Multiple space updates: `google.workspace.chat.space.v1.batchUpdated`
  string event_type = 6;

  oneof payload {
    // Event payload for a new message.
    //
    // Event type: `google.workspace.chat.message.v1.created`
    MessageCreatedEventData message_created_event_data = 12;

    // Event payload for an updated message.
    //
    // Event type: `google.workspace.chat.message.v1.updated`
    MessageUpdatedEventData message_updated_event_data = 13;

    // Event payload for a deleted message.
    //
    // Event type: `google.workspace.chat.message.v1.deleted`
    MessageDeletedEventData message_deleted_event_data = 14;

    // Event payload for multiple new messages.
    //
    // Event type: `google.workspace.chat.message.v1.batchCreated`
    MessageBatchCreatedEventData message_batch_created_event_data = 26;

    // Event payload for multiple updated messages.
    //
    // Event type: `google.workspace.chat.message.v1.batchUpdated`
    MessageBatchUpdatedEventData message_batch_updated_event_data = 27;

    // Event payload for multiple deleted messages.
    //
    // Event type: `google.workspace.chat.message.v1.batchDeleted`
    MessageBatchDeletedEventData message_batch_deleted_event_data = 28;

    // Event payload for a space update.
    //
    // Event type: `google.workspace.chat.space.v1.updated`
    SpaceUpdatedEventData space_updated_event_data = 15;

    // Event payload for multiple updates to a space.
    //
    // Event type: `google.workspace.chat.space.v1.batchUpdated`
    SpaceBatchUpdatedEventData space_batch_updated_event_data = 29;

    // Event payload for a new membership.
    //
    // Event type: `google.workspace.chat.membership.v1.created`
    MembershipCreatedEventData membership_created_event_data = 17;

    // Event payload for an updated membership.
    //
    // Event type: `google.workspace.chat.membership.v1.updated`
    MembershipUpdatedEventData membership_updated_event_data = 18;

    // Event payload for a deleted membership.
    //
    // Event type: `google.workspace.chat.membership.v1.deleted`
    MembershipDeletedEventData membership_deleted_event_data = 219;

    // Event payload for multiple new memberships.
    //
    // Event type: `google.workspace.chat.membership.v1.batchCreated`
    MembershipBatchCreatedEventData membership_batch_created_event_data = 31;

    // Event payload for multiple updated memberships.
    //
    // Event type: `google.workspace.chat.membership.v1.batchUpdated`
    MembershipBatchUpdatedEventData membership_batch_updated_event_data = 32;

    // Event payload for multiple deleted memberships.
    //
    // Event type: `google.workspace.chat.membership.v1.batchDeleted`
    MembershipBatchDeletedEventData membership_batch_deleted_event_data = 33;

    // Event payload for a new reaction.
    //
    // Event type: `google.workspace.chat.reaction.v1.created`
    ReactionCreatedEventData reaction_created_event_data = 21;

    // Event payload for a deleted reaction.
    //
    // Event type: `google.workspace.chat.reaction.v1.deleted`
    ReactionDeletedEventData reaction_deleted_event_data = 22;

    // Event payload for multiple new reactions.
    //
    // Event type: `google.workspace.chat.reaction.v1.batchCreated`
    ReactionBatchCreatedEventData reaction_batch_created_event_data = 34;

    // Event payload for multiple deleted reactions.
    //
    // Event type: `google.workspace.chat.reaction.v1.batchDeleted`
    ReactionBatchDeletedEventData reaction_batch_deleted_event_data = 35;
  }
}

// Request message for getting a space event.
message GetSpaceEventRequest {
  // Required. The resource name of the space event.
  //
  // Format: `spaces/{space}/spaceEvents/{spaceEvent}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "chat.googleapis.com/SpaceEvent" }
  ];
}

// Request message for listing space events.
message ListSpaceEventsRequest {
  // Required. Resource name of the [Google Chat
  // space](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces)
  // where the events occurred.
  //
  // Format: `spaces/{space}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "chat.googleapis.com/SpaceEvent"
    }
  ];

  // Optional. The maximum number of space events returned. The service might
  // return fewer than this value.
  //
  // Negative values return an `INVALID_ARGUMENT` error.
  int32 page_size = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous list space events call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to list space events must
  // match the call that provided the page token. Passing different values to
  // the other parameters might lead to unexpected results.
  string page_token = 6 [(google.api.field_behavior) = OPTIONAL];

  // Required. A query filter.
  //
  // You must specify at least one event type (`event_type`)
  // using the has `:` operator. To filter by multiple event types, use the `OR`
  // operator. Omit batch event types in your filter. The request automatically
  // returns any related batch events. For example, if you filter by new
  // reactions
  // (`google.workspace.chat.reaction.v1.created`), the server also returns
  // batch new reactions events
  // (`google.workspace.chat.reaction.v1.batchCreated`). For a list of supported
  // event types, see the [`SpaceEvents` reference
  // documentation](https://developers.google.com/workspace/chat/api/reference/rest/v1/spaces.spaceEvents#SpaceEvent.FIELDS.event_type).
  //
  // Optionally, you can also filter by start time (`start_time`) and
  // end time (`end_time`):
  //
  // * `start_time`: Exclusive timestamp from which to start listing space
  // events.
  //  You can list events that occurred up to 28 days ago. If unspecified, lists
  //  space events from the past 28 days.
  // * `end_time`: Inclusive timestamp until which space events are listed.
  //  If unspecified, lists events up to the time of the request.
  //
  // To specify a start or end time, use the equals `=` operator and format in
  // [RFC-3339](https://www.rfc-editor.org/rfc/rfc3339). To filter by both
  // `start_time` and `end_time`, use the `AND` operator.
  //
  // For example, the following queries are valid:
  //
  // ```
  // start_time="2023-08-23T19:20:33+00:00" AND
  // end_time="2023-08-23T19:21:54+00:00"
  // ```
  // ```
  // start_time="2023-08-23T19:20:33+00:00" AND
  // (event_types:"google.workspace.chat.space.v1.updated" OR
  // event_types:"google.workspace.chat.message.v1.created")
  // ```
  //
  // The following queries are invalid:
  //
  // ```
  // start_time="2023-08-23T19:20:33+00:00" OR
  // end_time="2023-08-23T19:21:54+00:00"
  // ```
  // ```
  // event_types:"google.workspace.chat.space.v1.updated" AND
  // event_types:"google.workspace.chat.message.v1.created"
  // ```
  //
  // Invalid queries are rejected by the server with an `INVALID_ARGUMENT`
  // error.
  string filter = 8 [(google.api.field_behavior) = REQUIRED];
}

// Response message for listing space events.
message ListSpaceEventsResponse {
  // Results are returned in chronological order (oldest event first).
  // Note: The `permissionSettings` field is not returned in the Space
  // object for list requests.
  repeated SpaceEvent space_events = 1;

  // Continuation token used to fetch more events.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
