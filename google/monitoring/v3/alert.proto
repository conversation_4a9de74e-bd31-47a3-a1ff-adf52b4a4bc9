// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.v3;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/monitoring/v3/common.proto";
import "google/monitoring/v3/mutation_record.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/wrappers.proto";
import "google/rpc/status.proto";
import "google/type/timeofday.proto";

option csharp_namespace = "Google.Cloud.Monitoring.V3";
option go_package = "cloud.google.com/go/monitoring/apiv3/v2/monitoringpb;monitoringpb";
option java_multiple_files = true;
option java_outer_classname = "AlertProto";
option java_package = "com.google.monitoring.v3";
option php_namespace = "Google\\Cloud\\Monitoring\\V3";
option ruby_package = "Google::Cloud::Monitoring::V3";

// A description of the conditions under which some aspect of your system is
// considered to be "unhealthy" and the ways to notify people or services about
// this state. For an overview of alerting policies, see
// [Introduction to Alerting](https://cloud.google.com/monitoring/alerts/).
//
message AlertPolicy {
  option (google.api.resource) = {
    type: "monitoring.googleapis.com/AlertPolicy"
    pattern: "projects/{project}/alertPolicies/{alert_policy}"
    pattern: "organizations/{organization}/alertPolicies/{alert_policy}"
    pattern: "folders/{folder}/alertPolicies/{alert_policy}"
    pattern: "*"
  };

  // Documentation that is included in the notifications and incidents
  // pertaining to this policy.
  message Documentation {
    // Links to content such as playbooks, repositories, and other resources.
    message Link {
      // A short display name for the link. The display name must not be empty
      // or exceed 63 characters. Example: "playbook".
      string display_name = 1;

      // The url of a webpage.
      // A url can be templatized by using variables
      // in the path or the query parameters. The total length of a URL should
      // not exceed 2083 characters before and after variable expansion.
      // Example: "https://my_domain.com/playbook?name=${resource.name}"
      string url = 2;
    }

    // The body of the documentation, interpreted according to `mime_type`.
    // The content may not exceed 8,192 Unicode characters and may not exceed
    // more than 10,240 bytes when encoded in UTF-8 format, whichever is
    // smaller. This text can be [templatized by using
    // variables](https://cloud.google.com/monitoring/alerts/doc-variables#doc-vars).
    string content = 1;

    // The format of the `content` field. Presently, only the value
    // `"text/markdown"` is supported. See
    // [Markdown](https://en.wikipedia.org/wiki/Markdown) for more information.
    string mime_type = 2;

    // Optional. The subject line of the notification. The subject line may not
    // exceed 10,240 bytes. In notifications generated by this policy, the
    // contents of the subject line after variable expansion will be truncated
    // to 255 bytes or shorter at the latest UTF-8 character boundary. The
    // 255-byte limit is recommended by [this
    // thread](https://stackoverflow.com/questions/1592291/what-is-the-email-subject-length-limit).
    // It is both the limit imposed by some third-party ticketing products and
    // it is common to define textual fields in databases as VARCHAR(255).
    //
    // The contents of the subject line can be [templatized by using
    // variables](https://cloud.google.com/monitoring/alerts/doc-variables#doc-vars).
    // If this field is missing or empty, a default subject line will be
    // generated.
    string subject = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Links to content such as playbooks, repositories, and other
    // resources. This field can contain up to 3 entries.
    repeated Link links = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // A condition is a true/false test that determines when an alerting policy
  // should open an incident. If a condition evaluates to true, it signifies
  // that something is wrong.
  message Condition {
    option (google.api.resource) = {
      type: "monitoring.googleapis.com/AlertPolicyCondition"
      pattern: "projects/{project}/alertPolicies/{alert_policy}/conditions/{condition}"
      pattern: "organizations/{organization}/alertPolicies/{alert_policy}/conditions/{condition}"
      pattern: "folders/{folder}/alertPolicies/{alert_policy}/conditions/{condition}"
      pattern: "*"
    };

    // Specifies how many time series must fail a predicate to trigger a
    // condition. If not specified, then a `{count: 1}` trigger is used.
    message Trigger {
      // A type of trigger.
      oneof type {
        // The absolute number of time series that must fail
        // the predicate for the condition to be triggered.
        int32 count = 1;

        // The percentage of time series that must fail the
        // predicate for the condition to be triggered.
        double percent = 2;
      }
    }

    // A condition control that determines how metric-threshold conditions
    // are evaluated when data stops arriving.
    // This control doesn't affect metric-absence policies.
    enum EvaluationMissingData {
      // An unspecified evaluation missing data option.  Equivalent to
      // EVALUATION_MISSING_DATA_NO_OP.
      EVALUATION_MISSING_DATA_UNSPECIFIED = 0;

      // If there is no data to evaluate the condition, then evaluate the
      // condition as false.
      EVALUATION_MISSING_DATA_INACTIVE = 1;

      // If there is no data to evaluate the condition, then evaluate the
      // condition as true.
      EVALUATION_MISSING_DATA_ACTIVE = 2;

      // Do not evaluate the condition to any value if there is no data.
      EVALUATION_MISSING_DATA_NO_OP = 3;
    }

    // A condition type that compares a collection of time series
    // against a threshold.
    message MetricThreshold {
      // Options used when forecasting the time series and testing
      // the predicted value against the threshold.
      message ForecastOptions {
        // Required. The length of time into the future to forecast whether a
        // time series will violate the threshold. If the predicted value is
        // found to violate the threshold, and the violation is observed in all
        // forecasts made for the configured `duration`, then the time series is
        // considered to be failing.
        // The forecast horizon can range from 1 hour to 60 hours.
        google.protobuf.Duration forecast_horizon = 1
            [(google.api.field_behavior) = REQUIRED];
      }

      // Required. A
      // [filter](https://cloud.google.com/monitoring/api/v3/filters) that
      // identifies which time series should be compared with the threshold.
      //
      // The filter is similar to the one that is specified in the
      // [`ListTimeSeries`
      // request](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/list)
      // (that call is useful to verify the time series that will be retrieved /
      // processed). The filter must specify the metric type and the resource
      // type. Optionally, it can specify resource labels and metric labels.
      // This field must not exceed 2048 Unicode characters in length.
      string filter = 2 [(google.api.field_behavior) = REQUIRED];

      // Specifies the alignment of data points in individual time series as
      // well as how to combine the retrieved time series together (such as
      // when aggregating multiple streams on each resource to a single
      // stream for each resource or when aggregating streams across all
      // members of a group of resources). Multiple aggregations
      // are applied in the order specified.
      //
      // This field is similar to the one in the [`ListTimeSeries`
      // request](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/list).
      // It is advisable to use the `ListTimeSeries` method when debugging this
      // field.
      repeated Aggregation aggregations = 8;

      // A [filter](https://cloud.google.com/monitoring/api/v3/filters) that
      // identifies a time series that should be used as the denominator of a
      // ratio that will be compared with the threshold. If a
      // `denominator_filter` is specified, the time series specified by the
      // `filter` field will be used as the numerator.
      //
      // The filter must specify the metric type and optionally may contain
      // restrictions on resource type, resource labels, and metric labels.
      // This field may not exceed 2048 Unicode characters in length.
      string denominator_filter = 9;

      // Specifies the alignment of data points in individual time series
      // selected by `denominatorFilter` as
      // well as how to combine the retrieved time series together (such as
      // when aggregating multiple streams on each resource to a single
      // stream for each resource or when aggregating streams across all
      // members of a group of resources).
      //
      // When computing ratios, the `aggregations` and
      // `denominator_aggregations` fields must use the same alignment period
      // and produce time series that have the same periodicity and labels.
      repeated Aggregation denominator_aggregations = 10;

      // When this field is present, the `MetricThreshold` condition forecasts
      // whether the time series is predicted to violate the threshold within
      // the `forecast_horizon`. When this field is not set, the
      // `MetricThreshold` tests the current value of the timeseries against the
      // threshold.
      ForecastOptions forecast_options = 12;

      // The comparison to apply between the time series (indicated by `filter`
      // and `aggregation`) and the threshold (indicated by `threshold_value`).
      // The comparison is applied on each time series, with the time series
      // on the left-hand side and the threshold on the right-hand side.
      //
      // Only `COMPARISON_LT` and `COMPARISON_GT` are supported currently.
      ComparisonType comparison = 4;

      // A value against which to compare the time series.
      double threshold_value = 5;

      // The amount of time that a time series must violate the
      // threshold to be considered failing. Currently, only values
      // that are a multiple of a minute--e.g., 0, 60, 120, or 300
      // seconds--are supported. If an invalid value is given, an
      // error will be returned. When choosing a duration, it is useful to
      // keep in mind the frequency of the underlying time series data
      // (which may also be affected by any alignments specified in the
      // `aggregations` field); a good duration is long enough so that a single
      // outlier does not generate spurious alerts, but short enough that
      // unhealthy states are detected and alerted on quickly.
      google.protobuf.Duration duration = 6;

      // The number/percent of time series for which the comparison must hold
      // in order for the condition to trigger. If unspecified, then the
      // condition will trigger if the comparison is true for any of the
      // time series that have been identified by `filter` and `aggregations`,
      // or by the ratio, if `denominator_filter` and `denominator_aggregations`
      // are specified.
      Trigger trigger = 7;

      // A condition control that determines how metric-threshold conditions
      // are evaluated when data stops arriving. To use this control, the value
      // of the `duration` field must be greater than or equal to 60 seconds.
      EvaluationMissingData evaluation_missing_data = 11;
    }

    // A condition type that checks that monitored resources
    // are reporting data. The configuration defines a metric and
    // a set of monitored resources. The predicate is considered in violation
    // when a time series for the specified metric of a monitored
    // resource does not include any data in the specified `duration`.
    message MetricAbsence {
      // Required. A
      // [filter](https://cloud.google.com/monitoring/api/v3/filters) that
      // identifies which time series should be compared with the threshold.
      //
      // The filter is similar to the one that is specified in the
      // [`ListTimeSeries`
      // request](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/list)
      // (that call is useful to verify the time series that will be retrieved /
      // processed). The filter must specify the metric type and the resource
      // type. Optionally, it can specify resource labels and metric labels.
      // This field must not exceed 2048 Unicode characters in length.
      string filter = 1 [(google.api.field_behavior) = REQUIRED];

      // Specifies the alignment of data points in individual time series as
      // well as how to combine the retrieved time series together (such as
      // when aggregating multiple streams on each resource to a single
      // stream for each resource or when aggregating streams across all
      // members of a group of resources). Multiple aggregations
      // are applied in the order specified.
      //
      // This field is similar to the one in the [`ListTimeSeries`
      // request](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/list).
      // It is advisable to use the `ListTimeSeries` method when debugging this
      // field.
      repeated Aggregation aggregations = 5;

      // The amount of time that a time series must fail to report new
      // data to be considered failing. The minimum value of this field
      // is 120 seconds. Larger values that are a multiple of a
      // minute--for example, 240 or 300 seconds--are supported.
      // If an invalid value is given, an
      // error will be returned. The `Duration.nanos` field is
      // ignored.
      google.protobuf.Duration duration = 2;

      // The number/percent of time series for which the comparison must hold
      // in order for the condition to trigger. If unspecified, then the
      // condition will trigger if the comparison is true for any of the
      // time series that have been identified by `filter` and `aggregations`.
      Trigger trigger = 3;
    }

    // A condition type that checks whether a log message in the [scoping
    // project](https://cloud.google.com/monitoring/api/v3#project_name)
    // satisfies the given filter. Logs from other projects in the metrics
    // scope are not evaluated.
    message LogMatch {
      // Required. A logs-based filter. See [Advanced Logs
      // Queries](https://cloud.google.com/logging/docs/view/advanced-queries)
      // for how this filter should be constructed.
      string filter = 1 [(google.api.field_behavior) = REQUIRED];

      // Optional. A map from a label key to an extractor expression, which is
      // used to extract the value for this label key. Each entry in this map is
      // a specification for how data should be extracted from log entries that
      // match `filter`. Each combination of extracted values is treated as a
      // separate rule for the purposes of triggering notifications. Label keys
      // and corresponding values can be used in notifications generated by this
      // condition.
      //
      // Please see [the documentation on logs-based metric
      // `valueExtractor`s](https://cloud.google.com/logging/docs/reference/v2/rest/v2/projects.metrics#LogMetric.FIELDS.value_extractor)
      // for syntax and examples.
      map<string, string> label_extractors = 2;
    }

    // A condition type that allows alerting policies to be defined using
    // [Monitoring Query Language](https://cloud.google.com/monitoring/mql).
    message MonitoringQueryLanguageCondition {
      // [Monitoring Query Language](https://cloud.google.com/monitoring/mql)
      // query that outputs a boolean stream.
      string query = 1;

      // The amount of time that a time series must violate the
      // threshold to be considered failing. Currently, only values
      // that are a multiple of a minute--e.g., 0, 60, 120, or 300
      // seconds--are supported. If an invalid value is given, an
      // error will be returned. When choosing a duration, it is useful to
      // keep in mind the frequency of the underlying time series data
      // (which may also be affected by any alignments specified in the
      // `aggregations` field); a good duration is long enough so that a single
      // outlier does not generate spurious alerts, but short enough that
      // unhealthy states are detected and alerted on quickly.
      google.protobuf.Duration duration = 2;

      // The number/percent of time series for which the comparison must hold
      // in order for the condition to trigger. If unspecified, then the
      // condition will trigger if the comparison is true for any of the
      // time series that have been identified by `filter` and `aggregations`,
      // or by the ratio, if `denominator_filter` and `denominator_aggregations`
      // are specified.
      Trigger trigger = 3;

      // A condition control that determines how metric-threshold conditions
      // are evaluated when data stops arriving.
      EvaluationMissingData evaluation_missing_data = 4;
    }

    // A condition type that allows alerting policies to be defined using
    // [Prometheus Query Language
    // (PromQL)](https://prometheus.io/docs/prometheus/latest/querying/basics/).
    //
    // The PrometheusQueryLanguageCondition message contains information
    // from a Prometheus alerting rule and its associated rule group.
    //
    // A Prometheus alerting rule is described
    // [here](https://prometheus.io/docs/prometheus/latest/configuration/alerting_rules/).
    // The semantics of a Prometheus alerting rule is described
    // [here](https://prometheus.io/docs/prometheus/latest/configuration/recording_rules/#rule).
    //
    // A Prometheus rule group is described
    // [here](https://prometheus.io/docs/prometheus/latest/configuration/recording_rules/).
    // The semantics of a Prometheus rule group is described
    // [here](https://prometheus.io/docs/prometheus/latest/configuration/recording_rules/#rule_group).
    //
    // Because Cloud Alerting has no representation of a Prometheus rule
    // group resource, we must embed the information of the parent rule
    // group inside each of the conditions that refer to it. We must also
    // update the contents of all Prometheus alerts in case the information
    // of their rule group changes.
    //
    // The PrometheusQueryLanguageCondition protocol buffer combines the
    // information of the corresponding rule group and alerting rule.
    // The structure of the PrometheusQueryLanguageCondition protocol buffer
    // does NOT mimic the structure of the Prometheus rule group and alerting
    // rule YAML declarations. The PrometheusQueryLanguageCondition protocol
    // buffer may change in the future to support future rule group and/or
    // alerting rule features. There are no new such features at the present
    // time (2023-06-26).
    message PrometheusQueryLanguageCondition {
      // Required. The PromQL expression to evaluate. Every evaluation cycle
      // this expression is evaluated at the current time, and all resultant
      // time series become pending/firing alerts. This field must not be empty.
      string query = 1 [(google.api.field_behavior) = REQUIRED];

      // Optional. Alerts are considered firing once their PromQL expression was
      // evaluated to be "true" for this long.
      // Alerts whose PromQL expression was not evaluated to be "true" for
      // long enough are considered pending.
      // Must be a non-negative duration or missing.
      // This field is optional. Its default value is zero.
      google.protobuf.Duration duration = 2
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. How often this rule should be evaluated.
      // Must be a positive multiple of 30 seconds or missing.
      // This field is optional. Its default value is 30 seconds.
      // If this PrometheusQueryLanguageCondition was generated from a
      // Prometheus alerting rule, then this value should be taken from the
      // enclosing rule group.
      google.protobuf.Duration evaluation_interval = 3
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Labels to add to or overwrite in the PromQL query result.
      // Label names [must be
      // valid](https://prometheus.io/docs/concepts/data_model/#metric-names-and-labels).
      // Label values can be [templatized by using
      // variables](https://cloud.google.com/monitoring/alerts/doc-variables#doc-vars).
      // The only available variable names are the names of the labels in the
      // PromQL result, including "__name__" and "value". "labels" may be empty.
      map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The rule group name of this alert in the corresponding
      // Prometheus configuration file.
      //
      // Some external tools may require this field to be populated correctly
      // in order to refer to the original Prometheus configuration file.
      // The rule group name and the alert name are necessary to update the
      // relevant AlertPolicies in case the definition of the rule group changes
      // in the future.
      //
      // This field is optional. If this field is not empty, then it must
      // contain a valid UTF-8 string.
      // This field may not exceed 2048 Unicode characters in length.
      string rule_group = 5 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The alerting rule name of this alert in the corresponding
      // Prometheus configuration file.
      //
      // Some external tools may require this field to be populated correctly
      // in order to refer to the original Prometheus configuration file.
      // The rule group name and the alert name are necessary to update the
      // relevant AlertPolicies in case the definition of the rule group changes
      // in the future.
      //
      // This field is optional. If this field is not empty, then it must be a
      // [valid Prometheus label
      // name](https://prometheus.io/docs/concepts/data_model/#metric-names-and-labels).
      // This field may not exceed 2048 Unicode characters in length.
      string alert_rule = 6 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Whether to disable metric existence validation for this
      // condition.
      //
      // This allows alerting policies to be defined on metrics that do not yet
      // exist, improving advanced customer workflows such as configuring
      // alerting policies using Terraform.
      //
      // Users with the `monitoring.alertPolicyViewer` role are able to see the
      // name of the non-existent metric in the alerting policy condition.
      bool disable_metric_validation = 7
          [(google.api.field_behavior) = OPTIONAL];
    }

    // A condition that allows alerting policies to be defined using GoogleSQL.
    // SQL conditions examine a sliding window of logs using GoogleSQL.
    // Alert policies with SQL conditions may incur additional billing.
    message SqlCondition {
      // Used to schedule the query to run every so many minutes.
      message Minutes {
        // Required. Number of minutes between runs. The interval must be
        // greater than or equal to 5 minutes and less than or equal to 1440
        // minutes.
        int32 periodicity = 1 [(google.api.field_behavior) = REQUIRED];
      }

      // Used to schedule the query to run every so many hours.
      message Hourly {
        // Required. The number of hours between runs. Must be greater than or
        // equal to 1 hour and less than or equal to 48 hours.
        int32 periodicity = 1 [(google.api.field_behavior) = REQUIRED];

        // Optional. The number of minutes after the hour (in UTC) to run the
        // query. Must be greater than or equal to 0 minutes and less than or
        // equal to 59 minutes.  If left unspecified, then an arbitrary offset
        // is used.
        optional int32 minute_offset = 2
            [(google.api.field_behavior) = OPTIONAL];
      }

      // Used to schedule the query to run every so many days.
      message Daily {
        // Required. The number of days between runs. Must be greater than or
        // equal to 1 day and less than or equal to 31 days.
        int32 periodicity = 1 [(google.api.field_behavior) = REQUIRED];

        // Optional. The time of day (in UTC) at which the query should run. If
        // left unspecified, the server picks an arbitrary time of day and runs
        // the query at the same time each day.
        google.type.TimeOfDay execution_time = 2
            [(google.api.field_behavior) = OPTIONAL];
      }

      // A test that checks if the number of rows in the result set
      // violates some threshold.
      message RowCountTest {
        // Required. The comparison to apply between the number of rows returned
        // by the query and the threshold.
        ComparisonType comparison = 1 [(google.api.field_behavior) = REQUIRED];

        // Required. The value against which to compare the row count.
        int64 threshold = 2 [(google.api.field_behavior) = REQUIRED];
      }

      // A test that uses an alerting result in a boolean column produced by
      // the SQL query.
      message BooleanTest {
        // Required. The name of the column containing the boolean value. If the
        // value in a row is NULL, that row is ignored.
        string column = 1 [(google.api.field_behavior) = REQUIRED];
      }

      // Required. The Log Analytics SQL query to run, as a string.  The query
      // must conform to the required shape. Specifically, the query must not
      // try to filter the input by time.  A filter will automatically be
      // applied to filter the input so that the query receives all rows
      // received since the last time the query was run.
      //
      // For example, the following query extracts all log entries containing an
      // HTTP request:
      //
      //     SELECT
      //       timestamp, log_name, severity, http_request, resource, labels
      //     FROM
      //       my-project.global._Default._AllLogs
      //     WHERE
      //       http_request IS NOT NULL
      string query = 1 [(google.api.field_behavior) = REQUIRED];

      // The schedule indicates how often the query should be run.
      oneof schedule {
        // Schedule the query to execute every so many minutes.
        Minutes minutes = 2;

        // Schedule the query to execute every so many hours.
        Hourly hourly = 3;

        // Schedule the query to execute every so many days.
        Daily daily = 4;
      }

      // The test to be run against the SQL result set.
      oneof evaluate {
        // Test the row count against a threshold.
        RowCountTest row_count_test = 5;

        // Test the boolean value in the indicated column.
        BooleanTest boolean_test = 6;
      }
    }

    // Required if the condition exists. The unique resource name for this
    // condition. Its format is:
    //
    //     projects/[PROJECT_ID_OR_NUMBER]/alertPolicies/[POLICY_ID]/conditions/[CONDITION_ID]
    //
    // `[CONDITION_ID]` is assigned by Cloud Monitoring when the
    // condition is created as part of a new or updated alerting policy.
    //
    // When calling the
    // [alertPolicies.create][google.monitoring.v3.AlertPolicyService.CreateAlertPolicy]
    // method, do not include the `name` field in the conditions of the
    // requested alerting policy. Cloud Monitoring creates the
    // condition identifiers and includes them in the new policy.
    //
    // When calling the
    // [alertPolicies.update][google.monitoring.v3.AlertPolicyService.UpdateAlertPolicy]
    // method to update a policy, including a condition `name` causes the
    // existing condition to be updated. Conditions without names are added to
    // the updated policy. Existing conditions are deleted if they are not
    // updated.
    //
    // Best practice is to preserve `[CONDITION_ID]` if you make only small
    // changes, such as those to condition thresholds, durations, or trigger
    // values.  Otherwise, treat the change as a new condition and let the
    // existing condition be deleted.
    string name = 12;

    // A short name or phrase used to identify the condition in dashboards,
    // notifications, and incidents. To avoid confusion, don't use the same
    // display name for multiple conditions in the same policy.
    string display_name = 6;

    // Only one of the following condition types will be specified.
    oneof condition {
      // A condition that compares a time series against a threshold.
      MetricThreshold condition_threshold = 1;

      // A condition that checks that a time series continues to
      // receive new data points.
      MetricAbsence condition_absent = 2;

      // A condition that checks for log messages matching given constraints. If
      // set, no other conditions can be present.
      LogMatch condition_matched_log = 20;

      // A condition that uses the Monitoring Query Language to define
      // alerts.
      MonitoringQueryLanguageCondition condition_monitoring_query_language = 19;

      // A condition that uses the Prometheus query language to define alerts.
      PrometheusQueryLanguageCondition condition_prometheus_query_language = 21;

      // A condition that periodically evaluates a SQL query result.
      SqlCondition condition_sql = 22;
    }
  }

  // Operators for combining conditions.
  enum ConditionCombinerType {
    // An unspecified combiner.
    COMBINE_UNSPECIFIED = 0;

    // Combine conditions using the logical `AND` operator. An
    // incident is created only if all the conditions are met
    // simultaneously. This combiner is satisfied if all conditions are
    // met, even if they are met on completely different resources.
    AND = 1;

    // Combine conditions using the logical `OR` operator. An incident
    // is created if any of the listed conditions is met.
    OR = 2;

    // Combine conditions using logical `AND` operator, but unlike the regular
    // `AND` option, an incident is created only if all conditions are met
    // simultaneously on at least one resource.
    AND_WITH_MATCHING_RESOURCE = 3;
  }

  // Control over how the notification channels in `notification_channels`
  // are notified when this alert fires.
  message AlertStrategy {
    // Control over the rate of notifications sent to this alerting policy's
    // notification channels.
    message NotificationRateLimit {
      // Not more than one notification per `period`.
      google.protobuf.Duration period = 1;
    }

    // Control when notifications will be sent out.
    enum NotificationPrompt {
      // No strategy specified. Treated as error.
      NOTIFICATION_PROMPT_UNSPECIFIED = 0;

      // Notify when an incident is opened.
      OPENED = 1;

      // Notify when an incident is closed.
      CLOSED = 3;
    }

    // Control over how the notification channels in `notification_channels`
    // are notified when this alert fires, on a per-channel basis.
    message NotificationChannelStrategy {
      // The full REST resource name for the notification channels that these
      // settings apply to. Each of these correspond to the name field in one
      // of the NotificationChannel objects referenced in the
      // notification_channels field of this AlertPolicy.
      // The format is:
      //
      //     projects/[PROJECT_ID_OR_NUMBER]/notificationChannels/[CHANNEL_ID]
      repeated string notification_channel_names = 1;

      // The frequency at which to send reminder notifications for open
      // incidents.
      google.protobuf.Duration renotify_interval = 2;
    }

    // Required for log-based alerting policies, i.e. policies with a `LogMatch`
    // condition.
    //
    // This limit is not implemented for alerting policies that do not have
    // a LogMatch condition.
    NotificationRateLimit notification_rate_limit = 1;

    // For log-based alert policies, the notification prompts is always
    // [OPENED]. For non log-based alert policies, the notification prompts can
    // be [OPENED] or [OPENED, CLOSED].
    repeated NotificationPrompt notification_prompts = 2;

    // If an alerting policy that was active has no data for this long, any open
    // incidents will close
    google.protobuf.Duration auto_close = 3;

    // Control how notifications will be sent out, on a per-channel basis.
    repeated NotificationChannelStrategy notification_channel_strategy = 4;
  }

  // An enumeration of possible severity level for an alerting policy.
  enum Severity {
    // No severity is specified. This is the default value.
    SEVERITY_UNSPECIFIED = 0;

    // This is the highest severity level. Use this if the problem could
    // cause significant damage or downtime.
    CRITICAL = 1;

    // This is the medium severity level. Use this if the problem could
    // cause minor damage or downtime.
    ERROR = 2;

    // This is the lowest severity level. Use this if the problem is not causing
    // any damage or downtime, but could potentially lead to a problem in the
    // future.
    WARNING = 3;
  }

  // Identifier. Required if the policy exists. The resource name for this
  // policy. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]/alertPolicies/[ALERT_POLICY_ID]
  //
  // `[ALERT_POLICY_ID]` is assigned by Cloud Monitoring when the policy
  // is created. When calling the
  // [alertPolicies.create][google.monitoring.v3.AlertPolicyService.CreateAlertPolicy]
  // method, do not include the `name` field in the alerting policy passed as
  // part of the request.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // A short name or phrase used to identify the policy in dashboards,
  // notifications, and incidents. To avoid confusion, don't use the same
  // display name for multiple policies in the same project. The name is
  // limited to 512 Unicode characters.
  //
  // The convention for the display_name of a PrometheusQueryLanguageCondition
  // is "{rule group name}/{alert name}", where the {rule group name} and
  // {alert name} should be taken from the corresponding Prometheus
  // configuration file. This convention is not enforced.
  // In any case the display_name is not a unique key of the AlertPolicy.
  string display_name = 2;

  // Documentation that is included with notifications and incidents related to
  // this policy. Best practice is for the documentation to include information
  // to help responders understand, mitigate, escalate, and correct the
  // underlying problems detected by the alerting policy. Notification channels
  // that have limited capacity might not show this documentation.
  Documentation documentation = 13;

  // User-supplied key/value data to be used for organizing and
  // identifying the `AlertPolicy` objects.
  //
  // The field can contain up to 64 entries. Each key and value is limited to
  // 63 Unicode characters or 128 bytes, whichever is smaller. Labels and
  // values can contain only lowercase letters, numerals, underscores, and
  // dashes. Keys must begin with a letter.
  //
  // Note that Prometheus {alert name} is a
  // [valid Prometheus label
  // names](https://prometheus.io/docs/concepts/data_model/#metric-names-and-labels),
  // whereas Prometheus {rule group} is an unrestricted UTF-8 string.
  // This means that they cannot be stored as-is in user labels, because
  // they may contain characters that are not allowed in user-label values.
  map<string, string> user_labels = 16;

  // A list of conditions for the policy. The conditions are combined by AND or
  // OR according to the `combiner` field. If the combined conditions evaluate
  // to true, then an incident is created. A policy can have from one to six
  // conditions.
  // If `condition_time_series_query_language` is present, it must be the only
  // `condition`.
  // If `condition_monitoring_query_language` is present, it must be the only
  // `condition`.
  repeated Condition conditions = 12;

  // How to combine the results of multiple conditions to determine if an
  // incident should be opened.
  // If `condition_time_series_query_language` is present, this must be
  // `COMBINE_UNSPECIFIED`.
  ConditionCombinerType combiner = 6;

  // Whether or not the policy is enabled. On write, the default interpretation
  // if unset is that the policy is enabled. On read, clients should not make
  // any assumption about the state if it has not been populated. The
  // field should always be populated on List and Get operations, unless
  // a field projection has been specified that strips it out.
  google.protobuf.BoolValue enabled = 17;

  // Read-only description of how the alerting policy is invalid. This field is
  // only set when the alerting policy is invalid. An invalid alerting policy
  // will not generate incidents.
  google.rpc.Status validity = 18;

  // Identifies the notification channels to which notifications should be sent
  // when incidents are opened or closed or when new violations occur on
  // an already opened incident. Each element of this array corresponds to
  // the `name` field in each of the
  // [`NotificationChannel`][google.monitoring.v3.NotificationChannel]
  // objects that are returned from the [`ListNotificationChannels`]
  // [google.monitoring.v3.NotificationChannelService.ListNotificationChannels]
  // method. The format of the entries in this field is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]/notificationChannels/[CHANNEL_ID]
  repeated string notification_channels = 14;

  // A read-only record of the creation of the alerting policy. If provided
  // in a call to create or update, this field will be ignored.
  MutationRecord creation_record = 10;

  // A read-only record of the most recent change to the alerting policy. If
  // provided in a call to create or update, this field will be ignored.
  MutationRecord mutation_record = 11;

  // Control over how this alerting policy's notification channels are notified.
  AlertStrategy alert_strategy = 21;

  // Optional. The severity of an alerting policy indicates how important
  // incidents generated by that policy are. The severity level will be
  // displayed on the Incident detail page and in notifications.
  Severity severity = 22 [(google.api.field_behavior) = OPTIONAL];
}
