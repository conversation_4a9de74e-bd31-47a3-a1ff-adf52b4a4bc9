// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.metricsscope.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/monitoring/metricsscope/v1/metrics_scope.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/monitoring/metricsscope/apiv1/metricsscopepb;metricsscopepb";
option java_multiple_files = true;
option java_outer_classname = "MetricsScopesProto";
option java_package = "com.google.monitoring.metricsscope.v1";
option csharp_namespace = "Google.Cloud.Monitoring.MetricsScope.V1";
option php_namespace = "Google\\Cloud\\Monitoring\\MetricsScope\\V1";
option ruby_package = "Google::Cloud::Monitoring::MetricsScope::V1";

// Manages Cloud Monitoring Metrics Scopes, and the monitoring of Google Cloud
// projects and AWS accounts.
service MetricsScopes {
  option (google.api.default_host) = "monitoring.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/monitoring,"
      "https://www.googleapis.com/auth/monitoring.read,"
      "https://www.googleapis.com/auth/monitoring.write";

  // Returns a specific `Metrics Scope`.
  rpc GetMetricsScope(GetMetricsScopeRequest) returns (MetricsScope) {
    option (google.api.http) = {
      get: "/v1/{name=locations/global/metricsScopes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of every `Metrics Scope` that a specific `MonitoredProject`
  // has been added to. The metrics scope representing the specified monitored
  // project will always be the first entry in the response.
  rpc ListMetricsScopesByMonitoredProject(ListMetricsScopesByMonitoredProjectRequest) returns (ListMetricsScopesByMonitoredProjectResponse) {
    option (google.api.http) = {
      get: "/v1/locations/global/metricsScopes:listMetricsScopesByMonitoredProject"
    };
  }

  // Adds a `MonitoredProject` with the given project ID
  // to the specified `Metrics Scope`.
  rpc CreateMonitoredProject(CreateMonitoredProjectRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=locations/global/metricsScopes/*}/projects"
      body: "monitored_project"
    };
    option (google.api.method_signature) = "parent,monitored_project";
    option (google.longrunning.operation_info) = {
      response_type: "MonitoredProject"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a `MonitoredProject` from the specified `Metrics Scope`.
  rpc DeleteMonitoredProject(DeleteMonitoredProjectRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=locations/global/metricsScopes/*/projects/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Request for the `GetMetricsScope` method.
message GetMetricsScopeRequest {
  // Required. The resource name of the `Metrics Scope`.
  // Example:
  // `locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "monitoring.googleapis.com/MetricsScope"
    }
  ];
}

// Request for the `ListMetricsScopesByMonitoredProject` method.
message ListMetricsScopesByMonitoredProjectRequest {
  // Required. The resource name of the `Monitored Project` being requested.
  // Example:
  // `projects/{MONITORED_PROJECT_ID_OR_NUMBER}`
  string monitored_resource_container = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response for the `ListMetricsScopesByMonitoredProject` method.
message ListMetricsScopesByMonitoredProjectResponse {
  // A set of all metrics scopes that the specified monitored project has been
  // added to.
  repeated MetricsScope metrics_scopes = 1;
}

// Request for the `CreateMonitoredProject` method.
message CreateMonitoredProjectRequest {
  // Required. The resource name of the existing `Metrics Scope` that will monitor this
  // project.
  // Example:
  // `locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "monitoring.googleapis.com/MetricsScope"
    }
  ];

  // Required. The initial `MonitoredProject` configuration.
  // Specify only the `monitored_project.name` field. All other fields are
  // ignored. The `monitored_project.name` must be in the format:
  // `locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}/projects/{MONITORED_PROJECT_ID_OR_NUMBER}`
  MonitoredProject monitored_project = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteMonitoredProject` method.
message DeleteMonitoredProjectRequest {
  // Required. The resource name of the `MonitoredProject`.
  // Example:
  // `locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}/projects/{MONITORED_PROJECT_ID_OR_NUMBER}`
  //
  // Authorization requires the following [Google
  // IAM](https://cloud.google.com/iam) permissions on both the `Metrics Scope`
  // and on the `MonitoredProject`: `monitoring.metricsScopes.link`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "monitoring.googleapis.com/MonitoredProject"
    }
  ];
}

// Contains metadata for longrunning operation for the edit Metrics Scope
// endpoints.
message OperationMetadata {
  // Batch operation states.
  enum State {
    // Invalid.
    STATE_UNSPECIFIED = 0;

    // Request has been received.
    CREATED = 1;

    // Request is actively being processed.
    RUNNING = 2;

    // The batch processing is done.
    DONE = 3;

    // The batch processing was cancelled.
    CANCELLED = 4;
  }

  // Current state of the batch operation.
  State state = 1;

  // The time when the batch request was received.
  google.protobuf.Timestamp create_time = 5;

  // The time when the operation result was last updated.
  google.protobuf.Timestamp update_time = 6;
}
