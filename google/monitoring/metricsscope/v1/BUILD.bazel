# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "metricsscope_proto",
    srcs = [
        "metrics_scope.proto",
        "metrics_scopes.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "metricsscope_proto_with_info",
    deps = [
        ":metricsscope_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "metricsscope_java_proto",
    deps = [":metricsscope_proto"],
)

java_grpc_library(
    name = "metricsscope_java_grpc",
    srcs = [":metricsscope_proto"],
    deps = [":metricsscope_java_proto"],
)

java_gapic_library(
    name = "metricsscope_java_gapic",
    srcs = [":metricsscope_proto_with_info"],
    gapic_yaml = "monitoring_gapic.yaml",
    grpc_service_config = "metricsscopes_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    test_deps = [
        ":metricsscope_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":metricsscope_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "metricsscope_java_gapic_test_suite",
    test_classes = [
        "com.google.monitoring.metricsscope.v1.MetricsScopesClientTest",
    ],
    runtime_deps = [":metricsscope_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-monitoring-metricsscope-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":metricsscope_java_gapic",
        ":metricsscope_java_grpc",
        ":metricsscope_java_proto",
        ":metricsscope_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "metricsscope_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/monitoring/metricsscope/apiv1/metricsscopepb",
    protos = [":metricsscope_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "metricsscope_go_gapic",
    srcs = [":metricsscope_proto_with_info"],
    grpc_service_config = "metricsscopes_grpc_service_config.json",
    importpath = "cloud.google.com/go/monitoring/metricsscope/apiv1;metricsscope",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [
        ":metricsscope_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-monitoring-metricsscope-v1-go",
    deps = [
        ":metricsscope_go_gapic",
        ":metricsscope_go_gapic_srcjar-metadata.srcjar",
        ":metricsscope_go_gapic_srcjar-snippets.srcjar",
        ":metricsscope_go_gapic_srcjar-test.srcjar",
        ":metricsscope_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "metricsscope_py_gapic",
    srcs = [":metricsscope_proto"],
    grpc_service_config = "metricsscopes_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=monitoring_metrics_scope",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-monitoring-metrics-scopes",
    ],
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "metricsscope_py_gapic_test",
    srcs = [
        "metricsscope_py_gapic_pytest.py",
        "metricsscope_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":metricsscope_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "monitoring-metricsscope-v1-py",
    deps = [
        ":metricsscope_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "metricsscope_php_proto",
    deps = [":metricsscope_proto"],
)

php_gapic_library(
    name = "metricsscope_php_gapic",
    srcs = [":metricsscope_proto_with_info"],
    grpc_service_config = "metricsscopes_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc+rest",
    deps = [
        ":metricsscope_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-monitoring-metricsscope-v1-php",
    deps = [
        ":metricsscope_php_gapic",
        ":metricsscope_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "metricsscope_nodejs_gapic",
    package_name = "@google-cloud/metricsscope",
    src = ":metricsscope_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "metricsscopes_grpc_service_config.json",
    package = "google.monitoring.metricsscope.v1",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "monitoring-metricsscope-v1-nodejs",
    deps = [
        ":metricsscope_nodejs_gapic",
        ":metricsscope_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "metricsscope_ruby_proto",
    deps = [":metricsscope_proto"],
)

ruby_grpc_library(
    name = "metricsscope_ruby_grpc",
    srcs = [":metricsscope_proto"],
    deps = [":metricsscope_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "metricsscope_ruby_gapic",
    srcs = [":metricsscope_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=monitoring.googleapis.com",
        "ruby-cloud-api-shortname=monitoring",
        "ruby-cloud-gem-name=google-cloud-monitoring-metrics_scope-v1",
        "ruby-cloud-product-url=https://cloud.google.com/monitoring/settings/manage-api",
        "ruby-cloud-wrapper-gem-override=google-cloud-monitoring",
    ],
    grpc_service_config = "metricsscopes_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Monitoring collects metrics, events, and metadata from Google Cloud, Amazon Web Services (AWS), hosted uptime probes, and application instrumentation. The Metrics Scopes API manages the list of monitored projects and accounts.",
    ruby_cloud_title = "Cloud Monitoring Metrics Scopes V1",
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [
        ":metricsscope_ruby_grpc",
        ":metricsscope_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-monitoring-metricsscope-v1-ruby",
    deps = [
        ":metricsscope_ruby_gapic",
        ":metricsscope_ruby_grpc",
        ":metricsscope_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "metricsscope_csharp_proto",
    deps = [":metricsscope_proto"],
)

csharp_grpc_library(
    name = "metricsscope_csharp_grpc",
    srcs = [":metricsscope_proto"],
    deps = [":metricsscope_csharp_proto"],
)

csharp_gapic_library(
    name = "metricsscope_csharp_gapic",
    srcs = [":metricsscope_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "metricsscopes_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [
        ":metricsscope_csharp_grpc",
        ":metricsscope_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-monitoring-metricsscope-v1-csharp",
    deps = [
        ":metricsscope_csharp_gapic",
        ":metricsscope_csharp_grpc",
        ":metricsscope_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "metricsscope_cc_proto",
    deps = [":metricsscope_proto"],
)

cc_grpc_library(
    name = "metricsscope_cc_grpc",
    srcs = [":metricsscope_proto"],
    grpc_only = True,
    deps = [":metricsscope_cc_proto"],
)
