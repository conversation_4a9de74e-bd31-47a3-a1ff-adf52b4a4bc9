type: google.api.Service
config_version: 3
name: monitoring.googleapis.com
title: Cloud Monitoring API

apis:
- name: google.monitoring.metricsscope.v1.MetricsScopes

types:
- name: google.monitoring.metricsscope.v1.OperationMetadata

documentation:
  summary: |-
    Manages your Cloud Monitoring data and configurations. Most projects must
    be associated with a Workspace, with a few exceptions as noted on the
    individual method pages. The table entries below are presented in
    alphabetical order, not in order of common use. For explanations of the
    concepts found in the table entries, read the [Cloud Monitoring
    documentation](https://cloud.google.com/monitoring/docs).

authentication:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.metricsscope.v1.MetricsScopes.CreateMonitoredProject
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.write
  - selector: google.monitoring.metricsscope.v1.MetricsScopes.DeleteMonitoredProject
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.write
  - selector: google.monitoring.metricsscope.v1.MetricsScopes.GetMetricsScope
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.metricsscope.v1.MetricsScopes.ListMetricsScopesByMonitoredProject
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
