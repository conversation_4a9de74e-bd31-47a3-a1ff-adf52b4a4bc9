{"methodConfig": [{"name": [{"service": "google.monitoring.metricsscipe.v1.MetricsScopes"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN"]}}, {"name": [{"service": "google.monitoring.metricsscipe.v1.MetricsScopes", "method": "GetMetricsScope"}, {"service": "google.monitoring.metricsscipe.v1.MetricsScopes", "method": "ListMetricsScopesByMonitoredProject"}, {"service": "google.monitoring.metricsscipe.v1.MetricsScopes", "method": "CreateMonitoredProject"}, {"service": "google.monitoring.metricsscipe.v1.MetricsScopes", "method": "DeleteMonitoredProject"}, {"service": "google.longrunning.Operations", "method": "GetOperation"}], "timeout": "30s"}]}