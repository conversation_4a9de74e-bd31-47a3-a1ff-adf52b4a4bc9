// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/monitoring/dashboard/v1/dashboard_filter.proto";
import "google/monitoring/dashboard/v1/layouts.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "DashboardsProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A Google Stackdriver dashboard. Dashboards define the content and layout
// of pages in the Stackdriver web application.
message Dashboard {
  option (google.api.resource) = {
    type: "monitoring.googleapis.com/Dashboard"
    pattern: "projects/{project}/dashboards/{dashboard}"
  };

  // Identifier. The resource name of the dashboard.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The mutable, human-readable name.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // `etag` is used for optimistic concurrency control as a way to help
  // prevent simultaneous updates of a policy from overwriting each other.
  // An `etag` is returned in the response to `GetDashboard`, and
  // users are expected to put that etag in the request to `UpdateDashboard` to
  // ensure that their change will be applied to the same version of the
  // Dashboard configuration. The field should not be passed during
  // dashboard creation.
  string etag = 4;

  // A dashboard's root container element that defines the layout style.
  oneof layout {
    // Content is arranged with a basic layout that re-flows a simple list of
    // informational elements like widgets or tiles.
    GridLayout grid_layout = 5;

    // The content is arranged as a grid of tiles, with each content widget
    // occupying one or more grid blocks.
    MosaicLayout mosaic_layout = 6;

    // The content is divided into equally spaced rows and the widgets are
    // arranged horizontally.
    RowLayout row_layout = 8;

    // The content is divided into equally spaced columns and the widgets are
    // arranged vertically.
    ColumnLayout column_layout = 9;
  }

  // Filters to reduce the amount of data charted based on the filter criteria.
  repeated DashboardFilter dashboard_filters = 11;

  // Labels applied to the dashboard
  map<string, string> labels = 12;
}
