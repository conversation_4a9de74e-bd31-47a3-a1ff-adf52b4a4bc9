// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "TextProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A widget that displays textual content.
message Text {
  // The format type of the text content.
  enum Format {
    // Format is unspecified. Defaults to MARKDOWN.
    FORMAT_UNSPECIFIED = 0;

    // The text contains Markdown formatting.
    MARKDOWN = 1;

    // The text contains no special formatting.
    RAW = 2;
  }

  // Properties that determine how the title and content are styled
  message TextStyle {
    // The horizontal alignment of both the title and content on a text widget
    enum HorizontalAlignment {
      // No horizontal alignment specified, will default to H_LEFT
      HORIZONTAL_ALIGNMENT_UNSPECIFIED = 0;

      // Left-align
      H_LEFT = 1;

      // Center-align
      H_CENTER = 2;

      // Right-align
      H_RIGHT = 3;
    }

    // The vertical alignment of both the title and content on a text widget
    enum VerticalAlignment {
      // No vertical alignment specified, will default to V_TOP
      VERTICAL_ALIGNMENT_UNSPECIFIED = 0;

      // Top-align
      V_TOP = 1;

      // Center-align
      V_CENTER = 2;

      // Bottom-align
      V_BOTTOM = 3;
    }

    // Specifies padding size around a text widget
    enum PaddingSize {
      // No padding size specified, will default to P_EXTRA_SMALL
      PADDING_SIZE_UNSPECIFIED = 0;

      // Extra small padding
      P_EXTRA_SMALL = 1;

      // Small padding
      P_SMALL = 2;

      // Medium padding
      P_MEDIUM = 3;

      // Large padding
      P_LARGE = 4;

      // Extra large padding
      P_EXTRA_LARGE = 5;
    }

    // Specifies a font size for the title and content of a text widget
    enum FontSize {
      // No font size specified, will default to FS_LARGE
      FONT_SIZE_UNSPECIFIED = 0;

      // Extra small font size
      FS_EXTRA_SMALL = 1;

      // Small font size
      FS_SMALL = 2;

      // Medium font size
      FS_MEDIUM = 3;

      // Large font size
      FS_LARGE = 4;

      // Extra large font size
      FS_EXTRA_LARGE = 5;
    }

    // Specifies where a visual pointer is placed on a text widget (also
    // sometimes called a "tail")
    enum PointerLocation {
      // No visual pointer
      POINTER_LOCATION_UNSPECIFIED = 0;

      // Placed in the middle of the top of the widget
      PL_TOP = 1;

      // Placed in the middle of the right side of the widget
      PL_RIGHT = 2;

      // Placed in the middle of the bottom of the widget
      PL_BOTTOM = 3;

      // Placed in the middle of the left side of the widget
      PL_LEFT = 4;

      // Placed on the left side of the top of the widget
      PL_TOP_LEFT = 5;

      // Placed on the right side of the top of the widget
      PL_TOP_RIGHT = 6;

      // Placed on the top of the right side of the widget
      PL_RIGHT_TOP = 7;

      // Placed on the bottom of the right side of the widget
      PL_RIGHT_BOTTOM = 8;

      // Placed on the right side of the bottom of the widget
      PL_BOTTOM_RIGHT = 9;

      // Placed on the left side of the bottom of the widget
      PL_BOTTOM_LEFT = 10;

      // Placed on the bottom of the left side of the widget
      PL_LEFT_BOTTOM = 11;

      // Placed on the top of the left side of the widget
      PL_LEFT_TOP = 12;
    }

    // The background color as a hex string. "#RRGGBB" or "#RGB"
    string background_color = 1;

    // The text color as a hex string. "#RRGGBB" or "#RGB"
    string text_color = 2;

    // The horizontal alignment of both the title and content
    HorizontalAlignment horizontal_alignment = 3;

    // The vertical alignment of both the title and content
    VerticalAlignment vertical_alignment = 4;

    // The amount of padding around the widget
    PaddingSize padding = 5;

    // Font sizes for both the title and content. The title will still be larger
    // relative to the content.
    FontSize font_size = 6;

    // The pointer location for this widget (also sometimes called a "tail")
    PointerLocation pointer_location = 7;
  }

  // The text content to be displayed.
  string content = 1;

  // How the text content is formatted.
  Format format = 2;

  // How the text is styled
  TextStyle style = 3;
}
