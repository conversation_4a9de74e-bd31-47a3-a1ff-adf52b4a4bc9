// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/api/monitored_resource.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "IncidentListProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A widget that displays a list of incidents
message IncidentList {
  // Optional. The monitored resource for which incidents are listed.
  // The resource doesn't need to be fully specified. That is, you can specify
  // the resource type but not the values of the resource labels.
  // The resource type and labels are used for filtering.
  repeated google.api.MonitoredResource monitored_resources = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of alert policy names to filter the incident list by.
  // Don't include the project ID prefix in the policy name. For
  // example, use `alertPolicies/utilization`.
  repeated string policy_names = 2 [(google.api.field_behavior) = OPTIONAL];
}
