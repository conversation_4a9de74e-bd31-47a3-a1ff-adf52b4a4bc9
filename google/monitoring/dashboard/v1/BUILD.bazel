# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "dashboard_proto",
    srcs = [
        "alertchart.proto",
        "collapsible_group.proto",
        "common.proto",
        "dashboard.proto",
        "dashboard_filter.proto",
        "dashboards_service.proto",
        "drilldowns.proto",
        "error_reporting_panel.proto",
        "incident_list.proto",
        "layouts.proto",
        "logs_panel.proto",
        "metrics.proto",
        "piechart.proto",
        "scorecard.proto",
        "section_header.proto",
        "service.proto",
        "single_view_group.proto",
        "table.proto",
        "table_display_options.proto",
        "text.proto",
        "widget.proto",
        "xychart.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:monitored_resource_proto",
        "//google/api:resource_proto",
        "//google/type:interval_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
    ],
)

proto_library_with_info(
    name = "dashboard_proto_with_info",
    deps = [
        ":dashboard_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "dashboard_java_proto",
    deps = [":dashboard_proto"],
)

java_grpc_library(
    name = "dashboard_java_grpc",
    srcs = [":dashboard_proto"],
    deps = [":dashboard_java_proto"],
)

java_gapic_library(
    name = "dashboard_java_gapic",
    srcs = [":dashboard_proto_with_info"],
    gapic_yaml = "monitoring_gapic.yaml",
    grpc_service_config = "dashboards_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    test_deps = [
        ":dashboard_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":dashboard_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "dashboard_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.monitoring.dashboard.v1.DashboardsServiceClientHttpJsonTest",
        "com.google.cloud.monitoring.dashboard.v1.DashboardsServiceClientTest",
    ],
    runtime_deps = [":dashboard_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-monitoring-dashboard-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":dashboard_java_gapic",
        ":dashboard_java_grpc",
        ":dashboard_java_proto",
        ":dashboard_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "dashboard_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb",
    protos = [":dashboard_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:monitoredres_go_proto",
        "//google/type:interval_go_proto",
    ],
)

go_gapic_library(
    name = "dashboard_go_gapic",
    srcs = [":dashboard_proto_with_info"],
    grpc_service_config = "dashboards_grpc_service_config.json",
    importpath = "cloud.google.com/go/monitoring/dashboard/apiv1;dashboard",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc+rest",
    deps = [
        ":dashboard_go_proto",
        "//google/api:monitoredres_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-monitoring-dashboard-v1-go",
    deps = [
        ":dashboard_go_gapic",
        ":dashboard_go_gapic_srcjar-metadata.srcjar",
        ":dashboard_go_gapic_srcjar-snippets.srcjar",
        ":dashboard_go_gapic_srcjar-test.srcjar",
        ":dashboard_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "dashboard_py_gapic",
    srcs = [":dashboard_proto"],
    grpc_service_config = "dashboards_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=monitoring_dashboard",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-monitoring-dashboards",
    ],
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "dashboard_py_gapic_test",
    srcs = [
        "dashboard_py_gapic_pytest.py",
        "dashboard_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":dashboard_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "monitoring-dashboard-v1-py",
    deps = [
        ":dashboard_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "dashboard_php_proto",
    deps = [":dashboard_proto"],
)

php_gapic_library(
    name = "dashboard_php_gapic",
    srcs = [":dashboard_proto_with_info"],
    grpc_service_config = "dashboards_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc+rest",
    deps = [
        ":dashboard_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-monitoring-dashboard-v1-php",
    deps = [
        ":dashboard_php_gapic",
        ":dashboard_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "dashboard_nodejs_gapic",
    package_name = "@google-cloud/monitoring-dashboards",
    src = ":dashboard_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "dashboards_grpc_service_config.json",
    package = "google.monitoring.dashboard.v1",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "monitoring-dashboard-v1-nodejs",
    deps = [
        ":dashboard_nodejs_gapic",
        ":dashboard_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "dashboard_ruby_proto",
    deps = [":dashboard_proto"],
)

ruby_grpc_library(
    name = "dashboard_ruby_grpc",
    srcs = [":dashboard_proto"],
    deps = [":dashboard_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "dashboard_ruby_gapic",
    srcs = [":dashboard_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=monitoring.googleapis.com",
        "ruby-cloud-api-shortname=monitoring",
        "ruby-cloud-env-prefix=MONITORING_DASHBOARD",
        "ruby-cloud-gem-name=google-cloud-monitoring-dashboard-v1",
        "ruby-cloud-product-url=https://cloud.google.com/monitoring",
        "ruby-cloud-wrapper-gem-override=google-cloud-monitoring",
    ],
    grpc_service_config = "dashboards_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Monitoring collects metrics, events, and metadata from Google Cloud, Amazon Web Services (AWS), hosted uptime probes, and application instrumentation. The Dashboards API manages arrangements of display widgets.",
    ruby_cloud_title = "Cloud Monitoring Dashboards V1",
    service_yaml = "monitoring.yaml",
    transport = "grpc+rest",
    deps = [
        ":dashboard_ruby_grpc",
        ":dashboard_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-monitoring-dashboard-v1-ruby",
    deps = [
        ":dashboard_ruby_gapic",
        ":dashboard_ruby_grpc",
        ":dashboard_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "dashboard_csharp_proto",
    deps = [":dashboard_proto"],
)

csharp_grpc_library(
    name = "dashboard_csharp_grpc",
    srcs = [":dashboard_proto"],
    deps = [":dashboard_csharp_proto"],
)

csharp_gapic_library(
    name = "dashboard_csharp_gapic",
    srcs = [":dashboard_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "dashboards_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc+rest",
    deps = [
        ":dashboard_csharp_grpc",
        ":dashboard_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-monitoring-dashboard-v1-csharp",
    deps = [
        ":dashboard_csharp_gapic",
        ":dashboard_csharp_grpc",
        ":dashboard_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "dashboard_cc_proto",
    deps = [":dashboard_proto"],
)

cc_grpc_library(
    name = "dashboard_cc_grpc",
    srcs = [":dashboard_proto"],
    grpc_only = True,
    deps = [":dashboard_cc_proto"],
)
