// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/monitoring/dashboard/v1/metrics.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "ScorecardProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A widget showing the latest value of a metric, and how this value relates to
// one or more thresholds.
message Scorecard {
  // A gauge chart shows where the current value sits within a pre-defined
  // range. The upper and lower bounds should define the possible range of
  // values for the scorecard's query (inclusive).
  message GaugeView {
    // The lower bound for this gauge chart. The value of the chart should
    // always be greater than or equal to this.
    double lower_bound = 1;

    // The upper bound for this gauge chart. The value of the chart should
    // always be less than or equal to this.
    double upper_bound = 2;
  }

  // A sparkChart is a small chart suitable for inclusion in a table-cell or
  // inline in text. This message contains the configuration for a sparkChart
  // to show up on a Scorecard, showing recent trends of the scorecard's
  // timeseries.
  message SparkChartView {
    // Required. The type of sparkchart to show in this chartView.
    SparkChartType spark_chart_type = 1
        [(google.api.field_behavior) = REQUIRED];

    // The lower bound on data point frequency in the chart implemented by
    // specifying the minimum alignment period to use in a time series query.
    // For example, if the data is published once every 10 minutes it would not
    // make sense to fetch and align data at one minute intervals. This field is
    // optional and exists only as a hint.
    google.protobuf.Duration min_alignment_period = 2;
  }

  // Required. Fields for querying time series data from the
  // Stackdriver metrics API.
  TimeSeriesQuery time_series_query = 1
      [(google.api.field_behavior) = REQUIRED];

  // Defines the optional additional chart shown on the scorecard. If
  // neither is included - then a default scorecard is shown.
  oneof data_view {
    // Will cause the scorecard to show a gauge chart.
    GaugeView gauge_view = 4;

    // Will cause the scorecard to show a spark chart.
    SparkChartView spark_chart_view = 5;

    // Will cause the `Scorecard` to show only the value, with no indicator to
    // its value relative to its thresholds.
    google.protobuf.Empty blank_view = 7;
  }

  // The thresholds used to determine the state of the scorecard given the
  // time series' current value. For an actual value x, the scorecard is in a
  // danger state if x is less than or equal to a danger threshold that triggers
  // below, or greater than or equal to a danger threshold that triggers above.
  // Similarly, if x is above/below a warning threshold that triggers
  // above/below, then the scorecard is in a warning state - unless x also puts
  // it in a danger state. (Danger trumps warning.)
  //
  // As an example, consider a scorecard with the following four thresholds:
  //
  // ```
  // {
  //   value: 90,
  //   category: 'DANGER',
  //   trigger: 'ABOVE',
  // },
  // {
  //   value: 70,
  //   category: 'WARNING',
  //   trigger: 'ABOVE',
  // },
  // {
  //   value: 10,
  //   category: 'DANGER',
  //   trigger: 'BELOW',
  // },
  // {
  //   value: 20,
  //   category: 'WARNING',
  //   trigger: 'BELOW',
  // }
  // ```
  //
  // Then: values less than or equal to 10 would put the scorecard in a DANGER
  // state, values greater than 10 but less than or equal to 20 a WARNING state,
  // values strictly between 20 and 70 an OK state, values greater than or equal
  // to 70 but less than 90 a WARNING state, and values greater than or equal to
  // 90 a DANGER state.
  repeated Threshold thresholds = 6;
}
