type: google.api.Service
config_version: 3
name: monitoring.googleapis.com
title: Cloud Monitoring API

apis:
- name: google.monitoring.dashboard.v1.DashboardsService

documentation:
  summary: Manages your Cloud Monitoring data and configurations.

authentication:
  rules:
  - selector: 'google.monitoring.dashboard.v1.DashboardsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.write
  - selector: google.monitoring.dashboard.v1.DashboardsService.GetDashboard
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.dashboard.v1.DashboardsService.ListDashboards
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
