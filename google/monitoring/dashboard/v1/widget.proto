// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/monitoring/dashboard/v1/alertchart.proto";
import "google/monitoring/dashboard/v1/collapsible_group.proto";
import "google/monitoring/dashboard/v1/error_reporting_panel.proto";
import "google/monitoring/dashboard/v1/incident_list.proto";
import "google/monitoring/dashboard/v1/logs_panel.proto";
import "google/monitoring/dashboard/v1/piechart.proto";
import "google/monitoring/dashboard/v1/scorecard.proto";
import "google/monitoring/dashboard/v1/section_header.proto";
import "google/monitoring/dashboard/v1/single_view_group.proto";
import "google/monitoring/dashboard/v1/table.proto";
import "google/monitoring/dashboard/v1/text.proto";
import "google/monitoring/dashboard/v1/xychart.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "WidgetProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// Widget contains a single dashboard component and configuration of how to
// present the component in the dashboard.
message Widget {
  // Optional. The title of the widget.
  string title = 1 [(google.api.field_behavior) = OPTIONAL];

  // Content defines the component used to populate the widget.
  oneof content {
    // A chart of time series data.
    XyChart xy_chart = 2;

    // A scorecard summarizing time series data.
    Scorecard scorecard = 3;

    // A raw string or markdown displaying textual content.
    Text text = 4;

    // A blank space.
    google.protobuf.Empty blank = 5;

    // A chart of alert policy data.
    AlertChart alert_chart = 7;

    // A widget that displays time series data in a tabular format.
    TimeSeriesTable time_series_table = 8;

    // A widget that groups the other widgets. All widgets that are within
    // the area spanned by the grouping widget are considered member widgets.
    CollapsibleGroup collapsible_group = 9;

    // A widget that shows a stream of logs.
    LogsPanel logs_panel = 10;

    // A widget that shows list of incidents.
    IncidentList incident_list = 12;

    // A widget that displays timeseries data as a pie chart.
    PieChart pie_chart = 14;

    // A widget that displays a list of error groups.
    ErrorReportingPanel error_reporting_panel = 19;

    // A widget that defines a section header for easier navigation of the
    // dashboard.
    SectionHeader section_header = 21;

    // A widget that groups the other widgets by using a dropdown menu.
    SingleViewGroup single_view_group = 22;
  }

  // Optional. The widget id. Ids may be made up of alphanumerics, dashes and
  // underscores. Widget ids are optional.
  string id = 17 [(google.api.field_behavior) = OPTIONAL];
}
