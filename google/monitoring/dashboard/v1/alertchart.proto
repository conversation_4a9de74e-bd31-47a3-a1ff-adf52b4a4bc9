// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "AlertChartProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A chart that displays alert policy data.
message AlertChart {
  option (google.api.resource) = {
    type: "monitoring.googleapis.com/AlertPolicy"
    pattern: "projects/{project}/alertPolicies/{alert_policy}"
  };

  // Required. The resource name of the alert policy. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]/alertPolicies/[ALERT_POLICY_ID]
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}
