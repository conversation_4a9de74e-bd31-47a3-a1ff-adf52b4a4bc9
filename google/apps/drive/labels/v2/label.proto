// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/apps/drive/labels/v2/common.proto";
import "google/apps/drive/labels/v2/field.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2;labels";
option java_multiple_files = true;
option java_outer_classname = "LabelProto";
option java_package = "com.google.apps.drive.labels.v2";
option objc_class_prefix = "DLBL";
option (google.api.resource_definition) = {
  type: "cloudidentity.googleapis.com/Customer"
  pattern: "customers/{customer}"
};

// A label defines a taxonomy that can be applied to Drive items in order to
// organize and search across items. Labels can be simple strings, or can
// contain fields that describe additional metadata that can be further used to
// organize and search Drive items.
message Label {
  option (google.api.resource) = {
    type: "drivelabels.googleapis.com/Label"
    pattern: "labels/{id}"
  };

  // Basic properties of the label.
  message Properties {
    // Required. Title of the label.
    string title = 1 [(google.api.field_behavior) = REQUIRED];

    // The description of the label.
    string description = 2;
  }

  // UI display hints for rendering the label.
  message DisplayHints {
    // Whether the label should be shown in the UI as disabled.
    bool disabled = 1;

    // This label should be hidden in the search menu when searching for Drive
    // items.
    bool hidden_in_search = 2;

    // This label should be shown in the apply menu when applying values to a
    // Drive item.
    bool shown_in_apply = 3;

    // Order to display label in a list.
    int64 priority = 4;
  }

  // The capabilities a user has on this label's applied metadata.
  message AppliedCapabilities {
    // Whether the user can read applied metadata related to this label.
    bool can_read = 1;

    // Whether the user can apply this label to items.
    bool can_apply = 2;

    // Whether the user can remove this label from items.
    bool can_remove = 3;
  }

  // The capabilities related to this label when editing the label.
  message SchemaCapabilities {
    // Whether the user can change this label.
    bool can_update = 1;

    // Whether the user can delete this label.
    // The user must have permission and the label must be disabled.
    bool can_delete = 2;

    // Whether the user can disable this label.
    // The user must have permission and this label must not already be
    // disabled.
    bool can_disable = 3;

    // Whether the user can enable this label.
    // The user must have permission and this label must be disabled.
    bool can_enable = 4;
  }

  // Behavior of this label when it's applied to Drive items.
  message AppliedLabelPolicy {
    // Indicates how the applied label and field values should be copied when
    // a Drive item is copied.
    enum CopyMode {
      // Copy mode unspecified.
      COPY_MODE_UNSPECIFIED = 0;

      // The applied label and field values are not copied by default when
      // the Drive item it's applied to is copied.
      DO_NOT_COPY = 1;

      // The applied label and field values are always copied when the
      // Drive item it's applied to is copied.
      // Only admins can use this mode.
      ALWAYS_COPY = 2;

      // The applied label and field values are copied if the
      // label is appliable by the user making the copy.
      COPY_APPLIABLE = 3;
    }

    // Indicates how the applied label and field values should be copied when
    // a Drive item is copied.
    CopyMode copy_mode = 1;
  }

  // The type of this label.
  enum LabelType {
    // Unknown label type.
    LABEL_TYPE_UNSPECIFIED = 0;

    // Shared labels may be shared with users to apply to Drive items.
    SHARED = 1;

    // Admin-owned label. Only creatable and editable by admins. Supports some
    // additional admin-only features.
    ADMIN = 2;

    // A label owned by an internal Google application rather than a customer.
    // These labels are read-only.
    GOOGLE_APP = 3;
  }

  // Output only. Resource name of the label. Will be in the form of either:
  // `labels/{id}` or `labels/{id}@{revision_id}` depending on the request.
  // See `id` and `revision_id` below.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Globally unique identifier of this label. ID makes up part of
  // the label `name`, but unlike `name`, ID is consistent between revisions.
  // Matches the regex: `([a-zA-Z0-9])+`
  string id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Revision ID of the label. Revision ID might be part of the
  // label `name` depending on the request issued. A new revision is created
  // whenever revisioned properties of a label are changed. Matches the regex:
  // `([a-zA-Z0-9])+`
  string revision_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type of label.
  LabelType label_type = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. The user who created this label.
  UserInfo creator = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this label was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who created this label revision.
  UserInfo revision_creator = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this label revision was created.
  google.protobuf.Timestamp revision_create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who published this label.  This value has no meaning
  // when the label is not published.
  UserInfo publisher = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this label was published. This value has no meaning
  // when the label is not published.
  google.protobuf.Timestamp publish_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who disabled this label. This value has no meaning
  // when the label is not disabled.
  UserInfo disabler = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this label was disabled. This value has no meaning
  // when the label is not disabled.
  google.protobuf.Timestamp disable_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The customer this label belongs to.
  // For example: "customers/123abc789."
  string customer = 13 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudidentity.googleapis.com/Customer"
    }
  ];

  // Required. The basic properties of the label.
  Properties properties = 14 [(google.api.field_behavior) = REQUIRED];

  // Output only. The lifecycle state of the label including whether it's
  // published, deprecated, and has draft changes.
  Lifecycle lifecycle = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. UI display hints for rendering the label.
  DisplayHints display_hints = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The capabilities related to this label on applied metadata.
  AppliedCapabilities applied_capabilities = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The capabilities the user has on this label.
  SchemaCapabilities schema_capabilities = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Behavior of this label when it's applied to Drive items.
  AppliedLabelPolicy applied_label_policy = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of fields in descending priority order.
  repeated Field fields = 20;

  // Custom URL to present to users to allow them to learn more about this label
  // and how it should be used.
  string learn_more_uri = 21;

  // Output only. The LockStatus of this label.
  LockStatus lock_status = 22 [(google.api.field_behavior) = OUTPUT_ONLY];
}
