// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2;

import "google/api/field_behavior.proto";
import "google/apps/drive/labels/v2/common.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2;labels";
option java_multiple_files = true;
option java_outer_classname = "FieldProto";
option java_package = "com.google.apps.drive.labels.v2";
option objc_class_prefix = "DLBL";

// Defines a field that has a display name, data type, and other
// configuration options. This field defines the kind of metadata that may be
// set on a Drive item.
message Field {
  // The basic properties of the field.
  message Properties {
    // Required. The display text to show in the UI identifying this field.
    string display_name = 1 [(google.api.field_behavior) = REQUIRED];

    // Whether the field should be marked as required.
    bool required = 2;

    // Input only. Insert or move this field before the indicated field. If
    // empty, the field is placed at the end of the list.
    string insert_before_field = 3 [(google.api.field_behavior) = INPUT_ONLY];
  }

  // UI display hints for rendering a field.
  message DisplayHints {
    // Whether the field should be shown as required in the UI.
    bool required = 1;

    // Whether the field should be shown in the UI as disabled.
    bool disabled = 2;

    // This field should be hidden in the search menu when searching for Drive
    // items.
    bool hidden_in_search = 3;

    // This field should be shown in the apply menu when applying values to a
    // Drive item.
    bool shown_in_apply = 4;
  }

  // The capabilities related to this field when editing the field.
  message SchemaCapabilities {
    // Whether the user can change this field.
    bool can_update = 1;

    // Whether the user can delete this field.
    // The user must have permission and the field must be deprecated.
    bool can_delete = 2;

    // Whether the user can disable this field.
    // The user must have permission and this field must not already be
    // disabled.
    bool can_disable = 3;

    // Whether the user can enable this field.
    // The user must have permission and this field must be disabled.
    bool can_enable = 4;
  }

  // The capabilities related to this field on applied metadata.
  message AppliedCapabilities {
    // Whether the user can read related applied metadata on items.
    bool can_read = 1;

    // Whether the user can search for Drive items referencing this field.
    bool can_search = 2;

    // Whether the user can set this field on Drive items.
    bool can_write = 3;
  }

  // Options for a multi-valued variant of an associated field type.
  message ListOptions {
    // Maximum number of entries permitted.
    int32 max_entries = 1;
  }

  // Options for the Text field type.
  message TextOptions {
    // Output only. The minimum valid length of values for the text field.
    int32 min_length = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The maximum valid length of values for the text field.
    int32 max_length = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Options for the Integer field type.
  message IntegerOptions {
    // Output only. The minimum valid value for the integer field.
    int64 min_value = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The maximum valid value for the integer field.
    int64 max_value = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Options for the date field type.
  message DateOptions {
    // Localized date format options.
    enum DateFormat {
      // Date format unspecified.
      DATE_FORMAT_UNSPECIFIED = 0;

      // Includes full month name.
      // For example, January 12, 1999
      // (MMMM d, y)
      LONG_DATE = 1;

      // Short, numeric, representation.
      // For example, 12/13/99
      // (M/d/yy)
      SHORT_DATE = 2;
    }

    // Localized date formatting option. Field values are rendered in
    // this format according to their locale.
    DateFormat date_format_type = 1;

    // Output only. ICU date format.
    string date_format = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Minimum valid value (year, month, day).
    google.type.Date min_value = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Maximum valid value (year, month, day).
    google.type.Date max_value = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Options for the selection field type.
  message SelectionOptions {
    // Selection field choice.
    message Choice {
      // Basic properties of the choice.
      message Properties {
        // Required. The display text to show in the UI identifying this field.
        string display_name = 1 [(google.api.field_behavior) = REQUIRED];

        // The description of this label.
        string description = 2;

        // The badge configuration for this choice. When set, the
        // label that owns this choice is considered a "badged label".
        BadgeConfig badge_config = 3;

        // Input only. Insert or move this choice before the indicated choice.
        // If empty, the choice is placed at the end of the list.
        string insert_before_choice = 4
            [(google.api.field_behavior) = INPUT_ONLY];
      }

      // UI display hints for rendering an option.
      message DisplayHints {
        // Whether the option should be shown in the UI as disabled.
        bool disabled = 1;

        // This option should be hidden in the search menu when searching for
        // Drive items.
        bool hidden_in_search = 2;

        // This option should be shown in the apply menu when applying values to
        // a Drive item.
        bool shown_in_apply = 3;

        // The colors to use for the badge. Changed to Google Material colors
        // based on the chosen `properties.badge_config.color`.
        BadgeColors badge_colors = 4;

        // The dark-mode color to use for the badge. Changed to Google Material
        // colors based on the chosen `properties.badge_config.color`.
        BadgeColors dark_badge_colors = 5;

        // The priority of this badge. Used to compare and sort between multiple
        // badges. A lower number means the badge should be shown first.
        // When a badging configuration is not present, this will be 0.
        // Otherwise, this will be set to `BadgeConfig.priority_override` or the
        // default heuristic which prefers creation date of the label, and field
        // and option priority.
        int64 badge_priority = 6;
      }

      // The capabilities related to this choice when editing the choice.
      message SchemaCapabilities {
        // Whether the user can update this choice.
        bool can_update = 1;

        // Whether the user can delete this choice.
        bool can_delete = 2;

        // Whether the user can disable this choice.
        bool can_disable = 3;

        // Whether the user can enable this choice.
        bool can_enable = 4;
      }

      // The capabilities related to this choice on applied metadata.
      message AppliedCapabilities {
        // Whether the user can read related applied metadata on items.
        bool can_read = 1;

        // Whether the user can use this choice in search queries.
        bool can_search = 2;

        // Whether the user can select this choice on an item.
        bool can_select = 3;
      }

      // The unique value of the choice.
      // This ID is autogenerated. Matches the regex: `([a-zA-Z0-9_])+`.
      string id = 1;

      // Basic properties of the choice.
      Properties properties = 2;

      // Output only. Lifecycle of the choice.
      Lifecycle lifecycle = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. UI display hints for rendering a choice.
      DisplayHints display_hints = 4
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The capabilities related to this option when editing the
      // option.
      SchemaCapabilities schema_capabilities = 5
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The capabilities related to this choice on applied
      // metadata.
      AppliedCapabilities applied_capabilities = 6
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The user who created this choice.
      UserInfo creator = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The time this choice was created.
      google.protobuf.Timestamp create_time = 8
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The user who updated this choice last.
      UserInfo updater = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The time this choice was updated last.
      google.protobuf.Timestamp update_time = 10
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The user who published this choice. This value has no
      // meaning when the choice is not published.
      UserInfo publisher = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The time this choice was published. This value has no
      // meaning when the choice is not published.
      google.protobuf.Timestamp publish_time = 12
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The user who disabled this choice. This value has no
      // meaning when the option is not disabled.
      UserInfo disabler = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The time this choice was disabled. This value has no
      // meaning when the choice is not disabled.
      google.protobuf.Timestamp disable_time = 14
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The LockStatus of this choice.
      LockStatus lock_status = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // When specified, indicates this field supports a list of values.
    // Once the field is published, this cannot be changed.
    ListOptions list_options = 1;

    // The options available for this selection field.
    // The list order is consistent, and modified with `insert_before_choice`.
    repeated Choice choices = 2;
  }

  // Options for the user field type.
  message UserOptions {
    // When specified, indicates that this field supports a list of values.
    // Once the field is published, this cannot be changed.
    ListOptions list_options = 1;
  }

  // The data type and options of this field.
  // Once published, the data type cannot be changed.
  oneof type {
    // Text field options.
    TextOptions text_options = 16;

    // Integer field options.
    IntegerOptions integer_options = 18;

    // Date field options.
    DateOptions date_options = 19;

    // Selection field options.
    SelectionOptions selection_options = 20;

    // User field options.
    UserOptions user_options = 21;
  }

  // Output only. The key of a field, unique within a label or library.
  //
  // This value is autogenerated. Matches the regex: `([a-zA-Z0-9])+`
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The key to use when constructing Drive search queries to find
  // files based on values defined for this field on files.
  // For example, "`{query_key}` > 2001-01-01".
  string query_key = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The basic properties of the field.
  Properties properties = 3;

  // Output only. The lifecycle of this field.
  Lifecycle lifecycle = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. UI display hints for rendering a field.
  DisplayHints display_hints = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The capabilities this user has when editing this field.
  SchemaCapabilities schema_capabilities = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The capabilities this user has on this field and its value
  // when the label is applied on Drive items.
  AppliedCapabilities applied_capabilities = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who created this field.
  UserInfo creator = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this field was created.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who modified this field.
  UserInfo updater = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this field was updated.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who published this field. This value has no meaning
  // when the field is not published.
  UserInfo publisher = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who disabled this field. This value has no meaning
  // when the field is not disabled.
  UserInfo disabler = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this field was disabled. This value has no meaning
  // when the field is not disabled.
  google.protobuf.Timestamp disable_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The LockStatus of this field.
  LockStatus lock_status = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}
