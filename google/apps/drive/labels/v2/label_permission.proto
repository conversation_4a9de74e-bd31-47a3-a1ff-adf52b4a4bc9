// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2;

import "google/api/resource.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2;labels";
option java_multiple_files = true;
option java_outer_classname = "LabelPermissionProto";
option java_package = "com.google.apps.drive.labels.v2";
option objc_class_prefix = "DLBL";
option (google.api.resource_definition) = {
  type: "groups.googleapis.com/Group"
  pattern: "groups/{group}"
};

// The permission that applies to a principal (user, group, audience) on a
// label.
message LabelPermission {
  option (google.api.resource) = {
    type: "drivelabels.googleapis.com/LabelPermission"
    pattern: "labels/{label}/permissions/{permission}"
  };

  // Roles are concentric with subsequent role.
  enum LabelRole {
    // Unknown role.
    LABEL_ROLE_UNSPECIFIED = 0;

    // A reader can read the label and associated metadata applied to Drive
    // items.
    READER = 1;

    // An applier can write associated metadata on Drive items in which they
    // also have write access to. Implies `READER`.
    APPLIER = 2;

    // An organizer can pin this label in shared drives they manage
    // and add new appliers to the label.
    ORGANIZER = 3;

    // Editors can make any update including deleting the label which
    // also deletes the associated Drive item metadata. Implies `APPLIER`.
    EDITOR = 4;
  }

  // The principal this permission applies to. Must be either an email, user,
  // group, or audience.
  // Example:
  // * people/12345
  // * groups/45678
  // * audiences/default
  oneof principal {
    // Person resource name.
    string person = 3 [
      (google.api.resource_reference) = { type: "people.googleapis.com/Person" }
    ];

    // Group resource name.
    string group = 4 [
      (google.api.resource_reference) = { type: "groups.googleapis.com/Group" }
    ];

    // Audience to grant a role to. The magic value of `audiences/default` may
    // be used to apply the role to the default audience in the context of the
    // organization that owns the Label.
    string audience = 5;
  }

  // Resource name of this permission.
  string name = 1;

  // Specifies the email address for a user or group pricinpal. Not populated
  // for audience principals. User and Group permissions may only be inserted
  // using email address. On update requests, if email address is specified,
  // no principal should be specified.
  string email = 2;

  // The role the principal should have.
  LabelRole role = 6;
}
