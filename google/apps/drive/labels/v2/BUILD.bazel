# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "labels_proto",
    srcs = [
        "common.proto",
        "error_details.proto",
        "exception_detail.proto",
        "field.proto",
        "label.proto",
        "label_limits.proto",
        "label_lock.proto",
        "label_permission.proto",
        "label_service.proto",
        "requests.proto",
        "user_capabilities.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/type:color_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "labels_proto_with_info",
    deps = [
        ":labels_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "labels_java_proto",
    deps = [":labels_proto"],
)

java_grpc_library(
    name = "labels_java_grpc",
    srcs = [":labels_proto"],
    deps = [":labels_java_proto"],
)

java_gapic_library(
    name = "labels_java_gapic",
    srcs = [":labels_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "drivelabels_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "drivelabels_v2.yaml",
    test_deps = [
        ":labels_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":labels_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "labels_java_gapic_test_suite",
    test_classes = [
        "com.google.apps.drive.labels.v2.LabelServiceClientHttpJsonTest",
        "com.google.apps.drive.labels.v2.LabelServiceClientTest",
    ],
    runtime_deps = [":labels_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-drive-labels-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":labels_java_gapic",
        ":labels_java_grpc",
        ":labels_java_proto",
        ":labels_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "labels_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/apps/drive/labels/v2",
    protos = [":labels_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:color_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "labels_go_gapic",
    srcs = [":labels_proto_with_info"],
    grpc_service_config = "drivelabels_v2_grpc_service_config.json",
    importpath = "google.golang.org/google/apps/drive/labels/v2;labels",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "drivelabels_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":labels_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-drive-labels-v2-go",
    deps = [
        ":labels_go_gapic",
        ":labels_go_gapic_srcjar-metadata.srcjar",
        ":labels_go_gapic_srcjar-snippets.srcjar",
        ":labels_go_gapic_srcjar-test.srcjar",
        ":labels_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "labels_py_gapic",
    srcs = [":labels_proto"],
    grpc_service_config = "drivelabels_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "drivelabels_v2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "labels_py_gapic_test",
    srcs = [
        "labels_py_gapic_pytest.py",
        "labels_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":labels_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "drive-labels-v2-py",
    deps = [
        ":labels_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "labels_php_proto",
    deps = [":labels_proto"],
)

php_gapic_library(
    name = "labels_php_gapic",
    srcs = [":labels_proto_with_info"],
    grpc_service_config = "drivelabels_v2_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "drivelabels_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":labels_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-drive-labels-v2-php",
    deps = [
        ":labels_php_gapic",
        ":labels_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "labels_nodejs_gapic",
    package_name = "@google-cloud/labels",
    src = ":labels_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "drivelabels_v2_grpc_service_config.json",
    package = "google.apps.drive.labels.v2",
    rest_numeric_enums = True,
    service_yaml = "drivelabels_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "drive-labels-v2-nodejs",
    deps = [
        ":labels_nodejs_gapic",
        ":labels_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "labels_ruby_proto",
    deps = [":labels_proto"],
)

ruby_grpc_library(
    name = "labels_ruby_grpc",
    srcs = [":labels_proto"],
    deps = [":labels_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "labels_ruby_gapic",
    srcs = [":labels_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-drive-labels-v2"],
    grpc_service_config = "drivelabels_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "drivelabels_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":labels_ruby_grpc",
        ":labels_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-drive-labels-v2-ruby",
    deps = [
        ":labels_ruby_gapic",
        ":labels_ruby_grpc",
        ":labels_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "labels_csharp_proto",
    extra_opts = [],
    deps = [":labels_proto"],
)

csharp_grpc_library(
    name = "labels_csharp_grpc",
    srcs = [":labels_proto"],
    deps = [":labels_csharp_proto"],
)

csharp_gapic_library(
    name = "labels_csharp_gapic",
    srcs = [":labels_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "drivelabels_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "drivelabels_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":labels_csharp_grpc",
        ":labels_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-drive-labels-v2-csharp",
    deps = [
        ":labels_csharp_gapic",
        ":labels_csharp_grpc",
        ":labels_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "labels_cc_proto",
    deps = [":labels_proto"],
)

cc_grpc_library(
    name = "labels_cc_grpc",
    srcs = [":labels_proto"],
    grpc_only = True,
    deps = [":labels_cc_proto"],
)
