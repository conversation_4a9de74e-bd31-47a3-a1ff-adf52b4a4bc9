// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/type/color.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2;labels";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.apps.drive.labels.v2";
option objc_class_prefix = "DLBL";
option (google.api.resource_definition) = {
  type: "people.googleapis.com/Person"
  pattern: "persons/{person}"
};

// The lifecycle state of an object, such as label, field, or choice. The
// lifecycle enforces the following transitions:
//
// * `UNPUBLISHED_DRAFT` (starting state)
// * `UNPUBLISHED_DRAFT` -> `PUBLISHED`
// * `UNPUBLISHED_DRAFT` -> (Deleted)
// * `PUBLISHED` -> `DISABLED`
// * `DISABLED` -> `PUBLISHED`
// * `DISABLED` -> (Deleted)
//
// The published and disabled states have some distinct characteristics:
//
// * Published—Some kinds of changes might be made to an object in this state,
//   in which case `has_unpublished_changes` will be true. Also, some kinds of
//   changes are not permitted. Generally, any change that would invalidate or
//   cause new restrictions on existing metadata related to the label are
//   rejected.
// * Disabled—When disabled, the configured `DisabledPolicy` takes effect.
message Lifecycle {
  // The policy that governs how to treat a disabled label, field, or selection
  // choice in different contexts.
  message DisabledPolicy {
    // Whether to hide this disabled object in the search menu for Drive items.
    //
    // * When `false`, the object is generally shown in the UI as disabled but
    // it appears in the search results when searching for Drive items.
    // * When `true`, the object is generally hidden in the UI when
    //   searching for Drive items.
    bool hide_in_search = 1;

    // Whether to show this disabled object in the apply menu on Drive items.
    //
    // * When `true`, the object is generally shown in the UI as disabled
    //   and is unselectable.
    // * When `false`, the object is generally hidden in the UI.
    bool show_in_apply = 2;
  }

  // The state of the object associated with this lifecycle.
  enum State {
    // Unknown State.
    STATE_UNSPECIFIED = 0;

    // The initial state of an object. Once published, the object can never
    // return to this state. Once an object is published, certain kinds of
    // changes are no longer permitted.
    UNPUBLISHED_DRAFT = 1;

    // The object has been published. The object might have unpublished draft
    // changes as indicated by `has_unpublished_changes`.
    PUBLISHED = 2;

    // The object has been published and has since been disabled. The object
    // might have unpublished draft changes as indicated by
    // `has_unpublished_changes`.
    DISABLED = 3;

    // The object has been deleted.
    DELETED = 4;
  }

  // Output only. The state of the object associated with this lifecycle.
  State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the object associated with this lifecycle has
  // unpublished changes.
  bool has_unpublished_changes = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The policy that governs how to show a disabled label, field, or selection
  // choice.
  DisabledPolicy disabled_policy = 3;
}

// Information about a user.
message UserInfo {
  // The identifier for this user that can be used with the People API to get
  // more information.
  // For example, people/12345678.
  string person = 1 [
    (google.api.resource_reference) = { type: "people.googleapis.com/Person" }
  ];
}

// Badge status of the label.
message BadgeConfig {
  // The color of the badge. When not specified, no badge is rendered.
  // The background, foreground, and solo (light and dark mode) colors set here
  // are changed in the Drive UI into the closest recommended supported color.
  google.type.Color color = 1;

  // Override the default global priority of this badge.
  // When set to 0, the default priority heuristic is used.
  int64 priority_override = 2;
}

// The color derived from BadgeConfig and changed to the closest recommended
// supported color.
message BadgeColors {
  // Output only. Badge background that pairs with the foreground.
  google.type.Color background_color = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Badge foreground that pairs with the background.
  google.type.Color foreground_color = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Color that can be used for text without a background.
  google.type.Color solo_color = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains information about whether a label component should be considered
// locked.
message LockStatus {
  // Output only. Indicates whether this label component is the (direct) target
  // of a LabelLock.  A label component can be implicitly locked even if it's
  // not the direct target of a LabelLock, in which case this field is set to
  // false.
  bool locked = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}
