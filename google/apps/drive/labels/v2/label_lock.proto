// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/apps/drive/labels/v2/common.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2;labels";
option java_multiple_files = true;
option java_outer_classname = "LabelLockProto";
option java_package = "com.google.apps.drive.labels.v2";
option objc_class_prefix = "DLBL";

// A Lock that can be applied to a Label, Field, or Choice.
message LabelLock {
  option (google.api.resource) = {
    type: "drivelabels.googleapis.com/LabelLock"
    pattern: "labels/{label}/locks/{lock}"
  };

  // A description of a user's capabilities on a LabelLock.
  message Capabilities {
    // True if the user is authorized to view the policy.
    bool can_view_policy = 1;
  }

  // A description of a LabelLock's state.
  enum State {
    // Unknown state.
    STATE_UNSPECIFIED = 0;

    // The LabelLock is active and is being enforced by the server.
    ACTIVE = 1;

    // The LabelLock is being deleted.  The LabelLock will continue to be
    // enforced by the server until it has been fully removed.
    DELETING = 2;
  }

  // Output only. Resource name of this LabelLock.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The ID of the Field that should be locked.  Empty if the whole
  // Label should be locked.
  string field_id = 2;

  // The ID of the Selection Field Choice that should be locked.  If present,
  // `field_id` must also be present.
  string choice_id = 3;

  // Output only. The time this LabelLock was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user whose credentials were used to create the LabelLock.
  // This will not be present if no user was responsible for creating the
  // LabelLock.
  UserInfo creator = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A timestamp indicating when this LabelLock was scheduled for
  // deletion. This will be present only if this LabelLock is in the DELETING
  // state.
  google.protobuf.Timestamp delete_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user's capabilities on this LabelLock.
  Capabilities capabilities = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This LabelLock's state.
  State state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}
