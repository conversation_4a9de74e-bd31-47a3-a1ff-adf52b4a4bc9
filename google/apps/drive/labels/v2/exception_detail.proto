// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2;

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2;labels";
option java_multiple_files = true;
option java_outer_classname = "ExceptionDetailProto";
option java_package = "com.google.apps.drive.labels.v2";

// Every ExceptionType maps to one and only one Exception class. This allows
// internal clients to identify the exact server exception that caused the
// error for debugging and logging purposes.
// Add new ExceptionTypes to EXCEPTION_TYPE_TO_ERROR_CODE_MAP in
// j/c/g/apps/boq/metadata/model/service/exceptions/CategoryExceptionHelper
enum ExceptionType {
  // Unknown ExceptionType.
  EXCEPTION_TYPE_UNSPECIFIED = 0;

  // The required field is missing.
  FIELD_REQUIRED = 1;

  // Unable to create a metamodel with the given ID because it already exists.
  METAMODEL_ALREADY_EXISTS = 2;

  // Metamodel was not found
  METAMODEL_NOT_FOUND = 3;

  // Metamodel state transition isn't allowed.
  ILLEGAL_METAMODEL_STATE_TRANSITION = 4;

  // Metamodel deprecation policy is invalid.
  INVALID_METAMODEL_DEPRECATION_POLICY = 5;

  // Cannot delete a metamodel due to the pending deprecation policy.
  METAMODEL_DELETION_DENIED_UNTIL = 6;

  // A Field value is invalid.
  INVALID_FIELD = 7;

  // Precondition failed when updating a metamodel
  METAMODEL_PRECONDITION_FAILED = 8;

  // Multiple fields had the same key.
  DUPLICATE_FIELD_KEY = 9;

  // Removing a field from a Metamodel (e.g. a published Metamodel) is not
  // permitted.
  ILLEGAL_FIELD_REMOVAL = 10;

  // Cannot specify field options for a different field type.
  ILLEGAL_FIELD_OPTIONS_FOR_FIELD = 11;

  // Some changes are not supported
  UNSUPPORTED_CHANGE_TO_PUBLISHED_METAMODEL = 12;

  // Cannot change the metamodel state in an update
  ILLEGAL_METAMODEL_STATE_TRANSITION_IN_UPDATE = 13;

  // The page token is expired
  PAGE_TOKEN_EXPIRED = 14;

  // The user is not authorized to make the request.
  NOT_AUTHORIZED = 15;

  // Illegal field state transition
  ILLEGAL_FIELD_STATE_TRANSITION = 16;

  // Illegal choice set option state transition
  ILLEGAL_CHOICE_SET_OPTION_STATE_TRANSITION = 17;

  // Invalid choice set options
  INVALID_CHOICE_SET_OPTIONS = 18;

  // Invalid field key
  INVALID_FIELD_KEY = 19;

  // A specified property on a field is outside the allowed range.
  INVALID_FIELD_PROPERTY_RANGE = 20;

  // A localized string wasn't valid. This may be because the locale is invalid,
  // its missing a default value, or the translation is empty for a set locale.
  INVALID_LOCALIZED_STRING = 21;

  // cannot change a property on a published field
  ILLEGAL_CHANGE_TO_PUBLISHED_FIELD = 22;

  // A field update is not inclusive of the previous value
  INVALID_FIELD_UPDATE_NOT_INCLUSIVE = 23;

  // A field update is not inclusive of the previous value
  INVALID_CHOICE_SET_STATE = 24;

  // An unknown error occurred
  INTERNAL_SERVER_ERROR = 500;
}

// Exception detail.
message ExceptionDetail {
  // The type of exception that occurred. Required.
  ExceptionType error_type = 1;
}
