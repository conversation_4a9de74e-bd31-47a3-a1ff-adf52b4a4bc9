type: google.api.Service
config_version: 3
name: drivelabels.googleapis.com
title: Drive Labels API

apis:
- name: google.apps.drive.labels.v2beta.LabelService

documentation:
  summary: An API for managing Drive Labels

authentication:
  rules:
  - selector: 'google.apps.drive.labels.v2beta.LabelService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive.admin.labels,
        https://www.googleapis.com/auth/drive.labels
  - selector: google.apps.drive.labels.v2beta.LabelService.GetLabel
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive.admin.labels,
        https://www.googleapis.com/auth/drive.admin.labels.readonly,
        https://www.googleapis.com/auth/drive.labels,
        https://www.googleapis.com/auth/drive.labels.readonly
  - selector: google.apps.drive.labels.v2beta.LabelService.GetLabelLimits
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive.admin.labels,
        https://www.googleapis.com/auth/drive.admin.labels.readonly,
        https://www.googleapis.com/auth/drive.labels,
        https://www.googleapis.com/auth/drive.labels.readonly
  - selector: google.apps.drive.labels.v2beta.LabelService.GetUserCapabilities
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive.admin.labels,
        https://www.googleapis.com/auth/drive.admin.labels.readonly,
        https://www.googleapis.com/auth/drive.labels,
        https://www.googleapis.com/auth/drive.labels.readonly
  - selector: google.apps.drive.labels.v2beta.LabelService.ListLabelLocks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive.admin.labels,
        https://www.googleapis.com/auth/drive.admin.labels.readonly,
        https://www.googleapis.com/auth/drive.labels,
        https://www.googleapis.com/auth/drive.labels.readonly
  - selector: google.apps.drive.labels.v2beta.LabelService.ListLabelPermissions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive.admin.labels,
        https://www.googleapis.com/auth/drive.admin.labels.readonly,
        https://www.googleapis.com/auth/drive.labels,
        https://www.googleapis.com/auth/drive.labels.readonly
  - selector: google.apps.drive.labels.v2beta.LabelService.ListLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive.admin.labels,
        https://www.googleapis.com/auth/drive.admin.labels.readonly,
        https://www.googleapis.com/auth/drive.labels,
        https://www.googleapis.com/auth/drive.labels.readonly
