// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2beta;labels";
option java_multiple_files = true;
option java_outer_classname = "UserCapabilitiesProto";
option java_package = "com.google.apps.drive.labels.v2beta";
option objc_class_prefix = "DLBL";

// The capabilities of a user.
message UserCapabilities {
  option (google.api.resource) = {
    type: "drivelabels.googleapis.com/UserCapabilities"
    pattern: "users/{id}/capabilities"
  };

  // Output only. Resource name for the user capabilities.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the user is allowed access to the label manager.
  bool can_access_label_manager = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the user is an administrator for the shared labels
  // feature.
  bool can_administrate_labels = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the user is allowed to create new shared labels.
  bool can_create_shared_labels = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the user is allowed to create new admin labels.
  bool can_create_admin_labels = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
