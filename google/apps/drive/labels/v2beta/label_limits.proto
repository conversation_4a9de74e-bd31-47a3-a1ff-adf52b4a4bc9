// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2beta;

import "google/api/resource.proto";
import "google/type/date.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2beta;labels";
option java_multiple_files = true;
option java_outer_classname = "LabelLimitsProto";
option java_package = "com.google.apps.drive.labels.v2beta";
option objc_class_prefix = "DLBL";

// Label constraints governing the structure of a Label; such as, the maximum
// number of Fields allowed and maximum length of the label title.
message LabelLimits {
  option (google.api.resource) = {
    type: "drivelabels.googleapis.com/LabelLimits"
    pattern: "limits/label"
  };

  // Resource name.
  string name = 1;

  // The maximum number of characters allowed for the title.
  int32 max_title_length = 2;

  // The maximum number of characters allowed for the description.
  int32 max_description_length = 3;

  // The maximum number of Fields allowed within the label.
  int32 max_fields = 4;

  // The maximum number of published Fields that can be deleted.
  int32 max_deleted_fields = 5;

  // The maximum number of draft revisions that will be kept before deleting
  // old drafts.
  int32 max_draft_revisions = 6;

  // The limits for Fields.
  FieldLimits field_limits = 7;
}

// Field constants governing the structure of a Field; such as, the maximum
// title length, minimum and maximum field values or length, etc.
message FieldLimits {
  // Max length for the id.
  int32 max_id_length = 1;

  // Limits for Field title.
  int32 max_display_name_length = 2;

  // Limits for Field description, also called help text.
  int32 max_description_length = 3;

  // The relevant limits for the specified Field.Type.
  // Text Field limits.
  TextLimits text_limits = 4;

  // Long text Field limits.
  LongTextLimits long_text_limits = 5;

  // Integer Field limits.
  IntegerLimits integer_limits = 6;

  // Date Field limits.
  DateLimits date_limits = 7;

  // User Field limits.
  UserLimits user_limits = 8;

  // Selection Field limits.
  SelectionLimits selection_limits = 9;
}

// Limits for list-variant of a Field type.
message ListLimits {
  // Maximum number of values allowed for the Field type.
  int32 max_entries = 1;
}

// Limits for text Field type.
message TextLimits {
  // Minimum length allowed for a text Field type.
  int32 min_length = 1;

  // Maximum length allowed for a text Field type.
  int32 max_length = 2;
}

// Limits for long text Field type.
message LongTextLimits {
  // Minimum length allowed for a long text Field type.
  int32 min_length = 1;

  // Maximum length allowed for a long text Field type.
  int32 max_length = 2;
}

// Limits for integer Field type.
message IntegerLimits {
  // Minimum value for an integer Field type.
  int64 min_value = 1;

  // Maximum value for an integer Field type.
  int64 max_value = 2;
}

// Limits for date Field type.
message DateLimits {
  // Minimum value for the date Field type.
  google.type.Date min_value = 1;

  // Maximum value for the date Field type.
  google.type.Date max_value = 2;
}

// Limits for selection Field type.
message SelectionLimits {
  // Limits for list-variant of a Field type.
  ListLimits list_limits = 1;

  // Maximum ID length for a selection options.
  int32 max_id_length = 2;

  // Maximum length for display name.
  int32 max_display_name_length = 3;

  // The max number of choices.
  int32 max_choices = 4;

  // Maximum number of deleted choices.
  int32 max_deleted_choices = 5;
}

// Limits for Field.Type.USER.
message UserLimits {
  // Limits for list-variant of a Field type.
  ListLimits list_limits = 1;
}
