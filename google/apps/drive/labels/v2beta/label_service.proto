// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/apps/drive/labels/v2beta/label.proto";
import "google/apps/drive/labels/v2beta/label_limits.proto";
import "google/apps/drive/labels/v2beta/label_permission.proto";
import "google/apps/drive/labels/v2beta/requests.proto";
import "google/apps/drive/labels/v2beta/user_capabilities.proto";
import "google/protobuf/empty.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2beta;labels";
option java_multiple_files = true;
option java_outer_classname = "LabelServiceProto";
option java_package = "com.google.apps.drive.labels.v2beta";
option objc_class_prefix = "DLBL";

// Manage metadata taxonomies based on Labels and Fields that may be used within
// Google Drive to organize and find files using custom metadata.
service LabelService {
  option (google.api.default_host) = "drivelabels.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/drive.admin.labels,"
      "https://www.googleapis.com/auth/drive.admin.labels.readonly,"
      "https://www.googleapis.com/auth/drive.labels,"
      "https://www.googleapis.com/auth/drive.labels.readonly";

  // Gets the user capabilities.
  rpc GetUserCapabilities(GetUserCapabilitiesRequest)
      returns (UserCapabilities) {
    option (google.api.http) = {
      get: "/v2beta/{name=users/*/capabilities}"
    };
    option (google.api.method_signature) = "name";
  }

  // List labels.
  rpc ListLabels(ListLabelsRequest) returns (ListLabelsResponse) {
    option (google.api.http) = {
      get: "/v2beta/labels"
    };
  }

  // Get a label by its resource name.
  // Resource name may be any of:
  //
  // * `labels/{id}` - See `labels/{id}@latest`
  // * `labels/{id}@latest` - Gets the latest revision of the label.
  // * `labels/{id}@published` - Gets the current published revision of the
  //   label.
  // * `labels/{id}@{revision_id}` - Gets the label at the specified revision
  //   ID.
  rpc GetLabel(GetLabelRequest) returns (Label) {
    option (google.api.http) = {
      get: "/v2beta/{name=labels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get the constraints on the structure of a Label; such as, the maximum
  // number of Fields allowed and maximum length of the label title.
  rpc GetLabelLimits(GetLabelLimitsRequest) returns (LabelLimits) {
    option (google.api.http) = {
      get: "/v2beta/limits/label"
    };
  }

  // Creates a new Label.
  rpc CreateLabel(CreateLabelRequest) returns (Label) {
    option (google.api.http) = {
      post: "/v2beta/labels"
      body: "label"
    };
    option (google.api.method_signature) = "label";
  }

  // Updates a single Label by applying a set of update requests resulting in a
  // new draft revision. The batch update is all-or-nothing: If any of the
  // update requests are invalid, no changes are applied. The resulting draft
  // revision must be published before the changes may be used with Drive Items.
  rpc DeltaUpdateLabel(DeltaUpdateLabelRequest)
      returns (DeltaUpdateLabelResponse) {
    option (google.api.http) = {
      post: "/v2beta/{name=labels/*}:delta"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Label's `CopyMode`. Changes to this policy are not revisioned, do
  // not require publishing, and take effect immediately.
  rpc UpdateLabelCopyMode(UpdateLabelCopyModeRequest) returns (Label) {
    option (google.api.http) = {
      post: "/v2beta/{name=labels/*}:updateLabelCopyMode"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Publish all draft changes to the Label. Once published, the Label may not
  // return to its draft state. See
  // `google.apps.drive.labels.v2.Lifecycle` for more information.
  //
  // Publishing a Label will result in a new published revision. All previous
  // draft revisions will be deleted. Previous published revisions will be kept
  // but are subject to automated deletion as needed.
  //
  // Once published, some changes are no longer permitted. Generally, any change
  // that would invalidate or cause new restrictions on existing metadata
  // related to the Label will be rejected. For example, the following changes
  // to a Label will be rejected after the Label is published:
  // * The label cannot be directly deleted. It must be disabled first, then
  //   deleted.
  // * Field.FieldType cannot be changed.
  // * Changes to Field validation options cannot reject something that was
  //   previously accepted.
  // * Reducing the max entries.
  rpc PublishLabel(PublishLabelRequest) returns (Label) {
    option (google.api.http) = {
      post: "/v2beta/{name=labels/*}:publish"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Disable a published Label.
  // Disabling a Label will result in a new disabled published revision based on
  // the current published revision. If there is a draft revision, a new
  // disabled draft revision will be created based on the latest draft revision.
  // Older draft revisions will be deleted.
  //
  // Once disabled, a label may be deleted with `DeleteLabel`.
  rpc DisableLabel(DisableLabelRequest) returns (Label) {
    option (google.api.http) = {
      post: "/v2beta/{name=labels/*}:disable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Enable a disabled Label and restore it to its published state.
  // This will result in a new published revision based on the current disabled
  // published revision. If there is an existing disabled draft revision, a new
  // revision will be created based on that draft and will be enabled.
  rpc EnableLabel(EnableLabelRequest) returns (Label) {
    option (google.api.http) = {
      post: "/v2beta/{name=labels/*}:enable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Permanently deletes a Label and related metadata on Drive Items.
  //
  // Once deleted, the Label and related Drive item metadata will be deleted.
  // Only draft Labels, and disabled Labels may be deleted.
  rpc DeleteLabel(DeleteLabelRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2beta/{name=labels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists a Label's permissions.
  rpc ListLabelPermissions(ListLabelPermissionsRequest)
      returns (ListLabelPermissionsResponse) {
    option (google.api.http) = {
      get: "/v2beta/{parent=labels/*}/permissions"
      additional_bindings {
        get: "/v2beta/{parent=labels/*/revisions/*}/permissions"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a Label's permissions. If a permission for the indicated principal
  // doesn't exist, a new Label Permission is created, otherwise the existing
  // permission is updated. Permissions affect the Label resource as a whole,
  // are not revisioned, and do not require publishing.
  rpc CreateLabelPermission(CreateLabelPermissionRequest)
      returns (LabelPermission) {
    option (google.api.http) = {
      post: "/v2beta/{parent=labels/*}/permissions"
      body: "label_permission"
      additional_bindings {
        post: "/v2beta/{parent=labels/*/revisions/*}/permissions"
        body: "label_permission"
      }
    };
    option (google.api.method_signature) = "parent,label_permission";
  }

  // Updates a Label's permissions. If a permission for the indicated principal
  // doesn't exist, a new Label Permission is created, otherwise the existing
  // permission is updated. Permissions affect the Label resource as a whole,
  // are not revisioned, and do not require publishing.
  rpc UpdateLabelPermission(UpdateLabelPermissionRequest)
      returns (LabelPermission) {
    option (google.api.http) = {
      patch: "/v2beta/{parent=labels/*}/permissions"
      body: "label_permission"
      additional_bindings {
        patch: "/v2beta/{parent=labels/*/revisions/*}/permissions"
        body: "label_permission"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a Label's permission. Permissions affect the Label resource as a
  // whole, are not revisioned, and do not require publishing.
  rpc DeleteLabelPermission(DeleteLabelPermissionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2beta/{name=labels/*/permissions/*}"
      additional_bindings {
        delete: "/v2beta/{name=labels/*/revisions/*/permissions/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates Label permissions. If a permission for the
  // indicated principal doesn't exist, a new Label Permission is created,
  // otherwise the existing permission is updated. Permissions affect the Label
  // resource as a whole, are not revisioned, and do not require publishing.
  rpc BatchUpdateLabelPermissions(BatchUpdateLabelPermissionsRequest)
      returns (BatchUpdateLabelPermissionsResponse) {
    option (google.api.http) = {
      post: "/v2beta/{parent=labels/*}/permissions:batchUpdate"
      body: "*"
      additional_bindings {
        post: "/v2beta/{parent=labels/*/revisions/*}/permissions:batchUpdate"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes Label permissions. Permissions affect the Label resource as a
  // whole, are not revisioned, and do not require publishing.
  rpc BatchDeleteLabelPermissions(BatchDeleteLabelPermissionsRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2beta/{parent=labels/*}/permissions:batchDelete"
      body: "*"
      additional_bindings {
        post: "/v2beta/{parent=labels/*/revisions/*}/permissions:batchDelete"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the LabelLocks on a Label.
  rpc ListLabelLocks(ListLabelLocksRequest) returns (ListLabelLocksResponse) {
    option (google.api.http) = {
      get: "/v2beta/{parent=labels/*}/locks"
      additional_bindings { get: "/v2beta/{parent=labels/*/revisions/*}/locks" }
    };
    option (google.api.method_signature) = "parent";
  }
}
