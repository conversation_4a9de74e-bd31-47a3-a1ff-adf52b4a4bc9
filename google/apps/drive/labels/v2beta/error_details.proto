// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2beta;

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2beta;labels";
option java_multiple_files = true;
option java_outer_classname = "ErrorDetailsProto";
option java_package = "com.google.apps.drive.labels.v2beta";

// Describes violations in a request to create or update a Label or its
// Fields.
message InvalidArgument {
  // Describes the Field in which the violation occurred.
  message FieldViolation {
    // Possible reasons a field is invalid.
    enum Reason {
      // Unknown reason.
      REASON_UNSPECIFIED = 0;

      // The referenced field is required.
      FIELD_REQUIRED = 1;

      // The referenced value was invalid.
      INVALID_VALUE = 2;

      // The specified numeric value is out of the allowed range.
      VALUE_OUT_OF_RANGE = 3;

      // The specified string value was too long.
      STRING_VALUE_TOO_LONG = 4;

      // The number of entries exceeded the maximum.
      MAX_ENTRIES_EXCEEDED = 5;

      // The specified field is not found in the Label.
      FIELD_NOT_FOUND = 6;

      // The specified choice is not found in the Field.
      CHOICE_NOT_FOUND = 7;
    }

    // The path to the field where this violation occurred. This path is
    // specified using `FieldMask` format:
    // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
    string field = 1;

    // The detailed reason for this FieldViolation.
    Reason reason = 2;

    // A message that describes the violation. This message is intended to
    // be shown to end users, and is localized into the requesting user's
    // preferred language.
    string display_message = 3;
  }

  // Describes all violations in a client request.
  repeated FieldViolation field_violations = 1;
}

// Describes what preconditions have failed.
message PreconditionFailure {
  // Specific failure reason.
  message Violation {
    // The possible reasons a the violation occurred.
    enum Reason {
      // Unknown violation type.
      REASON_UNSPECIFIED = 0;

      // This Resource cannot be Disabled. Only Published resources can be
      // Disabled.
      CANNOT_DISABLE = 1;

      // This Resource cannot be Enabled. Only Disabled resources can be
      // Enabled.
      CANNOT_ENABLE = 2;

      // This Resource cannot be Published. Only Draft or Disabled resources
      // can be Published.
      CANNOT_PUBLISH = 3;

      // This Resource cannot be Unpublished. Once published, resources may
      // not be set in "Draft" state.
      CANNOT_UNPUBLISH = 4;

      // This Resource cannot be Deleted. Only Disabled resources
      // can be Deleted.
      CANNOT_DELETE = 5;

      // The request modified a range in a Field, but the new range does
      // not include the previous range. When this error happens, `field` points
      // at the Field where the violation occurred.
      CANNOT_RESTRICT_RANGE = 6;

      // The specified change cannot be made to published Resources.
      CANNOT_CHANGE_PUBLISHED_FIELD = 7;

      // The customer cannot create new labels because the maximum number
      // of labels for the customer has been reached.
      CANNOT_CREATE_MORE_LABELS = 8;

      // The Field type cannot be changed because the Field has been published.
      CANNOT_CHANGE_PUBLISHED_FIELD_TYPE = 9;

      // The Label component is locked and cannot be deleted
      CANNOT_MODIFY_LOCKED_COMPONENT = 10;

      // The Label cannot be enabled in the target application or applications.
      UNSUPPORT_ENABLED_APP_SETTINGS = 11;
    }

    // The path to the field where this violation occurred. This path is
    // specified using `FieldMask` format:
    // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
    string field = 1;

    // The type of this violation.
    Reason reason = 2;

    // A message that describes the violation. This message is intended to
    // be shown to end users, and is localized into the requesting user's
    // preferred language.
    string display_message = 3;
  }

  // Describes all violations in a client request.
  repeated Violation violation = 1;
}
