{"methodConfig": [{"name": [{"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "GetUserCapabilities"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "ListLabels"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "GetLabel"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "GetLabelLimits"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "ListLabelPermissions"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "ListLabelLocks"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "CreateLabel"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "DeltaUpdateLabel"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "UpdateLabelCopyMode"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "PublishLabel"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "Disable<PERSON><PERSON><PERSON>"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "Enable<PERSON><PERSON><PERSON>"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "DeleteLabel"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "CreateLabelPermission"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "UpdateLabelPermission"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "DeleteLabelPermission"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "BatchUpdateLabelPermissions"}, {"service": "google.apps.drive.labels.v2beta.LabelService.", "method": "BatchDeleteLabelPermissions"}], "timeout": "60s"}]}