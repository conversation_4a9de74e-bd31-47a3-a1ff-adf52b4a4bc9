// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.activity.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/apps/drive/activity/v2/query_drive_activity_request.proto";
import "google/apps/drive/activity/v2/query_drive_activity_response.proto";

option csharp_namespace = "Google.Apps.Drive.Activity.V2";
option go_package = "google.golang.org/genproto/googleapis/apps/drive/activity/v2;activity";
option java_multiple_files = true;
option java_outer_classname = "DriveActivityServiceProto";
option java_package = "com.google.apps.drive.activity.v2";
option objc_class_prefix = "GADA";
option php_namespace = "Google\\Apps\\Drive\\Activity\\V2";

// Service for querying activity on Drive items. Activity is user
// or system action on Drive items that happened in the past. A Drive item can
// be a file or folder, or a Team Drive.
service DriveActivityService {
  option (google.api.default_host) = "driveactivity.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/drive.activity,"
      "https://www.googleapis.com/auth/drive.activity.readonly";

  // Query past activity in Google Drive.
  rpc QueryDriveActivity(QueryDriveActivityRequest)
      returns (QueryDriveActivityResponse) {
    option (google.api.http) = {
      post: "/v2/activity:query"
      body: "*"
    };
  }
}
