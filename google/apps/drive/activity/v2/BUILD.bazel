# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "activity_proto",
    srcs = [
        "action.proto",
        "actor.proto",
        "common.proto",
        "drive_activity_service.proto",
        "query_drive_activity_request.proto",
        "query_drive_activity_response.proto",
        "target.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "activity_proto_with_info",
    deps = [
        ":activity_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "activity_java_proto",
    deps = [":activity_proto"],
)

java_grpc_library(
    name = "activity_java_grpc",
    srcs = [":activity_proto"],
    deps = [":activity_java_proto"],
)

java_gapic_library(
    name = "activity_java_gapic",
    srcs = [":activity_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "driveactivity_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "driveactivity_v2.yaml",
    test_deps = [
        ":activity_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":activity_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "activity_java_gapic_test_suite",
    test_classes = [
        "com.google.apps.drive.activity.v2.DriveActivityServiceClientHttpJsonTest",
        "com.google.apps.drive.activity.v2.DriveActivityServiceClientTest",
    ],
    runtime_deps = [":activity_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-apps-drive-activity-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":activity_java_gapic",
        ":activity_java_grpc",
        ":activity_java_proto",
        ":activity_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "activity_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/apps/drive/activity/v2",
    protos = [":activity_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "activity_go_gapic",
    srcs = [":activity_proto_with_info"],
    grpc_service_config = "driveactivity_grpc_service_config.json",
    importpath = "google.golang.org/google/apps/drive/activity/v2;activity",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "driveactivity_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":activity_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-apps-drive-activity-v2-go",
    deps = [
        ":activity_go_gapic",
        ":activity_go_gapic_srcjar-metadata.srcjar",
        ":activity_go_gapic_srcjar-snippets.srcjar",
        ":activity_go_gapic_srcjar-test.srcjar",
        ":activity_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "activity_py_gapic",
    srcs = [":activity_proto"],
    grpc_service_config = "driveactivity_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "driveactivity_v2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "activity_py_gapic_test",
    srcs = [
        "activity_py_gapic_pytest.py",
        "activity_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":activity_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "drive-activity-v2-py",
    deps = [
        ":activity_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "activity_php_proto",
    deps = [":activity_proto"],
)

php_gapic_library(
    name = "activity_php_gapic",
    srcs = [":activity_proto_with_info"],
    grpc_service_config = "driveactivity_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "driveactivity_v2.yaml",
    transport = "grpc+rest",
    deps = [":activity_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-apps-drive-activity-v2-php",
    deps = [
        ":activity_php_gapic",
        ":activity_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "activity_nodejs_gapic",
    package_name = "@google-cloud/drive-activity",
    src = ":activity_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "driveactivity_grpc_service_config.json",
    package = "google.apps.drive.activity.v2",
    rest_numeric_enums = True,
    service_yaml = "driveactivity_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "drive-activity-v2-nodejs",
    deps = [
        ":activity_nodejs_gapic",
        ":activity_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "activity_ruby_proto",
    deps = [":activity_proto"],
)

ruby_grpc_library(
    name = "activity_ruby_grpc",
    srcs = [":activity_proto"],
    deps = [":activity_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "activity_ruby_gapic",
    srcs = [":activity_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-apps-drive-activity-v2"],
    grpc_service_config = "driveactivity_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "driveactivity_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":activity_ruby_grpc",
        ":activity_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-apps-drive-activity-v2-ruby",
    deps = [
        ":activity_ruby_gapic",
        ":activity_ruby_grpc",
        ":activity_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "activity_csharp_proto",
    deps = [":activity_proto"],
)

csharp_grpc_library(
    name = "activity_csharp_grpc",
    srcs = [":activity_proto"],
    deps = [":activity_csharp_proto"],
)

csharp_gapic_library(
    name = "activity_csharp_gapic",
    srcs = [":activity_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "driveactivity_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "driveactivity_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":activity_csharp_grpc",
        ":activity_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-apps-drive-activity-v2-csharp",
    deps = [
        ":activity_csharp_gapic",
        ":activity_csharp_grpc",
        ":activity_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
