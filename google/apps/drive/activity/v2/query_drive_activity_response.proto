// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.activity.v2;

import "google/apps/drive/activity/v2/action.proto";
import "google/apps/drive/activity/v2/actor.proto";
import "google/apps/drive/activity/v2/common.proto";
import "google/apps/drive/activity/v2/target.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Drive.Activity.V2";
option go_package = "google.golang.org/genproto/googleapis/apps/drive/activity/v2;activity";
option java_multiple_files = true;
option java_outer_classname = "QueryDriveActivityResponseProto";
option java_package = "com.google.apps.drive.activity.v2";
option objc_class_prefix = "GADA";
option php_namespace = "Google\\Apps\\Drive\\Activity\\V2";

// Response message for querying Drive activity.
message QueryDriveActivityResponse {
  // List of activity requested.
  repeated DriveActivity activities = 1;

  // Token to retrieve the next page of results, or
  // empty if there are no more results in the list.
  string next_page_token = 2;
}

// A single Drive activity comprising one or more Actions by one or more
// Actors on one or more Targets. Some Action groupings occur spontaneously,
// such as moving an item into a shared folder triggering a permission change.
// Other groupings of related Actions, such as multiple Actors editing one item
// or moving multiple files into a new folder, are controlled by the selection
// of a ConsolidationStrategy in the QueryDriveActivityRequest.
message DriveActivity {
  // Key information about the primary action for this activity. This is either
  // representative, or the most important, of all actions in the activity,
  // according to the ConsolidationStrategy in the request.
  ActionDetail primary_action_detail = 2;

  // All actor(s) responsible for the activity.
  repeated Actor actors = 3;

  // Details on all actions in this activity.
  repeated Action actions = 4;

  // All Google Drive objects this activity is about (e.g. file, folder, drive).
  // This represents the state of the target immediately after the actions
  // occurred.
  repeated Target targets = 5;

  // The period of time when this activity occurred.
  oneof time {
    // The activity occurred at this specific time.
    google.protobuf.Timestamp timestamp = 6;

    // The activity occurred over this time range.
    TimeRange time_range = 7;
  }
}
