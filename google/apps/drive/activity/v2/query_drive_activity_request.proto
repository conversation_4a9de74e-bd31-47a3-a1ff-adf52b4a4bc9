// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.activity.v2;

option csharp_namespace = "Google.Apps.Drive.Activity.V2";
option go_package = "google.golang.org/genproto/googleapis/apps/drive/activity/v2;activity";
option java_multiple_files = true;
option java_outer_classname = "QueryDriveActivityRequestProto";
option java_package = "com.google.apps.drive.activity.v2";
option objc_class_prefix = "GADA";
option php_namespace = "Google\\Apps\\Drive\\Activity\\V2";

// The request message for querying Drive activity.
message QueryDriveActivityRequest {
  // The primary criteria in the query. The default is
  // ancestorName = `items/root`, if no key is specified.
  oneof key {
    // Return activities for this Drive item. The format is
    // `items/ITEM_ID`.
    string item_name = 1;

    // Return activities for this Drive folder, plus all children and
    // descendants. The format is `items/ITEM_ID`.
    string ancestor_name = 2;
  }

  // Details on how to consolidate related actions that make up the activity. If
  // not set, then related actions aren't consolidated.
  ConsolidationStrategy consolidation_strategy = 5;

  // The minimum number of activities desired in the response; the server
  // attempts to return at least this quantity. The server may also return fewer
  // activities if it has a partial response ready before the request times out.
  // If not set, a default value is used.
  int32 page_size = 6;

  // The token identifies which page of results to return. Set this to the
  // next_page_token value returned from a previous query to obtain the
  // following page of results. If not set, the first page of results is
  // returned.
  string page_token = 7;

  // The filtering for items returned from this query request. The format of the
  // filter string is a sequence of expressions, joined by an optional "AND",
  // where each expression is of the form "field operator value".
  //
  // Supported fields:
  //
  //   - `time`: Uses numerical operators on date values either in
  //     terms of milliseconds since Jan 1, 1970 or in <a
  //     href="https://www.rfc-editor.org/rfc/rfc3339" target="_blank">RFC
  //     3339</a> format. Examples:
  //       - `time > 1452409200000 AND time <= 1492812924310`
  //       - `time >= "2016-01-10T01:02:03-05:00"`
  //
  //   - `detail.action_detail_case`: Uses the "has" operator (:) and
  //     either a singular value or a list of allowed action types enclosed in
  //     parentheses, separated by a space. To exclude a result from the
  //     response, prepend a hyphen (`-`) to the beginning of the filter string.
  //     Examples:
  //       - `detail.action_detail_case:RENAME`
  //       - `detail.action_detail_case:(CREATE RESTORE)`
  //       - `-detail.action_detail_case:MOVE`
  //
  string filter = 8;
}

// How the individual activities are consolidated. If a set of activities is
// related they can be consolidated into one combined activity, such as one
// actor performing the same action on multiple targets, or multiple actors
// performing the same action on a single target. The strategy defines the rules
// for which activities are related.
message ConsolidationStrategy {
  // A strategy that does no consolidation of individual activities.
  message NoConsolidation {}

  // A strategy that consolidates activities using the grouping rules from the
  // legacy V1 Activity API. Similar actions occurring within a window of time
  // can be grouped across multiple targets (such as moving a set of files at
  // once) or multiple actors (such as several users editing the same item).
  // Grouping rules for this strategy are specific to each type of action.
  message Legacy {}

  // How the individual activities are consolidated.
  oneof strategy {
    // The individual activities are not consolidated.
    NoConsolidation none = 1;

    // The individual activities are consolidated using the legacy strategy.
    Legacy legacy = 2;
  }
}
