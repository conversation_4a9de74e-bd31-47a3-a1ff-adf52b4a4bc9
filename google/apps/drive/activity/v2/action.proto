// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.activity.v2;

import "google/apps/drive/activity/v2/actor.proto";
import "google/apps/drive/activity/v2/common.proto";
import "google/apps/drive/activity/v2/target.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Drive.Activity.V2";
option go_package = "google.golang.org/genproto/googleapis/apps/drive/activity/v2;activity";
option java_multiple_files = true;
option java_outer_classname = "ActionProto";
option java_package = "com.google.apps.drive.activity.v2";
option objc_class_prefix = "GADA";
option php_namespace = "Google\\Apps\\Drive\\Activity\\V2";

// Information about the action.
message Action {
  // The type and detailed information about the action.
  ActionDetail detail = 1;

  // The actor responsible for this action (or empty if all actors are
  // responsible).
  Actor actor = 3;

  // The target this action affects (or empty if affecting all targets). This
  // represents the state of the target immediately after this action occurred.
  Target target = 4;

  // When the action occurred (or empty if same time as entire activity).
  oneof time {
    // The action occurred at this specific time.
    google.protobuf.Timestamp timestamp = 5;

    // The action occurred over this time range.
    TimeRange time_range = 6;
  }
}

// Data describing the type and additional information of an action.
message ActionDetail {
  // Data describing the type and additional information of an action.
  oneof action_detail {
    // An object was created.
    Create create = 1;

    // An object was edited.
    Edit edit = 2;

    // An object was moved.
    Move move = 3;

    // An object was renamed.
    Rename rename = 4;

    // An object was deleted.
    Delete delete = 5;

    // A deleted object was restored.
    Restore restore = 6;

    // The permission on an object was changed.
    PermissionChange permission_change = 7;

    // A change about comments was made.
    Comment comment = 8;

    // A change happened in data leak prevention status.
    DataLeakPreventionChange dlp_change = 9;

    // An object was referenced in an application outside of Drive/Docs.
    ApplicationReference reference = 12;

    // Settings were changed.
    SettingsChange settings_change = 13;

    // Label was changed.
    AppliedLabelChange applied_label_change = 19;
  }
}

// An object was created.
message Create {
  // An object was created from scratch.
  message New {}

  // An object was uploaded into Drive.
  message Upload {}

  // An object was created by copying an existing object.
  message Copy {
    // The original object.
    TargetReference original_object = 1;
  }

  // The origin of the new object.
  oneof origin {
    // If present, indicates the object was newly created (e.g. as a blank
    // document), not derived from a Drive object or external object.
    New new = 1;

    // If present, indicates the object originated externally and was uploaded
    // to Drive.
    Upload upload = 2;

    // If present, indicates the object was created by copying an existing Drive
    // object.
    Copy copy = 3;
  }
}

// An empty message indicating an object was edited.
message Edit {}

// An object was moved.
message Move {
  // The added parent object(s).
  repeated TargetReference added_parents = 1;

  // The removed parent object(s).
  repeated TargetReference removed_parents = 2;
}

// An object was renamed.
message Rename {
  // The previous title of the drive object.
  string old_title = 1;

  // The new title of the drive object.
  string new_title = 2;
}

// An object was deleted.
message Delete {
  // The type of deletion.
  enum Type {
    // Deletion type is not available.
    TYPE_UNSPECIFIED = 0;

    // An object was put into the trash.
    TRASH = 1;

    // An object was deleted permanently.
    PERMANENT_DELETE = 2;
  }

  // The type of delete action taken.
  Type type = 1;
}

// A deleted object was restored.
message Restore {
  // The type of restoration.
  enum Type {
    // The type is not available.
    TYPE_UNSPECIFIED = 0;

    // An object was restored from the trash.
    UNTRASH = 1;
  }

  // The type of restore action taken.
  Type type = 1;
}

// A change of the permission setting on an item.
message PermissionChange {
  // The set of permissions added by this change.
  repeated Permission added_permissions = 1;

  // The set of permissions removed by this change.
  repeated Permission removed_permissions = 2;
}

// The permission setting of an object.
message Permission {
  // The [Google Drive permissions
  // roles](https://developers.google.com/drive/web/manage-sharing#roles).
  enum Role {
    // The role is not available.
    ROLE_UNSPECIFIED = 0;

    // A role granting full access.
    OWNER = 1;

    // A role granting the ability to manage people and settings.
    ORGANIZER = 2;

    // A role granting the ability to contribute and manage content.
    FILE_ORGANIZER = 3;

    // A role granting the ability to contribute content. This role is sometimes
    // also known as "writer".
    EDITOR = 4;

    // A role granting the ability to view and comment on content.
    COMMENTER = 5;

    // A role granting the ability to view content. This role is sometimes also
    // known as "reader".
    VIEWER = 6;

    // A role granting the ability to view content only after it has been
    // published to the web. This role is sometimes also known as "published
    // reader". See https://support.google.com/sites/answer/6372880 for more
    // information.
    PUBLISHED_VIEWER = 7;
  }

  // Represents any user (including a logged out user).
  message Anyone {}

  // Indicates the
  // [Google Drive permissions
  // role](https://developers.google.com/drive/web/manage-sharing#roles). The
  // role determines a user's ability to read, write, and comment on items.
  Role role = 1;

  // The entity granted the role.
  oneof scope {
    // The user to whom this permission applies.
    User user = 2;

    // The group to whom this permission applies.
    Group group = 3;

    // The domain to whom this permission applies.
    Domain domain = 4;

    // If set, this permission applies to anyone, even logged out users.
    Anyone anyone = 5;
  }

  // If true, the item can be discovered (e.g. in the user's "Shared with me"
  // collection) without needing a link to the item.
  bool allow_discovery = 6;
}

// A change about comments on an object.
message Comment {
  // A regular posted comment.
  message Post {
    // More detailed information about the change.
    enum Subtype {
      // Subtype not available.
      SUBTYPE_UNSPECIFIED = 0;

      // A post was added.
      ADDED = 1;

      // A post was deleted.
      DELETED = 2;

      // A reply was added.
      REPLY_ADDED = 3;

      // A reply was deleted.
      REPLY_DELETED = 4;

      // A posted comment was resolved.
      RESOLVED = 5;

      // A posted comment was reopened.
      REOPENED = 6;
    }

    // The sub-type of this event.
    Subtype subtype = 1;
  }

  // A comment with an assignment.
  message Assignment {
    // More detailed information about the change.
    enum Subtype {
      // Subtype not available.
      SUBTYPE_UNSPECIFIED = 0;

      // An assignment was added.
      ADDED = 1;

      // An assignment was deleted.
      DELETED = 2;

      // An assignment reply was added.
      REPLY_ADDED = 3;

      // An assignment reply was deleted.
      REPLY_DELETED = 4;

      // An assignment was resolved.
      RESOLVED = 5;

      // A resolved assignment was reopened.
      REOPENED = 6;

      // An assignment was reassigned.
      REASSIGNED = 7;
    }

    // The sub-type of this event.
    Subtype subtype = 1;

    // The user to whom the comment was assigned.
    User assigned_user = 7;
  }

  // A suggestion.
  message Suggestion {
    // More detailed information about the change.
    enum Subtype {
      // Subtype not available.
      SUBTYPE_UNSPECIFIED = 0;

      // A suggestion was added.
      ADDED = 1;

      // A suggestion was deleted.
      DELETED = 2;

      // A suggestion reply was added.
      REPLY_ADDED = 3;

      // A suggestion reply was deleted.
      REPLY_DELETED = 4;

      // A suggestion was accepted.
      ACCEPTED = 7;

      // A suggestion was rejected.
      REJECTED = 8;

      // An accepted suggestion was deleted.
      ACCEPT_DELETED = 9;

      // A rejected suggestion was deleted.
      REJECT_DELETED = 10;
    }

    // The sub-type of this event.
    Subtype subtype = 1;
  }

  // The type of changed comment.
  oneof type {
    // A change on a regular posted comment.
    Post post = 1;

    // A change on an assignment.
    Assignment assignment = 2;

    // A change on a suggestion.
    Suggestion suggestion = 3;
  }

  // Users who are mentioned in this comment.
  repeated User mentioned_users = 7;
}

// A change in the object's data leak prevention status.
message DataLeakPreventionChange {
  // The type of the change.
  enum Type {
    // An update to the DLP state that is neither FLAGGED or CLEARED.
    TYPE_UNSPECIFIED = 0;

    // Document has been flagged as containing sensitive content.
    FLAGGED = 1;

    // Document is no longer flagged as containing sensitive content.
    CLEARED = 2;
  }

  // The type of Data Leak Prevention (DLP) change.
  Type type = 1;
}

// Activity in applications other than Drive.
message ApplicationReference {
  // The type of the action.
  enum Type {
    // The type is not available.
    UNSPECIFIED_REFERENCE_TYPE = 0;

    // The links of one or more Drive items were posted.
    LINK = 1;

    // Comments were made regarding a Drive item.
    DISCUSS = 2;
  }

  // The reference type corresponding to this event.
  Type type = 1;
}

// Information about settings changes.
message SettingsChange {
  // Information about restriction policy changes to a feature.
  message RestrictionChange {
    // The feature which had changes to its restriction policy.
    enum Feature {
      // The feature which changed restriction settings was not available.
      FEATURE_UNSPECIFIED = 0;

      // When restricted, this prevents items from being shared outside the
      // domain.
      SHARING_OUTSIDE_DOMAIN = 1;

      // When restricted, this prevents direct sharing of individual items.
      DIRECT_SHARING = 2;

      // When restricted, this prevents actions like copy, download, and print
      // that might result in uncontrolled duplicates of items.
      ITEM_DUPLICATION = 3;

      // When restricted, this prevents use of Drive File Stream.
      DRIVE_FILE_STREAM = 4;

      // When restricted, this limits sharing of folders to managers only.
      FILE_ORGANIZER_CAN_SHARE_FOLDERS = 5;
    }

    // The restriction applicable to a feature.
    enum Restriction {
      // The type of restriction is not available.
      RESTRICTION_UNSPECIFIED = 0;

      // The feature is available without restriction.
      UNRESTRICTED = 1;

      // The use of this feature is fully restricted.
      FULLY_RESTRICTED = 2;
    }

    // The feature which had a change in restriction policy.
    Feature feature = 1;

    // The restriction in place after the change.
    Restriction new_restriction = 2;
  }

  // The set of changes made to restrictions.
  repeated RestrictionChange restriction_changes = 1;
}

// Label changes that were made on the Target.
message AppliedLabelChange {
  // A change made to a Label on the Target.
  message AppliedLabelChangeDetail {
    // Change to a Field value.
    message FieldValueChange {
      // Contains a value of a Field.
      message FieldValue {
        // Wrapper for Text Field value.
        message Text {
          // Value of Text Field.
          optional string value = 1;
        }

        // Wrapper for Text List Field value.
        message TextList {
          // Text values.
          repeated Text values = 1;
        }

        // Wrapper for Selection Field value as combined value/display_name
        // pair for selected choice.
        message Selection {
          // Selection value as Field Choice ID.
          optional string value = 1;

          // Selection value as human-readable display string.
          optional string display_name = 2;
        }

        // Wrapper for SelectionList Field value.
        message SelectionList {
          // Selection values.
          repeated Selection values = 1;
        }

        // Wrapper for Integer Field value.
        message Integer {
          // Integer value.
          optional int64 value = 1;
        }

        // Wrapper for User Field value.
        message SingleUser {
          // User value as email.
          optional string value = 1;
        }

        // Wrapper for UserList Field value.
        message UserList {
          // User values.
          repeated SingleUser values = 1;
        }

        // Wrapper for Date Field value.
        message Date {
          // Date value.
          optional google.protobuf.Timestamp value = 1;
        }

        // Field values for all Field types.
        oneof value {
          // Text Field value.
          Text text = 1;

          // Text List Field value.
          TextList text_list = 3;

          // Selection Field value.
          Selection selection = 4;

          // Selection List Field value.
          SelectionList selection_list = 5;

          // Integer Field value.
          Integer integer = 6;

          // User Field value.
          SingleUser user = 7;

          // User List Field value.
          UserList user_list = 8;

          // Date Field value.
          Date date = 9;
        }
      }

      // The ID of this field. Field IDs are unique within a Label.
      optional string field_id = 1;

      // The value that was previously set on the field. If not present,
      // the field was newly set. At least one of {old_value|new_value} is
      // always set.
      optional FieldValue old_value = 2;

      // The value that is now set on the field. If not present, the field was
      // cleared. At least one of {old_value|new_value} is always set.
      optional FieldValue new_value = 3;

      // The human-readable display name for this field.
      optional string display_name = 4;
    }

    // The type of Label change
    enum Type {
      // The type of change to this Label is not available.
      TYPE_UNSPECIFIED = 0;

      // The identified Label was added to the Target.
      LABEL_ADDED = 1;

      // The identified Label was removed from the Target.
      LABEL_REMOVED = 2;

      // Field values were changed on the Target.
      LABEL_FIELD_VALUE_CHANGED = 3;

      // The Label was applied as a side-effect of Drive item creation.
      LABEL_APPLIED_BY_ITEM_CREATE = 4;
    }

    // The Label name representing the Label that changed.
    // This name always contains the revision of the Label that was used
    // when this Action occurred. The format is
    // `labels/id@revision`.
    string label = 1;

    // The types of changes made to the Label on the Target.
    repeated Type types = 2;

    // The human-readable title of the label that changed.
    string title = 3;

    // Field Changes. Only present if `types` contains
    // `LABEL_FIELD_VALUE_CHANGED`.
    repeated FieldValueChange field_changes = 4;
  }

  // Changes that were made to the Label on the Target.
  repeated AppliedLabelChangeDetail changes = 1;
}
