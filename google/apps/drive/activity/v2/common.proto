// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.activity.v2;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Drive.Activity.V2";
option go_package = "google.golang.org/genproto/googleapis/apps/drive/activity/v2;activity";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.apps.drive.activity.v2";
option objc_class_prefix = "GADA";
option php_namespace = "Google\\Apps\\Drive\\Activity\\V2";

// Information about time ranges.
message TimeRange {
  // The start of the time range.
  google.protobuf.Timestamp start_time = 1;

  // The end of the time range.
  google.protobuf.Timestamp end_time = 2;
}

// Information about a group.
message Group {
  // The email address of the group.
  string email = 1;

  // The title of the group.
  string title = 2;
}

// Information about a domain.
message Domain {
  // The name of the domain, e.g. `google.com`.
  string name = 1;

  // An opaque string used to identify this domain.
  string legacy_id = 3;
}
