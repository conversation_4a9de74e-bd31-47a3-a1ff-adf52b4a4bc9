// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.activity.v2;

import "google/apps/drive/activity/v2/actor.proto";
import "google/apps/drive/activity/v2/common.proto";

option csharp_namespace = "Google.Apps.Drive.Activity.V2";
option go_package = "google.golang.org/genproto/googleapis/apps/drive/activity/v2;activity";
option java_multiple_files = true;
option java_outer_classname = "TargetProto";
option java_package = "com.google.apps.drive.activity.v2";
option objc_class_prefix = "GADA";
option php_namespace = "Google\\Apps\\Drive\\Activity\\V2";

// Information about the target of activity.
//
// For more information on how activity history is shared with users, see
// [Activity history
// visibility](https://developers.google.com/drive/activity/v2#activityhistory).
message Target {
  // The type of target object.
  oneof object {
    // The target is a Drive item.
    DriveItem drive_item = 1;

    // The target is a shared drive.
    Drive drive = 5;

    // The target is a comment on a Drive file.
    FileComment file_comment = 3;
  }

  // This field is deprecated; please use the `drive` field instead.
  TeamDrive team_drive = 2 [deprecated = true];
}

// A lightweight reference to the target of activity.
message TargetReference {
  // The type of target object.
  oneof object {
    // The target is a Drive item.
    DriveItemReference drive_item = 1;

    // The target is a shared drive.
    DriveReference drive = 3;
  }

  // This field is deprecated; please use the `drive` field instead.
  TeamDriveReference team_drive = 2 [deprecated = true];
}

// A comment on a file.
message FileComment {
  // The comment in the discussion thread. This identifier is an opaque string
  // compatible with the Drive API; see
  // https://developers.google.com/drive/v3/reference/comments/get
  string legacy_comment_id = 1;

  // The discussion thread to which the comment was added. This identifier is an
  // opaque string compatible with the Drive API and references the first
  // comment in a discussion; see
  // https://developers.google.com/drive/v3/reference/comments/get
  string legacy_discussion_id = 2;

  // The link to the discussion thread containing this comment, for example,
  // `https://docs.google.com/DOCUMENT_ID/edit?disco=THREAD_ID`.
  string link_to_discussion = 3;

  // The Drive item containing this comment.
  DriveItem parent = 4;
}

// A Drive item, such as a file or folder.
message DriveItem {
  // This item is deprecated; please see `DriveFile` instead.
  message File {
    option deprecated = true;
  }

  // This item is deprecated; please see `DriveFolder` instead.
  message Folder {
    option deprecated = true;

    // This item is deprecated; please see `DriveFolder.Type` instead.
    enum Type {
      option deprecated = true;

      // This item is deprecated; please see `DriveFolder.Type` instead.
      TYPE_UNSPECIFIED = 0;

      // This item is deprecated; please see `DriveFolder.Type` instead.
      MY_DRIVE_ROOT = 1;

      // This item is deprecated; please see `DriveFolder.Type` instead.
      TEAM_DRIVE_ROOT = 2;

      // This item is deprecated; please see `DriveFolder.Type` instead.
      STANDARD_FOLDER = 3;
    }

    // This field is deprecated; please see `DriveFolder.type` instead.
    Type type = 6;
  }

  // A Drive item which is a file.
  message DriveFile {}

  // A Drive item which is a folder.
  message DriveFolder {
    // The type of a Drive folder.
    enum Type {
      // The folder type is unknown.
      TYPE_UNSPECIFIED = 0;

      // The folder is the root of a user's MyDrive.
      MY_DRIVE_ROOT = 1;

      // The folder is the root of a shared drive.
      SHARED_DRIVE_ROOT = 2;

      // The folder is a standard, non-root, folder.
      STANDARD_FOLDER = 3;
    }

    // The type of Drive folder.
    Type type = 6;
  }

  // The target Drive item. The format is `items/ITEM_ID`.
  string name = 1;

  // The title of the Drive item.
  string title = 2;

  // This field is deprecated; please use the `driveFile` field instead.
  File file = 3 [deprecated = true];

  // This field is deprecated; please use the `driveFolder` field instead.
  Folder folder = 4 [deprecated = true];

  // If present, this describes the type of the Drive item.
  oneof item_type {
    // The Drive item is a file.
    DriveFile drive_file = 8;

    // The Drive item is a folder. Includes information about the type of
    // folder.
    DriveFolder drive_folder = 9;
  }

  // The MIME type of the Drive item.  See
  // https://developers.google.com/drive/v3/web/mime-types.
  string mime_type = 6;

  // Information about the owner of this Drive item.
  Owner owner = 7;
}

// Information about the owner of a Drive item.
message Owner {
  // The owner of the Drive item.
  oneof owner {
    // The user that owns the Drive item.
    User user = 1;

    // The drive that owns the item.
    DriveReference drive = 4;
  }

  // This field is deprecated; please use the `drive` field instead.
  TeamDriveReference team_drive = 2 [deprecated = true];

  // The domain of the Drive item owner.
  Domain domain = 3;
}

// This item is deprecated; please see `Drive` instead.
message TeamDrive {
  option deprecated = true;

  // This field is deprecated; please see `Drive.name` instead.
  string name = 1;

  // This field is deprecated; please see `Drive.title` instead.
  string title = 2;

  // This field is deprecated; please see `Drive.root` instead.
  DriveItem root = 3;
}

// Information about a shared drive.
message Drive {
  // The resource name of the shared drive. The format is
  // `COLLECTION_ID/DRIVE_ID`. Clients should not assume a specific collection
  // ID for this resource name.
  string name = 1;

  // The title of the shared drive.
  string title = 2;

  // The root of this shared drive.
  DriveItem root = 3;
}

// A lightweight reference to a Drive item, such as a file or folder.
message DriveItemReference {
  // The target Drive item. The format is `items/ITEM_ID`.
  string name = 1;

  // The title of the Drive item.
  string title = 2;

  // This field is deprecated; please use the `driveFile` field instead.
  DriveItem.File file = 3 [deprecated = true];

  // This field is deprecated; please use the `driveFolder` field instead.
  DriveItem.Folder folder = 4 [deprecated = true];

  // If present, this describes the type of the Drive item.
  oneof item_type {
    // The Drive item is a file.
    DriveItem.DriveFile drive_file = 8;

    // The Drive item is a folder. Includes information about the type of
    // folder.
    DriveItem.DriveFolder drive_folder = 9;
  }
}

// This item is deprecated; please see `DriveReference` instead.
message TeamDriveReference {
  option deprecated = true;

  // This field is deprecated; please see `DriveReference.name` instead.
  string name = 1;

  // This field is deprecated; please see `DriveReference.title` instead.
  string title = 2;
}

// A lightweight reference to a shared drive.
message DriveReference {
  // The resource name of the shared drive. The format is
  // `COLLECTION_ID/DRIVE_ID`. Clients should not assume a specific collection
  // ID for this resource name.
  string name = 1;

  // The title of the shared drive.
  string title = 2;
}
