// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.events.subscriptions.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Events.Subscriptions.V1";
option go_package = "cloud.google.com/go/apps/events/subscriptions/apiv1/subscriptionspb;subscriptionspb";
option java_multiple_files = true;
option java_outer_classname = "SubscriptionResourceProto";
option java_package = "com.google.apps.events.subscriptions.v1";
option php_namespace = "Google\\Apps\\Events\\Subscriptions\\V1";
option ruby_package = "Google::Apps::Events::Subscriptions::V1";
option (google.api.resource_definition) = {
  type: "cloudidentity.googleapis.com/User"
  pattern: "users/{user}"
};
option (google.api.resource_definition) = {
  type: "pubsub.googleapis.com/Topic"
  pattern: "projects/{project}/topics/{topic}"
};

// A subscription to receive events about a Google Workspace resource. To learn
// more about subscriptions, see the [Google Workspace Events API
// overview](https://developers.google.com/workspace/events).
message Subscription {
  option (google.api.resource) = {
    type: "workspaceevents.googleapis.com/Subscription"
    pattern: "subscriptions/{subscription}"
    plural: "subscriptions"
    singular: "subscription"
    style: DECLARATIVE_FRIENDLY
  };

  // Possible states for the subscription.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The subscription is active and can receive and deliver events to its
    // notification endpoint.
    ACTIVE = 1;

    // The subscription is unable to receive events due to an error.
    // To identify the error, see the
    // [`suspension_reason`][google.apps.events.subscriptions.v1.Subscription.suspension_reason]
    // field.
    SUSPENDED = 2;

    // The subscription is deleted.
    DELETED = 3;
  }

  // Possible errors for a subscription.
  enum ErrorType {
    // Default value. This value is unused.
    ERROR_TYPE_UNSPECIFIED = 0;

    // The authorizing user has revoked the grant of one or more OAuth scopes.
    // To learn more about authorization for Google Workspace, see [Configure
    // the OAuth consent
    // screen](https://developers.google.com/workspace/guides/configure-oauth-consent#choose-scopes).
    USER_SCOPE_REVOKED = 1;

    // The target resource for the subscription no longer exists.
    RESOURCE_DELETED = 2;

    // The user that authorized the creation of the subscription no longer has
    // access to the subscription's target resource.
    USER_AUTHORIZATION_FAILURE = 3;

    // The Google Workspace application doesn't have access to deliver
    // events to your subscription's notification endpoint.
    ENDPOINT_PERMISSION_DENIED = 4;

    // The subscription's notification endpoint doesn't exist, or the endpoint
    // can't be found in the Google Cloud project where you created the
    // subscription.
    ENDPOINT_NOT_FOUND = 6;

    // The subscription's notification endpoint failed to receive events due to
    // insufficient quota or reaching rate limiting.
    ENDPOINT_RESOURCE_EXHAUSTED = 7;

    // An unidentified error has occurred.
    OTHER = 5;
  }

  // The time when the subscription expires.
  //
  // The maximum expiration time depends on whether your subscription includes
  // resource data in event payloads (specified in the
  // [PayloadOptions][google.apps.events.subscriptions.v1.PayloadOptions]
  // field):
  //
  // * If payloads omit resource data, up to 7 days.
  // * If payloads include resource data, up to 4 hours. If your Google
  // Workspace organization grants access to the resource through [domain-wide
  // delegation](https://support.google.com/a/answer/162106), you can extend the
  // subscription's expiration time to up to 24 hours.
  //
  // After a subscription expires, it's deleted automatically. You receive
  // lifecycle events to the
  // [notification_endpoint][google.apps.events.subscriptions.v1.Subscription.notification_endpoint]
  // 12 hours and one hour before the subscription expires. For details, see
  // [Receive and respond to lifecycle
  // events](https://developers.google.com/workspace/events/guides/events-lifecycle).
  //
  // To prevent a subscription from expiring, you can use the
  // [`UpdateSubscription`][google.apps.events.subscriptions.v1.SubscriptionsService.UpdateSubscription]
  // method to extend its expiration date. For details, see [Update or renew a
  // subscription](https://developers.google.com/workspace/events/guides/update-subscription).
  oneof expiration {
    // Non-empty default. The timestamp in UTC when the subscription expires.
    // Always displayed on output, regardless of what was used on input.
    google.protobuf.Timestamp expire_time = 13
        [(google.api.field_behavior) = NON_EMPTY_DEFAULT];

    // Input only. The time-to-live (TTL) or duration for the subscription. If
    // unspecified or set to `0`, uses the maximum possible duration.
    google.protobuf.Duration ttl = 14
        [(google.api.field_behavior) = INPUT_ONLY];
  }

  // Optional. Immutable. Identifier. Resource name of the subscription.
  //
  // Format: `subscriptions/{subscription}`
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Output only. System-assigned unique identifier for the subscription.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The Google Workspace resource that's monitored for
  // events, formatted as the [full resource
  // name](https://google.aip.dev/122#full-resource-names). To learn about
  // target resources and the events that they support, see [Supported Google
  // Workspace
  // events](https://developers.google.com/workspace/events#supported-events).
  //
  // A user can only authorize your app to create one subscription for a given
  // target resource. If your app tries to create another subscription with the
  // same user credentials, the request returns an `ALREADY_EXISTS` error.
  string target_resource = 4 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // Required. Immutable. Unordered list. Input for creating a subscription.
  // Otherwise, output only. One or more types of events to receive about the
  // target resource. Formatted according to the CloudEvents specification.
  //
  // The supported event types depend on the target resource of your
  // subscription. For details, see [Supported Google Workspace
  // events](https://developers.google.com/workspace/events/guides#supported-events).
  //
  // By default, you also receive events about the [lifecycle of your
  // subscription](https://developers.google.com/workspace/events/guides/events-lifecycle).
  // You don't need to specify lifecycle events for this field.
  //
  // If you specify an event type that doesn't exist for the target resource,
  // the request returns an HTTP `400 Bad Request` status code.
  repeated string event_types = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = UNORDERED_LIST,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Options about what data to include in the event payload. Only
  // supported for Google Chat events.
  PayloadOptions payload_options = 6 [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. The endpoint where the subscription delivers events,
  // such as a Pub/Sub topic.
  NotificationEndpoint notification_endpoint = 7 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. The state of the subscription. Determines whether the
  // subscription can receive events and deliver them to the notification
  // endpoint.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The error that suspended the subscription.
  //
  // To reactivate the subscription, resolve the error and call the
  // [`ReactivateSubscription`][google.apps.events.subscriptions.v1.SubscriptionsService.ReactivateSubscription]
  // method.
  ErrorType suspension_reason = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who authorized the creation of the
  // subscription.
  //
  // Format: `users/{user}`
  //
  // For Google Workspace users, the `{user}` value is the
  // [`user.id`](https://developers.google.com/admin-sdk/directory/reference/rest/v1/users#User.FIELDS.ids)
  // field from the Directory API.
  string authority = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudidentity.googleapis.com/User"
    }
  ];

  // Output only. The time when the subscription is created.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last time that the subscription is updated.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If `true`, the subscription is in the process of being
  // updated.
  bool reconciling = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and might be sent on update requests to ensure the client has
  // an up-to-date value before proceeding.
  string etag = 17 [(google.api.field_behavior) = OPTIONAL];
}

// Options about what data to include in the event payload. Only supported for
// Google Chat events.
message PayloadOptions {
  // Optional. Whether the event payload includes data about the resource that
  // changed. For example, for an event where a Google Chat message was created,
  // whether the payload contains data about the
  // [`Message`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages)
  // resource. If false, the event payload only includes the name of the changed
  // resource.
  bool include_resource = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If `include_resource` is set to `true`, the list of fields to
  // include in the event payload. Separate fields with a comma. For example, to
  // include a Google Chat message's sender and create time, enter
  // `message.sender,message.createTime`. If omitted, the payload includes all
  // fields for the resource.
  //
  // If you specify a field that doesn't exist for the resource, the system
  // ignores the field.
  google.protobuf.FieldMask field_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// The endpoint where the subscription delivers events.
message NotificationEndpoint {
  oneof endpoint {
    // Immutable. The Cloud Pub/Sub topic that receives events for the
    // subscription.
    //
    // Format: `projects/{project}/topics/{topic}`
    //
    //
    // You must create the topic in the same Google Cloud project where
    // you create this subscription.
    //
    // When the topic receives events, the events are encoded as Cloud Pub/Sub
    // messages. For details, see the [Google Cloud Pub/Sub Protocol Binding for
    // CloudEvents](https://github.com/googleapis/google-cloudevents/blob/main/docs/spec/pubsub.md).
    string pubsub_topic = 1 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
    ];
  }
}
