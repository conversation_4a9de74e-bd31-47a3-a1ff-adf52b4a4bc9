# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "subscriptions_proto",
    srcs = [
        "subscription_resource.proto",
        "subscriptions_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "subscriptions_proto_with_info",
    deps = [
        ":subscriptions_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "subscriptions_java_proto",
    deps = [":subscriptions_proto"],
)

java_grpc_library(
    name = "subscriptions_java_grpc",
    srcs = [":subscriptions_proto"],
    deps = [":subscriptions_java_proto"],
)

java_gapic_library(
    name = "subscriptions_java_gapic",
    srcs = [":subscriptions_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "subscriptions_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workspaceevents_v1.yaml",
    test_deps = [
        ":subscriptions_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":subscriptions_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "subscriptions_java_gapic_test_suite",
    test_classes = [
        "com.google.apps.events.subscriptions.v1.SubscriptionsServiceClientHttpJsonTest",
        "com.google.apps.events.subscriptions.v1.SubscriptionsServiceClientTest",
    ],
    runtime_deps = [":subscriptions_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-events-subscriptions-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":subscriptions_java_gapic",
        ":subscriptions_java_grpc",
        ":subscriptions_java_proto",
        ":subscriptions_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "subscriptions_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/apps/events/subscriptions/apiv1/subscriptionspb",
    protos = [":subscriptions_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "subscriptions_go_gapic",
    srcs = [":subscriptions_proto_with_info"],
    grpc_service_config = "subscriptions_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/apps/events/subscriptions/apiv1;subscriptions",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "workspaceevents_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":subscriptions_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-events-subscriptions-v1-go",
    deps = [
        ":subscriptions_go_gapic",
        ":subscriptions_go_gapic_srcjar-metadata.srcjar",
        ":subscriptions_go_gapic_srcjar-snippets.srcjar",
        ":subscriptions_go_gapic_srcjar-test.srcjar",
        ":subscriptions_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "subscriptions_py_gapic",
    srcs = [":subscriptions_proto"],
    grpc_service_config = "subscriptions_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workspaceevents_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
    opt_args = [
        "python-gapic-namespace=google.apps",
        "python-gapic-name=events_subscriptions",
    ],
)

py_test(
    name = "subscriptions_py_gapic_test",
    srcs = [
        "subscriptions_py_gapic_pytest.py",
        "subscriptions_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":subscriptions_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "events-subscriptions-v1-py",
    deps = [
        ":subscriptions_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "subscriptions_php_proto",
    deps = [":subscriptions_proto"],
)

php_gapic_library(
    name = "subscriptions_php_gapic",
    srcs = [":subscriptions_proto_with_info"],
    grpc_service_config = "subscriptions_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "workspaceevents_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":subscriptions_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-events-subscriptions-v1-php",
    deps = [
        ":subscriptions_php_gapic",
        ":subscriptions_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "subscriptions_nodejs_gapic",
    package_name = "@google-cloud/subscriptions",
    src = ":subscriptions_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "subscriptions_v1_grpc_service_config.json",
    package = "google.apps.events.subscriptions.v1",
    rest_numeric_enums = True,
    service_yaml = "workspaceevents_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "events-subscriptions-v1-nodejs",
    deps = [
        ":subscriptions_nodejs_gapic",
        ":subscriptions_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "subscriptions_ruby_proto",
    deps = [":subscriptions_proto"],
)

ruby_grpc_library(
    name = "subscriptions_ruby_grpc",
    srcs = [":subscriptions_proto"],
    deps = [":subscriptions_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "subscriptions_ruby_gapic",
    srcs = [":subscriptions_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-apps-events-subscriptions-v1"],
    grpc_service_config = "subscriptions_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workspaceevents_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":subscriptions_ruby_grpc",
        ":subscriptions_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-apps-events-subscriptions-v1-ruby",
    deps = [
        ":subscriptions_ruby_gapic",
        ":subscriptions_ruby_grpc",
        ":subscriptions_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "subscriptions_csharp_proto",
    deps = [":subscriptions_proto"],
)

csharp_grpc_library(
    name = "subscriptions_csharp_grpc",
    srcs = [":subscriptions_proto"],
    deps = [":subscriptions_csharp_proto"],
)

csharp_gapic_library(
    name = "subscriptions_csharp_gapic",
    srcs = [":subscriptions_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "subscriptions_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workspaceevents_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":subscriptions_csharp_grpc",
        ":subscriptions_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-events-subscriptions-v1-csharp",
    deps = [
        ":subscriptions_csharp_gapic",
        ":subscriptions_csharp_grpc",
        ":subscriptions_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "subscriptions_cc_proto",
    deps = [":subscriptions_proto"],
)

cc_grpc_library(
    name = "subscriptions_cc_grpc",
    srcs = [":subscriptions_proto"],
    grpc_only = True,
    deps = [":subscriptions_cc_proto"],
)
