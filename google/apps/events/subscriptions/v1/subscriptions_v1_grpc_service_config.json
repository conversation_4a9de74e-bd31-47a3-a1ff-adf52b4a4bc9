{"methodConfig": [{"name": [{"service": "google.apps.events.subscriptions.v1.SubscriptionsService", "method": "GetSubscription"}, {"service": "google.apps.events.subscriptions.v1.SubscriptionsService", "method": "ListSubscriptions"}, {"service": "google.longrunning.Operations", "method": "GetOperation"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.apps.events.subscriptions.v1.SubscriptionsService", "method": "CreateSubscription"}, {"service": "google.apps.events.subscriptions.v1.SubscriptionsService", "method": "DeleteSubscription"}, {"service": "google.apps.events.subscriptions.v1.SubscriptionsService", "method": "UpdateSubscription"}, {"service": "google.apps.events.subscriptions.v1.SubscriptionsService", "method": "ReactivateSubscription"}], "timeout": "60s"}]}