// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.events.subscriptions.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/apps/events/subscriptions/v1/subscription_resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Apps.Events.Subscriptions.V1";
option go_package = "cloud.google.com/go/apps/events/subscriptions/apiv1/subscriptionspb;subscriptionspb";
option java_multiple_files = true;
option java_outer_classname = "SubscriptionsServiceProto";
option java_package = "com.google.apps.events.subscriptions.v1";
option php_namespace = "Google\\Apps\\Events\\Subscriptions\\V1";
option ruby_package = "Google::Apps::Events::Subscriptions::V1";

// A service that manages subscriptions to Google Workspace events.
service SubscriptionsService {
  option (google.api.default_host) = "workspaceevents.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/chat.bot,"
      "https://www.googleapis.com/auth/chat.memberships,"
      "https://www.googleapis.com/auth/chat.memberships.readonly,"
      "https://www.googleapis.com/auth/chat.messages,"
      "https://www.googleapis.com/auth/chat.messages.reactions,"
      "https://www.googleapis.com/auth/chat.messages.reactions.readonly,"
      "https://www.googleapis.com/auth/chat.messages.readonly,"
      "https://www.googleapis.com/auth/chat.spaces,"
      "https://www.googleapis.com/auth/chat.spaces.readonly,"
      "https://www.googleapis.com/auth/meetings.space.created,"
      "https://www.googleapis.com/auth/meetings.space.readonly";

  // Creates a Google Workspace subscription. To learn how to use this
  // method, see [Create a Google Workspace
  // subscription](https://developers.google.com/workspace/events/guides/create-subscription).
  rpc CreateSubscription(CreateSubscriptionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/subscriptions"
      body: "subscription"
    };
    option (google.api.method_signature) = "subscription";
    option (google.longrunning.operation_info) = {
      response_type: "Subscription"
      metadata_type: "CreateSubscriptionMetadata"
    };
  }

  // Deletes a Google Workspace subscription.
  // To learn how to use this method, see [Delete a Google Workspace
  // subscription](https://developers.google.com/workspace/events/guides/delete-subscription).
  rpc DeleteSubscription(DeleteSubscriptionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=subscriptions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteSubscriptionMetadata"
    };
  }

  // Gets details about a Google Workspace subscription. To learn how to use
  // this method, see [Get details about a Google Workspace
  // subscription](https://developers.google.com/workspace/events/guides/get-subscription).
  rpc GetSubscription(GetSubscriptionRequest) returns (Subscription) {
    option (google.api.http) = {
      get: "/v1/{name=subscriptions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Google Workspace subscriptions. To learn how to use this method, see
  // [List Google Workspace
  // subscriptions](https://developers.google.com/workspace/events/guides/list-subscriptions).
  rpc ListSubscriptions(ListSubscriptionsRequest)
      returns (ListSubscriptionsResponse) {
    option (google.api.http) = {
      get: "/v1/subscriptions"
    };
    option (google.api.method_signature) = "filter";
  }

  // Updates or renews a Google Workspace subscription. To learn how to use this
  // method, see [Update or renew a Google Workspace
  // subscription](https://developers.google.com/workspace/events/guides/update-subscription).
  rpc UpdateSubscription(UpdateSubscriptionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{subscription.name=subscriptions/*}"
      body: "subscription"
    };
    option (google.api.method_signature) = "subscription,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Subscription"
      metadata_type: "UpdateSubscriptionMetadata"
    };
  }

  // Reactivates a suspended Google Workspace subscription.
  //
  // This method resets your subscription's `State` field to `ACTIVE`. Before
  // you use this method, you must fix the error that suspended the
  // subscription. To learn how to use this method, see [Reactivate a Google
  // Workspace
  // subscription](https://developers.google.com/workspace/events/guides/reactivate-subscription).
  rpc ReactivateSubscription(ReactivateSubscriptionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=subscriptions/*}:reactivate"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Subscription"
      metadata_type: "ReactivateSubscriptionMetadata"
    };
  }
}

// The request message for
// [SubscriptionsService.CreateSubscription][google.apps.events.subscriptions.v1.SubscriptionsService.CreateSubscription].
message CreateSubscriptionRequest {
  // Required. The subscription resource to create.
  Subscription subscription = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set to `true`, validates and previews the request, but doesn't
  // create the subscription.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [SubscriptionsService.DeleteSubscription][google.apps.events.subscriptions.v1.SubscriptionsService.DeleteSubscription].
message DeleteSubscriptionRequest {
  // Required. Resource name of the subscription to delete.
  //
  // Format: `subscriptions/{subscription}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workspaceevents.googleapis.com/Subscription"
    }
  ];

  // Optional. If set to `true`, validates and previews the request, but doesn't
  // delete the subscription.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to `true` and the subscription isn't found, the request
  // succeeds but doesn't delete the subscription.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Etag of the subscription.
  //
  // If present, it must match with the server's etag. Otherwise, request
  // fails with the status `ABORTED`.
  string etag = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [SubscriptionsService.GetSubscription][google.apps.events.subscriptions.v1.SubscriptionsService.GetSubscription].
message GetSubscriptionRequest {
  // Required. Resource name of the subscription.
  //
  // Format: `subscriptions/{subscription}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workspaceevents.googleapis.com/Subscription"
    }
  ];
}

// The request message for
// [SubscriptionsService.UpdateSubscription][google.apps.events.subscriptions.v1.SubscriptionsService.UpdateSubscription].
message UpdateSubscriptionRequest {
  // Required. The subscription to update.
  //
  // The subscription's `name` field is used to identify the subscription to
  // update.
  Subscription subscription = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Required. The field to update.
  //
  // You can update one of the following fields in a subscription:
  //
  // * [`expire_time`][google.apps.events.subscriptions.v1.Subscription.expire_time]: The timestamp when the
  //   subscription expires.
  // * [`ttl`][google.apps.events.subscriptions.v1.Subscription.ttl]: The
  // time-to-live (TTL) or duration of the
  //   subscription.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to `true`, validates and previews the request, but doesn't
  // update the subscription.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [SubscriptionsService.ReactivateSubscription][google.apps.events.subscriptions.v1.SubscriptionsService.ReactivateSubscription].
message ReactivateSubscriptionRequest {
  // Required. Resource name of the subscription.
  //
  // Format: `subscriptions/{subscription}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workspaceevents.googleapis.com/Subscription"
    }
  ];
}

// The request message for
// [SubscriptionsService.ListSubscriptions][google.apps.events.subscriptions.v1.SubscriptionsService.ListSubscriptions].
message ListSubscriptionsRequest {
  // Optional. The maximum number of subscriptions to return. The service might
  // return fewer than this value.
  //
  // If unspecified or set to `0`, up to 50 subscriptions are returned.
  //
  // The maximum value is 100. If you specify a value more than 100, the system
  // only returns 100 subscriptions.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous list subscriptions call.
  // Provide this parameter to retrieve the subsequent page.
  //
  // When paginating, the filter value should match the call that provided the
  // page token. Passing a different value might lead to unexpected results.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A query filter.
  //
  // You can filter subscriptions by event type (`event_types`)
  // and target resource (`target_resource`).
  //
  // You must specify at least one event type in your query. To filter for
  // multiple event types, use the `OR` operator.
  //
  // To filter by both event type and target resource, use the `AND` operator
  // and specify the full resource name, such as
  // `//chat.googleapis.com/spaces/{space}`.
  //
  // For example, the following queries are valid:
  //
  // ```
  // event_types:"google.workspace.chat.membership.v1.updated" OR
  //   event_types:"google.workspace.chat.message.v1.created"
  //
  // event_types:"google.workspace.chat.message.v1.created" AND
  //   target_resource="//chat.googleapis.com/spaces/{space}"
  //
  // ( event_types:"google.workspace.chat.membership.v1.updated" OR
  //   event_types:"google.workspace.chat.message.v1.created" ) AND
  //   target_resource="//chat.googleapis.com/spaces/{space}"
  // ```
  //
  // The server rejects invalid queries with an `INVALID_ARGUMENT`
  // error.
  string filter = 3 [(google.api.field_behavior) = REQUIRED];
}

// The response message for
// [SubscriptionsService.ListSubscriptions][google.apps.events.subscriptions.v1.SubscriptionsService.ListSubscriptions].
message ListSubscriptionsResponse {
  // List of subscriptions.
  repeated Subscription subscriptions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Metadata for UpdateSubscription LRO.
message UpdateSubscriptionMetadata {}

// Metadata for CreateSubscription LRO.
message CreateSubscriptionMetadata {}

// Metadata for DeleteSubscription LRO.
message DeleteSubscriptionMetadata {}

// Metadata for ReactivateSubscription LRO.
message ReactivateSubscriptionMetadata {}
