type: google.api.Service
config_version: 3
name: workspaceevents.googleapis.com
title: Google Workspace Events API

apis:
- name: google.apps.events.subscriptions.v1.SubscriptionsService
- name: google.longrunning.Operations

documentation:
  summary: |-
    The Google Workspace Events API lets you subscribe to events and manage
    change notifications across Google Workspace applications.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=operations/**}'

authentication:
  rules:
  - selector: 'google.apps.events.subscriptions.v1.SubscriptionsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.readonly,
        https://www.googleapis.com/auth/chat.messages.readonly,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly,
        https://www.googleapis.com/auth/meetings.space.created,
        https://www.googleapis.com/auth/meetings.space.readonly
  - selector: google.apps.events.subscriptions.v1.SubscriptionsService.DeleteSubscription
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.readonly,
        https://www.googleapis.com/auth/chat.messages.readonly,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly,
        https://www.googleapis.com/auth/meetings.space.created,
        https://www.googleapis.com/auth/meetings.space.readonly
  - selector: google.apps.events.subscriptions.v1.SubscriptionsService.GetSubscription
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.readonly,
        https://www.googleapis.com/auth/chat.messages.readonly,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly,
        https://www.googleapis.com/auth/meetings.space.created,
        https://www.googleapis.com/auth/meetings.space.readonly
  - selector: google.apps.events.subscriptions.v1.SubscriptionsService.ListSubscriptions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.readonly,
        https://www.googleapis.com/auth/chat.messages.readonly,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly,
        https://www.googleapis.com/auth/meetings.space.created,
        https://www.googleapis.com/auth/meetings.space.readonly
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/chat.bot,
        https://www.googleapis.com/auth/chat.memberships,
        https://www.googleapis.com/auth/chat.memberships.readonly,
        https://www.googleapis.com/auth/chat.messages,
        https://www.googleapis.com/auth/chat.messages.reactions,
        https://www.googleapis.com/auth/chat.messages.reactions.readonly,
        https://www.googleapis.com/auth/chat.messages.readonly,
        https://www.googleapis.com/auth/chat.spaces,
        https://www.googleapis.com/auth/chat.spaces.readonly,
        https://www.googleapis.com/auth/meetings.space.created,
        https://www.googleapis.com/auth/meetings.space.readonly

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1208006&template=1723043
  documentation_uri: https://developers.google.com/workspace/events
  api_short_name: workspaceevents
  github_label: 'api: workspaceevents'
  doc_tag_prefix: workspaceevents
  organization: CLOUD
  library_settings:
  - version: google.apps.events.subscriptions.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://developers.google.com/workspace/events/reference/rest/
