// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.script.type;

import "google/apps/script/type/addon_widget_set.proto";
import "google/apps/script/type/extension_point.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Apps.Script.Type";
option go_package = "google.golang.org/genproto/googleapis/apps/script/type";
option java_multiple_files = true;
option java_package = "com.google.apps.script.type";
option php_namespace = "Google\\Apps\\Script\\Type";
option ruby_package = "Google::Apps::Script::Type";

// Add-on configuration that is shared across all add-on host applications.
message CommonAddOnManifest {
  // Required. The display name of the add-on.
  string name = 1;

  // Required. The URL for the logo image shown in the add-on toolbar.
  string logo_url = 2;

  // Common layout properties for the add-on cards.
  LayoutProperties layout_properties = 3;

  // The widgets used in the add-on. If this field is not specified,
  // it indicates that default set is used.
  AddOnWidgetSet add_on_widget_set = 4;

  // Whether to pass locale information from host app.
  bool use_locale_from_app = 5;

  // Defines an endpoint that will be executed in any context, in
  // any host. Any cards generated by this function will always be available to
  // the user, but may be eclipsed by contextual content when this add-on
  // declares more targeted triggers.
  HomepageExtensionPoint homepage_trigger = 6;

  // Defines a list of extension points in the universal action menu which
  // serves as a setting menu for the add-on. The extension point can be
  // link URL to open or an endpoint to execute as a form
  // submission.
  repeated UniversalActionExtensionPoint universal_actions = 7;

  // An OpenLink action
  // can only use a URL with an HTTPS, MAILTO or TEL scheme.  For HTTPS links,
  // the URL must also
  // [match](/gmail/add-ons/concepts/manifests#whitelisting_urls) one of the
  // prefixes specified in this whitelist. If the prefix omits the scheme, HTTPS
  // is assumed.  Notice that HTTP links are automatically rewritten to HTTPS
  // links.
  google.protobuf.ListValue open_link_url_prefixes = 8;
}

// Card layout properties shared across all add-on host applications.
message LayoutProperties {
  // The primary color of the add-on. It sets the color of toolbar. If no
  // primary color is set explicitly, the default value provided by the
  // framework is used.
  string primary_color = 1;

  // The secondary color of the add-on. It sets the color of buttons.
  // If primary color is set but no secondary color is set, the
  // secondary color is the same as the primary color. If neither primary
  // color nor secondary color is set, the default value provided by the
  // framework is used.
  string secondary_color = 2;
}

// Options for sending requests to add-on HTTP endpoints
message HttpOptions {
  // Configuration for the token sent in the HTTP Authorization header
  HttpAuthorizationHeader authorization_header = 1;
}

// Authorization header sent in add-on HTTP requests
enum HttpAuthorizationHeader {
  // Default value, equivalent to `SYSTEM_ID_TOKEN`
  HTTP_AUTHORIZATION_HEADER_UNSPECIFIED = 0;

  // Send an ID token for the project-specific Google Workspace Add-ons system
  // service account (default)
  SYSTEM_ID_TOKEN = 1;

  // Send an ID token for the end user
  USER_ID_TOKEN = 2;

  // Do not send an Authentication header
  NONE = 3;
}
