# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_grpc_library",
    "java_proto_library",
    "php_proto_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "type_proto",
    srcs = [
        "addon_widget_set.proto",
        "extension_point.proto",
        "script_manifest.proto",
    ],
    deps = [
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

java_proto_library(
    name = "type_java_proto",
    deps = [":type_proto"],
)

java_grpc_library(
    name = "type_java_grpc",
    srcs = [":type_proto"],
    deps = [":type_java_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-apps-script-type-java",
    transport = "grpc+rest",
    deps = [
        ":type_java_grpc",
        ":type_java_proto",
        ":type_proto",
    ],
)

go_proto_library(
    name = "type_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/apps/script/type",
    protos = [":type_proto"],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",
)

py_proto_library(
    name = "type_py_proto",
    deps = [":type_proto"],
)

py_gapic_library(
    name = "type_py_gapic",
    srcs = [":type_proto"],
    rest_numeric_enums = False,
    transport = "grpc",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "type-py",
    deps = [
        ":type_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
php_proto_library(
    name = "type_php_proto",
    deps = [":type_proto"],
)

ruby_proto_library(
    name = "type_ruby_proto",
    deps = [":type_proto"],
)

ruby_grpc_library(
    name = "type_ruby_grpc",
    srcs = [":type_proto"],
    deps = [":type_ruby_proto"],
)

csharp_proto_library(
    name = "type_csharp_proto",
    deps = [":type_proto"],
)

csharp_grpc_library(
    name = "type_csharp_grpc",
    srcs = [":type_proto"],
    deps = [":type_csharp_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-apps-script-type-csharp",
    package_name = "Google.Apps.Script.Type",
    generate_nongapic_package = True,
    deps = [
        ":type_csharp_grpc",
        ":type_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
