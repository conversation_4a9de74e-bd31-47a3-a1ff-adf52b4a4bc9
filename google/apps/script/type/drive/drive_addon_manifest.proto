// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.script.type.drive;

import "google/apps/script/type/extension_point.proto";

option csharp_namespace = "Google.Apps.Script.Type.Drive";
option go_package = "google.golang.org/genproto/googleapis/apps/script/type/drive";
option java_multiple_files = true;
option java_outer_classname = "DriveAddOnManifestProto";
option java_package = "com.google.apps.script.type.drive";
option php_namespace = "Google\\Apps\\Script\\Type\\Drive";
option ruby_package = "Google::Apps::Script::Type::Drive";

// Manifest section specific to Drive Add-ons.

// Drive add-on manifest.
message DriveAddOnManifest {
  // If present, this overrides the configuration from
  // `addOns.common.homepageTrigger`.
  google.apps.script.type.HomepageExtensionPoint homepage_trigger = 1;

  // Corresponds to behvior that should execute when items are selected
  // in relevant Drive view (e.g. the My Drive Doclist).
  DriveExtensionPoint on_items_selected_trigger = 2;
}

// A generic extension point with common features, e.g. something that simply
// needs a corresponding run function to work.
message DriveExtensionPoint {
  // Required. The endpoint to execute when this extension point is
  // activated.
  string run_function = 1;
}
