// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.script.type;

import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Apps.Script.Type";
option go_package = "google.golang.org/genproto/googleapis/apps/script/type";
option java_multiple_files = true;
option java_package = "com.google.apps.script.type";
option php_namespace = "Google\\Apps\\Script\\Type";
option ruby_package = "Google::Apps::Script::Type";

// Common Manifest protos for G Suite extension-point configuration.

// Common format for declaring a  menu item, or button, that appears within a
// host app.
message MenuItemExtensionPoint {
  // Required. The endpoint to execute when this extension point is
  // activated.
  string run_function = 1;

  // Required. User-visible text describing the action taken by activating this
  // extension point. For example, "Insert invoice".
  string label = 2;

  // The URL for the logo image shown in the add-on toolbar.
  //
  // If not set, defaults to the add-on's primary logo URL.
  string logo_url = 3;
}

// Common format for declaring an add-on's home-page view.
message HomepageExtensionPoint {
  // Required. The endpoint to execute when this extension point is
  // activated.
  string run_function = 1;

  // Optional. If set to `false`, disable the home-page view in this context.
  //
  // Defaults to `true` if unset.
  //
  // If an add-ons custom home-page view is disabled, an autogenerated overview
  // card will be provided for users instead.
  google.protobuf.BoolValue enabled = 2;
}

// Format for declaring a universal action menu item extension point.
message UniversalActionExtensionPoint {
  // Required. User-visible text describing the action taken by activating this
  // extension point, for example, "Add a new contact".
  string label = 1;

  // Required. The action type supported on a universal action menu item. It
  // could be either a link to open or an endpoint to execute.
  oneof action_type {
    // URL to be opened by the UniversalAction.
    string open_link = 2;

    // Endpoint to be run by the UniversalAction.
    string run_function = 3;
  }
}
