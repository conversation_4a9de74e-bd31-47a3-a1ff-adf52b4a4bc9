// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.script.type.calendar;

import "google/api/field_behavior.proto";
import "google/apps/script/type/extension_point.proto";

option csharp_namespace = "Google.Apps.Script.Type.Calendar";
option go_package = "google.golang.org/genproto/googleapis/apps/script/type/calendar";
option java_multiple_files = true;
option java_outer_classname = "CalendarAddOnManifestProto";
option java_package = "com.google.apps.script.type.calendar";
option php_namespace = "Google\\Apps\\Script\\Type\\Calendar";
option ruby_package = "Google::Apps::Script::Type::Calendar";

// Manifest section specific to Calendar Add-ons.

// Calendar add-on manifest.
message CalendarAddOnManifest {
  // An enum defining the level of data access event triggers require.
  enum EventAccess {
    // Default value when nothing is set for EventAccess.
    UNSPECIFIED = 0;

    // METADATA gives event triggers the permission to access the metadata of
    // events such as event id and calendar id.
    METADATA = 1;

    // READ gives event triggers access to all provided event fields including
    // the metadata, attendees, and conference data.
    READ = 3;

    // WRITE gives event triggers access to the metadata of events and the
    // ability to perform all actions, including adding attendees and setting
    // conference data.
    WRITE = 4;

    // READ_WRITE gives event triggers access to all provided event fields
    // including the metadata, attendees, and conference data and the ability to
    // perform all actions.
    READ_WRITE = 5;
  }

  // Defines an endpoint that will be executed contexts that don't
  // match a declared contextual trigger. Any cards generated by this function
  // will always be available to the user, but may be eclipsed by contextual
  // content when this add-on declares more targeted triggers.
  //
  // If present, this overrides the configuration from
  // `addOns.common.homepageTrigger`.
  google.apps.script.type.HomepageExtensionPoint homepage_trigger = 6;

  // Defines conference solutions provided by this add-on.
  repeated ConferenceSolution conference_solution = 3;

  // An endpoint to execute that creates a URL to the add-on's settings page.
  string create_settings_url_function = 5;

  // An endpoint to trigger when an event is opened (viewed/edited).
  CalendarExtensionPoint event_open_trigger = 10;

  // An endpoint to trigger when the open event is updated.
  CalendarExtensionPoint event_update_trigger = 11;

  // Define the level of data access when an event addon is triggered.
  EventAccess current_event_access = 12;
}

// Defines conference related values.
message ConferenceSolution {
  // Required. The endpoint to call when ConferenceData should be created.
  string on_create_function = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. IDs should be unique across ConferenceSolutions within one
  // add-on, but this is not strictly enforced. It is up to the add-on developer
  // to assign them uniquely, otherwise the wrong ConferenceSolution may be
  // used when the add-on is triggered. While the developer may change the
  // display name of an add-on, the ID should not be changed.
  string id = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The display name of the ConferenceSolution.
  string name = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The URL for the logo image of the ConferenceSolution.
  string logo_url = 6 [(google.api.field_behavior) = REQUIRED];
}

// Common format for declaring a calendar add-on's triggers.
message CalendarExtensionPoint {
  // Required. The endpoint to execute when this extension point is
  // activated.
  string run_function = 1 [(google.api.field_behavior) = REQUIRED];
}
