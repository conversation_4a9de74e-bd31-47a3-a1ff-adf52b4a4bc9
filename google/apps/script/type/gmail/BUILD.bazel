# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_grpc_library",
    "java_proto_library",
    "php_proto_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "gmail_proto",
    srcs = [
        "gmail_addon_manifest.proto",
    ],
    deps = [
        "//google/apps/script/type:type_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

java_proto_library(
    name = "gmail_java_proto",
    deps = [":gmail_proto"],
)

java_grpc_library(
    name = "gmail_java_grpc",
    srcs = [":gmail_proto"],
    deps = [":gmail_java_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-apps-script-type-gmail-java",
    transport = "grpc+rest",
    deps = [
        ":gmail_java_grpc",
        ":gmail_java_proto",
        ":gmail_proto",
    ],
)

go_proto_library(
    name = "gmail_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/apps/script/type/gmail",
    protos = [":gmail_proto"],
    deps = [
        "//google/apps/script/type:type_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",
)

py_proto_library(
    name = "gmail_py_proto",
    deps = [":gmail_proto"],
)

py_gapic_library(
    name = "gmail_py_gapic",
    srcs = [":gmail_proto"],
    rest_numeric_enums = False,
    transport = "grpc",
    opt_args = [
        "proto-plus-deps=google.apps.script.type",
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "gmail-py",
    deps = [
        ":gmail_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
php_proto_library(
    name = "gmail_php_proto",
    deps = [":gmail_proto"],
)

ruby_proto_library(
    name = "gmail_ruby_proto",
    deps = [":gmail_proto"],
)

ruby_grpc_library(
    name = "gmail_ruby_grpc",
    srcs = [":gmail_proto"],
    deps = [":gmail_ruby_proto"],
)

csharp_proto_library(
    name = "gmail_csharp_proto",
    deps = [":gmail_proto"],
)

csharp_grpc_library(
    name = "gmail_csharp_grpc",
    srcs = [":gmail_proto"],
    deps = [":gmail_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
