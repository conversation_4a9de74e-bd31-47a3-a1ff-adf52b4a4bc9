// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.script.type.sheets;

import "google/api/field_behavior.proto";
import "google/apps/script/type/extension_point.proto";

option csharp_namespace = "Google.Apps.Script.Type.Sheets";
option go_package = "google.golang.org/genproto/googleapis/apps/script/type/sheets";
option java_multiple_files = true;
option java_outer_classname = "SheetsAddOnManifestProto";
option java_package = "com.google.apps.script.type.sheets";
option php_namespace = "Google\\Apps\\Script\\Type\\Sheets";
option ruby_package = "Google::Apps::Script::Type::Sheets";

// Manifest section specific to Sheets Add-ons.

// Sheets add-on manifest.
message SheetsAddOnManifest {
  // If present, this overrides the configuration from
  // `addOns.common.homepageTrigger`.
  google.apps.script.type.HomepageExtensionPoint homepage_trigger = 3;

  // Endpoint to execute when file scope authorization is granted
  // for this document/user pair.
  SheetsExtensionPoint on_file_scope_granted_trigger = 5;
}

// Common format for declaring a Sheets add-on's triggers.
message SheetsExtensionPoint {
  // Required. The endpoint to execute when this extension point is activated.
  string run_function = 1 [(google.api.field_behavior) = REQUIRED];
}
