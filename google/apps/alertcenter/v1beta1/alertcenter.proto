// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.alertcenter.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/any.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Apps.AlertCenter.V1Beta1";
option go_package = "google.golang.org/genproto/googleapis/apps/alertcenter/v1beta1;alertcenter";
option java_multiple_files = true;
option java_outer_classname = "AlertCenterProto";
option java_package = "com.google.apps.alertcenter.v1beta1";
option objc_class_prefix = "GAIC";
option php_namespace = "Google\\Apps\\AlertCenter\\V1beta1";
option ruby_package = "Google::Apps::AlertCenter::V1beta1";

// Google Workspace Alert Center API (beta).
service AlertCenterService {
  option (google.api.default_host) = "alertcenter.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/apps.alerts";

  // Lists the alerts.
  rpc ListAlerts(ListAlertsRequest) returns (ListAlertsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/alerts"
    };
  }

  // Gets the specified alert. Attempting to get a nonexistent alert returns
  // `NOT_FOUND` error.
  rpc GetAlert(GetAlertRequest) returns (Alert) {
    option (google.api.http) = {
      get: "/v1beta1/alerts/{alert_id}"
    };
  }

  // Marks the specified alert for deletion. An alert that has been marked for
  // deletion is removed from Alert Center after 30 days.
  // Marking an alert for deletion has no effect on an alert which has
  // already been marked for deletion. Attempting to mark a nonexistent alert
  // for deletion results in a `NOT_FOUND` error.
  rpc DeleteAlert(DeleteAlertRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/alerts/{alert_id}"
    };
  }

  // Restores, or "undeletes", an alert that was marked for deletion within the
  // past 30 days. Attempting to undelete an alert which was marked for deletion
  // over 30 days ago (which has been removed from the Alert Center database) or
  // a nonexistent alert returns a `NOT_FOUND` error. Attempting to
  // undelete an alert which has not been marked for deletion has no effect.
  rpc UndeleteAlert(UndeleteAlertRequest) returns (Alert) {
    option (google.api.http) = {
      post: "/v1beta1/alerts/{alert_id}:undelete"
      body: "*"
    };
  }

  // Creates new feedback for an alert. Attempting to create a feedback for
  // a non-existent alert returns `NOT_FOUND` error. Attempting to create a
  // feedback for an alert that is marked for deletion returns
  // `FAILED_PRECONDITION' error.
  rpc CreateAlertFeedback(CreateAlertFeedbackRequest) returns (AlertFeedback) {
    option (google.api.http) = {
      post: "/v1beta1/alerts/{alert_id}/feedback"
      body: "feedback"
    };
  }

  // Lists all the feedback for an alert. Attempting to list feedbacks for
  // a non-existent alert returns `NOT_FOUND` error.
  rpc ListAlertFeedback(ListAlertFeedbackRequest)
      returns (ListAlertFeedbackResponse) {
    option (google.api.http) = {
      get: "/v1beta1/alerts/{alert_id}/feedback"
    };
  }

  // Returns the metadata of an alert. Attempting to get metadata for
  // a non-existent alert returns `NOT_FOUND` error.
  rpc GetAlertMetadata(GetAlertMetadataRequest) returns (AlertMetadata) {
    option (google.api.http) = {
      get: "/v1beta1/alerts/{alert_id}/metadata"
    };
  }

  // Returns customer-level settings.
  rpc GetSettings(GetSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      get: "/v1beta1/settings"
    };
  }

  // Updates the customer-level settings.
  rpc UpdateSettings(UpdateSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      patch: "/v1beta1/settings"
      body: "settings"
    };
  }

  // Performs batch delete operation on alerts.
  rpc BatchDeleteAlerts(BatchDeleteAlertsRequest)
      returns (BatchDeleteAlertsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/alerts:batchDelete"
      body: "*"
    };
  }

  // Performs batch undelete operation on alerts.
  rpc BatchUndeleteAlerts(BatchUndeleteAlertsRequest)
      returns (BatchUndeleteAlertsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/alerts:batchUndelete"
      body: "*"
    };
  }
}

// An alert affecting a customer.
message Alert {
  // Output only. The unique identifier of the Google Workspace account of the
  // customer.
  string customer_id = 1;

  // Output only. The unique identifier for the alert.
  string alert_id = 2;

  // Output only. The time this alert was created.
  google.protobuf.Timestamp create_time = 3;

  // Required. The time the event that caused this alert was started or
  // detected.
  google.protobuf.Timestamp start_time = 4;

  // Optional. The time the event that caused this alert ceased being active.
  // If provided, the end time must not be earlier than the start time.
  // If not provided, it indicates an ongoing alert.
  google.protobuf.Timestamp end_time = 5;

  // Required. The type of the alert.
  // This is output only after alert is created.
  // For a list of available alert types see
  // [Google Workspace Alert
  // types](https://developers.google.com/admin-sdk/alertcenter/reference/alert-types).
  string type = 6;

  // Required. A unique identifier for the system that reported the alert.
  // This is output only after alert is created.
  //
  // Supported sources are any of the following:
  //
  // * Google Operations
  // * Mobile device management
  // * Gmail phishing
  // * Data Loss Prevention
  // * Domain wide takeout
  // * State sponsored attack
  // * Google identity
  // * Apps outage
  string source = 7;

  // Optional. The data associated with this alert, for example
  // [google.apps.alertcenter.type.DeviceCompromised]
  // [google.apps.alertcenter.type.DeviceCompromised].
  google.protobuf.Any data = 8;

  // Output only. An optional
  // [Security Investigation Tool](https://support.google.com/a/answer/7575955)
  // query for this alert.
  string security_investigation_tool_link = 9;

  // Output only. `True` if this alert is marked for deletion.
  bool deleted = 11;

  // Output only. The metadata associated with this alert.
  AlertMetadata metadata = 12;

  // Output only. The time this alert was last updated.
  google.protobuf.Timestamp update_time = 13;

  // Optional. `etag` is used for optimistic concurrency control as a way to
  // help prevent simultaneous updates of an alert from overwriting each other.
  // It is strongly suggested that systems make use of the `etag` in the
  // read-modify-write cycle to perform alert updates in order to avoid race
  // conditions: An `etag` is returned in the response which contains alerts,
  // and systems are expected to put that etag in the request to update alert to
  // ensure that their change will be applied to the same version of the alert.
  //
  // If no `etag` is provided in the call to update alert, then the existing
  // alert is overwritten blindly.
  string etag = 14;
}

// A customer feedback about an alert.
message AlertFeedback {
  // Output only. The unique identifier of the Google Workspace account of the
  // customer.
  string customer_id = 1;

  // Output only. The alert identifier.
  string alert_id = 2;

  // Output only. The unique identifier for the feedback.
  string feedback_id = 3;

  // Output only. The time this feedback was created.
  google.protobuf.Timestamp create_time = 4;

  // Required. The type of the feedback.
  AlertFeedbackType type = 5;

  // Output only. The email of the user that provided the feedback.
  string email = 6;
}

// An alert metadata.
message AlertMetadata {
  // Output only. The unique identifier of the Google Workspace account of the
  // customer.
  string customer_id = 1;

  // Output only. The alert identifier.
  string alert_id = 2;

  // The current status of the alert.
  // The supported values are the following:
  //
  // * NOT_STARTED
  // * IN_PROGRESS
  // * CLOSED
  string status = 4;

  // The email address of the user assigned to the alert.
  string assignee = 5;

  // Output only. The time this metadata was last updated.
  google.protobuf.Timestamp update_time = 6;

  // The severity value of the alert. Alert Center will set this field at alert
  // creation time, default's to an empty string when it could not be
  // determined.
  // The supported values for update actions on this field are the following:
  //
  // * HIGH
  // * MEDIUM
  // * LOW
  string severity = 7;

  // Optional. `etag` is used for optimistic concurrency control as a way to
  // help prevent simultaneous updates of an alert metadata from overwriting
  // each other. It is strongly suggested that systems make use of the `etag` in
  // the read-modify-write cycle to perform metadata updates in order to avoid
  // race conditions: An `etag` is returned in the response which contains alert
  // metadata, and systems are expected to put that etag in the request to
  // update alert metadata to ensure that their change will be applied to the
  // same version of the alert metadata.
  //
  // If no `etag` is provided in the call to update alert metadata, then the
  // existing alert metadata is overwritten blindly.
  string etag = 8;
}

// The type of alert feedback.
enum AlertFeedbackType {
  // The feedback type is not specified.
  ALERT_FEEDBACK_TYPE_UNSPECIFIED = 0;

  // The alert report is not useful.
  NOT_USEFUL = 1;

  // The alert report is somewhat useful.
  SOMEWHAT_USEFUL = 2;

  // The alert report is very useful.
  VERY_USEFUL = 3;
}

// Customer-level settings.
message Settings {
  // Settings for callback notifications.
  // For more details see [Google Workspace Alert
  // Notification](https://developers.google.com/admin-sdk/alertcenter/guides/notifications).
  message Notification {
    // The format of the payload.
    enum PayloadFormat {
      // Payload format is not specified (will use JSON as default).
      PAYLOAD_FORMAT_UNSPECIFIED = 0;

      // Use JSON.
      JSON = 1;
    }

    // A reference to a Cloud Pubsub topic.
    //
    // To register for notifications, the owner of the topic must grant
    // `<EMAIL>` the
    //  `projects.topics.publish` permission.
    message CloudPubsubTopic {
      // The `name` field of a Cloud Pubsub [Topic]
      // (https://cloud.google.com/pubsub/docs/reference/rest/v1/projects.topics#Topic).
      string topic_name = 1;

      // Optional. The format of the payload that would be sent.
      // If not specified the format will be JSON.
      PayloadFormat payload_format = 2;
    }

    // Exactly one destination to be specified.
    oneof destination {
      // A Google Cloud Pub/sub topic destination.
      CloudPubsubTopic cloud_pubsub_topic = 1;
    }
  }

  // The list of notifications.
  repeated Notification notifications = 1;
}

// A request to perform batch delete on alerts.
message BatchDeleteAlertsRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alerts are associated with. The `customer_id` must have the
  // initial "C" stripped (for example, `046psxkn`). Inferred from the caller
  // identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Required. The list of alert IDs to delete.
  repeated string alert_id = 2;
}

// Response to batch delete operation on alerts.
message BatchDeleteAlertsResponse {
  // The successful list of alert IDs.
  repeated string success_alert_ids = 1;

  // The status details for each failed `alert_id`.
  map<string, google.rpc.Status> failed_alert_status = 2;
}

// A request to perform batch undelete on alerts.
message BatchUndeleteAlertsRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alerts are associated with. The `customer_id` must have the
  // initial "C" stripped (for example, `046psxkn`). Inferred from the caller
  // identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Required. The list of alert IDs to undelete.
  repeated string alert_id = 2;
}

// Response to batch undelete operation on alerts.
message BatchUndeleteAlertsResponse {
  // The successful list of alert IDs.
  repeated string success_alert_ids = 1;

  // The status details for each failed `alert_id`.
  map<string, google.rpc.Status> failed_alert_status = 2;
}

// An alert listing request.
message ListAlertsRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alerts are associated with. The `customer_id` must have the
  // initial "C" stripped (for example, `046psxkn`). Inferred from the caller
  // identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Optional. The requested page size. Server may return fewer items than
  // requested. If unspecified, server picks an appropriate default.
  int32 page_size = 2;

  // Optional. A token identifying a page of results the server should return.
  // If empty, a new iteration is started. To continue an iteration, pass in
  // the value from the previous ListAlertsResponse's
  // [next_page_token][google.apps.alertcenter.v1beta1.ListAlertsResponse.next_page_token]
  // field.
  string page_token = 3;

  // Optional. A query string for filtering alert results.
  // For more details, see [Query
  // filters](https://developers.google.com/admin-sdk/alertcenter/guides/query-filters)
  // and [Supported query filter
  // fields](https://developers.google.com/admin-sdk/alertcenter/reference/filter-fields#alerts.list).
  string filter = 4;

  // Optional. The sort order of the list results.
  // If not specified results may be returned in arbitrary order.
  // You can sort the results in descending order based on the creation
  // timestamp using `order_by="create_time desc"`.
  // Currently, supported sorting are `create_time asc`, `create_time desc`,
  // `update_time desc`
  string order_by = 5;
}

// Response message for an alert listing request.
message ListAlertsResponse {
  // The list of alerts.
  repeated Alert alerts = 1;

  // The token for the next page. If not empty, indicates that there may be more
  // alerts that match the listing request; this value can be used in a
  // subsequent
  // [ListAlertsRequest][google.apps.alertcenter.v1beta1.ListAlertsRequest] to
  // get alerts continuing from last result of the current list call.
  string next_page_token = 2;
}

// Request for a specific alert.
message GetAlertRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alert is associated with. The `customer_id` must have the
  // initial "C" stripped (for example, `046psxkn`). Inferred from the caller
  // identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Required. The identifier of the alert to retrieve.
  string alert_id = 2;
}

// A request to mark a specific alert for deletion.
message DeleteAlertRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alert is associated with. The `customer_id` must have the
  // initial "C" stripped (for example, `046psxkn`). Inferred from the caller
  // identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Required. The identifier of the alert to delete.
  string alert_id = 2;
}

// A request to undelete a specific alert that was marked for deletion.
message UndeleteAlertRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alert is associated with. The `customer_id` must have the
  // initial "C" stripped (for example, `046psxkn`). Inferred from the caller
  // identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Required. The identifier of the alert to undelete.
  string alert_id = 2;
}

// A request to create a new alert feedback.
message CreateAlertFeedbackRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alert is associated with. The `customer_id` must have the
  // initial "C" stripped (for example, `046psxkn`). Inferred from the caller
  // identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Required. The identifier of the alert this feedback belongs to.
  string alert_id = 2;

  // Required. The new alert feedback to create.
  AlertFeedback feedback = 3;
}

// An alert feedback listing request.
message ListAlertFeedbackRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alert is associated with. The `customer_id` must have the
  // initial "C" stripped (for example, `046psxkn`). Inferred from the caller
  // identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Required. The alert identifier.
  // The "-" wildcard could be used to represent all alerts.
  string alert_id = 2;

  // Optional. A query string for filtering alert feedback results.
  // For more details, see [Query
  // filters](https://developers.google.com/admin-sdk/alertcenter/guides/query-filters)
  // and [Supported query filter
  // fields](https://developers.google.com/admin-sdk/alertcenter/reference/filter-fields#alerts.feedback.list).
  string filter = 3;
}

// Response message for an alert feedback listing request.
message ListAlertFeedbackResponse {
  // The list of alert feedback.
  // Feedback entries for each alert are ordered by creation time descending.
  repeated AlertFeedback feedback = 1;
}

// Get the alert metadata.
message GetAlertMetadataRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alert metadata is associated with. The `customer_id` must
  // have the initial "C" stripped (for example, `046psxkn`). Inferred from the
  // caller identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // Required. The identifier of the alert this metadata belongs to.
  string alert_id = 2;
}

// Get the customer level settings.
message GetSettingsRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alert settings are associated with. The `customer_id` must/
  // have the initial "C" stripped (for example, `046psxkn`). Inferred from the
  // caller identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;
}

// Update the customer level settings.
message UpdateSettingsRequest {
  // Optional. The unique identifier of the Google Workspace account of the
  // customer the alert settings are associated with. The `customer_id` must
  // have the initial "C" stripped (for example, `046psxkn`). Inferred from the
  // caller identity if not provided. [Find your customer
  // ID](https://support.google.com/cloudidentity/answer/********).
  string customer_id = 1;

  // The customer settings to update.
  Settings settings = 2;
}
