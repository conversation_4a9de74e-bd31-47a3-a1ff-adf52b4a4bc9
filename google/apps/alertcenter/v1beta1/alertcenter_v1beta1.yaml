type: google.api.Service
config_version: 3
name: alertcenter.googleapis.com
title: Google Workspace Alert Center API

apis:
- name: google.apps.alertcenter.v1beta1.AlertCenterService

documentation:
  summary: |-
    Manages alerts on issues affecting your domain. Note: The current version
    of this API (v1beta1) is available to all Google Workspace customers.

authentication:
  rules:
  - selector: 'google.apps.alertcenter.v1beta1.AlertCenterService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/apps.alerts
