# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "alertcenter_proto",
    srcs = [
        "alertcenter.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "alertcenter_proto_with_info",
    deps = [
        ":alertcenter_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "alertcenter_java_proto",
    deps = [":alertcenter_proto"],
)

java_grpc_library(
    name = "alertcenter_java_grpc",
    srcs = [":alertcenter_proto"],
    deps = [":alertcenter_java_proto"],
)

java_gapic_library(
    name = "alertcenter_java_gapic",
    srcs = [":alertcenter_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "alertcenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "alertcenter_v1beta1.yaml",
    test_deps = [
        ":alertcenter_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":alertcenter_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "alertcenter_java_gapic_test_suite",
    test_classes = [
        "com.google.apps.alertcenter.v1beta1.AlertCenterServiceClientHttpJsonTest",
        "com.google.apps.alertcenter.v1beta1.AlertCenterServiceClientTest",
    ],
    runtime_deps = [":alertcenter_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-apps-alertcenter-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":alertcenter_java_gapic",
        ":alertcenter_java_grpc",
        ":alertcenter_java_proto",
        ":alertcenter_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "alertcenter_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/apps/alertcenter/v1beta1",
    protos = [":alertcenter_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "alertcenter_go_gapic",
    srcs = [":alertcenter_proto_with_info"],
    grpc_service_config = "alertcenter_grpc_service_config.json",
    importpath = "google.golang.org/google/apps/alertcenter/v1beta1;alertcenter",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "alertcenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":alertcenter_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-apps-alertcenter-v1beta1-go",
    deps = [
        ":alertcenter_go_gapic",
        ":alertcenter_go_gapic_srcjar-metadata.srcjar",
        ":alertcenter_go_gapic_srcjar-snippets.srcjar",
        ":alertcenter_go_gapic_srcjar-test.srcjar",
        ":alertcenter_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "alertcenter_py_gapic",
    srcs = [":alertcenter_proto"],
    grpc_service_config = "alertcenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "alertcenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "alertcenter_py_gapic_test",
    srcs = [
        "alertcenter_py_gapic_pytest.py",
        "alertcenter_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":alertcenter_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "apps-alertcenter-v1beta1-py",
    deps = [
        ":alertcenter_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "alertcenter_php_proto",
    deps = [":alertcenter_proto"],
)

php_gapic_library(
    name = "alertcenter_php_gapic",
    srcs = [":alertcenter_proto_with_info"],
    grpc_service_config = "alertcenter_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "alertcenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":alertcenter_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-apps-alertcenter-v1beta1-php",
    deps = [
        ":alertcenter_php_gapic",
        ":alertcenter_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "alertcenter_nodejs_gapic",
    package_name = "@google-cloud/alertcenter",
    src = ":alertcenter_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "alertcenter_grpc_service_config.json",
    package = "google.apps.alertcenter.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "alertcenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "apps-alertcenter-v1beta1-nodejs",
    deps = [
        ":alertcenter_nodejs_gapic",
        ":alertcenter_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "alertcenter_ruby_proto",
    deps = [":alertcenter_proto"],
)

ruby_grpc_library(
    name = "alertcenter_ruby_grpc",
    srcs = [":alertcenter_proto"],
    deps = [":alertcenter_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "alertcenter_ruby_gapic",
    srcs = [":alertcenter_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-apps-alertcenter-v1beta1"],
    grpc_service_config = "alertcenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "alertcenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":alertcenter_ruby_grpc",
        ":alertcenter_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-apps-alertcenter-v1beta1-ruby",
    deps = [
        ":alertcenter_ruby_gapic",
        ":alertcenter_ruby_grpc",
        ":alertcenter_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "alertcenter_csharp_proto",
    deps = [":alertcenter_proto"],
)

csharp_grpc_library(
    name = "alertcenter_csharp_grpc",
    srcs = [":alertcenter_proto"],
    deps = [":alertcenter_csharp_proto"],
)

csharp_gapic_library(
    name = "alertcenter_csharp_gapic",
    srcs = [":alertcenter_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "alertcenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "alertcenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":alertcenter_csharp_grpc",
        ":alertcenter_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-apps-alertcenter-v1beta1-csharp",
    deps = [
        ":alertcenter_csharp_gapic",
        ":alertcenter_csharp_grpc",
        ":alertcenter_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "alertcenter_cc_proto",
    deps = [":alertcenter_proto"],
)

cc_grpc_library(
    name = "alertcenter_cc_grpc",
    srcs = [":alertcenter_proto"],
    grpc_only = True,
    deps = [":alertcenter_cc_proto"],
)
