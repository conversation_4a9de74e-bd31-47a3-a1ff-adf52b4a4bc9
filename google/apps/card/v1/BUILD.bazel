# This file was automatically generated by BuildFileGenerator

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "card_proto",
    srcs = [
        "card.proto",
    ],
    deps = [
        "//google/type:color_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_proto_library",
    "java_gapic_assembly_gradle_pkg",
)

java_proto_library(
    name = "card_java_proto",
    deps = [":card_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-apps-card-v1-java",
    deps = [
        ":card_proto",
        ":card_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
    "go_gapic_assembly_pkg",
)

go_proto_library(
    name = "card_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/apps/card/v1",
    protos = [":card_proto"],
    deps = [
        "//google/type:color_go_proto",
    ],
)

go_gapic_assembly_pkg(
    name = "google-apps-card-v1-go",
    deps = [
        ":card_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
    "py_gapic_library",
    "py_gapic_assembly_pkg",
)

moved_proto_library(
    name = "card_moved_proto",
    srcs = [":card_proto"],
    deps = [
        "//google/type:color_proto",
    ],
)

py_proto_library(
    name = "card_py_proto",
    deps = [":card_moved_proto"],
)

py_grpc_library(
    name = "card_py_grpc",
    srcs = [":card_moved_proto"],
    deps = [":card_py_proto"],
)

py_gapic_library(
    name = "card_py_gapic",
    srcs = [":card_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-apps-card-v1-py",
    deps = [
        ":card_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "card_php_proto",
    deps = [":card_proto"],
)

php_gapic_assembly_pkg(
    name = "google-apps-card-v1-php",
    deps = [
        ":card_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "card_ruby_proto",
    deps = [":card_proto"],
)

ruby_grpc_library(
    name = "card_ruby_grpc",
    srcs = [":card_proto"],
    deps = [":card_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_proto_library",
    "csharp_gapic_assembly_pkg",
)

csharp_proto_library(
    name = "card_csharp_proto",
    deps = [":card_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-apps-card-v1-csharp",
    package_name = "Google.Apps.Card.V1",
    generate_nongapic_package = True,
    deps = [
        ":card_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "card_cc_proto",
    deps = [":card_proto"],
)

cc_grpc_library(
    name = "card_cc_grpc",
    srcs = [":card_proto"],
    grpc_only = True,
    deps = [":card_cc_proto"],
)
