// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.meet.v2beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Apps.Meet.V2Beta";
option go_package = "cloud.google.com/go/apps/meet/apiv2beta/meetpb;meetpb";
option java_multiple_files = true;
option java_outer_classname = "ResourceProto";
option java_package = "com.google.apps.meet.v2beta";
option php_namespace = "Google\\Apps\\Meet\\V2beta";
option ruby_package = "Google::Apps::Meet::V2beta";

// [Developer Preview](https://developers.google.com/workspace/preview).
// Virtual place where conferences are held. Only one active conference can be
// held in one space at any given time.
message Space {
  option (google.api.resource) = {
    type: "meet.googleapis.com/Space"
    pattern: "spaces/{space}"
  };

  // Immutable. Resource name of the space.
  // Format: `spaces/{space}`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. URI used to join meeting, such as
  // `https://meet.google.com/abc-mnop-xyz`.
  string meeting_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type friendly code to join the meeting. Format:
  // `[a-z]+-[a-z]+-[a-z]+` such as `abc-mnop-xyz`. The maximum length is 128
  // characters. Can ONLY be used as alias of the space ID to get the space.
  string meeting_code = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration pertaining to the meeting space.
  SpaceConfig config = 5;

  // Active conference if it exists.
  ActiveConference active_conference = 6;
}

// Active conference.
message ActiveConference {
  // Output only. Reference to 'ConferenceRecord' resource.
  // Format: `conferenceRecords/{conference_record}` where `{conference_record}`
  // is a unique id for each instance of a call within a space.
  string conference_record = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "meet.googleapis.com/ConferenceRecord"
    }
  ];
}

// The configuration pertaining to a meeting space.
message SpaceConfig {
  // Possible access types for a meeting space.
  enum AccessType {
    // Default value specified by the user's organization.
    // Note: This is never returned, as the configured access type is
    // returned instead.
    ACCESS_TYPE_UNSPECIFIED = 0;

    // Anyone with the join information (for example, the URL or phone access
    // information) can join without knocking.
    OPEN = 1;

    // Members of the host's organization, invited external users, and dial-in
    // users can join without knocking. Everyone else must knock.
    TRUSTED = 2;

    // Only invitees can join without knocking. Everyone else must knock.
    RESTRICTED = 3;
  }

  // Entry points that can be used to join a meeting.  Example:
  // `meet.google.com`, the Embed SDK Web, or a mobile application.
  enum EntryPointAccess {
    // Unused.
    ENTRY_POINT_ACCESS_UNSPECIFIED = 0;

    // All entry points are allowed.
    ALL = 1;

    // Only entry points owned by the Google Cloud project that created the
    // space can be used to join meetings in this space.  Apps can use the Embed
    // SDK Web or mobile Meet SDKs to create owned entry points.
    CREATOR_APP_ONLY = 2;
  }

  // Access type of the meeting space that determines who can join without
  // knocking. Default: The user's default access settings.  Controlled by the
  // user's admin for enterprise users or RESTRICTED.
  AccessType access_type = 1;

  // Defines the entry points that can be used to join meetings hosted in this
  // meeting space.
  // Default: EntryPointAccess.ALL
  EntryPointAccess entry_point_access = 2;
}

// [Developer Preview](https://developers.google.com/workspace/preview).
// Single instance of a meeting held in a space.
message ConferenceRecord {
  option (google.api.resource) = {
    type: "meet.googleapis.com/ConferenceRecord"
    pattern: "conferenceRecords/{conference_record}"
    plural: "conferenceRecords"
    singular: "conferenceRecord"
  };

  // Identifier. Resource name of the conference record.
  // Format: `conferenceRecords/{conference_record}` where `{conference_record}`
  // is a unique id for each instance of a call within a space.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Timestamp when the conference started, always set.
  google.protobuf.Timestamp start_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the conference ended.
  // Set for past conferences. Unset if the conference is ongoing.
  google.protobuf.Timestamp end_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server enforced expire time for when this conference record
  // resource is deleted. The resource is deleted 30 days after the conference
  // ends.
  google.protobuf.Timestamp expire_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The space where the conference was held.
  string space = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "meet.googleapis.com/Space" }
  ];
}

// [Developer Preview](https://developers.google.com/workspace/preview).
// User who attended or is attending a conference.
message Participant {
  option (google.api.resource) = {
    type: "meet.googleapis.com/Participant"
    pattern: "conferenceRecords/{conference_record}/participants/{participant}"
    plural: "participants"
    singular: "participant"
  };

  oneof user {
    // Signed-in user.
    SignedinUser signedin_user = 4;

    // Anonymous user.
    AnonymousUser anonymous_user = 5;

    // User who calls in from their phone.
    PhoneUser phone_user = 6;
  }

  // Output only. Resource name of the participant.
  // Format: `conferenceRecords/{conference_record}/participants/{participant}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the participant joined the meeting for the first
  // time.
  google.protobuf.Timestamp earliest_start_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the participant left the meeting for the last time.
  // This can be null if it is an active meeting.
  google.protobuf.Timestamp latest_end_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [Developer Preview](https://developers.google.com/workspace/preview).
// Refers to each unique join/leave session when a user joins a conference from
// a device. Note that any time a user joins the conference a new unique ID is
// assigned. That means if a user joins a space multiple times from the same
// device, they're assigned different IDs, and are also be treated as different
// participant sessions.
message ParticipantSession {
  option (google.api.resource) = {
    type: "meet.googleapis.com/ParticipantSession"
    pattern: "conferenceRecords/{conference_record}/participants/{participant}/participantSessions/{participant_session}"
    plural: "participantSessions"
    singular: "participantSession"
  };

  // Identifier. Session id.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Timestamp when the user session started.
  google.protobuf.Timestamp start_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the user session ended. Unset if the user
  // session hasn’t ended.
  google.protobuf.Timestamp end_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A signed-in user can be:
// a) An individual joining from a personal computer, mobile device, or through
// companion mode.
// b) A robot account used by conference room devices.
message SignedinUser {
  // Output only. Unique ID for the user. Interoperable with Admin SDK API and
  // People API. Format: `users/{user}`
  string user = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a personal device, it's the user's first and last name.
  // For a robot account, it's the admin specified device name. For example,
  // "Altostrat Room".
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// User who joins anonymously (meaning not signed into a Google Account).
message AnonymousUser {
  // Output only. User provided name when they join a conference anonymously.
  string display_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// User dialing in from a phone where the user's identity is unknown because
// they haven't signed in with a Google Account.
message PhoneUser {
  // Output only. Partially redacted user's phone number when they call in.
  string display_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [Developer Preview](https://developers.google.com/workspace/preview).
// Metadata about a recording created during a conference.
message Recording {
  option (google.api.resource) = {
    type: "meet.googleapis.com/Recording"
    pattern: "conferenceRecords/{conference_record}/recordings/{recording}"
    plural: "recordings"
    singular: "recording"
  };

  // Current state of the recording session.
  enum State {
    // Default, never used.
    STATE_UNSPECIFIED = 0;

    // An active recording session has started.
    STARTED = 1;

    // This recording session has ended, but the recording file hasn't been
    // generated yet.
    ENDED = 2;

    // Recording file is generated and ready to download.
    FILE_GENERATED = 3;
  }

  oneof destination {
    // Output only. Recording is saved to Google Drive as an mp4 file. The
    // `drive_destination` includes the Drive `fileId` that can be used to
    // download the file using the `files.get` method of the Drive API.
    DriveDestination drive_destination = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Resource name of the recording.
  // Format: `conferenceRecords/{conference_record}/recordings/{recording}`
  // where `{recording}` is a 1:1 mapping to each unique recording session
  // during the conference.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the recording started.
  google.protobuf.Timestamp start_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the recording ended.
  google.protobuf.Timestamp end_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Export location where a recording file is saved in Google Drive.
message DriveDestination {
  // Output only. The `fileId` for the underlying MP4 file. For example,
  // "1kuceFZohVoCh6FulBHxwy6I15Ogpc4hP". Use `$ GET
  // https://www.googleapis.com/drive/v3/files/{$fileId}?alt=media` to download
  // the blob. For more information, see
  // https://developers.google.com/drive/api/v3/reference/files/get.
  string file = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Link used to play back the recording file in the browser. For
  // example, `https://drive.google.com/file/d/{$fileId}/view`.
  string export_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [Developer Preview](https://developers.google.com/workspace/preview).
// Metadata for a transcript generated from a conference. It refers to the ASR
// (Automatic Speech Recognition) result of user's speech during the conference.
message Transcript {
  option (google.api.resource) = {
    type: "meet.googleapis.com/Transcript"
    pattern: "conferenceRecords/{conference_record}/transcripts/{transcript}"
    plural: "transcripts"
    singular: "transcript"
  };

  // Current state of the transcript session.
  enum State {
    // Default, never used.
    STATE_UNSPECIFIED = 0;

    // An active transcript session has started.
    STARTED = 1;

    // This transcript session has ended, but the transcript file hasn't been
    // generated yet.
    ENDED = 2;

    // Transcript file is generated and ready to download.
    FILE_GENERATED = 3;
  }

  oneof destination {
    // Output only. Where the Google Docs transcript is saved.
    DocsDestination docs_destination = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Resource name of the transcript.
  // Format: `conferenceRecords/{conference_record}/transcripts/{transcript}`,
  // where `{transcript}` is a 1:1 mapping to each unique transcription session
  // of the conference.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the transcript started.
  google.protobuf.Timestamp start_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the transcript stopped.
  google.protobuf.Timestamp end_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Google Docs location where the transcript file is saved.
message DocsDestination {
  // Output only. The document ID for the underlying Google Docs transcript
  // file. For example, "1kuceFZohVoCh6FulBHxwy6I15Ogpc4hP". Use the
  // `documents.get` method of the Google Docs API
  // (https://developers.google.com/docs/api/reference/rest/v1/documents/get) to
  // fetch the content.
  string document = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URI for the Google Docs transcript file. Use
  // `https://docs.google.com/document/d/{$DocumentId}/view` to browse the
  // transcript in the browser.
  string export_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [Developer Preview](https://developers.google.com/workspace/preview).
// Single entry for one user’s speech during a transcript session.
message TranscriptEntry {
  option (google.api.resource) = {
    type: "meet.googleapis.com/TranscriptEntry"
    pattern: "conferenceRecords/{conference_record}/transcripts/{transcript}/entries/{entry}"
    plural: "transcriptEntries"
    singular: "transcriptEntry"
  };

  // Output only. Resource name of the entry. Format:
  // "conferenceRecords/{conference_record}/transcripts/{transcript}/entries/{entry}"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Refer to the participant who speaks.
  string participant = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "meet.googleapis.com/Participant"
    }
  ];

  // Output only. The transcribed text of the participant's voice, at maximum
  // 10K words. Note that the limit is subject to change.
  string text = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Language of spoken text, such as "en-US".
  // IETF BCP 47 syntax (https://tools.ietf.org/html/bcp47)
  string language_code = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the transcript entry started.
  google.protobuf.Timestamp start_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the transcript entry ended.
  google.protobuf.Timestamp end_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
