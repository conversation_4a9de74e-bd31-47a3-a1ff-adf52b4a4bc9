# This build file includes a target for the Ruby wrapper library for
# google-apps-meet.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for meet.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "meet_ruby_wrapper",
    srcs = ["//google/apps/meet/v2:meet_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-apps-meet",
        "ruby-cloud-wrapper-of=v2:0.0",
    ],
    service_yaml = "//google/apps/meet/v2:meet_v2.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-apps-meet-ruby",
    deps = [
        ":meet_ruby_wrapper",
    ],
)
