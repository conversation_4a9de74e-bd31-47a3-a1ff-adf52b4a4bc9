# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "meet_proto",
    srcs = [
        "resource.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "meet_proto_with_info",
    deps = [
        ":meet_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "meet_java_proto",
    deps = [":meet_proto"],
)

java_grpc_library(
    name = "meet_java_grpc",
    srcs = [":meet_proto"],
    deps = [":meet_java_proto"],
)

java_gapic_library(
    name = "meet_java_gapic",
    srcs = [":meet_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "meet_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "meet_v2.yaml",
    test_deps = [
        ":meet_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":meet_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "meet_java_gapic_test_suite",
    test_classes = [
        "com.google.apps.meet.v2.ConferenceRecordsServiceClientHttpJsonTest",
        "com.google.apps.meet.v2.ConferenceRecordsServiceClientTest",
        "com.google.apps.meet.v2.SpacesServiceClientHttpJsonTest",
        "com.google.apps.meet.v2.SpacesServiceClientTest",
    ],
    runtime_deps = [":meet_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-apps-meet-v2-java",
    transport = "grpc+rest",
    deps = [
        ":meet_java_gapic",
        ":meet_java_grpc",
        ":meet_java_proto",
        ":meet_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "meet_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/apps/meet/apiv2/meetpb",
    protos = [":meet_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "meet_go_gapic",
    srcs = [":meet_proto_with_info"],
    grpc_service_config = "meet_v2_grpc_service_config.json",
    importpath = "cloud.google.com/go/apps/meet/apiv2;meet",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "meet_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":meet_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-apps-meet-v2-go",
    deps = [
        ":meet_go_gapic",
        ":meet_go_gapic_srcjar-test.srcjar",
        ":meet_go_gapic_srcjar-metadata.srcjar",
        ":meet_go_gapic_srcjar-snippets.srcjar",
        ":meet_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "meet_py_gapic",
    srcs = [":meet_proto"],
    grpc_service_config = "meet_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "meet_v2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "meet_py_gapic_test",
    srcs = [
        "meet_py_gapic_pytest.py",
        "meet_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":meet_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "apps-meet-v2-py",
    deps = [
        ":meet_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "meet_php_proto",
    deps = [":meet_proto"],
)

php_gapic_library(
    name = "meet_php_gapic",
    srcs = [":meet_proto_with_info"],
    grpc_service_config = "meet_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "meet_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":meet_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-apps-meet-v2-php",
    deps = [
        ":meet_php_gapic",
        ":meet_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "meet_nodejs_gapic",
    package_name = "@google-apps/meet",
    src = ":meet_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "meet_v2_grpc_service_config.json",
    package = "google.apps.meet.v2",
    rest_numeric_enums = True,
    service_yaml = "meet_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "apps-meet-v2-nodejs",
    deps = [
        ":meet_nodejs_gapic",
        ":meet_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "meet_ruby_proto",
    deps = [":meet_proto"],
)

ruby_grpc_library(
    name = "meet_ruby_grpc",
    srcs = [":meet_proto"],
    deps = [":meet_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "meet_ruby_gapic",
    srcs = [":meet_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-apps-meet-v2",
    ],
    grpc_service_config = "meet_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "meet_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":meet_ruby_grpc",
        ":meet_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-apps-meet-v2-ruby",
    deps = [
        ":meet_ruby_gapic",
        ":meet_ruby_grpc",
        ":meet_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "meet_csharp_proto",
    extra_opts = [],
    deps = [":meet_proto"],
)

csharp_grpc_library(
    name = "meet_csharp_grpc",
    srcs = [":meet_proto"],
    deps = [":meet_csharp_proto"],
)

csharp_gapic_library(
    name = "meet_csharp_gapic",
    srcs = [":meet_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "meet_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "meet_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":meet_csharp_grpc",
        ":meet_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-apps-meet-v2-csharp",
    deps = [
        ":meet_csharp_gapic",
        ":meet_csharp_grpc",
        ":meet_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "meet_cc_proto",
    deps = [":meet_proto"],
)

cc_grpc_library(
    name = "meet_cc_grpc",
    srcs = [":meet_proto"],
    grpc_only = True,
    deps = [":meet_cc_proto"],
)
