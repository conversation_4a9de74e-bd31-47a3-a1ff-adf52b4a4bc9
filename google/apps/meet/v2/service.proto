// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.meet.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/apps/meet/v2/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Apps.Meet.V2";
option go_package = "cloud.google.com/go/apps/meet/apiv2/meetpb;meetpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.apps.meet.v2";
option php_namespace = "Google\\Apps\\Meet\\V2";
option ruby_package = "Google::Apps::Meet::V2";

// REST API for services dealing with spaces.
service SpacesService {
  option (google.api.default_host) = "meet.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/meetings.space.created,"
      "https://www.googleapis.com/auth/meetings.space.readonly";

  // Creates a space.
  rpc CreateSpace(CreateSpaceRequest) returns (Space) {
    option (google.api.http) = {
      post: "/v2/spaces"
      body: "space"
    };
    option (google.api.method_signature) = "space";
  }

  // Gets a space by `space_id` or `meeting_code`.
  rpc GetSpace(GetSpaceRequest) returns (Space) {
    option (google.api.http) = {
      get: "/v2/{name=spaces/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a space.
  rpc UpdateSpace(UpdateSpaceRequest) returns (Space) {
    option (google.api.http) = {
      patch: "/v2/{space.name=spaces/*}"
      body: "space"
    };
    option (google.api.method_signature) = "space,update_mask";
  }

  // Ends an active conference (if there's one).
  rpc EndActiveConference(EndActiveConferenceRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2/{name=spaces/*}:endActiveConference"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// REST API for services dealing with conference records.
service ConferenceRecordsService {
  option (google.api.default_host) = "meet.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/meetings.space.created,"
      "https://www.googleapis.com/auth/meetings.space.readonly";

  // Gets a conference record by conference ID.
  rpc GetConferenceRecord(GetConferenceRecordRequest)
      returns (ConferenceRecord) {
    option (google.api.http) = {
      get: "/v2/{name=conferenceRecords/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the conference records. By default, ordered by start time and in
  // descending order.
  rpc ListConferenceRecords(ListConferenceRecordsRequest)
      returns (ListConferenceRecordsResponse) {
    option (google.api.http) = {
      get: "/v2/conferenceRecords"
    };
  }

  // Gets a participant by participant ID.
  rpc GetParticipant(GetParticipantRequest) returns (Participant) {
    option (google.api.http) = {
      get: "/v2/{name=conferenceRecords/*/participants/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the participants in a conference record. By default, ordered by join
  // time and in descending order. This API supports `fields` as standard
  // parameters like every other API. However, when the `fields` request
  // parameter is omitted, this API defaults to `'participants/*,
  // next_page_token'`.
  rpc ListParticipants(ListParticipantsRequest)
      returns (ListParticipantsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=conferenceRecords/*}/participants"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a participant session by participant session ID.
  rpc GetParticipantSession(GetParticipantSessionRequest)
      returns (ParticipantSession) {
    option (google.api.http) = {
      get: "/v2/{name=conferenceRecords/*/participants/*/participantSessions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the participant sessions of a participant in a conference record. By
  // default, ordered by join time and in descending order. This API supports
  // `fields` as standard parameters like every other API. However, when the
  // `fields` request parameter is omitted this API defaults to
  // `'participantsessions/*, next_page_token'`.
  rpc ListParticipantSessions(ListParticipantSessionsRequest)
      returns (ListParticipantSessionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=conferenceRecords/*/participants/*}/participantSessions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a recording by recording ID.
  rpc GetRecording(GetRecordingRequest) returns (Recording) {
    option (google.api.http) = {
      get: "/v2/{name=conferenceRecords/*/recordings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the recording resources from the conference record. By default,
  // ordered by start time and in ascending order.
  rpc ListRecordings(ListRecordingsRequest) returns (ListRecordingsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=conferenceRecords/*}/recordings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a transcript by transcript ID.
  rpc GetTranscript(GetTranscriptRequest) returns (Transcript) {
    option (google.api.http) = {
      get: "/v2/{name=conferenceRecords/*/transcripts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the set of transcripts from the conference record. By default,
  // ordered by start time and in ascending order.
  rpc ListTranscripts(ListTranscriptsRequest)
      returns (ListTranscriptsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=conferenceRecords/*}/transcripts"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a `TranscriptEntry` resource by entry ID.
  //
  // Note: The transcript entries returned by the Google Meet API might not
  // match the transcription found in the Google Docs transcript file. This can
  // occur when the Google Docs transcript file is modified after generation.
  rpc GetTranscriptEntry(GetTranscriptEntryRequest) returns (TranscriptEntry) {
    option (google.api.http) = {
      get: "/v2/{name=conferenceRecords/*/transcripts/*/entries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the structured transcript entries per transcript. By default, ordered
  // by start time and in ascending order.
  //
  // Note: The transcript entries returned by the Google Meet API might not
  // match the transcription found in the Google Docs transcript file. This can
  // occur when the Google Docs transcript file is modified after generation.
  rpc ListTranscriptEntries(ListTranscriptEntriesRequest)
      returns (ListTranscriptEntriesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=conferenceRecords/*/transcripts/*}/entries"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request to create a space.
message CreateSpaceRequest {
  // Space to be created. As of May 2023, the input space can be empty. Later on
  // the input space can be non-empty when space configuration is introduced.
  Space space = 1;
}

// Request to get a space.
message GetSpaceRequest {
  // Required. Resource name of the space.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "meet.googleapis.com/Space" }
  ];
}

// Request to update a space.
message UpdateSpaceRequest {
  // Required. Space to be updated.
  Space space = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Field mask used to specify the fields to be updated in the space.
  // If update_mask isn't provided, it defaults to '*' and updates all
  // fields provided in the request, including deleting fields not set in the
  // request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request to end an ongoing conference of a space.
message EndActiveConferenceRequest {
  // Required. Resource name of the space.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "meet.googleapis.com/Space" }
  ];
}

// Request to get a conference record.
message GetConferenceRecordRequest {
  // Required. Resource name of the conference.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "meet.googleapis.com/ConferenceRecord"
    }
  ];
}

// Request to fetch list of conference records per user.
message ListConferenceRecordsRequest {
  // Optional. Maximum number of conference records to return. The service might
  // return fewer than this value. If unspecified, at most 25 conference records
  // are returned. The maximum value is 100; values above 100 are coerced to
  // 100. Maximum might change in the future.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token returned from previous List Call.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User specified filtering condition in [EBNF
  // format](https://en.wikipedia.org/wiki/Extended_Backus%E2%80%93Naur_form).
  // The following are the filterable fields:
  //
  // * `space.meeting_code`
  // * `space.name`
  // * `start_time`
  // * `end_time`
  //
  // For example, `space.meeting_code = "abc-mnop-xyz"`.
  string filter = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response of ListConferenceRecords method.
message ListConferenceRecordsResponse {
  // List of conferences in one page.
  repeated ConferenceRecord conference_records = 1;

  // Token to be circulated back for further List call if current List does NOT
  // include all the Conferences. Unset if all conferences have been returned.
  string next_page_token = 2;
}

// Request to get a participant.
message GetParticipantRequest {
  // Required. Resource name of the participant.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "meet.googleapis.com/Participant"
    }
  ];
}

// Request to fetch list of participants per conference.
message ListParticipantsRequest {
  // Required. Format: `conferenceRecords/{conference_record}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "meet.googleapis.com/Participant"
    }
  ];

  // Maximum number of participants to return. The service might return fewer
  // than this value.
  // If unspecified, at most 100 participants are returned.
  // The maximum value is 250; values above 250 are coerced to 250.
  // Maximum might change in the future.
  int32 page_size = 2;

  // Page token returned from previous List Call.
  string page_token = 3;

  // Optional. User specified filtering condition in [EBNF
  // format](https://en.wikipedia.org/wiki/Extended_Backus%E2%80%93Naur_form).
  // The following are the filterable fields:
  //
  // * `earliest_start_time`
  // * `latest_end_time`
  //
  // For example, `latest_end_time IS NULL` returns active participants in
  // the conference.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response of ListParticipants method.
message ListParticipantsResponse {
  // List of participants in one page.
  repeated Participant participants = 1;

  // Token to be circulated back for further List call if current List doesn't
  // include all the participants. Unset if all participants are returned.
  string next_page_token = 2;

  // Total, exact number of `participants`. By default, this field isn't
  // included in the response. Set the field mask in
  // [SystemParameterContext](https://cloud.google.com/apis/docs/system-parameters)
  // to receive this field in the response.
  int32 total_size = 3;
}

// Request to get a participant session.
message GetParticipantSessionRequest {
  // Required. Resource name of the participant.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "meet.googleapis.com/ParticipantSession"
    }
  ];
}

// Request to fetch list of participant sessions per conference record, per
// participant.
message ListParticipantSessionsRequest {
  // Required. Format:
  // `conferenceRecords/{conference_record}/participants/{participant}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "meet.googleapis.com/ParticipantSession"
    }
  ];

  // Optional. Maximum number of participant sessions to return. The service
  // might return fewer than this value. If unspecified, at most 100
  // participants are returned. The maximum value is 250; values above 250 are
  // coerced to 250. Maximum might change in the future.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token returned from previous List Call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User specified filtering condition in [EBNF
  // format](https://en.wikipedia.org/wiki/Extended_Backus%E2%80%93Naur_form).
  // The following are the filterable fields:
  //
  // * `start_time`
  // * `end_time`
  //
  // For example, `end_time IS NULL` returns active participant sessions in
  // the conference record.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response of ListParticipants method.
message ListParticipantSessionsResponse {
  // List of participants in one page.
  repeated ParticipantSession participant_sessions = 1;

  // Token to be circulated back for further List call if current List doesn't
  // include all the participants. Unset if all participants are returned.
  string next_page_token = 2;
}

// Request message for GetRecording method.
message GetRecordingRequest {
  // Required. Resource name of the recording.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "meet.googleapis.com/Recording" }
  ];
}

// Request for ListRecordings method.
message ListRecordingsRequest {
  // Required. Format: `conferenceRecords/{conference_record}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "meet.googleapis.com/Recording"
    }
  ];

  // Maximum number of recordings to return. The service might return fewer
  // than this value.
  // If unspecified, at most 10 recordings are returned.
  // The maximum value is 100; values above 100 are coerced to 100.
  // Maximum might change in the future.
  int32 page_size = 2;

  // Page token returned from previous List Call.
  string page_token = 3;
}

// Response for ListRecordings method.
message ListRecordingsResponse {
  // List of recordings in one page.
  repeated Recording recordings = 1;

  // Token to be circulated back for further List call if current List doesn't
  // include all the recordings. Unset if all recordings are returned.
  string next_page_token = 2;
}

// Request for GetTranscript method.
message GetTranscriptRequest {
  // Required. Resource name of the transcript.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "meet.googleapis.com/Transcript" }
  ];
}

// Request for ListTranscripts method.
message ListTranscriptsRequest {
  // Required. Format: `conferenceRecords/{conference_record}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "meet.googleapis.com/Transcript"
    }
  ];

  // Maximum number of transcripts to return. The service might return fewer
  // than this value.
  // If unspecified, at most 10 transcripts are returned.
  // The maximum value is 100; values above 100 are coerced to 100.
  // Maximum might change in the future.
  int32 page_size = 2;

  // Page token returned from previous List Call.
  string page_token = 3;
}

// Response for ListTranscripts method.
message ListTranscriptsResponse {
  // List of transcripts in one page.
  repeated Transcript transcripts = 1;

  // Token to be circulated back for further List call if current List doesn't
  // include all the transcripts. Unset if all transcripts are returned.
  string next_page_token = 2;
}

// Request for GetTranscriptEntry method.
message GetTranscriptEntryRequest {
  // Required. Resource name of the `TranscriptEntry`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "meet.googleapis.com/TranscriptEntry"
    }
  ];
}

// Request for ListTranscriptEntries method.
message ListTranscriptEntriesRequest {
  // Required. Format:
  // `conferenceRecords/{conference_record}/transcripts/{transcript}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "meet.googleapis.com/TranscriptEntry"
    }
  ];

  // Maximum number of entries to return. The service might return fewer than
  // this value.
  // If unspecified, at most 10 entries are returned.
  // The maximum value is 100; values above 100 are coerced to 100.
  // Maximum might change in the future.
  int32 page_size = 2;

  // Page token returned from previous List Call.
  string page_token = 3;
}

// Response for ListTranscriptEntries method.
message ListTranscriptEntriesResponse {
  // List of TranscriptEntries in one page.
  repeated TranscriptEntry transcript_entries = 1;

  // Token to be circulated back for further List call if current List doesn't
  // include all the transcript entries. Unset if all entries are returned.
  string next_page_token = 2;
}
