{"methodConfig": [{"name": [{"service": "google.apps.meet.v2.SpacesService", "method": "GetSpace"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "GetConferenceRecord"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "ListConferenceRecords"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "GetParticipant"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "ListParticipants"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "GetParticipantSession"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "ListParticipantSessions"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "GetRecording"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "ListRecordings"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "GetTranscript"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "ListTranscripts"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "GetTranscriptEntry"}, {"service": "google.apps.meet.v2.ConferenceRecordsService", "method": "ListTranscriptEntries"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.apps.meet.v2.SpacesService", "method": "CreateSpace"}, {"service": "google.apps.meet.v2.SpacesService", "method": "UpdateSpace"}, {"service": "google.apps.meet.v2.SpacesService", "method": "EndActiveConference"}, {"service": "google.apps.meet.v2.SpacesService", "method": "DeleteSpace"}], "timeout": "60s"}]}