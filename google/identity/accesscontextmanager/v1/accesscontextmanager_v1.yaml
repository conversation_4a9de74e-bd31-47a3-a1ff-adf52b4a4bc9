type: google.api.Service
config_version: 3
name: accesscontextmanager.googleapis.com
title: Access Context Manager API

apis:
- name: google.identity.accesscontextmanager.v1.AccessContextManager
- name: google.longrunning.Operations

types:
- name: google.identity.accesscontextmanager.v1.AccessContextManagerOperationMetadata
- name: google.identity.accesscontextmanager.v1.CommitServicePerimetersResponse
- name: google.identity.accesscontextmanager.v1.GcpUserAccessBindingOperationMetadata
- name: google.identity.accesscontextmanager.v1.ReplaceAccessLevelsResponse
- name: google.identity.accesscontextmanager.v1.ReplaceServicePerimetersResponse

documentation:
  summary: |-
    An API for setting attribute based access control to requests to GCP
    services.

backend:
  rules:
  - selector: 'google.identity.accesscontextmanager.v1.AccessContextManager.*'
    deadline: 35.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 100.0

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=operations/**}'

authentication:
  rules:
  - selector: 'google.identity.accesscontextmanager.v1.AccessContextManager.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
