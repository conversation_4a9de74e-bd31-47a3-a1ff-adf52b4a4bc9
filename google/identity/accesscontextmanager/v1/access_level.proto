// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.identity.accesscontextmanager.v1;

import "google/api/resource.proto";
import "google/identity/accesscontextmanager/type/device_resources.proto";
import "google/protobuf/timestamp.proto";
import "google/type/expr.proto";

option csharp_namespace = "Google.Identity.AccessContextManager.V1";
option go_package = "cloud.google.com/go/accesscontextmanager/apiv1/accesscontextmanagerpb;accesscontextmanagerpb";
option java_multiple_files = true;
option java_outer_classname = "AccessLevelProto";
option java_package = "com.google.identity.accesscontextmanager.v1";
option objc_class_prefix = "GACM";
option php_namespace = "Google\\Identity\\AccessContextManager\\V1";
option ruby_package = "Google::Identity::AccessContextManager::V1";

// An `AccessLevel` is a label that can be applied to requests to Google Cloud
// services, along with a list of requirements necessary for the label to be
// applied.
message AccessLevel {
  option (google.api.resource) = {
    type: "accesscontextmanager.googleapis.com/AccessLevel"
    pattern: "accessPolicies/{access_policy}/accessLevels/{access_level}"
  };

  // Required. Resource name for the Access Level. The `short_name` component
  // must begin with a letter and only include alphanumeric and '_'. Format:
  // `accessPolicies/{access_policy}/accessLevels/{access_level}`. The maximum
  // length of the `access_level` component is 50 characters.
  string name = 1;

  // Human readable title. Must be unique within the Policy.
  string title = 2;

  // Description of the `AccessLevel` and its use. Does not affect behavior.
  string description = 3;

  // Required. Describes the necessary conditions for the level to apply.
  oneof level {
    // A `BasicLevel` composed of `Conditions`.
    BasicLevel basic = 4;

    // A `CustomLevel` written in the Common Expression Language.
    CustomLevel custom = 5;
  }

  // Output only. Time the `AccessLevel` was created in UTC.
  google.protobuf.Timestamp create_time = 6;

  // Output only. Time the `AccessLevel` was updated in UTC.
  google.protobuf.Timestamp update_time = 7;
}

// `BasicLevel` is an `AccessLevel` using a set of recommended features.
message BasicLevel {
  // Options for how the `conditions` list should be combined to determine if
  // this `AccessLevel` is applied. Default is AND.
  enum ConditionCombiningFunction {
    // All `Conditions` must be true for the `BasicLevel` to be true.
    AND = 0;

    // If at least one `Condition` is true, then the `BasicLevel` is true.
    OR = 1;
  }

  // Required. A list of requirements for the `AccessLevel` to be granted.
  repeated Condition conditions = 1;

  // How the `conditions` list should be combined to determine if a request is
  // granted this `AccessLevel`. If AND is used, each `Condition` in
  // `conditions` must be satisfied for the `AccessLevel` to be applied. If OR
  // is used, at least one `Condition` in `conditions` must be satisfied for the
  // `AccessLevel` to be applied. Default behavior is AND.
  ConditionCombiningFunction combining_function = 2;
}

// A condition necessary for an `AccessLevel` to be granted. The Condition is an
// AND over its fields. So a Condition is true if: 1) the request IP is from one
// of the listed subnetworks AND 2) the originating device complies with the
// listed device policy AND 3) all listed access levels are granted AND 4) the
// request was sent at a time allowed by the DateTimeRestriction.
message Condition {
  // CIDR block IP subnetwork specification. May be IPv4 or IPv6. Note that for
  // a CIDR IP address block, the specified IP address portion must be properly
  // truncated (i.e. all the host bits must be zero) or the input is considered
  // malformed. For example, "*********/24" is accepted but "*********/24" is
  // not. Similarly, for IPv6, "2001:db8::/32" is accepted whereas
  // "2001:db8::1/32" is not. The originating IP of a request must be in one of
  // the listed subnets in order for this Condition to be true. If empty, all IP
  // addresses are allowed.
  repeated string ip_subnetworks = 1;

  // Device specific restrictions, all restrictions must hold for the
  // Condition to be true. If not specified, all devices are allowed.
  DevicePolicy device_policy = 2;

  // A list of other access levels defined in the same `Policy`, referenced by
  // resource name. Referencing an `AccessLevel` which does not exist is an
  // error. All access levels listed must be granted for the Condition
  // to be true. Example:
  // "`accessPolicies/MY_POLICY/accessLevels/LEVEL_NAME"`
  repeated string required_access_levels = 3;

  // Whether to negate the Condition. If true, the Condition becomes a NAND over
  // its non-empty fields, each field must be false for the Condition overall to
  // be satisfied. Defaults to false.
  bool negate = 5;

  // The request must be made by one of the provided user or service
  // accounts. Groups are not supported.
  // Syntax:
  // `user:{emailid}`
  // `serviceAccount:{emailid}`
  // If not specified, a request may come from any user.
  repeated string members = 6;

  // The request must originate from one of the provided countries/regions.
  // Must be valid ISO 3166-1 alpha-2 codes.
  repeated string regions = 7;
}

// `CustomLevel` is an `AccessLevel` using the Cloud Common Expression Language
// to represent the necessary conditions for the level to apply to a request.
// See CEL spec at: https://github.com/google/cel-spec
message CustomLevel {
  // Required. A Cloud CEL expression evaluating to a boolean.
  google.type.Expr expr = 1;
}

// `DevicePolicy` specifies device specific restrictions necessary to acquire a
// given access level. A `DevicePolicy` specifies requirements for requests from
// devices to be granted access levels, it does not do any enforcement on the
// device. `DevicePolicy` acts as an AND over all specified fields, and each
// repeated field is an OR over its elements. Any unset fields are ignored. For
// example, if the proto is { os_type : DESKTOP_WINDOWS, os_type :
// DESKTOP_LINUX, encryption_status: ENCRYPTED}, then the DevicePolicy will be
// true for requests originating from encrypted Linux desktops and encrypted
// Windows desktops.
message DevicePolicy {
  // Whether or not screenlock is required for the DevicePolicy to be true.
  // Defaults to `false`.
  bool require_screenlock = 1;

  // Allowed encryptions statuses, an empty list allows all statuses.
  repeated google.identity.accesscontextmanager.type.DeviceEncryptionStatus allowed_encryption_statuses = 2;

  // Allowed OS versions, an empty list allows all types and all versions.
  repeated OsConstraint os_constraints = 3;

  // Allowed device management levels, an empty list allows all management
  // levels.
  repeated google.identity.accesscontextmanager.type.DeviceManagementLevel allowed_device_management_levels = 6;

  // Whether the device needs to be approved by the customer admin.
  bool require_admin_approval = 7;

  // Whether the device needs to be corp owned.
  bool require_corp_owned = 8;
}

// A restriction on the OS type and version of devices making requests.
message OsConstraint {
  // Required. The allowed OS type.
  google.identity.accesscontextmanager.type.OsType os_type = 1;

  // The minimum allowed OS version. If not set, any version of this OS
  // satisfies the constraint. Format: `"major.minor.patch"`.
  // Examples: `"10.5.301"`, `"9.2.1"`.
  string minimum_version = 2;

  // Only allows requests from devices with a verified Chrome OS.
  // Verifications includes requirements that the device is enterprise-managed,
  // conformant to domain policies, and the caller has permission to call
  // the API targeted by the request.
  bool require_verified_chrome_os = 3;
}
