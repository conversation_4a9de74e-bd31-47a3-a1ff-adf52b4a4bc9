// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.identity.accesscontextmanager.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/identity/accesscontextmanager/v1/access_level.proto";
import "google/identity/accesscontextmanager/v1/access_policy.proto";
import "google/identity/accesscontextmanager/v1/gcp_user_access_binding.proto";
import "google/identity/accesscontextmanager/v1/service_perimeter.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Identity.AccessContextManager.V1";
option go_package = "cloud.google.com/go/accesscontextmanager/apiv1/accesscontextmanagerpb;accesscontextmanagerpb";
option java_multiple_files = true;
option java_outer_classname = "AccessContextManagerProto";
option java_package = "com.google.identity.accesscontextmanager.v1";
option objc_class_prefix = "GACM";
option php_namespace = "Google\\Identity\\AccessContextManager\\V1";
option ruby_package = "Google::Identity::AccessContextManager::V1";

// API for setting [access levels]
// [google.identity.accesscontextmanager.v1.AccessLevel] and [service
// perimeters] [google.identity.accesscontextmanager.v1.ServicePerimeter]
// for Google Cloud projects. Each organization has one [access policy]
// [google.identity.accesscontextmanager.v1.AccessPolicy] that contains the
// [access levels] [google.identity.accesscontextmanager.v1.AccessLevel]
// and [service perimeters]
// [google.identity.accesscontextmanager.v1.ServicePerimeter]. This
// [access policy] [google.identity.accesscontextmanager.v1.AccessPolicy] is
// applicable to all resources in the organization.
// AccessPolicies
service AccessContextManager {
  option (google.api.default_host) = "accesscontextmanager.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists all [access policies]
  // [google.identity.accesscontextmanager.v1.AccessPolicy] in an
  // organization.
  rpc ListAccessPolicies(ListAccessPoliciesRequest) returns (ListAccessPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/accessPolicies"
    };
  }

  // Returns an [access policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy] based on the name.
  rpc GetAccessPolicy(GetAccessPolicyRequest) returns (AccessPolicy) {
    option (google.api.http) = {
      get: "/v1/{name=accessPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an access policy. This method fails if the organization already has
  // an access policy. The long-running operation has a successful status
  // after the access policy propagates to long-lasting storage.
  // Syntactic and basic semantic errors are returned in `metadata` as a
  // BadRequest proto.
  rpc CreateAccessPolicy(AccessPolicy) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/accessPolicies"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "AccessPolicy"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Updates an [access policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy]. The
  // long-running operation from this RPC has a successful status after the
  // changes to the [access policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy] propagate
  // to long-lasting storage.
  rpc UpdateAccessPolicy(UpdateAccessPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{policy.name=accessPolicies/*}"
      body: "policy"
    };
    option (google.api.method_signature) = "policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AccessPolicy"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Deletes an [access policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy] based on the
  // resource name. The long-running operation has a successful status after the
  // [access policy] [google.identity.accesscontextmanager.v1.AccessPolicy]
  // is removed from long-lasting storage.
  rpc DeleteAccessPolicy(DeleteAccessPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=accessPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Lists all [access levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] for an access
  // policy.
  rpc ListAccessLevels(ListAccessLevelsRequest) returns (ListAccessLevelsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accessPolicies/*}/accessLevels"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets an [access level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] based on the resource
  // name.
  rpc GetAccessLevel(GetAccessLevelRequest) returns (AccessLevel) {
    option (google.api.http) = {
      get: "/v1/{name=accessPolicies/*/accessLevels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an [access level]
  // [google.identity.accesscontextmanager.v1.AccessLevel]. The long-running
  // operation from this RPC has a successful status after the [access
  // level] [google.identity.accesscontextmanager.v1.AccessLevel]
  // propagates to long-lasting storage. If [access levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] contain
  // errors, an error response is returned for the first error encountered.
  rpc CreateAccessLevel(CreateAccessLevelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=accessPolicies/*}/accessLevels"
      body: "access_level"
    };
    option (google.api.method_signature) = "parent,access_level";
    option (google.longrunning.operation_info) = {
      response_type: "AccessLevel"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Updates an [access level]
  // [google.identity.accesscontextmanager.v1.AccessLevel]. The long-running
  // operation from this RPC has a successful status after the changes to
  // the [access level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] propagate
  // to long-lasting storage. If [access levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] contain
  // errors, an error response is returned for the first error encountered.
  rpc UpdateAccessLevel(UpdateAccessLevelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{access_level.name=accessPolicies/*/accessLevels/*}"
      body: "access_level"
    };
    option (google.api.method_signature) = "access_level,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AccessLevel"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Deletes an [access level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] based on the resource
  // name. The long-running operation from this RPC has a successful status
  // after the [access level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] has been removed
  // from long-lasting storage.
  rpc DeleteAccessLevel(DeleteAccessLevelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=accessPolicies/*/accessLevels/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Replaces all existing [access levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] in an [access
  // policy] [google.identity.accesscontextmanager.v1.AccessPolicy] with
  // the [access levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] provided. This
  // is done atomically. The long-running operation from this RPC has a
  // successful status after all replacements propagate to long-lasting
  // storage. If the replacement contains errors, an error response is returned
  // for the first error encountered.  Upon error, the replacement is cancelled,
  // and existing [access levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] are not
  // affected. The Operation.response field contains
  // ReplaceAccessLevelsResponse. Removing [access levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] contained in existing
  // [service perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] result in an
  // error.
  rpc ReplaceAccessLevels(ReplaceAccessLevelsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=accessPolicies/*}/accessLevels:replaceAll"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ReplaceAccessLevelsResponse"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Lists all [service perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] for an
  // access policy.
  rpc ListServicePerimeters(ListServicePerimetersRequest) returns (ListServicePerimetersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accessPolicies/*}/servicePerimeters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] based on the
  // resource name.
  rpc GetServicePerimeter(GetServicePerimeterRequest) returns (ServicePerimeter) {
    option (google.api.http) = {
      get: "/v1/{name=accessPolicies/*/servicePerimeters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter]. The
  // long-running operation from this RPC has a successful status after the
  // [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter]
  // propagates to long-lasting storage. If a [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] contains
  // errors, an error response is returned for the first error encountered.
  rpc CreateServicePerimeter(CreateServicePerimeterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=accessPolicies/*}/servicePerimeters"
      body: "service_perimeter"
    };
    option (google.api.method_signature) = "parent,service_perimeter";
    option (google.longrunning.operation_info) = {
      response_type: "ServicePerimeter"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Updates a [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter]. The
  // long-running operation from this RPC has a successful status after the
  // [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter]
  // propagates to long-lasting storage. If a [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] contains
  // errors, an error response is returned for the first error encountered.
  rpc UpdateServicePerimeter(UpdateServicePerimeterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{service_perimeter.name=accessPolicies/*/servicePerimeters/*}"
      body: "service_perimeter"
    };
    option (google.api.method_signature) = "service_perimeter,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ServicePerimeter"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Deletes a [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] based on the
  // resource name. The long-running operation from this RPC has a successful
  // status after the [service perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] is removed from
  // long-lasting storage.
  rpc DeleteServicePerimeter(DeleteServicePerimeterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=accessPolicies/*/servicePerimeters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Replace all existing [service perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] in an [access
  // policy] [google.identity.accesscontextmanager.v1.AccessPolicy] with the
  // [service perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] provided. This
  // is done atomically. The long-running operation from this RPC has a
  // successful status after all replacements propagate to long-lasting storage.
  // Replacements containing errors result in an error response for the first
  // error encountered. Upon an error, replacement are cancelled and existing
  // [service perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] are not
  // affected. The Operation.response field contains
  // ReplaceServicePerimetersResponse.
  rpc ReplaceServicePerimeters(ReplaceServicePerimetersRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=accessPolicies/*}/servicePerimeters:replaceAll"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ReplaceServicePerimetersResponse"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Commits the dry-run specification for all the [service perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] in an
  // [access policy][google.identity.accesscontextmanager.v1.AccessPolicy].
  // A commit operation on a service perimeter involves copying its `spec` field
  // to the `status` field of the service perimeter. Only [service perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] with
  // `use_explicit_dry_run_spec` field set to true are affected by a commit
  // operation. The long-running operation from this RPC has a successful
  // status after the dry-run specifications for all the [service perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] have been
  // committed. If a commit fails, it causes the long-running operation to
  // return an error response and the entire commit operation is cancelled.
  // When successful, the Operation.response field contains
  // CommitServicePerimetersResponse. The `dry_run` and the `spec` fields are
  // cleared after a successful commit operation.
  rpc CommitServicePerimeters(CommitServicePerimetersRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=accessPolicies/*}/servicePerimeters:commit"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "CommitServicePerimetersResponse"
      metadata_type: "AccessContextManagerOperationMetadata"
    };
  }

  // Lists all [GcpUserAccessBindings]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding] for a
  // Google Cloud organization.
  rpc ListGcpUserAccessBindings(ListGcpUserAccessBindingsRequest) returns (ListGcpUserAccessBindingsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*}/gcpUserAccessBindings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the [GcpUserAccessBinding]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding] with
  // the given name.
  rpc GetGcpUserAccessBinding(GetGcpUserAccessBindingRequest) returns (GcpUserAccessBinding) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/gcpUserAccessBindings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a [GcpUserAccessBinding]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding]. If the
  // client specifies a [name]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding.name],
  // the server ignores it. Fails if a resource already exists with the same
  // [group_key]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding.group_key].
  // Completion of this long-running operation does not necessarily signify that
  // the new binding is deployed onto all affected users, which may take more
  // time.
  rpc CreateGcpUserAccessBinding(CreateGcpUserAccessBindingRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/gcpUserAccessBindings"
      body: "gcp_user_access_binding"
    };
    option (google.api.method_signature) = "parent,gcp_user_access_binding";
    option (google.longrunning.operation_info) = {
      response_type: "GcpUserAccessBinding"
      metadata_type: "GcpUserAccessBindingOperationMetadata"
    };
  }

  // Updates a [GcpUserAccessBinding]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding].
  // Completion of this long-running operation does not necessarily signify that
  // the changed binding is deployed onto all affected users, which may take
  // more time.
  rpc UpdateGcpUserAccessBinding(UpdateGcpUserAccessBindingRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{gcp_user_access_binding.name=organizations/*/gcpUserAccessBindings/*}"
      body: "gcp_user_access_binding"
    };
    option (google.api.method_signature) = "gcp_user_access_binding,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "GcpUserAccessBinding"
      metadata_type: "GcpUserAccessBindingOperationMetadata"
    };
  }

  // Deletes a [GcpUserAccessBinding]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding].
  // Completion of this long-running operation does not necessarily signify that
  // the binding deletion is deployed onto all affected users, which may take
  // more time.
  rpc DeleteGcpUserAccessBinding(DeleteGcpUserAccessBindingRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/gcpUserAccessBindings/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "GcpUserAccessBindingOperationMetadata"
    };
  }

  // Sets the IAM policy for the specified Access Context Manager
  // [access policy][google.identity.accesscontextmanager.v1.AccessPolicy].
  // This method replaces the existing IAM policy on the access policy. The IAM
  // policy controls the set of users who can perform specific operations on the
  // Access Context Manager [access
  // policy][google.identity.accesscontextmanager.v1.AccessPolicy].
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=accessPolicies/*}:setIamPolicy"
      body: "*"
    };
  }

  // Gets the IAM policy for the specified Access Context Manager
  // [access policy][google.identity.accesscontextmanager.v1.AccessPolicy].
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=accessPolicies/*}:getIamPolicy"
      body: "*"
    };
  }

  // Returns the IAM permissions that the caller has on the specified Access
  // Context Manager resource. The resource can be an
  // [AccessPolicy][google.identity.accesscontextmanager.v1.AccessPolicy],
  // [AccessLevel][google.identity.accesscontextmanager.v1.AccessLevel], or
  // [ServicePerimeter][google.identity.accesscontextmanager.v1.ServicePerimeter
  // ]. This method does not support other resources.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest) returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=accessPolicies/*}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=accessPolicies/*/accessLevels/*}:testIamPermissions"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{resource=accessPolicies/*/servicePerimeters/*}:testIamPermissions"
        body: "*"
      }
    };
  }
}

// A request to list all `AccessPolicies` for a container.
message ListAccessPoliciesRequest {
  // Required. Resource name for the container to list AccessPolicy instances
  // from.
  //
  // Format:
  // `organizations/{org_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // Number of AccessPolicy instances to include in the list. Default 100.
  int32 page_size = 2;

  // Next page token for the next batch of AccessPolicy instances. Defaults to
  // the first page of results.
  string page_token = 3;
}

// A response to `ListAccessPoliciesRequest`.
message ListAccessPoliciesResponse {
  // List of the AccessPolicy instances.
  repeated AccessPolicy access_policies = 1;

  // The pagination token to retrieve the next page of results. If the value is
  // empty, no further results remain.
  string next_page_token = 2;
}

// A request to get a particular `AccessPolicy`.
message GetAccessPolicyRequest {
  // Required. Resource name for the access policy to get.
  //
  // Format `accessPolicies/{policy_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/AccessPolicy"
    }
  ];
}

// A request to update an `AccessPolicy`.
message UpdateAccessPolicyRequest {
  // Required. The updated AccessPolicy.
  AccessPolicy policy = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Mask to control which fields get updated. Must be non-empty.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// A request to delete an `AccessPolicy`.
message DeleteAccessPolicyRequest {
  // Required. Resource name for the access policy to delete.
  //
  // Format `accessPolicies/{policy_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/AccessPolicy"
    }
  ];
}

// A request to list all `AccessLevels` in an `AccessPolicy`.
message ListAccessLevelsRequest {
  // Required. Resource name for the access policy to list [Access Levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] from.
  //
  // Format:
  // `accessPolicies/{policy_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "accesscontextmanager.googleapis.com/AccessLevel"
    }
  ];

  // Number of [Access Levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] to include in
  // the list. Default 100.
  int32 page_size = 2;

  // Next page token for the next batch of [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] instances.
  // Defaults to the first page of results.
  string page_token = 3;

  // Whether to return `BasicLevels` in the Cloud Common Expression language, as
  // `CustomLevels`, rather than as `BasicLevels`. Defaults to returning
  // `AccessLevels` in the format they were defined.
  LevelFormat access_level_format = 4;
}

// A response to `ListAccessLevelsRequest`.
message ListAccessLevelsResponse {
  // List of the [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] instances.
  repeated AccessLevel access_levels = 1;

  // The pagination token to retrieve the next page of results. If the value is
  // empty, no further results remain.
  string next_page_token = 2;
}

// A request to get a particular `AccessLevel`.
message GetAccessLevelRequest {
  // Required. Resource name for the [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel].
  //
  // Format:
  // `accessPolicies/{policy_id}/accessLevels/{access_level_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/AccessLevel"
    }
  ];

  // Whether to return `BasicLevels` in the Cloud Common Expression
  // Language rather than as `BasicLevels`. Defaults to AS_DEFINED, where
  // [Access Levels] [google.identity.accesscontextmanager.v1.AccessLevel]
  // are returned as `BasicLevels` or `CustomLevels` based on how they were
  // created. If set to CEL, all [Access Levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] are returned as
  // `CustomLevels`. In the CEL case, `BasicLevels` are translated to equivalent
  // `CustomLevels`.
  LevelFormat access_level_format = 2;
}

// A request to create an `AccessLevel`.
message CreateAccessLevelRequest {
  // Required. Resource name for the access policy which owns this [Access
  // Level] [google.identity.accesscontextmanager.v1.AccessLevel].
  //
  // Format: `accessPolicies/{policy_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "accesscontextmanager.googleapis.com/AccessLevel"
    }
  ];

  // Required. The [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] to create.
  // Syntactic correctness of the [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] is a
  // precondition for creation.
  AccessLevel access_level = 2 [(google.api.field_behavior) = REQUIRED];
}

// A request to update an `AccessLevel`.
message UpdateAccessLevelRequest {
  // Required. The updated [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel]. Syntactic
  // correctness of the [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] is a
  // precondition for creation.
  AccessLevel access_level = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Mask to control which fields get updated. Must be non-empty.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// A request to delete an `AccessLevel`.
message DeleteAccessLevelRequest {
  // Required. Resource name for the [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel].
  //
  // Format:
  // `accessPolicies/{policy_id}/accessLevels/{access_level_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/AccessLevel"
    }
  ];
}

// A request to replace all existing Access Levels in an Access Policy with
// the Access Levels provided. This is done atomically.
message ReplaceAccessLevelsRequest {
  // Required. Resource name for the access policy which owns these
  // [Access Levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel].
  //
  // Format: `accessPolicies/{policy_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "accesscontextmanager.googleapis.com/AccessLevel"
    }
  ];

  // Required. The desired [Access Levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] that should
  // replace all existing [Access Levels]
  // [google.identity.accesscontextmanager.v1.AccessLevel] in the
  // [Access Policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy].
  repeated AccessLevel access_levels = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The etag for the version of the [Access Policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy] that this
  // replace operation is to be performed on. If, at the time of replace, the
  // etag for the Access Policy stored in Access Context Manager is different
  // from the specified etag, then the replace operation will not be performed
  // and the call will fail. This field is not required. If etag is not
  // provided, the operation will be performed as if a valid etag is provided.
  string etag = 4;
}

// A response to ReplaceAccessLevelsRequest. This will be put inside of
// Operation.response field.
message ReplaceAccessLevelsResponse {
  // List of the [Access Level]
  // [google.identity.accesscontextmanager.v1.AccessLevel] instances.
  repeated AccessLevel access_levels = 1;
}

// A request to list all `ServicePerimeters` in an `AccessPolicy`.
message ListServicePerimetersRequest {
  // Required. Resource name for the access policy to list [Service Perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] from.
  //
  // Format:
  // `accessPolicies/{policy_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "accesscontextmanager.googleapis.com/ServicePerimeter"
    }
  ];

  // Number of [Service Perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] to include
  // in the list. Default 100.
  int32 page_size = 2;

  // Next page token for the next batch of [Service Perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] instances.
  // Defaults to the first page of results.
  string page_token = 3;
}

// A response to `ListServicePerimetersRequest`.
message ListServicePerimetersResponse {
  // List of the [Service Perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] instances.
  repeated ServicePerimeter service_perimeters = 1;

  // The pagination token to retrieve the next page of results. If the value is
  // empty, no further results remain.
  string next_page_token = 2;
}

// A request to get a particular `ServicePerimeter`.
message GetServicePerimeterRequest {
  // Required. Resource name for the [Service Perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter].
  //
  // Format:
  // `accessPolicies/{policy_id}/servicePerimeters/{service_perimeters_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/ServicePerimeter"
    }
  ];
}

// A request to create a `ServicePerimeter`.
message CreateServicePerimeterRequest {
  // Required. Resource name for the access policy which owns this [Service
  // Perimeter] [google.identity.accesscontextmanager.v1.ServicePerimeter].
  //
  // Format: `accessPolicies/{policy_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "accesscontextmanager.googleapis.com/ServicePerimeter"
    }
  ];

  // Required. The [Service Perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] to create.
  // Syntactic correctness of the [Service Perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] is a
  // precondition for creation.
  ServicePerimeter service_perimeter = 2 [(google.api.field_behavior) = REQUIRED];
}

// A request to update a `ServicePerimeter`.
message UpdateServicePerimeterRequest {
  // Required. The updated `ServicePerimeter`. Syntactic correctness of the
  // `ServicePerimeter` is a precondition for creation.
  ServicePerimeter service_perimeter = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Mask to control which fields get updated. Must be non-empty.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// A request to delete a `ServicePerimeter`.
message DeleteServicePerimeterRequest {
  // Required. Resource name for the [Service Perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter].
  //
  // Format:
  // `accessPolicies/{policy_id}/servicePerimeters/{service_perimeter_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/ServicePerimeter"
    }
  ];
}

// A request to replace all existing Service Perimeters in an Access Policy
// with the Service Perimeters provided. This is done atomically.
message ReplaceServicePerimetersRequest {
  // Required. Resource name for the access policy which owns these
  // [Service Perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter].
  //
  // Format: `accessPolicies/{policy_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "accesscontextmanager.googleapis.com/ServicePerimeter"
    }
  ];

  // Required. The desired [Service Perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] that should
  // replace all existing [Service Perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] in the
  // [Access Policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy].
  repeated ServicePerimeter service_perimeters = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The etag for the version of the [Access Policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy] that this
  // replace operation is to be performed on. If, at the time of replace, the
  // etag for the Access Policy stored in Access Context Manager is different
  // from the specified etag, then the replace operation will not be performed
  // and the call will fail. This field is not required. If etag is not
  // provided, the operation will be performed as if a valid etag is provided.
  string etag = 3;
}

// A response to ReplaceServicePerimetersRequest. This will be put inside of
// Operation.response field.
message ReplaceServicePerimetersResponse {
  // List of the [Service Perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] instances.
  repeated ServicePerimeter service_perimeters = 1;
}

// A request to commit dry-run specs in all [Service Perimeters]
// [google.identity.accesscontextmanager.v1.ServicePerimeter] belonging to
// an [Access Policy][google.identity.accesscontextmanager.v1.AccessPolicy].
message CommitServicePerimetersRequest {
  // Required. Resource name for the parent [Access Policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy] which owns all
  // [Service Perimeters]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] in scope for
  // the commit operation.
  //
  // Format: `accessPolicies/{policy_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "accesscontextmanager.googleapis.com/ServicePerimeter"
    }
  ];

  // Optional. The etag for the version of the [Access Policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy] that this
  // commit operation is to be performed on. If, at the time of commit, the
  // etag for the Access Policy stored in Access Context Manager is different
  // from the specified etag, then the commit operation will not be performed
  // and the call will fail. This field is not required. If etag is not
  // provided, the operation will be performed as if a valid etag is provided.
  string etag = 2;
}

// A response to CommitServicePerimetersRequest. This will be put inside of
// Operation.response field.
message CommitServicePerimetersResponse {
  // List of all the [Service Perimeter]
  // [google.identity.accesscontextmanager.v1.ServicePerimeter] instances in
  // the [Access Policy]
  // [google.identity.accesscontextmanager.v1.AccessPolicy].
  repeated ServicePerimeter service_perimeters = 1;
}

// The format used in an `AccessLevel`.
enum LevelFormat {
  // The format was not specified.
  LEVEL_FORMAT_UNSPECIFIED = 0;

  // Uses the format the resource was defined in. BasicLevels are returned as
  // BasicLevels, CustomLevels are returned as CustomLevels.
  AS_DEFINED = 1;

  // Use Cloud Common Expression Language when returning the resource.  Both
  // BasicLevels and CustomLevels are returned as CustomLevels.
  CEL = 2;
}

// Request of [ListGcpUserAccessBindings]
// [google.identity.accesscontextmanager.v1.AccessContextManager.ListGcpUserAccessBindings].
message ListGcpUserAccessBindingsRequest {
  // Required. Example: "organizations/256"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // Optional. Maximum number of items to return. The server may return fewer items.
  // If left blank, the server may return any number of items.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If left blank, returns the first page. To enumerate all items, use the
  // [next_page_token]
  // [google.identity.accesscontextmanager.v1.ListGcpUserAccessBindingsResponse.next_page_token]
  // from your previous list operation.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response of [ListGcpUserAccessBindings]
// [google.identity.accesscontextmanager.v1.AccessContextManager.ListGcpUserAccessBindings].
message ListGcpUserAccessBindingsResponse {
  // [GcpUserAccessBinding]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding]
  repeated GcpUserAccessBinding gcp_user_access_bindings = 1;

  // Token to get the next page of items. If blank, there are no more items.
  string next_page_token = 2;
}

// Request of [GetGcpUserAccessBinding]
// [google.identity.accesscontextmanager.v1.AccessContextManager.GetGcpUserAccessBinding].
message GetGcpUserAccessBindingRequest {
  // Required. Example: "organizations/256/gcpUserAccessBindings/b3-BhcX_Ud5N"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/GcpUserAccessBinding"
    }
  ];
}

// Request of [CreateGcpUserAccessBinding]
// [google.identity.accesscontextmanager.v1.AccessContextManager.CreateGcpUserAccessBinding].
message CreateGcpUserAccessBindingRequest {
  // Required. Example: "organizations/256"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // Required. [GcpUserAccessBinding]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding]
  GcpUserAccessBinding gcp_user_access_binding = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request of [UpdateGcpUserAccessBinding]
// [google.identity.accesscontextmanager.v1.AccessContextManager.UpdateGcpUserAccessBinding].
message UpdateGcpUserAccessBindingRequest {
  // Required. [GcpUserAccessBinding]
  // [google.identity.accesscontextmanager.v1.GcpUserAccessBinding]
  GcpUserAccessBinding gcp_user_access_binding = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Only the fields specified in this mask are updated. Because name and
  // group_key cannot be changed, update_mask is required and must always be:
  //
  // update_mask {
  // paths: "access_levels"
  // }
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request of [DeleteGcpUserAccessBinding]
// [google.identity.accesscontextmanager.v1.AccessContextManager.DeleteGcpUserAccessBinding].
message DeleteGcpUserAccessBindingRequest {
  // Required. Example: "organizations/256/gcpUserAccessBindings/b3-BhcX_Ud5N"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/GcpUserAccessBinding"
    }
  ];
}

// Currently, a completed operation means nothing. In the future, this metadata
// and a completed operation may indicate that the binding has taken effect and
// is affecting access decisions for all users.
message GcpUserAccessBindingOperationMetadata {

}

// Metadata of Access Context Manager's Long Running Operations.
message AccessContextManagerOperationMetadata {

}
