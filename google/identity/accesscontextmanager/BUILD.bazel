# This build file includes a target for the Ruby wrapper library for
# google-identity-access_context_manager.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for accesscontextmanager.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "accesscontextmanager_ruby_wrapper",
    srcs = ["//google/identity/accesscontextmanager/v1:accesscontextmanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-identity-access_context_manager",
        "ruby-cloud-wrapper-of=v1:0.7",
        "ruby-cloud-product-url=https://cloud.google.com/access-context-manager/",
        "ruby-cloud-api-id=accesscontextmanager.googleapis.com",
        "ruby-cloud-api-shortname=accesscontextmanager",
    ],
    ruby_cloud_description = "Access Context Manager allows enterprises to configure access levels which map to a policy defined on request attributes.",
    ruby_cloud_title = "Access Context Manager",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-identity-accesscontextmanager-ruby",
    deps = [
        ":accesscontextmanager_ruby_wrapper",
    ],
)
