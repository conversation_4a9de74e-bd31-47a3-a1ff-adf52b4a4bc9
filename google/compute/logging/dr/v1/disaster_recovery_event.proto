// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.compute.logging.dr.v1;

option go_package = "google.golang.org/genproto/googleapis/compute/logging/dr/v1;dr";
option java_multiple_files = true;
option java_outer_classname = "DisasterRecoveryEventProto";
option java_package = "com.google.compute.logging.dr.v1";
option csharp_namespace = "Google.Compute.Logging.Dr.V1";
option php_namespace = "Google\\Compute\\Logging\\Dr\\V1";
option ruby_package = "Google::Compute::Logging::Dr::V1";

// An event signifying a Compute Engine resource is impacted by the disaster
// recovery.
message DisasterRecoveryEvent {
  // The severity of the disaster recovery event.
  enum Severity {
    // Unspecified.
    SEVERITY_UNSPECIFIED = 0;

    // The Compute Engine resource is broken. A person must take an action.
    ACTION_REQUIRED = 1;

    // The Compute Engine resource is functioning. A change was applied to the
    // resource during disaster recovery. Please take action to review
    // the change to avoid unexpected problems.
    ACTION_SUGGESTED = 2;

    // Normal maintenance opeartions during disaster recovery, such as start up,
    // shut down.
    NOTICE = 3;
  }

  // The severity level.
  optional Severity severity = 1;

  // Details about the impact on the Compute Engine resource, e.g. "the resource
  // is deleted during the disaster recovery".
  optional string details = 2;
}
