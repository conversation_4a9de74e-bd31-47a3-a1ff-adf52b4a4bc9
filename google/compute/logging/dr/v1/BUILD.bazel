# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
    "moved_proto_library",
    "php_gapic_assembly_pkg",
    "php_proto_library",
    "py_grpc_library",
    "py_proto_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "dr_proto",
    srcs = [
        "disaster_recovery_event.proto",
    ],
    deps = [
    ],
)

java_proto_library(
    name = "dr_java_proto",
    deps = [":dr_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-compute-logging-dr-v1-java",
    deps = [
        ":dr_java_proto",
        ":dr_proto",
    ],
)

go_proto_library(
    name = "dr_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/compute/logging/dr/v1",
    protos = [":dr_proto"],
    deps = [
    ],
)

go_gapic_assembly_pkg(
    name = "google-compute-logging-dr-v1-go",
    deps = [
        ":dr_go_proto",
    ],
)

moved_proto_library(
    name = "dr_moved_proto",
    srcs = [":dr_proto"],
    deps = [
    ],
)

py_proto_library(
    name = "dr_py_proto",
    deps = [":dr_moved_proto"],
)

py_grpc_library(
    name = "dr_py_grpc",
    srcs = [":dr_moved_proto"],
    deps = [":dr_py_proto"],
)

php_proto_library(
    name = "dr_php_proto",
    deps = [":dr_proto"],
)

php_gapic_assembly_pkg(
    name = "google-compute-logging-dr-v1-php",
    deps = [
        ":dr_php_proto",
    ],
)

ruby_proto_library(
    name = "dr_ruby_proto",
    deps = [":dr_proto"],
)

ruby_grpc_library(
    name = "dr_ruby_grpc",
    srcs = [":dr_proto"],
    deps = [":dr_ruby_proto"],
)

csharp_proto_library(
    name = "dr_csharp_proto",
    deps = [":dr_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-compute-logging-dr-v1-csharp",
    package_name = "Google.Compute.Logging.Dr.V1",
    generate_nongapic_package = True,
    deps = [
        ":dr_csharp_proto",
    ],
)

cc_proto_library(
    name = "dr_cc_proto",
    deps = [":dr_proto"],
)

cc_grpc_library(
    name = "dr_cc_grpc",
    srcs = [":dr_proto"],
    grpc_only = True,
    deps = [":dr_cc_proto"],
)
