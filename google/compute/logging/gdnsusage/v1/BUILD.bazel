# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "gdnsusage_proto",
    srcs = [
        "gdns_vm_usage.proto",
    ],
    deps = [
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "gdnsusage_java_proto",
    deps = [":gdnsusage_proto"],
)

java_grpc_library(
    name = "gdnsusage_java_grpc",
    srcs = [":gdnsusage_proto"],
    deps = [":gdnsusage_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_proto_library",
)

go_proto_library(
    name = "gdnsusage_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/compute/logging/gdnsusage/v1",
    protos = [":gdnsusage_proto"],
    deps = [
    ],
)

go_gapic_assembly_pkg(
    name = "gdnsusage-v1-go",
    deps = [
        ":gdnsusage_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "gdnsusage_moved_proto",
    srcs = [":gdnsusage_proto"],
    deps = [
    ],
)

py_proto_library(
    name = "gdnsusage_py_proto",
    deps = [":gdnsusage_moved_proto"],
)

py_grpc_library(
    name = "gdnsusage_py_grpc",
    srcs = [":gdnsusage_moved_proto"],
    deps = [":gdnsusage_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "gdnsusage_php_proto",
    deps = [":gdnsusage_proto"],
)

php_gapic_assembly_pkg(
    name = "gdnsusage-v1-php",
    deps = [
        ":gdnsusage_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "gdnsusage_ruby_proto",
    deps = [":gdnsusage_proto"],
)

ruby_grpc_library(
    name = "gdnsusage_ruby_grpc",
    srcs = [":gdnsusage_proto"],
    deps = [":gdnsusage_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "gdnsusage_csharp_proto",
    deps = [":gdnsusage_proto"],
)

csharp_grpc_library(
    name = "gdnsusage_csharp_grpc",
    srcs = [":gdnsusage_proto"],
    deps = [":gdnsusage_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "gdnsusage_cc_proto",
    deps = [":gdnsusage_proto"],
)

cc_grpc_library(
    name = "gdnsusage_cc_grpc",
    srcs = [":gdnsusage_proto"],
    grpc_only = True,
    deps = [":gdnsusage_cc_proto"],
)
