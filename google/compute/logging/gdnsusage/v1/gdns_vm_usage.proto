// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.compute.logging.gdnsusage.v1;

option go_package = "google.golang.org/genproto/googleapis/compute/logging/gdnsusage/v1;gdnsusage";
option java_multiple_files = true;
option java_outer_classname = "GdnsVmUsageProto";
option java_package = "google.compute.logging.gdnsusage.v1";
option csharp_namespace = "Google.Compute.Logging.GdnsUsage.V1";
option php_namespace = "Google\\Compute\\Logging\\GdnsUsage\\V1";
option ruby_package = "Google::Compute::Logging::GdnsUsage::V1";

// gdns_vm_usage.proto
// Definition of json_payload of Global Dns usage per-vm level for
// Platform Logs.

// Used for structured payload for reporting Platform Logs
message GdnsVmUsagePlatformLog {
  // source vm's information
  optional VmInfo source_vm = 1;

  // destination vm's information
  optional VmInfo destination_vm = 2;

  // message that informs users on how to fix the global DNS query that is
  // blocking the zonal DNS migration
  optional string debug_message = 3;

  // number of zDNS migration blocking queries sent from source_vm to
  // destination_vm
  optional int32 query_count = 5;
}

// VM details
message VmInfo {
  // project id of the vm
  optional string project_id = 1;

  // name of the vm
  optional string vm = 2;

  // zone of the vm
  optional string zone = 3;
}
