load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
proto_library(
    name = "code_proto",
    srcs = ["code.proto"],
    deps = [],
)

proto_library(
    name = "error_details_proto",
    srcs = ["error_details.proto"],
    deps = [
        "@com_google_protobuf//:duration_proto",
    ],
)

proto_library(
    name = "http_proto",
    srcs = ["http.proto"],
)

proto_library(
    name = "status_proto",
    srcs = ["status.proto"],
    deps = [
        "@com_google_protobuf//:any_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "rpc_java_proto",
    deps = [
        ":code_proto",
        ":error_details_proto",
        ":status_proto",
    ],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-rpc-java",
    transport = "grpc+rest",
    deps = [
        "error_details_proto",
        "status_proto",
        ":code_proto",
        ":rpc_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "go_proto_library")

go_proto_library(
    name = "code_go_proto",
    importpath = "google.golang.org/genproto/googleapis/rpc/code",
    protos = [":code_proto"],
)

go_proto_library(
    name = "errdetails_go_proto",
    importpath = "google.golang.org/genproto/googleapis/rpc/errdetails",
    protos = [":error_details_proto"],
)

go_proto_library(
    name = "status_go_proto",
    importpath = "google.golang.org/genproto/googleapis/rpc/status",
    protos = [":status_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_proto_library",
)

cc_proto_library(
    name = "code_cc_proto",
    deps = [":code_proto"],
)

cc_proto_library(
    name = "error_details_cc_proto",
    deps = [":error_details_proto"],
)

cc_proto_library(
    name = "status_cc_proto",
    deps = [":status_proto"],
)

##############################################################################
# Python
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "py_gapic_assembly_pkg", "py_proto_library")

py_proto_library(
    name = "code_py_proto",
    deps = [":code_proto"],
)

py_proto_library(
    name = "error_details_py_proto",
    deps = [":error_details_proto"],
)

py_proto_library(
    name = "http_py_proto",
    deps = [":http_proto"],
)

py_proto_library(
    name = "status_py_proto",
    deps = [":status_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "rpc-py",
    deps = [
        ":code_py_proto",
        ":error_details_py_proto",
        ":http_py_proto",
        ":status_py_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "rpc_php_proto",
    deps = [
        ":code_proto",
        ":error_details_proto",
        ":status_proto",
    ],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate php files for these protos.
# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-rpc-php",
    deps = [":rpc_php_proto"],
)
