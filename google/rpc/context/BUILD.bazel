# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "attribute_context_proto",
    srcs = [
        "attribute_context.proto",
    ],
    deps = [
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

# Manually added target. See b/274975612 for why.
proto_library(
    name = "audit_context_proto",
    srcs = [
        "audit_context.proto",
    ],
    deps = [
        "@com_google_protobuf//:struct_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "attribute_context_java_proto",
    deps = [":attribute_context_proto"],
)

# Manually added target. See b/274975612 for why.
java_proto_library(
    name = "audit_context_java_proto",
    deps = [":audit_context_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-context-java",
    transport = "grpc+rest",
    deps = [
        ":attribute_context_java_proto",
        ":attribute_context_proto",
        # Manually added. See b/274975612 for why.
        ":audit_context_java_proto",
        ":audit_context_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "attribute_context_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/rpc/context/attribute_context",
    protos = [":attribute_context_proto"],
    deps = [],
)

# Manually added target. See b/274975612 for why.
go_proto_library(
    name = "audit_context_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/rpc/context;context",
    protos = [":audit_context_proto"],
    deps = [],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_proto_library",
    "py_gapic_assembly_pkg",
)

py_proto_library(
    name = "attribute_context_py_proto",
    deps = [":attribute_context_proto"],
)

py_proto_library(
    name = "audit_context_py_proto",
    deps = [":audit_context_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "rpc-context-py",
    deps = [
        ":attribute_context_py_proto",
        ":audit_context_py_proto",
    ],
)


##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "attribute_context_php_proto",
    deps = [":attribute_context_proto"],
)

# Manually added target. See b/274975612 for why.
php_proto_library(
    name = "audit_context_php_proto",
    deps = [":audit_context_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate PHP files for these protos.
# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-context-php",
    deps = [
        ":attribute_context_php_proto",
        ":audit_context_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "attribute_context_ruby_proto",
    deps = [":attribute_context_proto"],
)

# Manually added target. See b/274975612 for why.
ruby_proto_library(
    name = "audit_context_ruby_proto",
    deps = [":audit_context_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "attribute_context_csharp_proto",
    deps = [":attribute_context_proto"],
)

# Manually added target. See b/274975612 for why.
csharp_proto_library(
    name = "audit_context_csharp_proto",
    deps = [":audit_context_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_proto_library",
)

cc_proto_library(
    name = "attribute_context_cc_proto",
    deps = [":attribute_context_proto"],
)

# Manually added target. See b/274975612 for why.
cc_proto_library(
    name = "audit_context_cc_proto",
    deps = [":audit_context_proto"],
)
