// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.home.enterprise.sdm.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Home.Enterprise.Sdm.V1";
option go_package = "google.golang.org/genproto/googleapis/home/<USER>/sdm/v1;sdm";
option java_multiple_files = true;
option java_package = "com.google.home.enterprise.sdm.v1";
option objc_class_prefix = "GHENTSDM";
option php_namespace = "Google\\Home\\Enterprise\\Sdm\\V1";

// Device resource represents an instance of enterprise managed device in the
// property.
message Device {
  option (google.api.resource) = {
    type: "smartdevicemanagement.googleapis.com/Device"
    pattern: "enterprises/{enterprise}/devices/{device}"
  };

  // Required. The resource name of the device. For example:
  // "enterprises/XYZ/devices/123".
  string name = 1;

  // Output only. Type of the device for general display purposes.
  // For example: "THERMOSTAT". The device type should not be used to deduce or
  // infer functionality of the actual device it is assigned to. Instead, use
  // the returned traits for the device.
  string type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Device traits.
  google.protobuf.Struct traits = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Assignee details of the device.
  repeated ParentRelation parent_relations = 5;
}

// Represents device relationships, for instance, structure/room to which the
// device is assigned to.
message ParentRelation {
  // Output only. The name of the relation -- e.g., structure/room where the
  // device is assigned to. For example: "enterprises/XYZ/structures/ABC" or
  // "enterprises/XYZ/structures/ABC/rooms/123"
  string parent = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The custom name of the relation -- e.g., structure/room where
  // the device is assigned to.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
