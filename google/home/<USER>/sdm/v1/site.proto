// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.home.enterprise.sdm.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Home.Enterprise.Sdm.V1";
option go_package = "google.golang.org/genproto/googleapis/home/<USER>/sdm/v1;sdm";
option java_multiple_files = true;
option java_package = "com.google.home.enterprise.sdm.v1";
option objc_class_prefix = "GHENTSDM";
option php_namespace = "Google\\Home\\Enterprise\\Sdm\\V1";

// Structure resource represents an instance of enterprise managed home or hotel
// room.
message Structure {
  option (google.api.resource) = {
    type: "smartdevicemanagement.googleapis.com/Structure"
    pattern: "enterprises/{enterprise}/structures/{structure}"
  };

  // Output only. The resource name of the structure. For example:
  // "enterprises/XYZ/structures/ABC".
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Structure traits.
  google.protobuf.Struct traits = 2;
}

// Room resource represents an instance of sub-space within a structure such as
// rooms in a hotel suite or rental apartment.
message Room {
  option (google.api.resource) = {
    type: "smartdevicemanagement.googleapis.com/Room"
    pattern: "enterprises/{enterprise}/structures/{structure}/rooms/{room}"
  };

  // Output only. The resource name of the room. For example:
  // "enterprises/XYZ/structures/ABC/rooms/123".
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Room traits.
  google.protobuf.Struct traits = 2;
}
