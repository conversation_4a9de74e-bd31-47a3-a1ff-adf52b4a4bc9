// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.home.enterprise.sdm.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/home/<USER>/sdm/v1/device.proto";
import "google/home/<USER>/sdm/v1/site.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Home.Enterprise.Sdm.V1";
option go_package = "google.golang.org/genproto/googleapis/home/<USER>/sdm/v1;sdm";
option java_multiple_files = true;
option java_package = "com.google.home.enterprise.sdm.v1";
option objc_class_prefix = "GHENTSDM";
option php_namespace = "Google\\Home\\Enterprise\\Sdm\\V1";

// core::0122 is getting triggered by adding fields custom_name.
// though, 0122 has exception for such field name as display_name. Due to
// historical reasons we use custom_name for exactly same purpose, so it is
// covered by that exception.

// A service that allows API consumers to provision and manage Google
// Home structures and devices for enterprise use cases.
service SmartDeviceManagementService {
  option (google.api.default_host) = "smartdevicemanagement.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/sdm.service";

  // Gets a device managed by the enterprise.
  rpc GetDevice(GetDeviceRequest) returns (Device) {
    option (google.api.http) = {
      get: "/v1/{name=enterprises/*/devices/*}"
    };
  }

  // Lists devices managed by the enterprise.
  rpc ListDevices(ListDevicesRequest) returns (ListDevicesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=enterprises/*}/devices"
    };
  }

  // Executes a command to device managed by the enterprise.
  rpc ExecuteDeviceCommand(ExecuteDeviceCommandRequest)
      returns (ExecuteDeviceCommandResponse) {
    option (google.api.http) = {
      post: "/v1/{name=enterprises/*/devices/*}:executeCommand"
      body: "*"
    };
  }

  // Gets a structure managed by the enterprise.
  rpc GetStructure(GetStructureRequest) returns (Structure) {
    option (google.api.http) = {
      get: "/v1/{name=enterprises/*/structures/*}"
    };
  }

  // Lists structures managed by the enterprise.
  rpc ListStructures(ListStructuresRequest) returns (ListStructuresResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=enterprises/*}/structures"
    };
  }

  // Gets a room managed by the enterprise.
  rpc GetRoom(GetRoomRequest) returns (Room) {
    option (google.api.http) = {
      get: "/v1/{name=enterprises/*/structures/*/rooms/*}"
    };
  }

  // Lists rooms managed by the enterprise.
  rpc ListRooms(ListRoomsRequest) returns (ListRoomsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=enterprises/*/structures/*}/rooms"
    };
  }
}

// Request message for SmartDeviceManagementService.GetDevice
message GetDeviceRequest {
  // The name of the device requested. For example:
  // "enterprises/XYZ/devices/123"
  string name = 1;
}

// Request message for SmartDeviceManagementService.ListDevices
message ListDevicesRequest {
  // The parent enterprise to list devices under. E.g. "enterprises/XYZ".
  string parent = 1;

  // Optional requested page size. Server may return fewer devices than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // Optional token of the page to retrieve.
  string page_token = 3;

  // Optional filter to list devices.
  //
  // Filters can be done on:
  // Device custom name (substring match):
  // 'customName=wing'
  string filter = 4;
}

// Response message for SmartDeviceManagementService.ListDevices
message ListDevicesResponse {
  // The list of devices.
  repeated Device devices = 1;

  // The pagination token to retrieve the next page of results.
  string next_page_token = 2;
}

// Request message for SmartDeviceManagementService.ExecuteDeviceCommand
message ExecuteDeviceCommandRequest {
  // The name of the device requested. For example:
  // "enterprises/XYZ/devices/123"
  string name = 1;

  // The command name to execute, represented by the fully qualified protobuf
  // message name.
  string command = 2;

  // The command message to execute, represented as a Struct.
  google.protobuf.Struct params = 3;
}

// Response message for SmartDeviceManagementService.ExecuteDeviceCommand
message ExecuteDeviceCommandResponse {
  // The results of executing the command.
  google.protobuf.Struct results = 1;
}

// Request message for SmartDeviceManagementService.GetStructure
message GetStructureRequest {
  // The name of the structure requested. For example:
  // "enterprises/XYZ/structures/ABC".
  string name = 1;
}

// Request message for SmartDeviceManagementService.ListStructures
message ListStructuresRequest {
  // The parent enterprise to list structures under. E.g. "enterprises/XYZ".
  string parent = 1;

  // Requested page size. Server may return fewer structures than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // The token of the page to retrieve.
  string page_token = 3;

  // Optional filter to list structures.
  string filter = 4;
}

// Response message for SmartDeviceManagementService.ListStructures
message ListStructuresResponse {
  // The list of structures.
  repeated Structure structures = 1;

  // The pagination token to retrieve the next page of results.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for SmartDeviceManagementService.GetRoom
message GetRoomRequest {
  // The name of the room requested. For example:
  // "enterprises/XYZ/structures/ABC/rooms/123".
  string name = 1;
}

// Request message for SmartDeviceManagementService.ListRooms
message ListRoomsRequest {
  // The parent resource name of the rooms requested. For example:
  // "enterprises/XYZ/structures/ABC".
  string parent = 1;

  // Requested page size. Server may return fewer rooms than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // The token of the page to retrieve.
  string page_token = 3;
}

// Response message for SmartDeviceManagementService.ListRooms
message ListRoomsResponse {
  // The list of rooms.
  repeated Room rooms = 1;

  // The pagination token to retrieve the next page of results.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
