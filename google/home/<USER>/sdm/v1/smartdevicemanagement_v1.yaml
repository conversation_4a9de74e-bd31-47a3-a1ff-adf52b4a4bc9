type: google.api.Service
config_version: 3
name: smartdevicemanagement.googleapis.com
title: Smart Device Management API

apis:
- name: google.home.enterprise.sdm.v1.SmartDeviceManagementService

documentation:
  summary: |-
    Allow select enterprise partners to access, control, and manage Google and
    Nest devices programmatically.

authentication:
  rules:
  - selector: 'google.home.enterprise.sdm.v1.SmartDeviceManagementService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/sdm.service

publishing:
  organization: CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED
  new_issue_uri: ''
  documentation_uri: ''
  api_short_name: ''
  github_label: ''
  doc_tag_prefix: ''
  codeowner_github_teams:
  library_settings:
