# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "sdm_proto",
    srcs = [
        "device.proto",
        "site.proto",
        "smart_device_management_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library_with_info(
    name = "sdm_proto_with_info",
    deps = [
        ":sdm_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "sdm_java_proto",
    deps = [":sdm_proto"],
)

java_grpc_library(
    name = "sdm_java_grpc",
    srcs = [":sdm_proto"],
    deps = [":sdm_java_proto"],
)

java_gapic_library(
    name = "sdm_java_gapic",
    srcs = [":sdm_proto_with_info"],
    grpc_service_config = "smart_device_management_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "smartdevicemanagement_v1.yaml",
    test_deps = [
        ":sdm_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":sdm_java_proto",
    ],
)

java_gapic_test(
    name = "sdm_java_gapic_test_suite",
    test_classes = [
        "com.google.home.enterprise.sdm.v1.SmartDeviceManagementServiceClientHttpJsonTest",
        "com.google.home.enterprise.sdm.v1.SmartDeviceManagementServiceClientTest",
    ],
    runtime_deps = [":sdm_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-enterprise-sdm-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":sdm_java_gapic",
        ":sdm_java_grpc",
        ":sdm_java_proto",
        ":sdm_proto",
    ],
)

go_proto_library(
    name = "sdm_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/home/<USER>/sdm/v1",
    protos = [":sdm_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "sdm_go_gapic",
    srcs = [":sdm_proto_with_info"],
    grpc_service_config = "smart_device_management_grpc_service_config.json",
    importpath = "google.golang.org/google/home/<USER>/sdm/v1;sdm",
    rest_numeric_enums = True,
    service_yaml = "smartdevicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":sdm_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-enterprise-sdm-v1-go",
    deps = [
        ":sdm_go_gapic",
        ":sdm_go_gapic_srcjar-snippets.srcjar",
        ":sdm_go_gapic_srcjar-test.srcjar",
        ":sdm_go_proto",
    ],
)

py_gapic_library(
    name = "sdm_py_gapic",
    srcs = [":sdm_proto"],
    grpc_service_config = "smart_device_management_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "smartdevicemanagement_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "sdm_py_gapic_test",
    srcs = [
        "sdm_py_gapic_pytest.py",
        "sdm_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":sdm_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "enterprise-sdm-v1-py",
    deps = [
        ":sdm_py_gapic",
    ],
)

php_proto_library(
    name = "sdm_php_proto",
    deps = [":sdm_proto"],
)

php_gapic_library(
    name = "sdm_php_gapic",
    srcs = [":sdm_proto_with_info"],
    grpc_service_config = "smart_device_management_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "smartdevicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [":sdm_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-enterprise-sdm-v1-php",
    deps = [
        ":sdm_php_gapic",
        ":sdm_php_proto",
    ],
)

nodejs_gapic_library(
    name = "sdm_nodejs_gapic",
    src = ":sdm_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "smart_device_management_grpc_service_config.json",
    package = "google.home.enterprise.sdm.v1",
    rest_numeric_enums = True,
    service_yaml = "smartdevicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "enterprise-sdm-v1-nodejs",
    deps = [
        ":sdm_nodejs_gapic",
        ":sdm_proto",
    ],
)

ruby_proto_library(
    name = "sdm_ruby_proto",
    deps = [":sdm_proto"],
)

ruby_grpc_library(
    name = "sdm_ruby_grpc",
    srcs = [":sdm_proto"],
    deps = [":sdm_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "sdm_ruby_gapic",
    srcs = [":sdm_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-sdm-v1"],
    grpc_service_config = "smart_device_management_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "smartdevicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":sdm_ruby_grpc",
        ":sdm_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-enterprise-sdm-v1-ruby",
    deps = [
        ":sdm_ruby_gapic",
        ":sdm_ruby_grpc",
        ":sdm_ruby_proto",
    ],
)

csharp_proto_library(
    name = "sdm_csharp_proto",
    deps = [":sdm_proto"],
)

csharp_grpc_library(
    name = "sdm_csharp_grpc",
    srcs = [":sdm_proto"],
    deps = [":sdm_csharp_proto"],
)

csharp_gapic_library(
    name = "sdm_csharp_gapic",
    srcs = [":sdm_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "smart_device_management_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "smartdevicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":sdm_csharp_grpc",
        ":sdm_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-enterprise-sdm-v1-csharp",
    deps = [
        ":sdm_csharp_gapic",
        ":sdm_csharp_grpc",
        ":sdm_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
