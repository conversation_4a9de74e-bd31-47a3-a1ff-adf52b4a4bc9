{"methodConfig": [{"name": [{"service": "google.home.graph.v1.HomeGraphApiService", "method": "RequestSyncDevices"}, {"service": "google.home.graph.v1.HomeGraphApiService", "method": "ReportStateAndNotification"}], "timeout": "10s"}, {"name": [{"service": "google.home.graph.v1.HomeGraphApiService", "method": "DeleteAgentUser"}, {"service": "google.home.graph.v1.HomeGraphApiService", "method": "Query"}, {"service": "google.home.graph.v1.HomeGraphApiService", "method": "Sync"}], "timeout": "10s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}