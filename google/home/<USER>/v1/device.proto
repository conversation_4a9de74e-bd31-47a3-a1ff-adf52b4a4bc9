// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.home.graph.v1;

import "google/protobuf/struct.proto";

option go_package = "google.golang.org/genproto/googleapis/home/<USER>/v1;graph";
option java_outer_classname = "DeviceProto";
option java_package = "com.google.home.graph.v1";
option php_namespace = "Google\\Home\\Graph\\V1";

// Third-party device definition.
message Device {
  // Third-party device ID.
  string id = 1;

  // Hardware type of the device.
  // See [device
  // types](https://developers.home.google.com/cloud-to-cloud/guides).
  string type = 2;

  // Traits supported by the device.
  // See [device
  // traits](https://developers.home.google.com/cloud-to-cloud/traits).
  repeated string traits = 3;

  // Names given to this device by your smart home Action.
  DeviceNames name = 4;

  // Indicates whether your smart home Action will report state of this device
  // to Google via
  // [ReportStateAndNotification][google.home.graph.v1.HomeGraphApiService.ReportStateAndNotification].
  bool will_report_state = 5;

  // Suggested name for the room where this device is installed.
  // Google attempts to use this value during user setup.
  string room_hint = 6;

  // Suggested name for the structure where this device is installed.
  // Google attempts to use this value during user setup.
  string structure_hint = 7;

  // Device manufacturer, model, hardware version, and software version.
  DeviceInfo device_info = 8;

  // Attributes for the traits supported by the device.
  google.protobuf.Struct attributes = 9;

  // Custom device attributes stored in Home Graph and provided to your
  // smart home Action in each
  // [QUERY](https://developers.home.google.com/cloud-to-cloud/intents/query)
  // and
  // [EXECUTE](https://developers.home.google.com/cloud-to-cloud/intents/execute)
  // intent.
  // Data in this object has a few constraints: No sensitive information,
  // including but not limited to Personally Identifiable Information.
  google.protobuf.Struct custom_data = 10;

  // Alternate IDs associated with this device.
  // This is used to identify cloud synced devices enabled for [local
  // fulfillment](https://developers.home.google.com/local-home/overview).
  repeated AgentOtherDeviceId other_device_ids = 11;

  // Indicates whether your smart home Action will report notifications
  // to Google for this device via
  // [ReportStateAndNotification][google.home.graph.v1.HomeGraphApiService.ReportStateAndNotification].
  //
  // If your smart home Action enables users to control device notifications,
  // you should update this field and call
  // [RequestSyncDevices][google.home.graph.v1.HomeGraphApiService.RequestSyncDevices].
  bool notification_supported_by_agent = 12;
}

// Identifiers used to describe the device.
message DeviceNames {
  // Primary name of the device, generally provided by the user.
  string name = 1;

  // Additional names provided by the user for the device.
  repeated string nicknames = 2;

  // List of names provided by the manufacturer rather than the user, such as
  // serial numbers, SKUs, etc.
  repeated string default_names = 3;
}

// Device information.
message DeviceInfo {
  // Device manufacturer.
  string manufacturer = 1;

  // Device model.
  string model = 2;

  // Device hardware version.
  string hw_version = 3;

  // Device software version.
  string sw_version = 4;
}

// Alternate third-party device ID.
message AgentOtherDeviceId {
  // Project ID for your smart home Action.
  string agent_id = 1;

  // Unique third-party device ID.
  string device_id = 2;
}
