// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admob.v1;

import "google/ads/admob/v1/admob_resources.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "google.golang.org/genproto/googleapis/ads/admob/v1;admob";
option java_outer_classname = "AdMobApiProto";
option java_package = "com.google.ads.admob.v1";

// The AdMob API allows AdMob publishers programmatically get information about
// their AdMob account.
service AdMobApi {
  option (google.api.default_host) = "admob.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/admob.report";

  // Gets information about the specified AdMob publisher account.
  rpc GetPublisherAccount(GetPublisherAccountRequest)
      returns (PublisherAccount) {
    option (google.api.http) = {
      get: "/v1/{name=accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the AdMob publisher account accessible with the client credential.
  // Currently, all credentials have access to at most one AdMob account.
  rpc ListPublisherAccounts(ListPublisherAccountsRequest)
      returns (ListPublisherAccountsResponse) {
    option (google.api.http) = {
      get: "/v1/accounts"
    };
  }

  // Generates an AdMob Network report based on the provided report
  // specification.
  rpc GenerateNetworkReport(GenerateNetworkReportRequest)
      returns (stream GenerateNetworkReportResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}/networkReport:generate"
      body: "*"
    };
  }

  // Generates an AdMob Mediation report based on the provided report
  // specification.
  rpc GenerateMediationReport(GenerateMediationReportRequest)
      returns (stream GenerateMediationReportResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}/mediationReport:generate"
      body: "*"
    };
  }
}

// Request to retrieve the specified publisher account.
message GetPublisherAccountRequest {
  // Resource name of the publisher account to retrieve.
  // Example: accounts/pub-****************
  string name = 1;
}

// Request to retrieve the AdMob publisher account accessible with the client
// credential. Currently all credentials have access to at most 1 account.
message ListPublisherAccountsRequest {
  // Maximum number of accounts to return.
  int32 page_size = 1;

  // The value returned by the last `ListPublisherAccountsResponse`; indicates
  // that this is a continuation of a prior `ListPublisherAccounts` call, and
  // that the system should return the next page of data.
  string page_token = 2;
}

// Response for the publisher account list request.
message ListPublisherAccountsResponse {
  // Publisher that the client credentials can access.
  repeated PublisherAccount account = 1;

  // If not empty, indicates that there might be more accounts for the request;
  // you must pass this value in a new `ListPublisherAccountsRequest`.
  string next_page_token = 2;
}

// Request to generate an AdMob Mediation report.
message GenerateMediationReportRequest {
  // Resource name of the account to generate the report for.
  // Example: accounts/pub-****************
  string parent = 1;

  // Network report specification.
  MediationReportSpec report_spec = 2;
}

// The streaming response for the AdMob Mediation report where the first
// response contains the report header, then a stream of row responses, and
// finally a footer as the last response message.
//
// For example:
//
//     [{
//       "header": {
//         "date_range": {
//           "start_date": {"year": 2018, "month": 9, "day": 1},
//           "end_date": {"year": 2018, "month": 9, "day": 1}
//         },
//         "localization_settings": {
//           "currency_code": "USD",
//           "language_code": "en-US"
//         }
//       }
//     },
//     {
//       "row": {
//         "dimension_values": {
//           "DATE": {"value": "********"},
//           "APP": {
//             "value": "ca-app-pub-****************~**********",
//              "display_label": "My app name!"
//           }
//         },
//         "metric_values": {
//           "ESTIMATED_EARNINGS": {"decimal_value": "1324746"}
//         }
//       }
//     },
//     {
//       "footer": {"matching_row_count": 1}
//     }]
message GenerateMediationReportResponse {
  // Each stream response message contains one type of payload.
  oneof payload {
    // Report generation settings that describes the report contents, such as
    // the report date range and localization settings.
    ReportHeader header = 1;

    // Actual report data.
    ReportRow row = 2;

    // Additional information about the generated report, such as warnings about
    // the data.
    ReportFooter footer = 3;
  }
}

// Request to generate an AdMob Network report.
message GenerateNetworkReportRequest {
  // Resource name of the account to generate the report for.
  // Example: accounts/pub-****************
  string parent = 1;

  // Network report specification.
  NetworkReportSpec report_spec = 2;
}

// The streaming response for the AdMob Network report where the first response
// contains the report header, then a stream of row responses, and finally a
// footer as the last response message.
//
// For example:
//
//     [{
//       "header": {
//         "dateRange": {
//           "startDate": {"year": 2018, "month": 9, "day": 1},
//           "endDate": {"year": 2018, "month": 9, "day": 1}
//         },
//         "localizationSettings": {
//           "currencyCode": "USD",
//           "languageCode": "en-US"
//         }
//       }
//     },
//     {
//       "row": {
//         "dimensionValues": {
//           "DATE": {"value": "********"},
//           "APP": {
//             "value": "ca-app-pub-****************~**********",
//              displayLabel: "My app name!"
//           }
//         },
//         "metricValues": {
//           "ESTIMATED_EARNINGS": {"microsValue": 6500000}
//         }
//       }
//     },
//     {
//       "footer": {"matchingRowCount": 1}
//     }]
message GenerateNetworkReportResponse {
  // Each stream response message contains one type of payload.
  oneof payload {
    // Report generation settings that describes the report contents, such as
    // the report date range and localization settings.
    ReportHeader header = 1;

    // Actual report data.
    ReportRow row = 2;

    // Additional information about the generated report, such as warnings about
    // the data.
    ReportFooter footer = 3;
  }
}
