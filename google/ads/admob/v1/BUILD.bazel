# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "admob_proto",
    srcs = [
        "admob_api.proto",
        "admob_resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/cloud:common_resources_proto",
        "//google/type:date_proto",
    ],
)

proto_library_with_info(
    name = "admob_proto_with_info",
    deps = [
        ":admob_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "admob_java_proto",
    deps = [":admob_proto"],
)

java_grpc_library(
    name = "admob_java_grpc",
    srcs = [":admob_proto"],
    deps = [":admob_java_proto"],
)

java_gapic_library(
    name = "admob_java_gapic",
    srcs = [
        ":admob_proto_with_info",
    ],
    gapic_yaml = "admob_gapic.yaml",
    grpc_service_config = "//google/ads/admob:admob_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "//google/ads/admob:admob_v1.yaml",
    test_deps = [
        ":admob_java_grpc",
    ],
    transport = "rest",
    deps = [
        ":admob_java_proto",
    ],
)

java_gapic_test(
    name = "admob_java_gapic_test_suite",
    test_classes = [
        "com.google.ads.admob.v1.AdMobApiClientTest",
    ],
    runtime_deps = [":admob_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-ads-admob-v1-java",
    transport = "rest",
    deps = [
        ":admob_java_gapic",
        ":admob_java_grpc",
        ":admob_java_proto",
        ":admob_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "admob_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/ads/admob/v1",
    protos = [":admob_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "admob_go_gapic",
    srcs = [":admob_proto_with_info"],
    grpc_service_config = None,
    importpath = "google.golang.org/google/ads/admob/v1;admob",
    rest_numeric_enums = True,
    service_yaml = "//google/ads/admob:admob_v1.yaml",
    transport = "rest",
    deps = [
        ":admob_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-ads-admob-v1-go",
    deps = [
        ":admob_go_gapic",
        ":admob_go_gapic_srcjar-snippets.srcjar",
        ":admob_go_gapic_srcjar-test.srcjar",
        ":admob_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_grpc_library",
    "py_proto_library",
    "py_test",
)

py_proto_library(
    name = "admob_py_proto",
    deps = [":admob_proto"],
)

py_grpc_library(
    name = "admob_py_grpc",
    srcs = [":admob_proto"],
    deps = [":admob_py_proto"],
)

py_gapic_library(
    name = "admob_py_gapic",
    srcs = [
        ":admob_proto_with_info",
    ],
    rest_numeric_enums = True,
    service_yaml = "//google/ads/admob:admob_v1.yaml",
    transport = "rest",
)

py_test(
    name = "admob_py_gapic_test",
    srcs = [
        "admob_py_gapic_pytest.py",
        "admob_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":admob_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "ads-admob-v1-py",
    deps = [
        ":admob_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_grpc_library",
    "php_proto_library",
)

php_proto_library(
    name = "admob_php_proto",
    deps = [":admob_proto"],
)

php_grpc_library(
    name = "admob_php_grpc",
    srcs = [":admob_proto"],
    deps = [":admob_php_proto"],
)

php_gapic_library(
    name = "admob_php_gapic",
    srcs = [":admob_proto"],
    gapic_yaml = "admob_gapic.yaml",
    grpc_service_config = None,
    rest_numeric_enums = True,
    service_yaml = "//google/ads/admob:admob_v1.yaml",
    transport = "rest",
    deps = [
        ":admob_php_grpc",
        ":admob_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-ads-admob-v1-php",
    deps = [
        ":admob_php_gapic",
        ":admob_php_grpc",
        ":admob_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "admob_nodejs_gapic",
    src = ":admob_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = None,
    package = "google.ads.admob.v1",
    rest_numeric_enums = True,
    service_yaml = "//google/ads/admob:admob_v1.yaml",
    transport = "rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "ads-admob-v1-nodejs",
    deps = [
        ":admob_nodejs_gapic",
        ":admob_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "admob_ruby_proto",
    deps = [":admob_proto"],
)

ruby_grpc_library(
    name = "admob_ruby_grpc",
    srcs = [":admob_proto"],
    deps = [":admob_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "admob_ruby_gapic",
    srcs = [":admob_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-admob-v1"],
    grpc_service_config = None,
    rest_numeric_enums = True,
    service_yaml = "//google/ads/admob:admob_v1.yaml",
    transport = "rest",
    deps = [
        ":admob_ruby_grpc",
        ":admob_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-ads-admob-v1-ruby",
    deps = [
        ":admob_ruby_gapic",
        ":admob_ruby_grpc",
        ":admob_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "admob_csharp_proto",
    deps = [":admob_proto"],
)

csharp_grpc_library(
    name = "admob_csharp_grpc",
    srcs = [":admob_proto"],
    deps = [":admob_csharp_proto"],
)

csharp_gapic_library(
    name = "admob_csharp_gapic",
    srcs = [":admob_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    rest_numeric_enums = True,
    service_yaml = "//google/ads/admob:admob_v1.yaml",
    transport = "rest",
    deps = [
        ":admob_csharp_grpc",
        ":admob_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-ads-admob-v1-csharp",
    deps = [
        ":admob_csharp_gapic",
        ":admob_csharp_grpc",
        ":admob_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
