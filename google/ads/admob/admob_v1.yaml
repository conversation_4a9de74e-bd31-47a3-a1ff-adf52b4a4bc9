type: google.api.Service
config_version: 3
name: admob.googleapis.com
title: AdMob API

apis:
- name: google.ads.admob.v1.AdMobApi

documentation:
  summary: The AdMob API allows publishers to programmatically get information about their AdMob
           account.

backend:
  rules:
  - selector: 'google.ads.admob.v1.AdMobApi.*'
    deadline: 120.0

authentication:
  rules:
  - selector: 'google.ads.admob.v1.AdMobApi.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/admob.report
