// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.errors;

import "google/ads/searchads360/v0/common/value.proto";
import "google/ads/searchads360/v0/errors/authentication_error.proto";
import "google/ads/searchads360/v0/errors/authorization_error.proto";
import "google/ads/searchads360/v0/errors/custom_column_error.proto";
import "google/ads/searchads360/v0/errors/date_error.proto";
import "google/ads/searchads360/v0/errors/date_range_error.proto";
import "google/ads/searchads360/v0/errors/distinct_error.proto";
import "google/ads/searchads360/v0/errors/header_error.proto";
import "google/ads/searchads360/v0/errors/internal_error.proto";
import "google/ads/searchads360/v0/errors/invalid_parameter_error.proto";
import "google/ads/searchads360/v0/errors/query_error.proto";
import "google/ads/searchads360/v0/errors/quota_error.proto";
import "google/ads/searchads360/v0/errors/request_error.proto";
import "google/ads/searchads360/v0/errors/size_limit_error.proto";
import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ErrorsProto";
option java_package = "com.google.ads.searchads360.v0.errors";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Errors";
option ruby_package = "Google::Ads::SearchAds360::V0::Errors";

// Proto file describing the common error protos

// Describes how a Search Ads 360 API call failed. It's returned inside
// google.rpc.Status.details when a call fails.
message SearchAds360Failure {
  // The list of errors that occurred.
  repeated SearchAds360Error errors = 1;

  // The unique ID of the request that is used for debugging purposes.
  string request_id = 2;
}

// Search Ads 360-specific error.
message SearchAds360Error {
  // An enum value that indicates which error occurred.
  ErrorCode error_code = 1;

  // A human-readable description of the error.
  string message = 2;

  // The value that triggered the error.
  google.ads.searchads360.v0.common.Value trigger = 3;

  // Describes the part of the request proto that caused the error.
  ErrorLocation location = 4;

  // Additional error details, which are returned by certain error codes. Most
  // error codes do not include details.
  ErrorDetails details = 5;
}

// The error reason represented by type and enum.
message ErrorCode {
  // The list of error enums
  oneof error_code {
    // An error caused by the request
    RequestErrorEnum.RequestError request_error = 1;

    // An error with the query
    QueryErrorEnum.QueryError query_error = 5;

    // An error encountered when trying to authorize a user.
    AuthorizationErrorEnum.AuthorizationError authorization_error = 9;

    // An unexpected server-side error.
    InternalErrorEnum.InternalError internal_error = 10;

    // An error with the amount of quota remaining.
    QuotaErrorEnum.QuotaError quota_error = 11;

    // Indicates failure to properly authenticate user.
    AuthenticationErrorEnum.AuthenticationError authentication_error = 17;

    // The reasons for the date error
    DateErrorEnum.DateError date_error = 33;

    // The reasons for the date range error
    DateRangeErrorEnum.DateRangeError date_range_error = 34;

    // The reasons for the distinct error
    DistinctErrorEnum.DistinctError distinct_error = 35;

    // The reasons for the header error.
    HeaderErrorEnum.HeaderError header_error = 66;

    // The reasons for the size limit error
    SizeLimitErrorEnum.SizeLimitError size_limit_error = 118;

    // The reasons for the custom column error
    CustomColumnErrorEnum.CustomColumnError custom_column_error = 144;

    // The reasons for invalid parameter errors.
    InvalidParameterErrorEnum.InvalidParameterError invalid_parameter_error =
        175;
  }
}

// Describes the part of the request proto that caused the error.
message ErrorLocation {
  // A part of a field path.
  message FieldPathElement {
    // The name of a field or a oneof
    string field_name = 1;

    // If field_name is a repeated field, this is the element that failed
    optional int32 index = 3;
  }

  // A field path that indicates which field was invalid in the request.
  repeated FieldPathElement field_path_elements = 2;
}

// Additional error details.
message ErrorDetails {
  // The error code that should have been returned, but wasn't. This is used
  // when the error code is not published in the client specified version.
  string unpublished_error_code = 1;

  // Details on the quota error, including the scope (account or developer), the
  // rate bucket name and the retry delay.
  QuotaErrorDetails quota_error_details = 4;
}

// Additional quota error details when there is QuotaError.
message QuotaErrorDetails {
  // Enum of possible scopes that quota buckets belong to.
  enum QuotaRateScope {
    // Unspecified enum
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Per customer account quota
    ACCOUNT = 2;

    // Per project quota
    DEVELOPER = 3;
  }

  // The rate scope of the quota limit.
  QuotaRateScope rate_scope = 1;

  // The high level description of the quota bucket.
  // Examples are "Get requests for standard access" or "Requests per account".
  string rate_name = 2;

  // Backoff period that customers should wait before sending next request.
  google.protobuf.Duration retry_delay = 3;
}
