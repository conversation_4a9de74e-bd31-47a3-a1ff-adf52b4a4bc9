// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "WebpageConditionOperatorProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing webpage condition operator.

// Container for enum describing webpage condition operator in webpage
// criterion.
message WebpageConditionOperatorEnum {
  // The webpage condition operator in webpage criterion.
  enum WebpageConditionOperator {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The argument web condition is equal to the compared web condition.
    EQUALS = 2;

    // The argument web condition is part of the compared web condition.
    CONTAINS = 3;
  }
}
