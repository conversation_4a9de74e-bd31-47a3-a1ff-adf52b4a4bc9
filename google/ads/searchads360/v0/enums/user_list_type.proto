// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "UserListTypeProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing user list type.

// The user list types.
message UserListTypeEnum {
  // Enum containing possible user list types.
  enum UserListType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // UserList represented as a collection of conversion types.
    REMARKETING = 2;

    // UserList represented as a combination of other user lists/interests.
    LOGICAL = 3;

    // UserList created in the Google Ad Manager platform.
    EXTERNAL_REMARKETING = 4;

    // UserList associated with a rule.
    RULE_BASED = 5;

    // UserList with users similar to users of another UserList.
    SIMILAR = 6;

    // UserList of first-party CRM data provided by advertiser in the form of
    // emails or other formats.
    CRM_BASED = 7;
  }
}
