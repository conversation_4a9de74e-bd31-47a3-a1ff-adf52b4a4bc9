// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AgeRangeTypeProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing age range types.

// Container for enum describing the type of demographic age ranges.
message AgeRangeTypeEnum {
  // The type of demographic age ranges (for example, between 18 and 24 years
  // old).
  enum AgeRangeType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Between 18 and 24 years old.
    AGE_RANGE_18_24 = 503001;

    // Between 25 and 34 years old.
    AGE_RANGE_25_34 = 503002;

    // Between 35 and 44 years old.
    AGE_RANGE_35_44 = 503003;

    // Between 45 and 54 years old.
    AGE_RANGE_45_54 = 503004;

    // Between 55 and 64 years old.
    AGE_RANGE_55_64 = 503005;

    // 65 years old and beyond.
    AGE_RANGE_65_UP = 503006;

    // Undetermined age range.
    AGE_RANGE_UNDETERMINED = 503999;
  }
}
