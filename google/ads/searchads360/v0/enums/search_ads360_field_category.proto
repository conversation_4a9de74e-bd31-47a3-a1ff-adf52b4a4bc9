// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SearchAds360FieldCategoryProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing SearchAds360Field categories.

// Container for enum that determines if the described artifact is a resource
// or a field, and if it is a field, when it segments search queries.
message SearchAds360FieldCategoryEnum {
  // The category of the artifact.
  enum SearchAds360FieldCategory {
    // Unspecified
    UNSPECIFIED = 0;

    // Unknown
    UNKNOWN = 1;

    // The described artifact is a resource.
    RESOURCE = 2;

    // The described artifact is a field and is an attribute of a resource.
    // Including a resource attribute field in a query may segment the query if
    // the resource to which it is attributed segments the resource found in
    // the FROM clause.
    ATTRIBUTE = 3;

    // The described artifact is a field and always segments search queries.
    SEGMENT = 5;

    // The described artifact is a field and is a metric. It never segments
    // search queries.
    METRIC = 6;
  }
}
