// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SummaryRowSettingProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing summary row setting.

// Indicates summary row setting in request parameter.
message SummaryRowSettingEnum {
  // Enum describing return summary row settings.
  enum SummaryRowSetting {
    // Not specified.
    UNSPECIFIED = 0;

    // Represent unknown values of return summary row.
    UNKNOWN = 1;

    // Do not return summary row.
    NO_SUMMARY_ROW = 2;

    // Return summary row along with results. The summary row will be returned
    // in the last batch alone (last batch will contain no results).
    SUMMARY_ROW_WITH_RESULTS = 3;

    // Return summary row only and return no results.
    SUMMARY_ROW_ONLY = 4;
  }
}
