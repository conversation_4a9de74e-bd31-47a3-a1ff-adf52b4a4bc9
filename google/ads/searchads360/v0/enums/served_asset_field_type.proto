// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ServedAssetFieldTypeProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing policy review statuses.

// Container for enum describing possible asset field types.
message ServedAssetFieldTypeEnum {
  // The possible asset field types.
  enum ServedAssetFieldType {
    // No value has been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // The asset is used in headline 1.
    HEADLINE_1 = 2;

    // The asset is used in headline 2.
    HEADLINE_2 = 3;

    // The asset is used in headline 3.
    HEADLINE_3 = 4;

    // The asset is used in description 1.
    DESCRIPTION_1 = 5;

    // The asset is used in description 2.
    DESCRIPTION_2 = 6;

    // The asset was used in a headline. Use this only if there is only one
    // headline in the ad. Otherwise, use the HEADLINE_1, HEADLINE_2 or
    // HEADLINE_3 enums
    HEADLINE = 7;

    // The asset was used as a headline in portrait image.
    HEADLINE_IN_PORTRAIT = 8;

    // The asset was used in a long headline (used in MultiAssetResponsiveAd).
    LONG_HEADLINE = 9;

    // The asset was used in a description. Use this only if there is only one
    // description in the ad. Otherwise, use the DESCRIPTION_1 or DESCRIPTION_@
    // enums
    DESCRIPTION = 10;

    // The asset was used as description in portrait image.
    DESCRIPTION_IN_PORTRAIT = 11;

    // The asset was used as business name in portrait image.
    BUSINESS_NAME_IN_PORTRAIT = 12;

    // The asset was used as business name.
    BUSINESS_NAME = 13;

    // The asset was used as a marketing image.
    MARKETING_IMAGE = 14;

    // The asset was used as a marketing image in portrait image.
    MARKETING_IMAGE_IN_PORTRAIT = 15;

    // The asset was used as a square marketing image.
    SQUARE_MARKETING_IMAGE = 16;

    // The asset was used as a portrait marketing image.
    PORTRAIT_MARKETING_IMAGE = 17;

    // The asset was used as a logo.
    LOGO = 18;

    // The asset was used as a landscape logo.
    LANDSCAPE_LOGO = 19;

    // The asset was used as a call-to-action.
    CALL_TO_ACTION = 20;

    // The asset was used as a YouTube video.
    YOU_TUBE_VIDEO = 21;

    // This asset is used as a sitelink.
    SITELINK = 22;

    // This asset is used as a call.
    CALL = 23;

    // This asset is used as a mobile app.
    MOBILE_APP = 24;

    // This asset is used as a callout.
    CALLOUT = 25;

    // This asset is used as a structured snippet.
    STRUCTURED_SNIPPET = 26;

    // This asset is used as a price.
    PRICE = 27;

    // This asset is used as a promotion.
    PROMOTION = 28;

    // This asset is used as an image.
    AD_IMAGE = 29;

    // The asset is used as a lead form.
    LEAD_FORM = 30;

    // The asset is used as a business logo.
    BUSINESS_LOGO = 31;
  }
}
