# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "enums_proto",
    srcs = glob(["*.proto"]),
    deps = [
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "enums_java_proto",
    deps = [":enums_proto"],
)

java_grpc_library(
    name = "enums_java_grpc",
    srcs = [":enums_proto"],
    deps = [":enums_java_proto"],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "enums_moved_proto",
    srcs = [":enums_proto"],
    deps = [
    ],
)

py_proto_library(
    name = "enums_py_proto",
    deps = [":enums_moved_proto"],
)

py_grpc_library(
    name = "enums_py_grpc",
    srcs = [":enums_moved_proto"],
    deps = [":enums_py_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "enums_csharp_proto",
    deps = [":enums_proto"],
)

csharp_grpc_library(
    name = "enums_csharp_grpc",
    srcs = [":enums_proto"],
    deps = [":enums_csharp_proto"],
)
