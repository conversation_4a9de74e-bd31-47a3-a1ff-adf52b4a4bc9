// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "CustomColumnRenderTypeProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing custom column render type.

// The render type of custom columns.
message CustomColumnRenderTypeEnum {
  // Enum containing the different ways a custom column can be interpreted.
  enum CustomColumnRenderType {
    // Not specified.
    UNSPECIFIED = 0;

    // Unknown.
    UNKNOWN = 1;

    // The custom column is a raw numerical value. See value_type field to
    // determine if it is an integer or a double.
    NUMBER = 2;

    // The custom column should be multiplied by 100 to retrieve the
    // percentage value.
    PERCENT = 3;

    // The custom column value is a monetary value and is in micros.
    MONEY = 4;

    // The custom column value is a string.
    STRING = 5;

    // The custom column value is a boolean.
    BOOLEAN = 6;

    // The custom column value is a date represented as an integer in YYYYMMDD
    // format.
    DATE = 7;
  }
}
