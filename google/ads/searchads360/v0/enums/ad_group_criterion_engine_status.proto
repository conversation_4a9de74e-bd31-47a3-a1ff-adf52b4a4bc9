// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdGroupCriterionEngineStatusProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing engine status for the ad_group_criterion.

// Container for enum describing possible AdGroupCriterion engine statuses.
message AdGroupCriterionEngineStatusEnum {
  // Enumerates AdGroupCriterion engine statuses.
  enum AdGroupCriterionEngineStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Deprecated. Do not use.
    AD_GROUP_CRITERION_ELIGIBLE = 2 [deprecated = true];

    // Baidu: Bid or quality too low to be displayed.
    AD_GROUP_CRITERION_INAPPROPRIATE_FOR_CAMPAIGN = 3;

    // Baidu: Bid or quality too low for mobile, but eligible to display for
    // desktop.
    AD_GROUP_CRITERION_INVALID_MOBILE_SEARCH = 4;

    // Baidu: Bid or quality too low for desktop, but eligible to display for
    // mobile.
    AD_GROUP_CRITERION_INVALID_PC_SEARCH = 5;

    // Baidu: Bid or quality too low to be displayed.
    AD_GROUP_CRITERION_INVALID_SEARCH = 6;

    // Baidu: Paused by Baidu due to low search volume.
    AD_GROUP_CRITERION_LOW_SEARCH_VOLUME = 7;

    // Baidu: Mobile URL in process to be reviewed.
    AD_GROUP_CRITERION_MOBILE_URL_UNDER_REVIEW = 8;

    // Baidu: The landing page for one device is invalid, while the landing
    // page for the other device is valid.
    AD_GROUP_CRITERION_PARTIALLY_INVALID = 9;

    // Baidu: Keyword has been created and paused by Baidu account management,
    // and is now ready for you to activate it.
    AD_GROUP_CRITERION_TO_BE_ACTIVATED = 10;

    // Baidu: In process to be reviewed by Baidu. Gemini: Criterion under
    // review.
    AD_GROUP_CRITERION_UNDER_REVIEW = 11;

    // Baidu: Criterion to be reviewed.
    AD_GROUP_CRITERION_NOT_REVIEWED = 12;

    // Deprecated. Do not use. Previously used by Gemini
    AD_GROUP_CRITERION_ON_HOLD = 13 [deprecated = true];

    // Y!J : Criterion pending review
    AD_GROUP_CRITERION_PENDING_REVIEW = 14;

    // Criterion has been paused.
    AD_GROUP_CRITERION_PAUSED = 15;

    // Criterion has been removed.
    AD_GROUP_CRITERION_REMOVED = 16;

    // Criterion has been approved.
    AD_GROUP_CRITERION_APPROVED = 17;

    // Criterion has been disapproved.
    AD_GROUP_CRITERION_DISAPPROVED = 18;

    // Criterion is active and serving.
    AD_GROUP_CRITERION_SERVING = 19;

    // Criterion has been paused since the account is paused.
    AD_GROUP_CRITERION_ACCOUNT_PAUSED = 20;
  }
}
