// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdEngineStatusProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing engine status for the ad_group_ad.

// Container for enum describing possible AdGroupAd engine statuses.
message AdGroupAdEngineStatusEnum {
  // Enumerates AdGroupAd engine statuses.
  enum AdGroupAdEngineStatus {
    // No value has been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Deprecated. Do not use.
    AD_GROUP_AD_ELIGIBLE = 2 [deprecated = true];

    // Baidu: Creative was not approved.
    AD_GROUP_AD_INAPPROPRIATE_FOR_CAMPAIGN = 3;

    // Baidu: Mobile URL in process to be reviewed.
    AD_GROUP_AD_MOBILE_URL_UNDER_REVIEW = 4;

    // Baidu: Creative is invalid on mobile device but valid on desktop.
    AD_GROUP_AD_PARTIALLY_INVALID = 5;

    // Baidu: Creative is ready for activation.
    AD_GROUP_AD_TO_BE_ACTIVATED = 6;

    // Baidu: Creative not reviewed.
    AD_GROUP_AD_NOT_REVIEWED = 7;

    // Deprecated. Do not use. Previously used by Gemini
    AD_GROUP_AD_ON_HOLD = 8 [deprecated = true];

    // Creative has been paused.
    AD_GROUP_AD_PAUSED = 9;

    // Creative has been removed.
    AD_GROUP_AD_REMOVED = 10;

    // Creative is pending review.
    AD_GROUP_AD_PENDING_REVIEW = 11;

    // Creative is under review.
    AD_GROUP_AD_UNDER_REVIEW = 12;

    // Creative has been approved.
    AD_GROUP_AD_APPROVED = 13;

    // Creative has been disapproved.
    AD_GROUP_AD_DISAPPROVED = 14;

    // Creative is serving.
    AD_GROUP_AD_SERVING = 15;

    // Creative has been paused because the account is paused.
    AD_GROUP_AD_ACCOUNT_PAUSED = 16;

    // Creative has been paused because the campaign is paused.
    AD_GROUP_AD_CAMPAIGN_PAUSED = 17;

    // Creative has been paused because the ad group is paused.
    AD_GROUP_AD_AD_GROUP_PAUSED = 18;
  }
}
