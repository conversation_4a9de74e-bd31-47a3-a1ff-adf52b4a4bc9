// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/common/criteria.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupBidModifierProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the ad group bid modifier resource.

// Represents an ad group bid modifier.
message AdGroupBidModifier {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AdGroupBidModifier"
    pattern: "customers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}"
  };

  // Immutable. The resource name of the ad group bid modifier.
  // Ad group bid modifier resource names have the form:
  //
  // `customers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupBidModifier"
    }
  ];

  // The modifier for the bid when the criterion matches. The modifier must be
  // in the range: 0.1 - 10.0. The range is 1.0 - 6.0 for PreferredContent.
  // Use 0 to opt out of a Device type.
  optional double bid_modifier = 15;

  // The criterion of this ad group bid modifier.
  //
  // Required in create operations starting in V5.
  oneof criterion {
    // Immutable. A device criterion.
    google.ads.searchads360.v0.common.DeviceInfo device = 11
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
