// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/asset_field_type.proto";
import "google/ads/searchads360/v0/enums/asset_link_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupAssetProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// AssetGroupAsset is the link between an asset and an asset group.
// Adding an AssetGroupAsset links an asset with an asset group.
message AssetGroupAsset {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AssetGroupAsset"
    pattern: "customers/{customer_id}/assetGroupAssets/{asset_group_id}~{asset_id}~{field_type}"
  };

  // Immutable. The resource name of the asset group asset.
  // Asset group asset resource name have the form:
  //
  // `customers/{customer_id}/assetGroupAssets/{asset_group_id}~{asset_id}~{field_type}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AssetGroupAsset"
    }
  ];

  // Immutable. The asset group which this asset group asset is linking.
  string asset_group = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AssetGroup"
    }
  ];

  // Immutable. The asset which this asset group asset is linking.
  string asset = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Asset"
    }
  ];

  // The description of the placement of the asset within the asset group. For
  // example: HEADLINE, YOUTUBE_VIDEO etc
  google.ads.searchads360.v0.enums.AssetFieldTypeEnum.AssetFieldType
      field_type = 4;

  // The status of the link between an asset and asset group.
  google.ads.searchads360.v0.enums.AssetLinkStatusEnum.AssetLinkStatus status =
      5;
}
