// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/conversion_custom_variable_cardinality.proto";
import "google/ads/searchads360/v0/enums/conversion_custom_variable_family.proto";
import "google/ads/searchads360/v0/enums/conversion_custom_variable_status.proto";
import "google/ads/searchads360/v0/enums/floodlight_variable_data_type.proto";
import "google/ads/searchads360/v0/enums/floodlight_variable_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ConversionCustomVariableProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Conversion Custom Variable resource.

// A conversion custom variable.
// See "About custom Floodlight metrics and dimensions in the new
// Search Ads 360" at https://support.google.com/sa360/answer/13567857
message ConversionCustomVariable {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/ConversionCustomVariable"
    pattern: "customers/{customer_id}/conversionCustomVariables/{conversion_custom_variable_id}"
  };

  // Information for Search Ads 360 Floodlight Conversion Custom Variables.
  message FloodlightConversionCustomVariableInfo {
    // Output only. Floodlight variable type defined in Search Ads 360.
    optional google.ads.searchads360.v0.enums.FloodlightVariableTypeEnum
        .FloodlightVariableType floodlight_variable_type = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Floodlight variable data type defined in Search Ads 360.
    optional google.ads.searchads360.v0.enums.FloodlightVariableDataTypeEnum
        .FloodlightVariableDataType floodlight_variable_data_type = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. The resource name of the conversion custom variable.
  // Conversion custom variable resource names have the form:
  //
  // `customers/{customer_id}/conversionCustomVariables/{conversion_custom_variable_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/ConversionCustomVariable"
    }
  ];

  // Output only. The ID of the conversion custom variable.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The name of the conversion custom variable.
  // Name should be unique. The maximum length of name is 100 characters.
  // There should not be any extra spaces before and after.
  string name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. The tag of the conversion custom variable.
  // Tag should be unique and consist of a "u" character directly followed with
  // a number less than ormequal to 100. For example: "u4".
  string tag = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The status of the conversion custom variable for conversion event accrual.
  google.ads.searchads360.v0.enums.ConversionCustomVariableStatusEnum
      .ConversionCustomVariableStatus status = 5;

  // Output only. The resource name of the customer that owns the conversion
  // custom variable.
  string owner_customer = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Customer"
    }
  ];

  // Output only. Family of the conversion custom variable.
  google.ads.searchads360.v0.enums.ConversionCustomVariableFamilyEnum
      .ConversionCustomVariableFamily family = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cardinality of the conversion custom variable.
  google.ads.searchads360.v0.enums.ConversionCustomVariableCardinalityEnum
      .ConversionCustomVariableCardinality cardinality = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Fields for Search Ads 360 floodlight conversion custom
  // variables.
  FloodlightConversionCustomVariableInfo
      floodlight_conversion_custom_variable_info = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The IDs of custom columns that use this conversion custom
  // variable.
  repeated int64 custom_column_ids = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
