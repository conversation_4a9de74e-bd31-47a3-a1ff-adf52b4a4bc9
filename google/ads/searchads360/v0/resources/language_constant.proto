// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "LanguageConstantProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the language constant resource.

// A language.
message LanguageConstant {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/LanguageConstant"
    pattern: "languageConstants/{criterion_id}"
  };

  // Output only. The resource name of the language constant.
  // Language constant resource names have the form:
  //
  // `languageConstants/{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/LanguageConstant"
    }
  ];

  // Output only. The ID of the language constant.
  optional int64 id = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The language code, for example, "en_US", "en_AU", "es", "fr",
  // etc.
  optional string code = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The full name of the language in English, for example,
  // "English (US)", "Spanish", etc.
  optional string name = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the language is targetable.
  optional bool targetable = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}
