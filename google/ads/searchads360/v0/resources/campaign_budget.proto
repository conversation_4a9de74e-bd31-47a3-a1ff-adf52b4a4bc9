// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/budget_delivery_method.proto";
import "google/ads/searchads360/v0/enums/budget_period.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignBudgetProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Budget resource.

// A campaign budget.
message CampaignBudget {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/CampaignBudget"
    pattern: "customers/{customer_id}/campaignBudgets/{budget_id}"
  };

  // Immutable. The resource name of the campaign budget.
  // Campaign budget resource names have the form:
  //
  // `customers/{customer_id}/campaignBudgets/{campaign_budget_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CampaignBudget"
    }
  ];

  // The amount of the budget, in the local currency for the account.
  // Amount is specified in micros, where one million is equivalent to one
  // currency unit. Monthly spend is capped at 30.4 times this amount.
  optional int64 amount_micros = 21;

  // The delivery method that determines the rate at which the campaign budget
  // is spent.
  //
  // Defaults to STANDARD if unspecified in a create operation.
  google.ads.searchads360.v0.enums.BudgetDeliveryMethodEnum.BudgetDeliveryMethod
      delivery_method = 7;

  // Immutable. Period over which to spend the budget. Defaults to DAILY if not
  // specified.
  google.ads.searchads360.v0.enums.BudgetPeriodEnum.BudgetPeriod period = 13
      [(google.api.field_behavior) = IMMUTABLE];
}
