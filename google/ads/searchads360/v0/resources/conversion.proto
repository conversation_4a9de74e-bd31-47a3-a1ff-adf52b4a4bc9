// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/asset_field_type.proto";
import "google/ads/searchads360/v0/enums/attribution_type.proto";
import "google/ads/searchads360/v0/enums/conversion_status.proto";
import "google/ads/searchads360/v0/enums/product_channel.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ConversionProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Conversion resource.

// A conversion.
message Conversion {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/Conversion"
    pattern: "customers/{customer_id}/conversions/{ad_group_id}~{criteria_id}~{ds_conversion_id}"
  };

  // Output only. The resource name of the conversion.
  // Conversion resource names have the form:
  //
  // `customers/{customer_id}/conversions/{ad_group_id}~{criterion_id}~{ds_conversion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Conversion"
    }
  ];

  // Output only. The ID of the conversion
  optional int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Search Ads 360 criterion ID. A value of 0 indicates that the
  // criterion is unattributed.
  optional int64 criterion_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Search Ads 360 inventory account ID containing the product
  // that was clicked on. Search Ads 360 generates this ID when you link an
  // inventory account in Search Ads 360.
  optional int64 merchant_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Ad ID. A value of 0 indicates that the ad is unattributed.
  optional int64 ad_id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A unique string, for the visit that the conversion is
  // attributed to, that is passed to the landing page as the click id URL
  // parameter.
  optional string click_id = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Search Ads 360 visit ID that the conversion is attributed
  // to.
  optional int64 visit_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For offline conversions, this is an ID provided by
  // advertisers. If an advertiser doesn't specify such an ID, Search Ads 360
  // generates one. For online conversions, this is equal to the id column or
  // the floodlight_order_id column depending on the advertiser's Floodlight
  // instructions.
  optional string advertiser_conversion_id = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the product clicked on.
  optional string product_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The sales channel of the product that was clicked on: Online
  // or Local.
  optional google.ads.searchads360.v0.enums.ProductChannelEnum.ProductChannel
      product_channel = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The language (ISO-639-1) that has been set for the Merchant
  // Center feed containing data about the product.
  optional string product_language_code = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The store in the Local Inventory Ad that was clicked on. This
  // should match the store IDs used in your local products feed.
  optional string product_store_id = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The country (ISO-3166-format) registered for the inventory
  // feed that contains the product clicked on.
  optional string product_country_code = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. What the conversion is attributed to: Visit or Keyword+Ad.
  optional google.ads.searchads360.v0.enums.AttributionTypeEnum.AttributionType
      attribution_type = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of the conversion event.
  optional string conversion_date_time = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of the last time the conversion was modified.
  optional string conversion_last_modified_date_time = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of the visit that the conversion is attributed
  // to.
  optional string conversion_visit_date_time = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The quantity of items recorded by the conversion, as
  // determined by the qty url parameter. The advertiser is responsible for
  // dynamically populating the parameter (such as number of items sold in the
  // conversion), otherwise it defaults to 1.
  optional int64 conversion_quantity = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The adjusted revenue in micros for the conversion event. This
  // will always be in the currency of the serving account.
  optional int64 conversion_revenue_micros = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The original, unchanged revenue associated with the Floodlight
  // event (in the currency of the current report), before Floodlight currency
  // instruction modifications.
  optional int64 floodlight_original_revenue = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Floodlight order ID provided by the advertiser for the
  // conversion.
  optional string floodlight_order_id = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the conversion, either ENABLED or REMOVED..
  optional
      google.ads.searchads360.v0.enums.ConversionStatusEnum.ConversionStatus
          status = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the asset which was interacted with during the
  // conversion event.
  optional int64 asset_id = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Asset field type of the conversion event.
  optional google.ads.searchads360.v0.enums.AssetFieldTypeEnum.AssetFieldType
      asset_field_type = 24 [(google.api.field_behavior) = OUTPUT_ONLY];
}
