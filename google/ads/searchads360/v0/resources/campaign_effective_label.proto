// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignEffectiveLabelProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the campaign effective label resource.

// Represents a relationship between a campaign and an effective label.
// An effective label is a label inherited or directly assigned to this
// campaign.
message CampaignEffectiveLabel {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/CampaignEffectiveLabel"
    pattern: "customers/{customer_id}/campaignEffectiveLabels/{campaign_id}~{label_id}"
    plural: "campaignEffectiveLabels"
    singular: "campaignEffectiveLabel"
  };

  // Immutable. Name of the resource.
  // CampaignEffectivelabel resource names have the form:
  // `customers/{customer_id}/campaignEffectiveLabels/{campaign_id}~{label_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CampaignEffectiveLabel"
    }
  ];

  // Immutable. The campaign to which the effective label is attached.
  optional string campaign = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Campaign"
    }
  ];

  // Immutable. The effective label assigned to the campaign.
  optional string label = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Label"
    }
  ];

  // Output only. The ID of the Customer which owns the effective label.
  optional int64 owner_customer_id = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
