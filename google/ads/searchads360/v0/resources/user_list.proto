// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/user_list_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "UserListProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the User List resource.

// A user list. This is a list of users a customer may target.
message UserList {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/UserList"
    pattern: "customers/{customer_id}/userLists/{user_list_id}"
  };

  // Immutable. The resource name of the user list.
  // User list resource names have the form:
  //
  // `customers/{customer_id}/userLists/{user_list_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/UserList"
    }
  ];

  // Output only. Id of the user list.
  optional int64 id = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Name of this user list. Depending on its access_reason, the user list name
  // may not be unique (for example, if access_reason=SHARED)
  optional string name = 27;

  // Output only. Type of this list.
  //
  // This field is read-only.
  google.ads.searchads360.v0.enums.UserListTypeEnum.UserListType type = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
