// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/product_bidding_category_level.proto";
import "google/ads/searchads360/v0/enums/product_bidding_category_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ProductBiddingCategoryConstantProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the ProductBiddingCategoryConstant resource.

// A Product Bidding Category.
message ProductBiddingCategoryConstant {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/ProductBiddingCategoryConstant"
    pattern: "productBiddingCategoryConstants/{country_code}~{level}~{canonical_value}"
  };

  // Output only. The resource name of the product bidding category.
  // Product bidding category resource names have the form:
  //
  // `productBiddingCategoryConstants/{country_code}~{level}~{id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/ProductBiddingCategoryConstant"
    }
  ];

  // Output only. ID of the product bidding category.
  //
  // This ID is equivalent to the google_product_category ID as described in
  // this article: https://support.google.com/merchants/answer/6324436.
  optional int64 id = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Two-letter upper-case country code of the product bidding
  // category.
  optional string country_code = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource name of the parent product bidding category.
  optional string product_bidding_category_constant_parent = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/ProductBiddingCategoryConstant"
    }
  ];

  // Output only. Level of the product bidding category.
  google.ads.searchads360.v0.enums.ProductBiddingCategoryLevelEnum
      .ProductBiddingCategoryLevel level = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of the product bidding category.
  google.ads.searchads360.v0.enums.ProductBiddingCategoryStatusEnum
      .ProductBiddingCategoryStatus status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Language code of the product bidding category.
  optional string language_code = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Display value of the product bidding category localized
  // according to language_code.
  optional string localized_name = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
