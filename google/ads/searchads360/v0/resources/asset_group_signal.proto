// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/common/criteria.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupSignalProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// AssetGroupSignal represents a signal in an asset group. The existence of a
// signal tells the performance max campaign who's most likely to convert.
// Performance Max uses the signal to look for new people with similar or
// stronger intent to find conversions across Search, Display, Video, and more.
message AssetGroupSignal {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AssetGroupSignal"
    pattern: "customers/{customer_id}/assetGroupSignals/{asset_group_id}~{criterion_id}"
  };

  // Immutable. The resource name of the asset group signal.
  // Asset group signal resource name have the form:
  //
  // `customers/{customer_id}/assetGroupSignals/{asset_group_id}~{signal_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AssetGroupSignal"
    }
  ];

  // Immutable. The asset group which this asset group signal belongs to.
  string asset_group = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AssetGroup"
    }
  ];

  // The signal of the asset group.
  oneof signal {
    // Immutable. The audience signal to be used by the performance max
    // campaign.
    google.ads.searchads360.v0.common.AudienceInfo audience = 4
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
