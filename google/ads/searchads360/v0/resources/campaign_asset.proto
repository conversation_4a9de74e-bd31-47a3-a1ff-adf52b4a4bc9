// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/asset_link_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignAssetProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the CampaignAsset resource.

// A link between a Campaign and an Asset.
message CampaignAsset {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/CampaignAsset"
    pattern: "customers/{customer_id}/campaignAssets/{campaign_id}~{asset_id}~{field_type}"
  };

  // Immutable. The resource name of the campaign asset.
  // CampaignAsset resource names have the form:
  //
  // `customers/{customer_id}/campaignAssets/{campaign_id}~{asset_id}~{field_type}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CampaignAsset"
    }
  ];

  // Immutable. The campaign to which the asset is linked.
  optional string campaign = 6 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Campaign"
    }
  ];

  // Immutable. The asset which is linked to the campaign.
  optional string asset = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Asset"
    }
  ];

  // Output only. Status of the campaign asset.
  google.ads.searchads360.v0.enums.AssetLinkStatusEnum.AssetLinkStatus status =
      5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
