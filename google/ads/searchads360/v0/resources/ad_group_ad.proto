// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/ad_group_ad_engine_status.proto";
import "google/ads/searchads360/v0/enums/ad_group_ad_status.proto";
import "google/ads/searchads360/v0/resources/ad.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the ad group ad resource.

// An ad group ad.
message AdGroupAd {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AdGroupAd"
    pattern: "customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}"
  };

  // Immutable. The resource name of the ad.
  // Ad group ad resource names have the form:
  //
  // `customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupAd"
    }
  ];

  // The status of the ad.
  google.ads.searchads360.v0.enums.AdGroupAdStatusEnum.AdGroupAdStatus status =
      3;

  // Immutable. The ad.
  Ad ad = 5 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The timestamp when this ad_group_ad was created. The datetime
  // is in the customer's time zone and in "yyyy-MM-dd HH:mm:ss.ssssss" format.
  string creation_time = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource names of labels attached to this ad group ad.
  repeated string labels = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupAdLabel"
    }
  ];

  // Output only. The resource names of effective labels attached to this ad.
  // An effective label is a label inherited or directly assigned to this ad.
  repeated string effective_labels = 19 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupAdEffectiveLabel"
    }
  ];

  // Output only. ID of the ad in the external engine account. This field is for
  // Search Ads 360 account only, for example, Yahoo Japan, Microsoft, Baidu
  // etc. For non-Search Ads 360 entity, use "ad_group_ad.ad.id" instead.
  string engine_id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional status of the ad in the external engine account.
  // Possible statuses (depending on the type of external account) include
  // active, eligible, pending review, etc.
  google.ads.searchads360.v0.enums.AdGroupAdEngineStatusEnum
      .AdGroupAdEngineStatus engine_status = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The datetime when this ad group ad was last modified. The
  // datetime is in the customer's time zone and in "yyyy-MM-dd HH:mm:ss.ssssss"
  // format.
  string last_modified_time = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
