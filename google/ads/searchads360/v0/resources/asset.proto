// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/common/asset_types.proto";
import "google/ads/searchads360/v0/enums/asset_engine_status.proto";
import "google/ads/searchads360/v0/enums/asset_status.proto";
import "google/ads/searchads360/v0/enums/asset_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the asset resource.

// Asset is a part of an ad which can be shared across multiple ads.
// It can be an image (ImageAsset), a video (YoutubeVideoAsset), etc.
// Assets are immutable and cannot be removed. To stop an asset from serving,
// remove the asset from the entity that is using it.
message Asset {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/Asset"
    pattern: "customers/{customer_id}/assets/{asset_id}"
  };

  // Immutable. The resource name of the asset.
  // Asset resource names have the form:
  //
  // `customers/{customer_id}/assets/{asset_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Asset"
    }
  ];

  // Output only. The ID of the asset.
  optional int64 id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional name of the asset.
  optional string name = 12;

  // Output only. Type of the asset.
  google.ads.searchads360.v0.enums.AssetTypeEnum.AssetType type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // A list of possible final URLs after all cross domain redirects.
  repeated string final_urls = 14;

  // URL template for constructing a tracking URL.
  optional string tracking_url_template = 17;

  // Output only. The status of the asset.
  google.ads.searchads360.v0.enums.AssetStatusEnum.AssetStatus status = 42
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this asset was created. The timestamp is in
  // the customer's time zone and in "yyyy-MM-dd HH:mm:ss" format.
  string creation_time = 43 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The datetime when this asset was last modified. The datetime
  // is in the customer's time zone and in "yyyy-MM-dd HH:mm:ss.ssssss" format.
  string last_modified_time = 44 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Engine Status for an asset.
  optional
      google.ads.searchads360.v0.enums.AssetEngineStatusEnum.AssetEngineStatus
          engine_status = 61 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The specific type of the asset.
  oneof asset_data {
    // Immutable. A YouTube video asset.
    google.ads.searchads360.v0.common.YoutubeVideoAsset youtube_video_asset = 5
        [(google.api.field_behavior) = IMMUTABLE];

    // Output only. An image asset.
    google.ads.searchads360.v0.common.ImageAsset image_asset = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A text asset.
    google.ads.searchads360.v0.common.TextAsset text_asset = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A unified callout asset.
    google.ads.searchads360.v0.common.UnifiedCalloutAsset callout_asset = 48
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A unified sitelink asset.
    google.ads.searchads360.v0.common.UnifiedSitelinkAsset sitelink_asset = 45
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A unified page feed asset.
    google.ads.searchads360.v0.common.UnifiedPageFeedAsset page_feed_asset = 46
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // A mobile app asset.
    google.ads.searchads360.v0.common.MobileAppAsset mobile_app_asset = 25;

    // Output only. A unified call asset.
    google.ads.searchads360.v0.common.UnifiedCallAsset call_asset = 47
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. A call to action asset.
    google.ads.searchads360.v0.common.CallToActionAsset call_to_action_asset =
        29 [(google.api.field_behavior) = IMMUTABLE];

    // Output only. A unified location asset.
    google.ads.searchads360.v0.common.UnifiedLocationAsset location_asset = 49
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
