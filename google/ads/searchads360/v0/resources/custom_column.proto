// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/custom_column_render_type.proto";
import "google/ads/searchads360/v0/enums/custom_column_value_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomColumnProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Custom Column resource.

// A custom column.
// See Search Ads 360 custom column at
// https://support.google.com/sa360/answer/9633916
message CustomColumn {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/CustomColumn"
    pattern: "customers/{customer_id}/customColumns/{custom_column_id}"
  };

  // Immutable. The resource name of the custom column.
  // Custom column resource names have the form:
  //
  // `customers/{customer_id}/customColumns/{custom_column_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CustomColumn"
    }
  ];

  // Output only. ID of the custom column.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. User-defined name of the custom column.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. User-defined description of the custom column.
  string description = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the result value of the custom column.
  google.ads.searchads360.v0.enums.CustomColumnValueTypeEnum
      .CustomColumnValueType value_type = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True when the custom column is referring to one or more
  // attributes.
  bool references_attributes = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True when the custom column is referring to one or more
  // metrics.
  bool references_metrics = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True when the custom column is available to be used in the
  // query of SearchAds360Service.Search and SearchAds360Service.SearchStream.
  bool queryable = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of the referenced system columns of this custom
  // column. For example, A custom column "sum of impressions and clicks" has
  // referenced system columns of {"metrics.clicks", "metrics.impressions"}.
  repeated string referenced_system_columns = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. How the result value of the custom column should be
  // interpreted.
  google.ads.searchads360.v0.enums.CustomColumnRenderTypeEnum
      .CustomColumnRenderType render_type = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
