// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/common/bidding.proto";
import "google/ads/searchads360/v0/common/custom_parameter.proto";
import "google/ads/searchads360/v0/common/frequency_cap.proto";
import "google/ads/searchads360/v0/common/real_time_bidding_setting.proto";
import "google/ads/searchads360/v0/enums/ad_serving_optimization_status.proto";
import "google/ads/searchads360/v0/enums/advertising_channel_sub_type.proto";
import "google/ads/searchads360/v0/enums/advertising_channel_type.proto";
import "google/ads/searchads360/v0/enums/asset_field_type.proto";
import "google/ads/searchads360/v0/enums/bidding_strategy_system_status.proto";
import "google/ads/searchads360/v0/enums/bidding_strategy_type.proto";
import "google/ads/searchads360/v0/enums/campaign_serving_status.proto";
import "google/ads/searchads360/v0/enums/campaign_status.proto";
import "google/ads/searchads360/v0/enums/negative_geo_target_type.proto";
import "google/ads/searchads360/v0/enums/optimization_goal_type.proto";
import "google/ads/searchads360/v0/enums/positive_geo_target_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Campaign resource.

// A campaign.
message Campaign {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/Campaign"
    pattern: "customers/{customer_id}/campaigns/{campaign_id}"
  };

  // The network settings for the campaign.
  message NetworkSettings {
    // Whether ads will be served with google.com search results.
    optional bool target_google_search = 5;

    // Whether ads will be served on partner sites in the Google Search Network
    // (requires `target_google_search` to also be `true`).
    optional bool target_search_network = 6;

    // Whether ads will be served on specified placements in the Google Display
    // Network. Placements are specified using the Placement criterion.
    optional bool target_content_network = 7;

    // Whether ads will be served on the Google Partner Network.
    // This is available only to some select Google partner accounts.
    optional bool target_partner_search_network = 8;
  }

  // The setting for controlling Dynamic Search Ads (DSA).
  message DynamicSearchAdsSetting {
    // Required. The Internet domain name that this setting represents, for
    // example, "google.com" or "www.google.com".
    string domain_name = 6 [(google.api.field_behavior) = REQUIRED];

    // Required. The language code specifying the language of the domain, for
    // example, "en".
    string language_code = 7 [(google.api.field_behavior) = REQUIRED];

    // Whether the campaign uses advertiser supplied URLs exclusively.
    optional bool use_supplied_urls_only = 8;
  }

  // The setting for Shopping campaigns. Defines the universe of products that
  // can be advertised by the campaign, and how this campaign interacts with
  // other Shopping campaigns.
  message ShoppingSetting {
    // Immutable. ID of the Merchant Center account.
    // This field is required for create operations. This field is immutable for
    // Shopping campaigns.
    optional int64 merchant_id = 5 [(google.api.field_behavior) = IMMUTABLE];

    // Sales country of products to include in the campaign.
    //
    optional string sales_country = 6;

    // Feed label of products to include in the campaign.
    // Only one of feed_label or sales_country can be set.
    // If used instead of sales_country, the feed_label field accepts country
    // codes in the same format for example: 'XX'.
    // Otherwise can be any string used for feed label in Google Merchant
    // Center.
    string feed_label = 10;

    // Priority of the campaign. Campaigns with numerically higher priorities
    // take precedence over those with lower priorities.
    // This field is required for Shopping campaigns, with values between 0 and
    // 2, inclusive.
    // This field is optional for Smart Shopping campaigns, but must be equal to
    // 3 if set.
    optional int32 campaign_priority = 7;

    // Whether to include local products.
    optional bool enable_local = 8;

    // Immutable. Whether to target Vehicle Listing inventory.
    bool use_vehicle_inventory = 9 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Campaign-level settings for tracking information.
  message TrackingSetting {
    // Output only. The url used for dynamic tracking.
    optional string tracking_url = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Represents a collection of settings related to ads geotargeting.
  message GeoTargetTypeSetting {
    // The setting used for positive geotargeting in this particular campaign.
    google.ads.searchads360.v0.enums.PositiveGeoTargetTypeEnum
        .PositiveGeoTargetType positive_geo_target_type = 1;

    // The setting used for negative geotargeting in this particular campaign.
    google.ads.searchads360.v0.enums.NegativeGeoTargetTypeEnum
        .NegativeGeoTargetType negative_geo_target_type = 2;
  }

  // Selective optimization setting for this campaign, which includes a set of
  // conversion actions to optimize this campaign towards.
  // This feature only applies to app campaigns that use MULTI_CHANNEL as
  // AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as
  // AdvertisingChannelSubType.
  message SelectiveOptimization {
    // The selected set of resource names for conversion actions for optimizing
    // this campaign.
    repeated string conversion_actions = 2 [(google.api.resource_reference) = {
      type: "searchads360.googleapis.com/ConversionAction"
    }];
  }

  // Optimization goal setting for this campaign, which includes a set of
  // optimization goal types.
  message OptimizationGoalSetting {
    // The list of optimization goal types.
    repeated google.ads.searchads360.v0.enums.OptimizationGoalTypeEnum
        .OptimizationGoalType optimization_goal_types = 1;
  }

  // Immutable. The resource name of the campaign.
  // Campaign resource names have the form:
  //
  // `customers/{customer_id}/campaigns/{campaign_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Campaign"
    }
  ];

  // Output only. The ID of the campaign.
  optional int64 id = 59 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the campaign.
  //
  // This field is required and should not be empty when creating new
  // campaigns.
  //
  // It must not contain any null (code point 0x0), NL line feed
  // (code point 0xA) or carriage return (code point 0xD) characters.
  optional string name = 58;

  // The status of the campaign.
  //
  // When a new campaign is added, the status defaults to ENABLED.
  google.ads.searchads360.v0.enums.CampaignStatusEnum.CampaignStatus status = 5;

  // Output only. The ad serving status of the campaign.
  google.ads.searchads360.v0.enums.CampaignServingStatusEnum
      .CampaignServingStatus serving_status = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The system status of the campaign's bidding strategy.
  google.ads.searchads360.v0.enums.BiddingStrategySystemStatusEnum
      .BiddingStrategySystemStatus bidding_strategy_system_status = 78
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The ad serving optimization status of the campaign.
  google.ads.searchads360.v0.enums.AdServingOptimizationStatusEnum
      .AdServingOptimizationStatus ad_serving_optimization_status = 8;

  // Immutable. The primary serving target for ads within the campaign.
  // The targeting options can be refined in `network_settings`.
  //
  // This field is required and should not be empty when creating new
  // campaigns.
  //
  // Can be set only when creating campaigns.
  // After the campaign is created, the field can not be changed.
  google.ads.searchads360.v0.enums.AdvertisingChannelTypeEnum
      .AdvertisingChannelType advertising_channel_type = 9
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Optional refinement to `advertising_channel_type`.
  // Must be a valid sub-type of the parent channel type.
  //
  // Can be set only when creating campaigns.
  // After campaign is created, the field can not be changed.
  google.ads.searchads360.v0.enums.AdvertisingChannelSubTypeEnum
      .AdvertisingChannelSubType advertising_channel_sub_type = 10
      [(google.api.field_behavior) = IMMUTABLE];

  // The URL template for constructing a tracking URL.
  optional string tracking_url_template = 60;

  // The list of mappings used to substitute custom parameter tags in a
  // `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
  repeated google.ads.searchads360.v0.common.CustomParameter
      url_custom_parameters = 12;

  // Settings for Real-Time Bidding, a feature only available for campaigns
  // targeting the Ad Exchange network.
  google.ads.searchads360.v0.common.RealTimeBiddingSetting
      real_time_bidding_setting = 39;

  // The network settings for the campaign.
  NetworkSettings network_settings = 14;

  // The setting for controlling Dynamic Search Ads (DSA).
  DynamicSearchAdsSetting dynamic_search_ads_setting = 33;

  // The setting for controlling Shopping campaigns.
  ShoppingSetting shopping_setting = 36;

  // The setting for ads geotargeting.
  GeoTargetTypeSetting geo_target_type_setting = 47;

  // Output only. The resource names of effective labels attached to this
  // campaign. An effective label is a label inherited or directly assigned to
  // this campaign.
  repeated string effective_labels = 95 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CampaignEffectiveLabel"
    }
  ];

  // Output only. The resource names of labels attached to this campaign.
  repeated string labels = 61 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CampaignLabel"
    }
  ];

  // The resource name of the campaign budget of the campaign.
  optional string campaign_budget = 62 [(google.api.resource_reference) = {
    type: "searchads360.googleapis.com/CampaignBudget"
  }];

  // Output only. The type of bidding strategy.
  //
  // A bidding strategy can be created by setting either the bidding scheme to
  // create a standard bidding strategy or the `bidding_strategy` field to
  // create a portfolio bidding strategy.
  //
  // This field is read-only.
  google.ads.searchads360.v0.enums.BiddingStrategyTypeEnum.BiddingStrategyType
      bidding_strategy_type = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource name of AccessibleBiddingStrategy, a read-only view
  // of the unrestricted attributes of the attached portfolio bidding strategy
  // identified by 'bidding_strategy'. Empty, if the campaign does not use a
  // portfolio strategy. Unrestricted strategy attributes are available to all
  // customers with whom the strategy is shared and are read from the
  // AccessibleBiddingStrategy resource. In contrast, restricted attributes are
  // only available to the owner customer of the strategy and their managers.
  // Restricted attributes can only be read from the BiddingStrategy resource.
  string accessible_bidding_strategy = 71 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AccessibleBiddingStrategy"
    }
  ];

  // The date when campaign started in serving customer's timezone in YYYY-MM-DD
  // format.
  optional string start_date = 63;

  // The last day of the campaign in serving customer's timezone in YYYY-MM-DD
  // format. On create, defaults to 2037-12-30, which means the campaign will
  // run indefinitely. To set an existing campaign to run indefinitely, set this
  // field to 2037-12-30.
  optional string end_date = 64;

  // Suffix used to append query parameters to landing pages that are served
  // with parallel tracking.
  optional string final_url_suffix = 65;

  // A list that limits how often each user will see this campaign's ads.
  repeated google.ads.searchads360.v0.common.FrequencyCapEntry frequency_caps =
      40;

  // Selective optimization setting for this campaign, which includes a set of
  // conversion actions to optimize this campaign towards.
  // This feature only applies to app campaigns that use MULTI_CHANNEL as
  // AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as
  // AdvertisingChannelSubType.
  SelectiveOptimization selective_optimization = 45;

  // Optimization goal setting for this campaign, which includes a set of
  // optimization goal types.
  OptimizationGoalSetting optimization_goal_setting = 54;

  // Output only. Campaign-level settings for tracking information.
  TrackingSetting tracking_setting = 46
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the campaign in the external engine account. This field
  // is for non-Google Ads account only, for example, Yahoo Japan, Microsoft,
  // Baidu etc. For Google Ads entity, use "campaign.id" instead.
  string engine_id = 68 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The asset field types that should be excluded from this campaign. Asset
  // links with these field types will not be inherited by this campaign from
  // the upper level.
  repeated google.ads.searchads360.v0.enums.AssetFieldTypeEnum.AssetFieldType
      excluded_parent_asset_field_types = 69;

  // Output only. The timestamp when this campaign was created. The timestamp is
  // in the customer's time zone and in "yyyy-MM-dd HH:mm:ss" format.
  // create_time will be deprecated in v1. Use creation_time instead.
  string create_time = 79 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this campaign was created. The timestamp is
  // in the customer's time zone and in "yyyy-MM-dd HH:mm:ss" format.
  string creation_time = 84 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The datetime when this campaign was last modified. The
  // datetime is in the customer's time zone and in "yyyy-MM-dd HH:mm:ss.ssssss"
  // format.
  string last_modified_time = 70 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Represents opting out of URL expansion to more targeted URLs. If opted out
  // (true), only the final URLs in the asset group or URLs specified in the
  // advertiser's Google Merchant Center or business data feeds are targeted.
  // If opted in (false), the entire domain will be targeted. This field can
  // only be set for Performance Max campaigns, where the default value is
  // false.
  optional bool url_expansion_opt_out = 72;

  // The bidding strategy for the campaign.
  //
  // Must be either portfolio (created through BiddingStrategy service) or
  // standard, that is embedded into the campaign.
  oneof campaign_bidding_strategy {
    // The resource name of the portfolio bidding strategy used by the campaign.
    string bidding_strategy = 67 [(google.api.resource_reference) = {
      type: "searchads360.googleapis.com/BiddingStrategy"
    }];

    // Standard Manual CPA bidding strategy.
    // Manual bidding strategy that allows advertiser to set the bid per
    // advertiser-specified action. Supported only for Local Services campaigns.
    google.ads.searchads360.v0.common.ManualCpa manual_cpa = 74;

    // Standard Manual CPC bidding strategy.
    // Manual click-based bidding where user pays per click.
    google.ads.searchads360.v0.common.ManualCpc manual_cpc = 24;

    // Standard Manual CPM bidding strategy.
    // Manual impression-based bidding where user pays per thousand
    // impressions.
    google.ads.searchads360.v0.common.ManualCpm manual_cpm = 25;

    // Standard Maximize Conversions bidding strategy that automatically
    // maximizes number of conversions while spending your budget.
    google.ads.searchads360.v0.common.MaximizeConversions maximize_conversions =
        30;

    // Standard Maximize Conversion Value bidding strategy that automatically
    // sets bids to maximize revenue while spending your budget.
    google.ads.searchads360.v0.common.MaximizeConversionValue
        maximize_conversion_value = 31;

    // Standard Target CPA bidding strategy that automatically sets bids to
    // help get as many conversions as possible at the target
    // cost-per-acquisition (CPA) you set.
    google.ads.searchads360.v0.common.TargetCpa target_cpa = 26;

    // Target Impression Share bidding strategy. An automated bidding strategy
    // that sets bids to achieve a chosen percentage of impressions.
    google.ads.searchads360.v0.common.TargetImpressionShare
        target_impression_share = 48;

    // Standard Target ROAS bidding strategy that automatically maximizes
    // revenue while averaging a specific target return on ad spend (ROAS).
    google.ads.searchads360.v0.common.TargetRoas target_roas = 29;

    // Standard Target Spend bidding strategy that automatically sets your bids
    // to help get as many clicks as possible within your budget.
    google.ads.searchads360.v0.common.TargetSpend target_spend = 27;

    // Standard Percent Cpc bidding strategy where bids are a fraction of the
    // advertised price for some good or service.
    google.ads.searchads360.v0.common.PercentCpc percent_cpc = 34;

    // A bidding strategy that automatically optimizes cost per thousand
    // impressions.
    google.ads.searchads360.v0.common.TargetCpm target_cpm = 41;
  }
}
