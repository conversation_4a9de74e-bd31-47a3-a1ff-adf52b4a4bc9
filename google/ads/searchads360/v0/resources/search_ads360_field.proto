// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/search_ads360_field_category.proto";
import "google/ads/searchads360/v0/enums/search_ads360_field_data_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "SearchAds360FieldProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Search Ads 360 Field resource.

// A field or resource (artifact) used by SearchAds360Service.
message SearchAds360Field {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/SearchAds360Field"
    pattern: "searchAds360Fields/{search_ads_360_field}"
  };

  // Output only. The resource name of the artifact.
  // Artifact resource names have the form:
  //
  // `SearchAds360Fields/{name}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/SearchAds360Field"
    }
  ];

  // Output only. The name of the artifact.
  optional string name = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The category of the artifact.
  google.ads.searchads360.v0.enums.SearchAds360FieldCategoryEnum
      .SearchAds360FieldCategory category = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the artifact can be used in a SELECT clause in search
  // queries.
  optional bool selectable = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the artifact can be used in a WHERE clause in search
  // queries.
  optional bool filterable = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the artifact can be used in a ORDER BY clause in
  // search queries.
  optional bool sortable = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of all resources, segments, and metrics that are
  // selectable with the described artifact.
  repeated string selectable_with = 25
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of all resources that are selectable with the
  // described artifact. Fields from these resources do not segment metrics when
  // included in search queries.
  //
  // This field is only set for artifacts whose category is RESOURCE.
  repeated string attribute_resources = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field lists the names of all metrics that are selectable
  // with the described artifact when it is used in the FROM clause. It is only
  // set for artifacts whose category is RESOURCE.
  repeated string metrics = 27 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field lists the names of all artifacts, whether a segment
  // or another resource, that segment metrics when included in search queries
  // and when the described artifact is used in the FROM clause. It is only set
  // for artifacts whose category is RESOURCE.
  repeated string segments = 28 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Values the artifact can assume if it is a field of type ENUM.
  //
  // This field is only set for artifacts of category SEGMENT or ATTRIBUTE.
  repeated string enum_values = 29 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field determines the operators that can be used with the
  // artifact in WHERE clauses.
  google.ads.searchads360.v0.enums.SearchAds360FieldDataTypeEnum
      .SearchAds360FieldDataType data_type = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of proto describing the artifact's data type.
  optional string type_url = 30 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the field artifact is repeated.
  optional bool is_repeated = 31 [(google.api.field_behavior) = OUTPUT_ONLY];
}
