// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/common/ad_type_infos.proto";
import "google/ads/searchads360/v0/enums/ad_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the ad type.

// An ad.
message Ad {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/Ad"
    pattern: "customers/{customer_id}/ads/{ad_id}"
  };

  // Immutable. The resource name of the ad.
  // Ad resource names have the form:
  //
  // `customers/{customer_id}/ads/{ad_id}`
  string resource_name = 37 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "searchads360.googleapis.com/Ad" }
  ];

  // Output only. The ID of the ad.
  optional int64 id = 40 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The list of possible final URLs after all cross-domain redirects for the
  // ad.
  repeated string final_urls = 41;

  // The URL that appears in the ad description for some ad formats.
  optional string display_url = 45;

  // Output only. The type of ad.
  google.ads.searchads360.v0.enums.AdTypeEnum.AdType type = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The name of the ad. This is only used to be able to identify the
  // ad. It does not need to be unique and does not affect the served ad. The
  // name field is currently only supported for DisplayUploadAd, ImageAd,
  // ShoppingComparisonListingAd and VideoAd.
  optional string name = 47 [(google.api.field_behavior) = IMMUTABLE];

  // Details pertinent to the ad type. Exactly one value must be set.
  oneof ad_data {
    // Immutable. Details pertaining to a text ad.
    google.ads.searchads360.v0.common.SearchAds360TextAdInfo text_ad = 55
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Details pertaining to an expanded text ad.
    google.ads.searchads360.v0.common.SearchAds360ExpandedTextAdInfo
        expanded_text_ad = 56 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Details pertaining to a responsive search ad.
    google.ads.searchads360.v0.common.SearchAds360ResponsiveSearchAdInfo
        responsive_search_ad = 57 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Details pertaining to a product ad.
    google.ads.searchads360.v0.common.SearchAds360ProductAdInfo product_ad = 58
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Details pertaining to an expanded dynamic search ad.
    google.ads.searchads360.v0.common.SearchAds360ExpandedDynamicSearchAdInfo
        expanded_dynamic_search_ad = 59
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
