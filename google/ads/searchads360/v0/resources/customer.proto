// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/account_status.proto";
import "google/ads/searchads360/v0/enums/account_type.proto";
import "google/ads/searchads360/v0/enums/conversion_tracking_status_enum.proto";
import "google/ads/searchads360/v0/enums/customer_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Customer resource.

// A customer.
message Customer {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/Customer"
    pattern: "customers/{customer_id}"
  };

  // Immutable. The resource name of the customer.
  // Customer resource names have the form:
  //
  // `customers/{customer_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Customer"
    }
  ];

  // Output only. The ID of the customer.
  optional int64 id = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional, non-unique descriptive name of the customer.
  optional string descriptive_name = 20;

  // Immutable. The currency in which the account operates.
  // A subset of the currency codes from the ISO 4217 standard is
  // supported.
  optional string currency_code = 21 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The local timezone ID of the customer.
  optional string time_zone = 22 [(google.api.field_behavior) = IMMUTABLE];

  // The URL template for constructing a tracking URL out of parameters.
  optional string tracking_url_template = 23;

  // The URL template for appending params to the final URL.
  optional string final_url_suffix = 24;

  // Whether auto-tagging is enabled for the customer.
  optional bool auto_tagging_enabled = 25;

  // Output only. Whether the customer is a manager.
  optional bool manager = 27 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Conversion tracking setting for a customer.
  ConversionTrackingSetting conversion_tracking_setting = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Engine account type, for example, Google Ads, Microsoft
  // Advertising, Yahoo Japan, Baidu, Facebook, Engine Track, etc.
  google.ads.searchads360.v0.enums.AccountTypeEnum.AccountType account_type = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. DoubleClick Campaign Manager (DCM) setting for a manager
  // customer.
  DoubleClickCampaignManagerSetting double_click_campaign_manager_setting = 32
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Account status, for example, Enabled, Paused, Removed, etc.
  google.ads.searchads360.v0.enums.AccountStatusEnum.AccountStatus
      account_status = 33 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The datetime when this customer was last modified. The
  // datetime is in the customer's time zone and in "yyyy-MM-dd HH:mm:ss.ssssss"
  // format.
  string last_modified_time = 34 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the account in the external engine account.
  string engine_id = 35 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the customer.
  google.ads.searchads360.v0.enums.CustomerStatusEnum.CustomerStatus status = 36
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this customer was created. The timestamp is
  // in the customer's time zone and in "yyyy-MM-dd HH:mm:ss" format.
  string creation_time = 42 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A collection of customer-wide settings related to Search Ads 360 Conversion
// Tracking.
message ConversionTrackingSetting {
  // Output only. The conversion tracking id used for this account. This id
  // doesn't indicate whether the customer uses conversion tracking
  // (conversion_tracking_status does). This field is read-only.
  optional int64 conversion_tracking_id = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The conversion tracking id of the customer's manager. This is
  // set when the customer is opted into  conversion tracking, and it overrides
  // conversion_tracking_id. This field can only be managed through the Google
  // Ads UI. This field is read-only.
  optional int64 google_ads_cross_account_conversion_tracking_id = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The conversion tracking id of the customer's manager. This is
  // set when the customer is opted into cross-account conversion tracking, and
  // it overrides conversion_tracking_id.
  optional int64 cross_account_conversion_tracking_id = 37
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the customer has accepted customer data terms. If
  // using cross-account conversion tracking, this value is inherited from the
  // manager. This field is read-only. For more
  // information, see https://support.google.com/adspolicy/answer/7475709.
  bool accepted_customer_data_terms = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Conversion tracking status. It indicates whether the customer
  // is using conversion tracking, and who is the conversion tracking owner of
  // this customer. If this customer is using cross-account conversion tracking,
  // the value returned will differ based on the `login-customer-id` of the
  // request.
  google.ads.searchads360.v0.enums.ConversionTrackingStatusEnum
      .ConversionTrackingStatus conversion_tracking_status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the customer is opted-in for enhanced conversions
  // for leads. If using cross-account conversion tracking, this value is
  // inherited from the manager. This field is read-only.
  bool enhanced_conversions_for_leads_enabled = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the customer where conversions are
  // created and managed. This field is read-only.
  string google_ads_conversion_customer = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// DoubleClick Campaign Manager (DCM) setting for a manager customer.
message DoubleClickCampaignManagerSetting {
  // Output only. ID of the Campaign Manager advertiser associated with this
  // customer.
  int64 advertiser_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the Campaign Manager network associated with this
  // customer.
  int64 network_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time zone of the Campaign Manager network associated with this
  // customer in IANA Time Zone Database format, such as America/New_York.
  string time_zone = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
