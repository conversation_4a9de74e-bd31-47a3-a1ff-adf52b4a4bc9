// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/common/criteria.proto";
import "google/ads/searchads360/v0/enums/ad_group_criterion_engine_status.proto";
import "google/ads/searchads360/v0/enums/ad_group_criterion_status.proto";
import "google/ads/searchads360/v0/enums/criterion_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupCriterionProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the ad group criterion resource.

// An ad group criterion.
// The ad_group_criterion report only returns criteria that were explicitly
// added to the ad group.
message AdGroupCriterion {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AdGroupCriterion"
    pattern: "customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}"
  };

  // A container for ad group criterion quality information.
  message QualityInfo {
    // Output only. The quality score.
    //
    // This field may not be populated if Google does not have enough
    // information to determine a value.
    optional int32 quality_score = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Estimates for criterion bids at various positions.
  message PositionEstimates {
    // Output only. The estimate of the CPC bid required for ad to be displayed
    // at the top of the first page of search results.
    optional int64 top_of_page_cpc_micros = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. The resource name of the ad group criterion.
  // Ad group criterion resource names have the form:
  //
  // `customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupCriterion"
    }
  ];

  // Output only. The ID of the criterion.
  optional int64 criterion_id = 56 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this ad group criterion was created. The
  // timestamp is in the customer's time zone and in "yyyy-MM-dd HH:mm:ss"
  // format.
  string creation_time = 81 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The status of the criterion.
  //
  // This is the status of the ad group criterion entity, set by the client.
  // Note: UI reports may incorporate additional information that affects
  // whether a criterion is eligible to run. In some cases a criterion that's
  // REMOVED in the API can still show as enabled in the UI.
  // For example, campaigns by default show to users of all age ranges unless
  // excluded. The UI will show each age range as "enabled", since they're
  // eligible to see the ads; but AdGroupCriterion.status will show "removed",
  // since no positive criterion was added.
  google.ads.searchads360.v0.enums.AdGroupCriterionStatusEnum
      .AdGroupCriterionStatus status = 3;

  // Output only. Information regarding the quality of the criterion.
  QualityInfo quality_info = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The ad group to which the criterion belongs.
  optional string ad_group = 57 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroup"
    }
  ];

  // Output only. The type of the criterion.
  google.ads.searchads360.v0.enums.CriterionTypeEnum.CriterionType type = 25
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
  //
  // This field is immutable. To switch a criterion from positive to negative,
  // remove then re-add it.
  optional bool negative = 58 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The resource names of labels attached to this ad group
  // criterion.
  repeated string labels = 60 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupCriterionLabel"
    }
  ];

  // Output only. The resource names of effective labels attached to this ad
  // group criterion. An effective label is a label inherited or directly
  // assigned to this ad group criterion.
  repeated string effective_labels = 87 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupCriterionEffectiveLabel"
    }
  ];

  // The modifier for the bid when the criterion matches. The modifier must be
  // in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
  optional double bid_modifier = 61;

  // The CPC (cost-per-click) bid.
  optional int64 cpc_bid_micros = 62;

  // Output only. The effective CPC (cost-per-click) bid.
  optional int64 effective_cpc_bid_micros = 66
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Estimates for criterion bids at various positions.
  PositionEstimates position_estimates = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The list of possible final URLs after all cross-domain redirects for the
  // ad.
  repeated string final_urls = 70;

  // Output only. The Engine Status for ad group criterion.
  optional google.ads.searchads360.v0.enums.AdGroupCriterionEngineStatusEnum
      .AdGroupCriterionEngineStatus engine_status = 80
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // URL template for appending params to final URL.
  optional string final_url_suffix = 72;

  // The URL template for constructing a tracking URL.
  optional string tracking_url_template = 73;

  // Output only. ID of the ad group criterion in the external engine account.
  // This field is for non-Google Ads account only, for example, Yahoo Japan,
  // Microsoft, Baidu etc. For Google Ads entity, use
  // "ad_group_criterion.criterion_id" instead.
  string engine_id = 76 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The datetime when this ad group criterion was last modified.
  // The datetime is in the customer's time zone and in "yyyy-MM-dd
  // HH:mm:ss.ssssss" format.
  string last_modified_time = 78 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The ad group criterion.
  //
  // Exactly one must be set.
  oneof criterion {
    // Immutable. Keyword.
    google.ads.searchads360.v0.common.KeywordInfo keyword = 27
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Listing group.
    google.ads.searchads360.v0.common.ListingGroupInfo listing_group = 32
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Age range.
    google.ads.searchads360.v0.common.AgeRangeInfo age_range = 36
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Gender.
    google.ads.searchads360.v0.common.GenderInfo gender = 37
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. User List.
    google.ads.searchads360.v0.common.UserListInfo user_list = 42
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Webpage
    google.ads.searchads360.v0.common.WebpageInfo webpage = 46
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Location.
    google.ads.searchads360.v0.common.LocationInfo location = 82
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
