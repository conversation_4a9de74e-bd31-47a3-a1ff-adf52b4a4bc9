# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

package(default_visibility = ["//visibility:public"])

exports_files(glob(["*.yaml"]) + ["searchads360_grpc_service_config.json"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")


proto_library(
    name = "searchads360_proto",
    srcs = [],
    deps = [
        "//google/ads/searchads360/v0/enums:enums_proto",
        "//google/ads/searchads360/v0/common:common_proto",
        "//google/ads/searchads360/v0/resources:resources_proto",
        "//google/ads/searchads360/v0/services:services_proto",
    ],
)

proto_library_with_info(
    name = "searchads360_proto_with_info",
    deps = [
        ":searchads360_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
)

java_gapic_library(
    name = "searchads360_java_gapic",
    srcs = [
        ":searchads360_proto_with_info",
    ],
    grpc_service_config = ":searchads360_grpc_service_config.json",
    deps = [
        "//google/ads/searchads360/v0/enums:enums_java_proto",
        "//google/ads/searchads360/v0/common:common_java_proto",
        "//google/ads/searchads360/v0/resources:resources_java_proto",
        "//google/ads/searchads360/v0/services:services_java_proto",
        "//google/ads/searchads360/v0/services:services_java_grpc",
    ],
)

java_gapic_test(
    name = "searchads360_java_gapic_suite",
    test_classes = [
        "com.google.ads.searchads360.v0.services.SearchAds360ServiceClientTest",
    ],
    runtime_deps = [":searchads360_java_gapic_test"],
)

java_gapic_assembly_gradle_pkg(
    name = "searchads360-java",
    deps = [
        ":searchads360_java_gapic",
        "//google/ads/searchads360/v0:searchads360_proto",
        "//google/ads/searchads360/v0/enums:enums_java_proto",
        "//google/ads/searchads360/v0/common:common_java_proto",
        "//google/ads/searchads360/v0/resources:resources_java_proto",
        "//google/ads/searchads360/v0/services:services_java_proto",
        "//google/ads/searchads360/v0/services:services_java_grpc",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
)

py_gapic_library(
    name = "searchads360_py_gapic",
    srcs = [":searchads360_proto_with_info"],
    grpc_service_config = "searchads360_grpc_service_config.json",
    opt_args = [
      "old-naming",
      "lazy-import",
      "python-gapic-name=searchads360",
      "python-gapic-templates=ads-templates"
    ],
)

py_gapic_assembly_pkg(
    name = "searchads360-py",
    deps = [
        ":searchads360_py_gapic",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
)

csharp_gapic_library(
    name = "searchads360_csharp_gapic",
    srcs = [
        ":searchads360_proto_with_info",
    ],
    grpc_service_config = "searchads360_grpc_service_config.json",
    deps = [
        "//google/ads/searchads360/v0/services:services_csharp_grpc",
    ],
)

csharp_gapic_assembly_pkg(
    name = "searchads360-csharp",
    deps = [
        ":searchads360_csharp_gapic",
        "//google/ads/searchads360/v0/common:common_csharp_proto",
        "//google/ads/searchads360/v0/enums:enums_csharp_proto",
        "//google/ads/searchads360/v0/resources:resources_csharp_proto",
        "//google/ads/searchads360/v0/services:services_csharp_grpc",
        "//google/ads/searchads360/v0/services:services_csharp_proto",
    ],
)
