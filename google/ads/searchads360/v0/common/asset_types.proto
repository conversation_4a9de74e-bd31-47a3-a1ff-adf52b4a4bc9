// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.common;

import "google/ads/searchads360/v0/common/criteria.proto";
import "google/ads/searchads360/v0/enums/call_conversion_reporting_state.proto";
import "google/ads/searchads360/v0/enums/call_to_action_type.proto";
import "google/ads/searchads360/v0/enums/location_ownership_type.proto";
import "google/ads/searchads360/v0/enums/mime_type.proto";
import "google/ads/searchads360/v0/enums/mobile_app_vendor.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/common;common";
option java_multiple_files = true;
option java_outer_classname = "AssetTypesProto";
option java_package = "com.google.ads.searchads360.v0.common";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Common";
option ruby_package = "Google::Ads::SearchAds360::V0::Common";

// Proto file containing info messages for specific asset types.

// A YouTube asset.
message YoutubeVideoAsset {
  // YouTube video id. This is the 11 character string value used in the
  // YouTube video URL.
  optional string youtube_video_id = 2;

  // YouTube video title.
  string youtube_video_title = 3;
}

// An Image asset.
message ImageAsset {
  // File size of the image asset in bytes.
  optional int64 file_size = 6;

  // MIME type of the image asset.
  google.ads.searchads360.v0.enums.MimeTypeEnum.MimeType mime_type = 3;

  // Metadata for this image at its original size.
  ImageDimension full_size = 4;
}

// Metadata for an image at a certain size, either original or resized.
message ImageDimension {
  // Height of the image.
  optional int64 height_pixels = 4;

  // Width of the image.
  optional int64 width_pixels = 5;

  // A URL that returns the image with this height and width.
  optional string url = 6;
}

// A Text asset.
message TextAsset {
  // Text content of the text asset.
  optional string text = 2;
}

// A unified callout asset.
message UnifiedCalloutAsset {
  // The callout text.
  // The length of this string should be between 1 and 25, inclusive.
  string callout_text = 1;

  // Start date of when this asset is effective and can begin serving, in
  // yyyy-MM-dd format.
  string start_date = 2;

  // Last date of when this asset is effective and still serving, in yyyy-MM-dd
  // format.
  string end_date = 3;

  // List of non-overlapping schedules specifying all time intervals for which
  // the asset may serve. There can be a maximum of 6 schedules per day, 42 in
  // total.
  repeated AdScheduleInfo ad_schedule_targets = 4;

  // Whether to show the asset in search user's time zone. Applies to Microsoft
  // Ads.
  bool use_searcher_time_zone = 5;
}

// A unified sitelink asset.
message UnifiedSitelinkAsset {
  // URL display text for the sitelink.
  // The length of this string should be between 1 and 25, inclusive.
  string link_text = 1;

  // First line of the description for the sitelink.
  // If set, the length should be between 1 and 35, inclusive, and description2
  // must also be set.
  string description1 = 2;

  // Second line of the description for the sitelink.
  // If set, the length should be between 1 and 35, inclusive, and description1
  // must also be set.
  string description2 = 3;

  // Start date of when this asset is effective and can begin serving, in
  // yyyy-MM-dd format.
  string start_date = 4;

  // Last date of when this asset is effective and still serving, in yyyy-MM-dd
  // format.
  string end_date = 5;

  // List of non-overlapping schedules specifying all time intervals for which
  // the asset may serve. There can be a maximum of 6 schedules per day, 42 in
  // total.
  repeated AdScheduleInfo ad_schedule_targets = 6;

  // ID used for tracking clicks for the sitelink asset. This is a Yahoo! Japan
  // only field.
  int64 tracking_id = 7;

  // Whether to show the sitelink asset in search user's time zone.
  // Applies to Microsoft Ads.
  bool use_searcher_time_zone = 8;

  // Whether the preference is for the sitelink asset to be displayed on mobile
  // devices. Applies to Microsoft Ads.
  bool mobile_preferred = 9;
}

// A Unified Page Feed asset.
message UnifiedPageFeedAsset {
  // The webpage that advertisers want to target.
  string page_url = 1;

  // Labels used to group the page urls.
  repeated string labels = 2;
}

// An asset representing a mobile app.
message MobileAppAsset {
  // Required. A string that uniquely identifies a mobile application. It should
  // just contain the platform native id, like "com.android.ebay" for Android or
  // "12345689" for iOS.
  string app_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The application store that distributes this specific app.
  google.ads.searchads360.v0.enums.MobileAppVendorEnum.MobileAppVendor
      app_store = 2 [(google.api.field_behavior) = REQUIRED];
}

// A unified call asset.
message UnifiedCallAsset {
  // Two-letter country code of the phone number. Examples: 'US', 'us'.
  string country_code = 1;

  // The advertiser's raw phone number. Examples: '**********', '(123)456-7890'
  string phone_number = 2;

  // Output only. Indicates whether this CallAsset should use its own call
  // conversion setting, follow the account level setting, or disable call
  // conversion.
  google.ads.searchads360.v0.enums.CallConversionReportingStateEnum
      .CallConversionReportingState call_conversion_reporting_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The conversion action to attribute a call conversion to. If not set, the
  // default conversion action is used. This field only has effect if
  // call_conversion_reporting_state is set to
  // USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTION.
  string call_conversion_action = 4 [(google.api.resource_reference) = {
    type: "searchads360.googleapis.com/ConversionAction"
  }];

  // List of non-overlapping schedules specifying all time intervals for which
  // the asset may serve. There can be a maximum of 6 schedules per day, 42 in
  // total.
  repeated AdScheduleInfo ad_schedule_targets = 5;

  // Whether the call only shows the phone number without a link to the website.
  // Applies to Microsoft Ads.
  bool call_only = 7;

  // Whether the call should be enabled on call tracking.
  // Applies to Microsoft Ads.
  bool call_tracking_enabled = 8;

  // Whether to show the call extension in search user's time zone.
  // Applies to Microsoft Ads.
  bool use_searcher_time_zone = 9;

  // Start date of when this asset is effective and can begin serving, in
  // yyyy-MM-dd format.
  string start_date = 10;

  // Last date of when this asset is effective and still serving, in yyyy-MM-dd
  // format.
  string end_date = 11;
}

// A call to action asset.
message CallToActionAsset {
  // Call to action.
  google.ads.searchads360.v0.enums.CallToActionTypeEnum.CallToActionType
      call_to_action = 1;
}

// A unified location asset.
message UnifiedLocationAsset {
  // Place IDs uniquely identify a place in the Google Places database and on
  // Google Maps.
  // This field is unique for a given customer ID and asset type. See
  // https://developers.google.com/places/web-service/place-id to learn more
  // about Place ID.
  string place_id = 1;

  // The list of business locations for the customer.
  // This will only be returned if the Location Asset is syncing from the
  // Business Profile account. It is possible to have multiple Business Profile
  // listings under the same account that point to the same Place ID.
  repeated BusinessProfileLocation business_profile_locations = 2;

  // The type of location ownership.
  // If the type is BUSINESS_OWNER, it will be served as a location extension.
  // If the type is AFFILIATE, it will be served as an affiliate location.
  google.ads.searchads360.v0.enums.LocationOwnershipTypeEnum
      .LocationOwnershipType location_ownership_type = 3;
}

// Business Profile location data synced from the linked Business Profile
// account.
message BusinessProfileLocation {
  // Advertiser specified label for the location on the Business Profile
  // account. This is synced from the Business Profile account.
  repeated string labels = 1;

  // Business Profile store code of this location. This is synced from the
  // Business Profile account.
  string store_code = 2;

  // Listing ID of this Business Profile location. This is synced from the
  // linked Business Profile account.
  int64 listing_id = 3;
}
