// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.common;

import "google/ads/searchads360/v0/common/ad_asset.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/common;common";
option java_multiple_files = true;
option java_outer_classname = "AdTypeInfosProto";
option java_package = "com.google.ads.searchads360.v0.common";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Common";
option ruby_package = "Google::Ads::SearchAds360::V0::Common";

// Proto file containing info messages for specific ad types.

// A Search Ads 360 text ad.
message SearchAds360TextAdInfo {
  // The headline of the ad.
  optional string headline = 1;

  // The first line of the ad's description.
  optional string description1 = 2;

  // The second line of the ad's description.
  optional string description2 = 3;

  // The displayed URL of the ad.
  optional string display_url = 4;

  // The displayed mobile URL of the ad.
  optional string display_mobile_url = 5;

  // The tracking id of the ad.
  optional int64 ad_tracking_id = 6;
}

// A Search Ads 360 expanded text ad.
message SearchAds360ExpandedTextAdInfo {
  // The headline of the ad.
  optional string headline = 1;

  // The second headline of the ad.
  optional string headline2 = 2;

  // The third headline of the ad.
  optional string headline3 = 3;

  // The first line of the ad's description.
  optional string description1 = 4;

  // The second line of the ad's description.
  optional string description2 = 5;

  // Text appended to the auto-generated visible URL with a delimiter.
  optional string path1 = 6;

  // Text appended to path1 with a delimiter.
  optional string path2 = 7;

  // The tracking id of the ad.
  optional int64 ad_tracking_id = 8;
}

// An expanded dynamic search ad.
message SearchAds360ExpandedDynamicSearchAdInfo {
  // The first line of the ad's description.
  optional string description1 = 1;

  // The second line of the ad's description.
  optional string description2 = 2;

  // The tracking id of the ad.
  optional int64 ad_tracking_id = 3;
}

// A Search Ads 360 product ad.
message SearchAds360ProductAdInfo {}

// A Search Ads 360 responsive search ad.
message SearchAds360ResponsiveSearchAdInfo {
  // Text appended to the auto-generated visible URL with a delimiter.
  optional string path1 = 1;

  // Text appended to path1 with a delimiter.
  optional string path2 = 2;

  // The tracking id of the ad.
  optional int64 ad_tracking_id = 3;

  // List of text assets for headlines. When the ad serves the headlines will
  // be selected from this list.
  repeated AdTextAsset headlines = 4;

  // List of text assets for descriptions. When the ad serves the descriptions
  // will be selected from this list.
  repeated AdTextAsset descriptions = 5;
}
