// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.common;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/common;common";
option java_multiple_files = true;
option java_outer_classname = "TextLabelProto";
option java_package = "com.google.ads.searchads360.v0.common";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Common";
option ruby_package = "Google::Ads::SearchAds360::V0::Common";

// A type of label displaying text on a colored background.
message TextLabel {
  // Background color of the label in HEX format. This string must match the
  // regular expression '^\#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$'.
  // Note: The background color may not be visible for manager accounts.
  optional string background_color = 3;

  // A short description of the label. The length must be no more than 200
  // characters.
  optional string description = 4;
}
