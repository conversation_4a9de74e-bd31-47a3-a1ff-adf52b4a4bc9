// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.common;

import "google/ads/searchads360/v0/common/criteria.proto";
import "google/ads/searchads360/v0/common/value.proto";
import "google/ads/searchads360/v0/enums/ad_network_type.proto";
import "google/ads/searchads360/v0/enums/conversion_action_category.proto";
import "google/ads/searchads360/v0/enums/day_of_week.proto";
import "google/ads/searchads360/v0/enums/device.proto";
import "google/ads/searchads360/v0/enums/product_channel.proto";
import "google/ads/searchads360/v0/enums/product_channel_exclusivity.proto";
import "google/ads/searchads360/v0/enums/product_condition.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/common;common";
option java_multiple_files = true;
option java_outer_classname = "SegmentsProto";
option java_package = "com.google.ads.searchads360.v0.common";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Common";
option ruby_package = "Google::Ads::SearchAds360::V0::Common";

// Proto file describing segment only fields.

// Segment only fields.
message Segments {
  // Ad network type.
  google.ads.searchads360.v0.enums.AdNetworkTypeEnum.AdNetworkType
      ad_network_type = 3;

  // Resource name of the conversion action.
  optional string conversion_action = 146 [(google.api.resource_reference) = {
    type: "searchads360.googleapis.com/ConversionAction"
  }];

  // Conversion action category.
  google.ads.searchads360.v0.enums.ConversionActionCategoryEnum
      .ConversionActionCategory conversion_action_category = 53;

  // Conversion action name.
  optional string conversion_action_name = 114;

  // The conversion custom dimensions.
  repeated Value conversion_custom_dimensions = 188;

  // Date to which metrics apply.
  // yyyy-MM-dd format, for example, 2018-04-17.
  optional string date = 79;

  // Day of the week, for example, MONDAY.
  google.ads.searchads360.v0.enums.DayOfWeekEnum.DayOfWeek day_of_week = 5;

  // Device to which metrics apply.
  google.ads.searchads360.v0.enums.DeviceEnum.Device device = 1;

  // Resource name of the geo target constant that represents a city.
  optional string geo_target_city = 118;

  // Resource name of the geo target constant that represents a country.
  optional string geo_target_country = 119;

  // Resource name of the geo target constant that represents a metro.
  optional string geo_target_metro = 122;

  // Resource name of the geo target constant that represents a region.
  optional string geo_target_region = 126;

  // Hour of day as a number between 0 and 23, inclusive.
  optional int32 hour = 88;

  // Keyword criterion.
  Keyword keyword = 61;

  // Month as represented by the date of the first day of a month. Formatted as
  // yyyy-MM-dd.
  optional string month = 90;

  // Bidding category (level 1) of the product.
  optional string product_bidding_category_level1 = 92;

  // Bidding category (level 2) of the product.
  optional string product_bidding_category_level2 = 93;

  // Bidding category (level 3) of the product.
  optional string product_bidding_category_level3 = 94;

  // Bidding category (level 4) of the product.
  optional string product_bidding_category_level4 = 95;

  // Bidding category (level 5) of the product.
  optional string product_bidding_category_level5 = 96;

  // Brand of the product.
  optional string product_brand = 97;

  // Channel of the product.
  google.ads.searchads360.v0.enums.ProductChannelEnum.ProductChannel
      product_channel = 30;

  // Channel exclusivity of the product.
  google.ads.searchads360.v0.enums.ProductChannelExclusivityEnum
      .ProductChannelExclusivity product_channel_exclusivity = 31;

  // Condition of the product.
  google.ads.searchads360.v0.enums.ProductConditionEnum.ProductCondition
      product_condition = 32;

  // Resource name of the geo target constant for the country of sale of the
  // product.
  optional string product_country = 98;

  // Custom attribute 0 of the product.
  optional string product_custom_attribute0 = 99;

  // Custom attribute 1 of the product.
  optional string product_custom_attribute1 = 100;

  // Custom attribute 2 of the product.
  optional string product_custom_attribute2 = 101;

  // Custom attribute 3 of the product.
  optional string product_custom_attribute3 = 102;

  // Custom attribute 4 of the product.
  optional string product_custom_attribute4 = 103;

  // Item ID of the product.
  optional string product_item_id = 104;

  // Resource name of the language constant for the language of the product.
  optional string product_language = 105;

  // Bidding category (level 1) of the product sold.
  optional string product_sold_bidding_category_level1 = 166;

  // Bidding category (level 2) of the product sold.
  optional string product_sold_bidding_category_level2 = 167;

  // Bidding category (level 3) of the product sold.
  optional string product_sold_bidding_category_level3 = 168;

  // Bidding category (level 4) of the product sold.
  optional string product_sold_bidding_category_level4 = 169;

  // Bidding category (level 5) of the product sold.
  optional string product_sold_bidding_category_level5 = 170;

  // Brand of the product sold.
  optional string product_sold_brand = 171;

  // Condition of the product sold.
  google.ads.searchads360.v0.enums.ProductConditionEnum.ProductCondition
      product_sold_condition = 172;

  // Custom attribute 0 of the product sold.
  optional string product_sold_custom_attribute0 = 173;

  // Custom attribute 1 of the product sold.
  optional string product_sold_custom_attribute1 = 174;

  // Custom attribute 2 of the product sold.
  optional string product_sold_custom_attribute2 = 175;

  // Custom attribute 3 of the product sold.
  optional string product_sold_custom_attribute3 = 176;

  // Custom attribute 4 of the product sold.
  optional string product_sold_custom_attribute4 = 177;

  // Item ID of the product sold.
  optional string product_sold_item_id = 178;

  // Title of the product sold.
  optional string product_sold_title = 179;

  // Type (level 1) of the product sold.
  optional string product_sold_type_l1 = 180;

  // Type (level 2) of the product sold.
  optional string product_sold_type_l2 = 181;

  // Type (level 3) of the product sold.
  optional string product_sold_type_l3 = 182;

  // Type (level 4) of the product sold.
  optional string product_sold_type_l4 = 183;

  // Type (level 5) of the product sold.
  optional string product_sold_type_l5 = 184;

  // Store ID of the product.
  optional string product_store_id = 106;

  // Title of the product.
  optional string product_title = 107;

  // Type (level 1) of the product.
  optional string product_type_l1 = 108;

  // Type (level 2) of the product.
  optional string product_type_l2 = 109;

  // Type (level 3) of the product.
  optional string product_type_l3 = 110;

  // Type (level 4) of the product.
  optional string product_type_l4 = 111;

  // Type (level 5) of the product.
  optional string product_type_l5 = 112;

  // Quarter as represented by the date of the first day of a quarter.
  // Uses the calendar year for quarters, for example, the second quarter of
  // 2018 starts on 2018-04-01. Formatted as yyyy-MM-dd.
  optional string quarter = 128;

  // The raw event conversion dimensions.
  repeated Value raw_event_conversion_dimensions = 189;

  // Week as defined as Monday through Sunday, and represented by the date of
  // Monday. Formatted as yyyy-MM-dd.
  optional string week = 130;

  // Year, formatted as yyyy.
  optional int32 year = 131;

  // Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics.
  // Indicates whether the interaction metrics occurred on the asset itself
  // or a different asset or ad unit.
  // Interactions (for example, clicks) are counted across all the parts of the
  // served ad (for example, Ad itself and other components like Sitelinks) when
  // they are served together. When interaction_on_this_asset is true, it means
  // the interactions are on this specific asset and when
  // interaction_on_this_asset is false, it means the interactions is not on
  // this specific asset but on other parts of the served ad this asset is
  // served with.
  optional AssetInteractionTarget asset_interaction_target = 139;
}

// A Keyword criterion segment.
message Keyword {
  // The AdGroupCriterion resource name.
  optional string ad_group_criterion = 3;

  // Keyword info.
  KeywordInfo info = 2;
}

// An AssetInteractionTarget segment.
message AssetInteractionTarget {
  // The asset resource name.
  string asset = 1;

  // Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics.
  // Indicates whether the interaction metrics occurred on the asset itself or a
  // different asset or ad unit.
  bool interaction_on_this_asset = 2;
}
