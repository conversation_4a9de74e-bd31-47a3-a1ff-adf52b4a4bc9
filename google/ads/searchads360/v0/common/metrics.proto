// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.common;

import "google/ads/searchads360/v0/common/value.proto";
import "google/ads/searchads360/v0/enums/interaction_event_type.proto";
import "google/ads/searchads360/v0/enums/quality_score_bucket.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/common;common";
option java_multiple_files = true;
option java_outer_classname = "MetricsProto";
option java_package = "com.google.ads.searchads360.v0.common";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Common";
option ruby_package = "Google::Ads::SearchAds360::V0::Common";

// Proto file describing metrics.

// Metrics data.
message Metrics {
  // Search absolute top impression share is the percentage of your Search ad
  // impressions that are shown in the most prominent Search position.
  optional double absolute_top_impression_percentage = 183;

  // All conversions from interactions (as oppose to view through conversions)
  // divided by the number of ad interactions.
  optional double all_conversions_from_interactions_rate = 191;

  // The value of all conversions.
  optional double all_conversions_value = 192;

  // The value of all conversions. When this column is selected with date, the
  // values in date column means the conversion date. Details for the
  // by_conversion_date columns are available at
  // https://support.google.com/sa360/answer/9250611.
  double all_conversions_value_by_conversion_date = 240;

  // The total number of conversions. This includes all conversions regardless
  // of the value of include_in_conversions_metric.
  optional double all_conversions = 193;

  // The total number of conversions. This includes all conversions regardless
  // of the value of include_in_conversions_metric. When this column is selected
  // with date, the values in date column means the conversion date. Details for
  // the by_conversion_date columns are available at
  // https://support.google.com/sa360/answer/9250611.
  double all_conversions_by_conversion_date = 241;

  // The value of all conversions divided by the total cost of ad interactions
  // (such as clicks for text ads or views for video ads).
  optional double all_conversions_value_per_cost = 194;

  // The number of times people clicked the "Call" button to call a store during
  // or after clicking an ad. This number doesn't include whether or not calls
  // were connected, or the duration of any calls.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_click_to_call = 195;

  // The number of times people clicked a "Get directions" button to navigate to
  // a store after clicking an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_directions = 196;

  // The value of all conversions from interactions divided by the total number
  // of interactions.
  optional double all_conversions_from_interactions_value_per_interaction = 197;

  // The number of times people clicked a link to view a store's menu after
  // clicking an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_menu = 198;

  // The number of times people placed an order at a store after clicking an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_order = 199;

  // The number of other conversions (for example, posting a review or saving a
  // location for a store) that occurred after people clicked an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_other_engagement = 200;

  // Estimated number of times people visited a store after clicking an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_store_visit = 201;

  // Clicks that Search Ads 360 has successfully recorded and forwarded to an
  // advertiser's landing page.
  optional double visits = 289;

  // The number of times that people were taken to a store's URL after clicking
  // an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_store_website = 202;

  // The average amount you pay per interaction. This amount is the total cost
  // of your ads divided by the total number of interactions.
  optional double average_cost = 203;

  // The total cost of all clicks divided by the total number of clicks
  // received.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional double average_cpc = 317;

  // Average cost-per-thousand impressions (CPM).
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional double average_cpm = 318;

  // The number of clicks.
  optional int64 clicks = 131;

  // The estimated percent of times that your ad was eligible to show
  // on the Display Network but didn't because your budget was too low.
  // Note: Content budget lost impression share is reported in the range of 0
  // to 0.9. Any value above 0.9 is reported as 0.9001.
  optional double content_budget_lost_impression_share = 159;

  // The impressions you've received on the Display Network divided
  // by the estimated number of impressions you were eligible to receive.
  // Note: Content impression share is reported in the range of 0.1 to 1. Any
  // value below 0.1 is reported as 0.0999.
  optional double content_impression_share = 160;

  // The conversion custom metrics.
  repeated Value conversion_custom_metrics = 336;

  // The estimated percentage of impressions on the Display Network
  // that your ads didn't receive due to poor Ad Rank.
  // Note: Content rank lost impression share is reported in the range of 0
  // to 0.9. Any value above 0.9 is reported as 0.9001.
  optional double content_rank_lost_impression_share = 163;

  // Average biddable conversions (from interaction) per conversion eligible
  // interaction. Shows how often, on average, an ad interaction leads to a
  // biddable conversion.
  optional double conversions_from_interactions_rate = 284;

  // The value of client account conversions. This only
  // includes conversion actions which
  // include_in_client_account_conversions_metric attribute is set to true. If
  // you use conversion-based bidding, your bid strategies will optimize for
  // these conversions.
  optional double client_account_conversions_value = 165;

  // The sum of biddable conversions value by conversion date. When this
  // column is selected with date, the values in date column means the
  // conversion date.
  double conversions_value_by_conversion_date = 283;

  // The value of biddable conversion divided by the total cost of conversion
  // eligible interactions.
  optional double conversions_value_per_cost = 288;

  // The value of conversions from interactions divided by the number of ad
  // interactions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double conversions_from_interactions_value_per_interaction = 167;

  // The number of client account conversions. This only
  // includes conversion actions which
  // include_in_client_account_conversions_metric attribute is set to true. If
  // you use conversion-based bidding, your bid strategies will optimize for
  // these conversions.
  optional double client_account_conversions = 168;

  // The sum of conversions by conversion date for biddable conversion types.
  // Can be fractional due to attribution modeling. When this column is selected
  // with date, the values in date column means the conversion date.
  double conversions_by_conversion_date = 282;

  // The sum of your cost-per-click (CPC) and cost-per-thousand impressions
  // (CPM) costs during this period.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 cost_micros = 316;

  // The cost of ad interactions divided by all conversions.
  optional double cost_per_all_conversions = 170;

  // Average conversion eligible cost per biddable conversion.
  optional double cost_per_conversion = 286;

  // The cost of ad interactions divided by current model attributed
  // conversions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double cost_per_current_model_attributed_conversion = 172;

  // Conversions from when a customer clicks on an ad on one device,
  // then converts on a different device or browser.
  // Cross-device conversions are already included in all_conversions.
  optional double cross_device_conversions = 173;

  // The number of cross-device conversions by conversion date.
  // Details for the by_conversion_date columns are available at
  // https://support.google.com/sa360/answer/9250611.
  optional double cross_device_conversions_by_conversion_date = 372;

  // The sum of the value of cross-device conversions.
  optional double cross_device_conversions_value = 253;

  // The sum of cross-device conversions value by conversion date.
  // Details for the by_conversion_date columns are available at
  // https://support.google.com/sa360/answer/9250611.
  optional double cross_device_conversions_value_by_conversion_date = 373;

  // The number of clicks your ad receives (Clicks) divided by the number
  // of times your ad is shown (Impressions).
  optional double ctr = 174;

  // The number of conversions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double conversions = 251;

  // The sum of conversion values for the conversions included in the
  // "conversions" field. This metric is useful only if you entered a value for
  // your conversion actions.
  optional double conversions_value = 252;

  // The creative historical quality score.
  google.ads.searchads360.v0.enums.QualityScoreBucketEnum.QualityScoreBucket
      historical_creative_quality_score = 80;

  // The average quality score.
  optional double average_quality_score = 364;

  // The quality of historical landing page experience.
  google.ads.searchads360.v0.enums.QualityScoreBucketEnum.QualityScoreBucket
      historical_landing_page_quality_score = 81;

  // The historical quality score.
  optional int64 historical_quality_score = 216;

  // The historical search predicted click through rate (CTR).
  google.ads.searchads360.v0.enums.QualityScoreBucketEnum.QualityScoreBucket
      historical_search_predicted_ctr = 83;

  // Count of how often your ad has appeared on a search results page or
  // website on the Google Network.
  optional int64 impressions = 221;

  // How often people interact with your ad after it is shown to them.
  // This is the number of interactions divided by the number of times your ad
  // is shown.
  optional double interaction_rate = 222;

  // The number of interactions.
  // An interaction is the main user action associated with an ad format-clicks
  // for text and shopping ads, views for video ads, and so on.
  optional int64 interactions = 223;

  // The types of payable and free interactions.
  repeated google.ads.searchads360.v0.enums.InteractionEventTypeEnum
      .InteractionEventType interaction_event_types = 100;

  // The percentage of clicks filtered out of your total number of clicks
  // (filtered + non-filtered clicks) during the reporting period.
  optional double invalid_click_rate = 224;

  // Number of clicks Google considers illegitimate and doesn't charge you for.
  optional int64 invalid_clicks = 225;

  // The percentage of clicks that have been filtered out of your total number
  // of clicks (filtered + non-filtered clicks) due to being general invalid
  // clicks. These are clicks Google considers illegitimate that are detected
  // through routine means of filtration (that is, known invalid data-center
  // traffic, bots and spiders or other crawlers, irregular patterns, etc).
  // You're not charged for them, and they don't affect your account statistics.
  // See the help page at
  // https://support.google.com/campaignmanager/answer/6076504 for
  // details.
  optional double general_invalid_click_rate = 370;

  // Number of general invalid clicks. These are a subset of your invalid clicks
  // that are detected through routine means of filtration (such as known
  // invalid data-center traffic, bots and spiders or other crawlers, irregular
  // patterns, etc.). You're not charged for them, and they don't affect your
  // account statistics. See the help page at
  // https://support.google.com/campaignmanager/answer/6076504 for
  // details.
  optional int64 general_invalid_clicks = 371;

  // The percentage of mobile clicks that go to a mobile-friendly page.
  optional double mobile_friendly_clicks_percentage = 229;

  // The raw event conversion metrics.
  repeated Value raw_event_conversion_metrics = 337;

  // The percentage of the customer's Shopping or Search ad impressions that are
  // shown in the most prominent Shopping position. See
  // https://support.google.com/sa360/answer/9566729
  // for details. Any value below 0.1 is reported as 0.0999.
  optional double search_absolute_top_impression_share = 136;

  // The number estimating how often your ad wasn't the very first ad among the
  // top ads in the search results due to a low budget. Note: Search
  // budget lost absolute top impression share is reported in the range of 0 to
  // 0.9. Any value above 0.9 is reported as 0.9001.
  optional double search_budget_lost_absolute_top_impression_share = 137;

  // The estimated percent of times that your ad was eligible to show on the
  // Search Network but didn't because your budget was too low. Note: Search
  // budget lost impression share is reported in the range of 0 to 0.9. Any
  // value above 0.9 is reported as 0.9001.
  optional double search_budget_lost_impression_share = 138;

  // The number estimating how often your ad didn't show adjacent to the top
  // organic search results due to a low budget. Note: Search
  // budget lost top impression share is reported in the range of 0 to 0.9. Any
  // value above 0.9 is reported as 0.9001.
  optional double search_budget_lost_top_impression_share = 139;

  // The number of clicks you've received on the Search Network
  // divided by the estimated number of clicks you were eligible to receive.
  // Note: Search click share is reported in the range of 0.1 to 1. Any value
  // below 0.1 is reported as 0.0999.
  optional double search_click_share = 140;

  // The impressions you've received divided by the estimated number of
  // impressions you were eligible to receive on the Search Network for search
  // terms that matched your keywords exactly (or were close variants of your
  // keyword), regardless of your keyword match types. Note: Search exact match
  // impression share is reported in the range of 0.1 to 1. Any value below 0.1
  // is reported as 0.0999.
  optional double search_exact_match_impression_share = 141;

  // The impressions you've received on the Search Network divided
  // by the estimated number of impressions you were eligible to receive.
  // Note: Search impression share is reported in the range of 0.1 to 1. Any
  // value below 0.1 is reported as 0.0999.
  optional double search_impression_share = 142;

  // The number estimating how often your ad wasn't the very first ad among the
  // top ads in the search results due to poor Ad Rank.
  // Note: Search rank lost absolute top impression share is reported in the
  // range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.
  optional double search_rank_lost_absolute_top_impression_share = 143;

  // The estimated percentage of impressions on the Search Network
  // that your ads didn't receive due to poor Ad Rank.
  // Note: Search rank lost impression share is reported in the range of 0 to
  // 0.9. Any value above 0.9 is reported as 0.9001.
  optional double search_rank_lost_impression_share = 144;

  // The number estimating how often your ad didn't show adjacent to the top
  // organic search results due to poor Ad Rank.
  // Note: Search rank lost top impression share is reported in the range of 0
  // to 0.9. Any value above 0.9 is reported as 0.9001.
  optional double search_rank_lost_top_impression_share = 145;

  // The impressions you've received among the top ads compared to the estimated
  // number of impressions you were eligible to receive among the top ads.
  // Note: Search top impression share is reported in the range of 0.1 to 1. Any
  // value below 0.1 is reported as 0.0999.
  //
  // Top ads are generally above the top organic results, although they may show
  // below the top organic results on certain queries.
  optional double search_top_impression_share = 146;

  // The percent of your ad impressions that are shown adjacent to the top
  // organic search results.
  optional double top_impression_percentage = 148;

  // The value of all conversions divided by the number of all conversions.
  optional double value_per_all_conversions = 150;

  // The value of all conversions divided by the number of all conversions. When
  // this column is selected with date, the values in date column means the
  // conversion date. Details for the by_conversion_date columns are available
  // at https://support.google.com/sa360/answer/9250611.
  optional double value_per_all_conversions_by_conversion_date = 244;

  // The value of biddable conversion divided by the number of biddable
  // conversions. Shows how much, on average, each of the biddable conversions
  // is worth.
  optional double value_per_conversion = 287;

  // Biddable conversions value by conversion date divided by biddable
  // conversions by conversion date. Shows how much, on average, each of the
  // biddable conversions is worth (by conversion date). When this column is
  // selected with date, the values in date column means the conversion date.
  optional double value_per_conversions_by_conversion_date = 285;

  // The total number of view-through conversions.
  // These happen when a customer sees an image or rich media ad, then later
  // completes a conversion on your site without interacting with (for example,
  // clicking on) another ad.
  optional int64 client_account_view_through_conversions = 155;

  // Client account cross-sell cost of goods sold (COGS) is the total cost
  // of products sold as a result of advertising a different product.
  // How it works: You report conversions with cart data for
  // completed purchases on your website. If the ad that was interacted with
  // before the purchase has an associated product (see Shopping Ads) then this
  // product is considered the advertised product. Any product included in the
  // order the customer places is a sold product. If these products don't match
  // then this is considered cross-sell. Cross-sell cost of goods sold is the
  // total cost of the products sold that weren't advertised. Example: Someone
  // clicked on a Shopping ad for a hat then bought the same hat and a shirt.
  // The hat has a cost of goods sold value of $3, the shirt has a cost of goods
  // sold value of $5. The cross-sell cost of goods sold for this order is $5.
  // This metric is only available if you report conversions with cart data.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 client_account_cross_sell_cost_of_goods_sold_micros = 321;

  // Cross-sell cost of goods sold (COGS) is the total cost of products sold as
  // a result of advertising a different product.
  // How it works: You report conversions with cart data for
  // completed purchases on your website. If the ad that was interacted with
  // before the purchase has an associated product (see Shopping Ads) then this
  // product is considered the advertised product. Any product included in the
  // order the customer places is a sold product. If these products don't match
  // then this is considered cross-sell. Cross-sell cost of goods sold is the
  // total cost of the products sold that weren't advertised. Example: Someone
  // clicked on a Shopping ad for a hat then bought the same hat and a shirt.
  // The hat has a cost of goods sold value of $3, the shirt has a cost of goods
  // sold value of $5. The cross-sell cost of goods sold for this order is $5.
  // This metric is only available if you report conversions with cart data.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 cross_sell_cost_of_goods_sold_micros = 327;

  // Client account cross-sell gross profit is the profit you made from
  // products sold as a result of advertising a different product, minus cost of
  // goods sold (COGS).
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the purchase is a sold
  // product. If these products don't match then this is considered cross-sell.
  // Cross-sell gross profit is the revenue you made from cross-sell attributed
  // to your ads minus the cost of the goods sold. Example: Someone clicked on a
  // Shopping ad for a hat then bought the same hat and a shirt. The shirt is
  // priced $20 and has a cost of goods sold value of $5. The cross-sell gross
  // profit of this order is $15 = $20 - $5. This metric is only available if
  // you report conversions with cart data. This metric is a monetary value and
  // returned in the customer's currency by default. See the metrics_currency
  // parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 client_account_cross_sell_gross_profit_micros = 322;

  // Cross-sell gross profit is the profit you made from products sold as a
  // result of advertising a different product, minus cost of goods sold (COGS).
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the purchase is a sold
  // product. If these products don't match then this is considered cross-sell.
  // Cross-sell gross profit is the revenue you made from cross-sell attributed
  // to your ads minus the cost of the goods sold.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The shirt is priced $20 and has a cost of goods sold value
  // of $5. The cross-sell gross profit of this order is $15 = $20 - $5.
  // This metric is only available if you report conversions with cart data.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 cross_sell_gross_profit_micros = 328;

  // Client account cross-sell revenue is the total amount you made from
  // products sold as a result of advertising a different product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If these products don't match then this is
  // considered cross-sell. Cross-sell revenue is the total value you made from
  // cross-sell attributed to your ads. Example: Someone clicked on a Shopping
  // ad for a hat then bought the same hat and a shirt. The hat is priced $10
  // and the shirt is priced $20. The cross-sell revenue of this order is $20.
  // This metric is only available if you report conversions with cart data.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 client_account_cross_sell_revenue_micros = 323;

  // Cross-sell revenue is the total amount you made from products sold as a
  // result of advertising a different product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If these products don't match then this is
  // considered cross-sell. Cross-sell revenue is the total value you made from
  // cross-sell attributed to your ads. Example: Someone clicked on a Shopping
  // ad for a hat then bought the same hat and a shirt. The hat is priced $10
  // and the shirt is priced $20. The cross-sell revenue of this order is $20.
  // This metric is only available if you report conversions with cart data.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 cross_sell_revenue_micros = 329;

  // Client account cross-sell units sold is
  // the total number of products sold as a result of advertising a different
  // product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If these products don't match then this is
  // considered cross-sell. Cross-sell units sold is the total number of
  // cross-sold products from all orders attributed to your ads.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat, a shirt and a jacket. The cross-sell units sold in this order is 2.
  // This metric is only available if you report conversions with cart data.
  optional double client_account_cross_sell_units_sold = 307;

  // Cross-sell units sold is the total number of products sold as a result of
  // advertising a different product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If these products don't match then this is
  // considered cross-sell. Cross-sell units sold is the total number of
  // cross-sold products from all orders attributed to your ads.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat, a shirt and a jacket. The cross-sell units sold in this order is 2.
  // This metric is only available if you report conversions with cart data.
  optional double cross_sell_units_sold = 330;

  // Client account lead cost of goods sold (COGS) is the total cost of
  // products sold as a result of advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with has an associated
  // product (see Shopping Ads) then this product is considered the advertised
  // product. Any product included in the order the customer places is a sold
  // product. If the advertised and sold products match, then the cost of these
  // goods is counted under lead cost of goods sold. Example: Someone clicked on
  // a Shopping ad for a hat then bought the same hat and a shirt. The hat has a
  // cost of goods sold value of $3, the shirt has a cost of goods sold value of
  // $5. The lead cost of goods sold for this order is $3. This metric is only
  // available if you report conversions with cart data. This metric is a
  // monetary value and returned in the customer's currency by default. See the
  // metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 client_account_lead_cost_of_goods_sold_micros = 324;

  // Lead cost of goods sold (COGS) is the total cost of products sold as a
  // result of advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with has an associated
  // product (see Shopping Ads) then this product is considered the advertised
  // product. Any product included in the order the customer places is a sold
  // product. If the advertised and sold products match, then the cost of these
  // goods is counted under lead cost of goods sold. Example: Someone clicked on
  // a Shopping ad for a hat then bought the same hat and a shirt. The hat has a
  // cost of goods sold value of $3, the shirt has a cost of goods sold value of
  // $5. The lead cost of goods sold for this order is $3. This metric is only
  // available if you report conversions with cart data. This metric is a
  // monetary value and returned in the customer's currency by default. See the
  // metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 lead_cost_of_goods_sold_micros = 332;

  // Client account lead gross profit is the profit you made from products
  // sold as a result of advertising the same product, minus cost of goods sold
  // (COGS).
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the revenue you made from these sales minus the cost of goods sold is your
  // lead gross profit. Example: Someone clicked on a Shopping ad for a hat then
  // bought the same hat and a shirt. The hat is priced $10 and has a cost of
  // goods sold value of $3. The lead gross profit of this order is $7 = $10 -
  // $3. This metric is only available if you report conversions with cart data.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 client_account_lead_gross_profit_micros = 325;

  // Lead gross profit is the profit you made from products sold as a result of
  // advertising the same product, minus cost of goods sold (COGS).
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the revenue you made from these sales minus the cost of goods sold is your
  // lead gross profit. Example: Someone clicked on a Shopping ad for a hat then
  // bought the same hat and a shirt. The hat is priced $10 and has a cost of
  // goods sold value of $3. The lead gross profit of this order is $7 = $10 -
  // $3. This metric is only available if you report conversions with cart data.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 lead_gross_profit_micros = 333;

  // Client account lead revenue is the total amount you made from
  // products sold as a result of advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the total value you made from the sales of these products is shown under
  // lead revenue. Example: Someone clicked on a Shopping ad for a hat then
  // bought the same hat and a shirt. The hat is priced $10 and the shirt is
  // priced $20. The lead revenue of this order is $10. This metric is only
  // available if you report conversions with cart data. This metric is a
  // monetary value and returned in the customer's currency by default. See the
  // metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 client_account_lead_revenue_micros = 326;

  // Lead revenue is the total amount you made from products sold as a result of
  // advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the total value you made from the sales of these products is shown under
  // lead revenue.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
  // lead revenue of this order is $10.
  // This metric is only available if you report conversions with cart data.
  // This metric is a monetary value and returned in the customer's currency by
  // default. See the metrics_currency parameter at
  // https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause
  optional int64 lead_revenue_micros = 334;

  // Client account lead units sold is the total number of
  // products sold as a result of advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the total number of these products sold is shown under lead units sold.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat, a shirt and a jacket. The lead units sold in this order is 1.
  // This metric is only available if you report conversions with cart data.
  optional double client_account_lead_units_sold = 311;

  // Lead units sold is the total number of products sold as a result of
  // advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the total number of these products sold is shown under lead units sold.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat, a shirt and a jacket. The lead units sold in this order is 1.
  // This metric is only available if you report conversions with cart data.
  optional double lead_units_sold = 335;
}
