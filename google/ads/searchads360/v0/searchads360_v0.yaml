type: google.api.Service
config_version: 3
name: searchads360.googleapis.com
title: Search Ads 360 Reporting API

apis:
- name: google.ads.searchads360.v0.services.CustomColumnService
- name: google.ads.searchads360.v0.services.CustomerService
- name: google.ads.searchads360.v0.services.SearchAds360FieldService
- name: google.ads.searchads360.v0.services.SearchAds360Service

types:
- name: google.ads.searchads360.v0.errors.SearchAds360Failure

documentation:
  summary: |-
    The Search Ads 360 API allows developers to automate downloading reports
    from Search Ads 360.

authentication:
  rules:
  - selector: google.ads.searchads360.v0.services.CustomColumnService.GetCustomColumn
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/doubleclicksearch
  - selector: google.ads.searchads360.v0.services.CustomColumnService.ListCustomColumns
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/doubleclicksearch
  - selector: google.ads.searchads360.v0.services.CustomerService.ListAccessibleCustomers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/doubleclicksearch
  - selector: google.ads.searchads360.v0.services.SearchAds360FieldService.GetSearchAds360Field
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/doubleclicksearch
  - selector: google.ads.searchads360.v0.services.SearchAds360FieldService.SearchSearchAds360Fields
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/doubleclicksearch
  - selector: google.ads.searchads360.v0.services.SearchAds360Service.Search
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/doubleclicksearch
  - selector: google.ads.searchads360.v0.services.SearchAds360Service.SearchStream
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/doubleclicksearch
