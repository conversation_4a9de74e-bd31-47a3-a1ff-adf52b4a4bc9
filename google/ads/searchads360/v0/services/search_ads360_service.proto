// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.services;

import "google/ads/searchads360/v0/common/metrics.proto";
import "google/ads/searchads360/v0/common/segments.proto";
import "google/ads/searchads360/v0/common/value.proto";
import "google/ads/searchads360/v0/enums/summary_row_setting.proto";
import "google/ads/searchads360/v0/resources/accessible_bidding_strategy.proto";
import "google/ads/searchads360/v0/resources/ad_group.proto";
import "google/ads/searchads360/v0/resources/ad_group_ad.proto";
import "google/ads/searchads360/v0/resources/ad_group_ad_effective_label.proto";
import "google/ads/searchads360/v0/resources/ad_group_ad_label.proto";
import "google/ads/searchads360/v0/resources/ad_group_asset.proto";
import "google/ads/searchads360/v0/resources/ad_group_asset_set.proto";
import "google/ads/searchads360/v0/resources/ad_group_audience_view.proto";
import "google/ads/searchads360/v0/resources/ad_group_bid_modifier.proto";
import "google/ads/searchads360/v0/resources/ad_group_criterion.proto";
import "google/ads/searchads360/v0/resources/ad_group_criterion_effective_label.proto";
import "google/ads/searchads360/v0/resources/ad_group_criterion_label.proto";
import "google/ads/searchads360/v0/resources/ad_group_effective_label.proto";
import "google/ads/searchads360/v0/resources/ad_group_label.proto";
import "google/ads/searchads360/v0/resources/age_range_view.proto";
import "google/ads/searchads360/v0/resources/asset.proto";
import "google/ads/searchads360/v0/resources/asset_group.proto";
import "google/ads/searchads360/v0/resources/asset_group_asset.proto";
import "google/ads/searchads360/v0/resources/asset_group_listing_group_filter.proto";
import "google/ads/searchads360/v0/resources/asset_group_signal.proto";
import "google/ads/searchads360/v0/resources/asset_group_top_combination_view.proto";
import "google/ads/searchads360/v0/resources/asset_set.proto";
import "google/ads/searchads360/v0/resources/asset_set_asset.proto";
import "google/ads/searchads360/v0/resources/audience.proto";
import "google/ads/searchads360/v0/resources/bidding_strategy.proto";
import "google/ads/searchads360/v0/resources/campaign.proto";
import "google/ads/searchads360/v0/resources/campaign_asset.proto";
import "google/ads/searchads360/v0/resources/campaign_asset_set.proto";
import "google/ads/searchads360/v0/resources/campaign_audience_view.proto";
import "google/ads/searchads360/v0/resources/campaign_budget.proto";
import "google/ads/searchads360/v0/resources/campaign_criterion.proto";
import "google/ads/searchads360/v0/resources/campaign_effective_label.proto";
import "google/ads/searchads360/v0/resources/campaign_label.proto";
import "google/ads/searchads360/v0/resources/cart_data_sales_view.proto";
import "google/ads/searchads360/v0/resources/conversion.proto";
import "google/ads/searchads360/v0/resources/conversion_action.proto";
import "google/ads/searchads360/v0/resources/conversion_custom_variable.proto";
import "google/ads/searchads360/v0/resources/customer.proto";
import "google/ads/searchads360/v0/resources/customer_asset.proto";
import "google/ads/searchads360/v0/resources/customer_asset_set.proto";
import "google/ads/searchads360/v0/resources/customer_client.proto";
import "google/ads/searchads360/v0/resources/customer_manager_link.proto";
import "google/ads/searchads360/v0/resources/dynamic_search_ads_search_term_view.proto";
import "google/ads/searchads360/v0/resources/gender_view.proto";
import "google/ads/searchads360/v0/resources/geo_target_constant.proto";
import "google/ads/searchads360/v0/resources/keyword_view.proto";
import "google/ads/searchads360/v0/resources/label.proto";
import "google/ads/searchads360/v0/resources/language_constant.proto";
import "google/ads/searchads360/v0/resources/location_view.proto";
import "google/ads/searchads360/v0/resources/product_bidding_category_constant.proto";
import "google/ads/searchads360/v0/resources/product_group_view.proto";
import "google/ads/searchads360/v0/resources/shopping_performance_view.proto";
import "google/ads/searchads360/v0/resources/user_list.proto";
import "google/ads/searchads360/v0/resources/user_location_view.proto";
import "google/ads/searchads360/v0/resources/visit.proto";
import "google/ads/searchads360/v0/resources/webpage_view.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/services;services";
option java_multiple_files = true;
option java_outer_classname = "SearchAds360ServiceProto";
option java_package = "com.google.ads.searchads360.v0.services";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Services";
option ruby_package = "Google::Ads::SearchAds360::V0::Services";

// Proto file describing the SearchAds360Service.

// Service to fetch data and metrics across resources.
service SearchAds360Service {
  option (google.api.default_host) = "searchads360.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/doubleclicksearch";

  // Returns all rows that match the search query.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QueryError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc Search(SearchSearchAds360Request) returns (SearchSearchAds360Response) {
    option (google.api.http) = {
      post: "/v0/customers/{customer_id=*}/searchAds360:search"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,query";
  }

  // Returns all rows that match the search stream query.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QueryError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc SearchStream(SearchSearchAds360StreamRequest)
      returns (stream SearchSearchAds360StreamResponse) {
    option (google.api.http) = {
      post: "/v0/customers/{customer_id=*}/searchAds360:searchStream"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,query";
  }
}

// Request message for
// [SearchAds360Service.Search][google.ads.searchads360.v0.services.SearchAds360Service.Search].
message SearchSearchAds360Request {
  // Required. The ID of the customer being queried.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The query string.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Token of the page to retrieve. If not specified, the first
  // page of results will be returned. Use the value obtained from
  // `next_page_token` in the previous response in order to request
  // the next page of results.
  string page_token = 3;

  // Number of elements to retrieve in a single page.
  // When too large a page is requested, the server may decide to
  // further limit the number of returned resources.
  int32 page_size = 4;

  // If true, the request is validated but not executed.
  bool validate_only = 5;

  // If true, the total number of results that match the query ignoring the
  // LIMIT clause will be included in the response.
  // Default is false.
  bool return_total_results_count = 7;

  // Determines whether a summary row will be returned. By default, summary row
  // is not returned. If requested, the summary row will be sent in a response
  // by itself after all other query results are returned.
  google.ads.searchads360.v0.enums.SummaryRowSettingEnum.SummaryRowSetting
      summary_row_setting = 8;
}

// Response message for
// [SearchAds360Service.Search][google.ads.searchads360.v0.services.SearchAds360Service.Search].
message SearchSearchAds360Response {
  // The list of rows that matched the query.
  repeated SearchAds360Row results = 1;

  // Pagination token used to retrieve the next page of results.
  // Pass the content of this string as the `page_token` attribute of
  // the next request. `next_page_token` is not returned for the last
  // page.
  string next_page_token = 2;

  // Total number of results that match the query ignoring the LIMIT
  // clause.
  int64 total_results_count = 3;

  // FieldMask that represents what fields were requested by the user.
  google.protobuf.FieldMask field_mask = 5;

  // Summary row that contains summary of metrics in results.
  // Summary of metrics means aggregation of metrics across all results,
  // here aggregation could be sum, average, rate, etc.
  SearchAds360Row summary_row = 6;

  // The headers of the custom columns in the results.
  repeated CustomColumnHeader custom_column_headers = 7;

  // The headers of the conversion custom metrics in the results.
  repeated ConversionCustomMetricHeader conversion_custom_metric_headers = 9;

  // The headers of the conversion custom dimensions in the results.
  repeated ConversionCustomDimensionHeader conversion_custom_dimension_headers =
      10;

  // The headers of the raw event conversion metrics in the results.
  repeated RawEventConversionMetricHeader raw_event_conversion_metric_headers =
      11;

  // The headers of the raw event conversion dimensions in the results.
  repeated RawEventConversionDimensionHeader
      raw_event_conversion_dimension_headers = 12;
}

// Request message for
// [SearchAds360Service.SearchStream][google.ads.searchads360.v0.services.SearchAds360Service.SearchStream].
message SearchSearchAds360StreamRequest {
  // Required. The ID of the customer being queried.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The query string.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // The number of rows that are returned in each stream response batch.
  // When too large batch is requested, the server may decide to further limit
  // the number of returned rows.
  int32 batch_size = 4;

  // Determines whether a summary row will be returned. By default, summary row
  // is not returned. If requested, the summary row will be sent in a response
  // by itself after all other query results are returned.
  google.ads.searchads360.v0.enums.SummaryRowSettingEnum.SummaryRowSetting
      summary_row_setting = 3;
}

// Response message for
// [SearchAds360Service.SearchStream][google.ads.searchads360.v0.services.SearchAds360Service.SearchStream].
message SearchSearchAds360StreamResponse {
  // The list of rows that matched the query.
  repeated SearchAds360Row results = 1;

  // FieldMask that represents what fields were requested by the user.
  google.protobuf.FieldMask field_mask = 2;

  // Summary row that contains summary of metrics in results.
  // Summary of metrics means aggregation of metrics across all results,
  // here aggregation could be sum, average, rate, etc.
  SearchAds360Row summary_row = 3;

  // The headers of the custom columns in the results.
  repeated CustomColumnHeader custom_column_headers = 5;

  // The headers of the conversion custom metrics in the results.
  repeated ConversionCustomMetricHeader conversion_custom_metric_headers = 7;

  // The headers of the conversion custom dimension in the results.
  repeated ConversionCustomDimensionHeader conversion_custom_dimension_headers =
      8;

  // The headers of the raw event conversion metrics in the results.
  repeated RawEventConversionMetricHeader raw_event_conversion_metric_headers =
      9;

  // The headers of the raw event conversion dimensions in the results.
  repeated RawEventConversionDimensionHeader
      raw_event_conversion_dimension_headers = 10;

  // The unique id of the request that is used for debugging purposes.
  string request_id = 4;
}

// A returned row from the query.
message SearchAds360Row {
  // The ad group referenced in the query.
  google.ads.searchads360.v0.resources.AdGroup ad_group = 3;

  // The ad referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupAd ad_group_ad = 16;

  // The ad group ad effective label referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupAdEffectiveLabel
      ad_group_ad_effective_label = 234;

  // The ad group ad label referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupAdLabel ad_group_ad_label = 120;

  // The ad group asset referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupAsset ad_group_asset = 154;

  // The ad group asset set referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupAssetSet ad_group_asset_set = 196;

  // The ad group audience view referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupAudienceView
      ad_group_audience_view = 57;

  // The bid modifier referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupBidModifier
      ad_group_bid_modifier = 24;

  // The criterion referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupCriterion ad_group_criterion = 17;

  // The ad group criterion effective label referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupCriterionEffectiveLabel
      ad_group_criterion_effective_label = 235;

  // The ad group criterion label referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupCriterionLabel
      ad_group_criterion_label = 121;

  // The ad group effective label referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupEffectiveLabel
      ad_group_effective_label = 231;

  // The ad group label referenced in the query.
  google.ads.searchads360.v0.resources.AdGroupLabel ad_group_label = 115;

  // The age range view referenced in the query.
  google.ads.searchads360.v0.resources.AgeRangeView age_range_view = 48;

  // The asset referenced in the query.
  google.ads.searchads360.v0.resources.Asset asset = 105;

  // The asset group asset referenced in the query.
  google.ads.searchads360.v0.resources.AssetGroupAsset asset_group_asset = 173;

  // The asset group signal referenced in the query.
  google.ads.searchads360.v0.resources.AssetGroupSignal asset_group_signal =
      191;

  // The asset group listing group filter referenced in the query.
  google.ads.searchads360.v0.resources.AssetGroupListingGroupFilter
      asset_group_listing_group_filter = 182;

  // The asset group top combination view referenced in the query.
  google.ads.searchads360.v0.resources.AssetGroupTopCombinationView
      asset_group_top_combination_view = 199;

  // The asset group referenced in the query.
  google.ads.searchads360.v0.resources.AssetGroup asset_group = 172;

  // The asset set asset referenced in the query.
  google.ads.searchads360.v0.resources.AssetSetAsset asset_set_asset = 180;

  // The asset set referenced in the query.
  google.ads.searchads360.v0.resources.AssetSet asset_set = 179;

  // The bidding strategy referenced in the query.
  google.ads.searchads360.v0.resources.BiddingStrategy bidding_strategy = 18;

  // The campaign budget referenced in the query.
  google.ads.searchads360.v0.resources.CampaignBudget campaign_budget = 19;

  // The campaign referenced in the query.
  google.ads.searchads360.v0.resources.Campaign campaign = 2;

  // The campaign asset referenced in the query.
  google.ads.searchads360.v0.resources.CampaignAsset campaign_asset = 142;

  // The campaign asset set referenced in the query.
  google.ads.searchads360.v0.resources.CampaignAssetSet campaign_asset_set =
      181;

  // The campaign audience view referenced in the query.
  google.ads.searchads360.v0.resources.CampaignAudienceView
      campaign_audience_view = 69;

  // The campaign criterion referenced in the query.
  google.ads.searchads360.v0.resources.CampaignCriterion campaign_criterion =
      20;

  // The campaign effective label referenced in the query.
  google.ads.searchads360.v0.resources.CampaignEffectiveLabel
      campaign_effective_label = 229;

  // The campaign label referenced in the query.
  google.ads.searchads360.v0.resources.CampaignLabel campaign_label = 108;

  // The cart data sales view referenced in the query.
  google.ads.searchads360.v0.resources.CartDataSalesView cart_data_sales_view =
      221;

  // The Audience referenced in the query.
  google.ads.searchads360.v0.resources.Audience audience = 190;

  // The conversion action referenced in the query.
  google.ads.searchads360.v0.resources.ConversionAction conversion_action = 103;

  // The conversion custom variable referenced in the query.
  google.ads.searchads360.v0.resources.ConversionCustomVariable
      conversion_custom_variable = 153;

  // The customer referenced in the query.
  google.ads.searchads360.v0.resources.Customer customer = 1;

  // The customer asset referenced in the query.
  google.ads.searchads360.v0.resources.CustomerAsset customer_asset = 155;

  // The customer asset set referenced in the query.
  google.ads.searchads360.v0.resources.CustomerAssetSet customer_asset_set =
      195;

  // The accessible bidding strategy referenced in the query.
  google.ads.searchads360.v0.resources.AccessibleBiddingStrategy
      accessible_bidding_strategy = 169;

  // The CustomerManagerLink referenced in the query.
  google.ads.searchads360.v0.resources.CustomerManagerLink
      customer_manager_link = 61;

  // The CustomerClient referenced in the query.
  google.ads.searchads360.v0.resources.CustomerClient customer_client = 70;

  // The dynamic search ads search term view referenced in the query.
  google.ads.searchads360.v0.resources.DynamicSearchAdsSearchTermView
      dynamic_search_ads_search_term_view = 106;

  // The gender view referenced in the query.
  google.ads.searchads360.v0.resources.GenderView gender_view = 40;

  // The geo target constant referenced in the query.
  google.ads.searchads360.v0.resources.GeoTargetConstant geo_target_constant =
      23;

  // The keyword view referenced in the query.
  google.ads.searchads360.v0.resources.KeywordView keyword_view = 21;

  // The label referenced in the query.
  google.ads.searchads360.v0.resources.Label label = 52;

  // The language constant referenced in the query.
  google.ads.searchads360.v0.resources.LanguageConstant language_constant = 55;

  // The location view referenced in the query.
  google.ads.searchads360.v0.resources.LocationView location_view = 123;

  // The Product Bidding Category referenced in the query.
  google.ads.searchads360.v0.resources.ProductBiddingCategoryConstant
      product_bidding_category_constant = 109;

  // The product group view referenced in the query.
  google.ads.searchads360.v0.resources.ProductGroupView product_group_view = 54;

  // The shopping performance view referenced in the query.
  google.ads.searchads360.v0.resources.ShoppingPerformanceView
      shopping_performance_view = 117;

  // The user list referenced in the query.
  google.ads.searchads360.v0.resources.UserList user_list = 38;

  // The user location view referenced in the query.
  google.ads.searchads360.v0.resources.UserLocationView user_location_view =
      135;

  // The webpage view referenced in the query.
  google.ads.searchads360.v0.resources.WebpageView webpage_view = 162;

  // The event level visit referenced in the query.
  google.ads.searchads360.v0.resources.Visit visit = 203;

  // The event level conversion referenced in the query.
  google.ads.searchads360.v0.resources.Conversion conversion = 206;

  // The metrics.
  google.ads.searchads360.v0.common.Metrics metrics = 4;

  // The segments.
  google.ads.searchads360.v0.common.Segments segments = 102;

  // The custom columns.
  repeated google.ads.searchads360.v0.common.Value custom_columns = 156;
}

// Message for custom column header.
message CustomColumnHeader {
  // The custom column ID.
  int64 id = 1;

  // The user defined name of the custom column.
  string name = 2;

  // True when the custom column references metrics.
  bool references_metrics = 3;
}

// Message for conversion custom metric header.
message ConversionCustomMetricHeader {
  // The conversion custom metric ID.
  int64 id = 1;

  // The user defined name of the conversion custom metric.
  string name = 2;
}

// Message for conversion custom dimension header.
message ConversionCustomDimensionHeader {
  // The conversion custom dimension ID.
  int64 id = 1;

  // The user defined name of the conversion custom dimension.
  string name = 2;
}

// Message for raw event conversion metric header.
message RawEventConversionMetricHeader {
  // The conversion custom variable ID.
  int64 id = 1;

  // The user defined name of the raw event metric.
  string name = 2;
}

// Message for raw event conversion dimension header.
message RawEventConversionDimensionHeader {
  // The conversion custom variable ID.
  int64 id = 1;

  // The user defined name of the raw event dimension.
  string name = 2;
}
