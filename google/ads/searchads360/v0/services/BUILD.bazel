# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "services_proto",
    srcs = glob(["*.proto"]),
    deps = [
        "//google/ads/searchads360/v0/common:common_proto",
        "//google/ads/searchads360/v0/enums:enums_proto",
        "//google/ads/searchads360/v0/resources:resources_proto",
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "services_java_proto",
    deps = [":services_proto"],
)

java_grpc_library(
    name = "services_java_grpc",
    srcs = [":services_proto"],
    deps = [":services_java_proto"],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "services_moved_proto",
    srcs = [":services_proto"],
    deps = [
        "//google/ads/searchads360/v0/common:common_proto",
        "//google/ads/searchads360/v0/enums:enums_proto",
        "//google/ads/searchads360/v0/resources:resources_proto",
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

py_proto_library(
    name = "services_py_proto",
    deps = [":services_moved_proto"],
)

py_grpc_library(
    name = "services_py_grpc",
    srcs = [":services_moved_proto"],
    deps = [":services_py_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "services_csharp_proto",
    deps = [":services_proto"],
)

csharp_grpc_library(
    name = "services_csharp_grpc",
    srcs = [":services_proto"],
    deps = [":services_csharp_proto"],
)
