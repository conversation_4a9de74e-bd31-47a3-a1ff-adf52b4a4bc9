// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.services;

import "google/ads/searchads360/v0/resources/custom_column.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomColumnServiceProto";
option java_package = "com.google.ads.searchads360.v0.services";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Services";
option ruby_package = "Google::Ads::SearchAds360::V0::Services";

// Proto file describing the Custom Column service.

// Service to manage custom columns.
service CustomColumnService {
  option (google.api.default_host) = "searchads360.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/doubleclicksearch";

  // Returns the requested custom column in full detail.
  rpc GetCustomColumn(GetCustomColumnRequest)
      returns (google.ads.searchads360.v0.resources.CustomColumn) {
    option (google.api.http) = {
      get: "/v0/{resource_name=customers/*/customColumns/*}"
    };
    option (google.api.method_signature) = "resource_name";
  }

  // Returns all the custom columns associated with the customer in full detail.
  rpc ListCustomColumns(ListCustomColumnsRequest)
      returns (ListCustomColumnsResponse) {
    option (google.api.http) = {
      get: "/v0/customers/{customer_id=*}/customColumns"
    };
    option (google.api.method_signature) = "customer_id";
  }
}

// Request message for
// [CustomColumnService.GetCustomColumn][google.ads.searchads360.v0.services.CustomColumnService.GetCustomColumn].
message GetCustomColumnRequest {
  // Required. The resource name of the custom column to fetch.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CustomColumn"
    }
  ];
}

// Request message for
// [CustomColumnService.ListCustomColumns][google.ads.searchads360.v0.services.CustomColumnService.ListCustomColumns]
message ListCustomColumnsRequest {
  // Required. The ID of the customer to apply the CustomColumn list operation
  // to.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for fetching all custom columns associated with a customer.
message ListCustomColumnsResponse {
  // The CustomColumns owned by the provided customer.
  repeated google.ads.searchads360.v0.resources.CustomColumn custom_columns = 1;
}
