# This build file includes a target for the Ruby wrapper library for
# google-ads-ad_manager.

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

# Generates a Ruby wrapper client for admanager.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "admanager_ruby_wrapper",
    srcs = ["//google/ads/admanager/v1:admanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-ads-ad_manager",
        "ruby-cloud-wrapper-of=v1:0.0",
    ],
    service_yaml = "//google/ads/admanager/v1:admanager_v1.yaml",
    transport = "rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-ads-ad_manager-ruby",
    deps = [
        ":admanager_ruby_wrapper",
    ],
)
