// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "TaxonomyTypeEnumProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper for
// [TaxonomyType][google.ads.admanager.v1.TaxonomyTypeEnum.TaxonomyType]
message TaxonomyTypeEnum {
  // The taxonomy type of the IAB defined taxonomies.
  // Used for Publisher provided signals.
  enum TaxonomyType {
    // Unspecified/not present
    TAXONOMY_TYPE_UNSPECIFIED = 0;

    // The IAB Audience Taxonomy v1.1.
    TAXONOMY_IAB_AUDIENCE_1_1 = 3;

    // The IAB Content Taxonomy v2.1.
    TAXONOMY_IAB_CONTENT_2_1 = 4;

    // The IAB Content Taxonomy v2.2.
    TAXONOMY_IAB_CONTENT_2_2 = 6;

    // The IAB Content Taxonomy v3.0.
    TAXONOMY_IAB_CONTENT_3_0 = 5;

    // The PPS structured video signals taxonomy.
    TAXONOMY_GOOGLE_STRUCTURED_VIDEO_1_0 = 7;
  }
}
