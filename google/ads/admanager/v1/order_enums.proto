// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "OrderEnumsProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper message for
// [OrderStatus][google.ads.admanager.v1.OrderStatusEnum.OrderStatus].
message OrderStatusEnum {
  // The status of an Order.
  enum OrderStatus {
    // Default value. This value is unused.
    ORDER_STATUS_UNSPECIFIED = 0;

    // Indicates that the Order has just been created but no approval has been
    // requested yet.
    DRAFT = 2;

    // Indicates that a request for approval for the Order has been made.
    PENDING_APPROVAL = 3;

    // Indicates that the Order has been approved and is ready to serve.
    APPROVED = 4;

    // Indicates that the Order has been disapproved and is not eligible to
    // serve.
    DISAPPROVED = 5;

    // This is a legacy state. Paused status should be checked on LineItems
    // within the order.
    PAUSED = 6;

    // Indicates that the Order has been canceled and cannot serve.
    CANCELED = 7;

    // Indicates that the Order has been deleted.
    DELETED = 8;
  }
}
