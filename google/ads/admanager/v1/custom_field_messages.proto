// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/custom_field_enums.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "CustomFieldMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// An additional, user-created field on an entity.
message CustomField {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/CustomField"
    pattern: "networks/{network_code}/customFields/{custom_field}"
    plural: "customFields"
    singular: "customField"
  };

  // Identifier. The resource name of the `CustomField`.
  // Format: `networks/{network_code}/customFields/{custom_field_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Unique ID of the CustomField. This value is readonly and is
  // assigned by Google.
  int64 custom_field_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Name of the CustomField. The max length is 127 characters.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A description of the custom field. The maximum length is 511
  // characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The status of the `CustomField`.
  CustomFieldStatusEnum.CustomFieldStatus status = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type of entity the `CustomField` can be applied to.
  CustomFieldEntityTypeEnum.CustomFieldEntityType entity_type = 7
      [(google.api.field_behavior) = REQUIRED];

  // Required. The data type of the `CustomField`.
  CustomFieldDataTypeEnum.CustomFieldDataType data_type = 8
      [(google.api.field_behavior) = REQUIRED];

  // Required. The visibility of the `CustomField`.
  CustomFieldVisibilityEnum.CustomFieldVisibility visibility = 9
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The drop-down options for the `CustomField`.
  //
  // Only applicable for `CustomField` with the drop-down data type.
  repeated CustomFieldOption options = 10
      [(google.api.field_behavior) = OPTIONAL];
}

// An option for a drop-down `CustomField`.
message CustomFieldOption {
  // Output only. `CustomFieldOption` ID.
  int64 custom_field_option_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the `CustomFieldOption`.
  //
  // This value has a maximum length of 127 characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];
}
