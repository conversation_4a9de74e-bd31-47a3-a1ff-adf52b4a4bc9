// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "TeamMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// A Team defines a grouping of users and what entities they have access to.
message Team {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/Team"
    pattern: "networks/{network_code}/teams/{team}"
    plural: "teams"
    singular: "team"
  };

  // Identifier. The resource name of the `Team`.
  // Format: `networks/{network_code}/teams/{team_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The unique ID of the Team. This value is assigned by Google.
  // Teams that are created by Google will have negative IDs.
  int64 team_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
