// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/placement_enums.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "PlacementMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The `Placement` resource.
message Placement {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/Placement"
    pattern: "networks/{network_code}/placements/{placement}"
    plural: "placements"
    singular: "placement"
  };

  // Identifier. The resource name of the `Placement`.
  // Format: `networks/{network_code}/placements/{placement_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. `Placement` ID.
  int64 placement_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the placement. Its maximum length is 255
  // characters.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A description of the Placement. This value is optional and its
  // maximum length is 65,535 characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A string used to uniquely identify the Placement for purposes
  // of serving the ad. This attribute is read-only and is assigned by Google
  // when a placement is created.
  string placement_code = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the Placement. This attribute is read-only.
  PlacementStatusEnum.PlacementStatus status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The resource names of AdUnits that constitute the Placement.
  // Format: "networks/{network_code}/adUnits/{ad_unit_id}"
  repeated string targeted_ad_units = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/AdUnit"
    }
  ];

  // Output only. The instant this Placement was last modified.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
