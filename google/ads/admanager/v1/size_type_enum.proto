// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "SizeTypeEnumProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper message for
// [SizeType][google.ads.admanager.v1.SizeTypeEnum.SizeType].
message SizeTypeEnum {
  // The different Size types for an ad.
  enum SizeType {
    // Default value. This value is unused.
    SIZE_TYPE_UNSPECIFIED = 0;

    // Dimension based size, an actual height and width in pixels.
    PIXEL = 1;

    // Size is expressed as a ratio. For example, 4:1 could be
    // met by a 100 x 25 sized image.
    ASPECT_RATIO = 2;

    // Out-of-page (Interstitial) size that is not related to the slot it is
    // served. This must be used with 1x1 size.
    INTERSTITIAL = 3;

    // Size is ignored. This must be used with 1x1
    // size.
    IGNORED = 4;

    // Native size, which is a function of the how the client renders the
    // creative. This must be used with 1x1 size.
    NATIVE = 5;

    // Fluid size. Automatically sizes the ad by filling the width of the
    // enclosing column and adjusting the height as appropriate. This must be
    // used with 1x1 size.
    FLUID = 6;

    // Audio size. Used with audio ads. This must be used with 1x1 size.
    AUDIO = 7;
  }
}
