// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/custom_targeting_key_enums.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "CustomTargetingKeyMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The `CustomTargetingKey` resource.
message CustomTargetingKey {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/CustomTargetingKey"
    pattern: "networks/{network_code}/customTargetingKeys/{custom_targeting_key}"
    plural: "customTargetingKeys"
    singular: "customTargetingKey"
  };

  // Identifier. The resource name of the `CustomTargetingKey`.
  // Format:
  // `networks/{network_code}/customTargetingKeys/{custom_targeting_key_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. `CustomTargetingKey` ID.
  int64 custom_targeting_key_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Name of the key. Keys can contain up to 10 characters each. You
  // can use alphanumeric characters and symbols other than the following:
  // ", ', =, !, +, #, *, ~, ;, ^, (, ), <, >, [, ], the white space character.
  string ad_tag_name = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Optional. Descriptive name for the `CustomTargetingKey`.
  string display_name = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Indicates whether users will select from predefined values or
  // create new targeting values, while specifying targeting criteria for a line
  // item.
  CustomTargetingKeyTypeEnum.CustomTargetingKeyType type = 5
      [(google.api.field_behavior) = REQUIRED];

  // Output only. Status of the `CustomTargetingKey`.
  CustomTargetingKeyStatusEnum.CustomTargetingKeyStatus status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Reportable state of the `CustomTargetingKey`.
  CustomTargetingKeyReportableTypeEnum.CustomTargetingKeyReportableType
      reportable_type = 7 [(google.api.field_behavior) = REQUIRED];
}
