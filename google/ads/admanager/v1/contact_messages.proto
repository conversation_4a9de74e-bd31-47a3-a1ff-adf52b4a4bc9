// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "ContactMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// A contact represents a person who is affiliated with a single company. A
// contact can have a variety of contact information associated to it, and can
// be invited to view their company's orders, line items, creatives, and
// reports.
message Contact {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/Contact"
    pattern: "networks/{network_code}/contacts/{contact}"
    plural: "contacts"
    singular: "contact"
  };

  // Identifier. The resource name of the `Contact`.
  // Format: `networks/{network_code}/contacts/{contact_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The unique ID of the contact. This value is readonly and is
  // assigned by Google.
  int64 contact_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
