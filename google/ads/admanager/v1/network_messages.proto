// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "NetworkMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The Network resource.
message Network {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/Network"
    pattern: "networks/{network_code}"
    plural: "networks"
    singular: "network"
  };

  // Identifier. The resource name of the Network.
  // Format: networks/{network_code}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Display name for Network.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Network Code.
  string network_code = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Property code.
  string property_code = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time zone associated with the delivery of orders and
  // reporting.
  string time_zone = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Primary currency code, in ISO-4217 format.
  string currency_code = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Currency codes that can be used as an alternative to the primary
  // currency code for trafficking Line Items.
  repeated string secondary_currency_codes = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Top most [Ad Unit](google.ads.admanager.v1.AdUnit) to which
  // descendant Ad Units can be added.
  // Format: networks/{network_code}/adUnit/{ad_unit_id}
  string effective_root_ad_unit = 8 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/AdUnit"
    }
  ];

  // Output only. Whether this is a test network.
  bool test_network = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Network ID.
  int64 network_id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}
