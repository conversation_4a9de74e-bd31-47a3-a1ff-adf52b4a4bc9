// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/taxonomy_type_enum.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "TaxonomyCategoryMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The `TaxonomyCategory` resource.
message TaxonomyCategory {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/TaxonomyCategory"
    pattern: "networks/{network_code}/taxonomyCategories/{taxonomy_category}"
    plural: "taxonomyCategories"
    singular: "taxonomyCategory"
  };

  // Identifier. The resource name of the `TaxonomyCategory`.
  // Format: `networks/{network_code}/taxonomyCategories/{taxonomy_category_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. `TaxonomyCategory` ID.
  int64 taxonomy_category_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Display name of the `TaxonomyCategory`.
  string display_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether this `TaxonomyCategory` only serves to group its
  // children.
  bool grouping_only = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the parent category this `TaxonomyCategory` descends
  // from.
  int64 parent_taxonomy_category_id = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The taxonomy that this `TaxonomyCategory` belongs to.
  TaxonomyTypeEnum.TaxonomyType taxonomy_type = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of names of the ancestors of this `TaxonomyCategory`.
  repeated string ancestor_names = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of IDs of the ancestors of this `TaxonomyCategory`.
  repeated int64 ancestor_taxonomy_category_ids = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
