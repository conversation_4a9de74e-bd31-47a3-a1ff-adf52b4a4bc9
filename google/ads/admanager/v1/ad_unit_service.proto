// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/ad_unit_messages.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "AdUnitServiceProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Provides methods for handling AdUnit objects.
service AdUnitService {
  option (google.api.default_host) = "admanager.googleapis.com";

  // API to retrieve an AdUnit object.
  rpc GetAdUnit(GetAdUnitRequest) returns (AdUnit) {
    option (google.api.http) = {
      get: "/v1/{name=networks/*/adUnits/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // API to retrieve a list of AdUnit objects.
  rpc ListAdUnits(ListAdUnitsRequest) returns (ListAdUnitsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=networks/*}/adUnits"
    };
    option (google.api.method_signature) = "parent";
  }

  // API to retrieve a list of AdUnitSize objects.
  rpc ListAdUnitSizes(ListAdUnitSizesRequest)
      returns (ListAdUnitSizesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=networks/*}/adUnitSizes"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request object for GetAdUnit method.
message GetAdUnitRequest {
  // Required. The resource name of the AdUnit.
  // Format: `networks/{network_code}/adUnits/{ad_unit_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/AdUnit"
    }
  ];
}

// Request object for ListAdUnits method.
message ListAdUnitsRequest {
  // Required. The parent, which owns this collection of AdUnits.
  // Format: `networks/{network_code}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Network"
    }
  ];

  // Optional. The maximum number of AdUnits to return. The service may return
  // fewer than this value. If unspecified, at most 50 ad units will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListAdUnits` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAdUnits` must match
  // the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to filter the response.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to specify sorting order.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters#order
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of individual resources to skip while paginating.
  int32 skip = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for ListAdUnitsRequest containing matching AdUnit resources.
message ListAdUnitsResponse {
  // The AdUnit from the specified network.
  repeated AdUnit ad_units = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Total number of AdUnits.
  // If a filter was included in the request, this reflects the total number
  // after the filtering is applied.
  //
  // `total_size` will not be calculated in the response unless it has been
  // included in a response field mask. The response field mask can be provided
  // to the method by using the URL parameter `$fields` or `fields`, or by using
  // the HTTP/gRPC header `X-Goog-FieldMask`.
  //
  // For more information, see
  // https://developers.google.com/ad-manager/api/beta/field-masks
  int32 total_size = 3;
}

// Request object for ListAdUnitSizes method.
message ListAdUnitSizesRequest {
  // Required. The parent, which owns this collection of AdUnitSizes.
  // Format: `networks/{network_code}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Network"
    }
  ];

  // Optional. The maximum number of AdUnitSizes to return. The service may
  // return fewer than this value. If unspecified, at most 50 ad unit sizes will
  // be returned. The maximum value is 1000; values above 1000 will be coerced
  // to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListAdUnitSizes` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAdUnitSizes` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to filter the response.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to specify sorting order.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters#order
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of individual resources to skip while paginating.
  int32 skip = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for ListAdUnitSizesRequest containing matching AdUnitSizes.
message ListAdUnitSizesResponse {
  // The AdUnitSizes from the specified network.
  repeated AdUnitSize ad_unit_sizes = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Total number of AdUnitSizes.
  // If a filter was included in the request, this reflects the total number
  // after the filtering is applied.
  //
  // `total_size` will not be calculated in the response unless it has been
  // included in a response field mask. The response field mask can be provided
  // to the method by using the URL parameter `$fields` or `fields`, or by using
  // the HTTP/gRPC header `X-Goog-FieldMask`.
  //
  // For more information, see
  // https://developers.google.com/ad-manager/api/beta/field-masks
  int32 total_size = 3;
}
