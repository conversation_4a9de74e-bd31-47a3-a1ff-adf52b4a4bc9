// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "TimeUnitEnumProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper message for TimeUnit.
message TimeUnitEnum {
  // Unit of time for the frequency cap.
  enum TimeUnit {
    // Default value. This value is unused.
    TIME_UNIT_UNSPECIFIED = 0;

    // Minute
    MINUTE = 1;

    // Hour
    HOUR = 2;

    // Day
    DAY = 3;

    // Week
    WEEK = 4;

    // Month
    MONTH = 5;

    // Lifetime
    LIFETIME = 6;

    // Per pod of ads in a video stream. Only valid for entities in a
    // VIDEO_PLAYER environment.
    POD = 7;

    // Per video stream. Only valid for entities in a VIDEO_PLAYER environment.
    STREAM = 8;
  }
}
