// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/role_enums.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "RoleMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The `Role` resource.
message Role {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/Role"
    pattern: "networks/{network_code}/roles/{role}"
    plural: "roles"
    singular: "role"
  };

  // Identifier. The resource name of the `Role`.
  // Format: `networks/{network_code}/roles/{role_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. `Role` ID.
  int64 role_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the `Role`.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the `Role`.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether the `Role` is a built-in or custom user role.
  bool built_in = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the `Role`.
  RoleStatusEnum.RoleStatus status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
