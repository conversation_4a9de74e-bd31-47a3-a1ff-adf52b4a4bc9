// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/order_messages.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "OrderServiceProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Provides methods for handling `Order` objects.
service OrderService {
  option (google.api.default_host) = "admanager.googleapis.com";

  // API to retrieve an Order object.
  rpc GetOrder(GetOrderRequest) returns (Order) {
    option (google.api.http) = {
      get: "/v1/{name=networks/*/orders/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // API to retrieve a list of `Order` objects.
  //
  // Fields used for literal matching in filter string:
  // * `order_id`
  // * `display_name`
  // * `external_order_id`
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=networks/*}/orders"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request object for `GetOrder` method.
message GetOrderRequest {
  // Required. The resource name of the Order.
  // Format: `networks/{network_code}/orders/{order_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/Order" }
  ];
}

// Request object for `ListOrders` method.
message ListOrdersRequest {
  // Required. The parent, which owns this collection of Orders.
  // Format: `networks/{network_code}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Network"
    }
  ];

  // Optional. The maximum number of `Orders` to return. The service may return
  // fewer than this value. If unspecified, at most 50 `Orders` will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListOrders` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListOrders` must match
  // the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to filter the response.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to specify sorting order.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters#order
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of individual resources to skip while paginating.
  int32 skip = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for `ListOrdersRequest` containing matching `Order`
// resources.
message ListOrdersResponse {
  // The `Order` from the specified network.
  repeated Order orders = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Total number of `Orders`.
  // If a filter was included in the request, this reflects the total number
  // after the filtering is applied.
  //
  // `total_size` will not be calculated in the response unless it has been
  // included in a response field mask. The response field mask can be provided
  // to the method by using the URL parameter `$fields` or `fields`, or by using
  // the HTTP/gRPC header `X-Goog-FieldMask`.
  //
  // For more information, see
  // https://developers.google.com/ad-manager/api/beta/field-masks
  int32 total_size = 3;
}
