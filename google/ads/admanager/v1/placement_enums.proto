// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "PlacementEnumsProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper message for
// [PlacementStatus][google.ads.admanager.v1.PlacementStatusEnum.PlacementStatus]
message PlacementStatusEnum {
  // Status of the placement.
  enum PlacementStatus {
    // Not specified value.
    PLACEMENT_STATUS_UNSPECIFIED = 0;

    // Stats are collected, user-visible.
    ACTIVE = 1;

    // No stats collected, not user-visible.
    INACTIVE = 2;

    // No stats collected, user-visible.
    ARCHIVED = 3;
  }
}
