// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/custom_targeting_value_enums.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "CustomTargetingValueMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The `CustomTargetingValue` resource.
message CustomTargetingValue {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/CustomTargetingValue"
    pattern: "networks/{network_code}/customTargetingKeys/{custom_targeting_key}/customTargetingValues/{custom_targeting_value}"
    plural: "customTargetingValues"
    singular: "customTargetingValue"
  };

  // Identifier. The resource name of the `CustomTargetingValue`.
  // Format:
  // `networks/{network_code}/customTargetingKeys/{custom_targeting_key_id}/customTargetingValues/{custom_targeting_value_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Immutable. Name of the `CustomTargetingValue`. Values can contain up to 40
  // characters each. You can use alphanumeric characters and symbols other than
  // the following: ", ', =, !, +, #, *, ~, ;, ^, (, ), <, >, [, ]. Values are
  // not data-specific; all values are treated as strings. For example, instead
  // of using "age>=18 AND <=34", try "18-34"
  string ad_tag_name = 4 [(google.api.field_behavior) = IMMUTABLE];

  // Optional. Descriptive name for the `CustomTargetingValue`.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. The way in which the CustomTargetingValue.name strings
  // will be matched.
  CustomTargetingValueMatchTypeEnum.CustomTargetingValueMatchType match_type = 6
      [
        (google.api.field_behavior) = IMMUTABLE,
        (google.api.field_behavior) = REQUIRED
      ];

  // Output only. Status of the `CustomTargetingValue`.
  CustomTargetingValueStatusEnum.CustomTargetingValueStatus status = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
