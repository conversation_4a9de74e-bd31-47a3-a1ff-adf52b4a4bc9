// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/entity_signals_mapping_messages.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "EntitySignalsMappingServiceProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Provides methods for handling `EntitySignalsMapping` objects.
service EntitySignalsMappingService {
  option (google.api.default_host) = "admanager.googleapis.com";

  // API to retrieve a `EntitySignalsMapping` object.
  rpc GetEntitySignalsMapping(GetEntitySignalsMappingRequest)
      returns (EntitySignalsMapping) {
    option (google.api.http) = {
      get: "/v1/{name=networks/*/entitySignalsMappings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // API to retrieve a list of `EntitySignalsMapping` objects.
  rpc ListEntitySignalsMappings(ListEntitySignalsMappingsRequest)
      returns (ListEntitySignalsMappingsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=networks/*}/entitySignalsMappings"
    };
    option (google.api.method_signature) = "parent";
  }

  // API to create an `EntitySignalsMapping` object.
  rpc CreateEntitySignalsMapping(CreateEntitySignalsMappingRequest)
      returns (EntitySignalsMapping) {
    option (google.api.http) = {
      post: "/v1/{parent=networks/*}/entitySignalsMappings"
      body: "entity_signals_mapping"
    };
    option (google.api.method_signature) = "parent,entity_signals_mapping";
  }

  // API to update an `EntitySignalsMapping` object.
  rpc UpdateEntitySignalsMapping(UpdateEntitySignalsMappingRequest)
      returns (EntitySignalsMapping) {
    option (google.api.http) = {
      patch: "/v1/{entity_signals_mapping.name=networks/*/entitySignalsMappings/*}"
      body: "entity_signals_mapping"
    };
    option (google.api.method_signature) = "entity_signals_mapping,update_mask";
  }

  // API to batch create `EntitySignalsMapping` objects.
  rpc BatchCreateEntitySignalsMappings(BatchCreateEntitySignalsMappingsRequest)
      returns (BatchCreateEntitySignalsMappingsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=networks/*}/entitySignalsMappings:batchCreate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
  }

  // API to batch update `EntitySignalsMapping` objects.
  rpc BatchUpdateEntitySignalsMappings(BatchUpdateEntitySignalsMappingsRequest)
      returns (BatchUpdateEntitySignalsMappingsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=networks/*}/entitySignalsMappings:batchUpdate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
  }
}

// Request object for `GetEntitySignalsMapping` method.
message GetEntitySignalsMappingRequest {
  // Required. The resource name of the EntitySignalsMapping.
  // Format:
  // `networks/{network_code}/entitySignalsMappings/{entity_signals_mapping_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/EntitySignalsMapping"
    }
  ];
}

// Request object for `ListEntitySignalsMappings` method.
message ListEntitySignalsMappingsRequest {
  // Required. The parent, which owns this collection of EntitySignalsMappings.
  // Format: `networks/{network_code}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Network"
    }
  ];

  // Optional. The maximum number of `EntitySignalsMappings` to return. The
  // service may return fewer than this value. If unspecified, at most 50
  // `EntitySignalsMappings` will be returned. The maximum value is 1000; values
  // above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListEntitySignalsMappings` call. Provide this to retrieve the subsequent
  // page.
  //
  // When paginating, all other parameters provided to
  // `ListEntitySignalsMappings` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to filter the response.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to specify sorting order.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters#order
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of individual resources to skip while paginating.
  int32 skip = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request object for 'CreateEntitySignalsMapping' method.
message CreateEntitySignalsMappingRequest {
  // Required. The parent resource where this EntitySignalsMapping will be
  // created. Format: `networks/{network_code}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "admanager.googleapis.com/EntitySignalsMapping"
    }
  ];

  // Required. The EntitySignalsMapping object to create.
  EntitySignalsMapping entity_signals_mapping = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request object for 'UpdateEntitySignalsMapping' method.
message UpdateEntitySignalsMappingRequest {
  // Required. The `EntitySignalsMapping` to update.
  //
  // The EntitySignalsMapping's name is used to identify the
  // EntitySignalsMapping to update.
  // Format:
  // `networks/{network_code}/entitySignalsMappings/{entity_signals_mapping}`
  EntitySignalsMapping entity_signals_mapping = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response object for `ListEntitySignalsMappingsRequest` containing matching
// `EntitySignalsMapping` resources.
message ListEntitySignalsMappingsResponse {
  // The `EntitySignalsMapping` from the specified network.
  repeated EntitySignalsMapping entity_signals_mappings = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Total number of `EntitySignalsMappings`.
  // If a filter was included in the request, this reflects the total number
  // after the filtering is applied.
  //
  // `total_size` will not be calculated in the response unless it has been
  // included in a response field mask. The response field mask can be provided
  // to the method by using the URL parameter `$fields` or `fields`, or by using
  // the HTTP/gRPC header `X-Goog-FieldMask`.
  //
  // For more information, see
  // https://developers.google.com/ad-manager/api/beta/field-masks
  int32 total_size = 3;
}

// Request object for `BatchCreateEntitySignalsMappings` method.
message BatchCreateEntitySignalsMappingsRequest {
  // Required. The parent resource where `EntitySignalsMappings` will be
  // created. Format: `networks/{network_code}` The parent field in the
  // CreateEntitySignalsMappingRequest must match this field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "admanager.googleapis.com/EntitySignalsMapping"
    }
  ];

  // Required. The `EntitySignalsMapping` objects to create.
  // A maximum of 100 objects can be created in a batch.
  repeated CreateEntitySignalsMappingRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response object for `BatchCreateEntitySignalsMappings` method.
message BatchCreateEntitySignalsMappingsResponse {
  // The `EntitySignalsMapping` objects created.
  repeated EntitySignalsMapping entity_signals_mappings = 1;
}

// Request object for `BatchUpdateEntitySignalsMappings` method.
message BatchUpdateEntitySignalsMappingsRequest {
  // Required. The parent resource where `EntitySignalsMappings` will be
  // updated. Format: `networks/{network_code}` The parent field in the
  // UpdateEntitySignalsMappingRequest must match this field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "admanager.googleapis.com/EntitySignalsMapping"
    }
  ];

  // Required. The `EntitySignalsMapping` objects to update.
  // A maximum of 100 objects can be updated in a batch.
  repeated UpdateEntitySignalsMappingRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response object for `BatchUpdateEntitySignalsMappings` method.
message BatchUpdateEntitySignalsMappingsResponse {
  // The `EntitySignalsMapping` objects updated.
  repeated EntitySignalsMapping entity_signals_mappings = 1;
}
