// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "CustomFieldEnumsProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper message for
// [CustomFieldDataType][google.ads.admanager.v1.CustomFieldDataTypeEnum.CustomFieldDataType]
message CustomFieldDataTypeEnum {
  // The data type for a CustomField.
  enum CustomFieldDataType {
    // No value specified
    CUSTOM_FIELD_DATA_TYPE_UNSPECIFIED = 0;

    // A string field
    //
    // The max length is 255 characters.
    STRING = 1;

    // A number field.
    NUMBER = 2;

    // A "Yes" or "No" toggle field.
    TOGGLE = 3;

    // A drop-down field.
    DROP_DOWN = 4;
  }
}

// Wrapper message for
// [CustomFieldEntityType][google.ads.admanager.v1.CustomFieldEntityTypeEnum.CustomFieldEntityType]
message CustomFieldEntityTypeEnum {
  // The types of entities that a CustomField can be applied to.
  enum CustomFieldEntityType {
    // No value specified
    CUSTOM_FIELD_ENTITY_TYPE_UNSPECIFIED = 0;

    // The CustomField is applied to LineItems.
    LINE_ITEM = 1;

    // The CustomField is applied to Orders.
    ORDER = 2;

    // The CustomField is applied to Creatives.
    CREATIVE = 3;

    // The CustomField is applied to Proposals.
    PROPOSAL = 4;

    // The CustomField is applied to ProposalLineItems.
    PROPOSAL_LINE_ITEM = 5;
  }
}

// Wrapper message for
// [CustomFieldStatus][google.ads.admanager.v1.CustomFieldStatusEnum.CustomFieldStatus]
message CustomFieldStatusEnum {
  // The status of the CustomField.
  enum CustomFieldStatus {
    // No value specified
    CUSTOM_FIELD_STATUS_UNSPECIFIED = 0;

    // The CustomField is active.
    ACTIVE = 1;

    // The CustomField is inactive.
    INACTIVE = 2;
  }
}

// Wrapper message for
// [CustomFieldVisibility][google.ads.admanager.v1.CustomFieldVisibilityEnum.CustomFieldVisibility]
message CustomFieldVisibilityEnum {
  // The visibility level of a CustomField.
  enum CustomFieldVisibility {
    // No value specified
    CUSTOM_FIELD_VISIBILITY_UNSPECIFIED = 0;

    // The CustomField is not visible in the UI and only visible through the
    // API.
    HIDDEN = 1;

    // The CustomField is visible in the UI and only editable through the API.
    READ_ONLY = 2;

    // The CustomField is visible and editable in both the API and UI.
    EDITABLE = 3;
  }
}
