// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/protobuf/any.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "AdManagerErrorProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// / AdManagerError contains all the information required for processing a
// / particular error thrown by the AdManager API.
// /
// / At least one AdManagerError should be included in all error messages sent
// to / the client.
message AdManagerError {
  // The unique identifying string for this error.
  string error_code = 1;

  // A publisher appropriate explanation of this error.
  string message = 2;

  // The field path that triggered this error.
  string field_path = 3;

  // The value that triggered this error.
  string trigger = 4;

  // The stack trace that accompanies this error.
  string stack_trace = 5;

  // A list of messages that carry any additional error details.
  repeated google.protobuf.Any details = 6;
}
