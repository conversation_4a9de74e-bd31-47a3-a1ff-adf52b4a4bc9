// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/size_type_enum.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "SizeProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Represents the dimensions of an AdUnit, LineItem, or Creative.
message Size {
  // Required. The width of the [Creative](google.ads.admanager.v1.Creative),
  // [AdUnit](google.ads.admanager.v1.AdUnit), or
  // [LineItem](google.ads.admanager.v1.LineItem).
  int32 width = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The height of the [Creative](google.ads.admanager.v1.Creative),
  // [AdUnit](google.ads.admanager.v1.AdUnit), or
  // [LineItem](google.ads.admanager.v1.LineItem).
  int32 height = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The SizeType of the
  // [Creative](google.ads.admanager.v1.Creative),
  // [AdUnit](google.ads.admanager.v1.AdUnit), or
  // [LineItem](google.ads.admanager.v1.LineItem).
  SizeTypeEnum.SizeType size_type = 3 [(google.api.field_behavior) = REQUIRED];
}
