// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/ad_unit_enums.proto";
import "google/ads/admanager/v1/applied_label.proto";
import "google/ads/admanager/v1/environment_type_enum.proto";
import "google/ads/admanager/v1/frequency_cap.proto";
import "google/ads/admanager/v1/size.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "AdUnitMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The AdUnit resource.
message AdUnit {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/AdUnit"
    pattern: "networks/{network_code}/adUnits/{ad_unit}"
    plural: "adUnits"
    singular: "adUnit"
  };

  // Identifier. The resource name of the AdUnit.
  // Format: `networks/{network_code}/adUnits/{ad_unit_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. AdUnit ID.
  int64 ad_unit_id = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The AdUnit's parent. Every ad unit has a parent except
  // for the root ad unit, which is created by Google. Format:
  // "networks/{network_code}/adUnits/{ad_unit_id}"
  string parent_ad_unit = 10 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/AdUnit"
    }
  ];

  // Output only. The path to this AdUnit in the ad unit hierarchy represented
  // as a list from the root to this ad unit's parent. For root ad units, this
  // list is empty.
  repeated AdUnitParent parent_path = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the ad unit. Its maximum length is 255
  // characters.
  string display_name = 9 [(google.api.field_behavior) = REQUIRED];

  // Immutable. A string used to uniquely identify the ad unit for the purposes
  // of serving the ad. This attribute is optional and can be set during ad unit
  // creation. If it is not provided, it will be assigned by Google based on the
  // ad unit ID.
  string ad_unit_code = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The status of this ad unit.  It defaults to ACTIVE.
  AdUnitStatusEnum.AdUnitStatus status = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The target window directly applied to this AdUnit.
  // If this field is not set, this AdUnit uses the target window specified in
  // effectiveTargetWindow.
  TargetWindowEnum.TargetWindow applied_target_window = 44
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Non-empty default. The target window of this AdUnit. This
  // value is inherited from ancestor AdUnits and defaults to TOP if no AdUnit
  // in the hierarchy specifies it.
  TargetWindowEnum.TargetWindow effective_target_window = 45 [
    (google.api.field_behavior) = NON_EMPTY_DEFAULT,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. The resource names of Teams directly applied to this AdUnit.
  // Format: "networks/{network_code}/teams/{team_id}"
  repeated string applied_teams = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/Team" }
  ];

  // Output only. The resource names of all Teams that this AdUnit is on as well
  // as those inherited from parent AdUnits. Format:
  // "networks/{network_code}/teams/{team_id}"
  repeated string teams = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/Team" }
  ];

  // Optional. A description of the ad unit. The maximum length is 65,535
  // characters.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If this field is set to true, then the AdUnit will not be
  // implicitly targeted when its parent is. Traffickers must explicitly
  // target such an AdUnit or else no line items will serve to it. This
  // feature is only available for Ad Manager 360 accounts.
  bool explicitly_targeted = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. This field is set to true if the ad unit has any children.
  bool has_children = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this AdUnit was last modified.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The sizes that can be served inside this ad unit.
  repeated AdUnitSize ad_unit_sizes = 14
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Determines what set top box video on demand channel this ad unit
  // corresponds to in an external set top box ad campaign system.
  string external_set_top_box_channel_id = 17
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The duration after which an Ad Unit will automatically refresh.
  // This is only valid for ad units in mobile apps. If not set, the ad unit
  // will not refresh.
  google.protobuf.Duration refresh_delay = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The set of labels applied directly to this ad unit.
  repeated AppliedLabel applied_labels = 21
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Contains the set of labels applied directly to the ad unit as
  // well as those inherited from the parent ad units. If a label has been
  // negated, only the negated label is returned. This field is readonly and is
  // assigned by Google.
  repeated AppliedLabel effective_applied_labels = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The set of label frequency caps applied directly to this ad unit.
  // There is a limit of 10 label frequency caps per ad unit.
  repeated LabelFrequencyCap applied_label_frequency_caps = 23
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The label frequency caps applied directly to the ad unit as
  // well as those inherited from parent ad units.
  repeated LabelFrequencyCap effective_label_frequency_caps = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The smart size mode for this ad unit. This attribute is optional
  // and defaults to SmartSizeMode.NONE for fixed sizes.
  SmartSizeModeEnum.SmartSizeMode smart_size_mode = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of AdSense enabled directly applied to this ad unit.
  // This attribute is optional and if not specified this ad unit will inherit
  // the value of effectiveAdsenseEnabled from its ancestors.
  optional bool applied_adsense_enabled = 26
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Specifies whether or not the AdUnit is enabled for serving ads
  // from the AdSense content network. This attribute defaults to the ad unit's
  // parent or ancestor's setting if one has been set. If no ancestor of the ad
  // unit has set appliedAdsenseEnabled, the attribute is defaulted to true.
  bool effective_adsense_enabled = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the size, environment, and companions of an ad in an ad unit.
message AdUnitSize {
  // Required. The Size of the AdUnit.
  Size size = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The EnvironmentType of the AdUnit
  EnvironmentTypeEnum.EnvironmentType environment_type = 2
      [(google.api.field_behavior) = REQUIRED];

  // The companions for this ad unit size. Companions are only valid if the
  // environment is
  // [VIDEO_PLAYER][google.ads.admanager.v1.EnvironmentTypeEnum.EnvironmentType].
  repeated Size companions = 3;
}

// The summary of a parent AdUnit.
message AdUnitParent {
  // Output only. The parent of the current AdUnit
  // Format: `networks/{network_code}/adUnits/{ad_unit_id}`
  string parent_ad_unit = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/AdUnit"
    }
  ];

  // Output only. The display name of the parent AdUnit.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A string used to uniquely identify the ad unit for the
  // purposes of serving the ad.
  string ad_unit_code = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Frequency cap using a label.
message LabelFrequencyCap {
  // Required. The label to used for frequency capping.
  // Format: "networks/{network_code}/labels/{label_id}"
  string label = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/Label" }
  ];

  // The frequency cap.
  FrequencyCap frequency_cap = 2;
}
