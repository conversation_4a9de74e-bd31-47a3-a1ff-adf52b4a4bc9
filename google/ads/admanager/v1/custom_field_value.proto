// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "CustomFieldValueProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// A value for a CustomField on a resource.
message CustomFieldValue {
  // Represent custom field value type.
  // Next Id: 5
  message Value {
    // The value of this field.
    oneof value {
      // The custom_field_option_id, if the CustomFieldDataType is DROPDOWN.
      int64 dropdown_value = 1;

      // The value, if the CustomFieldDataType is STRING.
      string string_value = 2;

      // The value, if the CustomFieldDataType is NUMBER.
      double number_value = 3;

      // The value, if the CustomFieldDataType is TOGGLE.
      bool toggle_value = 4;
    }
  }

  // Required. The custom field for which this is a value.
  // Format: "networks/{network_code}/customFields/{custom_field_id}"
  string custom_field = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/CustomField"
    }
  ];

  // Required. A typed value representation of the value.
  optional Value value = 2 [(google.api.field_behavior) = REQUIRED];
}
