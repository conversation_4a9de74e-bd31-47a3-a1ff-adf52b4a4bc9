// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "CustomTargetingKeyEnumsProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper message for
// [CustomTargetingKeyStatus][google.ads.admanager.v1.CustomTargetingKeyStatusEnum.CustomTargetingKeyStatus]
message CustomTargetingKeyStatusEnum {
  // Status of the custom targeting key.
  enum CustomTargetingKeyStatus {
    // Not specified value.
    CUSTOM_TARGETING_KEY_STATUS_UNSPECIFIED = 0;

    // Custom targeting key is active.
    ACTIVE = 1;

    // Custom targeting key is inactive.
    INACTIVE = 2;
  }
}

// Wrapper message for
// [CustomTargetingKeyType][google.ads.admanager.v1.CustomTargetingKeyTypeEnum.CustomTargetingKeyType]
message CustomTargetingKeyTypeEnum {
  // Type of the custom targeting key.
  enum CustomTargetingKeyType {
    // Not specified value.
    CUSTOM_TARGETING_KEY_TYPE_UNSPECIFIED = 0;

    // Key with a fixed set of values.
    PREDEFINED = 1;

    // Key without a fixed set of values
    FREEFORM = 2;
  }
}

// Wrapper message for
// [CustomTargetingKeyReportableType][google.ads.admanager.v1.CustomTargetingKeyReportableTypeEnum.CustomTargetingKeyReportableType]
message CustomTargetingKeyReportableTypeEnum {
  // Reportable type of the custom targeting key.
  enum CustomTargetingKeyReportableType {
    // Not specified value.
    CUSTOM_TARGETING_KEY_REPORTABLE_TYPE_UNSPECIFIED = 0;

    // Not available for reporting in the Ad Manager query tool.
    OFF = 1;

    // Available for reporting in the Ad Manager query tool.
    ON = 2;

    // Custom dimension available for reporting in the AdManager query tool.
    CUSTOM_DIMENSION = 3;
  }
}
