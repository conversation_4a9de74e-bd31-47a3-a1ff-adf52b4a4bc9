// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "ReportServiceProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Provides methods for interacting with reports.
service ReportService {
  option (google.api.default_host) = "admanager.googleapis.com";

  // API to retrieve a `Report` object.
  rpc GetReport(GetReportRequest) returns (Report) {
    option (google.api.http) = {
      get: "/v1/{name=networks/*/reports/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // API to retrieve a list of `Report` objects.
  rpc ListReports(ListReportsRequest) returns (ListReportsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=networks/*}/reports"
    };
    option (google.api.method_signature) = "parent";
  }

  // API to create a `Report` object.
  rpc CreateReport(CreateReportRequest) returns (Report) {
    option (google.api.http) = {
      post: "/v1/{parent=networks/*}/reports"
      body: "report"
    };
    option (google.api.method_signature) = "parent,report";
  }

  // API to update a `Report` object.
  rpc UpdateReport(UpdateReportRequest) returns (Report) {
    option (google.api.http) = {
      patch: "/v1/{report.name=networks/*/reports/*}"
      body: "report"
    };
    option (google.api.method_signature) = "report,update_mask";
  }

  // Initiates the execution of an existing report asynchronously. Users can
  // get the report by polling this operation via
  // `OperationsService.GetOperation`.
  // Poll every 5 seconds initially, with an exponential
  // backoff. Once a report is complete, the operation will contain a
  // `RunReportResponse` in its response field containing a report_result that
  // can be passed to the `FetchReportResultRows` method to retrieve the report
  // data.
  rpc RunReport(RunReportRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=networks/*/reports/*}:run"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "RunReportResponse"
      metadata_type: "RunReportMetadata"
    };
  }

  // Returns the result rows from a completed report.
  // The caller must have previously called `RunReport` and waited for that
  // operation to complete. The rows will be returned according to the order
  // specified by the `sorts` member of the report definition.
  rpc FetchReportResultRows(FetchReportResultRowsRequest)
      returns (FetchReportResultRowsResponse) {
    option (google.api.http) = {
      get: "/v1/{name=networks/*/reports/*/results/*}:fetchRows"
    };
    option (google.api.method_signature) = "name";
  }
}

// The `Report` resource.
message Report {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/Report"
    pattern: "networks/{network_code}/reports/{report}"
    plural: "reports"
    singular: "report"
  };

  // Represents a single value in a report.
  message Value {
    // A list of integer values.
    message IntList {
      // The values
      repeated int64 values = 1;
    }

    // A list of string values.
    message StringList {
      // The values
      repeated string values = 1;
    }

    // The value of the field, depends on the field's data format.
    oneof value {
      // For integer values.
      int64 int_value = 1;

      // For double values.
      double double_value = 2;

      // For string values.
      string string_value = 3;

      // For boolean values.
      bool bool_value = 4;

      // For lists of integer values.
      IntList int_list_value = 6;

      // For lists of string values.
      StringList string_list_value = 7;

      // For bytes values.
      bytes bytes_value = 8;
    }
  }

  // Represents a sorting in a report.
  message Sort {
    // Required. A field (dimension or metric) to sort by.
    Field field = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. The sort order. If true the sort will be descending.
    bool descending = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Use to sort on a specific slice of data.
    optional Slice slice = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. When using time period columns, use this to sort on a specific
    // column.
    optional int32 time_period_index = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Use to specify which metric value type to sort on. Defaults to
    // PRIMARY.
    optional MetricValueType metric_value_type = 5
        [(google.api.field_behavior) = OPTIONAL];
  }

  // A table containing report data including dimension and metric values.
  message DataTable {
    // A row of report data.
    message Row {
      // The order of the dimension values is the same as the order of the
      // dimensions specified in the request.
      repeated Value dimension_values = 1;

      //
      // The length of the metric_value_groups
      // field will be equal to the length of the date_ranges field in the
      // fetch response. The metric_value_groups field is
      // ordered such that each index corresponds to the date_range at the same
      // index. For example, given date_ranges [x, y], metric_value_groups will
      // have a length of two. The first entry in metric_value_groups represents
      // the metrics for date x and the second entry in metric_value_groups
      // represents the metrics for date y.
      repeated MetricValueGroup metric_value_groups = 2;
    }

    // Contains all metric values requested for a single date range and set of
    // column dimension values (returned in the columns field of the response).
    // The order of the metrics in each field corresponds to the order of the
    // metrics specified in the request.
    message MetricValueGroup {
      // Data for the PRIMARY MetricValueType.
      repeated Value primary_values = 1;

      // Data for the PRIMARY_PERCENT_OF_TOTAL MetricValueType.
      repeated Value primary_percent_of_total_values = 2;

      // Data for the COMPARISON MetricValueType.
      repeated Value comparison_values = 3;

      // Data for the COMPARISON_PERCENT_OF_TOTAL MetricValueType.
      repeated Value comparison_percent_of_total_values = 4;

      // Data for the ABSOLUTE_CHANGE MetricValueType.
      repeated Value absolute_change_values = 5;

      // Data for the RELATIVE_CHANGE MetricValueType.
      repeated Value relative_change_values = 6;

      // If true, the flag's conditions are met. If false, the flag's
      // conditions are not met. flag_values has the same length as
      // flags and index i of flag_values represents the flag at index i
      // of flags.
      repeated bool flag_values = 7;
    }
  }

  // A dimension or a metric in a report.
  message Field {
    // Either a dimension or a metric.
    oneof field {
      // The dimension this field represents.
      Dimension dimension = 1;

      // The metric this field represents.
      Metric metric = 2;
    }
  }

  // Use to specify a slice of data.
  //
  // For example, in a report, to focus on just data from the US, specify
  // `COUNTRY_NAME` for dimension and value: `"United States"`.
  message Slice {
    // Required. The dimension to slice on.
    Dimension dimension = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The value of the dimension.
    Value value = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // A filter over one or more fields.
  message Filter {
    // A filter on a specific field.
    message FieldFilter {
      // Required. The field to filter on.
      Field field = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. The operation of this filter.
      Operation operation = 2 [(google.api.field_behavior) = REQUIRED];

      // Required. Values to filter to.
      repeated Value values = 3 [(google.api.field_behavior) = REQUIRED];

      // Optional. Use to filter on a specific slice of data.
      optional Slice slice = 4 [(google.api.field_behavior) = OPTIONAL];

      // Optional. When using time period columns, use this to filter on a
      // specific column.
      optional int32 time_period_index = 5
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Use to specify which metric value type to filter on. Defaults
      // to PRIMARY.
      optional MetricValueType metric_value_type = 6
          [(google.api.field_behavior) = OPTIONAL];
    }

    // A list of filters.
    message FilterList {
      // Required. A list of filters.
      repeated Filter filters = 1 [(google.api.field_behavior) = REQUIRED];
    }

    // Supported filter operations.
    enum Operation {
      // For scalar operands, checks if the operand is in the set of provided
      // filter values.
      //
      // For list operands, checks if any element in the operand is in the set
      // of provided filter values.
      //
      // Default value.
      IN = 0;

      // For scalar operands, checks that the operand is not in the set of
      // provided filter values.
      //
      // For list operands, checks that none of the elements in the operand
      // is in the set of provided filter values.
      NOT_IN = 1;

      // For scalar string operands, checks if the operand contains any of the
      // provided filter substrings.
      //
      // For string list operands, checks if any string in the operand contains
      // any of the provided filter substrings.
      CONTAINS = 2;

      // For scalar string operands, checks that the operand contains none of
      // the provided filter substrings.
      //
      // For string list operands, checks that none of the strings in the
      // operand contain none of the provided filter substrings.
      NOT_CONTAINS = 3;

      // Operand is less than the provided filter value.
      LESS_THAN = 4;

      // Operand is less than or equal to provided filter value.
      LESS_THAN_EQUALS = 5;

      // Operand is greater than provided filter value.
      GREATER_THAN = 6;

      // Operand is greater than or equal to provided filter value.
      GREATER_THAN_EQUALS = 7;

      // Operand is between provided filter values.
      BETWEEN = 8;

      // Operand matches against a regex or set of regexes (one must match)
      MATCHES = 9;

      // Operand negative matches against a regex or set of regexes (none must
      // match)
      NOT_MATCHES = 10;
    }

    // The type determines how the underlying filters are combined.
    oneof type {
      // A filter on a single field.
      FieldFilter field_filter = 1;

      // A filter whose result is negated.
      Filter not_filter = 2;

      // A list of filters whose results are AND-ed.
      FilterList and_filter = 3;

      // A list of filters whose results are OR-ed.
      FilterList or_filter = 4;
    }
  }

  // A flag for a report. Flags are used show if certain thresholds are met.
  // Result rows that match the filter will have the corresponding
  // [MetricValueGroup.flagValues][MetricValueGroup] index set to true.
  // For more information about flags see:
  // https://support.google.com/admanager/answer/15079975
  message Flag {
    // Required. Filters to apply for the flag.
    repeated Filter filters = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Name of the flag.
    // The flag names RED, YELLOW, GREEN, BLUE, PURPLE, and GREY correspond to
    // the colored flags that appear in the UI. The UI will not display flags
    // with other names, but they are available for use by API clients.
    string name = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // A date range for a report.
  message DateRange {
    // A date range between two fixed dates (inclusive of end date).
    message FixedDateRange {
      // Required. The start date of this date range.
      google.type.Date start_date = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. The end date (inclusive) of this date range.
      google.type.Date end_date = 2 [(google.api.field_behavior) = REQUIRED];
    }

    // Options for relative date ranges.
    enum RelativeDateRange {
      // Default value. This value is unused.
      RELATIVE_DATE_RANGE_UNSPECIFIED = 0;

      // The date the report is run.
      TODAY = 1;

      // The date a day before the date that the report is run.
      YESTERDAY = 2;

      // The full week in which this report is run. Could include dates in
      // the future.
      THIS_WEEK = 3;

      // From the beginning of the calendar week (Monday to Sunday) in which the
      // up to and including the day the report is run.
      THIS_WEEK_TO_DATE = 29;

      // The full month in which this report is run. Could include dates in
      // the future.
      THIS_MONTH = 4;

      // From the beginning of the calendar month in which the report is run, to
      // up to and including the day the report is run.
      THIS_MONTH_TO_DATE = 26;

      // The full quarter in which this report is run. Could include dates
      // in the future.
      THIS_QUARTER = 5;

      // From the beginning of the calendar quarter in which the report is run,
      // up to and including the day the report is run.
      THIS_QUARTER_TO_DATE = 27;

      // The full year in which this report is run. Could include dates in
      // the future.
      THIS_YEAR = 6;

      // From the beginning of the calendar year in which the report is run, to
      // up to and including the day the report is run.
      THIS_YEAR_TO_DATE = 28;

      // The entire previous calendar week, Monday to Sunday (inclusive),
      // preceding the calendar week the report is run.
      LAST_WEEK = 7;

      // The entire previous calendar month preceding the calendar month the
      // report is run.
      LAST_MONTH = 8;

      // The entire previous calendar quarter preceding the calendar quarter the
      // report is run.
      LAST_QUARTER = 9;

      // The entire previous calendar year preceding the calendar year the
      // report is run.
      LAST_YEAR = 10;

      // The 7 days preceding the day the report is run.
      LAST_7_DAYS = 11;

      // The 30 days preceding the day the report is run.
      LAST_30_DAYS = 12;

      // The 60 days preceding the day the report is run.
      LAST_60_DAYS = 13;

      // The 90 days preceding the day the report is run.
      LAST_90_DAYS = 14;

      // The 180 days preceding the day the report is run.
      LAST_180_DAYS = 15;

      // The 360 days preceding the day the report is run.
      LAST_360_DAYS = 16;

      // The 365 days preceding the day the report is run.
      LAST_365_DAYS = 17;

      // The entire previous 3 calendar months preceding the calendar month the
      // report is run.
      LAST_3_MONTHS = 18;

      // The entire previous 6 calendar months preceding the calendar month the
      // report is run.
      LAST_6_MONTHS = 19;

      // The entire previous 6 calendar months preceding the calendar month the
      // report is run.
      LAST_12_MONTHS = 20;

      // From 3 years before the report is run, to the day before the report is
      // run, inclusive.
      ALL_AVAILABLE = 21;

      // Only valid when used in the comparison_date_range field. The complete
      // period preceding the date period provided in date_range.
      //
      // In the case where date_range is a FixedDateRange of N days, this will
      // be a period of N days where the end date is the date preceding the
      // start date of the date_range.
      //
      // In the case where date_range is a RelativeDateRange, this will be a
      // period of the same time frame preceding the date_range. In the case
      // where the date_range does not capture the full period because a report
      // is run in the middle of that period, this will still be the full
      // preceding period. For example, if date_range is THIS_WEEK, but the
      // report is run on a Wednesday, THIS_WEEK will be Monday - Wednesday, but
      // PREVIOUS_PERIOD will be Monday - Sunday.
      PREVIOUS_PERIOD = 22;

      // Only valid when used in the comparison_date_range field. The period
      // starting 1 year prior to the date period provided in date_range.
      //
      // In the case where date_range is a FixedDateRange, this will be a date
      // range starting 1 year prior to the date_range start date and ending 1
      // year prior to the date_range end date.
      //
      // In the case where date_range is a RelativeDateRange, this will be a
      // period of the same time frame exactly 1 year prior to the date_range.
      // In the case where the date_range does not capture the full period
      // because a report is run in the middle of that period, this will still
      // be the full period 1 year prior. For example, if date range is
      // THIS_WEEK, but the report is run on a Wednesday, THIS_WEEK will be
      // Monday - Wednesday, but SAME_PERIOD_PREVIOUS_YEAR will be Monday -
      // Sunday.
      SAME_PERIOD_PREVIOUS_YEAR = 24;
    }

    // Either a fixed or relative date range.
    oneof date_range_type {
      // A fixed date range.
      FixedDateRange fixed = 1;

      // A relative date range.
      RelativeDateRange relative = 2;
    }
  }

  // Valid time period columns.
  enum TimePeriodColumn {
    // Default value. Report will have no time period column.
    TIME_PERIOD_COLUMN_UNSPECIFIED = 0;

    // A column for each date in the report.
    TIME_PERIOD_COLUMN_DATE = 1;

    // A column for each week in the report.
    TIME_PERIOD_COLUMN_WEEK = 2;

    // A column for each month in the report.
    TIME_PERIOD_COLUMN_MONTH = 3;

    // A column for each quarter in the report.
    TIME_PERIOD_COLUMN_QUARTER = 4;
  }

  // Reporting dimensions.
  enum Dimension {
    // Default value. This value is unused.
    DIMENSION_UNSPECIFIED = 0;

    // The domain name of the advertiser.
    ADVERTISER_DOMAIN_NAME = 242;

    // The ID used in an external system for advertiser identification
    ADVERTISER_EXTERNAL_ID = 228;

    // The ID of an advertiser company assigned to an order
    ADVERTISER_ID = 131;

    // Labels applied to the advertiser
    // can be used for either competitive exclusion or ad exclusion
    ADVERTISER_LABELS = 230;

    // Label ids applied to the advertiser
    // can be used for either competitive exclusion or ad exclusion
    ADVERTISER_LABEL_IDS = 229;

    // The name of an advertiser company assigned to an order
    ADVERTISER_NAME = 132;

    // The name of the contact associated with an advertiser company
    ADVERTISER_PRIMARY_CONTACT = 227;

    // Shows an ENUM value describing whether a given piece of publisher
    // inventory was above (ATF) or below the fold (BTF) of a page.
    AD_LOCATION = 390;

    // Shows a localized string describing whether a given piece of publisher
    // inventory was above (ATF) or below the fold (BTF) of a page.
    AD_LOCATION_NAME = 391;

    // The code of the ad unit where the ad was requested.
    AD_UNIT_CODE = 64;

    // The code of the first level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_1 = 65;

    // The code of the tenth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_10 = 74;

    // The code of the eleventh level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_11 = 75;

    // The code of the twelfth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_12 = 76;

    // The code of the thirteenth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_13 = 77;

    // The code of the fourteenth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_14 = 78;

    // The code of the fifteenth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_15 = 79;

    // The code of the sixteenth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_16 = 80;

    // The code of the second level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_2 = 66;

    // The code of the third level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_3 = 67;

    // The code of the fourth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_4 = 68;

    // The code of the fifth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_5 = 69;

    // The code of the sixth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_6 = 70;

    // The code of the seventh level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_7 = 71;

    // The code of the eighth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_8 = 72;

    // The code of the ninth level ad unit of the ad unit where the ad was
    // requested.
    AD_UNIT_CODE_LEVEL_9 = 73;

    // The depth of the ad unit's hierarchy
    AD_UNIT_DEPTH = 101;

    // The ID of the ad unit where the ad was requested.
    AD_UNIT_ID = 25;

    // The full hierarchy of ad unit IDs where the ad was requested, from
    // root to leaf, excluding the root ad unit ID.
    AD_UNIT_ID_ALL_LEVEL = 27;

    // The first level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_1 = 30;

    // The tenth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_10 = 48;

    // The eleventh level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_11 = 50;

    // The twelfth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_12 = 52;

    // The thirteenth level ad unit ID of the ad unit where the ad was
    // requested.
    AD_UNIT_ID_LEVEL_13 = 54;

    // The fourteenth level ad unit ID of the ad unit where the ad was
    // requested.
    AD_UNIT_ID_LEVEL_14 = 56;

    // The fifteenth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_15 = 58;

    // The sixteenth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_16 = 60;

    // The second level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_2 = 32;

    // The third level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_3 = 34;

    // The fourth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_4 = 36;

    // The fifth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_5 = 38;

    // The sixth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_6 = 40;

    // The seventh level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_7 = 42;

    // The eighth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_8 = 44;

    // The ninth level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_LEVEL_9 = 46;

    // The top-level ad unit ID of the ad unit where the ad was requested.
    AD_UNIT_ID_TOP_LEVEL = 142;

    // The name of the ad unit where the ad was requested.
    AD_UNIT_NAME = 26;

    // The full hierarchy of ad unit names where the ad was requested, from
    // root to leaf, excluding the root ad unit name.
    AD_UNIT_NAME_ALL_LEVEL = 29;

    // The first level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_1 = 31;

    // The tenth level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_10 = 49;

    // The eleventh level ad unit name of the ad unit where the ad was
    // requested.
    AD_UNIT_NAME_LEVEL_11 = 51;

    // The twelfth level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_12 = 53;

    // The thirteenth level ad unit name of the ad unit where the ad was
    // requested.
    AD_UNIT_NAME_LEVEL_13 = 55;

    // The fourteenth level ad unit name of the ad unit where the ad was
    // requested.
    AD_UNIT_NAME_LEVEL_14 = 57;

    // The fifteenth level ad unit name of the ad unit where the ad was
    // requested.
    AD_UNIT_NAME_LEVEL_15 = 59;

    // The sixteenth level ad unit name of the ad unit where the ad was
    // requested.
    AD_UNIT_NAME_LEVEL_16 = 61;

    // The second level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_2 = 33;

    // The third level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_3 = 35;

    // The fourth level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_4 = 37;

    // The fifth level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_5 = 39;

    // The sixth level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_6 = 41;

    // The seventh level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_7 = 43;

    // The eighth level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_8 = 45;

    // The ninth level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_LEVEL_9 = 47;

    // The top-level ad unit name of the ad unit where the ad was requested.
    AD_UNIT_NAME_TOP_LEVEL = 143;

    // The reward amount of the ad unit where the ad was requested.
    AD_UNIT_REWARD_AMOUNT = 63;

    // The reward type of the ad unit where the ad was requested.
    AD_UNIT_REWARD_TYPE = 62;

    // The status of the ad unit
    AD_UNIT_STATUS = 206;

    // The name of the status of the ad unit
    AD_UNIT_STATUS_NAME = 207;

    // The app version.
    APP_VERSION = 392;

    // The ID used in an external system for advertiser identification
    BACKFILL_ADVERTISER_EXTERNAL_ID = 349;

    // The ID of an advertiser company assigned to a backfill order
    BACKFILL_ADVERTISER_ID = 346;

    // Labels applied to the advertiser
    // can be used for either competitive exclusion or ad exclusion
    BACKFILL_ADVERTISER_LABELS = 351;

    // Label ids applied to the advertiser
    // can be used for either competitive exclusion or ad exclusion
    BACKFILL_ADVERTISER_LABEL_IDS = 350;

    // The name of an advertiser company assigned to a backfill order
    BACKFILL_ADVERTISER_NAME = 347;

    // The name of the contact associated with an advertiser company
    BACKFILL_ADVERTISER_PRIMARY_CONTACT = 348;

    // Enum value of Backfill creative billing type
    BACKFILL_CREATIVE_BILLING_TYPE = 378;

    // Localized string value of Backfill creative billing type
    BACKFILL_CREATIVE_BILLING_TYPE_NAME = 379;

    // Represents the click-through URL of a Backfill creative
    BACKFILL_CREATIVE_CLICK_THROUGH_URL = 376;

    // The ID of a Backfill creative
    BACKFILL_CREATIVE_ID = 370;

    // Backfill creative name
    BACKFILL_CREATIVE_NAME = 371;

    // Third party vendor name of a Backfill creative
    BACKFILL_CREATIVE_THIRD_PARTY_VENDOR = 377;

    // Enum value of Backfill creative type
    BACKFILL_CREATIVE_TYPE = 374;

    // Localized string name of Backfill creative type
    BACKFILL_CREATIVE_TYPE_NAME = 375;

    // Whether a Backfill line item is archived.
    BACKFILL_LINE_ITEM_ARCHIVED = 278;

    // Backfill line item comanion delivery option ENUM value.
    BACKFILL_LINE_ITEM_COMPANION_DELIVERY_OPTION = 258;

    // Localized Backfill line item comanion delivery option name.
    BACKFILL_LINE_ITEM_COMPANION_DELIVERY_OPTION_NAME = 259;

    // The computed status of the BackfillLineItem.
    BACKFILL_LINE_ITEM_COMPUTED_STATUS = 296;

    // The localized name of the computed status of the BackfillLineItem.
    BACKFILL_LINE_ITEM_COMPUTED_STATUS_NAME = 297;

    // The contracted units bought for the Backfill line item.
    BACKFILL_LINE_ITEM_CONTRACTED_QUANTITY = 280;

    // The cost per unit of the Backfill line item.
    BACKFILL_LINE_ITEM_COST_PER_UNIT = 272;

    // Backfill line item cost type ENUM value.
    BACKFILL_LINE_ITEM_COST_TYPE = 264;

    // Localized Backfill line item cost type name.
    BACKFILL_LINE_ITEM_COST_TYPE_NAME = 265;

    // Represent the end date of a Backfill creative associated with a Backfill
    // line item
    BACKFILL_LINE_ITEM_CREATIVE_END_DATE = 381;

    // The creative rotation type of the BackfillLineItem.
    BACKFILL_LINE_ITEM_CREATIVE_ROTATION_TYPE = 290;

    // The localized name of the creative rotation type of the BackfillLineItem.
    BACKFILL_LINE_ITEM_CREATIVE_ROTATION_TYPE_NAME = 291;

    // Represent the start date of a Backfill creative associated with a
    // Backfill line item
    BACKFILL_LINE_ITEM_CREATIVE_START_DATE = 380;

    // The 3 letter currency code of the Backfill line item
    BACKFILL_LINE_ITEM_CURRENCY_CODE = 288;

    // The progress made for the delivery of the Backfill line item.
    BACKFILL_LINE_ITEM_DELIVERY_INDICATOR = 274;

    // The delivery rate type of the BackfillLineItem.
    BACKFILL_LINE_ITEM_DELIVERY_RATE_TYPE = 292;

    // The localized name of the delivery rate type of the BackfillLineItem.
    BACKFILL_LINE_ITEM_DELIVERY_RATE_TYPE_NAME = 293;

    // The discount of the BackfillLineItem in whole units in the
    // BackfillLineItem's currency code, or if unspecified the Network's
    // currency code.
    BACKFILL_LINE_ITEM_DISCOUNT_ABSOLUTE = 294;

    // The discount of the BackfillLineItem in percentage.
    BACKFILL_LINE_ITEM_DISCOUNT_PERCENTAGE = 295;

    // The end date of the Backfill line item.
    BACKFILL_LINE_ITEM_END_DATE = 267;

    // The end date and time of the Backfill line item.
    BACKFILL_LINE_ITEM_END_DATE_TIME = 269;

    // The ENUM value of the environment a Backfill line item is targeting.
    BACKFILL_LINE_ITEM_ENVIRONMENT_TYPE = 302;

    // The localized name of the environment a Backfill line item is targeting.
    BACKFILL_LINE_ITEM_ENVIRONMENT_TYPE_NAME = 257;

    // The deal ID of the Backfill line item. Set for Programmatic Direct
    // campaigns.
    BACKFILL_LINE_ITEM_EXTERNAL_DEAL_ID = 285;

    // The external ID of the Backfill line item.
    BACKFILL_LINE_ITEM_EXTERNAL_ID = 273;

    // The frequency cap of the Backfill line item (descriptive string).
    BACKFILL_LINE_ITEM_FREQUENCY_CAP = 303;

    // Backfill line item ID.
    BACKFILL_LINE_ITEM_ID = 298;

    // The application that last modified the Backfill line item.
    BACKFILL_LINE_ITEM_LAST_MODIFIED_BY_APP = 289;

    // The total number of clicks delivered of the lifetime of the Backfill line
    // item.
    BACKFILL_LINE_ITEM_LIFETIME_CLICKS = 283;

    // The total number of impressions delivered over the lifetime of the
    // Backfill line item.
    BACKFILL_LINE_ITEM_LIFETIME_IMPRESSIONS = 282;

    // The total number of viewable impressions delivered over the lifetime of
    // the Backfill line item.
    BACKFILL_LINE_ITEM_LIFETIME_VIEWABLE_IMPRESSIONS = 284;

    // Whether or not the Backfill line item is Makegood. Makegood refers to
    // free inventory offered to buyers to compensate for mistakes or
    // under-delivery in the original campaigns.
    BACKFILL_LINE_ITEM_MAKEGOOD = 276;

    // Backfill line item name.
    BACKFILL_LINE_ITEM_NAME = 299;

    // The cost of booking for the Backfill line item (non-CPD).
    BACKFILL_LINE_ITEM_NON_CPD_BOOKED_REVENUE = 286;

    // Whether a Backfill line item is eligible for opitimization.
    BACKFILL_LINE_ITEM_OPTIMIZABLE = 277;

    // Goal type ENUM value of the primary goal of the Backfill line item.
    BACKFILL_LINE_ITEM_PRIMARY_GOAL_TYPE = 262;

    // Localized goal type name of the primary goal of the Backfill line item.
    BACKFILL_LINE_ITEM_PRIMARY_GOAL_TYPE_NAME = 263;

    // Unit type ENUM value of the primary goal of the Backfill line item.
    BACKFILL_LINE_ITEM_PRIMARY_GOAL_UNIT_TYPE = 260;

    // Localized unit type name of the primary goal of the Backfill line item.
    BACKFILL_LINE_ITEM_PRIMARY_GOAL_UNIT_TYPE_NAME = 261;

    // The priority of this Backfill line item as a value between 1 and 16.
    // In general, a lower priority means more serving priority for the
    // Backfill line item.
    BACKFILL_LINE_ITEM_PRIORITY = 266;

    // ENUM value describing the state of inventory reservation for the
    // BackfillLineItem.
    BACKFILL_LINE_ITEM_RESERVATION_STATUS = 306;

    // Localized string describing the state of inventory reservation for the
    // BackfillLineItem.
    BACKFILL_LINE_ITEM_RESERVATION_STATUS_NAME = 307;

    // The start date of the Backfill line item.
    BACKFILL_LINE_ITEM_START_DATE = 268;

    // The start date and time of the Backfill line item.
    BACKFILL_LINE_ITEM_START_DATE_TIME = 270;

    // Backfill line item type ENUM value.
    BACKFILL_LINE_ITEM_TYPE = 300;

    // Localized Backfill line item type name.
    BACKFILL_LINE_ITEM_TYPE_NAME = 301;

    // Whether the Backfill line item end time and end date is set to
    // effectively never end.
    BACKFILL_LINE_ITEM_UNLIMITED_END = 271;

    // The artificial cost per unit used by the Ad server to help rank
    // inventory.
    BACKFILL_LINE_ITEM_VALUE_COST_PER_UNIT = 275;

    // The web property code used for dynamic allocation Backfill line items.
    BACKFILL_LINE_ITEM_WEB_PROPERTY_CODE = 287;

    // The ID of Backfill creative, includes regular creatives, and master and
    // companions in case of creative sets
    BACKFILL_MASTER_COMPANION_CREATIVE_ID = 372;

    // Name of Backfill creative, includes regular creatives, and master and
    // companions in case of creative sets
    BACKFILL_MASTER_COMPANION_CREATIVE_NAME = 373;

    // Backfill order agency.
    BACKFILL_ORDER_AGENCY = 313;

    // Backfill order agency ID.
    BACKFILL_ORDER_AGENCY_ID = 314;

    // Backfill order booked CPC.
    BACKFILL_ORDER_BOOKED_CPC = 315;

    // Backfill order booked CPM.
    BACKFILL_ORDER_BOOKED_CPM = 316;

    // Backfill order delivery status ENUM value.
    BACKFILL_ORDER_DELIVERY_STATUS = 340;

    // Backfill order delivery status localized name.
    BACKFILL_ORDER_DELIVERY_STATUS_NAME = 341;

    // Backfill order end date.
    BACKFILL_ORDER_END_DATE = 317;

    // Backfill order end date and time.
    BACKFILL_ORDER_END_DATE_TIME = 319;

    // Backfill order external ID.
    BACKFILL_ORDER_EXTERNAL_ID = 320;

    // Backfill order id.
    BACKFILL_ORDER_ID = 338;

    // Backfill order labels.
    BACKFILL_ORDER_LABELS = 334;

    // Backfill order labels IDs.
    BACKFILL_ORDER_LABEL_IDS = 335;

    // Backfill order lifetime clicks.
    BACKFILL_ORDER_LIFETIME_CLICKS = 322;

    // Backfill order lifetime impressions.
    BACKFILL_ORDER_LIFETIME_IMPRESSIONS = 323;

    // Backfill order name.
    BACKFILL_ORDER_NAME = 339;

    // Backfill order PO number.
    BACKFILL_ORDER_PO_NUMBER = 324;

    // Whether the Backfill order is programmatic.
    BACKFILL_ORDER_PROGRAMMATIC = 321;

    // Backfill order sales person.
    BACKFILL_ORDER_SALESPERSON = 325;

    // Backfill order secondary sales people.
    BACKFILL_ORDER_SECONDARY_SALESPEOPLE = 329;

    // Backfill order secondary sales people ID.
    BACKFILL_ORDER_SECONDARY_SALESPEOPLE_ID = 328;

    // Backfill order secondary traffickers.
    BACKFILL_ORDER_SECONDARY_TRAFFICKERS = 331;

    // Backfill order secondary traffickers ID.
    BACKFILL_ORDER_SECONDARY_TRAFFICKERS_ID = 330;

    // Backfill order start date.
    BACKFILL_ORDER_START_DATE = 332;

    // Backfill order start date and time.
    BACKFILL_ORDER_START_DATE_TIME = 333;

    // Backfill order trafficker.
    BACKFILL_ORDER_TRAFFICKER = 326;

    // Backfill order trafficker ID.
    BACKFILL_ORDER_TRAFFICKER_ID = 327;

    // Whether the Backfill order end time and end date is set to effectively
    // never end.
    BACKFILL_ORDER_UNLIMITED_END = 318;

    // The ID of the buyer on a backfill programmatic proposal.
    BACKFILL_PROGRAMMATIC_BUYER_ID = 336;

    // The name of the buyer on a backfill programmatic proposal.
    BACKFILL_PROGRAMMATIC_BUYER_NAME = 337;

    // The amount of information about the Publisher's page sent to the buyer
    // who purchased the impressions.
    BRANDING_TYPE = 383;

    // The localized version of branding type, the amount of information about
    // the Publisher's page sent to the buyer who purchased the impressions.
    BRANDING_TYPE_NAME = 384;

    // Browser category.
    BROWSER_CATEGORY = 119;

    // Browser category name.
    BROWSER_CATEGORY_NAME = 120;

    // The ID of the browser.
    BROWSER_ID = 235;

    // The name of the browser.
    BROWSER_NAME = 236;

    // Mobile carrier ID.
    CARRIER_ID = 369;

    // Name of the mobile carrier.
    CARRIER_NAME = 368;

    // The ID of an advertiser, classified by Google, associated with a creative
    // transacted
    CLASSIFIED_ADVERTISER_ID = 133;

    // The name of an advertiser, classified by Google, associated with a
    // creative transacted
    CLASSIFIED_ADVERTISER_NAME = 134;

    // ID of the brand, as classified by Google,
    CLASSIFIED_BRAND_ID = 243;

    // Name of the brand, as classified by Google,
    CLASSIFIED_BRAND_NAME = 244;

    // ID of the video content served.
    CONTENT_ID = 246;

    // Name of the video content served.
    CONTENT_NAME = 247;

    // The criteria ID of the country in which the ad served.
    COUNTRY_ID = 11;

    // The name of the country in which the ad served.
    COUNTRY_NAME = 12;

    // Enum value of creative billing type
    CREATIVE_BILLING_TYPE = 366;

    // Localized string value of creative billing type
    CREATIVE_BILLING_TYPE_NAME = 367;

    // Represents the click-through URL of a creative
    CREATIVE_CLICK_THROUGH_URL = 174;

    // The ID of a creative
    CREATIVE_ID = 138;

    // Creative name
    CREATIVE_NAME = 139;

    // Creative technology ENUM
    CREATIVE_TECHNOLOGY = 148;

    // Creative technology locallized name
    CREATIVE_TECHNOLOGY_NAME = 149;

    // Third party vendor name of a creative
    CREATIVE_THIRD_PARTY_VENDOR = 361;

    // Enum value of creative type
    CREATIVE_TYPE = 344;

    // Localized string name of creative type
    CREATIVE_TYPE_NAME = 345;

    // Breaks down reporting data by date.
    DATE = 3;

    // Breaks down reporting data by day of the week. Monday is 1 and 7 is
    // Sunday.
    DAY_OF_WEEK = 4;

    // Demand channel.
    DEMAND_CHANNEL = 9;

    // Demand channel name.
    DEMAND_CHANNEL_NAME = 10;

    // Demand subchannel.
    DEMAND_SUBCHANNEL = 22;

    // Demand subchannel name.
    DEMAND_SUBCHANNEL_NAME = 23;

    // The device on which an ad was served.
    DEVICE = 226;

    // The device category to which an ad is being targeted.
    DEVICE_CATEGORY = 15;

    // The name of the category of device (smartphone, feature phone, tablet, or
    // desktop) to which an ad is being targeted.
    DEVICE_CATEGORY_NAME = 16;

    // The localized name of the device on which an ad was served.
    DEVICE_NAME = 225;

    // ID of the yield partner as classified by Google
    EXCHANGE_THIRD_PARTY_COMPANY_ID = 185;

    // Name of the yield partner as classified by Google
    EXCHANGE_THIRD_PARTY_COMPANY_NAME = 186;

    // The ID of the first look pricing rule.
    FIRST_LOOK_PRICING_RULE_ID = 248;

    // The name of the first look pricing rule.
    FIRST_LOOK_PRICING_RULE_NAME = 249;

    // Breaks down reporting data by hour in one day.
    HOUR = 100;

    // The interaction type of an ad.
    INTERACTION_TYPE = 223;

    // The localized name of the interaction type of an ad.
    INTERACTION_TYPE_NAME = 224;

    // Inventory format.
    // The format of the ad unit (e.g, banner) where the ad was requested.
    INVENTORY_FORMAT = 17;

    // Inventory format name.
    // The format of the ad unit (e.g, banner) where the ad was requested.
    INVENTORY_FORMAT_NAME = 18;

    // Inventory type.
    // The kind of web page or device where the ad was requested.
    INVENTORY_TYPE = 19;

    // Inventory type name.
    // The kind of web page or device where the ad was requested.
    INVENTORY_TYPE_NAME = 20;

    // Whether traffic is Adx Direct.
    IS_ADX_DIRECT = 382;

    // Whether traffic is First Look.
    IS_FIRST_LOOK_DEAL = 401;

    // The Custom Targeting Value ID
    KEY_VALUES_ID = 214;

    // The Custom Targeting Value formatted like <key_name>=<value_name>
    KEY_VALUES_NAME = 215;

    // Whether a Line item is archived.
    LINE_ITEM_ARCHIVED = 188;

    // Line item comanion delivery option ENUM value.
    LINE_ITEM_COMPANION_DELIVERY_OPTION = 204;

    // Localized line item comanion delivery option name.
    LINE_ITEM_COMPANION_DELIVERY_OPTION_NAME = 205;

    // The computed status of the LineItem.
    LINE_ITEM_COMPUTED_STATUS = 250;

    // The localized name of the computed status of the LineItem.
    LINE_ITEM_COMPUTED_STATUS_NAME = 251;

    // The contracted units bought for the Line item.
    LINE_ITEM_CONTRACTED_QUANTITY = 92;

    // The cost per unit of the Line item.
    LINE_ITEM_COST_PER_UNIT = 85;

    // Line item cost type ENUM value.
    LINE_ITEM_COST_TYPE = 212;

    // Localized line item cost type name.
    LINE_ITEM_COST_TYPE_NAME = 213;

    // Represent the end date of a creative associated with line item
    LINE_ITEM_CREATIVE_END_DATE = 176;

    // The creative rotation type of the LineItem.
    LINE_ITEM_CREATIVE_ROTATION_TYPE = 189;

    // The localized name of the creative rotation type of the LineItem.
    LINE_ITEM_CREATIVE_ROTATION_TYPE_NAME = 190;

    // Represent the start date of a creative associated with line item
    LINE_ITEM_CREATIVE_START_DATE = 175;

    // The 3 letter currency code of the Line Item
    LINE_ITEM_CURRENCY_CODE = 180;

    // The progress made for the delivery of the Line item.
    LINE_ITEM_DELIVERY_INDICATOR = 87;

    // The delivery rate type of the LineItem.
    LINE_ITEM_DELIVERY_RATE_TYPE = 191;

    // The localized name of the delivery rate type of the LineItem.
    LINE_ITEM_DELIVERY_RATE_TYPE_NAME = 192;

    // The discount of the LineItem in whole units in the LineItem's currency
    // code, or if unspecified the Network's currency code.
    LINE_ITEM_DISCOUNT_ABSOLUTE = 195;

    // The discount of the LineItem in percentage.
    LINE_ITEM_DISCOUNT_PERCENTAGE = 196;

    // The end date of the Line item.
    LINE_ITEM_END_DATE = 81;

    // The end date and time of the Line item.
    LINE_ITEM_END_DATE_TIME = 83;

    // The ENUM value of the environment a LineItem is targeting.
    LINE_ITEM_ENVIRONMENT_TYPE = 201;

    // The localized name of the environment a LineItem is targeting.
    LINE_ITEM_ENVIRONMENT_TYPE_NAME = 202;

    // The deal ID of the Line item. Set for Programmatic Direct campaigns.
    LINE_ITEM_EXTERNAL_DEAL_ID = 97;

    // The external ID of the Line item.
    LINE_ITEM_EXTERNAL_ID = 86;

    // The frequency cap of the Line item (descriptive string).
    LINE_ITEM_FREQUENCY_CAP = 256;

    // Line item ID.
    LINE_ITEM_ID = 1;

    // The application that last modified the Line Item.
    LINE_ITEM_LAST_MODIFIED_BY_APP = 181;

    // The total number of clicks delivered of the lifetime of the Line item.
    LINE_ITEM_LIFETIME_CLICKS = 95;

    // The total number of impressions delivered over the lifetime of the
    // Line item.
    LINE_ITEM_LIFETIME_IMPRESSIONS = 94;

    // The total number of viewable impressions delivered over the lifetime of
    // the Line item.
    LINE_ITEM_LIFETIME_VIEWABLE_IMPRESSIONS = 96;

    // Whether or not the Line item is Makegood. Makegood refers to free
    // inventory offered to buyers to compensate for mistakes or under-delivery
    // in the original campaigns.
    LINE_ITEM_MAKEGOOD = 89;

    // Line item Name.
    LINE_ITEM_NAME = 2;

    // The cost of booking for the Line item (non-CPD).
    LINE_ITEM_NON_CPD_BOOKED_REVENUE = 98;

    // Whether a Line item is eligible for opitimization.
    LINE_ITEM_OPTIMIZABLE = 90;

    // Goal type ENUM value of the primary goal of the line item.
    LINE_ITEM_PRIMARY_GOAL_TYPE = 210;

    // Localized goal type name of the primary goal of the line item.
    LINE_ITEM_PRIMARY_GOAL_TYPE_NAME = 211;

    // The total number of impressions or clicks that are reserved for a
    // line item. For line items of type BULK or PRICE_PRIORITY, this represents
    // the number of remaining impressions reserved. If the line item has an
    // impression cap goal, this represents the number of impressions or
    // conversions that the line item will stop serving at if reached.
    LINE_ITEM_PRIMARY_GOAL_UNITS_ABSOLUTE = 93;

    // The percentage of impressions or clicks that are reserved for a
    // line item. For line items of type SPONSORSHIP, this represents the
    // percentage of available impressions reserved. For line items of type
    // NETWORK or HOUSE, this represents the percentage of remaining impressions
    // reserved.
    LINE_ITEM_PRIMARY_GOAL_UNITS_PERCENTAGE = 396;

    // Unit type ENUM value of the primary goal of the line item.
    LINE_ITEM_PRIMARY_GOAL_UNIT_TYPE = 208;

    // Localized unit type name of the primary goal of the line item.
    LINE_ITEM_PRIMARY_GOAL_UNIT_TYPE_NAME = 209;

    // The priority of this Line item as a value between 1 and 16.
    // In general, a lower priority means more serving priority for the
    // Line item.
    LINE_ITEM_PRIORITY = 24;

    // ENUM value describing the state of inventory reservation for the
    // LineItem.
    LINE_ITEM_RESERVATION_STATUS = 304;

    // Localized string describing the state of inventory reservation for the
    // LineItem.
    LINE_ITEM_RESERVATION_STATUS_NAME = 305;

    // The start date of the Line item.
    LINE_ITEM_START_DATE = 82;

    // The start date and time of the Line item.
    LINE_ITEM_START_DATE_TIME = 84;

    // Line item type ENUM value.
    LINE_ITEM_TYPE = 193;

    // Localized line item type name.
    LINE_ITEM_TYPE_NAME = 194;

    // Whether the Line item end time and end date is set to effectively never
    // end.
    LINE_ITEM_UNLIMITED_END = 187;

    // The artificial cost per unit used by the Ad server to help rank
    // inventory.
    LINE_ITEM_VALUE_COST_PER_UNIT = 88;

    // The web property code used for dynamic allocation Line Items.
    LINE_ITEM_WEB_PROPERTY_CODE = 179;

    // The ID of creative, includes regular creatives, and master and companions
    // in case of creative sets
    MASTER_COMPANION_CREATIVE_ID = 140;

    // Name of creative, includes regular creatives, and master and companions
    // in case of creative sets
    MASTER_COMPANION_CREATIVE_NAME = 141;

    // Whether the mobile app is free.
    MOBILE_APP_FREE = 128;

    // URL of app icon for the mobile app.
    MOBILE_APP_ICON_URL = 129;

    // The ID of the Mobile App.
    MOBILE_APP_ID = 123;

    // The name of the mobile app.
    MOBILE_APP_NAME = 127;

    // Ownership status of the mobile app.
    MOBILE_APP_OWNERSHIP_STATUS = 311;

    // Ownership status of the mobile app.
    MOBILE_APP_OWNERSHIP_STATUS_NAME = 312;

    // The App Store of the mobile app.
    MOBILE_APP_STORE = 125;

    // The localized name of the mobile app store.
    MOBILE_APP_STORE_NAME = 245;

    // Mobile inventory type.
    // Identifies whether a mobile ad came from a regular web page, an AMP web
    // page, or a mobile app.
    // Values match the Inventory type dimension available in the Overview Home
    // dashboard. Note: Video takes precedence over any other value, for
    // example, if there is an in-stream video impression on a desktop device,
    // it will be attributed to in-stream video and not desktop web.
    MOBILE_INVENTORY_TYPE = 99;

    // Mobile inventory type name.
    // Identifies whether a mobile ad came from a regular web page, an AMP web
    // page, or a mobile app.
    MOBILE_INVENTORY_TYPE_NAME = 21;

    // SDK version of the mobile device.
    MOBILE_SDK_VERSION_NAME = 130;

    // Breaks down reporting data by month and year.
    MONTH_YEAR = 6;

    // Native ad format ID.
    NATIVE_AD_FORMAT_ID = 255;

    // Native ad format name.
    NATIVE_AD_FORMAT_NAME = 254;

    // Native style ID.
    NATIVE_STYLE_ID = 253;

    // Native style name.
    NATIVE_STYLE_NAME = 252;

    // Operating system category.
    OPERATING_SYSTEM_CATEGORY = 117;

    // Operating system category name.
    OPERATING_SYSTEM_CATEGORY_NAME = 118;

    // ID of the operating system version.
    OPERATING_SYSTEM_VERSION_ID = 238;

    // Details of the operating system, including version.
    OPERATING_SYSTEM_VERSION_NAME = 237;

    // Order agency.
    ORDER_AGENCY = 150;

    // Order agency ID.
    ORDER_AGENCY_ID = 151;

    // Order booked CPC.
    ORDER_BOOKED_CPC = 152;

    // Order booked CPM.
    ORDER_BOOKED_CPM = 153;

    // Order delivery status ENUM value.
    ORDER_DELIVERY_STATUS = 231;

    // Order delivery status localized name.
    ORDER_DELIVERY_STATUS_NAME = 239;

    // Order end date.
    ORDER_END_DATE = 154;

    // Order end date and time.
    ORDER_END_DATE_TIME = 155;

    // Order external ID.
    ORDER_EXTERNAL_ID = 156;

    // Order id.
    ORDER_ID = 7;

    // Order labels.
    ORDER_LABELS = 170;

    // Order labels IDs.
    ORDER_LABEL_IDS = 171;

    // Order lifetime clicks.
    ORDER_LIFETIME_CLICKS = 158;

    // Order lifetime impressions.
    ORDER_LIFETIME_IMPRESSIONS = 159;

    // Order name.
    ORDER_NAME = 8;

    // Order PO number.
    ORDER_PO_NUMBER = 160;

    // Whether the Order is programmatic.
    ORDER_PROGRAMMATIC = 157;

    // Order sales person.
    ORDER_SALESPERSON = 161;

    // Order secondary sales people.
    ORDER_SECONDARY_SALESPEOPLE = 164;

    // Order secondary sales people ID.
    ORDER_SECONDARY_SALESPEOPLE_ID = 165;

    // Order secondary traffickers.
    ORDER_SECONDARY_TRAFFICKERS = 166;

    // Order secondary traffickers ID.
    ORDER_SECONDARY_TRAFFICKERS_ID = 167;

    // Order start date.
    ORDER_START_DATE = 168;

    // Order start date and time.
    ORDER_START_DATE_TIME = 169;

    // Order trafficker.
    ORDER_TRAFFICKER = 162;

    // Order trafficker ID.
    ORDER_TRAFFICKER_ID = 163;

    // Whether the Order end time and end date is set to effectively never
    // end.
    ORDER_UNLIMITED_END = 203;

    // Placement ID
    PLACEMENT_ID = 113;

    // The full list of placement IDs associated with the ad unit.
    PLACEMENT_ID_ALL = 144;

    // Placement name
    PLACEMENT_NAME = 114;

    // The full list of placement names associated with the ad unit.
    PLACEMENT_NAME_ALL = 145;

    // Placement status ENUM value
    PLACEMENT_STATUS = 362;

    // The full list of placement status ENUM values associated with the ad
    // unit.
    PLACEMENT_STATUS_ALL = 363;

    // Localized placement status name.
    PLACEMENT_STATUS_NAME = 364;

    // The full list of localized placement status names associated with the ad
    // unit.
    PLACEMENT_STATUS_NAME_ALL = 365;

    // The ID of the buyer on a programmatic proposal.
    PROGRAMMATIC_BUYER_ID = 240;

    // The name of the buyer on a programmatic proposal.
    PROGRAMMATIC_BUYER_NAME = 241;

    // Programmatic channel.
    // The type of transaction that occurred in Ad Exchange.
    PROGRAMMATIC_CHANNEL = 13;

    // Programmatic channel name.
    // The type of transaction that occurred in Ad Exchange.
    PROGRAMMATIC_CHANNEL_NAME = 14;

    // The size of a rendered creative, It can differ with the creative's size
    // if a creative is shown in an ad slot of a different size.
    RENDERED_CREATIVE_SIZE = 343;

    // Inventory Requested Ad Sizes dimension
    REQUESTED_AD_SIZES = 352;

    // Request type ENUM
    REQUEST_TYPE = 146;

    // Request type locallized name
    REQUEST_TYPE_NAME = 147;

    // Information about domain or subdomains.
    SITE = 387;

    // The ID of the browser, device or other environment into which a line item
    // or creative was served.
    TARGETING_ID = 232;

    // Information about the browser, device and other environments into which
    // a line item or creative was served.
    TARGETING_NAME = 233;

    // The way in which advertisers targeted their ads.
    TARGETING_TYPE = 385;

    // The localized name of the way in which advertisers targeted their ads.
    TARGETING_TYPE_NAME = 386;

    // Inventory Traffic source dimension
    TRAFFIC_SOURCE = 388;

    // Inventory Traffic source dimension name
    TRAFFIC_SOURCE_NAME = 389;

    // Unified pricing rule ID dimension
    UNIFIED_PRICING_RULE_ID = 393;

    // Unified pricing rule name dimension
    UNIFIED_PRICING_RULE_NAME = 394;

    // The video placement enum as defined by ADCOM 1.0-202303.
    VIDEO_PLCMT = 172;

    // The localized name of the video placement as defined by ADCOM 1.0-202303.
    VIDEO_PLCMT_NAME = 173;

    // Breaks down reporting data by week of the year.
    WEEK = 5;

    // Name of the company within a yield group
    YIELD_GROUP_BUYER_NAME = 184;

    // ID of the group of ad networks or exchanges used for Mediation and Open
    // Bidding
    YIELD_GROUP_ID = 182;

    // Name of the group of ad networks or exchanges used for Mediation and Open
    // Bidding
    YIELD_GROUP_NAME = 183;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 0 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_0_OPTION_ID = 10000;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 1 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_1_OPTION_ID = 10001;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 2 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_2_OPTION_ID = 10002;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 3 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_3_OPTION_ID = 10003;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 4 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_4_OPTION_ID = 10004;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 5 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_5_OPTION_ID = 10005;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 6 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_6_OPTION_ID = 10006;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 7 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_7_OPTION_ID = 10007;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 8 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_8_OPTION_ID = 10008;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 9 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_9_OPTION_ID = 10009;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 10 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_10_OPTION_ID = 10010;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 11 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_11_OPTION_ID = 10011;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 12 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_12_OPTION_ID = 10012;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 13 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_13_OPTION_ID = 10013;

    // Custom field option ID for Line Item with custom field ID equal to the ID
    // in index 14 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_14_OPTION_ID = 10014;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 0 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_0_VALUE = 11000;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 1 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_1_VALUE = 11001;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 2 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_2_VALUE = 11002;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 3 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_3_VALUE = 11003;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 4 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_4_VALUE = 11004;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 5 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_5_VALUE = 11005;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 6 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_6_VALUE = 11006;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 7 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_7_VALUE = 11007;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 8 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_8_VALUE = 11008;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 9 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_9_VALUE = 11009;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 10 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_10_VALUE = 11010;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 11 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_11_VALUE = 11011;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 12 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_12_VALUE = 11012;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 13 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_13_VALUE = 11013;

    // Custom field value for Line Item with custom field ID equal to the ID
    // in index 14 of `ReportDefinition.line_item_custom_field_ids`.
    LINE_ITEM_CUSTOM_FIELD_14_VALUE = 11014;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 0 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_0_OPTION_ID = 12000;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 1 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_1_OPTION_ID = 12001;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 2 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_2_OPTION_ID = 12002;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 3 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_3_OPTION_ID = 12003;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 4 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_4_OPTION_ID = 12004;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 5 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_5_OPTION_ID = 12005;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 6 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_6_OPTION_ID = 12006;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 7 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_7_OPTION_ID = 12007;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 8 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_8_OPTION_ID = 12008;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 9 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_9_OPTION_ID = 12009;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 10 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_10_OPTION_ID = 12010;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 11 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_11_OPTION_ID = 12011;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 12 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_12_OPTION_ID = 12012;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 13 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_13_OPTION_ID = 12013;

    // Custom field option ID for Order with custom field ID equal to the ID
    // in index 14 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_14_OPTION_ID = 12014;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 0 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_0_VALUE = 13000;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 1 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_1_VALUE = 13001;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 2 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_2_VALUE = 13002;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 3 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_3_VALUE = 13003;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 4 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_4_VALUE = 13004;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 5 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_5_VALUE = 13005;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 6 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_6_VALUE = 13006;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 7 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_7_VALUE = 13007;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 8 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_8_VALUE = 13008;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 9 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_9_VALUE = 13009;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 10 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_10_VALUE = 13010;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 11 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_11_VALUE = 13011;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 12 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_12_VALUE = 13012;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 13 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_13_VALUE = 13013;

    // Custom field value for Order with custom field ID equal to the ID
    // in index 14 of `ReportDefinition.order_custom_field_ids`.
    ORDER_CUSTOM_FIELD_14_VALUE = 13014;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 0 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_0_OPTION_ID = 14000;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 1 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_1_OPTION_ID = 14001;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 2 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_2_OPTION_ID = 14002;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 3 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_3_OPTION_ID = 14003;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 4 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_4_OPTION_ID = 14004;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 5 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_5_OPTION_ID = 14005;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 6 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_6_OPTION_ID = 14006;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 7 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_7_OPTION_ID = 14007;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 8 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_8_OPTION_ID = 14008;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 9 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_9_OPTION_ID = 14009;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 10 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_10_OPTION_ID = 14010;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 11 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_11_OPTION_ID = 14011;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 12 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_12_OPTION_ID = 14012;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 13 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_13_OPTION_ID = 14013;

    // Custom field option ID for Creative with custom field ID equal to the ID
    // in index 14 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_14_OPTION_ID = 14014;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 0 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_0_VALUE = 15000;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 1 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_1_VALUE = 15001;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 2 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_2_VALUE = 15002;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 3 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_3_VALUE = 15003;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 4 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_4_VALUE = 15004;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 5 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_5_VALUE = 15005;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 6 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_6_VALUE = 15006;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 7 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_7_VALUE = 15007;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 8 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_8_VALUE = 15008;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 9 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_9_VALUE = 15009;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 10 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_10_VALUE = 15010;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 11 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_11_VALUE = 15011;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 12 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_12_VALUE = 15012;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 13 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_13_VALUE = 15013;

    // Custom field value for Creative with custom field ID equal to the ID
    // in index 14 of `ReportDefinition.creative_custom_field_ids`.
    CREATIVE_CUSTOM_FIELD_14_VALUE = 15014;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 0 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_0_OPTION_ID = 16000;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 1 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_1_OPTION_ID = 16001;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 2 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_2_OPTION_ID = 16002;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 3 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_3_OPTION_ID = 16003;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 4 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_4_OPTION_ID = 16004;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 5 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_5_OPTION_ID = 16005;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 6 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_6_OPTION_ID = 16006;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 7 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_7_OPTION_ID = 16007;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 8 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_8_OPTION_ID = 16008;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 9 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_9_OPTION_ID = 16009;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 10 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_10_OPTION_ID = 16010;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 11 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_11_OPTION_ID = 16011;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 12 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_12_OPTION_ID = 16012;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 13 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_13_OPTION_ID = 16013;

    // Custom field option ID for Backfill line item with custom field ID equal
    // to the ID in index 14 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_14_OPTION_ID = 16014;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 0 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_0_VALUE = 17000;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 1 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_1_VALUE = 17001;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 2 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_2_VALUE = 17002;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 3 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_3_VALUE = 17003;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 4 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_4_VALUE = 17004;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 5 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_5_VALUE = 17005;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 6 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_6_VALUE = 17006;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 7 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_7_VALUE = 17007;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 8 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_8_VALUE = 17008;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 9 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_9_VALUE = 17009;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 10 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_10_VALUE = 17010;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 11 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_11_VALUE = 17011;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 12 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_12_VALUE = 17012;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 13 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_13_VALUE = 17013;

    // Custom field value for Backfill line item with custom field ID equal to
    // the ID in index 14 of `ReportDefinition.line_item_custom_field_ids`.
    BACKFILL_LINE_ITEM_CUSTOM_FIELD_14_VALUE = 17014;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 0 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_0_OPTION_ID = 18000;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 1 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_1_OPTION_ID = 18001;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 2 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_2_OPTION_ID = 18002;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 3 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_3_OPTION_ID = 18003;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 4 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_4_OPTION_ID = 18004;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 5 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_5_OPTION_ID = 18005;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 6 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_6_OPTION_ID = 18006;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 7 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_7_OPTION_ID = 18007;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 8 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_8_OPTION_ID = 18008;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 9 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_9_OPTION_ID = 18009;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 10 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_10_OPTION_ID = 18010;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 11 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_11_OPTION_ID = 18011;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 12 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_12_OPTION_ID = 18012;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 13 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_13_OPTION_ID = 18013;

    // Custom field option ID for Backfill order with custom field ID equal to
    // the ID in index 14 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_14_OPTION_ID = 18014;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 0 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_0_VALUE = 19000;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 1 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_1_VALUE = 19001;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 2 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_2_VALUE = 19002;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 3 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_3_VALUE = 19003;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 4 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_4_VALUE = 19004;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 5 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_5_VALUE = 19005;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 6 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_6_VALUE = 19006;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 7 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_7_VALUE = 19007;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 8 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_8_VALUE = 19008;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 9 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_9_VALUE = 19009;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 10 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_10_VALUE = 19010;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 11 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_11_VALUE = 19011;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 12 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_12_VALUE = 19012;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 13 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_13_VALUE = 19013;

    // Custom field value for Backfill order with custom field ID equal to the
    // ID in index 14 of `ReportDefinition.order_custom_field_ids`.
    BACKFILL_ORDER_CUSTOM_FIELD_14_VALUE = 19014;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 0 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_0_OPTION_ID = 20000;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 1 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_1_OPTION_ID = 20001;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 2 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_2_OPTION_ID = 20002;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 3 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_3_OPTION_ID = 20003;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 4 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_4_OPTION_ID = 20004;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 5 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_5_OPTION_ID = 20005;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 6 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_6_OPTION_ID = 20006;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 7 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_7_OPTION_ID = 20007;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 8 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_8_OPTION_ID = 20008;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 9 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_9_OPTION_ID = 20009;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 10 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_10_OPTION_ID = 20010;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 11 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_11_OPTION_ID = 20011;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 12 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_12_OPTION_ID = 20012;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 13 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_13_OPTION_ID = 20013;

    // Custom field option ID for Backfill creative with custom field ID equal
    // to the ID in index 14 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_14_OPTION_ID = 20014;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 0 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_0_VALUE = 21000;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 1 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_1_VALUE = 21001;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 2 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_2_VALUE = 21002;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 3 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_3_VALUE = 21003;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 4 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_4_VALUE = 21004;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 5 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_5_VALUE = 21005;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 6 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_6_VALUE = 21006;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 7 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_7_VALUE = 21007;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 8 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_8_VALUE = 21008;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 9 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_9_VALUE = 21009;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 10 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_10_VALUE = 21010;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 11 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_11_VALUE = 21011;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 12 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_12_VALUE = 21012;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 13 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_13_VALUE = 21013;

    // Custom field value for Backfill creative with custom field ID equal to
    // the ID in index 14 of `ReportDefinition.creative_custom_field_ids`.
    BACKFILL_CREATIVE_CUSTOM_FIELD_14_VALUE = 21014;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 0 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_0_VALUE_ID = 100000;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 1 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_1_VALUE_ID = 100001;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 2 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_2_VALUE_ID = 100002;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 3 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_3_VALUE_ID = 100003;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 4 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_4_VALUE_ID = 100004;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 5 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_5_VALUE_ID = 100005;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 6 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_6_VALUE_ID = 100006;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 9 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_7_VALUE_ID = 100007;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 8 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_8_VALUE_ID = 100008;

    // Custom Dimension Value ID for Custom Dimension with key equal to the key
    // in index 9 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_9_VALUE_ID = 100009;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 0 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_0_VALUE = 101000;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 1 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_1_VALUE = 101001;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 2 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_2_VALUE = 101002;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 3 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_3_VALUE = 101003;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 4 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_4_VALUE = 101004;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 5 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_5_VALUE = 101005;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 6 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_6_VALUE = 101006;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 7 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_7_VALUE = 101007;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 8 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_8_VALUE = 101008;

    // Custom Dimension Value name for Custom Dimension with key equal to the
    // id in index 9 of `ReportDefinition.custom_dimension_key_ids`.
    CUSTOM_DIMENSION_9_VALUE = 101009;
  }

  // Reporting metrics.
  enum Metric {
    // Default value. This value is unused.
    METRIC_UNSPECIFIED = 0;

    // Active View total average time in seconds that specific impressions are
    // reported as being viewable.
    ACTIVE_VIEW_AVERAGE_VIEWABLE_TIME = 61;

    // Total number of impressions that were eligible to measure viewability.
    ACTIVE_VIEW_ELIGIBLE_IMPRESSIONS = 58;

    // The total number of impressions that were sampled and measured by active
    // view.
    ACTIVE_VIEW_MEASURABLE_IMPRESSIONS = 57;

    // The percentage of total impressions that were measurable by active view
    // (out of all the total impressions sampled for active view).
    ACTIVE_VIEW_MEASURABLE_IMPRESSIONS_RATE = 60;

    // The total number of impressions viewed on the user's screen.
    ACTIVE_VIEW_VIEWABLE_IMPRESSIONS = 56;

    // The percentage of total impressions viewed on the user's screen (out of
    // the total impressions measurable by active view).
    ACTIVE_VIEW_VIEWABLE_IMPRESSIONS_RATE = 59;

    // Active View AdSense average time in seconds that specific impressions are
    // reported as being viewable.
    ADSENSE_ACTIVE_VIEW_AVERAGE_VIEWABLE_TIME = 73;

    // Total number of impressions delivered by AdSense that were eligible to
    // measure viewability.
    ADSENSE_ACTIVE_VIEW_ELIGIBLE_IMPRESSIONS = 70;

    // The number of impressions delivered by AdSense that were sampled, and
    // measurable by active view.
    ADSENSE_ACTIVE_VIEW_MEASURABLE_IMPRESSIONS = 69;

    // The percentage of impressions delivered by AdSense that were measurable
    // by active view (out of all AdSense impressions sampled for active view).
    ADSENSE_ACTIVE_VIEW_MEASURABLE_IMPRESSIONS_RATE = 72;

    // The number of impressions delivered by AdSense viewed on the user's
    // screen.
    ADSENSE_ACTIVE_VIEW_VIEWABLE_IMPRESSIONS = 68;

    // The percentage of impressions delivered by AdSense viewed on the user's
    // screen (out of AdSense impressions measurable by active view).
    ADSENSE_ACTIVE_VIEW_VIEWABLE_IMPRESSIONS_RATE = 71;

    // The average effective cost-per-thousand-impressions earned from the ads
    // delivered by AdSense through line item dynamic allocation.
    ADSENSE_AVERAGE_ECPM = 26;

    // Number of clicks delivered by AdSense demand channel.
    ADSENSE_CLICKS = 23;

    // The ratio of impressions served by AdSense that resulted in users
    // clicking on an ad. The clickthrough rate (CTR) is updated nightly. The
    // AdSense CTR is calculated as: (AdSense clicks / AdSense impressions).
    ADSENSE_CTR = 24;

    // Total impressions delivered by AdSense.
    ADSENSE_IMPRESSIONS = 22;

    // Ratio of clicks delivered by AdSense through line item dynamic allocation
    // in relation to the total clicks delivered.
    ADSENSE_PERCENT_CLICKS = 28;

    // Ratio of impressions delivered by AdSense through line item dynamic
    // allocation in relation to the total impressions delivered.
    ADSENSE_PERCENT_IMPRESSIONS = 27;

    // Ratio of revenue generated by AdSense through line item dynamic
    // allocation in relation to the total revenue.
    ADSENSE_PERCENT_REVENUE = 29;

    // Ratio of revenue generated by AdSense through line item dynamic
    // allocation in relation to the total revenue (excluding CPD).
    ADSENSE_PERCENT_REVENUE_WITHOUT_CPD = 30;

    // The total number of times that an AdSense ad is delivered.
    ADSENSE_RESPONSES_SERVED = 41;

    // Revenue generated from AdSense through line item dynamic allocation,
    // calculated in the network's currency and time zone.
    ADSENSE_REVENUE = 25;

    // Active View AdExchange average time in seconds that specific impressions
    // are reported as being viewable.
    AD_EXCHANGE_ACTIVE_VIEW_AVERAGE_VIEWABLE_TIME = 79;

    // Total number of impressions delivered by Ad Exchange that were eligible
    // to measure viewability.
    AD_EXCHANGE_ACTIVE_VIEW_ELIGIBLE_IMPRESSIONS = 76;

    // The number of impressions delivered by Ad Exchange that were sampled, and
    // measurable by active view.
    AD_EXCHANGE_ACTIVE_VIEW_MEASURABLE_IMPRESSIONS = 75;

    // The percentage of impressions delivered by Ad Exchange that were
    // measurable by active view (out of all Ad Exchange impressions sampled for
    // active view).
    AD_EXCHANGE_ACTIVE_VIEW_MEASURABLE_IMPRESSIONS_RATE = 78;

    // The number of impressions delivered by Ad Exchange viewed on the user's
    // screen.
    AD_EXCHANGE_ACTIVE_VIEW_VIEWABLE_IMPRESSIONS = 74;

    // The percentage of impressions delivered by Ad Exchange viewed on the
    // user's screen (out of Ad Exchange impressions measurable by active view).
    AD_EXCHANGE_ACTIVE_VIEW_VIEWABLE_IMPRESSIONS_RATE = 77;

    // The average effective cost-per-thousand-impressions earned from the ads
    // delivered by Ad Exchange through line item dynamic allocation.
    AD_EXCHANGE_AVERAGE_ECPM = 18;

    // Number of clicks delivered by the Ad Exchange.
    AD_EXCHANGE_CLICKS = 15;

    // The ratio of impressions served by the Ad Exchange that resulted in users
    // clicking on an ad. The clickthrough rate (CTR) is updated nightly. Ad
    // Exchange CTR is calculated as: (Ad Exchange clicks / Ad Exchange
    // impressions).
    AD_EXCHANGE_CTR = 16;

    // Total impressions delivered by the Ad Exchange.
    AD_EXCHANGE_IMPRESSIONS = 14;

    // Ratio of clicks delivered by Ad Exchange through line item dynamic
    // allocation in relation to the total clicks delivered.
    AD_EXCHANGE_PERCENT_CLICKS = 20;

    // Ratio of impressions delivered by Ad Exchange through line item dynamic
    // allocation in relation to the total impressions delivered.
    AD_EXCHANGE_PERCENT_IMPRESSIONS = 19;

    // Ratio of revenue generated by Ad Exchange through line item dynamic
    // allocation in relation to the total revenue.
    AD_EXCHANGE_PERCENT_REVENUE = 21;

    // Ratio of revenue generated by Ad Exchange through line item dynamic
    // allocation in relation to the total revenue (excluding CPD).
    AD_EXCHANGE_PERCENT_REVENUE_WITHOUT_CPD = 31;

    // The total number of times that an Ad Exchange ad is delivered.
    AD_EXCHANGE_RESPONSES_SERVED = 42;

    // Revenue generated from the Ad Exchange through line item dynamic
    // allocation, calculated in your network's currency and time zone.
    AD_EXCHANGE_REVENUE = 17;

    // The total number of times that an ad request is sent to the ad server
    // including dynamic allocation.
    AD_REQUESTS = 38;

    // Active View ad server average time in seconds that specific impressions
    // are reported as being viewable.
    AD_SERVER_ACTIVE_VIEW_AVERAGE_VIEWABLE_TIME = 67;

    // Total number of impressions delivered by the ad server that were eligible
    // to measure viewability.
    AD_SERVER_ACTIVE_VIEW_ELIGIBLE_IMPRESSIONS = 64;

    // The number of impressions delivered by the ad server that were sampled,
    // and measurable by active view.
    AD_SERVER_ACTIVE_VIEW_MEASURABLE_IMPRESSIONS = 63;

    // The percentage of impressions delivered by the ad server that were
    // measurable by active view (out of all the ad server impressions sampled
    // for active view).
    AD_SERVER_ACTIVE_VIEW_MEASURABLE_IMPRESSIONS_RATE = 66;

    // The number of impressions delivered by the ad server viewed on the user's
    // screen.
    AD_SERVER_ACTIVE_VIEW_VIEWABLE_IMPRESSIONS = 62;

    // The percentage of impressions delivered by the ad server viewed on the
    // user's screen (out of the ad server impressions measurable by active
    // view).
    AD_SERVER_ACTIVE_VIEW_VIEWABLE_IMPRESSIONS_RATE = 65;

    // Average effective cost-per-thousand-impressions earned from the ads
    // delivered by the Google Ad Manager server.
    AD_SERVER_AVERAGE_ECPM = 34;

    // Average effective cost-per-thousand-impressions earned from the ads
    // delivered by the Google Ad Manager server, excluding CPD value.
    AD_SERVER_AVERAGE_ECPM_WITHOUT_CPD = 10;

    // Total clicks served by the Google Ad Manager server. It usually takes
    // about 30 minutes for new clicks to be recorded and added to the total
    // displayed in reporting.
    AD_SERVER_CLICKS = 7;

    // CPD revenue earned, calculated in your network's currency, for
    // the ads delivered by the Google Ad Manager server. Sum of all booked
    // revenue.
    AD_SERVER_CPD_REVENUE = 32;

    // Ratio of impressions served by the Google Ad Manager server that resulted
    // in users clicking on an ad. The clickthrough rate (CTR) is updated
    // nightly. The ad server CTR is calculated as: (Ad server clicks / Ad
    // server impressions).
    AD_SERVER_CTR = 8;

    // Total impressions delivered by the Ad Server.
    AD_SERVER_IMPRESSIONS = 6;

    // Ratio of clicks delivered by the Google Ad Manager server in relation to
    // the total clicks delivered.
    AD_SERVER_PERCENT_CLICKS = 12;

    // Ratio of impressions delivered by the Google Ad Manager server in
    // relation to the total impressions delivered.
    AD_SERVER_PERCENT_IMPRESSIONS = 11;

    // Ratio of revenue generated by the Google Ad Manager server in relation
    // to the total revenue.
    AD_SERVER_PERCENT_REVENUE = 35;

    // Ratio of revenue generated by the Google Ad Manager server (excluding
    // CPD) in relation to the total revenue.
    AD_SERVER_PERCENT_REVENUE_WITHOUT_CPD = 13;

    // The total number of times that an ad is served by the ad server.
    AD_SERVER_RESPONSES_SERVED = 40;

    // All CPM, CPC, and CPD revenue earned, calculated in your network's
    // currency, for the ads delivered by the Google Ad Manager server.
    // Sum of all booked revenue.
    AD_SERVER_REVENUE = 33;

    // Revenue (excluding CPD) earned, calculated in your network's currency,
    // for the ads delivered by the Google Ad Manager server. Sum of all booked
    // revenue.
    AD_SERVER_REVENUE_WITHOUT_CPD = 9;

    // Number of winning bids received from Open Bidding buyers, even when the
    // winning bid is placed at the end of a mediation for mobile apps chain.
    AUCTIONS_WON = 80;

    // eCPM averaged across the Google Ad Manager server, AdSense,
    // and Ad Exchange.
    AVERAGE_ECPM = 37;

    // eCPM averaged across the Google Ad Manager server (excluding CPD),
    // AdSense, and Ad Exchange.
    AVERAGE_ECPM_WITHOUT_CPD = 5;

    // Number of bids received from Open Bidding buyers, regardless of whether
    // the returned bid competes in an auction.
    BIDS = 81;

    // Number of bids received from Open Bidding buyers that competed in
    // the auction.
    BIDS_IN_AUCTION = 82;

    // Number of times a yield partner is asked to return bid to fill
    // a yield group request.
    CALLOUTS = 83;

    // The number of times a user clicked on an ad.
    CLICKS = 2;

    // The total number of times that the code for an ad is served by the ad
    // server including dynamic allocation.
    CODE_SERVED_COUNT = 44;

    // For standard ads, your ad clickthrough rate (CTR) is the number of ad
    //  clicks divided by the number of individual ad impressions expressed as a
    //  fraction. Ad CTR = Clicks / Ad impressions.
    CTR = 3;

    // The number of coviewed impressions sold by Google in partner sales.
    GOOGLE_SOLD_AUCTION_COVIEWED_IMPRESSIONS = 129;

    // The number of auction impressions sold by Google in partner sales.
    GOOGLE_SOLD_AUCTION_IMPRESSIONS = 128;

    // The number of coviewed impressions sold by Google in partner sales.
    GOOGLE_SOLD_COVIEWED_IMPRESSIONS = 131;

    // The number of impressions sold by Google in partner sales.
    GOOGLE_SOLD_IMPRESSIONS = 130;

    // The number of coviewed impressions sold by Google in partner sales.
    GOOGLE_SOLD_RESERVATION_COVIEWED_IMPRESSIONS = 127;

    // The number of reservation impressions sold by Google in partner sales.
    GOOGLE_SOLD_RESERVATION_IMPRESSIONS = 126;

    // Total impressions from the Google Ad Manager server, AdSense,
    // Ad Exchange, and yield group partners.
    IMPRESSIONS = 1;

    // The number of filled pod requests (filled by partner or Google) in
    // partner sales.
    PARTNER_SALES_FILLED_POD_REQUESTS = 135;

    // The percent of filled requests to total ad requests in
    // partner sales.
    PARTNER_SALES_FILL_RATE = 136;

    // The percent of partner filled requests to total ad requests
    // in partner sales.
    PARTNER_SALES_PARTNER_MATCH_RATE = 137;

    // The number of queries eligible for partner sales.
    PARTNER_SALES_QUERIES = 132;

    // The number of partner unfilled impressions in partner sales.
    // If a pod request is not filled by partner but filled by Google, this
    // metric will still count 1.
    PARTNER_SALES_UNFILLED_IMPRESSIONS = 133;

    // The number of partner unmatched queries in partner sales.
    // If an ad request is not filled by partner but filled by Google, this
    // metric will still count 1.
    PARTNER_SALES_UNMATCHED_QUERIES = 134;

    // The number of code served sold by partner in partner sales.
    PARTNER_SOLD_CODE_SERVED = 125;

    // The number of coviewed impressions sold by partner in partner sales.
    PARTNER_SOLD_COVIEWED_IMPRESSIONS = 124;

    // The number of impressions sold by partner in partner sales.
    PARTNER_SOLD_IMPRESSIONS = 123;

    // The total number of ad requests eligible for programmatic inventory,
    // including Programmatic Guaranteed, Preferred Deals, backfill, and open
    // auction.
    PROGRAMMATIC_ELIGIBLE_AD_REQUESTS = 177;

    // The number of programmatic responses served divided by the number of
    // programmatic eligible ad requests. Includes Ad Exchange, Open Bidding,
    // and Preferred Deals.
    PROGRAMMATIC_MATCH_RATE = 178;

    // Total number of ad responses served from programmatic demand sources.
    // Includes Ad Exchange, Open Bidding, and Preferred Deals.
    //
    // Differs from AD_EXCHANGE_RESPONSES_SERVED, which doesn't include Open
    // Bidding ad requests.
    PROGRAMMATIC_RESPONSES_SERVED = 176;

    // The total number of times that an ad is served by the ad server including
    // dynamic allocation.
    RESPONSES_SERVED = 39;

    // Total amount of CPM, CPC, and CPD revenue based on the number of units
    // served by the Google Ad Manager server, AdSense, Ad Exchange, and
    // third-party Mediation networks.
    REVENUE = 36;

    // Total amount of revenue (excluding CPD) based on the number of units
    // served by the Google Ad Manager server, AdSense, Ad Exchange, and
    // third-party Mediation networks.
    REVENUE_WITHOUT_CPD = 4;

    // Number of times a yield group buyer successfully returned a bid in
    // response to a yield group callout.
    SUCCESSFUL_RESPONSES = 84;

    // The total number of missed impressions due to the ad servers' inability
    // to find ads to serve including dynamic allocation.
    UNFILLED_IMPRESSIONS = 45;

    // The total number of times that an ad is not returned by the ad server.
    UNMATCHED_AD_REQUESTS = 43;

    // Number of times an Offerwall message was shown to users.
    USER_MESSAGES_OFFERWALL_MESSAGES_SHOWN = 121;

    // The number of messages where the user gained an entitlement.
    USER_MESSAGES_OFFERWALL_SUCCESSFUL_ENGAGEMENTS = 122;

    // The number of user interactions with a video, on average, such as pause,
    // full screen, mute, etc.
    VIDEO_INTERACTION_AVERAGE_INTERACTION_RATE = 92;

    // The number of times a user collapses a video, either to its original size
    // or to a different size.
    VIDEO_INTERACTION_COLLAPSES = 93;

    // The number of times a user expands a video.
    VIDEO_INTERACTION_EXPANDS = 95;

    // The number of times ad clip played in full screen mode.
    VIDEO_INTERACTION_FULL_SCREENS = 96;

    // The number of times video player was in mute state during play of ad
    //  clip.
    VIDEO_INTERACTION_MUTES = 97;

    // The number of times user paused ad clip.
    VIDEO_INTERACTION_PAUSES = 98;

    // The number of times the user unpaused the video.
    VIDEO_INTERACTION_RESUMES = 99;

    // The number of times a user rewinds the video.
    VIDEO_INTERACTION_REWINDS = 100;

    // The number of times a user unmutes the video.
    VIDEO_INTERACTION_UNMUTES = 101;

    // The number of times a skippable video is skipped.
    VIDEO_INTERACTION_VIDEO_SKIPS = 102;

    // The number of total creative serves in video realtime reporting.
    VIDEO_REAL_TIME_CREATIVE_SERVES = 139;

    // The number of errors of type 100 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_100_COUNT = 143;

    // The number of errors of type 101 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_101_COUNT = 144;

    // The number of errors of type 102 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_102_COUNT = 145;

    // The number of errors of type 200 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_200_COUNT = 146;

    // The number of errors of type 201 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_201_COUNT = 147;

    // The number of errors of type 202 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_202_COUNT = 148;

    // The number of errors of type 203 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_203_COUNT = 149;

    // The number of errors of type 300 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_300_COUNT = 150;

    // The number of errors of type 301 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_301_COUNT = 151;

    // The number of errors of type 302 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_302_COUNT = 152;

    // The number of errors of type 303 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_303_COUNT = 153;

    // The number of errors of type 400 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_400_COUNT = 154;

    // The number of errors of type 401 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_401_COUNT = 155;

    // The number of errors of type 402 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_402_COUNT = 156;

    // The number of errors of type 403 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_403_COUNT = 157;

    // The number of errors of type 405 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_405_COUNT = 158;

    // The number of errors of type 406 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_406_COUNT = 159;

    // The number of errors of type 407 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_407_COUNT = 160;

    // The number of errors of type 408 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_408_COUNT = 161;

    // The number of errors of type 409 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_409_COUNT = 162;

    // The number of errors of type 410 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_410_COUNT = 163;

    // The number of errors of type 500 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_500_COUNT = 164;

    // The number of errors of type 501 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_501_COUNT = 165;

    // The number of errors of type 502 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_502_COUNT = 166;

    // The number of errors of type 503 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_503_COUNT = 167;

    // The number of errors of type 600 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_600_COUNT = 168;

    // The number of errors of type 601 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_601_COUNT = 169;

    // The number of errors of type 602 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_602_COUNT = 170;

    // The number of errors of type 603 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_603_COUNT = 171;

    // The number of errors of type 604 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_604_COUNT = 172;

    // The number of errors of type 900 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_900_COUNT = 173;

    // The number of errors of type 901 in video realtime reporting.
    VIDEO_REAL_TIME_ERROR_901_COUNT = 174;

    // The number of total impressions in video realtime reporting.
    VIDEO_REAL_TIME_IMPRESSIONS = 138;

    // The number of matched queries in video realtime reporting.
    VIDEO_REAL_TIME_MATCHED_QUERIES = 140;

    // The number of all errors in video realtime reporting.
    VIDEO_REAL_TIME_TOTAL_ERROR_COUNT = 175;

    // The number of total queries in video realtime reporting.
    VIDEO_REAL_TIME_TOTAL_QUERIES = 142;

    // The number of unmatched queries in video realtime reporting.
    VIDEO_REAL_TIME_UNMATCHED_QUERIES = 141;

    // Number of times that the publisher specified a video ad played
    // automatically.
    VIDEO_VIEWERSHIP_AUTO_PLAYS = 103;

    // Average percentage of the video watched by users.
    VIDEO_VIEWERSHIP_AVERAGE_VIEW_RATE = 104;

    // Average time(seconds) users watched the video.
    VIDEO_VIEWERSHIP_AVERAGE_VIEW_TIME = 105;

    // Number of times that the publisher specified a video ad was clicked to
    // play.
    VIDEO_VIEWERSHIP_CLICK_TO_PLAYS = 106;

    // The number of times the video played to completion.
    VIDEO_VIEWERSHIP_COMPLETES = 107;

    // Percentage of times the video played to the end.
    VIDEO_VIEWERSHIP_COMPLETION_RATE = 108;

    // The number of engaged views: ad is viewed to completion or for 30s,
    // whichever comes first.
    VIDEO_VIEWERSHIP_ENGAGED_VIEWS = 109;

    // The number of times the video played to 25% of its length.
    VIDEO_VIEWERSHIP_FIRST_QUARTILES = 110;

    // The number of times the video reached its midpoint during play.
    VIDEO_VIEWERSHIP_MIDPOINTS = 111;

    // The number of times a skip button is shown in video.
    VIDEO_VIEWERSHIP_SKIP_BUTTONS_SHOWN = 112;

    // The number of impressions where the video was played.
    VIDEO_VIEWERSHIP_STARTS = 113;

    // The number of times the video played to 75% of its length.
    VIDEO_VIEWERSHIP_THIRD_QUARTILES = 114;

    // The number of times an error occurred, such as a VAST redirect error, a
    // video playback error, or an invalid response error.
    VIDEO_VIEWERSHIP_TOTAL_ERROR_COUNT = 115;

    // The percentage of video error count.
    VIDEO_VIEWERSHIP_TOTAL_ERROR_RATE = 94;

    // Duration of the video creative.
    VIDEO_VIEWERSHIP_VIDEO_LENGTH = 116;

    // View-through rate represented as a percentage.
    VIDEO_VIEWERSHIP_VIEW_THROUGH_RATE = 117;

    // The estimated net rate for yield groups or individual
    // yield group partners.
    YIELD_GROUP_ESTIMATED_CPM = 88;

    // Total net revenue earned by a yield group, based upon the yield group
    // estimated CPM and yield group impressions recorded.
    YIELD_GROUP_ESTIMATED_REVENUE = 87;

    // Number of matched yield group requests where a yield partner delivered
    // their ad to publisher inventory.
    YIELD_GROUP_IMPRESSIONS = 85;

    // Yield group Mediation fill rate indicating how often a network
    // fills an ad request.
    YIELD_GROUP_MEDIATION_FILL_RATE = 89;

    // Total requests where a Mediation chain was served.
    YIELD_GROUP_MEDIATION_MATCHED_QUERIES = 86;

    // The number of mediation chain passback across all channels.
    YIELD_GROUP_MEDIATION_PASSBACKS = 118;

    // Revenue per thousand impressions based on data collected by Ad Manager
    // from third-party ad network reports.
    YIELD_GROUP_MEDIATION_THIRD_PARTY_ECPM = 90;
  }

  // Possible metric value types to add.
  enum MetricValueType {
    // The values for the primary date_range.
    PRIMARY = 0;

    // Each metrics' percent of the total for the primary date_range.
    PRIMARY_PERCENT_OF_TOTAL = 1;

    // The values for the comparison_date_range.
    COMPARISON = 2;

    // Each metrics' percent of the total for the comparison_date_range.
    COMPARISON_PERCENT_OF_TOTAL = 3;

    // The absolute change between the primary and comparison date ranges.
    ABSOLUTE_CHANGE = 4;

    // The relative change between the primary and comparison date ranges.
    RELATIVE_CHANGE = 5;
  }

  // Supported report types.
  enum ReportType {
    // Default value. This value is unused.
    REPORT_TYPE_UNSPECIFIED = 0;

    // Historical.
    HISTORICAL = 1;
  }

  // The visibility of a report.
  enum Visibility {
    // Default value. Reports with hidden visibility will not appear in the Ad
    // Manager UI.
    HIDDEN = 0;

    // Reports with draft visibility will appear in the Ad Manager UI only if
    // the user has configured the UI to show them.
    DRAFT = 1;

    // Reports with saved visibility will appear in the Ad Manager UI by
    // default.
    SAVED = 2;
  }

  // Identifier. The resource name of the report.
  // Report resource name have the form:
  // `networks/{network_code}/reports/{report_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Report ID.
  int64 report_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The visibility of a report.
  Visibility visibility = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The report definition of the report.
  ReportDefinition report_definition = 4
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Display name for the report.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The instant this report was last modified.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The instant this report was created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The locale of this report. Locale is set from the user's
  // locale at the time of the request. Locale can not be modified.
  string locale = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The schedule options of this report.
  ScheduleOptions schedule_options = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for a running a report.
message RunReportRequest {
  // Required. The report to run.
  // Format: `networks/{network_code}/reports/{report_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Report"
    }
  ];
}

// `RunReport` operation metadata.
message RunReportMetadata {
  // An estimate of how close this report is to being completed. Will always be
  // 100 for failed and completed reports.
  int32 percent_complete = 2;

  // The result's parent report.
  string report = 4 [(google.api.resource_reference) = {
    type: "admanager.googleapis.com/Report"
  }];
}

// Response message for a completed `RunReport` operation.
message RunReportResponse {
  // The unique name of the generated result. Use with `FetchReportResultRows`
  // to retrieve data.
  string report_result = 1;
}

// Request object for `GetReport` method.
message GetReportRequest {
  // Required. The resource name of the report.
  // Format: `networks/{network_code}/reports/{report_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Report"
    }
  ];
}

// Request object for `ListReports` method.
message ListReportsRequest {
  // Required. The parent, which owns this collection of reports.
  // Format: `networks/{network_code}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Network"
    }
  ];

  // Optional. The maximum number of `Reports` to return. The service may return
  // fewer than this value. If unspecified, at most 50 `Reports` will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListReports` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListReports` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to filter the response.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression to specify sorting order.
  // See syntax details at
  // https://developers.google.com/ad-manager/api/beta/filters#order
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of individual resources to skip while paginating.
  int32 skip = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for `ListReportsResponse` containing matching `Report`
// objects.
message ListReportsResponse {
  // The `Report` objects from the specified network.
  repeated Report reports = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Total number of `Report` objects.
  // If a filter was included in the request, this reflects the total number
  // after the filtering is applied.
  //
  // `total_size` will not be calculated in the response unless it has been
  // included in a response field mask. The response field mask can be provided
  // to the method by using the URL parameter `$fields` or `fields`, or by using
  // the HTTP/gRPC header `X-Goog-FieldMask`.
  //
  // For more information, see
  // https://developers.google.com/ad-manager/api/beta/field-masks
  int32 total_size = 3;
}

// Request object for `CreateReport` method.
message CreateReportRequest {
  // Required. The parent resource where this `Report` will be created.
  // Format: `networks/{network_code}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "admanager.googleapis.com/Report"
    }
  ];

  // Required. The `Report` to create.
  Report report = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request object for `UpdateReport` method.
message UpdateReportRequest {
  // Required. The `Report` to update.
  Report report = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for the fetch report result rows endpoint.
message FetchReportResultRowsRequest {
  // The report result being fetched.
  // Format:
  // `networks/{network_code}/reports/{report_id}/results/{report_result_id}`
  string name = 1;

  // Optional. The maximum number of rows to return. The service may return
  // fewer than this value. If unspecified, at most 1,000 rows will be returned.
  // The maximum value is 10,000; values above 10,000 will be reduced to 10,000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `FetchReportResultRows`
  // call. Provide this to retrieve the second and subsequent batches of rows.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for the fetch report result rows endpoint.
message FetchReportResultRowsResponse {
  // Up to `page_size` rows of report data.
  repeated Report.DataTable.Row rows = 1;

  // The time at which the report was scheduled to run. For non-scheduled
  // reports, this is the time at which the report was requested to be run.
  google.protobuf.Timestamp run_time = 2;

  // The computed fixed date ranges this report includes.
  // Only returned with the first page of results (when page_token is not
  // included in the request).
  repeated Report.DateRange.FixedDateRange date_ranges = 3;

  // The computed comparison fixed date ranges this report includes.
  // Only returned with the first page of results (when page_token is not
  // included in the request).
  repeated Report.DateRange.FixedDateRange comparison_date_ranges = 4;

  // The total number of rows available from this report. Useful for
  // pagination.
  // Only returned with the first page of results (when page_token is not
  // included in the request).
  int32 total_row_count = 5;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 6;
}

// The definition of how a report should be run.
message ReportDefinition {
  // Required. The list of dimensions to report on. If empty, the report will
  // have no dimensions, and any metrics will be totals.
  repeated Report.Dimension dimensions = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of metrics to report on. If empty, the report will have
  // no metrics.
  repeated Report.Metric metrics = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filters for this report.
  repeated Report.Filter filters = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The time zone the date range is defined in for this report.
  // Defaults to publisher's time zone if not specified. Time zone in IANA
  // format. Acceptable values depend on the report type. Publisher time zone is
  // always accepted. Use "America/Los_Angeles" for pacific time, or "Etc/UTC"
  // for UTC.
  string time_zone = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The ISO 4217 currency code for this report. Defaults to publisher
  // currency code if not specified.
  string currency_code = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. The primary date range of this report.
  Report.DateRange date_range = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. The comparison date range of this report. If unspecified, the
  // report will not have any comparison metrics.
  optional Report.DateRange comparison_date_range = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom Dimension keys that represent CUSTOM_DIMENSION_*
  // dimensions. The index of this repeated field corresponds to the index on
  // each dimension. For example, custom_dimension_key_ids[0] describes
  // CUSTOM_DIMENSION_0_VALUE_ID and CUSTOM_DIMENSION_0_VALUE.
  repeated int64 custom_dimension_key_ids = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom field IDs that represent LINE_ITEM_CUSTOM_FIELD_*
  // dimensions. The index of this repeated field corresponds to the index on
  // each dimension. For example, line_item_custom_field_ids[0] describes
  // LINE_ITEM_CUSTOM_FIELD_0_OPTION_ID and LINE_ITEM_CUSTOM_FIELD_0_VALUE.
  repeated int64 line_item_custom_field_ids = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom field IDs that represent ORDER_CUSTOM_FIELD_*
  // dimensions. The index of this repeated field corresponds to the index on
  // each dimension. For example, order_custom_field_ids[0] describes
  // ORDER_CUSTOM_FIELD_0_OPTION_ID and ORDER_CUSTOM_FIELD_0_VALUE.
  repeated int64 order_custom_field_ids = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom field IDs that represent CREATIVE_CUSTOM_FIELD_*
  // dimensions. The index of this repeated field corresponds to the index on
  // each dimension. For example, creative_custom_field_ids[0] describes
  // CREATIVE_CUSTOM_FIELD_0_OPTION_ID and CREATIVE_CUSTOM_FIELD_0_VALUE.
  repeated int64 creative_custom_field_ids = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The type of this report.
  Report.ReportType report_type = 8 [(google.api.field_behavior) = REQUIRED];

  // Optional. Include a time period column to introduce comparison columns in
  // the report for each generated period. For example, set to  "QUARTERS" here
  // to have a column for each quarter present in the primary date range. If
  // "PREVIOUS PERIOD" is specified in comparison_date_range, then each quarter
  // column will also include comparison values for its relative previous
  // quarter.
  Report.TimePeriodColumn time_period_column = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. List of flags for this report. Used to flag rows in a result set
  // based on a set of defined filters.
  repeated Report.Flag flags = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Default sorts to apply to this report.
  repeated Report.Sort sorts = 15 [(google.api.field_behavior) = OPTIONAL];
}

// The options for a scheduled report.
message ScheduleOptions {
  // Condition for when to email the scheduled report.
  enum DeliveryCondition {
    // Never deliver report.
    NEVER = 0;

    // Always deliver report.
    ALWAYS = 1;

    // Deliver report when flag's conditions are met.
    WHEN_FLAG_CONDITIONS_MET = 2;
  }

  // Information pertaining to schedule itself.
  Schedule schedule = 1;

  // Option for when to deliver the scheduled report.
  DeliveryCondition delivery_condition = 2;

  // Optional. The flags evaluated when ReportDeliveryOption.WHEN_FLAG_PRESENT
  // is specified.
  repeated Report.Flag flags = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The schedule for the report
message Schedule {
  // Days of week to schedule report run.
  message WeeklySchedule {
    // Specifies days of the week on which to run report.
    repeated google.type.DayOfWeek weekly_scheduled_days = 1;
  }

  // Days of Month to schedule report run.
  message MonthlySchedule {
    // Specifies days of the month to run report. Range is from 1-31.
    // Will ignore days that are not valid for the given month.
    repeated int32 monthly_scheduled_days = 1;
  }

  // Frequency to run report.
  enum Frequency {
    // No Frequency specified.
    FREQUENCY_UNSPECIFIED = 0;

    // Schedule report to run every hour.
    HOURLY = 1;

    // Schedule report to run twice a day (every 12 hours).
    TWO_TIMES_DAILY = 2;

    // Schedule report to run three times a day (every 8 hours).
    THREE_TIMES_DAILY = 3;

    // Schedule report to run four times a day (every 6 hours).
    FOUR_TIMES_DAILY = 4;

    // Schedule report to run on a daily basis.
    DAILY = 5;

    // Schedule report to run on a weekly basis.
    WEEKLY = 6;

    // Schedule report to run on a monthly basis.
    MONTHLY = 7;
  }

  // The scheduled values based on the frequency selected.
  // WEEKLY - uses WeeklySchedule
  // MONTHLY - uses MonthlySchedule
  oneof frequency_schedule {
    // Days of week to schedule report run.
    WeeklySchedule weekly_schedule = 6;

    // Days of month to schedule report run.
    MonthlySchedule monthly_schedule = 7;
  }

  // Date for the first run of the report.
  google.type.Date start_date = 1;

  // Date for the final run of the report.
  google.type.Date end_date = 2;

  // Frequency to run report.
  Frequency frequency = 3;

  // Indicates start time for schedule to run
  // Will use the time_zone from `ReportDefinition`. Defaults
  // to the publisher's time zone if not specified.
  //
  // For HOURLY, TWO_TIMES_DAILY, THREE_TIMES_DAILY, or FOUR_TIMES_DAILY, this
  // will be the time of day that the first report will run on the first day.
  // For example, if the start time is 2:00 PM, and the frequency is
  // THREE_TIMES_DAILY, the first day will have reports scheduled at 2:00 PM,
  // 10:00 PM. Each subsequent day will have reports scheduled at 6:00 AM,
  // 2:00 PM, 10:00 PM.
  google.type.TimeOfDay start_time = 4;
}
