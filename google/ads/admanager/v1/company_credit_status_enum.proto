// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "CompanyCreditStatusEnumProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper message for
// [CompanyCreditStatus][google.ads.admanager.v1.CompanyCreditStatusEnum.CompanyCreditStatus]
message CompanyCreditStatusEnum {
  // The credit status of a company.
  //
  // Credit statuses specify the credit-worthiness of a company and affect the
  // ad serving of campaigns belonging to the company.
  //
  // In basic settings, only the
  // [ACTIVE][google.ads.admanager.v1.CompanyCreditStatusEnum.CompanyCreditStatus.ACTIVE]
  // and
  // [INACTIVE][google.ads.admanager.v1.CompanyCreditStatusEnum.CompanyCreditStatus.INACTIVE]
  // credit statuses are applicable. In advance settings, all credit statuses
  // are applicable.
  enum CompanyCreditStatus {
    // No value specified
    COMPANY_CREDIT_STATUS_UNSPECIFIED = 0;

    // The company's credit status is active.
    //
    // Line items belonging to the company can serve.
    //
    // This credit status is the default for basic settings and is available in
    // both basic and advance settings.
    ACTIVE = 1;

    // The company's credit status is inactive.
    //
    // Line items belonging to the company cannot be activated. However, line
    // items that were activated before the credit status changed will remain
    // active. New orders or line items belonging to the company cannot be
    // created.
    //
    // Companies with this credit status will be hidden by default in company
    // search results.
    //
    // This credit status is available in both basic and advance settings.
    INACTIVE = 2;

    // The company's credit status is on hold.
    //
    // Line items belonging to the company cannot be activated. However, line
    // items that were activated before the credit status changed will remain
    // active. New orders or line items belonging to the company can be
    // created.
    //
    // This credit status is the default in advance settings and is only
    // available in advance settings.
    ON_HOLD = 3;

    // The company's credit status is stopped.
    //
    // Line items belonging to the company cannot be activated. However, line
    // items that were activated before the credit status changed will remain
    // active. New orders or line items belonging to the company cannot be
    // created.
    //
    // This credit status is only available in advance settings.
    STOP = 4;

    // The company's credit status is blocked.
    //
    // All active line items belonging to the company will stop serving with
    // immediate effect. Line items belonging to the company cannot be
    // activated, and new orders or line items belonging to the company cannot
    // be created.
    //
    // This credit status is only available in advance settings.
    BLOCKED = 5;
  }
}
