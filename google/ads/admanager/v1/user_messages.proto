// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "UserMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The User resource.
message User {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/User"
    pattern: "networks/{network_code}/users/{user}"
    plural: "users"
    singular: "user"
  };

  // Identifier. The resource name of the User.
  // Format: `networks/{network_code}/users/{user_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. `User` ID.
  int64 user_id = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The name of the User. It has a maximum length of 128 characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The email or login of the User. In order to create a new user,
  // you must already have a Google Account.
  string email = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The unique Role ID of the User. Roles that are created by Google
  // will have negative IDs.
  string role = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/Role" }
  ];

  // Output only. Specifies whether or not the User is active. An inactive user
  // cannot log in to the system or perform any operations.
  bool active = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. An identifier for the User that is meaningful to the publisher.
  // This attribute has a maximum length of 255 characters.
  string external_id = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether the user is an OAuth2 service account user.
  // Service account users can only be added through the UI.
  bool service_account = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The IANA Time Zone Database time zone, e.g. "America/New_York",
  // used in the orders and line items UI for this User. If not provided, the UI
  // then defaults to using the Network's timezone. This setting only affects
  // the UI for this user and does not affect the timezone of any dates and
  // times returned in API responses.
  string orders_ui_local_time_zone = 9 [(google.api.field_behavior) = OPTIONAL];
}
