// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/applied_label.proto";
import "google/ads/admanager/v1/company_credit_status_enum.proto";
import "google/ads/admanager/v1/company_type_enum.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "CompanyMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The `Company` resource.
message Company {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/Company"
    pattern: "networks/{network_code}/companies/{company}"
    plural: "companies"
    singular: "company"
  };

  // Identifier. The resource name of the `Company`.
  // Format: `networks/{network_code}/companies/{company_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. `Company` ID.
  int64 company_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the `Company`.
  //
  // This value has a maximum length of 127 characters.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the `Company`.
  CompanyTypeEnum.CompanyType type = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The address for the `Company`.
  //
  // This value has a maximum length of 1024 characters.
  string address = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The email for the `Company`.
  //
  // This value has a maximum length of 128 characters.
  string email = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The fax number for the `Company`.
  //
  // This value has a maximum length of 63 characters.
  string fax = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The phone number for the `Company`.
  //
  // This value has a maximum length of 63 characters.
  string phone = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The external ID for the `Company`.
  //
  // This value has a maximum length of 255 characters.
  string external_id = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Comments about the `Company`.
  //
  // This value has a maximum length of 1024 characters.
  string comment = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The credit status of the `Company`.
  //
  // This attribute defaults to `ACTIVE` if basic settings are enabled and
  // `ON_HOLD` if advance settings are enabled.
  CompanyCreditStatusEnum.CompanyCreditStatus credit_status = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels that are directly applied to the `Company`.
  repeated AppliedLabel applied_labels = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The resource names of primary Contact of the `Company`.
  // Format: "networks/{network_code}/contacts/{contact_id}"
  optional string primary_contact = 13 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Contact"
    }
  ];

  // Optional. The resource names of Teams that are directly associated with the
  // `Company`. Format: "networks/{network_code}/teams/{team_id}"
  repeated string applied_teams = 14 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/Team" }
  ];

  // Output only. The time the `Company` was last modified.
  google.protobuf.Timestamp update_time = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The ID of the Google-recognized canonicalized form of the
  // `Company`.
  int64 third_party_company_id = 16 [(google.api.field_behavior) = OPTIONAL];
}
