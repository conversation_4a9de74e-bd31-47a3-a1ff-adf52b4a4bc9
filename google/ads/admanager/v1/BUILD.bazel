# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "admanager_proto",
    srcs = [
        "ad_unit_enums.proto",
        "ad_unit_messages.proto",
        "ad_unit_service.proto",
        "admanager_error.proto",
        "applied_label.proto",
        "company_credit_status_enum.proto",
        "company_messages.proto",
        "company_service.proto",
        "company_type_enum.proto",
        "contact_messages.proto",
        "custom_field_enums.proto",
        "custom_field_messages.proto",
        "custom_field_service.proto",
        "custom_field_value.proto",
        "custom_targeting_key_enums.proto",
        "custom_targeting_key_messages.proto",
        "custom_targeting_key_service.proto",
        "custom_targeting_value_enums.proto",
        "custom_targeting_value_messages.proto",
        "custom_targeting_value_service.proto",
        "entity_signals_mapping_messages.proto",
        "entity_signals_mapping_service.proto",
        "environment_type_enum.proto",
        "frequency_cap.proto",
        "label_messages.proto",
        "network_messages.proto",
        "network_service.proto",
        "order_enums.proto",
        "order_messages.proto",
        "order_service.proto",
        "placement_enums.proto",
        "placement_messages.proto",
        "placement_service.proto",
        "report_service.proto",
        "role_enums.proto",
        "role_messages.proto",
        "role_service.proto",
        "size.proto",
        "size_type_enum.proto",
        "taxonomy_category_messages.proto",
        "taxonomy_category_service.proto",
        "taxonomy_type_enum.proto",
        "team_messages.proto",
        "time_unit_enum.proto",
        "user_messages.proto",
        "user_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "//google/type:dayofweek_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "admanager_proto_with_info",
    deps = [
        ":admanager_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_proto_library",
)

java_proto_library(
    name = "admanager_java_proto",
    deps = [":admanager_proto"],
)

java_gapic_library(
    name = "admanager_java_gapic",
    srcs = [":admanager_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "admanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "admanager_v1.yaml",
    test_deps = [
    ],
    transport = "rest",
    deps = [
        ":admanager_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "admanager_java_gapic_test_suite",
    test_classes = [
        "com.google.ads.admanager.v1.AdUnitServiceClientTest",
        "com.google.ads.admanager.v1.CompanyServiceClientTest",
        "com.google.ads.admanager.v1.CustomFieldServiceClientTest",
        "com.google.ads.admanager.v1.CustomTargetingKeyServiceClientTest",
        "com.google.ads.admanager.v1.CustomTargetingValueServiceClientTest",
        "com.google.ads.admanager.v1.EntitySignalsMappingServiceClientTest",
        "com.google.ads.admanager.v1.NetworkServiceClientTest",
        "com.google.ads.admanager.v1.OrderServiceClientTest",
        "com.google.ads.admanager.v1.PlacementServiceClientTest",
        "com.google.ads.admanager.v1.ReportServiceClientTest",
        "com.google.ads.admanager.v1.RoleServiceClientTest",
        "com.google.ads.admanager.v1.TaxonomyCategoryServiceClientTest",
        "com.google.ads.admanager.v1.UserServiceClientTest",
    ],
    runtime_deps = [":admanager_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-ads-admanager-v1-java",
    include_samples = True,
    transport = "rest",
    deps = [
        ":admanager_java_gapic",
        ":admanager_java_proto",
        ":admanager_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "admanager_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/ads/admanager/v1",
    protos = [":admanager_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
        "//google/type:dayofweek_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "admanager_go_gapic",
    srcs = [":admanager_proto_with_info"],
    grpc_service_config = "admanager_grpc_service_config.json",
    importpath = "google.golang.org/google/ads/admanager/v1;admanager",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "admanager_v1.yaml",
    transport = "rest",
    deps = [
        ":admanager_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-ads-admanager-v1-go",
    deps = [
        ":admanager_go_gapic",
        ":admanager_go_gapic_srcjar-metadata.srcjar",
        ":admanager_go_gapic_srcjar-snippets.srcjar",
        ":admanager_go_gapic_srcjar-test.srcjar",
        ":admanager_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "admanager_py_gapic",
    srcs = [":admanager_proto"],
    grpc_service_config = "admanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "admanager_v1.yaml",
    transport = "rest",
    deps = [
    ],
)

py_test(
    name = "admanager_py_gapic_test",
    srcs = [
        "admanager_py_gapic_pytest.py",
        "admanager_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":admanager_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "ads-admanager-v1-py",
    deps = [
        ":admanager_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "admanager_php_proto",
    deps = [":admanager_proto"],
)

php_gapic_library(
    name = "admanager_php_gapic",
    srcs = [":admanager_proto_with_info"],
    grpc_service_config = "admanager_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "admanager_v1.yaml",
    transport = "rest",
    deps = [
        ":admanager_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-ads-admanager-v1-php",
    deps = [
        ":admanager_php_gapic",
        ":admanager_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "admanager_nodejs_gapic",
    package_name = "@google-ads/admanager",
    src = ":admanager_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "admanager_grpc_service_config.json",
    package = "google.ads.admanager.v1",
    rest_numeric_enums = True,
    service_yaml = "admanager_v1.yaml",
    transport = "rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "ads-admanager-v1-nodejs",
    deps = [
        ":admanager_nodejs_gapic",
        ":admanager_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "admanager_ruby_proto",
    deps = [":admanager_proto"],
)

ruby_grpc_library(
    name = "admanager_ruby_grpc",
    srcs = [":admanager_proto"],
    deps = [":admanager_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "admanager_ruby_gapic",
    srcs = [":admanager_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-ads-ad_manager-v1"],
    grpc_service_config = "admanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "admanager_v1.yaml",
    transport = "rest",
    deps = [
        ":admanager_ruby_grpc",
        ":admanager_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-ads-admanager-v1-ruby",
    deps = [
        ":admanager_ruby_gapic",
        ":admanager_ruby_grpc",
        ":admanager_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "admanager_csharp_proto",
    extra_opts = [],
    deps = [":admanager_proto"],
)

csharp_grpc_library(
    name = "admanager_csharp_grpc",
    srcs = [":admanager_proto"],
    deps = [":admanager_csharp_proto"],
)

csharp_gapic_library(
    name = "admanager_csharp_gapic",
    srcs = [":admanager_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "admanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "admanager_v1.yaml",
    transport = "rest",
    deps = [
        ":admanager_csharp_grpc",
        ":admanager_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-ads-admanager-v1-csharp",
    deps = [
        ":admanager_csharp_gapic",
        ":admanager_csharp_grpc",
        ":admanager_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "admanager_cc_proto",
    deps = [":admanager_proto"],
)

cc_grpc_library(
    name = "admanager_cc_grpc",
    srcs = [":admanager_proto"],
    grpc_only = True,
    deps = [":admanager_cc_proto"],
)
