// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "SearchTermInsightErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing search term insight errors.

// Container for enum describing possible search term insight errors.
message SearchTermInsightErrorEnum {
  // Enum describing possible search term insight errors.
  enum SearchTermInsightError {
    // Name unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Search term insights cannot be filtered by metrics when segmenting.
    FILTERING_NOT_ALLOWED_WITH_SEGMENTS = 2;

    // Search term insights cannot have a LIMIT when segmenting.
    LIMIT_NOT_ALLOWED_WITH_SEGMENTS = 3;

    // A selected field requires another field to be selected with it.
    MISSING_FIELD_IN_SELECT_CLAUSE = 4;

    // A selected field/resource requires filtering by a single resource.
    REQUIRES_FILTER_BY_SINGLE_RESOURCE = 5;

    // Search term insights cannot be sorted when segmenting.
    SORTING_NOT_ALLOWED_WITH_SEGMENTS = 6;

    // Search term insights cannot have a summary row when segmenting.
    SUMMARY_ROW_NOT_ALLOWED_WITH_SEGMENTS = 7;
  }
}
