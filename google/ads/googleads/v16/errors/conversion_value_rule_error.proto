// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ConversionValueRuleErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing conversion value rule errors.

// Container for enum describing possible conversion value rule errors.
message ConversionValueRuleErrorEnum {
  // Enum describing possible conversion value rule errors.
  enum ConversionValueRuleError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The value rule's geo location condition contains invalid geo target
    // constant(s), for example, there's no matching geo target.
    INVALID_GEO_TARGET_CONSTANT = 2;

    // The value rule's geo location condition contains conflicting included and
    // excluded geo targets. Specifically, some of the excluded geo target(s)
    // are the same as or contain some of the included geo target(s). For
    // example, the geo location condition includes California but excludes U.S.
    CONFLICTING_INCLUDED_AND_EXCLUDED_GEO_TARGET = 3;

    // User specified conflicting conditions for two value rules in the same
    // value rule set.
    CONFLICTING_CONDITIONS = 4;

    // The value rule cannot be removed because it's still included in some
    // value rule set.
    CANNOT_REMOVE_IF_INCLUDED_IN_VALUE_RULE_SET = 5;

    // The value rule contains a condition that's not allowed by the value rule
    // set including this value rule.
    CONDITION_NOT_ALLOWED = 6;

    // The value rule contains a field that should be unset.
    FIELD_MUST_BE_UNSET = 7;

    // Pausing the value rule requires pausing the value rule set because the
    // value rule is (one of) the last enabled in the value rule set.
    CANNOT_PAUSE_UNLESS_VALUE_RULE_SET_IS_PAUSED = 8;

    // The value rule's geo location condition contains untargetable geo target
    // constant(s).
    UNTARGETABLE_GEO_TARGET = 9;

    // The value rule's audience condition contains invalid user list(s). In
    // another word, there's no matching user list.
    INVALID_AUDIENCE_USER_LIST = 10;

    // The value rule's audience condition contains inaccessible user list(s).
    INACCESSIBLE_USER_LIST = 11;

    // The value rule's audience condition contains invalid user_interest(s).
    // This might be because there is no matching user interest, or the user
    // interest is not visible.
    INVALID_AUDIENCE_USER_INTEREST = 12;

    // When a value rule is created, it shouldn't have REMOVED status.
    CANNOT_ADD_RULE_WITH_STATUS_REMOVED = 13;
  }
}
