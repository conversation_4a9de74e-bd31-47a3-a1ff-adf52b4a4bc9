// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CampaignBudgetErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing campaign budget errors.

// Container for enum describing possible campaign budget errors.
message CampaignBudgetErrorEnum {
  // Enum describing possible campaign budget errors.
  enum CampaignBudgetError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The campaign budget cannot be shared.
    CAMPAIGN_BUDGET_CANNOT_BE_SHARED = 17;

    // The requested campaign budget no longer exists.
    CAMPAIGN_BUDGET_REMOVED = 2;

    // The campaign budget is associated with at least one campaign, and so the
    // campaign budget cannot be removed.
    CAMPAIGN_BUDGET_IN_USE = 3;

    // Customer is not on the allow-list for this campaign budget period.
    CAMPAIGN_BUDGET_PERIOD_NOT_AVAILABLE = 4;

    // This field is not mutable on implicitly shared campaign budgets
    CANNOT_MODIFY_FIELD_OF_IMPLICITLY_SHARED_CAMPAIGN_BUDGET = 6;

    // Cannot change explicitly shared campaign budgets back to implicitly
    // shared ones.
    CANNOT_UPDATE_CAMPAIGN_BUDGET_TO_IMPLICITLY_SHARED = 7;

    // An implicit campaign budget without a name cannot be changed to
    // explicitly shared campaign budget.
    CANNOT_UPDATE_CAMPAIGN_BUDGET_TO_EXPLICITLY_SHARED_WITHOUT_NAME = 8;

    // Cannot change an implicitly shared campaign budget to an explicitly
    // shared one.
    CANNOT_UPDATE_CAMPAIGN_BUDGET_TO_EXPLICITLY_SHARED = 9;

    // Only explicitly shared campaign budgets can be used with multiple
    // campaigns.
    CANNOT_USE_IMPLICITLY_SHARED_CAMPAIGN_BUDGET_WITH_MULTIPLE_CAMPAIGNS = 10;

    // A campaign budget with this name already exists.
    DUPLICATE_NAME = 11;

    // A money amount was not in the expected currency.
    MONEY_AMOUNT_IN_WRONG_CURRENCY = 12;

    // A money amount was less than the minimum CPC for currency.
    MONEY_AMOUNT_LESS_THAN_CURRENCY_MINIMUM_CPC = 13;

    // A money amount was greater than the maximum allowed.
    MONEY_AMOUNT_TOO_LARGE = 14;

    // A money amount was negative.
    NEGATIVE_MONEY_AMOUNT = 15;

    // A money amount was not a multiple of a minimum unit.
    NON_MULTIPLE_OF_MINIMUM_CURRENCY_UNIT = 16;

    // Total budget amount must be unset when BudgetPeriod is DAILY.
    TOTAL_BUDGET_AMOUNT_MUST_BE_UNSET_FOR_BUDGET_PERIOD_DAILY = 18;

    // The period of the budget is not allowed.
    INVALID_PERIOD = 19;

    // Cannot use accelerated delivery method on this budget.
    CANNOT_USE_ACCELERATED_DELIVERY_MODE = 20;

    // Budget amount must be unset when BudgetPeriod is CUSTOM.
    BUDGET_AMOUNT_MUST_BE_UNSET_FOR_CUSTOM_BUDGET_PERIOD = 21;
  }
}
