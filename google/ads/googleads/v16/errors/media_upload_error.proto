// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "MediaUploadErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing media uploading errors.

// Container for enum describing possible media uploading errors.
message MediaUploadErrorEnum {
  // Enum describing possible media uploading errors.
  enum MediaUploadError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The uploaded file is too big.
    FILE_TOO_BIG = 2;

    // Image data is unparseable.
    UNPARSEABLE_IMAGE = 3;

    // Animated images are not allowed.
    ANIMATED_IMAGE_NOT_ALLOWED = 4;

    // The image or media bundle format is not allowed.
    FORMAT_NOT_ALLOWED = 5;

    // Cannot reference URL external to the media bundle.
    EXTERNAL_URL_NOT_ALLOWED = 6;

    // HTML5 ad is trying to reference an asset not in .ZIP file.
    INVALID_URL_REFERENCE = 7;

    // The media bundle contains no primary entry.
    MISSING_PRIMARY_MEDIA_BUNDLE_ENTRY = 8;

    // Animation has disallowed visual effects.
    ANIMATED_VISUAL_EFFECT = 9;

    // Animation longer than the allowed 30 second limit.
    ANIMATION_TOO_LONG = 10;

    // The aspect ratio of the image does not match the expected aspect ratios
    // provided in the asset spec.
    ASPECT_RATIO_NOT_ALLOWED = 11;

    // Audio files are not allowed in bundle.
    AUDIO_NOT_ALLOWED_IN_MEDIA_BUNDLE = 12;

    // CMYK jpegs are not supported.
    CMYK_JPEG_NOT_ALLOWED = 13;

    // Flash movies are not allowed.
    FLASH_NOT_ALLOWED = 14;

    // The frame rate of the video is higher than the allowed 5fps.
    FRAME_RATE_TOO_HIGH = 15;

    // ZIP file from Google Web Designer is not published.
    GOOGLE_WEB_DESIGNER_ZIP_FILE_NOT_PUBLISHED = 16;

    // Image constraints are violated, but more details (like
    // DIMENSIONS_NOT_ALLOWED or ASPECT_RATIO_NOT_ALLOWED) can not be provided.
    // This happens when asset spec contains more than one constraint and
    // criteria of different constraints are violated.
    IMAGE_CONSTRAINTS_VIOLATED = 17;

    // Media bundle data is unrecognizable.
    INVALID_MEDIA_BUNDLE = 18;

    // There was a problem with one or more of the media bundle entries.
    INVALID_MEDIA_BUNDLE_ENTRY = 19;

    // The asset has an invalid mime type.
    INVALID_MIME_TYPE = 20;

    // The media bundle contains an invalid asset path.
    INVALID_PATH = 21;

    // Image has layout problem.
    LAYOUT_PROBLEM = 22;

    // An asset had a URL reference that is malformed per RFC 1738 convention.
    MALFORMED_URL = 23;

    // The uploaded media bundle format is not allowed.
    MEDIA_BUNDLE_NOT_ALLOWED = 24;

    // The media bundle is not compatible with the asset spec product type.
    // (For example, Gmail, dynamic remarketing, etc.)
    MEDIA_BUNDLE_NOT_COMPATIBLE_TO_PRODUCT_TYPE = 25;

    // A bundle being uploaded that is incompatible with multiple assets for
    // different reasons.
    MEDIA_BUNDLE_REJECTED_BY_MULTIPLE_ASSET_SPECS = 26;

    // The media bundle contains too many files.
    TOO_MANY_FILES_IN_MEDIA_BUNDLE = 27;

    // Google Web Designer not created for "Google Ads" environment.
    UNSUPPORTED_GOOGLE_WEB_DESIGNER_ENVIRONMENT = 28;

    // Unsupported HTML5 feature in HTML5 asset.
    UNSUPPORTED_HTML5_FEATURE = 29;

    // URL in HTML5 entry is not SSL compliant.
    URL_IN_MEDIA_BUNDLE_NOT_SSL_COMPLIANT = 30;

    // Video file name is longer than the 50 allowed characters.
    VIDEO_FILE_NAME_TOO_LONG = 31;

    // Multiple videos with same name in a bundle.
    VIDEO_MULTIPLE_FILES_WITH_SAME_NAME = 32;

    // Videos are not allowed in media bundle.
    VIDEO_NOT_ALLOWED_IN_MEDIA_BUNDLE = 33;

    // This type of media cannot be uploaded through the Google Ads API.
    CANNOT_UPLOAD_MEDIA_TYPE_THROUGH_API = 34;

    // The dimensions of the image are not allowed.
    DIMENSIONS_NOT_ALLOWED = 35;
  }
}
