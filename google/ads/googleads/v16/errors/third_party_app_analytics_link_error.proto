// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ThirdPartyAppAnalyticsLinkErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing ThirdPartyAppAnalyticsLink errors.

// Container for enum describing possible third party app analytics link errors.
message ThirdPartyAppAnalyticsLinkErrorEnum {
  // Enum describing possible third party app analytics link errors.
  enum ThirdPartyAppAnalyticsLinkError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The provided analytics provider ID is invalid.
    INVALID_ANALYTICS_PROVIDER_ID = 2;

    // The provided mobile app ID is invalid.
    INVALID_MOBILE_APP_ID = 3;

    // The mobile app corresponding to the provided app ID is not
    // active/enabled.
    MOBILE_APP_IS_NOT_ENABLED = 4;

    // Regenerating shareable link ID is only allowed on active links
    CANNOT_REGENERATE_SHAREABLE_LINK_ID_FOR_REMOVED_LINK = 5;
  }
}
