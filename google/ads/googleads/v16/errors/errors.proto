// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

import "google/ads/googleads/v16/common/policy.proto";
import "google/ads/googleads/v16/common/value.proto";
import "google/ads/googleads/v16/enums/resource_limit_type.proto";
import "google/ads/googleads/v16/errors/access_invitation_error.proto";
import "google/ads/googleads/v16/errors/account_budget_proposal_error.proto";
import "google/ads/googleads/v16/errors/account_link_error.proto";
import "google/ads/googleads/v16/errors/ad_customizer_error.proto";
import "google/ads/googleads/v16/errors/ad_error.proto";
import "google/ads/googleads/v16/errors/ad_group_ad_error.proto";
import "google/ads/googleads/v16/errors/ad_group_bid_modifier_error.proto";
import "google/ads/googleads/v16/errors/ad_group_criterion_customizer_error.proto";
import "google/ads/googleads/v16/errors/ad_group_criterion_error.proto";
import "google/ads/googleads/v16/errors/ad_group_customizer_error.proto";
import "google/ads/googleads/v16/errors/ad_group_error.proto";
import "google/ads/googleads/v16/errors/ad_group_feed_error.proto";
import "google/ads/googleads/v16/errors/ad_parameter_error.proto";
import "google/ads/googleads/v16/errors/ad_sharing_error.proto";
import "google/ads/googleads/v16/errors/adx_error.proto";
import "google/ads/googleads/v16/errors/asset_error.proto";
import "google/ads/googleads/v16/errors/asset_group_asset_error.proto";
import "google/ads/googleads/v16/errors/asset_group_error.proto";
import "google/ads/googleads/v16/errors/asset_group_listing_group_filter_error.proto";
import "google/ads/googleads/v16/errors/asset_group_signal_error.proto";
import "google/ads/googleads/v16/errors/asset_link_error.proto";
import "google/ads/googleads/v16/errors/asset_set_asset_error.proto";
import "google/ads/googleads/v16/errors/asset_set_error.proto";
import "google/ads/googleads/v16/errors/asset_set_link_error.proto";
import "google/ads/googleads/v16/errors/audience_error.proto";
import "google/ads/googleads/v16/errors/audience_insights_error.proto";
import "google/ads/googleads/v16/errors/authentication_error.proto";
import "google/ads/googleads/v16/errors/authorization_error.proto";
import "google/ads/googleads/v16/errors/batch_job_error.proto";
import "google/ads/googleads/v16/errors/bidding_error.proto";
import "google/ads/googleads/v16/errors/bidding_strategy_error.proto";
import "google/ads/googleads/v16/errors/billing_setup_error.proto";
import "google/ads/googleads/v16/errors/campaign_budget_error.proto";
import "google/ads/googleads/v16/errors/campaign_conversion_goal_error.proto";
import "google/ads/googleads/v16/errors/campaign_criterion_error.proto";
import "google/ads/googleads/v16/errors/campaign_customizer_error.proto";
import "google/ads/googleads/v16/errors/campaign_draft_error.proto";
import "google/ads/googleads/v16/errors/campaign_error.proto";
import "google/ads/googleads/v16/errors/campaign_experiment_error.proto";
import "google/ads/googleads/v16/errors/campaign_feed_error.proto";
import "google/ads/googleads/v16/errors/campaign_lifecycle_goal_error.proto";
import "google/ads/googleads/v16/errors/campaign_shared_set_error.proto";
import "google/ads/googleads/v16/errors/change_event_error.proto";
import "google/ads/googleads/v16/errors/change_status_error.proto";
import "google/ads/googleads/v16/errors/collection_size_error.proto";
import "google/ads/googleads/v16/errors/context_error.proto";
import "google/ads/googleads/v16/errors/conversion_action_error.proto";
import "google/ads/googleads/v16/errors/conversion_adjustment_upload_error.proto";
import "google/ads/googleads/v16/errors/conversion_custom_variable_error.proto";
import "google/ads/googleads/v16/errors/conversion_goal_campaign_config_error.proto";
import "google/ads/googleads/v16/errors/conversion_upload_error.proto";
import "google/ads/googleads/v16/errors/conversion_value_rule_error.proto";
import "google/ads/googleads/v16/errors/conversion_value_rule_set_error.proto";
import "google/ads/googleads/v16/errors/country_code_error.proto";
import "google/ads/googleads/v16/errors/criterion_error.proto";
import "google/ads/googleads/v16/errors/currency_code_error.proto";
import "google/ads/googleads/v16/errors/currency_error.proto";
import "google/ads/googleads/v16/errors/custom_audience_error.proto";
import "google/ads/googleads/v16/errors/custom_conversion_goal_error.proto";
import "google/ads/googleads/v16/errors/custom_interest_error.proto";
import "google/ads/googleads/v16/errors/customer_client_link_error.proto";
import "google/ads/googleads/v16/errors/customer_customizer_error.proto";
import "google/ads/googleads/v16/errors/customer_error.proto";
import "google/ads/googleads/v16/errors/customer_feed_error.proto";
import "google/ads/googleads/v16/errors/customer_lifecycle_goal_error.proto";
import "google/ads/googleads/v16/errors/customer_manager_link_error.proto";
import "google/ads/googleads/v16/errors/customer_sk_ad_network_conversion_value_schema_error.proto";
import "google/ads/googleads/v16/errors/customer_user_access_error.proto";
import "google/ads/googleads/v16/errors/customizer_attribute_error.proto";
import "google/ads/googleads/v16/errors/database_error.proto";
import "google/ads/googleads/v16/errors/date_error.proto";
import "google/ads/googleads/v16/errors/date_range_error.proto";
import "google/ads/googleads/v16/errors/distinct_error.proto";
import "google/ads/googleads/v16/errors/enum_error.proto";
import "google/ads/googleads/v16/errors/experiment_arm_error.proto";
import "google/ads/googleads/v16/errors/experiment_error.proto";
import "google/ads/googleads/v16/errors/extension_feed_item_error.proto";
import "google/ads/googleads/v16/errors/extension_setting_error.proto";
import "google/ads/googleads/v16/errors/feed_attribute_reference_error.proto";
import "google/ads/googleads/v16/errors/feed_error.proto";
import "google/ads/googleads/v16/errors/feed_item_error.proto";
import "google/ads/googleads/v16/errors/feed_item_set_error.proto";
import "google/ads/googleads/v16/errors/feed_item_set_link_error.proto";
import "google/ads/googleads/v16/errors/feed_item_target_error.proto";
import "google/ads/googleads/v16/errors/feed_item_validation_error.proto";
import "google/ads/googleads/v16/errors/feed_mapping_error.proto";
import "google/ads/googleads/v16/errors/field_error.proto";
import "google/ads/googleads/v16/errors/field_mask_error.proto";
import "google/ads/googleads/v16/errors/function_error.proto";
import "google/ads/googleads/v16/errors/function_parsing_error.proto";
import "google/ads/googleads/v16/errors/geo_target_constant_suggestion_error.proto";
import "google/ads/googleads/v16/errors/header_error.proto";
import "google/ads/googleads/v16/errors/id_error.proto";
import "google/ads/googleads/v16/errors/identity_verification_error.proto";
import "google/ads/googleads/v16/errors/image_error.proto";
import "google/ads/googleads/v16/errors/internal_error.proto";
import "google/ads/googleads/v16/errors/invoice_error.proto";
import "google/ads/googleads/v16/errors/keyword_plan_ad_group_error.proto";
import "google/ads/googleads/v16/errors/keyword_plan_ad_group_keyword_error.proto";
import "google/ads/googleads/v16/errors/keyword_plan_campaign_error.proto";
import "google/ads/googleads/v16/errors/keyword_plan_campaign_keyword_error.proto";
import "google/ads/googleads/v16/errors/keyword_plan_error.proto";
import "google/ads/googleads/v16/errors/keyword_plan_idea_error.proto";
import "google/ads/googleads/v16/errors/label_error.proto";
import "google/ads/googleads/v16/errors/language_code_error.proto";
import "google/ads/googleads/v16/errors/list_operation_error.proto";
import "google/ads/googleads/v16/errors/manager_link_error.proto";
import "google/ads/googleads/v16/errors/media_bundle_error.proto";
import "google/ads/googleads/v16/errors/media_file_error.proto";
import "google/ads/googleads/v16/errors/media_upload_error.proto";
import "google/ads/googleads/v16/errors/merchant_center_error.proto";
import "google/ads/googleads/v16/errors/multiplier_error.proto";
import "google/ads/googleads/v16/errors/mutate_error.proto";
import "google/ads/googleads/v16/errors/new_resource_creation_error.proto";
import "google/ads/googleads/v16/errors/not_allowlisted_error.proto";
import "google/ads/googleads/v16/errors/not_empty_error.proto";
import "google/ads/googleads/v16/errors/null_error.proto";
import "google/ads/googleads/v16/errors/offline_user_data_job_error.proto";
import "google/ads/googleads/v16/errors/operation_access_denied_error.proto";
import "google/ads/googleads/v16/errors/operator_error.proto";
import "google/ads/googleads/v16/errors/partial_failure_error.proto";
import "google/ads/googleads/v16/errors/payments_account_error.proto";
import "google/ads/googleads/v16/errors/policy_finding_error.proto";
import "google/ads/googleads/v16/errors/policy_validation_parameter_error.proto";
import "google/ads/googleads/v16/errors/policy_violation_error.proto";
import "google/ads/googleads/v16/errors/product_link_error.proto";
import "google/ads/googleads/v16/errors/product_link_invitation_error.proto";
import "google/ads/googleads/v16/errors/query_error.proto";
import "google/ads/googleads/v16/errors/quota_error.proto";
import "google/ads/googleads/v16/errors/range_error.proto";
import "google/ads/googleads/v16/errors/reach_plan_error.proto";
import "google/ads/googleads/v16/errors/recommendation_error.proto";
import "google/ads/googleads/v16/errors/recommendation_subscription_error.proto";
import "google/ads/googleads/v16/errors/region_code_error.proto";
import "google/ads/googleads/v16/errors/request_error.proto";
import "google/ads/googleads/v16/errors/resource_access_denied_error.proto";
import "google/ads/googleads/v16/errors/resource_count_limit_exceeded_error.proto";
import "google/ads/googleads/v16/errors/search_term_insight_error.proto";
import "google/ads/googleads/v16/errors/setting_error.proto";
import "google/ads/googleads/v16/errors/shared_criterion_error.proto";
import "google/ads/googleads/v16/errors/shared_set_error.proto";
import "google/ads/googleads/v16/errors/size_limit_error.proto";
import "google/ads/googleads/v16/errors/smart_campaign_error.proto";
import "google/ads/googleads/v16/errors/string_format_error.proto";
import "google/ads/googleads/v16/errors/string_length_error.proto";
import "google/ads/googleads/v16/errors/third_party_app_analytics_link_error.proto";
import "google/ads/googleads/v16/errors/time_zone_error.proto";
import "google/ads/googleads/v16/errors/url_field_error.proto";
import "google/ads/googleads/v16/errors/user_data_error.proto";
import "google/ads/googleads/v16/errors/user_list_error.proto";
import "google/ads/googleads/v16/errors/video_campaign_error.proto";
import "google/ads/googleads/v16/errors/youtube_video_registration_error.proto";
import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ErrorsProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing the common error protos

// Describes how a GoogleAds API call failed. It's returned inside
// google.rpc.Status.details when a call fails.
message GoogleAdsFailure {
  // The list of errors that occurred.
  repeated GoogleAdsError errors = 1;

  // The unique ID of the request that is used for debugging purposes.
  string request_id = 2;
}

// GoogleAds-specific error.
message GoogleAdsError {
  // An enum value that indicates which error occurred.
  ErrorCode error_code = 1;

  // A human-readable description of the error.
  string message = 2;

  // The value that triggered the error.
  google.ads.googleads.v16.common.Value trigger = 3;

  // Describes the part of the request proto that caused the error.
  ErrorLocation location = 4;

  // Additional error details, which are returned by certain error codes. Most
  // error codes do not include details.
  ErrorDetails details = 5;
}

// The error reason represented by type and enum.
message ErrorCode {
  // The list of error enums
  oneof error_code {
    // An error caused by the request
    RequestErrorEnum.RequestError request_error = 1;

    // An error with a Bidding Strategy mutate.
    BiddingStrategyErrorEnum.BiddingStrategyError bidding_strategy_error = 2;

    // An error with a URL field mutate.
    UrlFieldErrorEnum.UrlFieldError url_field_error = 3;

    // An error with a list operation.
    ListOperationErrorEnum.ListOperationError list_operation_error = 4;

    // An error with an AWQL query
    QueryErrorEnum.QueryError query_error = 5;

    // An error with a mutate
    MutateErrorEnum.MutateError mutate_error = 7;

    // An error with a field mask
    FieldMaskErrorEnum.FieldMaskError field_mask_error = 8;

    // An error encountered when trying to authorize a user.
    AuthorizationErrorEnum.AuthorizationError authorization_error = 9;

    // An unexpected server-side error.
    InternalErrorEnum.InternalError internal_error = 10;

    // An error with the amount of quota remaining.
    QuotaErrorEnum.QuotaError quota_error = 11;

    // An error with an Ad Group Ad mutate.
    AdErrorEnum.AdError ad_error = 12;

    // An error with an Ad Group mutate.
    AdGroupErrorEnum.AdGroupError ad_group_error = 13;

    // An error with a Campaign Budget mutate.
    CampaignBudgetErrorEnum.CampaignBudgetError campaign_budget_error = 14;

    // An error with a Campaign mutate.
    CampaignErrorEnum.CampaignError campaign_error = 15;

    // An error with a Video Campaign mutate.
    VideoCampaignErrorEnum.VideoCampaignError video_campaign_error = 182;

    // Indicates failure to properly authenticate user.
    AuthenticationErrorEnum.AuthenticationError authentication_error = 17;

    // The reasons for the ad group criterion customizer error.
    AdGroupCriterionCustomizerErrorEnum.AdGroupCriterionCustomizerError
        ad_group_criterion_customizer_error = 161;

    // Indicates failure to properly authenticate user.
    AdGroupCriterionErrorEnum.AdGroupCriterionError ad_group_criterion_error =
        18;

    // The reasons for the ad group customizer error.
    AdGroupCustomizerErrorEnum.AdGroupCustomizerError
        ad_group_customizer_error = 159;

    // The reasons for the ad customizer error
    AdCustomizerErrorEnum.AdCustomizerError ad_customizer_error = 19;

    // The reasons for the ad group ad error
    AdGroupAdErrorEnum.AdGroupAdError ad_group_ad_error = 21;

    // The reasons for the ad sharing error
    AdSharingErrorEnum.AdSharingError ad_sharing_error = 24;

    // The reasons for the adx error
    AdxErrorEnum.AdxError adx_error = 25;

    // The reasons for the asset error
    AssetErrorEnum.AssetError asset_error = 107;

    // The reasons for the asset group asset error
    AssetGroupAssetErrorEnum.AssetGroupAssetError asset_group_asset_error = 149;

    // The reasons for the asset group listing group filter error
    AssetGroupListingGroupFilterErrorEnum.AssetGroupListingGroupFilterError
        asset_group_listing_group_filter_error = 155;

    // The reasons for the asset group error
    AssetGroupErrorEnum.AssetGroupError asset_group_error = 148;

    // The reasons for the asset set asset error
    AssetSetAssetErrorEnum.AssetSetAssetError asset_set_asset_error = 153;

    // The reasons for the asset set link error
    AssetSetLinkErrorEnum.AssetSetLinkError asset_set_link_error = 154;

    // The reasons for the asset set error
    AssetSetErrorEnum.AssetSetError asset_set_error = 152;

    // The reasons for the bidding errors
    BiddingErrorEnum.BiddingError bidding_error = 26;

    // The reasons for the campaign criterion error
    CampaignCriterionErrorEnum.CampaignCriterionError campaign_criterion_error =
        29;

    // The reasons for the campaign conversion goal error
    CampaignConversionGoalErrorEnum.CampaignConversionGoalError
        campaign_conversion_goal_error = 166;

    // The reasons for the campaign customizer error.
    CampaignCustomizerErrorEnum.CampaignCustomizerError
        campaign_customizer_error = 160;

    // The reasons for the collection size error
    CollectionSizeErrorEnum.CollectionSizeError collection_size_error = 31;

    // The reasons for the conversion goal campaign config error
    ConversionGoalCampaignConfigErrorEnum.ConversionGoalCampaignConfigError
        conversion_goal_campaign_config_error = 165;

    // The reasons for the country code error
    CountryCodeErrorEnum.CountryCodeError country_code_error = 109;

    // The reasons for the criterion error
    CriterionErrorEnum.CriterionError criterion_error = 32;

    // The reasons for the custom conversion goal error
    CustomConversionGoalErrorEnum.CustomConversionGoalError
        custom_conversion_goal_error = 150;

    // The reasons for the customer customizer error.
    CustomerCustomizerErrorEnum.CustomerCustomizerError
        customer_customizer_error = 158;

    // The reasons for the customer error
    CustomerErrorEnum.CustomerError customer_error = 90;

    // The reasons for the customizer attribute error.
    CustomizerAttributeErrorEnum.CustomizerAttributeError
        customizer_attribute_error = 151;

    // The reasons for the date error
    DateErrorEnum.DateError date_error = 33;

    // The reasons for the date range error
    DateRangeErrorEnum.DateRangeError date_range_error = 34;

    // The reasons for the distinct error
    DistinctErrorEnum.DistinctError distinct_error = 35;

    // The reasons for the feed attribute reference error
    FeedAttributeReferenceErrorEnum.FeedAttributeReferenceError
        feed_attribute_reference_error = 36;

    // The reasons for the function error
    FunctionErrorEnum.FunctionError function_error = 37;

    // The reasons for the function parsing error
    FunctionParsingErrorEnum.FunctionParsingError function_parsing_error = 38;

    // The reasons for the id error
    IdErrorEnum.IdError id_error = 39;

    // The reasons for the image error
    ImageErrorEnum.ImageError image_error = 40;

    // The reasons for the language code error
    LanguageCodeErrorEnum.LanguageCodeError language_code_error = 110;

    // The reasons for the media bundle error
    MediaBundleErrorEnum.MediaBundleError media_bundle_error = 42;

    // The reasons for media uploading errors.
    MediaUploadErrorEnum.MediaUploadError media_upload_error = 116;

    // The reasons for the media file error
    MediaFileErrorEnum.MediaFileError media_file_error = 86;

    // Container for enum describing possible merchant center errors.
    MerchantCenterErrorEnum.MerchantCenterError merchant_center_error = 162;

    // The reasons for the multiplier error
    MultiplierErrorEnum.MultiplierError multiplier_error = 44;

    // The reasons for the new resource creation error
    NewResourceCreationErrorEnum.NewResourceCreationError
        new_resource_creation_error = 45;

    // The reasons for the not empty error
    NotEmptyErrorEnum.NotEmptyError not_empty_error = 46;

    // The reasons for the null error
    NullErrorEnum.NullError null_error = 47;

    // The reasons for the operator error
    OperatorErrorEnum.OperatorError operator_error = 48;

    // The reasons for the range error
    RangeErrorEnum.RangeError range_error = 49;

    // The reasons for error in applying a recommendation
    RecommendationErrorEnum.RecommendationError recommendation_error = 58;

    // The reasons for the recommendation subscription error.
    RecommendationSubscriptionErrorEnum.RecommendationSubscriptionError
        recommendation_subscription_error = 180;

    // The reasons for the region code error
    RegionCodeErrorEnum.RegionCodeError region_code_error = 51;

    // The reasons for the setting error
    SettingErrorEnum.SettingError setting_error = 52;

    // The reasons for the string format error
    StringFormatErrorEnum.StringFormatError string_format_error = 53;

    // The reasons for the string length error
    StringLengthErrorEnum.StringLengthError string_length_error = 54;

    // The reasons for the operation access denied error
    OperationAccessDeniedErrorEnum.OperationAccessDeniedError
        operation_access_denied_error = 55;

    // The reasons for the resource access denied error
    ResourceAccessDeniedErrorEnum.ResourceAccessDeniedError
        resource_access_denied_error = 56;

    // The reasons for the resource count limit exceeded error
    ResourceCountLimitExceededErrorEnum.ResourceCountLimitExceededError
        resource_count_limit_exceeded_error = 57;

    // The reasons for YouTube video registration errors.
    YoutubeVideoRegistrationErrorEnum.YoutubeVideoRegistrationError
        youtube_video_registration_error = 117;

    // The reasons for the ad group bid modifier error
    AdGroupBidModifierErrorEnum.AdGroupBidModifierError
        ad_group_bid_modifier_error = 59;

    // The reasons for the context error
    ContextErrorEnum.ContextError context_error = 60;

    // The reasons for the field error
    FieldErrorEnum.FieldError field_error = 61;

    // The reasons for the shared set error
    SharedSetErrorEnum.SharedSetError shared_set_error = 62;

    // The reasons for the shared criterion error
    SharedCriterionErrorEnum.SharedCriterionError shared_criterion_error = 63;

    // The reasons for the campaign shared set error
    CampaignSharedSetErrorEnum.CampaignSharedSetError
        campaign_shared_set_error = 64;

    // The reasons for the conversion action error
    ConversionActionErrorEnum.ConversionActionError conversion_action_error =
        65;

    // The reasons for the conversion adjustment upload error
    ConversionAdjustmentUploadErrorEnum.ConversionAdjustmentUploadError
        conversion_adjustment_upload_error = 115;

    // The reasons for the conversion custom variable error
    ConversionCustomVariableErrorEnum.ConversionCustomVariableError
        conversion_custom_variable_error = 143;

    // The reasons for the conversion upload error
    ConversionUploadErrorEnum.ConversionUploadError conversion_upload_error =
        111;

    // The reasons for the conversion value rule error
    ConversionValueRuleErrorEnum.ConversionValueRuleError
        conversion_value_rule_error = 145;

    // The reasons for the conversion value rule set error
    ConversionValueRuleSetErrorEnum.ConversionValueRuleSetError
        conversion_value_rule_set_error = 146;

    // The reasons for the header error.
    HeaderErrorEnum.HeaderError header_error = 66;

    // The reasons for the database error.
    DatabaseErrorEnum.DatabaseError database_error = 67;

    // The reasons for the policy finding error.
    PolicyFindingErrorEnum.PolicyFindingError policy_finding_error = 68;

    // The reason for enum error.
    EnumErrorEnum.EnumError enum_error = 70;

    // The reason for keyword plan error.
    KeywordPlanErrorEnum.KeywordPlanError keyword_plan_error = 71;

    // The reason for keyword plan campaign error.
    KeywordPlanCampaignErrorEnum.KeywordPlanCampaignError
        keyword_plan_campaign_error = 72;

    // The reason for keyword plan campaign keyword error.
    KeywordPlanCampaignKeywordErrorEnum.KeywordPlanCampaignKeywordError
        keyword_plan_campaign_keyword_error = 132;

    // The reason for keyword plan ad group error.
    KeywordPlanAdGroupErrorEnum.KeywordPlanAdGroupError
        keyword_plan_ad_group_error = 74;

    // The reason for keyword plan ad group keyword error.
    KeywordPlanAdGroupKeywordErrorEnum.KeywordPlanAdGroupKeywordError
        keyword_plan_ad_group_keyword_error = 133;

    // The reason for keyword idea error.
    KeywordPlanIdeaErrorEnum.KeywordPlanIdeaError keyword_plan_idea_error = 76;

    // The reasons for account budget proposal errors.
    AccountBudgetProposalErrorEnum.AccountBudgetProposalError
        account_budget_proposal_error = 77;

    // The reasons for the user list error
    UserListErrorEnum.UserListError user_list_error = 78;

    // The reasons for the change event error
    ChangeEventErrorEnum.ChangeEventError change_event_error = 136;

    // The reasons for the change status error
    ChangeStatusErrorEnum.ChangeStatusError change_status_error = 79;

    // The reasons for the feed error
    FeedErrorEnum.FeedError feed_error = 80;

    // The reasons for the geo target constant suggestion error.
    GeoTargetConstantSuggestionErrorEnum.GeoTargetConstantSuggestionError
        geo_target_constant_suggestion_error = 81;

    // The reasons for the campaign draft error
    CampaignDraftErrorEnum.CampaignDraftError campaign_draft_error = 82;

    // The reasons for the feed item error
    FeedItemErrorEnum.FeedItemError feed_item_error = 83;

    // The reason for the label error.
    LabelErrorEnum.LabelError label_error = 84;

    // The reasons for the billing setup error
    BillingSetupErrorEnum.BillingSetupError billing_setup_error = 87;

    // The reasons for the customer client link error
    CustomerClientLinkErrorEnum.CustomerClientLinkError
        customer_client_link_error = 88;

    // The reasons for the customer manager link error
    CustomerManagerLinkErrorEnum.CustomerManagerLinkError
        customer_manager_link_error = 91;

    // The reasons for the feed mapping error
    FeedMappingErrorEnum.FeedMappingError feed_mapping_error = 92;

    // The reasons for the customer feed error
    CustomerFeedErrorEnum.CustomerFeedError customer_feed_error = 93;

    // The reasons for the ad group feed error
    AdGroupFeedErrorEnum.AdGroupFeedError ad_group_feed_error = 94;

    // The reasons for the campaign feed error
    CampaignFeedErrorEnum.CampaignFeedError campaign_feed_error = 96;

    // The reasons for the custom interest error
    CustomInterestErrorEnum.CustomInterestError custom_interest_error = 97;

    // The reasons for the campaign experiment error
    CampaignExperimentErrorEnum.CampaignExperimentError
        campaign_experiment_error = 98;

    // The reasons for the extension feed item error
    ExtensionFeedItemErrorEnum.ExtensionFeedItemError
        extension_feed_item_error = 100;

    // The reasons for the ad parameter error
    AdParameterErrorEnum.AdParameterError ad_parameter_error = 101;

    // The reasons for the feed item validation error
    FeedItemValidationErrorEnum.FeedItemValidationError
        feed_item_validation_error = 102;

    // The reasons for the extension setting error
    ExtensionSettingErrorEnum.ExtensionSettingError extension_setting_error =
        103;

    // The reasons for the feed item set error
    FeedItemSetErrorEnum.FeedItemSetError feed_item_set_error = 140;

    // The reasons for the feed item set link error
    FeedItemSetLinkErrorEnum.FeedItemSetLinkError feed_item_set_link_error =
        141;

    // The reasons for the feed item target error
    FeedItemTargetErrorEnum.FeedItemTargetError feed_item_target_error = 104;

    // The reasons for the policy violation error
    PolicyViolationErrorEnum.PolicyViolationError policy_violation_error = 105;

    // The reasons for the mutate job error
    PartialFailureErrorEnum.PartialFailureError partial_failure_error = 112;

    // The reasons for the policy validation parameter error
    PolicyValidationParameterErrorEnum.PolicyValidationParameterError
        policy_validation_parameter_error = 114;

    // The reasons for the size limit error
    SizeLimitErrorEnum.SizeLimitError size_limit_error = 118;

    // The reasons for the offline user data job error.
    OfflineUserDataJobErrorEnum.OfflineUserDataJobError
        offline_user_data_job_error = 119;

    // The reasons for the not allowlisted error
    NotAllowlistedErrorEnum.NotAllowlistedError not_allowlisted_error = 137;

    // The reasons for the manager link error
    ManagerLinkErrorEnum.ManagerLinkError manager_link_error = 121;

    // The reasons for the currency code error
    CurrencyCodeErrorEnum.CurrencyCodeError currency_code_error = 122;

    // The reasons for the experiment error
    ExperimentErrorEnum.ExperimentError experiment_error = 123;

    // The reasons for the access invitation error
    AccessInvitationErrorEnum.AccessInvitationError access_invitation_error =
        124;

    // The reasons for the reach plan error
    ReachPlanErrorEnum.ReachPlanError reach_plan_error = 125;

    // The reasons for the invoice error
    InvoiceErrorEnum.InvoiceError invoice_error = 126;

    // The reasons for errors in payments accounts service
    PaymentsAccountErrorEnum.PaymentsAccountError payments_account_error = 127;

    // The reasons for the time zone error
    TimeZoneErrorEnum.TimeZoneError time_zone_error = 128;

    // The reasons for the asset link error
    AssetLinkErrorEnum.AssetLinkError asset_link_error = 129;

    // The reasons for the user data error.
    UserDataErrorEnum.UserDataError user_data_error = 130;

    // The reasons for the batch job error
    BatchJobErrorEnum.BatchJobError batch_job_error = 131;

    // The reasons for the account link status change error
    AccountLinkErrorEnum.AccountLinkError account_link_error = 134;

    // The reasons for the third party app analytics link mutate error
    ThirdPartyAppAnalyticsLinkErrorEnum.ThirdPartyAppAnalyticsLinkError
        third_party_app_analytics_link_error = 135;

    // The reasons for the customer user access mutate error
    CustomerUserAccessErrorEnum.CustomerUserAccessError
        customer_user_access_error = 138;

    // The reasons for the custom audience error
    CustomAudienceErrorEnum.CustomAudienceError custom_audience_error = 139;

    // The reasons for the audience error
    AudienceErrorEnum.AudienceError audience_error = 164;

    // The reasons for the Search term insight error
    SearchTermInsightErrorEnum.SearchTermInsightError
        search_term_insight_error = 174;

    // The reasons for the Smart campaign error
    SmartCampaignErrorEnum.SmartCampaignError smart_campaign_error = 147;

    // The reasons for the experiment arm error
    ExperimentArmErrorEnum.ExperimentArmError experiment_arm_error = 156;

    // The reasons for the Audience Insights error
    AudienceInsightsErrorEnum.AudienceInsightsError audience_insights_error =
        167;

    // The reasons for the product link error
    ProductLinkErrorEnum.ProductLinkError product_link_error = 169;

    // The reasons for the customer SK Ad network conversion value schema error
    CustomerSkAdNetworkConversionValueSchemaErrorEnum
        .CustomerSkAdNetworkConversionValueSchemaError
            customer_sk_ad_network_conversion_value_schema_error = 170;

    // The reasons for the currency errors.
    CurrencyErrorEnum.CurrencyError currency_error = 171;

    // The reasons for the asset group hint error
    AssetGroupSignalErrorEnum.AssetGroupSignalError asset_group_signal_error =
        176;

    // The reasons for the product link invitation error
    ProductLinkInvitationErrorEnum.ProductLinkInvitationError
        product_link_invitation_error = 177;

    // The reasons for the customer lifecycle goal error
    CustomerLifecycleGoalErrorEnum.CustomerLifecycleGoalError
        customer_lifecycle_goal_error = 178;

    // The reasons for the campaign lifecycle goal error
    CampaignLifecycleGoalErrorEnum.CampaignLifecycleGoalError
        campaign_lifecycle_goal_error = 179;

    // The reasons for an identity verification error.
    IdentityVerificationErrorEnum.IdentityVerificationError
        identity_verification_error = 181;
  }
}

// Describes the part of the request proto that caused the error.
message ErrorLocation {
  // A part of a field path.
  message FieldPathElement {
    // The name of a field or a oneof
    string field_name = 1;

    // If field_name is a repeated field, this is the element that failed
    optional int32 index = 3;
  }

  // A field path that indicates which field was invalid in the request.
  repeated FieldPathElement field_path_elements = 2;
}

// Additional error details.
message ErrorDetails {
  // The error code that should have been returned, but wasn't. This is used
  // when the error code is not published in the client specified version.
  string unpublished_error_code = 1;

  // Describes an ad policy violation.
  PolicyViolationDetails policy_violation_details = 2;

  // Describes policy violation findings.
  PolicyFindingDetails policy_finding_details = 3;

  // Details on the quota error, including the scope (account or developer), the
  // rate bucket name and the retry delay.
  QuotaErrorDetails quota_error_details = 4;

  // Details for a resource count limit exceeded error.
  ResourceCountDetails resource_count_details = 5;
}

// Error returned as part of a mutate response.
// This error indicates single policy violation by some text
// in one of the fields.
message PolicyViolationDetails {
  // Human readable description of policy violation.
  string external_policy_description = 2;

  // Unique identifier for this violation.
  // If policy is exemptible, this key may be used to request exemption.
  google.ads.googleads.v16.common.PolicyViolationKey key = 4;

  // Human readable name of the policy.
  string external_policy_name = 5;

  // Whether user can file an exemption request for this violation.
  bool is_exemptible = 6;
}

// Error returned as part of a mutate response.
// This error indicates one or more policy findings in the fields of a
// resource.
message PolicyFindingDetails {
  // The list of policy topics for the resource. Contains the PROHIBITED or
  // FULLY_LIMITED policy topic entries that prevented the resource from being
  // saved (among any other entries the resource may also have).
  repeated google.ads.googleads.v16.common.PolicyTopicEntry
      policy_topic_entries = 1;
}

// Additional quota error details when there is QuotaError.
message QuotaErrorDetails {
  // Enum of possible scopes that quota buckets belong to.
  enum QuotaRateScope {
    // Unspecified enum
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Per customer account quota
    ACCOUNT = 2;

    // Per project or DevToken quota
    DEVELOPER = 3;
  }

  // The rate scope of the quota limit.
  QuotaRateScope rate_scope = 1;

  // The high level description of the quota bucket.
  // Examples are "Get requests for standard access" or "Requests per account".
  string rate_name = 2;

  // Backoff period that customers should wait before sending next request.
  google.protobuf.Duration retry_delay = 3;
}

// Error details returned when an resource count limit was exceeded.
message ResourceCountDetails {
  // The ID of the resource whose limit was exceeded.
  // External customer ID if the limit is for a customer.
  string enclosing_id = 1;

  // The name of the resource (Customer, Campaign etc.) whose limit was
  // exceeded.
  string enclosing_resource = 5;

  // The limit which was exceeded.
  int32 limit = 2;

  // The resource limit type which was exceeded.
  google.ads.googleads.v16.enums.ResourceLimitTypeEnum.ResourceLimitType
      limit_type = 3;

  // The count of existing entities.
  int32 existing_count = 4;
}
