// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupAssetErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing asset group asset errors.

// Container for enum describing possible asset group asset errors.
message AssetGroupAssetErrorEnum {
  // Enum describing possible asset group asset errors.
  enum AssetGroupAssetError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Cannot add duplicated asset group asset.
    DUPLICATE_RESOURCE = 2;

    // Expandable tags are not allowed in description assets.
    EXPANDABLE_TAGS_NOT_ALLOWED_IN_DESCRIPTION = 3;

    // Ad customizers are not supported in assetgroup's text assets.
    AD_CUSTOMIZER_NOT_SUPPORTED = 4;

    // Cannot add a HotelPropertyAsset to an AssetGroup that isn't linked
    // to the parent campaign's hotel_property_asset_set field.
    HOTEL_PROPERTY_ASSET_NOT_LINKED_TO_CAMPAIGN = 5;
  }
}
