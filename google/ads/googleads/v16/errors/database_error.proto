// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "DatabaseErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing database errors.

// Container for enum describing possible database errors.
message DatabaseErrorEnum {
  // Enum describing possible database errors.
  enum DatabaseError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Multiple requests were attempting to modify the same resource at once.
    // Retry the request.
    CONCURRENT_MODIFICATION = 2;

    // The request conflicted with existing data. This error will usually be
    // replaced with a more specific error if the request is retried.
    DATA_CONSTRAINT_VIOLATION = 3;

    // The data written is too large. Split the request into smaller
    // requests.
    REQUEST_TOO_LARGE = 4;
  }
}
