// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetSetErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing asset set errors.

// Container for enum describing possible asset set errors.
message AssetSetErrorEnum {
  // Enum describing possible asset set errors.
  enum AssetSetError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The asset set name matches that of another enabled asset set.
    DUPLICATE_ASSET_SET_NAME = 2;

    // The type of AssetSet.asset_set_source does not match the type of
    // AssetSet.location_set.source in its parent AssetSet.
    INVALID_PARENT_ASSET_SET_TYPE = 3;

    // The asset set source doesn't match its parent AssetSet's data.
    ASSET_SET_SOURCE_INCOMPATIBLE_WITH_PARENT_ASSET_SET = 4;

    // This AssetSet type cannot be linked to CustomerAssetSet.
    ASSET_SET_TYPE_CANNOT_BE_LINKED_TO_CUSTOMER = 5;

    // The chain id(s) in ChainSet of a LOCATION_SYNC typed AssetSet is invalid.
    INVALID_CHAIN_IDS = 6;

    // The relationship type in ChainSet of a LOCATION_SYNC typed AssetSet is
    // not supported.
    LOCATION_SYNC_ASSET_SET_DOES_NOT_SUPPORT_RELATIONSHIP_TYPE = 7;

    // There is more than one enabled LocationSync typed AssetSet under one
    // customer.
    NOT_UNIQUE_ENABLED_LOCATION_SYNC_TYPED_ASSET_SET = 8;

    // The place id(s) in a LocationSync typed AssetSet is invalid and can't be
    // decoded.
    INVALID_PLACE_IDS = 9;

    // The Google Business Profile OAuth info is invalid.
    OAUTH_INFO_INVALID = 11;

    // The Google Business Profile OAuth info is missing.
    OAUTH_INFO_MISSING = 12;

    // Can't delete an AssetSet if it has any enabled linkages (e.g.
    // CustomerAssetSet), or AssetSet is a parent AssetSet and has enabled child
    // AssetSet associated.
    CANNOT_DELETE_AS_ENABLED_LINKAGES_EXIST = 10;
  }
}
