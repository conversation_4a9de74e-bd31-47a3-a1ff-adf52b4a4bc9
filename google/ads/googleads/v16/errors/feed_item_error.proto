// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "FeedItemErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing feed item errors.

// Container for enum describing possible feed item errors.
message FeedItemErrorEnum {
  // Enum describing possible feed item errors.
  enum FeedItemError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Cannot convert the feed attribute value from string to its real type.
    CANNOT_CONVERT_ATTRIBUTE_VALUE_FROM_STRING = 2;

    // Cannot operate on removed feed item.
    CANNOT_OPERATE_ON_REMOVED_FEED_ITEM = 3;

    // Date time zone does not match the account's time zone.
    DATE_TIME_MUST_BE_IN_ACCOUNT_TIME_ZONE = 4;

    // Feed item with the key attributes could not be found.
    KEY_ATTRIBUTES_NOT_FOUND = 5;

    // Url feed attribute value is not valid.
    INVALID_URL = 6;

    // Some key attributes are missing.
    MISSING_KEY_ATTRIBUTES = 7;

    // Feed item has same key attributes as another feed item.
    KEY_ATTRIBUTES_NOT_UNIQUE = 8;

    // Cannot modify key attributes on an existing feed item.
    CANNOT_MODIFY_KEY_ATTRIBUTE_VALUE = 9;

    // The feed attribute value is too large.
    SIZE_TOO_LARGE_FOR_MULTI_VALUE_ATTRIBUTE = 10;

    // Feed is read only.
    LEGACY_FEED_TYPE_READ_ONLY = 11;
  }
}
