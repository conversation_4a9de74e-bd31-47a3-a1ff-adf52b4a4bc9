// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanAdGroupKeywordErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing errors from applying a keyword plan ad group keyword or
// keyword plan campaign keyword.

// Container for enum describing possible errors from applying an ad group
// keyword or a campaign keyword from a keyword plan.
message KeywordPlanAdGroupKeywordErrorEnum {
  // Enum describing possible errors from applying a keyword plan ad group
  // keyword or keyword plan campaign keyword.
  enum KeywordPlanAdGroupKeywordError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // A keyword or negative keyword has invalid match type.
    INVALID_KEYWORD_MATCH_TYPE = 2;

    // A keyword or negative keyword with same text and match type already
    // exists.
    DUPLICATE_KEYWORD = 3;

    // Keyword or negative keyword text exceeds the allowed limit.
    KEYWORD_TEXT_TOO_LONG = 4;

    // Keyword or negative keyword text has invalid characters or symbols.
    KEYWORD_HAS_INVALID_CHARS = 5;

    // Keyword or negative keyword text has too many words.
    KEYWORD_HAS_TOO_MANY_WORDS = 6;

    // Keyword or negative keyword has invalid text.
    INVALID_KEYWORD_TEXT = 7;

    // Cpc Bid set for negative keyword.
    NEGATIVE_KEYWORD_HAS_CPC_BID = 8;

    // New broad match modifier (BMM) KpAdGroupKeywords are not allowed.
    NEW_BMM_KEYWORDS_NOT_ALLOWED = 9;
  }
}
