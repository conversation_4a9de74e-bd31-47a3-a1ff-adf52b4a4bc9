// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "FeedAttributeReferenceErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing feed attribute reference errors.

// Container for enum describing possible feed attribute reference errors.
message FeedAttributeReferenceErrorEnum {
  // Enum describing possible feed attribute reference errors.
  enum FeedAttributeReferenceError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // A feed referenced by ID has been removed.
    CANNOT_REFERENCE_REMOVED_FEED = 2;

    // There is no enabled feed with the given name.
    INVALID_FEED_NAME = 3;

    // There is no feed attribute in an enabled feed with the given name.
    INVALID_FEED_ATTRIBUTE_NAME = 4;
  }
}
