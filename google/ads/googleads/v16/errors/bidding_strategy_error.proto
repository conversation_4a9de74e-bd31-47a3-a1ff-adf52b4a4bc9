// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "BiddingStrategyErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing bidding strategy errors.

// Container for enum describing possible bidding strategy errors.
message BiddingStrategyErrorEnum {
  // Enum describing possible bidding strategy errors.
  enum BiddingStrategyError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Each bidding strategy must have a unique name.
    DUPLICATE_NAME = 2;

    // Bidding strategy type is immutable.
    CANNOT_CHANGE_BIDDING_STRATEGY_TYPE = 3;

    // Only bidding strategies not linked to campaigns, adgroups or adgroup
    // criteria can be removed.
    CANNOT_REMOVE_ASSOCIATED_STRATEGY = 4;

    // The specified bidding strategy is not supported.
    BIDDING_STRATEGY_NOT_SUPPORTED = 5;

    // The bidding strategy is incompatible with the campaign's bidding
    // strategy goal type.
    INCOMPATIBLE_BIDDING_STRATEGY_AND_BIDDING_STRATEGY_GOAL_TYPE = 6;
  }
}
