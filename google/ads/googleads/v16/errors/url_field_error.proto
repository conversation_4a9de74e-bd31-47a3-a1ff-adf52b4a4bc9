// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "UrlFieldErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing url field errors.

// Container for enum describing possible url field errors.
message UrlFieldErrorEnum {
  // Enum describing possible url field errors.
  enum UrlFieldError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The tracking url template is invalid.
    INVALID_TRACKING_URL_TEMPLATE = 2;

    // The tracking url template contains invalid tag.
    INVALID_TAG_IN_TRACKING_URL_TEMPLATE = 3;

    // The tracking url template must contain at least one tag (for example,
    // {lpurl}), This applies only to tracking url template associated with
    // website ads or product ads.
    MISSING_TRACKING_URL_TEMPLATE_TAG = 4;

    // The tracking url template must start with a valid protocol (or lpurl
    // tag).
    MISSING_PROTOCOL_IN_TRACKING_URL_TEMPLATE = 5;

    // The tracking url template starts with an invalid protocol.
    INVALID_PROTOCOL_IN_TRACKING_URL_TEMPLATE = 6;

    // The tracking url template contains illegal characters.
    MALFORMED_TRACKING_URL_TEMPLATE = 7;

    // The tracking url template must contain a host name (or lpurl tag).
    MISSING_HOST_IN_TRACKING_URL_TEMPLATE = 8;

    // The tracking url template has an invalid or missing top level domain
    // extension.
    INVALID_TLD_IN_TRACKING_URL_TEMPLATE = 9;

    // The tracking url template contains nested occurrences of the same
    // conditional tag (for example, {ifmobile:{ifmobile:x}}).
    REDUNDANT_NESTED_TRACKING_URL_TEMPLATE_TAG = 10;

    // The final url is invalid.
    INVALID_FINAL_URL = 11;

    // The final url contains invalid tag.
    INVALID_TAG_IN_FINAL_URL = 12;

    // The final url contains nested occurrences of the same conditional tag
    // (for example, {ifmobile:{ifmobile:x}}).
    REDUNDANT_NESTED_FINAL_URL_TAG = 13;

    // The final url must start with a valid protocol.
    MISSING_PROTOCOL_IN_FINAL_URL = 14;

    // The final url starts with an invalid protocol.
    INVALID_PROTOCOL_IN_FINAL_URL = 15;

    // The final url contains illegal characters.
    MALFORMED_FINAL_URL = 16;

    // The final url must contain a host name.
    MISSING_HOST_IN_FINAL_URL = 17;

    // The tracking url template has an invalid or missing top level domain
    // extension.
    INVALID_TLD_IN_FINAL_URL = 18;

    // The final mobile url is invalid.
    INVALID_FINAL_MOBILE_URL = 19;

    // The final mobile url contains invalid tag.
    INVALID_TAG_IN_FINAL_MOBILE_URL = 20;

    // The final mobile url contains nested occurrences of the same conditional
    // tag (for example, {ifmobile:{ifmobile:x}}).
    REDUNDANT_NESTED_FINAL_MOBILE_URL_TAG = 21;

    // The final mobile url must start with a valid protocol.
    MISSING_PROTOCOL_IN_FINAL_MOBILE_URL = 22;

    // The final mobile url starts with an invalid protocol.
    INVALID_PROTOCOL_IN_FINAL_MOBILE_URL = 23;

    // The final mobile url contains illegal characters.
    MALFORMED_FINAL_MOBILE_URL = 24;

    // The final mobile url must contain a host name.
    MISSING_HOST_IN_FINAL_MOBILE_URL = 25;

    // The tracking url template has an invalid or missing top level domain
    // extension.
    INVALID_TLD_IN_FINAL_MOBILE_URL = 26;

    // The final app url is invalid.
    INVALID_FINAL_APP_URL = 27;

    // The final app url contains invalid tag.
    INVALID_TAG_IN_FINAL_APP_URL = 28;

    // The final app url contains nested occurrences of the same conditional tag
    // (for example, {ifmobile:{ifmobile:x}}).
    REDUNDANT_NESTED_FINAL_APP_URL_TAG = 29;

    // More than one app url found for the same OS type.
    MULTIPLE_APP_URLS_FOR_OSTYPE = 30;

    // The OS type given for an app url is not valid.
    INVALID_OSTYPE = 31;

    // The protocol given for an app url is not valid. (For example,
    // "android-app://")
    INVALID_PROTOCOL_FOR_APP_URL = 32;

    // The package id (app id) given for an app url is not valid.
    INVALID_PACKAGE_ID_FOR_APP_URL = 33;

    // The number of url custom parameters for an resource exceeds the maximum
    // limit allowed.
    URL_CUSTOM_PARAMETERS_COUNT_EXCEEDS_LIMIT = 34;

    // An invalid character appears in the parameter key.
    INVALID_CHARACTERS_IN_URL_CUSTOM_PARAMETER_KEY = 39;

    // An invalid character appears in the parameter value.
    INVALID_CHARACTERS_IN_URL_CUSTOM_PARAMETER_VALUE = 40;

    // The url custom parameter value fails url tag validation.
    INVALID_TAG_IN_URL_CUSTOM_PARAMETER_VALUE = 41;

    // The custom parameter contains nested occurrences of the same conditional
    // tag (for example, {ifmobile:{ifmobile:x}}).
    REDUNDANT_NESTED_URL_CUSTOM_PARAMETER_TAG = 42;

    // The protocol (http:// or https://) is missing.
    MISSING_PROTOCOL = 43;

    // Unsupported protocol in URL. Only http and https are supported.
    INVALID_PROTOCOL = 52;

    // The url is invalid.
    INVALID_URL = 44;

    // Destination Url is deprecated.
    DESTINATION_URL_DEPRECATED = 45;

    // The url contains invalid tag.
    INVALID_TAG_IN_URL = 46;

    // The url must contain at least one tag (for example, {lpurl}).
    MISSING_URL_TAG = 47;

    // Duplicate url id.
    DUPLICATE_URL_ID = 48;

    // Invalid url id.
    INVALID_URL_ID = 49;

    // The final url suffix cannot begin with '?' or '&' characters and must be
    // a valid query string.
    FINAL_URL_SUFFIX_MALFORMED = 50;

    // The final url suffix cannot contain {lpurl} related or {ignore} tags.
    INVALID_TAG_IN_FINAL_URL_SUFFIX = 51;

    // The top level domain is invalid, for example, not a public top level
    // domain listed in publicsuffix.org.
    INVALID_TOP_LEVEL_DOMAIN = 53;

    // Malformed top level domain in URL.
    MALFORMED_TOP_LEVEL_DOMAIN = 54;

    // Malformed URL.
    MALFORMED_URL = 55;

    // No host found in URL.
    MISSING_HOST = 56;

    // Custom parameter value cannot be null.
    NULL_CUSTOM_PARAMETER_VALUE = 57;

    // Track parameter is not supported.
    VALUE_TRACK_PARAMETER_NOT_SUPPORTED = 58;
  }
}
