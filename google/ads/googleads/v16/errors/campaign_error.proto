// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CampaignErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing campaign errors.

// Container for enum describing possible campaign errors.
message CampaignErrorEnum {
  // Enum describing possible campaign errors.
  enum CampaignError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Cannot target content network.
    CANNOT_TARGET_CONTENT_NETWORK = 3;

    // Cannot target search network.
    CANNOT_TARGET_SEARCH_NETWORK = 4;

    // Cannot cover search network without google search network.
    CANNOT_TARGET_SEARCH_NETWORK_WITHOUT_GOOGLE_SEARCH = 5;

    // Cannot target Google Search network for a CPM campaign.
    CANNOT_TARGET_GOOGLE_SEARCH_FOR_CPM_CAMPAIGN = 6;

    // Must target at least one network.
    CAMPAIGN_MUST_TARGET_AT_LEAST_ONE_NETWORK = 7;

    // Only some Google partners are allowed to target partner search network.
    CANNOT_TARGET_PARTNER_SEARCH_NETWORK = 8;

    // Cannot target content network only as campaign has criteria-level bidding
    // strategy.
    CANNOT_TARGET_CONTENT_NETWORK_ONLY_WITH_CRITERIA_LEVEL_BIDDING_STRATEGY = 9;

    // Cannot modify the start or end date such that the campaign duration would
    // not contain the durations of all runnable trials.
    CAMPAIGN_DURATION_MUST_CONTAIN_ALL_RUNNABLE_TRIALS = 10;

    // Cannot modify dates, budget or status of a trial campaign.
    CANNOT_MODIFY_FOR_TRIAL_CAMPAIGN = 11;

    // Trying to modify the name of an active or paused campaign, where the name
    // is already assigned to another active or paused campaign.
    DUPLICATE_CAMPAIGN_NAME = 12;

    // Two fields are in conflicting modes.
    INCOMPATIBLE_CAMPAIGN_FIELD = 13;

    // Campaign name cannot be used.
    INVALID_CAMPAIGN_NAME = 14;

    // Given status is invalid.
    INVALID_AD_SERVING_OPTIMIZATION_STATUS = 15;

    // Error in the campaign level tracking URL.
    INVALID_TRACKING_URL = 16;

    // Cannot set both tracking URL template and tracking setting. A user has
    // to clear legacy tracking setting in order to add tracking URL template.
    CANNOT_SET_BOTH_TRACKING_URL_TEMPLATE_AND_TRACKING_SETTING = 17;

    // The maximum number of impressions for Frequency Cap should be an integer
    // greater than 0.
    MAX_IMPRESSIONS_NOT_IN_RANGE = 18;

    // Only the Day, Week and Month time units are supported.
    TIME_UNIT_NOT_SUPPORTED = 19;

    // Operation not allowed on a campaign whose serving status has ended
    INVALID_OPERATION_IF_SERVING_STATUS_HAS_ENDED = 20;

    // This budget is exclusively linked to a Campaign that is using experiments
    // so it cannot be shared.
    BUDGET_CANNOT_BE_SHARED = 21;

    // Campaigns using experiments cannot use a shared budget.
    CAMPAIGN_CANNOT_USE_SHARED_BUDGET = 22;

    // A different budget cannot be assigned to a campaign when there are
    // running or scheduled trials.
    CANNOT_CHANGE_BUDGET_ON_CAMPAIGN_WITH_TRIALS = 23;

    // No link found between the campaign and the label.
    CAMPAIGN_LABEL_DOES_NOT_EXIST = 24;

    // The label has already been attached to the campaign.
    CAMPAIGN_LABEL_ALREADY_EXISTS = 25;

    // A ShoppingSetting was not found when creating a shopping campaign.
    MISSING_SHOPPING_SETTING = 26;

    // The country in shopping setting is not an allowed country.
    INVALID_SHOPPING_SALES_COUNTRY = 27;

    // The requested channel type is not available according to the customer's
    // account setting.
    ADVERTISING_CHANNEL_TYPE_NOT_AVAILABLE_FOR_ACCOUNT_TYPE = 31;

    // The AdvertisingChannelSubType is not a valid subtype of the primary
    // channel type.
    INVALID_ADVERTISING_CHANNEL_SUB_TYPE = 32;

    // At least one conversion must be selected.
    AT_LEAST_ONE_CONVERSION_MUST_BE_SELECTED = 33;

    // Setting ad rotation mode for a campaign is not allowed. Ad rotation mode
    // at campaign is deprecated.
    CANNOT_SET_AD_ROTATION_MODE = 34;

    // Trying to change start date on a campaign that has started.
    CANNOT_MODIFY_START_DATE_IF_ALREADY_STARTED = 35;

    // Trying to modify a date into the past.
    CANNOT_SET_DATE_TO_PAST = 36;

    // Hotel center id in the hotel setting does not match any customer links.
    MISSING_HOTEL_CUSTOMER_LINK = 37;

    // Hotel center id in the hotel setting must match an active customer link.
    INVALID_HOTEL_CUSTOMER_LINK = 38;

    // Hotel setting was not found when creating a hotel ads campaign.
    MISSING_HOTEL_SETTING = 39;

    // A Campaign cannot use shared campaign budgets and be part of a campaign
    // group.
    CANNOT_USE_SHARED_CAMPAIGN_BUDGET_WHILE_PART_OF_CAMPAIGN_GROUP = 40;

    // The app ID was not found.
    APP_NOT_FOUND = 41;

    // Campaign.shopping_setting.enable_local is not supported for the specified
    // campaign type.
    SHOPPING_ENABLE_LOCAL_NOT_SUPPORTED_FOR_CAMPAIGN_TYPE = 42;

    // The merchant does not support the creation of campaigns for Shopping
    // Comparison Listing Ads.
    MERCHANT_NOT_ALLOWED_FOR_COMPARISON_LISTING_ADS = 43;

    // The App campaign for engagement cannot be created because there aren't
    // enough installs.
    INSUFFICIENT_APP_INSTALLS_COUNT = 44;

    // The App campaign for engagement cannot be created because the app is
    // sensitive.
    SENSITIVE_CATEGORY_APP = 45;

    // Customers with Housing, Employment, or Credit ads must accept updated
    // personalized ads policy to continue creating campaigns.
    HEC_AGREEMENT_REQUIRED = 46;

    // The field is not compatible with view through conversion optimization.
    NOT_COMPATIBLE_WITH_VIEW_THROUGH_CONVERSION_OPTIMIZATION = 49;

    // The field type cannot be excluded because an active campaign-asset link
    // of this type exists.
    INVALID_EXCLUDED_PARENT_ASSET_FIELD_TYPE = 48;

    // The app pre-registration campaign cannot be created for non-Android
    // applications.
    CANNOT_CREATE_APP_PRE_REGISTRATION_FOR_NON_ANDROID_APP = 50;

    // The campaign cannot be created since the app is not available for
    // pre-registration in any country.
    APP_NOT_AVAILABLE_TO_CREATE_APP_PRE_REGISTRATION_CAMPAIGN = 51;

    // The type of the Budget is not compatible with this Campaign.
    INCOMPATIBLE_BUDGET_TYPE = 52;

    // Category bid list in the local services campaign setting contains
    // multiple bids for the same category ID.
    LOCAL_SERVICES_DUPLICATE_CATEGORY_BID = 53;

    // Category bid list in the local services campaign setting contains
    // a bid for an invalid category ID.
    LOCAL_SERVICES_INVALID_CATEGORY_BID = 54;

    // Category bid list in the local services campaign setting is missing a
    // bid for a category ID that must be present.
    LOCAL_SERVICES_MISSING_CATEGORY_BID = 55;

    // The requested change in status is not supported.
    INVALID_STATUS_CHANGE = 57;

    // Travel Campaign's travel_account_id does not match any customer links.
    MISSING_TRAVEL_CUSTOMER_LINK = 58;

    // Travel Campaign's travel_account_id matches an existing customer link
    // but the customer link is not active.
    INVALID_TRAVEL_CUSTOMER_LINK = 59;

    // The asset set type is invalid to be set in
    // excluded_parent_asset_set_types field.
    INVALID_EXCLUDED_PARENT_ASSET_SET_TYPE = 62;

    // Campaign.hotel_property_asset_set must point to an asset set of type
    // HOTEL_PROPERTY.
    ASSET_SET_NOT_A_HOTEL_PROPERTY_ASSET_SET = 63;

    // The hotel property asset set can only be set on Performance Max for
    // travel goals campaigns.
    HOTEL_PROPERTY_ASSET_SET_ONLY_FOR_PERFORMANCE_MAX_FOR_TRAVEL_GOALS = 64;

    // Customer's average daily spend is too high to enable this feature.
    AVERAGE_DAILY_SPEND_TOO_HIGH = 65;

    // Cannot attach the campaign to a deleted campaign group.
    CANNOT_ATTACH_TO_REMOVED_CAMPAIGN_GROUP = 66;

    // Cannot attach the campaign to this bidding strategy.
    CANNOT_ATTACH_TO_BIDDING_STRATEGY = 67;

    // A budget with a different period cannot be assigned to the campaign.
    CANNOT_CHANGE_BUDGET_PERIOD = 68;

    // Customer does not have enough conversions to enable this feature.
    NOT_ENOUGH_CONVERSIONS = 71;

    // This campaign type can only have one conversion action.
    CANNOT_SET_MORE_THAN_ONE_CONVERSION_ACTION = 72;

    // The field is not compatible with the budget type.
    NOT_COMPATIBLE_WITH_BUDGET_TYPE = 73;

    // The feature is incompatible with ConversionActionType.UPLOAD_CLICKS.
    NOT_COMPATIBLE_WITH_UPLOAD_CLICKS_CONVERSION = 74;

    // App campaign setting app ID must match selective optimization conversion
    // action app ID.
    APP_ID_MUST_MATCH_CONVERSION_ACTION_APP_ID = 76;

    // Selective optimization conversion action with Download category is not
    // allowed.
    CONVERSION_ACTION_WITH_DOWNLOAD_CATEGORY_NOT_ALLOWED = 77;

    // One software download for selective optimization conversion action is
    // required for this campaign conversion action.
    CONVERSION_ACTION_WITH_DOWNLOAD_CATEGORY_REQUIRED = 78;

    // Conversion tracking is not enabled and is required for this feature.
    CONVERSION_TRACKING_NOT_ENABLED = 79;

    // The field is not compatible with the bidding strategy type.
    NOT_COMPATIBLE_WITH_BIDDING_STRATEGY_TYPE = 80;

    // Campaign is not compatible with a conversion tracker that has Google
    // attribution enabled.
    NOT_COMPATIBLE_WITH_GOOGLE_ATTRIBUTION_CONVERSIONS = 81;

    // Customer level conversion lag is too high.
    CONVERSION_LAG_TOO_HIGH = 82;

    // The advertiser set as an advertising partner is not an actively linked
    // advertiser to this customer.
    NOT_LINKED_ADVERTISING_PARTNER = 83;

    // Invalid number of advertising partner IDs.
    INVALID_NUMBER_OF_ADVERTISING_PARTNER_IDS = 84;

    // Cannot target the display network without also targeting YouTube.
    CANNOT_TARGET_DISPLAY_NETWORK_WITHOUT_YOUTUBE = 85;

    // This campaign type cannot be linked to a Comparison Shopping Service
    // account.
    CANNOT_LINK_TO_COMPARISON_SHOPPING_SERVICE_ACCOUNT = 86;

    // Standard Shopping campaigns that are linked to a Comparison Shopping
    // Service account cannot target this network.
    CANNOT_TARGET_NETWORK_FOR_COMPARISON_SHOPPING_SERVICE_LINKED_ACCOUNTS = 87;

    // Text asset automation settings can not be modified when there is an
    // active Performance Max optimization automatically created assets
    // experiment. End the experiment to modify these settings.
    CANNOT_MODIFY_TEXT_ASSET_AUTOMATION_WITH_ENABLED_TRIAL = 88;

    // Dynamic text asset cannot be opted out when final URL expansion is opted
    // in.
    DYNAMIC_TEXT_ASSET_CANNOT_OPT_OUT_WITH_FINAL_URL_EXPANSION_OPT_IN = 89;
  }
}
