// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ConversionGoalCampaignConfigErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing conversion goal campaign config errors.

// Container for enum describing possible conversion goal campaign config
// errors.
message ConversionGoalCampaignConfigErrorEnum {
  // Enum describing possible conversion goal campaign config errors.
  enum ConversionGoalCampaignConfigError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Campaign is managed by Search Ads 360 but uses Unified Goal.
    CANNOT_USE_CAMPAIGN_GOAL_FOR_SEARCH_ADS_360_MANAGED_CAMPAIGN = 2;

    // The campaign is using a custom goal that does not belong to its Google
    // Ads conversion customer (conversion tracking customer).
    CUSTOM_GOAL_DOES_NOT_BELONG_TO_GOOGLE_ADS_CONVERSION_CUSTOMER = 3;

    // The campaign is not allowed to use unified goals.
    CAMPAIGN_CANNOT_USE_UNIFIED_GOALS = 4;

    // The campaign is using campaign override goals but has no goals
    // configured.
    EMPTY_CONVERSION_GOALS = 5;

    // STORE_SALE and STORE_VISIT conversion types cannot be both included in
    // campaign level goal.
    STORE_SALE_STORE_VISIT_CANNOT_BE_BOTH_INCLUDED = 6;

    // Performance Max campaign is not allowed to use custom goal with store
    // sales conversion type.
    PERFORMANCE_MAX_CAMPAIGN_CANNOT_USE_CUSTOM_GOAL_WITH_STORE_SALES = 7;
  }
}
