// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "UserDataErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing user data errors.

// Container for enum describing possible user data errors.
message UserDataErrorEnum {
  // Enum describing possible request errors.
  enum UserDataError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Customer is not allowed to perform operations related to Customer Match.
    OPERATIONS_FOR_CUSTOMER_MATCH_NOT_ALLOWED = 2;

    // Maximum number of user identifiers allowed for each request is 100 and
    // for each operation is 20.
    TOO_MANY_USER_IDENTIFIERS = 3;

    // Current user list is not applicable for the given customer.
    USER_LIST_NOT_APPLICABLE = 4;
  }
}
