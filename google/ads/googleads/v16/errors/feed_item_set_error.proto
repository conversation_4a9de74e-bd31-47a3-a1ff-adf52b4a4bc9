// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "FeedItemSetErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing feed item set errors.

// Container for enum describing possible feed item set errors.
message FeedItemSetErrorEnum {
  // Enum describing possible feed item set errors.
  enum FeedItemSetError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The given ID refers to a removed FeedItemSet.
    FEED_ITEM_SET_REMOVED = 2;

    // The dynamic filter of a feed item set cannot be cleared on UPDATE if it
    // exists. A set is either static or dynamic once added, and that cannot
    // change.
    CANNOT_CLEAR_DYNAMIC_FILTER = 3;

    // The dynamic filter of a feed item set cannot be created on UPDATE if it
    // does not exist. A set is either static or dynamic once added, and that
    // cannot change.
    CANNOT_CREATE_DYNAMIC_FILTER = 4;

    // FeedItemSets can only be made for location or affiliate location feeds.
    INVALID_FEED_TYPE = 5;

    // FeedItemSets duplicate name. Name should be unique within an account.
    DUPLICATE_NAME = 6;

    // The feed type of the parent Feed is not compatible with the type of
    // dynamic filter being set. For example, you can only set
    // dynamic_location_set_filter for LOCATION feed item sets.
    WRONG_DYNAMIC_FILTER_FOR_FEED_TYPE = 7;

    // Chain ID specified for AffiliateLocationFeedData is invalid.
    DYNAMIC_FILTER_INVALID_CHAIN_IDS = 8;
  }
}
