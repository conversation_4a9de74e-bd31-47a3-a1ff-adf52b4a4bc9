// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetSetAssetErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing asset set asset errors.

// Container for enum describing possible asset set asset errors.
message AssetSetAssetErrorEnum {
  // Enum describing possible asset set asset errors.
  enum AssetSetAssetError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The asset type is not eligible to be linked to the specific type of asset
    // set.
    INVALID_ASSET_TYPE = 2;

    // The asset set type is not eligible to contain the specified type of
    // assets.
    INVALID_ASSET_SET_TYPE = 3;

    // The asset contains duplicate external key with another asset in the asset
    // set.
    DUPLICATE_EXTERNAL_KEY = 4;

    // When attaching a Location typed Asset to a LocationGroup typed AssetSet,
    // the AssetSetAsset linkage between the parent LocationSync AssetSet and
    // the Asset doesn't exist.
    PARENT_LINKAGE_DOES_NOT_EXIST = 5;
  }
}
