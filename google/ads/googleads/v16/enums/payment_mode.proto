// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "PaymentModeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing payment modes.

// Container for enum describing possible payment modes.
message PaymentModeEnum {
  // Enum describing possible payment modes.
  enum PaymentMode {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Pay per interaction.
    CLICKS = 4;

    // Pay per conversion value. This mode is only supported by campaigns with
    // AdvertisingChannelType.HOTEL, BiddingStrategyType.COMMISSION, and
    // BudgetType.STANDARD.
    CONVERSION_VALUE = 5;

    // Pay per conversion. This mode is only supported by campaigns with
    // AdvertisingChannelType.DISPLAY (excluding
    // AdvertisingChannelSubType.DISPLAY_GMAIL), BiddingStrategyType.TARGET_CPA,
    // and BudgetType.FIXED_CPA. The customer must also be eligible for this
    // mode. See Customer.eligibility_failure_reasons for details.
    CONVERSIONS = 6;

    // Pay per guest stay value. This mode is only supported by campaigns with
    // AdvertisingChannelType.HOTEL, BiddingStrategyType.COMMISSION, and
    // BudgetType.STANDARD.
    GUEST_STAY = 7;
  }
}
