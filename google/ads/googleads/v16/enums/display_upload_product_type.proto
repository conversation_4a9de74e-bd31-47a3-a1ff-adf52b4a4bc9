// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "DisplayUploadProductTypeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing display upload product types.

// Container for display upload product types. Product types that have the word
// "DYNAMIC" in them must be associated with a campaign that has a dynamic
// remarketing feed. See https://support.google.com/google-ads/answer/6053288
// for more info about dynamic remarketing. Other product types are regarded
// as "static" and do not have this requirement.
message DisplayUploadProductTypeEnum {
  // Enumerates display upload product types.
  enum DisplayUploadProductType {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // HTML5 upload ad. This product type requires the upload_media_bundle
    // field in DisplayUploadAdInfo to be set.
    HTML5_UPLOAD_AD = 2;

    // Dynamic HTML5 education ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in an education campaign.
    DYNAMIC_HTML5_EDUCATION_AD = 3;

    // Dynamic HTML5 flight ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in a flight campaign.
    DYNAMIC_HTML5_FLIGHT_AD = 4;

    // Dynamic HTML5 hotel and rental ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in a hotel campaign.
    DYNAMIC_HTML5_HOTEL_RENTAL_AD = 5;

    // Dynamic HTML5 job ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in a job campaign.
    DYNAMIC_HTML5_JOB_AD = 6;

    // Dynamic HTML5 local ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in a local campaign.
    DYNAMIC_HTML5_LOCAL_AD = 7;

    // Dynamic HTML5 real estate ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in a real estate campaign.
    DYNAMIC_HTML5_REAL_ESTATE_AD = 8;

    // Dynamic HTML5 custom ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in a custom campaign.
    DYNAMIC_HTML5_CUSTOM_AD = 9;

    // Dynamic HTML5 travel ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in a travel campaign.
    DYNAMIC_HTML5_TRAVEL_AD = 10;

    // Dynamic HTML5 hotel ad. This product type requires the
    // upload_media_bundle field in DisplayUploadAdInfo to be set. Can only be
    // used in a hotel campaign.
    DYNAMIC_HTML5_HOTEL_AD = 11;
  }
}
