// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ExperimentStatusProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing experiment status.

// Container for enum describing the experiment status.
message ExperimentStatusEnum {
  // The status of the experiment.
  enum ExperimentStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // The experiment is enabled.
    ENABLED = 2;

    // The experiment has been removed.
    REMOVED = 3;

    // The experiment has been halted.
    // This status can be set from ENABLED status through API.
    HALTED = 4;

    // The experiment will be promoted out of experimental status.
    PROMOTED = 5;

    // Initial status of the experiment.
    SETUP = 6;

    // The experiment's campaigns are pending materialization.
    // This status can be set from SETUP status through API.
    INITIATED = 7;

    // The experiment has been graduated.
    GRADUATED = 8;
  }
}
