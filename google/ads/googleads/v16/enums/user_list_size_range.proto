// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "UserListSizeRangeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing user list size range.

// Size range in terms of number of users of a UserList.
message UserListSizeRangeEnum {
  // Enum containing possible user list size ranges.
  enum UserListSizeRange {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // User list has less than 500 users.
    LESS_THAN_FIVE_HUNDRED = 2;

    // User list has number of users in range of 500 to 1000.
    LESS_THAN_ONE_THOUSAND = 3;

    // User list has number of users in range of 1000 to 10000.
    ONE_THOUSAND_TO_TEN_THOUSAND = 4;

    // User list has number of users in range of 10000 to 50000.
    TEN_THOUSAND_TO_FIFTY_THOUSAND = 5;

    // User list has number of users in range of 50000 to 100000.
    FIFTY_THOUSAND_TO_ONE_HUNDRED_THOUSAND = 6;

    // User list has number of users in range of 100000 to 300000.
    ONE_HUNDRED_THOUSAND_TO_THREE_HUNDRED_THOUSAND = 7;

    // User list has number of users in range of 300000 to 500000.
    THREE_HUNDRED_THOUSAND_TO_FIVE_HUNDRED_THOUSAND = 8;

    // User list has number of users in range of 500000 to 1 million.
    FIVE_HUNDRED_THOUSAND_TO_ONE_MILLION = 9;

    // User list has number of users in range of 1 to 2 millions.
    ONE_MILLION_TO_TWO_MILLION = 10;

    // User list has number of users in range of 2 to 3 millions.
    TWO_MILLION_TO_THREE_MILLION = 11;

    // User list has number of users in range of 3 to 5 millions.
    THREE_MILLION_TO_FIVE_MILLION = 12;

    // User list has number of users in range of 5 to 10 millions.
    FIVE_MILLION_TO_TEN_MILLION = 13;

    // User list has number of users in range of 10 to 20 millions.
    TEN_MILLION_TO_TWENTY_MILLION = 14;

    // User list has number of users in range of 20 to 30 millions.
    TWENTY_MILLION_TO_THIRTY_MILLION = 15;

    // User list has number of users in range of 30 to 50 millions.
    THIRTY_MILLION_TO_FIFTY_MILLION = 16;

    // User list has over 50 million users.
    OVER_FIFTY_MILLION = 17;
  }
}
