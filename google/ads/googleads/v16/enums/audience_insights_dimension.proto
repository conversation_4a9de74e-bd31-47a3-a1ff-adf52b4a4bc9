// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AudienceInsightsDimensionProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing audience insights dimensions.

// Container for enum describing insights dimensions.
message AudienceInsightsDimensionEnum {
  // Possible dimensions for use in generating insights.
  enum AudienceInsightsDimension {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // A Product & Service category.
    CATEGORY = 2;

    // A Knowledge Graph entity.
    KNOWLEDGE_GRAPH = 3;

    // A country, represented by a geo target.
    GEO_TARGET_COUNTRY = 4;

    // A geographic location within a country.
    SUB_COUNTRY_LOCATION = 5;

    // A YouTube channel.
    YOUTUBE_CHANNEL = 6;

    // A YouTube Dynamic Lineup.
    YOUTUBE_DYNAMIC_LINEUP = 7;

    // An Affinity UserInterest.
    AFFINITY_USER_INTEREST = 8;

    // An In-Market UserInterest.
    IN_MARKET_USER_INTEREST = 9;

    // A Parental Status value (parent, or not a parent).
    PARENTAL_STATUS = 10;

    // A household income percentile range.
    INCOME_RANGE = 11;

    // An age range.
    AGE_RANGE = 12;

    // A gender.
    GENDER = 13;
  }
}
