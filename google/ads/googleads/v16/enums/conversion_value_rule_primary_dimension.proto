// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ConversionValueRulePrimaryDimensionProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing conversion value rule primary dimension.

// Container for enum describing value rule primary dimension for stats.
message ConversionValueRulePrimaryDimensionEnum {
  // Identifies the primary dimension for conversion value rule stats.
  enum ConversionValueRulePrimaryDimension {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // For no-value-rule-applied conversions after value rule is enabled.
    NO_RULE_APPLIED = 2;

    // Below are for value-rule-applied conversions:
    // The original stats.
    ORIGINAL = 3;

    // When a new or returning customer condition is satisfied.
    NEW_VS_RETURNING_USER = 4;

    // When a query-time Geo location condition is satisfied.
    GEO_LOCATION = 5;

    // When a query-time browsing device condition is satisfied.
    DEVICE = 6;

    // When a query-time audience condition is satisfied.
    AUDIENCE = 7;

    // When multiple rules are applied.
    MULTIPLE = 8;
  }
}
