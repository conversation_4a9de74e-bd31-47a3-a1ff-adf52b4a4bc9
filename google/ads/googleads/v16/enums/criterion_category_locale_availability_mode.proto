// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "CriterionCategoryLocaleAvailabilityModeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing the criterion category locale availability mode.

// Describes locale availability mode for a criterion availability - whether
// it's available globally, or a particular country with all languages, or a
// particular language with all countries, or a country-language pair.
message CriterionCategoryLocaleAvailabilityModeEnum {
  // Enum containing the possible CriterionCategoryLocaleAvailabilityMode.
  enum CriterionCategoryLocaleAvailabilityMode {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The category is available to campaigns of all locales.
    ALL_LOCALES = 2;

    // The category is available to campaigns within a list of countries,
    // regardless of language.
    COUNTRY_AND_ALL_LANGUAGES = 3;

    // The category is available to campaigns within a list of languages,
    // regardless of country.
    LANGUAGE_AND_ALL_COUNTRIES = 4;

    // The category is available to campaigns within a list of country, language
    // pairs.
    COUNTRY_AND_LANGUAGE = 5;
  }
}
