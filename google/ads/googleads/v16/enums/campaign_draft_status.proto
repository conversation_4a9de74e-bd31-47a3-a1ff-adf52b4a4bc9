// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "CampaignDraftStatusProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing campaign draft status.

// Container for enum describing possible statuses of a campaign draft.
message CampaignDraftStatusEnum {
  // Possible statuses of a campaign draft.
  enum CampaignDraftStatus {
    // The status has not been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Initial state of the draft, the advertiser can start adding changes with
    // no effect on serving.
    PROPOSED = 2;

    // The campaign draft is removed.
    REMOVED = 3;

    // Advertiser requested to promote draft's changes back into the original
    // campaign. Advertiser can poll the long running operation returned by
    // the promote action to see the status of the promotion.
    PROMOTING = 5;

    // The process to merge changes in the draft back to the original campaign
    // has completed successfully.
    PROMOTED = 4;

    // The promotion failed after it was partially applied. Promote cannot be
    // attempted again safely, so the issue must be corrected in the original
    // campaign.
    PROMOTE_FAILED = 6;
  }
}
