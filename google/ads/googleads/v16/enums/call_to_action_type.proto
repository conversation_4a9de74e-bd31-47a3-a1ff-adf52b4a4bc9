// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "CallToActionTypeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing call to action type.

// Container for enum describing the call to action types.
message CallToActionTypeEnum {
  // Enum describing possible types of call to action.
  enum CallToActionType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The call to action type is learn more.
    LEARN_MORE = 2;

    // The call to action type is get quote.
    GET_QUOTE = 3;

    // The call to action type is apply now.
    APPLY_NOW = 4;

    // The call to action type is sign up.
    SIGN_UP = 5;

    // The call to action type is contact us.
    CONTACT_US = 6;

    // The call to action type is subscribe.
    SUBSCRIBE = 7;

    // The call to action type is download.
    DOWNLOAD = 8;

    // The call to action type is book now.
    BOOK_NOW = 9;

    // The call to action type is shop now.
    SHOP_NOW = 10;

    // The call to action type is buy now.
    BUY_NOW = 11;

    // The call to action type is donate now.
    DONATE_NOW = 12;

    // The call to action type is order now.
    ORDER_NOW = 13;

    // The call to action type is play now.
    PLAY_NOW = 14;

    // The call to action type is see more.
    SEE_MORE = 15;

    // The call to action type is start now.
    START_NOW = 16;

    // The call to action type is visit site.
    VISIT_SITE = 17;

    // The call to action type is watch now.
    WATCH_NOW = 18;
  }
}
