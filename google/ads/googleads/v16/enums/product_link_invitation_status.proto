// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ProductLinkInvitationStatusProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Container for enum describing possible statuses of a product link
// invitation.
message ProductLinkInvitationStatusEnum {
  // Describes the possible statuses for an invitation between a Google Ads
  // customer and another account.
  enum ProductLinkInvitationStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The invitation is accepted.
    ACCEPTED = 2;

    // An invitation has been sent to the other account. A user on the other
    // account may now accept the invitation by setting the status to ACCEPTED.
    REQUESTED = 3;

    // This invitation has been sent by a user on the other account. It may be
    // accepted by a user on this account by setting the status to ACCEPTED.
    PENDING_APPROVAL = 4;

    // The invitation is revoked by the user who sent the invitation.
    REVOKED = 5;

    // The invitation has been rejected by the invitee.
    REJECTED = 6;

    // The invitation has timed out before being accepted by the
    // invitee.
    EXPIRED = 7;
  }
}
