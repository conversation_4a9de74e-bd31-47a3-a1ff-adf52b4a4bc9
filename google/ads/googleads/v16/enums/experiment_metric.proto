// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ExperimentMetricProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing experiment metric.

// Container for enum describing the type of experiment metric.
message ExperimentMetricEnum {
  // The type of experiment metric.
  enum ExperimentMetric {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // The goal of the experiment is clicks.
    CLICKS = 2;

    // The goal of the experiment is impressions.
    IMPRESSIONS = 3;

    // The goal of the experiment is cost.
    COST = 4;

    // The goal of the experiment is conversion rate.
    CONVERSIONS_PER_INTERACTION_RATE = 5;

    // The goal of the experiment is cost per conversion.
    COST_PER_CONVERSION = 6;

    // The goal of the experiment is conversion value per cost.
    CONVERSIONS_VALUE_PER_COST = 7;

    // The goal of the experiment is avg cpc.
    AVERAGE_CPC = 8;

    // The goal of the experiment is ctr.
    CTR = 9;

    // The goal of the experiment is incremental conversions.
    INCREMENTAL_CONVERSIONS = 10;

    // The goal of the experiment is completed video views.
    COMPLETED_VIDEO_VIEWS = 11;

    // The goal of the experiment is custom algorithms.
    CUSTOM_ALGORITHMS = 12;

    // The goal of the experiment is conversions.
    CONVERSIONS = 13;

    // The goal of the experiment is conversion value.
    CONVERSION_VALUE = 14;
  }
}
