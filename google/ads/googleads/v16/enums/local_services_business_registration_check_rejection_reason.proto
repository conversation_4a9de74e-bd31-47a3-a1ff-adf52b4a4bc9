// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesBusinessRegistrationCheckRejectionReasonProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Container for enum describing the rejection reason of a local services
// business registration check verification artifact.
message LocalServicesBusinessRegistrationCheckRejectionReasonEnum {
  // Enums describing possible rejection reasons of a local services business
  // registration check verification artifact.
  enum LocalServicesBusinessRegistrationCheckRejectionReason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Business name doesn't match business name for the Local Services Ad.
    BUSINESS_NAME_MISMATCH = 2;

    // Business details mismatch.
    BUSINESS_DETAILS_MISMATCH = 3;

    // Business registration ID not found.
    ID_NOT_FOUND = 4;

    // Uploaded document not clear, blurry, etc.
    POOR_DOCUMENT_IMAGE_QUALITY = 5;

    // Uploaded document has expired.
    DOCUMENT_EXPIRED = 6;

    // Document revoked or annuled.
    DOCUMENT_INVALID = 7;

    // Document type mismatch.
    DOCUMENT_TYPE_MISMATCH = 8;

    // Uploaded document could not be verified as legitimate.
    DOCUMENT_UNVERIFIABLE = 9;

    // The business registration process could not be completed due to an issue.
    // Contact https://support.google.com/localservices to learn more.
    OTHER = 10;
  }
}
