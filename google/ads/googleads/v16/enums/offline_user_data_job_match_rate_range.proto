// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "OfflineUserDataJobMatchRateRangeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Container for enum describing reasons match rate ranges for a customer match
// list upload.
message OfflineUserDataJobMatchRateRangeEnum {
  // The match rate range of an offline user data job.
  enum OfflineUserDataJobMatchRateRange {
    // Not specified.
    UNSPECIFIED = 0;

    // Default value for match rate range.
    UNKNOWN = 1;

    // Match rate range for offline data upload entity is between 0% and 19%.
    MATCH_RANGE_LESS_THAN_20 = 2;

    // Match rate range for offline data upload entity is between 20% and 30%.
    MATCH_RANGE_20_TO_30 = 3;

    // Match rate range for offline data upload entity is between 31% and 40%.
    MATCH_RANGE_31_TO_40 = 4;

    // Match rate range for offline data upload entity is between 41% and 50%.
    MATCH_RANGE_41_TO_50 = 5;

    // Match rate range for offline data upload entity is between 51% and 60%.
    MATCH_RANGE_51_TO_60 = 6;

    // Match rate range for offline data upload entity is between 61% and 70%.
    MATCH_RANGE_61_TO_70 = 7;

    // Match rate range for offline data upload entity is between 71% and 80%.
    MATCH_RANGE_71_TO_80 = 8;

    // Match rate range for offline data upload entity is between 81% and 90%.
    MATCH_RANGE_81_TO_90 = 9;

    // Match rate range for offline data upload entity is more than or equal to
    // 91%.
    MATCH_RANGE_91_TO_100 = 10;
  }
}
