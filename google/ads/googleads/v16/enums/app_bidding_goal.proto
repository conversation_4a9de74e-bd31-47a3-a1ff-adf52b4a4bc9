// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AppBiddingGoalProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Container for enum describing an app bidding goal for raise Target CPA
// recommendation.
message AppBiddingGoalEnum {
  // Represents the goal towards which the bidding strategy, of an app
  // campaign, should optimize for.
  enum AppBiddingGoal {
    // Not specified.
    UNSPECIFIED = 0;

    // Represents value unknown in this version of the API.
    UNKNOWN = 1;

    // The bidding strategy of the app campaign should aim to maximize
    // installation of the app.
    OPTIMIZE_FOR_INSTALL_CONVERSION_VOLUME = 2;

    // The bidding strategy of the app campaign should aim to maximize
    // the selected in-app conversions' volume.
    OPTIMIZE_FOR_IN_APP_CONVERSION_VOLUME = 3;

    // The bidding strategy of the app campaign should aim to maximize
    // all conversions' value, that is, install and selected in-app conversions.
    OPTIMIZE_FOR_TOTAL_CONVERSION_VALUE = 4;

    // The bidding strategy of the app campaign should aim to maximize
    // just the selected in-app conversion's volume, while achieving or
    // exceeding target cost per in-app conversion.
    OPTIMIZE_FOR_TARGET_IN_APP_CONVERSION = 5;

    // The bidding strategy of the app campaign should aim to maximize
    // all conversions' value, that is, install and selected in-app conversions
    // while achieving or exceeding target return on advertising spend.
    OPTIMIZE_FOR_RETURN_ON_ADVERTISING_SPEND = 6;

    // This bidding strategy of the app campaign should aim to
    // maximize installation of the app without advertiser-provided target
    // cost-per-install.
    OPTIMIZE_FOR_INSTALL_CONVERSION_VOLUME_WITHOUT_TARGET_CPI = 7;

    // This bidding strategy of the app campaign should aim to
    // maximize pre-registration of the app.
    OPTIMIZE_FOR_PRE_REGISTRATION_CONVERSION_VOLUME = 8;
  }
}
