// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AppPlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing App placeholder fields.

// Values for App placeholder fields.
message AppPlaceholderFieldEnum {
  // Possible values for App placeholder fields.
  enum AppPlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: INT64. The application store that the target application
    // belongs to. Valid values are: 1 = Apple iTunes Store; 2 = Google Play
    // Store.
    STORE = 2;

    // Data Type: STRING. The store-specific ID for the target application.
    ID = 3;

    // Data Type: STRING. The visible text displayed when the link is rendered
    // in an ad.
    LINK_TEXT = 4;

    // Data Type: STRING. The destination URL of the in-app link.
    URL = 5;

    // Data Type: URL_LIST. Final URLs for the in-app link when using Upgraded
    // URLs.
    FINAL_URLS = 6;

    // Data Type: URL_LIST. Final Mobile URLs for the in-app link when using
    // Upgraded URLs.
    FINAL_MOBILE_URLS = 7;

    // Data Type: URL. Tracking template for the in-app link when using Upgraded
    // URLs.
    TRACKING_URL = 8;

    // Data Type: STRING. Final URL suffix for the in-app link when using
    // parallel tracking.
    FINAL_URL_SUFFIX = 9;
  }
}
