// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LookalikeExpansionLevelProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Lookalike Expansion level proto
message LookalikeExpansionLevelEnum {
  // Expansion level, reflecting the size of the lookalike audience
  enum LookalikeExpansionLevel {
    // Not specified.
    UNSPECIFIED = 0;

    // Invalid expansion level.
    UNKNOWN = 1;

    // Expansion to a small set of users that are similar to the seed lists
    NARROW = 2;

    // Expansion to a medium set of users that are similar to the seed lists.
    // Includes all users of EXPANSION_LEVEL_NARROW, and more.
    BALANCED = 3;

    // Expansion to a large set of users that are similar to the seed lists.
    // Includes all users of EXPANSION_LEVEL_BALANCED, and more.
    BROAD = 4;
  }
}
