// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "HotelAssetSuggestionStatusProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing hotel asset suggestion status.

// Container for enum describing possible statuses of a hotel asset suggestion.
message HotelAssetSuggestionStatusEnum {
  // Possible statuses of a hotel asset suggestion.
  enum HotelAssetSuggestionStatus {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The hotel asset suggestion was successfully retrieved.
    SUCCESS = 2;

    // A hotel look up returns nothing.
    HOTEL_NOT_FOUND = 3;

    // A Google Places ID is invalid and cannot be decoded.
    INVALID_PLACE_ID = 4;
  }
}
