// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ConversionValueRuleSetStatusProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing conversion value rule set status.

// Container for enum describing possible statuses of a conversion value rule
// set.
message ConversionValueRuleSetStatusEnum {
  // Possible statuses of a conversion value rule set.
  enum ConversionValueRuleSetStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Conversion Value Rule Set is enabled and can be applied.
    ENABLED = 2;

    // Conversion Value Rule Set is permanently deleted and can't be applied.
    REMOVED = 3;

    // Conversion Value Rule Set is paused and won't be applied. It can be
    // enabled again.
    PAUSED = 4;
  }
}
