// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SimulationTypeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing simulation types.

// Container for enum describing the field a simulation modifies.
message SimulationTypeEnum {
  // Enum describing the field a simulation modifies.
  enum SimulationType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The simulation is for a CPC bid.
    CPC_BID = 2;

    // The simulation is for a CPV bid.
    CPV_BID = 3;

    // The simulation is for a CPA target.
    TARGET_CPA = 4;

    // The simulation is for a bid modifier.
    BID_MODIFIER = 5;

    // The simulation is for a ROAS target.
    TARGET_ROAS = 6;

    // The simulation is for a percent CPC bid.
    PERCENT_CPC_BID = 7;

    // The simulation is for an impression share target.
    TARGET_IMPRESSION_SHARE = 8;

    // The simulation is for a budget.
    BUDGET = 9;
  }
}
