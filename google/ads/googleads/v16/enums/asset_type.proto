// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AssetTypeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing asset type.

// Container for enum describing the types of asset.
message AssetTypeEnum {
  // Enum describing possible types of asset.
  enum AssetType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // YouTube video asset.
    YOUTUBE_VIDEO = 2;

    // Media bundle asset.
    MEDIA_BUNDLE = 3;

    // Image asset.
    IMAGE = 4;

    // Text asset.
    TEXT = 5;

    // Lead form asset.
    LEAD_FORM = 6;

    // Book on Google asset.
    BOOK_ON_GOOGLE = 7;

    // Promotion asset.
    PROMOTION = 8;

    // Callout asset.
    CALLOUT = 9;

    // Structured Snippet asset.
    STRUCTURED_SNIPPET = 10;

    // Sitelink asset.
    SITELINK = 11;

    // Page Feed asset.
    PAGE_FEED = 12;

    // Dynamic Education asset.
    DYNAMIC_EDUCATION = 13;

    // Mobile app asset.
    MOBILE_APP = 14;

    // Hotel callout asset.
    HOTEL_CALLOUT = 15;

    // Call asset.
    CALL = 16;

    // Price asset.
    PRICE = 17;

    // Call to action asset.
    CALL_TO_ACTION = 18;

    // Dynamic real estate asset.
    DYNAMIC_REAL_ESTATE = 19;

    // Dynamic custom asset.
    DYNAMIC_CUSTOM = 20;

    // Dynamic hotels and rentals asset.
    DYNAMIC_HOTELS_AND_RENTALS = 21;

    // Dynamic flights asset.
    DYNAMIC_FLIGHTS = 22;

    // Discovery Carousel Card asset.
    DISCOVERY_CAROUSEL_CARD = 23;

    // Dynamic travel asset.
    DYNAMIC_TRAVEL = 24;

    // Dynamic local asset.
    DYNAMIC_LOCAL = 25;

    // Dynamic jobs asset.
    DYNAMIC_JOBS = 26;

    // Location asset.
    LOCATION = 27;

    // Hotel property asset.
    HOTEL_PROPERTY = 28;
  }
}
