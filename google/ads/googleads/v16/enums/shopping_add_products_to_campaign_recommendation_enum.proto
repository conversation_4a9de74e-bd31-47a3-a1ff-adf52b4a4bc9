// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ShoppingAddProductsToCampaignRecommendationEnumProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing ShoppingAddProductsToCampaignRecommendationEnum enum.

// Indicates the key issue that results in a shopping campaign targeting zero
// products.
message ShoppingAddProductsToCampaignRecommendationEnum {
  // Issues that results in a shopping campaign targeting zero products.
  enum Reason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The Merchant Center account does not have any submitted products.
    MERCHANT_CENTER_ACCOUNT_HAS_NO_SUBMITTED_PRODUCTS = 2;

    // The Merchant Center account does not have any submitted products in the
    // feed.
    MERCHANT_CENTER_ACCOUNT_HAS_NO_SUBMITTED_PRODUCTS_IN_FEED = 3;

    // The Google Ads account has active campaign filters that prevents
    // inclusion of offers in the campaign.
    ADS_ACCOUNT_EXCLUDES_OFFERS_FROM_CAMPAIGN = 4;

    // All products available have been explicitly excluded from
    // being targeted by the campaign.
    ALL_PRODUCTS_ARE_EXCLUDED_FROM_CAMPAIGN = 5;
  }
}
