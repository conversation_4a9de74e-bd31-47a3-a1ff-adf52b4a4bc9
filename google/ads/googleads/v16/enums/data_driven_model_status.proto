// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "DataDrivenModelStatusProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing data-driven model status.

// Container for enum indicating data driven model status.
message DataDrivenModelStatusEnum {
  // Enumerates data driven model statuses.
  enum DataDrivenModelStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The data driven model is available.
    AVAILABLE = 2;

    // The data driven model is stale. It hasn't been updated for at least 7
    // days. It is still being used, but will become expired if it does not get
    // updated for 30 days.
    STALE = 3;

    // The data driven model expired. It hasn't been updated for at least 30
    // days and cannot be used. Most commonly this is because there hasn't been
    // the required number of events in a recent 30-day period.
    EXPIRED = 4;

    // The data driven model has never been generated. Most commonly this is
    // because there has never been the required number of events in any 30-day
    // period.
    NEVER_GENERATED = 5;
  }
}
