// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ChangeClientTypeProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing the sources that the change event resource was
// made through.

// Container for enum describing the sources that the change event resource
// was made through.
message ChangeClientTypeEnum {
  // The source that the change_event resource was made through.
  enum ChangeClientType {
    // No value has been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents an unclassified client type
    // unknown in this version.
    UNKNOWN = 1;

    // Changes made through the "ads.google.com".
    // For example, changes made through campaign management.
    GOOGLE_ADS_WEB_CLIENT = 2;

    // Changes made through Google Ads automated rules.
    GOOGLE_ADS_AUTOMATED_RULE = 3;

    // Changes made through Google Ads scripts.
    GOOGLE_ADS_SCRIPTS = 4;

    // Changes made by Google Ads bulk upload.
    GOOGLE_ADS_BULK_UPLOAD = 5;

    // Changes made by Google Ads API.
    GOOGLE_ADS_API = 6;

    // Changes made by Google Ads Editor. This value is a placeholder.
    // The API does not return these changes.
    GOOGLE_ADS_EDITOR = 7;

    // Changes made by Google Ads mobile app.
    GOOGLE_ADS_MOBILE_APP = 8;

    // Changes made through Google Ads recommendations.
    GOOGLE_ADS_RECOMMENDATIONS = 9;

    // Changes made through Search Ads 360 Sync.
    SEARCH_ADS_360_SYNC = 10;

    // Changes made through Search Ads 360 Post.
    SEARCH_ADS_360_POST = 11;

    // Changes made through internal tools.
    // For example, when a user sets a URL template on an entity like a
    // Campaign, it's automatically wrapped with the SA360 Clickserver URL.
    INTERNAL_TOOL = 12;

    // Types of changes that are not categorized, for example,
    // changes made by coupon redemption through Google Ads.
    OTHER = 13;

    // Changes made by subscribing to Google Ads recommendations.
    GOOGLE_ADS_RECOMMENDATIONS_SUBSCRIPTION = 14;
  }
}
