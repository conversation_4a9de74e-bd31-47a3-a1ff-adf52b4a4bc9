// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/android_privacy_interaction_type.proto";
import "google/ads/googleads/v16/enums/android_privacy_network_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AndroidPrivacySharedKeyGoogleAdGroupProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the AndroidPrivacySharedKeyGoogleAdGroup resource.

// An Android privacy shared key view for Google ad group key.
message AndroidPrivacySharedKeyGoogleAdGroup {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AndroidPrivacySharedKeyGoogleAdGroup"
    pattern: "customers/{customer_id}/androidPrivacySharedKeyGoogleAdGroups/{campaign_id}~{ad_group_id}~{android_privacy_interaction_type}~{android_privacy_network_type}~{android_privacy_interaction_date}"
  };

  // Output only. The resource name of the Android privacy shared key.
  // Android privacy shared key resource names have the form:
  //
  // `customers/{customer_id}/androidPrivacySharedKeyGoogleAdGroups/{campaign_id}~{ad_group_id}~{android_privacy_interaction_type}~{android_privacy_network_type}~{android_privacy_interaction_date(yyyy-mm-dd)}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AndroidPrivacySharedKeyGoogleAdGroup"
    }
  ];

  // Output only. The campaign ID used in the share key encoding.
  int64 campaign_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The interaction type enum used in the share key encoding.
  google.ads.googleads.v16.enums.AndroidPrivacyInteractionTypeEnum
      .AndroidPrivacyInteractionType android_privacy_interaction_type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The interaction date used in the shared key encoding in the
  // format of "YYYY-MM-DD" in UTC timezone.
  string android_privacy_interaction_date = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The network type enum used in the share key encoding.
  google.ads.googleads.v16.enums.AndroidPrivacyNetworkTypeEnum
      .AndroidPrivacyNetworkType android_privacy_network_type = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ad group ID used in the share key encoding.
  int64 ad_group_id = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. 128 bit hex string of the encoded shared ad group key,
  // including a '0x' prefix. This key can be used to do a bitwise OR operator
  // with the aggregate conversion key to create a full aggregation key to
  // retrieve the Aggregate API Report in Android Privacy Sandbox.
  string shared_ad_group_key = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
