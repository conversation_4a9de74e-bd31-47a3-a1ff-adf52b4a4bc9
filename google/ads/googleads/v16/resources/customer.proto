// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/brand_safety_suitability.proto";
import "google/ads/googleads/v16/enums/conversion_tracking_status_enum.proto";
import "google/ads/googleads/v16/enums/customer_pay_per_conversion_eligibility_failure_reason.proto";
import "google/ads/googleads/v16/enums/customer_status.proto";
import "google/ads/googleads/v16/enums/local_services_verification_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Customer resource.

// A customer.
message Customer {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Customer"
    pattern: "customers/{customer_id}"
  };

  // Immutable. The resource name of the customer.
  // Customer resource names have the form:
  //
  // `customers/{customer_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];

  // Output only. The ID of the customer.
  optional int64 id = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional, non-unique descriptive name of the customer.
  optional string descriptive_name = 20;

  // Immutable. The currency in which the account operates.
  // A subset of the currency codes from the ISO 4217 standard is
  // supported.
  optional string currency_code = 21 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The local timezone ID of the customer.
  optional string time_zone = 22 [(google.api.field_behavior) = IMMUTABLE];

  // The URL template for constructing a tracking URL out of parameters.
  // Only mutable in an `update` operation.
  optional string tracking_url_template = 23;

  // The URL template for appending params to the final URL.
  // Only mutable in an `update` operation.
  optional string final_url_suffix = 24;

  // Whether auto-tagging is enabled for the customer.
  optional bool auto_tagging_enabled = 25;

  // Output only. Whether the Customer has a Partners program badge. If the
  // Customer is not associated with the Partners program, this will be false.
  // For more information, see
  // https://support.google.com/partners/answer/3125774.
  optional bool has_partners_badge = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the customer is a manager.
  optional bool manager = 27 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the customer is a test account.
  optional bool test_account = 28 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Call reporting setting for a customer. Only mutable in an `update`
  // operation.
  CallReportingSetting call_reporting_setting = 10;

  // Output only. Conversion tracking setting for a customer.
  ConversionTrackingSetting conversion_tracking_setting = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Remarketing setting for a customer.
  RemarketingSetting remarketing_setting = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reasons why the customer is not eligible to use
  // PaymentMode.CONVERSIONS. If the list is empty, the customer is eligible.
  // This field is read-only.
  repeated google.ads.googleads.v16.enums
      .CustomerPayPerConversionEligibilityFailureReasonEnum
      .CustomerPayPerConversionEligibilityFailureReason
          pay_per_conversion_eligibility_failure_reasons = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Optimization score of the customer.
  //
  // Optimization score is an estimate of how well a customer's campaigns are
  // set to perform. It ranges from 0% (0.0) to 100% (1.0). This field is null
  // for all manager customers, and for unscored non-manager customers.
  //
  // See "About optimization score" at
  // https://support.google.com/google-ads/answer/9061546.
  //
  // This field is read-only.
  optional double optimization_score = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Optimization score weight of the customer.
  //
  // Optimization score weight can be used to compare/aggregate optimization
  // scores across multiple non-manager customers. The aggregate optimization
  // score of a manager is computed as the sum over all of their customers of
  // `Customer.optimization_score * Customer.optimization_score_weight`. This
  // field is 0 for all manager customers, and for unscored non-manager
  // customers.
  //
  // This field is read-only.
  double optimization_score_weight = 30
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the customer.
  google.ads.googleads.v16.enums.CustomerStatusEnum.CustomerStatus status = 36
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if feed based location has been migrated to asset based
  // location.
  optional bool location_asset_auto_migration_done = 38
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if feed based image has been migrated to asset based
  // image.
  optional bool image_asset_auto_migration_done = 39
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp of migration from feed based location to asset base
  // location in yyyy-MM-dd HH:mm:ss format.
  optional string location_asset_auto_migration_done_date_time = 40
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp of migration from feed based image to asset base
  // image in yyyy-MM-dd HH:mm:ss format.
  optional string image_asset_auto_migration_done_date_time = 41
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Customer Agreement Setting for a customer.
  CustomerAgreementSetting customer_agreement_setting = 44
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Settings for Local Services customer.
  LocalServicesSettings local_services_settings = 45
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Brand Safety setting at the account level. Allows for
  // selecting an inventory type to show your ads on content that is the right
  // fit for your brand. See
  // https://support.google.com/google-ads/answer/7515513.
  google.ads.googleads.v16.enums.BrandSafetySuitabilityEnum
      .BrandSafetySuitability video_brand_safety_suitability = 46
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Call reporting setting for a customer. Only mutable in an `update` operation.
message CallReportingSetting {
  // Enable reporting of phone call events by redirecting them through Google
  // System.
  optional bool call_reporting_enabled = 10;

  // Whether to enable call conversion reporting.
  optional bool call_conversion_reporting_enabled = 11;

  // Customer-level call conversion action to attribute a call conversion to.
  // If not set a default conversion action is used. Only in effect when
  // call_conversion_reporting_enabled is set to true.
  optional string call_conversion_action = 12
      [(google.api.resource_reference) = {
        type: "googleads.googleapis.com/ConversionAction"
      }];
}

// A collection of customer-wide settings related to Google Ads Conversion
// Tracking.
message ConversionTrackingSetting {
  // Output only. The conversion tracking id used for this account. This id
  // doesn't indicate whether the customer uses conversion tracking
  // (conversion_tracking_status does). This field is read-only.
  optional int64 conversion_tracking_id = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The conversion tracking id of the customer's manager. This is
  // set when the customer is opted into cross account conversion tracking, and
  // it overrides conversion_tracking_id. This field can only be managed through
  // the Google Ads UI. This field is read-only.
  optional int64 cross_account_conversion_tracking_id = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the customer has accepted customer data terms. If
  // using cross-account conversion tracking, this value is inherited from the
  // manager. This field is read-only. For more
  // information, see https://support.google.com/adspolicy/answer/7475709.
  bool accepted_customer_data_terms = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Conversion tracking status. It indicates whether the customer
  // is using conversion tracking, and who is the conversion tracking owner of
  // this customer. If this customer is using cross-account conversion tracking,
  // the value returned will differ based on the `login-customer-id` of the
  // request.
  google.ads.googleads.v16.enums.ConversionTrackingStatusEnum
      .ConversionTrackingStatus conversion_tracking_status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the customer is opted-in for enhanced conversions
  // for leads. If using cross-account conversion tracking, this value is
  // inherited from the manager. This field is read-only.
  bool enhanced_conversions_for_leads_enabled = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the customer where conversions are
  // created and managed. This field is read-only.
  string google_ads_conversion_customer = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Remarketing setting for a customer.
message RemarketingSetting {
  // Output only. The Google tag.
  optional string google_global_site_tag = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Customer Agreement Setting for a customer.
message CustomerAgreementSetting {
  // Output only. Whether the customer has accepted lead form term of service.
  bool accepted_lead_form_terms = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Settings for Local Services customer.
message LocalServicesSettings {
  // Output only. A read-only list of geo vertical level license statuses.
  repeated GranularLicenseStatus granular_license_statuses = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A read-only list of geo vertical level insurance statuses.
  repeated GranularInsuranceStatus granular_insurance_statuses = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// License status at geo + vertical level.
message GranularLicenseStatus {
  // Output only. Geotarget criterion ID associated with the status. Can be on
  // country or state/province geo level, depending on requirements and
  // location. See https://developers.google.com/google-ads/api/data/geotargets
  // for more information.
  optional int64 geo_criterion_id = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Service category associated with the status. For example,
  // xcat:service_area_business_plumber.
  // For more details see:
  // https://developers.google.com/google-ads/api/data/codes-formats#local_services_ids
  optional string category_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Granular license status, per geo + vertical.
  optional google.ads.googleads.v16.enums.LocalServicesVerificationStatusEnum
      .LocalServicesVerificationStatus verification_status = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Insurance status at geo + vertical level.
message GranularInsuranceStatus {
  // Output only. Geotarget criterion ID associated with the status. Can be on
  // country or state/province geo level, depending on requirements and
  // location. See https://developers.google.com/google-ads/api/data/geotargets
  // for more information.
  optional int64 geo_criterion_id = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Service category associated with the status. For example,
  // xcat:service_area_business_plumber.
  // For more details see:
  // https://developers.google.com/google-ads/api/data/codes-formats#local_services_ids
  optional string category_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Granular insurance status, per geo + vertical.
  optional google.ads.googleads.v16.enums.LocalServicesVerificationStatusEnum
      .LocalServicesVerificationStatus verification_status = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
