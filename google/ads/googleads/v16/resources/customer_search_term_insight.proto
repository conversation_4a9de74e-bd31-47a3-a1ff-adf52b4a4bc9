// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerSearchTermInsightProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the customer search term insight resource.

// A Customer search term view.
// Historical data is available starting March 2023.
message CustomerSearchTermInsight {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerSearchTermInsight"
    pattern: "customers/{customer_id}/customerSearchTermInsights/{cluster_id}"
  };

  // Output only. The resource name of the customer level search term insight.
  // Customer level search term insight resource names have the form:
  //
  // `customers/{customer_id}/customerSearchTermInsights/{category_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerSearchTermInsight"
    }
  ];

  // Output only. The label for the search category. An empty string denotes the
  // catch-all category for search terms that didn't fit into another category.
  optional string category_label = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the insight.
  optional int64 id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
