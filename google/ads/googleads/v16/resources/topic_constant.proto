// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "TopicConstantProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Topic Constant resource.

// Use topics to target or exclude placements in the Google Display Network
// based on the category into which the placement falls (for example,
// "Pets & Animals/Pets/Dogs").
message TopicConstant {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/TopicConstant"
    pattern: "topicConstants/{topic_id}"
  };

  // Output only. The resource name of the topic constant.
  // topic constant resource names have the form:
  //
  // `topicConstants/{topic_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/TopicConstant"
    }
  ];

  // Output only. The ID of the topic.
  optional int64 id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource name of parent of the topic constant.
  optional string topic_constant_parent = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/TopicConstant"
    }
  ];

  // Output only. The category to target or exclude. Each subsequent element in
  // the array describes a more specific sub-category. For example,
  // {"Pets & Animals", "Pets", "Dogs"} represents the
  // "Pets & Animals/Pets/Dogs" category. List of available topic categories at
  // https://developers.google.com/google-ads/api/reference/data/verticals
  repeated string path = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
