// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/audiences.proto";
import "google/ads/googleads/v16/enums/audience_scope.proto";
import "google/ads/googleads/v16/enums/audience_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AudienceProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Audience resource.

// Audience is an effective targeting option that lets you
// intersect different segment attributes, such as detailed demographics and
// affinities, to create audiences that represent sections of your target
// segments.
message Audience {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Audience"
    pattern: "customers/{customer_id}/audiences/{audience_id}"
  };

  // Immutable. The resource name of the audience.
  // Audience names have the form:
  //
  // `customers/{customer_id}/audiences/{audience_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Audience"
    }
  ];

  // Output only. ID of the audience.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of this audience. Indicates whether the audience
  // is enabled or removed.
  google.ads.googleads.v16.enums.AudienceStatusEnum.AudienceStatus status = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Name of the audience. It should be unique across all audiences within the
  // account. It must have a minimum length of 1 and maximum length of 255.
  // Required when scope is not set or is set to CUSTOMER. Cannot be set or
  // updated when scope is ASSET_GROUP.
  optional string name = 10;

  // Description of this audience.
  string description = 5;

  // Positive dimensions specifying the audience composition.
  repeated google.ads.googleads.v16.common.AudienceDimension dimensions = 6;

  // Negative dimension specifying the audience composition.
  google.ads.googleads.v16.common.AudienceExclusionDimension
      exclusion_dimension = 7;

  // Defines the scope this audience can be used in. By default, the scope is
  // CUSTOMER. Audiences can be created with a scope of ASSET_GROUP for
  // exclusive use by a single asset_group. Scope may change from ASSET_GROUP to
  // CUSTOMER but not from CUSTOMER to ASSET_GROUP.
  google.ads.googleads.v16.enums.AudienceScopeEnum.AudienceScope scope = 8;

  // Immutable. The asset group that this audience is scoped under. Must be set
  // if and only if scope is ASSET_GROUP. Immutable after creation. If an
  // audience with ASSET_GROUP scope is upgraded to CUSTOMER scope, this field
  // will automatically be cleared.
  string asset_group = 9 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroup"
    }
  ];
}
