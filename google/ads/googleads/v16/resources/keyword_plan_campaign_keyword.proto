// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/keyword_match_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanCampaignKeywordProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the keyword plan negative keyword resource.

// A Keyword Plan Campaign keyword.
// Only negative keywords are supported for Campaign Keyword.
message KeywordPlanCampaignKeyword {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/KeywordPlanCampaignKeyword"
    pattern: "customers/{customer_id}/keywordPlanCampaignKeywords/{keyword_plan_campaign_keyword_id}"
  };

  // Immutable. The resource name of the Keyword Plan Campaign keyword.
  // KeywordPlanCampaignKeyword resource names have the form:
  //
  // `customers/{customer_id}/keywordPlanCampaignKeywords/{kp_campaign_keyword_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/KeywordPlanCampaignKeyword"
    }
  ];

  // The Keyword Plan campaign to which this negative keyword belongs.
  optional string keyword_plan_campaign = 8 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/KeywordPlanCampaign"
  }];

  // Output only. The ID of the Keyword Plan negative keyword.
  optional int64 id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The keyword text.
  optional string text = 10;

  // The keyword match type.
  google.ads.googleads.v16.enums.KeywordMatchTypeEnum.KeywordMatchType
      match_type = 5;

  // Immutable. If true, the keyword is negative.
  // Must be set to true. Only negative campaign keywords are supported.
  optional bool negative = 11 [(google.api.field_behavior) = IMMUTABLE];
}
