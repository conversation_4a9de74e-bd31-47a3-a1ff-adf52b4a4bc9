// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/asset_set_types.proto";
import "google/ads/googleads/v16/enums/asset_set_status.proto";
import "google/ads/googleads/v16/enums/asset_set_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetSetProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// An asset set representing a collection of assets.
// Use AssetSetAsset to link an asset to the asset set.
message AssetSet {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AssetSet"
    pattern: "customers/{customer_id}/assetSets/{asset_set_id}"
  };

  // Merchant ID and Feed Label from Google Merchant Center.
  message MerchantCenterFeed {
    // Required. Merchant ID from Google Merchant Center
    int64 merchant_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Feed Label from Google Merchant Center.
    optional string feed_label = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // For Performance Max for travel goals campaigns with a Hotel
  // Center account link. Read-only.
  message HotelPropertyData {
    // Output only. The hotel center ID of the partner.
    optional int64 hotel_center_id = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Name of the hotel partner.
    optional string partner_name = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The ID of the asset set.
  int64 id = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The resource name of the asset set.
  // Asset set resource names have the form:
  //
  // `customers/{customer_id}/assetSets/{asset_set_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetSet"
    }
  ];

  // Required. Name of the asset set. Required. It must have a minimum length of
  // 1 and maximum length of 128.
  string name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. The type of the asset set. Required.
  google.ads.googleads.v16.enums.AssetSetTypeEnum.AssetSetType type = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. The status of the asset set. Read-only.
  google.ads.googleads.v16.enums.AssetSetStatusEnum.AssetSetStatus status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Merchant ID and Feed Label from Google Merchant Center.
  MerchantCenterFeed merchant_center_feed = 5;

  // Immutable. Parent asset set id for the asset set where the elements of this
  // asset set come from. For example: the sync level location AssetSet id where
  // the the elements in LocationGroup AssetSet come from. This field is
  // required and only applicable for Location Group typed AssetSet.
  int64 location_group_parent_asset_set_id = 10
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. For Performance Max for travel goals campaigns with a Hotel
  // Center account link. Read-only.
  HotelPropertyData hotel_property_data = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Asset set data specific to each asset set type. Not all types have specific
  // data.
  oneof asset_set_source {
    // Location asset set data. This will be used for sync level location
    // set. This can only be set if AssetSet's type is LOCATION_SYNC.
    google.ads.googleads.v16.common.LocationSet location_set = 7;

    // Business Profile location group asset set data.
    google.ads.googleads.v16.common.BusinessProfileLocationGroup
        business_profile_location_group = 8;

    // Represents information about a Chain dynamic location group.
    // Only applicable if the sync level AssetSet's type is LOCATION_SYNC and
    // sync source is chain.
    google.ads.googleads.v16.common.ChainLocationGroup chain_location_group = 9;
  }
}
