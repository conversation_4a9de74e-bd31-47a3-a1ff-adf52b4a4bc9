// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/custom_parameter.proto";
import "google/ads/googleads/v16/common/feed_common.proto";
import "google/ads/googleads/v16/common/policy.proto";
import "google/ads/googleads/v16/enums/feed_item_quality_approval_status.proto";
import "google/ads/googleads/v16/enums/feed_item_quality_disapproval_reason.proto";
import "google/ads/googleads/v16/enums/feed_item_status.proto";
import "google/ads/googleads/v16/enums/feed_item_validation_status.proto";
import "google/ads/googleads/v16/enums/geo_targeting_restriction.proto";
import "google/ads/googleads/v16/enums/placeholder_type.proto";
import "google/ads/googleads/v16/enums/policy_approval_status.proto";
import "google/ads/googleads/v16/enums/policy_review_status.proto";
import "google/ads/googleads/v16/errors/feed_item_validation_error.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "FeedItemProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the FeedItem resource.

// A feed item.
message FeedItem {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/FeedItem"
    pattern: "customers/{customer_id}/feedItems/{feed_id}~{feed_item_id}"
  };

  // Immutable. The resource name of the feed item.
  // Feed item resource names have the form:
  //
  // `customers/{customer_id}/feedItems/{feed_id}~{feed_item_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/FeedItem"
    }
  ];

  // Immutable. The feed to which this feed item belongs.
  optional string feed = 11 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Feed" }
  ];

  // Output only. The ID of this feed item.
  optional int64 id = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Start time in which this feed item is effective and can begin serving. The
  // time is in the customer's time zone.
  // The format is "YYYY-MM-DD HH:MM:SS".
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  optional string start_date_time = 13;

  // End time in which this feed item is no longer effective and will stop
  // serving. The time is in the customer's time zone.
  // The format is "YYYY-MM-DD HH:MM:SS".
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  optional string end_date_time = 14;

  // The feed item's attribute values.
  repeated FeedItemAttributeValue attribute_values = 6;

  // Geo targeting restriction specifies the type of location that can be used
  // for targeting.
  google.ads.googleads.v16.enums.GeoTargetingRestrictionEnum
      .GeoTargetingRestriction geo_targeting_restriction = 7;

  // The list of mappings used to substitute custom parameter tags in a
  // `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
  repeated google.ads.googleads.v16.common.CustomParameter
      url_custom_parameters = 8;

  // Output only. Status of the feed item.
  // This field is read-only.
  google.ads.googleads.v16.enums.FeedItemStatusEnum.FeedItemStatus status = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of info about a feed item's validation and approval state
  // for active feed mappings. There will be an entry in the list for each type
  // of feed mapping associated with the feed, for example, a feed with a
  // sitelink and a call feed mapping would cause every feed item associated
  // with that feed to have an entry in this list for both sitelink and call.
  // This field is read-only.
  repeated FeedItemPlaceholderPolicyInfo policy_infos = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A feed item attribute value.
message FeedItemAttributeValue {
  // Id of the feed attribute for which the value is associated with.
  optional int64 feed_attribute_id = 11;

  // Int64 value. Should be set if feed_attribute_id refers to a feed attribute
  // of type INT64.
  optional int64 integer_value = 12;

  // Bool value. Should be set if feed_attribute_id refers to a feed attribute
  // of type BOOLEAN.
  optional bool boolean_value = 13;

  // String value. Should be set if feed_attribute_id refers to a feed attribute
  // of type STRING, URL or DATE_TIME.
  // For STRING the maximum length is 1500 characters. For URL the maximum
  // length is 2076 characters. For DATE_TIME the string must be in the format
  // "YYYYMMDD HHMMSS".
  optional string string_value = 14;

  // Double value. Should be set if feed_attribute_id refers to a feed attribute
  // of type DOUBLE.
  optional double double_value = 15;

  // Price value. Should be set if feed_attribute_id refers to a feed attribute
  // of type PRICE.
  google.ads.googleads.v16.common.Money price_value = 6;

  // Repeated int64 value. Should be set if feed_attribute_id refers to a feed
  // attribute of type INT64_LIST.
  repeated int64 integer_values = 16;

  // Repeated bool value. Should be set if feed_attribute_id refers to a feed
  // attribute of type BOOLEAN_LIST.
  repeated bool boolean_values = 17;

  // Repeated string value. Should be set if feed_attribute_id refers to a feed
  // attribute of type STRING_LIST, URL_LIST or DATE_TIME_LIST.
  // For STRING_LIST and URL_LIST the total size of the list in bytes may not
  // exceed 3000. For DATE_TIME_LIST the number of elements may not exceed 200.
  //
  // For STRING_LIST the maximum length of each string element is 1500
  // characters. For URL_LIST the maximum length is 2076 characters. For
  // DATE_TIME the format of the string must be the same as start and end time
  // for the feed item.
  repeated string string_values = 18;

  // Repeated double value. Should be set if feed_attribute_id refers to a feed
  // attribute of type DOUBLE_LIST.
  repeated double double_values = 19;
}

// Policy, validation, and quality approval info for a feed item for the
// specified placeholder type.
message FeedItemPlaceholderPolicyInfo {
  // Output only. The placeholder type.
  google.ads.googleads.v16.enums.PlaceholderTypeEnum.PlaceholderType
      placeholder_type_enum = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The FeedMapping that contains the placeholder type.
  optional string feed_mapping_resource_name = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Where the placeholder type is in the review process.
  google.ads.googleads.v16.enums.PolicyReviewStatusEnum.PolicyReviewStatus
      review_status = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The overall approval status of the placeholder type,
  // calculated based on the status of its individual policy topic entries.
  google.ads.googleads.v16.enums.PolicyApprovalStatusEnum.PolicyApprovalStatus
      approval_status = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of policy findings for the placeholder type.
  repeated google.ads.googleads.v16.common.PolicyTopicEntry
      policy_topic_entries = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The validation status of the placeholder type.
  google.ads.googleads.v16.enums.FeedItemValidationStatusEnum
      .FeedItemValidationStatus validation_status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of placeholder type validation errors.
  repeated FeedItemValidationError validation_errors = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Placeholder type quality evaluation approval status.
  google.ads.googleads.v16.enums.FeedItemQualityApprovalStatusEnum
      .FeedItemQualityApprovalStatus quality_approval_status = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of placeholder type quality evaluation disapproval
  // reasons.
  repeated google.ads.googleads.v16.enums.FeedItemQualityDisapprovalReasonEnum
      .FeedItemQualityDisapprovalReason quality_disapproval_reasons = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Stores a validation error and the set of offending feed attributes which
// together are responsible for causing a feed item validation error.
message FeedItemValidationError {
  // Output only. Error code indicating what validation error was triggered. The
  // description of the error can be found in the 'description' field.
  google.ads.googleads.v16.errors.FeedItemValidationErrorEnum
      .FeedItemValidationError validation_error = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The description of the validation error.
  optional string description = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Set of feed attributes in the feed item flagged during
  // validation. If empty, no specific feed attributes can be associated with
  // the error (for example, error across the entire feed item).
  repeated int64 feed_attribute_ids = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Any extra information related to this error which is not
  // captured by validation_error and feed_attribute_id (for example,
  // placeholder field IDs when feed_attribute_id is not mapped). Note that
  // extra_info is not localized.
  optional string extra_info = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
