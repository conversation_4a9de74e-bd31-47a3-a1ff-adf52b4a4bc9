// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/tag_snippet.proto";
import "google/ads/googleads/v16/enums/attribution_model.proto";
import "google/ads/googleads/v16/enums/conversion_action_category.proto";
import "google/ads/googleads/v16/enums/conversion_action_counting_type.proto";
import "google/ads/googleads/v16/enums/conversion_action_status.proto";
import "google/ads/googleads/v16/enums/conversion_action_type.proto";
import "google/ads/googleads/v16/enums/conversion_origin.proto";
import "google/ads/googleads/v16/enums/data_driven_model_status.proto";
import "google/ads/googleads/v16/enums/mobile_app_vendor.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ConversionActionProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Conversion Action resource.

// A conversion action.
message ConversionAction {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ConversionAction"
    pattern: "customers/{customer_id}/conversionActions/{conversion_action_id}"
  };

  // Settings related to this conversion action's attribution model.
  message AttributionModelSettings {
    // The attribution model type of this conversion action.
    google.ads.googleads.v16.enums.AttributionModelEnum.AttributionModel
        attribution_model = 1;

    // Output only. The status of the data-driven attribution model for the
    // conversion action.
    google.ads.googleads.v16.enums.DataDrivenModelStatusEnum
        .DataDrivenModelStatus data_driven_model_status = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Settings related to the value for conversion events associated with this
  // conversion action.
  message ValueSettings {
    // The value to use when conversion events for this conversion action are
    // sent with an invalid, disallowed or missing value, or when
    // this conversion action is configured to always use the default value.
    optional double default_value = 4;

    // The currency code to use when conversion events for this conversion
    // action are sent with an invalid or missing currency code, or when this
    // conversion action is configured to always use the default value.
    optional string default_currency_code = 5;

    // Controls whether the default value and default currency code are used in
    // place of the value and currency code specified in conversion events for
    // this conversion action.
    optional bool always_use_default_value = 6;
  }

  // Settings related to a third party app analytics conversion action.
  message ThirdPartyAppAnalyticsSettings {
    // Output only. The event name of a third-party app analytics conversion.
    optional string event_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Name of the third-party app analytics provider.
    string provider_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Settings related to a Firebase conversion action.
  message FirebaseSettings {
    // Output only. The event name of a Firebase conversion.
    optional string event_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Firebase project ID of the conversion.
    optional string project_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The GA property ID of the conversion.
    int64 property_id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The GA property name of the conversion.
    string property_name = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Settings related to a Google Analytics 4 conversion action.
  message GoogleAnalytics4Settings {
    // Output only. The name of the GA 4 event.
    string event_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The name of the GA 4 property.
    string property_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The ID of the GA 4 property.
    int64 property_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. The resource name of the conversion action.
  // Conversion action resource names have the form:
  //
  // `customers/{customer_id}/conversionActions/{conversion_action_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ConversionAction"
    }
  ];

  // Output only. The ID of the conversion action.
  optional int64 id = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the conversion action.
  //
  // This field is required and should not be empty when creating new
  // conversion actions.
  optional string name = 22;

  // The status of this conversion action for conversion event accrual.
  google.ads.googleads.v16.enums.ConversionActionStatusEnum
      .ConversionActionStatus status = 4;

  // Immutable. The type of this conversion action.
  google.ads.googleads.v16.enums.ConversionActionTypeEnum.ConversionActionType
      type = 5 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The conversion origin of this conversion action.
  google.ads.googleads.v16.enums.ConversionOriginEnum.ConversionOrigin origin =
      30 [(google.api.field_behavior) = OUTPUT_ONLY];

  // If a conversion action's primary_for_goal bit is false, the conversion
  // action is non-biddable for all campaigns regardless of their customer
  // conversion goal or campaign conversion goal.
  // However, custom conversion goals do not respect primary_for_goal, so if
  // a campaign has a custom conversion goal configured with a
  // primary_for_goal = false conversion action, that conversion action is
  // still biddable.
  // By default, primary_for_goal will be true if not set. In V9,
  // primary_for_goal can only be set to false after creation through an
  // 'update' operation because it's not declared as optional.
  optional bool primary_for_goal = 31;

  // The category of conversions reported for this conversion action.
  google.ads.googleads.v16.enums.ConversionActionCategoryEnum
      .ConversionActionCategory category = 6;

  // Output only. The resource name of the conversion action owner customer, or
  // null if this is a system-defined conversion action.
  optional string owner_customer = 23 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];

  // Whether this conversion action should be included in the "conversions"
  // metric.
  optional bool include_in_conversions_metric = 24;

  // The maximum number of days that may elapse between an interaction
  // (for example, a click) and a conversion event.
  optional int64 click_through_lookback_window_days = 25;

  // The maximum number of days which may elapse between an impression and a
  // conversion without an interaction.
  optional int64 view_through_lookback_window_days = 26;

  // Settings related to the value for conversion events associated with this
  // conversion action.
  ValueSettings value_settings = 11;

  // How to count conversion events for the conversion action.
  google.ads.googleads.v16.enums.ConversionActionCountingTypeEnum
      .ConversionActionCountingType counting_type = 12;

  // Settings related to this conversion action's attribution model.
  AttributionModelSettings attribution_model_settings = 13;

  // Output only. The snippets used for tracking conversions.
  repeated google.ads.googleads.v16.common.TagSnippet tag_snippets = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The phone call duration in seconds after which a conversion should be
  // reported for this conversion action.
  //
  // The value must be between 0 and 10000, inclusive.
  optional int64 phone_call_duration_seconds = 27;

  // App ID for an app conversion action.
  optional string app_id = 28;

  // Output only. Mobile app vendor for an app conversion action.
  google.ads.googleads.v16.enums.MobileAppVendorEnum.MobileAppVendor
      mobile_app_vendor = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Firebase settings for Firebase conversion types.
  FirebaseSettings firebase_settings = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Third Party App Analytics settings for third party conversion
  // types.
  ThirdPartyAppAnalyticsSettings third_party_app_analytics_settings = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Google Analytics 4 settings for Google Analytics 4 conversion
  // types.
  GoogleAnalytics4Settings google_analytics_4_settings = 34
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
