// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/bidding.proto";
import "google/ads/googleads/v16/enums/bidding_strategy_status.proto";
import "google/ads/googleads/v16/enums/bidding_strategy_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "BiddingStrategyProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the BiddingStrategy resource

// A bidding strategy.
message BiddingStrategy {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/BiddingStrategy"
    pattern: "customers/{customer_id}/biddingStrategies/{bidding_strategy_id}"
  };

  // Immutable. The resource name of the bidding strategy.
  // Bidding strategy resource names have the form:
  //
  // `customers/{customer_id}/biddingStrategies/{bidding_strategy_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BiddingStrategy"
    }
  ];

  // Output only. The ID of the bidding strategy.
  optional int64 id = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the bidding strategy.
  // All bidding strategies within an account must be named distinctly.
  //
  // The length of this string should be between 1 and 255, inclusive,
  // in UTF-8 bytes, (trimmed).
  optional string name = 17;

  // Output only. The status of the bidding strategy.
  //
  // This field is read-only.
  google.ads.googleads.v16.enums.BiddingStrategyStatusEnum.BiddingStrategyStatus
      status = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the bidding strategy.
  // Create a bidding strategy by setting the bidding scheme.
  //
  // This field is read-only.
  google.ads.googleads.v16.enums.BiddingStrategyTypeEnum.BiddingStrategyType
      type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The currency used by the bidding strategy (ISO 4217 three-letter
  // code).
  //
  // For bidding strategies in manager customers, this currency can be set on
  // creation and defaults to the manager customer's currency. For serving
  // customers, this field cannot be set; all strategies in a serving customer
  // implicitly use the serving customer's currency. In all cases the
  // effective_currency_code field returns the currency used by the strategy.
  string currency_code = 23 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The currency used by the bidding strategy (ISO 4217
  // three-letter code).
  //
  // For bidding strategies in manager customers, this is the currency set by
  // the advertiser when creating the strategy. For serving customers, this is
  // the customer's currency_code.
  //
  // Bidding strategy metrics are reported in this currency.
  //
  // This field is read-only.
  optional string effective_currency_code = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // ID of the campaign budget that this portfolio bidding strategy
  // is aligned with. When a portfolio and a campaign budget are aligned, that
  // means that they are attached to the same set of campaigns. After a bidding
  // strategy is aligned with a campaign budget, campaigns that are added to the
  // bidding strategy must also use the aligned campaign budget.
  int64 aligned_campaign_budget_id = 25;

  // Output only. The number of campaigns attached to this bidding strategy.
  //
  // This field is read-only.
  optional int64 campaign_count = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of non-removed campaigns attached to this bidding
  // strategy.
  //
  // This field is read-only.
  optional int64 non_removed_campaign_count = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The bidding scheme.
  //
  // Only one can be set.
  oneof scheme {
    // A bidding strategy that raises bids for clicks that seem more likely to
    // lead to a conversion and lowers them for clicks where they seem less
    // likely.
    google.ads.googleads.v16.common.EnhancedCpc enhanced_cpc = 7;

    // An automated bidding strategy to help get the most conversion value for
    // your campaigns while spending your budget.
    google.ads.googleads.v16.common.MaximizeConversionValue
        maximize_conversion_value = 21;

    // An automated bidding strategy to help get the most conversions for your
    // campaigns while spending your budget.
    google.ads.googleads.v16.common.MaximizeConversions maximize_conversions =
        22;

    // A bidding strategy that sets bids to help get as many conversions as
    // possible at the target cost-per-acquisition (CPA) you set.
    google.ads.googleads.v16.common.TargetCpa target_cpa = 9;

    // A bidding strategy that automatically optimizes towards a chosen
    // percentage of impressions.
    google.ads.googleads.v16.common.TargetImpressionShare
        target_impression_share = 48;

    // A bidding strategy that helps you maximize revenue while averaging a
    // specific target Return On Ad Spend (ROAS).
    google.ads.googleads.v16.common.TargetRoas target_roas = 11;

    // A bid strategy that sets your bids to help get as many clicks as
    // possible within your budget.
    google.ads.googleads.v16.common.TargetSpend target_spend = 12;
  }
}
