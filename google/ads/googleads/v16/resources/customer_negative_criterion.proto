// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/criteria.proto";
import "google/ads/googleads/v16/enums/criterion_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerNegativeCriterionProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Customer Negative Criterion resource.

// A negative criterion for exclusions at the customer level.
message CustomerNegativeCriterion {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerNegativeCriterion"
    pattern: "customers/{customer_id}/customerNegativeCriteria/{criterion_id}"
  };

  // Immutable. The resource name of the customer negative criterion.
  // Customer negative criterion resource names have the form:
  //
  // `customers/{customer_id}/customerNegativeCriteria/{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerNegativeCriterion"
    }
  ];

  // Output only. The ID of the criterion.
  optional int64 id = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the criterion.
  google.ads.googleads.v16.enums.CriterionTypeEnum.CriterionType type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The customer negative criterion.
  //
  // Exactly one must be set.
  oneof criterion {
    // Immutable. ContentLabel.
    google.ads.googleads.v16.common.ContentLabelInfo content_label = 4
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. MobileApplication.
    google.ads.googleads.v16.common.MobileApplicationInfo mobile_application = 5
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. MobileAppCategory.
    google.ads.googleads.v16.common.MobileAppCategoryInfo mobile_app_category =
        6 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Placement.
    google.ads.googleads.v16.common.PlacementInfo placement = 7
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. YouTube Video.
    google.ads.googleads.v16.common.YouTubeVideoInfo youtube_video = 8
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. YouTube Channel.
    google.ads.googleads.v16.common.YouTubeChannelInfo youtube_channel = 9
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. NegativeKeywordList.
    google.ads.googleads.v16.common.NegativeKeywordListInfo
        negative_keyword_list = 11 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. IPBLock
    google.ads.googleads.v16.common.IpBlockInfo ip_block = 12
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
