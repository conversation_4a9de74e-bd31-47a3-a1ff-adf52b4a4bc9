// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/combined_audience_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CombinedAudienceProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Combined Audience resource.

// Describe a resource for combined audiences which includes different
// audiences.
message CombinedAudience {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CombinedAudience"
    pattern: "customers/{customer_id}/combinedAudiences/{combined_audience_id}"
  };

  // Immutable. The resource name of the combined audience.
  // Combined audience names have the form:
  //
  // `customers/{customer_id}/combinedAudience/{combined_audience_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CombinedAudience"
    }
  ];

  // Output only. ID of the combined audience.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of this combined audience. Indicates whether the
  // combined audience is enabled or removed.
  google.ads.googleads.v16.enums.CombinedAudienceStatusEnum
      .CombinedAudienceStatus status = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the combined audience. It should be unique across all
  // combined audiences.
  string name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Description of this combined audience.
  string description = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
