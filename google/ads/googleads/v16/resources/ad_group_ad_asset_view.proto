// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/policy.proto";
import "google/ads/googleads/v16/enums/asset_field_type.proto";
import "google/ads/googleads/v16/enums/asset_performance_label.proto";
import "google/ads/googleads/v16/enums/asset_source.proto";
import "google/ads/googleads/v16/enums/policy_approval_status.proto";
import "google/ads/googleads/v16/enums/policy_review_status.proto";
import "google/ads/googleads/v16/enums/served_asset_field_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdAssetViewProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the ad group ad asset view resource.

// A link between an AdGroupAd and an Asset.
// Currently we only support AdGroupAdAssetView for AppAds and Responsive Search
// Ads.
message AdGroupAdAssetView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupAdAssetView"
    pattern: "customers/{customer_id}/adGroupAdAssetViews/{ad_group_id}~{ad_id}~{asset_id}~{field_type}"
  };

  // Output only. The resource name of the ad group ad asset view.
  // Ad group ad asset view resource names have the form (Before V4):
  //
  // `customers/{customer_id}/adGroupAdAssets/{AdGroupAdAsset.ad_group_id}~{AdGroupAdAsset.ad.ad_id}~{AdGroupAdAsset.asset_id}~{AdGroupAdAsset.field_type}`
  //
  // Ad group ad asset view resource names have the form (Beginning from V4):
  //
  // `customers/{customer_id}/adGroupAdAssetViews/{AdGroupAdAsset.ad_group_id}~{AdGroupAdAsset.ad_id}~{AdGroupAdAsset.asset_id}~{AdGroupAdAsset.field_type}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAdAssetView"
    }
  ];

  // Output only. The ad group ad to which the asset is linked.
  optional string ad_group_ad = 9 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAd"
    }
  ];

  // Output only. The asset which is linked to the ad group ad.
  optional string asset = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // Output only. Role that the asset takes in the ad.
  google.ads.googleads.v16.enums.AssetFieldTypeEnum.AssetFieldType field_type =
      2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status between the asset and the latest version of the ad.
  // If true, the asset is linked to the latest version of the ad. If false, it
  // means the link once existed but has been removed and is no longer present
  // in the latest version of the ad.
  optional bool enabled = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Policy information for the ad group ad asset.
  AdGroupAdAssetPolicySummary policy_summary = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Performance of an asset linkage.
  google.ads.googleads.v16.enums.AssetPerformanceLabelEnum.AssetPerformanceLabel
      performance_label = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Pinned field.
  google.ads.googleads.v16.enums.ServedAssetFieldTypeEnum.ServedAssetFieldType
      pinned_field = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of the ad group ad asset.
  google.ads.googleads.v16.enums.AssetSourceEnum.AssetSource source = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains policy information for an ad group ad asset.
message AdGroupAdAssetPolicySummary {
  // Output only. The list of policy findings for the ad group ad asset.
  repeated google.ads.googleads.v16.common.PolicyTopicEntry
      policy_topic_entries = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Where in the review process this ad group ad asset is.
  google.ads.googleads.v16.enums.PolicyReviewStatusEnum.PolicyReviewStatus
      review_status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The overall approval status of this ad group ad asset,
  // calculated based on the status of its individual policy topic entries.
  google.ads.googleads.v16.enums.PolicyApprovalStatusEnum.PolicyApprovalStatus
      approval_status = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
