// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/criteria.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignBidModifierProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Campaign Bid Modifier resource.

// Represents a bid-modifiable only criterion at the campaign level.
message CampaignBidModifier {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignBidModifier"
    pattern: "customers/{customer_id}/campaignBidModifiers/{campaign_id}~{criterion_id}"
  };

  // Immutable. The resource name of the campaign bid modifier.
  // Campaign bid modifier resource names have the form:
  //
  // `customers/{customer_id}/campaignBidModifiers/{campaign_id}~{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignBidModifier"
    }
  ];

  // Output only. The campaign to which this criterion belongs.
  optional string campaign = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The ID of the criterion to bid modify.
  //
  // This field is ignored for mutates.
  optional int64 criterion_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The modifier for the bid when the criterion matches.
  optional double bid_modifier = 8;

  // The criterion of this campaign bid modifier.
  //
  // Required in create operations starting in V5.
  oneof criterion {
    // Immutable. Criterion for interaction type. Only supported for search
    // campaigns.
    google.ads.googleads.v16.common.InteractionTypeInfo interaction_type = 5
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
