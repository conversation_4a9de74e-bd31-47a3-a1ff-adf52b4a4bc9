// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "DomainCategoryProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Domain Category resource.

// A category generated automatically by crawling a domain. If a campaign uses
// the DynamicSearchAdsSetting, then domain categories will be generated for
// the domain. The categories can be targeted using WebpageConditionInfo.
// See: https://support.google.com/google-ads/answer/2471185
message DomainCategory {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/DomainCategory"
    pattern: "customers/{customer_id}/domainCategories/{campaign_id}~{base64_category}~{language_code}"
  };

  // Output only. The resource name of the domain category.
  // Domain category resource names have the form:
  //
  // `customers/{customer_id}/domainCategories/{campaign_id}~{category_base64}~{language_code}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/DomainCategory"
    }
  ];

  // Output only. The campaign this category is recommended for.
  optional string campaign = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. Recommended category for the website domain, for example, if
  // you have a website about electronics, the categories could be "cameras",
  // "televisions", etc.
  optional string category = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The language code specifying the language of the website, for
  // example, "en" for English. The language can be specified in the
  // DynamicSearchAdsSetting required for dynamic search ads. This is the
  // language of the pages from your website that you want Google Ads to find,
  // create ads for, and match searches with.
  optional string language_code = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The domain for the website. The domain can be specified in the
  // DynamicSearchAdsSetting required for dynamic search ads.
  optional string domain = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Fraction of pages on your site that this category matches.
  optional double coverage_fraction = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The position of this category in the set of categories. Lower
  // numbers indicate a better match for the domain. null indicates not
  // recommended.
  optional int64 category_rank = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether this category has sub-categories.
  optional bool has_children = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The recommended cost per click for the category.
  optional int64 recommended_cpc_bid_micros = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
