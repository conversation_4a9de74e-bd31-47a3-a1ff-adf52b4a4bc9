// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "UserLocationViewProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the user location view resource.

// A user location view.
//
// User Location View includes all metrics aggregated at the country level,
// one row per country. It reports metrics at the actual physical location of
// the user by targeted or not targeted location. If other segment fields are
// used, you may get more than one row per country.
message UserLocationView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/UserLocationView"
    pattern: "customers/{customer_id}/userLocationViews/{country_criterion_id}~{is_targeting_location}"
  };

  // Output only. The resource name of the user location view.
  // UserLocation view resource names have the form:
  //
  // `customers/{customer_id}/userLocationViews/{country_criterion_id}~{targeting_location}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserLocationView"
    }
  ];

  // Output only. Criterion Id for the country.
  optional int64 country_criterion_id = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether location was targeted or not.
  optional bool targeting_location = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
