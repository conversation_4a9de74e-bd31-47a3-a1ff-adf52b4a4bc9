// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/account_budget_proposal_status.proto";
import "google/ads/googleads/v16/enums/account_budget_proposal_type.proto";
import "google/ads/googleads/v16/enums/spending_limit_type.proto";
import "google/ads/googleads/v16/enums/time_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AccountBudgetProposalProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the AccountBudgetProposal resource.

// An account-level budget proposal.
//
// All fields prefixed with 'proposed' may not necessarily be applied directly.
// For example, proposed spending limits may be adjusted before their
// application.  This is true if the 'proposed' field has an 'approved'
// counterpart, for example, spending limits.
//
// Note that the proposal type (proposal_type) changes which fields are
// required and which must remain empty.
message AccountBudgetProposal {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AccountBudgetProposal"
    pattern: "customers/{customer_id}/accountBudgetProposals/{account_budget_proposal_id}"
  };

  // Immutable. The resource name of the proposal.
  // AccountBudgetProposal resource names have the form:
  //
  // `customers/{customer_id}/accountBudgetProposals/{account_budget_proposal_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AccountBudgetProposal"
    }
  ];

  // Output only. The ID of the proposal.
  optional int64 id = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The resource name of the billing setup associated with this
  // proposal.
  optional string billing_setup = 26 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BillingSetup"
    }
  ];

  // Immutable. The resource name of the account-level budget associated with
  // this proposal.
  optional string account_budget = 27 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AccountBudget"
    }
  ];

  // Immutable. The type of this proposal, for example, END to end the budget
  // associated with this proposal.
  google.ads.googleads.v16.enums.AccountBudgetProposalTypeEnum
      .AccountBudgetProposalType proposal_type = 4
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The status of this proposal.
  // When a new proposal is created, the status defaults to PENDING.
  google.ads.googleads.v16.enums.AccountBudgetProposalStatusEnum
      .AccountBudgetProposalStatus status = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The name to assign to the account-level budget.
  optional string proposed_name = 28 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The approved start date time in yyyy-mm-dd hh:mm:ss format.
  optional string approved_start_date_time = 30
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. A purchase order number is a value that enables the user to help
  // them reference this budget in their monthly invoices.
  optional string proposed_purchase_order_number = 35
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Notes associated with this budget.
  optional string proposed_notes = 36 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The date time when this account-level budget proposal was
  // created, which is not the same as its approval date time, if applicable.
  optional string creation_date_time = 37
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date time when this account-level budget was approved, if
  // applicable.
  optional string approval_date_time = 38
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The proposed start date time of the account-level budget, which cannot be
  // in the past.
  oneof proposed_start_time {
    // Immutable. The proposed start date time in yyyy-mm-dd hh:mm:ss format.
    string proposed_start_date_time = 29
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The proposed start date time as a well-defined type, for
    // example, NOW.
    google.ads.googleads.v16.enums.TimeTypeEnum.TimeType
        proposed_start_time_type = 7 [(google.api.field_behavior) = IMMUTABLE];
  }

  // The proposed end date time of the account-level budget, which cannot be in
  // the past.
  oneof proposed_end_time {
    // Immutable. The proposed end date time in yyyy-mm-dd hh:mm:ss format.
    string proposed_end_date_time = 31
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The proposed end date time as a well-defined type, for
    // example, FOREVER.
    google.ads.googleads.v16.enums.TimeTypeEnum.TimeType
        proposed_end_time_type = 9 [(google.api.field_behavior) = IMMUTABLE];
  }

  // The approved end date time of the account-level budget.
  oneof approved_end_time {
    // Output only. The approved end date time in yyyy-mm-dd hh:mm:ss format.
    string approved_end_date_time = 32
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The approved end date time as a well-defined type, for
    // example, FOREVER.
    google.ads.googleads.v16.enums.TimeTypeEnum.TimeType
        approved_end_time_type = 22 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The proposed spending limit.
  oneof proposed_spending_limit {
    // Immutable. The proposed spending limit in micros.  One million is
    // equivalent to one unit.
    int64 proposed_spending_limit_micros = 33
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The proposed spending limit as a well-defined type, for
    // example, INFINITE.
    google.ads.googleads.v16.enums.SpendingLimitTypeEnum.SpendingLimitType
        proposed_spending_limit_type = 11
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // The approved spending limit.
  oneof approved_spending_limit {
    // Output only. The approved spending limit in micros.  One million is
    // equivalent to one unit.
    int64 approved_spending_limit_micros = 34
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The approved spending limit as a well-defined type, for
    // example, INFINITE.
    google.ads.googleads.v16.enums.SpendingLimitTypeEnum.SpendingLimitType
        approved_spending_limit_type = 24
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
