// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/change_client_type.proto";
import "google/ads/googleads/v16/enums/change_event_resource_type.proto";
import "google/ads/googleads/v16/enums/resource_change_operation.proto";
import "google/ads/googleads/v16/resources/ad.proto";
import "google/ads/googleads/v16/resources/ad_group.proto";
import "google/ads/googleads/v16/resources/ad_group_ad.proto";
import "google/ads/googleads/v16/resources/ad_group_asset.proto";
import "google/ads/googleads/v16/resources/ad_group_bid_modifier.proto";
import "google/ads/googleads/v16/resources/ad_group_criterion.proto";
import "google/ads/googleads/v16/resources/ad_group_feed.proto";
import "google/ads/googleads/v16/resources/asset.proto";
import "google/ads/googleads/v16/resources/asset_set.proto";
import "google/ads/googleads/v16/resources/asset_set_asset.proto";
import "google/ads/googleads/v16/resources/campaign.proto";
import "google/ads/googleads/v16/resources/campaign_asset.proto";
import "google/ads/googleads/v16/resources/campaign_asset_set.proto";
import "google/ads/googleads/v16/resources/campaign_budget.proto";
import "google/ads/googleads/v16/resources/campaign_criterion.proto";
import "google/ads/googleads/v16/resources/campaign_feed.proto";
import "google/ads/googleads/v16/resources/customer_asset.proto";
import "google/ads/googleads/v16/resources/feed.proto";
import "google/ads/googleads/v16/resources/feed_item.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ChangeEventProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Change Event resource.

// Describes the granular change of returned resources of certain resource
// types. Changes made through the UI or API in the past 30 days are included.
// Previous and new values of the changed fields are shown. ChangeEvent could
// have up to 3 minutes delay to reflect a new change.
message ChangeEvent {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ChangeEvent"
    pattern: "customers/{customer_id}/changeEvents/{timestamp_micros}~{command_index}~{mutate_index}"
  };

  // A wrapper proto presenting all supported resources.
  // Only the resource of the change_resource_type will be set.
  message ChangedResource {
    // Output only. Set if change_resource_type == AD.
    Ad ad = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == AD_GROUP.
    AdGroup ad_group = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == AD_GROUP_CRITERION.
    AdGroupCriterion ad_group_criterion = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == CAMPAIGN.
    Campaign campaign = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == CAMPAIGN_BUDGET.
    CampaignBudget campaign_budget = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == AD_GROUP_BID_MODIFIER.
    AdGroupBidModifier ad_group_bid_modifier = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == CAMPAIGN_CRITERION.
    CampaignCriterion campaign_criterion = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == FEED.
    Feed feed = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == FEED_ITEM.
    FeedItem feed_item = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == CAMPAIGN_FEED.
    CampaignFeed campaign_feed = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == AD_GROUP_FEED.
    AdGroupFeed ad_group_feed = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == AD_GROUP_AD.
    AdGroupAd ad_group_ad = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == ASSET.
    Asset asset = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == CUSTOMER_ASSET.
    CustomerAsset customer_asset = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == CAMPAIGN_ASSET.
    CampaignAsset campaign_asset = 15
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == AD_GROUP_ASSET.
    AdGroupAsset ad_group_asset = 16
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == ASSET_SET.
    AssetSet asset_set = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == ASSET_SET_ASSET.
    AssetSetAsset asset_set_asset = 18
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Set if change_resource_type == CAMPAIGN_ASSET_SET.
    CampaignAssetSet campaign_asset_set = 19
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The resource name of the change event.
  // Change event resource names have the form:
  //
  // `customers/{customer_id}/changeEvents/{timestamp_micros}~{command_index}~{mutate_index}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ChangeEvent"
    }
  ];

  // Output only. Time at which the change was committed on this resource.
  string change_date_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the changed resource. This dictates what resource
  // will be set in old_resource and new_resource.
  google.ads.googleads.v16.enums.ChangeEventResourceTypeEnum
      .ChangeEventResourceType change_resource_type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Simply resource this change occurred on.
  string change_resource_name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Where the change was made through.
  google.ads.googleads.v16.enums.ChangeClientTypeEnum.ChangeClientType
      client_type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The email of the user who made this change.
  string user_email = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The old resource before the change. Only changed fields will
  // be populated.
  ChangedResource old_resource = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The new resource after the change. Only changed fields will be
  // populated.
  ChangedResource new_resource = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The operation on the changed resource.
  google.ads.googleads.v16.enums.ResourceChangeOperationEnum
      .ResourceChangeOperation resource_change_operation = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of fields that are changed in the returned resource.
  google.protobuf.FieldMask changed_fields = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Campaign affected by this change.
  string campaign = 11 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The AdGroup affected by this change.
  string ad_group = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. The Feed affected by this change.
  string feed = 13 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Feed" }
  ];

  // Output only. The FeedItem affected by this change.
  string feed_item = 14 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/FeedItem"
    }
  ];

  // Output only. The Asset affected by this change.
  string asset = 20 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];
}
