// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ShoppingPerformanceViewProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the ShoppingPerformanceView resource.

// Shopping performance view.
// Provides Shopping campaign statistics aggregated at several product dimension
// levels. Product dimension values from Merchant Center such as brand,
// category, custom attributes, product condition and product type will reflect
// the state of each dimension as of the date and time when the corresponding
// event was recorded.
message ShoppingPerformanceView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ShoppingPerformanceView"
    pattern: "customers/{customer_id}/shoppingPerformanceView"
  };

  // Output only. The resource name of the Shopping performance view.
  // Shopping performance view resource names have the form:
  // `customers/{customer_id}/shoppingPerformanceView`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ShoppingPerformanceView"
    }
  ];
}
