// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/criteria.proto";
import "google/ads/googleads/v16/enums/campaign_criterion_status.proto";
import "google/ads/googleads/v16/enums/criterion_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignCriterionProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Campaign Criterion resource.

// A campaign criterion.
message CampaignCriterion {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignCriterion"
    pattern: "customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}"
  };

  // Immutable. The resource name of the campaign criterion.
  // Campaign criterion resource names have the form:
  //
  // `customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignCriterion"
    }
  ];

  // Immutable. The campaign to which the criterion belongs.
  optional string campaign = 37 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The ID of the criterion.
  //
  // This field is ignored during mutate.
  optional int64 criterion_id = 38 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name of the criterion.
  //
  // This field is ignored for mutates.
  string display_name = 43 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The modifier for the bids when the criterion matches. The modifier must be
  // in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
  // Use 0 to opt out of a Device type.
  optional float bid_modifier = 39;

  // Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
  optional bool negative = 40 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The type of the criterion.
  google.ads.googleads.v16.enums.CriterionTypeEnum.CriterionType type = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The status of the criterion.
  google.ads.googleads.v16.enums.CampaignCriterionStatusEnum
      .CampaignCriterionStatus status = 35;

  // The campaign criterion.
  //
  // Exactly one must be set.
  oneof criterion {
    // Immutable. Keyword.
    google.ads.googleads.v16.common.KeywordInfo keyword = 8
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Placement.
    google.ads.googleads.v16.common.PlacementInfo placement = 9
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Mobile app category.
    google.ads.googleads.v16.common.MobileAppCategoryInfo mobile_app_category =
        10 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Mobile application.
    google.ads.googleads.v16.common.MobileApplicationInfo mobile_application =
        11 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Location.
    google.ads.googleads.v16.common.LocationInfo location = 12
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Device.
    google.ads.googleads.v16.common.DeviceInfo device = 13
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Ad Schedule.
    google.ads.googleads.v16.common.AdScheduleInfo ad_schedule = 15
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Age range.
    google.ads.googleads.v16.common.AgeRangeInfo age_range = 16
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Gender.
    google.ads.googleads.v16.common.GenderInfo gender = 17
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Income range.
    google.ads.googleads.v16.common.IncomeRangeInfo income_range = 18
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Parental status.
    google.ads.googleads.v16.common.ParentalStatusInfo parental_status = 19
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. User List.
    google.ads.googleads.v16.common.UserListInfo user_list = 22
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. YouTube Video.
    google.ads.googleads.v16.common.YouTubeVideoInfo youtube_video = 20
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. YouTube Channel.
    google.ads.googleads.v16.common.YouTubeChannelInfo youtube_channel = 21
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Proximity.
    google.ads.googleads.v16.common.ProximityInfo proximity = 23
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Topic.
    google.ads.googleads.v16.common.TopicInfo topic = 24
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Listing scope.
    google.ads.googleads.v16.common.ListingScopeInfo listing_scope = 25
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Language.
    google.ads.googleads.v16.common.LanguageInfo language = 26
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. IpBlock.
    google.ads.googleads.v16.common.IpBlockInfo ip_block = 27
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. ContentLabel.
    google.ads.googleads.v16.common.ContentLabelInfo content_label = 28
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Carrier.
    google.ads.googleads.v16.common.CarrierInfo carrier = 29
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. User Interest.
    google.ads.googleads.v16.common.UserInterestInfo user_interest = 30
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Webpage.
    google.ads.googleads.v16.common.WebpageInfo webpage = 31
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Operating system version.
    google.ads.googleads.v16.common.OperatingSystemVersionInfo
        operating_system_version = 32 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Mobile Device.
    google.ads.googleads.v16.common.MobileDeviceInfo mobile_device = 33
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Location Group
    google.ads.googleads.v16.common.LocationGroupInfo location_group = 34
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Custom Affinity.
    google.ads.googleads.v16.common.CustomAffinityInfo custom_affinity = 36
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Custom Audience
    google.ads.googleads.v16.common.CustomAudienceInfo custom_audience = 41
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Combined Audience.
    google.ads.googleads.v16.common.CombinedAudienceInfo combined_audience = 42
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Smart Campaign Keyword Theme.
    google.ads.googleads.v16.common.KeywordThemeInfo keyword_theme = 45
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. GLS service campaign criterion.
    google.ads.googleads.v16.common.LocalServiceIdInfo local_service_id = 46
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Brand list campaign criterion.
    google.ads.googleads.v16.common.BrandListInfo brand_list = 47
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
