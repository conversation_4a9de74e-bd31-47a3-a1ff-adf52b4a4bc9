// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/criterion_category_availability.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "DetailedDemographicProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Detailed Demographic resource.

// A detailed demographic: a particular interest-based vertical to be targeted
// to reach users based on long-term life facts.
message DetailedDemographic {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/DetailedDemographic"
    pattern: "customers/{customer_id}/detailedDemographics/{detailed_demographic_id}"
  };

  // Output only. The resource name of the detailed demographic.
  // Detailed demographic resource names have the form:
  //
  // `customers/{customer_id}/detailedDemographics/{detailed_demographic_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/DetailedDemographic"
    }
  ];

  // Output only. The ID of the detailed demographic.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the detailed demographic. For example,"Highest
  // Level of Educational Attainment"
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The parent of the detailed_demographic.
  string parent = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/DetailedDemographic"
    }
  ];

  // Output only. True if the detailed demographic is launched to all channels
  // and locales.
  bool launched_to_all = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Availability information of the detailed demographic.
  repeated google.ads.googleads.v16.common.CriterionCategoryAvailability
      availabilities = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}
