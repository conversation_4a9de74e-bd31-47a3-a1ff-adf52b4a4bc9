// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/media_type.proto";
import "google/ads/googleads/v16/enums/mime_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "MediaFileProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the media file resource.

// A media file.
message MediaFile {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/MediaFile"
    pattern: "customers/{customer_id}/mediaFiles/{media_file_id}"
  };

  // Immutable. The resource name of the media file.
  // Media file resource names have the form:
  //
  // `customers/{customer_id}/mediaFiles/{media_file_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/MediaFile"
    }
  ];

  // Output only. The ID of the media file.
  optional int64 id = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Type of the media file.
  google.ads.googleads.v16.enums.MediaTypeEnum.MediaType type = 5
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The mime type of the media file.
  google.ads.googleads.v16.enums.MimeTypeEnum.MimeType mime_type = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The URL of where the original media file was downloaded from (or
  // a file name). Only used for media of type AUDIO and IMAGE.
  optional string source_url = 13 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The name of the media file. The name can be used by clients to
  // help identify previously uploaded media.
  optional string name = 14 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The size of the media file in bytes.
  optional int64 file_size = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The specific type of the media file.
  oneof mediatype {
    // Immutable. Encapsulates an Image.
    MediaImage image = 3 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. A ZIP archive media the content of which contains HTML5
    // assets.
    MediaBundle media_bundle = 4 [(google.api.field_behavior) = IMMUTABLE];

    // Output only. Encapsulates an Audio.
    MediaAudio audio = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. Encapsulates a Video.
    MediaVideo video = 11 [(google.api.field_behavior) = IMMUTABLE];
  }
}

// Encapsulates an Image.
message MediaImage {
  // Immutable. Raw image data.
  optional bytes data = 4 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The url to the full size version of the image.
  optional string full_size_image_url = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The url to the preview size version of the image.
  optional string preview_size_image_url = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents a ZIP archive media the content of which contains HTML5 assets.
message MediaBundle {
  // Immutable. Raw zipped data.
  optional bytes data = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The url to access the uploaded zipped data.
  // For example, https://tpc.googlesyndication.com/simgad/123
  // This field is read-only.
  optional string url = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Encapsulates an Audio.
message MediaAudio {
  // Output only. The duration of the Audio in milliseconds.
  optional int64 ad_duration_millis = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Encapsulates a Video.
message MediaVideo {
  // Output only. The duration of the Video in milliseconds.
  optional int64 ad_duration_millis = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The YouTube video ID (as seen in YouTube URLs). Adding prefix
  // "https://www.youtube.com/watch?v=" to this ID will get the YouTube
  // streaming URL for this video.
  optional string youtube_video_id = 6
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The Advertising Digital Identification code for this video, as
  // defined by the American Association of Advertising Agencies, used mainly
  // for television commercials.
  optional string advertising_id_code = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Industry Standard Commercial Identifier code for this
  // video, used mainly for television commercials.
  optional string isci_code = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
