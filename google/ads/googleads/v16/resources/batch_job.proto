// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/batch_job_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "BatchJobProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the batch job resource.

// A list of mutates being processed asynchronously. The mutates are uploaded
// by the user. The mutates themselves aren't readable and the results of the
// job can only be read using BatchJobService.ListBatchJobResults.
message BatchJob {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/BatchJob"
    pattern: "customers/{customer_id}/batchJobs/{batch_job_id}"
  };

  // Additional information about the batch job. This message is also used as
  // metadata returned in batch job Long Running Operations.
  message BatchJobMetadata {
    // Output only. The time when this batch job was created.
    // Formatted as yyyy-mm-dd hh:mm:ss. Example: "2018-03-05 09:15:00"
    optional string creation_date_time = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when this batch job started running.
    // Formatted as yyyy-mm-dd hh:mm:ss. Example: "2018-03-05 09:15:30"
    optional string start_date_time = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when this batch job was completed.
    // Formatted as yyyy-MM-dd HH:mm:ss. Example: "2018-03-05 09:16:00"
    optional string completion_date_time = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The fraction (between 0.0 and 1.0) of mutates that have been
    // processed. This is empty if the job hasn't started running yet.
    optional double estimated_completion_ratio = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of mutate operations in the batch job.
    optional int64 operation_count = 11
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of mutate operations executed by the batch job.
    // Present only if the job has started running.
    optional int64 executed_operation_count = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. The approximate upper bound for how long a batch job can be
    // executed, in seconds. If the job runs more than the given upper bound,
    // the job will be canceled.
    optional int32 execution_limit_seconds = 13
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // Immutable. The resource name of the batch job.
  // Batch job resource names have the form:
  //
  // `customers/{customer_id}/batchJobs/{batch_job_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BatchJob"
    }
  ];

  // Output only. ID of this batch job.
  optional int64 id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The next sequence token to use when adding operations. Only
  // set when the batch job status is PENDING.
  optional string next_add_sequence_token = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Contains additional information about this batch job.
  BatchJobMetadata metadata = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of this batch job.
  google.ads.googleads.v16.enums.BatchJobStatusEnum.BatchJobStatus status = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the long-running operation that can be
  // used to poll for completion. Only set when the batch job status is RUNNING
  // or DONE.
  optional string long_running_operation = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
