// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/ad_customizer_placeholder_field.proto";
import "google/ads/googleads/v16/enums/affiliate_location_placeholder_field.proto";
import "google/ads/googleads/v16/enums/app_placeholder_field.proto";
import "google/ads/googleads/v16/enums/call_placeholder_field.proto";
import "google/ads/googleads/v16/enums/callout_placeholder_field.proto";
import "google/ads/googleads/v16/enums/custom_placeholder_field.proto";
import "google/ads/googleads/v16/enums/dsa_page_feed_criterion_field.proto";
import "google/ads/googleads/v16/enums/education_placeholder_field.proto";
import "google/ads/googleads/v16/enums/feed_mapping_criterion_type.proto";
import "google/ads/googleads/v16/enums/feed_mapping_status.proto";
import "google/ads/googleads/v16/enums/flight_placeholder_field.proto";
import "google/ads/googleads/v16/enums/hotel_placeholder_field.proto";
import "google/ads/googleads/v16/enums/image_placeholder_field.proto";
import "google/ads/googleads/v16/enums/job_placeholder_field.proto";
import "google/ads/googleads/v16/enums/local_placeholder_field.proto";
import "google/ads/googleads/v16/enums/location_extension_targeting_criterion_field.proto";
import "google/ads/googleads/v16/enums/location_placeholder_field.proto";
import "google/ads/googleads/v16/enums/message_placeholder_field.proto";
import "google/ads/googleads/v16/enums/placeholder_type.proto";
import "google/ads/googleads/v16/enums/price_placeholder_field.proto";
import "google/ads/googleads/v16/enums/promotion_placeholder_field.proto";
import "google/ads/googleads/v16/enums/real_estate_placeholder_field.proto";
import "google/ads/googleads/v16/enums/sitelink_placeholder_field.proto";
import "google/ads/googleads/v16/enums/structured_snippet_placeholder_field.proto";
import "google/ads/googleads/v16/enums/travel_placeholder_field.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "FeedMappingProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the FeedMapping resource.

// A feed mapping.
message FeedMapping {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/FeedMapping"
    pattern: "customers/{customer_id}/feedMappings/{feed_id}~{feed_mapping_id}"
  };

  // Immutable. The resource name of the feed mapping.
  // Feed mapping resource names have the form:
  //
  // `customers/{customer_id}/feedMappings/{feed_id}~{feed_mapping_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/FeedMapping"
    }
  ];

  // Immutable. The feed of this feed mapping.
  optional string feed = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Feed" }
  ];

  // Immutable. Feed attributes to field mappings. These mappings are a
  // one-to-many relationship meaning that 1 feed attribute can be used to
  // populate multiple placeholder fields, but 1 placeholder field can only draw
  // data from 1 feed attribute. Ad Customizer is an exception, 1 placeholder
  // field can be mapped to multiple feed attributes. Required.
  repeated AttributeFieldMapping attribute_field_mappings = 5
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Status of the feed mapping.
  // This field is read-only.
  google.ads.googleads.v16.enums.FeedMappingStatusEnum.FeedMappingStatus
      status = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Feed mapping target. Can be either a placeholder or a criterion. For a
  // given feed, the active FeedMappings must have unique targets. Required.
  oneof target {
    // Immutable. The placeholder type of this mapping (for example, if the
    // mapping maps feed attributes to placeholder fields).
    google.ads.googleads.v16.enums.PlaceholderTypeEnum.PlaceholderType
        placeholder_type = 3 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The criterion type of this mapping (for example, if the
    // mapping maps feed attributes to criterion fields).
    google.ads.googleads.v16.enums.FeedMappingCriterionTypeEnum
        .FeedMappingCriterionType criterion_type = 4
        [(google.api.field_behavior) = IMMUTABLE];
  }
}

// Maps from feed attribute id to a placeholder or criterion field id.
message AttributeFieldMapping {
  // Immutable. Feed attribute from which to map.
  optional int64 feed_attribute_id = 24
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The placeholder field ID. If a placeholder field enum is not
  // published in the current API version, then this field will be populated and
  // the field oneof will be empty. This field is read-only.
  optional int64 field_id = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Placeholder or criterion field to be populated using data from
  // the above feed attribute. Required.
  oneof field {
    // Immutable. Sitelink Placeholder Fields.
    google.ads.googleads.v16.enums.SitelinkPlaceholderFieldEnum
        .SitelinkPlaceholderField sitelink_field = 3
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Call Placeholder Fields.
    google.ads.googleads.v16.enums.CallPlaceholderFieldEnum.CallPlaceholderField
        call_field = 4 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. App Placeholder Fields.
    google.ads.googleads.v16.enums.AppPlaceholderFieldEnum.AppPlaceholderField
        app_field = 5 [(google.api.field_behavior) = IMMUTABLE];

    // Output only. Location Placeholder Fields. This field is read-only.
    google.ads.googleads.v16.enums.LocationPlaceholderFieldEnum
        .LocationPlaceholderField location_field = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Affiliate Location Placeholder Fields. This field is
    // read-only.
    google.ads.googleads.v16.enums.AffiliateLocationPlaceholderFieldEnum
        .AffiliateLocationPlaceholderField affiliate_location_field = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. Callout Placeholder Fields.
    google.ads.googleads.v16.enums.CalloutPlaceholderFieldEnum
        .CalloutPlaceholderField callout_field = 8
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Structured Snippet Placeholder Fields.
    google.ads.googleads.v16.enums.StructuredSnippetPlaceholderFieldEnum
        .StructuredSnippetPlaceholderField structured_snippet_field = 9
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Message Placeholder Fields.
    google.ads.googleads.v16.enums.MessagePlaceholderFieldEnum
        .MessagePlaceholderField message_field = 10
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Price Placeholder Fields.
    google.ads.googleads.v16.enums.PricePlaceholderFieldEnum
        .PricePlaceholderField price_field = 11
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Promotion Placeholder Fields.
    google.ads.googleads.v16.enums.PromotionPlaceholderFieldEnum
        .PromotionPlaceholderField promotion_field = 12
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Ad Customizer Placeholder Fields
    google.ads.googleads.v16.enums.AdCustomizerPlaceholderFieldEnum
        .AdCustomizerPlaceholderField ad_customizer_field = 13
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Dynamic Search Ad Page Feed Fields.
    google.ads.googleads.v16.enums.DsaPageFeedCriterionFieldEnum
        .DsaPageFeedCriterionField dsa_page_feed_field = 14
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Location Target Fields.
    google.ads.googleads.v16.enums.LocationExtensionTargetingCriterionFieldEnum
        .LocationExtensionTargetingCriterionField
            location_extension_targeting_field = 15
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Education Placeholder Fields
    google.ads.googleads.v16.enums.EducationPlaceholderFieldEnum
        .EducationPlaceholderField education_field = 16
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Flight Placeholder Fields
    google.ads.googleads.v16.enums.FlightPlaceholderFieldEnum
        .FlightPlaceholderField flight_field = 17
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Custom Placeholder Fields
    google.ads.googleads.v16.enums.CustomPlaceholderFieldEnum
        .CustomPlaceholderField custom_field = 18
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Hotel Placeholder Fields
    google.ads.googleads.v16.enums.HotelPlaceholderFieldEnum
        .HotelPlaceholderField hotel_field = 19
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Real Estate Placeholder Fields
    google.ads.googleads.v16.enums.RealEstatePlaceholderFieldEnum
        .RealEstatePlaceholderField real_estate_field = 20
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Travel Placeholder Fields
    google.ads.googleads.v16.enums.TravelPlaceholderFieldEnum
        .TravelPlaceholderField travel_field = 21
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Local Placeholder Fields
    google.ads.googleads.v16.enums.LocalPlaceholderFieldEnum
        .LocalPlaceholderField local_field = 22
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Job Placeholder Fields
    google.ads.googleads.v16.enums.JobPlaceholderFieldEnum.JobPlaceholderField
        job_field = 23 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Image Placeholder Fields
    google.ads.googleads.v16.enums.ImagePlaceholderFieldEnum
        .ImagePlaceholderField image_field = 26
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
