// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/feed_item_set_filter_type_infos.proto";
import "google/ads/googleads/v16/enums/feed_item_set_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "FeedItemSetProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Represents a set of feed items. The set can be used and shared among certain
// feed item features. For instance, the set can be referenced within the
// matching functions of CustomerFeed, CampaignFeed, and AdGroupFeed.
message FeedItemSet {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/FeedItemSet"
    pattern: "customers/{customer_id}/feedItemSets/{feed_id}~{feed_item_set_id}"
  };

  // Immutable. The resource name of the feed item set.
  // Feed item set resource names have the form:
  // `customers/{customer_id}/feedItemSets/{feed_id}~{feed_item_set_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/FeedItemSet"
    }
  ];

  // Immutable. The resource name of the feed containing the feed items in the
  // set. Immutable. Required.
  string feed = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Feed" }
  ];

  // Output only. ID of the set.
  int64 feed_item_set_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Name of the set. Must be unique within the account.
  string display_name = 4;

  // Output only. Status of the feed item set.
  // This field is read-only.
  google.ads.googleads.v16.enums.FeedItemSetStatusEnum.FeedItemSetStatus
      status = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Represents a filter on locations in a feed item set.
  // Only applicable if the parent Feed of the FeedItemSet is a LOCATION feed.
  oneof dynamic_set_filter {
    // Filter for dynamic location set.
    // It is only used for sets of locations.
    google.ads.googleads.v16.common.DynamicLocationSetFilter
        dynamic_location_set_filter = 5;

    // Filter for dynamic affiliate location set.
    // This field doesn't apply generally to feed item sets. It is only used for
    // sets of affiliate locations.
    google.ads.googleads.v16.common.DynamicAffiliateLocationSetFilter
        dynamic_affiliate_location_set_filter = 6;
  }
}
