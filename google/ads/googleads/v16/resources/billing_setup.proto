// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/billing_setup_status.proto";
import "google/ads/googleads/v16/enums/time_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "BillingSetupProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the BillingSetup resource.

// A billing setup, which associates a payments account and an advertiser. A
// billing setup is specific to one advertiser.
message BillingSetup {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/BillingSetup"
    pattern: "customers/{customer_id}/billingSetups/{billing_setup_id}"
  };

  // Container of payments account information for this billing.
  message PaymentsAccountInfo {
    // Output only. A 16 digit id used to identify the payments account
    // associated with the billing setup.
    //
    // This must be passed as a string with dashes, for example,
    // "1234-5678-9012-3456".
    optional string payments_account_id = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. The name of the payments account associated with the billing
    // setup.
    //
    // This enables the user to specify a meaningful name for a payments account
    // to aid in reconciling monthly invoices.
    //
    // This name will be printed in the monthly invoices.
    optional string payments_account_name = 7
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. A 12 digit id used to identify the payments profile associated
    // with the billing setup.
    //
    // This must be passed in as a string with dashes, for example,
    // "1234-5678-9012".
    optional string payments_profile_id = 8
        [(google.api.field_behavior) = IMMUTABLE];

    // Output only. The name of the payments profile associated with the billing
    // setup.
    optional string payments_profile_name = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A secondary payments profile id present in uncommon
    // situations, for example, when a sequential liability agreement has been
    // arranged.
    optional string secondary_payments_profile_id = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. The resource name of the billing setup.
  // BillingSetup resource names have the form:
  //
  // `customers/{customer_id}/billingSetups/{billing_setup_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BillingSetup"
    }
  ];

  // Output only. The ID of the billing setup.
  optional int64 id = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the billing setup.
  google.ads.googleads.v16.enums.BillingSetupStatusEnum.BillingSetupStatus
      status = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The resource name of the payments account associated with this
  // billing setup. Payments resource names have the form:
  //
  // `customers/{customer_id}/paymentsAccounts/{payments_account_id}`
  // When setting up billing, this is used to signup with an existing payments
  // account (and then payments_account_info should not be set).
  // When getting a billing setup, this and payments_account_info will be
  // populated.
  optional string payments_account = 18 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/PaymentsAccount"
    }
  ];

  // Immutable. The payments account information associated with this billing
  // setup. When setting up billing, this is used to signup with a new payments
  // account (and then payments_account should not be set). When getting a
  // billing setup, this and payments_account will be populated.
  PaymentsAccountInfo payments_account_info = 12
      [(google.api.field_behavior) = IMMUTABLE];

  // When creating a new billing setup, this is when the setup should take
  // effect. NOW is the only acceptable start time if the customer doesn't have
  // any approved setups.
  //
  // When fetching an existing billing setup, this is the requested start time.
  // However, if the setup was approved (see status) after the requested start
  // time, then this is the approval time.
  oneof start_time {
    // Immutable. The start date time in yyyy-MM-dd or yyyy-MM-dd HH:mm:ss
    // format. Only a future time is allowed.
    string start_date_time = 16 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The start time as a type. Only NOW is allowed.
    google.ads.googleads.v16.enums.TimeTypeEnum.TimeType start_time_type = 10
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // When the billing setup ends / ended. This is either FOREVER or the start
  // time of the next scheduled billing setup.
  oneof end_time {
    // Output only. The end date time in yyyy-MM-dd or yyyy-MM-dd HH:mm:ss
    // format.
    string end_date_time = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The end time as a type.  The only possible value is FOREVER.
    google.ads.googleads.v16.enums.TimeTypeEnum.TimeType end_time_type = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
