// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/local_services_lead_credit_state.proto";
import "google/ads/googleads/v16/enums/local_services_lead_status.proto";
import "google/ads/googleads/v16/enums/local_services_lead_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesLeadProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the local services lead resource.

// Data from Local Services Lead.
// Contains details of Lead which is generated when user calls, messages or
// books service from advertiser.
// More info: https://ads.google.com/local-services-ads
message LocalServicesLead {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/LocalServicesLead"
    pattern: "customers/{customer_id}/localServicesLeads/{local_services_lead_id}"
  };

  // Output only. The resource name of the local services lead data.
  // Local Services Lead resource name have the form
  //
  // `customers/{customer_id}/localServicesLead/{local_services_lead_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LocalServicesLead"
    }
  ];

  // Output only. ID of this Lead.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Service category of the lead. For example:
  // `xcat:service_area_business_hvac`,
  // `xcat:service_area_business_real_estate_agent`, etc.
  // For more details see:
  // https://developers.google.com/google-ads/api/data/codes-formats#local_services_ids
  string category_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Service for the  category. For example: `buyer_agent`,
  // `seller_agent` for the category of
  // `xcat:service_area_business_real_estate_agent`.
  string service_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Lead's contact details.
  ContactDetails contact_details = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of Local Services lead: phone, message, booking, etc.
  google.ads.googleads.v16.enums.LocalServicesLeadTypeEnum.LeadType lead_type =
      6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current status of lead.
  google.ads.googleads.v16.enums.LocalServicesLeadStatusEnum.LeadStatus
      lead_status = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date time at which lead was created by Local Services Ads.
  // The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's timezone.
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  string creation_date_time = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Language used by the Local Services provider linked to lead.
  // See https://developers.google.com/google-ads/api/data/codes-formats#locales
  string locale = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Note added by advertiser for the lead.
  optional Note note = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if the advertiser was charged for the lead.
  bool lead_charged = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Credit details of the lead.
  optional CreditDetails credit_details = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Fields containing consumer contact details.
message ContactDetails {
  // Output only. Consumer phone number in E164 format.
  string phone_number = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Consumer email address.
  string email = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Consumer name if consumer provided name from Message or
  // Booking form on google.com
  string consumer_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents a note added to a lead by the advertiser. Advertisers can edit
// notes, which will reset edit time and change description.
message Note {
  // Output only. The date time when lead note was edited. The format is
  // "YYYY-MM-DD HH:MM:SS" in the Google Ads account's timezone. Examples:
  // "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  string edit_date_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Content of lead note.
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the credit details of a lead.
message CreditDetails {
  // Output only. Credit state of the lead.
  google.ads.googleads.v16.enums.LocalServicesCreditStateEnum.CreditState
      credit_state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date time when the credit state of the lead was last
  // updated. The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's
  // timezone. Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  string credit_state_last_update_date_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
