// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/common/simulation.proto";
import "google/ads/googleads/v16/enums/simulation_modification_method.proto";
import "google/ads/googleads/v16/enums/simulation_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupCriterionSimulationProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the ad group criterion simulation resource.

// An ad group criterion simulation. Supported combinations of advertising
// channel type, criterion type, simulation type, and simulation modification
// method are detailed below respectively. Hotel AdGroupCriterion simulation
// operations starting in V5.
//
// 1. DISPLAY - KEYWORD - CPC_BID - UNIFORM
// 2. SEARCH - KEYWORD - CPC_BID - UNIFORM
// 3. SHOPPING - LISTING_GROUP - CPC_BID - UNIFORM
// 4. HOTEL - LISTING_GROUP - CPC_BID - UNIFORM
// 5. HOTEL - LISTING_GROUP - PERCENT_CPC_BID - UNIFORM
message AdGroupCriterionSimulation {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupCriterionSimulation"
    pattern: "customers/{customer_id}/adGroupCriterionSimulations/{ad_group_id}~{criterion_id}~{type}~{modification_method}~{start_date}~{end_date}"
  };

  // Output only. The resource name of the ad group criterion simulation.
  // Ad group criterion simulation resource names have the form:
  //
  // `customers/{customer_id}/adGroupCriterionSimulations/{ad_group_id}~{criterion_id}~{type}~{modification_method}~{start_date}~{end_date}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupCriterionSimulation"
    }
  ];

  // Output only. AdGroup ID of the simulation.
  optional int64 ad_group_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Criterion ID of the simulation.
  optional int64 criterion_id = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The field that the simulation modifies.
  google.ads.googleads.v16.enums.SimulationTypeEnum.SimulationType type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. How the simulation modifies the field.
  google.ads.googleads.v16.enums.SimulationModificationMethodEnum
      .SimulationModificationMethod modification_method = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. First day on which the simulation is based, in YYYY-MM-DD
  // format.
  optional string start_date = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last day on which the simulation is based, in YYYY-MM-DD
  // format.
  optional string end_date = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of simulation points.
  oneof point_list {
    // Output only. Simulation points if the simulation type is CPC_BID.
    google.ads.googleads.v16.common.CpcBidSimulationPointList
        cpc_bid_point_list = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Simulation points if the simulation type is PERCENT_CPC_BID.
    google.ads.googleads.v16.common.PercentCpcBidSimulationPointList
        percent_cpc_bid_point_list = 13
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
