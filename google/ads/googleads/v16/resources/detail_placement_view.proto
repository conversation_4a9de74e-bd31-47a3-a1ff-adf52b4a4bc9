// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/placement_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "DetailPlacementViewProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the detail placement view resource.

// A view with metrics aggregated by ad group and URL or YouTube video.
message DetailPlacementView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/DetailPlacementView"
    pattern: "customers/{customer_id}/detailPlacementViews/{ad_group_id}~{base64_placement}"
  };

  // Output only. The resource name of the detail placement view.
  // Detail placement view resource names have the form:
  //
  // `customers/{customer_id}/detailPlacementViews/{ad_group_id}~{base64_placement}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/DetailPlacementView"
    }
  ];

  // Output only. The automatic placement string at detail level, e. g. website
  // URL, mobile application ID, or a YouTube video ID.
  optional string placement = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name is URL name for websites, YouTube video name
  // for YouTube videos, and translated mobile app name for mobile apps.
  optional string display_name = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL of the group placement, for example, domain, link to the
  // mobile application in app store, or a YouTube channel URL.
  optional string group_placement_target_url = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL of the placement, for example, website, link to the mobile
  // application in app store, or a YouTube video URL.
  optional string target_url = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the placement, for example, Website, YouTube Video,
  // and Mobile Application.
  google.ads.googleads.v16.enums.PlacementTypeEnum.PlacementType
      placement_type = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}
