// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdLabelProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the ad group ad label resource.

// A relationship between an ad group ad and a label.
message AdGroupAdLabel {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupAdLabel"
    pattern: "customers/{customer_id}/adGroupAdLabels/{ad_group_id}~{ad_id}~{label_id}"
  };

  // Immutable. The resource name of the ad group ad label.
  // Ad group ad label resource names have the form:
  // `customers/{customer_id}/adGroupAdLabels/{ad_group_id}~{ad_id}~{label_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAdLabel"
    }
  ];

  // Immutable. The ad group ad to which the label is attached.
  optional string ad_group_ad = 4 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAd"
    }
  ];

  // Immutable. The label assigned to the ad group ad.
  optional string label = 5 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Label" }
  ];
}
