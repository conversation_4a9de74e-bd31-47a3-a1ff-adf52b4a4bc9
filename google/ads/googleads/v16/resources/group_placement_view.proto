// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/placement_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "GroupPlacementViewProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the group placement view resource.

// A group placement view.
message GroupPlacementView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/GroupPlacementView"
    pattern: "customers/{customer_id}/groupPlacementViews/{ad_group_id}~{base64_placement}"
  };

  // Output only. The resource name of the group placement view.
  // Group placement view resource names have the form:
  //
  // `customers/{customer_id}/groupPlacementViews/{ad_group_id}~{base64_placement}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/GroupPlacementView"
    }
  ];

  // Output only. The automatic placement string at group level, e. g. web
  // domain, mobile app ID, or a YouTube channel ID.
  optional string placement = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Domain name for websites and YouTube channel name for YouTube
  // channels.
  optional string display_name = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL of the group placement, for example, domain, link to the
  // mobile application in app store, or a YouTube channel URL.
  optional string target_url = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the placement, for example, Website, YouTube Channel,
  // Mobile Application.
  google.ads.googleads.v16.enums.PlacementTypeEnum.PlacementType
      placement_type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
