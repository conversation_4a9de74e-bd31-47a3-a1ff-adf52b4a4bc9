// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/conversion_action_category.proto";
import "google/ads/googleads/v16/enums/conversion_value_rule_set_status.proto";
import "google/ads/googleads/v16/enums/value_rule_set_attachment_type.proto";
import "google/ads/googleads/v16/enums/value_rule_set_dimension.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ConversionValueRuleSetProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Conversion Value Rule Set resource.

// A conversion value rule set
message ConversionValueRuleSet {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ConversionValueRuleSet"
    pattern: "customers/{customer_id}/conversionValueRuleSets/{conversion_value_rule_set_id}"
  };

  // Immutable. The resource name of the conversion value rule set.
  // Conversion value rule set resource names have the form:
  //
  // `customers/{customer_id}/conversionValueRuleSets/{conversion_value_rule_set_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ConversionValueRuleSet"
    }
  ];

  // Output only. The ID of the conversion value rule set.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource names of rules within the rule set.
  repeated string conversion_value_rules = 3
      [(google.api.resource_reference) = {
        type: "googleads.googleapis.com/ConversionValueRule"
      }];

  // Defines dimensions for Value Rule conditions. The condition types of value
  // rules within this value rule set must be of these dimensions. The first
  // entry in this list is the primary dimension of the included value rules.
  // When using value rule primary dimension segmentation, conversion values
  // will be segmented into the values adjusted by value rules and the original
  // values, if some value rules apply.
  repeated google.ads.googleads.v16.enums.ValueRuleSetDimensionEnum
      .ValueRuleSetDimension dimensions = 4;

  // Output only. The resource name of the conversion value rule set's owner
  // customer. When the value rule set is inherited from a manager customer,
  // owner_customer will be the resource name of the manager whereas the
  // customer in the resource_name will be of the requesting serving customer.
  // ** Read-only **
  string owner_customer = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];

  // Immutable. Defines the scope where the conversion value rule set is
  // attached.
  google.ads.googleads.v16.enums.ValueRuleSetAttachmentTypeEnum
      .ValueRuleSetAttachmentType attachment_type = 6
      [(google.api.field_behavior) = IMMUTABLE];

  // The resource name of the campaign when the conversion value rule
  // set is attached to a campaign.
  string campaign = 7 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/Campaign"
  }];

  // Output only. The status of the conversion value rule set.
  // ** Read-only **
  google.ads.googleads.v16.enums.ConversionValueRuleSetStatusEnum
      .ConversionValueRuleSetStatus status = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The conversion action categories of the conversion value rule
  // set.
  repeated google.ads.googleads.v16.enums.ConversionActionCategoryEnum
      .ConversionActionCategory conversion_action_categories = 9
      [(google.api.field_behavior) = IMMUTABLE];
}
