// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/lead_form_field_user_input_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "LeadFormSubmissionDataProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the lead form submission data resource.

// Data from lead form submissions.
message LeadFormSubmissionData {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/LeadFormSubmissionData"
    pattern: "customers/{customer_id}/leadFormSubmissionData/{lead_form_user_submission_id}"
  };

  // Output only. The resource name of the lead form submission data.
  // Lead form submission data resource names have the form:
  //
  // `customers/{customer_id}/leadFormSubmissionData/{lead_form_submission_data_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LeadFormSubmissionData"
    }
  ];

  // Output only. ID of this lead form submission.
  string id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Asset associated with the submitted lead form.
  string asset = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // Output only. Campaign associated with the submitted lead form.
  string campaign = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. Submission data associated with a lead form.
  repeated LeadFormSubmissionField lead_form_submission_fields = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Submission data associated with a custom lead form.
  repeated CustomLeadFormSubmissionField custom_lead_form_submission_fields = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. AdGroup associated with the submitted lead form.
  string ad_group = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. AdGroupAd associated with the submitted lead form.
  string ad_group_ad = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAd"
    }
  ];

  // Output only. Google Click Id associated with the submissed lead form.
  string gclid = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date and time at which the lead form was submitted. The
  // format is "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01
  // 12:32:45-08:00".
  string submission_date_time = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Fields in the submitted lead form.
message LeadFormSubmissionField {
  // Output only. Field type for lead form fields.
  google.ads.googleads.v16.enums.LeadFormFieldUserInputTypeEnum
      .LeadFormFieldUserInputType field_type = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Field value for lead form fields.
  string field_value = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Fields in the submitted custom question
message CustomLeadFormSubmissionField {
  // Output only. Question text for custom question, maximum number of
  // characters is 300.
  string question_text = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Field value for custom question response, maximum number of
  // characters is 70.
  string field_value = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
