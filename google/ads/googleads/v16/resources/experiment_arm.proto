// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ExperimentArmProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the Experiment arm resource.

// A Google ads experiment for users to experiment changes on multiple
// campaigns, compare the performance, and apply the effective changes.
message ExperimentArm {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ExperimentArm"
    pattern: "customers/{customer_id}/experimentArms/{trial_id}~{trial_arm_id}"
  };

  // Immutable. The resource name of the experiment arm.
  // Experiment arm resource names have the form:
  //
  // `customers/{customer_id}/experimentArms/{TrialArm.trial_id}~{TrialArm.trial_arm_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ExperimentArm"
    }
  ];

  // Immutable. The experiment to which the ExperimentArm belongs.
  string experiment = 8 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];

  // Required. The name of the experiment arm. It must have a minimum length of
  // 1 and maximum length of 1024. It must be unique under an experiment.
  string name = 3 [(google.api.field_behavior) = REQUIRED];

  // Whether this arm is a control arm. A control arm is the arm against
  // which the other arms are compared.
  bool control = 4;

  // Traffic split of the trial arm. The value should be between 1 and 100
  // and must total 100 between the two trial arms.
  int64 traffic_split = 5;

  // List of campaigns in the trial arm. The max length is one.
  repeated string campaigns = 6 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/Campaign"
  }];

  // Output only. The in design campaigns in the treatment experiment arm.
  repeated string in_design_campaigns = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];
}
