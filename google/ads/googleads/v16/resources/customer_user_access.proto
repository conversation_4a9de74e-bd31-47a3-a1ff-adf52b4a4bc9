// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/access_role.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerUserAccessProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the CustomerUserAccess resource.

// Represents the permission of a single user onto a single customer.
message CustomerUserAccess {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerUserAccess"
    pattern: "customers/{customer_id}/customerUserAccesses/{user_id}"
  };

  // Immutable. Name of the resource.
  // Resource names have the form:
  // `customers/{customer_id}/customerUserAccesses/{user_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerUserAccess"
    }
  ];

  // Output only. User id of the user with the customer access.
  // Read only field
  int64 user_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the user.
  // Read only field
  optional string email_address = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Access role of the user.
  google.ads.googleads.v16.enums.AccessRoleEnum.AccessRole access_role = 4;

  // Output only. The customer user access creation time.
  // Read only field
  // The format is "YYYY-MM-DD HH:MM:SS".
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  optional string access_creation_date_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The email address of the inviter user.
  // Read only field
  optional string inviter_user_email_address = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
