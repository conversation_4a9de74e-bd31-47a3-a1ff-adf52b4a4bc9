// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/common/criteria.proto";
import "google/ads/googleads/v16/common/extensions.proto";
import "google/ads/googleads/v16/enums/ad_group_type.proto";
import "google/ads/googleads/v16/enums/advertising_channel_type.proto";
import "google/ads/googleads/v16/enums/bidding_strategy_type.proto";
import "google/ads/googleads/v16/enums/conversion_tracking_status_enum.proto";
import "google/ads/googleads/v16/enums/keyword_match_type.proto";
import "google/ads/googleads/v16/enums/recommendation_type.proto";
import "google/ads/googleads/v16/resources/ad.proto";
import "google/ads/googleads/v16/resources/asset.proto";
import "google/ads/googleads/v16/resources/recommendation.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "RecommendationServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the Recommendation service.

// Service to manage recommendations.
service RecommendationService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Applies given recommendations with corresponding apply parameters.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RecommendationError]()
  //   [RequestError]()
  //   [UrlFieldError]()
  rpc ApplyRecommendation(ApplyRecommendationRequest)
      returns (ApplyRecommendationResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/recommendations:apply"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }

  // Dismisses given recommendations.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RecommendationError]()
  //   [RequestError]()
  rpc DismissRecommendation(DismissRecommendationRequest)
      returns (DismissRecommendationResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/recommendations:dismiss"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }

  // Generates Recommendations based off the requested recommendation_types.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RecommendationError]()
  //   [RequestError]()
  rpc GenerateRecommendations(GenerateRecommendationsRequest)
      returns (GenerateRecommendationsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/recommendations:generate"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,recommendation_types,advertising_channel_type";
  }
}

// Request message for
// [RecommendationService.ApplyRecommendation][google.ads.googleads.v16.services.RecommendationService.ApplyRecommendation].
message ApplyRecommendationRequest {
  // Required. The ID of the customer with the recommendation.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to apply recommendations.
  // If partial_failure=false all recommendations should be of the same type
  // There is a limit of 100 operations per request.
  repeated ApplyRecommendationOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, operations will be carried
  // out as a transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;
}

// Information about the operation to apply a recommendation and any parameters
// to customize it.
message ApplyRecommendationOperation {
  // Parameters to use when applying a campaign budget recommendation.
  message CampaignBudgetParameters {
    // New budget amount to set for target budget resource. This is a required
    // field.
    optional int64 new_budget_amount_micros = 2;
  }

  // Parameters to use when applying a forecasting set target roas
  // recommendation.
  message ForecastingSetTargetRoasParameters {
    // New target ROAS (revenue per unit of spend) to set for a campaign
    // resource.
    // The value is between 0.01 and 1000.0, inclusive.
    optional double target_roas = 1;

    // New campaign budget amount to set for a campaign resource.
    optional int64 campaign_budget_amount_micros = 2;
  }

  // Parameters to use when applying a text ad recommendation.
  message TextAdParameters {
    // New ad to add to recommended ad group. All necessary fields need to be
    // set in this message. This is a required field.
    google.ads.googleads.v16.resources.Ad ad = 1;
  }

  // Parameters to use when applying keyword recommendation.
  message KeywordParameters {
    // The ad group resource to add keyword to. This is a required field.
    optional string ad_group = 4;

    // The match type of the keyword. This is a required field.
    google.ads.googleads.v16.enums.KeywordMatchTypeEnum.KeywordMatchType
        match_type = 2;

    // Optional, CPC bid to set for the keyword. If not set, keyword will use
    // bid based on bidding strategy used by target ad group.
    optional int64 cpc_bid_micros = 5;
  }

  // Parameters to use when applying Target CPA recommendation.
  message TargetCpaOptInParameters {
    // Average CPA to use for Target CPA bidding strategy. This is a required
    // field.
    optional int64 target_cpa_micros = 3;

    // Optional, budget amount to set for the campaign.
    optional int64 new_campaign_budget_amount_micros = 4;
  }

  // Parameters to use when applying a Target ROAS opt-in recommendation.
  message TargetRoasOptInParameters {
    // Average ROAS (revenue per unit of spend) to use for Target ROAS bidding
    // strategy. The value is between 0.01 and 1000.0, inclusive. This is a
    // required field, unless new_campaign_budget_amount_micros is set.
    optional double target_roas = 1;

    // Optional, budget amount to set for the campaign.
    optional int64 new_campaign_budget_amount_micros = 2;
  }

  // Parameters to use when applying callout extension recommendation.
  message CalloutExtensionParameters {
    // Callout extensions to be added. This is a required field.
    repeated google.ads.googleads.v16.common.CalloutFeedItem
        callout_extensions = 1;
  }

  // Parameters to use when applying call extension recommendation.
  message CallExtensionParameters {
    // Call extensions to be added. This is a required field.
    repeated google.ads.googleads.v16.common.CallFeedItem call_extensions = 1;
  }

  // Parameters to use when applying sitelink recommendation.
  message SitelinkExtensionParameters {
    // Sitelinks to be added. This is a required field.
    repeated google.ads.googleads.v16.common.SitelinkFeedItem
        sitelink_extensions = 1;
  }

  // Parameters to use when applying callout asset recommendations.
  message CalloutAssetParameters {
    // Required. Callout assets to be added. This is a required field.
    AdAssetApplyParameters ad_asset_apply_parameters = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Parameters to use when applying call asset recommendations.
  message CallAssetParameters {
    // Required. Call assets to be added. This is a required field.
    AdAssetApplyParameters ad_asset_apply_parameters = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Parameters to use when applying sitelink asset recommendations.
  message SitelinkAssetParameters {
    // Required. Sitelink assets to be added. This is a required field.
    AdAssetApplyParameters ad_asset_apply_parameters = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Parameters to use when applying raise Target CPA recommendations.
  message RaiseTargetCpaParameters {
    // Required. Target to set CPA multiplier to. This is a required field.
    double target_cpa_multiplier = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Parameters to use when applying lower Target ROAS recommendations.
  message LowerTargetRoasParameters {
    // Required. Target to set ROAS multiplier to. This is a required field.
    double target_roas_multiplier = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Common parameters used when applying ad asset recommendations.
  message AdAssetApplyParameters {
    // Scope to apply the assets to.
    enum ApplyScope {
      // The apply scope has not been specified.
      UNSPECIFIED = 0;

      // Unknown.
      UNKNOWN = 1;

      // Apply at the customer scope.
      CUSTOMER = 2;

      // Apply at the campaign scope.
      CAMPAIGN = 3;
    }

    // The assets to create and attach to a scope. This may be combined with
    // existing_assets in the same call.
    repeated google.ads.googleads.v16.resources.Asset new_assets = 1;

    // The resource names of existing assets to attach to a scope. This may be
    // combined with new_assets in the same call.
    repeated string existing_assets = 2;

    // Required. The scope at which to apply the assets. Assets at the campaign
    // scope level will be applied to the campaign associated with the
    // recommendation. Assets at the customer scope will apply to the entire
    // account. Assets at the campaign scope will override any attached at the
    // customer scope.
    ApplyScope scope = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // Parameters to use when applying move unused budget recommendation.
  message MoveUnusedBudgetParameters {
    // Budget amount to move from excess budget to constrained budget. This is
    // a required field.
    optional int64 budget_micros_to_move = 2;
  }

  // Parameters to use when applying a responsive search ad asset
  // recommendation.
  message ResponsiveSearchAdAssetParameters {
    // Updated ad. The current ad's content will be replaced.
    google.ads.googleads.v16.resources.Ad updated_ad = 1;
  }

  // Parameters to use when applying a responsive search ad improve ad strength
  // recommendation.
  message ResponsiveSearchAdImproveAdStrengthParameters {
    // Updated ad. The current ad's content will be replaced.
    google.ads.googleads.v16.resources.Ad updated_ad = 1;
  }

  // Parameters to use when applying a responsive search ad recommendation.
  message ResponsiveSearchAdParameters {
    // Required. New ad to add to recommended ad group.
    google.ads.googleads.v16.resources.Ad ad = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Parameters to use when applying a raise target CPA bid too low
  // recommendation. The apply is asynchronous and can take minutes depending on
  // the number of ad groups there is in the related campaign..
  message RaiseTargetCpaBidTooLowParameters {
    // Required. A number greater than 1.0 indicating the factor by which to
    // increase the target CPA. This is a required field.
    double target_multiplier = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Parameters to use when applying a use broad match keyword recommendation.
  message UseBroadMatchKeywordParameters {
    // New budget amount to set for target budget resource.
    optional int64 new_budget_amount_micros = 1;
  }

  // Parameters to use when applying a set target CPA recommendation.
  message ForecastingSetTargetCpaParameters {
    // Average CPA to use for Target CPA bidding strategy.
    optional int64 target_cpa_micros = 1;

    // New campaign budget amount to set for a campaign resource.
    optional int64 campaign_budget_amount_micros = 2;
  }

  // Parameters to use when applying a lead form asset recommendation.
  message LeadFormAssetParameters {
    // Required. Lead form assets to be added. This is a required field.
    AdAssetApplyParameters ad_asset_apply_parameters = 1
        [(google.api.field_behavior) = REQUIRED];

    // If true, the "Submit Lead Form" goal will be set on the target campaign.
    // As a result, ads will be shown as lead form creative ads. If false,
    // the "Submit Lead Form" goal will not be set on the campaign and ads will
    // contain lead form assets.
    optional bool set_submit_lead_form_asset_campaign_goal = 2;
  }

  // The resource name of the recommendation to apply.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/Recommendation"
  }];

  // Parameters to use when applying the recommendation.
  oneof apply_parameters {
    // Optional parameters to use when applying a campaign budget
    // recommendation.
    CampaignBudgetParameters campaign_budget = 2;

    // Optional parameters to use when applying a text ad recommendation.
    TextAdParameters text_ad = 3;

    // Optional parameters to use when applying keyword recommendation.
    KeywordParameters keyword = 4;

    // Optional parameters to use when applying target CPA opt-in
    // recommendation.
    TargetCpaOptInParameters target_cpa_opt_in = 5;

    // Optional parameters to use when applying target ROAS opt-in
    // recommendation.
    TargetRoasOptInParameters target_roas_opt_in = 10;

    // Parameters to use when applying callout extension recommendation.
    CalloutExtensionParameters callout_extension = 6;

    // Parameters to use when applying call extension recommendation.
    CallExtensionParameters call_extension = 7;

    // Parameters to use when applying sitelink recommendation.
    SitelinkExtensionParameters sitelink_extension = 8;

    // Parameters to use when applying move unused budget recommendation.
    MoveUnusedBudgetParameters move_unused_budget = 9;

    // Parameters to use when applying a responsive search ad recommendation.
    ResponsiveSearchAdParameters responsive_search_ad = 11;

    // Parameters to use when applying a use broad match keyword recommendation.
    UseBroadMatchKeywordParameters use_broad_match_keyword = 12;

    // Parameters to use when applying a responsive search ad asset
    // recommendation.
    ResponsiveSearchAdAssetParameters responsive_search_ad_asset = 13;

    // Parameters to use when applying a responsive search ad improve ad
    // strength recommendation.
    ResponsiveSearchAdImproveAdStrengthParameters
        responsive_search_ad_improve_ad_strength = 14;

    // Parameters to use when applying a raise target CPA bid too low
    // recommendation. The apply is asynchronous and can take minutes depending
    // on the number of ad groups there is in the related campaign.
    RaiseTargetCpaBidTooLowParameters raise_target_cpa_bid_too_low = 15;

    // Parameters to use when applying a forecasting set target ROAS
    // recommendation.
    ForecastingSetTargetRoasParameters forecasting_set_target_roas = 16;

    // Parameters to use when applying callout asset recommendation.
    CalloutAssetParameters callout_asset = 17;

    // Parameters to use when applying call asset recommendation.
    CallAssetParameters call_asset = 18;

    // Parameters to use when applying sitelink asset recommendation.
    SitelinkAssetParameters sitelink_asset = 19;

    // Parameters to use when applying raise Target CPA recommendation.
    RaiseTargetCpaParameters raise_target_cpa = 20;

    // Parameters to use when applying lower Target ROAS recommendation.
    LowerTargetRoasParameters lower_target_roas = 21;

    // Parameters to use when applying forecasting set target CPA
    // recommendation.
    ForecastingSetTargetCpaParameters forecasting_set_target_cpa = 22;

    // Parameters to use when applying set target CPA
    // recommendation.
    ForecastingSetTargetCpaParameters set_target_cpa = 23;

    // Parameters to use when applying set target ROAS
    // recommendation.
    ForecastingSetTargetRoasParameters set_target_roas = 24;

    // Parameters to use when applying lead form asset recommendation.
    LeadFormAssetParameters lead_form_asset = 25;
  }
}

// Response message for
// [RecommendationService.ApplyRecommendation][google.ads.googleads.v16.services.RecommendationService.ApplyRecommendation].
message ApplyRecommendationResponse {
  // Results of operations to apply recommendations.
  repeated ApplyRecommendationResult results = 1;

  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors) we return the RPC level error.
  google.rpc.Status partial_failure_error = 2;
}

// The result of applying a recommendation.
message ApplyRecommendationResult {
  // Returned for successful applies.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/Recommendation"
  }];
}

// Request message for
// [RecommendationService.DismissRecommendation][google.ads.googleads.v16.services.RecommendationService.DismissRecommendation].
message DismissRecommendationRequest {
  // Operation to dismiss a single recommendation identified by resource_name.
  message DismissRecommendationOperation {
    // The resource name of the recommendation to dismiss.
    string resource_name = 1;
  }

  // Required. The ID of the customer with the recommendation.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to dismiss recommendations.
  // If partial_failure=false all recommendations should be of the same type
  // There is a limit of 100 operations per request.
  repeated DismissRecommendationOperation operations = 3
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, operations will be carried in a
  // single transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 2;
}

// Response message for
// [RecommendationService.DismissRecommendation][google.ads.googleads.v16.services.RecommendationService.DismissRecommendation].
message DismissRecommendationResponse {
  // The result of dismissing a recommendation.
  message DismissRecommendationResult {
    // Returned for successful dismissals.
    string resource_name = 1;
  }

  // Results of operations to dismiss recommendations.
  repeated DismissRecommendationResult results = 1;

  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors) we return the RPC level error.
  google.rpc.Status partial_failure_error = 2;
}

// Request message for
// [RecommendationService.GenerateRecommendations][google.ads.googleads.v16.services.RecommendationService.GenerateRecommendations].
message GenerateRecommendationsRequest {
  // Current bidding information of the campaign. Provides a wrapper for
  // bidding-related signals that inform recommendations.
  message BiddingInfo {
    // Current bidding strategy.
    // This field is necessary for the following recommendation_types:
    // MAXIMIZE_CLICKS_OPT_IN, MAXIMIZE_CONVERSIONS_OPT_IN,
    // MAXIMIZE_CONVERSION_VALUE_OPT_IN, SET_TARGET_CPA, SET_TARGET_ROAS,
    // TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN
    optional google.ads.googleads.v16.enums.BiddingStrategyTypeEnum
        .BiddingStrategyType bidding_strategy_type = 1;

    // Optional details related to the bidding_strategy_type.
    oneof bidding_strategy_target_info {
      // Current target_cpa in micros.
      // This can be populated for campaigns with a bidding strategy type of
      // TARGET_CPA or MAXIMIZE_CONVERSIONS.
      int64 target_cpa_micros = 2;

      // Current target_roas.
      // This can be populated for campaigns with a bidding strategy type of
      // TARGET_ROAS or MAXIMIZE_CONVERSION_VALUE.
      double target_roas = 3;
    }
  }

  // Current AdGroup Information of the campaign.
  message AdGroupInfo {
    // Optional. AdGroup Type of the AdGroup.
    // This field is necessary for the following recommendation_types if
    // ad_group_info is set:
    // KEYWORD
    optional google.ads.googleads.v16.enums.AdGroupTypeEnum.AdGroupType
        ad_group_type = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Current keywords.
    // This field is optional for the following recommendation_types if
    // ad_group_info is set:
    // KEYWORD
    repeated google.ads.googleads.v16.common.KeywordInfo keywords = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // A keyword seed and a specific url to generate keywords from.
  message SeedInfo {
    // A specific url to generate ideas from, for example: www.example.com/cars.
    optional string url_seed = 2;

    // Optional. Keywords or phrases to generate ideas from, for example: cars
    // or "car dealership near me".
    repeated string keyword_seeds = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The ID of the customer generating recommendations.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. List of eligible recommendation_types to generate. If the
  // uploaded criteria isn't sufficient to make a recommendation, or the
  // campaign is already in the recommended state, no recommendation will be
  // returned for that type. Generally, a recommendation is returned if all
  // required fields for that recommendation_type are uploaded, but there are
  // cases where this is still not sufficient.
  //
  // The following recommendation_types are supported for recommendation
  // generation:
  // KEYWORD, MAXIMIZE_CLICKS_OPT_IN, MAXIMIZE_CONVERSIONS_OPT_IN,
  // MAXIMIZE_CONVERSION_VALUE_OPT_IN, SET_TARGET_CPA, SET_TARGET_ROAS,
  // SITELINK_ASSET, TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN
  repeated
      google.ads.googleads.v16.enums.RecommendationTypeEnum.RecommendationType
          recommendation_types = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Advertising channel type of the campaign.
  // The following advertising_channel_types are supported for recommendation
  // generation:
  // PERFORMANCE_MAX and SEARCH
  google.ads.googleads.v16.enums.AdvertisingChannelTypeEnum
      .AdvertisingChannelType advertising_channel_type = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Number of sitelinks on the campaign.
  // This field is necessary for the following recommendation_types:
  // SITELINK_ASSET
  optional int32 campaign_sitelink_count = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Current conversion tracking status.
  // This field is necessary for the following recommendation_types:
  // MAXIMIZE_CLICKS_OPT_IN, MAXIMIZE_CONVERSIONS_OPT_IN,
  // MAXIMIZE_CONVERSION_VALUE_OPT_IN, SET_TARGET_CPA, SET_TARGET_ROAS,
  // TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN
  optional google.ads.googleads.v16.enums.ConversionTrackingStatusEnum
      .ConversionTrackingStatus conversion_tracking_status = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Current bidding information of the campaign.
  // This field is necessary for the following recommendation_types:
  // MAXIMIZE_CLICKS_OPT_IN, MAXIMIZE_CONVERSIONS_OPT_IN,
  // MAXIMIZE_CONVERSION_VALUE_OPT_IN, SET_TARGET_CPA, SET_TARGET_ROAS,
  // TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN
  optional BiddingInfo bidding_info = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Current AdGroup Information.
  // Supports information from a single AdGroup.
  // This field is optional for the following recommendation_types:
  // KEYWORD
  repeated AdGroupInfo ad_group_info = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Seed information for Keywords.
  // This field is necessary for the following recommendation_types:
  // KEYWORD
  optional SeedInfo seed_info = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [RecommendationService.GenerateRecommendations][google.ads.googleads.v16.services.RecommendationService.GenerateRecommendations].
message GenerateRecommendationsResponse {
  // List of generated recommendations from the passed in set of requested
  // recommendation_types. If there isn't sufficient data to generate a
  // recommendation for the requested recommendation_types, the result set won't
  // contain a recommendation for that type.
  repeated google.ads.googleads.v16.resources.Recommendation recommendations =
      1;
}
