// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/enums/response_content_type.proto";
import "google/ads/googleads/v16/resources/custom_conversion_goal.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomConversionGoalServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the CustomConversionGoal service.

// Service to manage custom conversion goal.
service CustomConversionGoalService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, updates or removes custom conversion goals. Operation statuses
  // are returned.
  rpc MutateCustomConversionGoals(MutateCustomConversionGoalsRequest)
      returns (MutateCustomConversionGoalsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/customConversionGoals:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [CustomConversionGoalService.MutateCustomConversionGoals][google.ads.googleads.v16.services.CustomConversionGoalService.MutateCustomConversionGoals].
message MutateCustomConversionGoalsRequest {
  // Required. The ID of the customer whose custom conversion goals are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual custom conversion
  // goal.
  repeated CustomConversionGoalOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v16.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 4;
}

// A single operation (create, remove) on a custom conversion goal.
message CustomConversionGoalOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new custom
    // conversion goal
    google.ads.googleads.v16.resources.CustomConversionGoal create = 1;

    // Update operation: The custom conversion goal is expected to have a
    // valid resource name.
    google.ads.googleads.v16.resources.CustomConversionGoal update = 2;

    // Remove operation: A resource name for the removed custom conversion goal
    // is expected, in this format:
    //
    // 'customers/{customer_id}/conversionActions/{ConversionGoal.custom_goal_config.conversion_type_ids}'
    string remove = 3 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomConversionGoal"
    }];
  }
}

// Response message for a custom conversion goal mutate.
message MutateCustomConversionGoalsResponse {
  // All results for the mutate.
  repeated MutateCustomConversionGoalResult results = 1;
}

// The result for the custom conversion goal mutate.
message MutateCustomConversionGoalResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomConversionGoal"
  }];

  // The mutated CustomConversionGoal with only mutable fields after mutate.
  // The field will only be returned when response_content_type is set to
  // "MUTABLE_RESOURCE".
  google.ads.googleads.v16.resources.CustomConversionGoal
      custom_conversion_goal = 2;
}
