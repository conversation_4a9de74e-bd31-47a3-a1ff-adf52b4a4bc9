// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/resources/asset_group.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the AssetGroup service.

// Service to manage asset group
service AssetGroupService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, updates or removes asset groups. Operation statuses are
  // returned.
  rpc MutateAssetGroups(MutateAssetGroupsRequest)
      returns (MutateAssetGroupsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/assetGroups:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [AssetGroupService.MutateAssetGroups][google.ads.googleads.v16.services.AssetGroupService.MutateAssetGroups].
message MutateAssetGroupsRequest {
  // Required. The ID of the customer whose asset groups are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual asset groups.
  repeated AssetGroupOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;
}

// A single operation (create, remove) on an asset group.
message AssetGroupOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new asset group
    google.ads.googleads.v16.resources.AssetGroup create = 1;

    // Update operation: The asset group is expected to have a valid resource
    // name.
    google.ads.googleads.v16.resources.AssetGroup update = 2;

    // Remove operation: A resource name for the removed asset group is
    // expected, in this format:
    // `customers/{customer_id}/assetGroups/{asset_group_id}`
    string remove = 3 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroup"
    }];
  }
}

// Response message for an asset group mutate.
message MutateAssetGroupsResponse {
  // All results for the mutate.
  repeated MutateAssetGroupResult results = 1;

  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 2;
}

// The result for the asset group mutate.
message MutateAssetGroupResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/AssetGroup"
  }];
}
