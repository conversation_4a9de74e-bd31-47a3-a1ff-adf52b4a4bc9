// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/resources/custom_audience.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomAudienceServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the Custom Audience service.

// Service to manage custom audiences.
service CustomAudienceService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates or updates custom audiences. Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CustomAudienceError]()
  //   [CustomInterestError]()
  //   [FieldError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [OperationAccessDeniedError]()
  //   [PolicyViolationError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateCustomAudiences(MutateCustomAudiencesRequest)
      returns (MutateCustomAudiencesResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/customAudiences:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [CustomAudienceService.MutateCustomAudiences][google.ads.googleads.v16.services.CustomAudienceService.MutateCustomAudiences].
message MutateCustomAudiencesRequest {
  // Required. The ID of the customer whose custom audiences are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual custom audiences.
  repeated CustomAudienceOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// A single operation (create, update) on a custom audience.
message CustomAudienceOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new custom
    // audience.
    google.ads.googleads.v16.resources.CustomAudience create = 1;

    // Update operation: The custom audience is expected to have a valid
    // resource name.
    google.ads.googleads.v16.resources.CustomAudience update = 2;

    // Remove operation: A resource name for the removed custom audience is
    // expected, in this format:
    //
    // `customers/{customer_id}/customAudiences/{custom_audience_id}`
    string remove = 3 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomAudience"
    }];
  }
}

// Response message for custom audience mutate.
message MutateCustomAudiencesResponse {
  // All results for the mutate.
  repeated MutateCustomAudienceResult results = 1;
}

// The result for the custom audience mutate.
message MutateCustomAudienceResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomAudience"
  }];
}
