# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

# TODO(ohren): Change srcs to use an enumeration of each individual proto
# instead of *.proto globbing once the build file generator supports
# subpackages.
proto_library(
    name = "services_proto",
    srcs = glob(["*.proto"]),
    deps = [
        "//google/ads/googleads/v16/common:common_proto",
        "//google/ads/googleads/v16/enums:enums_proto",
        "//google/ads/googleads/v16/errors:errors_proto",
        "//google/ads/googleads/v16/resources:resources_proto",
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "services_proto_with_info",
    deps = [
        ":services_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "services_java_proto",
    deps = [":services_proto"],
)

java_grpc_library(
    name = "services_java_grpc",
    srcs = [":services_proto"],
    deps = [":services_java_proto"],
)

##############################################################################
# PHP
##############################################################################

# PHP targets are in the parent directory's BUILD.bazel file to facilitate
# aggregating metadata using a single underlying call to protoc.

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "services_csharp_proto",
    deps = [":services_proto"],
)

csharp_grpc_library(
    name = "services_csharp_grpc",
    srcs = [":services_proto"],
    deps = [":services_csharp_proto"],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "services_ruby_proto",
    deps = [":services_proto"],
)

ruby_grpc_library(
    name = "services_ruby_grpc",
    srcs = [":services_proto"],
    deps = [":services_ruby_proto"],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_grpc_library",
    "py_proto_library",
)

py_proto_library(
    name = "services_py_proto",
    deps = [":services_proto"],
)

py_grpc_library(
    name = "services_py_grpc",
    srcs = [":services_proto"],
    deps = [":services_py_proto"],
)
