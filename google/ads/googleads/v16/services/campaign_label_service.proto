// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/resources/campaign_label.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "CampaignLabelServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the Campaign Label service.

// Service to manage labels on campaigns.
service CampaignLabelService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates and removes campaign-label relationships.
  // Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [LabelError]()
  //   [MutateError]()
  //   [NewResourceCreationError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateCampaignLabels(MutateCampaignLabelsRequest)
      returns (MutateCampaignLabelsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/campaignLabels:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [CampaignLabelService.MutateCampaignLabels][google.ads.googleads.v16.services.CampaignLabelService.MutateCampaignLabels].
message MutateCampaignLabelsRequest {
  // Required. ID of the customer whose campaign-label relationships are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on campaign-label
  // relationships.
  repeated CampaignLabelOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;
}

// A single operation (create, remove) on a campaign-label relationship.
message CampaignLabelOperation {
  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new campaign-label
    // relationship.
    google.ads.googleads.v16.resources.CampaignLabel create = 1;

    // Remove operation: A resource name for the campaign-label relationship
    // being removed, in this format:
    //
    // `customers/{customer_id}/campaignLabels/{campaign_id}~{label_id}`
    string remove = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignLabel"
    }];
  }
}

// Response message for a campaign labels mutate.
message MutateCampaignLabelsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 3;

  // All results for the mutate.
  repeated MutateCampaignLabelResult results = 2;
}

// The result for a campaign label mutate.
message MutateCampaignLabelResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CampaignLabel"
  }];
}
