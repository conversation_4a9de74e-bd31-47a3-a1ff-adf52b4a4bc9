// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/enums/response_content_type.proto";
import "google/ads/googleads/v16/resources/conversion_value_rule_set.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "ConversionValueRuleSetServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the Conversion Value Rule Set service.

// Service to manage conversion value rule sets.
service ConversionValueRuleSetService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, updates or removes conversion value rule sets. Operation statuses
  // are returned.
  rpc MutateConversionValueRuleSets(MutateConversionValueRuleSetsRequest)
      returns (MutateConversionValueRuleSetsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/conversionValueRuleSets:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [ConversionValueRuleSetService.MutateConversionValueRuleSets][google.ads.googleads.v16.services.ConversionValueRuleSetService.MutateConversionValueRuleSets].
message MutateConversionValueRuleSetsRequest {
  // Required. The ID of the customer whose conversion value rule sets are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual conversion value
  // rule sets.
  repeated ConversionValueRuleSetOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 5;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v16.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 4;
}

// A single operation (create, update, remove) on a conversion value rule set.
message ConversionValueRuleSetOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new conversion
    // value rule set.
    google.ads.googleads.v16.resources.ConversionValueRuleSet create = 1;

    // Update operation: The conversion value rule set is expected to have a
    // valid resource name.
    google.ads.googleads.v16.resources.ConversionValueRuleSet update = 2;

    // Remove operation: A resource name for the removed conversion value rule
    // set is expected, in this format:
    //
    // `customers/{customer_id}/conversionValueRuleSets/{conversion_value_rule_set_id}`
    string remove = 3 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/ConversionValueRuleSet"
    }];
  }
}

// Response message for
// [ConversionValueRuleSetService.MutateConversionValueRuleSets][google.ads.googleads.v16.services.ConversionValueRuleSetService.MutateConversionValueRuleSets].
message MutateConversionValueRuleSetsResponse {
  // All results for the mutate.
  repeated MutateConversionValueRuleSetResult results = 1;

  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 2;
}

// The result for the conversion value rule set mutate.
message MutateConversionValueRuleSetResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ConversionValueRuleSet"
  }];

  // The mutated conversion value rule set with only mutable fields after
  // mutate. The field will only be returned when response_content_type is set
  // to "MUTABLE_RESOURCE".
  google.ads.googleads.v16.resources.ConversionValueRuleSet
      conversion_value_rule_set = 2;
}
