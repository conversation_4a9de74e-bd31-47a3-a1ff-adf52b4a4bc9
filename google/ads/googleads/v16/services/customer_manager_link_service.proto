// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/resources/customer_manager_link.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerManagerLinkServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Service to manage customer-manager links.
service CustomerManagerLinkService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Updates customer manager links. Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [ManagerLinkError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateCustomerManagerLink(MutateCustomerManagerLinkRequest)
      returns (MutateCustomerManagerLinkResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/customerManagerLinks:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }

  // Moves a client customer to a new manager customer.
  // This simplifies the complex request that requires two operations to move
  // a client customer to a new manager, for example:
  // 1. Update operation with Status INACTIVE (previous manager) and,
  // 2. Update operation with Status ACTIVE (new manager).
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MoveManagerLink(MoveManagerLinkRequest)
      returns (MoveManagerLinkResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/customerManagerLinks:moveManagerLink"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,previous_customer_manager_link,new_manager";
  }
}

// Request message for
// [CustomerManagerLinkService.MutateCustomerManagerLink][google.ads.googleads.v16.services.CustomerManagerLinkService.MutateCustomerManagerLink].
message MutateCustomerManagerLinkRequest {
  // Required. The ID of the customer whose customer manager links are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual customer manager
  // links.
  repeated CustomerManagerLinkOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// Request message for
// [CustomerManagerLinkService.MoveManagerLink][google.ads.googleads.v16.services.CustomerManagerLinkService.MoveManagerLink].
message MoveManagerLinkRequest {
  // Required. The ID of the client customer that is being moved.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the previous CustomerManagerLink.
  // The resource name has the form:
  // `customers/{customer_id}/customerManagerLinks/{manager_customer_id}~{manager_link_id}`
  string previous_customer_manager_link = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the new manager customer that the client
  // wants to move to. Customer resource names have the format:
  // "customers/{customer_id}"
  string new_manager = 3 [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;
}

// Updates the status of a CustomerManagerLink.
// The following actions are possible:
// 1. Update operation with status ACTIVE accepts a pending invitation.
// 2. Update operation with status REFUSED declines a pending invitation.
// 3. Update operation with status INACTIVE terminates link to manager.
message CustomerManagerLinkOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The mutate operation.
  oneof operation {
    // Update operation: The link is expected to have a valid resource name.
    google.ads.googleads.v16.resources.CustomerManagerLink update = 2;
  }
}

// Response message for a CustomerManagerLink mutate.
message MutateCustomerManagerLinkResponse {
  // A result that identifies the resource affected by the mutate request.
  repeated MutateCustomerManagerLinkResult results = 1;
}

// Response message for a CustomerManagerLink moveManagerLink.
message MoveManagerLinkResponse {
  // Returned for successful operations. Represents a CustomerManagerLink
  // resource of the newly created link between client customer and new manager
  // customer.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomerManagerLink"
  }];
}

// The result for the customer manager link mutate.
message MutateCustomerManagerLinkResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomerManagerLink"
  }];
}
