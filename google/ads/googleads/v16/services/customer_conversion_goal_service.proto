// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/resources/customer_conversion_goal.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerConversionGoalServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the CustomerConversionGoal service.

// Service to manage customer conversion goal.
service CustomerConversionGoalService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, updates or removes customer conversion goals. Operation statuses
  // are returned.
  rpc MutateCustomerConversionGoals(MutateCustomerConversionGoalsRequest)
      returns (MutateCustomerConversionGoalsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/customerConversionGoals:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [CustomerConversionGoalService.MutateCustomerConversionGoals][google.ads.googleads.v16.services.CustomerConversionGoalService.MutateCustomerConversionGoals].
message MutateCustomerConversionGoalsRequest {
  // Required. The ID of the customer whose customer conversion goals are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual customer
  // conversion goal.
  repeated CustomerConversionGoalOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// A single operation (update) on a customer conversion goal.
message CustomerConversionGoalOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 2;

  // The mutate operation.
  oneof operation {
    // Update operation: The customer conversion goal is expected to have a
    // valid resource name.
    google.ads.googleads.v16.resources.CustomerConversionGoal update = 1;
  }
}

// Response message for a customer conversion goal mutate.
message MutateCustomerConversionGoalsResponse {
  // All results for the mutate.
  repeated MutateCustomerConversionGoalResult results = 1;
}

// The result for the customer conversion goal mutate.
message MutateCustomerConversionGoalResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomerConversionGoal"
  }];
}
