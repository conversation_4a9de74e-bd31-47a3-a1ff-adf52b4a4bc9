// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/resources/customer_user_access_invitation.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerUserAccessInvitationServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the CustomerUserAccessInvitation service.

// This service manages the access invitation extended to users for a given
// customer.
service CustomerUserAccessInvitationService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates or removes an access invitation.
  //
  // List of thrown errors:
  //   [AccessInvitationError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateCustomerUserAccessInvitation(
      MutateCustomerUserAccessInvitationRequest)
      returns (MutateCustomerUserAccessInvitationResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/customerUserAccessInvitations:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }
}

// Request message for
// [CustomerUserAccessInvitationService.MutateCustomerUserAccessInvitation][google.ads.googleads.v16.services.CustomerUserAccessInvitationService.MutateCustomerUserAccessInvitation]
message MutateCustomerUserAccessInvitationRequest {
  // Required. The ID of the customer whose access invitation is being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform on the access invitation
  CustomerUserAccessInvitationOperation operation = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A single operation (create or remove) on customer user access invitation.
message CustomerUserAccessInvitationOperation {
  // The mutate operation
  oneof operation {
    // Create operation: No resource name is expected for the new access
    // invitation.
    google.ads.googleads.v16.resources.CustomerUserAccessInvitation create = 1;

    // Remove operation: A resource name for the revoke invitation is
    // expected, in this format:
    //
    // `customers/{customer_id}/customerUserAccessInvitations/{invitation_id}`
    string remove = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerUserAccessInvitation"
    }];
  }
}

// Response message for access invitation mutate.
message MutateCustomerUserAccessInvitationResponse {
  // Result for the mutate.
  MutateCustomerUserAccessInvitationResult result = 1;
}

// The result for the access invitation mutate.
message MutateCustomerUserAccessInvitationResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomerUserAccessInvitation"
  }];
}
