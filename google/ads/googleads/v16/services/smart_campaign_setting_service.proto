// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/enums/response_content_type.proto";
import "google/ads/googleads/v16/enums/smart_campaign_not_eligible_reason.proto";
import "google/ads/googleads/v16/enums/smart_campaign_status.proto";
import "google/ads/googleads/v16/resources/smart_campaign_setting.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "SmartCampaignSettingServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the Smart campaign setting service.

// Service to manage Smart campaign settings.
service SmartCampaignSettingService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns the status of the requested Smart campaign.
  rpc GetSmartCampaignStatus(GetSmartCampaignStatusRequest)
      returns (GetSmartCampaignStatusResponse) {
    option (google.api.http) = {
      get: "/v16/{resource_name=customers/*/smartCampaignSettings/*}:getSmartCampaignStatus"
    };
    option (google.api.method_signature) = "resource_name";
  }

  // Updates Smart campaign settings for campaigns.
  rpc MutateSmartCampaignSettings(MutateSmartCampaignSettingsRequest)
      returns (MutateSmartCampaignSettingsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/smartCampaignSettings:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [SmartCampaignSettingService.GetSmartCampaignStatus][google.ads.googleads.v16.services.SmartCampaignSettingService.GetSmartCampaignStatus].
message GetSmartCampaignStatusRequest {
  // Required. The resource name of the Smart campaign setting belonging to the
  // Smart campaign to fetch the status of.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/SmartCampaignSetting"
    }
  ];
}

// Details related to Smart campaigns that are not eligible to serve.
message SmartCampaignNotEligibleDetails {
  // The reason why the Smart campaign is not eligible to serve.
  optional google.ads.googleads.v16.enums.SmartCampaignNotEligibleReasonEnum
      .SmartCampaignNotEligibleReason not_eligible_reason = 1;
}

// Details related to Smart campaigns that are eligible to serve.
message SmartCampaignEligibleDetails {
  // The timestamp of the last impression observed in the last 48 hours for this
  // campaign.
  // The timestamp is in the customer’s timezone and in
  // “yyyy-MM-dd HH:mm:ss” format.
  optional string last_impression_date_time = 1;

  // The timestamp of when the campaign will end, if applicable.
  // The timestamp is in the customer’s timezone and in
  // “yyyy-MM-dd HH:mm:ss” format.
  optional string end_date_time = 2;
}

// Details related to paused Smart campaigns.
message SmartCampaignPausedDetails {
  // The timestamp of when the campaign was last paused.
  // The timestamp is in the customer’s timezone and in
  // “yyyy-MM-dd HH:mm:ss” format.
  optional string paused_date_time = 1;
}

// Details related to removed Smart campaigns.
message SmartCampaignRemovedDetails {
  // The timestamp of when the campaign was removed.
  // The timestamp is in the customer’s timezone and in
  // “yyyy-MM-dd HH:mm:ss” format.
  optional string removed_date_time = 1;
}

// Details related to Smart campaigns that have ended.
message SmartCampaignEndedDetails {
  // The timestamp of when the campaign ended.
  // The timestamp is in the customer’s timezone and in
  // “yyyy-MM-dd HH:mm:ss” format.
  optional string end_date_time = 1;
}

// Response message for
// [SmartCampaignSettingService.GetSmartCampaignStatus][google.ads.googleads.v16.services.SmartCampaignSettingService.GetSmartCampaignStatus].
message GetSmartCampaignStatusResponse {
  // The status of this Smart campaign.
  google.ads.googleads.v16.enums.SmartCampaignStatusEnum.SmartCampaignStatus
      smart_campaign_status = 1;

  // Additional details accompanying the status of a Smart campaign.
  oneof smart_campaign_status_details {
    // Details related to Smart campaigns that are ineligible to serve.
    SmartCampaignNotEligibleDetails not_eligible_details = 2;

    // Details related to Smart campaigns that are eligible to serve.
    SmartCampaignEligibleDetails eligible_details = 3;

    // Details related to paused Smart campaigns.
    SmartCampaignPausedDetails paused_details = 4;

    // Details related to removed Smart campaigns.
    SmartCampaignRemovedDetails removed_details = 5;

    // Details related to Smart campaigns that have ended.
    SmartCampaignEndedDetails ended_details = 6;
  }
}

// Request message for
// [SmartCampaignSettingService.MutateSmartCampaignSettings][google.ads.googleads.v16.services.SmartCampaignSettingService.MutateSmartCampaignSettings].
message MutateSmartCampaignSettingsRequest {
  // Required. The ID of the customer whose Smart campaign settings are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual Smart campaign
  // settings.
  repeated SmartCampaignSettingOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v16.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 5;
}

// A single operation to update Smart campaign settings for a campaign.
message SmartCampaignSettingOperation {
  // Update operation: The Smart campaign setting must specify a valid
  // resource name.
  google.ads.googleads.v16.resources.SmartCampaignSetting update = 1;

  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 2;
}

// Response message for campaign mutate.
message MutateSmartCampaignSettingsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 1;

  // All results for the mutate.
  repeated MutateSmartCampaignSettingResult results = 2;
}

// The result for the Smart campaign setting mutate.
message MutateSmartCampaignSettingResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/SmartCampaignSetting"
  }];

  // The mutated Smart campaign setting with only mutable fields after mutate.
  // The field will only be returned when response_content_type is set to
  // "MUTABLE_RESOURCE".
  google.ads.googleads.v16.resources.SmartCampaignSetting
      smart_campaign_setting = 2;
}
