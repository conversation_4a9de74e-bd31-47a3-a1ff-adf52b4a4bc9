// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/resources/google_ads_field.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "GoogleAdsFieldServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the GoogleAdsFieldService.

// Service to fetch Google Ads API fields.
service GoogleAdsFieldService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns just the requested field.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc GetGoogleAdsField(GetGoogleAdsFieldRequest)
      returns (google.ads.googleads.v16.resources.GoogleAdsField) {
    option (google.api.http) = {
      get: "/v16/{resource_name=googleAdsFields/*}"
    };
    option (google.api.method_signature) = "resource_name";
  }

  // Returns all fields that match the search query.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QueryError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc SearchGoogleAdsFields(SearchGoogleAdsFieldsRequest)
      returns (SearchGoogleAdsFieldsResponse) {
    option (google.api.http) = {
      post: "/v16/googleAdsFields:search"
      body: "*"
    };
    option (google.api.method_signature) = "query";
  }
}

// Request message for
// [GoogleAdsFieldService.GetGoogleAdsField][google.ads.googleads.v16.services.GoogleAdsFieldService.GetGoogleAdsField].
message GetGoogleAdsFieldRequest {
  // Required. The resource name of the field to get.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/GoogleAdsField"
    }
  ];
}

// Request message for
// [GoogleAdsFieldService.SearchGoogleAdsFields][google.ads.googleads.v16.services.GoogleAdsFieldService.SearchGoogleAdsFields].
message SearchGoogleAdsFieldsRequest {
  // Required. The query string.
  string query = 1 [(google.api.field_behavior) = REQUIRED];

  // Token of the page to retrieve. If not specified, the first page of
  // results will be returned. Use the value obtained from `next_page_token`
  // in the previous response in order to request the next page of results.
  string page_token = 2;

  // Number of elements to retrieve in a single page.
  // When too large a page is requested, the server may decide to further
  // limit the number of returned resources.
  int32 page_size = 3;
}

// Response message for
// [GoogleAdsFieldService.SearchGoogleAdsFields][google.ads.googleads.v16.services.GoogleAdsFieldService.SearchGoogleAdsFields].
message SearchGoogleAdsFieldsResponse {
  // The list of fields that matched the query.
  repeated google.ads.googleads.v16.resources.GoogleAdsField results = 1;

  // Pagination token used to retrieve the next page of results. Pass the
  // content of this string as the `page_token` attribute of the next request.
  // `next_page_token` is not returned for the last page.
  string next_page_token = 2;

  // Total number of results that match the query ignoring the LIMIT clause.
  int64 total_results_count = 3;
}
