// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/common/offline_user_data.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "UserDataServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the UserDataService.

// Service to manage user data uploads.
// Any uploads made to a Customer Match list through this service will be
// eligible for matching as per the customer matching process. See
// https://support.google.com/google-ads/answer/7474263. However, the uploads
// made through this service will not be visible under the 'Segment members'
// section for the Customer Match List in the Google Ads UI.
service UserDataService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Uploads the given user data.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CollectionSizeError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [OfflineUserDataJobError]()
  //   [QuotaError]()
  //   [RequestError]()
  //   [UserDataError]()
  rpc UploadUserData(UploadUserDataRequest) returns (UploadUserDataResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}:uploadUserData"
      body: "*"
    };
  }
}

// Request message for
// [UserDataService.UploadUserData][google.ads.googleads.v16.services.UserDataService.UploadUserData]
message UploadUserDataRequest {
  // Required. The ID of the customer for which to update the user data.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to be done.
  repeated UserDataOperation operations = 3
      [(google.api.field_behavior) = REQUIRED];

  // Metadata of the request.
  oneof metadata {
    // Metadata for data updates to a Customer Match user list.
    google.ads.googleads.v16.common.CustomerMatchUserListMetadata
        customer_match_user_list_metadata = 2;
  }
}

// Operation to be made for the UploadUserDataRequest.
message UserDataOperation {
  // Operation to be made for the UploadUserDataRequest.
  oneof operation {
    // The list of user data to be appended to the user list.
    google.ads.googleads.v16.common.UserData create = 1;

    // The list of user data to be removed from the user list.
    google.ads.googleads.v16.common.UserData remove = 2;
  }
}

// Response message for
// [UserDataService.UploadUserData][google.ads.googleads.v16.services.UserDataService.UploadUserData]
// Uploads made through this service will not be visible under the 'Segment
// members' section for the Customer Match List in the Google Ads UI.
message UploadUserDataResponse {
  // The date time at which the request was received by API, formatted as
  // "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string upload_date_time = 3;

  // Number of upload data operations received by API.
  optional int32 received_operations_count = 4;
}
