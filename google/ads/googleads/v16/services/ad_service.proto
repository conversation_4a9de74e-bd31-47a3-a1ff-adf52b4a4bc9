// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/common/policy.proto";
import "google/ads/googleads/v16/enums/response_content_type.proto";
import "google/ads/googleads/v16/resources/ad.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "AdServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the  Ad service.

// Service to manage ads.
service AdService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns the requested ad in full detail.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc GetAd(GetAdRequest) returns (google.ads.googleads.v16.resources.Ad) {
    option (google.api.http) = {
      get: "/v16/{resource_name=customers/*/ads/*}"
    };
    option (google.api.method_signature) = "resource_name";
  }

  // Updates ads. Operation statuses are returned. Updating ads is not supported
  // for TextAd, ExpandedDynamicSearchAd, GmailAd and ImageAd.
  //
  // List of thrown errors:
  //   [AdCustomizerError]()
  //   [AdError]()
  //   [AdSharingError]()
  //   [AdxError]()
  //   [AssetError]()
  //   [AssetLinkError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CollectionSizeError]()
  //   [DatabaseError]()
  //   [DateError]()
  //   [DistinctError]()
  //   [FeedAttributeReferenceError]()
  //   [FieldError]()
  //   [FieldMaskError]()
  //   [FunctionError]()
  //   [FunctionParsingError]()
  //   [HeaderError]()
  //   [IdError]()
  //   [ImageError]()
  //   [InternalError]()
  //   [ListOperationError]()
  //   [MediaBundleError]()
  //   [MediaFileError]()
  //   [MutateError]()
  //   [NewResourceCreationError]()
  //   [NotEmptyError]()
  //   [NullError]()
  //   [OperatorError]()
  //   [PolicyFindingError]()
  //   [PolicyViolationError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  //   [SizeLimitError]()
  //   [StringFormatError]()
  //   [StringLengthError]()
  //   [UrlFieldError]()
  rpc MutateAds(MutateAdsRequest) returns (MutateAdsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/ads:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [AdService.GetAd][google.ads.googleads.v16.services.AdService.GetAd].
message GetAdRequest {
  // Required. The resource name of the ad to fetch.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Ad" }
  ];
}

// Request message for
// [AdService.MutateAds][google.ads.googleads.v16.services.AdService.MutateAds].
message MutateAdsRequest {
  // Required. The ID of the customer whose ads are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual ads.
  repeated AdOperation operations = 2 [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 4;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v16.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 5;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// A single update operation on an ad.
message AdOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 2;

  // Configuration for how policies are validated.
  google.ads.googleads.v16.common.PolicyValidationParameter
      policy_validation_parameter = 3;

  // The mutate operation.
  oneof operation {
    // Update operation: The ad is expected to have a valid resource name
    // in this format:
    //
    // `customers/{customer_id}/ads/{ad_id}`
    google.ads.googleads.v16.resources.Ad update = 1;
  }
}

// Response message for an ad mutate.
message MutateAdsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 3;

  // All results for the mutate.
  repeated MutateAdResult results = 2;
}

// The result for the ad mutate.
message MutateAdResult {
  // The resource name returned for successful operations.
  string resource_name = 1 [
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Ad" }
  ];

  // The mutated ad with only mutable fields after mutate. The field will only
  // be returned when response_content_type is set to "MUTABLE_RESOURCE".
  google.ads.googleads.v16.resources.Ad ad = 2;
}
