// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/enums/response_content_type.proto";
import "google/ads/googleads/v16/resources/recommendation_subscription.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "RecommendationSubscriptionServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the Recommendation service.

// Service to manage recommendation subscriptions.
service RecommendationSubscriptionService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Mutates given subscription with corresponding apply parameters.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RecommendationError]()
  //   [RequestError]()
  //   [UrlFieldError]()
  rpc MutateRecommendationSubscription(MutateRecommendationSubscriptionRequest)
      returns (MutateRecommendationSubscriptionResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/recommendationSubscriptions:mutateRecommendationSubscription"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [RecommendationSubscriptionService.MutateRecommendationSubscription]
message MutateRecommendationSubscriptionRequest {
  // Required. The ID of the subscribing customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of create or update operations.
  repeated RecommendationSubscriptionOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation. The mutable
  // resource will only be returned if the resource has the appropriate response
  // field. For example, MutateCampaignResult.campaign.
  google.ads.googleads.v16.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 5;
}

// A single operation (create, update) on a recommendation subscription.
// [RecommendationSubscriptionService.MutateRecommendationSubscription]
message RecommendationSubscriptionOperation {
  // Optional. FieldMask that determines which resource fields are modified in
  // an update.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Create or update operation.
  oneof operation {
    // Create operation: No resource name is expected for the new subscription.
    google.ads.googleads.v16.resources.RecommendationSubscription create = 1;

    // Update operation: The subscription is expected to have a valid
    // resource name.
    google.ads.googleads.v16.resources.RecommendationSubscription update = 2;
  }
}

// Response message for
// [RecommendationSubscriptionService.MutateRecommendationSubscription]
message MutateRecommendationSubscriptionResponse {
  // Results, one per operation.
  repeated MutateRecommendationSubscriptionResult results = 1;

  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors) we return the RPC level error.
  google.rpc.Status partial_failure_error = 2;
}

// Result message for
// [RecommendationSubscriptionService.MutateRecommendationSubscription]
message MutateRecommendationSubscriptionResult {
  // Resource name of the subscription that was modified.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/RecommendationSubscription"
  }];

  // The mutated recommendation subscription with only mutable fields after
  // mutate. The field will only be returned when response_content_type is set
  // to "MUTABLE_RESOURCE".
  google.ads.googleads.v16.resources.RecommendationSubscription
      recommendation_subscription = 2;
}
