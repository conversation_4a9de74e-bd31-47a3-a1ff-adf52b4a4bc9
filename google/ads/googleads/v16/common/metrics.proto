// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/enums/interaction_event_type.proto";
import "google/ads/googleads/v16/enums/quality_score_bucket.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "MetricsProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing metrics.

// Metrics data.
message Metrics {
  // Search absolute top impression share is the percentage of your Search ad
  // impressions that are shown in the most prominent Search position.
  optional double absolute_top_impression_percentage = 183;

  // Average cost of viewable impressions (`active_view_impressions`).
  optional double active_view_cpm = 184;

  // Active view measurable clicks divided by active view viewable impressions.
  //
  // This metric is reported only for the Display Network.
  optional double active_view_ctr = 185;

  // A measurement of how often your ad has become viewable on a Display
  // Network site.
  optional int64 active_view_impressions = 186;

  // The ratio of impressions that could be measured by Active View over the
  // number of served impressions.
  optional double active_view_measurability = 187;

  // The cost of the impressions you received that were measurable by Active
  // View.
  optional int64 active_view_measurable_cost_micros = 188;

  // The number of times your ads are appearing on placements in positions
  // where they can be seen.
  optional int64 active_view_measurable_impressions = 189;

  // The percentage of time when your ad appeared on an Active View enabled site
  // (measurable impressions) and was viewable (viewable impressions).
  optional double active_view_viewability = 190;

  // All conversions from interactions (as oppose to view through conversions)
  // divided by the number of ad interactions.
  optional double all_conversions_from_interactions_rate = 191;

  // The value of all conversions.
  optional double all_conversions_value = 192;

  // The value of all conversions. When this column is selected with date, the
  // values in date column means the conversion date. Details for the
  // by_conversion_date columns are available at
  // https://support.google.com/google-ads/answer/9549009.
  double all_conversions_value_by_conversion_date = 240;

  // All of new customers' lifetime conversion value. If you have set up
  // customer acquisition goal at either account level or campaign level, this
  // will include the additional conversion value from new customers for both
  // biddable and non-biddable conversions. If your campaign has adopted the
  // customer acquisition goal and selected "bid higher for new customers",
  // these values will be included in "all_conversions_value". See
  // https://support.google.com/google-ads/answer/******** for more details.
  optional double all_new_customer_lifetime_value = 294;

  // The total number of conversions. This includes all conversions regardless
  // of the value of include_in_conversions_metric.
  optional double all_conversions = 193;

  // The total number of conversions. This includes all conversions regardless
  // of the value of include_in_conversions_metric. When this column is selected
  // with date, the values in date column means the conversion date. Details for
  // the by_conversion_date columns are available at
  // https://support.google.com/google-ads/answer/9549009.
  double all_conversions_by_conversion_date = 241;

  // The value of all conversions divided by the total cost of ad interactions
  // (such as clicks for text ads or views for video ads).
  optional double all_conversions_value_per_cost = 194;

  // The number of times people clicked the "Call" button to call a store during
  // or after clicking an ad. This number doesn't include whether or not calls
  // were connected, or the duration of any calls.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_click_to_call = 195;

  // The number of times people clicked a "Get directions" button to navigate to
  // a store after clicking an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_directions = 196;

  // The value of all conversions from interactions divided by the total number
  // of interactions.
  optional double all_conversions_from_interactions_value_per_interaction = 197;

  // The number of times people clicked a link to view a store's menu after
  // clicking an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_menu = 198;

  // The number of times people placed an order at a store after clicking an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_order = 199;

  // The number of other conversions (for example, posting a review or saving a
  // location for a store) that occurred after people clicked an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_other_engagement = 200;

  // Estimated number of times people visited a store after clicking an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_store_visit = 201;

  // The number of times that people were taken to a store's URL after clicking
  // an ad.
  //
  // This metric applies to feed items only.
  optional double all_conversions_from_store_website = 202;

  // This metric is part of the Auction Insights report, and tells how often
  // the ads of another participant showed in the most prominent position on the
  // search results page.
  // This percentage is computed only over the auctions that you appeared in
  // the page.
  //
  // This metric is not publicly available.
  optional double auction_insight_search_absolute_top_impression_percentage =
      258;

  // This metric is part of the Auction Insights report, and tells the
  // percentage of impressions that another participant obtained, over the total
  // number of impressions that your ads were eligible for.
  // Any value below 0.1 is reported as 0.0999.
  //
  // This metric is not publicly available.
  optional double auction_insight_search_impression_share = 259;

  // This metric is part of the Auction Insights report, and tells the
  // percentage of impressions that your ads outranked (showed above)
  // another participant in the auction, compared to the total number of
  // impressions that your ads were eligible for.
  // Any value below 0.1 is reported as 0.0999.
  //
  // This metric is not publicly available.
  optional double auction_insight_search_outranking_share = 260;

  // This metric is part of the Auction Insights report, and tells how often
  // another participant's ad received an impression when your ad also received
  // an impression.
  //
  // This metric is not publicly available.
  optional double auction_insight_search_overlap_rate = 261;

  // This metric is part of the Auction Insights report, and tells how often
  // another participant's ad was shown in a higher position than yours, when
  // both of your ads were shown at the same page.
  //
  // This metric is not publicly available.
  optional double auction_insight_search_position_above_rate = 262;

  // This metric is part of the Auction Insights report, and tells how often
  // the ads of another participant showed adjacent to the top organic search
  // results. This percentage is computed only over the auctions that you
  // appeared in the page.
  //
  // This metric is not publicly available.
  optional double auction_insight_search_top_impression_percentage = 263;

  // The average amount you pay per interaction. This amount is the total cost
  // of your ads divided by the total number of interactions.
  optional double average_cost = 203;

  // The total cost of all clicks divided by the total number of clicks
  // received.
  optional double average_cpc = 204;

  // The average amount that you've been charged for an ad engagement. This
  // amount is the total cost of all ad engagements divided by the total number
  // of ad engagements.
  optional double average_cpe = 205;

  // Average cost-per-thousand impressions (CPM).
  optional double average_cpm = 206;

  // The average amount you pay each time someone views your ad.
  // The average CPV is defined by the total cost of all ad views divided by
  // the number of views.
  optional double average_cpv = 207;

  // Average number of pages viewed per session.
  optional double average_page_views = 208;

  // Total duration of all sessions (in seconds) / number of sessions. Imported
  // from Google Analytics.
  optional double average_time_on_site = 209;

  // An indication of how other advertisers are bidding on similar products.
  optional double benchmark_average_max_cpc = 210;

  // Number of app installs.
  optional double biddable_app_install_conversions = 254;

  // Number of in-app actions.
  optional double biddable_app_post_install_conversions = 255;

  // An indication on how other advertisers' Shopping ads for similar products
  // are performing based on how often people who see their ad click on it.
  optional double benchmark_ctr = 211;

  // Percentage of clicks where the user only visited a single page on your
  // site. Imported from Google Analytics.
  optional double bounce_rate = 212;

  // The number of clicks.
  optional int64 clicks = 131;

  // The number of times your ad or your site's listing in the unpaid
  // results was clicked. See the help page at
  // https://support.google.com/google-ads/answer/3097241 for details.
  optional int64 combined_clicks = 156;

  // The number of times your ad or your site's listing in the unpaid
  // results was clicked (combined_clicks) divided by combined_queries. See the
  // help page at https://support.google.com/google-ads/answer/3097241 for
  // details.
  optional double combined_clicks_per_query = 157;

  // The number of searches that returned pages from your site in the unpaid
  // results or showed one of your text ads. See the help page at
  // https://support.google.com/google-ads/answer/3097241 for details.
  optional int64 combined_queries = 158;

  // The estimated percent of times that your ad was eligible to show
  // on the Display Network but didn't because your budget was too low.
  // Note: Content budget lost impression share is reported in the range of 0
  // to 0.9. Any value above 0.9 is reported as 0.9001.
  optional double content_budget_lost_impression_share = 159;

  // The impressions you've received on the Display Network divided
  // by the estimated number of impressions you were eligible to receive.
  // Note: Content impression share is reported in the range of 0.1 to 1. Any
  // value below 0.1 is reported as 0.0999.
  optional double content_impression_share = 160;

  // The last date/time a conversion tag for this conversion action successfully
  // fired and was seen by Google Ads. This firing event may not have been the
  // result of an attributable conversion (for example, because the tag was
  // fired from a browser that did not previously click an ad from an
  // appropriate advertiser). The date/time is in the customer's time zone.
  optional string conversion_last_received_request_date_time = 161;

  // The date of the most recent conversion for this conversion action. The date
  // is in the customer's time zone.
  optional string conversion_last_conversion_date = 162;

  // The estimated percentage of impressions on the Display Network
  // that your ads didn't receive due to poor Ad Rank.
  // Note: Content rank lost impression share is reported in the range of 0
  // to 0.9. Any value above 0.9 is reported as 0.9001.
  optional double content_rank_lost_impression_share = 163;

  // Conversions from interactions divided by the number of ad interactions
  // (such as clicks for text ads or views for video ads). This only includes
  // conversion actions which include_in_conversions_metric attribute is set to
  // true. If you use conversion-based bidding, your bid strategies will
  // optimize for these conversions.
  optional double conversions_from_interactions_rate = 164;

  // The value of conversions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double conversions_value = 165;

  // The value of conversions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions. When this column is selected with date, the values in date
  // column means the conversion date. Details for the by_conversion_date
  // columns are available at
  // https://support.google.com/google-ads/answer/9549009.
  double conversions_value_by_conversion_date = 242;

  // New customers' lifetime conversion value. If you have set up
  // customer acquisition goal at either account level or campaign level, this
  // will include the additional conversion value from new customers for
  // biddable conversions. If your campaign has adopted the customer
  // acquisition goal and selected "bid higher for new customers", these values
  // will be included in "conversions_value" for optimization. See
  // https://support.google.com/google-ads/answer/******** for more details.
  optional double new_customer_lifetime_value = 293;

  // The value of conversions divided by the cost of ad interactions. This only
  // includes conversion actions which include_in_conversions_metric attribute
  // is set to true. If you use conversion-based bidding, your bid strategies
  // will optimize for these conversions.
  optional double conversions_value_per_cost = 166;

  // The value of conversions from interactions divided by the number of ad
  // interactions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double conversions_from_interactions_value_per_interaction = 167;

  // The number of conversions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double conversions = 168;

  // The number of conversions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions. When this column is selected with date, the values in date
  // column means the conversion date. Details for the by_conversion_date
  // columns are available at
  // https://support.google.com/google-ads/answer/9549009.
  double conversions_by_conversion_date = 243;

  // The sum of your cost-per-click (CPC) and cost-per-thousand impressions
  // (CPM) costs during this period.
  optional int64 cost_micros = 169;

  // The cost of ad interactions divided by all conversions.
  optional double cost_per_all_conversions = 170;

  // The cost of ad interactions divided by conversions. This only includes
  // conversion actions which include_in_conversions_metric attribute is set to
  // true. If you use conversion-based bidding, your bid strategies will
  // optimize for these conversions.
  optional double cost_per_conversion = 171;

  // The cost of ad interactions divided by current model attributed
  // conversions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double cost_per_current_model_attributed_conversion = 172;

  // Conversions from when a customer clicks on a Google Ads ad on one device,
  // then converts on a different device or browser.
  // Cross-device conversions are already included in all_conversions.
  optional double cross_device_conversions = 173;

  // The sum of the value of cross-device conversions, in micros.
  optional int64 cross_device_conversions_value_micros = 312;

  // The number of clicks your ad receives (Clicks) divided by the number
  // of times your ad is shown (Impressions).
  optional double ctr = 174;

  // Shows how your historic conversions data would look under the attribution
  // model you've currently selected. This only includes conversion actions
  // which include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double current_model_attributed_conversions = 175;

  // Current model attributed conversions from interactions divided by the
  // number of ad interactions (such as clicks for text ads or views for video
  // ads). This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double current_model_attributed_conversions_from_interactions_rate =
      176;

  // The value of current model attributed conversions from interactions divided
  // by the number of ad interactions. This only includes conversion actions
  // which include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double
      current_model_attributed_conversions_from_interactions_value_per_interaction =
          177;

  // The value of current model attributed conversions. This only includes
  // conversion actions which include_in_conversions_metric attribute is set to
  // true. If you use conversion-based bidding, your bid strategies will
  // optimize for these conversions.
  optional double current_model_attributed_conversions_value = 178;

  // The value of current model attributed conversions divided by the cost of ad
  // interactions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double current_model_attributed_conversions_value_per_cost = 179;

  // How often people engage with your ad after it's shown to them. This is the
  // number of ad expansions divided by the number of times your ad is shown.
  optional double engagement_rate = 180;

  // The number of engagements.
  // An engagement occurs when a viewer expands your Lightbox ad. Also, in the
  // future, other ad types may support engagement metrics.
  optional int64 engagements = 181;

  // Average lead value based on clicks.
  optional double hotel_average_lead_value_micros = 213;

  // Commission bid rate in micros. A 20% commission is represented as
  // 200,000.
  optional int64 hotel_commission_rate_micros = 256;

  // Expected commission cost. The result of multiplying the commission value
  // times the hotel_commission_rate in advertiser currency.
  optional double hotel_expected_commission_cost = 257;

  // The average price difference between the price offered by reporting hotel
  // advertiser and the cheapest price offered by the competing advertiser.
  optional double hotel_price_difference_percentage = 214;

  // The number of impressions that hotel partners could have had given their
  // feed performance.
  optional int64 hotel_eligible_impressions = 215;

  // The creative historical quality score.
  google.ads.googleads.v16.enums.QualityScoreBucketEnum.QualityScoreBucket
      historical_creative_quality_score = 80;

  // The quality of historical landing page experience.
  google.ads.googleads.v16.enums.QualityScoreBucketEnum.QualityScoreBucket
      historical_landing_page_quality_score = 81;

  // The historical quality score.
  optional int64 historical_quality_score = 216;

  // The historical search predicted click through rate (CTR).
  google.ads.googleads.v16.enums.QualityScoreBucketEnum.QualityScoreBucket
      historical_search_predicted_ctr = 83;

  // The number of times the ad was forwarded to someone else as a message.
  optional int64 gmail_forwards = 217;

  // The number of times someone has saved your Gmail ad to their inbox as a
  // message.
  optional int64 gmail_saves = 218;

  // The number of clicks to the landing page on the expanded state of Gmail
  // ads.
  optional int64 gmail_secondary_clicks = 219;

  // The number of times a store's location-based ad was shown.
  //
  // This metric applies to feed items only.
  optional int64 impressions_from_store_reach = 220;

  // Count of how often your ad has appeared on a search results page or
  // website on the Google Network.
  optional int64 impressions = 221;

  // How often people interact with your ad after it is shown to them.
  // This is the number of interactions divided by the number of times your ad
  // is shown.
  optional double interaction_rate = 222;

  // The number of interactions.
  // An interaction is the main user action associated with an ad format-clicks
  // for text and shopping ads, views for video ads, and so on.
  optional int64 interactions = 223;

  // The types of payable and free interactions.
  repeated google.ads.googleads.v16.enums.InteractionEventTypeEnum
      .InteractionEventType interaction_event_types = 100;

  // The percentage of clicks filtered out of your total number of clicks
  // (filtered + non-filtered clicks) during the reporting period.
  optional double invalid_click_rate = 224;

  // Number of clicks Google considers illegitimate and doesn't charge you for.
  optional int64 invalid_clicks = 225;

  // Number of message chats initiated for Click To Message impressions that
  // were message tracking eligible.
  optional int64 message_chats = 226;

  // Number of Click To Message impressions that were message tracking eligible.
  optional int64 message_impressions = 227;

  // Number of message chats initiated (message_chats) divided by the number
  // of message impressions (message_impressions).
  // Rate at which a user initiates a message chat from an ad impression with
  // a messaging option and message tracking enabled.
  // Note that this rate can be more than 1.0 for a given message impression.
  optional double message_chat_rate = 228;

  // The percentage of mobile clicks that go to a mobile-friendly page.
  optional double mobile_friendly_clicks_percentage = 229;

  // Total optimization score uplift of all recommendations.
  optional double optimization_score_uplift = 247;

  // URL for the optimization score page in the Google Ads web interface.
  // This metric can be selected from `customer` or `campaign`, and can be
  // segmented by `segments.recommendation_type`. For example, `SELECT
  // metrics.optimization_score_url, segments.recommendation_type FROM
  // customer` will return a URL for each unique (customer, recommendation_type)
  // combination.
  optional string optimization_score_url = 248;

  // The number of times someone clicked your site's listing in the unpaid
  // results for a particular query. See the help page at
  // https://support.google.com/google-ads/answer/3097241 for details.
  optional int64 organic_clicks = 230;

  // The number of times someone clicked your site's listing in the unpaid
  // results (organic_clicks) divided by the total number of searches that
  // returned pages from your site (organic_queries). See the help page at
  // https://support.google.com/google-ads/answer/3097241 for details.
  optional double organic_clicks_per_query = 231;

  // The number of listings for your site in the unpaid search results. See the
  // help page at https://support.google.com/google-ads/answer/3097241 for
  // details.
  optional int64 organic_impressions = 232;

  // The number of times a page from your site was listed in the unpaid search
  // results (organic_impressions) divided by the number of searches returning
  // your site's listing in the unpaid results (organic_queries). See the help
  // page at https://support.google.com/google-ads/answer/3097241 for details.
  optional double organic_impressions_per_query = 233;

  // The total number of searches that returned your site's listing in the
  // unpaid results. See the help page at
  // https://support.google.com/google-ads/answer/3097241 for details.
  optional int64 organic_queries = 234;

  // Percentage of first-time sessions (from people who had never visited your
  // site before). Imported from Google Analytics.
  optional double percent_new_visitors = 235;

  // Number of offline phone calls.
  optional int64 phone_calls = 236;

  // Number of offline phone impressions.
  optional int64 phone_impressions = 237;

  // Number of phone calls received (phone_calls) divided by the number of
  // times your phone number is shown (phone_impressions).
  optional double phone_through_rate = 238;

  // Your clickthrough rate (Ctr) divided by the average clickthrough rate of
  // all advertisers on the websites that show your ads. Measures how your ads
  // perform on Display Network sites compared to other ads on the same sites.
  optional double relative_ctr = 239;

  // The percentage of the customer's Shopping or Search ad impressions that are
  // shown in the most prominent Shopping position. See
  // https://support.google.com/google-ads/answer/7501826
  // for details. Any value below 0.1 is reported as 0.0999.
  optional double search_absolute_top_impression_share = 136;

  // The number estimating how often your ad wasn't the very first ad among the
  // top ads in the search results due to a low budget. Note: Search
  // budget lost absolute top impression share is reported in the range of 0 to
  // 0.9. Any value above 0.9 is reported as 0.9001.
  optional double search_budget_lost_absolute_top_impression_share = 137;

  // The estimated percent of times that your ad was eligible to show on the
  // Search Network but didn't because your budget was too low. Note: Search
  // budget lost impression share is reported in the range of 0 to 0.9. Any
  // value above 0.9 is reported as 0.9001.
  optional double search_budget_lost_impression_share = 138;

  // The number estimating how often your ad didn't show adjacent to the top
  // organic search results due to a low budget. Note: Search
  // budget lost top impression share is reported in the range of 0 to 0.9. Any
  // value above 0.9 is reported as 0.9001.
  optional double search_budget_lost_top_impression_share = 139;

  // The number of clicks you've received on the Search Network
  // divided by the estimated number of clicks you were eligible to receive.
  // Note: Search click share is reported in the range of 0.1 to 1. Any value
  // below 0.1 is reported as 0.0999.
  optional double search_click_share = 140;

  // The impressions you've received divided by the estimated number of
  // impressions you were eligible to receive on the Search Network for search
  // terms that matched your keywords exactly (or were close variants of your
  // keyword), regardless of your keyword match types. Note: Search exact match
  // impression share is reported in the range of 0.1 to 1. Any value below 0.1
  // is reported as 0.0999.
  optional double search_exact_match_impression_share = 141;

  // The impressions you've received on the Search Network divided
  // by the estimated number of impressions you were eligible to receive.
  // Note: Search impression share is reported in the range of 0.1 to 1. Any
  // value below 0.1 is reported as 0.0999.
  optional double search_impression_share = 142;

  // The number estimating how often your ad wasn't the very first ad among the
  // top ads in the search results due to poor Ad Rank.
  // Note: Search rank lost absolute top impression share is reported in the
  // range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.
  optional double search_rank_lost_absolute_top_impression_share = 143;

  // The estimated percentage of impressions on the Search Network
  // that your ads didn't receive due to poor Ad Rank.
  // Note: Search rank lost impression share is reported in the range of 0 to
  // 0.9. Any value above 0.9 is reported as 0.9001.
  optional double search_rank_lost_impression_share = 144;

  // The number estimating how often your ad didn't show adjacent to the top
  // organic search results due to poor Ad Rank.
  // Note: Search rank lost top impression share is reported in the range of 0
  // to 0.9. Any value above 0.9 is reported as 0.9001.
  optional double search_rank_lost_top_impression_share = 145;

  // The impressions you've received among the top ads compared to the estimated
  // number of impressions you were eligible to receive among the top ads.
  // Note: Search top impression share is reported in the range of 0.1 to 1. Any
  // value below 0.1 is reported as 0.0999.
  //
  // Top ads are generally above the top organic results, although they may show
  // below the top organic results on certain queries.
  optional double search_top_impression_share = 146;

  // Search volume range for a search term insight category.
  optional SearchVolumeRange search_volume = 295;

  // A measure of how quickly your page loads after clicks on your mobile ads.
  // The score is a range from 1 to 10, 10 being the fastest.
  optional int64 speed_score = 147;

  // The average Target CPA, or unset if not available (for example, for
  // campaigns that had traffic from portfolio bidding strategies or non-tCPA).
  optional int64 average_target_cpa_micros = 290;

  // The average Target ROAS, or unset if not available (for example, for
  // campaigns that had traffic from portfolio bidding strategies or non-tROAS).
  optional double average_target_roas = 250;

  // The percent of your ad impressions that are shown adjacent to the top
  // organic search results.
  optional double top_impression_percentage = 148;

  // The percentage of ad clicks to Accelerated Mobile Pages (AMP) landing pages
  // that reach a valid AMP page.
  optional double valid_accelerated_mobile_pages_clicks_percentage = 149;

  // The value of all conversions divided by the number of all conversions.
  optional double value_per_all_conversions = 150;

  // The value of all conversions divided by the number of all conversions. When
  // this column is selected with date, the values in date column means the
  // conversion date. Details for the by_conversion_date columns are available
  // at https://support.google.com/google-ads/answer/9549009.
  optional double value_per_all_conversions_by_conversion_date = 244;

  // The value of conversions divided by the number of conversions. This only
  // includes conversion actions which include_in_conversions_metric attribute
  // is set to true. If you use conversion-based bidding, your bid strategies
  // will optimize for these conversions.
  optional double value_per_conversion = 151;

  // The value of conversions divided by the number of conversions. This only
  // includes conversion actions which include_in_conversions_metric attribute
  // is set to true. If you use conversion-based bidding, your bid strategies
  // will optimize for these conversions. When this column is selected with
  // date, the values in date column means the conversion date. Details for the
  // by_conversion_date columns are available at
  // https://support.google.com/google-ads/answer/9549009.
  optional double value_per_conversions_by_conversion_date = 245;

  // The value of current model attributed conversions divided by the number of
  // the conversions. This only includes conversion actions which
  // include_in_conversions_metric attribute is set to true. If you use
  // conversion-based bidding, your bid strategies will optimize for these
  // conversions.
  optional double value_per_current_model_attributed_conversion = 152;

  // Percentage of impressions where the viewer watched all of your video.
  optional double video_quartile_p100_rate = 132;

  // Percentage of impressions where the viewer watched 25% of your video.
  optional double video_quartile_p25_rate = 133;

  // Percentage of impressions where the viewer watched 50% of your video.
  optional double video_quartile_p50_rate = 134;

  // Percentage of impressions where the viewer watched 75% of your video.
  optional double video_quartile_p75_rate = 135;

  // The number of views your TrueView video ad receives divided by its number
  // of impressions, including thumbnail impressions for TrueView in-display
  // ads.
  optional double video_view_rate = 153;

  // The number of times your video ads were viewed.
  optional int64 video_views = 154;

  // The total number of view-through conversions.
  // These happen when a customer sees an image or rich media ad, then later
  // completes a conversion on your site without interacting with (for example,
  // clicking on) another ad.
  optional int64 view_through_conversions = 155;

  // The number of iOS Store Kit Ad Network conversions.
  int64 sk_ad_network_installs = 246;

  // The total number of iOS Store Kit Ad Network conversions.
  int64 sk_ad_network_total_conversions = 292;

  // Clicks from properties not owned by the publisher for which the traffic
  // the publisher has paid for or acquired through incentivized activity
  int64 publisher_purchased_clicks = 264;

  // Clicks from properties for which the traffic the publisher has not paid
  // for or acquired through incentivized activity
  int64 publisher_organic_clicks = 265;

  // Clicks from traffic which is not identified as "Publisher Purchased" or
  // "Publisher Organic"
  int64 publisher_unknown_clicks = 266;

  // Number of call button clicks on any location surface after a chargeable ad
  // event (click or impression). This measure is coming from Asset based
  // location.
  optional double all_conversions_from_location_asset_click_to_call = 267;

  // Number of driving directions clicks on any location surface after a
  // chargeable ad event (click or impression). This measure is coming
  // from Asset based location.
  optional double all_conversions_from_location_asset_directions = 268;

  // Number of menu link clicks on any location surface after a chargeable ad
  // event (click or impression). This measure is coming from Asset based
  // location.
  optional double all_conversions_from_location_asset_menu = 269;

  // Number of order clicks on any location surface after a chargeable ad event
  // (click or impression). This measure is coming from Asset based
  // location.
  optional double all_conversions_from_location_asset_order = 270;

  // Number of other types of local action clicks on any location surface after
  // a chargeable ad event (click or impression). This measure is coming
  // from Asset based location.
  optional double all_conversions_from_location_asset_other_engagement = 271;

  // Estimated number of visits to the store after a chargeable
  // ad event (click or impression). This measure is coming from Asset
  // based location.
  optional double all_conversions_from_location_asset_store_visits = 272;

  // Number of website URL clicks on any location surface after a chargeable ad
  // event (click or impression). This measure is coming from Asset based
  // location.
  optional double all_conversions_from_location_asset_website = 273;

  // Number of impressions in which the store location was shown or the location
  // was used for targeting. This measure is coming from Asset based
  // location.
  optional int64 eligible_impressions_from_location_asset_store_reach = 274;

  // Number of call button clicks on any location surface after an impression.
  // This measure is coming from Asset based location.
  optional double view_through_conversions_from_location_asset_click_to_call =
      275;

  // Number of driving directions clicks on any location surface after an
  // impression. This measure is coming from Asset based location.
  optional double view_through_conversions_from_location_asset_directions = 276;

  // Number of menu link clicks on any location surface after an impression.
  // This measure is coming from Asset based location.
  optional double view_through_conversions_from_location_asset_menu = 277;

  // Number of order clicks on any location surface after an impression. This
  // measure is coming from Asset based location.
  optional double view_through_conversions_from_location_asset_order = 278;

  // Number of other types of local action clicks on any location surface after
  // an impression. This measure is coming from Asset based location.
  optional double
      view_through_conversions_from_location_asset_other_engagement = 279;

  // Estimated number of visits to the store after an impression.
  // This measure is coming from Asset based location.
  optional double view_through_conversions_from_location_asset_store_visits =
      280;

  // Number of website URL clicks on any location surface after an impression.
  // This measure is coming from Asset based location.
  optional double view_through_conversions_from_location_asset_website = 281;

  // Orders is the total number of purchase conversions you received attributed
  // to your ads.
  // How it works: You report conversions with cart data for
  // completed purchases on your website. If a conversion is attributed to
  // previous interactions with your ads (clicks for text or Shopping ads, views
  // for video ads etc.) it's counted as an order.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt in an order on your website. Even though they bought 2
  // products, this would count as 1 order.
  // This metric is only available if you report conversions with cart data.
  optional double orders = 296;

  // Average order value is the average revenue you made per order attributed to
  // your ads.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. Average order value is the total revenue from your orders
  // divided by the total number of orders.
  // Example: You received 3 orders which made $10, $15 and $20 worth of
  // revenue. The average order value is $15 = ($10 + $15 + $20)/3.
  // This metric is only available if you report conversions with cart data.
  optional int64 average_order_value_micros = 297;

  // Average cart size is the average number of products in each order
  // attributed to your ads.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. Average cart size is the total number of products sold
  // divided by the total number of orders you received.
  // Example: You received 2 orders, the first included 3 products and the
  // second included 2. The average cart size is 2.5 products = (3+2)/2.
  // This metric is only available if you report conversions with cart data.
  optional double average_cart_size = 298;

  // Cost of goods sold (COGS) is the total cost of the products you sold in
  // orders attributed to your ads.
  // How it works: You can add a cost of goods sold value to every product in
  // Merchant Center. If you report conversions with cart data, the products you
  // sold are matched with their cost of goods sold value and this can be used
  // to calculate the gross profit you made on each order.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
  // has a cost of goods sold value of $5. The cost of goods sold for this order
  // is $8 = $3 + $5.
  // This metric is only available if you report conversions with cart data.
  optional int64 cost_of_goods_sold_micros = 299;

  // Gross profit is the profit you made from orders attributed to your ads
  // minus the cost of goods sold (COGS).
  // How it works: Gross profit is the revenue you made from sales attributed to
  // your ads minus cost of goods sold. Gross profit calculations only include
  // products that have a cost of goods sold value in Merchant Center.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt in an order from your website. The hat is priced $10 and
  // the shirt is priced $20. The hat has a cost of goods sold value of $3, but
  // the shirt has no cost of goods sold value. Gross profit for this order will
  // only take into account the hat, so it's $7 = $10 - $3.
  // This metric is only available if you report conversions with cart data.
  optional int64 gross_profit_micros = 300;

  // Gross profit margin is the percentage gross profit you made from orders
  // attributed to your ads, after taking out the cost of goods sold (COGS).
  // How it works: You report conversions with cart data for completed purchases
  // on your website. Gross profit margin is the gross profit you made divided
  // by your total revenue and multiplied by 100%. Gross profit margin
  // calculations only include products that have a cost of goods sold value in
  // Merchant Center.
  // Example: Someone bought a hat and a shirt in an order on your website. The
  // hat is priced $10 and has a cost of goods sold value of $3. The shirt is
  // priced $20 but has no cost of goods sold value. Gross profit margin for
  // this order will only take into account the hat because it has a cost of
  // goods sold value, so it's 70% = ($10 - $3)/$10 x 100%.
  // This metric is only available if you report conversions with cart data.
  optional double gross_profit_margin = 301;

  // Revenue is the total amount you made from orders attributed to your ads.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. Revenue is the total value of all the orders you received
  // attributed to your ads, minus any discount.
  // Example: Someone clicked on a Shopping ad  for a hat then bought the same
  // hat and a shirt in an order from your website. The hat is priced $10 and
  // the shirt is priced $20. The entire order has a $5 discount. The revenue
  // from this order is $25 = ($10 + $20) - $5.
  // This metric is only available if you report conversions with cart data.
  optional int64 revenue_micros = 302;

  // Units sold is the total number of products sold from orders attributed to
  // your ads.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. Units sold is the total number of products sold from all
  // orders attributed to your ads.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat, a shirt and a jacket. The units sold in this order is 3.
  // This metric is only available if you report conversions with cart data.
  optional double units_sold = 303;

  // Cross-sell cost of goods sold (COGS) is the total cost of products sold as
  // a result of advertising a different product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If these products don't match then this is
  // considered cross-sell. Cross-sell cost of goods sold is the total cost of
  // the products sold that weren't advertised.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
  // has a cost of goods sold value of $5. The cross-sell cost of goods sold for
  // this order is $5.
  // This metric is only available if you report conversions with cart data.
  optional int64 cross_sell_cost_of_goods_sold_micros = 304;

  // Cross-sell gross profit is the profit you made from products sold as a
  // result of advertising a different product, minus cost of goods sold (COGS).
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the purchase is a sold
  // product. If these products don't match then this is considered cross-sell.
  // Cross-sell gross profit is the revenue you made from cross-sell attributed
  // to your ads minus the cost of the goods sold.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The shirt is priced $20 and has a cost of goods sold value
  // of $5. The cross-sell gross profit of this order is $15 = $20 - $5.
  // This metric is only available if you report conversions with cart data.
  optional int64 cross_sell_gross_profit_micros = 305;

  // Cross-sell revenue is the total amount you made from products sold as a
  // result of advertising a different product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If these products don't match then this is
  // considered cross-sell. Cross-sell revenue is the total value you made from
  // cross-sell attributed to your ads.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
  // cross-sell revenue of this order is $20.
  // This metric is only available if you report conversions with cart data.
  optional int64 cross_sell_revenue_micros = 306;

  // Cross-sell units sold is the total number of products sold as a result of
  // advertising a different product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If these products don't match then this is
  // considered cross-sell. Cross-sell units sold is the total number of
  // cross-sold products from all orders attributed to your ads.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat, a shirt and a jacket. The cross-sell units sold in this order is 2.
  // This metric is only available if you report conversions with cart data.
  optional double cross_sell_units_sold = 307;

  // Lead cost of goods sold (COGS) is the total cost of products sold as a
  // result of advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with has an associated
  // product (see Shopping Ads) then this product is considered the advertised
  // product. Any product included in the order the customer places is a sold
  // product. If the advertised and sold products match, then the cost of these
  // goods is counted under lead cost of goods sold.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
  // has a cost of goods sold value of $5. The lead cost of goods sold for this
  // order is $3.
  // This metric is only available if you report conversions with cart data.
  optional int64 lead_cost_of_goods_sold_micros = 308;

  // Lead gross profit is the profit you made from products sold as a result of
  // advertising the same product, minus cost of goods sold (COGS).
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the revenue you made from these sales minus the cost of goods sold is your
  // lead gross profit.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The hat is priced $10 and has a cost of goods sold value
  // of $3. The lead gross profit of this order is $7 = $10 - $3.
  // This metric is only available if you report conversions with cart data.
  optional int64 lead_gross_profit_micros = 309;

  // Lead revenue is the total amount you made from products sold as a result of
  // advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the total value you made from the sales of these products is shown under
  // lead revenue.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
  // lead revenue of this order is $10.
  // This metric is only available if you report conversions with cart data.
  optional int64 lead_revenue_micros = 310;

  // Lead units sold is the total number of products sold as a result of
  // advertising the same product.
  // How it works: You report conversions with cart data for completed purchases
  // on your website. If the ad that was interacted with before the purchase has
  // an associated product (see Shopping Ads) then this product is considered
  // the advertised product. Any product included in the order the customer
  // places is a sold product. If the advertised and sold products match, then
  // the total number of these products sold is shown under lead units sold.
  // Example: Someone clicked on a Shopping ad for a hat then bought the same
  // hat, a shirt and a jacket. The lead units sold in this order is 1.
  // This metric is only available if you report conversions with cart data.
  optional double lead_units_sold = 311;

  // The number of unique users who saw your ad during the requested time
  // period. This metric cannot be aggregated, and can only be requested for
  // date ranges of 92 days or less. This metric is available for following
  // campaign types - Display, Video, Discovery and App.
  optional int64 unique_users = 319;

  // The average number of times a unique user saw your ad during the requested
  // time period. This metric cannot be aggregated, and can only be requested
  // for date ranges of 92 days or less. This metric is available for following
  // campaign types - Display, Video, Discovery and App.
  optional double average_impression_frequency_per_user = 320;
}

// Search volume range.
// Actual search volume falls within this range.
message SearchVolumeRange {
  // Lower bound of search volume.
  optional int64 min = 1;

  // Upper bound of search volume.
  optional int64 max = 2;
}
