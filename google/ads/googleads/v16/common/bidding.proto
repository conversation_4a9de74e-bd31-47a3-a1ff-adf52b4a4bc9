// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/enums/target_frequency_time_unit.proto";
import "google/ads/googleads/v16/enums/target_impression_share_location.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "BiddingProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing bidding schemes.

// Commission is an automatic bidding strategy in which the advertiser pays a
// certain portion of the conversion value.
message Commission {
  // Commission rate defines the portion of the conversion value that the
  // advertiser will be billed. A commission rate of x should be passed into
  // this field as (x * 1,000,000). For example, 106,000 represents a commission
  // rate of 0.106 (10.6%).
  optional int64 commission_rate_micros = 2;
}

// An automated bidding strategy that raises bids for clicks
// that seem more likely to lead to a conversion and lowers
// them for clicks where they seem less likely.
//
// This bidding strategy is deprecated and cannot be created anymore. Use
// ManualCpc with enhanced_cpc_enabled set to true for equivalent functionality.
message EnhancedCpc {}

// Manual bidding strategy that allows advertiser to set the bid per
// advertiser-specified action.
message ManualCpa {}

// Manual click-based bidding where user pays per click.
message ManualCpc {
  // Whether bids are to be enhanced based on conversion optimizer data.
  optional bool enhanced_cpc_enabled = 2;
}

// Manual impression-based bidding where user pays per thousand impressions.
message ManualCpm {}

// View based bidding where user pays per video view.
message ManualCpv {}

// An automated bidding strategy to help get the most conversions for your
// campaigns while spending your budget.
message MaximizeConversions {
  // Maximum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  // Mutable for portfolio bidding strategies only.
  int64 cpc_bid_ceiling_micros = 2;

  // Minimum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  // Mutable for portfolio bidding strategies only.
  int64 cpc_bid_floor_micros = 3;

  // The target cost-per-action (CPA) option. This is the average amount that
  // you would like to spend per conversion action specified in micro units of
  // the bidding strategy's currency. If set, the bid strategy will get as many
  // conversions as possible at or below the target cost-per-action. If the
  // target CPA is not set, the bid strategy will aim to achieve the lowest
  // possible CPA given the budget.
  int64 target_cpa_micros = 4;
}

// An automated bidding strategy to help get the most conversion value for your
// campaigns while spending your budget.
message MaximizeConversionValue {
  // The target return on ad spend (ROAS) option. If set, the bid strategy will
  // maximize revenue while averaging the target return on ad spend. If the
  // target ROAS is high, the bid strategy may not be able to spend the full
  // budget. If the target ROAS is not set, the bid strategy will aim to
  // achieve the highest possible ROAS for the budget.
  double target_roas = 2;

  // Maximum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  // Mutable for portfolio bidding strategies only.
  int64 cpc_bid_ceiling_micros = 3;

  // Minimum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  // Mutable for portfolio bidding strategies only.
  int64 cpc_bid_floor_micros = 4;
}

// An automated bid strategy that sets bids to help get as many conversions as
// possible at the target cost-per-acquisition (CPA) you set.
message TargetCpa {
  // Average CPA target.
  // This target should be greater than or equal to minimum billable unit based
  // on the currency for the account.
  optional int64 target_cpa_micros = 4;

  // Maximum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  // This should only be set for portfolio bid strategies.
  optional int64 cpc_bid_ceiling_micros = 5;

  // Minimum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  // This should only be set for portfolio bid strategies.
  optional int64 cpc_bid_floor_micros = 6;
}

// Target CPM (cost per thousand impressions) is an automated bidding strategy
// that sets bids to optimize performance given the target CPM you set.
message TargetCpm {
  // Additional information related to bidding goal.
  oneof goal {
    // Target Frequency bidding goal details.
    TargetCpmTargetFrequencyGoal target_frequency_goal = 1;
  }
}

// Target Frequency bidding goal details.
message TargetCpmTargetFrequencyGoal {
  // Target Frequency count representing how many times you want to reach
  // a single user.
  int64 target_count = 1;

  // Time window expressing the period over which you want to reach
  // the specified target_count.
  google.ads.googleads.v16.enums.TargetFrequencyTimeUnitEnum
      .TargetFrequencyTimeUnit time_unit = 2;
}

// An automated bidding strategy that sets bids so that a certain percentage of
// search ads are shown at the top of the first page (or other targeted
// location).
message TargetImpressionShare {
  // The targeted location on the search results page.
  google.ads.googleads.v16.enums.TargetImpressionShareLocationEnum
      .TargetImpressionShareLocation location = 1;

  // The chosen fraction of ads to be shown in the targeted location in micros.
  // For example, 1% equals 10,000.
  optional int64 location_fraction_micros = 4;

  // The highest CPC bid the automated bidding system is permitted to specify.
  // This is a required field entered by the advertiser that sets the ceiling
  // and specified in local micros.
  optional int64 cpc_bid_ceiling_micros = 5;
}

// An automated bidding strategy that helps you maximize revenue while
// averaging a specific target return on ad spend (ROAS).
message TargetRoas {
  // Required. The chosen revenue (based on conversion data) per unit of spend.
  // Value must be between 0.01 and 1000.0, inclusive.
  optional double target_roas = 4;

  // Maximum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  // This should only be set for portfolio bid strategies.
  optional int64 cpc_bid_ceiling_micros = 5;

  // Minimum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  // This should only be set for portfolio bid strategies.
  optional int64 cpc_bid_floor_micros = 6;
}

// An automated bid strategy that sets your bids to help get as many clicks
// as possible within your budget.
message TargetSpend {
  // The spend target under which to maximize clicks.
  // A TargetSpend bidder will attempt to spend the smaller of this value
  // or the natural throttling spend amount.
  // If not specified, the budget is used as the spend target.
  // This field is deprecated and should no longer be used. See
  // https://ads-developers.googleblog.com/2020/05/reminder-about-sunset-creation-of.html
  // for details.
  optional int64 target_spend_micros = 3 [deprecated = true];

  // Maximum bid limit that can be set by the bid strategy.
  // The limit applies to all keywords managed by the strategy.
  optional int64 cpc_bid_ceiling_micros = 4;
}

// A bidding strategy where bids are a fraction of the advertised price for
// some good or service.
message PercentCpc {
  // Maximum bid limit that can be set by the bid strategy. This is
  // an optional field entered by the advertiser and specified in local micros.
  // Note: A zero value is interpreted in the same way as having bid_ceiling
  // undefined.
  optional int64 cpc_bid_ceiling_micros = 3;

  // Adjusts the bid for each auction upward or downward, depending on the
  // likelihood of a conversion. Individual bids may exceed
  // cpc_bid_ceiling_micros, but the average bid amount for a campaign should
  // not.
  optional bool enhanced_cpc_enabled = 4;
}
