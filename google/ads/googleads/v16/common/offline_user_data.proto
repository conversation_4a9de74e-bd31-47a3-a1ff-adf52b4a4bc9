// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/common/consent.proto";
import "google/ads/googleads/v16/enums/user_identifier_source.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "OfflineUserDataProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing offline user data.

// Address identifier of offline data.
message OfflineUserAddressInfo {
  // First name of the user, which is hashed as SHA-256 after normalized
  // (Lowercase all characters; Remove any extra spaces before, after, and in
  // between).
  optional string hashed_first_name = 7;

  // Last name of the user, which is hashed as SHA-256 after normalized (lower
  // case only and no punctuation).
  optional string hashed_last_name = 8;

  // City of the address. Only accepted for Store Sales and
  // ConversionAdjustmentUploadService.
  optional string city = 9;

  // State code of the address. Only accepted for Store Sales and
  // ConversionAdjustmentUploadService.
  optional string state = 10;

  // 2-letter country code in ISO-3166-1 alpha-2 of the user's address.
  optional string country_code = 11;

  // Postal code of the user's address.
  optional string postal_code = 12;

  // The street address of the user hashed using SHA-256 hash function after
  // normalization (lower case only). Only accepted for
  // ConversionAdjustmentUploadService.
  optional string hashed_street_address = 13;
}

// User identifying information.
message UserIdentifier {
  // Source of the user identifier when the upload is from Store Sales,
  // ConversionUploadService, or ConversionAdjustmentUploadService.
  google.ads.googleads.v16.enums.UserIdentifierSourceEnum.UserIdentifierSource
      user_identifier_source = 6;

  // Exactly one must be specified. For OfflineUserDataJobService, Customer
  // Match accepts hashed_email, hashed_phone_number, mobile_id,
  // third_party_user_id, and address_info; Store Sales accepts hashed_email,
  // hashed_phone_number, third_party_user_id, and address_info.
  // ConversionUploadService accepts hashed_email and hashed_phone_number.
  // ConversionAdjustmentUploadService accepts hashed_email,
  // hashed_phone_number, and address_info.
  oneof identifier {
    // Hashed email address using SHA-256 hash function after normalization.
    // Accepted for Customer Match, Store Sales, ConversionUploadService, and
    // ConversionAdjustmentUploadService.
    string hashed_email = 7;

    // Hashed phone number using SHA-256 hash function after normalization
    // (E164 standard). Accepted for Customer Match, Store Sales,
    // ConversionUploadService, and ConversionAdjustmentUploadService.
    string hashed_phone_number = 8;

    // Mobile device ID (advertising ID/IDFA). Accepted only for Customer Match.
    string mobile_id = 9;

    // Advertiser-assigned user ID for Customer Match upload, or
    // third-party-assigned user ID for Store Sales. Accepted only for Customer
    // Match and Store Sales.
    string third_party_user_id = 10;

    // Address information. Accepted only for Customer Match, Store Sales, and
    // ConversionAdjustmentUploadService.
    OfflineUserAddressInfo address_info = 5;
  }
}

// Attribute of the store sales transaction.
message TransactionAttribute {
  // Timestamp when transaction occurred. Required.
  // The format is "YYYY-MM-DD HH:MM:SS[+/-HH:MM]", where [+/-HH:MM] is an
  // optional timezone offset from UTC. If the offset is absent, the API will
  // use the account's timezone as default.
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30+03:00"
  optional string transaction_date_time = 8;

  // Transaction amount in micros. Required.
  // Transaction amount in micros needs to be greater than 1000.
  // If item Attributes are provided, it represents the total value of the
  // items, after multiplying the unit price per item by the quantity provided
  // in the ItemAttributes.
  optional double transaction_amount_micros = 9;

  // Transaction currency code. ISO 4217 three-letter code is used. Required.
  optional string currency_code = 10;

  // The resource name of conversion action to report conversions to.
  // Required.
  optional string conversion_action = 11;

  // Transaction order id.
  // Accessible only to customers on the allow-list.
  optional string order_id = 12;

  // Store attributes of the transaction.
  // Accessible only to customers on the allow-list.
  StoreAttribute store_attribute = 6;

  // Value of the custom variable for each transaction.
  // Accessible only to customers on the allow-list.
  optional string custom_value = 13;

  // Item attributes of the transaction.
  ItemAttribute item_attribute = 14;
}

// Store attributes of the transaction.
message StoreAttribute {
  // Store code from
  // https://support.google.com/business/answer/3370250#storecode
  optional string store_code = 2;
}

// Item attributes of the transaction.
message ItemAttribute {
  // A unique identifier of a product. It can be either the Merchant Center Item
  // ID or GTIN (Global Trade Item Number).
  string item_id = 1;

  // ID of the Merchant Center Account.
  optional int64 merchant_id = 2;

  // Common Locale Data Repository (CLDR) territory code of the country
  // associated with the feed where your items are uploaded. See
  // https://developers.google.com/google-ads/api/reference/data/codes-formats#country-codes
  // for more information.
  string country_code = 3;

  // ISO 639-1 code of the language associated with the feed where your items
  // are uploaded
  string language_code = 4;

  // The number of items sold. Defaults to 1 if not set.
  int64 quantity = 5;
}

// User data holding user identifiers and attributes.
message UserData {
  // User identification info. Required.
  repeated UserIdentifier user_identifiers = 1;

  // Additional transactions/attributes associated with the user.
  // Required when updating store sales data.
  TransactionAttribute transaction_attribute = 2;

  // Additional attributes associated with the user. Required when updating
  // customer match attributes. These have an expiration of 540 days.
  UserAttribute user_attribute = 3;

  // The consent setting for the user. Customer match will ignore this field
  // and return a warning.
  optional Consent consent = 4;
}

// User attribute, can only be used with CUSTOMER_MATCH_WITH_ATTRIBUTES job
// type.
message UserAttribute {
  // Advertiser defined lifetime value for the user.
  optional int64 lifetime_value_micros = 1;

  // Advertiser defined lifetime value bucket for the user. The valid range for
  // a lifetime value bucket is from 1 (low) to 10 (high), except for remove
  // operation where 0 will also be accepted.
  optional int32 lifetime_value_bucket = 2;

  // Timestamp of the last purchase made by the user.
  // The format is YYYY-MM-DD HH:MM:SS[+/-HH:MM], where [+/-HH:MM] is an
  // optional timezone offset from UTC. If the offset is absent, the API will
  // use the account's timezone as default.
  string last_purchase_date_time = 3;

  // Advertiser defined average number of purchases that are made by the user in
  // a 30 day period.
  int32 average_purchase_count = 4;

  // Advertiser defined average purchase value in micros for the user.
  int64 average_purchase_value_micros = 5;

  // Timestamp when the user was acquired.
  // The format is YYYY-MM-DD HH:MM:SS[+/-HH:MM], where [+/-HH:MM] is an
  // optional timezone offset from UTC. If the offset is absent, the API will
  // use the account's timezone as default.
  string acquisition_date_time = 6;

  // The shopping loyalty related data. Shopping utilizes this data to provide
  // users with a better experience. Accessible only to merchants on the
  // allow-list with the user's consent.
  optional ShoppingLoyalty shopping_loyalty = 7;

  // Optional. Advertiser defined lifecycle stage for the user. The accepted
  // values are "Lead", "Active" and "Churned".
  string lifecycle_stage = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Timestamp of the first purchase made by the user.
  // The format is YYYY-MM-DD HH:MM:SS[+/-HH:MM], where [+/-HH:MM] is an
  // optional timezone offset from UTC. If the offset is absent, the API will
  // use the account's timezone as default.
  string first_purchase_date_time = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Advertiser defined events and their attributes. All the values in
  // the nested fields are required. Currently this field is in beta.
  repeated EventAttribute event_attribute = 10
      [(google.api.field_behavior) = OPTIONAL];
}

// Advertiser defined events and their attributes. All the values in the
// nested fields are required.
message EventAttribute {
  // Required. Advertiser defined event to be used for remarketing. The accepted
  // values are "Viewed", "Cart", "Purchased" and "Recommended".
  string event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Timestamp at which the event happened.
  // The format is YYYY-MM-DD HH:MM:SS[+/-HH:MM], where [+/-HH:MM] is an
  // optional timezone offset from UTC. If the offset is absent, the API will
  // use the account's timezone as default.
  string event_date_time = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Item attributes of the event.
  repeated EventItemAttribute item_attribute = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Event Item attributes of the Customer Match.
message EventItemAttribute {
  // Optional. A unique identifier of a product. It can be either the Merchant
  // Center Item ID or GTIN (Global Trade Item Number).
  string item_id = 1 [(google.api.field_behavior) = OPTIONAL];
}

// The shopping loyalty related data. Shopping utilizes this data to provide
// users with a better experience.
// Accessible only to merchants on the allow-list.
message ShoppingLoyalty {
  // The membership tier. It is a free-form string as each merchant may have
  // their own loyalty system. For example, it could be a number from 1 to 10,
  // or a string such as "Golden" or "Silver", or even empty string "".
  optional string loyalty_tier = 1;
}

// Metadata for customer match user list.
message CustomerMatchUserListMetadata {
  // The resource name of remarketing list to update data.
  // Required for job of CUSTOMER_MATCH_USER_LIST type.
  optional string user_list = 2;

  // The consent setting for all the users in this job.
  optional Consent consent = 3;
}

// Metadata for Store Sales Direct.
message StoreSalesMetadata {
  // This is the fraction of all transactions that are identifiable (for
  // example, associated with any form of customer information). Required. The
  // fraction needs to be between 0 and 1 (excluding 0).
  optional double loyalty_fraction = 5;

  // This is the ratio of sales being uploaded compared to the overall sales
  // that can be associated with a customer. Required.
  // The fraction needs to be between 0 and 1 (excluding 0). For example, if you
  // upload half the sales that you are able to associate with a customer, this
  // would be 0.5.
  optional double transaction_upload_fraction = 6;

  // Name of the store sales custom variable key. A predefined key that
  // can be applied to the transaction and then later used for custom
  // segmentation in reporting.
  // Accessible only to customers on the allow-list.
  optional string custom_key = 7;

  // Metadata for a third party Store Sales upload.
  StoreSalesThirdPartyMetadata third_party_metadata = 3;
}

// Metadata for a third party Store Sales.
// This product is only for customers on the allow-list. Contact your
// Google business development representative for details on the upload
// configuration.
message StoreSalesThirdPartyMetadata {
  // Time the advertiser uploaded the data to the partner. Required.
  // The format is "YYYY-MM-DD HH:MM:SS".
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  optional string advertiser_upload_date_time = 7;

  // The fraction of transactions that are valid. Invalid transactions may
  // include invalid formats or values.
  // Required.
  // The fraction needs to be between 0 and 1 (excluding 0).
  optional double valid_transaction_fraction = 8;

  // The fraction of valid transactions that are matched to a third party
  // assigned user ID on the partner side.
  // Required.
  // The fraction needs to be between 0 and 1 (excluding 0).
  optional double partner_match_fraction = 9;

  // The fraction of valid transactions that are uploaded by the partner to
  // Google.
  // Required.
  // The fraction needs to be between 0 and 1 (excluding 0).
  optional double partner_upload_fraction = 10;

  // Version of partner IDs to be used for uploads. Required.
  optional string bridge_map_version_id = 11;

  // ID of the third party partner updating the transaction feed.
  optional int64 partner_id = 12;
}
