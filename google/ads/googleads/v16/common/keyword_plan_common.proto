// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/common/dates.proto";
import "google/ads/googleads/v16/enums/device.proto";
import "google/ads/googleads/v16/enums/keyword_plan_aggregate_metric_type.proto";
import "google/ads/googleads/v16/enums/keyword_plan_competition_level.proto";
import "google/ads/googleads/v16/enums/keyword_plan_concept_group_type.proto";
import "google/ads/googleads/v16/enums/month_of_year.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanCommonProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing Keyword Planner messages.

// Historical metrics specific to the targeting options selected.
// Targeting options include geographies, network, and so on.
// Refer to https://support.google.com/google-ads/answer/3022575 for more
// details.
message KeywordPlanHistoricalMetrics {
  // Approximate number of monthly searches on this query, averaged
  // for the past 12 months.
  optional int64 avg_monthly_searches = 7;

  // Approximate number of searches on this query for the past twelve months.
  repeated MonthlySearchVolume monthly_search_volumes = 6;

  // The competition level for the query.
  google.ads.googleads.v16.enums.KeywordPlanCompetitionLevelEnum
      .KeywordPlanCompetitionLevel competition = 2;

  // The competition index for the query in the range [0, 100].
  // Shows how competitive ad placement is for a keyword.
  // The level of competition from 0-100 is determined by the number of ad slots
  // filled divided by the total number of ad slots available. If not enough
  // data is available, null is returned.
  optional int64 competition_index = 8;

  // Top of page bid low range (20th percentile) in micros for the keyword.
  optional int64 low_top_of_page_bid_micros = 9;

  // Top of page bid high range (80th percentile) in micros for the keyword.
  optional int64 high_top_of_page_bid_micros = 10;

  // Average Cost Per Click in micros for the keyword.
  optional int64 average_cpc_micros = 11;
}

// Historical metrics options.
message HistoricalMetricsOptions {
  // The year month range for historical metrics. If not specified, metrics
  // for the past 12 months are returned.
  // Search metrics are available for the past 4 years. If the search volume is
  // not available for the entire year_month_range provided, the subset of the
  // year month range for which search volume is available are returned.
  optional YearMonthRange year_month_range = 1;

  // Indicates whether to include average cost per click value.
  // Average CPC is provided only for legacy support.
  bool include_average_cpc = 2;
}

// Monthly search volume.
message MonthlySearchVolume {
  // The year of the search volume (for example, 2020).
  optional int64 year = 4;

  // The month of the search volume.
  google.ads.googleads.v16.enums.MonthOfYearEnum.MonthOfYear month = 2;

  // Approximate number of searches for the month.
  // A null value indicates the search volume is unavailable for
  // that month.
  optional int64 monthly_searches = 5;
}

// The aggregate metrics specification of the request.
message KeywordPlanAggregateMetrics {
  // The list of aggregate metrics to fetch data.
  repeated google.ads.googleads.v16.enums.KeywordPlanAggregateMetricTypeEnum
      .KeywordPlanAggregateMetricType aggregate_metric_types = 1;
}

// The aggregated historical metrics for keyword plan keywords.
message KeywordPlanAggregateMetricResults {
  // The aggregate searches for all the keywords segmented by device
  // for the specified time.
  // Supports the following device types: MOBILE, TABLET, DESKTOP.
  //
  // This is only set when KeywordPlanAggregateMetricTypeEnum.DEVICE is set
  // in the KeywordPlanAggregateMetrics field in the request.
  repeated KeywordPlanDeviceSearches device_searches = 1;
}

// The total searches for the device type during the specified time period.
message KeywordPlanDeviceSearches {
  // The device type.
  google.ads.googleads.v16.enums.DeviceEnum.Device device = 1;

  // The total searches for the device.
  optional int64 search_count = 2;
}

// The annotations for the keyword plan keywords.
message KeywordAnnotations {
  // The list of concepts for the keyword.
  repeated KeywordConcept concepts = 1;
}

// The concept for the keyword.
message KeywordConcept {
  // The concept name for the keyword in the concept_group.
  string name = 1;

  // The concept group of the concept details.
  ConceptGroup concept_group = 2;
}

// The concept group for the keyword concept.
message ConceptGroup {
  // The concept group name.
  string name = 1;

  // The concept group type.
  google.ads.googleads.v16.enums.KeywordPlanConceptGroupTypeEnum
      .KeywordPlanConceptGroupType type = 2;
}
