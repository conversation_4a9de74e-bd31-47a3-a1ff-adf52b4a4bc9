// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/enums/experiment_metric.proto";
import "google/ads/googleads/v16/enums/experiment_metric_direction.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "MetricGoalProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing experiment metric goal.

// A metric goal for an experiment.
message MetricGoal {
  // The metric of the goal. For example, clicks, impressions, cost,
  // conversions, etc.
  google.ads.googleads.v16.enums.ExperimentMetricEnum.ExperimentMetric metric =
      1;

  // The metric direction of the goal. For example, increase, decrease, no
  // change.
  google.ads.googleads.v16.enums.ExperimentMetricDirectionEnum
      .ExperimentMetricDirection direction = 2;
}
