// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/common/policy.proto";
import "google/ads/googleads/v16/enums/asset_link_primary_status.proto";
import "google/ads/googleads/v16/enums/asset_link_primary_status_reason.proto";
import "google/ads/googleads/v16/enums/asset_offline_evaluation_error_reasons.proto";
import "google/ads/googleads/v16/enums/policy_approval_status.proto";
import "google/ads/googleads/v16/enums/policy_review_status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "AssetPolicyProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing asset policies.

// Contains policy information for an asset inside an ad.
message AdAssetPolicySummary {
  // The list of policy findings for this asset.
  repeated PolicyTopicEntry policy_topic_entries = 1;

  // Where in the review process this asset.
  google.ads.googleads.v16.enums.PolicyReviewStatusEnum.PolicyReviewStatus
      review_status = 2;

  // The overall approval status of this asset, which is calculated based on
  // the status of its individual policy topic entries.
  google.ads.googleads.v16.enums.PolicyApprovalStatusEnum.PolicyApprovalStatus
      approval_status = 3;
}

// Provides the detail of a PrimaryStatus.
// Each asset link has a PrimaryStatus value (e.g. NOT_ELIGIBLE, meaning not
// serving), and list of corroborating PrimaryStatusReasons (e.g.
// [ASSET_DISAPPROVED]). Each reason may have some additional details
// annotated with it.  For instance, when the reason is ASSET_DISAPPROVED, the
// details field will contain additional information about the offline
// evaluation errors which led to the asset being disapproved.
message AssetLinkPrimaryStatusDetails {
  // Provides the reason of this PrimaryStatus.
  optional google.ads.googleads.v16.enums.AssetLinkPrimaryStatusReasonEnum
      .AssetLinkPrimaryStatusReason reason = 1;

  // Provides the PrimaryStatus of this status detail.
  optional google.ads.googleads.v16.enums.AssetLinkPrimaryStatusEnum
      .AssetLinkPrimaryStatus status = 2;

  // Provides the details associated with the asset link primary status.
  oneof details {
    // Provides the details for AssetLinkPrimaryStatusReason.ASSET_DISAPPROVED
    AssetDisapproved asset_disapproved = 3;
  }
}

// Details related to AssetLinkPrimaryStatusReasonPB.ASSET_DISAPPROVED
message AssetDisapproved {
  // Provides the quality evaluation disapproval reason of an asset.
  repeated google.ads.googleads.v16.enums.AssetOfflineEvaluationErrorReasonsEnum
      .AssetOfflineEvaluationErrorReasons offline_evaluation_error_reasons = 1;
}
