// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/common/ad_asset.proto";
import "google/ads/googleads/v16/enums/call_conversion_reporting_state.proto";
import "google/ads/googleads/v16/enums/display_ad_format_setting.proto";
import "google/ads/googleads/v16/enums/display_upload_product_type.proto";
import "google/ads/googleads/v16/enums/legacy_app_install_ad_app_store.proto";
import "google/ads/googleads/v16/enums/mime_type.proto";
import "google/ads/googleads/v16/enums/video_thumbnail.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "AdTypeInfosProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file containing info messages for specific ad types.

// A text ad.
message TextAdInfo {
  // The headline of the ad.
  optional string headline = 4;

  // The first line of the ad's description.
  optional string description1 = 5;

  // The second line of the ad's description.
  optional string description2 = 6;
}

// An expanded text ad.
message ExpandedTextAdInfo {
  // The first part of the ad's headline.
  optional string headline_part1 = 8;

  // The second part of the ad's headline.
  optional string headline_part2 = 9;

  // The third part of the ad's headline.
  optional string headline_part3 = 10;

  // The description of the ad.
  optional string description = 11;

  // The second description of the ad.
  optional string description2 = 12;

  // The text that can appear alongside the ad's displayed URL.
  optional string path1 = 13;

  // Additional text that can appear alongside the ad's displayed URL.
  optional string path2 = 14;
}

// An expanded dynamic search ad.
message ExpandedDynamicSearchAdInfo {
  // The description of the ad.
  optional string description = 3;

  // The second description of the ad.
  optional string description2 = 4;
}

// A hotel ad.
message HotelAdInfo {}

// A travel ad.
message TravelAdInfo {}

// A Smart Shopping ad.
message ShoppingSmartAdInfo {}

// A standard Shopping ad.
message ShoppingProductAdInfo {}

// A Shopping Comparison Listing ad.
message ShoppingComparisonListingAdInfo {
  // Headline of the ad. This field is required. Allowed length is between 25
  // and 45 characters.
  optional string headline = 2;
}

// An image ad.
message ImageAdInfo {
  // Width in pixels of the full size image.
  optional int64 pixel_width = 15;

  // Height in pixels of the full size image.
  optional int64 pixel_height = 16;

  // URL of the full size image.
  optional string image_url = 17;

  // Width in pixels of the preview size image.
  optional int64 preview_pixel_width = 18;

  // Height in pixels of the preview size image.
  optional int64 preview_pixel_height = 19;

  // URL of the preview size image.
  optional string preview_image_url = 20;

  // The mime type of the image.
  google.ads.googleads.v16.enums.MimeTypeEnum.MimeType mime_type = 10;

  // The name of the image. If the image was created from a MediaFile, this is
  // the MediaFile's name. If the image was created from bytes, this is empty.
  optional string name = 21;

  // The image to create the ImageAd from. This can be specified in one of
  // two ways.
  // 1. An existing MediaFile resource.
  // 2. The raw image data as bytes.
  oneof image {
    // The image assets used for the ad.
    AdImageAsset image_asset = 22;

    // Raw image data as bytes.
    bytes data = 13;

    // An ad ID to copy the image from.
    int64 ad_id_to_copy_image_from = 14;
  }
}

// Representation of video bumper in-stream ad format (very short in-stream
// non-skippable video ad).
message VideoBumperInStreamAdInfo {
  // The image assets of the companion banner used with the ad.
  AdImageAsset companion_banner = 3;

  // Label on the "Call To Action" button taking the user to the video ad's
  // final URL.
  string action_button_label = 4;

  // Additional text displayed with the CTA (call-to-action) button to give
  // context and encourage clicking on the button.
  string action_headline = 5;
}

// Representation of video non-skippable in-stream ad format (15 second
// in-stream non-skippable video ad).
message VideoNonSkippableInStreamAdInfo {
  // The image assets of the companion banner used with the ad.
  AdImageAsset companion_banner = 5;

  // Label on the "Call To Action" button taking the user to the video ad's
  // final URL.
  string action_button_label = 3;

  // Additional text displayed with the "Call To Action" button to give
  // context and encourage clicking on the button.
  string action_headline = 4;
}

// Representation of video TrueView in-stream ad format (ad shown during video
// playback, often at beginning, which displays a skip button a few seconds into
// the video).
message VideoTrueViewInStreamAdInfo {
  // Label on the CTA (call-to-action) button taking the user to the video ad's
  // final URL.
  // Required for TrueView for action campaigns, optional otherwise.
  string action_button_label = 4;

  // Additional text displayed with the CTA (call-to-action) button to give
  // context and encourage clicking on the button.
  string action_headline = 5;

  // The image assets of the companion banner used with the ad.
  AdImageAsset companion_banner = 7;
}

// Representation of video out-stream ad format (ad shown alongside a feed
// with automatic playback, without sound).
message VideoOutstreamAdInfo {
  // The headline of the ad.
  string headline = 3;

  // The description line.
  string description = 4;
}

// Representation of In-feed video ad format.
message InFeedVideoAdInfo {
  // The headline of the ad.
  string headline = 1;

  // First text line for the ad.
  string description1 = 2;

  // Second text line for the ad.
  string description2 = 3;

  // Video thumbnail image to use.
  google.ads.googleads.v16.enums.VideoThumbnailEnum.VideoThumbnail thumbnail =
      4;
}

// A video ad.
message VideoAdInfo {
  // The YouTube video assets used for the ad.
  AdVideoAsset video = 8;

  // Format-specific schema for the different video formats.
  oneof format {
    // Video TrueView in-stream ad format.
    VideoTrueViewInStreamAdInfo in_stream = 2;

    // Video bumper in-stream ad format.
    VideoBumperInStreamAdInfo bumper = 3;

    // Video out-stream ad format.
    VideoOutstreamAdInfo out_stream = 4;

    // Video non-skippable in-stream ad format.
    VideoNonSkippableInStreamAdInfo non_skippable = 5;

    // In-feed video ad format.
    InFeedVideoAdInfo in_feed = 9;
  }
}

// A video responsive ad.
message VideoResponsiveAdInfo {
  // List of text assets used for the short headline. Currently, only a single
  // value for the short headline is supported.
  repeated AdTextAsset headlines = 1;

  // List of text assets used for the long headline.
  // Currently, only a single value for the long headline is supported.
  repeated AdTextAsset long_headlines = 2;

  // List of text assets used for the description.
  // Currently, only a single value for the description is supported.
  repeated AdTextAsset descriptions = 3;

  // List of text assets used for the button, for example, the "Call To Action"
  // button. Currently, only a single value for the button is supported.
  repeated AdTextAsset call_to_actions = 4;

  // List of YouTube video assets used for the ad.
  // Currently, only a single value for the YouTube video asset is supported.
  repeated AdVideoAsset videos = 5;

  // List of image assets used for the companion banner.
  // Currently, only a single value for the companion banner asset is supported.
  repeated AdImageAsset companion_banners = 6;

  // First part of text that appears in the ad with the displayed URL.
  string breadcrumb1 = 7;

  // Second part of text that appears in the ad with the displayed URL.
  string breadcrumb2 = 8;
}

// A responsive search ad.
//
// Responsive search ads let you create an ad that adapts to show more text, and
// more relevant messages, to your customers. Enter multiple headlines and
// descriptions when creating a responsive search ad, and over time, Google Ads
// will automatically test different combinations and learn which combinations
// perform best. By adapting your ad's content to more closely match potential
// customers' search terms, responsive search ads may improve your campaign's
// performance.
//
// More information at https://support.google.com/google-ads/answer/7684791
message ResponsiveSearchAdInfo {
  // List of text assets for headlines. When the ad serves the headlines will
  // be selected from this list.
  repeated AdTextAsset headlines = 1;

  // List of text assets for descriptions. When the ad serves the descriptions
  // will be selected from this list.
  repeated AdTextAsset descriptions = 2;

  // First part of text that can be appended to the URL in the ad.
  optional string path1 = 5;

  // Second part of text that can be appended to the URL in the ad. This field
  // can only be set when `path1` is also set.
  optional string path2 = 6;
}

// A legacy responsive display ad. Ads of this type are labeled 'Responsive ads'
// in the Google Ads UI.
message LegacyResponsiveDisplayAdInfo {
  // The short version of the ad's headline.
  optional string short_headline = 16;

  // The long version of the ad's headline.
  optional string long_headline = 17;

  // The description of the ad.
  optional string description = 18;

  // The business name in the ad.
  optional string business_name = 19;

  // Advertiser's consent to allow flexible color. When true, the ad may be
  // served with different color if necessary. When false, the ad will be served
  // with the specified colors or a neutral color.
  // The default value is `true`.
  // Must be true if `main_color` and `accent_color` are not set.
  optional bool allow_flexible_color = 20;

  // The accent color of the ad in hexadecimal, for example, #ffffff for white.
  // If one of `main_color` and `accent_color` is set, the other is required as
  // well.
  optional string accent_color = 21;

  // The main color of the ad in hexadecimal, for example, #ffffff for white.
  // If one of `main_color` and `accent_color` is set, the other is required as
  // well.
  optional string main_color = 22;

  // The call-to-action text for the ad.
  optional string call_to_action_text = 23;

  // The MediaFile resource name of the logo image used in the ad.
  optional string logo_image = 24;

  // The MediaFile resource name of the square logo image used in the ad.
  optional string square_logo_image = 25;

  // The MediaFile resource name of the marketing image used in the ad.
  optional string marketing_image = 26;

  // The MediaFile resource name of the square marketing image used in the ad.
  optional string square_marketing_image = 27;

  // Specifies which format the ad will be served in. Default is ALL_FORMATS.
  google.ads.googleads.v16.enums.DisplayAdFormatSettingEnum
      .DisplayAdFormatSetting format_setting = 13;

  // Prefix before price. For example, 'as low as'.
  optional string price_prefix = 28;

  // Promotion text used for dynamic formats of responsive ads. For example
  // 'Free two-day shipping'.
  optional string promo_text = 29;
}

// An app ad.
message AppAdInfo {
  // Mandatory ad text.
  AdTextAsset mandatory_ad_text = 1;

  // List of text assets for headlines. When the ad serves the headlines will
  // be selected from this list.
  repeated AdTextAsset headlines = 2;

  // List of text assets for descriptions. When the ad serves the descriptions
  // will be selected from this list.
  repeated AdTextAsset descriptions = 3;

  // List of image assets that may be displayed with the ad.
  repeated AdImageAsset images = 4;

  // List of YouTube video assets that may be displayed with the ad.
  repeated AdVideoAsset youtube_videos = 5;

  // List of media bundle assets that may be used with the ad.
  repeated AdMediaBundleAsset html5_media_bundles = 6;
}

// App engagement ads allow you to write text encouraging a specific action in
// the app, like checking in, making a purchase, or booking a flight.
// They allow you to send users to a specific part of your app where they can
// find what they're looking for easier and faster.
message AppEngagementAdInfo {
  // List of text assets for headlines. When the ad serves the headlines will
  // be selected from this list.
  repeated AdTextAsset headlines = 1;

  // List of text assets for descriptions. When the ad serves the descriptions
  // will be selected from this list.
  repeated AdTextAsset descriptions = 2;

  // List of image assets that may be displayed with the ad.
  repeated AdImageAsset images = 3;

  // List of video assets that may be displayed with the ad.
  repeated AdVideoAsset videos = 4;
}

// App pre-registration ads link to your app or game listing on Google Play, and
// can run on Google Play, on YouTube (in-stream only), and within other apps
// and mobile websites on the Display Network. It will help capture people's
// interest in your app or game and generate an early install base for your app
// or game before a launch.
message AppPreRegistrationAdInfo {
  // List of text assets for headlines. When the ad serves the headlines will
  // be selected from this list.
  repeated AdTextAsset headlines = 1;

  // List of text assets for descriptions. When the ad serves the descriptions
  // will be selected from this list.
  repeated AdTextAsset descriptions = 2;

  // List of image asset IDs whose images may be displayed with the ad.
  repeated AdImageAsset images = 3;

  // List of YouTube video asset IDs whose videos may be displayed with the ad.
  repeated AdVideoAsset youtube_videos = 4;
}

// A legacy app install ad that only can be used by a few select customers.
message LegacyAppInstallAdInfo {
  // The ID of the mobile app.
  optional string app_id = 6;

  // The app store the mobile app is available in.
  google.ads.googleads.v16.enums.LegacyAppInstallAdAppStoreEnum
      .LegacyAppInstallAdAppStore app_store = 2;

  // The headline of the ad.
  optional string headline = 7;

  // The first description line of the ad.
  optional string description1 = 8;

  // The second description line of the ad.
  optional string description2 = 9;
}

// A responsive display ad.
message ResponsiveDisplayAdInfo {
  // Marketing images to be used in the ad. Valid image types are GIF,
  // JPEG, and PNG. The minimum size is 600x314 and the aspect ratio must
  // be 1.91:1 (+-1%). At least one `marketing_image` is required. Combined
  // with `square_marketing_images`, the maximum is 15.
  repeated AdImageAsset marketing_images = 1;

  // Square marketing images to be used in the ad. Valid image types are GIF,
  // JPEG, and PNG. The minimum size is 300x300 and the aspect ratio must
  // be 1:1 (+-1%). At least one square `marketing_image` is required. Combined
  // with `marketing_images`, the maximum is 15.
  repeated AdImageAsset square_marketing_images = 2;

  // Logo images to be used in the ad. Valid image types are GIF,
  // JPEG, and PNG. The minimum size is 512x128 and the aspect ratio must
  // be 4:1 (+-1%). Combined with `square_logo_images`, the maximum is 5.
  repeated AdImageAsset logo_images = 3;

  // Square logo images to be used in the ad. Valid image types are GIF,
  // JPEG, and PNG. The minimum size is 128x128 and the aspect ratio must
  // be 1:1 (+-1%). Combined with `logo_images`, the maximum is 5.
  repeated AdImageAsset square_logo_images = 4;

  // Short format headlines for the ad. The maximum length is 30 characters.
  // At least 1 and max 5 headlines can be specified.
  repeated AdTextAsset headlines = 5;

  // A required long format headline. The maximum length is 90 characters.
  AdTextAsset long_headline = 6;

  // Descriptive texts for the ad. The maximum length is 90 characters. At
  // least 1 and max 5 headlines can be specified.
  repeated AdTextAsset descriptions = 7;

  // Optional YouTube videos for the ad. A maximum of 5 videos can be specified.
  repeated AdVideoAsset youtube_videos = 8;

  // The advertiser/brand name. Maximum display width is 25.
  optional string business_name = 17;

  // The main color of the ad in hexadecimal, for example, #ffffff for white.
  // If one of `main_color` and `accent_color` is set, the other is required as
  // well.
  optional string main_color = 18;

  // The accent color of the ad in hexadecimal, for example, #ffffff for white.
  // If one of `main_color` and `accent_color` is set, the other is required as
  // well.
  optional string accent_color = 19;

  // Advertiser's consent to allow flexible color. When true, the ad may be
  // served with different color if necessary. When false, the ad will be served
  // with the specified colors or a neutral color.
  // The default value is `true`.
  // Must be true if `main_color` and `accent_color` are not set.
  optional bool allow_flexible_color = 20;

  // The call-to-action text for the ad. Maximum display width is 30.
  optional string call_to_action_text = 21;

  // Prefix before price. For example, 'as low as'.
  optional string price_prefix = 22;

  // Promotion text used for dynamic formats of responsive ads. For example
  // 'Free two-day shipping'.
  optional string promo_text = 23;

  // Specifies which format the ad will be served in. Default is ALL_FORMATS.
  google.ads.googleads.v16.enums.DisplayAdFormatSettingEnum
      .DisplayAdFormatSetting format_setting = 16;

  // Specification for various creative controls.
  ResponsiveDisplayAdControlSpec control_spec = 24;
}

// A local ad.
message LocalAdInfo {
  // List of text assets for headlines. When the ad serves the headlines will
  // be selected from this list. At least 1 and at most 5 headlines must be
  // specified.
  repeated AdTextAsset headlines = 1;

  // List of text assets for descriptions. When the ad serves the descriptions
  // will be selected from this list. At least 1 and at most 5 descriptions must
  // be specified.
  repeated AdTextAsset descriptions = 2;

  // List of text assets for call-to-actions. When the ad serves the
  // call-to-actions will be selected from this list. At least 1 and at most
  // 5 call-to-actions must be specified.
  repeated AdTextAsset call_to_actions = 3;

  // List of marketing image assets that may be displayed with the ad. The
  // images must be 314x600 pixels or 320x320 pixels. At least 1 and at most
  // 20 image assets must be specified.
  repeated AdImageAsset marketing_images = 4;

  // List of logo image assets that may be displayed with the ad. The images
  // must be 128x128 pixels and not larger than 120KB. At least 1 and at most 5
  // image assets must be specified.
  repeated AdImageAsset logo_images = 5;

  // List of YouTube video assets that may be displayed with the ad. At least 1
  // and at most 20 video assets must be specified.
  repeated AdVideoAsset videos = 6;

  // First part of optional text that can be appended to the URL in the ad.
  optional string path1 = 9;

  // Second part of optional text that can be appended to the URL in the ad.
  // This field can only be set when `path1` is also set.
  optional string path2 = 10;
}

// A generic type of display ad. The exact ad format is controlled by the
// `display_upload_product_type` field, which determines what kinds of data
// need to be included with the ad.
message DisplayUploadAdInfo {
  // The product type of this ad. See comments on the enum for details.
  google.ads.googleads.v16.enums.DisplayUploadProductTypeEnum
      .DisplayUploadProductType display_upload_product_type = 1;

  // The asset data that makes up the ad.
  oneof media_asset {
    // A media bundle asset to be used in the ad. For information about the
    // media bundle for HTML5_UPLOAD_AD, see
    // https://support.google.com/google-ads/answer/1722096
    // Media bundles that are part of dynamic product types use a special format
    // that needs to be created through the Google Web Designer. See
    // https://support.google.com/webdesigner/answer/7543898 for more
    // information.
    AdMediaBundleAsset media_bundle = 2;
  }
}

// Specification for various creative controls for a responsive display ad.
message ResponsiveDisplayAdControlSpec {
  // Whether the advertiser has opted into the asset enhancements feature.
  bool enable_asset_enhancements = 1;

  // Whether the advertiser has opted into auto-gen video feature.
  bool enable_autogen_video = 2;
}

// A Smart campaign ad.
message SmartCampaignAdInfo {
  // List of text assets, each of which corresponds to a headline when the ad
  // serves. This list consists of a minimum of 3 and up to 15 text assets.
  repeated AdTextAsset headlines = 1;

  // List of text assets, each of which corresponds to a description when the ad
  // serves. This list consists of a minimum of 2 and up to 4 text assets.
  repeated AdTextAsset descriptions = 2;
}

// A call ad.
message CallAdInfo {
  // The country code in the ad.
  string country_code = 1;

  // The phone number in the ad.
  string phone_number = 2;

  // The business name in the ad.
  string business_name = 3;

  // First headline in the ad.
  string headline1 = 11;

  // Second headline in the ad.
  string headline2 = 12;

  // The first line of the ad's description.
  string description1 = 4;

  // The second line of the ad's description.
  string description2 = 5;

  // Whether to enable call tracking for the creative. Enabling call
  // tracking also enables call conversions.
  bool call_tracked = 6;

  // Whether to disable call conversion for the creative.
  // If set to `true`, disables call conversions even when `call_tracked` is
  // `true`.
  // If `call_tracked` is `false`, this field is ignored.
  bool disable_call_conversion = 7;

  // The URL to be used for phone number verification.
  string phone_number_verification_url = 8;

  // The conversion action to attribute a call conversion to. If not set a
  // default conversion action is used. This field only has effect if
  // `call_tracked` is set to `true`. Otherwise this field is ignored.
  string conversion_action = 9;

  // The call conversion behavior of this call ad. It can use its own call
  // conversion setting, inherit the account level setting, or be disabled.
  google.ads.googleads.v16.enums.CallConversionReportingStateEnum
      .CallConversionReportingState conversion_reporting_state = 10;

  // First part of text that can be appended to the URL in the ad. Optional.
  string path1 = 13;

  // Second part of text that can be appended to the URL in the ad. This field
  // can only be set when `path1` is also set. Optional.
  string path2 = 14;
}

// A discovery multi asset ad.
message DiscoveryMultiAssetAdInfo {
  // Marketing image assets to be used in the ad. Valid image types are GIF,
  // JPEG, and PNG. The minimum size is 600x314 and the aspect ratio must
  // be 1.91:1 (+-1%). Required if square_marketing_images is
  // not present. Combined with `square_marketing_images` and
  // `portrait_marketing_images` the maximum is 20.
  repeated AdImageAsset marketing_images = 1;

  // Square marketing image assets to be used in the ad. Valid image types are
  // GIF, JPEG, and PNG. The minimum size is 300x300 and the aspect ratio must
  // be 1:1 (+-1%). Required if marketing_images is not present.  Combined with
  // `marketing_images` and `portrait_marketing_images` the maximum is 20.
  repeated AdImageAsset square_marketing_images = 2;

  // Portrait marketing image assets to be used in the ad. Valid image types are
  // GIF, JPEG, and PNG. The minimum size is 480x600 and the aspect ratio must
  // be 4:5 (+-1%).  Combined with `marketing_images` and
  // `square_marketing_images` the maximum is 20.
  repeated AdImageAsset portrait_marketing_images = 3;

  // Logo image assets to be used in the ad. Valid image types are GIF,
  // JPEG, and PNG. The minimum size is 128x128 and the aspect ratio must be
  // 1:1 (+-1%). At least 1 and max 5 logo images can be specified.
  repeated AdImageAsset logo_images = 4;

  // Headline text asset of the ad. Maximum display width is 30. At least 1 and
  // max 5 headlines can be specified.
  repeated AdTextAsset headlines = 5;

  // The descriptive text of the ad. Maximum display width is 90. At least 1 and
  // max 5 descriptions can be specified.
  repeated AdTextAsset descriptions = 6;

  // The Advertiser/brand name. Maximum display width is 25. Required.
  optional string business_name = 7;

  // Call to action text.
  optional string call_to_action_text = 8;

  // Boolean option that indicates if this ad must be served with lead form.
  optional bool lead_form_only = 9;
}

// A discovery carousel ad.
message DiscoveryCarouselAdInfo {
  // Required. The Advertiser/brand name.
  string business_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Logo image to be used in the ad.  The minimum size is 128x128 and
  // the aspect ratio must be 1:1 (+-1%).
  AdImageAsset logo_image = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Headline of the ad.
  AdTextAsset headline = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The descriptive text of the ad.
  AdTextAsset description = 4 [(google.api.field_behavior) = REQUIRED];

  // Call to action text.
  string call_to_action_text = 5;

  // Required. Carousel cards that will display with the ad. Min 2 max 10.
  repeated AdDiscoveryCarouselCardAsset carousel_cards = 6
      [(google.api.field_behavior) = REQUIRED];
}

// A discovery video responsive ad.
message DiscoveryVideoResponsiveAdInfo {
  // List of text assets used for the short headline.
  repeated AdTextAsset headlines = 1;

  // List of text assets used for the long headline.
  repeated AdTextAsset long_headlines = 2;

  // List of text assets used for the description.
  repeated AdTextAsset descriptions = 3;

  // List of YouTube video assets used for the ad.
  repeated AdVideoAsset videos = 4;

  // Logo image to be used in the ad. Valid image types are GIF, JPEG, and PNG.
  // The minimum size is 128x128 and the aspect ratio must be 1:1 (+-1%).
  repeated AdImageAsset logo_images = 5;

  // First part of text that appears in the ad with the displayed URL.
  string breadcrumb1 = 6;

  // Second part of text that appears in the ad with the displayed URL.
  string breadcrumb2 = 7;

  // Required. The advertiser/brand name.
  AdTextAsset business_name = 8 [(google.api.field_behavior) = REQUIRED];

  // Assets of type CallToActionAsset used for the "Call To Action" button.
  repeated AdCallToActionAsset call_to_actions = 9;
}

// A Demand Gen product ad.
message DemandGenProductAdInfo {
  // Required. Text asset used for the short headline.
  optional AdTextAsset headline = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Text asset used for the description.
  optional AdTextAsset description = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Logo image to be used in the ad. Valid image types are GIF, JPEG,
  // and PNG. The minimum size is 128x128 and the aspect ratio must be 1:1
  // (+-1%).
  optional AdImageAsset logo_image = 3 [(google.api.field_behavior) = REQUIRED];

  // First part of text that appears in the ad with the displayed URL.
  string breadcrumb1 = 4;

  // Second part of text that appears in the ad with the displayed URL.
  string breadcrumb2 = 5;

  // Required. The advertiser/brand name.
  AdTextAsset business_name = 6 [(google.api.field_behavior) = REQUIRED];

  // Asset of type CallToActionAsset used for the "Call To Action" button.
  optional AdCallToActionAsset call_to_action = 7;
}
