// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/common/custom_parameter.proto";
import "google/ads/googleads/v16/common/feed_common.proto";
import "google/ads/googleads/v16/enums/app_store.proto";
import "google/ads/googleads/v16/enums/call_conversion_reporting_state.proto";
import "google/ads/googleads/v16/enums/price_extension_price_qualifier.proto";
import "google/ads/googleads/v16/enums/price_extension_price_unit.proto";
import "google/ads/googleads/v16/enums/price_extension_type.proto";
import "google/ads/googleads/v16/enums/promotion_extension_discount_modifier.proto";
import "google/ads/googleads/v16/enums/promotion_extension_occasion.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "ExtensionsProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing extension types.

// Represents an App extension.
message AppFeedItem {
  // The visible text displayed when the link is rendered in an ad.
  // This string must not be empty, and the length of this string should
  // be between 1 and 25, inclusive.
  optional string link_text = 9;

  // The store-specific ID for the target application.
  // This string must not be empty.
  optional string app_id = 10;

  // The application store that the target application belongs to.
  // This field is required.
  google.ads.googleads.v16.enums.AppStoreEnum.AppStore app_store = 3;

  // A list of possible final URLs after all cross domain redirects.
  // This list must not be empty.
  repeated string final_urls = 11;

  // A list of possible final mobile URLs after all cross domain redirects.
  repeated string final_mobile_urls = 12;

  // URL template for constructing a tracking URL. Default value is "{lpurl}".
  optional string tracking_url_template = 13;

  // A list of mappings to be used for substituting URL custom parameter tags in
  // the tracking_url_template, final_urls, and/or final_mobile_urls.
  repeated CustomParameter url_custom_parameters = 7;

  // URL template for appending params to landing page URLs served with parallel
  // tracking.
  optional string final_url_suffix = 14;
}

// Represents a Call extension.
message CallFeedItem {
  // The advertiser's phone number to append to the ad.
  // This string must not be empty.
  optional string phone_number = 7;

  // Uppercase two-letter country code of the advertiser's phone number.
  // This string must not be empty.
  optional string country_code = 8;

  // Indicates whether call tracking is enabled. By default, call tracking is
  // not enabled.
  optional bool call_tracking_enabled = 9;

  // The conversion action to attribute a call conversion to. If not set a
  // default conversion action is used. This field only has effect if
  // call_tracking_enabled is set to true. Otherwise this field is ignored.
  optional string call_conversion_action = 10;

  // If true, disable call conversion tracking. call_conversion_action should
  // not be set if this is true. Optional.
  optional bool call_conversion_tracking_disabled = 11;

  // Enum value that indicates whether this call extension uses its own call
  // conversion setting (or just have call conversion disabled), or following
  // the account level setting.
  google.ads.googleads.v16.enums.CallConversionReportingStateEnum
      .CallConversionReportingState call_conversion_reporting_state = 6;
}

// Represents a callout extension.
message CalloutFeedItem {
  // The callout text.
  // The length of this string should be between 1 and 25, inclusive.
  optional string callout_text = 2;
}

// Represents a location extension.
message LocationFeedItem {
  // The name of the business.
  optional string business_name = 9;

  // Line 1 of the business address.
  optional string address_line_1 = 10;

  // Line 2 of the business address.
  optional string address_line_2 = 11;

  // City of the business address.
  optional string city = 12;

  // Province of the business address.
  optional string province = 13;

  // Postal code of the business address.
  optional string postal_code = 14;

  // Country code of the business address.
  optional string country_code = 15;

  // Phone number of the business.
  optional string phone_number = 16;
}

// Represents an affiliate location extension.
message AffiliateLocationFeedItem {
  // The name of the business.
  optional string business_name = 11;

  // Line 1 of the business address.
  optional string address_line_1 = 12;

  // Line 2 of the business address.
  optional string address_line_2 = 13;

  // City of the business address.
  optional string city = 14;

  // Province of the business address.
  optional string province = 15;

  // Postal code of the business address.
  optional string postal_code = 16;

  // Country code of the business address.
  optional string country_code = 17;

  // Phone number of the business.
  optional string phone_number = 18;

  // Id of the retail chain that is advertised as a seller of your product.
  optional int64 chain_id = 19;

  // Name of chain.
  optional string chain_name = 20;
}

// An extension that users can click on to send a text message to the
// advertiser.
message TextMessageFeedItem {
  // The business name to prepend to the message text.
  // This field is required.
  optional string business_name = 6;

  // Uppercase two-letter country code of the advertiser's phone number.
  // This field is required.
  optional string country_code = 7;

  // The advertiser's phone number the message will be sent to. Required.
  optional string phone_number = 8;

  // The text to show in the ad.
  // This field is required.
  optional string text = 9;

  // The message extension_text populated in the messaging app.
  optional string extension_text = 10;
}

// Represents a Price extension.
message PriceFeedItem {
  // Price extension type of this extension.
  google.ads.googleads.v16.enums.PriceExtensionTypeEnum.PriceExtensionType
      type = 1;

  // Price qualifier for all offers of this price extension.
  google.ads.googleads.v16.enums.PriceExtensionPriceQualifierEnum
      .PriceExtensionPriceQualifier price_qualifier = 2;

  // Tracking URL template for all offers of this price extension.
  optional string tracking_url_template = 7;

  // The code of the language used for this price extension.
  optional string language_code = 8;

  // The price offerings in this price extension.
  repeated PriceOffer price_offerings = 5;

  // Tracking URL template for all offers of this price extension.
  optional string final_url_suffix = 9;
}

// Represents one price offer in a price extension.
message PriceOffer {
  // Header text of this offer.
  optional string header = 7;

  // Description text of this offer.
  optional string description = 8;

  // Price value of this offer.
  Money price = 3;

  // Price unit for this offer.
  google.ads.googleads.v16.enums.PriceExtensionPriceUnitEnum
      .PriceExtensionPriceUnit unit = 4;

  // A list of possible final URLs after all cross domain redirects.
  repeated string final_urls = 9;

  // A list of possible final mobile URLs after all cross domain redirects.
  repeated string final_mobile_urls = 10;
}

// Represents a Promotion extension.
message PromotionFeedItem {
  // A freeform description of what the promotion is targeting.
  // This field is required.
  optional string promotion_target = 16;

  // Enum that modifies the qualification of the discount.
  google.ads.googleads.v16.enums.PromotionExtensionDiscountModifierEnum
      .PromotionExtensionDiscountModifier discount_modifier = 2;

  // Start date of when the promotion is eligible to be redeemed.
  optional string promotion_start_date = 19;

  // Last date when the promotion is eligible to be redeemed.
  optional string promotion_end_date = 20;

  // The occasion the promotion was intended for.
  // If an occasion is set, the redemption window will need to fall within
  // the date range associated with the occasion.
  google.ads.googleads.v16.enums.PromotionExtensionOccasionEnum
      .PromotionExtensionOccasion occasion = 9;

  // A list of possible final URLs after all cross domain redirects.
  // This field is required.
  repeated string final_urls = 21;

  // A list of possible final mobile URLs after all cross domain redirects.
  repeated string final_mobile_urls = 22;

  // URL template for constructing a tracking URL.
  optional string tracking_url_template = 23;

  // A list of mappings to be used for substituting URL custom parameter tags in
  // the tracking_url_template, final_urls, and/or final_mobile_urls.
  repeated CustomParameter url_custom_parameters = 13;

  // URL template for appending params to landing page URLs served with parallel
  // tracking.
  optional string final_url_suffix = 24;

  // The language of the promotion.
  // Represented as BCP 47 language tag.
  optional string language_code = 25;

  // Discount type, can be percentage off or amount off.
  oneof discount_type {
    // Percentage off discount in the promotion in micros.
    // One million is equivalent to one percent.
    // Either this or money_off_amount is required.
    int64 percent_off = 17;

    // Money amount off for discount in the promotion.
    // Either this or percent_off is required.
    Money money_amount_off = 4;
  }

  // Promotion trigger. Can be by promotion code or promo by eligible order
  // amount.
  oneof promotion_trigger {
    // A code the user should use in order to be eligible for the promotion.
    string promotion_code = 18;

    // The amount the total order needs to be for the user to be eligible for
    // the promotion.
    Money orders_over_amount = 6;
  }
}

// Represents a structured snippet extension.
message StructuredSnippetFeedItem {
  // The header of the snippet.
  // This string must not be empty.
  optional string header = 3;

  // The values in the snippet.
  // The maximum size of this collection is 10.
  repeated string values = 4;
}

// Represents a sitelink.
message SitelinkFeedItem {
  // URL display text for the sitelink.
  // The length of this string should be between 1 and 25, inclusive.
  optional string link_text = 9;

  // First line of the description for the sitelink.
  // If this value is set, line2 must also be set.
  // The length of this string should be between 0 and 35, inclusive.
  optional string line1 = 10;

  // Second line of the description for the sitelink.
  // If this value is set, line1 must also be set.
  // The length of this string should be between 0 and 35, inclusive.
  optional string line2 = 11;

  // A list of possible final URLs after all cross domain redirects.
  repeated string final_urls = 12;

  // A list of possible final mobile URLs after all cross domain redirects.
  repeated string final_mobile_urls = 13;

  // URL template for constructing a tracking URL.
  optional string tracking_url_template = 14;

  // A list of mappings to be used for substituting URL custom parameter tags in
  // the tracking_url_template, final_urls, and/or final_mobile_urls.
  repeated CustomParameter url_custom_parameters = 7;

  // Final URL suffix to be appended to landing page URLs served with
  // parallel tracking.
  optional string final_url_suffix = 15;
}

// Represents a hotel callout extension.
message HotelCalloutFeedItem {
  // The callout text.
  // The length of this string should be between 1 and 25, inclusive.
  optional string text = 3;

  // The language of the hotel callout text.
  // IETF BCP 47 compliant language code.
  optional string language_code = 4;
}

// Represents an advertiser provided image extension.
message ImageFeedItem {
  // Required. Resource name of the image asset.
  string image_asset = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];
}
