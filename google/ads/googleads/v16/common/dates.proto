// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/enums/month_of_year.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "DatesProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing date range message.

// A date range.
message DateRange {
  // The start date, in yyyy-mm-dd format. This date is inclusive.
  optional string start_date = 3;

  // The end date, in yyyy-mm-dd format. This date is inclusive.
  optional string end_date = 4;
}

// The year month range inclusive of the start and end months.
// Eg: A year month range to represent Jan 2020 would be: (Jan 2020, Jan 2020).
message YearMonthRange {
  // The inclusive start year month.
  YearMonth start = 1;

  // The inclusive end year month.
  YearMonth end = 2;
}

// Year month.
message YearMonth {
  // The year (for example, 2020).
  int64 year = 1;

  // The month of the year. (for example, FEBRUARY).
  google.ads.googleads.v16.enums.MonthOfYearEnum.MonthOfYear month = 2;
}
