{"methodConfig": [{"name": [{"service": "google.ads.googleads.v16.services.AccountBudgetProposalService"}, {"service": "google.ads.googleads.v16.services.AccountLinkService"}, {"service": "google.ads.googleads.v16.services.AdGroupAdLabelService"}, {"service": "google.ads.googleads.v16.services.AdGroupAdService"}, {"service": "google.ads.googleads.v16.services.AdGroupAssetService"}, {"service": "google.ads.googleads.v16.services.AdGroupAssetSetService"}, {"service": "google.ads.googleads.v16.services.AdGroupBidModifierService"}, {"service": "google.ads.googleads.v16.services.AdGroupCriterionCustomizerService"}, {"service": "google.ads.googleads.v16.services.AdGroupCriterionLabelService"}, {"service": "google.ads.googleads.v16.services.AdGroupCriterionService"}, {"service": "google.ads.googleads.v16.services.AdGroupCustomizerService"}, {"service": "google.ads.googleads.v16.services.AdGroupExtensionSettingService"}, {"service": "google.ads.googleads.v16.services.AdGroupFeedService"}, {"service": "google.ads.googleads.v16.services.AdGroupLabelService"}, {"service": "google.ads.googleads.v16.services.AdGroupService"}, {"service": "google.ads.googleads.v16.services.AdParameterService"}, {"service": "google.ads.googleads.v16.services.AdService"}, {"service": "google.ads.googleads.v16.services.AssetGroupAssetService"}, {"service": "google.ads.googleads.v16.services.AssetGroupListingGroupFilterService"}, {"service": "google.ads.googleads.v16.services.AssetGroupService"}, {"service": "google.ads.googleads.v16.services.AssetGroupSignalService"}, {"service": "google.ads.googleads.v16.services.AssetService"}, {"service": "google.ads.googleads.v16.services.AssetSetAssetService"}, {"service": "google.ads.googleads.v16.services.AssetSetService"}, {"service": "google.ads.googleads.v16.services.AudienceInsightsService"}, {"service": "google.ads.googleads.v16.services.AudienceService"}, {"service": "google.ads.googleads.v16.services.BatchJobService"}, {"service": "google.ads.googleads.v16.services.BiddingDataExclusionService"}, {"service": "google.ads.googleads.v16.services.BiddingSeasonalityAdjustmentService"}, {"service": "google.ads.googleads.v16.services.BiddingStrategyService"}, {"service": "google.ads.googleads.v16.services.BillingSetupService"}, {"service": "google.ads.googleads.v16.services.BrandSuggestionService"}, {"service": "google.ads.googleads.v16.services.CampaignAssetService"}, {"service": "google.ads.googleads.v16.services.CampaignAssetSetService"}, {"service": "google.ads.googleads.v16.services.CampaignBidModifierService"}, {"service": "google.ads.googleads.v16.services.CampaignBudgetService"}, {"service": "google.ads.googleads.v16.services.CampaignConversionGoalService"}, {"service": "google.ads.googleads.v16.services.CampaignCriterionService"}, {"service": "google.ads.googleads.v16.services.CampaignCustomizerService"}, {"service": "google.ads.googleads.v16.services.CampaignDraftService"}, {"service": "google.ads.googleads.v16.services.CampaignExtensionSettingService"}, {"service": "google.ads.googleads.v16.services.CampaignFeedService"}, {"service": "google.ads.googleads.v16.services.CampaignGroupService"}, {"service": "google.ads.googleads.v16.services.CampaignLabelService"}, {"service": "google.ads.googleads.v16.services.CampaignLifecycleGoalService"}, {"service": "google.ads.googleads.v16.services.CampaignService"}, {"service": "google.ads.googleads.v16.services.CampaignSharedSetService"}, {"service": "google.ads.googleads.v16.services.ConversionActionService"}, {"service": "google.ads.googleads.v16.services.ConversionAdjustmentUploadService"}, {"service": "google.ads.googleads.v16.services.ConversionCustomVariableService"}, {"service": "google.ads.googleads.v16.services.ConversionGoalCampaignConfigService"}, {"service": "google.ads.googleads.v16.services.ConversionUploadService"}, {"service": "google.ads.googleads.v16.services.ConversionValueRuleService"}, {"service": "google.ads.googleads.v16.services.ConversionValueRuleSetService"}, {"service": "google.ads.googleads.v16.services.CustomAudienceService"}, {"service": "google.ads.googleads.v16.services.CustomConversionGoalService"}, {"service": "google.ads.googleads.v16.services.CustomInterestService"}, {"service": "google.ads.googleads.v16.services.CustomerAssetService"}, {"service": "google.ads.googleads.v16.services.CustomerAssetSetService"}, {"service": "google.ads.googleads.v16.services.CustomerClientLinkService"}, {"service": "google.ads.googleads.v16.services.CustomerConversionGoalService"}, {"service": "google.ads.googleads.v16.services.CustomerCustomizerService"}, {"service": "google.ads.googleads.v16.services.CustomerExtensionSettingService"}, {"service": "google.ads.googleads.v16.services.CustomerFeedService"}, {"service": "google.ads.googleads.v16.services.CustomerLabelService"}, {"service": "google.ads.googleads.v16.services.CustomerLifecycleGoalService"}, {"service": "google.ads.googleads.v16.services.CustomerManagerLinkService"}, {"service": "google.ads.googleads.v16.services.CustomerNegativeCriterionService"}, {"service": "google.ads.googleads.v16.services.CustomerService"}, {"service": "google.ads.googleads.v16.services.CustomerSkAdNetworkConversionValueSchemaService"}, {"service": "google.ads.googleads.v16.services.CustomerUserAccessInvitationService"}, {"service": "google.ads.googleads.v16.services.CustomerUserAccessService"}, {"service": "google.ads.googleads.v16.services.CustomizerAttributeService"}, {"service": "google.ads.googleads.v16.services.ExperimentArmService"}, {"service": "google.ads.googleads.v16.services.ExperimentService"}, {"service": "google.ads.googleads.v16.services.ExtensionFeedItemService"}, {"service": "google.ads.googleads.v16.services.FeedItemService"}, {"service": "google.ads.googleads.v16.services.FeedItemSetLinkService"}, {"service": "google.ads.googleads.v16.services.FeedItemSetService"}, {"service": "google.ads.googleads.v16.services.FeedItemTargetService"}, {"service": "google.ads.googleads.v16.services.FeedMappingService"}, {"service": "google.ads.googleads.v16.services.FeedService"}, {"service": "google.ads.googleads.v16.services.GeoTargetConstantService"}, {"service": "google.ads.googleads.v16.services.GoogleAdsFieldService"}, {"service": "google.ads.googleads.v16.services.GoogleAdsService"}, {"service": "google.ads.googleads.v16.services.IdentityVerificationService"}, {"service": "google.ads.googleads.v16.services.InvoiceService"}, {"service": "google.ads.googleads.v16.services.KeywordPlanAdGroupKeywordService"}, {"service": "google.ads.googleads.v16.services.KeywordPlanAdGroupService"}, {"service": "google.ads.googleads.v16.services.KeywordPlanCampaignKeywordService"}, {"service": "google.ads.googleads.v16.services.KeywordPlanCampaignService"}, {"service": "google.ads.googleads.v16.services.KeywordPlanIdeaService"}, {"service": "google.ads.googleads.v16.services.KeywordPlanService"}, {"service": "google.ads.googleads.v16.services.KeywordThemeConstantService"}, {"service": "google.ads.googleads.v16.services.LabelService"}, {"service": "google.ads.googleads.v16.services.OfflineUserDataJobService"}, {"service": "google.ads.googleads.v16.services.PaymentsAccountService"}, {"service": "google.ads.googleads.v16.services.ProductLinkInvitationService"}, {"service": "google.ads.googleads.v16.services.ProductLinkService"}, {"service": "google.ads.googleads.v16.services.ReachPlanService"}, {"service": "google.ads.googleads.v16.services.RecommendationService"}, {"service": "google.ads.googleads.v16.services.RecommendationSubscriptionService"}, {"service": "google.ads.googleads.v16.services.RemarketingActionService"}, {"service": "google.ads.googleads.v16.services.SharedCriterionService"}, {"service": "google.ads.googleads.v16.services.SharedSetService"}, {"service": "google.ads.googleads.v16.services.SmartCampaignSettingService"}, {"service": "google.ads.googleads.v16.services.SmartCampaignSuggestService"}, {"service": "google.ads.googleads.v16.services.ThirdPartyAppAnalyticsLinkService"}, {"service": "google.ads.googleads.v16.services.TravelAssetSuggestionService"}, {"service": "google.ads.googleads.v16.services.UserDataService"}, {"service": "google.ads.googleads.v16.services.UserListService"}], "timeout": "14400s", "retryPolicy": {"initialBackoff": "5s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}