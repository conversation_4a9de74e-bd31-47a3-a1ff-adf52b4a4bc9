// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/simulation.proto";
import "google/ads/googleads/v18/enums/simulation_modification_method.proto";
import "google/ads/googleads/v18/enums/simulation_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignSimulationProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the campaign simulation resource.

// A campaign simulation. Supported combinations of advertising
// channel type, simulation type and simulation modification
// method is detailed below respectively.
//
// * SEARCH - CPC_BID - UNIFORM
// * SEARCH - CPC_BID - SCALING
// * SEARCH - TARGET_CPA - UNIFORM
// * SEARCH - TARGET_CPA - SCALING
// * SEARCH - TARGET_ROAS - UNIFORM
// * SEARCH - TARGET_IMPRESSION_SHARE - UNIFORM
// * SEARCH - BUDGET - UNIFORM
// * SHOPPING - BUDGET - UNIFORM
// * SHOPPING - TARGET_ROAS - UNIFORM
// * MULTI_CHANNEL - TARGET_CPA - UNIFORM
// * MULTI_CHANNEL - TARGET_ROAS - UNIFORM
// * DISCOVERY - TARGET_CPA - DEFAULT
// * DISPLAY - TARGET_CPA - UNIFORM
// * PERFORMANCE_MAX - TARGET_CPA - UNIFORM
// * PERFORMANCE_MAX - TARGET_ROAS - UNIFORM
// * PERFORMANCE_MAX - BUDGET - UNIFORM
message CampaignSimulation {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignSimulation"
    pattern: "customers/{customer_id}/campaignSimulations/{campaign_id}~{type}~{modification_method}~{start_date}~{end_date}"
  };

  // Output only. The resource name of the campaign simulation.
  // Campaign simulation resource names have the form:
  //
  // `customers/{customer_id}/campaignSimulations/{campaign_id}~{type}~{modification_method}~{start_date}~{end_date}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignSimulation"
    }
  ];

  // Output only. Campaign id of the simulation.
  int64 campaign_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The field that the simulation modifies.
  google.ads.googleads.v18.enums.SimulationTypeEnum.SimulationType type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. How the simulation modifies the field.
  google.ads.googleads.v18.enums.SimulationModificationMethodEnum
      .SimulationModificationMethod modification_method = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. First day on which the simulation is based, in YYYY-MM-DD
  // format.
  string start_date = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last day on which the simulation is based, in YYYY-MM-DD
  // format
  string end_date = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of simulation points.
  oneof point_list {
    // Output only. Simulation points if the simulation type is CPC_BID.
    google.ads.googleads.v18.common.CpcBidSimulationPointList
        cpc_bid_point_list = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Simulation points if the simulation type is TARGET_CPA.
    google.ads.googleads.v18.common.TargetCpaSimulationPointList
        target_cpa_point_list = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Simulation points if the simulation type is TARGET_ROAS.
    google.ads.googleads.v18.common.TargetRoasSimulationPointList
        target_roas_point_list = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Simulation points if the simulation type is
    // TARGET_IMPRESSION_SHARE.
    google.ads.googleads.v18.common.TargetImpressionShareSimulationPointList
        target_impression_share_point_list = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Simulation points if the simulation type is BUDGET.
    google.ads.googleads.v18.common.BudgetSimulationPointList
        budget_point_list = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
