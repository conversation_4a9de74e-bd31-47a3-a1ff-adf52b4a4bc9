// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/criteria.proto";
import "google/ads/googleads/v18/common/extensions.proto";
import "google/ads/googleads/v18/enums/extension_type.proto";
import "google/ads/googleads/v18/enums/feed_item_status.proto";
import "google/ads/googleads/v18/enums/feed_item_target_device.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ExtensionFeedItemProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the ExtensionFeedItem resource.

// An extension feed item.
message ExtensionFeedItem {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ExtensionFeedItem"
    pattern: "customers/{customer_id}/extensionFeedItems/{feed_item_id}"
  };

  // Immutable. The resource name of the extension feed item.
  // Extension feed item resource names have the form:
  //
  // `customers/{customer_id}/extensionFeedItems/{feed_item_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ExtensionFeedItem"
    }
  ];

  // Output only. The ID of this feed item. Read-only.
  optional int64 id = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The extension type of the extension feed item.
  // This field is read-only.
  google.ads.googleads.v18.enums.ExtensionTypeEnum.ExtensionType
      extension_type = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Start time in which this feed item is effective and can begin serving. The
  // time is in the customer's time zone.
  // The format is "YYYY-MM-DD HH:MM:SS".
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  optional string start_date_time = 26;

  // End time in which this feed item is no longer effective and will stop
  // serving. The time is in the customer's time zone.
  // The format is "YYYY-MM-DD HH:MM:SS".
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  optional string end_date_time = 27;

  // List of non-overlapping schedules specifying all time intervals
  // for which the feed item may serve. There can be a maximum of 6 schedules
  // per day.
  repeated google.ads.googleads.v18.common.AdScheduleInfo ad_schedules = 16;

  // The targeted device.
  google.ads.googleads.v18.enums.FeedItemTargetDeviceEnum.FeedItemTargetDevice
      device = 17;

  // The targeted geo target constant.
  optional string targeted_geo_target_constant = 30
      [(google.api.resource_reference) = {
        type: "googleads.googleapis.com/GeoTargetConstant"
      }];

  // The targeted keyword.
  google.ads.googleads.v18.common.KeywordInfo targeted_keyword = 22;

  // Output only. Status of the feed item.
  // This field is read-only.
  google.ads.googleads.v18.enums.FeedItemStatusEnum.FeedItemStatus status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Extension type.
  oneof extension {
    // Sitelink.
    google.ads.googleads.v18.common.SitelinkFeedItem sitelink_feed_item = 2;

    // Structured snippet extension.
    google.ads.googleads.v18.common.StructuredSnippetFeedItem
        structured_snippet_feed_item = 3;

    // App extension.
    google.ads.googleads.v18.common.AppFeedItem app_feed_item = 7;

    // Call extension.
    google.ads.googleads.v18.common.CallFeedItem call_feed_item = 8;

    // Callout extension.
    google.ads.googleads.v18.common.CalloutFeedItem callout_feed_item = 9;

    // Text message extension.
    google.ads.googleads.v18.common.TextMessageFeedItem text_message_feed_item =
        10;

    // Price extension.
    google.ads.googleads.v18.common.PriceFeedItem price_feed_item = 11;

    // Promotion extension.
    google.ads.googleads.v18.common.PromotionFeedItem promotion_feed_item = 12;

    // Output only. Location extension. Locations are synced from a Business
    // Profile into a feed. This field is read-only.
    google.ads.googleads.v18.common.LocationFeedItem location_feed_item = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Affiliate location extension. Feed locations are populated
    // by Google Ads based on a chain ID. This field is read-only.
    google.ads.googleads.v18.common.AffiliateLocationFeedItem
        affiliate_location_feed_item = 15
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Hotel Callout extension.
    google.ads.googleads.v18.common.HotelCalloutFeedItem
        hotel_callout_feed_item = 23;

    // Immutable. Advertiser provided image extension.
    google.ads.googleads.v18.common.ImageFeedItem image_feed_item = 31
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // Targeting at either the campaign or ad group level. Feed items that target
  // a campaign or ad group will only serve with that resource.
  oneof serving_resource_targeting {
    // The targeted campaign.
    string targeted_campaign = 28 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }];

    // The targeted ad group.
    string targeted_ad_group = 29 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }];
  }
}
