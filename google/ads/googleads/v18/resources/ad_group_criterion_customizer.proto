// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/customizer_value.proto";
import "google/ads/googleads/v18/enums/customizer_value_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupCriterionCustomizerProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// A customizer value for the associated CustomizerAttribute at the
// AdGroupCriterion level.
message AdGroupCriterionCustomizer {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupCriterionCustomizer"
    pattern: "customers/{customer_id}/adGroupCriterionCustomizers/{ad_group_id}~{criterion_id}~{customizer_attribute_id}"
  };

  // Immutable. The resource name of the ad group criterion customizer.
  // Ad group criterion customizer resource names have the form:
  //
  // `customers/{customer_id}/adGroupCriterionCustomizers/{ad_group_id}~{criterion_id}~{customizer_attribute_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupCriterionCustomizer"
    }
  ];

  // Immutable. The ad group criterion to which the customizer attribute is
  // linked. It must be a keyword criterion.
  optional string ad_group_criterion = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupCriterion"
    }
  ];

  // Required. Immutable. The customizer attribute which is linked to the ad
  // group criterion.
  string customizer_attribute = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomizerAttribute"
    }
  ];

  // Output only. The status of the ad group criterion customizer.
  google.ads.googleads.v18.enums.CustomizerValueStatusEnum.CustomizerValueStatus
      status = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The value to associate with the customizer attribute at this
  // level. The value must be of the type specified for the CustomizerAttribute.
  google.ads.googleads.v18.common.CustomizerValue value = 5
      [(google.api.field_behavior) = REQUIRED];
}
