// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/ad_strength.proto";
import "google/ads/googleads/v18/enums/asset_group_primary_status.proto";
import "google/ads/googleads/v18/enums/asset_group_primary_status_reason.proto";
import "google/ads/googleads/v18/enums/asset_group_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// An asset group.
// AssetGroupAsset is used to link an asset to the asset group.
// AssetGroupSignal is used to associate a signal to an asset group.
message AssetGroup {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AssetGroup"
    pattern: "customers/{customer_id}/assetGroups/{asset_group_id}"
  };

  // Immutable. The resource name of the asset group.
  // Asset group resource names have the form:
  //
  // `customers/{customer_id}/assetGroups/{asset_group_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroup"
    }
  ];

  // Output only. The ID of the asset group.
  int64 id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The campaign with which this asset group is associated.
  // The asset which is linked to the asset group.
  string campaign = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Required. Name of the asset group. Required. It must have a minimum length
  // of 1 and maximum length of 128. It must be unique under a campaign.
  string name = 3 [(google.api.field_behavior) = REQUIRED];

  // A list of final URLs after all cross domain redirects. In performance max,
  // by default, the urls are eligible for expansion unless opted out.
  repeated string final_urls = 4;

  // A list of final mobile URLs after all cross domain redirects. In
  // performance max, by default, the urls are eligible for expansion
  // unless opted out.
  repeated string final_mobile_urls = 5;

  // The status of the asset group.
  google.ads.googleads.v18.enums.AssetGroupStatusEnum.AssetGroupStatus status =
      6;

  // Output only. The primary status of the asset group. Provides insights into
  // why an asset group is not serving or not serving optimally.
  google.ads.googleads.v18.enums.AssetGroupPrimaryStatusEnum
      .AssetGroupPrimaryStatus primary_status = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides reasons into why an asset group is not serving or not
  // serving optimally. It will be empty when the asset group is serving without
  // issues.
  repeated google.ads.googleads.v18.enums.AssetGroupPrimaryStatusReasonEnum
      .AssetGroupPrimaryStatusReason primary_status_reasons = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // First part of text that may appear appended to the url displayed in
  // the ad.
  string path1 = 7;

  // Second part of text that may appear appended to the url displayed in
  // the ad. This field can only be set when path1 is set.
  string path2 = 8;

  // Output only. Overall ad strength of this asset group.
  google.ads.googleads.v18.enums.AdStrengthEnum.AdStrength ad_strength = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
