// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/policy.proto";
import "google/ads/googleads/v18/enums/ad_group_ad_primary_status.proto";
import "google/ads/googleads/v18/enums/ad_group_ad_primary_status_reason.proto";
import "google/ads/googleads/v18/enums/ad_group_ad_status.proto";
import "google/ads/googleads/v18/enums/ad_strength.proto";
import "google/ads/googleads/v18/enums/asset_automation_status.proto";
import "google/ads/googleads/v18/enums/asset_automation_type.proto";
import "google/ads/googleads/v18/enums/policy_approval_status.proto";
import "google/ads/googleads/v18/enums/policy_review_status.proto";
import "google/ads/googleads/v18/resources/ad.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the ad group ad resource.

// An ad group ad.
message AdGroupAd {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupAd"
    pattern: "customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}"
  };

  // Immutable. The resource name of the ad.
  // Ad group ad resource names have the form:
  //
  // `customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAd"
    }
  ];

  // The status of the ad.
  google.ads.googleads.v18.enums.AdGroupAdStatusEnum.AdGroupAdStatus status = 3;

  // Immutable. The ad group to which the ad belongs.
  optional string ad_group = 9 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Immutable. The ad.
  Ad ad = 5 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Policy information for the ad.
  AdGroupAdPolicySummary policy_summary = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Overall ad strength for this ad group ad.
  google.ads.googleads.v18.enums.AdStrengthEnum.AdStrength ad_strength = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of recommendations to improve the ad strength. For
  // example, a recommendation could be "Try adding a few more unique headlines
  // or unpinning some assets.".
  repeated string action_items = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource names of labels attached to this ad group ad.
  repeated string labels = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAdLabel"
    }
  ];

  // Output only. Provides aggregated view into why an ad group ad is not
  // serving or not serving optimally.
  google.ads.googleads.v18.enums.AdGroupAdPrimaryStatusEnum
      .AdGroupAdPrimaryStatus primary_status = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides reasons for why an ad group ad is not serving or not
  // serving optimally.
  repeated google.ads.googleads.v18.enums.AdGroupAdPrimaryStatusReasonEnum
      .AdGroupAdPrimaryStatusReason primary_status_reasons = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Settings that control the types of asset automation. See the
  // AssetAutomationTypeEnum documentation for the default opt in/out behavior
  // of each type.
  repeated AdGroupAdAssetAutomationSetting
      ad_group_ad_asset_automation_settings = 18;
}

// Contains policy information for an ad.
message AdGroupAdPolicySummary {
  // Output only. The list of policy findings for this ad.
  repeated google.ads.googleads.v18.common.PolicyTopicEntry
      policy_topic_entries = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Where in the review process this ad is.
  google.ads.googleads.v18.enums.PolicyReviewStatusEnum.PolicyReviewStatus
      review_status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The overall approval status of this ad, calculated based on
  // the status of its individual policy topic entries.
  google.ads.googleads.v18.enums.PolicyApprovalStatusEnum.PolicyApprovalStatus
      approval_status = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Asset automation setting for an AdGroupAd.
message AdGroupAdAssetAutomationSetting {
  // The asset automation type that this setting configures.
  optional
      google.ads.googleads.v18.enums.AssetAutomationTypeEnum.AssetAutomationType
          asset_automation_type = 1;

  // The opt-in/out status for the specified asset automation type.
  optional google.ads.googleads.v18.enums.AssetAutomationStatusEnum
      .AssetAutomationStatus asset_automation_status = 2;
}
