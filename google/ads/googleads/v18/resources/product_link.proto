// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/linked_product_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ProductLinkProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Represents the data sharing connection between  a Google
// Ads customer and another product.
message ProductLink {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ProductLink"
    pattern: "customers/{customer_id}/productLinks/{product_link_id}"
  };

  // Immutable. Resource name of the product link.
  // ProductLink resource names have the form:
  //
  // `customers/{customer_id}/productLinks/{product_link_id} `
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductLink"
    }
  ];

  // Output only. The ID of the link.
  // This field is read only.
  optional int64 product_link_id = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the linked product.
  google.ads.googleads.v18.enums.LinkedProductTypeEnum.LinkedProductType type =
      3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A product linked to this account.
  oneof linked_product {
    // Immutable. Data partner link.
    DataPartnerIdentifier data_partner = 4
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Google Ads link.
    GoogleAdsIdentifier google_ads = 5
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Google Merchant Center link.
    MerchantCenterIdentifier merchant_center = 12
        [(google.api.field_behavior) = IMMUTABLE];

    // Output only. Advertising Partner link.
    AdvertisingPartnerIdentifier advertising_partner = 13
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// The identifier for Data Partner account.
message DataPartnerIdentifier {
  // Immutable. The customer ID of the Data partner account.
  // This field is required and should not be empty when creating a new
  // data partner link. It is unable to be modified after the creation of
  // the link.
  optional int64 data_partner_id = 1 [(google.api.field_behavior) = IMMUTABLE];
}

// The identifier for Google Ads account.
message GoogleAdsIdentifier {
  // Immutable. The resource name of the Google Ads account.
  // This field is required and should not be empty when creating a new
  // Google Ads link. It is unable to be modified after the creation of
  // the link.
  optional string customer = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];
}

// The identifier for Google Merchant Center account
message MerchantCenterIdentifier {
  // Immutable. The customer ID of the Google Merchant Center account.
  // This field is required and should not be empty when creating a new
  // Merchant Center link. It is unable to be modified after the creation of
  // the link.
  optional int64 merchant_center_id = 1
      [(google.api.field_behavior) = IMMUTABLE];
}

// The identifier for the Advertising Partner Google Ads account.
message AdvertisingPartnerIdentifier {
  // Output only. The resource name of the advertising partner Google Ads
  // account. This field is required and should not be empty when creating a new
  // Advertising Partner link. It is unable to be modified after the creation of
  // the link.
  optional string customer = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];
}
