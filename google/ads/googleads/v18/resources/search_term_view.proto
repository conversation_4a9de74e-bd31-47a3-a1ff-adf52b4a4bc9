// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/search_term_targeting_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "SearchTermViewProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the SearchTermView resource.

// A search term view with metrics aggregated by search term at the ad group
// level.
message SearchTermView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/SearchTermView"
    pattern: "customers/{customer_id}/searchTermViews/{campaign_id}~{ad_group_id}~{query}"
  };

  // Output only. The resource name of the search term view.
  // Search term view resource names have the form:
  //
  // `customers/{customer_id}/searchTermViews/{campaign_id}~{ad_group_id}~{URL-base64_search_term}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/SearchTermView"
    }
  ];

  // Output only. The search term.
  optional string search_term = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ad group the search term served in.
  optional string ad_group = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. Indicates whether the search term is currently one of your
  // targeted or excluded keywords.
  google.ads.googleads.v18.enums.SearchTermTargetingStatusEnum
      .SearchTermTargetingStatus status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
