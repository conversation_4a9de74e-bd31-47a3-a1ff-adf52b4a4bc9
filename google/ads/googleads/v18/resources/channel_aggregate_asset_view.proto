// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/advertising_channel_type.proto";
import "google/ads/googleads/v18/enums/asset_field_type.proto";
import "google/ads/googleads/v18/enums/asset_source.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ChannelAggregateAssetViewProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the ChannelAggregateAsset resource.

// A channel-level aggregate asset view that shows where the asset is linked,
// performamce of the asset and stats.
message ChannelAggregateAssetView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ChannelAggregateAssetView"
    pattern: "customers/{customer_id}/channelAggregateAssetViews/{advertising_channel_type}~{asset_id}~{asset_source}~{field_type}"
  };

  // Output only. The resource name of the channel aggregate asset view.
  // Channel aggregate asset view resource names have the form:
  //
  // `customers/{customer_id}/channelAggregateAssetViews/{ChannelAssetV2.advertising_channel_type}~{ChannelAssetV2.asset_id}~{ChannelAssetV2.asset_source}~{ChannelAssetV2.field_type}"`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ChannelAggregateAssetView"
    }
  ];

  // Output only. Channel in which the asset served.
  optional google.ads.googleads.v18.enums.AdvertisingChannelTypeEnum
      .AdvertisingChannelType advertising_channel_type = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the asset.
  optional string asset = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // Output only. Source of the asset link.
  optional google.ads.googleads.v18.enums.AssetSourceEnum.AssetSource
      asset_source = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. FieldType of the asset.
  optional google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType
      field_type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
