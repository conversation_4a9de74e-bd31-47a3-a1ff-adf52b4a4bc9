// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/text_label.proto";
import "google/ads/googleads/v18/enums/label_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "LabelProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// A label.
message Label {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Label"
    pattern: "customers/{customer_id}/labels/{label_id}"
  };

  // Immutable. Name of the resource.
  // Label resource names have the form:
  // `customers/{customer_id}/labels/{label_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Label" }
  ];

  // Output only. ID of the label. Read only.
  optional int64 id = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the label.
  //
  // This field is required and should not be empty when creating a new label.
  //
  // The length of this string should be between 1 and 80, inclusive.
  optional string name = 7;

  // Output only. Status of the label. Read only.
  google.ads.googleads.v18.enums.LabelStatusEnum.LabelStatus status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // A type of label displaying text on a colored background.
  google.ads.googleads.v18.common.TextLabel text_label = 5;
}
