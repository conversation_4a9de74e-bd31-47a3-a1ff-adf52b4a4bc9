// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/placement_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "PerformanceMaxPlacementViewProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the Performance Max placement view resource.

// A view with impression metrics for Performance Max campaign placements.
message PerformanceMaxPlacementView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/PerformanceMaxPlacementView"
    pattern: "customers/{customer_id}/performanceMaxPlacementViews/{base_64_placement}"
    plural: "performanceMaxPlacementViews"
    singular: "performanceMaxPlacementView"
  };

  // Output only. The resource name of the Performance Max placement view.
  // Performance Max placement view resource names have the form:
  //
  // `customers/{customer_id}/performanceMaxPlacementViews/{base_64_placement}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/PerformanceMaxPlacementView"
    }
  ];

  // Output only. The default placement string, such as the website URL, mobile
  // application ID, or a YouTube video ID.
  optional string placement = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name displayed to represent the placement, such as the URL
  // name for websites, YouTube video name for YouTube videos, and translated
  // mobile app name for mobile apps.
  optional string display_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL of the placement, for example, website, link to the mobile
  // application in app store, or a YouTube video URL.
  optional string target_url = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the placement. Possible values for Performance Max
  // placements are WEBSITE, MOBILE_APPLICATION, or YOUTUBE_VIDEO.
  google.ads.googleads.v18.enums.PlacementTypeEnum.PlacementType
      placement_type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
