// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/user_list_customer_type_category.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "UserListCustomerTypeProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// A user list customer type
message UserListCustomerType {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/UserListCustomerType"
    pattern: "customers/{customer_id}/userListCustomerTypes/{user_list_id}~{semantic_label}"
  };

  // Immutable. The resource name of the user list customer type
  // User list customer type resource names have the form:
  // `customers/{customer_id}/userListCustomerTypes/{user_list_id}~{customer_type_category}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserListCustomerType"
    }
  ];

  // Immutable. The resource name for the user list this user list customer type
  // is associated with
  string user_list = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserList"
    }
  ];

  // Immutable. The user list customer type category
  google.ads.googleads.v18.enums.UserListCustomerTypeCategoryEnum
      .UserListCustomerTypeCategory customer_type_category = 3
      [(google.api.field_behavior) = IMMUTABLE];
}
