// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/account_link_status.proto";
import "google/ads/googleads/v18/enums/linked_account_type.proto";
import "google/ads/googleads/v18/enums/mobile_app_vendor.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AccountLinkProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Represents the data sharing connection between a Google Ads account and
// another account
message AccountLink {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AccountLink"
    pattern: "customers/{customer_id}/accountLinks/{account_link_id}"
  };

  // Immutable. Resource name of the account link.
  // AccountLink resource names have the form:
  // `customers/{customer_id}/accountLinks/{account_link_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AccountLink"
    }
  ];

  // Output only. The ID of the link.
  // This field is read only.
  optional int64 account_link_id = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The status of the link.
  google.ads.googleads.v18.enums.AccountLinkStatusEnum.AccountLinkStatus
      status = 3;

  // Output only. The type of the linked account.
  google.ads.googleads.v18.enums.LinkedAccountTypeEnum.LinkedAccountType type =
      4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // An account linked to this Google Ads account.
  oneof linked_account {
    // Immutable. A third party app analytics link.
    ThirdPartyAppAnalyticsLinkIdentifier third_party_app_analytics = 5
        [(google.api.field_behavior) = IMMUTABLE];
  }
}

// The identifiers of a Third Party App Analytics Link.
message ThirdPartyAppAnalyticsLinkIdentifier {
  // Immutable. The ID of the app analytics provider.
  // This field should not be empty when creating a new third
  // party app analytics link. It is unable to be modified after the creation of
  // the link.
  optional int64 app_analytics_provider_id = 4
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. A string that uniquely identifies a mobile application from
  // which the data was collected to the Google Ads API. For iOS, the ID string
  // is the 9 digit string that appears at the end of an App Store URL (for
  // example, "*********" for "Gmail" whose App Store link is
  // https://apps.apple.com/us/app/gmail-email-by-google/id*********). For
  // Android, the ID string is the application's package name (for example,
  // "com.google.android.gm" for "Gmail" given Google Play link
  // https://play.google.com/store/apps/details?id=com.google.android.gm)
  // This field should not be empty when creating a new third
  // party app analytics link. It is unable to be modified after the creation of
  // the link.
  optional string app_id = 5 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The vendor of the app.
  // This field should not be empty when creating a new third
  // party app analytics link. It is unable to be modified after the creation of
  // the link.
  google.ads.googleads.v18.enums.MobileAppVendorEnum.MobileAppVendor
      app_vendor = 3 [(google.api.field_behavior) = IMMUTABLE];
}
