// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/data_link_status.proto";
import "google/ads/googleads/v18/enums/data_link_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "DataLinkProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Represents the data sharing connection between  a Google
// Ads customer and another product's data.
message DataLink {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/DataLink"
    pattern: "customers/{customer_id}/dataLinks/{product_link_id}~{data_link_id}"
  };

  // Immutable. Resource name of the product data link.
  // DataLink resource names have the form:
  //
  // `customers/{customer_id}/datalinks/{product_link_id}~{data_link_id}}
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/DataLink"
    }
  ];

  // Output only. The ID of the link.
  // This field is read only.
  optional int64 product_link_id = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the data link.
  // This field is read only.
  optional int64 data_link_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the data.
  google.ads.googleads.v18.enums.DataLinkTypeEnum.DataLinkType type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the data link.
  google.ads.googleads.v18.enums.DataLinkStatusEnum.DataLinkStatus status = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Data linked to this account.
  oneof data_link_entity {
    // Immutable. A data link to YouTube video.
    YoutubeVideoIdentifier youtube_video = 6
        [(google.api.field_behavior) = IMMUTABLE];
  }
}

// The identifier for YouTube video
message YoutubeVideoIdentifier {
  // Immutable. The ID of the hosting channel of the video. This is a string
  // value with “UC” prefix. For example, "UCK8sQmJBp8GCxrOtXWBpyEA".
  optional string channel_id = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The ID of the video associated with the video link. This is the
  // 11 character string value used in the YouTube video URL. For example, video
  // ID is jV1vkHv4zq8 from the YouTube video URL
  // "https://www.youtube.com/watch?v=jV1vkHv4zq8&t=2s".
  optional string video_id = 2 [(google.api.field_behavior) = IMMUTABLE];
}
