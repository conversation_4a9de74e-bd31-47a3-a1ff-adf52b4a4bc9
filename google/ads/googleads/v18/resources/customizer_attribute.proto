// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/customizer_attribute_status.proto";
import "google/ads/googleads/v18/enums/customizer_attribute_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomizerAttributeProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// A customizer attribute.
// Use CustomerCustomizer, CampaignCustomizer, AdGroupCustomizer, or
// AdGroupCriterionCustomizer to associate a customizer attribute and
// set its value at the customer, campaign, ad group, or ad group criterion
// level, respectively.
message CustomizerAttribute {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomizerAttribute"
    pattern: "customers/{customer_id}/customizerAttributes/{customizer_attribute_id}"
  };

  // Immutable. The resource name of the customizer attribute.
  // Customizer Attribute resource names have the form:
  //
  // `customers/{customer_id}/customizerAttributes/{customizer_attribute_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomizerAttribute"
    }
  ];

  // Output only. The ID of the customizer attribute.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Name of the customizer attribute. Required. It must
  // have a minimum length of 1 and maximum length of 40. Name of an enabled
  // customizer attribute must be unique (case insensitive).
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Immutable. The type of the customizer attribute.
  google.ads.googleads.v18.enums.CustomizerAttributeTypeEnum
      .CustomizerAttributeType type = 4
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The status of the customizer attribute.
  google.ads.googleads.v18.enums.CustomizerAttributeStatusEnum
      .CustomizerAttributeStatus status = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
