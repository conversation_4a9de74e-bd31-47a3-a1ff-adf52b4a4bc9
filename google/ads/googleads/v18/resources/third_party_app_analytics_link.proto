// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ThirdPartyAppAnalyticsLinkProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// A data sharing connection, allowing the import of third party app analytics
// into a Google Ads Customer.
message ThirdPartyAppAnalyticsLink {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ThirdPartyAppAnalyticsLink"
    pattern: "customers/{customer_id}/thirdPartyAppAnalyticsLinks/{customer_link_id}"
  };

  // Immutable. The resource name of the third party app analytics link.
  // Third party app analytics link resource names have the form:
  //
  // `customers/{customer_id}/thirdPartyAppAnalyticsLinks/{account_link_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ThirdPartyAppAnalyticsLink"
    }
  ];

  // Output only. The shareable link ID that should be provided to the third
  // party when setting up app analytics. This is able to be regenerated using
  // regenerate method in the ThirdPartyAppAnalyticsLinkService.
  optional string shareable_link_id = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
