// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/criteria.proto";
import "google/ads/googleads/v18/enums/bid_modifier_source.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupBidModifierProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the ad group bid modifier resource.

// Represents an ad group bid modifier.
message AdGroupBidModifier {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupBidModifier"
    pattern: "customers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}"
  };

  // Immutable. The resource name of the ad group bid modifier.
  // Ad group bid modifier resource names have the form:
  //
  // `customers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupBidModifier"
    }
  ];

  // Immutable. The ad group to which this criterion belongs.
  optional string ad_group = 13 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. The ID of the criterion to bid modify.
  //
  // This field is ignored for mutates.
  optional int64 criterion_id = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The modifier for the bid when the criterion matches. The modifier must be
  // in the range: 0.1 - 10.0. The range is 1.0 - 6.0 for PreferredContent.
  // Use 0 to opt out of a Device type.
  optional double bid_modifier = 15;

  // Output only. The base ad group from which this draft/trial adgroup bid
  // modifier was created. If ad_group is a base ad group then this field will
  // be equal to ad_group. If the ad group was created in the draft or trial and
  // has no corresponding base ad group, then this field will be null. This
  // field is readonly.
  optional string base_ad_group = 16 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. Bid modifier source.
  google.ads.googleads.v18.enums.BidModifierSourceEnum.BidModifierSource
      bid_modifier_source = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The criterion of this ad group bid modifier.
  //
  // Required in create operations starting in V5.
  oneof criterion {
    // Immutable. Criterion for hotel date selection (default dates versus user
    // selected).
    google.ads.googleads.v18.common.HotelDateSelectionTypeInfo
        hotel_date_selection_type = 5 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Criterion for number of days prior to the stay the booking is
    // being made.
    google.ads.googleads.v18.common.HotelAdvanceBookingWindowInfo
        hotel_advance_booking_window = 6
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Criterion for length of hotel stay in nights.
    google.ads.googleads.v18.common.HotelLengthOfStayInfo hotel_length_of_stay =
        7 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Criterion for day of the week the booking is for.
    google.ads.googleads.v18.common.HotelCheckInDayInfo hotel_check_in_day = 8
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. A device criterion.
    google.ads.googleads.v18.common.DeviceInfo device = 11
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Criterion for a hotel check-in date range.
    google.ads.googleads.v18.common.HotelCheckInDateRangeInfo
        hotel_check_in_date_range = 17
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
