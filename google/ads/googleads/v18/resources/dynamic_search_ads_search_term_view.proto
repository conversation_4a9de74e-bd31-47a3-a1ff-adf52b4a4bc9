// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "DynamicSearchAdsSearchTermViewProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the Dynamic Search Ads Search Term View resource.

// A dynamic search ads search term view.
message DynamicSearchAdsSearchTermView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/DynamicSearchAdsSearchTermView"
    pattern: "customers/{customer_id}/dynamicSearchAdsSearchTermViews/{ad_group_id}~{search_term_fingerprint}~{headline_fingerprint}~{landing_page_fingerprint}~{page_url_fingerprint}"
  };

  // Output only. The resource name of the dynamic search ads search term view.
  // Dynamic search ads search term view resource names have the form:
  //
  // `customers/{customer_id}/dynamicSearchAdsSearchTermViews/{ad_group_id}~{search_term_fingerprint}~{headline_fingerprint}~{landing_page_fingerprint}~{page_url_fingerprint}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/DynamicSearchAdsSearchTermView"
    }
  ];

  // Output only. Search term
  //
  // This field is read-only.
  optional string search_term = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The dynamically generated headline of the Dynamic Search Ad.
  //
  // This field is read-only.
  optional string headline = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The dynamically selected landing page URL of the impression.
  //
  // This field is read-only.
  optional string landing_page = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of page feed item served for the impression.
  //
  // This field is read-only.
  optional string page_url = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if query matches a negative keyword.
  //
  // This field is read-only.
  optional bool has_negative_keyword = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if query is added to targeted keywords.
  //
  // This field is read-only.
  optional bool has_matching_keyword = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if query matches a negative url.
  //
  // This field is read-only.
  optional bool has_negative_url = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
