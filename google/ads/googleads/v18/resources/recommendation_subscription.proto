// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/recommendation_subscription_status.proto";
import "google/ads/googleads/v18/enums/recommendation_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "RecommendationSubscriptionProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the RecommendationSubscription resource.

// Recommendation Subscription resource
message RecommendationSubscription {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/RecommendationSubscription"
    pattern: "customers/{customer_id}/recommendationSubscriptions/{recommendation_type}"
  };

  // Immutable. The resource name of the recommendation subscription.
  //
  // `customers/{customer_id}/recommendationSubscriptions/{recommendation_type}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/RecommendationSubscription"
    }
  ];

  // Required. Immutable. The type of recommendation subscribed to.
  google.ads.googleads.v18.enums.RecommendationTypeEnum.RecommendationType
      type = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Time in seconds when the subscription was first created. The
  // datetime is in the customer's time zone and in "yyyy-MM-dd HH:mm:ss"
  // format.
  optional string create_date_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Contains the time in microseconds, when the Recommendation
  // Subscription was last updated. The datetime is in the customer's time zone
  // and in "yyyy-MM-dd HH:mm:ss.ssssss" format.
  optional string modify_date_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Status of the subscription, either enabled or paused.
  optional google.ads.googleads.v18.enums.RecommendationSubscriptionStatusEnum
      .RecommendationSubscriptionStatus status = 5
      [(google.api.field_behavior) = REQUIRED];
}
