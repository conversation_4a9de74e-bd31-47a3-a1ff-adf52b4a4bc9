// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "KeywordThemeConstantProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the Smart Campaign keyword theme constant resource.

// A Smart Campaign keyword theme constant.
message KeywordThemeConstant {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/KeywordThemeConstant"
    pattern: "keywordThemeConstants/{express_category_id}~{express_sub_category_id}"
  };

  // Output only. The resource name of the keyword theme constant.
  // Keyword theme constant resource names have the form:
  //
  // `keywordThemeConstants/{keyword_theme_id}~{sub_keyword_theme_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/KeywordThemeConstant"
    }
  ];

  // Output only. The ISO-3166 Alpha-2 country code of the constant, eg. "US".
  // To display and query matching purpose, the keyword theme needs to be
  // localized.
  optional string country_code = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ISO-639-1 language code with 2 letters of the constant,
  // eg. "en". To display and query matching purpose, the keyword theme needs to
  // be localized.
  optional string language_code = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name of the keyword theme or sub keyword theme.
  optional string display_name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
