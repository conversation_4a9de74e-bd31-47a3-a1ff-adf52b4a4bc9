// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/product_availability.proto";
import "google/ads/googleads/v18/enums/product_channel.proto";
import "google/ads/googleads/v18/enums/product_channel_exclusivity.proto";
import "google/ads/googleads/v18/enums/product_condition.proto";
import "google/ads/googleads/v18/enums/product_issue_severity.proto";
import "google/ads/googleads/v18/enums/product_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ShoppingProductProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the ShoppingProduct resource.

// A shopping product from Google Merchant Center that can be advertised by
// campaigns.
//
// The resource returns currently existing products from Google Merchant Center
// accounts linked with the customer. A campaign includes a product by
// specifying its merchant id (or, if available, the Multi Client Account id of
// the merchant) in the `ShoppingSetting`, and can limit the inclusion to
// products having a specified feed label. Standard Shopping campaigns can also
// limit the inclusion through a `campaign_criterion.listing_scope`.
//
// Queries to this resource specify a scope:
// Account:
//   - Filters on campaigns or ad groups are not specified.
//   - All products from the linked Google Merchant Center accounts are
//     returned.
//   - Metrics and some fields (see the per-field documentation) are aggregated
//     across all Shopping and Performance Max campaigns that include a product.
// Campaign:
//   - An equality filter on `campaign` is specified. Supported campaign types
//     are Shopping, Performance Max, Demand Gen, Video.
//   - Only products that are included by the specified campaign are returned.
//   - Metrics and some fields (see the per-field documentation) are restricted
//     to the specified campaign.
// Ad group:
//   - An equality filter on `ad group` and `campaign` is specified. Supported
//     campaign types are Shopping, Demand Gen, Video.
//   - Only products that are included by the specified campaign are returned.
//   - Metrics and some fields (see the per-field documentation) are restricted
//     to the specified ad group.
// Note that segmentation by date segments is not permitted and will return
// UNSUPPORTED_DATE_SEGMENTATION error. On the other hand, filtering on date
// segments is allowed.
message ShoppingProduct {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ShoppingProduct"
    pattern: "customers/{customer_id}/shoppingProducts/{merchant_center_id}~{channel}~{language_code}~{feed_label}~{item_id}"
    plural: "shoppingProducts"
    singular: "shoppingProduct"
  };

  // An issue affecting whether a product can show in ads.
  message ProductIssue {
    // Output only. The error code that identifies the issue.
    string error_code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The severity of the issue in Google Ads.
    google.ads.googleads.v18.enums.ProductIssueSeverityEnum.ProductIssueSeverity
        ads_severity = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The name of the product's attribute, if any, that triggered
    // the issue.
    optional string attribute_name = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The short description of the issue in English.
    string description = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The detailed description of the issue in English.
    string detail = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The URL of the Help Center article for the issue.
    string documentation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. List of upper-case two-letter ISO 3166-1 codes of the
    // regions affected by the issue. If empty, all regions are affected.
    repeated string affected_regions = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The resource name of the shopping product.
  // Shopping product resource names have the form:
  //
  // `customers/{customer_id}/shoppingProducts/{merchant_center_id}~{channel}~{language_code}~{feed_label}~{item_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ShoppingProduct"
    }
  ];

  // Output only. The id of the merchant that owns the product.
  int64 merchant_center_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The product channel describing the locality of the product.
  google.ads.googleads.v18.enums.ProductChannelEnum.ProductChannel channel = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The language code as provided by the merchant, in BCP 47
  // format.
  string language_code = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The product feed label as provided by the merchant.
  string feed_label = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The item id of the product as provided by the merchant.
  string item_id = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The id of the Multi Client Account of the merchant, if
  // present.
  optional int64 multi_client_account_id = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The title of the product as provided by the merchant.
  optional string title = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The brand of the product as provided by the merchant.
  optional string brand = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The price of the product in micros as provided by the
  // merchant, in the currency specified in `currency_code` (e.g. $2.97 is
  // reported as 2970000).
  optional int64 price_micros = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The currency code as provided by the merchant, in ISO 4217
  // format.
  optional string currency_code = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The channel exclusivity of the product as provided by the
  // merchant.
  optional google.ads.googleads.v18.enums.ProductChannelExclusivityEnum
      .ProductChannelExclusivity channel_exclusivity = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The condition of the product as provided by the merchant.
  optional google.ads.googleads.v18.enums.ProductConditionEnum.ProductCondition
      condition = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The availability of the product as provided by the merchant.
  optional
      google.ads.googleads.v18.enums.ProductAvailabilityEnum.ProductAvailability
          availability = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Upper-case two-letter ISO 3166-1 code of the regions where the
  // product is intended to be shown in ads.
  repeated string target_countries = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The custom attribute 0 of the product as provided by the
  // merchant.
  optional string custom_attribute0 = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The custom attribute 1 of the product as provided by the
  // merchant.
  optional string custom_attribute1 = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The custom attribute 2 of the product as provided by the
  // merchant.
  optional string custom_attribute2 = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The custom attribute 3 of the product as provided by the
  // merchant.
  optional string custom_attribute3 = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The custom attribute 4 of the product as provided by the
  // merchant.
  optional string custom_attribute4 = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The category level 1 of the product.
  optional string category_level1 = 21 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductCategoryConstant"
    }
  ];

  // Output only. The category level 2 of the product.
  optional string category_level2 = 22 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductCategoryConstant"
    }
  ];

  // Output only. The category level 3 of the product.
  optional string category_level3 = 23 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductCategoryConstant"
    }
  ];

  // Output only. The category level 4 of the product.
  optional string category_level4 = 24 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductCategoryConstant"
    }
  ];

  // Output only. The category level 5 of the product.
  optional string category_level5 = 25 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductCategoryConstant"
    }
  ];

  // Output only. The product type level 1 as provided by the merchant.
  optional string product_type_level1 = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The product type level 2 as provided by the merchant.
  optional string product_type_level2 = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The product type level 3 as provided by the merchant.
  optional string product_type_level3 = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The product type level 4 as provided by the merchant.
  optional string product_type_level4 = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The product type level 5 as provided by the merchant.
  optional string product_type_level5 = 30
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The effective maximum cost-per-click (effective max. CPC) of
  // the product. This field is available only if the query specifies the
  // campaign or ad group scope, and if the campaign uses manual bidding. The
  // value is the highest bid set for the product in product groups across all
  // enabled ad groups. It represents the most you're willing to pay for a click
  // on the product. This field can take up to 24 hours to update.
  optional int64 effective_max_cpc_micros = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status that indicates whether the product can show in ads.
  // The value of this field is restricted to the scope specified in the query,
  // see the documentation of the resource.
  // This field can take up to 24 hours to update.
  google.ads.googleads.v18.enums.ProductStatusEnum.ProductStatus status = 32
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of issues affecting whether the product can show in
  // ads. The value of this field is restricted to the scope specified in the
  // query, see the documentation of the resource. This field can take up to 24
  // hours to update.
  repeated ProductIssue issues = 33 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A campaign that includes the product.
  // This field is selectable only in the campaign scope, which requires an
  // equality filter on `campaign`.
  optional string campaign = 34 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. An ad group of a campaign that includes the product.
  // This field is selectable only in the ad group scope, which requires an
  // equality filter on `campaign` and `ad_group`.
  optional string ad_group = 35 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];
}
