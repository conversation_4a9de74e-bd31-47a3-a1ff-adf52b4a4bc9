// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/local_services_conversation_type.proto";
import "google/ads/googleads/v18/enums/local_services_participant_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesLeadConversationProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the local services lead conversation resource.

// Data from Local Services Lead Conversation.
// Contains details of Lead Conversation which is generated when user calls,
// messages or books service from advertiser. These are appended to a Lead.
// More info: https://ads.google.com/local-services-ads
message LocalServicesLeadConversation {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/LocalServicesLeadConversation"
    pattern: "customers/{customer_id}/localServicesLeadConversations/{local_services_lead_conversation_id}"
  };

  // Output only. The resource name of the local services lead conversation
  // data. Local Services Lead Conversation resource name have the form
  //
  // `customers/{customer_id}/localServicesLeadConversation/{local_services_lead_conversation_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LocalServicesLeadConversation"
    }
  ];

  // Output only. ID of this Lead Conversation.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of GLS lead conversation, EMAIL, MESSAGE, PHONE_CALL,
  // SMS, etc.
  google.ads.googleads.v18.enums.LocalServicesLeadConversationTypeEnum
      .ConversationType conversation_channel = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of participant in the lead conversation, ADVERTISER or
  // CONSUMER.
  google.ads.googleads.v18.enums.LocalServicesParticipantTypeEnum
      .ParticipantType participant_type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource name of Lead associated to the Lead Conversation.
  string lead = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LocalServicesLead"
    }
  ];

  // Output only. The date time at which lead conversation was created by Local
  // Services Ads. The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads
  // account's timezone. Examples: "2018-03-05 09:15:00" or "2018-02-01
  // 14:34:30"
  string event_date_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Details of phone call conversation in case of PHONE_CALL.
  optional PhoneCallDetails phone_call_details = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Details of message conversation in case of EMAIL, MESSAGE or
  // SMS.
  optional MessageDetails message_details = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents details of a phone call conversation.
message PhoneCallDetails {
  // Output only. The duration (in milliseconds) of the phone call (end to end).
  int64 call_duration_millis = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL to the call recording audio file.
  string call_recording_url = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents details of text message in case of email, message or SMS.
message MessageDetails {
  // Output only. Textual content of the message.
  string text = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL to the SMS or email attachments. These URLs can be used to
  // download the contents of the attachment by using the developer token.
  repeated string attachment_urls = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
