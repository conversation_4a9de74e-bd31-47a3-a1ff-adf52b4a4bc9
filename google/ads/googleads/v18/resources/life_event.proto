// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/criterion_category_availability.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "LifeEventProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the Life Event resource.

// A life event: a particular interest-based vertical to be targeted to reach
// users when they are in the midst of important life milestones.
message LifeEvent {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/LifeEvent"
    pattern: "customers/{customer_id}/lifeEvents/{life_event_id}"
  };

  // Output only. The resource name of the life event.
  // Life event resource names have the form:
  //
  // `customers/{customer_id}/lifeEvents/{life_event_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LifeEvent"
    }
  ];

  // Output only. The ID of the life event.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the life event, for example,"Recently Moved"
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The parent of the life_event.
  string parent = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LifeEvent"
    }
  ];

  // Output only. True if the life event is launched to all channels and
  // locales.
  bool launched_to_all = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Availability information of the life event.
  repeated google.ads.googleads.v18.common.CriterionCategoryAvailability
      availabilities = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}
