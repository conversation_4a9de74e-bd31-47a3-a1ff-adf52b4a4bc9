// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/campaign_shared_set_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignSharedSetProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the CampaignSharedSet resource.

// CampaignSharedSets are used for managing the shared sets associated with a
// campaign.
message CampaignSharedSet {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignSharedSet"
    pattern: "customers/{customer_id}/campaignSharedSets/{campaign_id}~{shared_set_id}"
  };

  // Immutable. The resource name of the campaign shared set.
  // Campaign shared set resource names have the form:
  //
  // `customers/{customer_id}/campaignSharedSets/{campaign_id}~{shared_set_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignSharedSet"
    }
  ];

  // Immutable. The campaign to which the campaign shared set belongs.
  optional string campaign = 5 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Immutable. The shared set associated with the campaign. This may be a
  // negative keyword shared set of another customer. This customer should be a
  // manager of the other customer, otherwise the campaign shared set will exist
  // but have no serving effect. Only negative keyword shared sets can be
  // associated with Shopping campaigns. Only negative placement shared sets can
  // be associated with Display mobile app campaigns.
  optional string shared_set = 6 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/SharedSet"
    }
  ];

  // Output only. The status of this campaign shared set. Read only.
  google.ads.googleads.v18.enums.CampaignSharedSetStatusEnum
      .CampaignSharedSetStatus status = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
