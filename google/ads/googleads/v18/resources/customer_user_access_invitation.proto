// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/access_invitation_status.proto";
import "google/ads/googleads/v18/enums/access_role.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerUserAccessInvitationProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the CustomerUserAccessInvitation resource.

// Represent an invitation to a new user on this customer account.
message CustomerUserAccessInvitation {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerUserAccessInvitation"
    pattern: "customers/{customer_id}/customerUserAccessInvitations/{invitation_id}"
  };

  // Immutable. Name of the resource.
  // Resource names have the form:
  // `customers/{customer_id}/customerUserAccessInvitations/{invitation_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerUserAccessInvitation"
    }
  ];

  // Output only. The ID of the invitation.
  // This field is read-only.
  int64 invitation_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Access role of the user.
  google.ads.googleads.v18.enums.AccessRoleEnum.AccessRole access_role = 3
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Email address the invitation was sent to.
  // This can differ from the email address of the account
  // that accepts the invite.
  string email_address = 4 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time invitation was created.
  // This field is read-only.
  // The format is "YYYY-MM-DD HH:MM:SS".
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  string creation_date_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Invitation status of the user.
  google.ads.googleads.v18.enums.AccessInvitationStatusEnum
      .AccessInvitationStatus invitation_status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
