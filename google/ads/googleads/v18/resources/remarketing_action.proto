// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/tag_snippet.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "RemarketingActionProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the Remarketing Action resource.

// A remarketing action. A snippet of JavaScript code that will collect the
// product id and the type of page people visited (product page, shopping cart
// page, purchase page, general site visit) on an advertiser's website.
message RemarketingAction {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/RemarketingAction"
    pattern: "customers/{customer_id}/remarketingActions/{remarketing_action_id}"
  };

  // Immutable. The resource name of the remarketing action.
  // Remarketing action resource names have the form:
  //
  // `customers/{customer_id}/remarketingActions/{remarketing_action_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/RemarketingAction"
    }
  ];

  // Output only. Id of the remarketing action.
  optional int64 id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the remarketing action.
  //
  // This field is required and should not be empty when creating new
  // remarketing actions.
  optional string name = 6;

  // Output only. The snippets used for tracking remarketing actions.
  repeated google.ads.googleads.v18.common.TagSnippet tag_snippets = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
