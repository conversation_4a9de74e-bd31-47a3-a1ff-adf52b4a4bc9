// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/budget_delivery_method.proto";
import "google/ads/googleads/v18/enums/budget_period.proto";
import "google/ads/googleads/v18/enums/budget_status.proto";
import "google/ads/googleads/v18/enums/budget_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignBudgetProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the Budget resource.

// A campaign budget.
message CampaignBudget {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignBudget"
    pattern: "customers/{customer_id}/campaignBudgets/{campaign_budget_id}"
  };

  // Immutable. The resource name of the campaign budget.
  // Campaign budget resource names have the form:
  //
  // `customers/{customer_id}/campaignBudgets/{campaign_budget_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignBudget"
    }
  ];

  // Output only. The ID of the campaign budget.
  //
  // A campaign budget is created using the CampaignBudgetService create
  // operation and is assigned a budget ID. A budget ID can be shared across
  // different campaigns; the system will then allocate the campaign budget
  // among different campaigns to get optimum results.
  optional int64 id = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the campaign budget.
  //
  // When creating a campaign budget through CampaignBudgetService, every
  // explicitly shared campaign budget must have a non-null, non-empty name.
  // Campaign budgets that are not explicitly shared derive their name from the
  // attached campaign's name.
  //
  // The length of this string must be between 1 and 255, inclusive,
  // in UTF-8 bytes, (trimmed).
  optional string name = 20;

  // The amount of the budget, in the local currency for the account.
  // Amount is specified in micros, where one million is equivalent to one
  // currency unit. Monthly spend is capped at 30.4 times this amount.
  optional int64 amount_micros = 21;

  // The lifetime amount of the budget, in the local currency for the account.
  // Amount is specified in micros, where one million is equivalent to one
  // currency unit.
  optional int64 total_amount_micros = 22;

  // Output only. The status of this campaign budget. This field is read-only.
  google.ads.googleads.v18.enums.BudgetStatusEnum.BudgetStatus status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The delivery method that determines the rate at which the campaign budget
  // is spent.
  //
  // Defaults to STANDARD if unspecified in a create operation.
  google.ads.googleads.v18.enums.BudgetDeliveryMethodEnum.BudgetDeliveryMethod
      delivery_method = 7;

  // Specifies whether the budget is explicitly shared. Defaults to true if
  // unspecified in a create operation.
  //
  // If true, the budget was created with the purpose of sharing
  // across one or more campaigns.
  //
  // If false, the budget was created with the intention of only being used
  // with a single campaign. The budget's name and status will stay in sync
  // with the campaign's name and status. Attempting to share the budget with a
  // second campaign will result in an error.
  //
  // A non-shared budget can become an explicitly shared. The same operation
  // must also assign the budget a name.
  //
  // A shared campaign budget can never become non-shared.
  optional bool explicitly_shared = 23;

  // Output only. The number of campaigns actively using the budget.
  //
  // This field is read-only.
  optional int64 reference_count = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether there is a recommended budget for this
  // campaign budget.
  //
  // This field is read-only.
  optional bool has_recommended_budget = 25
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The recommended budget amount. If no recommendation is
  // available, this will be set to the budget amount. Amount is specified in
  // micros, where one million is equivalent to one currency unit.
  //
  // This field is read-only.
  optional int64 recommended_budget_amount_micros = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Period over which to spend the budget. Defaults to DAILY if not
  // specified.
  google.ads.googleads.v18.enums.BudgetPeriodEnum.BudgetPeriod period = 13
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The estimated change in weekly clicks if the recommended
  // budget is applied.
  //
  // This field is read-only.
  optional int64 recommended_budget_estimated_change_weekly_clicks = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The estimated change in weekly cost in micros if the
  // recommended budget is applied. One million is equivalent to one currency
  // unit.
  //
  // This field is read-only.
  optional int64 recommended_budget_estimated_change_weekly_cost_micros = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The estimated change in weekly interactions if the recommended
  // budget is applied.
  //
  // This field is read-only.
  optional int64 recommended_budget_estimated_change_weekly_interactions = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The estimated change in weekly views if the recommended budget
  // is applied.
  //
  // This field is read-only.
  optional int64 recommended_budget_estimated_change_weekly_views = 30
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The type of the campaign budget.
  google.ads.googleads.v18.enums.BudgetTypeEnum.BudgetType type = 18
      [(google.api.field_behavior) = IMMUTABLE];

  // ID of the portfolio bidding strategy that this shared campaign budget
  // is aligned with. When a bidding strategy and a campaign budget are aligned,
  // they are attached to the same set of campaigns. After a campaign budget is
  // aligned with a bidding strategy, campaigns that are added to the campaign
  // budget must also use the aligned bidding strategy.
  int64 aligned_bidding_strategy_id = 31;
}
