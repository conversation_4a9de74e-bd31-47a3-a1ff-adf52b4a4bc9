// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/product_category_level.proto";
import "google/ads/googleads/v18/enums/product_category_state.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ProductCategoryConstantProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the ProductCategoryConstant resource.

// A Product Category.
message ProductCategoryConstant {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ProductCategoryConstant"
    pattern: "productCategoryConstants/{level}~{category_id}"
    plural: "productCategoryConstants"
    singular: "productCategoryConstant"
  };

  // Localization for the product category.
  message ProductCategoryLocalization {
    // Output only. Upper-case two-letter ISO 3166-1 country code of the
    // localized category.
    string region_code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Two-letter ISO 639-1 language code of the localized
    // category.
    string language_code = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The name of the category in the specified locale.
    string value = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The resource name of the product category.
  // Product category resource names have the form:
  //
  // `productCategoryConstants/{level}~{category_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductCategoryConstant"
    }
  ];

  // Output only. The ID of the product category.
  //
  // This ID is equivalent to the google_product_category ID as described in
  // this article: https://support.google.com/merchants/answer/6324436.
  int64 category_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource name of the parent product category.
  optional string product_category_constant_parent = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductCategoryConstant"
    }
  ];

  // Output only. Level of the product category.
  google.ads.googleads.v18.enums.ProductCategoryLevelEnum.ProductCategoryLevel
      level = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the product category.
  google.ads.googleads.v18.enums.ProductCategoryStateEnum.ProductCategoryState
      state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of all available localizations of the product category.
  repeated ProductCategoryLocalization localizations = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
