// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/manager_link_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerClientLinkProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the CustomerClientLink resource.

// Represents customer client link relationship.
message CustomerClientLink {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerClientLink"
    pattern: "customers/{customer_id}/customerClientLinks/{client_customer_id}~{manager_link_id}"
  };

  // Immutable. Name of the resource.
  // CustomerClientLink resource names have the form:
  // `customers/{customer_id}/customerClientLinks/{client_customer_id}~{manager_link_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerClientLink"
    }
  ];

  // Immutable. The client customer linked to this customer.
  optional string client_customer = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];

  // Output only. This is uniquely identifies a customer client link. Read only.
  optional int64 manager_link_id = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // This is the status of the link between client and manager.
  google.ads.googleads.v18.enums.ManagerLinkStatusEnum.ManagerLinkStatus
      status = 5;

  // The visibility of the link. Users can choose whether or not to see hidden
  // links in the Google Ads UI.
  // Default value is false
  optional bool hidden = 9;
}
