// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/operating_system_version_operator_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "OperatingSystemVersionConstantProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the operating system version constant resource.

// A mobile operating system version or a range of versions, depending on
// `operator_type`. List of available mobile platforms at
// https://developers.google.com/google-ads/api/reference/data/codes-formats#mobile-platforms
message OperatingSystemVersionConstant {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/OperatingSystemVersionConstant"
    pattern: "operatingSystemVersionConstants/{criterion_id}"
  };

  // Output only. The resource name of the operating system version constant.
  // Operating system version constant resource names have the form:
  //
  // `operatingSystemVersionConstants/{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/OperatingSystemVersionConstant"
    }
  ];

  // Output only. The ID of the operating system version.
  optional int64 id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the operating system.
  optional string name = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The OS Major Version number.
  optional int32 os_major_version = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The OS Minor Version number.
  optional int32 os_minor_version = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Determines whether this constant represents a single version
  // or a range of versions.
  google.ads.googleads.v18.enums.OperatingSystemVersionOperatorTypeEnum
      .OperatingSystemVersionOperatorType operator_type = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
