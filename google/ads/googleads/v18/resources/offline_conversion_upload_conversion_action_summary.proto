// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/offline_conversion_diagnostic_status_enum.proto";
import "google/ads/googleads/v18/enums/offline_event_upload_client_enum.proto";
import "google/ads/googleads/v18/resources/offline_conversion_upload_client_summary.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "OfflineConversionUploadConversionActionSummaryProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Offline conversion upload summary at conversion action level.
message OfflineConversionUploadConversionActionSummary {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/OfflineConversionUploadConversionActionSummary"
    pattern: "customers/{customer_id}/offlineConversionUploadConversionActionSummaries/{conversion_type_id}~{client}"
  };

  // Output only. The resource name of the offline conversion upload summary at
  // conversion action level. Offline conversion upload conversion action
  // summary resource names have the form:
  //
  // `customers/{customer_id}/offlineConversionUploadConversionActionSummaries/{conversion_action_id}~{client}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/OfflineConversionUploadConversionActionSummary"
    }
  ];

  // Output only. Client type of the upload event.
  google.ads.googleads.v18.enums.OfflineEventUploadClientEnum
      .OfflineEventUploadClient client = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Conversion action id.
  int64 conversion_action_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the conversion action.
  string conversion_action_name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Overall status for offline conversion upload conversion action
  // summary. Status is generated from most recent calendar day with upload
  // stats.
  google.ads.googleads.v18.enums.OfflineConversionDiagnosticStatusEnum
      .OfflineConversionDiagnosticStatus status = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total count of uploaded events.
  int64 total_event_count = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total count of successful uploaded events.
  int64 successful_event_count = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total count of pending uploaded events.
  int64 pending_event_count = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Date for the latest upload batch. The format is "yyyy-mm-dd
  // hh:mm:ss", and it's in the time zone of the Google Ads account.
  string last_upload_date_time = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Summary of history stats by last N days.
  repeated OfflineConversionSummary daily_summaries = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Summary of history stats by last N jobs.
  repeated OfflineConversionSummary job_summaries = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Details for each error code. Alerts are generated from most
  // recent calendar day with upload stats.
  repeated OfflineConversionAlert alerts = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
