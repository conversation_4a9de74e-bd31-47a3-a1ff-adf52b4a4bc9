// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/linked_product_type.proto";
import "google/ads/googleads/v18/enums/product_link_invitation_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ProductLinkInvitationProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Represents an invitation for data sharing connection between a Google Ads
// account and another account.
message ProductLinkInvitation {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ProductLinkInvitation"
    pattern: "customers/{customer_id}/productLinkInvitations/{customer_invitation_id}"
  };

  // Immutable. The resource name of a product link invitation.
  // Product link invitation resource names have the form:
  //
  // `customers/{customer_id}/productLinkInvitations/{product_link_invitation_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductLinkInvitation"
    }
  ];

  // Output only. The ID of the product link invitation.
  // This field is read only.
  int64 product_link_invitation_id = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the product link invitation.
  // This field is read only.
  google.ads.googleads.v18.enums.ProductLinkInvitationStatusEnum
      .ProductLinkInvitationStatus status = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the invited account.
  // This field is read only and can be used for filtering invitations with
  // {@code GoogleAdsService.SearchGoogleAdsRequest}.
  google.ads.googleads.v18.enums.LinkedProductTypeEnum.LinkedProductType type =
      6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // An account invited to link to this Google Ads account.
  oneof invited_account {
    // Output only. Hotel link invitation.
    HotelCenterLinkInvitationIdentifier hotel_center = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Merchant Center link invitation.
    MerchantCenterLinkInvitationIdentifier merchant_center = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Advertising Partner link invitation.
    AdvertisingPartnerLinkInvitationIdentifier advertising_partner = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// The identifier for Hotel account.
message HotelCenterLinkInvitationIdentifier {
  // Output only. The hotel center id of the hotel account.
  // This field is read only
  int64 hotel_center_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The identifier for Merchant Center Account.
message MerchantCenterLinkInvitationIdentifier {
  // Output only. The Merchant Center id of the Merchant account.
  // This field is read only
  int64 merchant_center_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The identifier for the Advertising Partner Google Ads account.
message AdvertisingPartnerLinkInvitationIdentifier {
  // Immutable. The resource name of the advertising partner Google Ads account.
  // This field is read only.
  optional string customer = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];
}
