// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/enums/google_ads_field_category.proto";
import "google/ads/googleads/v18/enums/google_ads_field_data_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "GoogleAdsFieldProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the Google Ads Field resource.

// A field or resource (artifact) used by GoogleAdsService.
message GoogleAdsField {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/GoogleAdsField"
    pattern: "googleAdsFields/{google_ads_field}"
  };

  // Output only. The resource name of the artifact.
  // Artifact resource names have the form:
  //
  // `googleAdsFields/{name}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/GoogleAdsField"
    }
  ];

  // Output only. The name of the artifact.
  optional string name = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The category of the artifact.
  google.ads.googleads.v18.enums.GoogleAdsFieldCategoryEnum
      .GoogleAdsFieldCategory category = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the artifact can be used in a SELECT clause in search
  // queries.
  optional bool selectable = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the artifact can be used in a WHERE clause in search
  // queries.
  optional bool filterable = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the artifact can be used in a ORDER BY clause in
  // search queries.
  optional bool sortable = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of all resources, segments, and metrics that are
  // selectable with the described artifact.
  repeated string selectable_with = 25
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of all resources that are selectable with the
  // described artifact. Fields from these resources do not segment metrics when
  // included in search queries.
  //
  // This field is only set for artifacts whose category is RESOURCE.
  repeated string attribute_resources = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field lists the names of all metrics that are selectable
  // with the described artifact when it is used in the FROM clause. It is only
  // set for artifacts whose category is RESOURCE.
  repeated string metrics = 27 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field lists the names of all artifacts, whether a segment
  // or another resource, that segment metrics when included in search queries
  // and when the described artifact is used in the FROM clause. It is only set
  // for artifacts whose category is RESOURCE.
  repeated string segments = 28 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Values the artifact can assume if it is a field of type ENUM.
  //
  // This field is only set for artifacts of category SEGMENT or ATTRIBUTE.
  repeated string enum_values = 29 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field determines the operators that can be used with the
  // artifact in WHERE clauses.
  google.ads.googleads.v18.enums.GoogleAdsFieldDataTypeEnum
      .GoogleAdsFieldDataType data_type = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of proto describing the artifact's data type.
  optional string type_url = 30 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the field artifact is repeated.
  optional bool is_repeated = 31 [(google.api.field_behavior) = OUTPUT_ONLY];
}
