// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/common/audience_insights_attribute.proto";
import "google/ads/googleads/v18/common/criteria.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "ContentCreatorInsightsServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the content creator insights service.

// Content Creator Insights Service helps users find information about YouTube
// Creators and their content and how these creators and their audiences can be
// reached with Google Ads. Accessible to allowlisted customers only.
service ContentCreatorInsightsService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns insights for a collection of YouTube Creators and Channels.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  rpc GenerateCreatorInsights(GenerateCreatorInsightsRequest)
      returns (GenerateCreatorInsightsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:generateCreatorInsights"
      body: "*"
    };
  }
}

// Request message for [ContentCreatorInsightsService.GenerateCreatorInsights]
message GenerateCreatorInsightsRequest {
  // The audience attributes (such as Age, Gender, Affinity, and In-Market) and
  // creator attributes (such as creator location and creator's content topics)
  // used to search for top creators.
  message SearchAttributes {
    // Optional. Audience attributes that describe an audience of viewers. This
    // is used to search for creators whose own viewers match the input
    // audience.
    repeated google.ads.googleads.v18.common.AudienceInsightsAttribute
        audience_attributes = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Creator attributes that describe a collection of types of
    // content. This is used to search for creators whose content matches the
    // input creator attributes.
    repeated google.ads.googleads.v18.common.AudienceInsightsAttribute
        creator_attributes = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // A collection of YouTube Channels.
  message YouTubeChannels {
    // Optional. The YouTube Channel IDs to fetch creator insights for.
    repeated google.ads.googleads.v18.common.YouTubeChannelInfo
        youtube_channels = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the customer being planned for.  This is a
  // user-defined value.
  string customer_insights_group = 2 [(google.api.field_behavior) = REQUIRED];

  // A criteria used to search for creators and creator insights.
  oneof criteria {
    // The list of attributes to search for top creators in.
    SearchAttributes search_attributes = 3;

    // The list of YouTube Channel IDs to fetch creator insights for.
    YouTubeChannels search_channels = 4;
  }
}

// Response message for [ContentCreatorInsightsService.GenerateCreatorInsights]
message GenerateCreatorInsightsResponse {
  // A collection of YouTube Creators, each containing a collection of YouTube
  // Channels maintained by the YouTube Creator.
  repeated YouTubeCreatorInsights creator_insights = 1;
}

// A YouTube creator and the insights for this creator.
message YouTubeCreatorInsights {
  // The name of the creator.
  string creator_name = 1;

  // The list of YouTube Channels
  repeated YouTubeChannelInsights creator_channels = 2;
}

// YouTube Channel metrics.
message YouTubeMetrics {
  // The number of subscribers.
  int64 subscriber_count = 1;

  // The total number of views.
  int64 views_count = 2;
}

// YouTube Channel insights, and its metadata (such as channel name and channel
// ID), returned for a creator insights response.
message YouTubeChannelInsights {
  // The name of the YouTube Channel.
  string display_name = 1;

  // The YouTube Channel ID.
  google.ads.googleads.v18.common.YouTubeChannelInfo youtube_channel = 2;

  // The metrics for a YouTube Channel.
  YouTubeMetrics channel_metrics = 3;

  // The types of audiences and demographics associated with a channel's main
  // audience. Audiences and demographics will have a breakdown of subscriber
  // share across dimensions of the same value.
  repeated google.ads.googleads.v18.common.AudienceInsightsAttributeMetadata
      channel_audience_demographics = 4;

  // The attributes associated with the content made by a channel.
  repeated google.ads.googleads.v18.common.AudienceInsightsAttributeMetadata
      channel_attributes = 5;

  // Metadata string associated with the type of channel.
  string channel_type = 6;
}
