// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/ad_group_ad_label.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdLabelServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the Ad Group Ad Label service.

// Service to manage labels on ad group ads.
service AdGroupAdLabelService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates and removes ad group ad labels.
  // Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [LabelError]()
  //   [MutateError]()
  //   [NewResourceCreationError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateAdGroupAdLabels(MutateAdGroupAdLabelsRequest)
      returns (MutateAdGroupAdLabelsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/adGroupAdLabels:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [AdGroupAdLabelService.MutateAdGroupAdLabels][google.ads.googleads.v18.services.AdGroupAdLabelService.MutateAdGroupAdLabels].
message MutateAdGroupAdLabelsRequest {
  // Required. ID of the customer whose ad group ad labels are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on ad group ad labels.
  repeated AdGroupAdLabelOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;
}

// A single operation (create, remove) on an ad group ad label.
message AdGroupAdLabelOperation {
  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new ad group ad
    // label.
    google.ads.googleads.v18.resources.AdGroupAdLabel create = 1;

    // Remove operation: A resource name for the ad group ad label
    // being removed, in this format:
    //
    // `customers/{customer_id}/adGroupAdLabels/{ad_group_id}~{ad_id}~{label_id}`
    string remove = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAdLabel"
    }];
  }
}

// Response message for an ad group ad labels mutate.
message MutateAdGroupAdLabelsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 3;

  // All results for the mutate.
  repeated MutateAdGroupAdLabelResult results = 2;
}

// The result for an ad group ad label mutate.
message MutateAdGroupAdLabelResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/AdGroupAdLabel"
  }];
}
