// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "ShareablePreviewServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Service to generate Shareable Previews.
service ShareablePreviewService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns the requested Shareable Preview.
  rpc GenerateShareablePreviews(GenerateShareablePreviewsRequest)
      returns (GenerateShareablePreviewsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:generateShareablePreviews"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,shareable_previews";
  }
}

// Request message for
// [ShareablePreviewService.GenerateShareablePreviews][google.ads.googleads.v18.services.ShareablePreviewService.GenerateShareablePreviews].
message GenerateShareablePreviewsRequest {
  // Required. The customer creating the shareable previews request.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of shareable previews to generate.
  repeated ShareablePreview shareable_previews = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A shareable preview with its identifier.
message ShareablePreview {
  // Required. Asset group of the shareable preview.
  AssetGroupIdentifier asset_group_identifier = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Asset group of the shareable preview.
message AssetGroupIdentifier {
  // Required. The asset group identifier.
  int64 asset_group_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [ShareablePreviewService.GenerateShareablePreviews][google.ads.googleads.v18.services.ShareablePreviewService.GenerateShareablePreviews].
message GenerateShareablePreviewsResponse {
  // List of generate shareable preview results.
  repeated ShareablePreviewOrError responses = 1;
}

// Result of the generate shareable preview.
message ShareablePreviewOrError {
  // The asset group of the shareable preview.
  AssetGroupIdentifier asset_group_identifier = 3;

  // The shareable preview result or error.
  oneof generate_shareable_preview_response {
    // The shareable preview result.
    ShareablePreviewResult shareable_preview_result = 1;

    // The shareable preview partial failure error.
    google.rpc.Status partial_failure_error = 2;
  }
}

// Message to hold a shareable preview result.
message ShareablePreviewResult {
  // The shareable preview URL.
  string shareable_preview_url = 1;

  // Expiration date time using the ISO-8601 format.
  string expiration_date_time = 2;
}
