// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/product_link.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "ProductLinkServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// This service allows management of links between  a Google
// Ads customer and another product.
service ProductLinkService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates a product link.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc CreateProductLink(CreateProductLinkRequest)
      returns (CreateProductLinkResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/productLinks:create"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,product_link";
  }

  // Removes a product link.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc RemoveProductLink(RemoveProductLinkRequest)
      returns (RemoveProductLinkResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/productLinks:remove"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,resource_name";
  }
}

// Request message for
// [ProductLinkService.CreateProductLink][google.ads.googleads.v18.services.ProductLinkService.CreateProductLink].
message CreateProductLinkRequest {
  // Required. The ID of the customer for which the product link is created.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The product link to be created.
  google.ads.googleads.v18.resources.ProductLink product_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [ProductLinkService.CreateProductLink][google.ads.googleads.v18.services.ProductLinkService.CreateProductLink].
message CreateProductLinkResponse {
  // Returned for successful operations. Resource name of the product link.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ProductLink"
  }];
}

// Request message for
// [ProductLinkService.RemoveProductLink][google.ads.googleads.v18.services.ProductLinkService.RemoveProductLink].
message RemoveProductLinkRequest {
  // Required. The ID of the customer being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Remove operation: A resource name for the product link to remove
  // is expected, in this format:
  //
  // `customers/{customer_id}/productLinks/{product_link_id} `
  string resource_name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductLink"
    }
  ];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// Response message for product link removal.
message RemoveProductLinkResponse {
  // Result for the remove request.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ProductLink"
  }];
}
