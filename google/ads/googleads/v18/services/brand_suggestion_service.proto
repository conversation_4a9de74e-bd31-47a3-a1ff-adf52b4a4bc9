// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/enums/brand_state.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "BrandSuggestionServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the MerchantCenterLink service.

// This service will suggest brands based on a prefix.
service BrandSuggestionService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Rpc to return a list of matching brands based on a prefix for this
  // customer.
  rpc SuggestBrands(SuggestBrandsRequest) returns (SuggestBrandsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:suggestBrands"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,brand_prefix";
  }
}

// Request message for
// [BrandSuggestionService.SuggestBrands][google.ads.googleads.v18.services.BrandSuggestionService.SuggestBrands].
message SuggestBrandsRequest {
  // Required. The ID of the customer onto which to apply the brand suggestion
  // operation.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The prefix of a brand name.
  optional string brand_prefix = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Ids of the brands already selected by advertisers. They will be
  // excluded in response. These are expected to be brand ids not brand names.
  repeated string selected_brands = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [BrandSuggestionService.SuggestBrands][google.ads.googleads.v18.services.BrandSuggestionService.SuggestBrands].
message SuggestBrandsResponse {
  // Generated brand suggestions of verified brands for the given prefix.
  repeated BrandSuggestion brands = 1;
}

// Information of brand suggestion.
message BrandSuggestion {
  // Id for the brand. It would be CKG MID for verified/global scoped brands.
  string id = 1;

  // Name of the brand.
  string name = 2;

  // Urls which uniquely identify the brand.
  repeated string urls = 3;

  // Current state of the brand.
  google.ads.googleads.v18.enums.BrandStateEnum.BrandState state = 4;
}
