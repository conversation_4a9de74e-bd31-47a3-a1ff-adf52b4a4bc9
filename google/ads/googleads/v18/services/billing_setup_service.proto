// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/billing_setup.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "BillingSetupServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the BillingSetup service.

// A service for designating the business entity responsible for accrued costs.
//
// A billing setup is associated with a payments account.  Billing-related
// activity for all billing setups associated with a particular payments account
// will appear on a single invoice generated monthly.
//
// Mutates:
// The REMOVE operation cancels a pending billing setup.
// The CREATE operation creates a new billing setup.
service BillingSetupService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates a billing setup, or cancels an existing billing setup.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [BillingSetupError]()
  //   [DateError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateBillingSetup(MutateBillingSetupRequest)
      returns (MutateBillingSetupResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/billingSetups:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }
}

// Request message for billing setup mutate operations.
message MutateBillingSetupRequest {
  // Required. Id of the customer to apply the billing setup mutate operation
  // to.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform.
  BillingSetupOperation operation = 2 [(google.api.field_behavior) = REQUIRED];
}

// A single operation on a billing setup, which describes the cancellation of an
// existing billing setup.
message BillingSetupOperation {
  // Only one of these operations can be set. "Update" operations are not
  // supported.
  oneof operation {
    // Creates a billing setup. No resource name is expected for the new billing
    // setup.
    google.ads.googleads.v18.resources.BillingSetup create = 2;

    // Resource name of the billing setup to remove. A setup cannot be
    // removed unless it is in a pending state or its scheduled start time is in
    // the future. The resource name looks like
    // `customers/{customer_id}/billingSetups/{billing_id}`.
    string remove = 1 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/BillingSetup"
    }];
  }
}

// Response message for a billing setup operation.
message MutateBillingSetupResponse {
  // A result that identifies the resource affected by the mutate request.
  MutateBillingSetupResult result = 1;
}

// Result for a single billing setup mutate.
message MutateBillingSetupResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/BillingSetup"
  }];
}
