// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/account_link.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "AccountLinkServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// This service allows management of links between Google Ads accounts and other
// accounts.
service AccountLinkService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates an account link.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  //   [ThirdPartyAppAnalyticsLinkError]()
  rpc CreateAccountLink(CreateAccountLinkRequest)
      returns (CreateAccountLinkResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/accountLinks:create"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,account_link";
  }

  // Creates or removes an account link.
  // From V5, create is not supported through
  // AccountLinkService.MutateAccountLink. Use
  // AccountLinkService.CreateAccountLink instead.
  //
  // List of thrown errors:
  //   [AccountLinkError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateAccountLink(MutateAccountLinkRequest)
      returns (MutateAccountLinkResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/accountLinks:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }
}

// Request message for
// [AccountLinkService.CreateAccountLink][google.ads.googleads.v18.services.AccountLinkService.CreateAccountLink].
message CreateAccountLinkRequest {
  // Required. The ID of the customer for which the account link is created.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The account link to be created.
  google.ads.googleads.v18.resources.AccountLink account_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [AccountLinkService.CreateAccountLink][google.ads.googleads.v18.services.AccountLinkService.CreateAccountLink].
message CreateAccountLinkResponse {
  // Returned for successful operations. Resource name of the account link.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/AccountLink"
  }];
}

// Request message for
// [AccountLinkService.MutateAccountLink][google.ads.googleads.v18.services.AccountLinkService.MutateAccountLink].
message MutateAccountLinkRequest {
  // Required. The ID of the customer being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform on the link.
  AccountLinkOperation operation = 2 [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;
}

// A single update on an account link.
message AccountLinkOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The operation to perform.
  oneof operation {
    // Update operation: The account link is expected to have
    // a valid resource name.
    google.ads.googleads.v18.resources.AccountLink update = 2;

    // Remove operation: A resource name for the account link to remove is
    // expected, in this format:
    //
    // `customers/{customer_id}/accountLinks/{account_link_id}`
    string remove = 3 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/AccountLink"
    }];
  }
}

// Response message for account link mutate.
message MutateAccountLinkResponse {
  // Result for the mutate.
  MutateAccountLinkResult result = 1;

  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 2;
}

// The result for the account link mutate.
message MutateAccountLinkResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/AccountLink"
  }];
}
