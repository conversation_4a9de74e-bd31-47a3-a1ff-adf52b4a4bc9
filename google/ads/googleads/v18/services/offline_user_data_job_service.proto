// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/common/offline_user_data.proto";
import "google/ads/googleads/v18/resources/offline_user_data_job.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "OfflineUserDataJobServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the OfflineUserDataJobService.

// Service to manage offline user data jobs.
service OfflineUserDataJobService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates an offline user data job.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [NotAllowlistedError]()
  //   [OfflineUserDataJobError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc CreateOfflineUserDataJob(CreateOfflineUserDataJobRequest)
      returns (CreateOfflineUserDataJobResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/offlineUserDataJobs:create"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,job";
  }

  // Adds operations to the offline user data job.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [OfflineUserDataJobError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc AddOfflineUserDataJobOperations(AddOfflineUserDataJobOperationsRequest)
      returns (AddOfflineUserDataJobOperationsResponse) {
    option (google.api.http) = {
      post: "/v18/{resource_name=customers/*/offlineUserDataJobs/*}:addOperations"
      body: "*"
    };
    option (google.api.method_signature) = "resource_name,operations";
  }

  // Runs the offline user data job.
  //
  // When finished, the long running operation will contain the processing
  // result or failure information, if any.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [OfflineUserDataJobError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc RunOfflineUserDataJob(RunOfflineUserDataJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v18/{resource_name=customers/*/offlineUserDataJobs/*}:run"
      body: "*"
    };
    option (google.api.method_signature) = "resource_name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.ads.googleads.v18.resources.OfflineUserDataJobMetadata"
    };
  }
}

// Request message for
// [OfflineUserDataJobService.CreateOfflineUserDataJob][google.ads.googleads.v18.services.OfflineUserDataJobService.CreateOfflineUserDataJob].
message CreateOfflineUserDataJobRequest {
  // Required. The ID of the customer for which to create an offline user data
  // job.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The offline user data job to be created.
  google.ads.googleads.v18.resources.OfflineUserDataJob job = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;

  // If true, match rate range for the offline user data job is calculated and
  // made available in the resource.
  bool enable_match_rate_range_preview = 5;
}

// Response message for
// [OfflineUserDataJobService.CreateOfflineUserDataJob][google.ads.googleads.v18.services.OfflineUserDataJobService.CreateOfflineUserDataJob].
message CreateOfflineUserDataJobResponse {
  // The resource name of the OfflineUserDataJob.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/OfflineUserDataJob"
  }];
}

// Request message for
// [OfflineUserDataJobService.RunOfflineUserDataJob][google.ads.googleads.v18.services.OfflineUserDataJobService.RunOfflineUserDataJob].
message RunOfflineUserDataJobRequest {
  // Required. The resource name of the OfflineUserDataJob to run.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/OfflineUserDataJob"
    }
  ];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 2;
}

// Request message for
// [OfflineUserDataJobService.AddOfflineUserDataJobOperations][google.ads.googleads.v18.services.OfflineUserDataJobService.AddOfflineUserDataJobOperations].
message AddOfflineUserDataJobOperationsRequest {
  // Required. The resource name of the OfflineUserDataJob.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/OfflineUserDataJob"
    }
  ];

  // True to enable partial failure for the offline user data job.
  optional bool enable_partial_failure = 4;

  // True to enable warnings for the offline user data job. When enabled, a
  // warning will not block the OfflineUserDataJobOperation, and will also
  // return warning messages about malformed field values.
  optional bool enable_warnings = 6;

  // Required. The list of operations to be done.
  repeated OfflineUserDataJobOperation operations = 3
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 5;
}

// Operation to be made for the AddOfflineUserDataJobOperationsRequest.
message OfflineUserDataJobOperation {
  // Operation to be made for the AddOfflineUserDataJobOperationsRequest.
  oneof operation {
    // Add the provided data to the transaction. Data cannot be retrieved after
    // being uploaded.
    google.ads.googleads.v18.common.UserData create = 1;

    // Remove the provided data from the transaction. Data cannot be retrieved
    // after being uploaded.
    google.ads.googleads.v18.common.UserData remove = 2;

    // Remove all previously provided data. This is only supported for Customer
    // Match.
    bool remove_all = 3;
  }
}

// Response message for
// [OfflineUserDataJobService.AddOfflineUserDataJobOperations][google.ads.googleads.v18.services.OfflineUserDataJobService.AddOfflineUserDataJobOperations].
message AddOfflineUserDataJobOperationsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 1;

  // Non blocking errors that pertain to operation failures in the warnings
  // mode. Returned only when enable_warnings = true.
  google.rpc.Status warning = 2;
}
