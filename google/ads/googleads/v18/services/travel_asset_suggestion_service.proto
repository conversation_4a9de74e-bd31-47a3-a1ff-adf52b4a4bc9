// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/enums/asset_field_type.proto";
import "google/ads/googleads/v18/enums/call_to_action_type.proto";
import "google/ads/googleads/v18/enums/hotel_asset_suggestion_status.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "TravelAssetSuggestionServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Service to retrieve Travel asset suggestions.
service TravelAssetSuggestionService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns Travel Asset suggestions. Asset
  // suggestions are returned on a best-effort basis. There are no guarantees
  // that all possible asset types will be returned for any given hotel
  // property.
  rpc SuggestTravelAssets(SuggestTravelAssetsRequest)
      returns (SuggestTravelAssetsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:suggestTravelAssets"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,language_option";
  }
}

// Request message for
// [TravelAssetSuggestionService.SuggestTravelAssets][google.ads.googleads.v18.services.TravelAssetSuggestionService.SuggestTravelAssets].
message SuggestTravelAssetsRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The language specifications in BCP 47 format (for example, en-US,
  // zh-CN, etc.) for the asset suggestions. Text will be in this language.
  // Usually matches one of the campaign target languages.
  string language_option = 2 [(google.api.field_behavior) = REQUIRED];

  // The Google Maps Place IDs of hotels for which assets are requested. See
  // https://developers.google.com/places/web-service/place-id for more
  // information.
  repeated string place_ids = 4;
}

// Response message for
// [TravelAssetSuggestionService.SuggestTravelAssets][google.ads.googleads.v18.services.TravelAssetSuggestionService.SuggestTravelAssets].
message SuggestTravelAssetsResponse {
  // Asset suggestions for each place ID submitted in the request.
  repeated HotelAssetSuggestion hotel_asset_suggestions = 1;
}

// Message containing the asset suggestions for a hotel.
message HotelAssetSuggestion {
  // Google Places ID of the hotel.
  string place_id = 1;

  // Suggested final URL for an AssetGroup.
  string final_url = 2;

  // Hotel name in requested language.
  string hotel_name = 3;

  // Call to action type.
  google.ads.googleads.v18.enums.CallToActionTypeEnum.CallToActionType
      call_to_action = 4;

  // Text assets such as headline, description, etc.
  repeated HotelTextAsset text_assets = 5;

  // Image assets such as landscape/portrait/square, etc.
  repeated HotelImageAsset image_assets = 6;

  // The status of the hotel asset suggestion.
  google.ads.googleads.v18.enums.HotelAssetSuggestionStatusEnum
      .HotelAssetSuggestionStatus status = 7;
}

// A single text asset suggestion for a hotel.
message HotelTextAsset {
  // Asset text in requested language.
  string text = 1;

  // The text asset type. For example, HEADLINE, DESCRIPTION, etc.
  google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType
      asset_field_type = 2;
}

// A single image asset suggestion for a hotel.
message HotelImageAsset {
  // URI for the image.
  string uri = 1;

  // The Image asset type. For example, MARKETING_IMAGE,
  // PORTRAIT_MARKETING_IMAGE, etc.
  google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType
      asset_field_type = 2;
}
