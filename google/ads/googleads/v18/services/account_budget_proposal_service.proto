// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/account_budget_proposal.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "AccountBudgetProposalServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the AccountBudgetProposal service.

// A service for managing account-level budgets through proposals.
//
// A proposal is a request to create a new budget or make changes to an
// existing one.
//
// Mutates:
// The CREATE operation creates a new proposal.
// UPDATE operations aren't supported.
// The REMOVE operation cancels a pending proposal.
service AccountBudgetProposalService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, updates, or removes account budget proposals.  Operation statuses
  // are returned.
  //
  // List of thrown errors:
  //   [AccountBudgetProposalError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [DateError]()
  //   [FieldError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  //   [StringLengthError]()
  rpc MutateAccountBudgetProposal(MutateAccountBudgetProposalRequest)
      returns (MutateAccountBudgetProposalResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/accountBudgetProposals:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }
}

// Request message for
// [AccountBudgetProposalService.MutateAccountBudgetProposal][google.ads.googleads.v18.services.AccountBudgetProposalService.MutateAccountBudgetProposal].
message MutateAccountBudgetProposalRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform on an individual account-level budget
  // proposal.
  AccountBudgetProposalOperation operation = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// A single operation to propose the creation of a new account-level budget or
// edit/end/remove an existing one.
message AccountBudgetProposalOperation {
  // FieldMask that determines which budget fields are modified.  While budgets
  // may be modified, proposals that propose such modifications are final.
  // Therefore, update operations are not supported for proposals.
  //
  // Proposals that modify budgets have the 'update' proposal type.  Specifying
  // a mask for any other proposal type is considered an error.
  google.protobuf.FieldMask update_mask = 3;

  // The mutate operation.
  oneof operation {
    // Create operation: A new proposal to create a new budget, edit an
    // existing budget, end an actively running budget, or remove an approved
    // budget scheduled to start in the future.
    // No resource name is expected for the new proposal.
    google.ads.googleads.v18.resources.AccountBudgetProposal create = 2;

    // Remove operation: A resource name for the removed proposal is expected,
    // in this format:
    //
    // `customers/{customer_id}/accountBudgetProposals/{account_budget_proposal_id}`
    // A request may be cancelled iff it is pending.
    string remove = 1 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/AccountBudgetProposal"
    }];
  }
}

// Response message for account-level budget mutate operations.
message MutateAccountBudgetProposalResponse {
  // The result of the mutate.
  MutateAccountBudgetProposalResult result = 2;
}

// The result for the account budget proposal mutate.
message MutateAccountBudgetProposalResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/AccountBudgetProposal"
  }];
}
