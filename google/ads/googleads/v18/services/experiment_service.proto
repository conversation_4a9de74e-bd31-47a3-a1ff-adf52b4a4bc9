// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/experiment.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "ExperimentServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the Experiment service.

// Service to manage experiments.
service ExperimentService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, updates, or removes experiments. Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [ExperimentError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateExperiments(MutateExperimentsRequest)
      returns (MutateExperimentsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/experiments:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }

  // Immediately ends an experiment, changing the experiment's scheduled
  // end date and without waiting for end of day. End date is updated to be the
  // time of the request.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [ExperimentError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc EndExperiment(EndExperimentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v18/{experiment=customers/*/experiments/*}:endExperiment"
      body: "*"
    };
    option (google.api.method_signature) = "experiment";
  }

  // Returns all errors that occurred during the last Experiment update (either
  // scheduling or promotion).
  // Supports standard list paging.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc ListExperimentAsyncErrors(ListExperimentAsyncErrorsRequest)
      returns (ListExperimentAsyncErrorsResponse) {
    option (google.api.http) = {
      get: "/v18/{resource_name=customers/*/experiments/*}:listExperimentAsyncErrors"
    };
    option (google.api.method_signature) = "resource_name";
  }

  // Graduates an experiment to a full campaign.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [ExperimentError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc GraduateExperiment(GraduateExperimentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v18/{experiment=customers/*/experiments/*}:graduateExperiment"
      body: "*"
    };
    option (google.api.method_signature) =
        "experiment,campaign_budget_mappings";
  }

  // Schedule an experiment. The in design campaign
  // will be converted into a real campaign (called the experiment campaign)
  // that will begin serving ads if successfully created.
  //
  // The experiment is scheduled immediately with status INITIALIZING.
  // This method returns a long running operation that tracks the forking of the
  // in design campaign. If the forking fails, a list of errors can be retrieved
  // using the ListExperimentAsyncErrors method. The operation's
  // metadata will be a string containing the resource name of the created
  // experiment.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [ExperimentError]()
  //   [DatabaseError]()
  //   [DateError]()
  //   [DateRangeError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  rpc ScheduleExperiment(ScheduleExperimentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v18/{resource_name=customers/*/experiments/*}:scheduleExperiment"
      body: "*"
    };
    option (google.api.method_signature) = "resource_name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.ads.googleads.v18.services.ScheduleExperimentMetadata"
    };
  }

  // Promotes the trial campaign thus applying changes in the trial campaign
  // to the base campaign.
  // This method returns a long running operation that tracks the promotion of
  // the experiment campaign. If it fails, a list of errors can be retrieved
  // using the ListExperimentAsyncErrors method. The operation's
  // metadata will be a string containing the resource name of the created
  // experiment.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [ExperimentError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc PromoteExperiment(PromoteExperimentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v18/{resource_name=customers/*/experiments/*}:promoteExperiment"
      body: "*"
    };
    option (google.api.method_signature) = "resource_name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.ads.googleads.v18.services.PromoteExperimentMetadata"
    };
  }
}

// Request message for
// [ExperimentService.MutateExperiments][google.ads.googleads.v18.services.ExperimentService.MutateExperiments].
message MutateExperimentsRequest {
  // Required. The ID of the customer whose experiments are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual experiments.
  repeated ExperimentOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;
}

// A single operation on an experiment.
message ExperimentOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The mutate operation.
  oneof operation {
    // Create operation
    google.ads.googleads.v18.resources.Experiment create = 1;

    // Update operation: The experiment is expected to have a valid
    // resource name.
    google.ads.googleads.v18.resources.Experiment update = 2;

    // Remove operation: The experiment is expected to have a valid
    // resource name, in this format:
    //
    // `customers/{customer_id}/experiments/{campaign_experiment_id}`
    string remove = 3 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }];
  }
}

// Response message for experiment mutate.
message MutateExperimentsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 1;

  // All results for the mutate.
  repeated MutateExperimentResult results = 2;
}

// The result for the campaign experiment mutate.
message MutateExperimentResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/Experiment"
  }];
}

// Request message for
// [ExperimentService.EndExperiment][google.ads.googleads.v18.services.ExperimentService.EndExperiment].
message EndExperimentRequest {
  // Required. The resource name of the campaign experiment to end.
  string experiment = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 2;
}

// Request message for
// [ExperimentService.ListExperimentAsyncErrors][google.ads.googleads.v18.services.ExperimentService.ListExperimentAsyncErrors].
message ListExperimentAsyncErrorsRequest {
  // Required. The name of the experiment from which to retrieve the async
  // errors.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];

  // Token of the page to retrieve. If not specified, the first
  // page of results will be returned. Use the value obtained from
  // `next_page_token` in the previous response in order to request
  // the next page of results.
  string page_token = 2;

  // Number of elements to retrieve in a single page.
  // When a page request is too large, the server may decide to
  // further limit the number of returned resources.
  // The maximum page size is 1000.
  int32 page_size = 3;
}

// Response message for
// [ExperimentService.ListExperimentAsyncErrors][google.ads.googleads.v18.services.ExperimentService.ListExperimentAsyncErrors].
message ListExperimentAsyncErrorsResponse {
  // details of the errors when performing the asynchronous operation.
  repeated google.rpc.Status errors = 1;

  // Pagination token used to retrieve the next page of results.
  // Pass the content of this string as the `page_token` attribute of
  // the next request. `next_page_token` is not returned for the last
  // page.
  string next_page_token = 2;
}

// Request message for
// [ExperimentService.GraduateExperiment][google.ads.googleads.v18.services.ExperimentService.GraduateExperiment].
message GraduateExperimentRequest {
  // Required. The experiment to be graduated.
  string experiment = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];

  // Required. List of campaign budget mappings for graduation. Each campaign
  // that appears here will graduate, and will be assigned a new budget that is
  // paired with it in the mapping. The maximum size is one.
  repeated CampaignBudgetMapping campaign_budget_mappings = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// The mapping of experiment campaign and budget to be graduated.
message CampaignBudgetMapping {
  // Required. The experiment campaign to graduate.
  string experiment_campaign = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Required. The budget that should be attached to the graduating experiment
  // campaign.
  string campaign_budget = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignBudget"
    }
  ];
}

// Request message for
// [ExperimentService.ScheduleExperiment][google.ads.googleads.v18.services.ExperimentService.ScheduleExperiment].
message ScheduleExperimentRequest {
  // Required. The scheduled experiment.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 2;
}

// The metadata of the scheduled experiment.
message ScheduleExperimentMetadata {
  // Required. The scheduled experiment.
  string experiment = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];
}

// Request message for
// [ExperimentService.PromoteExperiment][google.ads.googleads.v18.services.ExperimentService.PromoteExperiment].
message PromoteExperimentRequest {
  // Required. The resource name of the experiment to promote.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 2;
}

// The metadata of the promoted experiment.
message PromoteExperimentMetadata {
  // Required. The promoted experiment.
  string experiment = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];
}
