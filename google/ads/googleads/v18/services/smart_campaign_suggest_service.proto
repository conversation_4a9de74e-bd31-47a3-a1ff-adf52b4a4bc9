// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/common/ad_type_infos.proto";
import "google/ads/googleads/v18/common/criteria.proto";
import "google/ads/googleads/v18/resources/keyword_theme_constant.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "SmartCampaignSuggestServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Service to get suggestions for Smart Campaigns.
service SmartCampaignSuggestService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns BudgetOption suggestions.
  rpc SuggestSmartCampaignBudgetOptions(
      SuggestSmartCampaignBudgetOptionsRequest)
      returns (SuggestSmartCampaignBudgetOptionsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:suggestSmartCampaignBudgetOptions"
      body: "*"
    };
  }

  // Suggests a Smart campaign ad compatible with the Ad family of resources,
  // based on data points such as targeting and the business to advertise.
  rpc SuggestSmartCampaignAd(SuggestSmartCampaignAdRequest)
      returns (SuggestSmartCampaignAdResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:suggestSmartCampaignAd"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,suggestion_info";
  }

  // Suggests keyword themes to advertise on.
  rpc SuggestKeywordThemes(SuggestKeywordThemesRequest)
      returns (SuggestKeywordThemesResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:suggestKeywordThemes"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,suggestion_info";
  }
}

// Request message for
// [SmartCampaignSuggestService.SuggestSmartCampaignBudgetOptions][google.ads.googleads.v18.services.SmartCampaignSuggestService.SuggestSmartCampaignBudgetOptions].
message SuggestSmartCampaignBudgetOptionsRequest {
  // Required. The ID of the customer whose budget options are to be suggested.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. For first time campaign creation use SuggestionInfo, for
  // subsequent updates on BudgetOptions based on an already created campaign
  // use that campaign.
  oneof suggestion_data {
    // Required. The resource name of the campaign to get suggestion for.
    string campaign = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "googleads.googleapis.com/Campaign"
      }
    ];

    // Required. Information needed to get budget options
    SmartCampaignSuggestionInfo suggestion_info = 3
        [(google.api.field_behavior) = REQUIRED];
  }
}

// Information needed to get suggestion for Smart Campaign. More information
// provided will help the system to derive better suggestions.
message SmartCampaignSuggestionInfo {
  // A list of locations.
  message LocationList {
    // Required. Locations.
    repeated google.ads.googleads.v18.common.LocationInfo locations = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // A context that describes a business.
  message BusinessContext {
    // Optional. The name of the business.
    string business_name = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. Landing page URL of the campaign.
  string final_url = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The two letter advertising language for the Smart campaign to be
  // constructed, default to 'en' if not set.
  string language_code = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The business ad schedule.
  repeated google.ads.googleads.v18.common.AdScheduleInfo ad_schedules = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Smart campaign keyword themes. This field may greatly improve
  // suggestion accuracy and we recommend always setting it if possible.
  repeated google.ads.googleads.v18.common.KeywordThemeInfo keyword_themes = 7
      [(google.api.field_behavior) = OPTIONAL];

  // The business settings to consider when generating suggestions.
  // Settings are automatically extracted from the business when provided.
  // Otherwise, these settings must be specified explicitly.
  oneof business_setting {
    // Optional. Context describing the business to advertise.
    BusinessContext business_context = 8
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The resource name of a Business Profile location.
    // Business Profile location resource names can be fetched through the
    // Business Profile API and adhere to the following format:
    // `locations/{locationId}`.
    //
    // See the [Business Profile API]
    // (https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations)
    // for additional details.
    string business_profile_location = 9
        [(google.api.field_behavior) = OPTIONAL];
  }

  // The geo target of the campaign, either a list of locations or
  // a single proximity shall be specified.
  oneof geo_target {
    // Optional. The targeting geo location by locations.
    LocationList location_list = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The targeting geo location by proximity.
    google.ads.googleads.v18.common.ProximityInfo proximity = 5
        [(google.api.field_behavior) = OPTIONAL];
  }
}

// Response message for
// [SmartCampaignSuggestService.SuggestSmartCampaignBudgetOptions][google.ads.googleads.v18.services.SmartCampaignSuggestService.SuggestSmartCampaignBudgetOptions].
// Depending on whether the system could suggest the options, either all of the
// options or none of them might be returned.
message SuggestSmartCampaignBudgetOptionsResponse {
  // Performance metrics for a given budget option.
  message Metrics {
    // The estimated min daily clicks.
    int64 min_daily_clicks = 1;

    // The estimated max daily clicks.
    int64 max_daily_clicks = 2;
  }

  // Smart Campaign budget option.
  message BudgetOption {
    // The amount of the budget, in the local currency for the account.
    // Amount is specified in micros, where one million is equivalent to one
    // currency unit.
    int64 daily_amount_micros = 1;

    // Metrics pertaining to the suggested budget, could be empty if there is
    // not enough information to derive the estimates.
    Metrics metrics = 2;
  }

  // Optional. The lowest budget option.
  optional BudgetOption low = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The recommended budget option.
  optional BudgetOption recommended = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The highest budget option.
  optional BudgetOption high = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [SmartCampaignSuggestService.SuggestSmartCampaignAd][google.ads.googleads.v18.services.SmartCampaignSuggestService.SuggestSmartCampaignAd].
message SuggestSmartCampaignAdRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Inputs used to suggest a Smart campaign ad.
  // Required fields: final_url, language_code, keyword_themes.
  // Optional but recommended fields to improve the quality of the suggestion:
  // business_setting and geo_target.
  SmartCampaignSuggestionInfo suggestion_info = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [SmartCampaignSuggestService.SuggestSmartCampaignAd][google.ads.googleads.v18.services.SmartCampaignSuggestService.SuggestSmartCampaignAd].
message SuggestSmartCampaignAdResponse {
  // Optional. Ad info includes 3 creative headlines and 2 creative
  // descriptions.
  google.ads.googleads.v18.common.SmartCampaignAdInfo ad_info = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [SmartCampaignSuggestService.SuggestKeywordThemes][google.ads.googleads.v18.services.SmartCampaignSuggestService.SuggestKeywordThemes].
message SuggestKeywordThemesRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Information to get keyword theme suggestions.
  // Required fields:
  //
  // * suggestion_info.final_url
  // * suggestion_info.language_code
  // * suggestion_info.geo_target
  //
  // Recommended fields:
  //
  // * suggestion_info.business_setting
  SmartCampaignSuggestionInfo suggestion_info = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [SmartCampaignSuggestService.SuggestKeywordThemes][google.ads.googleads.v18.services.SmartCampaignSuggestService.SuggestKeywordThemes].
message SuggestKeywordThemesResponse {
  // A Smart campaign keyword theme suggestion.
  message KeywordTheme {
    // A keyword theme.
    oneof keyword_theme {
      // A Smart campaign keyword theme constant.
      google.ads.googleads.v18.resources.KeywordThemeConstant
          keyword_theme_constant = 1;

      // A free-form text keyword theme.
      string free_form_keyword_theme = 2;
    }
  }

  // Smart campaign keyword theme suggestions.
  repeated KeywordTheme keyword_themes = 2;
}
