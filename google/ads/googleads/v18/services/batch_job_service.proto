// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/enums/response_content_type.proto";
import "google/ads/googleads/v18/resources/batch_job.proto";
import "google/ads/googleads/v18/services/google_ads_service.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "BatchJobServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the BatchJobService.

// Service to manage batch jobs.
service BatchJobService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Mutates a batch job.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  //   [ResourceCountLimitExceededError]()
  rpc MutateBatchJob(MutateBatchJobRequest) returns (MutateBatchJobResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/batchJobs:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }

  // Returns the results of the batch job. The job must be done.
  // Supports standard list paging.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [BatchJobError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc ListBatchJobResults(ListBatchJobResultsRequest)
      returns (ListBatchJobResultsResponse) {
    option (google.api.http) = {
      get: "/v18/{resource_name=customers/*/batchJobs/*}:listResults"
    };
    option (google.api.method_signature) = "resource_name";
  }

  // Runs the batch job.
  //
  // The Operation.metadata field type is BatchJobMetadata. When finished, the
  // long running operation will not contain errors or a response. Instead, use
  // ListBatchJobResults to get the results of the job.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [BatchJobError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc RunBatchJob(RunBatchJobRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v18/{resource_name=customers/*/batchJobs/*}:run"
      body: "*"
    };
    option (google.api.method_signature) = "resource_name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.ads.googleads.v18.resources.BatchJob.BatchJobMetadata"
    };
  }

  // Add operations to the batch job.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [BatchJobError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  //   [ResourceCountLimitExceededError]()
  rpc AddBatchJobOperations(AddBatchJobOperationsRequest)
      returns (AddBatchJobOperationsResponse) {
    option (google.api.http) = {
      post: "/v18/{resource_name=customers/*/batchJobs/*}:addOperations"
      body: "*"
    };
    option (google.api.method_signature) =
        "resource_name,sequence_token,mutate_operations";
    option (google.api.method_signature) = "resource_name,mutate_operations";
  }
}

// Request message for
// [BatchJobService.MutateBatchJob][google.ads.googleads.v18.services.BatchJobService.MutateBatchJob].
message MutateBatchJobRequest {
  // Required. The ID of the customer for which to create a batch job.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform on an individual batch job.
  BatchJobOperation operation = 2 [(google.api.field_behavior) = REQUIRED];
}

// A single operation on a batch job.
message BatchJobOperation {
  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new batch job.
    google.ads.googleads.v18.resources.BatchJob create = 1;

    // Remove operation: The batch job must not have been run. A resource name
    // for the removed batch job is expected, in this format:
    //
    // `customers/{customer_id}/batchJobs/{batch_job_id}`
    string remove = 4 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/BatchJob"
    }];
  }
}

// Response message for
// [BatchJobService.MutateBatchJob][google.ads.googleads.v18.services.BatchJobService.MutateBatchJob].
message MutateBatchJobResponse {
  // The result for the mutate.
  MutateBatchJobResult result = 1;
}

// The result for the batch job mutate.
message MutateBatchJobResult {
  // The resource name of the batch job.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/BatchJob"
  }];
}

// Request message for
// [BatchJobService.RunBatchJob][google.ads.googleads.v18.services.BatchJobService.RunBatchJob].
message RunBatchJobRequest {
  // Required. The resource name of the BatchJob to run.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BatchJob"
    }
  ];
}

// Request message for
// [BatchJobService.AddBatchJobOperations][google.ads.googleads.v18.services.BatchJobService.AddBatchJobOperations].
message AddBatchJobOperationsRequest {
  // Required. The resource name of the batch job.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BatchJob"
    }
  ];

  // A token used to enforce sequencing.
  //
  // The first AddBatchJobOperations request for a batch job should not set
  // sequence_token. Subsequent requests must set sequence_token to the value of
  // next_sequence_token received in the previous AddBatchJobOperations
  // response.
  string sequence_token = 2;

  // Required. The list of mutates being added.
  //
  // Operations can use negative integers as temp ids to signify dependencies
  // between entities created in this batch job. For example, a customer with
  // id = 1234 can create a campaign and an ad group in that same campaign by
  // creating a campaign in the first operation with the resource name
  // explicitly set to "customers/1234/campaigns/-1", and creating an ad group
  // in the second operation with the campaign field also set to
  // "customers/1234/campaigns/-1".
  repeated MutateOperation mutate_operations = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [BatchJobService.AddBatchJobOperations][google.ads.googleads.v18.services.BatchJobService.AddBatchJobOperations].
message AddBatchJobOperationsResponse {
  // The total number of operations added so far for this batch job.
  int64 total_operations = 1;

  // The sequence token to be used when calling AddBatchJobOperations again if
  // more operations need to be added. The next AddBatchJobOperations request
  // must set the sequence_token field to the value of this field.
  string next_sequence_token = 2;
}

// Request message for
// [BatchJobService.ListBatchJobResults][google.ads.googleads.v18.services.BatchJobService.ListBatchJobResults].
message ListBatchJobResultsRequest {
  // Required. The resource name of the batch job whose results are being
  // listed.
  string resource_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BatchJob"
    }
  ];

  // Token of the page to retrieve. If not specified, the first
  // page of results will be returned. Use the value obtained from
  // `next_page_token` in the previous response in order to request
  // the next page of results.
  string page_token = 2;

  // Number of elements to retrieve in a single page.
  // When a page request is too large, the server may decide to
  // further limit the number of returned resources.
  int32 page_size = 3;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned.
  google.ads.googleads.v18.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 4;
}

// Response message for
// [BatchJobService.ListBatchJobResults][google.ads.googleads.v18.services.BatchJobService.ListBatchJobResults].
message ListBatchJobResultsResponse {
  // The list of rows that matched the query.
  repeated BatchJobResult results = 1;

  // Pagination token used to retrieve the next page of results.
  // Pass the content of this string as the `page_token` attribute of
  // the next request. `next_page_token` is not returned for the last
  // page.
  string next_page_token = 2;
}

// An individual batch job result.
message BatchJobResult {
  // Index of the mutate operation.
  int64 operation_index = 1;

  // Response for the mutate.
  // May be empty if errors occurred.
  MutateOperationResponse mutate_operation_response = 2;

  // Details of the errors when processing the operation.
  google.rpc.Status status = 3;
}
