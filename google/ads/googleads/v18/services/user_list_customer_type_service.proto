// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/user_list_customer_type.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "UserListCustomerTypeServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the UserListCustomerType service.

// Service to manage user list customer types.
service UserListCustomerTypeService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Attach or remove user list customer types. Operation statuses
  // are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [UserListCustomerTypeError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateUserListCustomerTypes(MutateUserListCustomerTypesRequest)
      returns (MutateUserListCustomerTypesResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/userListCustomerTypes:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [UserListCustomerTypeService.MutateUserListCustomerTypes][google.ads.googleads.v18.services.UserListCustomerTypeService.MutateUserListCustomerTypes].
message MutateUserListCustomerTypesRequest {
  // Required. The ID of the customer whose user list customer types are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on the user list customer
  // types.
  repeated UserListCustomerTypeOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, the request is validated but not executed. Only errors
  // are returned, not results.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A single mutate operation on the user list customer type.
message UserListCustomerTypeOperation {
  // The mutate operation.
  oneof operation {
    // Attach a user list customer type to a user list.
    // No resource name is expected for the new user list customer type.
    google.ads.googleads.v18.resources.UserListCustomerType create = 1;

    // Remove an existing user list customer type.
    // A resource name for the removed user list customer type is
    // expected, in this format:
    //
    // `customers/{customer_id}/userListCustomerTypes/{user_list_id}~{customer_type_category}`
    string remove = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserListCustomerType"
    }];
  }
}

// Response message for a user list customer type mutate.
message MutateUserListCustomerTypesResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 1;

  // All results for the mutate.
  repeated MutateUserListCustomerTypeResult results = 2;
}

// The result for the user list customer type mutate.
message MutateUserListCustomerTypeResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/UserListCustomerType"
  }];
}
