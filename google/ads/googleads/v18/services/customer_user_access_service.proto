// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/customer_user_access.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerUserAccessServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// This service manages the permissions of a user on a given customer.
service CustomerUserAccessService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Updates, removes permission of a user on a given customer. Operation
  // statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CustomerUserAccessError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateCustomerUserAccess(MutateCustomerUserAccessRequest)
      returns (MutateCustomerUserAccessResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/customerUserAccesses:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }
}

// Mutate Request for
// [CustomerUserAccessService.MutateCustomerUserAccess][google.ads.googleads.v18.services.CustomerUserAccessService.MutateCustomerUserAccess].
message MutateCustomerUserAccessRequest {
  // Required. The ID of the customer being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform on the customer
  CustomerUserAccessOperation operation = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A single operation (update, remove) on customer user access.
message CustomerUserAccessOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 3;

  // The mutate operation.
  oneof operation {
    // Update operation: The customer user access is expected to have a valid
    // resource name.
    google.ads.googleads.v18.resources.CustomerUserAccess update = 1;

    // Remove operation: A resource name for the removed access is
    // expected, in this format:
    //
    // `customers/{customer_id}/customerUserAccesses/{CustomerUserAccess.user_id}`
    string remove = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerUserAccess"
    }];
  }
}

// Response message for customer user access mutate.
message MutateCustomerUserAccessResponse {
  // Result for the mutate.
  MutateCustomerUserAccessResult result = 1;
}

// The result for the customer user access mutate.
message MutateCustomerUserAccessResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomerUserAccess"
  }];
}
