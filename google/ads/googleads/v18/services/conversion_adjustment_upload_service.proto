// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/common/offline_user_data.proto";
import "google/ads/googleads/v18/enums/conversion_adjustment_type.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "ConversionAdjustmentUploadServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Service to upload conversion adjustments.
service ConversionAdjustmentUploadService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Processes the given conversion adjustments.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [PartialFailureError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc UploadConversionAdjustments(UploadConversionAdjustmentsRequest)
      returns (UploadConversionAdjustmentsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:uploadConversionAdjustments"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,conversion_adjustments,partial_failure";
  }
}

// Request message for
// [ConversionAdjustmentUploadService.UploadConversionAdjustments][google.ads.googleads.v18.services.ConversionAdjustmentUploadService.UploadConversionAdjustments].
message UploadConversionAdjustmentsRequest {
  // Required. The ID of the customer performing the upload.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The conversion adjustments that are being uploaded.
  repeated ConversionAdjustment conversion_adjustments = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried out
  // in one transaction if and only if they are all valid. This should always be
  // set to true.
  // See
  // https://developers.google.com/google-ads/api/docs/best-practices/partial-failures
  // for more information about partial failure.
  bool partial_failure = 3 [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;

  // Optional. Optional input to set job ID. Must be a non-negative number that
  // is less than 2^31 if provided. If this field is not provided, the API will
  // generate a job ID in the range [2^31, (2^63)-1]. The API will return the
  // value for this request in the `job_id` field of the
  // `UploadConversionAdjustmentsResponse`.
  optional int32 job_id = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ConversionAdjustmentUploadService.UploadConversionAdjustments][google.ads.googleads.v18.services.ConversionAdjustmentUploadService.UploadConversionAdjustments].
message UploadConversionAdjustmentsResponse {
  // Errors that pertain to conversion adjustment failures in the partial
  // failure mode. Returned when all errors occur inside the adjustments. If any
  // errors occur outside the adjustments (for example, auth errors), we return
  // an RPC level error. See
  // https://developers.google.com/google-ads/api/docs/best-practices/partial-failures
  // for more information about partial failure.
  google.rpc.Status partial_failure_error = 1;

  // Returned for successfully processed conversion adjustments. Proto will be
  // empty for rows that received an error. Results are not returned when
  // validate_only is true.
  repeated ConversionAdjustmentResult results = 2;

  // Job ID for the upload batch.
  int64 job_id = 3;
}

// A conversion adjustment.
message ConversionAdjustment {
  // For adjustments, uniquely identifies a conversion that was reported
  // without an order ID specified. If the adjustment_type is ENHANCEMENT, this
  // value is optional but may be set in addition to the order_id.
  GclidDateTimePair gclid_date_time_pair = 12;

  // The order ID of the conversion to be adjusted. If the conversion was
  // reported with an order ID specified, that order ID must be used as the
  // identifier here. The order ID is required for enhancements.
  optional string order_id = 13;

  // Resource name of the conversion action associated with this conversion
  // adjustment. Note: Although this resource name consists of a customer id and
  // a conversion action id, validation will ignore the customer id and use the
  // conversion action id as the sole identifier of the conversion action.
  optional string conversion_action = 8;

  // The date time at which the adjustment occurred. Must be after the
  // conversion_date_time. The timezone must be specified. The format is
  // "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string adjustment_date_time = 9;

  // The adjustment type.
  google.ads.googleads.v18.enums.ConversionAdjustmentTypeEnum
      .ConversionAdjustmentType adjustment_type = 5;

  // Information needed to restate the conversion's value.
  // Required for restatements. Should not be supplied for retractions. An error
  // will be returned if provided for a retraction.
  // NOTE: If you want to upload a second restatement with a different adjusted
  // value, it must have a new, more recent, adjustment occurrence time.
  // Otherwise, it will be treated as a duplicate of the previous restatement
  // and ignored.
  RestatementValue restatement_value = 6;

  // The user identifiers to enhance the original conversion.
  // ConversionAdjustmentUploadService only accepts user identifiers in
  // enhancements. The maximum number of user identifiers for each
  // enhancement is 5.
  repeated google.ads.googleads.v18.common.UserIdentifier user_identifiers = 10;

  // The user agent to enhance the original conversion. This can be found in
  // your user's HTTP request header when they convert on your web page.
  // Example, "Mozilla/5.0 (iPhone; CPU iPhone OS 12_2 like Mac OS X)". User
  // agent can only be specified in enhancements with user identifiers. This
  // should match the user agent of the request that sent the original
  // conversion so the conversion and its enhancement are either both attributed
  // as same-device or both attributed as cross-device.
  optional string user_agent = 11;
}

// Contains information needed to restate a conversion's value.
message RestatementValue {
  // The restated conversion value. This is the value of the conversion after
  // restatement. For example, to change the value of a conversion from 100 to
  // 70, an adjusted value of 70 should be reported.
  // NOTE: If you want to upload a second restatement with a different adjusted
  // value, it must have a new, more recent, adjustment occurrence time.
  // Otherwise, it will be treated as a duplicate of the previous restatement
  // and ignored.
  optional double adjusted_value = 3;

  // The currency of the restated value. If not provided, then the default
  // currency from the conversion action is used, and if that is not set then
  // the account currency is used. This is the ISO 4217 3-character currency
  // code for example, USD or EUR.
  optional string currency_code = 4;
}

// Uniquely identifies a conversion that was reported without an order ID
// specified.
message GclidDateTimePair {
  // Google click ID (gclid) associated with the original conversion for this
  // adjustment.
  optional string gclid = 3;

  // The date time at which the original conversion for this adjustment
  // occurred. The timezone must be specified. The format is "yyyy-mm-dd
  // hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string conversion_date_time = 4;
}

// Information identifying a successfully processed ConversionAdjustment.
message ConversionAdjustmentResult {
  // The gclid and conversion date time of the conversion.
  GclidDateTimePair gclid_date_time_pair = 9;

  // The order ID of the conversion to be adjusted.
  string order_id = 10;

  // Resource name of the conversion action associated with this conversion
  // adjustment.
  optional string conversion_action = 7;

  // The date time at which the adjustment occurred. The format is
  // "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string adjustment_date_time = 8;

  // The adjustment type.
  google.ads.googleads.v18.enums.ConversionAdjustmentTypeEnum
      .ConversionAdjustmentType adjustment_type = 5;
}
