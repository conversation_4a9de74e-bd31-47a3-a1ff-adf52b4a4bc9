// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/common/consent.proto";
import "google/ads/googleads/v18/common/offline_user_data.proto";
import "google/ads/googleads/v18/enums/conversion_environment_enum.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "ConversionUploadServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Service to upload conversions.
service ConversionUploadService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Processes the given click conversions.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [ConversionUploadError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [PartialFailureError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc UploadClickConversions(UploadClickConversionsRequest)
      returns (UploadClickConversionsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:uploadClickConversions"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,conversions,partial_failure";
  }

  // Processes the given call conversions.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [PartialFailureError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc UploadCallConversions(UploadCallConversionsRequest)
      returns (UploadCallConversionsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}:uploadCallConversions"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,conversions,partial_failure";
  }
}

// Request message for
// [ConversionUploadService.UploadClickConversions][google.ads.googleads.v18.services.ConversionUploadService.UploadClickConversions].
message UploadClickConversionsRequest {
  // Required. The ID of the customer performing the upload.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The conversions that are being uploaded.
  repeated ClickConversion conversions = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // This should always be set to true.
  // See
  // https://developers.google.com/google-ads/api/docs/best-practices/partial-failures
  // for more information about partial failure.
  bool partial_failure = 3 [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;

  // If true, the API will perform all upload checks and return errors if
  // any are found. If false, it will perform only basic input validation,
  // skip subsequent upload checks, and return success even if no click
  // was found for the provided `user_identifiers`.
  //
  // This setting only affects Enhanced conversions for leads uploads that use
  // `user_identifiers` instead of `GCLID`, `GBRAID`, or `WBRAID`. When
  // uploading enhanced conversions for leads, you should upload all conversion
  // events to the API, including those that may not come from Google Ads
  // campaigns. The upload of an event that is not from a Google Ads campaign
  // will result in a `CLICK_NOT_FOUND` error if this field is set to `true`.
  // Since these errors are expected for such events, set this field to `false`
  // so you can confirm your uploads are properly formatted but ignore
  // `CLICK_NOT_FOUND` errors from all of the conversions that are not from a
  // Google Ads campaign. This will allow you to focus only on errors that you
  // can address.
  //
  // Default is false.
  bool debug_enabled = 5;

  // Optional. Optional input to set job ID. Must be a non-negative number that
  // is less than 2^31 if provided. If this field is not provided, the API will
  // generate a job ID in the range [2^31, (2^63)-1]. The API will return the
  // value for this request in the `job_id` field of the
  // `UploadClickConversionsResponse`.
  optional int32 job_id = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ConversionUploadService.UploadClickConversions][google.ads.googleads.v18.services.ConversionUploadService.UploadClickConversions].
message UploadClickConversionsResponse {
  // Errors that pertain to conversion failures in the partial failure mode.
  // Returned when all errors occur inside the conversions. If any errors occur
  // outside the conversions (for example, auth errors), we return an RPC level
  // error. See
  // https://developers.google.com/google-ads/api/docs/best-practices/partial-failures
  // for more information about partial failure.
  google.rpc.Status partial_failure_error = 1;

  // Returned for successfully processed conversions. Proto will be empty for
  // rows that received an error. Results are not returned when validate_only is
  // true.
  repeated ClickConversionResult results = 2;

  // Job ID for the upload batch.
  int64 job_id = 3;
}

// Request message for
// [ConversionUploadService.UploadCallConversions][google.ads.googleads.v18.services.ConversionUploadService.UploadCallConversions].
message UploadCallConversionsRequest {
  // Required. The ID of the customer performing the upload.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The conversions that are being uploaded.
  repeated CallConversion conversions = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // This should always be set to true.
  // See
  // https://developers.google.com/google-ads/api/docs/best-practices/partial-failures
  // for more information about partial failure.
  bool partial_failure = 3 [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;
}

// Response message for
// [ConversionUploadService.UploadCallConversions][google.ads.googleads.v18.services.ConversionUploadService.UploadCallConversions].
message UploadCallConversionsResponse {
  // Errors that pertain to conversion failures in the partial failure mode.
  // Returned when all errors occur inside the conversions. If any errors occur
  // outside the conversions (for example, auth errors), we return an RPC level
  // error. See
  // https://developers.google.com/google-ads/api/docs/best-practices/partial-failures
  // for more information about partial failure.
  google.rpc.Status partial_failure_error = 1;

  // Returned for successfully processed conversions. Proto will be empty for
  // rows that received an error. Results are not returned when validate_only is
  // true.
  repeated CallConversionResult results = 2;
}

// A click conversion.
message ClickConversion {
  // The Google click ID (gclid) associated with this conversion.
  optional string gclid = 9;

  // The click identifier for clicks associated with app conversions and
  // originating from iOS devices starting with iOS14.
  string gbraid = 18;

  // The click identifier for clicks associated with web conversions and
  // originating from iOS devices starting with iOS14.
  string wbraid = 19;

  // Resource name of the conversion action associated with this conversion.
  // Note: Although this resource name consists of a customer id and a
  // conversion action id, validation will ignore the customer id and use the
  // conversion action id as the sole identifier of the conversion action.
  optional string conversion_action = 10;

  // The date time at which the conversion occurred. Must be after
  // the click time. The timezone must be specified. The format is
  // "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string conversion_date_time = 11;

  // The value of the conversion for the advertiser.
  optional double conversion_value = 12;

  // Currency associated with the conversion value. This is the ISO 4217
  // 3-character currency code. For example: USD, EUR.
  optional string currency_code = 13;

  // The order ID associated with the conversion. An order id can only be used
  // for one conversion per conversion action.
  optional string order_id = 14;

  // Additional data about externally attributed conversions. This field
  // is required for conversions with an externally attributed conversion
  // action, but should not be set otherwise.
  ExternalAttributionData external_attribution_data = 7;

  // The custom variables associated with this conversion.
  repeated CustomVariable custom_variables = 15;

  // The cart data associated with this conversion.
  CartData cart_data = 16;

  // The user identifiers associated with this conversion. Only hashed_email and
  // hashed_phone_number are supported for conversion uploads. The maximum
  // number of user identifiers for each conversion is 5.
  repeated google.ads.googleads.v18.common.UserIdentifier user_identifiers = 17;

  // The environment this conversion was recorded on, for example, App or Web.
  google.ads.googleads.v18.enums.ConversionEnvironmentEnum.ConversionEnvironment
      conversion_environment = 20;

  // The consent setting for the event.
  google.ads.googleads.v18.common.Consent consent = 23;
}

// A call conversion.
message CallConversion {
  // The caller id from which this call was placed. Caller id is expected to be
  // in E.164 format with preceding '+' sign, for example, "+16502531234".
  optional string caller_id = 7;

  // The date time at which the call occurred. The timezone must be specified.
  // The format is "yyyy-mm-dd hh:mm:ss+|-hh:mm",
  // for example, "2019-01-01 12:32:45-08:00".
  optional string call_start_date_time = 8;

  // Resource name of the conversion action associated with this conversion.
  // Note: Although this resource name consists of a customer id and a
  // conversion action id, validation will ignore the customer id and use the
  // conversion action id as the sole identifier of the conversion action.
  optional string conversion_action = 9;

  // The date time at which the conversion occurred. Must be after the call
  // time. The timezone must be specified. The format is
  // "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string conversion_date_time = 10;

  // The value of the conversion for the advertiser.
  optional double conversion_value = 11;

  // Currency associated with the conversion value. This is the ISO 4217
  // 3-character currency code. For example: USD, EUR.
  optional string currency_code = 12;

  // The custom variables associated with this conversion.
  repeated CustomVariable custom_variables = 13;

  // The consent setting for the event.
  google.ads.googleads.v18.common.Consent consent = 14;
}

// Contains additional information about externally attributed conversions.
message ExternalAttributionData {
  // Represents the fraction of the conversion that is attributed to the
  // Google Ads click.
  optional double external_attribution_credit = 3;

  // Specifies the attribution model name.
  optional string external_attribution_model = 4;
}

// Identifying information for a successfully processed ClickConversion.
message ClickConversionResult {
  // The Google Click ID (gclid) associated with this conversion.
  optional string gclid = 4;

  // The click identifier for clicks associated with app conversions and
  // originating from iOS devices starting with iOS14.
  string gbraid = 8;

  // The click identifier for clicks associated with web conversions and
  // originating from iOS devices starting with iOS14.
  string wbraid = 9;

  // Resource name of the conversion action associated with this conversion.
  optional string conversion_action = 5;

  // The date time at which the conversion occurred. The format is
  // "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string conversion_date_time = 6;

  // The user identifiers associated with this conversion. Only hashed_email and
  // hashed_phone_number are supported for conversion uploads. The maximum
  // number of user identifiers for each conversion is 5.
  repeated google.ads.googleads.v18.common.UserIdentifier user_identifiers = 7;
}

// Identifying information for a successfully processed CallConversionUpload.
message CallConversionResult {
  // The caller id from which this call was placed. Caller id is expected to be
  // in E.164 format with preceding '+' sign.
  optional string caller_id = 5;

  // The date time at which the call occurred. The format is
  // "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string call_start_date_time = 6;

  // Resource name of the conversion action associated with this conversion.
  optional string conversion_action = 7;

  // The date time at which the conversion occurred. The format is
  // "yyyy-mm-dd hh:mm:ss+|-hh:mm", for example, "2019-01-01 12:32:45-08:00".
  optional string conversion_date_time = 8;
}

// A custom variable.
message CustomVariable {
  // Resource name of the custom variable associated with this conversion.
  // Note: Although this resource name consists of a customer id and a
  // conversion custom variable id, validation will ignore the customer id and
  // use the conversion custom variable id as the sole identifier of the
  // conversion custom variable.
  string conversion_custom_variable = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ConversionCustomVariable"
  }];

  // The value string of this custom variable.
  // The value of the custom variable should not contain private customer data,
  // such as email addresses or phone numbers.
  string value = 2;
}

// Contains additional information about cart data.
message CartData {
  // Contains data of the items purchased.
  message Item {
    // The shopping id of the item. Must be equal to the Merchant Center product
    // identifier.
    string product_id = 1;

    // Number of items sold.
    int32 quantity = 2;

    // Unit price excluding tax, shipping, and any transaction
    // level discounts. The currency code is the same as that in the
    // ClickConversion message.
    double unit_price = 3;
  }

  // The Merchant Center ID where the items are uploaded.
  int64 merchant_id = 6;

  // The country code associated with the feed where the items are uploaded.
  string feed_country_code = 2;

  // The language code associated with the feed where the items are uploaded.
  string feed_language_code = 3;

  // Sum of all transaction level discounts, such as free shipping and
  // coupon discounts for the whole cart. The currency code is the same
  // as that in the ClickConversion message.
  double local_transaction_cost = 4;

  // Data of the items purchased.
  repeated Item items = 5;
}
