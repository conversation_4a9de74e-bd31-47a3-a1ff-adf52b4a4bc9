// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/enums/response_content_type.proto";
import "google/ads/googleads/v18/resources/customer_asset_set.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerAssetSetServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// Proto file describing the CustomerAssetSet service.

// Service to manage customer asset set
service CustomerAssetSetService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, or removes customer asset sets. Operation statuses are
  // returned.
  rpc MutateCustomerAssetSets(MutateCustomerAssetSetsRequest)
      returns (MutateCustomerAssetSetsResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/customerAssetSets:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [CustomerAssetSetService.MutateCustomerAssetSets][google.ads.googleads.v18.services.CustomerAssetSetService.MutateCustomerAssetSets].
message MutateCustomerAssetSetsRequest {
  // Required. The ID of the customer whose customer asset sets are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual customer asset
  // sets.
  repeated CustomerAssetSetOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v18.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 5;
}

// A single operation (create, remove) on a customer asset set.
message CustomerAssetSetOperation {
  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new customer asset
    // set.
    google.ads.googleads.v18.resources.CustomerAssetSet create = 1;

    // Remove operation: A resource name for the removed customer asset set is
    // expected, in this format:
    // `customers/{customer_id}/customerAssetSets/{asset_set_id}`
    string remove = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerAssetSet"
    }];
  }
}

// Response message for a customer asset set mutate.
message MutateCustomerAssetSetsResponse {
  // All results for the mutate.
  repeated MutateCustomerAssetSetResult results = 1;

  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (e.g. auth errors),
  // we return an RPC level error.
  google.rpc.Status partial_failure_error = 2;
}

// The result for the customer asset set mutate.
message MutateCustomerAssetSetResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomerAssetSet"
  }];

  // The mutated customer asset set with only mutable fields after mutate. The
  // field will only be returned when response_content_type is set to
  // "MUTABLE_RESOURCE".
  google.ads.googleads.v18.resources.CustomerAssetSet customer_asset_set = 2;
}
