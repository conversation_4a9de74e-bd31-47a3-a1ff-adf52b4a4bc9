// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesLeadServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// This service allows management of LocalServicesLead resources.
service LocalServicesLeadService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // RPC to append Local Services Lead Conversation resources to Local Services
  // Lead resources.
  rpc AppendLeadConversation(AppendLeadConversationRequest)
      returns (AppendLeadConversationResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/localServices:appendLeadConversation"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,conversations";
  }
}

// Request message for
// [LocalServicesLeadService.AppendLeadConversation][google.ads.googleads.v18.services.LocalServicesLeadService.AppendLeadConversation].
message AppendLeadConversationRequest {
  // Required. The Id of the customer which owns the leads onto which the
  // conversations will be appended.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Conversations that are being appended.
  repeated Conversation conversations = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [LocalServicesLeadService.AppendLeadConversation][google.ads.googleads.v18.services.LocalServicesLeadService.AppendLeadConversation].
message AppendLeadConversationResponse {
  // Required. List of append conversation operation results.
  repeated ConversationOrError responses = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Details of the conversation that needs to be appended.
message Conversation {
  // Required. The resource name of the local services lead that the
  // conversation should be applied to.
  string local_services_lead = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LocalServicesLead"
    }
  ];

  // Required. Text message that user wanted to append to lead.
  string text = 2 [(google.api.field_behavior) = REQUIRED];
}

// Result of the append conversation operation.
message ConversationOrError {
  // Append operation response
  oneof append_lead_conversation_response {
    // The resource name of the appended local services lead conversation.
    string local_services_lead_conversation = 1;

    // Failure status when the request could not be processed.
    google.rpc.Status partial_failure_error = 2;
  }
}
