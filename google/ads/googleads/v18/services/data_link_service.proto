// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.services;

import "google/ads/googleads/v18/resources/data_link.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/services;services";
option java_multiple_files = true;
option java_outer_classname = "DataLinkServiceProto";
option java_package = "com.google.ads.googleads.v18.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Services";
option ruby_package = "Google::Ads::GoogleAds::V18::Services";

// This service allows management of data links between  a Google
// Ads customer and another data entity.
service DataLinkService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates a data link. The requesting Google Ads account name and account ID
  // will be shared with the third party (such as YouTube creators for video
  // links) to whom you are creating the link with. Only customers on the
  // allow-list can create data links.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [DataLinkError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc CreateDataLink(CreateDataLinkRequest) returns (CreateDataLinkResponse) {
    option (google.api.http) = {
      post: "/v18/customers/{customer_id=*}/dataLinks:create"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,data_link";
  }
}

// Request message for
// [DataLinkService.CreateDataLink][google.ads.googleads.v18.services.DataLinkService.CreateDataLink].
message CreateDataLinkRequest {
  // Required. The ID of the customer for which the data link is created.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The data link to be created.
  google.ads.googleads.v18.resources.DataLink data_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [DataLinkService.CreateDataLink][google.ads.googleads.v18.services.DataLinkService.CreateDataLink].
message CreateDataLinkResponse {
  // Returned for successful operations. Resource name of the data link.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/DataLink"
  }];
}
