// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.common;

import "google/ads/googleads/v18/common/policy.proto";
import "google/ads/googleads/v18/enums/policy_approval_status.proto";
import "google/ads/googleads/v18/enums/policy_review_status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/common;common";
option java_multiple_files = true;
option java_outer_classname = "PolicySummaryProto";
option java_package = "com.google.ads.googleads.v18.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Common";
option ruby_package = "Google::Ads::GoogleAds::V18::Common";

// Proto file describing policy summary.

// Contains policy summary information.
message PolicySummary {
  // The list of policy findings.
  repeated PolicyTopicEntry policy_topic_entries = 1;

  // Where in the review process the resource is.
  google.ads.googleads.v18.enums.PolicyReviewStatusEnum.PolicyReviewStatus
      review_status = 2;

  // The overall approval status, which is calculated based on
  // the status of its individual policy topic entries.
  google.ads.googleads.v18.enums.PolicyApprovalStatusEnum.PolicyApprovalStatus
      approval_status = 3;
}
