// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.common;

import "google/ads/googleads/v18/enums/chain_relationship_type.proto";
import "google/ads/googleads/v18/enums/location_ownership_type.proto";
import "google/ads/googleads/v18/enums/location_string_filter_type.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/common;common";
option java_multiple_files = true;
option java_outer_classname = "AssetSetTypesProto";
option java_package = "com.google.ads.googleads.v18.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Common";
option ruby_package = "Google::Ads::GoogleAds::V18::Common";

// Proto file containing info messages for specific asset set types.

// Data related to location set. One of the Google Business Profile (previously
// known as Google My Business) data, Chain data, and map location data need to
// be specified.
message LocationSet {
  // Required. Immutable. Location Ownership Type (owned location or affiliate
  // location).
  google.ads.googleads.v18.enums.LocationOwnershipTypeEnum.LocationOwnershipType
      location_ownership_type = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Location data specific to each sync source.
  oneof source {
    // Data used to configure a location set populated from Google Business
    // Profile locations.
    BusinessProfileLocationSet business_profile_location_set = 1;

    // Data used to configure a location on chain set populated with the
    // specified chains.
    ChainSet chain_location_set = 2;

    // Only set if locations are synced based on selected maps locations
    MapsLocationSet maps_location_set = 5;
  }
}

// Data used to configure a location set populated from Google Business Profile
// locations.
// Different types of filters are AND'ed together, if they are specified.
message BusinessProfileLocationSet {
  // Required. Immutable. The HTTP authorization token used to obtain
  // authorization.
  string http_authorization_token = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. Immutable. Email address of a Google Business Profile account or
  // email address of a manager of the Google Business Profile account.
  string email_address = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Used to filter Google Business Profile listings by business name. If
  // businessNameFilter is set, only listings with a matching business name are
  // candidates to be sync'd into Assets.
  string business_name_filter = 3;

  // Used to filter Google Business Profile listings by labels. If entries exist
  // in labelFilters, only listings that have any of the labels set are
  // candidates to be synchronized into Assets. If no entries exist in
  // labelFilters, then all listings are candidates for syncing.
  // Label filters are OR'ed together.
  repeated string label_filters = 4;

  // Used to filter Google Business Profile listings by listing id. If entries
  // exist in listingIdFilters, only listings specified by the filters are
  // candidates to be synchronized into Assets. If no entries exist in
  // listingIdFilters, then all listings are candidates for syncing.
  // Listing ID filters are OR'ed together.
  repeated int64 listing_id_filters = 5;

  // Immutable. The account ID of the managed business whose locations are to be
  // used. If this field is not set, then all businesses accessible by the user
  // (specified by the emailAddress) are used.
  string business_account_id = 6 [(google.api.field_behavior) = IMMUTABLE];
}

// Data used to configure a location set populated with the specified chains.
message ChainSet {
  // Required. Immutable. Relationship type the specified chains have with this
  // advertiser.
  google.ads.googleads.v18.enums.ChainRelationshipTypeEnum.ChainRelationshipType
      relationship_type = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. A list of chain level filters, all filters are OR'ed together.
  repeated ChainFilter chains = 2 [(google.api.field_behavior) = REQUIRED];
}

// One chain level filter on location in a feed item set.
// The filtering logic among all the fields is AND.
message ChainFilter {
  // Required. Used to filter chain locations by chain id. Only chain locations
  // that belong to the specified chain will be in the asset set.
  int64 chain_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Used to filter chain locations by location attributes.
  // Only chain locations that belong to all of the specified attribute(s) will
  // be in the asset set. If this field is empty, it means no filtering on this
  // field.
  repeated string location_attributes = 2;
}

// Wrapper for multiple maps location sync data
message MapsLocationSet {
  // Required. A list of maps location info that user manually synced in.
  repeated MapsLocationInfo maps_locations = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Wrapper for place ids
message MapsLocationInfo {
  // Place ID of the Maps location.
  string place_id = 1;
}

// Information about a Business Profile dynamic location group.
// Only applicable if the sync level AssetSet's type is LOCATION_SYNC and
// sync source is Business Profile.
message BusinessProfileLocationGroup {
  // Filter for dynamic Business Profile location sets.
  DynamicBusinessProfileLocationGroupFilter
      dynamic_business_profile_location_group_filter = 1;
}

// Represents a filter on Business Profile locations in an asset set. If
// multiple filters are provided, they are AND'ed together.
message DynamicBusinessProfileLocationGroupFilter {
  // Used to filter Business Profile locations by label. Only locations that
  // have any of the listed labels will be in the asset set.
  // Label filters are OR'ed together.
  repeated string label_filters = 1;

  // Used to filter Business Profile locations by business name.
  optional BusinessProfileBusinessNameFilter business_name_filter = 2;

  // Used to filter Business Profile locations by listing ids.
  repeated int64 listing_id_filters = 3;
}

// Business Profile location group business name filter.
message BusinessProfileBusinessNameFilter {
  // Business name string to use for filtering.
  string business_name = 1;

  // The type of string matching to use when filtering with business_name.
  google.ads.googleads.v18.enums.LocationStringFilterTypeEnum
      .LocationStringFilterType filter_type = 2;
}

// Represents information about a Chain dynamic location group.
// Only applicable if the sync level AssetSet's type is LOCATION_SYNC and
// sync source is chain.
message ChainLocationGroup {
  // Used to filter chain locations by chain ids.
  // Only Locations that belong to the specified chain(s) will be in the asset
  // set.
  repeated ChainFilter dynamic_chain_location_group_filters = 1;
}
