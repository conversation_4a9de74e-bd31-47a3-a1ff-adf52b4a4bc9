// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.common;

import "google/ads/googleads/v18/enums/gender_type.proto";
import "google/ads/googleads/v18/enums/income_range_type.proto";
import "google/ads/googleads/v18/enums/parental_status_type.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/common;common";
option java_multiple_files = true;
option java_outer_classname = "AudiencesProto";
option java_package = "com.google.ads.googleads.v18.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Common";
option ruby_package = "Google::Ads::GoogleAds::V18::Common";

// Positive dimension specifying user's audience.
message AudienceDimension {
  // Dimension specifying users who belong to the audience.
  oneof dimension {
    // Dimension specifying users by their age.
    AgeDimension age = 1;

    // Dimension specifying users by their gender.
    GenderDimension gender = 2;

    // Dimension specifying users by their household income.
    HouseholdIncomeDimension household_income = 3;

    // Dimension specifying users by their parental status.
    ParentalStatusDimension parental_status = 4;

    // Dimension specifying users by their membership in other audience
    // segments.
    AudienceSegmentDimension audience_segments = 5;
  }
}

// Negative dimension specifying users to exclude from the audience.
message AudienceExclusionDimension {
  // Audience segment to be excluded.
  repeated ExclusionSegment exclusions = 1;
}

// An audience segment to be excluded from an audience.
message ExclusionSegment {
  // Segment to be excluded.
  oneof segment {
    // User list segment to be excluded.
    UserListSegment user_list = 1;
  }
}

// Dimension specifying users by their age.
message AgeDimension {
  // Contiguous age range to be included in the dimension.
  repeated AgeSegment age_ranges = 1;

  // Include users whose age is not determined.
  optional bool include_undetermined = 2;
}

// Contiguous age range.
message AgeSegment {
  // Minimum age to include. A minimum age must be specified and must be at
  // least 18. Allowed values are 18, 25, 35, 45, 55, and 65.
  optional int32 min_age = 1;

  // Maximum age to include. A maximum age need not be specified. If specified,
  // max_age must be greater than min_age, and allowed values are 24, 34, 44,
  // 54, and 64.
  optional int32 max_age = 2;
}

// Dimension specifying users by their gender.
message GenderDimension {
  // Included gender demographic segments.
  repeated google.ads.googleads.v18.enums.GenderTypeEnum.GenderType genders = 1;

  // Include users whose gender is not determined.
  optional bool include_undetermined = 2;
}

// Dimension specifying users by their household income.
message HouseholdIncomeDimension {
  // Included household income demographic segments.
  repeated google.ads.googleads.v18.enums.IncomeRangeTypeEnum.IncomeRangeType
      income_ranges = 1;

  // Include users whose household income is not determined.
  optional bool include_undetermined = 2;
}

// Dimension specifying users by their parental status.
message ParentalStatusDimension {
  // Included parental status demographic segments.
  repeated
      google.ads.googleads.v18.enums.ParentalStatusTypeEnum.ParentalStatusType
          parental_statuses = 1;

  // Include users whose parental status is undetermined.
  optional bool include_undetermined = 2;
}

// Dimension specifying users by their membership in other audience segments.
message AudienceSegmentDimension {
  // Included audience segments. Users are included if they belong to at least
  // one segment.
  repeated AudienceSegment segments = 1;
}

// Positive audience segment.
message AudienceSegment {
  // Positive segment.
  oneof segment {
    // User list segment.
    UserListSegment user_list = 1;

    // Affinity or In-market segment.
    UserInterestSegment user_interest = 2;

    // Live-event audience segment.
    LifeEventSegment life_event = 3;

    // Detailed demographic segment.
    DetailedDemographicSegment detailed_demographic = 4;

    // Custom audience segment.
    CustomAudienceSegment custom_audience = 5;
  }
}

// User list segment.
message UserListSegment {
  // The user list resource.
  optional string user_list = 1;
}

// User interest segment.
message UserInterestSegment {
  // The user interest resource.
  optional string user_interest_category = 1;
}

// Live event segment.
message LifeEventSegment {
  // The life event resource.
  optional string life_event = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/LifeEvent"
  }];
}

// Detailed demographic segment.
message DetailedDemographicSegment {
  // The detailed demographic resource.
  optional string detailed_demographic = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/DetailedDemographic"
  }];
}

// Custom audience segment.
message CustomAudienceSegment {
  // The custom audience resource.
  optional string custom_audience = 1;
}
