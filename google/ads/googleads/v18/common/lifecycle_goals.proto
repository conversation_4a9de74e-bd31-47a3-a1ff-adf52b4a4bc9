// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.common;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/common;common";
option java_multiple_files = true;
option java_outer_classname = "LifecycleGoalsProto";
option java_package = "com.google.ads.googleads.v18.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Common";
option ruby_package = "Google::Ads::GoogleAds::V18::Common";

// Proto file describing lifecycle goal settings.

// Lifecycle goal value settings.
message LifecycleGoalValueSettings {
  // Value of the lifecycle goal. For example, for customer acquisition goal,
  // value is the incremental conversion value for new customers who are not of
  // high value.
  optional double value = 1;

  // High lifetime value of the lifecycle goal. For example, for customer
  // acquisition goal, high lifetime value is the incremental conversion value
  // for new customers who are of high value. High lifetime value should be
  // greater than value, if set.
  // In current stage, high lifetime value feature is in beta and this field
  // is read-only.
  optional double high_lifetime_value = 2;
}
