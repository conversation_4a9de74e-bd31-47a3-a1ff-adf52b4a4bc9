// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.common;

import "google/ads/googleads/v18/common/criteria.proto";
import "google/ads/googleads/v18/enums/audience_insights_dimension.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/common;common";
option java_multiple_files = true;
option java_outer_classname = "AudienceInsightsAttributeProto";
option java_package = "com.google.ads.googleads.v18.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Common";
option ruby_package = "Google::Ads::GoogleAds::V18::Common";

// Proto file describing audience insights attributes.

// An audience attribute, with metadata about it, returned in response to a
// search.
message AudienceInsightsAttributeMetadata {
  // The type of the attribute.
  google.ads.googleads.v18.enums.AudienceInsightsDimensionEnum
      .AudienceInsightsDimension dimension = 1;

  // The attribute itself.
  AudienceInsightsAttribute attribute = 2;

  // The human-readable name of the attribute.
  string display_name = 3;

  // A string that supplements the display_name to identify the attribute.
  // If the dimension is TOPIC, this is a brief description of the
  // Knowledge Graph entity, such as "American singer-songwriter".
  // If the dimension is CATEGORY, this is the complete path to the category in
  // The Product & Service taxonomy, for example
  // "/Apparel/Clothing/Outerwear".
  string display_info = 4;

  // An estimate of the number of reachable YouTube users matching this
  // attribute in the requested location, or zero if that information is not
  // available for this attribute. This field is not populated in every
  // response.
  int64 potential_youtube_reach = 8;

  // The share of subscribers within this attribute, between and including 0 and
  // 1. This field is not populated in every response.
  double subscriber_share = 9;

  // Metadata specific to the dimension of this attribute.
  oneof dimension_metadata {
    // Special metadata for a YouTube channel.
    YouTubeChannelAttributeMetadata youtube_channel_metadata = 5;

    // Special metadata for a YouTube Dynamic Lineup.
    DynamicLineupAttributeMetadata dynamic_attribute_metadata = 6;

    // Special metadata for a Location.
    LocationAttributeMetadata location_attribute_metadata = 7;
  }
}

// An audience attribute that can be used to request insights about the
// audience.
message AudienceInsightsAttribute {
  // An audience attribute.
  oneof attribute {
    // An audience attribute defined by an age range.
    AgeRangeInfo age_range = 1;

    // An audience attribute defined by a gender.
    GenderInfo gender = 2;

    // An audience attribute defined by a geographic location.
    LocationInfo location = 3;

    // An Affinity or In-Market audience.
    UserInterestInfo user_interest = 4;

    // An audience attribute defined by interest in a topic represented by a
    // Knowledge Graph entity.
    AudienceInsightsEntity entity = 5;

    // An audience attribute defined by interest in a Product & Service
    // category.
    AudienceInsightsCategory category = 6;

    // A YouTube Dynamic Lineup.
    AudienceInsightsDynamicLineup dynamic_lineup = 7;

    // A Parental Status value (parent, or not a parent).
    ParentalStatusInfo parental_status = 8;

    // A household income percentile range.
    IncomeRangeInfo income_range = 9;

    // A YouTube channel.
    YouTubeChannelInfo youtube_channel = 10;
  }
}

// A Knowledge Graph entity, represented by its machine id.
message AudienceInsightsEntity {
  // Required. The machine ID (mid) of the Knowledge Graph entity.
  string knowledge_graph_machine_id = 1
      [(google.api.field_behavior) = REQUIRED];
}

// A Product and Service category.
message AudienceInsightsCategory {
  // Required. The criterion ID of the category.
  string category_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// A YouTube Dynamic Lineup.
message AudienceInsightsDynamicLineup {
  // Required. The numeric ID of the dynamic lineup.
  string dynamic_lineup_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// Metadata associated with a YouTube channel attribute.
message YouTubeChannelAttributeMetadata {
  // The approximate number of subscribers to the YouTube channel.
  int64 subscriber_count = 1;
}

// Metadata associated with a Dynamic Lineup attribute.
message DynamicLineupAttributeMetadata {
  // A YouTube channel returned as an example of the content in a lineup.
  message SampleChannel {
    // A YouTube channel.
    YouTubeChannelInfo youtube_channel = 1;

    // The name of the sample channel.
    string display_name = 2;

    // Metadata for the sample channel.
    YouTubeChannelAttributeMetadata youtube_channel_metadata = 3;
  }

  // The national market associated with the lineup.
  LocationInfo inventory_country = 1;

  // The median number of impressions per month on this lineup.
  optional int64 median_monthly_inventory = 2;

  // The lower end of a range containing the number of channels in the lineup.
  optional int64 channel_count_lower_bound = 3;

  // The upper end of a range containing the number of channels in the lineup.
  optional int64 channel_count_upper_bound = 4;

  // Examples of channels that are included in the lineup.
  repeated SampleChannel sample_channels = 5;
}

// Metadata associated with a Location attribute.
message LocationAttributeMetadata {
  // The country location that this attribute’s sub country location is located
  // in.
  LocationInfo country_location = 1;
}
