// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ConversionActionCategoryProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Container for enum describing the category of conversions that are associated
// with a ConversionAction.
message ConversionActionCategoryEnum {
  // The category of conversions that are associated with a ConversionAction.
  enum ConversionActionCategory {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Default category.
    DEFAULT = 2;

    // User visiting a page.
    PAGE_VIEW = 3;

    // Purchase, sales, or "order placed" event.
    PURCHASE = 4;

    // Signup user action.
    SIGNUP = 5;

    // Software download action (as for an app).
    DOWNLOAD = 7;

    // The addition of items to a shopping cart or bag on an advertiser site.
    ADD_TO_CART = 8;

    // When someone enters the checkout flow on an advertiser site.
    BEGIN_CHECKOUT = 9;

    // The start of a paid subscription for a product or service.
    SUBSCRIBE_PAID = 10;

    // A call to indicate interest in an advertiser's offering.
    PHONE_CALL_LEAD = 11;

    // A lead conversion imported from an external source into Google Ads.
    IMPORTED_LEAD = 12;

    // A submission of a form on an advertiser site indicating business
    // interest.
    SUBMIT_LEAD_FORM = 13;

    // A booking of an appointment with an advertiser's business.
    BOOK_APPOINTMENT = 14;

    // A quote or price estimate request.
    REQUEST_QUOTE = 15;

    // A search for an advertiser's business location with intention to visit.
    GET_DIRECTIONS = 16;

    // A click to an advertiser's partner's site.
    OUTBOUND_CLICK = 17;

    // A call, SMS, email, chat or other type of contact to an advertiser.
    CONTACT = 18;

    // A website engagement event such as long site time or a Google Analytics
    // (GA) Smart Goal. Intended to be used for GA, Firebase, GA Gold goal
    // imports.
    ENGAGEMENT = 19;

    // A visit to a physical store location.
    STORE_VISIT = 20;

    // A sale occurring in a physical store.
    STORE_SALE = 21;

    // A lead conversion imported from an external source into Google Ads,
    // that has been further qualified by the advertiser (marketing/sales team).
    // In the lead-to-sale journey, advertisers get leads, then act on them
    // by reaching out to the consumer. If the consumer is interested and
    // may end up buying their product, the advertiser marks such leads as
    // "qualified leads".
    QUALIFIED_LEAD = 22;

    // A lead conversion imported from an external source into Google Ads, that
    // has further completed a chosen stage as defined by the lead gen
    // advertiser.
    CONVERTED_LEAD = 23;
  }
}
