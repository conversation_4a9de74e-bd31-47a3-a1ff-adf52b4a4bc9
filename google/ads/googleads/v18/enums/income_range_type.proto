// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "IncomeRangeTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing income range types.

// Container for enum describing the type of demographic income ranges.
message IncomeRangeTypeEnum {
  // The type of demographic income ranges (for example, between 0% to 50%).
  enum IncomeRangeType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // 0%-50%.
    INCOME_RANGE_0_50 = 510001;

    // 50% to 60%.
    INCOME_RANGE_50_60 = 510002;

    // 60% to 70%.
    INCOME_RANGE_60_70 = 510003;

    // 70% to 80%.
    INCOME_RANGE_70_80 = 510004;

    // 80% to 90%.
    INCOME_RANGE_80_90 = 510005;

    // Greater than 90%.
    INCOME_RANGE_90_UP = 510006;

    // Undetermined income range.
    INCOME_RANGE_UNDETERMINED = 510000;
  }
}
