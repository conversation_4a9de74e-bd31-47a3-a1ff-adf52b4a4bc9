// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "TrackingCodeTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Container for enum describing the type of the generated tag snippets for
// tracking conversions.
message TrackingCodeTypeEnum {
  // The type of the generated tag snippets for tracking conversions.
  enum TrackingCodeType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The snippet that is fired as a result of a website page loading.
    WEBPAGE = 2;

    // The snippet contains a JavaScript function which fires the tag. This
    // function is typically called from an onClick handler added to a link or
    // button element on the page.
    WEBPAGE_ONCLICK = 3;

    // For embedding on a mobile webpage. The snippet contains a JavaScript
    // function which fires the tag.
    CLICK_TO_CALL = 4;

    // The snippet that is used to replace the phone number on your website with
    // a Google forwarding number for call tracking purposes.
    WEBSITE_CALL = 5;
  }
}
