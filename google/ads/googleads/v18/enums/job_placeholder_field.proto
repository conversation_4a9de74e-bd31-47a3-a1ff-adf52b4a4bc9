// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "JobsPlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing Job placeholder fields.

// Values for Job placeholder fields.
// For more information about dynamic remarketing feeds, see
// https://support.google.com/google-ads/answer/6053288.
message JobPlaceholderFieldEnum {
  // Possible values for Job placeholder fields.
  enum JobPlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: STRING. Required. If only JOB_ID is specified, then it must be
    // unique. If both JOB_ID and LOCATION_ID are specified, then the
    // pair must be unique.
    // ID) pair must be unique.
    JOB_ID = 2;

    // Data Type: STRING. Combination of JOB_ID and LOCATION_ID must be unique
    // per offer.
    LOCATION_ID = 3;

    // Data Type: STRING. Required. Main headline with job title to be shown in
    // dynamic ad.
    TITLE = 4;

    // Data Type: STRING. Job subtitle to be shown in dynamic ad.
    SUBTITLE = 5;

    // Data Type: STRING. Description of job to be shown in dynamic ad.
    DESCRIPTION = 6;

    // Data Type: URL. Image to be displayed in the ad. Highly recommended for
    // image ads.
    IMAGE_URL = 7;

    // Data Type: STRING. Category of property used to group like items together
    // for recommendation engine.
    CATEGORY = 8;

    // Data Type: STRING_LIST. Keywords used for product retrieval.
    CONTEXTUAL_KEYWORDS = 9;

    // Data Type: STRING. Complete property address, including postal code.
    ADDRESS = 10;

    // Data Type: STRING. Salary or salary range of job to be shown in dynamic
    // ad.
    SALARY = 11;

    // Data Type: URL_LIST. Required. Final URLs to be used in ad when using
    // Upgraded URLs; the more specific the better (for example, the individual
    // URL of a specific job and its location).
    FINAL_URLS = 12;

    // Data Type: URL_LIST. Final mobile URLs for the ad when using Upgraded
    // URLs.
    FINAL_MOBILE_URLS = 14;

    // Data Type: URL. Tracking template for the ad when using Upgraded URLs.
    TRACKING_URL = 15;

    // Data Type: STRING. Android app link. Must be formatted as:
    // android-app://{package_id}/{scheme}/{host_path}.
    // The components are defined as follows:
    // package_id: app ID as specified in Google Play.
    // scheme: the scheme to pass to the application. Can be HTTP, or a custom
    //   scheme.
    // host_path: identifies the specific content within your application.
    ANDROID_APP_LINK = 16;

    // Data Type: STRING_LIST. List of recommended job IDs to show together with
    // this item.
    SIMILAR_JOB_IDS = 17;

    // Data Type: STRING. iOS app link.
    IOS_APP_LINK = 18;

    // Data Type: INT64. iOS app store ID.
    IOS_APP_STORE_ID = 19;
  }
}
