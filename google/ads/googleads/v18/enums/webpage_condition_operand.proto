// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "WebpageConditionOperandProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing webpage condition operand.

// Container for enum describing webpage condition operand in webpage criterion.
message WebpageConditionOperandEnum {
  // The webpage condition operand in webpage criterion.
  enum WebpageConditionOperand {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Operand denoting a webpage URL targeting condition.
    URL = 2;

    // Operand denoting a webpage category targeting condition.
    CATEGORY = 3;

    // Operand denoting a webpage title targeting condition.
    PAGE_TITLE = 4;

    // Operand denoting a webpage content targeting condition.
    PAGE_CONTENT = 5;

    // Operand denoting a webpage custom label targeting condition.
    CUSTOM_LABEL = 6;
  }
}
