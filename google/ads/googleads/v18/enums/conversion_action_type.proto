// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ConversionActionTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing conversion action type.

// Container for enum describing possible types of a conversion action.
message ConversionActionTypeEnum {
  // Possible types of a conversion action.
  enum ConversionActionType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Conversions that occur when a user clicks on an ad's call extension.
    AD_CALL = 2;

    // Conversions that occur when a user on a mobile device clicks a phone
    // number.
    CLICK_TO_CALL = 3;

    // Conversions that occur when a user downloads a mobile app from the Google
    // Play Store.
    GOOGLE_PLAY_DOWNLOAD = 4;

    // Conversions that occur when a user makes a purchase in an app through
    // Android billing.
    GOOGLE_PLAY_IN_APP_PURCHASE = 5;

    // Call conversions that are tracked by the advertiser and uploaded.
    UPLOAD_CALLS = 6;

    // Conversions that are tracked by the advertiser and uploaded with
    // attributed clicks.
    UPLOAD_CLICKS = 7;

    // Conversions that occur on a webpage.
    WEBPAGE = 8;

    // Conversions that occur when a user calls a dynamically-generated phone
    // number from an advertiser's website.
    WEBSITE_CALL = 9;

    // Store Sales conversion based on first-party or third-party merchant
    // data uploads.
    // Only customers on the allowlist can use store sales direct upload types.
    STORE_SALES_DIRECT_UPLOAD = 10;

    // Store Sales conversion based on first-party or third-party merchant
    // data uploads and/or from in-store purchases using cards from payment
    // networks.
    // Only customers on the allowlist can use store sales types.
    // Read only.
    STORE_SALES = 11;

    // Android app first open conversions tracked through Firebase.
    FIREBASE_ANDROID_FIRST_OPEN = 12;

    // Android app in app purchase conversions tracked through Firebase.
    FIREBASE_ANDROID_IN_APP_PURCHASE = 13;

    // Android app custom conversions tracked through Firebase.
    FIREBASE_ANDROID_CUSTOM = 14;

    // iOS app first open conversions tracked through Firebase.
    FIREBASE_IOS_FIRST_OPEN = 15;

    // iOS app in app purchase conversions tracked through Firebase.
    FIREBASE_IOS_IN_APP_PURCHASE = 16;

    // iOS app custom conversions tracked through Firebase.
    FIREBASE_IOS_CUSTOM = 17;

    // Android app first open conversions tracked through Third Party App
    // Analytics.
    THIRD_PARTY_APP_ANALYTICS_ANDROID_FIRST_OPEN = 18;

    // Android app in app purchase conversions tracked through Third Party App
    // Analytics.
    THIRD_PARTY_APP_ANALYTICS_ANDROID_IN_APP_PURCHASE = 19;

    // Android app custom conversions tracked through Third Party App Analytics.
    THIRD_PARTY_APP_ANALYTICS_ANDROID_CUSTOM = 20;

    // iOS app first open conversions tracked through Third Party App Analytics.
    THIRD_PARTY_APP_ANALYTICS_IOS_FIRST_OPEN = 21;

    // iOS app in app purchase conversions tracked through Third Party App
    // Analytics.
    THIRD_PARTY_APP_ANALYTICS_IOS_IN_APP_PURCHASE = 22;

    // iOS app custom conversions tracked through Third Party App Analytics.
    THIRD_PARTY_APP_ANALYTICS_IOS_CUSTOM = 23;

    // Conversions that occur when a user pre-registers a mobile app from the
    // Google Play Store. Read only.
    ANDROID_APP_PRE_REGISTRATION = 24;

    // Conversions that track all Google Play downloads which aren't tracked
    // by an app-specific type. Read only.
    ANDROID_INSTALLS_ALL_OTHER_APPS = 25;

    // Floodlight activity that counts the number of times that users have
    // visited a particular webpage after seeing or clicking on one of
    // an advertiser's ads. Read only.
    FLOODLIGHT_ACTION = 26;

    // Floodlight activity that tracks the number of sales made or the number
    // of items purchased. Can also capture the total value of each sale.
    // Read only.
    FLOODLIGHT_TRANSACTION = 27;

    // Conversions that track local actions from Google's products and
    // services after interacting with an ad. Read only.
    GOOGLE_HOSTED = 28;

    // Conversions reported when a user submits a lead form. Read only.
    LEAD_FORM_SUBMIT = 29;

    // Conversions that come from Salesforce. Read only.
    SALESFORCE = 30;

    // Conversions imported from Search Ads 360 Floodlight data. Read only.
    SEARCH_ADS_360 = 31;

    // Call conversions that occur on Smart campaign Ads without call tracking
    // setup, using Smart campaign custom criteria. Read only.
    SMART_CAMPAIGN_AD_CLICKS_TO_CALL = 32;

    // The user clicks on a call element within Google Maps. Smart campaign
    // only. Read only.
    SMART_CAMPAIGN_MAP_CLICKS_TO_CALL = 33;

    // The user requests directions to a business location within Google Maps.
    // Smart campaign only. Read only.
    SMART_CAMPAIGN_MAP_DIRECTIONS = 34;

    // Call conversions that occur on Smart campaign Ads with call tracking
    // setup, using Smart campaign custom criteria. Read only.
    SMART_CAMPAIGN_TRACKED_CALLS = 35;

    // Conversions that occur when a user visits an advertiser's retail store.
    // Read only.
    STORE_VISITS = 36;

    // Conversions created from website events (such as form submissions or page
    // loads), that don't use individually coded event snippets. Read only.
    WEBPAGE_CODELESS = 37;

    // Conversions that come from linked Universal Analytics goals.
    UNIVERSAL_ANALYTICS_GOAL = 38;

    // Conversions that come from linked Universal Analytics transactions.
    UNIVERSAL_ANALYTICS_TRANSACTION = 39;

    // Conversions that come from linked Google Analytics 4 custom event
    // conversions.
    GOOGLE_ANALYTICS_4_CUSTOM = 40;

    // Conversions that come from linked Google Analytics 4 purchase
    // conversions.
    GOOGLE_ANALYTICS_4_PURCHASE = 41;
  }
}
