// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SmartCampaignStatusProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing Smart campaign statuses.

// A container for an enum that describes Smart campaign statuses.
message SmartCampaignStatusEnum {
  // Smart campaign statuses.
  enum SmartCampaignStatus {
    // The status has not been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // The campaign was paused.
    PAUSED = 2;

    // The campaign is not eligible to serve and has issues that may require
    // intervention.
    NOT_ELIGIBLE = 3;

    // The campaign is pending the approval of at least one ad.
    PENDING = 4;

    // The campaign is eligible to serve.
    ELIGIBLE = 5;

    // The campaign has been removed.
    REMOVED = 6;

    // The campaign has ended.
    ENDED = 7;
  }
}
