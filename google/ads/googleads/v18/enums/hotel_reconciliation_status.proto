// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "HotelReconciliationStatusProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing hotel reconciliation row status.

// Container for HotelReconciliationStatus.
message HotelReconciliationStatusEnum {
  // Status of the hotel booking reconciliation.
  enum HotelReconciliationStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // Bookings are for a future date, or a stay is underway but the check-out
    // date hasn't passed. An active reservation can't be reconciled.
    RESERVATION_ENABLED = 2;

    // Check-out has already taken place, or the booked dates have passed
    // without cancellation. Bookings that are not reconciled within 45 days of
    // the check-out date are billed based on the original booking price.
    RECONCILIATION_NEEDED = 3;

    // These bookings have been reconciled. Reconciled bookings are billed 45
    // days after the check-out date.
    RECONCILED = 4;

    // This booking was marked as canceled. Canceled stays with a value greater
    // than zero (due to minimum stay rules or cancellation fees) are billed 45
    // days after the check-out date.
    CANCELED = 5;
  }
}
