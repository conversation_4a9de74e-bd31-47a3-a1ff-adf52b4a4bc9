// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LocalPlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing Local placeholder fields.

// Values for Local placeholder fields.
// For more information about dynamic remarketing feeds, see
// https://support.google.com/google-ads/answer/6053288.
message LocalPlaceholderFieldEnum {
  // Possible values for Local placeholder fields.
  enum LocalPlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: STRING. Required. Unique ID.
    DEAL_ID = 2;

    // Data Type: STRING. Required. Main headline with local deal title to be
    // shown in dynamic ad.
    DEAL_NAME = 3;

    // Data Type: STRING. Local deal subtitle to be shown in dynamic ad.
    SUBTITLE = 4;

    // Data Type: STRING. Description of local deal to be shown in dynamic ad.
    DESCRIPTION = 5;

    // Data Type: STRING. Price to be shown in the ad. Highly recommended for
    // dynamic ads. Example: "100.00 USD"
    PRICE = 6;

    // Data Type: STRING. Formatted price to be shown in the ad.
    // Example: "Starting at $100.00 USD", "$80 - $100"
    FORMATTED_PRICE = 7;

    // Data Type: STRING. Sale price to be shown in the ad.
    // Example: "80.00 USD"
    SALE_PRICE = 8;

    // Data Type: STRING. Formatted sale price to be shown in the ad.
    // Example: "On sale for $80.00", "$60 - $80"
    FORMATTED_SALE_PRICE = 9;

    // Data Type: URL. Image to be displayed in the ad.
    IMAGE_URL = 10;

    // Data Type: STRING. Complete property address, including postal code.
    ADDRESS = 11;

    // Data Type: STRING. Category of local deal used to group like items
    // together for recommendation engine.
    CATEGORY = 12;

    // Data Type: STRING_LIST. Keywords used for product retrieval.
    CONTEXTUAL_KEYWORDS = 13;

    // Data Type: URL_LIST. Required. Final URLs to be used in ad when using
    // Upgraded URLs; the more specific the better (for example, the individual
    // URL of a specific local deal and its location).
    FINAL_URLS = 14;

    // Data Type: URL_LIST. Final mobile URLs for the ad when using Upgraded
    // URLs.
    FINAL_MOBILE_URLS = 15;

    // Data Type: URL. Tracking template for the ad when using Upgraded URLs.
    TRACKING_URL = 16;

    // Data Type: STRING. Android app link. Must be formatted as:
    // android-app://{package_id}/{scheme}/{host_path}.
    // The components are defined as follows:
    // package_id: app ID as specified in Google Play.
    // scheme: the scheme to pass to the application. Can be HTTP, or a custom
    //   scheme.
    // host_path: identifies the specific content within your application.
    ANDROID_APP_LINK = 17;

    // Data Type: STRING_LIST. List of recommended local deal IDs to show
    // together with this item.
    SIMILAR_DEAL_IDS = 18;

    // Data Type: STRING. iOS app link.
    IOS_APP_LINK = 19;

    // Data Type: INT64. iOS app store ID.
    IOS_APP_STORE_ID = 20;
  }
}
