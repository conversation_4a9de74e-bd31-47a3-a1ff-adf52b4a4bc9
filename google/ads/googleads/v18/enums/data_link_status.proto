// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "DataLinkStatusProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Container for enum describing different types of data links.
message DataLinkStatusEnum {
  // Describes the possible data link statuses.
  enum DataLinkStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // Link has been requested by one party, but not confirmed by the other
    // party.
    REQUESTED = 2;

    // Link is waiting for the customer to approve.
    PENDING_APPROVAL = 3;

    // Link is established and can be used as needed.
    ENABLED = 4;

    // Link is no longer valid and should be ignored.
    DISABLED = 5;

    // Link request has been cancelled by the requester and further cleanup may
    // be needed.
    REVOKED = 6;

    // Link request has been rejected by the approver.
    REJECTED = 7;
  }
}
