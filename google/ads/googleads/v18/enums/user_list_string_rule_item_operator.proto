// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "UserListStringRuleItemOperatorProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Supported rule operator for string type.
message UserListStringRuleItemOperatorEnum {
  // Enum describing possible user list string rule item operators.
  enum UserListStringRuleItemOperator {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Contains.
    CONTAINS = 2;

    // Equals.
    EQUALS = 3;

    // Starts with.
    STARTS_WITH = 4;

    // Ends with.
    ENDS_WITH = 5;

    // Not equals.
    NOT_EQUALS = 6;

    // Not contains.
    NOT_CONTAINS = 7;

    // Not starts with.
    NOT_STARTS_WITH = 8;

    // Not ends with.
    NOT_ENDS_WITH = 9;
  }
}
