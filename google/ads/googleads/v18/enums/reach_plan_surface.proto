// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ReachPlanSurfaceProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Container for enum describing surfaces available for targeting in forecasts.
// Surfaces refer to the available types of ad inventories such as In-Feed,
// In-Stream, and Shorts.
message ReachPlanSurfaceEnum {
  // Surfaces available to target in reach forecasts.
  enum ReachPlanSurface {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // In-Feed ad surface.
    IN_FEED = 2;

    // In-Stream bumper ad surface.
    IN_STREAM_BUMPER = 3;

    // In-Stream non-skippable ad surface.
    IN_STREAM_NON_SKIPPABLE = 4;

    // In-Stream skippable ad surface.
    IN_STREAM_SKIPPABLE = 5;

    // Shorts ad surface.
    SHORTS = 6;
  }
}
