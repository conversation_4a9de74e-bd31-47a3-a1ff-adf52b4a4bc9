// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ConversionTrackingStatusEnumProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Container for enum representing the conversion tracking status of the
// customer.
message ConversionTrackingStatusEnum {
  // Conversion Tracking status of the customer.
  enum ConversionTrackingStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Customer does not use any conversion tracking.
    NOT_CONVERSION_TRACKED = 2;

    // The conversion actions are created and managed by this customer.
    CONVERSION_TRACKING_MANAGED_BY_SELF = 3;

    // The conversion actions are created and managed by the manager specified
    // in the request's `login-customer-id`.
    CONVERSION_TRACKING_MANAGED_BY_THIS_MANAGER = 4;

    // The conversion actions are created and managed by a manager different
    // from the customer or manager specified in the request's
    // `login-customer-id`.
    CONVERSION_TRACKING_MANAGED_BY_ANOTHER_MANAGER = 5;
  }
}
