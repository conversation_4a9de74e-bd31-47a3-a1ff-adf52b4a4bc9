// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdPrimaryStatusProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing ad group ad primary status.

// Ad Group Ad Primary Status. Provides insight into why an ad group ad is not
// serving or not serving optimally.
message AdGroupAdPrimaryStatusEnum {
  // The possible statuses of an AdGroupAd.
  enum AdGroupAdPrimaryStatus {
    // No value has been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The ad group ad is eligible to serve.
    ELIGIBLE = 2;

    // The ad group ad is paused.
    PAUSED = 3;

    // The ad group ad is removed.
    REMOVED = 4;

    // The ad cannot serve now, but may serve later without advertiser action.
    PENDING = 5;

    // The ad group ad is serving in a limited capacity.
    LIMITED = 6;

    // The ad group ad is not eligible to serve.
    NOT_ELIGIBLE = 7;
  }
}
