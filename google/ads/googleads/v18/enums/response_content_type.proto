// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ResponseContentTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing the response content types used in mutate operations.

// Container for possible response content types.
message ResponseContentTypeEnum {
  // Possible response content types.
  enum ResponseContentType {
    // Not specified. Will return the resource name only in the response.
    UNSPECIFIED = 0;

    // The mutate response will be the resource name.
    RESOURCE_NAME_ONLY = 1;

    // The mutate response will contain the resource name and the resource with
    // mutable fields if possible. Otherwise, only the resource name will be
    // returned.
    MUTABLE_RESOURCE = 2;
  }
}
