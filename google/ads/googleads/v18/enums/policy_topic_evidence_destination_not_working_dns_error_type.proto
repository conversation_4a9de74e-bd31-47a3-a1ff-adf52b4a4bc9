// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing DNS error types of destination not working policy topic
// evidence.

// Container for enum describing possible policy topic evidence destination not
// working DNS error types.
message PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum {
  // The possible policy topic evidence destination not working DNS error types.
  enum PolicyTopicEvidenceDestinationNotWorkingDnsErrorType {
    // No value has been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // Host name not found in DNS when fetching landing page.
    HOSTNAME_NOT_FOUND = 2;

    // Google internal crawler issue when communicating with DNS. This error
    // doesn't mean the landing page doesn't work. Google will recrawl the
    // landing page.
    GOOGLE_CRAWLER_DNS_ISSUE = 3;
  }
}
