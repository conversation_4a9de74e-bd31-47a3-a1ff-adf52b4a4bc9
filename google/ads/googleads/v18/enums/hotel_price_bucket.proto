// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "HotelPriceBucketProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing hotel price buckets.

// Container for enum describing hotel price bucket for a hotel itinerary.
message HotelPriceBucketEnum {
  // Enum describing possible hotel price buckets.
  enum HotelPriceBucket {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // Uniquely lowest price. Partner has the lowest price, and no other
    // partners are within a small variance of that price.
    LOWEST_UNIQUE = 2;

    // Tied for lowest price. Partner is within a small variance of the lowest
    // price.
    LOWEST_TIED = 3;

    // Not lowest price. Partner is not within a small variance of the lowest
    // price.
    NOT_LOWEST = 4;

    // Partner was the only one shown.
    ONLY_PARTNER_SHOWN = 5;
  }
}
