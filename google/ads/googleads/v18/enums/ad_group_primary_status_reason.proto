// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdGroupPrimaryStatusReasonProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing ad group source.

// Ad Group Primary Status Reason. Provides insight into why an ad group is not
// serving or not serving optimally. These reasons are aggregated to determine
// an overall AdGroupPrimaryStatus.
message AdGroupPrimaryStatusReasonEnum {
  // Possible ad group status reasons.
  enum AdGroupPrimaryStatusReason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The user-specified campaign status is removed. Contributes to
    // AdGroupPrimaryStatus.NOT_ELIGIBLE.
    CAMPAIGN_REMOVED = 2;

    // The user-specified campaign status is paused. Contributes to
    // AdGroupPrimaryStatus.NOT_ELIGIBLE.
    CAMPAIGN_PAUSED = 3;

    // The user-specified time for this campaign to start is in the future.
    // Contributes to AdGroupPrimaryStatus.NOT_ELIGIBLE
    CAMPAIGN_PENDING = 4;

    // The user-specified time for this campaign to end has passed. Contributes
    // to AdGroupPrimaryStatus.NOT_ELIGIBLE.
    CAMPAIGN_ENDED = 5;

    // The user-specified ad group status is paused. Contributes to
    // AdGroupPrimaryStatus.PAUSED.
    AD_GROUP_PAUSED = 6;

    // The user-specified ad group status is removed. Contributes to
    // AdGroupPrimaryStatus.REMOVED.
    AD_GROUP_REMOVED = 7;

    // The construction of this ad group is not yet complete. Contributes to
    // AdGroupPrimaryStatus.NOT_ELIGIBLE.
    AD_GROUP_INCOMPLETE = 8;

    // The user-specified keyword statuses in this ad group are all paused.
    // Contributes to AdGroupPrimaryStatus.NOT_ELIGIBLE.
    KEYWORDS_PAUSED = 9;

    // No eligible keywords exist in this ad group. Contributes to
    // AdGroupPrimaryStatus.NOT_ELIGIBLE.
    NO_KEYWORDS = 10;

    // The user-specified ad group ads statuses in this ad group are all paused.
    // Contributes to AdGroupPrimaryStatus.NOT_ELIGIBLE.
    AD_GROUP_ADS_PAUSED = 11;

    // No eligible ad group ads exist in this ad group. Contributes to
    // AdGroupPrimaryStatus.NOT_ELIGIBLE.
    NO_AD_GROUP_ADS = 12;

    // Policy status reason when at least one ad is disapproved. Contributes to
    // multiple AdGroupPrimaryStatus.
    HAS_ADS_DISAPPROVED = 13;

    // Policy status reason when at least one ad is limited by policy.
    // Contributes to multiple AdGroupPrimaryStatus.
    HAS_ADS_LIMITED_BY_POLICY = 14;

    // Policy status reason when most ads are pending review. Contributes to
    // AdGroupPrimaryStatus.PENDING.
    MOST_ADS_UNDER_REVIEW = 15;

    // The AdGroup belongs to a Draft campaign. Contributes to
    // AdGroupPrimaryStatus.NOT_ELIGIBLE.
    CAMPAIGN_DRAFT = 16;

    // Ad group has been paused due to prolonged low activity in serving.
    // Contributes to AdGroupPrimaryStatus.PAUSED.
    AD_GROUP_PAUSED_DUE_TO_LOW_ACTIVITY = 19;
  }
}
