// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "MessagePlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing Message placeholder fields.

// Values for Message placeholder fields.
message MessagePlaceholderFieldEnum {
  // Possible values for Message placeholder fields.
  enum MessagePlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: STRING. The name of your business.
    BUSINESS_NAME = 2;

    // Data Type: STRING. Country code of phone number.
    COUNTRY_CODE = 3;

    // Data Type: STRING. A phone number that's capable of sending and receiving
    // text messages.
    PHONE_NUMBER = 4;

    // Data Type: STRING. The text that will go in your click-to-message ad.
    MESSAGE_EXTENSION_TEXT = 5;

    // Data Type: STRING. The message text automatically shows in people's
    // messaging apps when they tap to send you a message.
    MESSAGE_TEXT = 6;
  }
}
