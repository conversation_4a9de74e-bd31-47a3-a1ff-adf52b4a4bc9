// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AssetAutomationTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing asset automation type

// Container for enum describing the type of asset automation.
message AssetAutomationTypeEnum {
  // The type of asset automation.
  enum AssetAutomationType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used as a return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Text asset automation includes headlines and descriptions.
    // By default, advertisers are opted-in for Performance Max and
    // opted-out for Search.
    TEXT_ASSET_AUTOMATION = 2;

    // Converts horizontal video assets to vertical orientation using
    // content-aware technology. By default, advertisers are opted in for
    // DemandGenVideoResponsiveAd.
    GENERATE_VERTICAL_YOUTUBE_VIDEOS = 3;

    // Shortens video assets to better capture user attention using
    // content-aware technology. By default, advertisers are opted out.
    GENERATE_SHORTER_YOUTUBE_VIDEOS = 4;
  }
}
