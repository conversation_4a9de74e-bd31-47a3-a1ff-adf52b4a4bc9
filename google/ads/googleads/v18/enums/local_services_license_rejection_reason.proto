// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesLicenseRejectionReasonProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Container for enum describing the rejection reason of a local services
// license verification artifact.
message LocalServicesLicenseRejectionReasonEnum {
  // Enums describing possible rejection reasons of a local services license
  // verification artifact.
  enum LocalServicesLicenseRejectionReason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Business name doesn't match business name for the Local Services Ad.
    BUSINESS_NAME_MISMATCH = 2;

    // License is unauthorized or been revoked.
    UNAUTHORIZED = 3;

    // License is expired.
    EXPIRED = 4;

    // License is poor quality - blurry images, illegible, etc...
    POOR_QUALITY = 5;

    // License cannot be verified due to a not legitimate image.
    UNVERIFIABLE = 6;

    // License is not the requested document type or contains an invalid ID.
    WRONG_DOCUMENT_OR_ID = 7;

    // License has another flaw not listed explicitly.
    OTHER = 8;
  }
}
