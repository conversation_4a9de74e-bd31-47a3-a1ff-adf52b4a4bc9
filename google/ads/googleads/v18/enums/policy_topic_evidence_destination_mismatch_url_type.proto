// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "PolicyTopicEvidenceDestinationMismatchUrlTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing policy topic evidence destination mismatch url types.

// Container for enum describing possible policy topic evidence destination
// mismatch url types.
message PolicyTopicEvidenceDestinationMismatchUrlTypeEnum {
  // The possible policy topic evidence destination mismatch url types.
  enum PolicyTopicEvidenceDestinationMismatchUrlType {
    // No value has been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // The display url.
    DISPLAY_URL = 2;

    // The final url.
    FINAL_URL = 3;

    // The final mobile url.
    FINAL_MOBILE_URL = 4;

    // The tracking url template, with substituted desktop url.
    TRACKING_URL = 5;

    // The tracking url template, with substituted mobile url.
    MOBILE_TRACKING_URL = 6;
  }
}
