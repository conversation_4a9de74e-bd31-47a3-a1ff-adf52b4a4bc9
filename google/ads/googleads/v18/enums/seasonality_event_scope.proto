// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SeasonalityEventScopeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing seasonality event status.

// Message describing seasonality event scopes. The two types of seasonality
// events are BiddingSeasonalityAdjustments and BiddingDataExclusions.
message SeasonalityEventScopeEnum {
  // The possible scopes of a Seasonality Event.
  enum SeasonalityEventScope {
    // No value has been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // The seasonality event is applied to all the customer's traffic for
    // supported advertising channel types and device types. The CUSTOMER scope
    // cannot be used in mutates.
    CUSTOMER = 2;

    // The seasonality event is applied to all specified campaigns.
    CAMPAIGN = 4;

    // The seasonality event is applied to all campaigns that belong to
    // specified channel types.
    CHANNEL = 5;
  }
}
