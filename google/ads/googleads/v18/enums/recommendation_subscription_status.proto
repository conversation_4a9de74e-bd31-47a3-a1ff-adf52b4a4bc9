// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "RecommendationSubscriptionStatusProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing Recommendation Subscription status.

// Container for enum describing recommendation subscription statuses.
message RecommendationSubscriptionStatusEnum {
  // Enum describing recommendation subscription statuses.
  enum RecommendationSubscriptionStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Output-only. Represents a format not yet defined in this enum.
    UNKNOWN = 1;

    // A subscription in the enabled state will automatically
    // apply any recommendations of that type.
    ENABLED = 2;

    // Recommendations of the relevant type will not be
    // automatically applied. Subscriptions cannot be deleted. Once
    // created, they can only move between enabled and paused states.
    PAUSED = 3;
  }
}
