// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ExperimentTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing experiment type.

// Container for enum describing the type of experiment.
message ExperimentTypeEnum {
  // The type of the experiment.
  enum ExperimentType {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // This is a DISPLAY_AND_VIDEO_360 experiment.
    DISPLAY_AND_VIDEO_360 = 2;

    // This is an ad variation experiment.
    AD_VARIATION = 3;

    // A custom experiment consisting of Video campaigns.
    YOUTUBE_CUSTOM = 5;

    // A custom experiment consisting of display campaigns.
    DISPLAY_CUSTOM = 6;

    // A custom experiment consisting of search campaigns.
    SEARCH_CUSTOM = 7;

    // An experiment that compares bidding strategies for display campaigns.
    DISPLAY_AUTOMATED_BIDDING_STRATEGY = 8;

    // An experiment that compares bidding strategies for search campaigns."
    SEARCH_AUTOMATED_BIDDING_STRATEGY = 9;

    // An experiment that compares bidding strategies for shopping campaigns.
    SHOPPING_AUTOMATED_BIDDING_STRATEGY = 10;

    // DEPRECATED. A smart matching experiment with search campaigns.
    SMART_MATCHING = 11;

    // A custom experiment consisting of hotel campaigns.
    HOTEL_CUSTOM = 12;
  }
}
