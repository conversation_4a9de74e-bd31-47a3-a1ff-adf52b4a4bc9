// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "OfflineConversionDiagnosticStatusEnumProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// All possible statuses for oci diagnostics.
message OfflineConversionDiagnosticStatusEnum {
  // Possible statuses of the offline ingestion setup.
  enum OfflineConversionDiagnosticStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Your offline data ingestion setup is active and optimal for downstream
    // processing.
    EXCELLENT = 2;

    // Your offline ingestion setup is active, but there are further
    // improvements you could make. See alerts.
    GOOD = 3;

    // Your offline ingestion setup is active, but there are errors that require
    // your attention. See alerts.
    NEEDS_ATTENTION = 4;

    // Your offline ingestion setup has not received data in the last 28 days,
    // there may be something wrong.
    NO_RECENT_UPLOAD = 6;
  }
}
