// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LeadFormCallToActionTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Describes the type of call-to-action phrases in a lead form.
message LeadFormCallToActionTypeEnum {
  // Enum describing the type of call-to-action phrases in a lead form.
  enum LeadFormCallToActionType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Learn more.
    LEARN_MORE = 2;

    // Get quote.
    GET_QUOTE = 3;

    // Apply now.
    APPLY_NOW = 4;

    // Sign Up.
    SIGN_UP = 5;

    // Contact us.
    CONTACT_US = 6;

    // Subscribe.
    SUBSCRIBE = 7;

    // Download.
    DOWNLOAD = 8;

    // Book now.
    BOOK_NOW = 9;

    // Get offer.
    GET_OFFER = 10;

    // Register.
    REGISTER = 11;

    // Get info.
    GET_INFO = 12;

    // Request a demo.
    REQUEST_DEMO = 13;

    // Join now.
    JOIN_NOW = 14;

    // Get started.
    GET_STARTED = 15;
  }
}
