// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesLeadStatusProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing the local services lead resource.

// Container for enum describing possible statuses of lead.
message LocalServicesLeadStatusEnum {
  // Possible statuses of lead.
  enum LeadStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // New lead which hasn't yet been seen by advertiser.
    NEW = 2;

    // Lead that thas been interacted by advertiser.
    ACTIVE = 3;

    // Lead has been booked.
    BOOKED = 4;

    // Lead was declined by advertiser.
    DECLINED = 5;

    // Lead has expired due to inactivity.
    EXPIRED = 6;

    // Disabled due to spam or dispute.
    DISABLED = 7;

    // Consumer declined the lead.
    CONSUMER_DECLINED = 8;

    // Personally Identifiable Information of the lead is wiped out.
    WIPED_OUT = 9;
  }
}
