// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "CustomerPayPerConversionEligibilityFailureReasonProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing pay per conversion eligibility failure reasons.

// Container for enum describing reasons why a customer is not eligible to use
// PaymentMode.CONVERSIONS.
message CustomerPayPerConversionEligibilityFailureReasonEnum {
  // Enum describing possible reasons a customer is not eligible to use
  // PaymentMode.CONVERSIONS.
  enum CustomerPayPerConversionEligibilityFailureReason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Customer does not have enough conversions.
    NOT_ENOUGH_CONVERSIONS = 2;

    // Customer's conversion lag is too high.
    CONVERSION_LAG_TOO_HIGH = 3;

    // Customer uses shared budgets.
    HAS_CAMPAIGN_WITH_SHARED_BUDGET = 4;

    // Customer has conversions with ConversionActionType.UPLOAD_CLICKS.
    HAS_UPLOAD_CLICKS_CONVERSION = 5;

    // Customer's average daily spend is too high.
    AVERAGE_DAILY_SPEND_TOO_HIGH = 6;

    // Customer's eligibility has not yet been calculated by the Google Ads
    // backend. Check back soon.
    ANALYSIS_NOT_COMPLETE = 7;

    // Customer is not eligible due to other reasons.
    OTHER = 8;
  }
}
