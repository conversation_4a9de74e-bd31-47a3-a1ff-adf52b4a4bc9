// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "BiddingStrategyTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing bidding schemes.

// Container for enum describing possible bidding strategy types.
message BiddingStrategyTypeEnum {
  // Enum describing possible bidding strategy types.
  enum BiddingStrategyType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Commission is an automatic bidding strategy in which the advertiser pays
    // a certain portion of the conversion value.
    COMMISSION = 16;

    // Enhanced CPC is a bidding strategy that raises bids for clicks
    // that seem more likely to lead to a conversion and lowers
    // them for clicks where they seem less likely.
    ENHANCED_CPC = 2;

    // Fixed CPM is a manual bidding strategy with a fixed CPM.
    FIXED_CPM = 19;

    // Used for return value only. Indicates that a campaign does not have a
    // bidding strategy. This prevents the campaign from serving. For example,
    // a campaign may be attached to a manager bidding strategy and the serving
    // account is subsequently unlinked from the manager account. In this case
    // the campaign will automatically be detached from the now inaccessible
    // manager bidding strategy and transition to the INVALID bidding strategy
    // type.
    INVALID = 17;

    // Manual bidding strategy that allows advertiser to set the bid per
    // advertiser-specified action.
    MANUAL_CPA = 18;

    // Manual click based bidding where user pays per click.
    MANUAL_CPC = 3;

    // Manual impression based bidding
    // where user pays per thousand impressions.
    MANUAL_CPM = 4;

    // A bidding strategy that pays a configurable amount per video view.
    MANUAL_CPV = 13;

    // A bidding strategy that automatically maximizes number of conversions
    // given a daily budget.
    MAXIMIZE_CONVERSIONS = 10;

    // An automated bidding strategy that automatically sets bids to maximize
    // revenue while spending your budget.
    MAXIMIZE_CONVERSION_VALUE = 11;

    // Page-One Promoted bidding scheme, which sets max cpc bids to
    // target impressions on page one or page one promoted slots on google.com.
    // This enum value is deprecated.
    PAGE_ONE_PROMOTED = 5;

    // Percent Cpc is bidding strategy where bids are a fraction of the
    // advertised price for some good or service.
    PERCENT_CPC = 12;

    // Target CPA is an automated bid strategy that sets bids
    // to help get as many conversions as possible
    // at the target cost-per-acquisition (CPA) you set.
    TARGET_CPA = 6;

    // Target CPM is an automated bid strategy that sets bids to help get
    // as many impressions as possible at the target cost per one thousand
    // impressions (CPM) you set.
    TARGET_CPM = 14;

    // Target CPV is an automated bidding strategy that sets bids to optimize
    // performance given the average target cost per view.
    TARGET_CPV = 20;

    // An automated bidding strategy that sets bids so that a certain percentage
    // of search ads are shown at the top of the first page (or other targeted
    // location).
    TARGET_IMPRESSION_SHARE = 15;

    // Target Outrank Share is an automated bidding strategy that sets bids
    // based on the target fraction of auctions where the advertiser
    // should outrank a specific competitor.
    // This enum value is deprecated.
    TARGET_OUTRANK_SHARE = 7;

    // Target ROAS is an automated bidding strategy
    // that helps you maximize revenue while averaging
    // a specific target Return On Average Spend (ROAS).
    TARGET_ROAS = 8;

    // Target Spend is an automated bid strategy that sets your bids
    // to help get as many clicks as possible within your budget.
    TARGET_SPEND = 9;
  }
}
