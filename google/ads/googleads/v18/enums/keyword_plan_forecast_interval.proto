// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanForecastIntervalProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing keyword plan forecast intervals.

// Container for enumeration of forecast intervals.
message KeywordPlanForecastIntervalEnum {
  // Forecast intervals.
  enum KeywordPlanForecastInterval {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // The next week date range for keyword plan. The next week is based
    // on the default locale of the user's account and is mostly SUN-SAT or
    // MON-SUN.
    // This can be different from next-7 days.
    NEXT_WEEK = 3;

    // The next month date range for keyword plan.
    NEXT_MONTH = 4;

    // The next quarter date range for keyword plan.
    NEXT_QUARTER = 5;
  }
}
