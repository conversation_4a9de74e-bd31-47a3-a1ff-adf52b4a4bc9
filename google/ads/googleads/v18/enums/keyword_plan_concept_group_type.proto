// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanConceptGroupTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing Keyword Planner Concept Group types.

// Container for enumeration of keyword plan concept group types.
message KeywordPlanConceptGroupTypeEnum {
  // Enumerates keyword plan concept group types.
  enum KeywordPlanConceptGroupType {
    // The concept group classification different from brand/non-brand.
    // This is a catch all bucket for all classifications that are none of the
    // below.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // The concept group classification is based on BRAND.
    BRAND = 2;

    // The concept group classification based on BRAND, that didn't fit well
    // with the BRAND classifications. These are generally outliers and can have
    // very few keywords in this type of classification.
    OTHER_BRANDS = 3;

    // These concept group classification is not based on BRAND. This is
    // returned for generic keywords that don't have a brand association.
    NON_BRAND = 4;
  }
}
