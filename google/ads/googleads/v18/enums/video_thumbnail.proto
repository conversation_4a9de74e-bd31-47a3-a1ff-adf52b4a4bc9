// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "VideoThumbnailProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing video thumbnails.

// Defines the thumbnail to use for In-Display video ads. Note that
// DEFAULT_THUMBNAIL may have been uploaded by the user while thumbnails 1-3 are
// auto-generated from the video.
message VideoThumbnailEnum {
  // Enum listing the possible types of a video thumbnail.
  enum VideoThumbnail {
    // The type has not been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    // This is a response-only value.
    UNKNOWN = 1;

    // The default thumbnail. Can be auto-generated or user-uploaded.
    DEFAULT_THUMBNAIL = 2;

    // Thumbnail 1, generated from the video.
    THUMBNAIL_1 = 3;

    // Thumbnail 2, generated from the video.
    THUMBNAIL_2 = 4;

    // Thumbnail 3, generated from the video.
    THUMBNAIL_3 = 5;
  }
}
