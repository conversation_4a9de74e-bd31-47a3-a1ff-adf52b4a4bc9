// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdTypeProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing the ad type.

// Container for enum describing possible types of an ad.
message AdTypeEnum {
  // The possible types of an ad.
  enum AdType {
    // No value has been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // The ad is a text ad.
    TEXT_AD = 2;

    // The ad is an expanded text ad.
    EXPANDED_TEXT_AD = 3;

    // The ad is an expanded dynamic search ad.
    EXPANDED_DYNAMIC_SEARCH_AD = 7;

    // The ad is a hotel ad.
    HOTEL_AD = 8;

    // The ad is a Smart Shopping ad.
    SHOPPING_SMART_AD = 9;

    // The ad is a standard Shopping ad.
    SHOPPING_PRODUCT_AD = 10;

    // The ad is a video ad.
    VIDEO_AD = 12;

    // This ad is an Image ad.
    IMAGE_AD = 14;

    // The ad is a responsive search ad.
    RESPONSIVE_SEARCH_AD = 15;

    // The ad is a legacy responsive display ad.
    LEGACY_RESPONSIVE_DISPLAY_AD = 16;

    // The ad is an app ad.
    APP_AD = 17;

    // The ad is a legacy app install ad.
    LEGACY_APP_INSTALL_AD = 18;

    // The ad is a responsive display ad.
    RESPONSIVE_DISPLAY_AD = 19;

    // The ad is a local ad.
    LOCAL_AD = 20;

    // The ad is a display upload ad with the HTML5_UPLOAD_AD product type.
    HTML5_UPLOAD_AD = 21;

    // The ad is a display upload ad with one of the DYNAMIC_HTML5_* product
    // types.
    DYNAMIC_HTML5_AD = 22;

    // The ad is an app engagement ad.
    APP_ENGAGEMENT_AD = 23;

    // The ad is a Shopping Comparison Listing ad.
    SHOPPING_COMPARISON_LISTING_AD = 24;

    // Video bumper ad.
    VIDEO_BUMPER_AD = 25;

    // Video non-skippable in-stream ad.
    VIDEO_NON_SKIPPABLE_IN_STREAM_AD = 26;

    // Video outstream ad.
    VIDEO_OUTSTREAM_AD = 27;

    // Video TrueView in-stream ad.
    VIDEO_TRUEVIEW_IN_STREAM_AD = 29;

    // Video responsive ad.
    VIDEO_RESPONSIVE_AD = 30;

    // Smart campaign ad.
    SMART_CAMPAIGN_AD = 31;

    // Call ad.
    CALL_AD = 32;

    // Universal app pre-registration ad.
    APP_PRE_REGISTRATION_AD = 33;

    // In-feed video ad.
    IN_FEED_VIDEO_AD = 34;

    // Demand Gen multi asset ad.
    DEMAND_GEN_MULTI_ASSET_AD = 40;

    // Demand Gen carousel ad.
    DEMAND_GEN_CAROUSEL_AD = 41;

    // Travel ad.
    TRAVEL_AD = 37;

    // Demand Gen video responsive ad.
    DEMAND_GEN_VIDEO_RESPONSIVE_AD = 42;

    // Demand Gen product ad.
    DEMAND_GEN_PRODUCT_AD = 39;
  }
}
