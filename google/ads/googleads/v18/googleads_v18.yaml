type: google.api.Service
config_version: 3
name: googleads.googleapis.com
title: Google Ads API

apis:
- name: google.ads.googleads.v18.services.AccountBudgetProposalService
- name: google.ads.googleads.v18.services.AccountLinkService
- name: google.ads.googleads.v18.services.AdGroupAdLabelService
- name: google.ads.googleads.v18.services.AdGroupAdService
- name: google.ads.googleads.v18.services.AdGroupAssetService
- name: google.ads.googleads.v18.services.AdGroupAssetSetService
- name: google.ads.googleads.v18.services.AdGroupBidModifierService
- name: google.ads.googleads.v18.services.AdGroupCriterionCustomizerService
- name: google.ads.googleads.v18.services.AdGroupCriterionLabelService
- name: google.ads.googleads.v18.services.AdGroupCriterionService
- name: google.ads.googleads.v18.services.AdGroupCustomizerService
- name: google.ads.googleads.v18.services.AdGroupExtensionSettingService
- name: google.ads.googleads.v18.services.AdGroupFeedService
- name: google.ads.googleads.v18.services.AdGroupLabelService
- name: google.ads.googleads.v18.services.AdGroupService
- name: google.ads.googleads.v18.services.AdParameterService
- name: google.ads.googleads.v18.services.AdService
- name: google.ads.googleads.v18.services.AssetGroupAssetService
- name: google.ads.googleads.v18.services.AssetGroupListingGroupFilterService
- name: google.ads.googleads.v18.services.AssetGroupService
- name: google.ads.googleads.v18.services.AssetGroupSignalService
- name: google.ads.googleads.v18.services.AssetService
- name: google.ads.googleads.v18.services.AssetSetAssetService
- name: google.ads.googleads.v18.services.AssetSetService
- name: google.ads.googleads.v18.services.AudienceInsightsService
- name: google.ads.googleads.v18.services.AudienceService
- name: google.ads.googleads.v18.services.BatchJobService
- name: google.ads.googleads.v18.services.BiddingDataExclusionService
- name: google.ads.googleads.v18.services.BiddingSeasonalityAdjustmentService
- name: google.ads.googleads.v18.services.BiddingStrategyService
- name: google.ads.googleads.v18.services.BillingSetupService
- name: google.ads.googleads.v18.services.BrandSuggestionService
- name: google.ads.googleads.v18.services.CampaignAssetService
- name: google.ads.googleads.v18.services.CampaignAssetSetService
- name: google.ads.googleads.v18.services.CampaignBidModifierService
- name: google.ads.googleads.v18.services.CampaignBudgetService
- name: google.ads.googleads.v18.services.CampaignConversionGoalService
- name: google.ads.googleads.v18.services.CampaignCriterionService
- name: google.ads.googleads.v18.services.CampaignCustomizerService
- name: google.ads.googleads.v18.services.CampaignDraftService
- name: google.ads.googleads.v18.services.CampaignExtensionSettingService
- name: google.ads.googleads.v18.services.CampaignFeedService
- name: google.ads.googleads.v18.services.CampaignGroupService
- name: google.ads.googleads.v18.services.CampaignLabelService
- name: google.ads.googleads.v18.services.CampaignLifecycleGoalService
- name: google.ads.googleads.v18.services.CampaignService
- name: google.ads.googleads.v18.services.CampaignSharedSetService
- name: google.ads.googleads.v18.services.ContentCreatorInsightsService
- name: google.ads.googleads.v18.services.ConversionActionService
- name: google.ads.googleads.v18.services.ConversionAdjustmentUploadService
- name: google.ads.googleads.v18.services.ConversionCustomVariableService
- name: google.ads.googleads.v18.services.ConversionGoalCampaignConfigService
- name: google.ads.googleads.v18.services.ConversionUploadService
- name: google.ads.googleads.v18.services.ConversionValueRuleService
- name: google.ads.googleads.v18.services.ConversionValueRuleSetService
- name: google.ads.googleads.v18.services.CustomAudienceService
- name: google.ads.googleads.v18.services.CustomConversionGoalService
- name: google.ads.googleads.v18.services.CustomInterestService
- name: google.ads.googleads.v18.services.CustomerAssetService
- name: google.ads.googleads.v18.services.CustomerAssetSetService
- name: google.ads.googleads.v18.services.CustomerClientLinkService
- name: google.ads.googleads.v18.services.CustomerConversionGoalService
- name: google.ads.googleads.v18.services.CustomerCustomizerService
- name: google.ads.googleads.v18.services.CustomerExtensionSettingService
- name: google.ads.googleads.v18.services.CustomerFeedService
- name: google.ads.googleads.v18.services.CustomerLabelService
- name: google.ads.googleads.v18.services.CustomerLifecycleGoalService
- name: google.ads.googleads.v18.services.CustomerManagerLinkService
- name: google.ads.googleads.v18.services.CustomerNegativeCriterionService
- name: google.ads.googleads.v18.services.CustomerService
- name: google.ads.googleads.v18.services.CustomerSkAdNetworkConversionValueSchemaService
- name: google.ads.googleads.v18.services.CustomerUserAccessInvitationService
- name: google.ads.googleads.v18.services.CustomerUserAccessService
- name: google.ads.googleads.v18.services.CustomizerAttributeService
- name: google.ads.googleads.v18.services.DataLinkService
- name: google.ads.googleads.v18.services.ExperimentArmService
- name: google.ads.googleads.v18.services.ExperimentService
- name: google.ads.googleads.v18.services.ExtensionFeedItemService
- name: google.ads.googleads.v18.services.FeedItemService
- name: google.ads.googleads.v18.services.FeedItemSetLinkService
- name: google.ads.googleads.v18.services.FeedItemSetService
- name: google.ads.googleads.v18.services.FeedItemTargetService
- name: google.ads.googleads.v18.services.FeedMappingService
- name: google.ads.googleads.v18.services.FeedService
- name: google.ads.googleads.v18.services.GeoTargetConstantService
- name: google.ads.googleads.v18.services.GoogleAdsFieldService
- name: google.ads.googleads.v18.services.GoogleAdsService
- name: google.ads.googleads.v18.services.IdentityVerificationService
- name: google.ads.googleads.v18.services.InvoiceService
- name: google.ads.googleads.v18.services.KeywordPlanAdGroupKeywordService
- name: google.ads.googleads.v18.services.KeywordPlanAdGroupService
- name: google.ads.googleads.v18.services.KeywordPlanCampaignKeywordService
- name: google.ads.googleads.v18.services.KeywordPlanCampaignService
- name: google.ads.googleads.v18.services.KeywordPlanIdeaService
- name: google.ads.googleads.v18.services.KeywordPlanService
- name: google.ads.googleads.v18.services.KeywordThemeConstantService
- name: google.ads.googleads.v18.services.LabelService
- name: google.ads.googleads.v18.services.LocalServicesLeadService
- name: google.ads.googleads.v18.services.OfflineUserDataJobService
- name: google.ads.googleads.v18.services.PaymentsAccountService
- name: google.ads.googleads.v18.services.ProductLinkInvitationService
- name: google.ads.googleads.v18.services.ProductLinkService
- name: google.ads.googleads.v18.services.ReachPlanService
- name: google.ads.googleads.v18.services.RecommendationService
- name: google.ads.googleads.v18.services.RecommendationSubscriptionService
- name: google.ads.googleads.v18.services.RemarketingActionService
- name: google.ads.googleads.v18.services.ShareablePreviewService
- name: google.ads.googleads.v18.services.SharedCriterionService
- name: google.ads.googleads.v18.services.SharedSetService
- name: google.ads.googleads.v18.services.SmartCampaignSettingService
- name: google.ads.googleads.v18.services.SmartCampaignSuggestService
- name: google.ads.googleads.v18.services.ThirdPartyAppAnalyticsLinkService
- name: google.ads.googleads.v18.services.TravelAssetSuggestionService
- name: google.ads.googleads.v18.services.UserDataService
- name: google.ads.googleads.v18.services.UserListCustomerTypeService
- name: google.ads.googleads.v18.services.UserListService

types:
- name: google.ads.googleads.v18.errors.GoogleAdsFailure
- name: google.ads.googleads.v18.resources.BatchJob.BatchJobMetadata
- name: google.ads.googleads.v18.resources.OfflineUserDataJobMetadata
- name: google.ads.googleads.v18.services.PromoteExperimentMetadata
- name: google.ads.googleads.v18.services.ScheduleExperimentMetadata

documentation:
  summary: 'Manage your Google Ads accounts, campaigns, and reports with this API.'
  overview: |-
    The Google Ads API enables an app to integrate with the Google Ads
    platform. You can efficiently retrieve and change your Google Ads data
    using the API, making it ideal for managing large or complex accounts and
    campaigns.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v18/{name=customers/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v18/{name=customers/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v18/{name=customers/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v18/{name=customers/*/operations}'
  - selector: google.longrunning.Operations.WaitOperation
    post: '/v18/{name=customers/*/operations/*}:wait'
    body: '*'

authentication:
  rules:
  - selector: google.ads.googleads.v18.services.AccountBudgetProposalService.MutateAccountBudgetProposal
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AccountLinkService.CreateAccountLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AccountLinkService.MutateAccountLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupAdLabelService.MutateAdGroupAdLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupAdService.MutateAdGroupAds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupAdService.RemoveAutomaticallyCreatedAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupAssetService.MutateAdGroupAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupAssetSetService.MutateAdGroupAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupBidModifierService.MutateAdGroupBidModifiers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupCriterionCustomizerService.MutateAdGroupCriterionCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupCriterionLabelService.MutateAdGroupCriterionLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupCriterionService.MutateAdGroupCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupCustomizerService.MutateAdGroupCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupExtensionSettingService.MutateAdGroupExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupFeedService.MutateAdGroupFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupLabelService.MutateAdGroupLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdGroupService.MutateAdGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdParameterService.MutateAdParameters
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AdService.MutateAds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AssetGroupAssetService.MutateAssetGroupAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AssetGroupListingGroupFilterService.MutateAssetGroupListingGroupFilters
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AssetGroupService.MutateAssetGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AssetGroupSignalService.MutateAssetGroupSignals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AssetService.MutateAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AssetSetAssetService.MutateAssetSetAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AssetSetService.MutateAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.AudienceInsightsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.AudienceService.MutateAudiences
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.BatchJobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.BiddingDataExclusionService.MutateBiddingDataExclusions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.BiddingSeasonalityAdjustmentService.MutateBiddingSeasonalityAdjustments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.BiddingStrategyService.MutateBiddingStrategies
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.BillingSetupService.MutateBillingSetup
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.BrandSuggestionService.SuggestBrands
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignAssetService.MutateCampaignAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignAssetSetService.MutateCampaignAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignBidModifierService.MutateCampaignBidModifiers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignBudgetService.MutateCampaignBudgets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignConversionGoalService.MutateCampaignConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignCriterionService.MutateCampaignCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignCustomizerService.MutateCampaignCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.CampaignDraftService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignExtensionSettingService.MutateCampaignExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignFeedService.MutateCampaignFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignGroupService.MutateCampaignGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignLabelService.MutateCampaignLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignLifecycleGoalService.ConfigureCampaignLifecycleGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignService.MutateCampaigns
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CampaignSharedSetService.MutateCampaignSharedSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ContentCreatorInsightsService.GenerateCreatorInsights
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ConversionActionService.MutateConversionActions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ConversionAdjustmentUploadService.UploadConversionAdjustments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ConversionCustomVariableService.MutateConversionCustomVariables
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ConversionGoalCampaignConfigService.MutateConversionGoalCampaignConfigs
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ConversionUploadService.UploadCallConversions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ConversionUploadService.UploadClickConversions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ConversionValueRuleService.MutateConversionValueRules
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ConversionValueRuleSetService.MutateConversionValueRuleSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomAudienceService.MutateCustomAudiences
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomConversionGoalService.MutateCustomConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomInterestService.MutateCustomInterests
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerAssetService.MutateCustomerAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerAssetSetService.MutateCustomerAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerClientLinkService.MutateCustomerClientLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerConversionGoalService.MutateCustomerConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerCustomizerService.MutateCustomerCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerExtensionSettingService.MutateCustomerExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerFeedService.MutateCustomerFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerLabelService.MutateCustomerLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerLifecycleGoalService.ConfigureCustomerLifecycleGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerManagerLinkService.MoveManagerLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerManagerLinkService.MutateCustomerManagerLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerNegativeCriterionService.MutateCustomerNegativeCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.CustomerService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerSkAdNetworkConversionValueSchemaService.MutateCustomerSkAdNetworkConversionValueSchema
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerUserAccessInvitationService.MutateCustomerUserAccessInvitation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomerUserAccessService.MutateCustomerUserAccess
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.CustomizerAttributeService.MutateCustomizerAttributes
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.DataLinkService.CreateDataLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ExperimentArmService.MutateExperimentArms
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.ExperimentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ExtensionFeedItemService.MutateExtensionFeedItems
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.FeedItemService.MutateFeedItems
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.FeedItemSetLinkService.MutateFeedItemSetLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.FeedItemSetService.MutateFeedItemSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.FeedItemTargetService.MutateFeedItemTargets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.FeedMappingService.MutateFeedMappings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.FeedService.MutateFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.GeoTargetConstantService.SuggestGeoTargetConstants
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.GoogleAdsFieldService.GetGoogleAdsField
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.GoogleAdsFieldService.SearchGoogleAdsFields
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.GoogleAdsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.IdentityVerificationService.GetIdentityVerification
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.IdentityVerificationService.StartIdentityVerification
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.InvoiceService.ListInvoices
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.KeywordPlanAdGroupKeywordService.MutateKeywordPlanAdGroupKeywords
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.KeywordPlanAdGroupService.MutateKeywordPlanAdGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.KeywordPlanCampaignKeywordService.MutateKeywordPlanCampaignKeywords
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.KeywordPlanCampaignService.MutateKeywordPlanCampaigns
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.KeywordPlanIdeaService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.KeywordPlanService.MutateKeywordPlans
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.KeywordThemeConstantService.SuggestKeywordThemeConstants
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.LabelService.MutateLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.LocalServicesLeadService.AppendLeadConversation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.OfflineUserDataJobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.PaymentsAccountService.ListPaymentsAccounts
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.ProductLinkInvitationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ProductLinkService.CreateProductLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ProductLinkService.RemoveProductLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.ReachPlanService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.RecommendationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.RecommendationSubscriptionService.MutateRecommendationSubscription
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.RemarketingActionService.MutateRemarketingActions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ShareablePreviewService.GenerateShareablePreviews
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.SharedCriterionService.MutateSharedCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.SharedSetService.MutateSharedSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.SmartCampaignSettingService.GetSmartCampaignStatus
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.SmartCampaignSettingService.MutateSmartCampaignSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v18.services.SmartCampaignSuggestService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.ThirdPartyAppAnalyticsLinkService.RegenerateShareableLinkId
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.TravelAssetSuggestionService.SuggestTravelAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.UserDataService.UploadUserData
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.UserListCustomerTypeService.MutateUserListCustomerTypes
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v18.services.UserListService.MutateUserLists
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
