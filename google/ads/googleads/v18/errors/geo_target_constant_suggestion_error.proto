// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "GeoTargetConstantSuggestionErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Container for enum describing possible geo target constant suggestion errors.
message GeoTargetConstantSuggestionErrorEnum {
  // Enum describing possible geo target constant suggestion errors.
  enum GeoTargetConstantSuggestionError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // A location name cannot be greater than 300 characters.
    LOCATION_NAME_SIZE_LIMIT = 2;

    // At most 25 location names can be specified in a SuggestGeoTargetConstants
    // method.
    LOCATION_NAME_LIMIT = 3;

    // The country code is invalid.
    INVALID_COUNTRY_CODE = 4;

    // Geo target constant resource names or location names must be provided in
    // the request.
    REQUEST_PARAMETERS_UNSET = 5;
  }
}
