// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing ad group ad errors.

// Container for enum describing possible ad group ad errors.
message AdGroupAdErrorEnum {
  // Enum describing possible ad group ad errors.
  enum AdGroupAdError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // No link found between the adgroup ad and the label.
    AD_GROUP_AD_LABEL_DOES_NOT_EXIST = 2;

    // The label has already been attached to the adgroup ad.
    AD_GROUP_AD_LABEL_ALREADY_EXISTS = 3;

    // The specified ad was not found in the adgroup
    AD_NOT_UNDER_ADGROUP = 4;

    // Removed ads may not be modified
    CANNOT_OPERATE_ON_REMOVED_ADGROUPAD = 5;

    // An ad of this type is deprecated and cannot be created. Only deletions
    // are permitted.
    CANNOT_CREATE_DEPRECATED_ADS = 6;

    // Text ads are deprecated and cannot be created. Use expanded text ads
    // instead.
    CANNOT_CREATE_TEXT_ADS = 7;

    // A required field was not specified or is an empty string.
    EMPTY_FIELD = 8;

    // An ad may only be modified once per call
    RESOURCE_REFERENCED_IN_MULTIPLE_OPS = 9;

    // AdGroupAds with the given ad type cannot be paused.
    AD_TYPE_CANNOT_BE_PAUSED = 10;

    // AdGroupAds with the given ad type cannot be removed.
    AD_TYPE_CANNOT_BE_REMOVED = 11;

    // An ad of this type is deprecated and cannot be updated. Only removals
    // are permitted.
    CANNOT_UPDATE_DEPRECATED_ADS = 12;
  }
}
