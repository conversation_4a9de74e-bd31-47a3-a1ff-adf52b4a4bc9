// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CustomerSkAdNetworkConversionValueSchemaErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing CustomerSkAdNetworkConversionValueSchema errors.

// Container for enum describing possible
// CustomerSkAdNetworkConversionValueSchema errors.
message CustomerSkAdNetworkConversionValueSchemaErrorEnum {
  // Enum describing possible CustomerSkAdNetworkConversionValueSchema errors.
  enum CustomerSkAdNetworkConversionValueSchemaError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The customer link ID provided is invalid.
    INVALID_LINK_ID = 2;

    // The app ID provided is invalid.
    INVALID_APP_ID = 3;

    // The conversion value schema provided is invalid.
    INVALID_SCHEMA = 4;

    // The customer link id provided could not be found.
    LINK_CODE_NOT_FOUND = 5;

    // The SkAdNetwork event counter provided is invalid.
    INVALID_EVENT_COUNTER = 7;

    // The SkAdNetwork event name provided is invalid.
    INVALID_EVENT_NAME = 8;
  }
}
