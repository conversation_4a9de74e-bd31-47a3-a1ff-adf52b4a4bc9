// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ConversionValueRuleSetErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing conversion value rule set errors.

// Container for enum describing possible conversion value rule set errors.
message ConversionValueRuleSetErrorEnum {
  // Enum describing possible conversion value rule set errors.
  enum ConversionValueRuleSetError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Two value rules in this value rule set contain conflicting conditions.
    CONFLICTING_VALUE_RULE_CONDITIONS = 2;

    // This value rule set includes a value rule that cannot be found, has been
    // permanently removed or belongs to a different customer.
    INVALID_VALUE_RULE = 3;

    // An error that's thrown when a mutate operation is trying to
    // replace/remove some existing elements in the dimensions field. In other
    // words, ADD op is always fine and UPDATE op is fine if it's only appending
    // new elements into dimensions list.
    DIMENSIONS_UPDATE_ONLY_ALLOW_APPEND = 4;

    // An error that's thrown when a mutate is adding new value rule(s) into a
    // value rule set and the added value rule(s) include conditions that are
    // not specified in the dimensions of the value rule set.
    CONDITION_TYPE_NOT_ALLOWED = 5;

    // The dimensions field contains duplicate elements.
    DUPLICATE_DIMENSIONS = 6;

    // This value rule set is attached to an invalid campaign id. Either a
    // campaign with this campaign id doesn't exist or it belongs to a different
    // customer.
    INVALID_CAMPAIGN_ID = 7;

    // When a mutate request tries to pause a value rule set, the enabled
    // value rules in this set must be paused in the same command, or this error
    // will be thrown.
    CANNOT_PAUSE_UNLESS_ALL_VALUE_RULES_ARE_PAUSED = 8;

    // When a mutate request tries to pause all the value rules in a value rule
    // set, the value rule set must be paused, or this error will be thrown.
    SHOULD_PAUSE_WHEN_ALL_VALUE_RULES_ARE_PAUSED = 9;

    // This value rule set is attached to a campaign that does not support value
    // rules. Currently, campaign level value rule sets can only be created on
    // Search, or Display campaigns.
    VALUE_RULES_NOT_SUPPORTED_FOR_CAMPAIGN_TYPE = 10;

    // To add a value rule set that applies on Store Visits/Store Sales
    // conversion action categories, the customer must have valid Store Visits/
    // Store Sales conversion actions.
    INELIGIBLE_CONVERSION_ACTION_CATEGORIES = 11;

    // If NO_CONDITION is used as a dimension of a value rule set, it must be
    // the only dimension.
    DIMENSION_NO_CONDITION_USED_WITH_OTHER_DIMENSIONS = 12;

    // Dimension NO_CONDITION can only be used by Store Visits/Store Sales value
    // rule set.
    DIMENSION_NO_CONDITION_NOT_ALLOWED = 13;

    // Value rule sets defined on the specified conversion action categories are
    // not supported. The list of conversion action categories must be an empty
    // list, only STORE_VISIT, or only STORE_SALE.
    UNSUPPORTED_CONVERSION_ACTION_CATEGORIES = 14;

    // Dimension ITINERARY can only be used on campaigns with an advertising
    // channel type of PERFORMANCE_MAX or HOTEL.
    DIMENSION_NOT_SUPPORTED_FOR_CAMPAIGN_TYPE = 15;
  }
}
