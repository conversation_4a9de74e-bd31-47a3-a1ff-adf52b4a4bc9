// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "FeedItemValidationErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing feed item validation errors.

// Container for enum describing possible validation errors of a feed item.
message FeedItemValidationErrorEnum {
  // The possible validation errors of a feed item.
  enum FeedItemValidationError {
    // No value has been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // String is too short.
    STRING_TOO_SHORT = 2;

    // String is too long.
    STRING_TOO_LONG = 3;

    // Value is not provided.
    VALUE_NOT_SPECIFIED = 4;

    // Phone number format is invalid for region.
    INVALID_DOMESTIC_PHONE_NUMBER_FORMAT = 5;

    // String does not represent a phone number.
    INVALID_PHONE_NUMBER = 6;

    // Phone number format is not compatible with country code.
    PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY = 7;

    // Premium rate number is not allowed.
    PREMIUM_RATE_NUMBER_NOT_ALLOWED = 8;

    // Phone number type is not allowed.
    DISALLOWED_NUMBER_TYPE = 9;

    // Specified value is outside of the valid range.
    VALUE_OUT_OF_RANGE = 10;

    // Call tracking is not supported in the selected country.
    CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY = 11;

    // Customer is not on the allow-list for call tracking.
    CUSTOMER_NOT_IN_ALLOWLIST_FOR_CALLTRACKING = 99;

    // Country code is invalid.
    INVALID_COUNTRY_CODE = 13;

    // The specified mobile app id is invalid.
    INVALID_APP_ID = 14;

    // Some required field attributes are missing.
    MISSING_ATTRIBUTES_FOR_FIELDS = 15;

    // Invalid email button type for email extension.
    INVALID_TYPE_ID = 16;

    // Email address is invalid.
    INVALID_EMAIL_ADDRESS = 17;

    // The HTTPS URL in email extension is invalid.
    INVALID_HTTPS_URL = 18;

    // Delivery address is missing from email extension.
    MISSING_DELIVERY_ADDRESS = 19;

    // FeedItem scheduling start date comes after end date.
    START_DATE_AFTER_END_DATE = 20;

    // FeedItem scheduling start time is missing.
    MISSING_FEED_ITEM_START_TIME = 21;

    // FeedItem scheduling end time is missing.
    MISSING_FEED_ITEM_END_TIME = 22;

    // Cannot compute system attributes on a FeedItem that has no FeedItemId.
    MISSING_FEED_ITEM_ID = 23;

    // Call extension vanity phone numbers are not supported.
    VANITY_PHONE_NUMBER_NOT_ALLOWED = 24;

    // Invalid review text.
    INVALID_REVIEW_EXTENSION_SNIPPET = 25;

    // Invalid format for numeric value in ad parameter.
    INVALID_NUMBER_FORMAT = 26;

    // Invalid format for date value in ad parameter.
    INVALID_DATE_FORMAT = 27;

    // Invalid format for price value in ad parameter.
    INVALID_PRICE_FORMAT = 28;

    // Unrecognized type given for value in ad parameter.
    UNKNOWN_PLACEHOLDER_FIELD = 29;

    // Enhanced sitelinks must have both description lines specified.
    MISSING_ENHANCED_SITELINK_DESCRIPTION_LINE = 30;

    // Review source is ineligible.
    REVIEW_EXTENSION_SOURCE_INELIGIBLE = 31;

    // Review text cannot contain hyphens or dashes.
    HYPHENS_IN_REVIEW_EXTENSION_SNIPPET = 32;

    // Review text cannot contain double quote characters.
    DOUBLE_QUOTES_IN_REVIEW_EXTENSION_SNIPPET = 33;

    // Review text cannot contain quote characters.
    QUOTES_IN_REVIEW_EXTENSION_SNIPPET = 34;

    // Parameters are encoded in the wrong format.
    INVALID_FORM_ENCODED_PARAMS = 35;

    // URL parameter name must contain only letters, numbers, underscores, and
    // dashes.
    INVALID_URL_PARAMETER_NAME = 36;

    // Cannot find address location.
    NO_GEOCODING_RESULT = 37;

    // Review extension text has source name.
    SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT = 38;

    // Some phone numbers can be shorter than usual. Some of these short numbers
    // are carrier-specific, and we disallow those in ad extensions because they
    // will not be available to all users.
    CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED = 39;

    // Triggered when a request references a placeholder field id that does not
    // exist.
    INVALID_PLACEHOLDER_FIELD_ID = 40;

    // URL contains invalid ValueTrack tags or format.
    INVALID_URL_TAG = 41;

    // Provided list exceeds acceptable size.
    LIST_TOO_LONG = 42;

    // Certain combinations of attributes aren't allowed to be specified in the
    // same feed item.
    INVALID_ATTRIBUTES_COMBINATION = 43;

    // An attribute has the same value repeatedly.
    DUPLICATE_VALUES = 44;

    // Advertisers can link a conversion action with a phone number to indicate
    // that sufficiently long calls forwarded to that phone number should be
    // counted as conversions of the specified type.  This is an error message
    // indicating that the conversion action specified is invalid (for example,
    // the conversion action does not exist within the appropriate Google Ads
    // account, or it is a type of conversion not appropriate to phone call
    // conversions).
    INVALID_CALL_CONVERSION_ACTION_ID = 45;

    // Tracking template requires final url to be set.
    CANNOT_SET_WITHOUT_FINAL_URLS = 46;

    // An app id was provided that doesn't exist in the given app store.
    APP_ID_DOESNT_EXIST_IN_APP_STORE = 47;

    // Invalid U2 final url.
    INVALID_FINAL_URL = 48;

    // Invalid U2 tracking url.
    INVALID_TRACKING_URL = 49;

    // Final URL should start from App download URL.
    INVALID_FINAL_URL_FOR_APP_DOWNLOAD_URL = 50;

    // List provided is too short.
    LIST_TOO_SHORT = 51;

    // User Action field has invalid value.
    INVALID_USER_ACTION = 52;

    // Type field has invalid value.
    INVALID_TYPE_NAME = 53;

    // Change status for event is invalid.
    INVALID_EVENT_CHANGE_STATUS = 54;

    // The header of a structured snippets extension is not one of the valid
    // headers.
    INVALID_SNIPPETS_HEADER = 55;

    // Android app link is not formatted correctly
    INVALID_ANDROID_APP_LINK = 56;

    // Phone number incompatible with call tracking for country.
    NUMBER_TYPE_WITH_CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY = 57;

    // The input is identical to a reserved keyword
    RESERVED_KEYWORD_OTHER = 58;

    // Each option label in the message extension must be unique.
    DUPLICATE_OPTION_LABELS = 59;

    // Each option prefill in the message extension must be unique.
    DUPLICATE_OPTION_PREFILLS = 60;

    // In message extensions, the number of optional labels and optional
    // prefills must be the same.
    UNEQUAL_LIST_LENGTHS = 61;

    // All currency codes in an ad extension must be the same.
    INCONSISTENT_CURRENCY_CODES = 62;

    // Headers in price extension are not unique.
    PRICE_EXTENSION_HAS_DUPLICATED_HEADERS = 63;

    // Header and description in an item are the same.
    ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION = 64;

    // Price extension has too few items.
    PRICE_EXTENSION_HAS_TOO_FEW_ITEMS = 65;

    // The given value is not supported.
    UNSUPPORTED_VALUE = 66;

    // Invalid final mobile url.
    INVALID_FINAL_MOBILE_URL = 67;

    // The given string value of Label contains invalid characters
    INVALID_KEYWORDLESS_AD_RULE_LABEL = 68;

    // The given URL contains value track parameters.
    VALUE_TRACK_PARAMETER_NOT_SUPPORTED = 69;

    // The given value is not supported in the selected language of an
    // extension.
    UNSUPPORTED_VALUE_IN_SELECTED_LANGUAGE = 70;

    // The iOS app link is not formatted correctly.
    INVALID_IOS_APP_LINK = 71;

    // iOS app link or iOS app store id is missing.
    MISSING_IOS_APP_LINK_OR_IOS_APP_STORE_ID = 72;

    // Promotion time is invalid.
    PROMOTION_INVALID_TIME = 73;

    // Both the percent off and money amount off fields are set.
    PROMOTION_CANNOT_SET_PERCENT_OFF_AND_MONEY_AMOUNT_OFF = 74;

    // Both the promotion code and orders over amount fields are set.
    PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT = 75;

    // Too many decimal places are specified.
    TOO_MANY_DECIMAL_PLACES_SPECIFIED = 76;

    // Ad Customizers are present and not allowed.
    AD_CUSTOMIZERS_NOT_ALLOWED = 77;

    // Language code is not valid.
    INVALID_LANGUAGE_CODE = 78;

    // Language is not supported.
    UNSUPPORTED_LANGUAGE = 79;

    // IF Function is present and not allowed.
    IF_FUNCTION_NOT_ALLOWED = 80;

    // Final url suffix is not valid.
    INVALID_FINAL_URL_SUFFIX = 81;

    // Final url suffix contains an invalid tag.
    INVALID_TAG_IN_FINAL_URL_SUFFIX = 82;

    // Final url suffix is formatted incorrectly.
    INVALID_FINAL_URL_SUFFIX_FORMAT = 83;

    // Consent for call recording, which is required for the use of call
    // extensions, was not provided by the advertiser. See
    // https://support.google.com/google-ads/answer/7412639.
    CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED = 84;

    // Multiple message delivery options are set.
    ONLY_ONE_DELIVERY_OPTION_IS_ALLOWED = 85;

    // No message delivery option is set.
    NO_DELIVERY_OPTION_IS_SET = 86;

    // String value of conversion reporting state field is not valid.
    INVALID_CONVERSION_REPORTING_STATE = 87;

    // Image size is not right.
    IMAGE_SIZE_WRONG = 88;

    // Email delivery is not supported in the country specified in the country
    // code field.
    EMAIL_DELIVERY_NOT_AVAILABLE_IN_COUNTRY = 89;

    // Auto reply is not supported in the country specified in the country code
    // field.
    AUTO_REPLY_NOT_AVAILABLE_IN_COUNTRY = 90;

    // Invalid value specified for latitude.
    INVALID_LATITUDE_VALUE = 91;

    // Invalid value specified for longitude.
    INVALID_LONGITUDE_VALUE = 92;

    // Too many label fields provided.
    TOO_MANY_LABELS = 93;

    // Invalid image url.
    INVALID_IMAGE_URL = 94;

    // Latitude value is missing.
    MISSING_LATITUDE_VALUE = 95;

    // Longitude value is missing.
    MISSING_LONGITUDE_VALUE = 96;

    // Unable to find address.
    ADDRESS_NOT_FOUND = 97;

    // Cannot target provided address.
    ADDRESS_NOT_TARGETABLE = 98;

    // The specified asset ID does not exist.
    INVALID_ASSET_ID = 100;

    // The asset type cannot be set for the field.
    INCOMPATIBLE_ASSET_TYPE = 101;

    // The image has unexpected size.
    IMAGE_ERROR_UNEXPECTED_SIZE = 102;

    // The image aspect ratio is not allowed.
    IMAGE_ERROR_ASPECT_RATIO_NOT_ALLOWED = 103;

    // The image file is too large.
    IMAGE_ERROR_FILE_TOO_LARGE = 104;

    // The image format is unsupported.
    IMAGE_ERROR_FORMAT_NOT_ALLOWED = 105;

    // Image violates constraints without more details.
    IMAGE_ERROR_CONSTRAINTS_VIOLATED = 106;

    // An error occurred when validating image.
    IMAGE_ERROR_SERVER_ERROR = 107;
  }
}
