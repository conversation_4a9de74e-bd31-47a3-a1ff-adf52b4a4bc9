// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetSetLinkErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing asset set link errors.

// Container for enum describing possible asset set link errors.
message AssetSetLinkErrorEnum {
  // Enum describing possible asset set link errors.
  enum AssetSetLinkError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Advertising channel type cannot be attached to the asset set due to
    // channel-based restrictions.
    INCOMPATIBLE_ADVERTISING_CHANNEL_TYPE = 2;

    // For this asset set type, only one campaign to feed linkage is allowed.
    DUPLICATE_FEED_LINK = 3;

    // The asset set type and campaign type are incompatible.
    INCOMPATIBLE_ASSET_SET_TYPE_WITH_CAMPAIGN_TYPE = 4;

    // Cannot link duplicate asset sets to the same campaign.
    DUPLICATE_ASSET_SET_LINK = 5;

    // Cannot remove the asset set link. If a campaign is linked with only one
    // asset set and you attempt to unlink them, this error will be triggered.
    ASSET_SET_LINK_CANNOT_BE_REMOVED = 6;
  }
}
