// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "LabelErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing label errors.

// Container for enum describing possible label errors.
message LabelErrorEnum {
  // Enum describing possible label errors.
  enum LabelError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // An inactive label cannot be applied.
    CANNOT_APPLY_INACTIVE_LABEL = 2;

    // A label cannot be applied to a disabled ad group criterion.
    CANNOT_APPLY_LABEL_TO_DISABLED_AD_GROUP_CRITERION = 3;

    // A label cannot be applied to a negative ad group criterion.
    CANNOT_APPLY_LABEL_TO_NEGATIVE_AD_GROUP_CRITERION = 4;

    // Cannot apply more than 50 labels per resource.
    EXCEEDED_LABEL_LIMIT_PER_TYPE = 5;

    // Labels from a manager account cannot be applied to campaign, ad group,
    // ad group ad, or ad group criterion resources.
    INVALID_RESOURCE_FOR_MANAGER_LABEL = 6;

    // Label names must be unique.
    DUPLICATE_NAME = 7;

    // Label names cannot be empty.
    INVALID_LABEL_NAME = 8;

    // Labels cannot be applied to a draft.
    CANNOT_ATTACH_LABEL_TO_DRAFT = 9;

    // Labels not from a manager account cannot be applied to the customer
    // resource.
    CANNOT_ATTACH_NON_MANAGER_LABEL_TO_CUSTOMER = 10;
  }
}
