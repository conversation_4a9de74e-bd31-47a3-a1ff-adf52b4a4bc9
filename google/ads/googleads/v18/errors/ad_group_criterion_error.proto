// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AdGroupCriterionErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing ad group criterion errors.

// Container for enum describing possible ad group criterion errors.
message AdGroupCriterionErrorEnum {
  // Enum describing possible ad group criterion errors.
  enum AdGroupCriterionError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // No link found between the AdGroupCriterion and the label.
    AD_GROUP_CRITERION_LABEL_DOES_NOT_EXIST = 2;

    // The label has already been attached to the AdGroupCriterion.
    AD_GROUP_CRITERION_LABEL_ALREADY_EXISTS = 3;

    // Negative AdGroupCriterion cannot have labels.
    CANNOT_ADD_LABEL_TO_NEGATIVE_CRITERION = 4;

    // Too many operations for a single call.
    TOO_MANY_OPERATIONS = 5;

    // Negative ad group criteria are not updateable.
    CANT_UPDATE_NEGATIVE = 6;

    // Concrete type of criterion (keyword v.s. placement) is required for ADD
    // and SET operations.
    CONCRETE_TYPE_REQUIRED = 7;

    // Bid is incompatible with ad group's bidding settings.
    BID_INCOMPATIBLE_WITH_ADGROUP = 8;

    // Cannot target and exclude the same criterion at once.
    CANNOT_TARGET_AND_EXCLUDE = 9;

    // The URL of a placement is invalid.
    ILLEGAL_URL = 10;

    // Keyword text was invalid.
    INVALID_KEYWORD_TEXT = 11;

    // Destination URL was invalid.
    INVALID_DESTINATION_URL = 12;

    // The destination url must contain at least one tag (for example, {lpurl})
    MISSING_DESTINATION_URL_TAG = 13;

    // Keyword-level cpm bid is not supported
    KEYWORD_LEVEL_BID_NOT_SUPPORTED_FOR_MANUALCPM = 14;

    // For example, cannot add a biddable ad group criterion that had been
    // removed.
    INVALID_USER_STATUS = 15;

    // Criteria type cannot be targeted for the ad group. Either the account is
    // restricted to keywords only, the criteria type is incompatible with the
    // campaign's bidding strategy, or the criteria type can only be applied to
    // campaigns.
    CANNOT_ADD_CRITERIA_TYPE = 16;

    // Criteria type cannot be excluded for the ad group. Refer to the
    // documentation for a specific criterion to check if it is excludable.
    CANNOT_EXCLUDE_CRITERIA_TYPE = 17;

    // Partial failure is not supported for shopping campaign mutate operations.
    CAMPAIGN_TYPE_NOT_COMPATIBLE_WITH_PARTIAL_FAILURE = 27;

    // Operations in the mutate request changes too many shopping ad groups.
    // Split requests for multiple shopping ad groups across multiple
    // requests.
    OPERATIONS_FOR_TOO_MANY_SHOPPING_ADGROUPS = 28;

    // Not allowed to modify url fields of an ad group criterion if there are
    // duplicate elements for that ad group criterion in the request.
    CANNOT_MODIFY_URL_FIELDS_WITH_DUPLICATE_ELEMENTS = 29;

    // Cannot set url fields without also setting final urls.
    CANNOT_SET_WITHOUT_FINAL_URLS = 30;

    // Cannot clear final urls if final mobile urls exist.
    CANNOT_CLEAR_FINAL_URLS_IF_FINAL_MOBILE_URLS_EXIST = 31;

    // Cannot clear final urls if final app urls exist.
    CANNOT_CLEAR_FINAL_URLS_IF_FINAL_APP_URLS_EXIST = 32;

    // Cannot clear final urls if tracking url template exists.
    CANNOT_CLEAR_FINAL_URLS_IF_TRACKING_URL_TEMPLATE_EXISTS = 33;

    // Cannot clear final urls if url custom parameters exist.
    CANNOT_CLEAR_FINAL_URLS_IF_URL_CUSTOM_PARAMETERS_EXIST = 34;

    // Cannot set both destination url and final urls.
    CANNOT_SET_BOTH_DESTINATION_URL_AND_FINAL_URLS = 35;

    // Cannot set both destination url and tracking url template.
    CANNOT_SET_BOTH_DESTINATION_URL_AND_TRACKING_URL_TEMPLATE = 36;

    // Final urls are not supported for this criterion type.
    FINAL_URLS_NOT_SUPPORTED_FOR_CRITERION_TYPE = 37;

    // Final mobile urls are not supported for this criterion type.
    FINAL_MOBILE_URLS_NOT_SUPPORTED_FOR_CRITERION_TYPE = 38;
  }
}
