// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "OfflineUserDataJobErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing offline user data job errors.

// Container for enum describing possible offline user data job errors.
message OfflineUserDataJobErrorEnum {
  // Enum describing possible request errors.
  enum OfflineUserDataJobError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The user list ID provided for the job is invalid.
    INVALID_USER_LIST_ID = 3;

    // Type of the user list is not applicable for the job.
    INVALID_USER_LIST_TYPE = 4;

    // Customer is not allowisted for using user ID in upload data.
    NOT_ON_ALLOWLIST_FOR_USER_ID = 33;

    // Upload data is not compatible with the upload key type of the associated
    // user list.
    INCOMPATIBLE_UPLOAD_KEY_TYPE = 6;

    // The user identifier is missing valid data.
    MISSING_USER_IDENTIFIER = 7;

    // The mobile ID is malformed.
    INVALID_MOBILE_ID_FORMAT = 8;

    // Maximum number of user identifiers allowed per request is 100,000 and per
    // operation is 20.
    TOO_MANY_USER_IDENTIFIERS = 9;

    // Customer is not on the allow-list for store sales direct data.
    NOT_ON_ALLOWLIST_FOR_STORE_SALES_DIRECT = 31;

    // Customer is not on the allow-list for unified store sales data.
    NOT_ON_ALLOWLIST_FOR_UNIFIED_STORE_SALES = 32;

    // The partner ID in store sales direct metadata is invalid.
    INVALID_PARTNER_ID = 11;

    // The data in user identifier should not be encoded.
    INVALID_ENCODING = 12;

    // The country code is invalid.
    INVALID_COUNTRY_CODE = 13;

    // Incompatible user identifier when using third_party_user_id for store
    // sales direct first party data or not using third_party_user_id for store
    // sales third party data.
    INCOMPATIBLE_USER_IDENTIFIER = 14;

    // A transaction time in the future is not allowed.
    FUTURE_TRANSACTION_TIME = 15;

    // The conversion_action specified in transaction_attributes is used to
    // report conversions to a conversion action configured in Google Ads. This
    // error indicates there is no such conversion action in the account.
    INVALID_CONVERSION_ACTION = 16;

    // Mobile ID is not supported for store sales direct data.
    MOBILE_ID_NOT_SUPPORTED = 17;

    // When a remove-all operation is provided, it has to be the first operation
    // of the operation list.
    INVALID_OPERATION_ORDER = 18;

    // Mixing creation and removal of offline data in the same job is not
    // allowed.
    CONFLICTING_OPERATION = 19;

    // The external update ID already exists.
    EXTERNAL_UPDATE_ID_ALREADY_EXISTS = 21;

    // Once the upload job is started, new operations cannot be added.
    JOB_ALREADY_STARTED = 22;

    // Remove operation is not allowed for store sales direct updates.
    REMOVE_NOT_SUPPORTED = 23;

    // Remove-all is not supported for certain offline user data job types.
    REMOVE_ALL_NOT_SUPPORTED = 24;

    // The SHA256 encoded value is malformed.
    INVALID_SHA256_FORMAT = 25;

    // The custom key specified is not enabled for the unified store sales
    // upload.
    CUSTOM_KEY_DISABLED = 26;

    // The custom key specified is not predefined through the Google Ads UI.
    CUSTOM_KEY_NOT_PREDEFINED = 27;

    // The custom key specified is not set in the upload.
    CUSTOM_KEY_NOT_SET = 29;

    // The customer has not accepted the customer data terms in the conversion
    // settings page.
    CUSTOMER_NOT_ACCEPTED_CUSTOMER_DATA_TERMS = 30;

    // User attributes cannot be uploaded into a user list.
    ATTRIBUTES_NOT_APPLICABLE_FOR_CUSTOMER_MATCH_USER_LIST = 34;

    // Lifetime bucket value must be a number from 0 to 10; 0 is only accepted
    // for remove operations
    LIFETIME_VALUE_BUCKET_NOT_IN_RANGE = 35;

    // Identifiers not supported for Customer Match attributes. User attributes
    // can only be provided with contact info (email, phone, address) user
    // identifiers.
    INCOMPATIBLE_USER_IDENTIFIER_FOR_ATTRIBUTES = 36;

    // A time in the future is not allowed.
    FUTURE_TIME_NOT_ALLOWED = 37;

    // Last purchase date time cannot be less than acquisition date time.
    LAST_PURCHASE_TIME_LESS_THAN_ACQUISITION_TIME = 38;

    // Only emails are accepted as user identifiers for shopping loyalty match.
    // {-- api.dev/not-precedent: The identifier is not limited to ids, but
    // also include other user info eg. phone numbers.}
    CUSTOMER_IDENTIFIER_NOT_ALLOWED = 39;

    // Provided item ID is invalid.
    INVALID_ITEM_ID = 40;

    // First purchase date time cannot be greater than the last purchase date
    // time.
    FIRST_PURCHASE_TIME_GREATER_THAN_LAST_PURCHASE_TIME = 42;

    // Provided lifecycle stage is invalid.
    INVALID_LIFECYCLE_STAGE = 43;

    // The event value of the Customer Match user attribute is invalid.
    INVALID_EVENT_VALUE = 44;

    // All the fields are not present in the EventAttribute of the Customer
    // Match.
    EVENT_ATTRIBUTE_ALL_FIELDS_ARE_REQUIRED = 45;

    // Consent was provided at the operation level for an OfflineUserDataJobType
    // that expects it at the job level. The provided operation-level consent
    // will be ignored.
    OPERATION_LEVEL_CONSENT_PROVIDED = 48;
  }
}
