// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ExperimentArmErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing experiment arm errors.

// Container for enum describing possible experiment arm error.
message ExperimentArmErrorEnum {
  // Enum describing possible experiment arm errors.
  enum ExperimentArmError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Number of experiment arms is above limit.
    EXPERIMENT_ARM_COUNT_LIMIT_EXCEEDED = 2;

    // Cannot add campaign with invalid status to the experiment arm.
    INVALID_CAMPAIGN_STATUS = 3;

    // Cannot add duplicate experiment arm name in one experiment.
    DUPLICATE_EXPERIMENT_ARM_NAME = 4;

    // Cannot set campaigns of treatment experiment arm.
    CANNOT_SET_TREATMENT_ARM_CAMPAIGN = 5;

    // Cannot edit campaign ids in trial arms in non SETUP experiment.
    CANNOT_MODIFY_CAMPAIGN_IDS = 6;

    // Cannot modify the campaigns in the control arm
    // if there is not a suffix set in the trial.
    CANNOT_MODIFY_CAMPAIGN_WITHOUT_SUFFIX_SET = 7;

    // Traffic split related settings (like traffic share bounds) can't be
    // modified after the trial has started.
    CANNOT_MUTATE_TRAFFIC_SPLIT_AFTER_START = 8;

    // Cannot use shared budget on experiment's control campaign.
    CANNOT_ADD_CAMPAIGN_WITH_SHARED_BUDGET = 9;

    // Cannot use custom budget on experiment's control campaigns.
    CANNOT_ADD_CAMPAIGN_WITH_CUSTOM_BUDGET = 10;

    // Cannot have enable_dynamic_assets turned on in experiment's campaigns.
    CANNOT_ADD_CAMPAIGNS_WITH_DYNAMIC_ASSETS_ENABLED = 11;

    // Cannot use campaign's advertising channel sub type in experiment.
    UNSUPPORTED_CAMPAIGN_ADVERTISING_CHANNEL_SUB_TYPE = 12;

    // Experiment date range must be within base campaign's date range.
    CANNOT_ADD_BASE_CAMPAIGN_WITH_DATE_RANGE = 13;

    // Bidding strategy is not supported in experiments.
    BIDDING_STRATEGY_NOT_SUPPORTED_IN_EXPERIMENTS = 14;

    // Traffic split is not supported for some channel types.
    TRAFFIC_SPLIT_NOT_SUPPORTED_FOR_CHANNEL_TYPE = 15;
  }
}
