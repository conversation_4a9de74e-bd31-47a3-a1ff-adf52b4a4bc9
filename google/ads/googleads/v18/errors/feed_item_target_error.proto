// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "FeedItemTargetErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing feed item target errors.

// Container for enum describing possible feed item target errors.
message FeedItemTargetErrorEnum {
  // Enum describing possible feed item target errors.
  enum FeedItemTargetError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // On CREATE, the FeedItemTarget must have a populated field in the oneof
    // target.
    MUST_SET_TARGET_ONEOF_ON_CREATE = 2;

    // The specified feed item target already exists, so it cannot be added.
    FEED_ITEM_TARGET_ALREADY_EXISTS = 3;

    // The schedules for a given feed item cannot overlap.
    FEED_ITEM_SCHEDULES_CANNOT_OVERLAP = 4;

    // Too many targets of a given type were added for a single feed item.
    TARGET_LIMIT_EXCEEDED_FOR_GIVEN_TYPE = 5;

    // Too many AdSchedules are enabled for the feed item for the given day.
    TOO_MANY_SCHEDULES_PER_DAY = 6;

    // A feed item may either have an enabled campaign target or an enabled ad
    // group target.
    CANNOT_HAVE_ENABLED_CAMPAIGN_AND_ENABLED_AD_GROUP_TARGETS = 7;

    // Duplicate ad schedules aren't allowed.
    DUPLICATE_AD_SCHEDULE = 8;

    // Duplicate keywords aren't allowed.
    DUPLICATE_KEYWORD = 9;
  }
}
