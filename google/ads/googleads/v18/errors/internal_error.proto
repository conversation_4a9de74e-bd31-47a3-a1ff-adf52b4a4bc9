// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "InternalErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing internal errors.

// Container for enum describing possible internal errors.
message InternalErrorEnum {
  // Enum describing possible internal errors.
  enum InternalError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Google Ads API encountered unexpected internal error.
    INTERNAL_ERROR = 2;

    // The intended error code doesn't exist in specified API version. It will
    // be released in a future API version.
    ERROR_CODE_NOT_PUBLISHED = 3;

    // Google Ads API encountered an unexpected transient error. The user
    // should retry their request in these cases.
    TRANSIENT_ERROR = 4;

    // The request took longer than a deadline.
    DEADLINE_EXCEEDED = 5;
  }
}
