// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanCampaignErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing errors from applying a keyword plan campaign.

// Container for enum describing possible errors from applying a keyword plan
// campaign.
message KeywordPlanCampaignErrorEnum {
  // Enum describing possible errors from applying a keyword plan campaign.
  enum KeywordPlanCampaignError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // A keyword plan campaign name is missing, empty, longer than allowed limit
    // or contains invalid chars.
    INVALID_NAME = 2;

    // A keyword plan campaign contains one or more untargetable languages.
    INVALID_LANGUAGES = 3;

    // A keyword plan campaign contains one or more invalid geo targets.
    INVALID_GEOS = 4;

    // The keyword plan campaign name is duplicate to an existing keyword plan
    // campaign name or other keyword plan campaign name in the request.
    DUPLICATE_NAME = 5;

    // The number of geo targets in the keyword plan campaign exceeds limits.
    MAX_GEOS_EXCEEDED = 6;

    // The number of languages in the keyword plan campaign exceeds limits.
    MAX_LANGUAGES_EXCEEDED = 7;
  }
}
