// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AudienceErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing audience errors.

// Container for enum describing possible audience errors.
message AudienceErrorEnum {
  // Enum describing possible audience errors.
  enum AudienceError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // An audience with this name already exists.
    NAME_ALREADY_IN_USE = 2;

    // A dimension within the audience definition is not valid.
    DIMENSION_INVALID = 3;

    // One of the audience segment added is not found.
    AUDIENCE_SEGMENT_NOT_FOUND = 4;

    // One of the audience segment type is not supported.
    AUDIENCE_SEGMENT_TYPE_NOT_SUPPORTED = 5;

    // The same segment already exists in this audience.
    DUPLICATE_AUDIENCE_SEGMENT = 6;

    // Audience can't have more than allowed number segments.
    TOO_MANY_SEGMENTS = 7;

    // Audience can't have multiple dimensions of same type.
    TOO_MANY_DIMENSIONS_OF_SAME_TYPE = 8;

    // The audience cannot be removed, because it is currently used in an
    // ad group criterion or asset group signal in an (enabled or paused)
    // ad group or campaign.
    IN_USE = 9;

    // Asset Group scoped audience requires an asset group ID.
    MISSING_ASSET_GROUP_ID = 10;

    // Audience scope may not be changed from Customer to AssetGroup.
    CANNOT_CHANGE_FROM_CUSTOMER_TO_ASSET_GROUP_SCOPE = 11;
  }
}
