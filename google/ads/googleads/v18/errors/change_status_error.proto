// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ChangeStatusErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing change status errors.

// Container for enum describing possible change status errors.
message ChangeStatusErrorEnum {
  // Enum describing possible change status errors.
  enum ChangeStatusError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The requested start date is too old.
    START_DATE_TOO_OLD = 3;

    // The change_status search request must specify a finite range filter
    // on last_change_date_time.
    CHANGE_DATE_RANGE_INFINITE = 4;

    // The change status search request has specified invalid date time filters
    // that can never logically produce any valid results (for example, start
    // time after end time).
    CHANGE_DATE_RANGE_NEGATIVE = 5;

    // The change_status search request must specify a LIMIT.
    LIMIT_NOT_SPECIFIED = 6;

    // The LIMIT specified by change_status request should be less than or equal
    // to 10K.
    INVALID_LIMIT_CLAUSE = 7;
  }
}
