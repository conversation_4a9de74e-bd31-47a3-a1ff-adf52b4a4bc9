// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "SmartCampaignErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing Smart campaign errors.

// Container for enum describing possible Smart campaign errors.
message SmartCampaignErrorEnum {
  // Enum describing possible Smart campaign errors.
  enum SmartCampaignError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The business location id is invalid.
    INVALID_BUSINESS_LOCATION_ID = 2;

    // The SmartCampaignSetting resource is only applicable for campaigns
    // with advertising channel type SMART.
    INVALID_CAMPAIGN = 3;

    // The business name or business location id is required.
    BUSINESS_NAME_OR_BUSINESS_LOCATION_ID_MISSING = 4;

    // A Smart campaign suggestion request field is required.
    REQUIRED_SUGGESTION_FIELD_MISSING = 5;

    // A location list or proximity is required.
    GEO_TARGETS_REQUIRED = 6;

    // The locale could not be determined.
    CANNOT_DETERMINE_SUGGESTION_LOCALE = 7;

    // The final URL could not be crawled.
    FINAL_URL_NOT_CRAWLABLE = 8;
  }
}
