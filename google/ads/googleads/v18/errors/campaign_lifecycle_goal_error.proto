// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CampaignLifecycleGoalErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing CampaignLifecycleGoal errors.

// Container for enum describing possible campaign lifecycle goal errors.
message CampaignLifecycleGoalErrorEnum {
  // Enum describing possible campaign lifecycle goal errors.
  enum CampaignLifecycleGoalError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Campaign is not specified.
    CAMPAIGN_MISSING = 2;

    // Cannot find the specified campaign.
    INVALID_CAMPAIGN = 3;

    // Optimization mode is unspecified or invalid.
    CUSTOMER_ACQUISITION_INVALID_OPTIMIZATION_MODE = 4;

    // The configured lifecycle goal setting is not compatible with the bidding
    // strategy the campaign is using. Specifically, BID_HIGHER_FOR_NEW_CUSTOMER
    // requires conversion-value based bidding strategy type such as
    // MAXIMIZE_CONVERSION_VALUE.
    INCOMPATIBLE_BIDDING_STRATEGY = 5;

    // Lifecycle goals require the campaign to optimize towards purchase
    // conversion goal.
    MISSING_PURCHASE_GOAL = 6;

    // CampaignLifecycleGoal.customer_acquisition_goal_settings.value_settings.high_lifetime_value
    // is invalid or not allowed, such as when the specified value is smaller
    // than 0.01, when the optimization mode is not BID_HIGHER_FOR_NEW_CUSTOMER,
    // or when
    // CampaignLifecycleGoal.customer_acquisition_goal_settings.value_settings.high_lifetime_value
    // is specified smaller than/without
    // CampaignLifecycleGoal.customer_acquisition_goal_settings.value_settings.value.
    CUSTOMER_ACQUISITION_INVALID_HIGH_LIFETIME_VALUE = 7;

    // Customer acquisition goal is not supported on this campaign type.
    CUSTOMER_ACQUISITION_UNSUPPORTED_CAMPAIGN_TYPE = 8;

    // CampaignLifecycleGoal.customer_acquisition_goal_settings.value_settings.value
    // is invalid or not allowed, such as when the specified value is smaller
    // than 0.01, or when the optimization mode is not
    // BID_HIGHER_FOR_NEW_CUSTOMER.
    CUSTOMER_ACQUISITION_INVALID_VALUE = 9;

    // To use BID_HIGHER_FOR_NEW_CUSTOMER mode, either
    // CampaignLifecycleGoal.customer_acquisition_goal_settings.value_settings.value
    // or CustomerLifecycleGoal.customer_acquisition_goal_value_settings.value
    // must have been specified. If a manager account is managing your account's
    // conversion tracking, then only the CustomerLifecycleGoal of that manager
    // account is used.
    CUSTOMER_ACQUISITION_VALUE_MISSING = 10;

    // In order for a campaign to adopt the customer acquisition goal,
    // CustomerLifecycleGoal.lifecycle_goal_customer_definition_settings.existing_user_lists
    // must include active and accessible userlist with more than 1000 members
    // in the Search/Youtube network. If a manager account is managing your
    // account's conversion tracking, then only the CustomerLifecycleGoal of
    // that manager account is used. Also make sure that the manager account
    // shares audience segments with sub-accounts with continuous audience
    // sharing.
    CUSTOMER_ACQUISITION_MISSING_EXISTING_CUSTOMER_DEFINITION = 11;

    // In order for a campaign to adopt the customer acquisition goal with high
    // lifetime value optimization,
    // CustomerLifecycleGoal.lifecycle_goal_customer_definition_settings.high_lifetime_value_user_lists
    // must include active and accessible userlist with more than 1000 members
    // in the Search/Youtube network. If a manager account is managing your
    // account's conversion tracking, then only the CustomerLifecycleGoal of
    // that manager account is used. Also make sure that the manager account
    // shares audience segments with sub-accounts using continuous audience
    // sharing.
    CUSTOMER_ACQUISITION_MISSING_HIGH_VALUE_CUSTOMER_DEFINITION = 12;
  }
}
