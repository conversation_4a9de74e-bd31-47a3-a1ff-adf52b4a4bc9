// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AuthorizationErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing authorization errors.

// Container for enum describing possible authorization errors.
message AuthorizationErrorEnum {
  // Enum describing possible authorization errors.
  enum AuthorizationError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // User doesn't have permission to access customer. Note: If you're
    // accessing a client customer, the manager's customer ID must be set in the
    // `login-customer-id` header. Learn more at
    // https://developers.google.com/google-ads/api/docs/concepts/call-structure#cid
    USER_PERMISSION_DENIED = 2;

    // The developer token is not on the allow-list.
    DEVELOPER_TOKEN_NOT_ON_ALLOWLIST = 13;

    // The developer token is not allowed with the project sent in the request.
    DEVELOPER_TOKEN_PROHIBITED = 4;

    // The Google Cloud project sent in the request does not have permission to
    // access the api.
    PROJECT_DISABLED = 5;

    // Authorization of the client failed.
    AUTHORIZATION_ERROR = 6;

    // The user does not have permission to perform this action
    // (for example, ADD, UPDATE, REMOVE) on the resource or call a method.
    ACTION_NOT_PERMITTED = 7;

    // Signup not complete.
    INCOMPLETE_SIGNUP = 8;

    // The customer account can't be accessed because it is not yet enabled or
    // has been deactivated.
    CUSTOMER_NOT_ENABLED = 24;

    // The developer must sign the terms of service. They can be found here:
    // ads.google.com/aw/apicenter
    MISSING_TOS = 9;

    // The developer token is only approved for use with test accounts. To
    // access non-test accounts, apply for Basic or Standard access.
    DEVELOPER_TOKEN_NOT_APPROVED = 10;

    // The login customer specified does not have access to the account
    // specified, so the request is invalid.
    INVALID_LOGIN_CUSTOMER_ID_SERVING_CUSTOMER_ID_COMBINATION = 11;

    // The developer specified does not have access to the service.
    SERVICE_ACCESS_DENIED = 12;

    // The customer (or login customer) isn't in Google Ads. It belongs to
    // another ads system.
    ACCESS_DENIED_FOR_ACCOUNT_TYPE = 25;

    // The developer does not have access to the metrics queried.
    METRIC_ACCESS_DENIED = 26;

    // The Google Cloud project is not under the required organization.
    CLOUD_PROJECT_NOT_UNDER_ORGANIZATION = 27;

    // The user does not have permission to perform this action on the resource
    // or method because the Google Ads account is suspended.
    ACTION_NOT_PERMITTED_FOR_SUSPENDED_ACCOUNT = 28;
  }
}
