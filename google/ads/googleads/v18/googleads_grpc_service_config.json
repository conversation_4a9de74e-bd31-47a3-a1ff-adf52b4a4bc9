{"methodConfig": [{"name": [{"service": "google.ads.googleads.v18.services.AccountBudgetProposalService"}, {"service": "google.ads.googleads.v18.services.AccountLinkService"}, {"service": "google.ads.googleads.v18.services.AdGroupAdLabelService"}, {"service": "google.ads.googleads.v18.services.AdGroupAdService"}, {"service": "google.ads.googleads.v18.services.AdGroupAssetService"}, {"service": "google.ads.googleads.v18.services.AdGroupAssetSetService"}, {"service": "google.ads.googleads.v18.services.AdGroupBidModifierService"}, {"service": "google.ads.googleads.v18.services.AdGroupCriterionCustomizerService"}, {"service": "google.ads.googleads.v18.services.AdGroupCriterionLabelService"}, {"service": "google.ads.googleads.v18.services.AdGroupCriterionService"}, {"service": "google.ads.googleads.v18.services.AdGroupCustomizerService"}, {"service": "google.ads.googleads.v18.services.AdGroupExtensionSettingService"}, {"service": "google.ads.googleads.v18.services.AdGroupFeedService"}, {"service": "google.ads.googleads.v18.services.AdGroupLabelService"}, {"service": "google.ads.googleads.v18.services.AdGroupService"}, {"service": "google.ads.googleads.v18.services.AdParameterService"}, {"service": "google.ads.googleads.v18.services.AdService"}, {"service": "google.ads.googleads.v18.services.AssetGroupAssetService"}, {"service": "google.ads.googleads.v18.services.AssetGroupListingGroupFilterService"}, {"service": "google.ads.googleads.v18.services.AssetGroupService"}, {"service": "google.ads.googleads.v18.services.AssetGroupSignalService"}, {"service": "google.ads.googleads.v18.services.AssetService"}, {"service": "google.ads.googleads.v18.services.AssetSetAssetService"}, {"service": "google.ads.googleads.v18.services.AssetSetService"}, {"service": "google.ads.googleads.v18.services.AudienceInsightsService"}, {"service": "google.ads.googleads.v18.services.AudienceService"}, {"service": "google.ads.googleads.v18.services.BatchJobService"}, {"service": "google.ads.googleads.v18.services.BiddingDataExclusionService"}, {"service": "google.ads.googleads.v18.services.BiddingSeasonalityAdjustmentService"}, {"service": "google.ads.googleads.v18.services.BiddingStrategyService"}, {"service": "google.ads.googleads.v18.services.BillingSetupService"}, {"service": "google.ads.googleads.v18.services.BrandSuggestionService"}, {"service": "google.ads.googleads.v18.services.CampaignAssetService"}, {"service": "google.ads.googleads.v18.services.CampaignAssetSetService"}, {"service": "google.ads.googleads.v18.services.CampaignBidModifierService"}, {"service": "google.ads.googleads.v18.services.CampaignBudgetService"}, {"service": "google.ads.googleads.v18.services.CampaignConversionGoalService"}, {"service": "google.ads.googleads.v18.services.CampaignCriterionService"}, {"service": "google.ads.googleads.v18.services.CampaignCustomizerService"}, {"service": "google.ads.googleads.v18.services.CampaignDraftService"}, {"service": "google.ads.googleads.v18.services.CampaignExtensionSettingService"}, {"service": "google.ads.googleads.v18.services.CampaignFeedService"}, {"service": "google.ads.googleads.v18.services.CampaignGroupService"}, {"service": "google.ads.googleads.v18.services.CampaignLabelService"}, {"service": "google.ads.googleads.v18.services.CampaignLifecycleGoalService"}, {"service": "google.ads.googleads.v18.services.CampaignService"}, {"service": "google.ads.googleads.v18.services.CampaignSharedSetService"}, {"service": "google.ads.googleads.v18.services.ContentCreatorInsightsService"}, {"service": "google.ads.googleads.v18.services.ConversionActionService"}, {"service": "google.ads.googleads.v18.services.ConversionAdjustmentUploadService"}, {"service": "google.ads.googleads.v18.services.ConversionCustomVariableService"}, {"service": "google.ads.googleads.v18.services.ConversionGoalCampaignConfigService"}, {"service": "google.ads.googleads.v18.services.ConversionUploadService"}, {"service": "google.ads.googleads.v18.services.ConversionValueRuleService"}, {"service": "google.ads.googleads.v18.services.ConversionValueRuleSetService"}, {"service": "google.ads.googleads.v18.services.CustomAudienceService"}, {"service": "google.ads.googleads.v18.services.CustomConversionGoalService"}, {"service": "google.ads.googleads.v18.services.CustomInterestService"}, {"service": "google.ads.googleads.v18.services.CustomerAssetService"}, {"service": "google.ads.googleads.v18.services.CustomerAssetSetService"}, {"service": "google.ads.googleads.v18.services.CustomerClientLinkService"}, {"service": "google.ads.googleads.v18.services.CustomerConversionGoalService"}, {"service": "google.ads.googleads.v18.services.CustomerCustomizerService"}, {"service": "google.ads.googleads.v18.services.CustomerExtensionSettingService"}, {"service": "google.ads.googleads.v18.services.CustomerFeedService"}, {"service": "google.ads.googleads.v18.services.CustomerLabelService"}, {"service": "google.ads.googleads.v18.services.CustomerLifecycleGoalService"}, {"service": "google.ads.googleads.v18.services.CustomerManagerLinkService"}, {"service": "google.ads.googleads.v18.services.CustomerNegativeCriterionService"}, {"service": "google.ads.googleads.v18.services.CustomerService"}, {"service": "google.ads.googleads.v18.services.CustomerSkAdNetworkConversionValueSchemaService"}, {"service": "google.ads.googleads.v18.services.CustomerUserAccessInvitationService"}, {"service": "google.ads.googleads.v18.services.CustomerUserAccessService"}, {"service": "google.ads.googleads.v18.services.CustomizerAttributeService"}, {"service": "google.ads.googleads.v18.services.DataLinkService"}, {"service": "google.ads.googleads.v18.services.ExperimentArmService"}, {"service": "google.ads.googleads.v18.services.ExperimentService"}, {"service": "google.ads.googleads.v18.services.ExtensionFeedItemService"}, {"service": "google.ads.googleads.v18.services.FeedItemService"}, {"service": "google.ads.googleads.v18.services.FeedItemSetLinkService"}, {"service": "google.ads.googleads.v18.services.FeedItemSetService"}, {"service": "google.ads.googleads.v18.services.FeedItemTargetService"}, {"service": "google.ads.googleads.v18.services.FeedMappingService"}, {"service": "google.ads.googleads.v18.services.FeedService"}, {"service": "google.ads.googleads.v18.services.GeoTargetConstantService"}, {"service": "google.ads.googleads.v18.services.GoogleAdsFieldService"}, {"service": "google.ads.googleads.v18.services.GoogleAdsService"}, {"service": "google.ads.googleads.v18.services.IdentityVerificationService"}, {"service": "google.ads.googleads.v18.services.InvoiceService"}, {"service": "google.ads.googleads.v18.services.KeywordPlanAdGroupKeywordService"}, {"service": "google.ads.googleads.v18.services.KeywordPlanAdGroupService"}, {"service": "google.ads.googleads.v18.services.KeywordPlanCampaignKeywordService"}, {"service": "google.ads.googleads.v18.services.KeywordPlanCampaignService"}, {"service": "google.ads.googleads.v18.services.KeywordPlanIdeaService"}, {"service": "google.ads.googleads.v18.services.KeywordPlanService"}, {"service": "google.ads.googleads.v18.services.KeywordThemeConstantService"}, {"service": "google.ads.googleads.v18.services.LabelService"}, {"service": "google.ads.googleads.v18.services.LocalServicesLeadService"}, {"service": "google.ads.googleads.v18.services.OfflineUserDataJobService"}, {"service": "google.ads.googleads.v18.services.PaymentsAccountService"}, {"service": "google.ads.googleads.v18.services.ProductLinkInvitationService"}, {"service": "google.ads.googleads.v18.services.ProductLinkService"}, {"service": "google.ads.googleads.v18.services.ReachPlanService"}, {"service": "google.ads.googleads.v18.services.RecommendationService"}, {"service": "google.ads.googleads.v18.services.RecommendationSubscriptionService"}, {"service": "google.ads.googleads.v18.services.RemarketingActionService"}, {"service": "google.ads.googleads.v18.services.ShareablePreviewService"}, {"service": "google.ads.googleads.v18.services.SharedCriterionService"}, {"service": "google.ads.googleads.v18.services.SharedSetService"}, {"service": "google.ads.googleads.v18.services.SmartCampaignSettingService"}, {"service": "google.ads.googleads.v18.services.SmartCampaignSuggestService"}, {"service": "google.ads.googleads.v18.services.ThirdPartyAppAnalyticsLinkService"}, {"service": "google.ads.googleads.v18.services.TravelAssetSuggestionService"}, {"service": "google.ads.googleads.v18.services.UserDataService"}, {"service": "google.ads.googleads.v18.services.UserListCustomerTypeService"}, {"service": "google.ads.googleads.v18.services.UserListService"}], "timeout": "14400s", "retryPolicy": {"initialBackoff": "5s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}