// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.common;

import "google/ads/googleads/v17/enums/customer_match_upload_key_type.proto";
import "google/ads/googleads/v17/enums/lookalike_expansion_level.proto";
import "google/ads/googleads/v17/enums/user_list_crm_data_source_type.proto";
import "google/ads/googleads/v17/enums/user_list_date_rule_item_operator.proto";
import "google/ads/googleads/v17/enums/user_list_flexible_rule_operator.proto";
import "google/ads/googleads/v17/enums/user_list_logical_rule_operator.proto";
import "google/ads/googleads/v17/enums/user_list_number_rule_item_operator.proto";
import "google/ads/googleads/v17/enums/user_list_prepopulation_status.proto";
import "google/ads/googleads/v17/enums/user_list_rule_type.proto";
import "google/ads/googleads/v17/enums/user_list_string_rule_item_operator.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/common;common";
option java_multiple_files = true;
option java_outer_classname = "UserListsProto";
option java_package = "com.google.ads.googleads.v17.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Common";
option ruby_package = "Google::Ads::GoogleAds::V17::Common";

// Proto file describing user list types.

// LookalikeUserlist, composed of users similar to those
//   of a configurable seed (set of UserLists)
message LookalikeUserListInfo {
  // Seed UserList ID from which this list is derived, provided by user.
  repeated int64 seed_user_list_ids = 1;

  // Expansion level, reflecting the size of the lookalike audience
  google.ads.googleads.v17.enums.LookalikeExpansionLevelEnum
      .LookalikeExpansionLevel expansion_level = 2;

  // Countries targeted by the Lookalike. Two-letter country code as defined by
  // ISO-3166
  repeated string country_codes = 3;
}

// SimilarUserList is a list of users which are similar to users from another
// UserList. These lists are read-only and automatically created by Google.
message SimilarUserListInfo {
  // Seed UserList from which this list is derived.
  optional string seed_user_list = 2;
}

// UserList of CRM users provided by the advertiser.
message CrmBasedUserListInfo {
  // A string that uniquely identifies a mobile application from which the data
  // was collected.
  // For iOS, the ID string is the 9 digit string that appears at the end of an
  // App Store URL (for example, "476943146" for "Flood-It! 2" whose App Store
  // link is http://itunes.apple.com/us/app/flood-it!-2/id476943146). For
  // Android, the ID string is the application's package name (for example,
  // "com.labpixies.colordrips" for "Color Drips" given Google Play link
  // https://play.google.com/store/apps/details?id=com.labpixies.colordrips).
  // Required when creating CrmBasedUserList for uploading mobile advertising
  // IDs.
  optional string app_id = 4;

  // Matching key type of the list.
  // Mixed data types are not allowed on the same list.
  // This field is required for an ADD operation.
  google.ads.googleads.v17.enums.CustomerMatchUploadKeyTypeEnum
      .CustomerMatchUploadKeyType upload_key_type = 2;

  // Data source of the list. Default value is FIRST_PARTY.
  // Only customers on the allow-list can create third-party sourced CRM lists.
  google.ads.googleads.v17.enums.UserListCrmDataSourceTypeEnum
      .UserListCrmDataSourceType data_source_type = 3;
}

// A client defined rule based on custom parameters sent by web sites or
// uploaded by the advertiser.
message UserListRuleInfo {
  // Rule type is used to determine how to group rule items.
  //
  // The default is OR of ANDs (disjunctive normal form).
  // That is, rule items will be ANDed together within rule item groups and the
  // groups themselves will be ORed together.
  //
  // OR of ANDs is the only supported type for FlexibleRuleUserList.
  google.ads.googleads.v17.enums.UserListRuleTypeEnum.UserListRuleType
      rule_type = 1;

  // List of rule item groups that defines this rule.
  // Rule item groups are grouped together based on rule_type.
  repeated UserListRuleItemGroupInfo rule_item_groups = 2;
}

// A group of rule items.
message UserListRuleItemGroupInfo {
  // Rule items that will be grouped together based on rule_type.
  repeated UserListRuleItemInfo rule_items = 1;
}

// An atomic rule item.
message UserListRuleItemInfo {
  // Rule variable name. It should match the corresponding key name fired
  // by the pixel.
  // A name must begin with US-ascii letters or underscore or UTF8 code that is
  // greater than 127 and consist of US-ascii letters or digits or underscore or
  // UTF8 code that is greater than 127.
  // For websites, there are two built-in variable URL (name = 'url__') and
  // referrer URL (name = 'ref_url__').
  // This field must be populated when creating a new rule item.
  optional string name = 5;

  // An atomic rule item.
  oneof rule_item {
    // An atomic rule item composed of a number operation.
    UserListNumberRuleItemInfo number_rule_item = 2;

    // An atomic rule item composed of a string operation.
    UserListStringRuleItemInfo string_rule_item = 3;

    // An atomic rule item composed of a date operation.
    UserListDateRuleItemInfo date_rule_item = 4;
  }
}

// A rule item composed of a date operation.
message UserListDateRuleItemInfo {
  // Date comparison operator.
  // This field is required and must be populated when creating new date
  // rule item.
  google.ads.googleads.v17.enums.UserListDateRuleItemOperatorEnum
      .UserListDateRuleItemOperator
      operator = 1;

  // String representing date value to be compared with the rule variable.
  // Supported date format is YYYY-MM-DD.
  // Times are reported in the customer's time zone.
  optional string value = 4;

  // The relative date value of the right hand side denoted by number of days
  // offset from now. The value field will override this field when both are
  // present.
  optional int64 offset_in_days = 5;
}

// A rule item composed of a number operation.
message UserListNumberRuleItemInfo {
  // Number comparison operator.
  // This field is required and must be populated when creating a new number
  // rule item.
  google.ads.googleads.v17.enums.UserListNumberRuleItemOperatorEnum
      .UserListNumberRuleItemOperator
      operator = 1;

  // Number value to be compared with the variable.
  // This field is required and must be populated when creating a new number
  // rule item.
  optional double value = 3;
}

// A rule item composed of a string operation.
message UserListStringRuleItemInfo {
  // String comparison operator.
  // This field is required and must be populated when creating a new string
  // rule item.
  google.ads.googleads.v17.enums.UserListStringRuleItemOperatorEnum
      .UserListStringRuleItemOperator
      operator = 1;

  // The right hand side of the string rule item. For URLs or referrer URLs,
  // the value can not contain illegal URL chars such as newlines, quotes,
  // tabs, or parentheses. This field is required and must be populated when
  // creating a new string rule item.
  optional string value = 3;
}

// Flexible rule that wraps the common rule and a lookback window.
message FlexibleRuleOperandInfo {
  // List of rule item groups that defines this rule.
  // Rule item groups are grouped together.
  UserListRuleInfo rule = 1;

  // Lookback window for this rule in days. From now until X days ago.
  optional int64 lookback_window_days = 2;
}

// Flexible rule representation of visitors with one or multiple actions. The
// flexible user list is defined by two lists of operands – inclusive_operands
// and exclusive_operands; each operand represents a set of users based on
// actions they took in a given timeframe. These lists of operands are combined
// with the AND_NOT operator, so that users represented by the inclusive
// operands are included in the user list, minus the users represented by the
// exclusive operands.
message FlexibleRuleUserListInfo {
  // Operator that defines how the inclusive operands are combined.
  google.ads.googleads.v17.enums.UserListFlexibleRuleOperatorEnum
      .UserListFlexibleRuleOperator inclusive_rule_operator = 1;

  // Rules representing users that should be included in the user list. These
  // are located on the left side of the AND_NOT operator, and joined together
  // by either AND/OR as specified by the inclusive_rule_operator.
  repeated FlexibleRuleOperandInfo inclusive_operands = 2;

  // Rules representing users that should be excluded from the user list. These
  // are located on the right side of the AND_NOT operator, and joined together
  // by OR.
  repeated FlexibleRuleOperandInfo exclusive_operands = 3;
}

// Representation of a userlist that is generated by a rule.
message RuleBasedUserListInfo {
  // The status of pre-population. The field is default to NONE if not set which
  // means the previous users will not be considered. If set to REQUESTED, past
  // site visitors or app users who match the list definition will be included
  // in the list (works on the Display Network only). This will only
  // add past users from within the last 30 days, depending on the
  // list's membership duration and the date when the remarketing tag is added.
  // The status will be updated to FINISHED once request is processed, or FAILED
  // if the request fails.
  google.ads.googleads.v17.enums.UserListPrepopulationStatusEnum
      .UserListPrepopulationStatus prepopulation_status = 1;

  // Flexible rule representation of visitors with one or multiple actions. The
  // flexible user list is defined by two lists of operands – inclusive_operands
  // and exclusive_operands; each operand represents a set of users based on
  // actions they took in a given timeframe. These lists of operands are
  // combined with the AND_NOT operator, so that users represented by the
  // inclusive operands are included in the user list, minus the users
  // represented by the exclusive operands.
  FlexibleRuleUserListInfo flexible_rule_user_list = 5;
}

// Represents a user list that is a custom combination of user lists.
message LogicalUserListInfo {
  // Logical list rules that define this user list. The rules are defined as a
  // logical operator (ALL/ANY/NONE) and a list of user lists. All the rules are
  // ANDed when they are evaluated.
  //
  // Required for creating a logical user list.
  repeated UserListLogicalRuleInfo rules = 1;
}

// A user list logical rule. A rule has a logical operator (and/or/not) and a
// list of user lists as operands.
message UserListLogicalRuleInfo {
  // The logical operator of the rule.
  google.ads.googleads.v17.enums.UserListLogicalRuleOperatorEnum
      .UserListLogicalRuleOperator
      operator = 1;

  // The list of operands of the rule.
  repeated LogicalUserListOperandInfo rule_operands = 2;
}

// Operand of logical user list that consists of a user list.
message LogicalUserListOperandInfo {
  // Resource name of a user list as an operand.
  optional string user_list = 2;
}

// User list targeting as a collection of conversions or remarketing actions.
message BasicUserListInfo {
  // Actions associated with this user list.
  repeated UserListActionInfo actions = 1;
}

// Represents an action type used for building remarketing user lists.
message UserListActionInfo {
  // Subtypes of user list action.
  oneof user_list_action {
    // A conversion action that's not generated from remarketing.
    string conversion_action = 3;

    // A remarketing action.
    string remarketing_action = 4;
  }
}
