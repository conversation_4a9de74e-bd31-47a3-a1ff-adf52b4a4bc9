// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.common;

import "google/ads/googleads/v17/common/criteria.proto";
import "google/ads/googleads/v17/enums/ad_destination_type.proto";
import "google/ads/googleads/v17/enums/ad_format_type.proto";
import "google/ads/googleads/v17/enums/ad_network_type.proto";
import "google/ads/googleads/v17/enums/budget_campaign_association_status.proto";
import "google/ads/googleads/v17/enums/click_type.proto";
import "google/ads/googleads/v17/enums/conversion_action_category.proto";
import "google/ads/googleads/v17/enums/conversion_attribution_event_type.proto";
import "google/ads/googleads/v17/enums/conversion_lag_bucket.proto";
import "google/ads/googleads/v17/enums/conversion_or_adjustment_lag_bucket.proto";
import "google/ads/googleads/v17/enums/conversion_value_rule_primary_dimension.proto";
import "google/ads/googleads/v17/enums/converting_user_prior_engagement_type_and_ltv_bucket.proto";
import "google/ads/googleads/v17/enums/day_of_week.proto";
import "google/ads/googleads/v17/enums/device.proto";
import "google/ads/googleads/v17/enums/external_conversion_source.proto";
import "google/ads/googleads/v17/enums/hotel_date_selection_type.proto";
import "google/ads/googleads/v17/enums/hotel_price_bucket.proto";
import "google/ads/googleads/v17/enums/hotel_rate_type.proto";
import "google/ads/googleads/v17/enums/month_of_year.proto";
import "google/ads/googleads/v17/enums/placeholder_type.proto";
import "google/ads/googleads/v17/enums/product_channel.proto";
import "google/ads/googleads/v17/enums/product_channel_exclusivity.proto";
import "google/ads/googleads/v17/enums/product_condition.proto";
import "google/ads/googleads/v17/enums/recommendation_type.proto";
import "google/ads/googleads/v17/enums/search_engine_results_page_type.proto";
import "google/ads/googleads/v17/enums/search_term_match_type.proto";
import "google/ads/googleads/v17/enums/sk_ad_network_ad_event_type.proto";
import "google/ads/googleads/v17/enums/sk_ad_network_attribution_credit.proto";
import "google/ads/googleads/v17/enums/sk_ad_network_coarse_conversion_value.proto";
import "google/ads/googleads/v17/enums/sk_ad_network_source_type.proto";
import "google/ads/googleads/v17/enums/sk_ad_network_user_type.proto";
import "google/ads/googleads/v17/enums/slot.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/common;common";
option java_multiple_files = true;
option java_outer_classname = "SegmentsProto";
option java_package = "com.google.ads.googleads.v17.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Common";
option ruby_package = "Google::Ads::GoogleAds::V17::Common";

// Proto file describing segment only fields.

// Segment only fields.
message Segments {
  // Activity account ID.
  optional int64 activity_account_id = 148;

  // The city where the travel activity is available.
  optional string activity_city = 185;

  // The country where the travel activity is available.
  optional string activity_country = 186;

  // Activity rating.
  optional int64 activity_rating = 149;

  // The state where the travel activity is available.
  optional string activity_state = 187;

  // Advertiser supplied activity ID.
  optional string external_activity_id = 150;

  // Ad Destination type.
  google.ads.googleads.v17.enums.AdDestinationTypeEnum.AdDestinationType
      ad_destination_type = 136;

  // Ad Format type.
  google.ads.googleads.v17.enums.AdFormatTypeEnum.AdFormatType ad_format_type =
      191;

  // Ad network type.
  google.ads.googleads.v17.enums.AdNetworkTypeEnum.AdNetworkType
      ad_network_type = 3;

  // Resource name of the ad group.
  optional string ad_group = 158;

  // Resource name of the asset group.
  optional string asset_group = 159;

  // Domain (visible URL) of a participant in the Auction Insights report.
  optional string auction_insight_domain = 145;

  // Budget campaign association status.
  BudgetCampaignAssociationStatus budget_campaign_association_status = 134;

  // Resource name of the campaign.
  optional string campaign = 157;

  // Click type.
  google.ads.googleads.v17.enums.ClickTypeEnum.ClickType click_type = 26;

  // Resource name of the conversion action.
  optional string conversion_action = 113 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ConversionAction"
  }];

  // Conversion action category.
  google.ads.googleads.v17.enums.ConversionActionCategoryEnum
      .ConversionActionCategory conversion_action_category = 53;

  // Conversion action name.
  optional string conversion_action_name = 114;

  // This segments your conversion columns by the original conversion and
  // conversion value versus the delta if conversions were adjusted. False row
  // has the data as originally stated; While true row has the delta between
  // data now and the data as originally stated. Summing the two together
  // results post-adjustment data.
  optional bool conversion_adjustment = 115;

  // Conversion attribution event type.
  google.ads.googleads.v17.enums.ConversionAttributionEventTypeEnum
      .ConversionAttributionEventType conversion_attribution_event_type = 2;

  // An enum value representing the number of days between the impression and
  // the conversion.
  google.ads.googleads.v17.enums.ConversionLagBucketEnum.ConversionLagBucket
      conversion_lag_bucket = 50;

  // An enum value representing the number of days between the impression and
  // the conversion or between the impression and adjustments to the conversion.
  google.ads.googleads.v17.enums.ConversionOrAdjustmentLagBucketEnum
      .ConversionOrAdjustmentLagBucket conversion_or_adjustment_lag_bucket = 51;

  // Date to which metrics apply.
  // yyyy-MM-dd format, for example, 2018-04-17.
  optional string date = 79;

  // Day of the week, for example, MONDAY.
  google.ads.googleads.v17.enums.DayOfWeekEnum.DayOfWeek day_of_week = 5;

  // Device to which metrics apply.
  google.ads.googleads.v17.enums.DeviceEnum.Device device = 1;

  // External conversion source.
  google.ads.googleads.v17.enums.ExternalConversionSourceEnum
      .ExternalConversionSource external_conversion_source = 55;

  // Resource name of the geo target constant that represents an airport.
  optional string geo_target_airport = 116;

  // Resource name of the geo target constant that represents a canton.
  optional string geo_target_canton = 117;

  // Resource name of the geo target constant that represents a city.
  optional string geo_target_city = 118;

  // Resource name of the geo target constant that represents a country.
  optional string geo_target_country = 119;

  // Resource name of the geo target constant that represents a county.
  optional string geo_target_county = 120;

  // Resource name of the geo target constant that represents a district.
  optional string geo_target_district = 121;

  // Resource name of the geo target constant that represents a metro.
  optional string geo_target_metro = 122;

  // Resource name of the geo target constant that represents the most
  // specific location.
  optional string geo_target_most_specific_location = 123;

  // Resource name of the geo target constant that represents a postal code.
  optional string geo_target_postal_code = 124;

  // Resource name of the geo target constant that represents a province.
  optional string geo_target_province = 125;

  // Resource name of the geo target constant that represents a region.
  optional string geo_target_region = 126;

  // Resource name of the geo target constant that represents a state.
  optional string geo_target_state = 127;

  // Hotel booking window in days.
  optional int64 hotel_booking_window_days = 135;

  // Hotel center ID.
  optional int64 hotel_center_id = 80;

  // Hotel check-in date. Formatted as yyyy-MM-dd.
  optional string hotel_check_in_date = 81;

  // Hotel check-in day of week.
  google.ads.googleads.v17.enums.DayOfWeekEnum.DayOfWeek
      hotel_check_in_day_of_week = 9;

  // Hotel city.
  optional string hotel_city = 82;

  // Hotel class.
  optional int32 hotel_class = 83;

  // Hotel country.
  optional string hotel_country = 84;

  // Hotel date selection type.
  google.ads.googleads.v17.enums.HotelDateSelectionTypeEnum
      .HotelDateSelectionType hotel_date_selection_type = 13;

  // Hotel length of stay.
  optional int32 hotel_length_of_stay = 85;

  // Hotel rate rule ID.
  optional string hotel_rate_rule_id = 86;

  // Hotel rate type.
  google.ads.googleads.v17.enums.HotelRateTypeEnum.HotelRateType
      hotel_rate_type = 74;

  // Hotel price bucket.
  google.ads.googleads.v17.enums.HotelPriceBucketEnum.HotelPriceBucket
      hotel_price_bucket = 78;

  // Hotel state.
  optional string hotel_state = 87;

  // Hour of day as a number between 0 and 23, inclusive.
  optional int32 hour = 88;

  // Only used with feed item metrics.
  // Indicates whether the interaction metrics occurred on the feed item itself
  // or a different extension or ad unit.
  optional bool interaction_on_this_extension = 89;

  // Keyword criterion.
  Keyword keyword = 61;

  // Month as represented by the date of the first day of a month. Formatted as
  // yyyy-MM-dd.
  optional string month = 90;

  // Month of the year, for example, January.
  google.ads.googleads.v17.enums.MonthOfYearEnum.MonthOfYear month_of_year = 18;

  // Partner hotel ID.
  optional string partner_hotel_id = 91;

  // Placeholder type. This is only used with feed item metrics.
  google.ads.googleads.v17.enums.PlaceholderTypeEnum.PlaceholderType
      placeholder_type = 20;

  // Aggregator ID of the product.
  optional int64 product_aggregator_id = 132;

  // Category (level 1) of the product.
  optional string product_category_level1 = 161;

  // Category (level 2) of the product.
  optional string product_category_level2 = 162;

  // Category (level 3) of the product.
  optional string product_category_level3 = 163;

  // Category (level 4) of the product.
  optional string product_category_level4 = 164;

  // Category (level 5) of the product.
  optional string product_category_level5 = 165;

  // Brand of the product.
  optional string product_brand = 97;

  // Channel of the product.
  google.ads.googleads.v17.enums.ProductChannelEnum.ProductChannel
      product_channel = 30;

  // Channel exclusivity of the product.
  google.ads.googleads.v17.enums.ProductChannelExclusivityEnum
      .ProductChannelExclusivity product_channel_exclusivity = 31;

  // Condition of the product.
  google.ads.googleads.v17.enums.ProductConditionEnum.ProductCondition
      product_condition = 32;

  // Resource name of the geo target constant for the country of sale of the
  // product.
  optional string product_country = 98;

  // Custom attribute 0 of the product.
  optional string product_custom_attribute0 = 99;

  // Custom attribute 1 of the product.
  optional string product_custom_attribute1 = 100;

  // Custom attribute 2 of the product.
  optional string product_custom_attribute2 = 101;

  // Custom attribute 3 of the product.
  optional string product_custom_attribute3 = 102;

  // Custom attribute 4 of the product.
  optional string product_custom_attribute4 = 103;

  // Feed label of the product.
  optional string product_feed_label = 147;

  // Item ID of the product.
  optional string product_item_id = 104;

  // Resource name of the language constant for the language of the product.
  optional string product_language = 105;

  // Merchant ID of the product.
  optional int64 product_merchant_id = 133;

  // Store ID of the product.
  optional string product_store_id = 106;

  // Title of the product.
  optional string product_title = 107;

  // Type (level 1) of the product.
  optional string product_type_l1 = 108;

  // Type (level 2) of the product.
  optional string product_type_l2 = 109;

  // Type (level 3) of the product.
  optional string product_type_l3 = 110;

  // Type (level 4) of the product.
  optional string product_type_l4 = 111;

  // Type (level 5) of the product.
  optional string product_type_l5 = 112;

  // Quarter as represented by the date of the first day of a quarter.
  // Uses the calendar year for quarters, for example, the second quarter of
  // 2018 starts on 2018-04-01. Formatted as yyyy-MM-dd.
  optional string quarter = 128;

  // Recommendation type.
  google.ads.googleads.v17.enums.RecommendationTypeEnum.RecommendationType
      recommendation_type = 140;

  // Type of the search engine results page.
  google.ads.googleads.v17.enums.SearchEngineResultsPageTypeEnum
      .SearchEngineResultsPageType search_engine_results_page_type = 70;

  // A search term subcategory. An empty string denotes the catch-all
  // subcategory for search terms that didn't fit into another subcategory.
  optional string search_subcategory = 155;

  // A search term.
  optional string search_term = 156;

  // Match type of the keyword that triggered the ad, including variants.
  google.ads.googleads.v17.enums.SearchTermMatchTypeEnum.SearchTermMatchType
      search_term_match_type = 22;

  // Position of the ad.
  google.ads.googleads.v17.enums.SlotEnum.Slot slot = 23;

  // Primary dimension of applied conversion value rules.
  // NO_RULE_APPLIED shows the total recorded value of conversions that
  // do not have a value rule applied.
  // ORIGINAL shows the original value of conversions to which a value rule
  // has been applied.
  // GEO_LOCATION, DEVICE, AUDIENCE show the net adjustment after value
  // rules were applied.
  google.ads.googleads.v17.enums.ConversionValueRulePrimaryDimensionEnum
      .ConversionValueRulePrimaryDimension
          conversion_value_rule_primary_dimension = 138;

  // Resource name of the ad group criterion that represents webpage criterion.
  optional string webpage = 129;

  // Week as defined as Monday through Sunday, and represented by the date of
  // Monday. Formatted as yyyy-MM-dd.
  optional string week = 130;

  // Year, formatted as yyyy.
  optional int32 year = 131;

  // iOS Store Kit Ad Network conversion value.
  // Null value means this segment is not applicable, for example, non-iOS
  // campaign.
  optional int64 sk_ad_network_fine_conversion_value = 137;

  // iOS Store Kit Ad Network redistributed fine conversion value.
  //
  // Google uses modeling on observed conversion values(obtained
  // from Apple) to calculate conversions from SKAN postbacks where
  // NULLs are returned. This column represents the sum of the modeled
  // conversion values and the observed conversion values. See
  // https://support.google.com/google-ads/answer/14892597
  // to lean more.
  optional int64 sk_ad_network_redistributed_fine_conversion_value = 190;

  // iOS Store Kit Ad Network user type.
  google.ads.googleads.v17.enums.SkAdNetworkUserTypeEnum.SkAdNetworkUserType
      sk_ad_network_user_type = 141;

  // iOS Store Kit Ad Network ad event type.
  google.ads.googleads.v17.enums.SkAdNetworkAdEventTypeEnum
      .SkAdNetworkAdEventType sk_ad_network_ad_event_type = 142;

  // App where the ad that drove the iOS Store Kit Ad Network install was
  // shown. Null value means this segment is not applicable, for example,
  // non-iOS campaign, or was not present in any postbacks sent by Apple.
  optional SkAdNetworkSourceApp sk_ad_network_source_app = 143;

  // iOS Store Kit Ad Network attribution credit
  google.ads.googleads.v17.enums.SkAdNetworkAttributionCreditEnum
      .SkAdNetworkAttributionCredit sk_ad_network_attribution_credit = 144;

  // iOS Store Kit Ad Network coarse conversion value.
  google.ads.googleads.v17.enums.SkAdNetworkCoarseConversionValueEnum
      .SkAdNetworkCoarseConversionValue sk_ad_network_coarse_conversion_value =
      151;

  // Website where the ad that drove the iOS Store Kit Ad Network install was
  // shown. Null value means this segment is not applicable, for example,
  // non-iOS campaign, or was not present in any postbacks sent by Apple.
  optional string sk_ad_network_source_domain = 152;

  // The source type where the ad that drove the iOS Store Kit Ad Network
  // install was shown. Null value means this segment is not applicable, for
  // example, non-iOS campaign, or neither source domain nor source app were
  // present in any postbacks sent by Apple.
  google.ads.googleads.v17.enums.SkAdNetworkSourceTypeEnum.SkAdNetworkSourceType
      sk_ad_network_source_type = 153;

  // iOS Store Kit Ad Network postback sequence index.
  optional int64 sk_ad_network_postback_sequence_index = 154;

  // The version of the SKAdNetwork API used.
  optional string sk_ad_network_version = 192;

  // Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics.
  // Indicates whether the interaction metrics occurred on the asset itself
  // or a different asset or ad unit.
  // Interactions (for example, clicks) are counted across all the parts of the
  // served ad (for example, Ad itself and other components like Sitelinks) when
  // they are served together. When interaction_on_this_asset is true, it means
  // the interactions are on this specific asset and when
  // interaction_on_this_asset is false, it means the interactions is not on
  // this specific asset but on other parts of the served ad this asset is
  // served with.
  optional AssetInteractionTarget asset_interaction_target = 139;

  // This is for segmenting conversions by whether the user is a new customer
  // or a returning customer. This segmentation is typically used to measure
  // the impact of customer acquisition goal.
  google.ads.googleads.v17.enums
      .ConvertingUserPriorEngagementTypeAndLtvBucketEnum
      .ConvertingUserPriorEngagementTypeAndLtvBucket
          new_versus_returning_customers = 160;
}

// A Keyword criterion segment.
message Keyword {
  // The AdGroupCriterion resource name.
  optional string ad_group_criterion = 3;

  // Keyword info.
  KeywordInfo info = 2;
}

// A BudgetCampaignAssociationStatus segment.
message BudgetCampaignAssociationStatus {
  // The campaign resource name.
  optional string campaign = 1;

  // Budget campaign association status.
  google.ads.googleads.v17.enums.BudgetCampaignAssociationStatusEnum
      .BudgetCampaignAssociationStatus status = 2;
}

// An AssetInteractionTarget segment.
message AssetInteractionTarget {
  // The asset resource name.
  string asset = 1;

  // Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics.
  // Indicates whether the interaction metrics occurred on the asset itself or a
  // different asset or ad unit.
  bool interaction_on_this_asset = 2;
}

// A SkAdNetworkSourceApp segment.
message SkAdNetworkSourceApp {
  // App id where the ad that drove the iOS Store Kit Ad Network install was
  // shown.
  optional string sk_ad_network_source_app_id = 1;
}
