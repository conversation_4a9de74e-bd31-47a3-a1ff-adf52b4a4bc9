// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.common;

import "google/ads/googleads/v17/enums/consent_status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/common;common";
option java_multiple_files = true;
option java_outer_classname = "ConsentProto";
option java_package = "com.google.ads.googleads.v17.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Common";
option ruby_package = "Google::Ads::GoogleAds::V17::Common";

// Proto file describing common consent proto messages.

// Consent
message Consent {
  // This represents consent for ad user data.
  google.ads.googleads.v17.enums.ConsentStatusEnum.ConsentStatus ad_user_data =
      1;

  // This represents consent for ad personalization.
  // This can only be set for OfflineUserDataJobService and UserDataService.
  google.ads.googleads.v17.enums.ConsentStatusEnum.ConsentStatus
      ad_personalization = 2;
}
