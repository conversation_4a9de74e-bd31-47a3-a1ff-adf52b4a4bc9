// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.common;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/common;common";
option java_multiple_files = true;
option java_outer_classname = "SimulationProto";
option java_package = "com.google.ads.googleads.v17.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Common";
option ruby_package = "Google::Ads::GoogleAds::V17::Common";

// Proto file describing simulation points.

// A container for simulation points for simulations of type CPC_BID.
message CpcBidSimulationPointList {
  // Projected metrics for a series of CPC bid amounts.
  repeated CpcBidSimulationPoint points = 1;
}

// A container for simulation points for simulations of type CPV_BID.
message CpvBidSimulationPointList {
  // Projected metrics for a series of CPV bid amounts.
  repeated CpvBidSimulationPoint points = 1;
}

// A container for simulation points for simulations of type TARGET_CPA.
message TargetCpaSimulationPointList {
  // Projected metrics for a series of target CPA amounts.
  repeated TargetCpaSimulationPoint points = 1;
}

// A container for simulation points for simulations of type TARGET_ROAS.
message TargetRoasSimulationPointList {
  // Projected metrics for a series of target ROAS amounts.
  repeated TargetRoasSimulationPoint points = 1;
}

// A container for simulation points for simulations of type PERCENT_CPC_BID.
message PercentCpcBidSimulationPointList {
  // Projected metrics for a series of percent CPC bid amounts.
  repeated PercentCpcBidSimulationPoint points = 1;
}

// A container for simulation points for simulations of type BUDGET.
message BudgetSimulationPointList {
  // Projected metrics for a series of budget amounts.
  repeated BudgetSimulationPoint points = 1;
}

// A container for simulation points for simulations of type
// TARGET_IMPRESSION_SHARE.
message TargetImpressionShareSimulationPointList {
  // Projected metrics for a specific target impression share value.
  repeated TargetImpressionShareSimulationPoint points = 1;
}

// Projected metrics for a specific CPC bid amount.
message CpcBidSimulationPoint {
  // Projected required daily budget that the advertiser must set in order to
  // receive the estimated traffic, in micros of advertiser currency.
  int64 required_budget_amount_micros = 17;

  // Projected number of biddable conversions.
  optional double biddable_conversions = 9;

  // Projected total value of biddable conversions.
  optional double biddable_conversions_value = 10;

  // Projected number of clicks.
  optional int64 clicks = 11;

  // Projected cost in micros.
  optional int64 cost_micros = 12;

  // Projected number of impressions.
  optional int64 impressions = 13;

  // Projected number of top slot impressions.
  // Only search advertising channel type supports this field.
  optional int64 top_slot_impressions = 14;

  // When SimulationModificationMethod = UNIFORM or DEFAULT,
  // cpc_bid_micros is set.
  // When SimulationModificationMethod = SCALING,
  // cpc_bid_scaling_modifier is set.
  oneof cpc_simulation_key_value {
    // The simulated CPC bid upon which projected metrics are based.
    int64 cpc_bid_micros = 15;

    // The simulated scaling modifier upon which projected metrics are based.
    // All CPC bids relevant to the simulated entity are scaled by this
    // modifier.
    double cpc_bid_scaling_modifier = 16;
  }
}

// Projected metrics for a specific CPV bid amount.
message CpvBidSimulationPoint {
  // The simulated CPV bid upon which projected metrics are based.
  optional int64 cpv_bid_micros = 5;

  // Projected cost in micros.
  optional int64 cost_micros = 6;

  // Projected number of impressions.
  optional int64 impressions = 7;

  // Projected number of views.
  optional int64 views = 8;
}

// Projected metrics for a specific target CPA amount.
message TargetCpaSimulationPoint {
  // Projected required daily budget that the advertiser must set in order to
  // receive the estimated traffic, in micros of advertiser currency.
  int64 required_budget_amount_micros = 19;

  // Projected number of biddable conversions.
  optional double biddable_conversions = 9;

  // Projected total value of biddable conversions.
  optional double biddable_conversions_value = 10;

  // Projected number of app installs.
  double app_installs = 15;

  // Projected number of in-app actions.
  double in_app_actions = 16;

  // Projected number of clicks.
  optional int64 clicks = 11;

  // Projected cost in micros.
  optional int64 cost_micros = 12;

  // Projected number of impressions.
  optional int64 impressions = 13;

  // Projected number of top slot impressions.
  // Only search advertising channel type supports this field.
  optional int64 top_slot_impressions = 14;

  // Projected number of interactions.
  // Only discovery advertising channel type supports this field.
  optional int64 interactions = 20;

  // When SimulationModificationMethod = UNIFORM or DEFAULT,
  // target_cpa_micros is set.
  // When SimulationModificationMethod = SCALING,
  // target_cpa_scaling_modifier is set.
  oneof target_cpa_simulation_key_value {
    // The simulated target CPA upon which projected metrics are based.
    int64 target_cpa_micros = 17;

    // The simulated scaling modifier upon which projected metrics are based.
    // All CPA targets relevant to the simulated entity are scaled by this
    // modifier.
    double target_cpa_scaling_modifier = 18;
  }
}

// Projected metrics for a specific target ROAS amount.
message TargetRoasSimulationPoint {
  // The simulated target ROAS upon which projected metrics are based.
  optional double target_roas = 8;

  // Projected required daily budget that the advertiser must set in order to
  // receive the estimated traffic, in micros of advertiser currency.
  int64 required_budget_amount_micros = 15;

  // Projected number of biddable conversions.
  optional double biddable_conversions = 9;

  // Projected total value of biddable conversions.
  optional double biddable_conversions_value = 10;

  // Projected number of clicks.
  optional int64 clicks = 11;

  // Projected cost in micros.
  optional int64 cost_micros = 12;

  // Projected number of impressions.
  optional int64 impressions = 13;

  // Projected number of top slot impressions.
  // Only Search advertising channel type supports this field.
  optional int64 top_slot_impressions = 14;
}

// Projected metrics for a specific percent CPC amount. Only Hotel advertising
// channel type supports this field.
message PercentCpcBidSimulationPoint {
  // The simulated percent CPC upon which projected metrics are based. Percent
  // CPC expressed as fraction of the advertised price for some good or service.
  // The value stored here is 1,000,000 * [fraction].
  optional int64 percent_cpc_bid_micros = 1;

  // Projected number of biddable conversions.
  optional double biddable_conversions = 2;

  // Projected total value of biddable conversions in local currency.
  optional double biddable_conversions_value = 3;

  // Projected number of clicks.
  optional int64 clicks = 4;

  // Projected cost in micros.
  optional int64 cost_micros = 5;

  // Projected number of impressions.
  optional int64 impressions = 6;

  // Projected number of top slot impressions.
  optional int64 top_slot_impressions = 7;
}

// Projected metrics for a specific budget amount.
message BudgetSimulationPoint {
  // The simulated budget upon which projected metrics are based.
  int64 budget_amount_micros = 1;

  // Projected required daily cpc bid ceiling that the advertiser must set to
  // realize this simulation, in micros of the advertiser currency.
  // Only campaigns with the Target Spend bidding strategy support this field.
  int64 required_cpc_bid_ceiling_micros = 2;

  // Projected number of biddable conversions.
  double biddable_conversions = 3;

  // Projected total value of biddable conversions.
  double biddable_conversions_value = 4;

  // Projected number of clicks.
  int64 clicks = 5;

  // Projected cost in micros.
  int64 cost_micros = 6;

  // Projected number of impressions.
  int64 impressions = 7;

  // Projected number of top slot impressions.
  // Only search advertising channel type supports this field.
  int64 top_slot_impressions = 8;

  // Projected number of interactions.
  // Only discovery advertising channel type supports this field.
  int64 interactions = 9;
}

// Projected metrics for a specific target impression share value.
message TargetImpressionShareSimulationPoint {
  // The simulated target impression share value (in micros) upon which
  // projected metrics are based.
  // For example, 10% impression share, which is equal to 0.1, is stored as
  // 100_000. This value is validated and will not exceed 1M (100%).
  int64 target_impression_share_micros = 1;

  // Projected required daily cpc bid ceiling that the advertiser must set to
  // realize this simulation, in micros of the advertiser currency.
  int64 required_cpc_bid_ceiling_micros = 2;

  // Projected required daily budget that the advertiser must set in order to
  // receive the estimated traffic, in micros of advertiser currency.
  int64 required_budget_amount_micros = 3;

  // Projected number of biddable conversions.
  double biddable_conversions = 4;

  // Projected total value of biddable conversions.
  double biddable_conversions_value = 5;

  // Projected number of clicks.
  int64 clicks = 6;

  // Projected cost in micros.
  int64 cost_micros = 7;

  // Projected number of impressions.
  int64 impressions = 8;

  // Projected number of top slot impressions.
  // Only search advertising channel type supports this field.
  int64 top_slot_impressions = 9;

  // Projected number of absolute top impressions.
  // Only search advertising channel type supports this field.
  int64 absolute_top_impressions = 10;
}
