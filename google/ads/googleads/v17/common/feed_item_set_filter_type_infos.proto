// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.common;

import "google/ads/googleads/v17/enums/feed_item_set_string_filter_type.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/common;common";
option java_multiple_files = true;
option java_outer_classname = "FeedItemSetFilterTypeInfosProto";
option java_package = "com.google.ads.googleads.v17.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Common";
option ruby_package = "Google::Ads::GoogleAds::V17::Common";

// Represents a filter on locations in a feed item set.
// Only applicable if the parent Feed of the FeedItemSet is a LOCATION feed.
message DynamicLocationSetFilter {
  // If multiple labels are set, then only feeditems marked with all the labels
  // will be added to the FeedItemSet.
  repeated string labels = 1;

  // Business name filter.
  BusinessNameFilter business_name_filter = 2;
}

// Represents a business name filter on locations in a FeedItemSet.
message BusinessNameFilter {
  // Business name string to use for filtering.
  string business_name = 1;

  // The type of string matching to use when filtering with business_name.
  google.ads.googleads.v17.enums.FeedItemSetStringFilterTypeEnum
      .FeedItemSetStringFilterType filter_type = 2;
}

// Represents a filter on affiliate locations in a FeedItemSet.
// Only applicable if the parent Feed of the FeedItemSet is an
// AFFILIATE_LOCATION feed.
message DynamicAffiliateLocationSetFilter {
  // Used to filter affiliate locations by chain ids. Only affiliate locations
  // that belong to the specified chain(s) will be added to the FeedItemSet.
  repeated int64 chain_ids = 1;
}
