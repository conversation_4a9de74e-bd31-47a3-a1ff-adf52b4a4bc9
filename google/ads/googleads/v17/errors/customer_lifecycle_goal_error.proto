// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CustomerLifecycleGoalErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing CustomerLifecycleGoal errors.

// Container for enum describing possible customer lifecycle goal errors.
message CustomerLifecycleGoalErrorEnum {
  // Enum describing possible customer lifecycle goal errors.
  enum CustomerLifecycleGoalError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // CustomerLifecycleGoal.customer_acquisition_goal_value_settings.value must
    // be set.
    CUSTOMER_ACQUISITION_VALUE_MISSING = 2;

    // CustomerLifecycleGoal.customer_acquisition_goal_value_settings.value must
    // be no less than 0.01.
    CUSTOMER_ACQUISITION_INVALID_VALUE = 3;

    // CustomerLifecycleGoal.customer_acquisition_goal_value_settings.high_lifetime_value
    // must be no less than 0.01. Also, to set this field,
    // CustomerLifecycleGoal.customer_acquisition_goal_value_settings.value must
    // also be present, and high_lifetime_value must be greater than value.
    CUSTOMER_ACQUISITION_INVALID_HIGH_LIFETIME_VALUE = 4;

    // CustomerLifecycleGoal.customer_acquisition_goal_value_settings.value
    // cannot be cleared. This value would have no effect as long as none of
    // your campaigns adopt the customer acquisitiong goal.
    CUSTOMER_ACQUISITION_VALUE_CANNOT_BE_CLEARED = 5;

    // CustomerLifecycleGoal.customer_acquisition_goal_value_settings.high_lifetime_value
    // cannot be cleared. This value would have no effect as long as none of
    // your campaigns adopt the high value optimization of customer acquisitiong
    // goal.
    CUSTOMER_ACQUISITION_HIGH_LIFETIME_VALUE_CANNOT_BE_CLEARED = 6;

    // Found invalid value in
    // CustomerLifecycleGoal.lifecycle_goal_customer_definition_settings.existing_user_lists.
    // The userlist must be accessible, active and belong to one of the
    // following types: CRM_BASED, RULE_BASED, REMARKETING.
    INVALID_EXISTING_USER_LIST = 7;

    // Found invalid value in
    // CustomerLifecycleGoal.lifecycle_goal_customer_definition_settings.high_lifetime_value_user_lists.
    // The userlist must be accessible, active and belong to one of the
    // following types: CRM_BASED, RULE_BASED, REMARKETING.
    INVALID_HIGH_LIFETIME_VALUE_USER_LIST = 8;
  }
}
