// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AutomaticallyCreatedAssetRemovalErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing automatically created asset removal errors.

// Container for enum describing possible automatically created asset removal
// errors.
message AutomaticallyCreatedAssetRemovalErrorEnum {
  // Enum describing possible automatically created asset removal errors.
  enum AutomaticallyCreatedAssetRemovalError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The ad does not exist.
    AD_DOES_NOT_EXIST = 2;

    // Ad type is not supported. Only Responsive Search Ad type is supported.
    INVALID_AD_TYPE = 3;

    // The asset does not exist.
    ASSET_DOES_NOT_EXIST = 4;

    // The asset field type does not match.
    ASSET_FIELD_TYPE_DOES_NOT_MATCH = 5;

    // Not an automatically created asset.
    NOT_AN_AUTOMATICALLY_CREATED_ASSET = 6;
  }
}
