// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "UserListCustomerTypeErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing UserListCustomerType errors.

// Container for enum describing possible user list customer type errors.
message UserListCustomerTypeErrorEnum {
  // Enum describing possible user list customer type errors.
  enum UserListCustomerTypeError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Cannot add the conflicting customer types to the same user list.
    // Conflicting labels:
    // 1. Purchasers - Converted Leads
    // 2. Purchasers - Qualified Leads
    // 3. Purchasers - Cart Abandoners
    // 4. Qualified Leads - Converted Leads
    // 5. Disengaged customers - Converted Leads
    // 6. Disengaged customers - Qualified Leads
    // 7. Disengaged customers- Cart Abandoners
    CONFLICTING_CUSTOMER_TYPES = 2;

    // The account does not have access to the user list.
    NO_ACCESS_TO_USER_LIST = 3;

    // The given user list is not eligible for applying customer types.
    // The user list must belong to one of the following types: CRM_BASED,
    // RULE_BASED, ADVERTISER_DATA_MODEL_BASED, GCN.
    USERLIST_NOT_ELIGIBLE = 4;

    // To edit the user list customer type, conversion tracking must be
    // enabled in your account. If cross-tracking is enabled, your account must
    // be a MCC manager account to modify user list customer types. More info at
    // https://support.google.com/google-ads/answer/3030657
    CONVERSION_TRACKING_NOT_ENABLED_OR_NOT_MCC_MANAGER_ACCOUNT = 5;

    // Too many user lists for the customer type.
    TOO_MANY_USER_LISTS_FOR_THE_CUSTOMER_TYPE = 6;
  }
}
