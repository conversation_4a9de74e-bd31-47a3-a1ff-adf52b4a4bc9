// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CampaignFeedErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing campaign feed errors.

// Container for enum describing possible campaign feed errors.
message CampaignFeedErrorEnum {
  // Enum describing possible campaign feed errors.
  enum CampaignFeedError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // An active feed already exists for this campaign and placeholder type.
    FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE = 2;

    // The specified feed is removed.
    CANNOT_CREATE_FOR_REMOVED_FEED = 4;

    // The CampaignFeed already exists. UPDATE should be used to modify the
    // existing CampaignFeed.
    CANNOT_CREATE_ALREADY_EXISTING_CAMPAIGN_FEED = 5;

    // Cannot update removed campaign feed.
    CANNOT_MODIFY_REMOVED_CAMPAIGN_FEED = 6;

    // Invalid placeholder type.
    INVALID_PLACEHOLDER_TYPE = 7;

    // Feed mapping for this placeholder type does not exist.
    MISSING_FEEDMAPPING_FOR_PLACEHOLDER_TYPE = 8;

    // Location CampaignFeeds cannot be created unless there is a location
    // CustomerFeed for the specified feed.
    NO_EXISTING_LOCATION_CUSTOMER_FEED = 9;

    // Feed is read only.
    LEGACY_FEED_TYPE_READ_ONLY = 10;
  }
}
