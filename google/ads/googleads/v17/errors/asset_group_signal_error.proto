// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupSignalErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing asset group signal errors.

// Container for enum describing possible asset group signal errors.
message AssetGroupSignalErrorEnum {
  // Enum describing possible asset group signal errors.
  enum AssetGroupSignalError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The number of words in the Search Theme signal exceed the allowed
    // maximum. You can add up to 10 words in a keyword. See
    // https://support.google.com/google-ads/answer/7476658 for details.
    TOO_MANY_WORDS = 2;

    // The search theme requested to be added violates certain policy.
    // See https://support.google.com/adspolicy/answer/6008942.
    SEARCH_THEME_POLICY_VIOLATION = 3;

    // The asset group referenced by the asset group signal does not match the
    // asset group referenced by the audience being used in the asset group
    // signal.
    AUDIENCE_WITH_WRONG_ASSET_GROUP_ID = 4;
  }
}
