// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "BiddingErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing bidding errors.

// Container for enum describing possible bidding errors.
message BiddingErrorEnum {
  // Enum describing possible bidding errors.
  enum BiddingError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Cannot transition to new bidding strategy.
    BIDDING_STRATEGY_TRANSITION_NOT_ALLOWED = 2;

    // Cannot attach bidding strategy to campaign.
    CANNOT_ATTACH_BIDDING_STRATEGY_TO_CAMPAIGN = 7;

    // Bidding strategy is not supported or cannot be used as anonymous.
    INVALID_ANONYMOUS_BIDDING_STRATEGY_TYPE = 10;

    // The type does not match the named strategy's type.
    INVALID_BIDDING_STRATEGY_TYPE = 14;

    // The bid is invalid.
    INVALID_BID = 17;

    // Bidding strategy is not available for the account type.
    BIDDING_STRATEGY_NOT_AVAILABLE_FOR_ACCOUNT_TYPE = 18;

    // Campaign can not be created with given bidding strategy. It can be
    // transitioned to the strategy, once eligible.
    CANNOT_CREATE_CAMPAIGN_WITH_BIDDING_STRATEGY = 21;

    // Cannot target content network only as campaign uses Page One Promoted
    // bidding strategy.
    CANNOT_TARGET_CONTENT_NETWORK_ONLY_WITH_CAMPAIGN_LEVEL_POP_BIDDING_STRATEGY =
        23;

    // Budget Optimizer and Target Spend bidding strategies are not supported
    // for campaigns with AdSchedule targeting.
    BIDDING_STRATEGY_NOT_SUPPORTED_WITH_AD_SCHEDULE = 24;

    // Pay per conversion is not available to all the customer, only few
    // customers on the allow-list can use this.
    PAY_PER_CONVERSION_NOT_AVAILABLE_FOR_CUSTOMER = 25;

    // Pay per conversion is not allowed with Target CPA.
    PAY_PER_CONVERSION_NOT_ALLOWED_WITH_TARGET_CPA = 26;

    // Cannot set bidding strategy to Manual CPM for search network only
    // campaigns.
    BIDDING_STRATEGY_NOT_ALLOWED_FOR_SEARCH_ONLY_CAMPAIGNS = 27;

    // The bidding strategy is not supported for use in drafts or experiments.
    BIDDING_STRATEGY_NOT_SUPPORTED_IN_DRAFTS_OR_EXPERIMENTS = 28;

    // Bidding strategy type does not support product type ad group criterion.
    BIDDING_STRATEGY_TYPE_DOES_NOT_SUPPORT_PRODUCT_TYPE_ADGROUP_CRITERION = 29;

    // Bid amount is too small.
    BID_TOO_SMALL = 30;

    // Bid amount is too big.
    BID_TOO_BIG = 31;

    // Bid has too many fractional digit precision.
    BID_TOO_MANY_FRACTIONAL_DIGITS = 32;

    // Invalid domain name specified.
    INVALID_DOMAIN_NAME = 33;

    // The field is not compatible with the payment mode.
    NOT_COMPATIBLE_WITH_PAYMENT_MODE = 34;

    // Bidding strategy type is incompatible with shared budget.
    BIDDING_STRATEGY_TYPE_INCOMPATIBLE_WITH_SHARED_BUDGET = 37;

    // The attached bidding strategy and budget must be aligned with each other
    // if alignment is specified on either entity.
    BIDDING_STRATEGY_AND_BUDGET_MUST_BE_ALIGNED = 38;

    // The attached bidding strategy and budget must be attached to the same
    // campaigns to become aligned.
    BIDDING_STRATEGY_AND_BUDGET_MUST_BE_ATTACHED_TO_THE_SAME_CAMPAIGNS_TO_ALIGN =
        39;

    // The aligned bidding strategy and budget must be removed at the same time.
    BIDDING_STRATEGY_AND_BUDGET_MUST_BE_REMOVED_TOGETHER = 40;

    // cpc_bid_floor_micros is greater than cpc_bid_ceiling_micros.
    CPC_BID_FLOOR_MICROS_GREATER_THAN_CPC_BID_CEILING_MICROS = 41;
  }
}
