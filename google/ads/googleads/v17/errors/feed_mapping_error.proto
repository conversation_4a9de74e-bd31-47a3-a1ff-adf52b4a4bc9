// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "FeedMappingErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing feed item errors.

// Container for enum describing possible feed item errors.
message FeedMappingErrorEnum {
  // Enum describing possible feed item errors.
  enum FeedMappingError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The given placeholder field does not exist.
    INVALID_PLACEHOLDER_FIELD = 2;

    // The given criterion field does not exist.
    INVALID_CRITERION_FIELD = 3;

    // The given placeholder type does not exist.
    INVALID_PLACEHOLDER_TYPE = 4;

    // The given criterion type does not exist.
    INVALID_CRITERION_TYPE = 5;

    // A feed mapping must contain at least one attribute field mapping.
    NO_ATTRIBUTE_FIELD_MAPPINGS = 7;

    // The type of the feed attribute referenced in the attribute field mapping
    // must match the type of the placeholder field.
    FEED_ATTRIBUTE_TYPE_MISMATCH = 8;

    // A feed mapping for a system generated feed cannot be operated on.
    CANNOT_OPERATE_ON_MAPPINGS_FOR_SYSTEM_GENERATED_FEED = 9;

    // Only one feed mapping for a placeholder type is allowed per feed or
    // customer (depending on the placeholder type).
    MULTIPLE_MAPPINGS_FOR_PLACEHOLDER_TYPE = 10;

    // Only one feed mapping for a criterion type is allowed per customer.
    MULTIPLE_MAPPINGS_FOR_CRITERION_TYPE = 11;

    // Only one feed attribute mapping for a placeholder field is allowed
    // (depending on the placeholder type).
    MULTIPLE_MAPPINGS_FOR_PLACEHOLDER_FIELD = 12;

    // Only one feed attribute mapping for a criterion field is allowed
    // (depending on the criterion type).
    MULTIPLE_MAPPINGS_FOR_CRITERION_FIELD = 13;

    // This feed mapping may not contain any explicit attribute field mappings.
    UNEXPECTED_ATTRIBUTE_FIELD_MAPPINGS = 14;

    // Location placeholder feed mappings can only be created for Places feeds.
    LOCATION_PLACEHOLDER_ONLY_FOR_PLACES_FEEDS = 15;

    // Mappings for typed feeds cannot be modified.
    CANNOT_MODIFY_MAPPINGS_FOR_TYPED_FEED = 16;

    // The given placeholder type can only be mapped to system generated feeds.
    INVALID_PLACEHOLDER_TYPE_FOR_NON_SYSTEM_GENERATED_FEED = 17;

    // The given placeholder type cannot be mapped to a system generated feed
    // with the given type.
    INVALID_PLACEHOLDER_TYPE_FOR_SYSTEM_GENERATED_FEED_TYPE = 18;

    // The "field" oneof was not set in an AttributeFieldMapping.
    ATTRIBUTE_FIELD_MAPPING_MISSING_FIELD = 19;

    // Feed is read only.
    LEGACY_FEED_TYPE_READ_ONLY = 20;
  }
}
