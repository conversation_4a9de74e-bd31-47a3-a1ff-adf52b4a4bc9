// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "BatchJobErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing batch job errors.

// Container for enum describing possible batch job errors.
message BatchJobErrorEnum {
  // Enum describing possible request errors.
  enum BatchJobError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The batch job cannot add more operations or run after it has started
    // running.
    CANNOT_MODIFY_JOB_AFTER_JOB_STARTS_RUNNING = 2;

    // The operations for an AddBatchJobOperations request were empty.
    EMPTY_OPERATIONS = 3;

    // The sequence token for an AddBatchJobOperations request was invalid.
    INVALID_SEQUENCE_TOKEN = 4;

    // Batch job results can only be retrieved once the job is finished.
    RESULTS_NOT_READY = 5;

    // The page size for ListBatchJobResults was invalid.
    INVALID_PAGE_SIZE = 6;

    // The batch job cannot be removed because it has started running.
    CAN_ONLY_REMOVE_PENDING_JOB = 7;

    // The batch job cannot be listed due to unexpected errors such as duplicate
    // checkpoints.
    CANNOT_LIST_RESULTS = 8;

    // The request contains interdependent AssetGroup and AssetGroupAsset
    // operations that are treated atomically as a single transaction, and one
    // or more of the operations in that transaction failed, which caused the
    // entire transaction, and therefore this mutate operation, to fail. The
    // operations that caused the transaction to fail can be found in the
    // consecutive AssetGroup or AssetGroupAsset results with the same asset
    // group id. The mutate operation will be successful once the remaining
    // errors in the transaction are fixed.
    ASSET_GROUP_AND_ASSET_GROUP_ASSET_TRANSACTION_FAILURE = 9;

    // The request contains interdependent AssetGroupListingGroupFilter
    // operations that are treated atomically as a single transaction, and one
    // or more of the operations in that transaction failed, which caused the
    // entire transaction, and therefore this mutate operation, to fail. The
    // operations that caused the transaction to fail can be found in the
    // consecutive AssetGroupListingGroupFilter results with the same asset
    // group id. The mutate operation will be successful once the remaining
    // errors in the transaction are fixed.
    ASSET_GROUP_LISTING_GROUP_FILTER_TRANSACTION_FAILURE = 10;

    // The AddBatchJobOperationsRequest is too large. Split the request into
    // smaller requests. The maximum allowed request size is 10484504 bytes.
    REQUEST_TOO_LARGE = 11;
  }
}
