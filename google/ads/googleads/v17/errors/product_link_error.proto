// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ProductLinkErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing ProductLink errors.

// Container for enum describing possible ProductLink errors.
message ProductLinkErrorEnum {
  // Enum describing possible ProductLink errors.
  enum ProductLinkError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The requested operation is invalid. For example, you are not allowed to
    // remove a link from a partner account.
    INVALID_OPERATION = 2;

    // The creation request is not permitted.
    CREATION_NOT_PERMITTED = 3;

    // A link cannot be created because a pending link already exists.
    INVITATION_EXISTS = 4;

    // A link cannot be created because an active link already exists.
    LINK_EXISTS = 5;
  }
}
