// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "YoutubeVideoRegistrationErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing YouTube video registration errors.

// Container for enum describing YouTube video registration errors.
message YoutubeVideoRegistrationErrorEnum {
  // Enum describing YouTube video registration errors.
  enum YoutubeVideoRegistrationError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Video to be registered wasn't found.
    VIDEO_NOT_FOUND = 2;

    // Video to be registered is not accessible (for example, private).
    VIDEO_NOT_ACCESSIBLE = 3;

    // Video to be registered is not eligible (for example, mature content).
    VIDEO_NOT_ELIGIBLE = 4;
  }
}
