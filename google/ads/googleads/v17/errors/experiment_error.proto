// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ExperimentErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing experiment errors.

// Container for enum describing possible experiment error.
message ExperimentErrorEnum {
  // Enum describing possible experiment errors.
  enum ExperimentError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The start date of an experiment cannot be set in the past.
    // Use a start date in the future.
    CANNOT_SET_START_DATE_IN_PAST = 2;

    // The end date of an experiment is before its start date.
    // Use an end date after the start date.
    END_DATE_BEFORE_START_DATE = 3;

    // The start date of an experiment is too far in the future.
    // Use a start date no more than 1 year in the future.
    START_DATE_TOO_FAR_IN_FUTURE = 4;

    // The experiment has the same name as an existing active experiment.
    DUPLICATE_EXPERIMENT_NAME = 5;

    // Experiments can only be modified when they are ENABLED.
    CANNOT_MODIFY_REMOVED_EXPERIMENT = 6;

    // The start date of an experiment cannot be modified if the existing start
    // date has already passed.
    START_DATE_ALREADY_PASSED = 7;

    // The end date of an experiment cannot be set in the past.
    CANNOT_SET_END_DATE_IN_PAST = 8;

    // The status of an experiment cannot be set to REMOVED.
    CANNOT_SET_STATUS_TO_REMOVED = 9;

    // The end date of an expired experiment cannot be modified.
    CANNOT_MODIFY_PAST_END_DATE = 10;

    // The status is invalid.
    INVALID_STATUS = 11;

    // Experiment arm contains campaigns with invalid advertising channel type.
    INVALID_CAMPAIGN_CHANNEL_TYPE = 12;

    // A pair of trials share members and have overlapping date ranges.
    OVERLAPPING_MEMBERS_AND_DATE_RANGE = 13;

    // Experiment arm contains invalid traffic split.
    INVALID_TRIAL_ARM_TRAFFIC_SPLIT = 14;

    // Experiment contains trial arms with overlapping traffic split.
    TRAFFIC_SPLIT_OVERLAPPING = 15;

    // The total traffic split of trial arms is not equal to 100.
    SUM_TRIAL_ARM_TRAFFIC_UNEQUALS_TO_TRIAL_TRAFFIC_SPLIT_DENOMINATOR = 16;

    // Traffic split related settings (like traffic share bounds) can't be
    // modified after the experiment has started.
    CANNOT_MODIFY_TRAFFIC_SPLIT_AFTER_START = 17;

    // The experiment could not be found.
    EXPERIMENT_NOT_FOUND = 18;

    // Experiment has not begun.
    EXPERIMENT_NOT_YET_STARTED = 19;

    // The experiment cannot have more than one control arm.
    CANNOT_HAVE_MULTIPLE_CONTROL_ARMS = 20;

    // The experiment doesn't set in-design campaigns.
    IN_DESIGN_CAMPAIGNS_NOT_SET = 21;

    // Clients must use the graduate action to graduate experiments and cannot
    // set the status to GRADUATED directly.
    CANNOT_SET_STATUS_TO_GRADUATED = 22;

    // Cannot use shared budget on base campaign when scheduling an experiment.
    CANNOT_CREATE_EXPERIMENT_CAMPAIGN_WITH_SHARED_BUDGET = 23;

    // Cannot use custom budget on base campaign when scheduling an experiment.
    CANNOT_CREATE_EXPERIMENT_CAMPAIGN_WITH_CUSTOM_BUDGET = 24;

    // Invalid status transition.
    STATUS_TRANSITION_INVALID = 25;

    // The experiment campaign name conflicts with a pre-existing campaign.
    DUPLICATE_EXPERIMENT_CAMPAIGN_NAME = 26;

    // Cannot remove in creation experiments.
    CANNOT_REMOVE_IN_CREATION_EXPERIMENT = 27;

    // Cannot add campaign with deprecated ad types. Deprecated ad types:
    // ENHANCED_DISPLAY, GALLERY, GMAIL, KEYWORDLESS, TEXT.
    CANNOT_ADD_CAMPAIGN_WITH_DEPRECATED_AD_TYPES = 28;

    // Sync can only be enabled for supported experiment types. Supported
    // experiment types: SEARCH_CUSTOM, DISPLAY_CUSTOM,
    // DISPLAY_AUTOMATED_BIDDING_STRATEGY, SEARCH_AUTOMATED_BIDDING_STRATEGY.
    CANNOT_ENABLE_SYNC_FOR_UNSUPPORTED_EXPERIMENT_TYPE = 29;

    // Experiment length cannot be longer than max length.
    INVALID_DURATION_FOR_AN_EXPERIMENT = 30;
  }
}
