// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AdSharingErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing ad sharing errors.

// Container for enum describing possible ad sharing errors.
message AdSharingErrorEnum {
  // Enum describing possible ad sharing errors.
  enum AdSharingError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Error resulting in attempting to add an Ad to an AdGroup that already
    // contains the Ad.
    AD_GROUP_ALREADY_CONTAINS_AD = 2;

    // Ad is not compatible with the AdGroup it is being shared with.
    INCOMPATIBLE_AD_UNDER_AD_GROUP = 3;

    // Cannot add AdGroupAd on inactive Ad.
    CANNOT_SHARE_INACTIVE_AD = 4;
  }
}
