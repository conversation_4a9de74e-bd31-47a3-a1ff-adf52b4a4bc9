// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "FieldErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing field errors.

// Container for enum describing possible field errors.
message FieldErrorEnum {
  // Enum describing possible field errors.
  enum FieldError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The required field was not present.
    REQUIRED = 2;

    // The field attempted to be mutated is immutable.
    IMMUTABLE_FIELD = 3;

    // The field's value is invalid.
    INVALID_VALUE = 4;

    // The field cannot be set.
    VALUE_MUST_BE_UNSET = 5;

    // The required repeated field was empty.
    REQUIRED_NONEMPTY_LIST = 6;

    // The field cannot be cleared.
    FIELD_CANNOT_BE_CLEARED = 7;

    // The field's value is on a deny-list for this field.
    BLOCKED_VALUE = 9;

    // The field's value cannot be modified, except for clearing.
    FIELD_CAN_ONLY_BE_CLEARED = 10;
  }
}
