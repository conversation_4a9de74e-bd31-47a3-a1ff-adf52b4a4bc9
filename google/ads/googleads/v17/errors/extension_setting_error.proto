// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ExtensionSettingErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing extension setting validation errors.

// Container for enum describing validation errors of extension settings.
message ExtensionSettingErrorEnum {
  // Enum describing possible extension setting errors.
  enum ExtensionSettingError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // A platform restriction was provided without input extensions or existing
    // extensions.
    EXTENSIONS_REQUIRED = 2;

    // The provided feed type does not correspond to the provided extensions.
    FEED_TYPE_EXTENSION_TYPE_MISMATCH = 3;

    // The provided feed type cannot be used.
    INVALID_FEED_TYPE = 4;

    // The provided feed type cannot be used at the customer level.
    INVALID_FEED_TYPE_FOR_CUSTOMER_EXTENSION_SETTING = 5;

    // Cannot change a feed item field on a CREATE operation.
    CANNOT_CHANGE_FEED_ITEM_ON_CREATE = 6;

    // Cannot update an extension that is not already in this setting.
    CANNOT_UPDATE_NEWLY_CREATED_EXTENSION = 7;

    // There is no existing AdGroupExtensionSetting for this type.
    NO_EXISTING_AD_GROUP_EXTENSION_SETTING_FOR_TYPE = 8;

    // There is no existing CampaignExtensionSetting for this type.
    NO_EXISTING_CAMPAIGN_EXTENSION_SETTING_FOR_TYPE = 9;

    // There is no existing CustomerExtensionSetting for this type.
    NO_EXISTING_CUSTOMER_EXTENSION_SETTING_FOR_TYPE = 10;

    // The AdGroupExtensionSetting already exists. UPDATE should be used to
    // modify the existing AdGroupExtensionSetting.
    AD_GROUP_EXTENSION_SETTING_ALREADY_EXISTS = 11;

    // The CampaignExtensionSetting already exists. UPDATE should be used to
    // modify the existing CampaignExtensionSetting.
    CAMPAIGN_EXTENSION_SETTING_ALREADY_EXISTS = 12;

    // The CustomerExtensionSetting already exists. UPDATE should be used to
    // modify the existing CustomerExtensionSetting.
    CUSTOMER_EXTENSION_SETTING_ALREADY_EXISTS = 13;

    // An active ad group feed already exists for this place holder type.
    AD_GROUP_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE = 14;

    // An active campaign feed already exists for this place holder type.
    CAMPAIGN_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE = 15;

    // An active customer feed already exists for this place holder type.
    CUSTOMER_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE = 16;

    // Value is not within the accepted range.
    VALUE_OUT_OF_RANGE = 17;

    // Cannot simultaneously set specified field with final urls.
    CANNOT_SET_FIELD_WITH_FINAL_URLS = 18;

    // Must set field with final urls.
    FINAL_URLS_NOT_SET = 19;

    // Phone number for a call extension is invalid.
    INVALID_PHONE_NUMBER = 20;

    // Phone number for a call extension is not supported for the given country
    // code.
    PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY = 21;

    // A carrier specific number in short format is not allowed for call
    // extensions.
    CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED = 22;

    // Premium rate numbers are not allowed for call extensions.
    PREMIUM_RATE_NUMBER_NOT_ALLOWED = 23;

    // Phone number type for a call extension is not allowed.
    DISALLOWED_NUMBER_TYPE = 24;

    // Phone number for a call extension does not meet domestic format
    // requirements.
    INVALID_DOMESTIC_PHONE_NUMBER_FORMAT = 25;

    // Vanity phone numbers (for example, those including letters) are not
    // allowed for call extensions.
    VANITY_PHONE_NUMBER_NOT_ALLOWED = 26;

    // Country code provided for a call extension is invalid.
    INVALID_COUNTRY_CODE = 27;

    // Call conversion type id provided for a call extension is invalid.
    INVALID_CALL_CONVERSION_TYPE_ID = 28;

    // For a call extension, the customer is not on the allow-list for call
    // tracking.
    CUSTOMER_NOT_IN_ALLOWLIST_FOR_CALLTRACKING = 69;

    // Call tracking is not supported for the given country for a call
    // extension.
    CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY = 30;

    // App id provided for an app extension is invalid.
    INVALID_APP_ID = 31;

    // Quotation marks present in the review text for a review extension.
    QUOTES_IN_REVIEW_EXTENSION_SNIPPET = 32;

    // Hyphen character present in the review text for a review extension.
    HYPHENS_IN_REVIEW_EXTENSION_SNIPPET = 33;

    // A blocked review source name or url was provided for a review
    // extension.
    REVIEW_EXTENSION_SOURCE_NOT_ELIGIBLE = 34;

    // Review source name should not be found in the review text.
    SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT = 35;

    // Field must be set.
    MISSING_FIELD = 36;

    // Inconsistent currency codes.
    INCONSISTENT_CURRENCY_CODES = 37;

    // Price extension cannot have duplicated headers.
    PRICE_EXTENSION_HAS_DUPLICATED_HEADERS = 38;

    // Price item cannot have duplicated header and description.
    PRICE_ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION = 39;

    // Price extension has too few items
    PRICE_EXTENSION_HAS_TOO_FEW_ITEMS = 40;

    // Price extension has too many items
    PRICE_EXTENSION_HAS_TOO_MANY_ITEMS = 41;

    // The input value is not currently supported.
    UNSUPPORTED_VALUE = 42;

    // Unknown or unsupported device preference.
    INVALID_DEVICE_PREFERENCE = 43;

    // Invalid feed item schedule end time (for example, endHour = 24 and
    // endMinute != 0).
    INVALID_SCHEDULE_END = 45;

    // Date time zone does not match the account's time zone.
    DATE_TIME_MUST_BE_IN_ACCOUNT_TIME_ZONE = 47;

    // Overlapping feed item schedule times (for example, 7-10AM and 8-11AM) are
    // not allowed.
    OVERLAPPING_SCHEDULES_NOT_ALLOWED = 48;

    // Feed item schedule end time must be after start time.
    SCHEDULE_END_NOT_AFTER_START = 49;

    // There are too many feed item schedules per day.
    TOO_MANY_SCHEDULES_PER_DAY = 50;

    // Cannot edit the same extension feed item more than once in the same
    // request.
    DUPLICATE_EXTENSION_FEED_ITEM_EDIT = 51;

    // Invalid structured snippet header.
    INVALID_SNIPPETS_HEADER = 52;

    // Phone number with call tracking enabled is not supported for the
    // specified country.
    PHONE_NUMBER_NOT_SUPPORTED_WITH_CALLTRACKING_FOR_COUNTRY = 53;

    // The targeted adgroup must belong to the targeted campaign.
    CAMPAIGN_TARGETING_MISMATCH = 54;

    // The feed used by the ExtensionSetting is removed and cannot be operated
    // on. Remove the ExtensionSetting to allow a new one to be created using
    // an active feed.
    CANNOT_OPERATE_ON_REMOVED_FEED = 55;

    // The ExtensionFeedItem type is required for this operation.
    EXTENSION_TYPE_REQUIRED = 56;

    // The matching function that links the extension feed to the customer,
    // campaign, or ad group is not compatible with the ExtensionSetting
    // services.
    INCOMPATIBLE_UNDERLYING_MATCHING_FUNCTION = 57;

    // Start date must be before end date.
    START_DATE_AFTER_END_DATE = 58;

    // Input price is not in a valid format.
    INVALID_PRICE_FORMAT = 59;

    // The promotion time is invalid.
    PROMOTION_INVALID_TIME = 60;

    // Cannot set both percent discount and money discount fields.
    PROMOTION_CANNOT_SET_PERCENT_DISCOUNT_AND_MONEY_DISCOUNT = 61;

    // Cannot set both promotion code and orders over amount fields.
    PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT = 62;

    // This field has too many decimal places specified.
    TOO_MANY_DECIMAL_PLACES_SPECIFIED = 63;

    // The language code is not valid.
    INVALID_LANGUAGE_CODE = 64;

    // The language is not supported.
    UNSUPPORTED_LANGUAGE = 65;

    // Customer hasn't consented for call recording, which is required for
    // adding/updating call extensions. See
    // https://support.google.com/google-ads/answer/7412639.
    CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED = 66;

    // The UPDATE operation does not specify any fields other than the resource
    // name in the update mask.
    EXTENSION_SETTING_UPDATE_IS_A_NOOP = 67;

    // The extension contains text which has been prohibited on policy grounds.
    DISALLOWED_TEXT = 68;
  }
}
