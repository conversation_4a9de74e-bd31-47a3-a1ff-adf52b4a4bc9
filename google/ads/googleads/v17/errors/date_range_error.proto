// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "DateRangeErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing date range errors.

// Container for enum describing possible date range errors.
message DateRangeErrorEnum {
  // Enum describing possible date range errors.
  enum DateRangeError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Invalid date.
    INVALID_DATE = 2;

    // The start date was after the end date.
    START_DATE_AFTER_END_DATE = 3;

    // Cannot set date to past time
    CANNOT_SET_DATE_TO_PAST = 4;

    // A date was used that is past the system "last" date.
    AFTER_MAXIMUM_ALLOWABLE_DATE = 5;

    // Trying to change start date on a resource that has started.
    CANNOT_MODIFY_START_DATE_IF_ALREADY_STARTED = 6;
  }
}
