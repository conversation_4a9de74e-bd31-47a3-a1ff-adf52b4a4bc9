// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ManagerLinkErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing ManagerLink errors.

// Container for enum describing possible ManagerLink errors.
message ManagerLinkErrorEnum {
  // Enum describing possible ManagerLink errors.
  enum ManagerLinkError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The manager and client have incompatible account types.
    ACCOUNTS_NOT_COMPATIBLE_FOR_LINKING = 2;

    // Client is already linked to too many managers.
    TOO_MANY_MANAGERS = 3;

    // Manager has too many pending invitations.
    TOO_MANY_INVITES = 4;

    // Client is already invited by this manager.
    ALREADY_INVITED_BY_THIS_MANAGER = 5;

    // The client is already managed by this manager.
    ALREADY_MANAGED_BY_THIS_MANAGER = 6;

    // Client is already managed in hierarchy.
    ALREADY_MANAGED_IN_HIERARCHY = 7;

    // Manager and sub-manager to be linked have duplicate client.
    DUPLICATE_CHILD_FOUND = 8;

    // Client has no active user that can access the client account.
    CLIENT_HAS_NO_ADMIN_USER = 9;

    // Adding this link would exceed the maximum hierarchy depth.
    MAX_DEPTH_EXCEEDED = 10;

    // Adding this link will create a cycle.
    CYCLE_NOT_ALLOWED = 11;

    // Manager account has the maximum number of linked clients.
    TOO_MANY_ACCOUNTS = 12;

    // Parent manager account has the maximum number of linked clients.
    TOO_MANY_ACCOUNTS_AT_MANAGER = 13;

    // The account is not authorized owner.
    NON_OWNER_USER_CANNOT_MODIFY_LINK = 14;

    // Your manager account is suspended, and you are no longer allowed to link
    // to clients.
    SUSPENDED_ACCOUNT_CANNOT_ADD_CLIENTS = 15;

    // You are not allowed to move a client to a manager that is not under your
    // current hierarchy.
    CLIENT_OUTSIDE_TREE = 16;

    // The changed status for mutate link is invalid.
    INVALID_STATUS_CHANGE = 17;

    // The change for mutate link is invalid.
    INVALID_CHANGE = 18;

    // You are not allowed to link a manager account to itself.
    CUSTOMER_CANNOT_MANAGE_SELF = 19;

    // The link was created with status ACTIVE and not PENDING.
    CREATING_ENABLED_LINK_NOT_ALLOWED = 20;
  }
}
