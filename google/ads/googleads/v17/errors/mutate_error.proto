// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "MutateErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing mutate errors.

// Container for enum describing possible mutate errors.
message MutateErrorEnum {
  // Enum describing possible mutate errors.
  enum MutateError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Requested resource was not found.
    RESOURCE_NOT_FOUND = 3;

    // Cannot mutate the same resource twice in one request.
    ID_EXISTS_IN_MULTIPLE_MUTATES = 7;

    // The field's contents don't match another field that represents the same
    // data.
    INCONSISTENT_FIELD_VALUES = 8;

    // Mutates are not allowed for the requested resource.
    MUTATE_NOT_ALLOWED = 9;

    // The resource isn't in Google Ads. It belongs to another ads system.
    RESOURCE_NOT_IN_GOOGLE_ADS = 10;

    // The resource being created already exists.
    RESOURCE_ALREADY_EXISTS = 11;

    // This resource cannot be used with "validate_only".
    RESOURCE_DOES_NOT_SUPPORT_VALIDATE_ONLY = 12;

    // This operation cannot be used with "partial_failure".
    OPERATION_DOES_NOT_SUPPORT_PARTIAL_FAILURE = 16;

    // Attempt to write to read-only fields.
    RESOURCE_READ_ONLY = 13;
  }
}
