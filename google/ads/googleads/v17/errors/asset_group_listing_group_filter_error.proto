// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupListingGroupFilterErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing asset group asset errors.

// Container for enum describing possible asset group listing group filter
// errors.
message AssetGroupListingGroupFilterErrorEnum {
  // Enum describing possible asset group listing group filter errors.
  enum AssetGroupListingGroupFilterError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Listing group tree is too deep.
    TREE_TOO_DEEP = 2;

    // Listing Group UNIT node cannot have children.
    UNIT_CANNOT_HAVE_CHILDREN = 3;

    // Listing Group SUBDIVISION node must have everything else child.
    SUBDIVISION_MUST_HAVE_EVERYTHING_ELSE_CHILD = 4;

    // Dimension type of Listing Group must be the same as that of its siblings.
    DIFFERENT_DIMENSION_TYPE_BETWEEN_SIBLINGS = 5;

    // The sibling Listing Groups target exactly the same dimension value.
    SAME_DIMENSION_VALUE_BETWEEN_SIBLINGS = 6;

    // The dimension type is the same as one of the ancestor Listing Groups.
    SAME_DIMENSION_TYPE_BETWEEN_ANCESTORS = 7;

    // Each Listing Group tree must have a single root.
    MULTIPLE_ROOTS = 8;

    // Invalid Listing Group dimension value.
    INVALID_DIMENSION_VALUE = 9;

    // Hierarchical dimension must refine a dimension of the same type.
    MUST_REFINE_HIERARCHICAL_PARENT_TYPE = 10;

    // Invalid Product Bidding Category.
    INVALID_PRODUCT_BIDDING_CATEGORY = 11;

    // Modifying case value is allowed only while updating the entire subtree at
    // the same time.
    CHANGING_CASE_VALUE_WITH_CHILDREN = 12;

    // Subdivision node has children which must be removed first.
    SUBDIVISION_HAS_CHILDREN = 13;

    // Dimension can't subdivide everything-else node in its own hierarchy.
    CANNOT_REFINE_HIERARCHICAL_EVERYTHING_ELSE = 14;

    // This dimension type is not allowed in this context.
    DIMENSION_TYPE_NOT_ALLOWED = 15;

    // All the webpage filters under an AssetGroup should be distinct.
    DUPLICATE_WEBPAGE_FILTER_UNDER_ASSET_GROUP = 16;

    // Filter of the listing source type is not allowed in the context.
    LISTING_SOURCE_NOT_ALLOWED = 17;

    // Exclusion filters are not allowed in the context.
    FILTER_EXCLUSION_NOT_ALLOWED = 18;

    // All the filters under an AssetGroup should have the same listing source.
    MULTIPLE_LISTING_SOURCES = 19;

    // All the conditions in a webpage needs to be of same type.
    MULTIPLE_WEBPAGE_CONDITION_TYPES_NOT_ALLOWED = 20;

    // All the webpage types of the filters under an AssetGroup should be of
    // same type. Example: All the webpage types can be of type custom_label or
    // url_contains but not both.
    MULTIPLE_WEBPAGE_TYPES_PER_ASSET_GROUP = 21;

    // All page feed filter nodes are root nodes and they can't have a parent.
    PAGE_FEED_FILTER_HAS_PARENT = 22;

    // There cannot be more than one mutate operation per request that targets a
    // single asset group listing group filter.
    MULTIPLE_OPERATIONS_ON_ONE_NODE = 23;
  }
}
