// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing asset group errors.

// Container for enum describing possible asset group errors.
message AssetGroupErrorEnum {
  // Enum describing possible asset group errors.
  enum AssetGroupError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Each asset group in a single campaign must have a unique name.
    DUPLICATE_NAME = 2;

    // Cannot add asset group for the campaign type.
    CANNOT_ADD_ASSET_GROUP_FOR_CAMPAIGN_TYPE = 3;

    // Not enough headline asset for a valid asset group.
    NOT_ENOUGH_HEADLINE_ASSET = 4;

    // Not enough long headline asset for a valid asset group.
    NOT_ENOUGH_LONG_HEADLINE_ASSET = 5;

    // Not enough description headline asset for a valid asset group.
    NOT_ENOUGH_DESCRIPTION_ASSET = 6;

    // Not enough business name asset for a valid asset group.
    NOT_ENOUGH_BUSINESS_NAME_ASSET = 7;

    // Not enough marketing image asset for a valid asset group.
    NOT_ENOUGH_MARKETING_IMAGE_ASSET = 8;

    // Not enough square marketing image asset for a valid asset group.
    NOT_ENOUGH_SQUARE_MARKETING_IMAGE_ASSET = 9;

    // Not enough logo asset for a valid asset group.
    NOT_ENOUGH_LOGO_ASSET = 10;

    // Final url and shopping merchant url does not have the same domain.
    FINAL_URL_SHOPPING_MERCHANT_HOME_PAGE_URL_DOMAINS_DIFFER = 11;

    // Path1 required when path2 is set.
    PATH1_REQUIRED_WHEN_PATH2_IS_SET = 12;

    // At least one short description asset is required for a valid asset group.
    SHORT_DESCRIPTION_REQUIRED = 13;

    // Final url field is required for asset group.
    FINAL_URL_REQUIRED = 14;

    // Final url contains invalid domain name.
    FINAL_URL_CONTAINS_INVALID_DOMAIN_NAME = 15;

    // Ad customizers are not supported in asset group's text field.
    AD_CUSTOMIZER_NOT_SUPPORTED = 16;

    // Cannot mutate asset group for campaign with removed status.
    CANNOT_MUTATE_ASSET_GROUP_FOR_REMOVED_CAMPAIGN = 17;
  }
}
