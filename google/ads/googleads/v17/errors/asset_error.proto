// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing asset errors.

// Container for enum describing possible asset errors.
message AssetErrorEnum {
  // Enum describing possible asset errors.
  enum AssetError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The customer is not is not on the allow-list for this asset type.
    CUSTOMER_NOT_ON_ALLOWLIST_FOR_ASSET_TYPE = 13;

    // Assets are duplicated across operations.
    DUPLICATE_ASSET = 3;

    // The asset name is duplicated, either across operations or with an
    // existing asset.
    DUPLICATE_ASSET_NAME = 4;

    // The Asset.asset_data oneof is empty.
    ASSET_DATA_IS_MISSING = 5;

    // The asset has a name which is different from an existing duplicate that
    // represents the same content.
    CANNOT_MODIFY_ASSET_NAME = 6;

    // The field cannot be set for this asset type.
    FIELD_INCOMPATIBLE_WITH_ASSET_TYPE = 7;

    // Call to action must come from the list of supported values.
    INVALID_CALL_TO_ACTION_TEXT = 8;

    // A lead form asset is created with an invalid combination of input fields.
    LEAD_FORM_INVALID_FIELDS_COMBINATION = 9;

    // Lead forms require that the Terms of Service have been agreed to before
    // mutates can be executed.
    LEAD_FORM_MISSING_AGREEMENT = 10;

    // Asset status is invalid in this operation.
    INVALID_ASSET_STATUS = 11;

    // The field cannot be modified by this asset type.
    FIELD_CANNOT_BE_MODIFIED_FOR_ASSET_TYPE = 12;

    // Ad schedules for the same asset cannot overlap.
    SCHEDULES_CANNOT_OVERLAP = 14;

    // Cannot set both percent off and money amount off fields of promotion
    // asset.
    PROMOTION_CANNOT_SET_PERCENT_OFF_AND_MONEY_AMOUNT_OFF = 15;

    // Cannot set both promotion code and orders over amount fields of promotion
    // asset.
    PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT = 16;

    // The field has too many decimal places specified.
    TOO_MANY_DECIMAL_PLACES_SPECIFIED = 17;

    // Duplicate assets across operations, which have identical Asset.asset_data
    // oneof, cannot have different asset level fields for asset types which are
    // deduped.
    DUPLICATE_ASSETS_WITH_DIFFERENT_FIELD_VALUE = 18;

    // Carrier specific short number is not allowed.
    CALL_CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED = 19;

    // Customer consent required for call recording Terms of Service.
    CALL_CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED = 20;

    // The type of the specified phone number is not allowed.
    CALL_DISALLOWED_NUMBER_TYPE = 21;

    // If the default call_conversion_action is not used, the customer must have
    // a ConversionAction with the same id and the ConversionAction must be call
    // conversion type.
    CALL_INVALID_CONVERSION_ACTION = 22;

    // The country code of the phone number is invalid.
    CALL_INVALID_COUNTRY_CODE = 23;

    // The format of the phone number is incorrect.
    CALL_INVALID_DOMESTIC_PHONE_NUMBER_FORMAT = 24;

    // The input phone number is not a valid phone number.
    CALL_INVALID_PHONE_NUMBER = 25;

    // The phone number is not supported for country.
    CALL_PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY = 26;

    // Premium rate phone number is not allowed.
    CALL_PREMIUM_RATE_NUMBER_NOT_ALLOWED = 27;

    // Vanity phone number is not allowed.
    CALL_VANITY_PHONE_NUMBER_NOT_ALLOWED = 28;

    // PriceOffering cannot have the same value for header and description.
    PRICE_HEADER_SAME_AS_DESCRIPTION = 29;

    // AppId is invalid.
    MOBILE_APP_INVALID_APP_ID = 30;

    // Invalid App download URL in final URLs.
    MOBILE_APP_INVALID_FINAL_URL_FOR_APP_DOWNLOAD_URL = 31;

    // Asset name is required for the asset type.
    NAME_REQUIRED_FOR_ASSET_TYPE = 32;

    // Legacy qualifying questions cannot be in the same Lead Form as
    // custom questions.
    LEAD_FORM_LEGACY_QUALIFYING_QUESTIONS_DISALLOWED = 33;

    // Unique name is required for this asset type.
    NAME_CONFLICT_FOR_ASSET_TYPE = 34;

    // Cannot modify asset source.
    CANNOT_MODIFY_ASSET_SOURCE = 35;

    // User can not modify the automatically created asset.
    CANNOT_MODIFY_AUTOMATICALLY_CREATED_ASSET = 36;

    // Lead Form is disallowed to use "LOCATION" answer type.
    LEAD_FORM_LOCATION_ANSWER_TYPE_DISALLOWED = 37;

    // Page Feed label text contains invalid characters.
    PAGE_FEED_INVALID_LABEL_TEXT = 38;
  }
}
