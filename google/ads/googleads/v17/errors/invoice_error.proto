// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "InvoiceErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing invoice errors.

// Container for enum describing possible invoice errors.
message InvoiceErrorEnum {
  // Enum describing possible invoice errors.
  enum InvoiceError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Cannot request invoices issued before 2019-01-01.
    YEAR_MONTH_TOO_OLD = 2;

    // Cannot request invoices for customer who doesn't receive invoices.
    NOT_INVOICED_CUSTOMER = 3;

    // Cannot request invoices for a non approved billing setup.
    BILLING_SETUP_NOT_APPROVED = 4;

    // Cannot request invoices for a billing setup that is not on monthly
    // invoicing.
    BILLING_SETUP_NOT_ON_MONTHLY_INVOICING = 5;

    // Cannot request invoices for a non serving customer.
    NON_SERVING_CUSTOMER = 6;
  }
}
