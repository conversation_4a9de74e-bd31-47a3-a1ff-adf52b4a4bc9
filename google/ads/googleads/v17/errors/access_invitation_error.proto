// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AccessInvitationErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing AccessInvitation errors.

// Container for enum describing possible AccessInvitation errors.
message AccessInvitationErrorEnum {
  // Enum describing possible AccessInvitation errors.
  enum AccessInvitationError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // The email address is invalid for sending an invitation.
    INVALID_EMAIL_ADDRESS = 2;

    // Email address already has access to this customer.
    EMAIL_ADDRESS_ALREADY_HAS_ACCESS = 3;

    // Invalid invitation status for the operation.
    INVALID_INVITATION_STATUS = 4;

    // Email address cannot <NAME_EMAIL>.
    GOOGLE_CONSUMER_ACCOUNT_NOT_ALLOWED = 5;

    // Invalid invitation ID.
    INVALID_INVITATION_ID = 6;

    // Email address already has a pending invitation.
    EMAIL_ADDRESS_ALREADY_HAS_PENDING_INVITATION = 7;

    // Pending invitation limit exceeded for the customer.
    PENDING_INVITATIONS_LIMIT_EXCEEDED = 8;

    // Email address doesn't conform to the email domain policy. See
    // https://support.google.com/google-ads/answer/2375456
    EMAIL_DOMAIN_POLICY_VIOLATED = 9;
  }
}
