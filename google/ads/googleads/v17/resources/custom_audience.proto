// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/custom_audience_member_type.proto";
import "google/ads/googleads/v17/enums/custom_audience_status.proto";
import "google/ads/googleads/v17/enums/custom_audience_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomAudienceProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Custom Audience resource.

// A custom audience. This is a list of users by interest.
message CustomAudience {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomAudience"
    pattern: "customers/{customer_id}/customAudiences/{custom_audience_id}"
  };

  // Immutable. The resource name of the custom audience.
  // Custom audience resource names have the form:
  //
  // `customers/{customer_id}/customAudiences/{custom_audience_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomAudience"
    }
  ];

  // Output only. ID of the custom audience.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of this custom audience. Indicates whether the custom
  // audience is enabled or removed.
  google.ads.googleads.v17.enums.CustomAudienceStatusEnum.CustomAudienceStatus
      status = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Name of the custom audience. It should be unique for all custom audiences
  // created by a customer.
  // This field is required for creating operations.
  string name = 4;

  // Type of the custom audience.
  // ("INTEREST" OR "PURCHASE_INTENT" is not allowed for newly created custom
  // audience but kept for existing audiences)
  google.ads.googleads.v17.enums.CustomAudienceTypeEnum.CustomAudienceType
      type = 5;

  // Description of this custom audience.
  string description = 6;

  // List of custom audience members that this custom audience is composed of.
  // Members can be added during CustomAudience creation. If members are
  // presented in UPDATE operation, existing members will be overridden.
  repeated CustomAudienceMember members = 7;
}

// A member of custom audience. A member can be a KEYWORD, URL,
// PLACE_CATEGORY or APP. It can only be created or removed but not changed.
message CustomAudienceMember {
  // The type of custom audience member, KEYWORD, URL, PLACE_CATEGORY or APP.
  google.ads.googleads.v17.enums.CustomAudienceMemberTypeEnum
      .CustomAudienceMemberType member_type = 1;

  // The CustomAudienceMember value. One field is populated depending on the
  // member type.
  oneof value {
    // A keyword or keyword phrase — at most 10 words and 80 characters.
    // Languages with double-width characters such as Chinese, Japanese,
    // or Korean, are allowed 40 characters, which describes the user's
    // interests or actions.
    string keyword = 2;

    // An HTTP URL, protocol-included — at most 2048 characters, which includes
    // contents users have interests in.
    string url = 3;

    // A place type described by a place category users visit.
    int64 place_category = 4;

    // A package name of Android apps which users installed such as
    // com.google.example.
    string app = 5;
  }
}
