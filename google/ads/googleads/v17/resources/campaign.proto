// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/bidding.proto";
import "google/ads/googleads/v17/common/custom_parameter.proto";
import "google/ads/googleads/v17/common/frequency_cap.proto";
import "google/ads/googleads/v17/common/real_time_bidding_setting.proto";
import "google/ads/googleads/v17/common/targeting_setting.proto";
import "google/ads/googleads/v17/enums/ad_serving_optimization_status.proto";
import "google/ads/googleads/v17/enums/advertising_channel_sub_type.proto";
import "google/ads/googleads/v17/enums/advertising_channel_type.proto";
import "google/ads/googleads/v17/enums/app_campaign_app_store.proto";
import "google/ads/googleads/v17/enums/app_campaign_bidding_strategy_goal_type.proto";
import "google/ads/googleads/v17/enums/asset_automation_status.proto";
import "google/ads/googleads/v17/enums/asset_automation_type.proto";
import "google/ads/googleads/v17/enums/asset_field_type.proto";
import "google/ads/googleads/v17/enums/asset_set_type.proto";
import "google/ads/googleads/v17/enums/bidding_strategy_system_status.proto";
import "google/ads/googleads/v17/enums/bidding_strategy_type.proto";
import "google/ads/googleads/v17/enums/brand_safety_suitability.proto";
import "google/ads/googleads/v17/enums/campaign_experiment_type.proto";
import "google/ads/googleads/v17/enums/campaign_keyword_match_type.proto";
import "google/ads/googleads/v17/enums/campaign_primary_status.proto";
import "google/ads/googleads/v17/enums/campaign_primary_status_reason.proto";
import "google/ads/googleads/v17/enums/campaign_serving_status.proto";
import "google/ads/googleads/v17/enums/campaign_status.proto";
import "google/ads/googleads/v17/enums/listing_type.proto";
import "google/ads/googleads/v17/enums/location_source_type.proto";
import "google/ads/googleads/v17/enums/negative_geo_target_type.proto";
import "google/ads/googleads/v17/enums/optimization_goal_type.proto";
import "google/ads/googleads/v17/enums/payment_mode.proto";
import "google/ads/googleads/v17/enums/performance_max_upgrade_status.proto";
import "google/ads/googleads/v17/enums/positive_geo_target_type.proto";
import "google/ads/googleads/v17/enums/vanity_pharma_display_url_mode.proto";
import "google/ads/googleads/v17/enums/vanity_pharma_text.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Campaign resource.

// A campaign.
message Campaign {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Campaign"
    pattern: "customers/{customer_id}/campaigns/{campaign_id}"
  };

  // Information about a campaign being upgraded to Performance Max.
  message PerformanceMaxUpgrade {
    // Output only. Indicates which Performance Max campaign the campaign is
    // upgraded to.
    string performance_max_campaign = 1 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "googleads.googleapis.com/Campaign"
      }
    ];

    // Output only. Indicates legacy campaign upgraded to Performance Max.
    string pre_upgrade_campaign = 2 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "googleads.googleapis.com/Campaign"
      }
    ];

    // Output only. The upgrade status of a campaign requested to be upgraded to
    // Performance Max.
    google.ads.googleads.v17.enums.PerformanceMaxUpgradeStatusEnum
        .PerformanceMaxUpgradeStatus status = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The network settings for the campaign.
  message NetworkSettings {
    // Whether ads will be served with google.com search results.
    optional bool target_google_search = 5;

    // Whether ads will be served on partner sites in the Google Search Network
    // (requires `target_google_search` to also be `true`).
    optional bool target_search_network = 6;

    // Whether ads will be served on specified placements in the Google Display
    // Network. Placements are specified using the Placement criterion.
    optional bool target_content_network = 7;

    // Whether ads will be served on the Google Partner Network.
    // This is available only to some select Google partner accounts.
    optional bool target_partner_search_network = 8;

    // Whether ads will be served on YouTube.
    optional bool target_youtube = 9;

    // Whether ads will be served on the Google TV network.
    optional bool target_google_tv_network = 10;
  }

  // Campaign-level settings for hotel ads.
  message HotelSettingInfo {
    // Immutable. The linked Hotel Center account.
    optional int64 hotel_center_id = 2
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // The setting for controlling Dynamic Search Ads (DSA).
  message DynamicSearchAdsSetting {
    // Required. The Internet domain name that this setting represents, for
    // example, "google.com" or "www.google.com".
    string domain_name = 6 [(google.api.field_behavior) = REQUIRED];

    // Required. The language code specifying the language of the domain, for
    // example, "en".
    string language_code = 7 [(google.api.field_behavior) = REQUIRED];

    // Whether the campaign uses advertiser supplied URLs exclusively.
    optional bool use_supplied_urls_only = 8;

    // The list of page feeds associated with the campaign.
    repeated string feeds = 9 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/Feed"
    }];
  }

  // The setting for Shopping campaigns. Defines the universe of products that
  // can be advertised by the campaign, and how this campaign interacts with
  // other Shopping campaigns.
  message ShoppingSetting {
    // ID of the Merchant Center account.
    // This field is required for create operations. This field is immutable for
    // Shopping campaigns.
    optional int64 merchant_id = 5;

    // Feed label of products to include in the campaign.
    // Only one of feed_label or sales_country can be set.
    // If used instead of sales_country, the feed_label field accepts country
    // codes in the same format for example: 'XX'.
    // Otherwise can be any string used for feed label in Google Merchant
    // Center.
    string feed_label = 10;

    // Priority of the campaign. Campaigns with numerically higher priorities
    // take precedence over those with lower priorities.
    // This field is required for Shopping campaigns, with values between 0 and
    // 2, inclusive.
    // This field is optional for Smart Shopping campaigns, but must be equal to
    // 3 if set.
    optional int32 campaign_priority = 7;

    // Whether to include local products.
    optional bool enable_local = 8;

    // Immutable. Whether to target Vehicle Listing inventory. This field is
    // supported only in Smart Shopping Campaigns. For setting Vehicle Listing
    // inventory in Performance Max campaigns, use `listing_type` instead.
    bool use_vehicle_inventory = 9 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The ads account IDs of advertising partners cooperating within
    // the campaign.
    repeated int64 advertising_partner_ids = 11
        [(google.api.field_behavior) = IMMUTABLE];

    // Disable the optional product feed. This field is currently supported
    // only for Demand Gen campaigns. See
    // https://support.google.com/google-ads/answer/******** to learn more about
    // this feature.
    optional bool disable_product_feed = 12;
  }

  // Campaign-level settings for tracking information.
  message TrackingSetting {
    // Output only. The url used for dynamic tracking.
    optional string tracking_url = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Represents a collection of settings related to ads geotargeting.
  message GeoTargetTypeSetting {
    // The setting used for positive geotargeting in this particular campaign.
    google.ads.googleads.v17.enums.PositiveGeoTargetTypeEnum
        .PositiveGeoTargetType positive_geo_target_type = 1;

    // The setting used for negative geotargeting in this particular campaign.
    google.ads.googleads.v17.enums.NegativeGeoTargetTypeEnum
        .NegativeGeoTargetType negative_geo_target_type = 2;
  }

  // Campaign setting for local campaigns.
  message LocalCampaignSetting {
    // The location source type for this local campaign.
    google.ads.googleads.v17.enums.LocationSourceTypeEnum.LocationSourceType
        location_source_type = 1;
  }

  // Campaign-level settings for App Campaigns.
  message AppCampaignSetting {
    // Represents the goal which the bidding strategy of this app campaign
    // should optimize towards.
    google.ads.googleads.v17.enums.AppCampaignBiddingStrategyGoalTypeEnum
        .AppCampaignBiddingStrategyGoalType bidding_strategy_goal_type = 1;

    // Immutable. A string that uniquely identifies a mobile application.
    optional string app_id = 4 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The application store that distributes this specific app.
    google.ads.googleads.v17.enums.AppCampaignAppStoreEnum.AppCampaignAppStore
        app_store = 3 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Describes how unbranded pharma ads will be displayed.
  message VanityPharma {
    // The display mode for vanity pharma URLs.
    google.ads.googleads.v17.enums.VanityPharmaDisplayUrlModeEnum
        .VanityPharmaDisplayUrlMode vanity_pharma_display_url_mode = 1;

    // The text that will be displayed in display URL of the text ad when
    // website description is the selected display mode for vanity pharma URLs.
    google.ads.googleads.v17.enums.VanityPharmaTextEnum.VanityPharmaText
        vanity_pharma_text = 2;
  }

  // Selective optimization setting for this campaign, which includes a set of
  // conversion actions to optimize this campaign towards.
  // This feature only applies to app campaigns that use MULTI_CHANNEL as
  // AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as
  // AdvertisingChannelSubType.
  message SelectiveOptimization {
    // The selected set of conversion actions for optimizing this campaign.
    repeated string conversion_actions = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/ConversionAction"
    }];
  }

  // Optimization goal setting for this campaign, which includes a set of
  // optimization goal types.
  message OptimizationGoalSetting {
    // The list of optimization goal types.
    repeated google.ads.googleads.v17.enums.OptimizationGoalTypeEnum
        .OptimizationGoalType optimization_goal_types = 1;
  }

  // Settings for the audience targeting.
  message AudienceSetting {
    // Immutable. If true, this campaign uses an Audience resource for audience
    // targeting. If false, this campaign may use audience segment criteria
    // instead.
    optional bool use_audience_grouped = 1
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // Settings for LocalServicesCampaign subresource.
  message LocalServicesCampaignSettings {
    // Categorical level bids associated with MANUAL_CPA bidding strategy.
    repeated CategoryBid category_bids = 1;
  }

  // Category bids in LocalServicesReportingCampaignSettings.
  message CategoryBid {
    // Category for which the bid will be associated with. For example,
    // xcat:service_area_business_plumber.
    optional string category_id = 1;

    // Manual CPA bid for the category. Bid must be greater than the
    // reserve price associated for that category. Value is in micros
    // and in the advertiser's currency.
    optional int64 manual_cpa_bid_micros = 2;
  }

  // Settings for Travel campaign.
  message TravelCampaignSettings {
    // Immutable. The Travel account ID associated with the Travel campaign.
    optional int64 travel_account_id = 1
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // Settings for Demand Gen campaign.
  message DemandGenCampaignSettings {
    // Immutable. Specifies whether this campaign uses upgraded targeting
    // options. When this field is set to `true`, you can use location and
    // language targeting at the ad group level as opposed to the standard
    // campaign-level targeting. This field defaults to `false`, and can only be
    // set when creating a campaign.
    optional bool upgraded_targeting = 1
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // Settings for Video campaign.
  message VideoCampaignSettings {
    // For campaigns using responsive ad containers inventory controls determine
    // on which inventories the ads can be shown. This only applies for
    // campaigns with the bidding strategies TARGET_CPM and FIXED_CPM.
    message VideoAdInventoryControl {
      // Determine if VideoResponsiveAds can be used for in-stream video ads.
      optional bool allow_in_stream = 1;

      // Determine if VideoResponsiveAds can be used for in-feed video ads.
      optional bool allow_in_feed = 2;

      // Determine if VideoResponsiveAds can be used as shorts format.
      optional bool allow_shorts = 3;
    }

    // Inventory control for responsive ad containers in reach campaigns.
    VideoAdInventoryControl video_ad_inventory_control = 1;
  }

  // Asset automation setting contains pair of AssetAutomationType and the
  // asset automation opt-in/out status
  message AssetAutomationSetting {
    // The asset automation type advertiser would like to opt-in/out.
    optional google.ads.googleads.v17.enums.AssetAutomationTypeEnum
        .AssetAutomationType asset_automation_type = 1;

    // The opt-in/out status of asset automation type.
    optional google.ads.googleads.v17.enums.AssetAutomationStatusEnum
        .AssetAutomationStatus asset_automation_status = 2;
  }

  // Immutable. The resource name of the campaign.
  // Campaign resource names have the form:
  //
  // `customers/{customer_id}/campaigns/{campaign_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The ID of the campaign.
  optional int64 id = 59 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the campaign.
  //
  // This field is required and should not be empty when creating new
  // campaigns.
  //
  // It must not contain any null (code point 0x0), NL line feed
  // (code point 0xA) or carriage return (code point 0xD) characters.
  optional string name = 58;

  // Output only. The primary status of the campaign.
  //
  // Provides insight into why a campaign is not serving or not serving
  // optimally. Modification to the campaign and its related entities might take
  // a while to be reflected in this status.
  google.ads.googleads.v17.enums.CampaignPrimaryStatusEnum.CampaignPrimaryStatus
      primary_status = 81 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The primary status reasons of the campaign.
  //
  // Provides insight into why a campaign is not serving or not serving
  // optimally. These reasons are aggregated to determine an overall
  // CampaignPrimaryStatus.
  repeated google.ads.googleads.v17.enums.CampaignPrimaryStatusReasonEnum
      .CampaignPrimaryStatusReason primary_status_reasons = 82
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The status of the campaign.
  //
  // When a new campaign is added, the status defaults to ENABLED.
  google.ads.googleads.v17.enums.CampaignStatusEnum.CampaignStatus status = 5;

  // Output only. The ad serving status of the campaign.
  google.ads.googleads.v17.enums.CampaignServingStatusEnum.CampaignServingStatus
      serving_status = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The system status of the campaign's bidding strategy.
  google.ads.googleads.v17.enums.BiddingStrategySystemStatusEnum
      .BiddingStrategySystemStatus bidding_strategy_system_status = 78
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The ad serving optimization status of the campaign.
  google.ads.googleads.v17.enums.AdServingOptimizationStatusEnum
      .AdServingOptimizationStatus ad_serving_optimization_status = 8;

  // Immutable. The primary serving target for ads within the campaign.
  // The targeting options can be refined in `network_settings`.
  //
  // This field is required and should not be empty when creating new
  // campaigns.
  //
  // Can be set only when creating campaigns.
  // After the campaign is created, the field can not be changed.
  google.ads.googleads.v17.enums.AdvertisingChannelTypeEnum
      .AdvertisingChannelType advertising_channel_type = 9
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Optional refinement to `advertising_channel_type`.
  // Must be a valid sub-type of the parent channel type.
  //
  // Can be set only when creating campaigns.
  // After campaign is created, the field can not be changed.
  google.ads.googleads.v17.enums.AdvertisingChannelSubTypeEnum
      .AdvertisingChannelSubType advertising_channel_sub_type = 10
      [(google.api.field_behavior) = IMMUTABLE];

  // The URL template for constructing a tracking URL.
  optional string tracking_url_template = 60;

  // The list of mappings used to substitute custom parameter tags in a
  // `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
  repeated google.ads.googleads.v17.common.CustomParameter
      url_custom_parameters = 12;

  // The Local Services Campaign related settings.
  LocalServicesCampaignSettings local_services_campaign_settings = 75;

  // Settings for Travel campaign.
  TravelCampaignSettings travel_campaign_settings = 85;

  // Settings for Demand Gen campaign.
  DemandGenCampaignSettings demand_gen_campaign_settings = 91;

  // Settings for Video campaign.
  VideoCampaignSettings video_campaign_settings = 94;

  // Settings for Real-Time Bidding, a feature only available for campaigns
  // targeting the Ad Exchange network.
  google.ads.googleads.v17.common.RealTimeBiddingSetting
      real_time_bidding_setting = 39;

  // The network settings for the campaign.
  NetworkSettings network_settings = 14;

  // Immutable. The hotel setting for the campaign.
  HotelSettingInfo hotel_setting = 32 [(google.api.field_behavior) = IMMUTABLE];

  // The setting for controlling Dynamic Search Ads (DSA).
  DynamicSearchAdsSetting dynamic_search_ads_setting = 33;

  // The setting for controlling Shopping campaigns.
  ShoppingSetting shopping_setting = 36;

  // Setting for targeting related features.
  google.ads.googleads.v17.common.TargetingSetting targeting_setting = 43;

  // Immutable. Setting for audience related features.
  optional AudienceSetting audience_setting = 73
      [(google.api.field_behavior) = IMMUTABLE];

  // The setting for ads geotargeting.
  GeoTargetTypeSetting geo_target_type_setting = 47;

  // The setting for local campaign.
  LocalCampaignSetting local_campaign_setting = 50;

  // The setting related to App Campaign.
  AppCampaignSetting app_campaign_setting = 51;

  // Output only. The resource names of labels attached to this campaign.
  repeated string labels = 61 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignLabel"
    }
  ];

  // Output only. The type of campaign: normal, draft, or experiment.
  google.ads.googleads.v17.enums.CampaignExperimentTypeEnum
      .CampaignExperimentType experiment_type = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the base campaign of a draft or
  // experiment campaign. For base campaigns, this is equal to `resource_name`.
  //
  // This field is read-only.
  optional string base_campaign = 56 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // The budget of the campaign.
  optional string campaign_budget = 62 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CampaignBudget"
  }];

  // Output only. The type of bidding strategy.
  //
  // A bidding strategy can be created by setting either the bidding scheme to
  // create a standard bidding strategy or the `bidding_strategy` field to
  // create a portfolio bidding strategy.
  //
  // This field is read-only.
  google.ads.googleads.v17.enums.BiddingStrategyTypeEnum.BiddingStrategyType
      bidding_strategy_type = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource name of AccessibleBiddingStrategy, a read-only view
  // of the unrestricted attributes of the attached portfolio bidding strategy
  // identified by 'bidding_strategy'. Empty, if the campaign does not use a
  // portfolio strategy. Unrestricted strategy attributes are available to all
  // customers with whom the strategy is shared and are read from the
  // AccessibleBiddingStrategy resource. In contrast, restricted attributes are
  // only available to the owner customer of the strategy and their managers.
  // Restricted attributes can only be read from the BiddingStrategy resource.
  string accessible_bidding_strategy = 71 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AccessibleBiddingStrategy"
    }
  ];

  // The date when campaign started in serving customer's timezone in YYYY-MM-DD
  // format.
  optional string start_date = 63;

  // The campaign group this campaign belongs to.
  optional string campaign_group = 76 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CampaignGroup"
  }];

  // The last day of the campaign in serving customer's timezone in YYYY-MM-DD
  // format. On create, defaults to 2037-12-30, which means the campaign will
  // run indefinitely. To set an existing campaign to run indefinitely, set this
  // field to 2037-12-30.
  optional string end_date = 64;

  // Suffix used to append query parameters to landing pages that are served
  // with parallel tracking.
  optional string final_url_suffix = 65;

  // A list that limits how often each user will see this campaign's ads.
  repeated google.ads.googleads.v17.common.FrequencyCapEntry frequency_caps =
      40;

  // Brand Safety setting at the individual campaign level. Allows for selecting
  // an inventory type to show your ads on content that is the right fit for
  // your brand. See https://support.google.com/google-ads/answer/7515513.
  google.ads.googleads.v17.enums.BrandSafetySuitabilityEnum
      .BrandSafetySuitability video_brand_safety_suitability = 42;

  // Describes how unbranded pharma ads will be displayed.
  VanityPharma vanity_pharma = 44;

  // Selective optimization setting for this campaign, which includes a set of
  // conversion actions to optimize this campaign towards.
  // This feature only applies to app campaigns that use MULTI_CHANNEL as
  // AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as
  // AdvertisingChannelSubType.
  SelectiveOptimization selective_optimization = 45;

  // Optimization goal setting for this campaign, which includes a set of
  // optimization goal types.
  OptimizationGoalSetting optimization_goal_setting = 54;

  // Output only. Campaign-level settings for tracking information.
  TrackingSetting tracking_setting = 46
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Payment mode for the campaign.
  google.ads.googleads.v17.enums.PaymentModeEnum.PaymentMode payment_mode = 52;

  // Output only. Optimization score of the campaign.
  //
  // Optimization score is an estimate of how well a campaign is set to perform.
  // It ranges from 0% (0.0) to 100% (1.0), with 100% indicating that the
  // campaign is performing at full potential. This field is null for unscored
  // campaigns.
  //
  // See "About optimization score" at
  // https://support.google.com/google-ads/answer/9061546.
  //
  // This field is read-only.
  optional double optimization_score = 66
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The asset field types that should be excluded from this campaign. Asset
  // links with these field types will not be inherited by this campaign from
  // the upper level.
  repeated google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldType
      excluded_parent_asset_field_types = 69;

  // The asset set types that should be excluded from this campaign. Asset set
  // links with these types will not be inherited by this campaign from
  // the upper level.
  // Location group types (GMB_DYNAMIC_LOCATION_GROUP,
  // CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
  // LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
  // location group asset sets are not allowed to be linked to this campaign,
  // and all Location Extension (LE) and Affiliate Location Extensions (ALE)
  // will not be served under this campaign.
  // Only LOCATION_SYNC is currently supported.
  repeated google.ads.googleads.v17.enums.AssetSetTypeEnum.AssetSetType
      excluded_parent_asset_set_types = 80;

  // Represents opting out of URL expansion to more targeted URLs. If opted out
  // (true), only the final URLs in the asset group or URLs specified in the
  // advertiser's Google Merchant Center or business data feeds are targeted.
  // If opted in (false), the entire domain will be targeted. This field can
  // only be set for Performance Max campaigns, where the default value is
  // false.
  optional bool url_expansion_opt_out = 72;

  // Output only. Information about campaigns being upgraded to Performance Max.
  PerformanceMaxUpgrade performance_max_upgrade = 77
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The set of hotel properties for Performance Max for travel goals
  // campaigns.
  optional string hotel_property_asset_set = 83 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetSet"
    }
  ];

  // Immutable. Listing type of ads served for this campaign.
  // Field is restricted for usage with Performance Max campaigns.
  optional google.ads.googleads.v17.enums.ListingTypeEnum.ListingType
      listing_type = 86 [(google.api.field_behavior) = IMMUTABLE];

  // Contains the opt-in/out status of each AssetAutomationType.
  // See documentation of each asset automation type enum for default
  // opt in/out behavior.
  repeated AssetAutomationSetting asset_automation_settings = 88;

  // Keyword match type of Campaign. Set to BROAD to set broad matching for all
  // keywords in a campaign.
  google.ads.googleads.v17.enums.CampaignKeywordMatchTypeEnum
      .CampaignKeywordMatchType keyword_match_type = 90;

  // The bidding strategy for the campaign.
  //
  // Must be either portfolio (created through BiddingStrategy service) or
  // standard, that is embedded into the campaign.
  oneof campaign_bidding_strategy {
    // Portfolio bidding strategy used by campaign.
    string bidding_strategy = 67 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/BiddingStrategy"
    }];

    // Commission is an automatic bidding strategy in which the advertiser pays
    // a certain portion of the conversion value.
    google.ads.googleads.v17.common.Commission commission = 49;

    // Standard Manual CPA bidding strategy.
    // Manual bidding strategy that allows advertiser to set the bid per
    // advertiser-specified action. Supported only for Local Services campaigns.
    google.ads.googleads.v17.common.ManualCpa manual_cpa = 74;

    // Standard Manual CPC bidding strategy.
    // Manual click-based bidding where user pays per click.
    google.ads.googleads.v17.common.ManualCpc manual_cpc = 24;

    // Standard Manual CPM bidding strategy.
    // Manual impression-based bidding where user pays per thousand
    // impressions.
    google.ads.googleads.v17.common.ManualCpm manual_cpm = 25;

    // A bidding strategy that pays a configurable amount per video view.
    google.ads.googleads.v17.common.ManualCpv manual_cpv = 37;

    // Standard Maximize Conversions bidding strategy that automatically
    // maximizes number of conversions while spending your budget.
    google.ads.googleads.v17.common.MaximizeConversions maximize_conversions =
        30;

    // Standard Maximize Conversion Value bidding strategy that automatically
    // sets bids to maximize revenue while spending your budget.
    google.ads.googleads.v17.common.MaximizeConversionValue
        maximize_conversion_value = 31;

    // Standard Target CPA bidding strategy that automatically sets bids to
    // help get as many conversions as possible at the target
    // cost-per-acquisition (CPA) you set.
    google.ads.googleads.v17.common.TargetCpa target_cpa = 26;

    // Target Impression Share bidding strategy. An automated bidding strategy
    // that sets bids to achieve a chosen percentage of impressions.
    google.ads.googleads.v17.common.TargetImpressionShare
        target_impression_share = 48;

    // Standard Target ROAS bidding strategy that automatically maximizes
    // revenue while averaging a specific target return on ad spend (ROAS).
    google.ads.googleads.v17.common.TargetRoas target_roas = 29;

    // Standard Target Spend bidding strategy that automatically sets your bids
    // to help get as many clicks as possible within your budget.
    google.ads.googleads.v17.common.TargetSpend target_spend = 27;

    // Standard Percent Cpc bidding strategy where bids are a fraction of the
    // advertised price for some good or service.
    google.ads.googleads.v17.common.PercentCpc percent_cpc = 34;

    // A bidding strategy that automatically optimizes cost per thousand
    // impressions.
    google.ads.googleads.v17.common.TargetCpm target_cpm = 41;

    // A manual bidding strategy with a fixed CPM.
    google.ads.googleads.v17.common.FixedCpm fixed_cpm = 92;

    // An automated bidding strategy that sets bids to optimize performance
    // given the target CPV you set.
    google.ads.googleads.v17.common.TargetCpv target_cpv = 93;
  }
}
