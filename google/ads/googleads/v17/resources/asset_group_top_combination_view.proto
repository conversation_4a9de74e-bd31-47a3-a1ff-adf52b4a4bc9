// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/asset_usage.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupTopCombinationViewProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the asset group top combination view resource.

// A view on the usage of ad group ad asset combination.
message AssetGroupTopCombinationView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AssetGroupTopCombinationView"
    pattern: "customers/{customer_id}/assetGroupTopCombinationViews/{asset_group_id}~{asset_combination_category}"
  };

  // Output only. The resource name of the asset group top combination view.
  // AssetGroup Top Combination view resource names have the form:
  // `"customers/{customer_id}/assetGroupTopCombinationViews/{asset_group_id}~{asset_combination_category}"
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroupTopCombinationView"
    }
  ];

  // Output only. The top combinations of assets that served together.
  repeated AssetGroupAssetCombinationData asset_group_top_combinations = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Asset group asset combination data
message AssetGroupAssetCombinationData {
  // Output only. Served assets.
  repeated google.ads.googleads.v17.common.AssetUsage
      asset_combination_served_assets = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
