// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/criteria.proto";
import "google/ads/googleads/v17/enums/ad_strength.proto";
import "google/ads/googleads/v17/enums/app_bidding_goal.proto";
import "google/ads/googleads/v17/enums/keyword_match_type.proto";
import "google/ads/googleads/v17/enums/recommendation_type.proto";
import "google/ads/googleads/v17/enums/shopping_add_products_to_campaign_recommendation_enum.proto";
import "google/ads/googleads/v17/enums/target_cpa_opt_in_recommendation_goal.proto";
import "google/ads/googleads/v17/resources/ad.proto";
import "google/ads/googleads/v17/resources/asset.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "RecommendationProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Recommendation resource.

// A recommendation.
message Recommendation {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Recommendation"
    pattern: "customers/{customer_id}/recommendations/{recommendation_id}"
  };

  // The Merchant Center account details.
  message MerchantInfo {
    // Output only. The Merchant Center account ID.
    int64 id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The name of the Merchant Center account.
    string name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Whether the Merchant Center account is a Multi-Client
    // account (MCA).
    bool multi_client = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The impact of making the change as described in the recommendation.
  // Some types of recommendations may not have impact information.
  message RecommendationImpact {
    // Output only. Base metrics at the time the recommendation was generated.
    RecommendationMetrics base_metrics = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Estimated metrics if the recommendation is applied.
    RecommendationMetrics potential_metrics = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Weekly account performance metrics. For some recommendation types, these
  // are averaged over the past 90-day period and hence can be fractional.
  message RecommendationMetrics {
    // Output only. Number of ad impressions.
    optional double impressions = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Number of ad clicks.
    optional double clicks = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Cost (in micros) for advertising, in the local currency for
    // the account.
    optional int64 cost_micros = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Number of conversions.
    optional double conversions = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Sum of the conversion value of the conversions.
    optional double conversions_value = 11
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Number of video views for a video ad campaign.
    optional double video_views = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The budget recommendation for budget constrained campaigns.
  message CampaignBudgetRecommendation {
    // The impact estimates for a given budget amount.
    message CampaignBudgetRecommendationOption {
      // Output only. The budget amount for this option.
      optional int64 budget_amount_micros = 3
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The impact estimate if budget is changed to amount
      // specified in this option.
      RecommendationImpact impact = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Output only. The current budget amount in micros.
    optional int64 current_budget_amount_micros = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The recommended budget amount in micros.
    optional int64 recommended_budget_amount_micros = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The budget amounts and associated impact estimates for some
    // values of possible budget amounts.
    repeated CampaignBudgetRecommendationOption budget_options = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The keyword recommendation.
  message KeywordRecommendation {
    // Information about a search term as related to a keyword recommendation.
    message SearchTerm {
      // Output only. The text of the search term.
      string text = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Estimated number of historical weekly searches for this
      // search term.
      int64 estimated_weekly_search_count = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Output only. The recommended keyword.
    google.ads.googleads.v17.common.KeywordInfo keyword = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A list of search terms this keyword matches. The same search
    // term may be repeated for multiple keywords.
    repeated SearchTerm search_terms = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The recommended CPC (cost-per-click) bid.
    optional int64 recommended_cpc_bid_micros = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The text ad recommendation.
  message TextAdRecommendation {
    // Output only. Recommended ad.
    Ad ad = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Creation date of the recommended ad.
    // YYYY-MM-DD format, for example, 2018-04-17.
    optional string creation_date = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Date, if present, is the earliest when the recommendation
    // will be auto applied. YYYY-MM-DD format, for example, 2018-04-17.
    optional string auto_apply_date = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Target CPA opt-in recommendation.
  message TargetCpaOptInRecommendation {
    // The Target CPA opt-in option with impact estimate.
    message TargetCpaOptInRecommendationOption {
      // Output only. The goal achieved by this option.
      google.ads.googleads.v17.enums.TargetCpaOptInRecommendationGoalEnum
          .TargetCpaOptInRecommendationGoal goal = 1
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Average CPA target.
      optional int64 target_cpa_micros = 5
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The minimum campaign budget, in local currency for the
      // account, required to achieve the target CPA. Amount is specified in
      // micros, where one million is equivalent to one currency unit.
      optional int64 required_campaign_budget_amount_micros = 6
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The impact estimate if this option is selected.
      RecommendationImpact impact = 4
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Output only. The available goals and corresponding options for Target CPA
    // strategy.
    repeated TargetCpaOptInRecommendationOption options = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The recommended average CPA target. See required budget
    // amount and impact of using this recommendation in options list.
    optional int64 recommended_target_cpa_micros = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Maximize Conversions Opt-In recommendation.
  message MaximizeConversionsOptInRecommendation {
    // Output only. The recommended new budget amount.
    optional int64 recommended_budget_amount_micros = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Enhanced Cost-Per-Click Opt-In recommendation.
  message EnhancedCpcOptInRecommendation {}

  // The Search Partners Opt-In recommendation.
  message SearchPartnersOptInRecommendation {}

  // The Maximize Clicks opt-in recommendation.
  message MaximizeClicksOptInRecommendation {
    // Output only. The recommended new budget amount.
    // Only set if the current budget is too high.
    optional int64 recommended_budget_amount_micros = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Optimize Ad Rotation recommendation.
  message OptimizeAdRotationRecommendation {}

  // The callout asset recommendation.
  message CalloutAssetRecommendation {
    // Output only. New callout extension assets recommended at the campaign
    // level.
    repeated Asset recommended_campaign_callout_assets = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. New callout extension assets recommended at the customer
    // level.
    repeated Asset recommended_customer_callout_assets = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The sitelink asset recommendation.
  message SitelinkAssetRecommendation {
    // Output only. New sitelink assets recommended at the campaign level.
    repeated Asset recommended_campaign_sitelink_assets = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. New sitelink assets recommended at the customer level.
    repeated Asset recommended_customer_sitelink_assets = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The call asset recommendation.
  message CallAssetRecommendation {}

  // The keyword match type recommendation.
  message KeywordMatchTypeRecommendation {
    // Output only. The existing keyword where the match type should be more
    // broad.
    google.ads.googleads.v17.common.KeywordInfo keyword = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The recommended new match type.
    google.ads.googleads.v17.enums.KeywordMatchTypeEnum.KeywordMatchType
        recommended_match_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The move unused budget recommendation.
  message MoveUnusedBudgetRecommendation {
    // Output only. The excess budget's resource_name.
    optional string excess_campaign_budget = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The recommendation for the constrained budget to increase.
    CampaignBudgetRecommendation budget_recommendation = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Target ROAS opt-in recommendation.
  message TargetRoasOptInRecommendation {
    // Output only. The recommended target ROAS (revenue per unit of spend).
    // The value is between 0.01 and 1000.0, inclusive.
    optional double recommended_target_roas = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The minimum campaign budget, in local currency for the
    // account, required to achieve the target ROAS. Amount is specified in
    // micros, where one million is equivalent to one currency unit.
    optional int64 required_campaign_budget_amount_micros = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The add responsive search ad asset recommendation.
  message ResponsiveSearchAdAssetRecommendation {
    // Output only. The current ad to be updated.
    Ad current_ad = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The recommended assets. This is populated only with the new
    // headlines and/or descriptions, and is otherwise empty.
    Ad recommended_assets = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The responsive search ad improve ad strength recommendation.
  message ResponsiveSearchAdImproveAdStrengthRecommendation {
    // Output only. The current ad to be updated.
    Ad current_ad = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The updated ad.
    Ad recommended_ad = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The add responsive search ad recommendation.
  message ResponsiveSearchAdRecommendation {
    // Output only. Recommended ad.
    Ad ad = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The use broad match keyword recommendation.
  message UseBroadMatchKeywordRecommendation {
    // Output only. Sample of keywords to be expanded to Broad Match.
    repeated google.ads.googleads.v17.common.KeywordInfo keyword = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total number of keywords to be expanded to Broad Match in
    // the campaign.
    int64 suggested_keywords_count = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total number of keywords in the campaign.
    int64 campaign_keywords_count = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Whether the associated campaign uses a shared budget.
    bool campaign_uses_shared_budget = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The budget recommended to avoid becoming budget constrained
    // after applying the recommendation.
    int64 required_campaign_budget_amount_micros = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The upgrade a Smart Shopping campaign to a Performance Max campaign
  // recommendation.
  message UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation {
    // Output only. ID of Merchant Center account.
    int64 merchant_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Country whose products from merchant's inventory should be
    // included.
    string sales_country_code = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The raise target CPA bid too low recommendation.
  message RaiseTargetCpaBidTooLowRecommendation {
    // Output only. A number greater than 1.0 indicating the factor by which we
    // recommend the target CPA should be increased.
    optional double recommended_target_multiplier = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The current average target CPA of the campaign, in micros of
    // customer local currency.
    optional int64 average_target_cpa_micros = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Display Expansion opt-in recommendation.
  message DisplayExpansionOptInRecommendation {}

  // The Upgrade Local campaign to Performance Max campaign recommendation.
  message UpgradeLocalCampaignToPerformanceMaxRecommendation {}

  // The forecasting set target ROAS recommendation.
  message ForecastingSetTargetRoasRecommendation {
    // Output only. The recommended target ROAS (revenue per unit of spend).
    // The value is between 0.01 and 1000.0, inclusive.
    double recommended_target_roas = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The campaign budget.
    CampaignBudget campaign_budget = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The shopping recommendation to add an attribute to offers that are demoted
  // because it is missing.
  message ShoppingOfferAttributeRecommendation {
    // Output only. The details of the Merchant Center account.
    MerchantInfo merchant = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The campaign feed label.
    string feed_label = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of online, servable offers.
    int64 offers_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of online, servable offers that are demoted for
    // missing attributes. Visit the Merchant Center for more details.
    int64 demoted_offers_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The shopping recommendation to fix disapproved products in a Shopping
  // Campaign Inventory.
  message ShoppingFixDisapprovedProductsRecommendation {
    // Output only. The details of the Merchant Center account.
    MerchantInfo merchant = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The feed label for the campaign.
    string feed_label = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of products of the campaign.
    int64 products_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The numbers of products of the campaign that are
    // disapproved.
    int64 disapproved_products_count = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The shopping recommendation to create a catch-all campaign that targets all
  // offers.
  message ShoppingTargetAllOffersRecommendation {
    // Output only. The details of the Merchant Center account.
    MerchantInfo merchant = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of untargeted offers.
    int64 untargeted_offers_count = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The offer feed label.
    string feed_label = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The shopping recommendation to add products to a Shopping Campaign
  // Inventory.
  message ShoppingAddProductsToCampaignRecommendation {
    // Output only. The details of the Merchant Center account.
    MerchantInfo merchant = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The feed label for the campaign.
    string feed_label = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The reason why no products are attached to the campaign.
    google.ads.googleads.v17.enums
        .ShoppingAddProductsToCampaignRecommendationEnum.Reason reason = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The shopping recommendation to fix Merchant Center account suspension
  // issues.
  message ShoppingMerchantCenterAccountSuspensionRecommendation {
    // Output only. The details of the Merchant Center account.
    MerchantInfo merchant = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The feed label of the campaign for which the suspension
    // happened.
    string feed_label = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The shopping recommendation to migrate Regular Shopping Campaign targeted
  // offers to Performance Max campaigns.
  message
  ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation {
    // Output only. The details of the Merchant Center account.
    MerchantInfo merchant = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The feed label of the offers targeted by the campaigns
    // sharing this suggestion.
    string feed_label = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Information of a target adjustment recommendation.
  message TargetAdjustmentInfo {
    // Output only. The shared set resource name of the portfolio bidding
    // strategy where the target is defined. Only populated if the
    // recommendation is portfolio level.
    optional string shared_set = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The factor by which we recommend the target to be adjusted
    // by.
    double recommended_target_multiplier = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The current average target of the campaign or portfolio
    // targeted by this recommendation.
    int64 current_average_target_micros = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Recommendation to raise Target CPA.
  message RaiseTargetCpaRecommendation {
    // Output only. The relevant information describing the recommended target
    // adjustment.
    TargetAdjustmentInfo target_adjustment = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Represents the goal towards which the bidding strategy
    // should optimize. Only populated for App Campaigns.
    optional google.ads.googleads.v17.enums.AppBiddingGoalEnum.AppBiddingGoal
        app_bidding_goal = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Recommendation to lower Target ROAS.
  message LowerTargetRoasRecommendation {
    // Output only. The relevant information describing the recommended target
    // adjustment.
    TargetAdjustmentInfo target_adjustment = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Recommendation to enable dynamic image extensions on the account,
  // allowing Google to find the best images from ad landing pages and
  // complement text ads.
  message DynamicImageExtensionOptInRecommendation {}

  // A campaign budget shared amongst various budget recommendation types.
  message CampaignBudget {
    // Output only. Current budget amount.
    int64 current_amount_micros = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Recommended budget amount.
    int64 recommended_new_amount_micros = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The date when the new budget would start being used.
    // This field will be set for the following recommendation types:
    // FORECASTING_SET_TARGET_ROAS , FORECASTING_SET_TARGET_CPA
    // YYYY-MM-DD format, for example, 2018-04-17.
    string new_start_date = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Performance Max Opt In recommendation.
  message PerformanceMaxOptInRecommendation {}

  // Recommendation to improve the asset group strength of a Performance Max
  // campaign to an "Excellent" rating.
  message ImprovePerformanceMaxAdStrengthRecommendation {
    // Output only. The asset group resource name.
    string asset_group = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Dynamic Search Ads to Performance Max migration recommendation.
  message MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation {
    // Output only. A link to the Google Ads UI where the customer can manually
    // apply the recommendation.
    string apply_link = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The set target CPA recommendations.
  message ForecastingSetTargetCpaRecommendation {
    // Output only. The recommended target CPA.
    int64 recommended_target_cpa_micros = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The campaign budget.
    CampaignBudget campaign_budget = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Recommendation to opt into Maximize Conversion Value bidding strategy.
  message MaximizeConversionValueOptInRecommendation {}

  // Recommendation to deploy Google Tag on more pages.
  message ImproveGoogleTagCoverageRecommendation {}

  // Recommendation to turn on Final URL expansion for your Performance Max
  // campaigns.
  message PerformanceMaxFinalUrlOptInRecommendation {}

  // The recommendation to update a customer list that hasn't been updated in
  // the last 90 days. The customer receiving the recommendation is not
  // necessarily the owner account. The owner account should update the customer
  // list.
  message RefreshCustomerMatchListRecommendation {
    // Output only. The user list ID.
    int64 user_list_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The name of the list.
    string user_list_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Days since last refresh.
    int64 days_since_last_refresh = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The top spending account.
    repeated AccountInfo top_spending_account = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. User lists can be shared with other accounts by the owner.
    // targeting_accounts_count is the number of those accounts that can use it
    // for targeting.
    int64 targeting_accounts_count = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The owner account. This is the account that should update
    // the customer list.
    AccountInfo owner_account = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Wrapper for information about a Google Ads account.
  message AccountInfo {
    // Output only. The customer ID of the account.
    int64 customer_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The descriptive name of the account.
    string descriptive_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The Custom Audience Opt In recommendation.
  message CustomAudienceOptInRecommendation {
    // Output only. The list of keywords to use for custom audience creation.
    repeated google.ads.googleads.v17.common.KeywordInfo keywords = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The lead form asset recommendation.
  message LeadFormAssetRecommendation {}

  // The improve Demand Gen ad strength recommendation.
  message ImproveDemandGenAdStrengthRecommendation {
    // Output only. The resource name of the ad that can be improved.
    string ad = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The current ad strength.
    google.ads.googleads.v17.enums.AdStrengthEnum.AdStrength ad_strength = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A list of recommendations to improve the ad strength.
    repeated string demand_gen_asset_action_items = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. The resource name of the recommendation.
  //
  // `customers/{customer_id}/recommendations/{recommendation_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Recommendation"
    }
  ];

  // Output only. The type of recommendation.
  google.ads.googleads.v17.enums.RecommendationTypeEnum.RecommendationType
      type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The impact on account performance as a result of applying the
  // recommendation.
  RecommendationImpact impact = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The budget targeted by this recommendation. This will be set
  // only when the recommendation affects a single campaign budget.
  //
  // This field will be set for the following recommendation types:
  // CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET, MARGINAL_ROI_CAMPAIGN_BUDGET,
  // MOVE_UNUSED_BUDGET
  optional string campaign_budget = 24 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignBudget"
    }
  ];

  // Output only. The campaign targeted by this recommendation.
  //
  // This field will be set for the following recommendation types:
  // CALL_EXTENSION, CALLOUT_EXTENSION, ENHANCED_CPC_OPT_IN,
  // USE_BROAD_MATCH_KEYWORD, KEYWORD, KEYWORD_MATCH_TYPE,
  // UPGRADE_LOCAL_CAMPAIGN_TO_PERFORMANCE_MAX, MAXIMIZE_CLICKS_OPT_IN,
  // MAXIMIZE_CONVERSIONS_OPT_IN, OPTIMIZE_AD_ROTATION,
  // RESPONSIVE_SEARCH_AD,
  // RESPONSIVE_SEARCH_AD_ASSET,
  // SEARCH_PARTNERS_OPT_IN, DISPLAY_EXPANSION_OPT_IN, SITELINK_EXTENSION,
  // TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN, TEXT_AD,
  // UPGRADE_SMART_SHOPPING_CAMPAIGN_TO_PERFORMANCE_MAX,
  // RAISE_TARGET_CPA_BID_TOO_LOW, FORECASTING_SET_TARGET_ROAS,
  // SHOPPING_ADD_AGE_GROUP, SHOPPING_ADD_COLOR, SHOPPING_ADD_GENDER,
  // SHOPPING_ADD_SIZE, SHOPPING_ADD_GTIN, SHOPPING_ADD_MORE_IDENTIFIERS,
  // SHOPPING_ADD_PRODUCTS_TO_CAMPAIGN, SHOPPING_FIX_DISAPPROVED_PRODUCTS,
  // SHOPPING_MIGRATE_REGULAR_SHOPPING_CAMPAIGN_OFFERS_TO_PERFORMANCE_MAX,
  // DYNAMIC_IMAGE_EXTENSION_OPT_IN, RAISE_TARGET_CPA, LOWER_TARGET_ROAS,
  // FORECASTING_SET_TARGET_CPA,
  // SET_TARGET_CPA, SET_TARGET_ROAS, MAXIMIZE_CONVERSION_VALUE_OPT_IN,
  // IMPROVE_GOOGLE_TAG_COVERAGE, PERFORMANCE_MAX_FINAL_URL_OPT_IN
  optional string campaign = 25 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The ad group targeted by this recommendation. This will be set
  // only when the recommendation affects a single ad group.
  //
  // This field will be set for the following recommendation types:
  // KEYWORD, OPTIMIZE_AD_ROTATION, RESPONSIVE_SEARCH_AD,
  // RESPONSIVE_SEARCH_AD_ASSET, TEXT_AD
  optional string ad_group = 26 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. Whether the recommendation is dismissed or not.
  optional bool dismissed = 27 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The campaigns targeted by this recommendation.
  //
  // This field will be set for the following recommendation types:
  // CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET,
  // MARGINAL_ROI_CAMPAIGN_BUDGET and MOVE_UNUSED_BUDGET
  repeated string campaigns = 38 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // The details of recommendation.
  oneof recommendation {
    // Output only. The campaign budget recommendation.
    CampaignBudgetRecommendation campaign_budget_recommendation = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The forecasting campaign budget recommendation.
    CampaignBudgetRecommendation forecasting_campaign_budget_recommendation = 22
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The keyword recommendation.
    KeywordRecommendation keyword_recommendation = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Add expanded text ad recommendation.
    TextAdRecommendation text_ad_recommendation = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The TargetCPA opt-in recommendation.
    TargetCpaOptInRecommendation target_cpa_opt_in_recommendation = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The MaximizeConversions Opt-In recommendation.
    MaximizeConversionsOptInRecommendation
        maximize_conversions_opt_in_recommendation = 11
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Enhanced Cost-Per-Click Opt-In recommendation.
    EnhancedCpcOptInRecommendation enhanced_cpc_opt_in_recommendation = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Search Partners Opt-In recommendation.
    SearchPartnersOptInRecommendation search_partners_opt_in_recommendation = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The MaximizeClicks Opt-In recommendation.
    MaximizeClicksOptInRecommendation maximize_clicks_opt_in_recommendation = 15
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Optimize Ad Rotation recommendation.
    OptimizeAdRotationRecommendation optimize_ad_rotation_recommendation = 16
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The keyword match type recommendation.
    KeywordMatchTypeRecommendation keyword_match_type_recommendation = 20
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The move unused budget recommendation.
    MoveUnusedBudgetRecommendation move_unused_budget_recommendation = 21
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Target ROAS opt-in recommendation.
    TargetRoasOptInRecommendation target_roas_opt_in_recommendation = 23
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The add responsive search ad recommendation.
    ResponsiveSearchAdRecommendation responsive_search_ad_recommendation = 28
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The marginal ROI campaign budget recommendation.
    CampaignBudgetRecommendation marginal_roi_campaign_budget_recommendation =
        29 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The use broad match keyword recommendation.
    UseBroadMatchKeywordRecommendation use_broad_match_keyword_recommendation =
        30 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The responsive search ad asset recommendation.
    ResponsiveSearchAdAssetRecommendation
        responsive_search_ad_asset_recommendation = 31
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The upgrade a Smart Shopping campaign to a Performance Max
    // campaign recommendation.
    UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation
        upgrade_smart_shopping_campaign_to_performance_max_recommendation = 32
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The responsive search ad improve ad strength recommendation.
    ResponsiveSearchAdImproveAdStrengthRecommendation
        responsive_search_ad_improve_ad_strength_recommendation = 33
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Display Expansion opt-in recommendation.
    DisplayExpansionOptInRecommendation
        display_expansion_opt_in_recommendation = 34
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The upgrade a Local campaign to a Performance Max campaign
    // recommendation.
    UpgradeLocalCampaignToPerformanceMaxRecommendation
        upgrade_local_campaign_to_performance_max_recommendation = 35
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The raise target CPA bid too low recommendation.
    RaiseTargetCpaBidTooLowRecommendation
        raise_target_cpa_bid_too_low_recommendation = 36
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The forecasting set target ROAS recommendation.
    ForecastingSetTargetRoasRecommendation
        forecasting_set_target_roas_recommendation = 37
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The callout asset recommendation.
    CalloutAssetRecommendation callout_asset_recommendation = 39
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The sitelink asset recommendation.
    SitelinkAssetRecommendation sitelink_asset_recommendation = 40
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The call asset recommendation.
    CallAssetRecommendation call_asset_recommendation = 41
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping add age group recommendation.
    ShoppingOfferAttributeRecommendation shopping_add_age_group_recommendation =
        42 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping add color recommendation.
    ShoppingOfferAttributeRecommendation shopping_add_color_recommendation = 43
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping add gender recommendation.
    ShoppingOfferAttributeRecommendation shopping_add_gender_recommendation = 44
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping add GTIN recommendation.
    ShoppingOfferAttributeRecommendation shopping_add_gtin_recommendation = 45
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping add more identifiers recommendation.
    ShoppingOfferAttributeRecommendation
        shopping_add_more_identifiers_recommendation = 46
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping add size recommendation.
    ShoppingOfferAttributeRecommendation shopping_add_size_recommendation = 47
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping add products to campaign recommendation.
    ShoppingAddProductsToCampaignRecommendation
        shopping_add_products_to_campaign_recommendation = 48
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping fix disapproved products recommendation.
    ShoppingFixDisapprovedProductsRecommendation
        shopping_fix_disapproved_products_recommendation = 49
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping target all offers recommendation.
    ShoppingTargetAllOffersRecommendation
        shopping_target_all_offers_recommendation = 50
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping fix suspended Merchant Center account
    // recommendation.
    ShoppingMerchantCenterAccountSuspensionRecommendation
        shopping_fix_suspended_merchant_center_account_recommendation = 51
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping fix Merchant Center account suspension warning
    // recommendation.
    ShoppingMerchantCenterAccountSuspensionRecommendation
        shopping_fix_merchant_center_account_suspension_warning_recommendation =
            52 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The shopping migrate Regular Shopping Campaign offers to
    // Performance Max recommendation.
    ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation
        shopping_migrate_regular_shopping_campaign_offers_to_performance_max_recommendation =
            53 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Recommendation to enable dynamic image extensions on the
    // account, allowing Google to find the best images from ad landing pages
    // and complement text ads.
    DynamicImageExtensionOptInRecommendation
        dynamic_image_extension_opt_in_recommendation = 54
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Recommendation to raise Target CPA.
    RaiseTargetCpaRecommendation raise_target_cpa_recommendation = 55
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Recommendation to lower Target ROAS.
    LowerTargetRoasRecommendation lower_target_roas_recommendation = 56
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Performance Max Opt In recommendation.
    PerformanceMaxOptInRecommendation performance_max_opt_in_recommendation = 57
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The improve Performance Max ad strength recommendation.
    ImprovePerformanceMaxAdStrengthRecommendation
        improve_performance_max_ad_strength_recommendation = 58
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Dynamic Search Ads to Performance Max migration
    // recommendation.
    MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation
        migrate_dynamic_search_ads_campaign_to_performance_max_recommendation =
            59 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The forecasting set target CPA recommendation.
    ForecastingSetTargetCpaRecommendation
        forecasting_set_target_cpa_recommendation = 60
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The set target CPA recommendation.
    ForecastingSetTargetCpaRecommendation set_target_cpa_recommendation = 61
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The set target ROAS recommendation.
    ForecastingSetTargetRoasRecommendation set_target_roas_recommendation = 62
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Maximize Conversion Value opt-in recommendation.
    MaximizeConversionValueOptInRecommendation
        maximize_conversion_value_opt_in_recommendation = 63
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Recommendation to deploy Google Tag on more pages.
    ImproveGoogleTagCoverageRecommendation
        improve_google_tag_coverage_recommendation = 64
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Recommendation to turn on Final URL expansion for your
    // Performance Max campaigns.
    PerformanceMaxFinalUrlOptInRecommendation
        performance_max_final_url_opt_in_recommendation = 65
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The refresh customer list recommendation.
    RefreshCustomerMatchListRecommendation
        refresh_customer_match_list_recommendation = 66
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The custom audience opt in recommendation.
    CustomAudienceOptInRecommendation custom_audience_opt_in_recommendation = 67
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The lead form asset recommendation.
    LeadFormAssetRecommendation lead_form_asset_recommendation = 68
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The improve Demand Gen ad strength recommendation.
    ImproveDemandGenAdStrengthRecommendation
        improve_demand_gen_ad_strength_recommendation = 69
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
