// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/asset_set_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetSetTypeViewProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the AssetSetTypeView resource.

// An asset set type view.
// This view reports non-overcounted metrics for each asset set type. Child
// asset set types are not included in this report. Their stats are aggregated
// under the parent asset set type.
message AssetSetTypeView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AssetSetTypeView"
    pattern: "customers/{customer_id}/assetSetTypeViews/{asset_set_type}"
  };

  // Output only. The resource name of the asset set type view.
  // Asset set type view resource names have the form:
  //
  // `customers/{customer_id}/assetSetTypeViews/{asset_set_type}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetSetTypeView"
    }
  ];

  // Output only. The asset set type of the asset set type view.
  google.ads.googleads.v17.enums.AssetSetTypeEnum.AssetSetType asset_set_type =
      3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
