// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/custom_interest_member_type.proto";
import "google/ads/googleads/v17/enums/custom_interest_status.proto";
import "google/ads/googleads/v17/enums/custom_interest_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomInterestProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Custom Interest resource.

// A custom interest. This is a list of users by interest.
message CustomInterest {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomInterest"
    pattern: "customers/{customer_id}/customInterests/{custom_interest_id}"
  };

  // Immutable. The resource name of the custom interest.
  // Custom interest resource names have the form:
  //
  // `customers/{customer_id}/customInterests/{custom_interest_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomInterest"
    }
  ];

  // Output only. Id of the custom interest.
  optional int64 id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Status of this custom interest. Indicates whether the custom interest is
  // enabled or removed.
  google.ads.googleads.v17.enums.CustomInterestStatusEnum.CustomInterestStatus
      status = 3;

  // Name of the custom interest. It should be unique across the same custom
  // affinity audience.
  // This field is required for create operations.
  optional string name = 9;

  // Type of the custom interest, CUSTOM_AFFINITY or CUSTOM_INTENT.
  // By default the type is set to CUSTOM_AFFINITY.
  google.ads.googleads.v17.enums.CustomInterestTypeEnum.CustomInterestType
      type = 5;

  // Description of this custom interest audience.
  optional string description = 10;

  // List of custom interest members that this custom interest is composed of.
  // Members can be added during CustomInterest creation. If members are
  // presented in UPDATE operation, existing members will be overridden.
  repeated CustomInterestMember members = 7;
}

// A member of custom interest audience. A member can be a keyword or url.
// It is immutable, that is, it can only be created or removed but not changed.
message CustomInterestMember {
  // The type of custom interest member, KEYWORD or URL.
  google.ads.googleads.v17.enums.CustomInterestMemberTypeEnum
      .CustomInterestMemberType member_type = 1;

  // Keyword text when member_type is KEYWORD or URL string when
  // member_type is URL.
  optional string parameter = 3;
}
