// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/offline_conversion_diagnostic_status_enum.proto";
import "google/ads/googleads/v17/enums/offline_event_upload_client_enum.proto";
import "google/ads/googleads/v17/errors/collection_size_error.proto";
import "google/ads/googleads/v17/errors/conversion_adjustment_upload_error.proto";
import "google/ads/googleads/v17/errors/conversion_upload_error.proto";
import "google/ads/googleads/v17/errors/date_error.proto";
import "google/ads/googleads/v17/errors/distinct_error.proto";
import "google/ads/googleads/v17/errors/field_error.proto";
import "google/ads/googleads/v17/errors/mutate_error.proto";
import "google/ads/googleads/v17/errors/not_allowlisted_error.proto";
import "google/ads/googleads/v17/errors/string_format_error.proto";
import "google/ads/googleads/v17/errors/string_length_error.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "OfflineConversionUploadClientSummaryProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Offline conversion upload summary at customer level.
message OfflineConversionUploadClientSummary {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/OfflineConversionUploadClientSummary"
    pattern: "customers/{customer_id}/offlineConversionUploadClientSummaries/{client}"
  };

  // Output only. The resource name of the offline conversion upload summary at
  // customer level. Offline conversion upload client summary resource names
  // have the form:
  //
  // `customers/{customer_id}/offlineConversionUploadClientSummaries/{client}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/OfflineConversionUploadClientSummary"
    }
  ];

  // Output only. Client type of the upload event.
  google.ads.googleads.v17.enums.OfflineEventUploadClientEnum
      .OfflineEventUploadClient client = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Overall status for offline conversion client summary. Status
  // is generated from most recent calendar day with upload stats.
  google.ads.googleads.v17.enums.OfflineConversionDiagnosticStatusEnum
      .OfflineConversionDiagnosticStatus status = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total count of uploaded events.
  int64 total_event_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total count of successful uploaded events.
  int64 successful_event_count = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Successful rate.
  double success_rate = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total count of pending uploaded events.
  int64 pending_event_count = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ratio of total pending events to total events.
  double pending_rate = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Date for the latest upload batch. The format is "yyyy-mm-dd
  // hh:mm:ss", and it's in the time zone of the Google Ads account.
  string last_upload_date_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Summary of history stats by last N days.
  repeated OfflineConversionSummary daily_summaries = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Summary of history stats by last N jobs.
  repeated OfflineConversionSummary job_summaries = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Details for each error code. Alerts are generated from most
  // recent calendar day with upload stats.
  repeated OfflineConversionAlert alerts = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Historical upload summary, grouped by upload date or job.
message OfflineConversionSummary {
  // Output only. Total count of successful event.
  int64 successful_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total count of failed event.
  int64 failed_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total count of pending uploaded event.
  int64 pending_count = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Dimension key for summary.
  oneof dimension_key {
    // Output only. Dimension key for last N jobs.
    int64 job_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Dimension key for last N days.
    string upload_date = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// Alert for offline conversion client summary.
message OfflineConversionAlert {
  // Output only. Error for offline conversion client alert.
  OfflineConversionError error = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Percentage of the error, the range of this field should be
  // [0, 1.0].
  double error_percentage = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Possible errors for offline conversion client summary.
message OfflineConversionError {
  // Error with description.
  oneof error_code {
    // Output only. Collection size error.
    google.ads.googleads.v17.errors.CollectionSizeErrorEnum.CollectionSizeError
        collection_size_error = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Conversion adjustment upload error.
    google.ads.googleads.v17.errors.ConversionAdjustmentUploadErrorEnum
        .ConversionAdjustmentUploadError conversion_adjustment_upload_error = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Conversion upload error.
    google.ads.googleads.v17.errors.ConversionUploadErrorEnum
        .ConversionUploadError conversion_upload_error = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Date error.
    google.ads.googleads.v17.errors.DateErrorEnum.DateError date_error = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Distinct error.
    google.ads.googleads.v17.errors.DistinctErrorEnum.DistinctError
        distinct_error = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Field error.
    google.ads.googleads.v17.errors.FieldErrorEnum.FieldError field_error = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Mutate error.
    google.ads.googleads.v17.errors.MutateErrorEnum.MutateError mutate_error = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Not allowlisted error.
    google.ads.googleads.v17.errors.NotAllowlistedErrorEnum.NotAllowlistedError
        not_allowlisted_error = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. String format error.
    google.ads.googleads.v17.errors.StringFormatErrorEnum.StringFormatError
        string_format_error = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. String length error.
    google.ads.googleads.v17.errors.StringLengthErrorEnum.StringLengthError
        string_length_error = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
