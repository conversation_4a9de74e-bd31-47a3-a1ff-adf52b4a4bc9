// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/asset_usage.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdAssetCombinationViewProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the asset combination view resource.

// A view on the usage of ad group ad asset combination.
// Now we only support AdGroupAdAssetCombinationView for Responsive Search Ads,
// with more ad types planned for the future.
message AdGroupAdAssetCombinationView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupAdAssetCombinationView"
    pattern: "customers/{customer_id}/adGroupAdAssetCombinationViews/{ad_group_id}~{ad_id}~{asset_combination_id_low}~{asset_combination_id_high}"
  };

  // Output only. The resource name of the ad group ad asset combination view.
  // The combination ID is 128 bits long, where the upper 64 bits are stored in
  // asset_combination_id_high, and the lower 64 bits are stored in
  // asset_combination_id_low.
  // AdGroupAd Asset Combination view resource names have the form:
  // `customers/{customer_id}/adGroupAdAssetCombinationViews/{AdGroupAd.ad_group_id}~{AdGroupAd.ad.ad_id}~{AssetCombination.asset_combination_id_low}~{AssetCombination.asset_combination_id_high}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAdAssetCombinationView"
    }
  ];

  // Output only. Served assets.
  repeated google.ads.googleads.v17.common.AssetUsage served_assets = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status between the asset combination and the latest
  // version of the ad. If true, the asset combination is linked to the latest
  // version of the ad. If false, it means the link once existed but has been
  // removed and is no longer present in the latest version of the ad.
  optional bool enabled = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
