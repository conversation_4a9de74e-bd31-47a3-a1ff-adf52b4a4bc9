// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/dates.proto";
import "google/ads/googleads/v17/enums/invoice_type.proto";
import "google/ads/googleads/v17/enums/month_of_year.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "InvoiceProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Invoice resource.

// An invoice. All invoice information is snapshotted to match the PDF invoice.
// For invoices older than the launch of InvoiceService, the snapshotted
// information may not match the PDF invoice.
message Invoice {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Invoice"
    pattern: "customers/{customer_id}/invoices/{invoice_id}"
  };

  // Represents a summarized view at account level.
  message AccountSummary {
    // Output only. The account associated with the account summary.
    optional string customer = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Pretax billing correction subtotal amount, in micros.
    optional int64 billing_correction_subtotal_amount_micros = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Tax on billing correction, in micros.
    optional int64 billing_correction_tax_amount_micros = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total billing correction amount, in micros.
    optional int64 billing_correction_total_amount_micros = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Pretax coupon adjustment subtotal amount, in micros.
    optional int64 coupon_adjustment_subtotal_amount_micros = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Tax on coupon adjustment, in micros.
    optional int64 coupon_adjustment_tax_amount_micros = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total coupon adjustment amount, in micros.
    optional int64 coupon_adjustment_total_amount_micros = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Pretax excess credit adjustment subtotal amount, in micros.
    optional int64 excess_credit_adjustment_subtotal_amount_micros = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Tax on excess credit adjustment, in micros.
    optional int64 excess_credit_adjustment_tax_amount_micros = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total excess credit adjustment amount, in micros.
    optional int64 excess_credit_adjustment_total_amount_micros = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Pretax regulatory costs subtotal amount, in micros.
    optional int64 regulatory_costs_subtotal_amount_micros = 11
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Tax on regulatory costs, in micros.
    optional int64 regulatory_costs_tax_amount_micros = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total regulatory costs amount, in micros.
    optional int64 regulatory_costs_total_amount_micros = 13
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Pretax export charge subtotal amount, in micros.
    optional int64 export_charge_subtotal_amount_micros = 17
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Tax on export charge, in micros.
    optional int64 export_charge_tax_amount_micros = 18
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total export charge amount, in micros.
    optional int64 export_charge_total_amount_micros = 19
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total pretax subtotal amount attributable to the account
    // during the service period, in micros.
    optional int64 subtotal_amount_micros = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total tax amount attributable to the account during the
    // service period, in micros.
    optional int64 tax_amount_micros = 15
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Total amount attributable to the account during the service
    // period, in micros. This equals the sum of the subtotal_amount_micros and
    // tax_amount_micros.
    optional int64 total_amount_micros = 16
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Represents a summarized account budget billable cost.
  message AccountBudgetSummary {
    // Output only. The resource name of the customer associated with this
    // account budget. This contains the customer ID, which appears on the
    // invoice PDF as "Account ID". Customer resource names have the form:
    //
    // `customers/{customer_id}`
    optional string customer = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The descriptive name of the account budget's customer. It
    // appears on the invoice PDF as "Account".
    optional string customer_descriptive_name = 11
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The resource name of the account budget associated with this
    // summarized billable cost. AccountBudget resource names have the form:
    //
    // `customers/{customer_id}/accountBudgets/{account_budget_id}`
    optional string account_budget = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The name of the account budget. It appears on the invoice
    // PDF as "Account budget".
    optional string account_budget_name = 13
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The purchase order number of the account budget. It appears
    // on the invoice PDF as "Purchase order".
    optional string purchase_order_number = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The pretax subtotal amount attributable to this budget
    // during the service period, in micros.
    optional int64 subtotal_amount_micros = 15
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The tax amount attributable to this budget during the
    // service period, in micros.
    optional int64 tax_amount_micros = 16
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The total amount attributable to this budget during the
    // service period, in micros. This equals the sum of the account budget
    // subtotal amount and the account budget tax amount.
    optional int64 total_amount_micros = 17
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The billable activity date range of the account budget,
    // within the service date range of this invoice. The end date is inclusive.
    // This can be different from the account budget's start and end time.
    google.ads.googleads.v17.common.DateRange billable_activity_date_range = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The pretax served amount attributable to this budget during
    // the service period, in micros. This is only useful to reconcile invoice
    // and delivery data.
    optional int64 served_amount_micros = 18
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The pretax billed amount attributable to this budget during
    // the service period, in micros. This does not account for any adjustments.
    optional int64 billed_amount_micros = 19
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The pretax overdelivery amount attributable to this budget
    // during the service period, in micros (negative value).
    optional int64 overdelivery_amount_micros = 20
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The pretax invalid activity amount attributable to this
    // budget in previous months, in micros (negative value).
    optional int64 invalid_activity_amount_micros = 21
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The list of summarized invalid activity credits with
    // original linkages.
    repeated InvalidActivitySummary invalid_activity_summaries = 22
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Details about the invalid activity for the invoice that contain
  // additional details about invoice against which corrections are made.
  message InvalidActivitySummary {
    // Output only. Original month of service related to this invalid activity
    // credit.
    optional google.ads.googleads.v17.enums.MonthOfYearEnum.MonthOfYear
        original_month_of_service = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Original year of service related to this invalid activity
    // credit.
    optional string original_year_of_service = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Original invoice number related to this invalid activity
    // credit.
    optional string original_invoice_id = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Original account budget name related to this invalid
    // activity credit.
    optional string original_account_budget_name = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Original purchase order number related to this invalid
    // activity credit.
    optional string original_purchase_order_number = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Invalid activity amount in micros.
    optional int64 amount_micros = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The resource name of the invoice. Multiple customers can share
  // a given invoice, so multiple resource names may point to the same invoice.
  // Invoice resource names have the form:
  //
  // `customers/{customer_id}/invoices/{invoice_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Invoice"
    }
  ];

  // Output only. The ID of the invoice. It appears on the invoice PDF as
  // "Invoice number".
  optional string id = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of invoice.
  google.ads.googleads.v17.enums.InvoiceTypeEnum.InvoiceType type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of this invoice's billing setup.
  //
  // `customers/{customer_id}/billingSetups/{billing_setup_id}`
  optional string billing_setup = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A 16 digit ID used to identify the payments account associated
  // with the billing setup, for example, "1234-5678-9012-3456". It appears on
  // the invoice PDF as "Billing Account Number".
  optional string payments_account_id = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A 12 digit ID used to identify the payments profile associated
  // with the billing setup, for example, "1234-5678-9012". It appears on the
  // invoice PDF as "Billing ID".
  optional string payments_profile_id = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The issue date in yyyy-mm-dd format. It appears on the invoice
  // PDF as either "Issue date" or "Invoice date".
  optional string issue_date = 29 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The due date in yyyy-mm-dd format.
  optional string due_date = 30 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The service period date range of this invoice. The end date is
  // inclusive.
  google.ads.googleads.v17.common.DateRange service_date_range = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The currency code. All costs are returned in this currency. A
  // subset of the currency codes derived from the ISO 4217 standard is
  // supported.
  optional string currency_code = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The pretax subtotal amount of invoice level adjustments, in
  // micros.
  int64 adjustments_subtotal_amount_micros = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The sum of taxes on the invoice level adjustments, in micros.
  int64 adjustments_tax_amount_micros = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total amount of invoice level adjustments, in micros.
  int64 adjustments_total_amount_micros = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The pretax subtotal amount of invoice level regulatory costs,
  // in micros.
  int64 regulatory_costs_subtotal_amount_micros = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The sum of taxes on the invoice level regulatory costs, in
  // micros.
  int64 regulatory_costs_tax_amount_micros = 23
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total amount of invoice level regulatory costs, in micros.
  int64 regulatory_costs_total_amount_micros = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The pretax subtotal amount of invoice level export charges, in
  // micros.
  optional int64 export_charge_subtotal_amount_micros = 40
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The sum of taxes on the invoice level export charges, in
  // micros.
  optional int64 export_charge_tax_amount_micros = 41
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total amount of invoice level export charges, in micros.
  optional int64 export_charge_total_amount_micros = 42
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The pretax subtotal amount, in micros. This is equal to the
  // sum of the AccountBudgetSummary subtotal amounts and
  // Invoice.adjustments_subtotal_amount_micros.
  optional int64 subtotal_amount_micros = 33
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The sum of all taxes on the invoice, in micros. This equals
  // the sum of the AccountBudgetSummary tax amounts, plus taxes not associated
  // with a specific account budget.
  optional int64 tax_amount_micros = 34
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total amount, in micros. This equals the sum of
  // Invoice.subtotal_amount_micros, Invoice.tax_amount_micros,
  // Invoice.regulatory_costs_subtotal_amount_micros, and
  // Invoice.export_charge_subtotal_amount_micros (which is separated into a
  // separate line item starting with V14.1).
  optional int64 total_amount_micros = 35
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the original invoice corrected, wrote
  // off, or canceled by this invoice, if applicable. If `corrected_invoice` is
  // set, `replaced_invoices` will not be set. Invoice resource names have the
  // form:
  //
  // `customers/{customer_id}/invoices/{invoice_id}`
  optional string corrected_invoice = 36
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the original invoice(s) being rebilled or
  // replaced by this invoice, if applicable. There might be multiple replaced
  // invoices due to invoice consolidation. The replaced invoices may not belong
  // to the same payments account. If `replaced_invoices` is set,
  // `corrected_invoice` will not be set. Invoice resource names have the form:
  //
  // `customers/{customer_id}/invoices/{invoice_id}`
  repeated string replaced_invoices = 37
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL to a PDF copy of the invoice. Users need to pass in
  // their OAuth token to request the PDF with this URL.
  optional string pdf_url = 38 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of summarized account budget information associated
  // with this invoice.
  repeated AccountBudgetSummary account_budget_summaries = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of summarized account information associated with
  // this invoice.
  repeated AccountSummary account_summaries = 39
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
