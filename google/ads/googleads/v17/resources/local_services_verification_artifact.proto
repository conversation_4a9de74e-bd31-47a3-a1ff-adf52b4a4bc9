// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/local_services.proto";
import "google/ads/googleads/v17/enums/local_services_business_registration_check_rejection_reason.proto";
import "google/ads/googleads/v17/enums/local_services_business_registration_type.proto";
import "google/ads/googleads/v17/enums/local_services_insurance_rejection_reason.proto";
import "google/ads/googleads/v17/enums/local_services_license_rejection_reason.proto";
import "google/ads/googleads/v17/enums/local_services_verification_artifact_status.proto";
import "google/ads/googleads/v17/enums/local_services_verification_artifact_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesVerificationArtifactProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// A local services verification resource.
message LocalServicesVerificationArtifact {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/LocalServicesVerificationArtifact"
    pattern: "customers/{customer_id}/localServicesVerificationArtifacts/{gls_verification_artifact_id}"
  };

  // Immutable. The resource name of the Local Services Verification.
  // Local Services Verification resource names have the form:
  //
  // `customers/{customer_id}/localServicesVerificationArtifacts/{verification_artifact_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LocalServicesVerificationArtifact"
    }
  ];

  // Output only. The ID of the verification artifact.
  optional int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this verification artifact was created.
  // The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's timezone.
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  string creation_date_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the verification artifact.
  google.ads.googleads.v17.enums.LocalServicesVerificationArtifactStatusEnum
      .LocalServicesVerificationArtifactStatus status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the verification artifact.
  google.ads.googleads.v17.enums.LocalServicesVerificationArtifactTypeEnum
      .LocalServicesVerificationArtifactType artifact_type = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The types of verification info.
  oneof artifact_data {
    // Output only. A background check verification artifact.
    BackgroundCheckVerificationArtifact background_check_verification_artifact =
        6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. An insurance verification artifact.
    InsuranceVerificationArtifact insurance_verification_artifact = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A license verification artifact.
    LicenseVerificationArtifact license_verification_artifact = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A business registration check verification artifact.
    BusinessRegistrationCheckVerificationArtifact
        business_registration_check_verification_artifact = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// A proto holding information specific to local services background check.
message BackgroundCheckVerificationArtifact {
  // Output only. URL to access background case.
  optional string case_url = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this background check case result was
  // adjudicated. The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads
  // account's timezone. Examples: "2018-03-05 09:15:00" or "2018-02-01
  // 14:34:30"
  optional string final_adjudication_date_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A proto holding information specific to a local services insurance.
message InsuranceVerificationArtifact {
  // Output only. Insurance amount. This is measured in "micros" of the currency
  // mentioned in the insurance document.
  optional int64 amount_micros = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Insurance document's rejection reason.
  optional
      google.ads.googleads.v17.enums.LocalServicesInsuranceRejectionReasonEnum
          .LocalServicesInsuranceRejectionReason rejection_reason = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The readonly field containing the information for an uploaded
  // insurance document.
  optional google.ads.googleads.v17.common.LocalServicesDocumentReadOnly
      insurance_document_readonly = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this insurance expires.
  // The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's timezone.
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  optional string expiration_date_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A proto holding information specific to a local services license.
message LicenseVerificationArtifact {
  // Output only. License type / name.
  optional string license_type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. License number.
  optional string license_number = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. First name of the licensee.
  optional string licensee_first_name = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last name of the licensee.
  optional string licensee_last_name = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. License rejection reason.
  optional
      google.ads.googleads.v17.enums.LocalServicesLicenseRejectionReasonEnum
          .LocalServicesLicenseRejectionReason rejection_reason = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The readonly field containing the information for an uploaded
  // license document.
  optional google.ads.googleads.v17.common.LocalServicesDocumentReadOnly
      license_document_readonly = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this license expires.
  // The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's timezone.
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  optional string expiration_date_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A proto holding information specific to a local services business
// registration check.
message BusinessRegistrationCheckVerificationArtifact {
  // Output only. The type of business registration check (number, document).
  optional
      google.ads.googleads.v17.enums.LocalServicesBusinessRegistrationTypeEnum
          .LocalServicesBusinessRegistrationType registration_type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The id of the check, such as vat_tax_id, representing "VAT Tax
  // ID" requirement.
  optional string check_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Registration document rejection reason.
  optional google.ads.googleads.v17.enums
      .LocalServicesBusinessRegistrationCheckRejectionReasonEnum
      .LocalServicesBusinessRegistrationCheckRejectionReason rejection_reason =
      5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Registration information for the business registration. It will be one
  // of the following forms based on the requirement from different countries.
  oneof business_registration {
    // Output only. Message storing government issued number for the business.
    BusinessRegistrationNumber registration_number = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Message storing document info for the business.
    BusinessRegistrationDocument registration_document = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// A proto holding information specific to a local services business
// registration number.
message BusinessRegistrationNumber {
  // Output only. Government-issued number for the business.
  optional string number = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A proto holding information specific to a local services business
// registration document.
message BusinessRegistrationDocument {
  // Output only. The readonly field containing the information for an uploaded
  // business registration document.
  optional google.ads.googleads.v17.common.LocalServicesDocumentReadOnly
      document_readonly = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}
