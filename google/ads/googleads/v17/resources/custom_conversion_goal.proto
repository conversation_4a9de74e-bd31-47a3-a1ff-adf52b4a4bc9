// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/custom_conversion_goal_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomConversionGoalProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Custom conversion goal that can make arbitrary conversion actions biddable.
message CustomConversionGoal {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomConversionGoal"
    pattern: "customers/{customer_id}/customConversionGoals/{goal_id}"
  };

  // Immutable. The resource name of the custom conversion goal.
  // Custom conversion goal resource names have the form:
  //
  // `customers/{customer_id}/customConversionGoals/{goal_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomConversionGoal"
    }
  ];

  // Immutable. The ID for this custom conversion goal.
  int64 id = 2 [(google.api.field_behavior) = IMMUTABLE];

  // The name for this custom conversion goal.
  string name = 3;

  // Conversion actions that the custom conversion goal makes biddable.
  repeated string conversion_actions = 4 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ConversionAction"
  }];

  // The status of the custom conversion goal.
  google.ads.googleads.v17.enums.CustomConversionGoalStatusEnum
      .CustomConversionGoalStatus status = 5;
}
