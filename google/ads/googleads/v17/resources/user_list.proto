// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/user_lists.proto";
import "google/ads/googleads/v17/enums/access_reason.proto";
import "google/ads/googleads/v17/enums/user_list_access_status.proto";
import "google/ads/googleads/v17/enums/user_list_closing_reason.proto";
import "google/ads/googleads/v17/enums/user_list_membership_status.proto";
import "google/ads/googleads/v17/enums/user_list_size_range.proto";
import "google/ads/googleads/v17/enums/user_list_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "UserListProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the User List resource.

// A user list. This is a list of users a customer may target.
message UserList {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/UserList"
    pattern: "customers/{customer_id}/userLists/{user_list_id}"
  };

  // Immutable. The resource name of the user list.
  // User list resource names have the form:
  //
  // `customers/{customer_id}/userLists/{user_list_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserList"
    }
  ];

  // Output only. Id of the user list.
  optional int64 id = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An option that indicates if a user may edit a list. Depends on
  // the list ownership and list type. For example, external remarketing user
  // lists are not editable.
  //
  // This field is read-only.
  optional bool read_only = 26 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Name of this user list. Depending on its access_reason, the user list name
  // may not be unique (for example, if access_reason=SHARED)
  optional string name = 27;

  // Description of this user list.
  optional string description = 28;

  // Membership status of this user list. Indicates whether a user list is open
  // or active. Only open user lists can accumulate more users and can be
  // targeted to.
  google.ads.googleads.v17.enums.UserListMembershipStatusEnum
      .UserListMembershipStatus membership_status = 6;

  // An ID from external system. It is used by user list sellers to correlate
  // IDs on their systems.
  optional string integration_code = 29;

  // Number of days a user's cookie stays on your list since its most recent
  // addition to the list. This field must be between 0 and 540 inclusive.
  // However, for CRM based userlists, this field can be set to 10000 which
  // means no expiration.
  //
  // This field is ignored for logical_user_list and rule_based_user_list types.
  // Membership to lists of these types depends on the rules defined by the
  // lists.
  optional int64 membership_life_span = 30;

  // Output only. Estimated number of users in this user list, on the Google
  // Display Network. This value is null if the number of users has not yet been
  // determined.
  //
  // This field is read-only.
  optional int64 size_for_display = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Size range in terms of number of users of the UserList, on the
  // Google Display Network.
  //
  // This field is read-only.
  google.ads.googleads.v17.enums.UserListSizeRangeEnum.UserListSizeRange
      size_range_for_display = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Estimated number of users in this user list in the google.com
  // domain. These are the users available for targeting in Search campaigns.
  // This value is null if the number of users has not yet been determined.
  //
  // This field is read-only.
  optional int64 size_for_search = 32
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Size range in terms of number of users of the UserList, for
  // Search ads.
  //
  // This field is read-only.
  google.ads.googleads.v17.enums.UserListSizeRangeEnum.UserListSizeRange
      size_range_for_search = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of this list.
  //
  // This field is read-only.
  google.ads.googleads.v17.enums.UserListTypeEnum.UserListType type = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Indicating the reason why this user list membership status is closed. It is
  // only populated on lists that were automatically closed due to inactivity,
  // and will be cleared once the list membership status becomes open.
  google.ads.googleads.v17.enums.UserListClosingReasonEnum.UserListClosingReason
      closing_reason = 14;

  // Output only. Indicates the reason this account has been granted access to
  // the list. The reason can be SHARED, OWNED, LICENSED or SUBSCRIBED.
  //
  // This field is read-only.
  google.ads.googleads.v17.enums.AccessReasonEnum.AccessReason access_reason =
      15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Indicates if this share is still enabled. When a UserList is shared with
  // the user this field is set to ENABLED. Later the userList owner can decide
  // to revoke the share and make it DISABLED.
  // The default value of this field is set to ENABLED.
  google.ads.googleads.v17.enums.UserListAccessStatusEnum.UserListAccessStatus
      account_user_list_status = 16;

  // Indicates if this user list is eligible for Google Search Network.
  optional bool eligible_for_search = 33;

  // Output only. Indicates this user list is eligible for Google Display
  // Network.
  //
  // This field is read-only.
  optional bool eligible_for_display = 34
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates match rate for Customer Match lists. The range of
  // this field is [0-100]. This will be null for other list types or when it's
  // not possible to calculate the match rate.
  //
  // This field is read-only.
  optional int32 match_rate_percentage = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The user list.
  //
  // Exactly one must be set.
  oneof user_list {
    // User list of CRM users provided by the advertiser.
    google.ads.googleads.v17.common.CrmBasedUserListInfo crm_based_user_list =
        19;

    // Output only. User list which are similar to users from another UserList.
    // These lists are readonly and automatically created by google.
    google.ads.googleads.v17.common.SimilarUserListInfo similar_user_list = 20
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // User list generated by a rule.
    google.ads.googleads.v17.common.RuleBasedUserListInfo rule_based_user_list =
        21;

    // User list that is a custom combination of user lists and user interests.
    google.ads.googleads.v17.common.LogicalUserListInfo logical_user_list = 22;

    // User list targeting as a collection of conversion or remarketing actions.
    google.ads.googleads.v17.common.BasicUserListInfo basic_user_list = 23;

    // Immutable. Lookalike User List.
    google.ads.googleads.v17.common.LookalikeUserListInfo lookalike_user_list =
        36 [(google.api.field_behavior) = IMMUTABLE];
  }
}
