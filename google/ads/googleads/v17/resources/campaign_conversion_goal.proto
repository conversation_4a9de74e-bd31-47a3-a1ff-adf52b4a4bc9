// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/conversion_action_category.proto";
import "google/ads/googleads/v17/enums/conversion_origin.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignConversionGoalProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// The biddability setting for the specified campaign only for all
// conversion actions with a matching category and origin.
message CampaignConversionGoal {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignConversionGoal"
    pattern: "customers/{customer_id}/campaignConversionGoals/{campaign_id}~{category}~{source}"
  };

  // Immutable. The resource name of the campaign conversion goal.
  // Campaign conversion goal resource names have the form:
  //
  // `customers/{customer_id}/campaignConversionGoals/{campaign_id}~{category}~{origin}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignConversionGoal"
    }
  ];

  // Immutable. The campaign with which this campaign conversion goal is
  // associated.
  string campaign = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // The conversion category of this campaign conversion goal.
  google.ads.googleads.v17.enums.ConversionActionCategoryEnum
      .ConversionActionCategory category = 3;

  // The conversion origin of this campaign conversion goal.
  google.ads.googleads.v17.enums.ConversionOriginEnum.ConversionOrigin origin =
      4;

  // The biddability of the campaign conversion goal.
  bool biddable = 5;
}
