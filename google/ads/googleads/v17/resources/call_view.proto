// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/call_tracking_display_location.proto";
import "google/ads/googleads/v17/enums/call_type.proto";
import "google/ads/googleads/v17/enums/google_voice_call_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CallViewProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the call view resource.

// A call view that includes data for call tracking of call-only ads or call
// extensions.
message CallView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CallView"
    pattern: "customers/{customer_id}/callViews/{call_detail_id}"
  };

  // Output only. The resource name of the call view.
  // Call view resource names have the form:
  //
  // `customers/{customer_id}/callViews/{call_detail_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CallView"
    }
  ];

  // Output only. Country code of the caller.
  string caller_country_code = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Area code of the caller. Null if the call duration is shorter
  // than 15 seconds.
  string caller_area_code = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The advertiser-provided call duration in seconds.
  int64 call_duration_seconds = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The advertiser-provided call start date time.
  string start_call_date_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The advertiser-provided call end date time.
  string end_call_date_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The call tracking display location.
  google.ads.googleads.v17.enums.CallTrackingDisplayLocationEnum
      .CallTrackingDisplayLocation call_tracking_display_location = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the call.
  google.ads.googleads.v17.enums.CallTypeEnum.CallType type = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the call.
  google.ads.googleads.v17.enums.GoogleVoiceCallStatusEnum.GoogleVoiceCallStatus
      call_status = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}
