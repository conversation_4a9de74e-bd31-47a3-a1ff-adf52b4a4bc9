// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerLabelProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the customer label resource.

// Represents a relationship between a customer and a label. This customer may
// not have access to all the labels attached to it. Additional CustomerLabels
// may be returned by increasing permissions with login-customer-id.
message CustomerLabel {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerLabel"
    pattern: "customers/{customer_id}/customerLabels/{label_id}"
  };

  // Immutable. Name of the resource.
  // Customer label resource names have the form:
  // `customers/{customer_id}/customerLabels/{label_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerLabel"
    }
  ];

  // Output only. The resource name of the customer to which the label is
  // attached. Read only.
  optional string customer = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];

  // Output only. The resource name of the label assigned to the customer.
  //
  // Note: the Customer ID portion of the label resource name is not
  // validated when creating a new CustomerLabel.
  optional string label = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Label" }
  ];
}
