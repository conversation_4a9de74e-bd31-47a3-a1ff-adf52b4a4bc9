// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/asset_types.proto";
import "google/ads/googleads/v17/common/custom_parameter.proto";
import "google/ads/googleads/v17/common/policy.proto";
import "google/ads/googleads/v17/enums/asset_field_type.proto";
import "google/ads/googleads/v17/enums/asset_source.proto";
import "google/ads/googleads/v17/enums/asset_type.proto";
import "google/ads/googleads/v17/enums/policy_approval_status.proto";
import "google/ads/googleads/v17/enums/policy_review_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the asset resource.

// Asset is a part of an ad which can be shared across multiple ads.
// It can be an image (ImageAsset), a video (YoutubeVideoAsset), etc.
// Assets are immutable and cannot be removed. To stop an asset from serving,
// remove the asset from the entity that is using it.
message Asset {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Asset"
    pattern: "customers/{customer_id}/assets/{asset_id}"
  };

  // Immutable. The resource name of the asset.
  // Asset resource names have the form:
  //
  // `customers/{customer_id}/assets/{asset_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // Output only. The ID of the asset.
  optional int64 id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional name of the asset.
  optional string name = 12;

  // Output only. Type of the asset.
  google.ads.googleads.v17.enums.AssetTypeEnum.AssetType type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // A list of possible final URLs after all cross domain redirects.
  repeated string final_urls = 14;

  // A list of possible final mobile URLs after all cross domain redirects.
  repeated string final_mobile_urls = 16;

  // URL template for constructing a tracking URL.
  optional string tracking_url_template = 17;

  // A list of mappings to be used for substituting URL custom parameter tags in
  // the tracking_url_template, final_urls, and/or final_mobile_urls.
  repeated google.ads.googleads.v17.common.CustomParameter
      url_custom_parameters = 18;

  // URL template for appending params to landing page URLs served with parallel
  // tracking.
  optional string final_url_suffix = 19;

  // Output only. Source of the asset.
  google.ads.googleads.v17.enums.AssetSourceEnum.AssetSource source = 38
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Policy information for the asset.
  AssetPolicySummary policy_summary = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Policy information for the asset for each FieldType.
  repeated AssetFieldTypePolicySummary field_type_policy_summaries = 40
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The specific type of the asset.
  oneof asset_data {
    // Immutable. A YouTube video asset.
    google.ads.googleads.v17.common.YoutubeVideoAsset youtube_video_asset = 5
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. A media bundle asset.
    google.ads.googleads.v17.common.MediaBundleAsset media_bundle_asset = 6
        [(google.api.field_behavior) = IMMUTABLE];

    // Output only. An image asset.
    google.ads.googleads.v17.common.ImageAsset image_asset = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. A text asset.
    google.ads.googleads.v17.common.TextAsset text_asset = 8
        [(google.api.field_behavior) = IMMUTABLE];

    // A lead form asset.
    google.ads.googleads.v17.common.LeadFormAsset lead_form_asset = 9;

    // A book on google asset.
    google.ads.googleads.v17.common.BookOnGoogleAsset book_on_google_asset = 10;

    // A promotion asset.
    google.ads.googleads.v17.common.PromotionAsset promotion_asset = 15;

    // A callout asset.
    google.ads.googleads.v17.common.CalloutAsset callout_asset = 20;

    // A structured snippet asset.
    google.ads.googleads.v17.common.StructuredSnippetAsset
        structured_snippet_asset = 21;

    // A sitelink asset.
    google.ads.googleads.v17.common.SitelinkAsset sitelink_asset = 22;

    // A page feed asset.
    google.ads.googleads.v17.common.PageFeedAsset page_feed_asset = 23;

    // A dynamic education asset.
    google.ads.googleads.v17.common.DynamicEducationAsset
        dynamic_education_asset = 24;

    // A mobile app asset.
    google.ads.googleads.v17.common.MobileAppAsset mobile_app_asset = 25;

    // A hotel callout asset.
    google.ads.googleads.v17.common.HotelCalloutAsset hotel_callout_asset = 26;

    // A call asset.
    google.ads.googleads.v17.common.CallAsset call_asset = 27;

    // A price asset.
    google.ads.googleads.v17.common.PriceAsset price_asset = 28;

    // Immutable. A call to action asset.
    google.ads.googleads.v17.common.CallToActionAsset call_to_action_asset = 29
        [(google.api.field_behavior) = IMMUTABLE];

    // A dynamic real estate asset.
    google.ads.googleads.v17.common.DynamicRealEstateAsset
        dynamic_real_estate_asset = 30;

    // A dynamic custom asset.
    google.ads.googleads.v17.common.DynamicCustomAsset dynamic_custom_asset =
        31;

    // A dynamic hotels and rentals asset.
    google.ads.googleads.v17.common.DynamicHotelsAndRentalsAsset
        dynamic_hotels_and_rentals_asset = 32;

    // A dynamic flights asset.
    google.ads.googleads.v17.common.DynamicFlightsAsset dynamic_flights_asset =
        33;

    // Immutable. A Demand Gen carousel card asset.
    google.ads.googleads.v17.common.DemandGenCarouselCardAsset
        demand_gen_carousel_card_asset = 50
        [(google.api.field_behavior) = IMMUTABLE];

    // A dynamic travel asset.
    google.ads.googleads.v17.common.DynamicTravelAsset dynamic_travel_asset =
        35;

    // A dynamic local asset.
    google.ads.googleads.v17.common.DynamicLocalAsset dynamic_local_asset = 36;

    // A dynamic jobs asset.
    google.ads.googleads.v17.common.DynamicJobsAsset dynamic_jobs_asset = 37;

    // Output only. A location asset.
    google.ads.googleads.v17.common.LocationAsset location_asset = 39
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. A hotel property asset.
    google.ads.googleads.v17.common.HotelPropertyAsset hotel_property_asset = 41
        [(google.api.field_behavior) = IMMUTABLE];
  }
}

// Contains policy information for an asset under AssetFieldType context.
message AssetFieldTypePolicySummary {
  // Output only. FieldType of this asset.
  optional google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldType
      asset_field_type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of this asset.
  optional google.ads.googleads.v17.enums.AssetSourceEnum.AssetSource
      asset_source = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Policy summary.
  optional AssetPolicySummary policy_summary_info = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains policy information for an asset.
message AssetPolicySummary {
  // Output only. The list of policy findings for this asset.
  repeated google.ads.googleads.v17.common.PolicyTopicEntry
      policy_topic_entries = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Where in the review process this asset is.
  google.ads.googleads.v17.enums.PolicyReviewStatusEnum.PolicyReviewStatus
      review_status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The overall approval status of this asset, calculated based on
  // the status of its individual policy topic entries.
  google.ads.googleads.v17.enums.PolicyApprovalStatusEnum.PolicyApprovalStatus
      approval_status = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
