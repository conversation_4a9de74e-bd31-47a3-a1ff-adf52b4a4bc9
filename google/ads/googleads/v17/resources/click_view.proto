// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/click_location.proto";
import "google/ads/googleads/v17/common/criteria.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ClickViewProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the ClickView resource.

// A click view with metrics aggregated at each click level, including both
// valid and invalid clicks. For non-Search campaigns, metrics.clicks
// represents the number of valid and invalid interactions.
// Queries including ClickView must have a filter limiting the results to one
// day and can be requested for dates back to 90 days before the time of the
// request.
message ClickView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ClickView"
    pattern: "customers/{customer_id}/clickViews/{date}~{gclid}"
  };

  // Output only. The resource name of the click view.
  // Click view resource names have the form:
  //
  // `customers/{customer_id}/clickViews/{date (yyyy-MM-dd)}~{gclid}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ClickView"
    }
  ];

  // Output only. The Google Click ID.
  optional string gclid = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The location criteria matching the area of interest associated
  // with the impression.
  google.ads.googleads.v17.common.ClickLocation area_of_interest = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The location criteria matching the location of presence
  // associated with the impression.
  google.ads.googleads.v17.common.ClickLocation location_of_presence = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Page number in search results where the ad was shown.
  optional int64 page_number = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The associated ad.
  optional string ad_group_ad = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAd"
    }
  ];

  // Output only. The associated campaign location target, if one exists.
  optional string campaign_location_target = 11 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/GeoTargetConstant"
    }
  ];

  // Output only. The associated user list, if one exists.
  optional string user_list = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserList"
    }
  ];

  // Output only. The associated keyword, if one exists and the click
  // corresponds to the SEARCH channel.
  string keyword = 13 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupCriterion"
    }
  ];

  // Output only. Basic information about the associated keyword, if it exists.
  google.ads.googleads.v17.common.KeywordInfo keyword_info = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
