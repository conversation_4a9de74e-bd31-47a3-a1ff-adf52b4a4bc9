// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/ad_type_infos.proto";
import "google/ads/googleads/v17/common/custom_parameter.proto";
import "google/ads/googleads/v17/common/final_app_url.proto";
import "google/ads/googleads/v17/common/url_collection.proto";
import "google/ads/googleads/v17/enums/ad_type.proto";
import "google/ads/googleads/v17/enums/device.proto";
import "google/ads/googleads/v17/enums/system_managed_entity_source.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the ad type.

// An ad.
message Ad {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Ad"
    pattern: "customers/{customer_id}/ads/{ad_id}"
  };

  // Immutable. The resource name of the ad.
  // Ad resource names have the form:
  //
  // `customers/{customer_id}/ads/{ad_id}`
  string resource_name = 37 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Ad" }
  ];

  // Output only. The ID of the ad.
  optional int64 id = 40 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The list of possible final URLs after all cross-domain redirects for the
  // ad.
  repeated string final_urls = 41;

  // A list of final app URLs that will be used on mobile if the user has the
  // specific app installed.
  repeated google.ads.googleads.v17.common.FinalAppUrl final_app_urls = 35;

  // The list of possible final mobile URLs after all cross-domain redirects
  // for the ad.
  repeated string final_mobile_urls = 42;

  // The URL template for constructing a tracking URL.
  optional string tracking_url_template = 43;

  // The suffix to use when constructing a final URL.
  optional string final_url_suffix = 44;

  // The list of mappings that can be used to substitute custom parameter tags
  // in a `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
  // For mutates, use url custom parameter operations.
  repeated google.ads.googleads.v17.common.CustomParameter
      url_custom_parameters = 10;

  // The URL that appears in the ad description for some ad formats.
  optional string display_url = 45;

  // Output only. The type of ad.
  google.ads.googleads.v17.enums.AdTypeEnum.AdType type = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates if this ad was automatically added by Google Ads and
  // not by a user. For example, this could happen when ads are automatically
  // created as suggestions for new ads based on knowledge of how existing ads
  // are performing.
  optional bool added_by_google_ads = 46
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The device preference for the ad. You can only specify a preference for
  // mobile devices. When this preference is set the ad will be preferred over
  // other ads when being displayed on a mobile device. The ad can still be
  // displayed on other device types, for example, if no other ads are
  // available. If unspecified (no device preference), all devices are targeted.
  // This is only supported by some ad types.
  google.ads.googleads.v17.enums.DeviceEnum.Device device_preference = 20;

  // Additional URLs for the ad that are tagged with a unique identifier that
  // can be referenced from other fields in the ad.
  repeated google.ads.googleads.v17.common.UrlCollection url_collections = 26;

  // Immutable. The name of the ad. This is only used to be able to identify the
  // ad. It does not need to be unique and does not affect the served ad. The
  // name field is currently only supported for DisplayUploadAd, ImageAd,
  // ShoppingComparisonListingAd and VideoAd.
  optional string name = 47 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. If this ad is system managed, then this field will indicate
  // the source. This field is read-only.
  google.ads.googleads.v17.enums.SystemManagedResourceSourceEnum
      .SystemManagedResourceSource system_managed_resource_source = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Details pertinent to the ad type. Exactly one value must be set.
  oneof ad_data {
    // Immutable. Details pertaining to a text ad.
    google.ads.googleads.v17.common.TextAdInfo text_ad = 6
        [(google.api.field_behavior) = IMMUTABLE];

    // Details pertaining to an expanded text ad.
    google.ads.googleads.v17.common.ExpandedTextAdInfo expanded_text_ad = 7;

    // Details pertaining to a call ad.
    google.ads.googleads.v17.common.CallAdInfo call_ad = 49;

    // Immutable. Details pertaining to an Expanded Dynamic Search Ad.
    // This type of ad has its headline, final URLs, and display URL
    // auto-generated at serving time according to domain name specific
    // information provided by `dynamic_search_ads_setting` linked at the
    // campaign level.
    google.ads.googleads.v17.common.ExpandedDynamicSearchAdInfo
        expanded_dynamic_search_ad = 14
        [(google.api.field_behavior) = IMMUTABLE];

    // Details pertaining to a hotel ad.
    google.ads.googleads.v17.common.HotelAdInfo hotel_ad = 15;

    // Details pertaining to a Smart Shopping ad.
    google.ads.googleads.v17.common.ShoppingSmartAdInfo shopping_smart_ad = 17;

    // Details pertaining to a Shopping product ad.
    google.ads.googleads.v17.common.ShoppingProductAdInfo shopping_product_ad =
        18;

    // Immutable. Details pertaining to an Image ad.
    google.ads.googleads.v17.common.ImageAdInfo image_ad = 22
        [(google.api.field_behavior) = IMMUTABLE];

    // Details pertaining to a Video ad.
    google.ads.googleads.v17.common.VideoAdInfo video_ad = 24;

    // Details pertaining to a Video responsive ad.
    google.ads.googleads.v17.common.VideoResponsiveAdInfo video_responsive_ad =
        39;

    // Details pertaining to a responsive search ad.
    google.ads.googleads.v17.common.ResponsiveSearchAdInfo
        responsive_search_ad = 25;

    // Details pertaining to a legacy responsive display ad.
    google.ads.googleads.v17.common.LegacyResponsiveDisplayAdInfo
        legacy_responsive_display_ad = 28;

    // Details pertaining to an app ad.
    google.ads.googleads.v17.common.AppAdInfo app_ad = 29;

    // Immutable. Details pertaining to a legacy app install ad.
    google.ads.googleads.v17.common.LegacyAppInstallAdInfo
        legacy_app_install_ad = 30 [(google.api.field_behavior) = IMMUTABLE];

    // Details pertaining to a responsive display ad.
    google.ads.googleads.v17.common.ResponsiveDisplayAdInfo
        responsive_display_ad = 31;

    // Details pertaining to a local ad.
    google.ads.googleads.v17.common.LocalAdInfo local_ad = 32;

    // Details pertaining to a display upload ad.
    google.ads.googleads.v17.common.DisplayUploadAdInfo display_upload_ad = 33;

    // Details pertaining to an app engagement ad.
    google.ads.googleads.v17.common.AppEngagementAdInfo app_engagement_ad = 34;

    // Details pertaining to a Shopping Comparison Listing ad.
    google.ads.googleads.v17.common.ShoppingComparisonListingAdInfo
        shopping_comparison_listing_ad = 36;

    // Details pertaining to a Smart campaign ad.
    google.ads.googleads.v17.common.SmartCampaignAdInfo smart_campaign_ad = 48;

    // Details pertaining to an app pre-registration ad.
    google.ads.googleads.v17.common.AppPreRegistrationAdInfo
        app_pre_registration_ad = 50;

    // Details pertaining to a Demand Gen multi asset ad.
    google.ads.googleads.v17.common.DemandGenMultiAssetAdInfo
        demand_gen_multi_asset_ad = 62;

    // Details pertaining to a Demand Gen carousel ad.
    google.ads.googleads.v17.common.DemandGenCarouselAdInfo
        demand_gen_carousel_ad = 63;

    // Details pertaining to a Demand Gen video responsive ad.
    google.ads.googleads.v17.common.DemandGenVideoResponsiveAdInfo
        demand_gen_video_responsive_ad = 64;

    // Details pertaining to a Demand Gen product ad.
    google.ads.googleads.v17.common.DemandGenProductAdInfo
        demand_gen_product_ad = 61;

    // Details pertaining to a travel ad.
    google.ads.googleads.v17.common.TravelAdInfo travel_ad = 54;
  }
}
