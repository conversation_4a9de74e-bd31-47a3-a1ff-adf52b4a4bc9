// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/goal_config_level.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ConversionGoalCampaignConfigProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Conversion goal settings for a Campaign.
message ConversionGoalCampaignConfig {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ConversionGoalCampaignConfig"
    pattern: "customers/{customer_id}/conversionGoalCampaignConfigs/{campaign_id}"
  };

  // Immutable. The resource name of the conversion goal campaign config.
  // Conversion goal campaign config resource names have the form:
  //
  // `customers/{customer_id}/conversionGoalCampaignConfigs/{campaign_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ConversionGoalCampaignConfig"
    }
  ];

  // Immutable. The campaign with which this conversion goal campaign config is
  // associated.
  string campaign = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // The level of goal config the campaign is using.
  google.ads.googleads.v17.enums.GoalConfigLevelEnum.GoalConfigLevel
      goal_config_level = 3;

  // The custom conversion goal the campaign is using for optimization.
  string custom_conversion_goal = 4 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomConversionGoal"
  }];
}
