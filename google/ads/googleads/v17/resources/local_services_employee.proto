// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/local_services_employee_status.proto";
import "google/ads/googleads/v17/enums/local_services_employee_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesEmployeeProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// A local services employee resource.
message LocalServicesEmployee {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/LocalServicesEmployee"
    pattern: "customers/{customer_id}/localServicesEmployees/{gls_employee_id}"
  };

  // Immutable. The resource name of the Local Services Verification.
  // Local Services Verification resource names have the form:
  //
  // `customers/{customer_id}/localServicesEmployees/{gls_employee_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/LocalServicesEmployee"
    }
  ];

  // Output only. The ID of the employee.
  optional int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp of employee creation.
  // The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's timezone.
  // Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
  string creation_date_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Employee status, such as DELETED or ENABLED.
  google.ads.googleads.v17.enums.LocalServicesEmployeeStatusEnum
      .LocalServicesEmployeeStatus status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Employee type.
  google.ads.googleads.v17.enums.LocalServicesEmployeeTypeEnum
      .LocalServicesEmployeeType type = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of degrees this employee has obtained, and wants to
  // feature.
  repeated UniversityDegree university_degrees = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The institutions where the employee has completed their
  // residency.
  repeated Residency residencies = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The institutions where the employee has completed their
  // fellowship.
  repeated Fellowship fellowships = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Job title for this employee, such as "Senior partner" in legal
  // verticals.
  optional string job_title = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The year that this employee started practicing in this field.
  optional int32 year_started_practicing = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Languages that the employee speaks, represented as language
  // tags from https://developers.google.com/admin-sdk/directory/v1/languages
  repeated string languages_spoken = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Category of the employee. A list of Local Services category
  // IDs can be found at
  // https://developers.google.com/google-ads/api/data/codes-formats#local_services_ids.
  repeated string category_ids = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. NPI id associated with the employee.
  optional string national_provider_id_number = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the employee.
  optional string email_address = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. First name of the employee.
  optional string first_name = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Middle name of the employee.
  optional string middle_name = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last name of the employee.
  optional string last_name = 17 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A list of degrees this employee has obtained, and wants to feature.
message UniversityDegree {
  // Output only. Name of the university at which the degree was obtained.
  optional string institution_name = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the degree obtained.
  optional string degree = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Year of graduation.
  optional int32 graduation_year = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Details about the employee's medical residency.
// Residency is a stage of graduate medical education in which a qualified
// medical professional practices under the supervision of a senior clinician.
message Residency {
  // Output only. Name of the institution at which the residency was completed.
  optional string institution_name = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Year of completion.
  optional int32 completion_year = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Details about the employee's medical Fellowship.
// Fellowship is a period of medical training that the professional undertakes
// after finishing their residency.
message Fellowship {
  // Output only. Name of the instutition at which the fellowship was completed.
  optional string institution_name = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Year of completion.
  optional int32 completion_year = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
