// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/geo_target_constant_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "GeoTargetConstantProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the geo target constant resource.

// A geo target constant.
message GeoTargetConstant {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/GeoTargetConstant"
    pattern: "geoTargetConstants/{criterion_id}"
  };

  // Output only. The resource name of the geo target constant.
  // Geo target constant resource names have the form:
  //
  // `geoTargetConstants/{geo_target_constant_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/GeoTargetConstant"
    }
  ];

  // Output only. The ID of the geo target constant.
  optional int64 id = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Geo target constant English name.
  optional string name = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ISO-3166-1 alpha-2 country code that is associated with
  // the target.
  optional string country_code = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Geo target constant target type.
  optional string target_type = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Geo target constant status.
  google.ads.googleads.v17.enums.GeoTargetConstantStatusEnum
      .GeoTargetConstantStatus status = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The fully qualified English name, consisting of the target's
  // name and that of its parent and country.
  optional string canonical_name = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the parent geo target constant.
  // Geo target constant resource names have the form:
  //
  // `geoTargetConstants/{parent_geo_target_constant_id}`
  optional string parent_geo_target = 9 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/GeoTargetConstant"
    }
  ];
}
