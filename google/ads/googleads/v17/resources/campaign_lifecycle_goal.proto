// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/lifecycle_goals.proto";
import "google/ads/googleads/v17/enums/customer_acquisition_optimization_mode.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignLifecycleGoalProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the campaign lifecycle resource.

// Campaign level customer lifecycle goal settings.
message CampaignLifecycleGoal {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignLifecycleGoal"
    pattern: "customers/{customer_id}/campaignLifecycleGoals/{campaign_id}"
  };

  // Immutable. The resource name of the customer lifecycle goal of a campaign.
  //
  // `customers/{customer_id}/campaignLifecycleGoal/{campaign_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignLifecycleGoal"
    }
  ];

  // Output only. The campaign where the goal is attached.
  string campaign = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The customer acquisition goal settings for the campaign. The
  // customer acquisition goal is described in this article:
  // https://support.google.com/google-ads/answer/12080169
  CustomerAcquisitionGoalSettings customer_acquisition_goal_settings = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The customer acquisition goal settings for the campaign.
message CustomerAcquisitionGoalSettings {
  // Output only. Customer acquisition optimization mode of this campaign.
  google.ads.googleads.v17.enums.CustomerAcquisitionOptimizationModeEnum
      .CustomerAcquisitionOptimizationMode optimization_mode = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Campaign specific values for the customer acquisition goal.
  google.ads.googleads.v17.common.LifecycleGoalValueSettings value_settings = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
