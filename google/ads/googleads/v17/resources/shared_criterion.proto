// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/criteria.proto";
import "google/ads/googleads/v17/enums/criterion_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "SharedCriterionProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the SharedCriterion resource.

// A criterion belonging to a shared set.
message SharedCriterion {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/SharedCriterion"
    pattern: "customers/{customer_id}/sharedCriteria/{shared_set_id}~{criterion_id}"
  };

  // Immutable. The resource name of the shared criterion.
  // Shared set resource names have the form:
  //
  // `customers/{customer_id}/sharedCriteria/{shared_set_id}~{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/SharedCriterion"
    }
  ];

  // Immutable. The shared set to which the shared criterion belongs.
  optional string shared_set = 10 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/SharedSet"
    }
  ];

  // Output only. The ID of the criterion.
  //
  // This field is ignored for mutates.
  optional int64 criterion_id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the criterion.
  google.ads.googleads.v17.enums.CriterionTypeEnum.CriterionType type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The criterion.
  //
  // Exactly one must be set.
  oneof criterion {
    // Immutable. Keyword.
    google.ads.googleads.v17.common.KeywordInfo keyword = 3
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. YouTube Video.
    google.ads.googleads.v17.common.YouTubeVideoInfo youtube_video = 5
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. YouTube Channel.
    google.ads.googleads.v17.common.YouTubeChannelInfo youtube_channel = 6
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Placement.
    google.ads.googleads.v17.common.PlacementInfo placement = 7
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Mobile App Category.
    google.ads.googleads.v17.common.MobileAppCategoryInfo mobile_app_category =
        8 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Mobile application.
    google.ads.googleads.v17.common.MobileApplicationInfo mobile_application = 9
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Brand.
    google.ads.googleads.v17.common.BrandInfo brand = 12
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
