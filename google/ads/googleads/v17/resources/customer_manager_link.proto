// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/manager_link_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerManagerLinkProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the CustomerManagerLink resource.

// Represents customer-manager link relationship.
message CustomerManagerLink {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerManagerLink"
    pattern: "customers/{customer_id}/customerManagerLinks/{manager_customer_id}~{manager_link_id}"
  };

  // Immutable. Name of the resource.
  // CustomerManagerLink resource names have the form:
  // `customers/{customer_id}/customerManagerLinks/{manager_customer_id}~{manager_link_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerManagerLink"
    }
  ];

  // Output only. The manager customer linked to the customer.
  optional string manager_customer = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];

  // Output only. ID of the customer-manager link. This field is read only.
  optional int64 manager_link_id = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Status of the link between the customer and the manager.
  google.ads.googleads.v17.enums.ManagerLinkStatusEnum.ManagerLinkStatus
      status = 5;
}
