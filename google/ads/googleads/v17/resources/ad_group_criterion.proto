// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/criteria.proto";
import "google/ads/googleads/v17/common/custom_parameter.proto";
import "google/ads/googleads/v17/enums/ad_group_criterion_approval_status.proto";
import "google/ads/googleads/v17/enums/ad_group_criterion_primary_status.proto";
import "google/ads/googleads/v17/enums/ad_group_criterion_primary_status_reason.proto";
import "google/ads/googleads/v17/enums/ad_group_criterion_status.proto";
import "google/ads/googleads/v17/enums/bidding_source.proto";
import "google/ads/googleads/v17/enums/criterion_system_serving_status.proto";
import "google/ads/googleads/v17/enums/criterion_type.proto";
import "google/ads/googleads/v17/enums/quality_score_bucket.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupCriterionProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the ad group criterion resource.

// An ad group criterion.
// The ad_group_criterion report only returns criteria that were explicitly
// added to the ad group.
message AdGroupCriterion {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupCriterion"
    pattern: "customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}"
  };

  // A container for ad group criterion quality information.
  message QualityInfo {
    // Output only. The quality score.
    //
    // This field may not be populated if Google does not have enough
    // information to determine a value.
    optional int32 quality_score = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The performance of the ad compared to other advertisers.
    google.ads.googleads.v17.enums.QualityScoreBucketEnum.QualityScoreBucket
        creative_quality_score = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The quality score of the landing page.
    google.ads.googleads.v17.enums.QualityScoreBucketEnum.QualityScoreBucket
        post_click_quality_score = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The click-through rate compared to that of other
    // advertisers.
    google.ads.googleads.v17.enums.QualityScoreBucketEnum.QualityScoreBucket
        search_predicted_ctr = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Estimates for criterion bids at various positions.
  message PositionEstimates {
    // Output only. The estimate of the CPC bid required for ad to be shown on
    // first page of search results.
    optional int64 first_page_cpc_micros = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The estimate of the CPC bid required for ad to be displayed
    // in first position, at the top of the first page of search results.
    optional int64 first_position_cpc_micros = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The estimate of the CPC bid required for ad to be displayed
    // at the top of the first page of search results.
    optional int64 top_of_page_cpc_micros = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Estimate of how many clicks per week you might get by
    // changing your keyword bid to the value in first_position_cpc_micros.
    optional int64 estimated_add_clicks_at_first_position_cpc = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Estimate of how your cost per week might change when
    // changing your keyword bid to the value in first_position_cpc_micros.
    optional int64 estimated_add_cost_at_first_position_cpc = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. The resource name of the ad group criterion.
  // Ad group criterion resource names have the form:
  //
  // `customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupCriterion"
    }
  ];

  // Output only. The ID of the criterion.
  //
  // This field is ignored for mutates.
  optional int64 criterion_id = 56 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name of the criterion.
  //
  // This field is ignored for mutates.
  string display_name = 77 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The status of the criterion.
  //
  // This is the status of the ad group criterion entity, set by the client.
  // Note: UI reports may incorporate additional information that affects
  // whether a criterion is eligible to run. In some cases a criterion that's
  // REMOVED in the API can still show as enabled in the UI.
  // For example, campaigns by default show to users of all age ranges unless
  // excluded. The UI will show each age range as "enabled", since they're
  // eligible to see the ads; but AdGroupCriterion.status will show "removed",
  // since no positive criterion was added.
  google.ads.googleads.v17.enums.AdGroupCriterionStatusEnum
      .AdGroupCriterionStatus status = 3;

  // Output only. Information regarding the quality of the criterion.
  QualityInfo quality_info = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The ad group to which the criterion belongs.
  optional string ad_group = 57 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. The type of the criterion.
  google.ads.googleads.v17.enums.CriterionTypeEnum.CriterionType type = 25
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
  //
  // This field is immutable. To switch a criterion from positive to negative,
  // remove then re-add it.
  optional bool negative = 58 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Serving status of the criterion.
  google.ads.googleads.v17.enums.CriterionSystemServingStatusEnum
      .CriterionSystemServingStatus system_serving_status = 52
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Approval status of the criterion.
  google.ads.googleads.v17.enums.AdGroupCriterionApprovalStatusEnum
      .AdGroupCriterionApprovalStatus approval_status = 53
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of disapproval reasons of the criterion.
  //
  // The different reasons for disapproving a criterion can be found here:
  // https://support.google.com/adspolicy/answer/6008942
  //
  // This field is read-only.
  repeated string disapproval_reasons = 59
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource names of labels attached to this ad group
  // criterion.
  repeated string labels = 60 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupCriterionLabel"
    }
  ];

  // The modifier for the bid when the criterion matches. The modifier must be
  // in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
  optional double bid_modifier = 61;

  // The CPC (cost-per-click) bid.
  optional int64 cpc_bid_micros = 62;

  // The CPM (cost-per-thousand viewable impressions) bid.
  optional int64 cpm_bid_micros = 63;

  // The CPV (cost-per-view) bid.
  optional int64 cpv_bid_micros = 64;

  // The CPC bid amount, expressed as a fraction of the advertised price
  // for some good or service. The valid range for the fraction is [0,1) and the
  // value stored here is 1,000,000 * [fraction].
  optional int64 percent_cpc_bid_micros = 65;

  // Output only. The effective CPC (cost-per-click) bid.
  optional int64 effective_cpc_bid_micros = 66
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The effective CPM (cost-per-thousand viewable impressions)
  // bid.
  optional int64 effective_cpm_bid_micros = 67
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The effective CPV (cost-per-view) bid.
  optional int64 effective_cpv_bid_micros = 68
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The effective Percent CPC bid amount.
  optional int64 effective_percent_cpc_bid_micros = 69
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of the effective CPC bid.
  google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSource
      effective_cpc_bid_source = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of the effective CPM bid.
  google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSource
      effective_cpm_bid_source = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of the effective CPV bid.
  google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSource
      effective_cpv_bid_source = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of the effective Percent CPC bid.
  google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSource
      effective_percent_cpc_bid_source = 35
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Estimates for criterion bids at various positions.
  PositionEstimates position_estimates = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The list of possible final URLs after all cross-domain redirects for the
  // ad.
  repeated string final_urls = 70;

  // The list of possible final mobile URLs after all cross-domain redirects.
  repeated string final_mobile_urls = 71;

  // URL template for appending params to final URL.
  optional string final_url_suffix = 72;

  // The URL template for constructing a tracking URL.
  optional string tracking_url_template = 73;

  // The list of mappings used to substitute custom parameter tags in a
  // `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
  repeated google.ads.googleads.v17.common.CustomParameter
      url_custom_parameters = 14;

  // Output only. The primary status for the ad group criterion.
  optional google.ads.googleads.v17.enums.AdGroupCriterionPrimaryStatusEnum
      .AdGroupCriterionPrimaryStatus primary_status = 85
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The primary status reasons for the ad group criterion.
  repeated
      google.ads.googleads.v17.enums.AdGroupCriterionPrimaryStatusReasonEnum
          .AdGroupCriterionPrimaryStatusReason primary_status_reasons = 86
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The ad group criterion.
  //
  // Exactly one must be set.
  oneof criterion {
    // Immutable. Keyword.
    google.ads.googleads.v17.common.KeywordInfo keyword = 27
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Placement.
    google.ads.googleads.v17.common.PlacementInfo placement = 28
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Mobile app category.
    google.ads.googleads.v17.common.MobileAppCategoryInfo mobile_app_category =
        29 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Mobile application.
    google.ads.googleads.v17.common.MobileApplicationInfo mobile_application =
        30 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Listing group.
    google.ads.googleads.v17.common.ListingGroupInfo listing_group = 32
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Age range.
    google.ads.googleads.v17.common.AgeRangeInfo age_range = 36
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Gender.
    google.ads.googleads.v17.common.GenderInfo gender = 37
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Income range.
    google.ads.googleads.v17.common.IncomeRangeInfo income_range = 38
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Parental status.
    google.ads.googleads.v17.common.ParentalStatusInfo parental_status = 39
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. User List.
    google.ads.googleads.v17.common.UserListInfo user_list = 42
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. YouTube Video.
    google.ads.googleads.v17.common.YouTubeVideoInfo youtube_video = 40
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. YouTube Channel.
    google.ads.googleads.v17.common.YouTubeChannelInfo youtube_channel = 41
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Topic.
    google.ads.googleads.v17.common.TopicInfo topic = 43
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. User Interest.
    google.ads.googleads.v17.common.UserInterestInfo user_interest = 45
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Webpage
    google.ads.googleads.v17.common.WebpageInfo webpage = 46
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. App Payment Model.
    google.ads.googleads.v17.common.AppPaymentModelInfo app_payment_model = 47
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Custom Affinity.
    google.ads.googleads.v17.common.CustomAffinityInfo custom_affinity = 48
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Custom Intent.
    google.ads.googleads.v17.common.CustomIntentInfo custom_intent = 49
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Custom Audience.
    google.ads.googleads.v17.common.CustomAudienceInfo custom_audience = 74
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Combined Audience.
    google.ads.googleads.v17.common.CombinedAudienceInfo combined_audience = 75
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Audience.
    google.ads.googleads.v17.common.AudienceInfo audience = 79
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Location.
    google.ads.googleads.v17.common.LocationInfo location = 82
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Language.
    google.ads.googleads.v17.common.LanguageInfo language = 83
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
