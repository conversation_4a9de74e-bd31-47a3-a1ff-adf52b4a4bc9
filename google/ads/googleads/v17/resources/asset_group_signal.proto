// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/criteria.proto";
import "google/ads/googleads/v17/enums/asset_group_signal_approval_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupSignalProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// AssetGroupSignal represents a signal in an asset group. The existence of a
// signal tells the performance max campaign who's most likely to convert.
// Performance Max uses the signal to look for new people with similar or
// stronger intent to find conversions across Search, Display, Video, and more.
message AssetGroupSignal {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AssetGroupSignal"
    pattern: "customers/{customer_id}/assetGroupSignals/{asset_group_id}~{criterion_id}"
  };

  // Immutable. The resource name of the asset group signal.
  // Asset group signal resource name have the form:
  //
  // `customers/{customer_id}/assetGroupSignals/{asset_group_id}~{signal_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroupSignal"
    }
  ];

  // Immutable. The asset group which this asset group signal belongs to.
  string asset_group = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroup"
    }
  ];

  // Output only. Approval status is the output value for search theme signal
  // after Google ads policy review. When using Audience signal, this field is
  // not used and will be absent.
  google.ads.googleads.v17.enums.AssetGroupSignalApprovalStatusEnum
      .AssetGroupSignalApprovalStatus approval_status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Computed for SearchTheme signals.
  // When using Audience signal, this field is not used and will be absent.
  repeated string disapproval_reasons = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The signal of the asset group.
  oneof signal {
    // Immutable. The audience signal to be used by the performance max
    // campaign.
    google.ads.googleads.v17.common.AudienceInfo audience = 4
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The search_theme signal to be used by the performance max
    // campaign.
    // Mutate errors of search_theme criterion includes
    // AssetGroupSignalError.UNSPECIFIED
    // AssetGroupSignalError.UNKNOWN
    // AssetGroupSignalError.TOO_MANY_WORDS
    // AssetGroupSignalError.SEARCH_THEME_POLICY_VIOLATION
    // FieldError.REQUIRED
    // StringFormatError.ILLEGAL_CHARS
    // StringLengthError.TOO_LONG
    // ResourceCountLimitExceededError.RESOURCE_LIMIT
    google.ads.googleads.v17.common.SearchThemeInfo search_theme = 5
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
