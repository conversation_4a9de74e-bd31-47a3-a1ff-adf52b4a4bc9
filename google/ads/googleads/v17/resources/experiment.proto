// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/metric_goal.proto";
import "google/ads/googleads/v17/enums/async_action_status.proto";
import "google/ads/googleads/v17/enums/experiment_status.proto";
import "google/ads/googleads/v17/enums/experiment_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ExperimentProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Experiment resource.

// A Google ads experiment for users to experiment changes on multiple
// campaigns, compare the performance, and apply the effective changes.
message Experiment {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/Experiment"
    pattern: "customers/{customer_id}/experiments/{trial_id}"
  };

  // Immutable. The resource name of the experiment.
  // Experiment resource names have the form:
  //
  // `customers/{customer_id}/experiments/{experiment_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Experiment"
    }
  ];

  // Output only. The ID of the experiment. Read only.
  optional int64 experiment_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The name of the experiment. It must have a minimum length of 1
  // and maximum length of 1024. It must be unique under a customer.
  string name = 10 [(google.api.field_behavior) = REQUIRED];

  // The description of the experiment. It must have a minimum length of 1 and
  // maximum length of 2048.
  string description = 11;

  // For system managed experiments, the advertiser must provide a suffix during
  // construction, in the setup stage before moving to initiated. The suffix
  // will be appended to the in-design and experiment campaign names so that the
  // name is base campaign name + suffix.
  string suffix = 12;

  // Required. The product/feature that uses this experiment.
  google.ads.googleads.v17.enums.ExperimentTypeEnum.ExperimentType type = 13
      [(google.api.field_behavior) = REQUIRED];

  // The Advertiser-chosen status of this experiment.
  google.ads.googleads.v17.enums.ExperimentStatusEnum.ExperimentStatus status =
      14;

  // Date when the experiment starts. By default, the experiment starts
  // now or on the campaign's start date, whichever is later. If this field is
  // set, then the experiment starts at the beginning of the specified date in
  // the customer's time zone.
  //
  // Format: YYYY-MM-DD
  // Example: 2019-03-14
  optional string start_date = 15;

  // Date when the experiment ends. By default, the experiment ends on
  // the campaign's end date. If this field is set, then the experiment ends at
  // the end of the specified date in the customer's time zone.
  //
  // Format: YYYY-MM-DD
  // Example: 2019-04-18
  optional string end_date = 16;

  // The goals of this experiment.
  repeated google.ads.googleads.v17.common.MetricGoal goals = 17;

  // Output only. The resource name of the long-running operation that can be
  // used to poll for completion of experiment schedule or promote. The most
  // recent long running operation is returned.
  optional string long_running_operation = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the experiment promotion process.
  google.ads.googleads.v17.enums.AsyncActionStatusEnum.AsyncActionStatus
      promote_status = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Set to true if changes to base campaigns should be synced to the
  // trial campaigns. Any changes made directly to trial campaigns will be
  // preserved. This field can only be set when the experiment is being created.
  optional bool sync_enabled = 20 [(google.api.field_behavior) = IMMUTABLE];
}
