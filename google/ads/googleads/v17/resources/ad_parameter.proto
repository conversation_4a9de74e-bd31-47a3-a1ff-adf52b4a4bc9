// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdParameterProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the ad parameter resource.

// An ad parameter that is used to update numeric values (such as prices or
// inventory levels) in any text line of an ad (including URLs). There can
// be a maximum of two AdParameters per ad group criterion. (One with
// parameter_index = 1 and one with parameter_index = 2.)
// In the ad the parameters are referenced by a placeholder of the form
// "{param#:value}". For example, "{param1:$17}"
message AdParameter {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdParameter"
    pattern: "customers/{customer_id}/adParameters/{ad_group_id}~{criterion_id}~{parameter_index}"
  };

  // Immutable. The resource name of the ad parameter.
  // Ad parameter resource names have the form:
  //
  // `customers/{customer_id}/adParameters/{ad_group_id}~{criterion_id}~{parameter_index}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdParameter"
    }
  ];

  // Immutable. The ad group criterion that this ad parameter belongs to.
  optional string ad_group_criterion = 5 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupCriterion"
    }
  ];

  // Immutable. The unique index of this ad parameter. Must be either 1 or 2.
  optional int64 parameter_index = 6 [(google.api.field_behavior) = IMMUTABLE];

  // Numeric value to insert into the ad text. The following restrictions
  //  apply:
  //  - Can use comma or period as a separator, with an optional period or
  //    comma (respectively) for fractional values. For example, 1,000,000.00
  //    and 2.000.000,10 are valid.
  //  - Can be prepended or appended with a currency symbol. For example,
  //    $99.99 is valid.
  //  - Can be prepended or appended with a currency code. For example, 99.99USD
  //    and EUR200 are valid.
  //  - Can use '%'. For example, 1.0% and 1,0% are valid.
  //  - Can use plus or minus. For example, -10.99 and 25+ are valid.
  //  - Can use '/' between two numbers. For example 4/1 and 0.95/0.45 are
  //    valid.
  optional string insertion_text = 7;
}
