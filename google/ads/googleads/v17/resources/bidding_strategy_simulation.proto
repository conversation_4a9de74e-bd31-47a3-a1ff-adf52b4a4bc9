// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/simulation.proto";
import "google/ads/googleads/v17/enums/simulation_modification_method.proto";
import "google/ads/googleads/v17/enums/simulation_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "BiddingStrategySimulationProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the bidding strategy simulation resource.

// A bidding strategy simulation. Supported combinations of simulation type
// and simulation modification method are detailed below respectively.
//
// 1. TARGET_CPA - UNIFORM
// 2. TARGET_ROAS - UNIFORM
message BiddingStrategySimulation {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/BiddingStrategySimulation"
    pattern: "customers/{customer_id}/biddingStrategySimulations/{bidding_strategy_id}~{type}~{modification_method}~{start_date}~{end_date}"
  };

  // Output only. The resource name of the bidding strategy simulation.
  // Bidding strategy simulation resource names have the form:
  //
  // `customers/{customer_id}/biddingStrategySimulations/{bidding_strategy_id}~{type}~{modification_method}~{start_date}~{end_date}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BiddingStrategySimulation"
    }
  ];

  // Output only. Bidding strategy shared set id of the simulation.
  int64 bidding_strategy_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The field that the simulation modifies.
  google.ads.googleads.v17.enums.SimulationTypeEnum.SimulationType type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. How the simulation modifies the field.
  google.ads.googleads.v17.enums.SimulationModificationMethodEnum
      .SimulationModificationMethod modification_method = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. First day on which the simulation is based, in YYYY-MM-DD
  // format.
  string start_date = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last day on which the simulation is based, in YYYY-MM-DD
  // format
  string end_date = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of simulation points.
  oneof point_list {
    // Output only. Simulation points if the simulation type is TARGET_CPA.
    google.ads.googleads.v17.common.TargetCpaSimulationPointList
        target_cpa_point_list = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Simulation points if the simulation type is TARGET_ROAS.
    google.ads.googleads.v17.common.TargetRoasSimulationPointList
        target_roas_point_list = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
