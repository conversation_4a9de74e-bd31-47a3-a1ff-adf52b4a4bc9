// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/keyword_plan_network.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanCampaignProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the keyword plan campaign resource.

// A Keyword Plan campaign.
// Max number of keyword plan campaigns per plan allowed: 1.
message KeywordPlanCampaign {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/KeywordPlanCampaign"
    pattern: "customers/{customer_id}/keywordPlanCampaigns/{keyword_plan_campaign_id}"
  };

  // Immutable. The resource name of the Keyword Plan campaign.
  // KeywordPlanCampaign resource names have the form:
  //
  // `customers/{customer_id}/keywordPlanCampaigns/{kp_campaign_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/KeywordPlanCampaign"
    }
  ];

  // The keyword plan this campaign belongs to.
  optional string keyword_plan = 9 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/KeywordPlan"
  }];

  // Output only. The ID of the Keyword Plan campaign.
  optional int64 id = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the Keyword Plan campaign.
  //
  // This field is required and should not be empty when creating Keyword Plan
  // campaigns.
  optional string name = 11;

  // The languages targeted for the Keyword Plan campaign.
  // Max allowed: 1.
  repeated string language_constants = 12 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/LanguageConstant"
  }];

  // Targeting network.
  //
  // This field is required and should not be empty when creating Keyword Plan
  // campaigns.
  google.ads.googleads.v17.enums.KeywordPlanNetworkEnum.KeywordPlanNetwork
      keyword_plan_network = 6;

  // A default max cpc bid in micros, and in the account currency, for all ad
  // groups under the campaign.
  //
  // This field is required and should not be empty when creating Keyword Plan
  // campaigns.
  optional int64 cpc_bid_micros = 13;

  // The geo targets.
  // Max number allowed: 20.
  repeated KeywordPlanGeoTarget geo_targets = 8;
}

// A geo target.
message KeywordPlanGeoTarget {
  // Required. The resource name of the geo target.
  optional string geo_target_constant = 2 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/GeoTargetConstant"
  }];
}
