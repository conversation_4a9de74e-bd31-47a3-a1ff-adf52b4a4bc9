// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/custom_parameter.proto";
import "google/ads/googleads/v17/common/targeting_setting.proto";
import "google/ads/googleads/v17/enums/ad_group_ad_rotation_mode.proto";
import "google/ads/googleads/v17/enums/ad_group_primary_status.proto";
import "google/ads/googleads/v17/enums/ad_group_primary_status_reason.proto";
import "google/ads/googleads/v17/enums/ad_group_status.proto";
import "google/ads/googleads/v17/enums/ad_group_type.proto";
import "google/ads/googleads/v17/enums/asset_field_type.proto";
import "google/ads/googleads/v17/enums/asset_set_type.proto";
import "google/ads/googleads/v17/enums/bidding_source.proto";
import "google/ads/googleads/v17/enums/targeting_dimension.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the ad group resource.

// An ad group.
message AdGroup {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroup"
    pattern: "customers/{customer_id}/adGroups/{ad_group_id}"
  };

  // Settings for the audience targeting.
  message AudienceSetting {
    // Immutable. If true, this ad group uses an Audience resource for audience
    // targeting. If false, this ad group may use audience segment criteria
    // instead.
    bool use_audience_grouped = 1 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Immutable. The resource name of the ad group.
  // Ad group resource names have the form:
  //
  // `customers/{customer_id}/adGroups/{ad_group_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. The ID of the ad group.
  optional int64 id = 34 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the ad group.
  //
  // This field is required and should not be empty when creating new ad
  // groups.
  //
  // It must contain fewer than 255 UTF-8 full-width characters.
  //
  // It must not contain any null (code point 0x0), NL line feed
  // (code point 0xA) or carriage return (code point 0xD) characters.
  optional string name = 35;

  // The status of the ad group.
  google.ads.googleads.v17.enums.AdGroupStatusEnum.AdGroupStatus status = 5;

  // Immutable. The type of the ad group.
  google.ads.googleads.v17.enums.AdGroupTypeEnum.AdGroupType type = 12
      [(google.api.field_behavior) = IMMUTABLE];

  // The ad rotation mode of the ad group.
  google.ads.googleads.v17.enums.AdGroupAdRotationModeEnum.AdGroupAdRotationMode
      ad_rotation_mode = 22;

  // Output only. For draft or experiment ad groups, this field is the resource
  // name of the base ad group from which this ad group was created. If a draft
  // or experiment ad group does not have a base ad group, then this field is
  // null.
  //
  // For base ad groups, this field equals the ad group resource name.
  //
  // This field is read-only.
  optional string base_ad_group = 36 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // The URL template for constructing a tracking URL.
  optional string tracking_url_template = 37;

  // The list of mappings used to substitute custom parameter tags in a
  // `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
  repeated google.ads.googleads.v17.common.CustomParameter
      url_custom_parameters = 6;

  // Immutable. The campaign to which the ad group belongs.
  optional string campaign = 38 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // The maximum CPC (cost-per-click) bid.
  optional int64 cpc_bid_micros = 39;

  // Output only. Value will be same as that of the CPC (cost-per-click) bid
  // value when the bidding strategy is one of manual cpc, enhanced cpc, page
  // one promoted or target outrank share, otherwise the value will be null.
  optional int64 effective_cpc_bid_micros = 57
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The maximum CPM (cost-per-thousand viewable impressions) bid.
  optional int64 cpm_bid_micros = 40;

  // The target CPA (cost-per-acquisition). If the ad group's campaign
  // bidding strategy is TargetCpa or MaximizeConversions (with its target_cpa
  // field set), then this field overrides the target CPA specified in the
  // campaign's bidding strategy.
  // Otherwise, this value is ignored.
  optional int64 target_cpa_micros = 41;

  // The CPV (cost-per-view) bid.
  optional int64 cpv_bid_micros = 42;

  // Average amount in micros that the advertiser is willing to pay for every
  // thousand times the ad is shown.
  optional int64 target_cpm_micros = 43;

  // The target ROAS (return-on-ad-spend) override. If the ad group's campaign
  // bidding strategy is TargetRoas or MaximizeConversionValue (with its
  // target_roas field set), then this field overrides the target ROAS specified
  // in the campaign's bidding strategy.
  // Otherwise, this value is ignored.
  optional double target_roas = 44;

  // The percent cpc bid amount, expressed as a fraction of the advertised price
  // for some good or service. The valid range for the fraction is [0,1) and the
  // value stored here is 1,000,000 * [fraction].
  optional int64 percent_cpc_bid_micros = 45;

  // The fixed amount in micros that the advertiser pays for every thousand
  // impressions of the ad.
  optional int64 fixed_cpm_micros = 64;

  // Average amount in micros that the advertiser is willing to pay for every ad
  // view.
  optional int64 target_cpv_micros = 65;

  // True if optimized targeting is enabled. Optimized Targeting is the
  // replacement for Audience Expansion.
  bool optimized_targeting_enabled = 59;

  // Allows advertisers to specify a targeting dimension on which to place
  // absolute bids. This is only applicable for campaigns that target only the
  // display network and not search.
  google.ads.googleads.v17.enums.TargetingDimensionEnum.TargetingDimension
      display_custom_bid_dimension = 23;

  // URL template for appending params to Final URL.
  optional string final_url_suffix = 46;

  // Setting for targeting related features.
  google.ads.googleads.v17.common.TargetingSetting targeting_setting = 25;

  // Immutable. Setting for audience related features.
  AudienceSetting audience_setting = 56
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The effective target CPA (cost-per-acquisition).
  // This field is read-only.
  optional int64 effective_target_cpa_micros = 47
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of the effective target CPA.
  // This field is read-only.
  google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSource
      effective_target_cpa_source = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The effective target ROAS (return-on-ad-spend).
  // This field is read-only.
  optional double effective_target_roas = 48
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of the effective target ROAS.
  // This field is read-only.
  google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSource
      effective_target_roas_source = 32
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource names of labels attached to this ad group.
  repeated string labels = 49 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupLabel"
    }
  ];

  // The asset field types that should be excluded from this ad group. Asset
  // links with these field types will not be inherited by this ad group from
  // the upper levels.
  repeated google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldType
      excluded_parent_asset_field_types = 54;

  // The asset set types that should be excluded from this ad group. Asset set
  // links with these types will not be inherited by this ad group from the
  // upper levels.
  // Location group types (GMB_DYNAMIC_LOCATION_GROUP,
  // CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
  // LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
  // location group asset sets are not allowed to be linked to this ad group,
  // and all Location Extension (LE) and Affiliate Location Extensions (ALE)
  // will not be served under this ad group.
  // Only LOCATION_SYNC is currently supported.
  repeated google.ads.googleads.v17.enums.AssetSetTypeEnum.AssetSetType
      excluded_parent_asset_set_types = 58;

  // Output only. Provides aggregated view into why an ad group is not serving
  // or not serving optimally.
  google.ads.googleads.v17.enums.AdGroupPrimaryStatusEnum.AdGroupPrimaryStatus
      primary_status = 62 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides reasons for why an ad group is not serving or not
  // serving optimally.
  repeated google.ads.googleads.v17.enums.AdGroupPrimaryStatusReasonEnum
      .AdGroupPrimaryStatusReason primary_status_reasons = 63
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
