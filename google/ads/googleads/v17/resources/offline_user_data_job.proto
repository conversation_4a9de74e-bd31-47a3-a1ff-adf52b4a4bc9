// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/offline_user_data.proto";
import "google/ads/googleads/v17/enums/offline_user_data_job_failure_reason.proto";
import "google/ads/googleads/v17/enums/offline_user_data_job_match_rate_range.proto";
import "google/ads/googleads/v17/enums/offline_user_data_job_status.proto";
import "google/ads/googleads/v17/enums/offline_user_data_job_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "OfflineUserDataJobProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the offline user data job resource.

// A job containing offline user data of store visitors, or user list members
// that will be processed asynchronously. The uploaded data isn't readable and
// the processing results of the job can only be read using
// GoogleAdsService.Search/SearchStream.
message OfflineUserDataJob {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/OfflineUserDataJob"
    pattern: "customers/{customer_id}/offlineUserDataJobs/{offline_user_data_update_id}"
  };

  // Immutable. The resource name of the offline user data job.
  // Offline user data job resource names have the form:
  //
  // `customers/{customer_id}/offlineUserDataJobs/{offline_user_data_job_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/OfflineUserDataJob"
    }
  ];

  // Output only. ID of this offline user data job.
  optional int64 id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. User specified job ID.
  optional int64 external_id = 10 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Type of the job.
  google.ads.googleads.v17.enums.OfflineUserDataJobTypeEnum
      .OfflineUserDataJobType type = 4
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Status of the job.
  google.ads.googleads.v17.enums.OfflineUserDataJobStatusEnum
      .OfflineUserDataJobStatus status = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reason for the processing failure, if status is FAILED.
  google.ads.googleads.v17.enums.OfflineUserDataJobFailureReasonEnum
      .OfflineUserDataJobFailureReason failure_reason = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Metadata of offline user data job depicting match rate range.
  OfflineUserDataJobMetadata operation_metadata = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Metadata of the job.
  oneof metadata {
    // Immutable. Metadata for data updates to a CRM-based user list.
    google.ads.googleads.v17.common.CustomerMatchUserListMetadata
        customer_match_user_list_metadata = 7
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Metadata for store sales data update.
    google.ads.googleads.v17.common.StoreSalesMetadata store_sales_metadata = 8
        [(google.api.field_behavior) = IMMUTABLE];
  }
}

// Metadata of offline user data job.
message OfflineUserDataJobMetadata {
  // Output only. Match rate of the Customer Match user list upload. Describes
  // the estimated match rate when the status of the job is "RUNNING" and final
  // match rate when the final match rate is available after the status of the
  // job is "SUCCESS/FAILED".
  google.ads.googleads.v17.enums.OfflineUserDataJobMatchRateRangeEnum
      .OfflineUserDataJobMatchRateRange match_rate_range = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
