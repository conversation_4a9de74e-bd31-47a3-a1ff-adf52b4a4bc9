// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/matching_function.proto";
import "google/ads/googleads/v17/enums/feed_link_status.proto";
import "google/ads/googleads/v17/enums/placeholder_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignFeedProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the CampaignFeed resource.

// A campaign feed.
message CampaignFeed {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignFeed"
    pattern: "customers/{customer_id}/campaignFeeds/{campaign_id}~{feed_id}"
  };

  // Immutable. The resource name of the campaign feed.
  // Campaign feed resource names have the form:
  //
  // `customers/{customer_id}/campaignFeeds/{campaign_id}~{feed_id}
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignFeed"
    }
  ];

  // Immutable. The feed to which the CampaignFeed belongs.
  optional string feed = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Feed" }
  ];

  // Immutable. The campaign to which the CampaignFeed belongs.
  optional string campaign = 8 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Indicates which placeholder types the feed may populate under the connected
  // campaign. Required.
  repeated google.ads.googleads.v17.enums.PlaceholderTypeEnum.PlaceholderType
      placeholder_types = 4;

  // Matching function associated with the CampaignFeed.
  // The matching function is used to filter the set of feed items selected.
  // Required.
  google.ads.googleads.v17.common.MatchingFunction matching_function = 5;

  // Output only. Status of the campaign feed.
  // This field is read-only.
  google.ads.googleads.v17.enums.FeedLinkStatusEnum.FeedLinkStatus status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
