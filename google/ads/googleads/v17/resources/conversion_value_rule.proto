// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/conversion_value_rule_status.proto";
import "google/ads/googleads/v17/enums/value_rule_device_type.proto";
import "google/ads/googleads/v17/enums/value_rule_geo_location_match_type.proto";
import "google/ads/googleads/v17/enums/value_rule_operation.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ConversionValueRuleProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Conversion Value Rule resource.

// A conversion value rule
message ConversionValueRule {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ConversionValueRule"
    pattern: "customers/{customer_id}/conversionValueRules/{conversion_value_rule_id}"
  };

  // Action applied when rule is applied.
  message ValueRuleAction {
    // Specifies applied operation.
    google.ads.googleads.v17.enums.ValueRuleOperationEnum.ValueRuleOperation
        operation = 1;

    // Specifies applied value.
    double value = 2;
  }

  // Condition on Geo dimension.
  message ValueRuleGeoLocationCondition {
    // Geo locations that advertisers want to exclude.
    repeated string excluded_geo_target_constants = 1
        [(google.api.resource_reference) = {
          type: "googleads.googleapis.com/GeoTargetConstant"
        }];

    // Excluded Geo location match type.
    google.ads.googleads.v17.enums.ValueRuleGeoLocationMatchTypeEnum
        .ValueRuleGeoLocationMatchType excluded_geo_match_type = 2;

    // Geo locations that advertisers want to include.
    repeated string geo_target_constants = 3
        [(google.api.resource_reference) = {
          type: "googleads.googleapis.com/GeoTargetConstant"
        }];

    // Included Geo location match type.
    google.ads.googleads.v17.enums.ValueRuleGeoLocationMatchTypeEnum
        .ValueRuleGeoLocationMatchType geo_match_type = 4;
  }

  // Condition on Device dimension.
  message ValueRuleDeviceCondition {
    // Value for device type condition.
    repeated google.ads.googleads.v17.enums.ValueRuleDeviceTypeEnum
        .ValueRuleDeviceType device_types = 1;
  }

  // Condition on Audience dimension.
  message ValueRuleAudienceCondition {
    // User Lists.
    repeated string user_lists = 1 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserList"
    }];

    // User Interests.
    repeated string user_interests = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserInterest"
    }];
  }

  // Immutable. The resource name of the conversion value rule.
  // Conversion value rule resource names have the form:
  //
  // `customers/{customer_id}/conversionValueRules/{conversion_value_rule_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ConversionValueRule"
    }
  ];

  // Output only. The ID of the conversion value rule.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Action applied when the rule is triggered.
  ValueRuleAction action = 3;

  // Condition for Geo location that must be satisfied for the value rule to
  // apply.
  ValueRuleGeoLocationCondition geo_location_condition = 4;

  // Condition for device type that must be satisfied for the value rule to
  // apply.
  ValueRuleDeviceCondition device_condition = 5;

  // Condition for audience that must be satisfied for the value rule to apply.
  ValueRuleAudienceCondition audience_condition = 6;

  // Output only. The resource name of the conversion value rule's owner
  // customer. When the value rule is inherited from a manager customer,
  // owner_customer will be the resource name of the manager whereas the
  // customer in the resource_name will be of the requesting serving customer.
  // ** Read-only **
  string owner_customer = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];

  // The status of the conversion value rule.
  google.ads.googleads.v17.enums.ConversionValueRuleStatusEnum
      .ConversionValueRuleStatus status = 8;
}
