// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/criteria.proto";
import "google/ads/googleads/v17/enums/feed_item_target_device.proto";
import "google/ads/googleads/v17/enums/feed_item_target_status.proto";
import "google/ads/googleads/v17/enums/feed_item_target_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "FeedItemTargetProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the FeedItemTarget resource.

// A feed item target.
message FeedItemTarget {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/FeedItemTarget"
    pattern: "customers/{customer_id}/feedItemTargets/{feed_id}~{feed_item_id}~{feed_item_target_type}~{feed_item_target_id}"
  };

  // Immutable. The resource name of the feed item target.
  // Feed item target resource names have the form:
  // `customers/{customer_id}/feedItemTargets/{feed_id}~{feed_item_id}~{feed_item_target_type}~{feed_item_target_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/FeedItemTarget"
    }
  ];

  // Immutable. The feed item to which this feed item target belongs.
  optional string feed_item = 12 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/FeedItem"
    }
  ];

  // Output only. The target type of this feed item target. This field is
  // read-only.
  google.ads.googleads.v17.enums.FeedItemTargetTypeEnum.FeedItemTargetType
      feed_item_target_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the targeted resource. This field is read-only.
  optional int64 feed_item_target_id = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of the feed item target.
  // This field is read-only.
  google.ads.googleads.v17.enums.FeedItemTargetStatusEnum.FeedItemTargetStatus
      status = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The targeted resource.
  oneof target {
    // Immutable. The targeted campaign.
    string campaign = 14 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "googleads.googleapis.com/Campaign"
      }
    ];

    // Immutable. The targeted ad group.
    string ad_group = 15 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "googleads.googleapis.com/AdGroup"
      }
    ];

    // Immutable. The targeted keyword.
    google.ads.googleads.v17.common.KeywordInfo keyword = 7
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The targeted geo target constant resource name.
    string geo_target_constant = 16 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "googleads.googleapis.com/GeoTargetConstant"
      }
    ];

    // Immutable. The targeted device.
    google.ads.googleads.v17.enums.FeedItemTargetDeviceEnum.FeedItemTargetDevice
        device = 9 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The targeted schedule.
    google.ads.googleads.v17.common.AdScheduleInfo ad_schedule = 10
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
