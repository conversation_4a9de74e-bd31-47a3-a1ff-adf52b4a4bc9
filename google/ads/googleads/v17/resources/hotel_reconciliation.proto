// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/hotel_reconciliation_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "HotelReconciliationProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the hotel reconciliation resource.

// A hotel reconciliation. It contains conversion information from Hotel
// bookings to reconcile with advertiser records. These rows may be updated
// or canceled before billing through Bulk Uploads.
message HotelReconciliation {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/HotelReconciliation"
    pattern: "customers/{customer_id}/hotelReconciliations/{commission_id}"
  };

  // Immutable. The resource name of the hotel reconciliation.
  // Hotel reconciliation resource names have the form:
  //
  // `customers/{customer_id}/hotelReconciliations/{commission_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/HotelReconciliation"
    }
  ];

  // Required. Output only. The commission ID is Google's ID for this booking.
  // Every booking event is assigned a Commission ID to help you match it to a
  // guest stay.
  string commission_id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. The order ID is the identifier for this booking as provided in
  // the 'transaction_id' parameter of the conversion tracking tag.
  string order_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name for the Campaign associated with the
  // conversion.
  string campaign = 11 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. Identifier for the Hotel Center account which provides the
  // rates for the Hotel campaign.
  int64 hotel_center_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unique identifier for the booked property, as provided in the
  // Hotel Center feed. The hotel ID comes from the 'ID' parameter of the
  // conversion tracking tag.
  string hotel_id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Check-in date recorded when the booking is made. If the
  // check-in date is modified at reconciliation, the revised date will then
  // take the place of the original date in this column. Format is YYYY-MM-DD.
  string check_in_date = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Check-out date recorded when the booking is made. If the
  // check-in date is modified at reconciliation, the revised date will then
  // take the place of the original date in this column. Format is YYYY-MM-DD.
  string check_out_date = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Output only. Reconciled value is the final value of a booking as
  // paid by the guest. If original booking value changes for any reason, such
  // as itinerary changes or room upsells, the reconciled value should be the
  // full final amount collected. If a booking is canceled, the reconciled value
  // should include the value of any cancellation fees or non-refundable nights
  // charged. Value is in millionths of the base unit currency. For example,
  // $12.35 would be represented as 12350000. Currency unit is in the default
  // customer currency.
  int64 reconciled_value_micros = 8 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Whether a given booking has been billed. Once billed, a
  // booking can't be modified.
  bool billed = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Output only. Current status of a booking with regards to
  // reconciliation and billing. Bookings should be reconciled within 45 days
  // after the check-out date. Any booking not reconciled within 45 days will be
  // billed at its original value.
  google.ads.googleads.v17.enums.HotelReconciliationStatusEnum
      .HotelReconciliationStatus status = 10 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
}
