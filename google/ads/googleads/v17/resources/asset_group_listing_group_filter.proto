// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/listing_group_filter_custom_attribute_index.proto";
import "google/ads/googleads/v17/enums/listing_group_filter_listing_source.proto";
import "google/ads/googleads/v17/enums/listing_group_filter_product_category_level.proto";
import "google/ads/googleads/v17/enums/listing_group_filter_product_channel.proto";
import "google/ads/googleads/v17/enums/listing_group_filter_product_condition.proto";
import "google/ads/googleads/v17/enums/listing_group_filter_product_type_level.proto";
import "google/ads/googleads/v17/enums/listing_group_filter_type_enum.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupListingGroupFilterProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// AssetGroupListingGroupFilter represents a listing group filter tree node in
// an asset group.
message AssetGroupListingGroupFilter {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AssetGroupListingGroupFilter"
    pattern: "customers/{customer_id}/assetGroupListingGroupFilters/{asset_group_id}~{listing_group_filter_id}"
  };

  // Immutable. The resource name of the asset group listing group filter.
  // Asset group listing group filter resource name have the form:
  //
  // `customers/{customer_id}/assetGroupListingGroupFilters/{asset_group_id}~{listing_group_filter_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroupListingGroupFilter"
    }
  ];

  // Immutable. The asset group which this asset group listing group filter is
  // part of.
  string asset_group = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroup"
    }
  ];

  // Output only. The ID of the ListingGroupFilter.
  int64 id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Type of a listing group filter node.
  google.ads.googleads.v17.enums.ListingGroupFilterTypeEnum
      .ListingGroupFilterType type = 4
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The source of listings filtered by this listing group filter.
  google.ads.googleads.v17.enums.ListingGroupFilterListingSourceEnum
      .ListingGroupFilterListingSource listing_source = 9
      [(google.api.field_behavior) = IMMUTABLE];

  // Dimension value with which this listing group is refining its parent.
  // Undefined for the root group.
  ListingGroupFilterDimension case_value = 6;

  // Immutable. Resource name of the parent listing group subdivision. Null for
  // the root listing group filter node.
  string parent_listing_group_filter = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroupListingGroupFilter"
    }
  ];

  // Output only. The path of dimensions defining this listing group filter.
  ListingGroupFilterDimensionPath path = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The path defining of dimensions defining a listing group filter.
message ListingGroupFilterDimensionPath {
  // Output only. The complete path of dimensions through the listing group
  // filter hierarchy (excluding the root node) to this listing group filter.
  repeated ListingGroupFilterDimension dimensions = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Listing dimensions for the asset group listing group filter.
message ListingGroupFilterDimension {
  // One element of a category at a certain level. Top-level categories
  // are at level 1, their children at level 2, and so on. We currently support
  // up to 5 levels. The user must specify a dimension type that indicates the
  // level of the category. All cases of the same subdivision must have the same
  // dimension type (category level).
  message ProductCategory {
    // ID of the product category.
    //
    // This ID is equivalent to the google_product_category ID as described in
    // this article: https://support.google.com/merchants/answer/6324436
    optional int64 category_id = 1;

    // Indicates the level of the category in the taxonomy.
    google.ads.googleads.v17.enums.ListingGroupFilterProductCategoryLevelEnum
        .ListingGroupFilterProductCategoryLevel level = 2;
  }

  // Brand of the product.
  message ProductBrand {
    // String value of the product brand.
    optional string value = 1;
  }

  // Locality of a product offer.
  message ProductChannel {
    // Value of the locality.
    google.ads.googleads.v17.enums.ListingGroupFilterProductChannelEnum
        .ListingGroupFilterProductChannel channel = 1;
  }

  // Condition of a product offer.
  message ProductCondition {
    // Value of the condition.
    google.ads.googleads.v17.enums.ListingGroupFilterProductConditionEnum
        .ListingGroupFilterProductCondition condition = 1;
  }

  // Custom attribute of a product offer.
  message ProductCustomAttribute {
    // String value of the product custom attribute.
    optional string value = 1;

    // Indicates the index of the custom attribute.
    google.ads.googleads.v17.enums.ListingGroupFilterCustomAttributeIndexEnum
        .ListingGroupFilterCustomAttributeIndex index = 2;
  }

  // Item id of a product offer.
  message ProductItemId {
    // Value of the id.
    optional string value = 1;
  }

  // Type of a product offer.
  message ProductType {
    // Value of the type.
    optional string value = 1;

    // Level of the type.
    google.ads.googleads.v17.enums.ListingGroupFilterProductTypeLevelEnum
        .ListingGroupFilterProductTypeLevel level = 2;
  }

  // Filters for URLs in a page feed and URLs from the advertiser web domain.
  // Several root nodes with this dimension are allowed in an asset group and
  // their conditions are considered in OR.
  message Webpage {
    // The webpage conditions are case sensitive and these are and-ed together
    // when evaluated for filtering. All the conditions should be of same type.
    // Example1: for URL1 = www.ads.google.com?ocid=1&euid=2
    // and URL2 = www.ads.google.com?ocid=1
    // and with "ocid" and "euid" as url_contains conditions,
    // URL1 will be matched, but URL2 not.
    //
    // Example2 : If URL1 has Label1, Label2 and URL2 has Label2, Label3, then
    // with Label1 and Label2 as custom_label conditions, URL1 will be matched
    // but not URL2.
    // With Label2 as the only custom_label condition then both URL1 and URL2
    // will be matched.
    repeated WebpageCondition conditions = 1;
  }

  // Matching condition for URL filtering.
  message WebpageCondition {
    // Condition for filtering the URLs.
    oneof condition {
      // Filters the URLs in a page feed that have this custom label. A custom
      // label can be added to a campaign by creating an AssetSet of type
      // PAGE_FEED and linking it to the campaign using CampaignAssetSet.
      string custom_label = 1;

      // Filters the URLs in a page feed and the URLs from the advertiser web
      // domain that contain this string.
      string url_contains = 2;
    }
  }

  // Dimension of one of the types below is always present.
  oneof dimension {
    // Category of a product offer.
    ProductCategory product_category = 10;

    // Brand of a product offer.
    ProductBrand product_brand = 2;

    // Locality of a product offer.
    ProductChannel product_channel = 3;

    // Condition of a product offer.
    ProductCondition product_condition = 4;

    // Custom attribute of a product offer.
    ProductCustomAttribute product_custom_attribute = 5;

    // Item id of a product offer.
    ProductItemId product_item_id = 6;

    // Type of a product offer.
    ProductType product_type = 7;

    // Filters for URLs in a page feed and URLs from the advertiser web domain.
    Webpage webpage = 9;
  }
}
