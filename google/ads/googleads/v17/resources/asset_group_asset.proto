// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/asset_policy.proto";
import "google/ads/googleads/v17/common/policy_summary.proto";
import "google/ads/googleads/v17/enums/asset_field_type.proto";
import "google/ads/googleads/v17/enums/asset_link_primary_status.proto";
import "google/ads/googleads/v17/enums/asset_link_primary_status_reason.proto";
import "google/ads/googleads/v17/enums/asset_link_status.proto";
import "google/ads/googleads/v17/enums/asset_performance_label.proto";
import "google/ads/googleads/v17/enums/asset_source.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupAssetProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// AssetGroupAsset is the link between an asset and an asset group.
// Adding an AssetGroupAsset links an asset with an asset group.
message AssetGroupAsset {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AssetGroupAsset"
    pattern: "customers/{customer_id}/assetGroupAssets/{asset_group_id}~{asset_id}~{field_type}"
  };

  // Immutable. The resource name of the asset group asset.
  // Asset group asset resource name have the form:
  //
  // `customers/{customer_id}/assetGroupAssets/{asset_group_id}~{asset_id}~{field_type}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroupAsset"
    }
  ];

  // Immutable. The asset group which this asset group asset is linking.
  string asset_group = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroup"
    }
  ];

  // Immutable. The asset which this asset group asset is linking.
  string asset = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // The description of the placement of the asset within the asset group. For
  // example: HEADLINE, YOUTUBE_VIDEO etc
  google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldType field_type =
      4;

  // The status of the link between an asset and asset group.
  google.ads.googleads.v17.enums.AssetLinkStatusEnum.AssetLinkStatus status = 5;

  // Output only. Provides the PrimaryStatus of this asset link.
  // Primary status is meant essentially to differentiate between the plain
  // "status" field, which has advertiser set values of enabled, paused, or
  // removed.  The primary status takes into account other signals (for assets
  // its mainly policy and quality approvals) to come up with a more
  // comprehensive status to indicate its serving state.
  google.ads.googleads.v17.enums.AssetLinkPrimaryStatusEnum
      .AssetLinkPrimaryStatus primary_status = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides a list of reasons for why an asset is not serving or
  // not serving at full capacity.
  repeated google.ads.googleads.v17.enums.AssetLinkPrimaryStatusReasonEnum
      .AssetLinkPrimaryStatusReason primary_status_reasons = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides the details of the primary status and its associated
  // reasons.
  repeated google.ads.googleads.v17.common.AssetLinkPrimaryStatusDetails
      primary_status_details = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The performance of this asset group asset.
  google.ads.googleads.v17.enums.AssetPerformanceLabelEnum.AssetPerformanceLabel
      performance_label = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The policy information for this asset group asset.
  google.ads.googleads.v17.common.PolicySummary policy_summary = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source of the asset group asset.
  google.ads.googleads.v17.enums.AssetSourceEnum.AssetSource source = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
