// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CurrencyConstantProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Currency Constant resource.

// A currency constant.
message CurrencyConstant {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CurrencyConstant"
    pattern: "currencyConstants/{code}"
  };

  // Output only. The resource name of the currency constant.
  // Currency constant resource names have the form:
  //
  // `currencyConstants/{code}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CurrencyConstant"
    }
  ];

  // Output only. ISO 4217 three-letter currency code, for example, "USD"
  optional string code = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Full English name of the currency.
  optional string name = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Standard symbol for describing this currency, for example, '$'
  // for US Dollars.
  optional string symbol = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The billable unit for this currency. Billed amounts should be
  // multiples of this value.
  optional int64 billable_unit_micros = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
