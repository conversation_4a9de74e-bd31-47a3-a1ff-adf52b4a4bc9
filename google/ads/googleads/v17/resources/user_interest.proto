// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/common/criterion_category_availability.proto";
import "google/ads/googleads/v17/enums/user_interest_taxonomy_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "UserInterestProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the User Interest resource.

// A user interest: a particular interest-based vertical to be targeted.
message UserInterest {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/UserInterest"
    pattern: "customers/{customer_id}/userInterests/{user_interest_id}"
  };

  // Output only. The resource name of the user interest.
  // User interest resource names have the form:
  //
  // `customers/{customer_id}/userInterests/{user_interest_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserInterest"
    }
  ];

  // Output only. Taxonomy type of the user interest.
  google.ads.googleads.v17.enums.UserInterestTaxonomyTypeEnum
      .UserInterestTaxonomyType taxonomy_type = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the user interest.
  optional int64 user_interest_id = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the user interest.
  optional string name = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The parent of the user interest.
  optional string user_interest_parent = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/UserInterest"
    }
  ];

  // Output only. True if the user interest is launched to all channels and
  // locales.
  optional bool launched_to_all = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Availability information of the user interest.
  repeated google.ads.googleads.v17.common.CriterionCategoryAvailability
      availabilities = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
