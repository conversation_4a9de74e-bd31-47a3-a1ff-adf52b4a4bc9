// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "TargetingDimensionProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing criteria types.

// The dimensions that can be targeted.
message TargetingDimensionEnum {
  // Enum describing possible targeting dimensions.
  enum TargetingDimension {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Keyword criteria, for example, 'mars cruise'. KEYWORD may be used as a
    // custom bid dimension. Keywords are always a targeting dimension, so may
    // not be set as a target "ALL" dimension with TargetRestriction.
    KEYWORD = 2;

    // Audience criteria, which include user list, user interest, custom
    // affinity,  and custom in market.
    AUDIENCE = 3;

    // Topic criteria for targeting categories of content, for example,
    // 'category::Animals>Pets' Used for Display and Video targeting.
    TOPIC = 4;

    // Criteria for targeting gender.
    GENDER = 5;

    // Criteria for targeting age ranges.
    AGE_RANGE = 6;

    // Placement criteria, which include websites like 'www.flowers4sale.com',
    // as well as mobile applications, mobile app categories, YouTube videos,
    // and YouTube channels.
    PLACEMENT = 7;

    // Criteria for parental status targeting.
    PARENTAL_STATUS = 8;

    // Criteria for income range targeting.
    INCOME_RANGE = 9;
  }
}
