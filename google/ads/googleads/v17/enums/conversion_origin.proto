// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ConversionOriginProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing conversion origin.

// Container for enum describing possible conversion origins.
message ConversionOriginEnum {
  // The possible places where a conversion can occur.
  enum ConversionOrigin {
    // The conversion origin has not been specified.
    UNSPECIFIED = 0;

    // The conversion origin is not known in this version.
    UNKNOWN = 1;

    // Conversion that occurs when a user visits a website or takes an action
    // there after viewing an ad.
    WEBSITE = 2;

    // Conversions reported by an offline pipeline which collects local actions
    // from Google-hosted pages (for example, Google Maps, Google Place Page,
    // etc) and attributes them to relevant ad events.
    GOOGLE_HOSTED = 3;

    // Conversion that occurs when a user performs an action through any app
    // platforms.
    APP = 4;

    // Conversion that occurs when a user makes a call from ads.
    CALL_FROM_ADS = 5;

    // Conversion that occurs when a user visits or makes a purchase at a
    // physical store.
    STORE = 6;

    // Conversion that occurs on YouTube.
    YOUTUBE_HOSTED = 7;
  }
}
