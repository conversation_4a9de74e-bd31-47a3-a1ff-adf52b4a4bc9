// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ResourceLimitTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Container for enum describing possible resource limit types.
message ResourceLimitTypeEnum {
  // Resource limit type.
  enum ResourceLimitType {
    // No value has been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents an unclassified operation unknown
    // in this version.
    UNKNOWN = 1;

    // Number of ENABLED and PAUSED campaigns per customer.
    CAMPAIGNS_PER_CUSTOMER = 2;

    // Number of ENABLED and PAUSED base campaigns per customer.
    BASE_CAMPAIGNS_PER_CUSTOMER = 3;

    // Number of ENABLED and PAUSED experiment campaigns per customer.
    EXPERIMENT_CAMPAIGNS_PER_CUSTOMER = 105;

    // Number of ENABLED and PAUSED Hotel campaigns per customer.
    HOTEL_CAMPAIGNS_PER_CUSTOMER = 4;

    // Number of ENABLED and PAUSED Smart Shopping campaigns per customer.
    SMART_SHOPPING_CAMPAIGNS_PER_CUSTOMER = 5;

    // Number of ENABLED ad groups per campaign.
    AD_GROUPS_PER_CAMPAIGN = 6;

    // Number of ENABLED ad groups per Shopping campaign.
    AD_GROUPS_PER_SHOPPING_CAMPAIGN = 8;

    // Number of ENABLED ad groups per Hotel campaign.
    AD_GROUPS_PER_HOTEL_CAMPAIGN = 9;

    // Number of ENABLED reporting ad groups per local campaign.
    REPORTING_AD_GROUPS_PER_LOCAL_CAMPAIGN = 10;

    // Number of ENABLED reporting ad groups per App campaign. It includes app
    // campaign and app campaign for engagement.
    REPORTING_AD_GROUPS_PER_APP_CAMPAIGN = 11;

    // Number of ENABLED managed ad groups per smart campaign.
    MANAGED_AD_GROUPS_PER_SMART_CAMPAIGN = 52;

    // Number of ENABLED ad group criteria per customer.
    // An ad group criterion is considered as ENABLED if:
    // 1. it's not REMOVED
    // 2. its ad group is not REMOVED
    // 3. its campaign is not REMOVED.
    AD_GROUP_CRITERIA_PER_CUSTOMER = 12;

    // Number of ad group criteria across all base campaigns for a customer.
    BASE_AD_GROUP_CRITERIA_PER_CUSTOMER = 13;

    // Number of ad group criteria across all experiment campaigns for a
    // customer.
    EXPERIMENT_AD_GROUP_CRITERIA_PER_CUSTOMER = 107;

    // Number of ENABLED ad group criteria per campaign.
    // An ad group criterion is considered as ENABLED if:
    // 1. it's not REMOVED
    // 2. its ad group is not REMOVED.
    AD_GROUP_CRITERIA_PER_CAMPAIGN = 14;

    // Number of ENABLED campaign criteria per customer.
    CAMPAIGN_CRITERIA_PER_CUSTOMER = 15;

    // Number of ENABLED campaign criteria across all base campaigns for a
    // customer.
    BASE_CAMPAIGN_CRITERIA_PER_CUSTOMER = 16;

    // Number of ENABLED campaign criteria across all experiment campaigns for a
    // customer.
    EXPERIMENT_CAMPAIGN_CRITERIA_PER_CUSTOMER = 108;

    // Number of ENABLED webpage criteria per customer, including
    // campaign level and ad group level.
    WEBPAGE_CRITERIA_PER_CUSTOMER = 17;

    // Number of ENABLED webpage criteria across all base campaigns for
    // a customer.
    BASE_WEBPAGE_CRITERIA_PER_CUSTOMER = 18;

    // Meximum number of ENABLED webpage criteria across all experiment
    // campaigns for a customer.
    EXPERIMENT_WEBPAGE_CRITERIA_PER_CUSTOMER = 19;

    // Number of combined audience criteria per ad group.
    COMBINED_AUDIENCE_CRITERIA_PER_AD_GROUP = 20;

    // Limit for placement criterion type group in customer negative criterion.
    CUSTOMER_NEGATIVE_PLACEMENT_CRITERIA_PER_CUSTOMER = 21;

    // Limit for YouTube TV channels in customer negative criterion.
    CUSTOMER_NEGATIVE_YOUTUBE_CHANNEL_CRITERIA_PER_CUSTOMER = 22;

    // Number of ENABLED criteria per ad group.
    CRITERIA_PER_AD_GROUP = 23;

    // Number of listing group criteria per ad group.
    LISTING_GROUPS_PER_AD_GROUP = 24;

    // Number of ENABLED explicitly shared budgets per customer.
    EXPLICITLY_SHARED_BUDGETS_PER_CUSTOMER = 25;

    // Number of ENABLED implicitly shared budgets per customer.
    IMPLICITLY_SHARED_BUDGETS_PER_CUSTOMER = 26;

    // Number of combined audience criteria per campaign.
    COMBINED_AUDIENCE_CRITERIA_PER_CAMPAIGN = 27;

    // Number of negative keywords per campaign.
    NEGATIVE_KEYWORDS_PER_CAMPAIGN = 28;

    // Number of excluded campaign criteria in placement dimension, for example,
    // placement, mobile application, YouTube channel, etc. The API criterion
    // type is NOT limited to placement only, and this does not include
    // exclusions at the ad group or other levels.
    NEGATIVE_PLACEMENTS_PER_CAMPAIGN = 29;

    // Number of geo targets per campaign.
    GEO_TARGETS_PER_CAMPAIGN = 30;

    // Number of negative IP blocks per campaign.
    NEGATIVE_IP_BLOCKS_PER_CAMPAIGN = 32;

    // Number of proximity targets per campaign.
    PROXIMITIES_PER_CAMPAIGN = 33;

    // Number of listing scopes per Shopping campaign.
    LISTING_SCOPES_PER_SHOPPING_CAMPAIGN = 34;

    // Number of listing scopes per non-Shopping campaign.
    LISTING_SCOPES_PER_NON_SHOPPING_CAMPAIGN = 35;

    // Number of criteria per negative keyword shared set.
    NEGATIVE_KEYWORDS_PER_SHARED_SET = 36;

    // Number of criteria per negative placement shared set.
    NEGATIVE_PLACEMENTS_PER_SHARED_SET = 37;

    // Default number of shared sets allowed per type per customer.
    SHARED_SETS_PER_CUSTOMER_FOR_TYPE_DEFAULT = 40;

    // Number of shared sets of negative placement list type for a
    // manager customer.
    SHARED_SETS_PER_CUSTOMER_FOR_NEGATIVE_PLACEMENT_LIST_LOWER = 41;

    // Number of hotel_advance_booking_window bid modifiers per ad group.
    HOTEL_ADVANCE_BOOKING_WINDOW_BID_MODIFIERS_PER_AD_GROUP = 44;

    // Number of ENABLED shared bidding strategies per customer.
    BIDDING_STRATEGIES_PER_CUSTOMER = 45;

    // Number of open basic user lists per customer.
    BASIC_USER_LISTS_PER_CUSTOMER = 47;

    // Number of open logical user lists per customer.
    LOGICAL_USER_LISTS_PER_CUSTOMER = 48;

    // Number of open rule based user lists per customer.
    RULE_BASED_USER_LISTS_PER_CUSTOMER = 153;

    // Number of ENABLED and PAUSED ad group ads across all base campaigns for a
    // customer.
    BASE_AD_GROUP_ADS_PER_CUSTOMER = 53;

    // Number of ENABLED and PAUSED ad group ads across all experiment campaigns
    // for a customer.
    EXPERIMENT_AD_GROUP_ADS_PER_CUSTOMER = 54;

    // Number of ENABLED and PAUSED ad group ads per campaign.
    AD_GROUP_ADS_PER_CAMPAIGN = 55;

    // Number of ENABLED ads per ad group that do not fall in to other buckets.
    // Includes text and many other types.
    TEXT_AND_OTHER_ADS_PER_AD_GROUP = 56;

    // Number of ENABLED image ads per ad group.
    IMAGE_ADS_PER_AD_GROUP = 57;

    // Number of ENABLED shopping smart ads per ad group.
    SHOPPING_SMART_ADS_PER_AD_GROUP = 58;

    // Number of ENABLED responsive search ads per ad group.
    RESPONSIVE_SEARCH_ADS_PER_AD_GROUP = 59;

    // Number of ENABLED app ads per ad group.
    APP_ADS_PER_AD_GROUP = 60;

    // Number of ENABLED app engagement ads per ad group.
    APP_ENGAGEMENT_ADS_PER_AD_GROUP = 61;

    // Number of ENABLED local ads per ad group.
    LOCAL_ADS_PER_AD_GROUP = 62;

    // Number of ENABLED video ads per ad group.
    VIDEO_ADS_PER_AD_GROUP = 63;

    // Number of ENABLED lead form CampaignAssets per campaign.
    LEAD_FORM_CAMPAIGN_ASSETS_PER_CAMPAIGN = 143;

    // Number of ENABLED promotion CustomerAssets per customer.
    PROMOTION_CUSTOMER_ASSETS_PER_CUSTOMER = 79;

    // Number of ENABLED promotion CampaignAssets per campaign.
    PROMOTION_CAMPAIGN_ASSETS_PER_CAMPAIGN = 80;

    // Number of ENABLED promotion AdGroupAssets per ad group.
    PROMOTION_AD_GROUP_ASSETS_PER_AD_GROUP = 81;

    // Number of ENABLED callout CustomerAssets per customer.
    CALLOUT_CUSTOMER_ASSETS_PER_CUSTOMER = 134;

    // Number of ENABLED callout CampaignAssets per campaign.
    CALLOUT_CAMPAIGN_ASSETS_PER_CAMPAIGN = 135;

    // Number of ENABLED callout AdGroupAssets per ad group.
    CALLOUT_AD_GROUP_ASSETS_PER_AD_GROUP = 136;

    // Number of ENABLED sitelink CustomerAssets per customer.
    SITELINK_CUSTOMER_ASSETS_PER_CUSTOMER = 137;

    // Number of ENABLED sitelink CampaignAssets per campaign.
    SITELINK_CAMPAIGN_ASSETS_PER_CAMPAIGN = 138;

    // Number of ENABLED sitelink AdGroupAssets per ad group.
    SITELINK_AD_GROUP_ASSETS_PER_AD_GROUP = 139;

    // Number of ENABLED structured snippet CustomerAssets per customer.
    STRUCTURED_SNIPPET_CUSTOMER_ASSETS_PER_CUSTOMER = 140;

    // Number of ENABLED structured snippet CampaignAssets per campaign.
    STRUCTURED_SNIPPET_CAMPAIGN_ASSETS_PER_CAMPAIGN = 141;

    // Number of ENABLED structured snippet AdGroupAssets per ad group.
    STRUCTURED_SNIPPET_AD_GROUP_ASSETS_PER_AD_GROUP = 142;

    // Number of ENABLED mobile app CustomerAssets per customer.
    MOBILE_APP_CUSTOMER_ASSETS_PER_CUSTOMER = 144;

    // Number of ENABLED mobile app CampaignAssets per campaign.
    MOBILE_APP_CAMPAIGN_ASSETS_PER_CAMPAIGN = 145;

    // Number of ENABLED mobile app AdGroupAssets per ad group.
    MOBILE_APP_AD_GROUP_ASSETS_PER_AD_GROUP = 146;

    // Number of ENABLED hotel callout CustomerAssets per customer.
    HOTEL_CALLOUT_CUSTOMER_ASSETS_PER_CUSTOMER = 147;

    // Number of ENABLED hotel callout CampaignAssets per campaign.
    HOTEL_CALLOUT_CAMPAIGN_ASSETS_PER_CAMPAIGN = 148;

    // Number of ENABLED hotel callout AdGroupAssets per ad group.
    HOTEL_CALLOUT_AD_GROUP_ASSETS_PER_AD_GROUP = 149;

    // Number of ENABLED call CustomerAssets per customer.
    CALL_CUSTOMER_ASSETS_PER_CUSTOMER = 150;

    // Number of ENABLED call CampaignAssets per campaign.
    CALL_CAMPAIGN_ASSETS_PER_CAMPAIGN = 151;

    // Number of ENABLED call AdGroupAssets per ad group.
    CALL_AD_GROUP_ASSETS_PER_AD_GROUP = 152;

    // Number of ENABLED price CustomerAssets per customer.
    PRICE_CUSTOMER_ASSETS_PER_CUSTOMER = 154;

    // Number of ENABLED price CampaignAssets per campaign.
    PRICE_CAMPAIGN_ASSETS_PER_CAMPAIGN = 155;

    // Number of ENABLED price AdGroupAssets per ad group.
    PRICE_AD_GROUP_ASSETS_PER_AD_GROUP = 156;

    // Number of ENABLED ad image CampaignAssets per campaign.
    AD_IMAGE_CAMPAIGN_ASSETS_PER_CAMPAIGN = 175;

    // Number of ENABLED ad image AdGroupAssets per ad group.
    AD_IMAGE_AD_GROUP_ASSETS_PER_AD_GROUP = 176;

    // Number of ENABLED page feed asset sets per customer.
    PAGE_FEED_ASSET_SETS_PER_CUSTOMER = 157;

    // Number of ENABLED dynamic education feed asset sets per customer.
    DYNAMIC_EDUCATION_FEED_ASSET_SETS_PER_CUSTOMER = 158;

    // Number of ENABLED assets per page feed asset set.
    ASSETS_PER_PAGE_FEED_ASSET_SET = 159;

    // Number of ENABLED assets per dynamic education asset set.
    ASSETS_PER_DYNAMIC_EDUCATION_FEED_ASSET_SET = 160;

    // Number of ENABLED dynamic real estate asset sets per customer.
    DYNAMIC_REAL_ESTATE_ASSET_SETS_PER_CUSTOMER = 161;

    // Number of ENABLED assets per dynamic real estate asset set.
    ASSETS_PER_DYNAMIC_REAL_ESTATE_ASSET_SET = 162;

    // Number of ENABLED dynamic custom asset sets per customer.
    DYNAMIC_CUSTOM_ASSET_SETS_PER_CUSTOMER = 163;

    // Number of ENABLED assets per dynamic custom asset set.
    ASSETS_PER_DYNAMIC_CUSTOM_ASSET_SET = 164;

    // Number of ENABLED dynamic hotels and rentals asset sets per
    // customer.
    DYNAMIC_HOTELS_AND_RENTALS_ASSET_SETS_PER_CUSTOMER = 165;

    // Number of ENABLED assets per dynamic hotels and rentals asset set.
    ASSETS_PER_DYNAMIC_HOTELS_AND_RENTALS_ASSET_SET = 166;

    // Number of ENABLED dynamic local asset sets per customer.
    DYNAMIC_LOCAL_ASSET_SETS_PER_CUSTOMER = 167;

    // Number of ENABLED assets per dynamic local asset set.
    ASSETS_PER_DYNAMIC_LOCAL_ASSET_SET = 168;

    // Number of ENABLED dynamic flights asset sets per customer.
    DYNAMIC_FLIGHTS_ASSET_SETS_PER_CUSTOMER = 169;

    // Number of ENABLED assets per dynamic flights asset set.
    ASSETS_PER_DYNAMIC_FLIGHTS_ASSET_SET = 170;

    // Number of ENABLED dynamic travel asset sets per customer.
    DYNAMIC_TRAVEL_ASSET_SETS_PER_CUSTOMER = 171;

    // Number of ENABLED assets per dynamic travel asset set.
    ASSETS_PER_DYNAMIC_TRAVEL_ASSET_SET = 172;

    // Number of ENABLED dynamic jobs asset sets per customer.
    DYNAMIC_JOBS_ASSET_SETS_PER_CUSTOMER = 173;

    // Number of ENABLED assets per dynamic jobs asset set.
    ASSETS_PER_DYNAMIC_JOBS_ASSET_SET = 174;

    // Number of ENABLED business name CampaignAssets per campaign.
    BUSINESS_NAME_CAMPAIGN_ASSETS_PER_CAMPAIGN = 179;

    // Number of ENABLED business logo CampaignAssets per campaign.
    BUSINESS_LOGO_CAMPAIGN_ASSETS_PER_CAMPAIGN = 180;

    // Number of versions per ad.
    VERSIONS_PER_AD = 82;

    // Number of ENABLED user feeds per customer.
    USER_FEEDS_PER_CUSTOMER = 90;

    // Number of ENABLED system feeds per customer.
    SYSTEM_FEEDS_PER_CUSTOMER = 91;

    // Number of feed attributes per feed.
    FEED_ATTRIBUTES_PER_FEED = 92;

    // Number of ENABLED feed items per customer.
    FEED_ITEMS_PER_CUSTOMER = 94;

    // Number of ENABLED campaign feeds per customer.
    CAMPAIGN_FEEDS_PER_CUSTOMER = 95;

    // Number of ENABLED campaign feeds across all base campaigns for a
    // customer.
    BASE_CAMPAIGN_FEEDS_PER_CUSTOMER = 96;

    // Number of ENABLED campaign feeds across all experiment campaigns for a
    // customer.
    EXPERIMENT_CAMPAIGN_FEEDS_PER_CUSTOMER = 109;

    // Number of ENABLED ad group feeds per customer.
    AD_GROUP_FEEDS_PER_CUSTOMER = 97;

    // Number of ENABLED ad group feeds across all base campaigns for a
    // customer.
    BASE_AD_GROUP_FEEDS_PER_CUSTOMER = 98;

    // Number of ENABLED ad group feeds across all experiment campaigns for a
    // customer.
    EXPERIMENT_AD_GROUP_FEEDS_PER_CUSTOMER = 110;

    // Number of ENABLED ad group feeds per campaign.
    AD_GROUP_FEEDS_PER_CAMPAIGN = 99;

    // Number of ENABLED feed items per customer.
    FEED_ITEM_SETS_PER_CUSTOMER = 100;

    // Number of feed items per feed item set.
    FEED_ITEMS_PER_FEED_ITEM_SET = 101;

    // Number of ENABLED campaign experiments per customer.
    CAMPAIGN_EXPERIMENTS_PER_CUSTOMER = 112;

    // Number of video experiment arms per experiment.
    EXPERIMENT_ARMS_PER_VIDEO_EXPERIMENT = 113;

    // Number of owned labels per customer.
    OWNED_LABELS_PER_CUSTOMER = 115;

    // Number of applied labels per campaign.
    LABELS_PER_CAMPAIGN = 117;

    // Number of applied labels per ad group.
    LABELS_PER_AD_GROUP = 118;

    // Number of applied labels per ad group ad.
    LABELS_PER_AD_GROUP_AD = 119;

    // Number of applied labels per ad group criterion.
    LABELS_PER_AD_GROUP_CRITERION = 120;

    // Number of customers with a single label applied.
    TARGET_CUSTOMERS_PER_LABEL = 121;

    // Number of ENABLED keyword plans per user per customer.
    // The limit is applied per <user, customer> pair because by default a plan
    // is private to a user of a customer. Each user of a customer has their own
    // independent limit.
    KEYWORD_PLANS_PER_USER_PER_CUSTOMER = 122;

    // Number of keyword plan ad group keywords per keyword plan.
    KEYWORD_PLAN_AD_GROUP_KEYWORDS_PER_KEYWORD_PLAN = 123;

    // Number of keyword plan ad groups per keyword plan.
    KEYWORD_PLAN_AD_GROUPS_PER_KEYWORD_PLAN = 124;

    // Number of keyword plan negative keywords (both campaign and ad group) per
    // keyword plan.
    KEYWORD_PLAN_NEGATIVE_KEYWORDS_PER_KEYWORD_PLAN = 125;

    // Number of keyword plan campaigns per keyword plan.
    KEYWORD_PLAN_CAMPAIGNS_PER_KEYWORD_PLAN = 126;

    // Number of ENABLED conversion actions per customer.
    CONVERSION_ACTIONS_PER_CUSTOMER = 128;

    // Number of operations in a single batch job.
    BATCH_JOB_OPERATIONS_PER_JOB = 130;

    // Number of PENDING or ENABLED batch jobs per customer.
    BATCH_JOBS_PER_CUSTOMER = 131;

    // Number of hotel check-in date range bid modifiers per ad agroup.
    HOTEL_CHECK_IN_DATE_RANGE_BID_MODIFIERS_PER_AD_GROUP = 132;

    // Number of shared sets of type ACCOUNT_LEVEL_NEGATIVE_KEYWORDS per
    // account.
    SHARED_SETS_PER_ACCOUNT_FOR_ACCOUNT_LEVEL_NEGATIVE_KEYWORDS = 177;

    // Number of keywords per ACCOUNT_LEVEL_NEGATIVE_KEYWORDS shared set.
    ACCOUNT_LEVEL_NEGATIVE_KEYWORDS_PER_SHARED_SET = 178;

    // Maximum number of asset per hotel property asset set.
    ENABLED_ASSET_PER_HOTEL_PROPERTY_ASSET_SET = 181;

    // Maximum number of enabled hotel property assets per asset group.
    ENABLED_HOTEL_PROPERTY_ASSET_LINKS_PER_ASSET_GROUP = 182;

    // Number of criteria per brand shared set.
    BRANDS_PER_SHARED_SET = 183;

    // Number of active brand list criteria per campaign.
    ENABLED_BRAND_LIST_CRITERIA_PER_CAMPAIGN = 184;

    // Maximum number of shared sets of brand type for an account.
    SHARED_SETS_PER_ACCOUNT_FOR_BRAND = 185;

    // Maximum number of lookalike lists per customer.
    LOOKALIKE_USER_LISTS_PER_CUSTOMER = 186;
  }
}
