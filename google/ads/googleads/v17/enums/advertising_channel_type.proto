// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdvertisingChannelTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing advertising channel types

// The channel type a campaign may target to serve on.
message AdvertisingChannelTypeEnum {
  // Enum describing the various advertising channel types.
  enum AdvertisingChannelType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Search Network. Includes display bundled, and Search+ campaigns.
    SEARCH = 2;

    // Google Display Network only.
    DISPLAY = 3;

    // Shopping campaigns serve on the shopping property
    // and on google.com search results.
    SHOPPING = 4;

    // Hotel Ads campaigns.
    HOTEL = 5;

    // Video campaigns.
    VIDEO = 6;

    // App Campaigns, and App Campaigns for Engagement, that run
    // across multiple channels.
    MULTI_CHANNEL = 7;

    // Local ads campaigns.
    LOCAL = 8;

    // Smart campaigns.
    SMART = 9;

    // Performance Max campaigns.
    PERFORMANCE_MAX = 10;

    // Local services campaigns.
    LOCAL_SERVICES = 11;

    // Travel campaigns.
    TRAVEL = 13;

    // Demand Gen campaigns.
    DEMAND_GEN = 14;
  }
}
