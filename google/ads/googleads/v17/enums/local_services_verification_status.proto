// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesVerificationStatusProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing Local Services Ads granular verification statuses.

// Container for enum describing status of a particular Local Services Ads
// verification category.
message LocalServicesVerificationStatusEnum {
  // Enum describing status of a particular Local Services Ads verification
  // category.
  enum LocalServicesVerificationStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Unknown verification status.
    UNKNOWN = 1;

    // Verification has started, but has not finished.
    NEEDS_REVIEW = 2;

    // Verification has failed.
    FAILED = 3;

    // Verification has passed.
    PASSED = 4;

    // Verification is not applicable.
    NOT_APPLICABLE = 5;

    // Verification is required but pending submission.
    NO_SUBMISSION = 6;

    // Not all required verification has been submitted.
    PARTIAL_SUBMISSION = 7;

    // Verification needs review by Local Services Ads Ops Specialist.
    PENDING_ESCALATION = 8;
  }
}
