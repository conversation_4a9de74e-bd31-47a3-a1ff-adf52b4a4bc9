// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "NegativeGeoTargetTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing negative geo target types.

// Container for enum describing possible negative geo target types.
message NegativeGeoTargetTypeEnum {
  // The possible negative geo target types.
  enum NegativeGeoTargetType {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // Specifies that a user is excluded from seeing the ad if they
    // are in, or show interest in, advertiser's excluded locations.
    PRESENCE_OR_INTEREST = 4;

    // Specifies that a user is excluded from seeing the ad if they
    // are in advertiser's excluded locations.
    PRESENCE = 5;
  }
}
