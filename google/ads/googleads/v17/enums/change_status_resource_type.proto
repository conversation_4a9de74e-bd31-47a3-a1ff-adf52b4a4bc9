// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ChangeStatusResourceTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing the resource types the ChangeStatus resource supports.

// Container for enum describing supported resource types for the ChangeStatus
// resource.
message ChangeStatusResourceTypeEnum {
  // Enum listing the resource types support by the ChangeStatus resource.
  enum ChangeStatusResourceType {
    // No value has been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents an unclassified resource unknown
    // in this version.
    UNKNOWN = 1;

    // An AdGroup resource change.
    AD_GROUP = 3;

    // An AdGroupAd resource change.
    AD_GROUP_AD = 4;

    // An AdGroupCriterion resource change.
    AD_GROUP_CRITERION = 5;

    // A Campaign resource change.
    CAMPAIGN = 6;

    // A CampaignCriterion resource change.
    CAMPAIGN_CRITERION = 7;

    // A Feed resource change.
    FEED = 9;

    // A FeedItem resource change.
    FEED_ITEM = 10;

    // An AdGroupFeed resource change.
    AD_GROUP_FEED = 11;

    // A CampaignFeed resource change.
    CAMPAIGN_FEED = 12;

    // An AdGroupBidModifier resource change.
    AD_GROUP_BID_MODIFIER = 13;

    // A SharedSet resource change.
    SHARED_SET = 14;

    // A CampaignSharedSet resource change.
    CAMPAIGN_SHARED_SET = 15;

    // An Asset resource change.
    ASSET = 16;

    // A CustomerAsset resource change.
    CUSTOMER_ASSET = 17;

    // A CampaignAsset resource change.
    CAMPAIGN_ASSET = 18;

    // An AdGroupAsset resource change.
    AD_GROUP_ASSET = 19;

    // A CombinedAudience resource change.
    COMBINED_AUDIENCE = 20;

    // An AssetGroup resource change.
    ASSET_GROUP = 21;
  }
}
