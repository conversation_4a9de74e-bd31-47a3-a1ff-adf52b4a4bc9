// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SitelinkPlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing Sitelink placeholder fields.

// Values for Sitelink placeholder fields.
message SitelinkPlaceholderFieldEnum {
  // Possible values for Sitelink placeholder fields.
  enum SitelinkPlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: STRING. The link text for your sitelink.
    TEXT = 2;

    // Data Type: STRING. First line of the sitelink description.
    LINE_1 = 3;

    // Data Type: STRING. Second line of the sitelink description.
    LINE_2 = 4;

    // Data Type: URL_LIST. Final URLs for the sitelink when using Upgraded
    // URLs.
    FINAL_URLS = 5;

    // Data Type: URL_LIST. Final Mobile URLs for the sitelink when using
    // Upgraded URLs.
    FINAL_MOBILE_URLS = 6;

    // Data Type: URL. Tracking template for the sitelink when using Upgraded
    // URLs.
    TRACKING_URL = 7;

    // Data Type: STRING. Final URL suffix for sitelink when using parallel
    // tracking.
    FINAL_URL_SUFFIX = 8;
  }
}
