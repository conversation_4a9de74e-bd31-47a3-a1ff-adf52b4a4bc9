// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesInsuranceRejectionReasonProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Container for enum describing the rejection reason of a local services
// insurance verification artifact.
message LocalServicesInsuranceRejectionReasonEnum {
  // Enums describing possible rejection reasons of a local services insurance
  // verification artifact.
  enum LocalServicesInsuranceRejectionReason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Business name doesn't match business name for the Local Services Ad.
    BUSINESS_NAME_MISMATCH = 2;

    // Insurance amount doesn't meet requirement listed in the legal summaries
    // documentation for that geographic + category ID combination.
    INSURANCE_AMOUNT_INSUFFICIENT = 3;

    // Insurance document is expired.
    EXPIRED = 4;

    // Insurance document is missing a signature.
    NO_SIGNATURE = 5;

    // Insurance document is missing a policy number.
    NO_POLICY_NUMBER = 6;

    // Commercial General Liability(CGL) box is not marked in the insurance
    // document.
    NO_COMMERCIAL_GENERAL_LIABILITY = 7;

    // Insurance document is in an editable format.
    EDITABLE_FORMAT = 8;

    // Insurance document does not cover insurance for a particular category.
    CATEGORY_MISMATCH = 9;

    // Insurance document is missing an expiration date.
    MISSING_EXPIRATION_DATE = 10;

    // Insurance document is poor quality - blurry images, illegible, etc...
    POOR_QUALITY = 11;

    // Insurance document is suspected of being edited.
    POTENTIALLY_EDITED = 12;

    // Insurance document not accepted. For example, documents of insurance
    // proposals, but missing coverages are not accepted.
    WRONG_DOCUMENT_TYPE = 13;

    // Insurance document is not final.
    NON_FINAL = 14;

    // Insurance document has another flaw not listed explicitly.
    OTHER = 15;
  }
}
