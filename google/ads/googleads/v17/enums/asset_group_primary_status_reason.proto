// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupPrimaryStatusReasonProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing asset group primary statuses.

// Container for enum describing possible asset group primary status reasons.
message AssetGroupPrimaryStatusReasonEnum {
  // Enum describing the possible asset group primary status reasons. Provides
  // reasons into why an asset group is not serving or not serving optimally.
  // It will be empty when the asset group is serving without issues.
  enum AssetGroupPrimaryStatusReason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The user-specified asset group status is paused. Contributes to
    // AssetGroupPrimaryStatus.PAUSED
    ASSET_GROUP_PAUSED = 2;

    // The user-specified asset group status is removed. Contributes to
    // AssetGroupPrimaryStatus.REMOVED.
    ASSET_GROUP_REMOVED = 3;

    // The user-specified campaign status is removed. Contributes to
    // AssetGroupPrimaryStatus.NOT_ELIGIBLE.
    CAMPAIGN_REMOVED = 4;

    // The user-specified campaign status is paused. Contributes to
    // AssetGroupPrimaryStatus.NOT_ELIGIBLE.
    CAMPAIGN_PAUSED = 5;

    // The user-specified time for this campaign to start is in the future.
    // Contributes to AssetGroupPrimaryStatus.NOT_ELIGIBLE.
    CAMPAIGN_PENDING = 6;

    // The user-specified time for this campaign to end has passed. Contributes
    // to AssetGroupPrimaryStatus.NOT_ELIGIBLE.
    CAMPAIGN_ENDED = 7;

    // The asset group is approved but only serves in limited capacity due to
    // policies. Contributes to AssetGroupPrimaryStatus.LIMITED.
    ASSET_GROUP_LIMITED = 8;

    // The asset group has been marked as disapproved. Contributes to
    // AssetGroupPrimaryStatus.NOT_ELIGIBLE.
    ASSET_GROUP_DISAPPROVED = 9;

    // The asset group has not completed policy review. Contributes to
    // AssetGroupPrimaryStatus.PENDING.
    ASSET_GROUP_UNDER_REVIEW = 10;
  }
}
