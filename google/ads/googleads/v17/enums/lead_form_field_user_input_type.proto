// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LeadFormFieldUserInputTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Describes the input type of a lead form field.
message LeadFormFieldUserInputTypeEnum {
  // Enum describing the input type of a lead form field.
  enum LeadFormFieldUserInputType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The user will be asked to fill in their given and family name. This field
    // cannot be set at the same time as GIVEN_NAME or FAMILY_NAME.
    FULL_NAME = 2;

    // The user will be asked to fill in their email address.
    EMAIL = 3;

    // The user will be asked to fill in their phone number.
    PHONE_NUMBER = 4;

    // The user will be asked to fill in their zip code.
    POSTAL_CODE = 5;

    // The user will be asked to fill in their street address.
    STREET_ADDRESS = 8;

    // The user will be asked to fill in their city.
    CITY = 9;

    // The user will be asked to fill in their region part of the address (for
    // example, state for US, province for Canada).
    REGION = 10;

    // The user will be asked to fill in their country.
    COUNTRY = 11;

    // The user will be asked to fill in their work email address.
    WORK_EMAIL = 12;

    // The user will be asked to fill in their company name.
    COMPANY_NAME = 13;

    // The user will be asked to fill in their work phone.
    WORK_PHONE = 14;

    // The user will be asked to fill in their job title.
    JOB_TITLE = 15;

    // The user will be asked to fill in their CPF for Brazil users.
    GOVERNMENT_ISSUED_ID_CPF_BR = 16;

    // The user will be asked to fill in their DNI for Argentina users.
    GOVERNMENT_ISSUED_ID_DNI_AR = 17;

    // The user will be asked to fill in their DNI for Peru users.
    GOVERNMENT_ISSUED_ID_DNI_PE = 18;

    // The user will be asked to fill in their RUT for Chile users.
    GOVERNMENT_ISSUED_ID_RUT_CL = 19;

    // The user will be asked to fill in their CC for Colombia users.
    GOVERNMENT_ISSUED_ID_CC_CO = 20;

    // The user will be asked to fill in their CI for Ecuador users.
    GOVERNMENT_ISSUED_ID_CI_EC = 21;

    // The user will be asked to fill in their RFC for Mexico users.
    GOVERNMENT_ISSUED_ID_RFC_MX = 22;

    // The user will be asked to fill in their first name. This
    // field can not be set at the same time as FULL_NAME.
    FIRST_NAME = 23;

    // The user will be asked to fill in their last name. This
    // field can not be set at the same time as FULL_NAME.
    LAST_NAME = 24;

    // Question: "Which model are you interested in?"
    // Category: "Auto"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    VEHICLE_MODEL = 1001;

    // Question: "Which type of vehicle are you interested in?"
    // Category: "Auto"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    VEHICLE_TYPE = 1002;

    // Question: "What is your preferred dealership?"
    // Category: "Auto"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PREFERRED_DEALERSHIP = 1003;

    // Question: "When do you plan on purchasing a vehicle?"
    // Category: "Auto"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    VEHICLE_PURCHASE_TIMELINE = 1004;

    // Question: "Do you own a vehicle?"
    // Category: "Auto"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    VEHICLE_OWNERSHIP = 1005;

    // Question: "What vehicle ownership option are you interested in?"
    // Category: "Auto"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    VEHICLE_PAYMENT_TYPE = 1009;

    // Question: "What type of vehicle condition are you interested in?"
    // Category: "Auto"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    VEHICLE_CONDITION = 1010;

    // Question: "What size is your company?"
    // Category: "Business"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    COMPANY_SIZE = 1006;

    // Question: "What is your annual sales volume?"
    // Category: "Business"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    ANNUAL_SALES = 1007;

    // Question: "How many years have you been in business?"
    // Category: "Business"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    YEARS_IN_BUSINESS = 1008;

    // Question: "What is your job department?"
    // Category: "Business"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    JOB_DEPARTMENT = 1011;

    // Question: "What is your job role?"
    // Category: "Business"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    JOB_ROLE = 1012;

    // Question: "Are you over 18 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_18_AGE = 1078;

    // Question: "Are you over 19 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_19_AGE = 1079;

    // Question: "Are you over 20 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_20_AGE = 1080;

    // Question: "Are you over 21 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_21_AGE = 1081;

    // Question: "Are you over 22 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_22_AGE = 1082;

    // Question: "Are you over 23 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_23_AGE = 1083;

    // Question: "Are you over 24 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_24_AGE = 1084;

    // Question: "Are you over 25 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_25_AGE = 1085;

    // Question: "Are you over 26 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_26_AGE = 1086;

    // Question: "Are you over 27 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_27_AGE = 1087;

    // Question: "Are you over 28 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_28_AGE = 1088;

    // Question: "Are you over 29 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_29_AGE = 1089;

    // Question: "Are you over 30 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_30_AGE = 1090;

    // Question: "Are you over 31 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_31_AGE = 1091;

    // Question: "Are you over 32 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_32_AGE = 1092;

    // Question: "Are you over 33 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_33_AGE = 1093;

    // Question: "Are you over 34 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_34_AGE = 1094;

    // Question: "Are you over 35 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_35_AGE = 1095;

    // Question: "Are you over 36 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_36_AGE = 1096;

    // Question: "Are you over 37 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_37_AGE = 1097;

    // Question: "Are you over 38 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_38_AGE = 1098;

    // Question: "Are you over 39 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_39_AGE = 1099;

    // Question: "Are you over 40 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_40_AGE = 1100;

    // Question: "Are you over 41 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_41_AGE = 1101;

    // Question: "Are you over 42 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_42_AGE = 1102;

    // Question: "Are you over 43 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_43_AGE = 1103;

    // Question: "Are you over 44 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_44_AGE = 1104;

    // Question: "Are you over 45 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_45_AGE = 1105;

    // Question: "Are you over 46 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_46_AGE = 1106;

    // Question: "Are you over 47 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_47_AGE = 1107;

    // Question: "Are you over 48 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_48_AGE = 1108;

    // Question: "Are you over 49 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_49_AGE = 1109;

    // Question: "Are you over 50 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_50_AGE = 1110;

    // Question: "Are you over 51 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_51_AGE = 1111;

    // Question: "Are you over 52 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_52_AGE = 1112;

    // Question: "Are you over 53 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_53_AGE = 1113;

    // Question: "Are you over 54 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_54_AGE = 1114;

    // Question: "Are you over 55 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_55_AGE = 1115;

    // Question: "Are you over 56 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_56_AGE = 1116;

    // Question: "Are you over 57 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_57_AGE = 1117;

    // Question: "Are you over 58 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_58_AGE = 1118;

    // Question: "Are you over 59 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_59_AGE = 1119;

    // Question: "Are you over 60 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_60_AGE = 1120;

    // Question: "Are you over 61 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_61_AGE = 1121;

    // Question: "Are you over 62 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_62_AGE = 1122;

    // Question: "Are you over 63 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_63_AGE = 1123;

    // Question: "Are you over 64 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_64_AGE = 1124;

    // Question: "Are you over 65 years of age?"
    // Category: "Demographics"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OVER_65_AGE = 1125;

    // Question: "Which program are you interested in?"
    // Category: "Education"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    EDUCATION_PROGRAM = 1013;

    // Question: "Which course are you interested in?"
    // Category: "Education"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    EDUCATION_COURSE = 1014;

    // Question: "Which product are you interested in?"
    // Category: "General"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PRODUCT = 1016;

    // Question: "Which service are you interested in?"
    // Category: "General"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    SERVICE = 1017;

    // Question: "Which offer are you interested in?"
    // Category: "General"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    OFFER = 1018;

    // Question: "Which category are you interested in?"
    // Category: "General"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    CATEGORY = 1019;

    // Question: "What is your preferred method of contact?"
    // Category: "General"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PREFERRED_CONTACT_METHOD = 1020;

    // Question: "What is your preferred location?"
    // Category: "General"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PREFERRED_LOCATION = 1021;

    // Question: "What is the best time to contact you?"
    // Category: "General"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PREFERRED_CONTACT_TIME = 1022;

    // Question: "When are you looking to make a purchase?"
    // Category: "General"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PURCHASE_TIMELINE = 1023;

    // Question: "How many years of work experience do you have?"
    // Category: "Jobs"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    YEARS_OF_EXPERIENCE = 1048;

    // Question: "What industry do you work in?"
    // Category: "Jobs"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    JOB_INDUSTRY = 1049;

    // Question: "What is your highest level of education?"
    // Category: "Jobs"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    LEVEL_OF_EDUCATION = 1050;

    // Question: "What type of property are you looking for?"
    // Category: "Real Estate"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PROPERTY_TYPE = 1024;

    // Question: "What do you need a realtor's help with?"
    // Category: "Real Estate"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    REALTOR_HELP_GOAL = 1025;

    // Question: "What neighborhood are you interested in?"
    // Category: "Real Estate"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PROPERTY_COMMUNITY = 1026;

    // Question: "What price range are you looking for?"
    // Category: "Real Estate"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PRICE_RANGE = 1027;

    // Question: "How many bedrooms are you looking for?"
    // Category: "Real Estate"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    NUMBER_OF_BEDROOMS = 1028;

    // Question: "Are you looking for a fully furnished property?"
    // Category: "Real Estate"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    FURNISHED_PROPERTY = 1029;

    // Question: "Are you looking for properties that allow pets?"
    // Category: "Real Estate"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PETS_ALLOWED_PROPERTY = 1030;

    // Question: "What is the next product you plan to purchase?"
    // Category: "Retail"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    NEXT_PLANNED_PURCHASE = 1031;

    // Question: "Would you like to sign up for an event?"
    // Category: "Retail"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    EVENT_SIGNUP_INTEREST = 1033;

    // Question: "Where are you interested in shopping?"
    // Category: "Retail"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    PREFERRED_SHOPPING_PLACES = 1034;

    // Question: "What is your favorite brand?"
    // Category: "Retail"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    FAVORITE_BRAND = 1035;

    // Question: "Which type of valid commercial license do you have?"
    // Category: "Transportation"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    TRANSPORTATION_COMMERCIAL_LICENSE_TYPE = 1036;

    // Question: "Interested in booking an event?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    EVENT_BOOKING_INTEREST = 1038;

    // Question: "What is your destination country?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    DESTINATION_COUNTRY = 1039;

    // Question: "What is your destination city?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    DESTINATION_CITY = 1040;

    // Question: "What is your departure country?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    DEPARTURE_COUNTRY = 1041;

    // Question: "What is your departure city?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    DEPARTURE_CITY = 1042;

    // Question: "What is your departure date?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    DEPARTURE_DATE = 1043;

    // Question: "What is your return date?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    RETURN_DATE = 1044;

    // Question: "How many people are you traveling with?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    NUMBER_OF_TRAVELERS = 1045;

    // Question: "What is your travel budget?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    TRAVEL_BUDGET = 1046;

    // Question: "Where do you want to stay during your travel?"
    // Category: "Travel"
    // This field is subject to a limit of 5 qualifying questions per form and
    // cannot be used if values are set using custom_question_fields.
    TRAVEL_ACCOMMODATION = 1047;
  }
}
