// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "RecommendationTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing Recommendation types.

// Container for enum describing types of recommendations.
message RecommendationTypeEnum {
  // Types of recommendations.
  enum RecommendationType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Budget recommendation for campaigns that are currently budget-constrained
    // (as opposed to the FORECASTING_CAMPAIGN_BUDGET recommendation, which
    // applies to campaigns that are expected to become budget-constrained in
    // the future).
    CAMPAIGN_BUDGET = 2;

    // Keyword recommendation.
    KEYWORD = 3;

    // Recommendation to add a new text ad.
    TEXT_AD = 4;

    // Recommendation to update a campaign to use a Target CPA bidding strategy.
    TARGET_CPA_OPT_IN = 5;

    // Recommendation to update a campaign to use the Maximize Conversions
    // bidding strategy.
    MAXIMIZE_CONVERSIONS_OPT_IN = 6;

    // Recommendation to enable Enhanced Cost Per Click for a campaign.
    ENHANCED_CPC_OPT_IN = 7;

    // Recommendation to start showing your campaign's ads on Google Search
    // Partners Websites.
    SEARCH_PARTNERS_OPT_IN = 8;

    // Recommendation to update a campaign to use a Maximize Clicks bidding
    // strategy.
    MAXIMIZE_CLICKS_OPT_IN = 9;

    // Recommendation to start using the "Optimize" ad rotation setting for the
    // given ad group.
    OPTIMIZE_AD_ROTATION = 10;

    // Recommendation to change an existing keyword from one match type to a
    // broader match type.
    KEYWORD_MATCH_TYPE = 14;

    // Recommendation to move unused budget from one budget to a constrained
    // budget.
    MOVE_UNUSED_BUDGET = 15;

    // Budget recommendation for campaigns that are expected to become
    // budget-constrained in the future (as opposed to the CAMPAIGN_BUDGET
    // recommendation, which applies to campaigns that are currently
    // budget-constrained).
    FORECASTING_CAMPAIGN_BUDGET = 16;

    // Recommendation to update a campaign to use a Target ROAS bidding
    // strategy.
    TARGET_ROAS_OPT_IN = 17;

    // Recommendation to add a new responsive search ad.
    RESPONSIVE_SEARCH_AD = 18;

    // Budget recommendation for campaigns whose ROI is predicted to increase
    // with a budget adjustment.
    MARGINAL_ROI_CAMPAIGN_BUDGET = 19;

    // Recommendation to add broad match versions of keywords for fully
    // automated conversion-based bidding campaigns.
    USE_BROAD_MATCH_KEYWORD = 20;

    // Recommendation to add new responsive search ad assets.
    RESPONSIVE_SEARCH_AD_ASSET = 21;

    // Recommendation to upgrade a Smart Shopping campaign to a Performance Max
    // campaign.
    UPGRADE_SMART_SHOPPING_CAMPAIGN_TO_PERFORMANCE_MAX = 22;

    // Recommendation to improve strength of responsive search ad.
    RESPONSIVE_SEARCH_AD_IMPROVE_AD_STRENGTH = 23;

    // Recommendation to update a campaign to use Display Expansion.
    DISPLAY_EXPANSION_OPT_IN = 24;

    // Recommendation to upgrade a Local campaign to a Performance Max
    // campaign.
    UPGRADE_LOCAL_CAMPAIGN_TO_PERFORMANCE_MAX = 25;

    // Recommendation to raise target CPA when it is too low and there are very
    // few or no conversions.
    // It is applied asynchronously and can take minutes
    // depending on the number of ad groups there are in the related campaign.
    RAISE_TARGET_CPA_BID_TOO_LOW = 26;

    // Recommendation to raise the budget in advance of a seasonal event that is
    // forecasted to increase traffic, and change bidding strategy from maximize
    // conversion value to target ROAS.
    FORECASTING_SET_TARGET_ROAS = 27;

    // Recommendation to add callout assets to campaign or customer level.
    CALLOUT_ASSET = 28;

    // Recommendation to add sitelink assets to campaign or customer level.
    SITELINK_ASSET = 29;

    // Recommendation to add call assets to campaign or customer level.
    CALL_ASSET = 30;

    // Recommendation to add the age group attribute to offers that are
    // demoted because of a missing age group.
    SHOPPING_ADD_AGE_GROUP = 31;

    // Recommendation to add a color to offers that are demoted
    // because of a missing color.
    SHOPPING_ADD_COLOR = 32;

    // Recommendation to add a gender to offers that are demoted
    // because of a missing gender.
    SHOPPING_ADD_GENDER = 33;

    // Recommendation to add a GTIN (Global Trade Item Number) to offers
    // that are demoted because of a missing GTIN.
    SHOPPING_ADD_GTIN = 34;

    // Recommendation to add more identifiers to offers that are demoted because
    // of missing identifiers.
    SHOPPING_ADD_MORE_IDENTIFIERS = 35;

    // Recommendation to add the size to offers that are demoted
    // because of a missing size.
    SHOPPING_ADD_SIZE = 36;

    // Recommendation informing a customer about a campaign that cannot serve
    // because no products are being targeted.
    SHOPPING_ADD_PRODUCTS_TO_CAMPAIGN = 37;

    // The shopping recommendation informing a customer about campaign with a
    // high percentage of disapproved products.
    SHOPPING_FIX_DISAPPROVED_PRODUCTS = 38;

    // Recommendation to create a catch-all campaign that targets all offers.
    SHOPPING_TARGET_ALL_OFFERS = 39;

    // Recommendation to fix Merchant Center account suspension issues.
    SHOPPING_FIX_SUSPENDED_MERCHANT_CENTER_ACCOUNT = 40;

    // Recommendation to fix Merchant Center account suspension warning issues.
    SHOPPING_FIX_MERCHANT_CENTER_ACCOUNT_SUSPENSION_WARNING = 41;

    // Recommendation to migrate offers targeted by Regular Shopping Campaigns
    // to existing Performance Max campaigns.
    SHOPPING_MIGRATE_REGULAR_SHOPPING_CAMPAIGN_OFFERS_TO_PERFORMANCE_MAX = 42;

    // Recommendation to enable dynamic image extensions on the account,
    // allowing Google to find the best images from ad landing pages and
    // complement text ads.
    DYNAMIC_IMAGE_EXTENSION_OPT_IN = 43;

    // Recommendation to raise Target CPA based on Google predictions modeled
    // from past conversions. It is applied asynchronously and can take minutes
    // depending on the number of ad groups there are in the related campaign.
    RAISE_TARGET_CPA = 44;

    // Recommendation to lower Target ROAS.
    LOWER_TARGET_ROAS = 45;

    // Recommendation to opt into Performance Max campaigns.
    PERFORMANCE_MAX_OPT_IN = 46;

    // Recommendation to improve the asset group strength of a Performance Max
    // campaign to an "Excellent" rating.
    IMPROVE_PERFORMANCE_MAX_AD_STRENGTH = 47;

    // Recommendation to migrate Dynamic Search Ads to Performance Max
    // campaigns.
    MIGRATE_DYNAMIC_SEARCH_ADS_CAMPAIGN_TO_PERFORMANCE_MAX = 48;

    // Recommendation to set a target CPA for campaigns that do not have one
    // specified, in advance of a seasonal event that is forecasted to increase
    // traffic.
    FORECASTING_SET_TARGET_CPA = 49;

    // Recommendation to set a target CPA for campaigns that do not have one
    // specified.
    SET_TARGET_CPA = 50;

    // Recommendation to set a target ROAS for campaigns that do not have one
    // specified.
    SET_TARGET_ROAS = 51;

    // Recommendation to update a campaign to use the Maximize Conversion Value
    // bidding strategy.
    MAXIMIZE_CONVERSION_VALUE_OPT_IN = 52;

    // Recommendation to deploy Google Tag on more pages.
    IMPROVE_GOOGLE_TAG_COVERAGE = 53;

    // Recommendation to turn on Final URL expansion for your Performance Max
    // campaigns.
    PERFORMANCE_MAX_FINAL_URL_OPT_IN = 54;

    // Recommendation to update a customer list that hasn't been updated
    // in the last 90 days.
    REFRESH_CUSTOMER_MATCH_LIST = 55;

    // Recommendation to create a custom audience.
    CUSTOM_AUDIENCE_OPT_IN = 56;

    // Recommendation to add lead form assets to campaign or customer level.
    LEAD_FORM_ASSET = 57;

    // Recommendation to improve the strength of ads in
    // Demand Gen campaigns.
    IMPROVE_DEMAND_GEN_AD_STRENGTH = 58;
  }
}
