// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SharedSetTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing shared set types.

// Container for enum describing types of shared sets.
message SharedSetTypeEnum {
  // Enum listing the possible shared set types.
  enum SharedSetType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // A set of keywords that can be excluded from targeting.
    NEGATIVE_KEYWORDS = 2;

    // A set of placements that can be excluded from targeting.
    NEGATIVE_PLACEMENTS = 3;

    // An account-level set of keywords that can be excluded from targeting.
    ACCOUNT_LEVEL_NEGATIVE_KEYWORDS = 4;

    // A set of brands can be included or excluded from targeting.
    BRANDS = 5;
  }
}
