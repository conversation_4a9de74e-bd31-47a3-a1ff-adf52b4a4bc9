// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "UserListCustomerTypeCategoryProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing user list customer type category.

// The user list customer type categories.
message UserListCustomerTypeCategoryEnum {
  // Enum containing possible user list customer type categories.
  enum UserListCustomerTypeCategory {
    // Not specified.
    UNSPECIFIED = 0;

    // Unknown type.
    UNKNOWN = 1;

    // Customer type category for all customers.
    ALL_CUSTOMERS = 2;

    // Customer type category for all purchasers.
    PURCHASERS = 3;

    // Customer type category for high value purchasers.
    HIGH_VALUE_CUSTOMERS = 4;

    // Customer type category for disengaged purchasers.
    DISENGAGED_CUSTOMERS = 5;

    // Customer type category for qualified leads.
    QUALIFIED_LEADS = 6;

    // Customer type category for converted leads.
    CONVERTED_LEADS = 7;

    // Customer type category for paid subscribers.
    PAID_SUBSCRIBERS = 8;

    // Customer type category for loyalty signups.
    LOYALTY_SIGN_UPS = 9;

    // Customer type category for cart abandoners.
    CART_ABANDONERS = 10;

    // Customer type category for loyalty tier 1 members.
    LOYALTY_TIER_1_MEMBERS = 11;

    // Customer type category for loyalty tier 2 members.
    LOYALTY_TIER_2_MEMBERS = 12;

    // Customer type category for loyalty tier 3 members.
    LOYALTY_TIER_3_MEMBERS = 13;

    // Customer type category for loyalty tier 4 members.
    LOYALTY_TIER_4_MEMBERS = 14;

    // Customer type category for loyalty tier 5 members.
    LOYALTY_TIER_5_MEMBERS = 15;

    // Customer type category for loyalty tier 6 members.
    LOYALTY_TIER_6_MEMBERS = 16;

    // Customer type category for loyalty tier 7 members.
    LOYALTY_TIER_7_MEMBERS = 17;
  }
}
