// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AssetLinkPrimaryStatusProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing asset source.

// Provides the primary status of an asset link.
// For example: a sitelink may be paused for a particular campaign.
message AssetLinkPrimaryStatusEnum {
  // Enum Provides insight into why an asset is not serving or not serving
  // at full capacity for a particular link level. There could be one status
  // with multiple reasons. For example, a sitelink might be paused by the user,
  // but also limited in serving due to violation of an alcohol policy. In
  // this case, the PrimaryStatus will be returned as PAUSED, since the asset's
  // effective status is determined by its paused state.
  enum AssetLinkPrimaryStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The asset is eligible to serve.
    ELIGIBLE = 2;

    // The user-specified asset link status is paused.
    PAUSED = 3;

    // The user-specified asset link status is removed.
    REMOVED = 4;

    // The asset may serve in the future.
    PENDING = 5;

    // The asset is serving in a partial capacity.
    LIMITED = 6;

    // The asset is not eligible to serve.
    NOT_ELIGIBLE = 7;
  }
}
