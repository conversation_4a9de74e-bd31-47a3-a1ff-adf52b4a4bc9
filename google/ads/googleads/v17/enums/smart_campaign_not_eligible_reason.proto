// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SmartCampaignNotEligibleReasonProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing reasons for why a Smart campaign is not eligible to
// serve.

// A container for an enum that describes reasons for why a Smart campaign
// is not eligible to serve.
message SmartCampaignNotEligibleReasonEnum {
  // Reasons for why a Smart campaign is not eligible to serve.
  enum SmartCampaignNotEligibleReason {
    // The status has not been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // The campaign is not eligible to serve because of an issue with the
    // account.
    ACCOUNT_ISSUE = 2;

    // The campaign is not eligible to serve because of a payment issue.
    BILLING_ISSUE = 3;

    // The business profile location associated with the campaign has been
    // removed.
    BUSINESS_PROFILE_LOCATION_REMOVED = 4;

    // All system-generated ads have been disapproved. Consult the
    // policy_summary field in the AdGroupAd resource for more details.
    ALL_ADS_DISAPPROVED = 5;
  }
}
