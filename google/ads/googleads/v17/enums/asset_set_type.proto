// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AssetSetTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing asset set type.

// Container for enum describing possible types of an asset set.
message AssetSetTypeEnum {
  // Possible types of an asset set.
  enum AssetSetType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Page asset set.
    PAGE_FEED = 2;

    // Dynamic education asset set.
    DYNAMIC_EDUCATION = 3;

    // Google Merchant Center asset set.
    MERCHANT_CENTER_FEED = 4;

    // Dynamic real estate asset set.
    DYNAMIC_REAL_ESTATE = 5;

    // Dynamic custom asset set.
    DYNAMIC_CUSTOM = 6;

    // Dynamic hotels and rentals asset set.
    DYNAMIC_HOTELS_AND_RENTALS = 7;

    // Dynamic flights asset set.
    DYNAMIC_FLIGHTS = 8;

    // Dynamic travel asset set.
    DYNAMIC_TRAVEL = 9;

    // Dynamic local asset set.
    DYNAMIC_LOCAL = 10;

    // Dynamic jobs asset set.
    DYNAMIC_JOBS = 11;

    // Location sync level asset set.
    LOCATION_SYNC = 12;

    // Business Profile location group asset set.
    BUSINESS_PROFILE_DYNAMIC_LOCATION_GROUP = 13;

    // Chain location group asset set which can be used for both owned
    // locations and affiliate locations.
    CHAIN_DYNAMIC_LOCATION_GROUP = 14;

    // Static location group asset set which can be used for both owned
    // locations and affiliate locations.
    STATIC_LOCATION_GROUP = 15;

    // Hotel Property asset set which is used to link a hotel property feed to
    // Performance Max for travel goals campaigns.
    HOTEL_PROPERTY = 16;
  }
}
