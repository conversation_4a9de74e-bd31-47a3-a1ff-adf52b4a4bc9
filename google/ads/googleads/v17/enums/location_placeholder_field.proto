// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LocationPlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing Location placeholder fields.

// Values for Location placeholder fields.
message LocationPlaceholderFieldEnum {
  // Possible values for Location placeholder fields.
  enum LocationPlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: STRING. The name of the business.
    BUSINESS_NAME = 2;

    // Data Type: STRING. Line 1 of the business address.
    ADDRESS_LINE_1 = 3;

    // Data Type: STRING. Line 2 of the business address.
    ADDRESS_LINE_2 = 4;

    // Data Type: STRING. City of the business address.
    CITY = 5;

    // Data Type: STRING. Province of the business address.
    PROVINCE = 6;

    // Data Type: STRING. Postal code of the business address.
    POSTAL_CODE = 7;

    // Data Type: STRING. Country code of the business address.
    COUNTRY_CODE = 8;

    // Data Type: STRING. Phone number of the business.
    PHONE_NUMBER = 9;
  }
}
