// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "CampaignPrimaryStatusProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing Campaign primary statuses.

// Container for enum describing possible campaign primary status.
message CampaignPrimaryStatusEnum {
  // Enum describing the possible campaign primary status. Provides insight into
  // why a campaign is not serving or not serving optimally. Modification to the
  // campaign and its related entities might take a while to be reflected in
  // this status.
  enum CampaignPrimaryStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The campaign is eligible to serve.
    ELIGIBLE = 2;

    // The user-specified campaign status is paused.
    PAUSED = 3;

    // The user-specified campaign status is removed.
    REMOVED = 4;

    // The user-specified time for this campaign to end has passed.
    ENDED = 5;

    // The campaign may serve in the future.
    PENDING = 6;

    // The campaign or its associated entities have incorrect user-specified
    // settings.
    MISCONFIGURED = 7;

    // The campaign or its associated entities are limited by user-specified
    // settings.
    LIMITED = 8;

    // The automated bidding system is adjusting to user-specified changes to
    // the campaign or associated entities.
    LEARNING = 9;

    // The campaign is not eligible to serve.
    NOT_ELIGIBLE = 10;
  }
}
