// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdFormatTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing ad format types.

// Container for enumeration of Google Ads format types.
message AdFormatTypeEnum {
  // Enumerates Google Ads format types.
  //
  // Note that this segmentation is available only for Video and Discovery
  // campaigns. For assets, only video assets are supported.
  enum AdFormatType {
    // No value has been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Value assigned to formats (such as experimental formats) which don't
    // support format segmentation in Video and Discovery campaigns.
    //
    // Note that these formats may change categories in the future, for example
    // if an experimental format is exposed or a new format is added. We
    // strongly recommend to not rely on this field for long term decisions.
    OTHER = 2;

    // Value assigned for Video TrueView for Action campaigns statistics.
    //
    // Note that statistics with this value may change categories in the future,
    // for example if format segmentation support is added for new campaign
    // types. We strongly recommend to not rely on this field for long term
    // decisions.
    UNSEGMENTED = 3;

    // Skippable in-stream ads.
    INSTREAM_SKIPPABLE = 4;

    // Non-skippable in-stream ads.
    INSTREAM_NON_SKIPPABLE = 5;

    // In-feed YouTube or discovery image ads served on feed surfaces (e.g.
    // Discover Feed, YouTube Home, etc.).
    INFEED = 6;

    // Short (<7 secs) in-stream non-skippable YouTube ads.
    BUMPER = 7;

    // Outstream ads.
    OUTSTREAM = 8;

    // Masthead ads.
    MASTHEAD = 9;

    // Audio ads.
    AUDIO = 10;

    // Vertical full-screen video or discovery image ad served on YouTube Shorts
    // or BrandConnect ads served as organic YouTube Shorts.
    SHORTS = 11;
  }
}
