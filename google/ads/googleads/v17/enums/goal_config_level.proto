// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "GoalConfigLevelProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing goal config level.

// Container for enum describing possible goal config levels.
message GoalConfigLevelEnum {
  // The possible goal config levels. Campaigns automatically inherit the
  // effective conversion account's customer goals unless they have been
  // configured with their own set of campaign goals.
  enum GoalConfigLevel {
    // The goal config level has not been specified.
    UNSPECIFIED = 0;

    // The goal config level is not known in this version.
    UNKNOWN = 1;

    // The goal config is defined at the customer level.
    CUSTOMER = 2;

    // The goal config is defined at the campaign level.
    CAMPAIGN = 3;
  }
}
