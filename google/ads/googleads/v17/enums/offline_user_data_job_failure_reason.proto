// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "OfflineUserDataJobFailureReasonProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing offline user data job failure reasons.

// Container for enum describing reasons why an offline user data job
// failed to be processed.
message OfflineUserDataJobFailureReasonEnum {
  // The failure reason of an offline user data job.
  enum OfflineUserDataJobFailureReason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The matched transactions are insufficient.
    INSUFFICIENT_MATCHED_TRANSACTIONS = 2;

    // The uploaded transactions are insufficient.
    INSUFFICIENT_TRANSACTIONS = 3;

    // The average transaction value is unusually high for your account. If this
    //  is intended, contact support to request an exception. Learn more at
    //  https://support.google.com/google-ads/answer/********#transaction_value
    HIGH_AVERAGE_TRANSACTION_VALUE = 4;

    // The average transaction value is unusually low for your account. If this
    // is intended, contact support to request an exception. Learn more at
    // https://support.google.com/google-ads/answer/********#transaction_value
    LOW_AVERAGE_TRANSACTION_VALUE = 5;

    // There's a currency code that you haven't used before in your uploads. If
    // this is intended, contact support to request an exception. Learn more at
    // https://support.google.com/google-ads/answer/********#Unrecognized_currency
    NEWLY_OBSERVED_CURRENCY_CODE = 6;
  }
}
