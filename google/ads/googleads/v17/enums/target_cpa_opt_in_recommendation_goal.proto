// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "TargetCpaOptInRecommendationGoalProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing TargetCpaOptIn recommendation goals.

// Container for enum describing goals for TargetCpaOptIn recommendation.
message TargetCpaOptInRecommendationGoalEnum {
  // Goal of TargetCpaOptIn recommendation.
  enum TargetCpaOptInRecommendationGoal {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Recommendation to set Target CPA to maintain the same cost.
    SAME_COST = 2;

    // Recommendation to set Target CPA to maintain the same conversions.
    SAME_CONVERSIONS = 3;

    // Recommendation to set Target CPA to maintain the same CPA.
    SAME_CPA = 4;

    // Recommendation to set Target CPA to a value that is as close as possible
    // to, yet lower than, the actual CPA (computed for past 28 days).
    CLOSEST_CPA = 5;
  }
}
