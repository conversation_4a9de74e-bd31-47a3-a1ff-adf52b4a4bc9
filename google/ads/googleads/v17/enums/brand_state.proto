// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "BrandStateProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing approval status for the criterion.

// Container for enum describing possible brand states.
message BrandStateEnum {
  // Enumeration of different brand states.
  enum BrandState {
    // No value has been specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Brand is verified and globally available for selection
    ENABLED = 2;

    // Brand was globally available in past but is no longer a valid brand
    // (based on business criteria)
    DEPRECATED = 3;

    // Brand is unverified and customer scoped, but can be selected by customer
    // (only who requested for same) for targeting
    UNVERIFIED = 4;

    // Was a customer-scoped (unverified) brand, which got approved by business
    // and added to the global list. Its assigned CKG MID should be used instead
    // of this
    APPROVED = 5;

    // Was a customer-scoped (unverified) brand, but the request was canceled by
    // customer and this brand id is no longer valid
    CANCELLED = 6;

    // Was a customer-scoped (unverified) brand, but the request was rejected by
    // internal business team and this brand id is no longer valid
    REJECTED = 7;
  }
}
