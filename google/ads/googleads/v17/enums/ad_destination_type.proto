// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdDestinationTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing ad destination types.

// Container for enumeration of Google Ads destination types.
message AdDestinationTypeEnum {
  // Enumerates Google Ads destination types
  enum AdDestinationType {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // Ads that don't intend to drive users off from ads to other destinations
    NOT_APPLICABLE = 2;

    // Website
    WEBSITE = 3;

    // App Deep Link
    APP_DEEP_LINK = 4;

    // iOS App Store or Play Store
    APP_STORE = 5;

    // Call Dialer
    PHONE_CALL = 6;

    // Map App
    MAP_DIRECTIONS = 7;

    // Location Dedicated Page
    LOCATION_LISTING = 8;

    // Text Message
    MESSAGE = 9;

    // Lead Generation Form
    LEAD_FORM = 10;

    // YouTube
    YOUTUBE = 11;

    // Ad Destination for Conversions with keys unknown
    UNMODELED_FOR_CONVERSIONS = 12;
  }
}
