// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AssetOfflineEvaluationErrorReasonsProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing asset source.

// Provides the quality evaluation disapproval reason of an asset.
message AssetOfflineEvaluationErrorReasonsEnum {
  // Enum describing the quality evaluation disapproval reason of an asset.
  enum AssetOfflineEvaluationErrorReasons {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // One or more descriptions repeats its corresponding row header.
    PRICE_ASSET_DESCRIPTION_REPEATS_ROW_HEADER = 2;

    // Price asset contains repetitive headers.
    PRICE_ASSET_REPETITIVE_HEADERS = 3;

    // Price item header is not relevant to the price type.
    PRICE_ASSET_HEADER_INCOMPATIBLE_WITH_PRICE_TYPE = 4;

    // Price item description is not relevant to the item header.
    PRICE_ASSET_DESCRIPTION_INCOMPATIBLE_WITH_ITEM_HEADER = 5;

    // Price asset has a price qualifier in a description.
    PRICE_ASSET_DESCRIPTION_HAS_PRICE_QUALIFIER = 6;

    // Unsupported language for price assets
    PRICE_ASSET_UNSUPPORTED_LANGUAGE = 7;

    // Human raters identified an issue with the price asset that isn't captured
    // by other error reasons. The primary purpose of this value is to represent
    // legacy FeedItem disapprovals that are no longer produced.
    PRICE_ASSET_OTHER_ERROR = 8;
  }
}
