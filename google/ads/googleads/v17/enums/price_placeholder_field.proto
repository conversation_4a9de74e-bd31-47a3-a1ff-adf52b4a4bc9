// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "PricePlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing Price placeholder fields.

// Values for Price placeholder fields.
message PricePlaceholderFieldEnum {
  // Possible values for Price placeholder fields.
  enum PricePlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: STRING. The type of your price feed. Must match one of the
    // predefined price feed type exactly.
    TYPE = 2;

    // Data Type: STRING. The qualifier of each price. Must match one of the
    // predefined price qualifiers exactly.
    PRICE_QUALIFIER = 3;

    // Data Type: URL. Tracking template for the price feed when using Upgraded
    // URLs.
    TRACKING_TEMPLATE = 4;

    // Data Type: STRING. Language of the price feed. Must match one of the
    // available available locale codes exactly.
    LANGUAGE = 5;

    // Data Type: STRING. Final URL suffix for the price feed when using
    // parallel tracking.
    FINAL_URL_SUFFIX = 6;

    // Data Type: STRING. The header of item 1 of the table.
    ITEM_1_HEADER = 100;

    // Data Type: STRING. The description of item 1 of the table.
    ITEM_1_DESCRIPTION = 101;

    // Data Type: MONEY. The price (money with currency) of item 1 of the table,
    // for example, 30 USD. The currency must match one of the available
    // currencies.
    ITEM_1_PRICE = 102;

    // Data Type: STRING. The price unit of item 1 of the table. Must match one
    // of the predefined price units.
    ITEM_1_UNIT = 103;

    // Data Type: URL_LIST. The final URLs of item 1 of the table when using
    // Upgraded URLs.
    ITEM_1_FINAL_URLS = 104;

    // Data Type: URL_LIST. The final mobile URLs of item 1 of the table when
    // using Upgraded URLs.
    ITEM_1_FINAL_MOBILE_URLS = 105;

    // Data Type: STRING. The header of item 2 of the table.
    ITEM_2_HEADER = 200;

    // Data Type: STRING. The description of item 2 of the table.
    ITEM_2_DESCRIPTION = 201;

    // Data Type: MONEY. The price (money with currency) of item 2 of the table,
    // for example, 30 USD. The currency must match one of the available
    // currencies.
    ITEM_2_PRICE = 202;

    // Data Type: STRING. The price unit of item 2 of the table. Must match one
    // of the predefined price units.
    ITEM_2_UNIT = 203;

    // Data Type: URL_LIST. The final URLs of item 2 of the table when using
    // Upgraded URLs.
    ITEM_2_FINAL_URLS = 204;

    // Data Type: URL_LIST. The final mobile URLs of item 2 of the table when
    // using Upgraded URLs.
    ITEM_2_FINAL_MOBILE_URLS = 205;

    // Data Type: STRING. The header of item 3 of the table.
    ITEM_3_HEADER = 300;

    // Data Type: STRING. The description of item 3 of the table.
    ITEM_3_DESCRIPTION = 301;

    // Data Type: MONEY. The price (money with currency) of item 3 of the table,
    // for example, 30 USD. The currency must match one of the available
    // currencies.
    ITEM_3_PRICE = 302;

    // Data Type: STRING. The price unit of item 3 of the table. Must match one
    // of the predefined price units.
    ITEM_3_UNIT = 303;

    // Data Type: URL_LIST. The final URLs of item 3 of the table when using
    // Upgraded URLs.
    ITEM_3_FINAL_URLS = 304;

    // Data Type: URL_LIST. The final mobile URLs of item 3 of the table when
    // using Upgraded URLs.
    ITEM_3_FINAL_MOBILE_URLS = 305;

    // Data Type: STRING. The header of item 4 of the table.
    ITEM_4_HEADER = 400;

    // Data Type: STRING. The description of item 4 of the table.
    ITEM_4_DESCRIPTION = 401;

    // Data Type: MONEY. The price (money with currency) of item 4 of the table,
    // for example, 30 USD. The currency must match one of the available
    // currencies.
    ITEM_4_PRICE = 402;

    // Data Type: STRING. The price unit of item 4 of the table. Must match one
    // of the predefined price units.
    ITEM_4_UNIT = 403;

    // Data Type: URL_LIST. The final URLs of item 4 of the table when using
    // Upgraded URLs.
    ITEM_4_FINAL_URLS = 404;

    // Data Type: URL_LIST. The final mobile URLs of item 4 of the table when
    // using Upgraded URLs.
    ITEM_4_FINAL_MOBILE_URLS = 405;

    // Data Type: STRING. The header of item 5 of the table.
    ITEM_5_HEADER = 500;

    // Data Type: STRING. The description of item 5 of the table.
    ITEM_5_DESCRIPTION = 501;

    // Data Type: MONEY. The price (money with currency) of item 5 of the table,
    // for example, 30 USD. The currency must match one of the available
    // currencies.
    ITEM_5_PRICE = 502;

    // Data Type: STRING. The price unit of item 5 of the table. Must match one
    // of the predefined price units.
    ITEM_5_UNIT = 503;

    // Data Type: URL_LIST. The final URLs of item 5 of the table when using
    // Upgraded URLs.
    ITEM_5_FINAL_URLS = 504;

    // Data Type: URL_LIST. The final mobile URLs of item 5 of the table when
    // using Upgraded URLs.
    ITEM_5_FINAL_MOBILE_URLS = 505;

    // Data Type: STRING. The header of item 6 of the table.
    ITEM_6_HEADER = 600;

    // Data Type: STRING. The description of item 6 of the table.
    ITEM_6_DESCRIPTION = 601;

    // Data Type: MONEY. The price (money with currency) of item 6 of the table,
    // for example, 30 USD. The currency must match one of the available
    // currencies.
    ITEM_6_PRICE = 602;

    // Data Type: STRING. The price unit of item 6 of the table. Must match one
    // of the predefined price units.
    ITEM_6_UNIT = 603;

    // Data Type: URL_LIST. The final URLs of item 6 of the table when using
    // Upgraded URLs.
    ITEM_6_FINAL_URLS = 604;

    // Data Type: URL_LIST. The final mobile URLs of item 6 of the table when
    // using Upgraded URLs.
    ITEM_6_FINAL_MOBILE_URLS = 605;

    // Data Type: STRING. The header of item 7 of the table.
    ITEM_7_HEADER = 700;

    // Data Type: STRING. The description of item 7 of the table.
    ITEM_7_DESCRIPTION = 701;

    // Data Type: MONEY. The price (money with currency) of item 7 of the table,
    // for example, 30 USD. The currency must match one of the available
    // currencies.
    ITEM_7_PRICE = 702;

    // Data Type: STRING. The price unit of item 7 of the table. Must match one
    // of the predefined price units.
    ITEM_7_UNIT = 703;

    // Data Type: URL_LIST. The final URLs of item 7 of the table when using
    // Upgraded URLs.
    ITEM_7_FINAL_URLS = 704;

    // Data Type: URL_LIST. The final mobile URLs of item 7 of the table when
    // using Upgraded URLs.
    ITEM_7_FINAL_MOBILE_URLS = 705;

    // Data Type: STRING. The header of item 8 of the table.
    ITEM_8_HEADER = 800;

    // Data Type: STRING. The description of item 8 of the table.
    ITEM_8_DESCRIPTION = 801;

    // Data Type: MONEY. The price (money with currency) of item 8 of the table,
    // for example, 30 USD. The currency must match one of the available
    // currencies.
    ITEM_8_PRICE = 802;

    // Data Type: STRING. The price unit of item 8 of the table. Must match one
    // of the predefined price units.
    ITEM_8_UNIT = 803;

    // Data Type: URL_LIST. The final URLs of item 8 of the table when using
    // Upgraded URLs.
    ITEM_8_FINAL_URLS = 804;

    // Data Type: URL_LIST. The final mobile URLs of item 8 of the table when
    // using Upgraded URLs.
    ITEM_8_FINAL_MOBILE_URLS = 805;
  }
}
