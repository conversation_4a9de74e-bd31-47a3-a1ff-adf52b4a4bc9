// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AccountBudgetProposalStatusProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing AccountBudgetProposal statuses.

// Message describing AccountBudgetProposal statuses.
message AccountBudgetProposalStatusEnum {
  // The possible statuses of an AccountBudgetProposal.
  enum AccountBudgetProposalStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The proposal is pending approval.
    PENDING = 2;

    // The proposal has been approved but the corresponding billing setup
    // has not.  This can occur for proposals that set up the first budget
    // when signing up for billing or when performing a change of bill-to
    // operation.
    APPROVED_HELD = 3;

    // The proposal has been approved.
    APPROVED = 4;

    // The proposal has been cancelled by the user.
    CANCELLED = 5;

    // The proposal has been rejected by the user, for example, by rejecting an
    // acceptance email.
    REJECTED = 6;
  }
}
