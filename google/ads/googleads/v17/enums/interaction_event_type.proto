// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "InteractionEventTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing types of payable and free interactions.

// Container for enum describing types of payable and free interactions.
message InteractionEventTypeEnum {
  // Enum describing possible types of payable and free interactions.
  enum InteractionEventType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Click to site. In most cases, this interaction navigates to an external
    // location, usually the advertiser's landing page. This is also the default
    // InteractionEventType for click events.
    CLICK = 2;

    // The user's expressed intent to engage with the ad in-place.
    ENGAGEMENT = 3;

    // User viewed a video ad.
    VIDEO_VIEW = 4;

    // The default InteractionEventType for ad conversion events.
    // This is used when an ad conversion row does NOT indicate
    // that the free interactions (for example, the ad conversions)
    // should be 'promoted' and reported as part of the core metrics.
    // These are simply other (ad) conversions.
    NONE = 5;
  }
}
