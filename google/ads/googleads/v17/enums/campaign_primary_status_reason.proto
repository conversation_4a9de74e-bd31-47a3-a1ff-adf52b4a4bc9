// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "CampaignPrimaryStatusReasonProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing CampaignPrimaryStatusReasons.

// Container for enum describing possible campaign primary status reasons.
message CampaignPrimaryStatusReasonEnum {
  // Enum describing the possible campaign primary status reasons.  Provides
  // insight into why a campaign is not serving or not serving optimally. These
  // reasons are aggregated to determine an overall campaign primary status.
  enum CampaignPrimaryStatusReason {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The user-specified campaign status is removed.
    CAMPAIGN_REMOVED = 2;

    // The user-specified campaign status is paused.
    CAMPAIGN_PAUSED = 3;

    // The user-specified time for this campaign to start is in the future.
    CAMPAIGN_PENDING = 4;

    // The user-specified time for this campaign to end has passed.
    CAMPAIGN_ENDED = 5;

    // The campaign is a draft.
    CAMPAIGN_DRAFT = 6;

    // The bidding strategy has incorrect user-specified settings.
    BIDDING_STRATEGY_MISCONFIGURED = 7;

    // The bidding strategy is limited by user-specified settings such as lack
    // of data or similar.
    BIDDING_STRATEGY_LIMITED = 8;

    // The automated bidding system is adjusting to user-specified changes to
    // the bidding strategy.
    BIDDING_STRATEGY_LEARNING = 9;

    // Campaign could capture more conversion value by adjusting CPA/ROAS
    // targets.
    BIDDING_STRATEGY_CONSTRAINED = 10;

    // The budget is limiting the campaign's ability to serve.
    BUDGET_CONSTRAINED = 11;

    // The budget has incorrect user-specified settings.
    BUDGET_MISCONFIGURED = 12;

    // Campaign is not targeting all relevant queries.
    SEARCH_VOLUME_LIMITED = 13;

    // The user-specified ad group statuses are all paused.
    AD_GROUPS_PAUSED = 14;

    // No eligible ad groups exist in this campaign.
    NO_AD_GROUPS = 15;

    // The user-specified keyword statuses are all paused.
    KEYWORDS_PAUSED = 16;

    // No eligible keywords exist in this campaign.
    NO_KEYWORDS = 17;

    // The user-specified ad group ad statuses are all paused.
    AD_GROUP_ADS_PAUSED = 18;

    // No eligible ad group ads exist in this campaign.
    NO_AD_GROUP_ADS = 19;

    // At least one ad in this campaign is limited by policy.
    HAS_ADS_LIMITED_BY_POLICY = 20;

    // At least one ad in this campaign is disapproved.
    HAS_ADS_DISAPPROVED = 21;

    // Most ads in this campaign are pending review.
    MOST_ADS_UNDER_REVIEW = 22;

    // The campaign has a lead form goal, and the lead form extension is
    // missing.
    MISSING_LEAD_FORM_EXTENSION = 23;

    // The campaign has a call goal, and the call extension is missing.
    MISSING_CALL_EXTENSION = 24;

    // The lead form extension is under review.
    LEAD_FORM_EXTENSION_UNDER_REVIEW = 25;

    // The lead extension is disapproved.
    LEAD_FORM_EXTENSION_DISAPPROVED = 26;

    // The call extension is under review.
    CALL_EXTENSION_UNDER_REVIEW = 27;

    // The call extension is disapproved.
    CALL_EXTENSION_DISAPPROVED = 28;

    // No eligible mobile application ad group criteria exist in this campaign.
    NO_MOBILE_APPLICATION_AD_GROUP_CRITERIA = 29;

    // The user-specified campaign group status is paused.
    CAMPAIGN_GROUP_PAUSED = 30;

    // The user-specified times of all group budgets associated with the parent
    // campaign group has passed.
    CAMPAIGN_GROUP_ALL_GROUP_BUDGETS_ENDED = 31;

    // The app associated with this ACi campaign is not released in the target
    // countries of the campaign.
    APP_NOT_RELEASED = 32;

    // The app associated with this ACi campaign is partially released in the
    // target countries of the campaign.
    APP_PARTIALLY_RELEASED = 33;

    // At least one asset group in this campaign is disapproved.
    HAS_ASSET_GROUPS_DISAPPROVED = 34;

    // At least one asset group in this campaign is limited by policy.
    HAS_ASSET_GROUPS_LIMITED_BY_POLICY = 35;

    // Most asset groups in this campaign are pending review.
    MOST_ASSET_GROUPS_UNDER_REVIEW = 36;

    // No eligible asset groups exist in this campaign.
    NO_ASSET_GROUPS = 37;

    // All asset groups in this campaign are paused.
    ASSET_GROUPS_PAUSED = 38;
  }
}
