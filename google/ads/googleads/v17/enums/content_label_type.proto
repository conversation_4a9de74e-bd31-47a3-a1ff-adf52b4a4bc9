// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "ContentLabelTypeProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing content label types.

// Container for enum describing content label types in ContentLabel.
message ContentLabelTypeEnum {
  // Enum listing the content label types supported by ContentLabel criterion.
  enum ContentLabelType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Sexually suggestive content.
    SEXUALLY_SUGGESTIVE = 2;

    // Below the fold placement.
    BELOW_THE_FOLD = 3;

    // Parked domain.
    PARKED_DOMAIN = 4;

    // Juvenile, gross & bizarre content.
    JUVENILE = 6;

    // Profanity & rough language.
    PROFANITY = 7;

    // Death & tragedy.
    TRAGEDY = 8;

    // Video.
    VIDEO = 9;

    // Content rating: G.
    VIDEO_RATING_DV_G = 10;

    // Content rating: PG.
    VIDEO_RATING_DV_PG = 11;

    // Content rating: T.
    VIDEO_RATING_DV_T = 12;

    // Content rating: MA.
    VIDEO_RATING_DV_MA = 13;

    // Content rating: not yet rated.
    VIDEO_NOT_YET_RATED = 14;

    // Embedded video.
    EMBEDDED_VIDEO = 15;

    // Live streaming video.
    LIVE_STREAMING_VIDEO = 16;

    // Sensitive social issues.
    SOCIAL_ISSUES = 17;

    // Content that's suitable for families to view together, including Made for
    // Kids videos on YouTube.
    BRAND_SUITABILITY_CONTENT_FOR_FAMILIES = 18;

    // Video games that simulate hand-to-hand fighting or combat with the use of
    // modern or medieval weapons.
    BRAND_SUITABILITY_GAMES_FIGHTING = 19;

    // Video games that feature mature content, such as violence, inappropriate
    // language, or sexual suggestiveness.
    BRAND_SUITABILITY_GAMES_MATURE = 20;

    // Health content that people might find sensitive or upsetting, such as
    // medical procedures or images and descriptions of various medical
    // conditions.
    BRAND_SUITABILITY_HEALTH_SENSITIVE = 21;

    // Health content from sources that may provide accurate information but
    // aren't as commonly cited as other, more well-known sources.
    BRAND_SUITABILITY_HEALTH_SOURCE_UNDETERMINED = 22;

    // News content that's been recently announced, regardless of the themes or
    // people being reported on.
    BRAND_SUITABILITY_NEWS_RECENT = 23;

    // News content that people might find sensitive or upsetting, such as
    // crimes, accidents, and natural incidents, or commentary on potentially
    // controversial social and political issues.
    BRAND_SUITABILITY_NEWS_SENSITIVE = 24;

    // News content from sources that aren't featured on Google News or YouTube
    // News.
    BRAND_SUITABILITY_NEWS_SOURCE_NOT_FEATURED = 25;

    // Political content, such as political statements made by well-known
    // politicians, political elections, or events widely perceived to be
    // political in nature.
    BRAND_SUITABILITY_POLITICS = 26;

    // Content with religious themes, such as religious teachings or customs,
    // holy sites or places of worship, well-known religious figures or people
    // dressed in religious attire, or religious opinions on social and
    // political issues.
    BRAND_SUITABILITY_RELIGION = 27;
  }
}
