// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupSignalApprovalStatusProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing approval status for the signal.

// Container for enum describing possible AssetGroupSignal approval statuses.
// Details see https://support.google.com/google-ads/answer/2453978.
message AssetGroupSignalApprovalStatusEnum {
  // Enumerates AssetGroupSignal approval statuses, which are only used for
  // Search Theme Signal.
  enum AssetGroupSignalApprovalStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // Search Theme is eligible to show ads.
    APPROVED = 2;

    // Low search volume; Below first page bid estimate.
    LIMITED = 3;

    // Search Theme is inactive and isn't showing ads. A disapproved Search
    // Theme usually means there's an issue with one or more of our advertising
    // policies.
    DISAPPROVED = 4;

    // Search Theme is under review. It won’t be able to trigger ads until
    // it's been reviewed.
    UNDER_REVIEW = 5;
  }
}
