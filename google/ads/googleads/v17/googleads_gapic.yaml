type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  csharp:
    package_name: Google.Ads.GoogleAds.V17.Services
  go:
    package_name: google.golang.org/google/ads/googleads/v17/services
  java:
    package_name: com.google.ads.googleads.v17.services
  nodejs:
    package_name: v17.services
  php:
    package_name: Google\Ads\GoogleAds\V17\Services
  python:
    package_name: google.ads.googleads_v17.gapic.services
  ruby:
    package_name: Google::Ads::Googleads::V17::Services
interfaces:
- name: google.ads.googleads.v17.services.OfflineUserDataJobService
  methods:
  - name: RunOfflineUserDataJob
    long_running:
      initial_poll_delay_millis: 300000
      max_poll_delay_millis: 3600000
      poll_delay_multiplier: 1.25
      total_poll_timeout_millis: 43200000
