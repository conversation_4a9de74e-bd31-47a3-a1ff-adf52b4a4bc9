// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/resources/payments_account.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "PaymentsAccountServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the payments account service.

// Service to provide payments accounts that can be used to set up consolidated
// billing.
service PaymentsAccountService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns all payments accounts associated with all managers
  // between the login customer ID and specified serving customer in the
  // hierarchy, inclusive.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [PaymentsAccountError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc ListPaymentsAccounts(ListPaymentsAccountsRequest)
      returns (ListPaymentsAccountsResponse) {
    option (google.api.http) = {
      get: "/v17/customers/{customer_id=*}/paymentsAccounts"
    };
    option (google.api.method_signature) = "customer_id";
  }
}

// Request message for fetching all accessible payments accounts.
message ListPaymentsAccountsRequest {
  // Required. The ID of the customer to apply the PaymentsAccount list
  // operation to.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [PaymentsAccountService.ListPaymentsAccounts][google.ads.googleads.v17.services.PaymentsAccountService.ListPaymentsAccounts].
message ListPaymentsAccountsResponse {
  // The list of accessible payments accounts.
  repeated google.ads.googleads.v17.resources.PaymentsAccount
      payments_accounts = 1;
}
