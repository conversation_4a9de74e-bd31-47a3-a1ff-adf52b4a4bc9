// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/enums/product_link_invitation_status.proto";
import "google/ads/googleads/v17/resources/product_link_invitation.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "ProductLinkInvitationServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// This service allows management of product link invitations from Google Ads
// accounts to other accounts.
service ProductLinkInvitationService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates a product link invitation.
  rpc CreateProductLinkInvitation(CreateProductLinkInvitationRequest)
      returns (CreateProductLinkInvitationResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/productLinkInvitations:create"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,product_link_invitation";
  }

  // Update a product link invitation.
  rpc UpdateProductLinkInvitation(UpdateProductLinkInvitationRequest)
      returns (UpdateProductLinkInvitationResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/productLinkInvitations:update"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,product_link_invitation_status,resource_name";
  }

  // Remove a product link invitation.
  rpc RemoveProductLinkInvitation(RemoveProductLinkInvitationRequest)
      returns (RemoveProductLinkInvitationResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/productLinkInvitations:remove"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,resource_name";
  }
}

// Request message for
// [ProductLinkInvitationService.CreateProductLinkInvitation][google.ads.googleads.v17.services.ProductLinkInvitationService.CreateProductLinkInvitation].
message CreateProductLinkInvitationRequest {
  // Required. The ID of the customer being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The product link invitation to be created.
  google.ads.googleads.v17.resources.ProductLinkInvitation
      product_link_invitation = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for product link invitation create.
message CreateProductLinkInvitationResponse {
  // Resource name of the product link invitation.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ProductLinkInvitation"
  }];
}

// Request message for
// [ProductLinkInvitationService.UpdateProductLinkInvitation][google.ads.googleads.v17.services.ProductLinkInvitationService.UpdateProductLinkInvitation].
message UpdateProductLinkInvitationRequest {
  // Required. The ID of the customer being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The product link invitation to be created.
  google.ads.googleads.v17.enums.ProductLinkInvitationStatusEnum
      .ProductLinkInvitationStatus product_link_invitation_status = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. Resource name of the product link invitation.
  string resource_name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductLinkInvitation"
    }
  ];
}

// Response message for product link invitation update.
message UpdateProductLinkInvitationResponse {
  // Result of the update.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ProductLinkInvitation"
  }];
}

// Request message for
// [ProductLinkinvitationService.RemoveProductLinkInvitation][].
message RemoveProductLinkInvitationRequest {
  // Required. The ID of the product link invitation being removed.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the product link invitation being removed.
  // expected, in this format:
  //
  // ` `
  string resource_name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ProductLinkInvitation"
    }
  ];
}

// Response message for product link invitation removeal.
message RemoveProductLinkInvitationResponse {
  // Result for the remove request.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ProductLinkInvitation"
  }];
}
