// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/resources/keyword_theme_constant.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "KeywordThemeConstantServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the Smart Campaign keyword theme constant service.

// Service to fetch Smart Campaign keyword themes.
service KeywordThemeConstantService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns KeywordThemeConstant suggestions by keyword themes.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc SuggestKeywordThemeConstants(SuggestKeywordThemeConstantsRequest)
      returns (SuggestKeywordThemeConstantsResponse) {
    option (google.api.http) = {
      post: "/v17/keywordThemeConstants:suggest"
      body: "*"
    };
  }
}

// Request message for
// [KeywordThemeConstantService.SuggestKeywordThemeConstants][google.ads.googleads.v17.services.KeywordThemeConstantService.SuggestKeywordThemeConstants].
message SuggestKeywordThemeConstantsRequest {
  // The query text of a keyword theme that will be used to map to similar
  // keyword themes. For example, "plumber" or "roofer".
  string query_text = 1;

  // Upper-case, two-letter country code as defined by ISO-3166. This for
  // refining the scope of the query, default to 'US' if not set.
  string country_code = 2;

  // The two letter language code for get corresponding keyword theme for
  // refining the scope of the query, default to 'en' if not set.
  string language_code = 3;
}

// Response message for
// [KeywordThemeConstantService.SuggestKeywordThemeConstants][google.ads.googleads.v17.services.KeywordThemeConstantService.SuggestKeywordThemeConstants].
message SuggestKeywordThemeConstantsResponse {
  // Smart Campaign keyword theme suggestions.
  repeated google.ads.googleads.v17.resources.KeywordThemeConstant
      keyword_theme_constants = 1;
}
