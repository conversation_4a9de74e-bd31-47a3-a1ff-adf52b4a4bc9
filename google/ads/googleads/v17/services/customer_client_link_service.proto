// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/resources/customer_client_link.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerClientLinkServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Service to manage customer client links.
service CustomerClientLinkService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates or updates a customer client link. Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [ManagerLinkError]()
  //   [MutateError]()
  //   [NewResourceCreationError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc MutateCustomerClientLink(MutateCustomerClientLinkRequest)
      returns (MutateCustomerClientLinkResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/customerClientLinks:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }
}

// Request message for
// [CustomerClientLinkService.MutateCustomerClientLink][google.ads.googleads.v17.services.CustomerClientLinkService.MutateCustomerClientLink].
message MutateCustomerClientLinkRequest {
  // Required. The ID of the customer whose customer link are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform on the individual CustomerClientLink.
  CustomerClientLinkOperation operation = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// A single operation (create, update) on a CustomerClientLink.
message CustomerClientLinkOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new link.
    google.ads.googleads.v17.resources.CustomerClientLink create = 1;

    // Update operation: The link is expected to have a valid resource name.
    google.ads.googleads.v17.resources.CustomerClientLink update = 2;
  }
}

// Response message for a CustomerClientLink mutate.
message MutateCustomerClientLinkResponse {
  // A result that identifies the resource affected by the mutate request.
  MutateCustomerClientLinkResult result = 1;
}

// The result for a single customer client link mutate.
message MutateCustomerClientLinkResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomerClientLink"
  }];
}
