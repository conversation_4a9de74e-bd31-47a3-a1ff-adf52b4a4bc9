// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/enums/response_content_type.proto";
import "google/ads/googleads/v17/resources/feed_item_target.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "FeedItemTargetServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the FeedItemTarget service.

// Service to manage feed item targets.
service FeedItemTargetService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates or removes feed item targets. Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CriterionError]()
  //   [DatabaseError]()
  //   [DistinctError]()
  //   [FeedItemTargetError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [IdError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [NotEmptyError]()
  //   [OperatorError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  //   [SizeLimitError]()
  //   [StringFormatError]()
  //   [StringLengthError]()
  rpc MutateFeedItemTargets(MutateFeedItemTargetsRequest)
      returns (MutateFeedItemTargetsResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/feedItemTargets:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [FeedItemTargetService.MutateFeedItemTargets][google.ads.googleads.v17.services.FeedItemTargetService.MutateFeedItemTargets].
message MutateFeedItemTargetsRequest {
  // Required. The ID of the customer whose feed item targets are being
  // modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual feed item
  // targets.
  repeated FeedItemTargetOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 4;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v17.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 5;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;
}

// A single operation (create, remove) on an feed item target.
message FeedItemTargetOperation {
  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new feed item
    // target.
    google.ads.googleads.v17.resources.FeedItemTarget create = 1;

    // Remove operation: A resource name for the removed feed item target is
    // expected, in this format:
    //
    // `customers/{customer_id}/feedItemTargets/{feed_id}~{feed_item_id}~{feed_item_target_type}~{feed_item_target_id}`
    string remove = 2 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/FeedItemTarget"
    }];
  }
}

// Response message for an feed item target mutate.
message MutateFeedItemTargetsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 3;

  // All results for the mutate.
  repeated MutateFeedItemTargetResult results = 2;
}

// The result for the feed item target mutate.
message MutateFeedItemTargetResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/FeedItemTarget"
  }];

  // The mutated feed item target with only mutable fields after mutate. The
  // field will only be returned when response_content_type is set to
  // "MUTABLE_RESOURCE".
  google.ads.googleads.v17.resources.FeedItemTarget feed_item_target = 2;
}
