// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/resources/customer_sk_ad_network_conversion_value_schema.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerSkAdNetworkConversionValueSchemaServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the Customer Negative Criterion service.

// Service to manage CustomerSkAdNetworkConversionValueSchema.
service CustomerSkAdNetworkConversionValueSchemaService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates or updates the CustomerSkAdNetworkConversionValueSchema.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldError]()
  //   [InternalError]()
  //   [MutateError]()
  rpc MutateCustomerSkAdNetworkConversionValueSchema(
      MutateCustomerSkAdNetworkConversionValueSchemaRequest)
      returns (MutateCustomerSkAdNetworkConversionValueSchemaResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/customerSkAdNetworkConversionValueSchemas:mutate"
      body: "*"
    };
  }
}

// A single update operation for a CustomerSkAdNetworkConversionValueSchema.
message CustomerSkAdNetworkConversionValueSchemaOperation {
  // Update operation: The schema is expected to have a valid resource name.
  google.ads.googleads.v17.resources.CustomerSkAdNetworkConversionValueSchema
      update = 1;
}

// Request message for
// [CustomerSkAdNetworkConversionValueSchemaService.MutateCustomerSkAdNetworkConversionValueSchema][google.ads.googleads.v17.services.CustomerSkAdNetworkConversionValueSchemaService.MutateCustomerSkAdNetworkConversionValueSchema].
message MutateCustomerSkAdNetworkConversionValueSchemaRequest {
  // The ID of the customer whose shared sets are being modified.
  string customer_id = 1;

  // The operation to perform.
  CustomerSkAdNetworkConversionValueSchemaOperation operation = 2;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;

  // Optional. If true, enables returning warnings. Warnings return error
  // messages and error codes without blocking the execution of the mutate
  // operation.
  bool enable_warnings = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The result for the CustomerSkAdNetworkConversionValueSchema mutate.
message MutateCustomerSkAdNetworkConversionValueSchemaResult {
  // Resource name of the customer that was modified.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CustomerSkAdNetworkConversionValueSchema"
  }];

  // App ID of the SkanConversionValue modified.
  string app_id = 2;
}

// Response message for MutateCustomerSkAdNetworkConversionValueSchema.
message MutateCustomerSkAdNetworkConversionValueSchemaResponse {
  // All results for the mutate.
  MutateCustomerSkAdNetworkConversionValueSchemaResult result = 1;

  // Non blocking errors that provides schema validation failure details.
  // Returned only when enable_warnings = true.
  google.rpc.Status warning = 2;
}
