// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/enums/response_content_type.proto";
import "google/ads/googleads/v17/resources/asset_group_listing_group_filter.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "AssetGroupListingGroupFilterServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the AssetGroupListingGroupFilter service.

// Service to manage asset group listing group filter.
service AssetGroupListingGroupFilterService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, updates or removes asset group listing group filters. Operation
  // statuses are returned.
  rpc MutateAssetGroupListingGroupFilters(
      MutateAssetGroupListingGroupFiltersRequest)
      returns (MutateAssetGroupListingGroupFiltersResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/assetGroupListingGroupFilters:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [AssetGroupListingGroupFilterService.MutateAssetGroupListingGroupFilters][google.ads.googleads.v17.services.AssetGroupListingGroupFilterService.MutateAssetGroupListingGroupFilters].
// partial_failure is not supported because the tree needs to be validated
// together.
message MutateAssetGroupListingGroupFiltersRequest {
  // Required. The ID of the customer whose asset group listing group filters
  // are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual asset group
  // listing group filters.
  repeated AssetGroupListingGroupFilterOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 3;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v17.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 4;
}

// A single operation (create, remove) on an asset group listing group filter.
message AssetGroupListingGroupFilterOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new asset group
    // listing group filter.
    google.ads.googleads.v17.resources.AssetGroupListingGroupFilter create = 1;

    // Update operation: The asset group listing group filter is expected to
    // have a valid resource name.
    google.ads.googleads.v17.resources.AssetGroupListingGroupFilter update = 2;

    // Remove operation: A resource name for the removed asset group listing
    // group filter is expected, in this format:
    // `customers/{customer_id}/assetGroupListingGroupFilters/{asset_group_id}~{listing_group_filter_id}`
    // An entity can be removed only if it's not referenced by other
    // parent_listing_group_id. If multiple entities are being deleted, the
    // mutates must be in the correct order.
    string remove = 3 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroupListingGroupFilter"
    }];
  }
}

// Response message for an asset group listing group filter mutate.
message MutateAssetGroupListingGroupFiltersResponse {
  // All results for the mutate.
  repeated MutateAssetGroupListingGroupFilterResult results = 1;
}

// The result for the asset group listing group filter mutate.
message MutateAssetGroupListingGroupFilterResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/AssetGroupListingGroupFilter"
  }];

  // The mutated AssetGroupListingGroupFilter with only mutable fields after
  // mutate. The field will only be returned when response_content_type is set
  // to "MUTABLE_RESOURCE".
  google.ads.googleads.v17.resources.AssetGroupListingGroupFilter
      asset_group_listing_group_filter = 2;
}
