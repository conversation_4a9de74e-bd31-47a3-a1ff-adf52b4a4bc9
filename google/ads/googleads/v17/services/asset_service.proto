// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/enums/response_content_type.proto";
import "google/ads/googleads/v17/resources/asset.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "AssetServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the Asset service.

// Service to manage assets. Asset types can be created with AssetService are
// YoutubeVideoAsset, MediaBundleAsset and ImageAsset. TextAsset should be
// created with Ad inline.
service AssetService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates assets. Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AssetError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CollectionSizeError]()
  //   [CurrencyCodeError]()
  //   [DatabaseError]()
  //   [DateError]()
  //   [DistinctError]()
  //   [FieldError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [IdError]()
  //   [InternalError]()
  //   [ListOperationError]()
  //   [MediaUploadError]()
  //   [MutateError]()
  //   [NotAllowlistedError]()
  //   [NotEmptyError]()
  //   [OperatorError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  //   [SizeLimitError]()
  //   [StringFormatError]()
  //   [StringLengthError]()
  //   [UrlFieldError]()
  //   [YoutubeVideoRegistrationError]()
  rpc MutateAssets(MutateAssetsRequest) returns (MutateAssetsResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/assets:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }
}

// Request message for
// [AssetService.MutateAssets][google.ads.googleads.v17.services.AssetService.MutateAssets]
message MutateAssetsRequest {
  // Required. The ID of the customer whose assets are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual assets.
  repeated AssetOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 5;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v17.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;
}

// A single operation to create an asset. Supported asset types are
// YoutubeVideoAsset, MediaBundleAsset, ImageAsset, LeadFormAsset,
// LocationAsset, and ImageAsset. TextAsset can be created with an Ad inline,
// but it can also be created apart from an Ad like other assets.
message AssetOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 3;

  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new asset.
    google.ads.googleads.v17.resources.Asset create = 1;

    // Update operation: The asset is expected to have a valid resource name in
    // this format:
    //
    // `customers/{customer_id}/assets/{asset_id}`
    google.ads.googleads.v17.resources.Asset update = 2;
  }
}

// Response message for an asset mutate.
message MutateAssetsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 3;

  // All results for the mutate.
  repeated MutateAssetResult results = 2;
}

// The result for the asset mutate.
message MutateAssetResult {
  // The resource name returned for successful operations.
  string resource_name = 1 [
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // The mutated asset with only mutable fields after mutate. The field will
  // only be returned when response_content_type is set to "MUTABLE_RESOURCE".
  google.ads.googleads.v17.resources.Asset asset = 2;
}
