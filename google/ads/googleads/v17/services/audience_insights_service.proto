// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/common/criteria.proto";
import "google/ads/googleads/v17/common/dates.proto";
import "google/ads/googleads/v17/enums/audience_insights_dimension.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "AudienceInsightsServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the audience insights service.

// Audience Insights Service helps users find information about groups of
// people and how they can be reached with Google Ads. Accessible to
// allowlisted customers only.
service AudienceInsightsService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates a saved report that can be viewed in the Insights Finder tool.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  rpc GenerateInsightsFinderReport(GenerateInsightsFinderReportRequest)
      returns (GenerateInsightsFinderReportResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}:generateInsightsFinderReport"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,baseline_audience,specific_audience";
  }

  // Searches for audience attributes that can be used to generate insights.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  rpc ListAudienceInsightsAttributes(ListAudienceInsightsAttributesRequest)
      returns (ListAudienceInsightsAttributesResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}:searchAudienceInsightsAttributes"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,dimensions,query_text";
  }

  // Lists date ranges for which audience insights data can be requested.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  rpc ListInsightsEligibleDates(ListInsightsEligibleDatesRequest)
      returns (ListInsightsEligibleDatesResponse) {
    option (google.api.http) = {
      post: "/v17/audienceInsights:listInsightsEligibleDates"
      body: "*"
    };
  }

  // Returns a collection of attributes that are represented in an audience of
  // interest, with metrics that compare each attribute's share of the audience
  // with its share of a baseline audience.
  //
  // List of thrown errors:
  //   [AudienceInsightsError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  rpc GenerateAudienceCompositionInsights(
      GenerateAudienceCompositionInsightsRequest)
      returns (GenerateAudienceCompositionInsightsResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}:generateAudienceCompositionInsights"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,audience,dimensions";
  }

  // Returns a collection of targeting insights (e.g. targetable audiences) that
  // are relevant to the requested audience.
  //
  // List of thrown errors:
  //   [AudienceInsightsError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  rpc GenerateSuggestedTargetingInsights(
      GenerateSuggestedTargetingInsightsRequest)
      returns (GenerateSuggestedTargetingInsightsResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}:generateSuggestedTargetingInsights"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,audience";
  }

  // Returns a collection of audience attributes along with estimates of the
  // overlap between their potential YouTube reach and that of a given input
  // attribute.
  //
  // List of thrown errors:
  //   [AudienceInsightsError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [FieldError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  rpc GenerateAudienceOverlapInsights(GenerateAudienceOverlapInsightsRequest)
      returns (GenerateAudienceOverlapInsightsResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}:generateAudienceOverlapInsights"
      body: "*"
    };
    option (google.api.method_signature) =
        "customer_id,country_location,primary_attribute,dimensions";
  }
}

// Request message for
// [AudienceInsightsService.GenerateInsightsFinderReport][google.ads.googleads.v17.services.AudienceInsightsService.GenerateInsightsFinderReport].
message GenerateInsightsFinderReportRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A baseline audience for this report, typically all people in a
  // region.
  BasicInsightsAudience baseline_audience = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The specific audience of interest for this report.  The insights
  // in the report will be based on attributes more prevalent in this audience
  // than in the report's baseline audience.
  BasicInsightsAudience specific_audience = 3
      [(google.api.field_behavior) = REQUIRED];

  // The name of the customer being planned for.  This is a user-defined value.
  string customer_insights_group = 4;
}

// The response message for
// [AudienceInsightsService.GenerateInsightsFinderReport][google.ads.googleads.v17.services.AudienceInsightsService.GenerateInsightsFinderReport],
// containing the shareable URL for the report.
message GenerateInsightsFinderReportResponse {
  // An HTTPS URL providing a deep link into the Insights Finder UI with the
  // report inputs filled in according to the request.
  string saved_report_url = 1;
}

// Request message for
// [AudienceInsightsService.GenerateAudienceCompositionInsights][google.ads.googleads.v17.services.AudienceInsightsService.GenerateAudienceCompositionInsights].
message GenerateAudienceCompositionInsightsRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The audience of interest for which insights are being requested.
  InsightsAudience audience = 2 [(google.api.field_behavior) = REQUIRED];

  // The baseline audience to which the audience of interest is being
  // compared.
  InsightsAudience baseline_audience = 6;

  // The one-month range of historical data to use for insights, in the format
  // "yyyy-mm". If unset, insights will be returned for the last thirty days of
  // data.
  string data_month = 3;

  // Required. The audience dimensions for which composition insights should be
  // returned.
  repeated google.ads.googleads.v17.enums.AudienceInsightsDimensionEnum
      .AudienceInsightsDimension dimensions = 4
      [(google.api.field_behavior) = REQUIRED];

  // The name of the customer being planned for.  This is a user-defined value.
  string customer_insights_group = 5;
}

// Response message for
// [AudienceInsightsService.GenerateAudienceCompositionInsights][google.ads.googleads.v17.services.AudienceInsightsService.GenerateAudienceCompositionInsights].
message GenerateAudienceCompositionInsightsResponse {
  // The contents of the insights report, organized into sections.
  // Each section is associated with one of the AudienceInsightsDimension values
  // in the request. There may be more than one section per dimension.
  repeated AudienceCompositionSection sections = 1;
}

// Request message for
// [AudienceInsightsService.GenerateSuggestedTargetingInsights][google.ads.googleads.v17.services.AudienceInsightsService.GenerateSuggestedTargetingInsights].
message GenerateSuggestedTargetingInsightsRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The audience of interest for which insights are being requested.
  InsightsAudience audience = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The baseline audience.  The default, if unspecified, is all
  // people in the same country as the audience of interest.
  InsightsAudience baseline_audience = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The one-month range of historical data to use for insights, in
  // the format "yyyy-mm". If unset, insights will be returned for the last
  // thirty days of data.
  string data_month = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the customer being planned for.  This is a
  // user-defined value.
  string customer_insights_group = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [AudienceInsightsService.GenerateSuggestedTargetingInsights][google.ads.googleads.v17.services.AudienceInsightsService.GenerateSuggestedTargetingInsights].
message GenerateSuggestedTargetingInsightsResponse {
  // Suggested insights for targetable audiences.
  repeated TargetingSuggestionMetrics suggestions = 1;
}

// A suggested targetable audience relevant to the requested audience.
message TargetingSuggestionMetrics {
  // Suggested location targeting.  These attributes all have dimension
  // GEO_TARGET_COUNTRY or SUB_COUNTRY_LOCATION
  repeated AudienceInsightsAttributeMetadata locations = 1;

  // Suggested age targeting; may be empty indicating no age targeting.
  repeated google.ads.googleads.v17.common.AgeRangeInfo age_ranges = 2;

  // Suggested gender targeting.  If present, this attribute has dimension
  // GENDER.
  google.ads.googleads.v17.common.GenderInfo gender = 3;

  // Suggested audience segments to target.  These attributes all have dimension
  // AFFINITY_USER_INTEREST or IN_MARKET_USER_INTEREST
  repeated AudienceInsightsAttributeMetadata user_interests = 4;

  // The fraction (from 0 to 1 inclusive) of the requested audience that can be
  // reached using the suggested targeting.
  double coverage = 5;

  // The ratio of coverage to the coverage of the baseline audience or zero if
  // this ratio is undefined or is not meaningful.
  double index = 6;

  // The approximate estimated number of people that can be reached on YouTube
  // using this targeting.
  int64 potential_youtube_reach = 7;
}

// Request message for
// [AudienceInsightsService.ListAudienceInsightsAttributes][google.ads.googleads.v17.services.AudienceInsightsService.ListAudienceInsightsAttributes].
message ListAudienceInsightsAttributesRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The types of attributes to be returned.
  repeated google.ads.googleads.v17.enums.AudienceInsightsDimensionEnum
      .AudienceInsightsDimension dimensions = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. A free text query.  If the requested dimensions include
  // Attributes CATEGORY or KNOWLEDGE_GRAPH, then the attributes returned for
  // those dimensions will match or be related to this string.  For other
  // dimensions, this field is ignored and all available attributes are
  // returned.
  string query_text = 3 [(google.api.field_behavior) = REQUIRED];

  // The name of the customer being planned for.  This is a user-defined value.
  string customer_insights_group = 4;

  // If SUB_COUNTRY_LOCATION attributes are one of the requested dimensions and
  // this field is present, then the SUB_COUNTRY_LOCATION attributes returned
  // will be located in these countries. If this field is absent, then location
  // attributes are not filtered by country. Setting this field when
  // SUB_COUNTRY_LOCATION attributes are not requested will return an error.
  repeated google.ads.googleads.v17.common.LocationInfo
      location_country_filters = 5;

  // If present, potential YouTube reach estimates within the specified market
  // will be returned for attributes for which they are available.  Reach is
  // only available for the AGE_RANGE, GENDER, AFFINITY_USER_INTEREST and
  // IN_MARKET_USER_INTEREST dimensions, and may not be available for every
  // attribute of those dimensions in every market.
  google.ads.googleads.v17.common.LocationInfo youtube_reach_location = 6;
}

// Response message for
// [AudienceInsightsService.ListAudienceInsightsAttributes][google.ads.googleads.v17.services.AudienceInsightsService.ListAudienceInsightsAttributes].
message ListAudienceInsightsAttributesResponse {
  // The attributes matching the search query.
  repeated AudienceInsightsAttributeMetadata attributes = 1;
}

// Request message for
// [AudienceInsightsService.ListInsightsEligibleDates][google.ads.googleads.v17.services.AudienceInsightsService.ListInsightsEligibleDates].
message ListInsightsEligibleDatesRequest {}

// Response message for
// [AudienceInsightsService.ListInsightsEligibleDates][google.ads.googleads.v17.services.AudienceInsightsService.ListInsightsEligibleDates].
message ListInsightsEligibleDatesResponse {
  // The months for which AudienceInsights data is currently
  // available, each represented as a string in the form "YYYY-MM".
  repeated string data_months = 1;

  // The actual dates covered by the "last 30 days" date range that will be used
  // implicitly for
  // [AudienceInsightsService.GenerateAudienceCompositionInsights][google.ads.googleads.v17.services.AudienceInsightsService.GenerateAudienceCompositionInsights]
  // requests that have no data_month set.
  google.ads.googleads.v17.common.DateRange last_thirty_days = 2;
}

// Request message for
// [AudienceInsightsService.GenerateAudienceOverlapInsights][google.ads.googleads.v17.services.AudienceInsightsService.GenerateAudienceOverlapInsights].
message GenerateAudienceOverlapInsightsRequest {
  // Required. The ID of the customer.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The country in which to calculate the sizes and overlaps of
  // audiences.
  google.ads.googleads.v17.common.LocationInfo country_location = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The audience attribute that should be intersected with all other
  // eligible audiences.  This must be an Affinity or In-Market UserInterest, an
  // AgeRange or a Gender.
  AudienceInsightsAttribute primary_attribute = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. The types of attributes of which to calculate the overlap with
  // the primary_attribute. The values must be a subset of
  // AFFINITY_USER_INTEREST, IN_MARKET_USER_INTEREST, AGE_RANGE and GENDER.
  repeated google.ads.googleads.v17.enums.AudienceInsightsDimensionEnum
      .AudienceInsightsDimension dimensions = 4
      [(google.api.field_behavior) = REQUIRED];

  // The name of the customer being planned for.  This is a user-defined value.
  string customer_insights_group = 5;
}

// Response message for
// [AudienceInsightsService.GenerateAudienceOverlapInsights][google.ads.googleads.v17.services.AudienceInsightsService.GenerateAudienceOverlapInsights].
message GenerateAudienceOverlapInsightsResponse {
  // Metadata for the primary attribute, including potential YouTube reach.
  AudienceInsightsAttributeMetadata primary_attribute_metadata = 1;

  // Lists of attributes and their overlap with the primary attribute, one list
  // per requested dimension.
  repeated DimensionOverlapResult dimension_results = 2;
}

// A list of audience attributes of a single dimension, including their overlap
// with a primary attribute, returned as part of a
// [GenerateAudienceOverlapInsightsResponse][google.ads.googleads.v17.services.GenerateAudienceOverlapInsightsResponse].
message DimensionOverlapResult {
  // The dimension of all the attributes in this section.
  google.ads.googleads.v17.enums.AudienceInsightsDimensionEnum
      .AudienceInsightsDimension dimension = 1;

  // The attributes and their overlap with the primary attribute.
  repeated AudienceOverlapItem items = 2;
}

// An audience attribute, with metadata including the overlap between this
// attribute's potential YouTube reach and that of a primary attribute.
message AudienceOverlapItem {
  // The attribute and its metadata, including potential YouTube reach.
  AudienceInsightsAttributeMetadata attribute_metadata = 1;

  // The estimated size of the intersection of this audience attribute with the
  // primary attribute, that is, the number of reachable YouTube users who match
  // BOTH the primary attribute and this one.
  int64 potential_youtube_reach_intersection = 2;
}

// An audience attribute that can be used to request insights about the
// audience.
message AudienceInsightsAttribute {
  // An audience attribute.
  oneof attribute {
    // An audience attribute defined by an age range.
    google.ads.googleads.v17.common.AgeRangeInfo age_range = 1;

    // An audience attribute defined by a gender.
    google.ads.googleads.v17.common.GenderInfo gender = 2;

    // An audience attribute defined by a geographic location.
    google.ads.googleads.v17.common.LocationInfo location = 3;

    // An Affinity or In-Market audience.
    google.ads.googleads.v17.common.UserInterestInfo user_interest = 4;

    // An audience attribute defined by interest in a topic represented by a
    // Knowledge Graph entity.
    AudienceInsightsEntity entity = 5;

    // An audience attribute defined by interest in a Product & Service
    // category.
    AudienceInsightsCategory category = 6;

    // A YouTube Dynamic Lineup
    AudienceInsightsDynamicLineup dynamic_lineup = 7;

    // A Parental Status value (parent, or not a parent).
    google.ads.googleads.v17.common.ParentalStatusInfo parental_status = 8;

    // A household income percentile range.
    google.ads.googleads.v17.common.IncomeRangeInfo income_range = 9;

    // A YouTube channel.
    google.ads.googleads.v17.common.YouTubeChannelInfo youtube_channel = 10;
  }
}

// An entity or category representing a topic that defines an audience.
message AudienceInsightsTopic {
  // An entity or category attribute.
  oneof topic {
    // A Knowledge Graph entity
    AudienceInsightsEntity entity = 1;

    // A Product & Service category
    AudienceInsightsCategory category = 2;
  }
}

// A Knowledge Graph entity, represented by its machine id.
message AudienceInsightsEntity {
  // Required. The machine id (mid) of the Knowledge Graph entity.
  string knowledge_graph_machine_id = 1
      [(google.api.field_behavior) = REQUIRED];
}

// A Product and Service category.
message AudienceInsightsCategory {
  // Required. The criterion id of the category.
  string category_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// A YouTube Dynamic Lineup.
message AudienceInsightsDynamicLineup {
  // Required. The numeric ID of the dynamic lineup.
  string dynamic_lineup_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// A description of an audience used for requesting insights.
message BasicInsightsAudience {
  // Required. The countries for this audience.
  repeated google.ads.googleads.v17.common.LocationInfo country_location = 1
      [(google.api.field_behavior) = REQUIRED];

  // Sub-country geographic location attributes.  If present, each of these
  // must be contained in one of the countries in this audience.
  repeated google.ads.googleads.v17.common.LocationInfo sub_country_locations =
      2;

  // Gender for the audience.  If absent, the audience does not restrict by
  // gender.
  google.ads.googleads.v17.common.GenderInfo gender = 3;

  // Age ranges for the audience.  If absent, the audience represents all people
  // over 18 that match the other attributes.
  repeated google.ads.googleads.v17.common.AgeRangeInfo age_ranges = 4;

  // User interests defining this audience.  Affinity and In-Market audiences
  // are supported.
  repeated google.ads.googleads.v17.common.UserInterestInfo user_interests = 5;

  // Topics, represented by Knowledge Graph entities and/or Product & Service
  // categories, that this audience is interested in.
  repeated AudienceInsightsTopic topics = 6;
}

// An audience attribute, with metadata about it, returned in response to a
// search.
message AudienceInsightsAttributeMetadata {
  // The type of the attribute.
  google.ads.googleads.v17.enums.AudienceInsightsDimensionEnum
      .AudienceInsightsDimension dimension = 1;

  // The attribute itself.
  AudienceInsightsAttribute attribute = 2;

  // The human-readable name of the attribute.
  string display_name = 3;

  // A string that supplements the display_name to identify the attribute.
  // If the dimension is TOPIC, this is a brief description of the
  // Knowledge Graph entity, such as "American singer-songwriter".
  // If the dimension is CATEGORY, this is the complete path to the category in
  // The Product & Service taxonomy, for example
  // "/Apparel/Clothing/Outerwear".
  string display_info = 5;

  // An estimate of the number of reachable YouTube users matching this
  // attribute in the requested location, or zero if that information is not
  // available for this attribute.  Only populated in
  // GenerateAudienceOverlapInsightsResponses and in
  // ListAudienceInsightsAttributesResponses when youtube_reach_location is
  // present in the request.
  int64 potential_youtube_reach = 9;

  // Metadata specific to the dimension of this attribute.
  oneof dimension_metadata {
    // Special metadata for a YouTube channel.
    YouTubeChannelAttributeMetadata youtube_channel_metadata = 6;

    // Special metadata for a YouTube Dynamic Lineup.
    DynamicLineupAttributeMetadata dynamic_attribute_metadata = 7;

    // Special metadata for a Location.
    LocationAttributeMetadata location_attribute_metadata = 8;
  }
}

// Metadata associated with a YouTube channel attribute.
message YouTubeChannelAttributeMetadata {
  // The approximate number of subscribers to the YouTube channel.
  int64 subscriber_count = 1;
}

// Metadata associated with a Dynamic Lineup attribute.
message DynamicLineupAttributeMetadata {
  // A YouTube channel returned as an example of the content in a lineup.
  message SampleChannel {
    // A YouTube channel.
    google.ads.googleads.v17.common.YouTubeChannelInfo youtube_channel = 1;

    // The name of the sample channel.
    string display_name = 2;

    // Metadata for the sample channel.
    YouTubeChannelAttributeMetadata youtube_channel_metadata = 3;
  }

  // The national market associated with the lineup.
  google.ads.googleads.v17.common.LocationInfo inventory_country = 1;

  // The median number of impressions per month on this lineup.
  optional int64 median_monthly_inventory = 2;

  // The lower end of a range containing the number of channels in the lineup.
  optional int64 channel_count_lower_bound = 3;

  // The upper end of a range containing the number of channels in the lineup.
  optional int64 channel_count_upper_bound = 4;

  // Examples of channels that are included in the lineup.
  repeated SampleChannel sample_channels = 5;
}

// Metadata associated with a Location attribute.
message LocationAttributeMetadata {
  // The country location of the sub country location.
  google.ads.googleads.v17.common.LocationInfo country_location = 1;
}

// A set of users, defined by various characteristics, for which insights can
// be requested in AudienceInsightsService.
message InsightsAudience {
  // Required. The countries for the audience.
  repeated google.ads.googleads.v17.common.LocationInfo country_locations = 1
      [(google.api.field_behavior) = REQUIRED];

  // Sub-country geographic location attributes.  If present, each of these
  // must be contained in one of the countries in this audience.  If absent, the
  // audience is geographically to the country_locations and no further.
  repeated google.ads.googleads.v17.common.LocationInfo sub_country_locations =
      2;

  // Gender for the audience.  If absent, the audience does not restrict by
  // gender.
  google.ads.googleads.v17.common.GenderInfo gender = 3;

  // Age ranges for the audience.  If absent, the audience represents all people
  // over 18 that match the other attributes.
  repeated google.ads.googleads.v17.common.AgeRangeInfo age_ranges = 4;

  // Parental status for the audience.  If absent, the audience does not
  // restrict by parental status.
  google.ads.googleads.v17.common.ParentalStatusInfo parental_status = 5;

  // Household income percentile ranges for the audience.  If absent, the
  // audience does not restrict by household income range.
  repeated google.ads.googleads.v17.common.IncomeRangeInfo income_ranges = 6;

  // Dynamic lineups representing the YouTube content viewed by the audience.
  repeated AudienceInsightsDynamicLineup dynamic_lineups = 7;

  // A combination of entity, category and user interest attributes defining the
  // audience. The combination has a logical AND-of-ORs structure: Attributes
  // within each InsightsAudienceAttributeGroup are combined with OR, and
  // the combinations themselves are combined together with AND.  For example,
  // the expression (Entity OR Affinity) AND (In-Market OR Category) can be
  // formed using two InsightsAudienceAttributeGroups with two Attributes
  // each.
  repeated InsightsAudienceAttributeGroup topic_audience_combinations = 8;
}

// A list of AudienceInsightsAttributes.
message InsightsAudienceAttributeGroup {
  // Required. A collection of audience attributes to be combined with logical
  // OR. Attributes need not all be the same dimension.  Only Knowledge Graph
  // entities, Product & Service Categories, and Affinity and In-Market
  // audiences are supported in this context.
  repeated AudienceInsightsAttribute attributes = 1
      [(google.api.field_behavior) = REQUIRED];
}

// A collection of related attributes of the same type in an audience
// composition insights report.
message AudienceCompositionSection {
  // The type of the attributes in this section.
  google.ads.googleads.v17.enums.AudienceInsightsDimensionEnum
      .AudienceInsightsDimension dimension = 1;

  // The most relevant segments for this audience.  If dimension is GENDER,
  // AGE_RANGE or PARENTAL_STATUS, then this list of attributes is exhaustive.
  repeated AudienceCompositionAttribute top_attributes = 3;

  // Additional attributes for this audience, grouped into clusters.  Only
  // populated if dimension is YOUTUBE_CHANNEL.
  repeated AudienceCompositionAttributeCluster clustered_attributes = 4;
}

// A collection of related attributes, with metadata and metrics, in an audience
// composition insights report.
message AudienceCompositionAttributeCluster {
  // The name of this cluster of attributes
  string cluster_display_name = 1;

  // If the dimension associated with this cluster is YOUTUBE_CHANNEL, then
  // cluster_metrics are metrics associated with the cluster as a whole.
  // For other dimensions, this field is unset.
  AudienceCompositionMetrics cluster_metrics = 3;

  // The individual attributes that make up this cluster, with metadata and
  // metrics.
  repeated AudienceCompositionAttribute attributes = 4;
}

// The share and index metrics associated with an attribute in an audience
// composition insights report.
message AudienceCompositionMetrics {
  // The fraction (from 0 to 1 inclusive) of the baseline audience that match
  // the attribute.
  double baseline_audience_share = 1;

  // The fraction (from 0 to 1 inclusive) of the specific audience that match
  // the attribute.
  double audience_share = 2;

  // The ratio of audience_share to baseline_audience_share, or zero if this
  // ratio is undefined or is not meaningful.
  double index = 3;

  // A relevance score from 0 to 1 inclusive.
  double score = 4;
}

// An audience attribute with metadata and metrics.
message AudienceCompositionAttribute {
  // The attribute with its metadata.
  AudienceInsightsAttributeMetadata attribute_metadata = 1;

  // Share and index metrics for the attribute.
  AudienceCompositionMetrics metrics = 2;
}
