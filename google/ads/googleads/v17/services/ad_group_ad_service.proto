// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/common/policy.proto";
import "google/ads/googleads/v17/enums/asset_field_type.proto";
import "google/ads/googleads/v17/enums/response_content_type.proto";
import "google/ads/googleads/v17/resources/ad_group_ad.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "AdGroupAdServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the Ad Group Ad service.

// Service to manage ads in an ad group.
service AdGroupAdService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Creates, updates, or removes ads. Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AdCustomizerError]()
  //   [AdError]()
  //   [AdGroupAdError]()
  //   [AdSharingError]()
  //   [AdxError]()
  //   [AssetError]()
  //   [AssetLinkError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CollectionSizeError]()
  //   [ContextError]()
  //   [DatabaseError]()
  //   [DateError]()
  //   [DistinctError]()
  //   [FeedAttributeReferenceError]()
  //   [FieldError]()
  //   [FieldMaskError]()
  //   [FunctionError]()
  //   [FunctionParsingError]()
  //   [HeaderError]()
  //   [IdError]()
  //   [ImageError]()
  //   [InternalError]()
  //   [ListOperationError]()
  //   [MediaBundleError]()
  //   [MediaFileError]()
  //   [MutateError]()
  //   [NewResourceCreationError]()
  //   [NotEmptyError]()
  //   [NullError]()
  //   [OperationAccessDeniedError]()
  //   [OperatorError]()
  //   [PolicyFindingError]()
  //   [PolicyValidationParameterError]()
  //   [PolicyViolationError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  //   [ResourceCountLimitExceededError]()
  //   [SizeLimitError]()
  //   [StringFormatError]()
  //   [StringLengthError]()
  //   [UrlFieldError]()
  rpc MutateAdGroupAds(MutateAdGroupAdsRequest)
      returns (MutateAdGroupAdsResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/adGroupAds:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operations";
  }

  // Remove automatically created assets from an ad.
  //
  // List of thrown errors:
  //   [AdError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [AutomaticallyCreatedAssetRemovalError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [MutateError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc RemoveAutomaticallyCreatedAssets(RemoveAutomaticallyCreatedAssetsRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v17/{ad_group_ad=customers/*/adGroupAds/*}:removeAutomaticallyCreatedAssets"
      body: "*"
    };
    option (google.api.method_signature) = "ad_group_ad,assets_with_field_type";
  }
}

// Request message for
// [AdGroupAdService.MutateAdGroupAds][google.ads.googleads.v17.services.AdGroupAdService.MutateAdGroupAds].
message MutateAdGroupAdsRequest {
  // Required. The ID of the customer whose ads are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual ads.
  repeated AdGroupAdOperation operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v17.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 5;
}

// A single operation (create, update, remove) on an ad group ad.
message AdGroupAdOperation {
  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 4;

  // Configuration for how policies are validated.
  google.ads.googleads.v17.common.PolicyValidationParameter
      policy_validation_parameter = 5;

  // The mutate operation.
  oneof operation {
    // Create operation: No resource name is expected for the new ad.
    google.ads.googleads.v17.resources.AdGroupAd create = 1;

    // Update operation: The ad is expected to have a valid resource name.
    google.ads.googleads.v17.resources.AdGroupAd update = 2;

    // Remove operation: A resource name for the removed ad is expected,
    // in this format:
    //
    // `customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}`
    string remove = 3 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAd"
    }];
  }
}

// Response message for an ad group ad mutate.
message MutateAdGroupAdsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 3;

  // All results for the mutate.
  repeated MutateAdGroupAdResult results = 2;
}

// The result for the ad mutate.
message MutateAdGroupAdResult {
  // The resource name returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/AdGroupAd"
  }];

  // The mutated ad group ad with only mutable fields after mutate. The field
  // will only be returned when response_content_type is set to
  // "MUTABLE_RESOURCE".
  google.ads.googleads.v17.resources.AdGroupAd ad_group_ad = 2;
}

// Request message for
// [AdGroupAdService.RemoveAutomaticallyCreatedAssetsRequest][].
message RemoveAutomaticallyCreatedAssetsRequest {
  // Required. The resource name of the AdGroupAd from which to remove
  // automatically created assets.
  string ad_group_ad = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAd"
    }
  ];

  // Required. List of assets with field type to be removed from the AdGroupAd.
  repeated AssetsWithFieldType assets_with_field_type = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The combination of system asset and field type to remove.
message AssetsWithFieldType {
  // Required. The resource name of the asset to be removed.
  string asset = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // Required. The asset field type.
  google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldType
      asset_field_type = 2 [(google.api.field_behavior) = REQUIRED];
}
