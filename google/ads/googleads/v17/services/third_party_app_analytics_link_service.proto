// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "ThirdPartyAppAnalyticsLinkServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// This service allows management of links between Google Ads and third party
// app analytics.
service ThirdPartyAppAnalyticsLinkService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Regenerate ThirdPartyAppAnalyticsLink.shareable_link_id that should be
  // provided to the third party when setting up app analytics.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc RegenerateShareableLinkId(RegenerateShareableLinkIdRequest)
      returns (RegenerateShareableLinkIdResponse) {
    option (google.api.http) = {
      post: "/v17/{resource_name=customers/*/thirdPartyAppAnalyticsLinks/*}:regenerateShareableLinkId"
      body: "*"
    };
  }
}

// Request message for
// [ThirdPartyAppAnalyticsLinkService.RegenerateShareableLinkId][google.ads.googleads.v17.services.ThirdPartyAppAnalyticsLinkService.RegenerateShareableLinkId].
message RegenerateShareableLinkIdRequest {
  // Resource name of the third party app analytics link.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ThirdPartyAppAnalyticsLink"
  }];
}

// Response message for
// [ThirdPartyAppAnalyticsLinkService.RegenerateShareableLinkId][google.ads.googleads.v17.services.ThirdPartyAppAnalyticsLinkService.RegenerateShareableLinkId].
message RegenerateShareableLinkIdResponse {}
