// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/resources/campaign_lifecycle_goal.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "CampaignLifecycleGoalServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Service to configure campaign lifecycle goals.
service CampaignLifecycleGoalService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Process the given campaign lifecycle configurations.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CampaignLifecycleGoalConfigError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc ConfigureCampaignLifecycleGoals(ConfigureCampaignLifecycleGoalsRequest)
      returns (ConfigureCampaignLifecycleGoalsResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}/campaignLifecycleGoal:configureCampaignLifecycleGoals"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }
}

// Request message for
// [CampaignLifecycleGoalService.configureCampaignLifecycleGoals][].
message ConfigureCampaignLifecycleGoalsRequest {
  // Required. The ID of the customer performing the upload.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform campaign lifecycle goal update.
  CampaignLifecycleGoalOperation operation = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If true, the request is validated but not executed. Only errors
  // are returned, not results.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A single operation on a campaign lifecycle goal.
message CampaignLifecycleGoalOperation {
  // Optional. FieldMask that determines which resource fields are modified in
  // an update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // The mutate operation.
  oneof operation {
    // Create operation: Create a new campaign lifecycle goal. The campaign
    // field should be set for this operation.
    google.ads.googleads.v17.resources.CampaignLifecycleGoal create = 1;

    // Update operation: Update an existing campaign lifecycle goal. The
    // campaign field should not be set for this operation.
    google.ads.googleads.v17.resources.CampaignLifecycleGoal update = 3;
  }
}

// Response message for
// [CampaignLifecycleGoalService.configureCampaignLifecycleGoals][].
message ConfigureCampaignLifecycleGoalsResponse {
  // Result for the campaign lifecycle goal configuration.
  ConfigureCampaignLifecycleGoalsResult result = 1;
}

// The result for the campaign lifecycle goal configuration.
message ConfigureCampaignLifecycleGoalsResult {
  // Returned for the successful operation.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CampaignLifecycleGoal"
  }];
}
