// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/enums/identity_verification_program.proto";
import "google/ads/googleads/v17/enums/identity_verification_program_status.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "IdentityVerificationServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the IdentityVerificatio Service.

// A service for managing Identity Verification Service.
service IdentityVerificationService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Starts Identity Verification for a given verification program type.
  //  Statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc StartIdentityVerification(StartIdentityVerificationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}:startIdentityVerification"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,verification_program";
  }

  // Returns Identity Verification information.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc GetIdentityVerification(GetIdentityVerificationRequest)
      returns (GetIdentityVerificationResponse) {
    option (google.api.http) = {
      get: "/v17/customers/{customer_id=*}/getIdentityVerification"
    };
    option (google.api.method_signature) = "customer_id";
  }
}

// Request message for
//  [IdentityVerificationService.StartIdentityVerification].
message StartIdentityVerificationRequest {
  // Required. The Id of the customer for whom we are creating this
  // verification.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The verification program type for which we want to start the
  // verification.
  google.ads.googleads.v17.enums.IdentityVerificationProgramEnum
      .IdentityVerificationProgram verification_program = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
//  [IdentityVerificationService.GetIdentityVerification].
message GetIdentityVerificationRequest {
  // Required.  The ID of the customer for whom we are requesting verification
  //  information.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
//  [IdentityVerificationService.GetIdentityVerification].
message GetIdentityVerificationResponse {
  // List of identity verifications for the customer.
  repeated IdentityVerification identity_verification = 1;
}

// An identity verification for a customer.
message IdentityVerification {
  // The verification program type.
  google.ads.googleads.v17.enums.IdentityVerificationProgramEnum
      .IdentityVerificationProgram verification_program = 1;

  // The verification requirement for this verification program for this
  // customer.
  optional IdentityVerificationRequirement identity_verification_requirement =
      2;

  // Information regarding progress for this verification program for this
  // customer.
  optional IdentityVerificationProgress verification_progress = 3;
}

// Information regarding the verification progress for a verification program
// type.
message IdentityVerificationProgress {
  // Current Status (PENDING_USER_ACTION, SUCCESS, FAILURE etc)
  google.ads.googleads.v17.enums.IdentityVerificationProgramStatusEnum
      .IdentityVerificationProgramStatus program_status = 1;

  // The timestamp when the action url will expire in "yyyy-MM-dd HH:mm:ss"
  // format.
  string invitation_link_expiration_time = 2;

  // Action URL for user to complete verification for the given verification
  // program type.
  string action_url = 3;
}

// Information regarding the verification requirement for a verification program
// type.
message IdentityVerificationRequirement {
  // The deadline to start verification in "yyyy-MM-dd HH:mm:ss" format.
  string verification_start_deadline_time = 1;

  // The deadline to submit verification.
  string verification_completion_deadline_time = 2;
}
