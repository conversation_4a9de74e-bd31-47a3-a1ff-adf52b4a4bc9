// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.services;

import "google/ads/googleads/v17/enums/access_role.proto";
import "google/ads/googleads/v17/enums/response_content_type.proto";
import "google/ads/googleads/v17/resources/customer.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerServiceProto";
option java_package = "com.google.ads.googleads.v17.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Services";
option ruby_package = "Google::Ads::GoogleAds::V17::Services";

// Proto file describing the Customer service.

// Service to manage customers.
service CustomerService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Updates a customer. Operation statuses are returned.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [DatabaseError]()
  //   [FieldMaskError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  //   [UrlFieldError]()
  rpc MutateCustomer(MutateCustomerRequest) returns (MutateCustomerResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,operation";
  }

  // Returns resource names of customers directly accessible by the
  // user authenticating the call.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc ListAccessibleCustomers(ListAccessibleCustomersRequest)
      returns (ListAccessibleCustomersResponse) {
    option (google.api.http) = {
      get: "/v17/customers:listAccessibleCustomers"
    };
  }

  // Creates a new client under manager. The new client customer is returned.
  //
  // List of thrown errors:
  //   [AccessInvitationError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [CurrencyCodeError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [ManagerLinkError]()
  //   [QuotaError]()
  //   [RequestError]()
  //   [StringLengthError]()
  //   [TimeZoneError]()
  rpc CreateCustomerClient(CreateCustomerClientRequest)
      returns (CreateCustomerClientResponse) {
    option (google.api.http) = {
      post: "/v17/customers/{customer_id=*}:createCustomerClient"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,customer_client";
  }
}

// Request message for
// [CustomerService.MutateCustomer][google.ads.googleads.v17.services.CustomerService.MutateCustomer].
message MutateCustomerRequest {
  // Required. The ID of the customer being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The operation to perform on the customer
  CustomerOperation operation = 4 [(google.api.field_behavior) = REQUIRED];

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 5;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation.
  google.ads.googleads.v17.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 6;
}

// Request message for
// [CustomerService.CreateCustomerClient][google.ads.googleads.v17.services.CustomerService.CreateCustomerClient].
message CreateCustomerClientRequest {
  // Required. The ID of the Manager under whom client customer is being
  // created.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The new client customer to create. The resource name on this
  // customer will be ignored.
  google.ads.googleads.v17.resources.Customer customer_client = 2
      [(google.api.field_behavior) = REQUIRED];

  // Email address of the user who should be invited on the created client
  // customer. Accessible only to customers on the allow-list.
  optional string email_address = 5;

  // The proposed role of user on the created client customer.
  // Accessible only to customers on the allow-list.
  google.ads.googleads.v17.enums.AccessRoleEnum.AccessRole access_role = 4;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 6;
}

// A single update on a customer.
message CustomerOperation {
  // Mutate operation. Only updates are supported for customer.
  google.ads.googleads.v17.resources.Customer update = 1;

  // FieldMask that determines which resource fields are modified in an update.
  google.protobuf.FieldMask update_mask = 2;
}

// Response message for CreateCustomerClient mutate.
message CreateCustomerClientResponse {
  // The resource name of the newly created customer. Customer resource names
  // have the form: `customers/{customer_id}`.
  string resource_name = 2 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/Customer"
  }];

  // Link for inviting user to access the created customer. Accessible to
  // allowlisted customers only.
  string invitation_link = 3;
}

// Response message for customer mutate.
message MutateCustomerResponse {
  // Result for the mutate.
  MutateCustomerResult result = 2;
}

// The result for the customer mutate.
message MutateCustomerResult {
  // Returned for successful operations.
  string resource_name = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/Customer"
  }];

  // The mutated customer with only mutable fields after mutate. The fields will
  // only be returned when response_content_type is set to "MUTABLE_RESOURCE".
  google.ads.googleads.v17.resources.Customer customer = 2;
}

// Request message for
// [CustomerService.ListAccessibleCustomers][google.ads.googleads.v17.services.CustomerService.ListAccessibleCustomers].
message ListAccessibleCustomersRequest {}

// Response message for
// [CustomerService.ListAccessibleCustomers][google.ads.googleads.v17.services.CustomerService.ListAccessibleCustomers].
message ListAccessibleCustomersResponse {
  // Resource name of customers directly accessible by the
  // user authenticating the call.
  repeated string resource_names = 1;
}
