type: google.api.Service
config_version: 3
name: googleads.googleapis.com
title: Google Ads API

apis:
- name: google.ads.googleads.v17.services.AccountBudgetProposalService
- name: google.ads.googleads.v17.services.AccountLinkService
- name: google.ads.googleads.v17.services.AdGroupAdLabelService
- name: google.ads.googleads.v17.services.AdGroupAdService
- name: google.ads.googleads.v17.services.AdGroupAssetService
- name: google.ads.googleads.v17.services.AdGroupAssetSetService
- name: google.ads.googleads.v17.services.AdGroupBidModifierService
- name: google.ads.googleads.v17.services.AdGroupCriterionCustomizerService
- name: google.ads.googleads.v17.services.AdGroupCriterionLabelService
- name: google.ads.googleads.v17.services.AdGroupCriterionService
- name: google.ads.googleads.v17.services.AdGroupCustomizerService
- name: google.ads.googleads.v17.services.AdGroupExtensionSettingService
- name: google.ads.googleads.v17.services.AdGroupFeedService
- name: google.ads.googleads.v17.services.AdGroupLabelService
- name: google.ads.googleads.v17.services.AdGroupService
- name: google.ads.googleads.v17.services.AdParameterService
- name: google.ads.googleads.v17.services.AdService
- name: google.ads.googleads.v17.services.AssetGroupAssetService
- name: google.ads.googleads.v17.services.AssetGroupListingGroupFilterService
- name: google.ads.googleads.v17.services.AssetGroupService
- name: google.ads.googleads.v17.services.AssetGroupSignalService
- name: google.ads.googleads.v17.services.AssetService
- name: google.ads.googleads.v17.services.AssetSetAssetService
- name: google.ads.googleads.v17.services.AssetSetService
- name: google.ads.googleads.v17.services.AudienceInsightsService
- name: google.ads.googleads.v17.services.AudienceService
- name: google.ads.googleads.v17.services.BatchJobService
- name: google.ads.googleads.v17.services.BiddingDataExclusionService
- name: google.ads.googleads.v17.services.BiddingSeasonalityAdjustmentService
- name: google.ads.googleads.v17.services.BiddingStrategyService
- name: google.ads.googleads.v17.services.BillingSetupService
- name: google.ads.googleads.v17.services.BrandSuggestionService
- name: google.ads.googleads.v17.services.CampaignAssetService
- name: google.ads.googleads.v17.services.CampaignAssetSetService
- name: google.ads.googleads.v17.services.CampaignBidModifierService
- name: google.ads.googleads.v17.services.CampaignBudgetService
- name: google.ads.googleads.v17.services.CampaignConversionGoalService
- name: google.ads.googleads.v17.services.CampaignCriterionService
- name: google.ads.googleads.v17.services.CampaignCustomizerService
- name: google.ads.googleads.v17.services.CampaignDraftService
- name: google.ads.googleads.v17.services.CampaignExtensionSettingService
- name: google.ads.googleads.v17.services.CampaignFeedService
- name: google.ads.googleads.v17.services.CampaignGroupService
- name: google.ads.googleads.v17.services.CampaignLabelService
- name: google.ads.googleads.v17.services.CampaignLifecycleGoalService
- name: google.ads.googleads.v17.services.CampaignService
- name: google.ads.googleads.v17.services.CampaignSharedSetService
- name: google.ads.googleads.v17.services.ConversionActionService
- name: google.ads.googleads.v17.services.ConversionAdjustmentUploadService
- name: google.ads.googleads.v17.services.ConversionCustomVariableService
- name: google.ads.googleads.v17.services.ConversionGoalCampaignConfigService
- name: google.ads.googleads.v17.services.ConversionUploadService
- name: google.ads.googleads.v17.services.ConversionValueRuleService
- name: google.ads.googleads.v17.services.ConversionValueRuleSetService
- name: google.ads.googleads.v17.services.CustomAudienceService
- name: google.ads.googleads.v17.services.CustomConversionGoalService
- name: google.ads.googleads.v17.services.CustomInterestService
- name: google.ads.googleads.v17.services.CustomerAssetService
- name: google.ads.googleads.v17.services.CustomerAssetSetService
- name: google.ads.googleads.v17.services.CustomerClientLinkService
- name: google.ads.googleads.v17.services.CustomerConversionGoalService
- name: google.ads.googleads.v17.services.CustomerCustomizerService
- name: google.ads.googleads.v17.services.CustomerExtensionSettingService
- name: google.ads.googleads.v17.services.CustomerFeedService
- name: google.ads.googleads.v17.services.CustomerLabelService
- name: google.ads.googleads.v17.services.CustomerLifecycleGoalService
- name: google.ads.googleads.v17.services.CustomerManagerLinkService
- name: google.ads.googleads.v17.services.CustomerNegativeCriterionService
- name: google.ads.googleads.v17.services.CustomerService
- name: google.ads.googleads.v17.services.CustomerSkAdNetworkConversionValueSchemaService
- name: google.ads.googleads.v17.services.CustomerUserAccessInvitationService
- name: google.ads.googleads.v17.services.CustomerUserAccessService
- name: google.ads.googleads.v17.services.CustomizerAttributeService
- name: google.ads.googleads.v17.services.ExperimentArmService
- name: google.ads.googleads.v17.services.ExperimentService
- name: google.ads.googleads.v17.services.ExtensionFeedItemService
- name: google.ads.googleads.v17.services.FeedItemService
- name: google.ads.googleads.v17.services.FeedItemSetLinkService
- name: google.ads.googleads.v17.services.FeedItemSetService
- name: google.ads.googleads.v17.services.FeedItemTargetService
- name: google.ads.googleads.v17.services.FeedMappingService
- name: google.ads.googleads.v17.services.FeedService
- name: google.ads.googleads.v17.services.GeoTargetConstantService
- name: google.ads.googleads.v17.services.GoogleAdsFieldService
- name: google.ads.googleads.v17.services.GoogleAdsService
- name: google.ads.googleads.v17.services.IdentityVerificationService
- name: google.ads.googleads.v17.services.InvoiceService
- name: google.ads.googleads.v17.services.KeywordPlanAdGroupKeywordService
- name: google.ads.googleads.v17.services.KeywordPlanAdGroupService
- name: google.ads.googleads.v17.services.KeywordPlanCampaignKeywordService
- name: google.ads.googleads.v17.services.KeywordPlanCampaignService
- name: google.ads.googleads.v17.services.KeywordPlanIdeaService
- name: google.ads.googleads.v17.services.KeywordPlanService
- name: google.ads.googleads.v17.services.KeywordThemeConstantService
- name: google.ads.googleads.v17.services.LabelService
- name: google.ads.googleads.v17.services.LocalServicesLeadService
- name: google.ads.googleads.v17.services.OfflineUserDataJobService
- name: google.ads.googleads.v17.services.PaymentsAccountService
- name: google.ads.googleads.v17.services.ProductLinkInvitationService
- name: google.ads.googleads.v17.services.ProductLinkService
- name: google.ads.googleads.v17.services.ReachPlanService
- name: google.ads.googleads.v17.services.RecommendationService
- name: google.ads.googleads.v17.services.RecommendationSubscriptionService
- name: google.ads.googleads.v17.services.RemarketingActionService
- name: google.ads.googleads.v17.services.ShareablePreviewService
- name: google.ads.googleads.v17.services.SharedCriterionService
- name: google.ads.googleads.v17.services.SharedSetService
- name: google.ads.googleads.v17.services.SmartCampaignSettingService
- name: google.ads.googleads.v17.services.SmartCampaignSuggestService
- name: google.ads.googleads.v17.services.ThirdPartyAppAnalyticsLinkService
- name: google.ads.googleads.v17.services.TravelAssetSuggestionService
- name: google.ads.googleads.v17.services.UserDataService
- name: google.ads.googleads.v17.services.UserListCustomerTypeService
- name: google.ads.googleads.v17.services.UserListService

types:
- name: google.ads.googleads.v17.errors.GoogleAdsFailure
- name: google.ads.googleads.v17.resources.BatchJob.BatchJobMetadata
- name: google.ads.googleads.v17.resources.OfflineUserDataJobMetadata
- name: google.ads.googleads.v17.services.PromoteExperimentMetadata
- name: google.ads.googleads.v17.services.ScheduleExperimentMetadata

documentation:
  summary: 'Manage your Google Ads accounts, campaigns, and reports with this API.'
  overview: |-
    The Google Ads API enables an app to integrate with the Google Ads
    platform. You can efficiently retrieve and change your Google Ads data
    using the API, making it ideal for managing large or complex accounts and
    campaigns.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v17/{name=customers/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v17/{name=customers/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v17/{name=customers/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v17/{name=customers/*/operations}'
  - selector: google.longrunning.Operations.WaitOperation
    post: '/v17/{name=customers/*/operations/*}:wait'
    body: '*'

authentication:
  rules:
  - selector: google.ads.googleads.v17.services.AccountBudgetProposalService.MutateAccountBudgetProposal
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AccountLinkService.CreateAccountLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AccountLinkService.MutateAccountLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupAdLabelService.MutateAdGroupAdLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupAdService.MutateAdGroupAds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupAdService.RemoveAutomaticallyCreatedAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupAssetService.MutateAdGroupAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupAssetSetService.MutateAdGroupAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupBidModifierService.MutateAdGroupBidModifiers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupCriterionCustomizerService.MutateAdGroupCriterionCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupCriterionLabelService.MutateAdGroupCriterionLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupCriterionService.MutateAdGroupCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupCustomizerService.MutateAdGroupCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupExtensionSettingService.MutateAdGroupExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupFeedService.MutateAdGroupFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupLabelService.MutateAdGroupLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdGroupService.MutateAdGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdParameterService.MutateAdParameters
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AdService.MutateAds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AssetGroupAssetService.MutateAssetGroupAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AssetGroupListingGroupFilterService.MutateAssetGroupListingGroupFilters
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AssetGroupService.MutateAssetGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AssetGroupSignalService.MutateAssetGroupSignals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AssetService.MutateAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AssetSetAssetService.MutateAssetSetAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AssetSetService.MutateAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.AudienceInsightsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.AudienceService.MutateAudiences
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.BatchJobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.BiddingDataExclusionService.MutateBiddingDataExclusions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.BiddingSeasonalityAdjustmentService.MutateBiddingSeasonalityAdjustments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.BiddingStrategyService.MutateBiddingStrategies
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.BillingSetupService.MutateBillingSetup
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.BrandSuggestionService.SuggestBrands
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignAssetService.MutateCampaignAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignAssetSetService.MutateCampaignAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignBidModifierService.MutateCampaignBidModifiers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignBudgetService.MutateCampaignBudgets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignConversionGoalService.MutateCampaignConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignCriterionService.MutateCampaignCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignCustomizerService.MutateCampaignCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.CampaignDraftService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignExtensionSettingService.MutateCampaignExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignFeedService.MutateCampaignFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignGroupService.MutateCampaignGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignLabelService.MutateCampaignLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignLifecycleGoalService.ConfigureCampaignLifecycleGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignService.MutateCampaigns
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CampaignSharedSetService.MutateCampaignSharedSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ConversionActionService.MutateConversionActions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ConversionAdjustmentUploadService.UploadConversionAdjustments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ConversionCustomVariableService.MutateConversionCustomVariables
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ConversionGoalCampaignConfigService.MutateConversionGoalCampaignConfigs
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ConversionUploadService.UploadCallConversions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ConversionUploadService.UploadClickConversions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ConversionValueRuleService.MutateConversionValueRules
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ConversionValueRuleSetService.MutateConversionValueRuleSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomAudienceService.MutateCustomAudiences
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomConversionGoalService.MutateCustomConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomInterestService.MutateCustomInterests
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerAssetService.MutateCustomerAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerAssetSetService.MutateCustomerAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerClientLinkService.MutateCustomerClientLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerConversionGoalService.MutateCustomerConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerCustomizerService.MutateCustomerCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerExtensionSettingService.MutateCustomerExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerFeedService.MutateCustomerFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerLabelService.MutateCustomerLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerLifecycleGoalService.ConfigureCustomerLifecycleGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerManagerLinkService.MoveManagerLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerManagerLinkService.MutateCustomerManagerLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerNegativeCriterionService.MutateCustomerNegativeCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.CustomerService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerSkAdNetworkConversionValueSchemaService.MutateCustomerSkAdNetworkConversionValueSchema
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerUserAccessInvitationService.MutateCustomerUserAccessInvitation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomerUserAccessService.MutateCustomerUserAccess
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.CustomizerAttributeService.MutateCustomizerAttributes
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ExperimentArmService.MutateExperimentArms
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.ExperimentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ExtensionFeedItemService.MutateExtensionFeedItems
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.FeedItemService.MutateFeedItems
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.FeedItemSetLinkService.MutateFeedItemSetLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.FeedItemSetService.MutateFeedItemSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.FeedItemTargetService.MutateFeedItemTargets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.FeedMappingService.MutateFeedMappings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.FeedService.MutateFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.GeoTargetConstantService.SuggestGeoTargetConstants
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.GoogleAdsFieldService.GetGoogleAdsField
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.GoogleAdsFieldService.SearchGoogleAdsFields
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.GoogleAdsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.IdentityVerificationService.GetIdentityVerification
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.IdentityVerificationService.StartIdentityVerification
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.InvoiceService.ListInvoices
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.KeywordPlanAdGroupKeywordService.MutateKeywordPlanAdGroupKeywords
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.KeywordPlanAdGroupService.MutateKeywordPlanAdGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.KeywordPlanCampaignKeywordService.MutateKeywordPlanCampaignKeywords
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.KeywordPlanCampaignService.MutateKeywordPlanCampaigns
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.KeywordPlanIdeaService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.KeywordPlanService.MutateKeywordPlans
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.KeywordThemeConstantService.SuggestKeywordThemeConstants
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.LabelService.MutateLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.LocalServicesLeadService.AppendLeadConversation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.OfflineUserDataJobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.PaymentsAccountService.ListPaymentsAccounts
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.ProductLinkInvitationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ProductLinkService.CreateProductLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ProductLinkService.RemoveProductLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.ReachPlanService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.RecommendationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.RecommendationSubscriptionService.MutateRecommendationSubscription
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.RemarketingActionService.MutateRemarketingActions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ShareablePreviewService.GenerateShareablePreviews
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.SharedCriterionService.MutateSharedCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.SharedSetService.MutateSharedSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.SmartCampaignSettingService.GetSmartCampaignStatus
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.SmartCampaignSettingService.MutateSmartCampaignSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v17.services.SmartCampaignSuggestService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.ThirdPartyAppAnalyticsLinkService.RegenerateShareableLinkId
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.TravelAssetSuggestionService.SuggestTravelAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.UserDataService.UploadUserData
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.UserListCustomerTypeService.MutateUserListCustomerTypes
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v17.services.UserListService.MutateUserLists
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
