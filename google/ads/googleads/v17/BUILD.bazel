# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

package(default_visibility = ["//visibility:public"])

exports_files(["googleads_grpc_service_config.json"] + ["*.yaml"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "googleads_proto",
    srcs = [],
    deps = [
        "//google/ads/googleads/v17/common:common_proto",
        "//google/ads/googleads/v17/enums:enums_proto",
        "//google/ads/googleads/v17/errors:errors_proto",
        "//google/ads/googleads/v17/resources:resources_proto",
        "//google/ads/googleads/v17/services:services_proto",
    ],
)

proto_library_with_info(
    name = "googleads_proto_with_info",
    deps = [
        ":googleads_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
)

java_gapic_library(
    name = "googleads_java_gapic",
    srcs = [
        ":googleads_proto_with_info",
    ],
    gapic_yaml = "googleads_gapic.yaml",
    grpc_service_config = ":googleads_grpc_service_config.json",
    service_yaml = "googleads_v17.yaml",
    deps = [
        "//google/ads/googleads/v17/common:common_java_proto",
        "//google/ads/googleads/v17/enums:enums_java_proto",
        "//google/ads/googleads/v17/resources:resources_java_proto",
        "//google/ads/googleads/v17/services:services_java_grpc",
        "//google/ads/googleads/v17/services:services_java_proto",
    ],
)

# TODO(ohren): Add more test classes when java_gapic_test is able to run more
# than a single test. Having at least one verifies proper compilation at least.
java_gapic_test(
    name = "googleads_java_gapic_suite",
    test_classes = [
        "com.google.ads.googleads.v17.services.CampaignServiceClientTest",
    ],
    runtime_deps = [":googleads_java_gapic_test"],
)

java_gapic_assembly_gradle_pkg(
    name = "googleads-java",
    deps = [
        ":googleads_java_gapic",
        "//google/ads/googleads/v17:googleads_proto",
        "//google/ads/googleads/v17/common:common_java_proto",
        "//google/ads/googleads/v17/enums:enums_java_proto",
        "//google/ads/googleads/v17/errors:errors_java_proto",
        "//google/ads/googleads/v17/resources:resources_java_proto",
        "//google/ads/googleads/v17/services:services_java_grpc",
        "//google/ads/googleads/v17/services:services_java_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_grpc_library",
    "php_proto_library",
)

php_proto_library(
    name = "googleads_php_proto",
    plugin_args = ["aggregate_metadata=google.ads.googleads"],
    deps = [":googleads_proto"],
)

php_grpc_library(
    name = "googleads_php_grpc",
    srcs = [":googleads_proto"],
    deps = [":googleads_php_proto"],
)

php_gapic_library(
    name = "googleads_php_gapic",
    srcs = [":googleads_proto"],
    gapic_yaml = "googleads_gapic.yaml",
    grpc_service_config = "googleads_grpc_service_config.json",
    service_yaml = "googleads_v17.yaml",
    generate_snippets = False,
    migration_mode = "NEW_SURFACE_ONLY",
    deps = [
        ":googleads_php_grpc",
        ":googleads_php_proto",
    ],
)

php_gapic_assembly_pkg(
    name = "googleads-php",
    deps = [
        ":googleads_php_gapic",
        ":googleads_php_grpc",
        ":googleads_php_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
)

csharp_gapic_library(
    name = "googleads_csharp_gapic",
    srcs = [
        ":googleads_proto_with_info",
    ],
    grpc_service_config = "googleads_grpc_service_config.json",
    service_yaml = "googleads_v17.yaml",
    deps = [
        "//google/ads/googleads/v17/services:services_csharp_grpc",
    ],
)

csharp_gapic_assembly_pkg(
    name = "googleads-csharp",
    deps = [
        ":googleads_csharp_gapic",
        "//google/ads/googleads/v17/common:common_csharp_proto",
        "//google/ads/googleads/v17/enums:enums_csharp_proto",
        "//google/ads/googleads/v17/errors:errors_csharp_proto",
        "//google/ads/googleads/v17/resources:resources_csharp_proto",
        "//google/ads/googleads/v17/services:services_csharp_grpc",
        "//google/ads/googleads/v17/services:services_csharp_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_ads_gapic_library",
    "ruby_gapic_assembly_pkg",
)

ruby_ads_gapic_library(
    name = "googleads_ruby_gapic",
    srcs = ["googleads_proto_with_info"],
    extra_protoc_parameters = [
        ":gem.:name=google-ads-googleads",
        ":defaults.:service.:default_host=googleads.googleapis.com",
        ":overrides.:namespace.Googleads=GoogleAds",
    ],
    grpc_service_config = "googleads_grpc_service_config.json",
    service_yaml = "googleads_v17.yaml",
)

ruby_gapic_assembly_pkg(
    name = "googleads-ruby",
    deps = [
        ":googleads_ruby_gapic",
        "//google/ads/googleads/v17/common:common_ruby_proto",
        "//google/ads/googleads/v17/enums:enums_ruby_proto",
        "//google/ads/googleads/v17/errors:errors_ruby_proto",
        "//google/ads/googleads/v17/resources:resources_ruby_proto",
        "//google/ads/googleads/v17/services:services_ruby_grpc",
        "//google/ads/googleads/v17/services:services_ruby_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
)

py_gapic_library(
    name = "googleads_py_gapic",
    srcs = [":googleads_proto_with_info"],
    service_yaml = "googleads_v17.yaml",
    grpc_service_config = "googleads_grpc_service_config.json",
    opt_args = [
        "old-naming",
        "lazy-import",
        "python-gapic-name=googleads",
        "python-gapic-templates=ads-templates",
        "warehouse-package-name=google-ads",
    ],
)

py_gapic_assembly_pkg(
    name = "googleads-py",
    deps = [
        ":googleads_py_gapic",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "googleads_nodejs_gapic",
    package_name = "google-ads",
    src = ":googleads_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "googleads_grpc_service_config.json",
    main_service = "GoogleAdsService",
    package = "google.ads.googleads.v17",
    service_yaml = "googleads_v17.yaml",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "googleads-nodejs",
    deps = [
        ":googleads_nodejs_gapic",
        ":googleads_proto",
    ],
)
