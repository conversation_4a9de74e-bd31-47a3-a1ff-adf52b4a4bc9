{"methodConfig": [{"name": [{"service": "google.ads.googleads.v17.services.AccountBudgetProposalService"}, {"service": "google.ads.googleads.v17.services.AccountLinkService"}, {"service": "google.ads.googleads.v17.services.AdGroupAdLabelService"}, {"service": "google.ads.googleads.v17.services.AdGroupAdService"}, {"service": "google.ads.googleads.v17.services.AdGroupAssetService"}, {"service": "google.ads.googleads.v17.services.AdGroupAssetSetService"}, {"service": "google.ads.googleads.v17.services.AdGroupBidModifierService"}, {"service": "google.ads.googleads.v17.services.AdGroupCriterionCustomizerService"}, {"service": "google.ads.googleads.v17.services.AdGroupCriterionLabelService"}, {"service": "google.ads.googleads.v17.services.AdGroupCriterionService"}, {"service": "google.ads.googleads.v17.services.AdGroupCustomizerService"}, {"service": "google.ads.googleads.v17.services.AdGroupExtensionSettingService"}, {"service": "google.ads.googleads.v17.services.AdGroupFeedService"}, {"service": "google.ads.googleads.v17.services.AdGroupLabelService"}, {"service": "google.ads.googleads.v17.services.AdGroupService"}, {"service": "google.ads.googleads.v17.services.AdParameterService"}, {"service": "google.ads.googleads.v17.services.AdService"}, {"service": "google.ads.googleads.v17.services.AssetGroupAssetService"}, {"service": "google.ads.googleads.v17.services.AssetGroupListingGroupFilterService"}, {"service": "google.ads.googleads.v17.services.AssetGroupService"}, {"service": "google.ads.googleads.v17.services.AssetGroupSignalService"}, {"service": "google.ads.googleads.v17.services.AssetService"}, {"service": "google.ads.googleads.v17.services.AssetSetAssetService"}, {"service": "google.ads.googleads.v17.services.AssetSetService"}, {"service": "google.ads.googleads.v17.services.AudienceInsightsService"}, {"service": "google.ads.googleads.v17.services.AudienceService"}, {"service": "google.ads.googleads.v17.services.BatchJobService"}, {"service": "google.ads.googleads.v17.services.BiddingDataExclusionService"}, {"service": "google.ads.googleads.v17.services.BiddingSeasonalityAdjustmentService"}, {"service": "google.ads.googleads.v17.services.BiddingStrategyService"}, {"service": "google.ads.googleads.v17.services.BillingSetupService"}, {"service": "google.ads.googleads.v17.services.BrandSuggestionService"}, {"service": "google.ads.googleads.v17.services.CampaignAssetService"}, {"service": "google.ads.googleads.v17.services.CampaignAssetSetService"}, {"service": "google.ads.googleads.v17.services.CampaignBidModifierService"}, {"service": "google.ads.googleads.v17.services.CampaignBudgetService"}, {"service": "google.ads.googleads.v17.services.CampaignConversionGoalService"}, {"service": "google.ads.googleads.v17.services.CampaignCriterionService"}, {"service": "google.ads.googleads.v17.services.CampaignCustomizerService"}, {"service": "google.ads.googleads.v17.services.CampaignDraftService"}, {"service": "google.ads.googleads.v17.services.CampaignExtensionSettingService"}, {"service": "google.ads.googleads.v17.services.CampaignFeedService"}, {"service": "google.ads.googleads.v17.services.CampaignGroupService"}, {"service": "google.ads.googleads.v17.services.CampaignLabelService"}, {"service": "google.ads.googleads.v17.services.CampaignLifecycleGoalService"}, {"service": "google.ads.googleads.v17.services.CampaignService"}, {"service": "google.ads.googleads.v17.services.CampaignSharedSetService"}, {"service": "google.ads.googleads.v17.services.ConversionActionService"}, {"service": "google.ads.googleads.v17.services.ConversionAdjustmentUploadService"}, {"service": "google.ads.googleads.v17.services.ConversionCustomVariableService"}, {"service": "google.ads.googleads.v17.services.ConversionGoalCampaignConfigService"}, {"service": "google.ads.googleads.v17.services.ConversionUploadService"}, {"service": "google.ads.googleads.v17.services.ConversionValueRuleService"}, {"service": "google.ads.googleads.v17.services.ConversionValueRuleSetService"}, {"service": "google.ads.googleads.v17.services.CustomAudienceService"}, {"service": "google.ads.googleads.v17.services.CustomConversionGoalService"}, {"service": "google.ads.googleads.v17.services.CustomInterestService"}, {"service": "google.ads.googleads.v17.services.CustomerAssetService"}, {"service": "google.ads.googleads.v17.services.CustomerAssetSetService"}, {"service": "google.ads.googleads.v17.services.CustomerClientLinkService"}, {"service": "google.ads.googleads.v17.services.CustomerConversionGoalService"}, {"service": "google.ads.googleads.v17.services.CustomerCustomizerService"}, {"service": "google.ads.googleads.v17.services.CustomerExtensionSettingService"}, {"service": "google.ads.googleads.v17.services.CustomerFeedService"}, {"service": "google.ads.googleads.v17.services.CustomerLabelService"}, {"service": "google.ads.googleads.v17.services.CustomerLifecycleGoalService"}, {"service": "google.ads.googleads.v17.services.CustomerManagerLinkService"}, {"service": "google.ads.googleads.v17.services.CustomerNegativeCriterionService"}, {"service": "google.ads.googleads.v17.services.CustomerService"}, {"service": "google.ads.googleads.v17.services.CustomerSkAdNetworkConversionValueSchemaService"}, {"service": "google.ads.googleads.v17.services.CustomerUserAccessInvitationService"}, {"service": "google.ads.googleads.v17.services.CustomerUserAccessService"}, {"service": "google.ads.googleads.v17.services.CustomizerAttributeService"}, {"service": "google.ads.googleads.v17.services.ExperimentArmService"}, {"service": "google.ads.googleads.v17.services.ExperimentService"}, {"service": "google.ads.googleads.v17.services.ExtensionFeedItemService"}, {"service": "google.ads.googleads.v17.services.FeedItemService"}, {"service": "google.ads.googleads.v17.services.FeedItemSetLinkService"}, {"service": "google.ads.googleads.v17.services.FeedItemSetService"}, {"service": "google.ads.googleads.v17.services.FeedItemTargetService"}, {"service": "google.ads.googleads.v17.services.FeedMappingService"}, {"service": "google.ads.googleads.v17.services.FeedService"}, {"service": "google.ads.googleads.v17.services.GeoTargetConstantService"}, {"service": "google.ads.googleads.v17.services.GoogleAdsFieldService"}, {"service": "google.ads.googleads.v17.services.GoogleAdsService"}, {"service": "google.ads.googleads.v17.services.IdentityVerificationService"}, {"service": "google.ads.googleads.v17.services.InvoiceService"}, {"service": "google.ads.googleads.v17.services.KeywordPlanAdGroupKeywordService"}, {"service": "google.ads.googleads.v17.services.KeywordPlanAdGroupService"}, {"service": "google.ads.googleads.v17.services.KeywordPlanCampaignKeywordService"}, {"service": "google.ads.googleads.v17.services.KeywordPlanCampaignService"}, {"service": "google.ads.googleads.v17.services.KeywordPlanIdeaService"}, {"service": "google.ads.googleads.v17.services.KeywordPlanService"}, {"service": "google.ads.googleads.v17.services.KeywordThemeConstantService"}, {"service": "google.ads.googleads.v17.services.LabelService"}, {"service": "google.ads.googleads.v17.services.LocalServicesLeadService"}, {"service": "google.ads.googleads.v17.services.OfflineUserDataJobService"}, {"service": "google.ads.googleads.v17.services.PaymentsAccountService"}, {"service": "google.ads.googleads.v17.services.ProductLinkInvitationService"}, {"service": "google.ads.googleads.v17.services.ProductLinkService"}, {"service": "google.ads.googleads.v17.services.ReachPlanService"}, {"service": "google.ads.googleads.v17.services.RecommendationService"}, {"service": "google.ads.googleads.v17.services.RecommendationSubscriptionService"}, {"service": "google.ads.googleads.v17.services.RemarketingActionService"}, {"service": "google.ads.googleads.v17.services.ShareablePreviewService"}, {"service": "google.ads.googleads.v17.services.SharedCriterionService"}, {"service": "google.ads.googleads.v17.services.SharedSetService"}, {"service": "google.ads.googleads.v17.services.SmartCampaignSettingService"}, {"service": "google.ads.googleads.v17.services.SmartCampaignSuggestService"}, {"service": "google.ads.googleads.v17.services.ThirdPartyAppAnalyticsLinkService"}, {"service": "google.ads.googleads.v17.services.TravelAssetSuggestionService"}, {"service": "google.ads.googleads.v17.services.UserDataService"}, {"service": "google.ads.googleads.v17.services.UserListCustomerTypeService"}, {"service": "google.ads.googleads.v17.services.UserListService"}], "timeout": "14400s", "retryPolicy": {"initialBackoff": "5s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}