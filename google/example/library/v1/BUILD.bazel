# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "library_proto",
    srcs = [
        "library.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

proto_library_with_info(
    name = "library_proto_with_info",
    deps = [
        ":library_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "library_java_proto",
    deps = [":library_proto"],
)

java_grpc_library(
    name = "library_java_grpc",
    srcs = [":library_proto"],
    deps = [":library_java_proto"],
)

java_gapic_library(
    name = "library_java_gapic",
    srcs = [":library_proto_with_info"],
    gapic_yaml = "library_example_gapic.yaml",
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "//google/example/library:library_example_v1.yaml",
    test_deps = [
        ":library_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":library_java_proto",
    ],
)

java_gapic_test(
    name = "library_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.example.library.v1.LibraryServiceClientHttpJsonTest",
        "com.google.cloud.example.library.v1.LibraryServiceClientTest",
    ],
    runtime_deps = [":library_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-example-library-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":library_java_gapic",
        ":library_java_grpc",
        ":library_java_proto",
        ":library_proto",
    ],
)

go_proto_library(
    name = "library_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/example/library/v1",
    protos = [":library_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "library_go_gapic",
    srcs = [":library_proto_with_info"],
    grpc_service_config = "library_grpc_service_config.json",
    importpath = "google.golang.org/google/example/library/v1;library",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "//google/example/library:library_example_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":library_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-example-library-v1-go",
    deps = [
        ":library_go_gapic",
        ":library_go_gapic_srcjar-metadata.srcjar",
        ":library_go_gapic_srcjar-snippets.srcjar",
        ":library_go_gapic_srcjar-test.srcjar",
        ":library_go_proto",
    ],
)

py_gapic_library(
    name = "library_py_gapic",
    srcs = [":library_proto"],
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "//google/example/library:library_example_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "library_py_gapic_test",
    srcs = [
        "library_py_gapic_pytest.py",
        "library_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":library_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "example-library-v1-py",
    deps = [
        ":library_py_gapic",
    ],
)

php_proto_library(
    name = "library_php_proto",
    deps = [":library_proto"],
)

php_gapic_library(
    name = "library_php_gapic",
    srcs = [":library_proto_with_info"],
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "//google/example/library:library_example_v1.yaml",
    transport = "grpc+rest",
    deps = [":library_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-example-library-v1-php",
    deps = [
        ":library_php_gapic",
        ":library_php_proto",
    ],
)

nodejs_gapic_library(
    name = "library_nodejs_gapic",
    package_name = "@google-cloud/library",
    src = ":library_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "library_grpc_service_config.json",
    package = "google.cloud.example.library.v1",
    rest_numeric_enums = True,
    service_yaml = "//google/example/library:library_example_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "example-library-v1-nodejs",
    deps = [
        ":library_nodejs_gapic",
        ":library_proto",
    ],
)

ruby_proto_library(
    name = "library_ruby_proto",
    deps = [":library_proto"],
)

ruby_grpc_library(
    name = "library_ruby_grpc",
    srcs = [":library_proto"],
    deps = [":library_ruby_proto"],
)

# Uncomment the following once the space issue has been fixed.
ruby_cloud_gapic_library(
    name = "library_ruby_gapic",
    srcs = [":library_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-example_library-v1",
        "ruby-cloud-env-prefix=LIBRARY",
        "ruby-cloud-product-url=https://cloud.google.com",
        "ruby-cloud-api-id=library-example.googleapis.com",
        "ruby-cloud-api-shortname=library",
    ],
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "This is a Google example service representing a simple digital library. It manages a collection of shelf resources, and each shelf owns a collection of book resources.",
    ruby_cloud_title = "Example Library V1",
    service_yaml = "//google/example/library:library_example_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":library_ruby_grpc",
        ":library_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-example-library-v1-ruby",
    deps = [
        ":library_ruby_gapic",
        ":library_ruby_grpc",
        ":library_ruby_proto",
    ],
)

csharp_proto_library(
    name = "library_csharp_proto",
    deps = [":library_proto"],
)

csharp_grpc_library(
    name = "library_csharp_grpc",
    srcs = [":library_proto"],
    deps = [":library_csharp_proto"],
)

csharp_gapic_library(
    name = "library_csharp_gapic",
    srcs = [":library_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "//google/example/library:library_example_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":library_csharp_grpc",
        ":library_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-example-library-v1-csharp",
    deps = [
        ":library_csharp_gapic",
        ":library_csharp_grpc",
        ":library_csharp_proto",
    ],
)

cc_proto_library(
    name = "library_cc_proto",
    deps = [":library_proto"],
)

cc_grpc_library(
    name = "library_cc_grpc",
    srcs = [":library_proto"],
    grpc_only = True,
    deps = [":library_cc_proto"],
)
