# This build file includes a target for the Ruby wrapper library for
# google-cloud-container.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for container.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "container_ruby_wrapper",
    srcs = ["//google/container/v1:container_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-container",
        "ruby-cloud-env-prefix=CONTAINER",
        "ruby-cloud-wrapper-of=v1:0.33;v1beta1:0.34",
        "ruby-cloud-product-url=https://cloud.google.com/kubernetes-engine",
        "ruby-cloud-api-id=container.googleapis.com",
        "ruby-cloud-api-shortname=container",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Builds and manages container-based applications, powered by the open source Kubernetes technology.",
    ruby_cloud_title = "Kubernetes Engine",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-container-ruby",
    deps = [
        ":container_ruby_wrapper",
    ],
)
