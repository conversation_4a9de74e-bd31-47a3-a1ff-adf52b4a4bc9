# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "container_proto",
    srcs = [
        "cluster_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:code_proto",
        "//google/rpc:status_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "container_proto_with_info",
    deps = [
        ":container_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "container_java_proto",
    deps = [":container_proto"],
)

java_grpc_library(
    name = "container_java_grpc",
    srcs = [":container_proto"],
    deps = [":container_java_proto"],
)

java_gapic_library(
    name = "container_java_gapic",
    srcs = [":container_proto_with_info"],
    gapic_yaml = "container_gapic.yaml",
    grpc_service_config = "container_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "container_v1beta1.yaml",
    test_deps = [
        ":container_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":container_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "container_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.container.v1beta1.ClusterManagerClientTest",
    ],
    runtime_deps = [":container_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-container-v1beta1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":container_java_gapic",
        ":container_java_grpc",
        ":container_java_proto",
        ":container_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "container_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/container/apiv1beta1/containerpb",
    protos = [":container_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:code_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "container_go_gapic",
    srcs = [":container_proto_with_info"],
    grpc_service_config = "container_grpc_service_config.json",
    importpath = "cloud.google.com/go/container/apiv1beta1;container",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "container_v1beta1.yaml",
    transport = "grpc",
    deps = [
        ":container_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-container-v1beta1-go",
    deps = [
        ":container_go_gapic",
        ":container_go_gapic_srcjar-metadata.srcjar",
        ":container_go_gapic_srcjar-snippets.srcjar",
        ":container_go_gapic_srcjar-test.srcjar",
        ":container_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "container_py_gapic",
    srcs = [":container_proto"],
    grpc_service_config = "container_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-container",
    ],
    rest_numeric_enums = True,
    service_yaml = "container_v1beta1.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "container_py_gapic_test",
    srcs = [
        "container_py_gapic_pytest.py",
        "container_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":container_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "container-v1beta1-py",
    deps = [
        ":container_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "container_php_proto",
    deps = [":container_proto"],
)

php_gapic_library(
    name = "container_php_gapic",
    srcs = [":container_proto_with_info"],
    grpc_service_config = "container_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "container_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":container_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-container-v1beta1-php",
    deps = [
        ":container_php_gapic",
        ":container_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "container_nodejs_gapic",
    package_name = "@google-cloud/container",
    src = ":container_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "container_grpc_service_config.json",
    package = "google.container.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "container_v1beta1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "container-v1beta1-nodejs",
    deps = [
        ":container_nodejs_gapic",
        ":container_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "container_ruby_proto",
    deps = [":container_proto"],
)

ruby_grpc_library(
    name = "container_ruby_grpc",
    srcs = [":container_proto"],
    deps = [":container_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "container_ruby_gapic",
    srcs = [":container_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=container.googleapis.com",
        "ruby-cloud-api-shortname=container",
        "ruby-cloud-env-prefix=CONTAINER",
        "ruby-cloud-gem-name=google-cloud-container-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/kubernetes-engine",
    ],
    grpc_service_config = "container_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Builds and manages container-based applications, powered by the open source Kubernetes technology.",
    ruby_cloud_title = "Kubernetes Engine V1beta1",
    service_yaml = "container_v1beta1.yaml",
    transport = "grpc",
    deps = [
        ":container_ruby_grpc",
        ":container_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-container-v1beta1-ruby",
    deps = [
        ":container_ruby_gapic",
        ":container_ruby_grpc",
        ":container_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "container_csharp_proto",
    extra_opts = [],
    deps = [":container_proto"],
)

csharp_grpc_library(
    name = "container_csharp_grpc",
    srcs = [":container_proto"],
    deps = [":container_csharp_proto"],
)

csharp_gapic_library(
    name = "container_csharp_gapic",
    srcs = [":container_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "container_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "container_v1beta1.yaml",
    transport = "grpc",
    deps = [
        ":container_csharp_grpc",
        ":container_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-container-v1beta1-csharp",
    deps = [
        ":container_csharp_gapic",
        ":container_csharp_grpc",
        ":container_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "container_cc_proto",
    deps = [":container_proto"],
)

cc_grpc_library(
    name = "container_cc_grpc",
    srcs = [":container_proto"],
    grpc_only = True,
    deps = [":container_cc_proto"],
)
