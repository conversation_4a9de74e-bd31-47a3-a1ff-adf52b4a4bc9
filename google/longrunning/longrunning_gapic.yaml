type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.longrunning
  python:
    package_name: google.longrunning.gapic
  go:
    package_name: cloud.google.com/go/longrunning/autogen
    domain_layer_location: cloud.google.com/go/longrunning
  csharp:
    package_name: Google.LongRunning
  ruby:
    package_name: Google::Longrunning
  php:
    package_name: Google\LongRunning
  nodejs:
    package_name: longrunning
interfaces:
- name: google.longrunning.Operations
  required_constructor_params:
  - service_address
  - scopes
  retry_params_def:
  - name: default
    initial_retry_delay_millis: 100
    retry_delay_multiplier: 1.3
    max_retry_delay_millis: 60000
    initial_rpc_timeout_millis: 90000
    rpc_timeout_multiplier: 1
    max_rpc_timeout_millis: 90000
    total_timeout_millis: 600000
  methods:
  - name: GetOperation
    retry_params_name: default
  - name: ListOperations
    retry_params_name: default
  - name: CancelOperation
    retry_codes_name: idempotent
    retry_params_name: default
  - name: DeleteOperation
    retry_codes_name: idempotent
    retry_params_name: default
  - name: WaitOperation
    surface_treatments:
    - include_languages:
      - go
      - java
      - csharp
      - ruby
      - nodejs
      - python
      - php
      visibility: DISABLED
