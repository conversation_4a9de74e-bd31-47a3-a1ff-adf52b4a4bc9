load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "operations_proto",
    srcs = ["operations.proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:descriptor_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
    ],
)

proto_library_with_info(
    name = "longrunning_proto_with_info",
    deps = [":operations_proto"],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "longrunning_java_proto",
    deps = [":operations_proto"],
)

java_grpc_library(
    name = "longrunning_java_grpc",
    srcs = [":operations_proto"],
    deps = [":longrunning_java_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-longrunning-java",
    transport = "grpc+rest",
    deps = [
        ":longrunning_java_grpc",
        ":longrunning_java_proto",
        ":operations_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "longrunning_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/longrunning/autogen/longrunningpb",
    protos = [":operations_proto"],
    visibility = ["//visibility:public"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "longrunning_go_gapic",
    srcs = [":longrunning_proto_with_info"],
    grpc_service_config = "longrunning_grpc_service_config.json",
    importpath = "cloud.google.com/go/longrunning/autogen;longrunning",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "longrunning.yaml",
    transport = "grpc+rest",
    deps = [":longrunning_go_proto"],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-longrunning-go",
    deps = [
        ":longrunning_go_gapic",
        ":longrunning_go_gapic_srcjar-metadata.srcjar",
        ":longrunning_go_gapic_srcjar-snippets.srcjar",
        ":longrunning_go_gapic_srcjar-test.srcjar",
        ":longrunning_go_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "longrunning_cc_proto",
    deps = [":operations_proto"],
)

cc_grpc_library(
    name = "longrunning_cc_grpc",
    srcs = [":operations_proto"],
    grpc_only = True,
    deps = [":longrunning_cc_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "longrunning_csharp_proto",
    deps = [":operations_proto"],
)

csharp_grpc_library(
    name = "longrunning_csharp_grpc",
    srcs = [":operations_proto"],
    deps = [":longrunning_csharp_proto"],
)

csharp_gapic_library(
    name = "longrunning_csharp_gapic",
    srcs = [":longrunning_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "longrunning_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "longrunning.yaml",
    deps = [
        ":longrunning_csharp_grpc",
        ":longrunning_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-longrunning-csharp",
    deps = [
        ":longrunning_csharp_gapic",
        ":longrunning_csharp_grpc",
        ":longrunning_csharp_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "longrunning_php_proto",
    deps = [":operations_proto"],
)

php_gapic_library(
    name = "longrunning_php_gapic",
    srcs = [":longrunning_proto_with_info"],
    grpc_service_config = "longrunning_grpc_service_config.json",
    migration_mode = "MIGRATING",
    rest_numeric_enums = False,
    service_yaml = "longrunning.yaml",
    transport = "grpc+rest",
    deps = [":longrunning_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-longrunning-php",
    deps = [
        ":longrunning_php_gapic",
        ":longrunning_php_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_proto_library",
)

py_proto_library(
    name = "operations_py_proto",
    deps = ["operations_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "operations-py",
    deps = [
        ":operations_py_proto",
    ],
)
