// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.bigtable.admin.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/bigtable/admin/v2/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Bigtable.Admin.V2";
option go_package = "cloud.google.com/go/bigtable/admin/apiv2/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "InstanceProto";
option java_package = "com.google.bigtable.admin.v2";
option php_namespace = "Google\\Cloud\\Bigtable\\Admin\\V2";
option ruby_package = "Google::Cloud::Bigtable::Admin::V2";
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};

// A collection of Bigtable [Tables][google.bigtable.admin.v2.Table] and
// the resources that serve them.
// All tables in an instance are served from all
// [Clusters][google.bigtable.admin.v2.Cluster] in the instance.
message Instance {
  option (google.api.resource) = {
    type: "bigtableadmin.googleapis.com/Instance"
    pattern: "projects/{project}/instances/{instance}"
  };

  // Possible states of an instance.
  enum State {
    // The state of the instance could not be determined.
    STATE_NOT_KNOWN = 0;

    // The instance has been successfully created and can serve requests
    // to its tables.
    READY = 1;

    // The instance is currently being created, and may be destroyed
    // if the creation process encounters an error.
    CREATING = 2;
  }

  // The type of the instance.
  enum Type {
    // The type of the instance is unspecified. If set when creating an
    // instance, a `PRODUCTION` instance will be created. If set when updating
    // an instance, the type will be left unchanged.
    TYPE_UNSPECIFIED = 0;

    // An instance meant for production use. `serve_nodes` must be set
    // on the cluster.
    PRODUCTION = 1;

    // DEPRECATED: Prefer PRODUCTION for all use cases, as it no longer enforces
    // a higher minimum node count than DEVELOPMENT.
    DEVELOPMENT = 2;
  }

  // The unique name of the instance. Values are of the form
  // `projects/{project}/instances/[a-z][a-z0-9\\-]+[a-z0-9]`.
  string name = 1;

  // Required. The descriptive name for this instance as it appears in UIs.
  // Can be changed at any time, but should be kept globally unique
  // to avoid confusion.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // (`OutputOnly`)
  // The current state of the instance.
  State state = 3;

  // The type of the instance. Defaults to `PRODUCTION`.
  Type type = 4;

  // Labels are a flexible and lightweight mechanism for organizing cloud
  // resources into groups that reflect a customer's organizational needs and
  // deployment strategies. They can be used to filter resources and aggregate
  // metrics.
  //
  // * Label keys must be between 1 and 63 characters long and must conform to
  //   the regular expression: `[\p{Ll}\p{Lo}][\p{Ll}\p{Lo}\p{N}_-]{0,62}`.
  // * Label values must be between 0 and 63 characters long and must conform to
  //   the regular expression: `[\p{Ll}\p{Lo}\p{N}_-]{0,63}`.
  // * No more than 64 labels can be associated with a given resource.
  // * Keys and values must both be under 128 bytes.
  map<string, string> labels = 5;

  // Output only. A server-assigned timestamp representing when this Instance
  // was created. For instances created before this field was added (August
  // 2021), this value is `seconds: 0, nanos: 1`.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  optional bool satisfies_pzs = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The Autoscaling targets for a Cluster. These determine the recommended nodes.
message AutoscalingTargets {
  // The cpu utilization that the Autoscaler should be trying to achieve.
  // This number is on a scale from 0 (no utilization) to
  // 100 (total utilization), and is limited between 10 and 80, otherwise it
  // will return INVALID_ARGUMENT error.
  int32 cpu_utilization_percent = 2;

  // The storage utilization that the Autoscaler should be trying to achieve.
  // This number is limited between 2560 (2.5TiB) and 5120 (5TiB) for a SSD
  // cluster and between 8192 (8TiB) and 16384 (16TiB) for an HDD cluster,
  // otherwise it will return INVALID_ARGUMENT error. If this value is set to 0,
  // it will be treated as if it were set to the default value: 2560 for SSD,
  // 8192 for HDD.
  int32 storage_utilization_gib_per_node = 3;
}

// Limits for the number of nodes a Cluster can autoscale up/down to.
message AutoscalingLimits {
  // Required. Minimum number of nodes to scale down to.
  int32 min_serve_nodes = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Maximum number of nodes to scale up to.
  int32 max_serve_nodes = 2 [(google.api.field_behavior) = REQUIRED];
}

// A resizable group of nodes in a particular cloud location, capable
// of serving all [Tables][google.bigtable.admin.v2.Table] in the parent
// [Instance][google.bigtable.admin.v2.Instance].
message Cluster {
  option (google.api.resource) = {
    type: "bigtableadmin.googleapis.com/Cluster"
    pattern: "projects/{project}/instances/{instance}/clusters/{cluster}"
  };

  // Possible states of a cluster.
  enum State {
    // The state of the cluster could not be determined.
    STATE_NOT_KNOWN = 0;

    // The cluster has been successfully created and is ready to serve requests.
    READY = 1;

    // The cluster is currently being created, and may be destroyed
    // if the creation process encounters an error.
    // A cluster may not be able to serve requests while being created.
    CREATING = 2;

    // The cluster is currently being resized, and may revert to its previous
    // node count if the process encounters an error.
    // A cluster is still capable of serving requests while being resized,
    // but may exhibit performance as if its number of allocated nodes is
    // between the starting and requested states.
    RESIZING = 3;

    // The cluster has no backing nodes. The data (tables) still
    // exist, but no operations can be performed on the cluster.
    DISABLED = 4;
  }

  // Possible node scaling factors of the clusters. Node scaling delivers better
  // latency and more throughput by removing node boundaries.
  enum NodeScalingFactor {
    // No node scaling specified. Defaults to NODE_SCALING_FACTOR_1X.
    NODE_SCALING_FACTOR_UNSPECIFIED = 0;

    // The cluster is running with a scaling factor of 1.
    NODE_SCALING_FACTOR_1X = 1;

    // The cluster is running with a scaling factor of 2.
    // All node count values must be in increments of 2 with this scaling factor
    // enabled, otherwise an INVALID_ARGUMENT error will be returned.
    NODE_SCALING_FACTOR_2X = 2;
  }

  // Autoscaling config for a cluster.
  message ClusterAutoscalingConfig {
    // Required. Autoscaling limits for this cluster.
    AutoscalingLimits autoscaling_limits = 1
        [(google.api.field_behavior) = REQUIRED];

    // Required. Autoscaling targets for this cluster.
    AutoscalingTargets autoscaling_targets = 2
        [(google.api.field_behavior) = REQUIRED];
  }

  // Configuration for a cluster.
  message ClusterConfig {
    // Autoscaling configuration for this cluster.
    ClusterAutoscalingConfig cluster_autoscaling_config = 1;
  }

  // Cloud Key Management Service (Cloud KMS) settings for a CMEK-protected
  // cluster.
  message EncryptionConfig {
    // Describes the Cloud KMS encryption key that will be used to protect the
    // destination Bigtable cluster. The requirements for this key are:
    //  1) The Cloud Bigtable service account associated with the project that
    //  contains this cluster must be granted the
    //  `cloudkms.cryptoKeyEncrypterDecrypter` role on the CMEK key.
    //  2) Only regional keys can be used and the region of the CMEK key must
    //  match the region of the cluster.
    //  3) All clusters within an instance must use the same CMEK key.
    // Values are of the form
    // `projects/{project}/locations/{location}/keyRings/{keyring}/cryptoKeys/{key}`
    string kms_key_name = 1 [(google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }];
  }

  // The unique name of the cluster. Values are of the form
  // `projects/{project}/instances/{instance}/clusters/[a-z][-a-z0-9]*`.
  string name = 1;

  // Immutable. The location where this cluster's nodes and storage reside. For
  // best performance, clients should be located as close as possible to this
  // cluster. Currently only zones are supported, so values should be of the
  // form `projects/{project}/locations/{zone}`.
  string location = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Output only. The current state of the cluster.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The number of nodes allocated to this cluster. More nodes enable higher
  // throughput and more consistent performance.
  int32 serve_nodes = 4;

  // Immutable. The node scaling factor of this cluster.
  NodeScalingFactor node_scaling_factor = 9
      [(google.api.field_behavior) = IMMUTABLE];

  oneof config {
    // Configuration for this cluster.
    ClusterConfig cluster_config = 7;
  }

  // Immutable. The type of storage used by this cluster to serve its
  // parent instance's tables, unless explicitly overridden.
  StorageType default_storage_type = 5
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The encryption configuration for CMEK-protected clusters.
  EncryptionConfig encryption_config = 6
      [(google.api.field_behavior) = IMMUTABLE];
}

// A configuration object describing how Cloud Bigtable should treat traffic
// from a particular end user application.
message AppProfile {
  option (google.api.resource) = {
    type: "bigtableadmin.googleapis.com/AppProfile"
    pattern: "projects/{project}/instances/{instance}/appProfiles/{app_profile}"
  };

  // Read/write requests are routed to the nearest cluster in the instance, and
  // will fail over to the nearest cluster that is available in the event of
  // transient errors or delays. Clusters in a region are considered
  // equidistant. Choosing this option sacrifices read-your-writes consistency
  // to improve availability.
  message MultiClusterRoutingUseAny {
    // If enabled, Bigtable will route the request based on the row key of the
    // request, rather than randomly. Instead, each row key will be assigned
    // to a cluster, and will stick to that cluster. If clusters are added or
    // removed, then this may affect which row keys stick to which clusters.
    // To avoid this, users can use a cluster group to specify which clusters
    // are to be used. In this case, new clusters that are not a part of the
    // cluster group will not be routed to, and routing will be unaffected by
    // the new cluster. Moreover, clusters specified in the cluster group cannot
    // be deleted unless removed from the cluster group.
    message RowAffinity {}

    // The set of clusters to route to. The order is ignored; clusters will be
    // tried in order of distance. If left empty, all clusters are eligible.
    repeated string cluster_ids = 1;

    // Possible algorithms for routing affinity. If enabled, Bigtable will
    // route between equidistant clusters in a deterministic order rather than
    // choosing randomly.
    //
    // This mechanism gives read-your-writes consistency for *most* requests
    // under *most* circumstances, without sacrificing availability. Consistency
    // is *not* guaranteed, as requests might still fail over between clusters
    // in the event of errors or latency.
    oneof affinity {
      // Row affinity sticky routing based on the row key of the request.
      // Requests that span multiple rows are routed non-deterministically.
      RowAffinity row_affinity = 3;
    }
  }

  // Unconditionally routes all read/write requests to a specific cluster.
  // This option preserves read-your-writes consistency but does not improve
  // availability.
  message SingleClusterRouting {
    // The cluster to which read/write requests should be routed.
    string cluster_id = 1;

    // Whether or not `CheckAndMutateRow` and `ReadModifyWriteRow` requests are
    // allowed by this app profile. It is unsafe to send these requests to
    // the same table/row/column in multiple clusters.
    bool allow_transactional_writes = 2;
  }

  // Possible priorities for an app profile. Note that higher priority writes
  // can sometimes queue behind lower priority writes to the same tablet, as
  // writes must be strictly sequenced in the durability log.
  enum Priority {
    // Default value. Mapped to PRIORITY_HIGH (the legacy behavior) on creation.
    PRIORITY_UNSPECIFIED = 0;

    PRIORITY_LOW = 1;

    PRIORITY_MEDIUM = 2;

    PRIORITY_HIGH = 3;
  }

  // Standard options for isolating this app profile's traffic from other use
  // cases.
  message StandardIsolation {
    // The priority of requests sent using this app profile.
    Priority priority = 1;
  }

  // Data Boost is a serverless compute capability that lets you run
  // high-throughput read jobs on your Bigtable data, without impacting the
  // performance of the clusters that handle your application traffic.
  // Currently, Data Boost exclusively supports read-only use-cases with
  // single-cluster routing.
  //
  // Data Boost reads are only guaranteed to see the results of writes that
  // were written at least 30 minutes ago. This means newly written values may
  // not become visible for up to 30m, and also means that old values may
  // remain visible for up to 30m after being deleted or overwritten. To
  // mitigate the staleness of the data, users may either wait 30m, or use
  // CheckConsistency.
  message DataBoostIsolationReadOnly {
    // Compute Billing Owner specifies how usage should be accounted when using
    // Data Boost. Compute Billing Owner also configures which Cloud Project is
    // charged for relevant quota.
    enum ComputeBillingOwner {
      // Unspecified value.
      COMPUTE_BILLING_OWNER_UNSPECIFIED = 0;

      // The host Cloud Project containing the targeted Bigtable Instance /
      // Table pays for compute.
      HOST_PAYS = 1;
    }

    // The Compute Billing Owner for this Data Boost App Profile.
    optional ComputeBillingOwner compute_billing_owner = 1;
  }

  // The unique name of the app profile. Values are of the form
  // `projects/{project}/instances/{instance}/appProfiles/[_a-zA-Z0-9][-_.a-zA-Z0-9]*`.
  string name = 1;

  // Strongly validated etag for optimistic concurrency control. Preserve the
  // value returned from `GetAppProfile` when calling `UpdateAppProfile` to
  // fail the request if there has been a modification in the mean time. The
  // `update_mask` of the request need not include `etag` for this protection
  // to apply.
  // See [Wikipedia](https://en.wikipedia.org/wiki/HTTP_ETag) and
  // [RFC 7232](https://tools.ietf.org/html/rfc7232#section-2.3) for more
  // details.
  string etag = 2;

  // Long form description of the use case for this AppProfile.
  string description = 3;

  // The routing policy for all read/write requests that use this app profile.
  // A value must be explicitly set.
  oneof routing_policy {
    // Use a multi-cluster routing policy.
    MultiClusterRoutingUseAny multi_cluster_routing_use_any = 5;

    // Use a single-cluster routing policy.
    SingleClusterRouting single_cluster_routing = 6;
  }

  // Options for isolating this app profile's traffic from other use cases.
  oneof isolation {
    // This field has been deprecated in favor of `standard_isolation.priority`.
    // If you set this field, `standard_isolation.priority` will be set instead.
    //
    // The priority of requests sent using this app profile.
    Priority priority = 7 [deprecated = true];

    // The standard options used for isolating this app profile's traffic from
    // other use cases.
    StandardIsolation standard_isolation = 11;

    // Specifies that this app profile is intended for read-only usage via the
    // Data Boost feature.
    DataBoostIsolationReadOnly data_boost_isolation_read_only = 10;
  }
}

// A tablet is a defined by a start and end key and is explained in
// https://cloud.google.com/bigtable/docs/overview#architecture and
// https://cloud.google.com/bigtable/docs/performance#optimization.
// A Hot tablet is a tablet that exhibits high average cpu usage during the time
// interval from start time to end time.
message HotTablet {
  option (google.api.resource) = {
    type: "bigtableadmin.googleapis.com/HotTablet"
    pattern: "projects/{project}/instances/{instance}/clusters/{cluster}/hotTablets/{hot_tablet}"
  };

  // The unique name of the hot tablet. Values are of the form
  // `projects/{project}/instances/{instance}/clusters/{cluster}/hotTablets/[a-zA-Z0-9_-]*`.
  string name = 1;

  // Name of the table that contains the tablet. Values are of the form
  // `projects/{project}/instances/{instance}/tables/[_a-zA-Z0-9][-_.a-zA-Z0-9]*`.
  string table_name = 2 [(google.api.resource_reference) = {
    type: "bigtableadmin.googleapis.com/Table"
  }];

  // Output only. The start time of the hot tablet.
  google.protobuf.Timestamp start_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The end time of the hot tablet.
  google.protobuf.Timestamp end_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Tablet Start Key (inclusive).
  string start_key = 5;

  // Tablet End Key (inclusive).
  string end_key = 6;

  // Output only. The average CPU usage spent by a node on this tablet over the
  // start_time to end_time time range. The percentage is the amount of CPU used
  // by the node to serve the tablet, from 0% (tablet was not interacted with)
  // to 100% (the node spent all cycles serving the hot tablet).
  float node_cpu_usage_percent = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
