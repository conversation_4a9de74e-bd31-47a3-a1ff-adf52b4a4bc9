{"methodConfig": [{"name": [{"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "CreateTable"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "ModifyColumnFamilies"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "DeleteTable"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "DeleteBackup"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "DeleteSnapshot"}], "timeout": "300s"}, {"name": [{"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "SetIamPolicy"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "RestoreTable"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "CreateBackup"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "UpdateBackup"}], "timeout": "60s"}, {"name": [{"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "ListTables"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "GetTable"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "GenerateConsistencyToken"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "GetIamPolicy"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "TestIamPermissions"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "GetSnapshot"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "ListSnapshots"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "GetBackup"}, {"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "ListBackups"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 2, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "CheckConsistency"}], "timeout": "3600s", "retryPolicy": {"maxAttempts": 100, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 2, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.bigtable.admin.v2.BigtableTableAdmin", "method": "DropRowRange"}], "timeout": "3600s"}, {"name": [{"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "CreateInstance"}], "timeout": "300s"}, {"name": [{"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "GetInstance"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "ListInstances"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "UpdateInstance"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "PartialUpdateInstance"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "GetCluster"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "ListClusters"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "UpdateCluster"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "GetAppProfile"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "ListAppProfiles"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "UpdateAppProfile"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "GetIamPolicy"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "TestIamPermissions"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "ListHotTablets"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 2, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "DeleteInstance"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "CreateCluster"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "DeleteCluster"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "CreateAppProfile"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "DeleteAppProfile"}, {"service": "google.bigtable.admin.v2.BigtableInstanceAdmin", "method": "SetIamPolicy"}], "timeout": "60s"}]}