// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.bigtable.admin.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/bigtable/admin/v2/instance.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Bigtable.Admin.V2";
option go_package = "cloud.google.com/go/bigtable/admin/apiv2/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "BigtableInstanceAdminProto";
option java_package = "com.google.bigtable.admin.v2";
option php_namespace = "Google\\Cloud\\Bigtable\\Admin\\V2";
option ruby_package = "Google::Cloud::Bigtable::Admin::V2";

// Service for creating, configuring, and deleting Cloud Bigtable Instances and
// Clusters. Provides access to the Instance and Cluster schemas only, not the
// tables' metadata or data stored in those tables.
service BigtableInstanceAdmin {
  option (google.api.default_host) = "bigtableadmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigtable.admin,"
      "https://www.googleapis.com/auth/bigtable.admin.cluster,"
      "https://www.googleapis.com/auth/bigtable.admin.instance,"
      "https://www.googleapis.com/auth/cloud-bigtable.admin,"
      "https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Create an instance within a project.
  //
  // Note that exactly one of Cluster.serve_nodes and
  // Cluster.cluster_config.cluster_autoscaling_config can be set. If
  // serve_nodes is set to non-zero, then the cluster is manually scaled. If
  // cluster_config.cluster_autoscaling_config is non-empty, then autoscaling is
  // enabled.
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/instances"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,instance_id,instance,clusters";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "CreateInstanceMetadata"
    };
  }

  // Gets information about an instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists information about instances in a project.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates an instance within a project. This method updates only the display
  // name and type for an Instance. To update other Instance properties, such as
  // labels, use PartialUpdateInstance.
  rpc UpdateInstance(Instance) returns (Instance) {
    option (google.api.http) = {
      put: "/v2/{name=projects/*/instances/*}"
      body: "*"
    };
  }

  // Partially updates an instance within a project. This method can modify all
  // fields of an Instance and is the preferred way to update an Instance.
  rpc PartialUpdateInstance(PartialUpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{instance.name=projects/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "UpdateInstanceMetadata"
    };
  }

  // Delete an instance from a project.
  rpc DeleteInstance(DeleteInstanceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a cluster within an instance.
  //
  // Note that exactly one of Cluster.serve_nodes and
  // Cluster.cluster_config.cluster_autoscaling_config can be set. If
  // serve_nodes is set to non-zero, then the cluster is manually scaled. If
  // cluster_config.cluster_autoscaling_config is non-empty, then autoscaling is
  // enabled.
  rpc CreateCluster(CreateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/instances/*}/clusters"
      body: "cluster"
    };
    option (google.api.method_signature) = "parent,cluster_id,cluster";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "CreateClusterMetadata"
    };
  }

  // Gets information about a cluster.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/instances/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists information about clusters in an instance.
  rpc ListClusters(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/instances/*}/clusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a cluster within an instance.
  //
  // Note that UpdateCluster does not support updating
  // cluster_config.cluster_autoscaling_config. In order to update it, you
  // must use PartialUpdateCluster.
  rpc UpdateCluster(Cluster) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      put: "/v2/{name=projects/*/instances/*/clusters/*}"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "UpdateClusterMetadata"
    };
  }

  // Partially updates a cluster within a project. This method is the preferred
  // way to update a Cluster.
  //
  // To enable and update autoscaling, set
  // cluster_config.cluster_autoscaling_config. When autoscaling is enabled,
  // serve_nodes is treated as an OUTPUT_ONLY field, meaning that updates to it
  // are ignored. Note that an update cannot simultaneously set serve_nodes to
  // non-zero and cluster_config.cluster_autoscaling_config to non-empty, and
  // also specify both in the update_mask.
  //
  // To disable autoscaling, clear cluster_config.cluster_autoscaling_config,
  // and explicitly set a serve_node count via the update_mask.
  rpc PartialUpdateCluster(PartialUpdateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{cluster.name=projects/*/instances/*/clusters/*}"
      body: "cluster"
    };
    option (google.api.method_signature) = "cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "PartialUpdateClusterMetadata"
    };
  }

  // Deletes a cluster from an instance.
  rpc DeleteCluster(DeleteClusterRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/instances/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an app profile within an instance.
  rpc CreateAppProfile(CreateAppProfileRequest) returns (AppProfile) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/instances/*}/appProfiles"
      body: "app_profile"
    };
    option (google.api.method_signature) = "parent,app_profile_id,app_profile";
  }

  // Gets information about an app profile.
  rpc GetAppProfile(GetAppProfileRequest) returns (AppProfile) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/instances/*/appProfiles/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists information about app profiles in an instance.
  rpc ListAppProfiles(ListAppProfilesRequest)
      returns (ListAppProfilesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/instances/*}/appProfiles"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates an app profile within an instance.
  rpc UpdateAppProfile(UpdateAppProfileRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{app_profile.name=projects/*/instances/*/appProfiles/*}"
      body: "app_profile"
    };
    option (google.api.method_signature) = "app_profile,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AppProfile"
      metadata_type: "UpdateAppProfileMetadata"
    };
  }

  // Deletes an app profile from an instance.
  rpc DeleteAppProfile(DeleteAppProfileRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/instances/*/appProfiles/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the access control policy for an instance resource. Returns an empty
  // policy if an instance exists but does not have a policy set.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/instances/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Sets the access control policy on an instance resource. Replaces any
  // existing policy.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/instances/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Returns permissions that the caller has on the specified instance resource.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/instances/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }

  // Lists hot tablets in a cluster, within the time range provided. Hot
  // tablets are ordered based on CPU usage.
  rpc ListHotTablets(ListHotTabletsRequest) returns (ListHotTabletsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/instances/*/clusters/*}/hotTablets"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for BigtableInstanceAdmin.CreateInstance.
message CreateInstanceRequest {
  // Required. The unique name of the project in which to create the new
  // instance. Values are of the form `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The ID to be used when referring to the new instance within its
  // project, e.g., just `myinstance` rather than
  // `projects/myproject/instances/myinstance`.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The instance to create.
  // Fields marked `OutputOnly` must be left blank.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The clusters to be created within the instance, mapped by desired
  // cluster ID, e.g., just `mycluster` rather than
  // `projects/myproject/instances/myinstance/clusters/mycluster`.
  // Fields marked `OutputOnly` must be left blank.
  // Currently, at most four clusters can be specified.
  map<string, Cluster> clusters = 4 [(google.api.field_behavior) = REQUIRED];
}

// Request message for BigtableInstanceAdmin.GetInstance.
message GetInstanceRequest {
  // Required. The unique name of the requested instance. Values are of the form
  // `projects/{project}/instances/{instance}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];
}

// Request message for BigtableInstanceAdmin.ListInstances.
message ListInstancesRequest {
  // Required. The unique name of the project for which a list of instances is
  // requested. Values are of the form `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // DEPRECATED: This field is unused and ignored.
  string page_token = 2;
}

// Response message for BigtableInstanceAdmin.ListInstances.
message ListInstancesResponse {
  // The list of requested instances.
  repeated Instance instances = 1;

  // Locations from which Instance information could not be retrieved,
  // due to an outage or some other transient condition.
  // Instances whose Clusters are all in one of the failed locations
  // may be missing from `instances`, and Instances with at least one
  // Cluster in a failed location may only have partial information returned.
  // Values are of the form `projects/<project>/locations/<zone_id>`
  repeated string failed_locations = 2;

  // DEPRECATED: This field is unused and ignored.
  string next_page_token = 3;
}

// Request message for BigtableInstanceAdmin.PartialUpdateInstance.
message PartialUpdateInstanceRequest {
  // Required. The Instance which will (partially) replace the current value.
  Instance instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The subset of Instance fields which should be replaced.
  // Must be explicitly set.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for BigtableInstanceAdmin.DeleteInstance.
message DeleteInstanceRequest {
  // Required. The unique name of the instance to be deleted.
  // Values are of the form `projects/{project}/instances/{instance}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];
}

// Request message for BigtableInstanceAdmin.CreateCluster.
message CreateClusterRequest {
  // Required. The unique name of the instance in which to create the new
  // cluster. Values are of the form `projects/{project}/instances/{instance}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];

  // Required. The ID to be used when referring to the new cluster within its
  // instance, e.g., just `mycluster` rather than
  // `projects/myproject/instances/myinstance/clusters/mycluster`.
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The cluster to be created.
  // Fields marked `OutputOnly` must be left blank.
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for BigtableInstanceAdmin.GetCluster.
message GetClusterRequest {
  // Required. The unique name of the requested cluster. Values are of the form
  // `projects/{project}/instances/{instance}/clusters/{cluster}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Cluster"
    }
  ];
}

// Request message for BigtableInstanceAdmin.ListClusters.
message ListClustersRequest {
  // Required. The unique name of the instance for which a list of clusters is
  // requested. Values are of the form
  // `projects/{project}/instances/{instance}`. Use `{instance} = '-'` to list
  // Clusters for all Instances in a project, e.g.,
  // `projects/myproject/instances/-`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];

  // DEPRECATED: This field is unused and ignored.
  string page_token = 2;
}

// Response message for BigtableInstanceAdmin.ListClusters.
message ListClustersResponse {
  // The list of requested clusters.
  repeated Cluster clusters = 1;

  // Locations from which Cluster information could not be retrieved,
  // due to an outage or some other transient condition.
  // Clusters from these locations may be missing from `clusters`,
  // or may only have partial information returned.
  // Values are of the form `projects/<project>/locations/<zone_id>`
  repeated string failed_locations = 2;

  // DEPRECATED: This field is unused and ignored.
  string next_page_token = 3;
}

// Request message for BigtableInstanceAdmin.DeleteCluster.
message DeleteClusterRequest {
  // Required. The unique name of the cluster to be deleted. Values are of the
  // form `projects/{project}/instances/{instance}/clusters/{cluster}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Cluster"
    }
  ];
}

// The metadata for the Operation returned by CreateInstance.
message CreateInstanceMetadata {
  // The request that prompted the initiation of this CreateInstance operation.
  CreateInstanceRequest original_request = 1;

  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 2;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 3;
}

// The metadata for the Operation returned by UpdateInstance.
message UpdateInstanceMetadata {
  // The request that prompted the initiation of this UpdateInstance operation.
  PartialUpdateInstanceRequest original_request = 1;

  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 2;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 3;
}

// The metadata for the Operation returned by CreateCluster.
message CreateClusterMetadata {
  // Progress info for copying a table's data to the new cluster.
  message TableProgress {
    enum State {
      STATE_UNSPECIFIED = 0;

      // The table has not yet begun copying to the new cluster.
      PENDING = 1;

      // The table is actively being copied to the new cluster.
      COPYING = 2;

      // The table has been fully copied to the new cluster.
      COMPLETED = 3;

      // The table was deleted before it finished copying to the new cluster.
      // Note that tables deleted after completion will stay marked as
      // COMPLETED, not CANCELLED.
      CANCELLED = 4;
    }

    // Estimate of the size of the table to be copied.
    int64 estimated_size_bytes = 2;

    // Estimate of the number of bytes copied so far for this table.
    // This will eventually reach 'estimated_size_bytes' unless the table copy
    // is CANCELLED.
    int64 estimated_copied_bytes = 3;

    State state = 4;
  }

  // The request that prompted the initiation of this CreateCluster operation.
  CreateClusterRequest original_request = 1;

  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 2;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 3;

  // Keys: the full `name` of each table that existed in the instance when
  // CreateCluster was first called, i.e.
  // `projects/<project>/instances/<instance>/tables/<table>`. Any table added
  // to the instance by a later API call will be created in the new cluster by
  // that API call, not this one.
  //
  // Values: information on how much of a table's data has been copied to the
  // newly-created cluster so far.
  map<string, TableProgress> tables = 4;
}

// The metadata for the Operation returned by UpdateCluster.
message UpdateClusterMetadata {
  // The request that prompted the initiation of this UpdateCluster operation.
  Cluster original_request = 1;

  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 2;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 3;
}

// The metadata for the Operation returned by PartialUpdateCluster.
message PartialUpdateClusterMetadata {
  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 1;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 2;

  // The original request for PartialUpdateCluster.
  PartialUpdateClusterRequest original_request = 3;
}

// Request message for BigtableInstanceAdmin.PartialUpdateCluster.
message PartialUpdateClusterRequest {
  // Required. The Cluster which contains the partial updates to be applied,
  // subject to the update_mask.
  Cluster cluster = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The subset of Cluster fields which should be replaced.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for BigtableInstanceAdmin.CreateAppProfile.
message CreateAppProfileRequest {
  // Required. The unique name of the instance in which to create the new app
  // profile. Values are of the form `projects/{project}/instances/{instance}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];

  // Required. The ID to be used when referring to the new app profile within
  // its instance, e.g., just `myprofile` rather than
  // `projects/myproject/instances/myinstance/appProfiles/myprofile`.
  string app_profile_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The app profile to be created.
  // Fields marked `OutputOnly` will be ignored.
  AppProfile app_profile = 3 [(google.api.field_behavior) = REQUIRED];

  // If true, ignore safety checks when creating the app profile.
  bool ignore_warnings = 4;
}

// Request message for BigtableInstanceAdmin.GetAppProfile.
message GetAppProfileRequest {
  // Required. The unique name of the requested app profile. Values are of the
  // form `projects/{project}/instances/{instance}/appProfiles/{app_profile}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/AppProfile"
    }
  ];
}

// Request message for BigtableInstanceAdmin.ListAppProfiles.
message ListAppProfilesRequest {
  // Required. The unique name of the instance for which a list of app profiles
  // is requested. Values are of the form
  // `projects/{project}/instances/{instance}`.
  // Use `{instance} = '-'` to list AppProfiles for all Instances in a project,
  // e.g., `projects/myproject/instances/-`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];

  // Maximum number of results per page.
  //
  // A page_size of zero lets the server choose the number of items to return.
  // A page_size which is strictly positive will return at most that many items.
  // A negative page_size will cause an error.
  //
  // Following the first request, subsequent paginated calls are not required
  // to pass a page_size. If a page_size is set in subsequent calls, it must
  // match the page_size given in the first request.
  int32 page_size = 3;

  // The value of `next_page_token` returned by a previous call.
  string page_token = 2;
}

// Response message for BigtableInstanceAdmin.ListAppProfiles.
message ListAppProfilesResponse {
  // The list of requested app profiles.
  repeated AppProfile app_profiles = 1;

  // Set if not all app profiles could be returned in a single response.
  // Pass this value to `page_token` in another request to get the next
  // page of results.
  string next_page_token = 2;

  // Locations from which AppProfile information could not be retrieved,
  // due to an outage or some other transient condition.
  // AppProfiles from these locations may be missing from `app_profiles`.
  // Values are of the form `projects/<project>/locations/<zone_id>`
  repeated string failed_locations = 3;
}

// Request message for BigtableInstanceAdmin.UpdateAppProfile.
message UpdateAppProfileRequest {
  // Required. The app profile which will (partially) replace the current value.
  AppProfile app_profile = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The subset of app profile fields which should be replaced.
  // If unset, all fields will be replaced.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, ignore safety checks when updating the app profile.
  bool ignore_warnings = 3;
}

// Request message for BigtableInstanceAdmin.DeleteAppProfile.
message DeleteAppProfileRequest {
  // Required. The unique name of the app profile to be deleted. Values are of
  // the form
  // `projects/{project}/instances/{instance}/appProfiles/{app_profile}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/AppProfile"
    }
  ];

  // Required. If true, ignore safety checks when deleting the app profile.
  bool ignore_warnings = 2 [(google.api.field_behavior) = REQUIRED];
}

// The metadata for the Operation returned by UpdateAppProfile.
message UpdateAppProfileMetadata {}

// Request message for BigtableInstanceAdmin.ListHotTablets.
message ListHotTabletsRequest {
  // Required. The cluster name to list hot tablets.
  // Value is in the following form:
  // `projects/{project}/instances/{instance}/clusters/{cluster}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Cluster"
    }
  ];

  // The start time to list hot tablets. The hot tablets in the response will
  // have start times between the requested start time and end time. Start time
  // defaults to Now if it is unset, and end time defaults to Now - 24 hours if
  // it is unset. The start time should be less than the end time, and the
  // maximum allowed time range between start time and end time is 48 hours.
  // Start time and end time should have values between Now and Now - 14 days.
  google.protobuf.Timestamp start_time = 2;

  // The end time to list hot tablets.
  google.protobuf.Timestamp end_time = 3;

  // Maximum number of results per page.
  //
  // A page_size that is empty or zero lets the server choose the number of
  // items to return. A page_size which is strictly positive will return at most
  // that many items. A negative page_size will cause an error.
  //
  // Following the first request, subsequent paginated calls do not need a
  // page_size field. If a page_size is set in subsequent calls, it must match
  // the page_size given in the first request.
  int32 page_size = 4;

  // The value of `next_page_token` returned by a previous call.
  string page_token = 5;
}

// Response message for BigtableInstanceAdmin.ListHotTablets.
message ListHotTabletsResponse {
  // List of hot tablets in the tables of the requested cluster that fall
  // within the requested time range. Hot tablets are ordered by node cpu usage
  // percent. If there are multiple hot tablets that correspond to the same
  // tablet within a 15-minute interval, only the hot tablet with the highest
  // node cpu usage will be included in the response.
  repeated HotTablet hot_tablets = 1;

  // Set if not all hot tablets could be returned in a single response.
  // Pass this value to `page_token` in another request to get the next
  // page of results.
  string next_page_token = 2;
}
