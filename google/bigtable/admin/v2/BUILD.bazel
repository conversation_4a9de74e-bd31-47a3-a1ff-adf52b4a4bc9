# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "admin_proto",
    srcs = [
        "bigtable_instance_admin.proto",
        "bigtable_table_admin.proto",
        "common.proto",
        "instance.proto",
        "table.proto",
        "types.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "admin_proto_with_info",
    deps = [
        ":admin_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "admin_java_proto",
    deps = [":admin_proto"],
)

java_grpc_library(
    name = "admin_java_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_java_proto"],
)

java_gapic_library(
    name = "admin_java_gapic",
    srcs = [":admin_proto_with_info"],
    gapic_yaml = "bigtableadmin_gapic.yaml",
    grpc_service_config = "bigtableadmin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigtableadmin_v2.yaml",
    test_deps = [
        ":admin_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":admin_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "admin_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigtable.admin.v2.BaseBigtableInstanceAdminClientTest",
        "com.google.cloud.bigtable.admin.v2.BaseBigtableTableAdminClientTest",
    ],
    runtime_deps = [":admin_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigtable-admin-v2-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":admin_java_gapic",
        ":admin_java_grpc",
        ":admin_java_proto",
        ":admin_proto",
    ],
)

go_proto_library(
    name = "admin_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigtable/admin/apiv2/adminpb",
    protos = [":admin_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "admin_go_gapic",
    srcs = [":admin_proto_with_info"],
    grpc_service_config = "bigtableadmin_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigtable/admin/apiv2;admin",
    rest_numeric_enums = True,
    service_yaml = "bigtableadmin_v2.yaml",
    transport = "grpc",
    deps = [
        ":admin_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigtable-admin-v2-go",
    deps = [
        ":admin_go_gapic",
        ":admin_go_gapic_srcjar-snippets.srcjar",
        ":admin_go_gapic_srcjar-test.srcjar",
        ":admin_go_proto",
    ],
)

py_gapic_library(
    name = "bigtable_admin_py_gapic",
    srcs = [":admin_proto"],
    grpc_service_config = "bigtableadmin_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=bigtable_admin",
        "autogen-snippets=False",
    ],
    rest_numeric_enums = True,
    service_yaml = "bigtableadmin_v2.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "bigtable_admin_py_gapic_test",
    srcs = [
        "bigtable_admin_py_gapic_pytest.py",
        "bigtable_admin_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":bigtable_admin_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigtable-admin-v2-py",
    deps = [
        ":bigtable_admin_py_gapic",
    ],
)

php_proto_library(
    name = "admin_php_proto",
    deps = [":admin_proto"],
)

php_gapic_library(
    name = "admin_php_gapic",
    srcs = [":admin_proto_with_info"],
    gapic_yaml = "bigtableadmin_gapic.yaml",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "bigtableadmin_v2.yaml",
    transport = "grpc+rest",
    deps = [":admin_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigtable-admin-v2-php",
    deps = [
        ":admin_php_gapic",
        ":admin_php_proto",
    ],
)

nodejs_gapic_library(
    name = "admin_nodejs_gapic",
    package_name = "@google-cloud/bigtable",
    src = ":admin_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "bigtableadmin_grpc_service_config.json",
    main_service = "bigtable",
    package = "google.bigtable.admin.v2",
    rest_numeric_enums = True,
    service_yaml = "bigtableadmin_v2.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigtable-admin-v2-nodejs",
    deps = [
        ":admin_nodejs_gapic",
        ":admin_proto",
    ],
)

ruby_proto_library(
    name = "admin_ruby_proto",
    deps = [":admin_proto"],
)

ruby_grpc_library(
    name = "admin_ruby_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "admin_ruby_gapic",
    srcs = [":admin_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigtable-admin-v2",
        "ruby-cloud-env-prefix=BIGTABLE",
        "ruby-cloud-product-url=https://cloud.google.com/bigtable",
        "ruby-cloud-api-id=bigtableadmin.googleapis.com",
        "ruby-cloud-api-shortname=bigtableadmin",
        "ruby-cloud-wrapper-gem-override=google-cloud-bigtable",
    ],
    grpc_service_config = "bigtableadmin_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Bigtable is a fully managed, scalable NoSQL database service for large analytical and operational workloads.",
    ruby_cloud_title = "Cloud Bigtable Admin V2",
    service_yaml = "bigtableadmin_v2.yaml",
    transport = "grpc",
    deps = [
        ":admin_ruby_grpc",
        ":admin_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigtable-admin-v2-ruby",
    deps = [
        ":admin_ruby_gapic",
        ":admin_ruby_grpc",
        ":admin_ruby_proto",
    ],
)

csharp_proto_library(
    name = "admin_csharp_proto",
    deps = [":admin_proto"],
)

csharp_grpc_library(
    name = "admin_csharp_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_csharp_proto"],
)

csharp_gapic_library(
    name = "admin_csharp_gapic",
    srcs = [":admin_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "bigtableadmin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigtableadmin_v2.yaml",
    transport = "grpc",
    deps = [
        ":admin_csharp_grpc",
        ":admin_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigtable-admin-v2-csharp",
    deps = [
        ":admin_csharp_gapic",
        ":admin_csharp_grpc",
        ":admin_csharp_proto",
    ],
)

cc_proto_library(
    name = "admin_cc_proto",
    deps = [":admin_proto"],
)

cc_grpc_library(
    name = "admin_cc_grpc",
    srcs = [":admin_proto"],
    grpc_only = True,
    deps = [":admin_cc_proto"],
)
