type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.bigtable.admin.v2
    interface_names:
      google.bigtable.admin.v2.BigtableInstanceAdmin: BaseBigtableInstanceAdmin
      google.bigtable.admin.v2.BigtableTableAdmin: BaseBigtableTableAdmin
interfaces:
- name: google.bigtable.admin.v2.BigtableInstanceAdmin
  methods:
  - name: CreateInstance
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 600000
  - name: PartialUpdateInstance
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 600000
  - name: CreateCluster
    long_running:
      initial_poll_delay_millis: 5000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000
      total_poll_timeout_millis: 21600000
  - name: UpdateCluster
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 600000
  - name: PartialUpdateCluster
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 600000
  - name: UpdateAppProfile
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 600000

- name: google.bigtable.admin.v2.BigtableTableAdmin
  methods:
  - name: CreateTableFromSnapshot
    long_running:
      initial_poll_delay_millis: 5000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000
      total_poll_timeout_millis: 3600000
  - name: SnapshotTable
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 600000
  - name: CreateBackup
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 600000
  - name: RestoreTable
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 600000
