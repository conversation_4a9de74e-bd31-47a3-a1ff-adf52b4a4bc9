// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.bigtable.admin.v2;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Bigtable.Admin.V2";
option go_package = "cloud.google.com/go/bigtable/admin/apiv2/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.bigtable.admin.v2";
option php_namespace = "Google\\Cloud\\Bigtable\\Admin\\V2";
option ruby_package = "Google::Cloud::Bigtable::Admin::V2";

// Storage media types for persisting Bigtable data.
enum StorageType {
  // The user did not specify a storage type.
  STORAGE_TYPE_UNSPECIFIED = 0;

  // Flash (SSD) storage should be used.
  SSD = 1;

  // Magnetic drive (HDD) storage should be used.
  HDD = 2;
}

// Encapsulates progress related information for a Cloud Bigtable long
// running operation.
message OperationProgress {
  // Percent completion of the operation.
  // Values are between 0 and 100 inclusive.
  int32 progress_percent = 1;

  // Time the request was received.
  google.protobuf.Timestamp start_time = 2;

  // If set, the time at which this operation failed or was completed
  // successfully.
  google.protobuf.Timestamp end_time = 3;
}
