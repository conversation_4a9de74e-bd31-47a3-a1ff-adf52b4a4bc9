type: google.api.Service
config_version: 3
name: bigtableadmin.googleapis.com
title: Cloud Bigtable Admin API

apis:
- name: google.bigtable.admin.v2.BigtableInstanceAdmin
- name: google.bigtable.admin.v2.BigtableTableAdmin

types:
- name: google.bigtable.admin.v2.Backup
- name: google.bigtable.admin.v2.CopyBackupMetadata
- name: google.bigtable.admin.v2.CreateAuthorizedViewMetadata
- name: google.bigtable.admin.v2.CreateBackupMetadata
- name: google.bigtable.admin.v2.CreateClusterMetadata
- name: google.bigtable.admin.v2.CreateInstanceMetadata
- name: google.bigtable.admin.v2.CreateTableFromSnapshotMetadata
- name: google.bigtable.admin.v2.OptimizeRestoredTableMetadata
- name: google.bigtable.admin.v2.PartialUpdateClusterMetadata
- name: google.bigtable.admin.v2.RestoreTableMetadata
- name: google.bigtable.admin.v2.SnapshotTableMetadata
- name: google.bigtable.admin.v2.UndeleteTableMetadata
- name: google.bigtable.admin.v2.UpdateAppProfileMetadata
- name: google.bigtable.admin.v2.UpdateAuthorizedViewMetadata
- name: google.bigtable.admin.v2.UpdateClusterMetadata
- name: google.bigtable.admin.v2.UpdateInstanceMetadata
- name: google.bigtable.admin.v2.UpdateTableMetadata

documentation:
  summary: Administer your Cloud Bigtable tables and instances.

backend:
  rules:
  - selector: 'google.bigtable.admin.v2.BigtableInstanceAdmin.*'
    deadline: 60.0
  - selector: 'google.bigtable.admin.v2.BigtableTableAdmin.*'
    deadline: 60.0
  - selector: google.bigtable.admin.v2.BigtableTableAdmin.CreateTable
    deadline: 130.0
  - selector: google.bigtable.admin.v2.BigtableTableAdmin.DropRowRange
    deadline: 900.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v2/{name=operations/**}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v2/{name=operations/**}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=operations/**}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=operations/projects/**}/operations'

authentication:
  rules:
  - selector: 'google.bigtable.admin.v2.BigtableInstanceAdmin.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.bigtable.admin.v2.BigtableInstanceAdmin.GetAppProfile
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.bigtable.admin.v2.BigtableInstanceAdmin.GetCluster
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.bigtable.admin.v2.BigtableInstanceAdmin.GetInstance
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.bigtable.admin.v2.BigtableInstanceAdmin.ListClusters
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.bigtable.admin.v2.BigtableInstanceAdmin.ListHotTablets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.bigtable.admin.v2.BigtableInstanceAdmin.ListInstances
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.bigtable.admin.v2.BigtableTableAdmin.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.table,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.table,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.bigtable.admin.v2.BigtableTableAdmin.GetTable
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.table,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.table,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.bigtable.admin.v2.BigtableTableAdmin.ListTables
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.table,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.table,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.longrunning.Operations.ListOperations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.admin,
        https://www.googleapis.com/auth/bigtable.admin.cluster,
        https://www.googleapis.com/auth/bigtable.admin.instance,
        https://www.googleapis.com/auth/cloud-bigtable.admin,
        https://www.googleapis.com/auth/cloud-bigtable.admin.cluster,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
