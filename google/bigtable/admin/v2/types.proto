// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.bigtable.admin.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Bigtable.Admin.V2";
option go_package = "cloud.google.com/go/bigtable/admin/apiv2/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "TypesProto";
option java_package = "com.google.bigtable.admin.v2";
option php_namespace = "Google\\Cloud\\Bigtable\\Admin\\V2";
option ruby_package = "Google::Cloud::Bigtable::Admin::V2";

// `Type` represents the type of data that is written to, read from, or stored
// in Bigtable. It is heavily based on the GoogleSQL standard to help maintain
// familiarity and consistency across products and features.
//
// For compatibility with Bigtable's existing untyped APIs, each `Type` includes
// an `Encoding` which describes how to convert to/from the underlying data.
//
// Each encoding also defines the following properties:
//
//  * Order-preserving: Does the encoded value sort consistently with the
//    original typed value? Note that Bigtable will always sort data based on
//    the raw encoded value, *not* the decoded type.
//     - Example: BYTES values sort in the same order as their raw encodings.
//     - Counterexample: Encoding INT64 as a fixed-width decimal string does
//       *not* preserve sort order when dealing with negative numbers.
//       `INT64(1) > INT64(-1)`, but `STRING("-00001") > STRING("00001)`.
//  * Self-delimiting: If we concatenate two encoded values, can we always tell
//    where the first one ends and the second one begins?
//     - Example: If we encode INT64s to fixed-width STRINGs, the first value
//       will always contain exactly N digits, possibly preceded by a sign.
//     - Counterexample: If we concatenate two UTF-8 encoded STRINGs, we have
//       no way to tell where the first one ends.
//  * Compatibility: Which other systems have matching encoding schemes? For
//    example, does this encoding have a GoogleSQL equivalent? HBase? Java?
message Type {
  // Bytes
  // Values of type `Bytes` are stored in `Value.bytes_value`.
  message Bytes {
    // Rules used to convert to/from lower level types.
    message Encoding {
      // Leaves the value "as-is"
      // * Order-preserving? Yes
      // * Self-delimiting? No
      // * Compatibility? N/A
      message Raw {}

      // Which encoding to use.
      oneof encoding {
        // Use `Raw` encoding.
        Raw raw = 1;
      }
    }

    // The encoding to use when converting to/from lower level types.
    Encoding encoding = 1;
  }

  // String
  // Values of type `String` are stored in `Value.string_value`.
  message String {
    // Rules used to convert to/from lower level types.
    message Encoding {
      // Deprecated: prefer the equivalent `Utf8Bytes`.
      message Utf8Raw {
        option deprecated = true;
      }

      // UTF-8 encoding
      // * Order-preserving? Yes (code point order)
      // * Self-delimiting? No
      // * Compatibility?
      //    - BigQuery Federation `TEXT` encoding
      //    - HBase `Bytes.toBytes`
      //    - Java `String#getBytes(StandardCharsets.UTF_8)`
      message Utf8Bytes {}

      // Which encoding to use.
      oneof encoding {
        // Deprecated: if set, converts to an empty `utf8_bytes`.
        Utf8Raw utf8_raw = 1 [deprecated = true];

        // Use `Utf8Bytes` encoding.
        Utf8Bytes utf8_bytes = 2;
      }
    }

    // The encoding to use when converting to/from lower level types.
    Encoding encoding = 1;
  }

  // Int64
  // Values of type `Int64` are stored in `Value.int_value`.
  message Int64 {
    // Rules used to convert to/from lower level types.
    message Encoding {
      // Encodes the value as an 8-byte big endian twos complement `Bytes`
      // value.
      // * Order-preserving? No (positive values only)
      // * Self-delimiting? Yes
      // * Compatibility?
      //    - BigQuery Federation `BINARY` encoding
      //    - HBase `Bytes.toBytes`
      //    - Java `ByteBuffer.putLong()` with `ByteOrder.BIG_ENDIAN`
      message BigEndianBytes {
        // Deprecated: ignored if set.
        Bytes bytes_type = 1;
      }

      // Which encoding to use.
      oneof encoding {
        // Use `BigEndianBytes` encoding.
        BigEndianBytes big_endian_bytes = 1;
      }
    }

    // The encoding to use when converting to/from lower level types.
    Encoding encoding = 1;
  }

  // bool
  // Values of type `Bool` are stored in `Value.bool_value`.
  message Bool {}

  // Float32
  // Values of type `Float32` are stored in `Value.float_value`.
  message Float32 {}

  // Float64
  // Values of type `Float64` are stored in `Value.float_value`.
  message Float64 {}

  // Timestamp
  // Values of type `Timestamp` are stored in `Value.timestamp_value`.
  message Timestamp {}

  // Date
  // Values of type `Date` are stored in `Value.date_value`.
  message Date {}

  // A structured data value, consisting of fields which map to dynamically
  // typed values.
  // Values of type `Struct` are stored in `Value.array_value` where entries are
  // in the same order and number as `field_types`.
  message Struct {
    // A struct field and its type.
    message Field {
      // The field name (optional). Fields without a `field_name` are considered
      // anonymous and cannot be referenced by name.
      string field_name = 1;

      // The type of values in this field.
      Type type = 2;
    }

    // The names and types of the fields in this struct.
    repeated Field fields = 1;
  }

  // An ordered list of elements of a given type.
  // Values of type `Array` are stored in `Value.array_value`.
  message Array {
    // The type of the elements in the array. This must not be `Array`.
    Type element_type = 1;
  }

  // A mapping of keys to values of a given type.
  // Values of type `Map` are stored in a `Value.array_value` where each entry
  // is another `Value.array_value` with two elements (the key and the value,
  // in that order).
  // Normally encoded Map values won't have repeated keys, however, clients are
  // expected to handle the case in which they do. If the same key appears
  // multiple times, the _last_ value takes precedence.
  message Map {
    // The type of a map key.
    // Only `Bytes`, `String`, and `Int64` are allowed as key types.
    Type key_type = 1;

    // The type of the values in a map.
    Type value_type = 2;
  }

  // A value that combines incremental updates into a summarized value.
  //
  // Data is never directly written or read using type `Aggregate`. Writes will
  // provide either the `input_type` or `state_type`, and reads will always
  // return the `state_type` .
  message Aggregate {
    // Computes the sum of the input values.
    // Allowed input: `Int64`
    // State: same as input
    message Sum {}

    // Computes the max of the input values.
    // Allowed input: `Int64`
    // State: same as input
    message Max {}

    // Computes the min of the input values.
    // Allowed input: `Int64`
    // State: same as input
    message Min {}

    // Computes an approximate unique count over the input values. When using
    // raw data as input, be careful to use a consistent encoding. Otherwise
    // the same value encoded differently could count more than once, or two
    // distinct values could count as identical.
    // Input: Any, or omit for Raw
    // State: TBD
    // Special state conversions: `Int64` (the unique count estimate)
    message HyperLogLogPlusPlusUniqueCount {}

    // Type of the inputs that are accumulated by this `Aggregate`, which must
    // specify a full encoding.
    // Use `AddInput` mutations to accumulate new inputs.
    Type input_type = 1;

    // Output only. Type that holds the internal accumulator state for the
    // `Aggregate`. This is a function of the `input_type` and `aggregator`
    // chosen, and will always specify a full encoding.
    Type state_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Which aggregator function to use. The configured types must match.
    oneof aggregator {
      // Sum aggregator.
      Sum sum = 4;

      // HyperLogLogPlusPlusUniqueCount aggregator.
      HyperLogLogPlusPlusUniqueCount hllpp_unique_count = 5;

      // Max aggregator.
      Max max = 6;

      // Min aggregator.
      Min min = 7;
    }
  }

  // The kind of type that this represents.
  oneof kind {
    // Bytes
    Bytes bytes_type = 1;

    // String
    String string_type = 2;

    // Int64
    Int64 int64_type = 5;

    // Float32
    Float32 float32_type = 12;

    // Float64
    Float64 float64_type = 9;

    // Bool
    Bool bool_type = 8;

    // Timestamp
    Timestamp timestamp_type = 10;

    // Date
    Date date_type = 11;

    // Aggregate
    Aggregate aggregate_type = 6;

    // Struct
    Struct struct_type = 7;

    // Array
    Array array_type = 3;

    // Map
    Map map_type = 4;
  }
}
