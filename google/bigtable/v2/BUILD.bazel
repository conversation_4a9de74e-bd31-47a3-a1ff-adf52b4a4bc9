# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "bigtable_proto",
    srcs = [
        "bigtable.proto",
        "data.proto",
        "feature_flags.proto",
        "request_stats.proto",
        "response_params.proto",
        "types.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/api:routing_proto",
        "//google/rpc:status_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "bigtable_proto_with_info",
    deps = [
        ":bigtable_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "bigtable_java_proto",
    deps = [":bigtable_proto"],
)

java_grpc_library(
    name = "bigtable_java_grpc",
    srcs = [":bigtable_proto"],
    deps = [":bigtable_java_proto"],
)

java_gapic_library(
    name = "bigtable_java_gapic",
    srcs = [":bigtable_proto_with_info"],
    gapic_yaml = "bigtable_gapic.yaml",
    grpc_service_config = "bigtable_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigtable_v2.yaml",
    test_deps = [
        ":bigtable_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":bigtable_java_proto",
    ],
)

java_gapic_test(
    name = "bigtable_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigtable.data.v2.BaseBigtableDataClientTest",
    ],
    runtime_deps = [":bigtable_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigtable-v2-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":bigtable_java_gapic",
        ":bigtable_java_grpc",
        ":bigtable_java_proto",
        ":bigtable_proto",
    ],
)

go_proto_library(
    name = "bigtable_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigtable/apiv2/bigtablepb",
    protos = [":bigtable_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "bigtable_go_gapic",
    srcs = [":bigtable_proto_with_info"],
    grpc_service_config = "bigtable_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigtable/apiv2;bigtable",
    rest_numeric_enums = True,
    service_yaml = "bigtable_v2.yaml",
    transport = "grpc",
    deps = [
        ":bigtable_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigtable-v2-go",
    deps = [
        ":bigtable_go_gapic",
        ":bigtable_go_gapic_srcjar-snippets.srcjar",
        ":bigtable_go_gapic_srcjar-test.srcjar",
        ":bigtable_go_proto",
    ],
)

py_gapic_library(
    name = "bigtable_py_gapic",
    srcs = [":bigtable_proto"],
    grpc_service_config = "bigtable_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "autogen-snippets=False",
    ],
    rest_numeric_enums = True,
    service_yaml = "bigtable_v2.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "bigtable_py_gapic_test",
    srcs = [
        "bigtable_py_gapic_pytest.py",
        "bigtable_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":bigtable_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "bigtable-v2-py",
    deps = [
        ":bigtable_py_gapic",
    ],
)

php_proto_library(
    name = "bigtable_php_proto",
    deps = [":bigtable_proto"],
)

php_gapic_library(
    name = "bigtable_php_gapic",
    srcs = [":bigtable_proto_with_info"],
    rest_numeric_enums = True,
    service_yaml = "bigtable_v2.yaml",
    transport = "grpc+rest",
    migration_mode = "NEW_SURFACE_ONLY",
    deps = [":bigtable_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigtable-v2-php",
    deps = [
        ":bigtable_php_gapic",
        ":bigtable_php_proto",
    ],
)

nodejs_gapic_library(
    name = "bigtable_nodejs_gapic",
    package_name = "@google-cloud/bigtable",
    src = ":bigtable_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "bigtable_grpc_service_config.json",
    handwritten_layer = True,
    main_service = "bigtable",
    package = "google.bigtable.v2",
    rest_numeric_enums = True,
    service_yaml = "bigtable_v2.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigtable-v2-nodejs",
    deps = [
        ":bigtable_nodejs_gapic",
        ":bigtable_proto",
    ],
)

ruby_proto_library(
    name = "bigtable_ruby_proto",
    deps = [":bigtable_proto"],
)

ruby_grpc_library(
    name = "bigtable_ruby_grpc",
    srcs = [":bigtable_proto"],
    deps = [":bigtable_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "bigtable_ruby_gapic",
    srcs = [":bigtable_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigtable-v2",
        "ruby-cloud-env-prefix=BIGTABLE",
        "ruby-cloud-product-url=https://cloud.google.com/bigtable",
        "ruby-cloud-api-id=bigtable.googleapis.com",
        "ruby-cloud-api-shortname=bigtable",
    ],
    grpc_service_config = "bigtable_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Bigtable is a fully managed, scalable NoSQL database service for large analytical and operational workloads.",
    ruby_cloud_title = "Cloud Bigtable V2",
    service_yaml = "bigtable_v2.yaml",
    deps = [
        ":bigtable_ruby_grpc",
        ":bigtable_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigtable-v2-ruby",
    deps = [
        ":bigtable_ruby_gapic",
        ":bigtable_ruby_grpc",
        ":bigtable_ruby_proto",
    ],
)

csharp_proto_library(
    name = "bigtable_csharp_proto",
    deps = [":bigtable_proto"],
)

csharp_grpc_library(
    name = "bigtable_csharp_grpc",
    srcs = [":bigtable_proto"],
    deps = [":bigtable_csharp_proto"],
)

csharp_gapic_library(
    name = "bigtable_csharp_gapic",
    srcs = [":bigtable_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "bigtable_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigtable_v2.yaml",
    deps = [
        ":bigtable_csharp_grpc",
        ":bigtable_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigtable-v2-csharp",
    deps = [
        ":bigtable_csharp_gapic",
        ":bigtable_csharp_grpc",
        ":bigtable_csharp_proto",
    ],
)

cc_proto_library(
    name = "bigtable_cc_proto",
    deps = [":bigtable_proto"],
)

cc_grpc_library(
    name = "bigtable_cc_grpc",
    srcs = [":bigtable_proto"],
    grpc_only = True,
    deps = [":bigtable_cc_proto"],
)
