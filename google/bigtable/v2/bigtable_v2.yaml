type: google.api.Service
config_version: 3
name: bigtable.googleapis.com
title: Cloud Bigtable API

apis:
- name: google.bigtable.v2.Bigtable

types:
- name: google.bigtable.v2.FeatureFlags
- name: google.bigtable.v2.ProtoRows
- name: google.bigtable.v2.RequestStats
- name: google.bigtable.v2.ResponseParams

documentation:
  summary: |-
    API for reading and writing the contents of Bigtable tables associated with
    a Google Cloud project.

authentication:
  rules:
  - selector: 'google.bigtable.v2.Bigtable.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.data,
        https://www.googleapis.com/auth/cloud-bigtable.data,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.bigtable.v2.Bigtable.ReadRows
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.data,
        https://www.googleapis.com/auth/bigtable.data.readonly,
        https://www.googleapis.com/auth/cloud-bigtable.data,
        https://www.googleapis.com/auth/cloud-bigtable.data.readonly,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.bigtable.v2.Bigtable.SampleRowKeys
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigtable.data,
        https://www.googleapis.com/auth/bigtable.data.readonly,
        https://www.googleapis.com/auth/cloud-bigtable.data,
        https://www.googleapis.com/auth/cloud-bigtable.data.readonly,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only

publishing:
  documentation_uri: https://cloud.google.com/bigtable/docs
  github_label: 'api: bigtable'
  doc_tag_prefix: bigtable
  organization: CLOUD
  library_settings:
  - version: google.bigtable.v2
    dotnet_settings:
      renamed_services:
        Bigtable: BigtableServiceApi
  proto_reference_documentation_uri: https://cloud.google.com/bigtable/docs/reference/data/rpc
