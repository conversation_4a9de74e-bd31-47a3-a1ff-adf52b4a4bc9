{"methodConfig": [{"name": [{"service": "google.bigtable.v2.Bigtable", "method": "CheckAndMutateRow"}, {"service": "google.bigtable.v2.Bigtable", "method": "ReadModifyWriteRow"}], "timeout": "20s"}, {"name": [{"service": "google.bigtable.v2.Bigtable", "method": "SampleRowKeys"}], "timeout": "60s"}, {"name": [{"service": "google.bigtable.v2.Bigtable", "method": "MutateRows"}], "timeout": "600s"}, {"name": [{"service": "google.bigtable.v2.Bigtable", "method": "ReadRows"}], "timeout": "43200s"}, {"name": [{"service": "google.bigtable.v2.Bigtable", "method": "MutateRow"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.010s", "maxAttempts": 5, "maxBackoff": "60s", "backoffMultiplier": 2, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.bigtable.v2.Bigtable", "method": "GenerateInitialChangeStreamPartitions"}], "timeout": "60s"}, {"name": [{"service": "google.bigtable.v2.Bigtable", "method": "ReadChangeStream"}], "timeout": "43200s"}]}