// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel;

import "google/actions/sdk/v2/interactionmodel/event_handler.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel;interactionmodel";
option java_multiple_files = true;
option java_outer_classname = "ConditionalEventProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel";

// Registers events that trigger as the result of a true condition.
message ConditionalEvent {
  // Required. Filter condition for this event to trigger. If condition is evaluated to
  // true then the associated `handler` will be triggered.
  // The following variable references are supported:
  //   `$session` - To reference data in session storage.
  //   `$user` - To reference data in user storage.
  // The following boolean operators are supported (with examples):
  //   `&&` - `session.params.counter > 0 && session.params.counter < 100`
  //   `||` - `session.params.foo == "John" || session.params.counter == "Adam"`
  //   `!`  - `!(session.params.counter == 5)`
  // The following comparisons are supported:
  //   `==`, `!=`, `<`, `>`, `<=`, `>=`
  // The following list and string operators are supported (with examples):
  //   `in`        - "Watermelon" in `session.params.fruitList`
  //   `size`      - `size(session.params.fruitList) > 2`
  //   `substring` - `session.params.fullName.contains("John")`
  string condition = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Destination scene which the conversation should jump to when the associated
  // condition is evaluated to true. The state of the current scene is destroyed
  // on the transition.
  string transition_to_scene = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Event handler which is triggered when the associated condition is evaluated
  // to `true`. Should execute before transitioning to the destination scene.
  // Useful to generate Prompts in response to events.
  EventHandler handler = 3 [(google.api.field_behavior) = OPTIONAL];
}
