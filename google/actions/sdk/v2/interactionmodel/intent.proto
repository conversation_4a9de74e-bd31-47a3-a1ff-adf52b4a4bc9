// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel;

import "google/actions/sdk/v2/interactionmodel/type/class_reference.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel;interactionmodel";
option java_multiple_files = true;
option java_outer_classname = "IntentProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel";

// Intents map open-ended user input to structured objects. Spoken
// phrases are matched to intents with Google's Natural Language Understanding
// (NLU). Intent matches can trigger events in your conversation design to
// progress the user's conversation.
// The intent name is specified in the name of the file.
message Intent {
  // Definition of a parameter which can be used inside training phrases.
  message IntentParameter {
    // Entity set references for an intent parameter.
    message EntitySetReferences {
      // A reference to the set of allowed entities for this intent parameter.
      message EntitySetReference {
        // Required. Identifies the specific collection of entities to be considered for a
        // given parameter. The corresponding entity set definition should be
        // present in the custom/entitySets/ directory.
        string entity_set = 1 [(google.api.field_behavior) = REQUIRED];
      }

      // Required. Entity set references for an intent parameter.
      repeated EntitySetReference entity_set_references = 1 [(google.api.field_behavior) = REQUIRED];
    }

    // Required. Unique name of the intent parameter. Can be used in conditions and
    // responses to reference intent parameters extracted by NLU with
    // $intent.params.[name].resolved
    string name = 1 [(google.api.field_behavior) = REQUIRED];

    // The type of the intent parameter.
    oneof parameter_type {
      // Optional. Declares the data type of this parameter.
      // This should not be set for built-in intents.
      google.actions.sdk.v2.interactionmodel.type.ClassReference type = 2 [(google.api.field_behavior) = OPTIONAL];

      // Optional. References to the sets of allowed entities for this intent parameter.
      // Only valid for parameters of a built-in intent. These
      // references point to entity sets in the 'custom/entitySets' directory.
      EntitySetReferences entity_set_references = 3 [(google.api.field_behavior) = OPTIONAL];
    }
  }

  // The list of parameters within the training phrases. All parameters must be
  // defined here to be used in the training phrase.
  repeated IntentParameter parameters = 1;

  // Training phrases allow Google’s NLU to automatically match intents with
  // user input. The more unique phrases that are provided, the better chance
  // this intent will be matched.
  // The following is the format of training phrase part which are annotated.
  // Note that `auto` field is optional and the default behavior when `auto` is
  // not specified is equivalent to `auto=false`.
  // `($<paramName> '<sample text>' auto=<true or false>)`
  // `auto = true` means the part was auto annotated by NLU.
  // `auto = false` means the part was annotated by the user. This is the
  //     default when auto is not specified.
  // Example:
  // "Book a flight from ($source 'San Francisco' auto=false) to ($dest
  // 'Vancouver')"
  repeated string training_phrases = 2;
}
