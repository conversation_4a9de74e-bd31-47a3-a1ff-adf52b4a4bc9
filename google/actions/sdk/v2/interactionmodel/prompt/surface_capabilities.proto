// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "SurfaceCapabilitiesProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// Represents the surface the user is using to make a request to the Action.
message SurfaceCapabilities {
  // Capabilities the device surface supports at the time of the request.
  enum Capability {
    // Unspecified surface capability.
    UNSPECIFIED = 0;

    // Device can speak to the user via text-to-speech or SSML.
    SPEECH = 1;

    // Device can display rich responses like cards, lists and tables.
    RICH_RESPONSE = 2;

    // Device can play long form audio media like music and podcasts.
    LONG_FORM_AUDIO = 3;

    // Device can display a interactive canvas response.
    INTERACTIVE_CANVAS = 4;

    // Device can use web links in rich responses to open a web browser.
    WEB_LINK = 5;

    // Device can support saving and fetching home storage.
    HOME_STORAGE = 6;
  }

  // Required. The capabilities of the surface making a request to the Action.
  repeated Capability capabilities = 1 [(google.api.field_behavior) = REQUIRED];
}
