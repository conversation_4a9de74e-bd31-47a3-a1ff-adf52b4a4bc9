# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "prompt_proto",
    srcs = [
        "static_prompt.proto",
        "static_simple_prompt.proto",
        "suggestion.proto",
        "surface_capabilities.proto",
    ],
    deps = [
        "//google/actions/sdk/v2/interactionmodel/prompt/content:content_proto",
        "//google/api:field_behavior_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
