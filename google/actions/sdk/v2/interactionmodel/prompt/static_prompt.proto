// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/actions/sdk/v2/interactionmodel/prompt/content/static_canvas_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_content_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_link_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/static_simple_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/suggestion.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/surface_capabilities.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticPromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// Represents a list of prompt candidates, one of which will be selected as the
// prompt to be shown in the response to the user.
// **This message is localizable.**
message StaticPrompt {
  // Represents a static prompt candidate.
  message StaticPromptCandidate {
    // Represents structured responses to send to the user, such as text,
    // speech, cards, canvas data, suggestion chips, etc.
    message StaticPromptResponse {
      // Optional. The first voice and text-only response.
      StaticSimplePrompt first_simple = 2 [(google.api.field_behavior) = OPTIONAL];

      // Optional. A content like a card, list or media to display to the user.
      StaticContentPrompt content = 3 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The last voice and text-only response.
      StaticSimplePrompt last_simple = 4 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Suggestions to be displayed to the user which will always
      // appear at the end of the response. If the `append` field in the
      // containing prompt is `true` the titles defined in this field will be
      // added to titles defined in any previously defined suggestions prompts
      // and duplicate values will be removed.
      repeated Suggestion suggestions = 5 [(google.api.field_behavior) = OPTIONAL];

      // Optional. An additional suggestion chip that can link out to the associated app
      // or site.
      // The chip will be rendered with the title "Open <name>". Max 20 chars.
      StaticLinkPrompt link = 6 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Mode for how this messages should be merged with previously defined
      // messages.
      // `true` will clear all previously defined messages (first and last
      // simple, content, suggestions link and canvas) and add messages defined
      // in this prompt. `false` will add messages defined in this prompt to
      // messages defined in previous responses. Setting this field to `false`
      // will also enable appending to some fields inside Simple prompts, the
      // Suggestions prompt and the Canvas prompt (part of the Content prompt).
      // The Content and Link messages will always be overwritten if defined in
      // the prompt. Default value is `false`.
      bool override = 7 [(google.api.field_behavior) = OPTIONAL];

      // A response to be used for interactive canvas experience.
      StaticCanvasPrompt canvas = 8;
    }

    // Optional. The criteria for whether this prompt matches a request. If the selector
    // is empty, this prompt will always be triggered.
    Selector selector = 1 [(google.api.field_behavior) = OPTIONAL];

    // The prompt response associated with the selector.
    StaticPromptResponse prompt_response = 2;
  }

  // Defines the criteria for whether a prompt matches a request.
  message Selector {
    // The set of required surface capabilities.
    SurfaceCapabilities surface_capabilities = 1;
  }

  // The list of candidate prompts to be sent to the client. Each prompt has a
  // selector to determine when it can be used. The first selector that matches
  // a request will be sent and the rest will be ignored.
  repeated StaticPromptCandidate candidates = 1;
}
