// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticLinkPromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// Defines a link which will be displayed as a suggestion chip and can be opened
// by the user.
message StaticLinkPrompt {
  // Name of the link
  string name = 1;

  // Defines behavior when the user opens the link.
  OpenUrl open = 2;
}

// Defines behavior when the user opens the link.
message OpenUrl {
  // The url field which could be any of:
  // - http/https urls for opening an App-linked App or a webpage
  string url = 1;

  // Indicates a hint for the url type.
  UrlHint hint = 2;
}

// Different types of url hints.
enum UrlHint {
  // Unspecified
  HINT_UNSPECIFIED = 0;

  // URL that points directly to AMP content, or to a canonical URL
  // which refers to AMP content via `<link rel="amphtml">`.
  AMP = 1;
}
