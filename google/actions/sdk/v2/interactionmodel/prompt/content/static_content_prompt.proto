// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/actions/sdk/v2/interactionmodel/prompt/content/static_card_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_collection_browse_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_collection_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_image_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_list_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_media_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_table_prompt.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticContentPromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// A placeholder for the Content part of a StaticPrompt.
message StaticContentPrompt {
  // Only one type of content can be present in a Prompt.
  oneof content {
    // A basic card.
    StaticCardPrompt card = 1;

    // An image.
    StaticImagePrompt image = 2;

    // Table card.
    StaticTablePrompt table = 3;

    // Response indicating a set of media to be played.
    StaticMediaPrompt media = 4;

    // A card for presenting a list of options to select from.
    StaticListPrompt list = 5;

    // A card presenting a list of options to select from.
    StaticCollectionPrompt collection = 6;

    // A card presenting a collection of web pages to open.
    StaticCollectionBrowsePrompt collection_browse = 7;
  }
}
