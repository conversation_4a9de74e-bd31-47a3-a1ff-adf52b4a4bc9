// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/actions/sdk/v2/interactionmodel/prompt/content/static_image_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_link_prompt.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticCollectionBrowsePromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// Presents a set of web documents as a collection of large-tile items. Items
// may be selected to launch their associated web document in a web viewer.
message StaticCollectionBrowsePrompt {
  // Item in the collection.
  message CollectionBrowseItem {
    // Required. Title of the collection item.
    string title = 1 [(google.api.field_behavior) = REQUIRED];

    // Description of the collection item.
    string description = 2;

    // Footer text for the collection item, displayed below the description.
    // Single line of text, truncated with an ellipsis.
    string footer = 3;

    // Image for the collection item.
    StaticImagePrompt image = 4;

    // Required. URI to open if the item selected.
    OpenUrl open_uri_action = 5 [(google.api.field_behavior) = REQUIRED];
  }

  // Items in the browse collection. The list size should be in the range [2,
  // 10].
  repeated CollectionBrowseItem items = 1;

  // Image display option for images in the collection.
  StaticImagePrompt.ImageFill image_fill = 2;
}
