// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/actions/sdk/v2/interactionmodel/prompt/content/static_image_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_link_prompt.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticTablePromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// A table card for displaying a table of text.
message StaticTablePrompt {
  // Optional. Overall title of the table. Must be set if subtitle is set.
  string title = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Subtitle for the table.
  string subtitle = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Image associated with the table.
  StaticImagePrompt image = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Headers and alignment of columns.
  repeated TableColumn columns = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Row data of the table. The first 3 rows are guaranteed to be shown but
  // others might be cut on certain surfaces. Please test with the simulator to
  // see which rows will be shown for a given surface. On surfaces that support
  // the `WEB_BROWSER` capability, you can point the user to
  // a web page with more data.
  repeated TableRow rows = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Button.
  StaticLinkPrompt button = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Describes a column in the table.
message TableColumn {
  // The alignment of the content within the cell.
  enum HorizontalAlignment {
    // HorizontalAlignment unspecified.
    UNSPECIFIED = 0;

    // Leading edge of the cell. This is the default.
    LEADING = 1;

    // Content is aligned to the center of the column.
    CENTER = 2;

    // Content is aligned to the trailing edge of the column.
    TRAILING = 3;
  }

  // Header text for the column.
  string header = 1;

  // Horizontal alignment of content w.r.t column. If unspecified, content
  // will be aligned to the leading edge.
  HorizontalAlignment align = 2;
}

// Describes a cell in a row.
message TableCell {
  // Text content of the cell.
  string text = 1;
}

// Describes a row in the table.
message TableRow {
  // Cells in this row. The first 3 cells are guaranteed to be shown but
  // others might be cut on certain surfaces. Please test with the simulator
  // to see which cells will be shown for a given surface.
  repeated TableCell cells = 1;

  // Indicates whether there should be a divider after each row.
  bool divider = 2;
}
