// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticImagePromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// An image displayed in the card.
message StaticImagePrompt {
  // Possible image display options for affecting the presentation of the image.
  // This should be used for when the image's aspect ratio does not match the
  // image container's aspect ratio.
  enum ImageFill {
    // ImageFill unspecified.
    UNSPECIFIED = 0;

    // Fill the gaps between the image and the image container with gray bars.
    GRAY = 1;

    // Fill the gaps between the image and the image container with white bars.
    WHITE = 2;

    // Image is scaled such that the image width and height match or exceed the
    // container dimensions. This may crop the top and bottom of the image if
    // the scaled image height is greater than the container height, or crop the
    // left and right of the image if the scaled image width is greater than the
    // container width. This is similar to "Zoom Mode" on a widescreen TV when
    // playing a 4:3 video.
    CROPPED = 3;
  }

  // Required. The source url of the image. Images can be JPG, PNG and GIF (animated and
  // non-animated). For example,`https://www.agentx.com/logo.png`.
  string url = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A text description of the image to be used for accessibility, e.g. screen
  // readers.
  string alt = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The height of the image in pixels.
  int32 height = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The width of the image in pixels.
  int32 width = 4 [(google.api.field_behavior) = OPTIONAL];
}
