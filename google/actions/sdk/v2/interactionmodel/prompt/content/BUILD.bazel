# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "content_proto",
    srcs = [
        "static_canvas_prompt.proto",
        "static_card_prompt.proto",
        "static_collection_browse_prompt.proto",
        "static_collection_prompt.proto",
        "static_content_prompt.proto",
        "static_image_prompt.proto",
        "static_link_prompt.proto",
        "static_list_prompt.proto",
        "static_media_prompt.proto",
        "static_table_prompt.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
