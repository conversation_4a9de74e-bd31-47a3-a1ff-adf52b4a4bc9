// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/actions/sdk/v2/interactionmodel/prompt/content/static_image_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/content/static_link_prompt.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticCardPromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// A basic card for displaying some information, e.g. an image and/or text.
message StaticCardPrompt {
  // Optional. Overall title of the card.
  string title = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Subtitle of the card.
  string subtitle = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Body text of the card which is needed unless image is present. Supports a
  // limited set of markdown syntax for formatting.
  string text = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A hero image for the card. The height is fixed to 192dp.
  StaticImagePrompt image = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. How the image background will be filled.
  StaticImagePrompt.ImageFill image_fill = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A clickable button to be shown in the Card.
  StaticLinkPrompt button = 6 [(google.api.field_behavior) = OPTIONAL];
}
