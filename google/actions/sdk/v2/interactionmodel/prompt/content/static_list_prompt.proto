// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/actions/sdk/v2/interactionmodel/prompt/content/static_image_prompt.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticListPromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// A card for presenting a list of options to select from.
message StaticListPrompt {
  // An item in the list.
  message ListItem {
    // Required. The NLU key that matches the entry key name in the associated type. When
    // item tapped, this key will be posted back as a select option parameter.
    string key = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Title of the item. When tapped, this text will be posted back to the
    // conversation verbatim as if the user had typed it. Each title must be
    // unique among the set of items.
    string title = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. Body text of the item.
    string description = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Item image.
    StaticImagePrompt image = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. Title of the list.
  string title = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Subtitle of the list.
  string subtitle = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. List items.
  repeated ListItem items = 3 [(google.api.field_behavior) = REQUIRED];
}
