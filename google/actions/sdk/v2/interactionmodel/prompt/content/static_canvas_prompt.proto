// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.prompt;

import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/prompt;prompt";
option java_multiple_files = true;
option java_outer_classname = "StaticCanvasPromptProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.prompt";

// Represents a Interactive Canvas response to be sent to the user.
// This can be used in conjunction with the `first_simple` field in the
// containing prompt to speak to the user in addition to displaying a
// interactive canvas response.
message StaticCanvasPrompt {
  // Required. URL of the web view to load.
  string url = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. JSON data to be passed through to the immersive experience web page as an
  // event. If the `override` field in the containing prompt is `false` data
  // values defined in this Canvas prompt will be added after data values
  // defined in previous Canvas prompts.
  repeated google.protobuf.Value data = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A true value means that the mic won't be opened for capturing input after
  // this immersive response is presented to the user.
  bool suppress_mic = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If `true`, conversation related metadata is included and send back to the
  // canvas application.
  bool send_state_data_to_canvas_app = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If `true` the canvas application occupies the full screen and won't
  // have a header at the top. A toast message will also be displayed on the
  // loading screen that includes the Action's display name, the developer's
  // name, and instructions for exiting the Action. Default value: `false`.
  bool enable_full_screen = 6 [(google.api.field_behavior) = OPTIONAL];
}
