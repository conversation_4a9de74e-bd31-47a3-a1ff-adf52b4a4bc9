// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel;

import "google/actions/sdk/v2/interactionmodel/prompt/static_prompt.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel;interactionmodel";
option java_multiple_files = true;
option java_outer_classname = "EventHandlerProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel";

// Defines a handler to be executed after an event. Examples of events are
// intent and condition based events in a scene.
message EventHandler {
  // Name of the webhook handler to call.
  string webhook_handler = 1;

  // Prompts can either be inlined or referenced by name.
  oneof prompt {
    // Inlined static prompt. Can contain references to string resources in
    // bundles.
    google.actions.sdk.v2.interactionmodel.prompt.StaticPrompt static_prompt = 2;

    // Name of the static prompt to invoke.
    string static_prompt_name = 3;
  }
}
