# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "interactionmodel_proto",
    srcs = [
        "conditional_event.proto",
        "entity_set.proto",
        "event_handler.proto",
        "global_intent_event.proto",
        "intent.proto",
        "intent_event.proto",
        "scene.proto",
        "slot.proto",
    ],
    deps = [
        "//google/actions/sdk/v2/interactionmodel/prompt:prompt_proto",
        "//google/actions/sdk/v2/interactionmodel/type:type_proto",
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
