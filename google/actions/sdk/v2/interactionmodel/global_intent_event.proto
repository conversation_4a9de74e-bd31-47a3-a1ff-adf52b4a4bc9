// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel;

import "google/actions/sdk/v2/interactionmodel/event_handler.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel;interactionmodel";
option java_multiple_files = true;
option java_outer_classname = "GlobalIntentEventProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel";

// Defines a global intent handler. Global intent events are scoped to the
// entire Actions project and may be overridden by intent handlers in a scene.
// Intent names must be unique within an Actions project.
//
// Global intents can be matched anytime during a session, allowing users to
// access common flows like  "get help" or "go back home." They can also be
// used to deep link users into specific flows when they invoke an Action.
//
// Note, the intent name is specified in the name of the file.
message GlobalIntentEvent {
  // Optional. Destination scene which the conversation should jump to. The state of the
  // current scene is destroyed on the transition.
  string transition_to_scene = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Event handler which is triggered when the intent is matched. Should execute
  // before transitioning to the destination scene. Useful to generate Prompts
  // in response to events.
  EventHandler handler = 2 [(google.api.field_behavior) = OPTIONAL];
}
