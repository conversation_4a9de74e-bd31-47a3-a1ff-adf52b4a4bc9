// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.type;

import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/type";
option java_multiple_files = true;
option java_outer_classname = "ClassReferenceProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.type";

// A reference to a class which is used to declare the type of a field or return
// value. Enums are also a type of class that can be referenced using
// ClassReference.
message ClassReference {
  // Required. Name of a built-in type or custom type of the parameter. Examples:
  // `PizzaToppings`, `actions.type.Number`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates whether the data type represents a list of values.
  bool list = 2 [(google.api.field_behavior) = OPTIONAL];
}
