// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.type;

import "google/actions/sdk/v2/interactionmodel/type/entity_display.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/type";
option java_multiple_files = true;
option java_outer_classname = "SynonymTypeProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.type";

// Type that matches text by set of synonyms.
message SynonymType {
  // Represents a synonym entity field that contains the details of a single
  // entry inside the type.
  message Entity {
    // Optional. The entity display details.
    EntityDisplay display = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The list of synonyms for the entity.
    // **This field is localizable.**
    repeated string synonyms = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // The type of matching that entries in this type will use. This will ensure
  // all of the types use the same matching method and allow variation of
  // matching for synonym matching (i.e. fuzzy versus exact). If the value is
  // `UNSPECIFIED` it will be defaulted to `EXACT_MATCH`.
  enum MatchType {
    // Defaults to `EXACT_MATCH`.
    UNSPECIFIED = 0;

    // Looks for an exact match of the synonym or name.
    EXACT_MATCH = 1;

    // Looser than `EXACT_MATCH`. Looks for similar matches as well as exact
    // matches.
    FUZZY_MATCH = 2;
  }

  // Optional. The match type for the synonym.
  MatchType match_type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When set to true this will match unknown words or phrases based on
  // surrounding input and intent training data, such as items that might be
  // added to a grocery list.
  bool accept_unknown_values = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Named map of synonym entities.
  map<string, Entity> entities = 2 [(google.api.field_behavior) = REQUIRED];
}
