// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.type;

import "google/actions/sdk/v2/interactionmodel/type/free_text_type.proto";
import "google/actions/sdk/v2/interactionmodel/type/regular_expression_type.proto";
import "google/actions/sdk/v2/interactionmodel/type/synonym_type.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/type";
option java_multiple_files = true;
option java_outer_classname = "TypeProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.type";

// Declaration of a custom type, as opposed to built-in types. Types can be
// assigned to slots in a scene or parameters of an intent's training phrases.
// Practically, Types can be thought of as enums.
// Note, type name is specified in the name of the file.
message Type {
  // Selection of sub type based on the type of matching to be done.
  oneof sub_type {
    // Synonyms type, which is essentially an enum.
    SynonymType synonym = 1;

    // Regex type, allows regular expression matching.
    RegularExpressionType regular_expression = 2;

    // FreeText type.
    FreeTextType free_text = 3;
  }

  // Set of exceptional words/phrases that shouldn't be matched by type.
  // Note: If word/phrase is matched by the type but listed as an exclusion it
  // won't be returned in parameter extraction result.
  // **This field is localizable.**
  repeated string exclusions = 4;
}
