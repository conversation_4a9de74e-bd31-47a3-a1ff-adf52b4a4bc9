// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel.type;

import "google/actions/sdk/v2/interactionmodel/type/entity_display.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel/type";
option java_multiple_files = true;
option java_outer_classname = "FreeTextTypeProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel.type";

// Type that matches any text if surrounding words context is close to provided
// training examples.
message FreeTextType {
  // Optional. Elements that will be displayed on the canvas once an entity is extracted
  // from a query. Only relevant for canvas enabled apps.
  EntityDisplay display = 2 [(google.api.field_behavior) = OPTIONAL];
}
