// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel;

import "google/actions/sdk/v2/interactionmodel/event_handler.proto";
import "google/actions/sdk/v2/interactionmodel/type/class_reference.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel;interactionmodel";
option java_multiple_files = true;
option java_outer_classname = "SlotProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel";

// Configuration for a slot. Slots are single units of data that can be filled
// through natural language (ie. intent parameters), session parameters, and
// other sources.
message Slot {
  // A single place where slot prompts are defined.
  message PromptSettings {
    // Prompt for the slot value itself. Example: "What size did you want?"
    EventHandler initial_prompt = 1;

    // Prompt to give when the user's input does not match the expected
    // value type for the slot for the first time. Example: "Sorry, I
    // didn't get that."
    EventHandler no_match_prompt1 = 2;

    // Prompt to give when the user's input does not match the expected
    // value type for the slot for the second time. Example: "Sorry, I
    // didn't get that."
    EventHandler no_match_prompt2 = 3;

    // Prompt to give when the user's input does not match the expected
    // value type for the slot for the last time. Example: "Sorry, I
    // didn't get that."
    EventHandler no_match_final_prompt = 4;

    // Prompt to give when the user does not provide an input for the first
    // time. Example: "Sorry, I didn't get that."
    EventHandler no_input_prompt1 = 5;

    // Prompt to give when the user does not provide an input for the second
    // time. Example: "Sorry, I didn't get that."
    EventHandler no_input_prompt2 = 6;

    // Prompt to give when the user does not provide an input for the last
    // time. Example: "Sorry, I didn't get that."
    EventHandler no_input_final_prompt = 7;
  }

  // Message describing the commit behavior associated with the slot after it
  // has been successfully filled.
  message CommitBehavior {
    // The session parameter to write the slot value after it is filled. Note
    // that nested paths are not currently supported. "$$" is used to write the
    // slot value to a session parameter with same name as the slot.
    // Eg: write_session_param = "fruit" corresponds to "$session.params.fruit".
    // write_session_param = "ticket" corresponds to "$session.params.ticket".
    string write_session_param = 1;
  }

  // Configuration to populate a default value for this slot.
  message DefaultValue {
    // Optional. The session parameter to be used to initialize the slot value, if it has
    // a non-empty value. The type of the value must match the type of the slot.
    // Note that nested paths are not currently supported.
    // Eg: `session_param = "fruit"` corresponds to `$session.params.fruit`.
    // `session_param = "ticket"` corresponds to `$session.params.ticket`.
    string session_param = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Constant default value for the slot. This will only be used if a value
    // for this slot was not populated through the `session_param`. The
    // type for this value must match the type of the slot.
    google.protobuf.Value constant = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Name of the slot.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Declares the data type of this slot.
  google.actions.sdk.v2.interactionmodel.type.ClassReference type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates whether the slot is required to be filled before
  // advancing. Required slots that are not filled will trigger a customizable
  // prompt to the user.
  bool required = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Registers Prompts for different stages of slot filling.
  PromptSettings prompt_settings = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Commit behavior associated with the slot.
  CommitBehavior commit_behavior = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Additional configuration associated with the slot which is
  // used for filling the slot. The format of the config is specific to the
  // type of the slot. Resource references to user or session parameter can be
  // added to this config. This config is needed for filling slots related to
  // transactions and user engagement.
  //
  // Example:
  //  For a slot of type actions.type.CompletePurchaseValue, the following
  //  config proposes a digital good order with a reference to a client defined
  //  session parameter `userSelectedSkuId`:
  //
  //    {
  //      "@type": "type.googleapis.com/
  //                  google.actions.transactions.v3.CompletePurchaseValueSpec",
  //      "skuId": {
  //        "skuType": "SKU_TYPE_IN_APP",
  //        "id": "$session.params.userSelectedSkuId",
  //        "packageName": "com.example.company"
  //      }
  //    }
  google.protobuf.Value config = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration to populate a default value for this slot.
  DefaultValue default_value = 7 [(google.api.field_behavior) = OPTIONAL];
}
