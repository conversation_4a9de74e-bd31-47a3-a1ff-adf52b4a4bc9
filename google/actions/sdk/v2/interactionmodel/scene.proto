// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel;

import "google/actions/sdk/v2/interactionmodel/conditional_event.proto";
import "google/actions/sdk/v2/interactionmodel/event_handler.proto";
import "google/actions/sdk/v2/interactionmodel/intent_event.proto";
import "google/actions/sdk/v2/interactionmodel/slot.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel;interactionmodel";
option java_multiple_files = true;
option java_outer_classname = "SceneProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel";

// Scene is the basic unit of control flow when designing a conversation. They
// can be chained together with other scenes, generate prompts for the end user,
// and define slots.
// The scene name is specified in the name of the file.
message Scene {
  // Handler to invoke when transitioning into this scene.
  EventHandler on_enter = 1;

  // The list of events that trigger based on intents. These events can
  // be triggered at any time after the on_load Handler has been called.
  // Important - these events define the set of intents which are scoped to
  // this scene and will take precedence over any globally defined events that
  // have the same intents or their triggering phrases. Intent names must be
  // unique within a scene.
  repeated IntentEvent intent_events = 2;

  // The list of events to trigger based on conditional statements. These are
  // evaluated after the form has been filled or immediately after on_load if
  // this scene does not have a form (evaluation is only done once). Only the
  // first matching event will be triggered.
  repeated ConditionalEvent conditional_events = 3;

  // Ordered list of slots. Each slot defines the type of data
  // that it will resolve and configuration to customize the experience of this
  // resolution (e.g. prompts).
  repeated Slot slots = 4;

  // Handler called when there is a change in state of a slot not
  // caused by updates within another Handler. This allows slots to be
  // invalidated, the scene invalidated or other changes to scene state.
  EventHandler on_slot_updated = 5;
}
