// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.interactionmodel;

import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/interactionmodel;interactionmodel";
option java_multiple_files = true;
option java_outer_classname = "EntitySetProto";
option java_package = "com.google.actions.sdk.v2.interactionmodel";

// Entity sets describe the pre-defined set of entities that the values of
// built-in intent parameters can come from. Entity sets can be referenced from
// entity_set in built-in intent parameters.
message EntitySet {
  // An entity a built-in intent parameter value can come from.
  message Entity {
    // Required. The ID of the entity.
    // For a list of built-in-intent parameters and their supported entities,
    // see
    // https://developers.google.com/assistant/conversational/build/built-in-intents
    string id = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. The list of entities this entity set supports.
  repeated Entity entities = 1 [(google.api.field_behavior) = REQUIRED];
}
