// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "DataFileProto";
option java_package = "com.google.actions.sdk.v2";

// Wrapper for repeated data file. Repeated fields cannot exist in a oneof.
message DataFiles {
  // Multiple data files.
  repeated DataFile data_files = 1;
}

// Represents a single file which contains unstructured data. Examples include
// image files, audio files, and cloud function source code.
message DataFile {
  // Relative path of the data file from the project root in the SDK file
  // structure.
  // Allowed file paths:
  //     - Images: `resources/images/{multiple
  //     directories}?/{ImageName}.{extension}`
  //     - Audio: `resources/audio/{multiple
  //     directories}?/{AudioFileName}.{extension}`
  //     - Inline Cloud Function Code: `webhooks/{WebhookName}.zip`
  // Allowed extensions:
  //     - Images: `png`, `jpg`, `jpeg`
  //     - Audio: `mp3`, `mpeg`
  //     - Inline Cloud Functions: `zip`
  string file_path = 1;

  // Required. The content type of this asset. Example: `text/html`. The content
  // type must comply with the specification
  // (http://www.w3.org/Protocols/rfc1341/4_Content-Type.html).
  // Cloud functions must be in zip format and the content type should
  // be `application/zip;zip_type=cloud_function`. The zip_type parameter
  // indicates that the zip is for a cloud function.
  string content_type = 2;

  // Content of the data file. Examples would be raw bytes of images, audio
  // files, or cloud function zip format.
  // There is 10 MB strict limit on the payload size.
  bytes payload = 3;
}
