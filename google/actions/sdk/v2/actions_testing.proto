// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/actions/sdk/v2/conversation/intent.proto";
import "google/actions/sdk/v2/conversation/prompt/content/canvas.proto";
import "google/actions/sdk/v2/conversation/prompt/prompt.proto";
import "google/actions/sdk/v2/event_logs.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/type/latlng.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "ActionsTestingProto";
option java_package = "com.google.actions.sdk.v2";

// Actions Testing API which allows developers to run automated tests.
service ActionsTesting {
  option (google.api.default_host) = "actions.googleapis.com";

  // Plays one round of the conversation.
  rpc SendInteraction(SendInteractionRequest) returns (SendInteractionResponse) {
    option (google.api.http) = {
      post: "/v2/{project=projects/*}:sendInteraction"
      body: "*"
    };
  }

  // Finds the intents that match a given query.
  rpc MatchIntents(MatchIntentsRequest) returns (MatchIntentsResponse) {
    option (google.api.http) = {
      post: "/v2/{project=projects/*}:matchIntents"
      body: "*"
    };
    option (google.api.method_signature) = "project,query,locale";
  }

  // Sets the Web & App Activity control on a service account.
  //
  // It is necessary to have this setting enabled in order to use call Actions.
  // The setting is originally disabled for service accounts, and it is
  // preserved until set to a different value. This means it only needs to be
  // enabled once per account (and not necessarily once per test), unless it is
  // later disabled.
  //
  // Returns an error if the caller is not a service account. User accounts can
  // change this setting via the Activity Controls page. See
  // https://support.google.com/websearch/answer/54068.
  rpc SetWebAndAppActivityControl(SetWebAndAppActivityControlRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2:setWebAndAppActivityControl"
      body: "*"
    };
    option (google.api.method_signature) = "enabled";
  }
}

// Request for playing a round of the conversation.
message SendInteractionRequest {
  // Required. The project being tested, indicated by the Project ID.
  // Format: projects/{project}
  string project = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Input provided by the user.
  UserInput input = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Properties of the device used for interacting with the Action.
  DeviceProperties device_properties = 3 [(google.api.field_behavior) = REQUIRED];

  // Opaque token that must be passed as received from SendInteractionResponse
  // on the previous interaction. This can be left unset in order to start a new
  // conversation, either as the first interaction of a testing session or to
  // abandon a previous conversation and start a new one.
  string conversation_token = 4;
}

// User input provided on a conversation round.
message UserInput {
  // Indicates the input source, typed query or voice query.
  enum InputType {
    // Unspecified input source.
    INPUT_TYPE_UNSPECIFIED = 0;

    // Query from a GUI interaction.
    TOUCH = 1;

    // Voice query.
    VOICE = 2;

    // Typed query.
    KEYBOARD = 3;

    // The action was triggered by a URL link.
    URL = 4;
  }

  // Content of the input sent by the user.
  string query = 1;

  // Type of the input.
  InputType type = 2;
}

// Properties of device relevant to a conversation round.
message DeviceProperties {
  // Possible surfaces used to interact with the Action.
  // Additional values may be included in the future.
  enum Surface {
    // Default value. This value is unused.
    SURFACE_UNSPECIFIED = 0;

    // Speaker (e.g. Google Home).
    SPEAKER = 1;

    // Phone.
    PHONE = 2;

    // Allo Chat.
    ALLO = 3;

    // Smart Display Device.
    SMART_DISPLAY = 4;

    // KaiOS.
    KAI_OS = 5;
  }

  // Surface used for interacting with the Action.
  Surface surface = 1;

  // Device location such as latitude, longitude, and formatted address.
  Location location = 2;

  // Locale as set on the device.
  // The format should follow BCP 47: https://tools.ietf.org/html/bcp47
  // Examples: en, en-US, es-419 (more examples at
  // https://tools.ietf.org/html/bcp47#appendix-A).
  string locale = 3;

  // Time zone as set on the device.
  // The format should follow the IANA Time Zone Database, e.g.
  // "America/New_York": https://www.iana.org/time-zones
  string time_zone = 4;
}

// Container that represents a location.
message Location {
  // Geo coordinates.
  // Requires the [DEVICE_PRECISE_LOCATION]
  // [google.actions.v2.Permission.DEVICE_PRECISE_LOCATION] permission.
  google.type.LatLng coordinates = 1;

  // Display address, e.g., "1600 Amphitheatre Pkwy, Mountain View, CA 94043".
  // Requires the [DEVICE_PRECISE_LOCATION]
  // [google.actions.v2.Permission.DEVICE_PRECISE_LOCATION] permission.
  string formatted_address = 2;

  // Zip code.
  // Requires the [DEVICE_PRECISE_LOCATION]
  // [google.actions.v2.Permission.DEVICE_PRECISE_LOCATION] or
  // [DEVICE_COARSE_LOCATION]
  // [google.actions.v2.Permission.DEVICE_COARSE_LOCATION] permission.
  string zip_code = 3;

  // City.
  // Requires the [DEVICE_PRECISE_LOCATION]
  // [google.actions.v2.Permission.DEVICE_PRECISE_LOCATION] or
  // [DEVICE_COARSE_LOCATION]
  // [google.actions.v2.Permission.DEVICE_COARSE_LOCATION] permission.
  string city = 4;
}

// Response to a round of the conversation.
message SendInteractionResponse {
  // Output provided to the user.
  Output output = 1;

  // Diagnostics information that explains how the request was handled.
  Diagnostics diagnostics = 2;

  // Opaque token to be set on SendInteractionRequest on the next RPC call in
  // order to continue the same conversation.
  string conversation_token = 3;
}

// User-visible output to the conversation round.
message Output {
  // Spoken response sent to user as a plain string.
  string text = 1;

  // Speech content produced by the Action. This may include markup elements
  // such as SSML.
  repeated string speech = 2;

  // Interactive Canvas content.
  google.actions.sdk.v2.conversation.Canvas canvas = 3;

  // State of the prompt at the end of the conversation round.
  // More information about the prompt:
  // https://developers.google.com/assistant/conversational/prompts
  google.actions.sdk.v2.conversation.Prompt actions_builder_prompt = 4;
}

// Diagnostics information related to the conversation round.
message Diagnostics {
  // List of events with details about processing of the conversation round
  // throughout the stages of the Actions Builder interaction model.
  // Populated for Actions Builder & Actions SDK apps only.
  repeated ExecutionEvent actions_builder_events = 1;
}

// Request for finding matching intents.
message MatchIntentsRequest {
  // Required. The project being tested, indicated by the Project ID.
  // Format: projects/{project}
  string project = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. User query as plain text.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Locale to use to evaluate the query, such as "en".
  // The format should follow BCP 47: https://tools.ietf.org/html/bcp47
  // See the list of supported languages in
  // https://developers.google.com/assistant/console/languages-locales
  string locale = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response for finding matching intents.
message MatchIntentsResponse {
  // Intents matched, ordered from most to least relevant. Only the first
  // 50 matches are returned.
  repeated google.actions.sdk.v2.conversation.Intent matched_intents = 1;
}

// Request for setting Web & App Activity preferences.
message SetWebAndAppActivityControlRequest {
  // Whether the setting should be set to an enabled or disabled state.
  bool enabled = 1;
}
