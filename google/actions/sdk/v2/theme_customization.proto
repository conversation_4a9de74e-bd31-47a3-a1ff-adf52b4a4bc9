// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "ThemeCustomizationProto";
option java_package = "com.google.actions.sdk.v2";

// Styles applied to cards that are presented to users
message ThemeCustomization {
  // Describes how the borders of images should be rendered.
  enum ImageCornerStyle {
    // Undefined / Unspecified.
    IMAGE_CORNER_STYLE_UNSPECIFIED = 0;

    // Round corner for image.
    CURVED = 1;

    // Rectangular corner for image.
    ANGLED = 2;
  }

  // Background color of cards. Acts as a fallback if `background_image` is
  // not provided by developers or `background_image` doesn't fit for certain
  // surfaces.
  // Example usage: #FAFAFA
  string background_color = 1;

  // Primary theme color of the Action will be used to set text color of title,
  // action item background color for Actions on Google cards.
  // Example usage: #FAFAFA
  string primary_color = 2;

  // The font family that will be used for title of cards.
  // Supported fonts:
  // - Sans Serif
  // - Sans Serif Medium
  // - Sans Serif Bold
  // - Sans Serif Black
  // - Sans Serif Condensed
  // - Sans Serif Condensed Medium
  // - Serif
  // - Serif Bold
  // - Monospace
  // - Cursive
  // - Sans Serif Smallcaps
  string font_family = 3;

  // Border style of foreground image of cards. For example, can be applied on
  // the foreground image of a basic card or carousel card.
  ImageCornerStyle image_corner_style = 4;

  // Landscape mode (minimum 1920x1200 pixels).
  // This should be specified as a reference to the corresponding image in the
  // `resources/images/` directory. Eg: `$resources.images.foo` (without the
  // extension) for image in `resources/images/foo.jpg`
  // When working on a project pulled from Console the Google managed url pulled
  // could be used.
  string landscape_background_image = 5;

  // Portrait mode (minimum 1200x1920 pixels).
  // This should be specified as a reference to the corresponding image in the
  // `resources/images/` directory. Eg: `$resources.images.foo` (without the
  // extension) for image in `resources/images/foo.jpg`
  // When working on a project pulled from Console the Google managed url pulled
  // could be used.
  string portrait_background_image = 6;
}
