// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "ActionProto";
option java_package = "com.google.actions.sdk.v2";

// Represents the list of Actions defined in a project.
message Actions {
  // Defines the engagement mechanisms associated with this action. This
  // allows end users to subscribe to push notification and daily update.
  message Engagement {
    // Defines push notification settings that this engagement supports.
    message PushNotification {

    }

    // Defines daily update settings that this engagement supports.
    message DailyUpdate {

    }

    // Indicates whether sharing links is enabled for this action and the
    // corresponding settings. Action links are used to deep link a user into a
    // specific action.
    // ActionLink is deprecated. Use AssistantLink instead.
    message ActionLink {
      option deprecated = true;

      // User friendly display title for the link.
      string title = 1;
    }

    // Indicates whether sharing links is enabled for this action and the
    // corresponding settings. Assistant links are used to deep link a user into
    // a specific action.
    message AssistantLink {
      // User friendly display title for the link.
      string title = 1;
    }

    // The title of the engagement that will be sent to end users asking for
    // their permission to receive updates. The prompt sent to end users for
    // daily updates will look like "What time would you like me to send your
    // daily {title}" and for push notifications will look like
    // "Is it ok if I send push notifications for {title}".
    // **This field is localizable.**
    string title = 1;

    // Push notification settings that this engagement supports.
    PushNotification push_notification = 2;

    // Recurring update settings that this engagement supports.
    oneof recurring_update {
      // Daily update settings that this engagement supports.
      DailyUpdate daily_update = 3;
    }

    // Link config for an action which determines whether sharing links is
    // enabled for the action and if so, contains the user friendly display name
    // for the link.
    // ActionLink is deprecated. Use AssistantLink instead.
    ActionLink action_link = 4 [deprecated = true];

    // Link config for an action which determines whether sharing links is
    // enabled for the action and if so, contains the user friendly display name
    // for the link.
    AssistantLink assistant_link = 6;
  }

  // Details regarding a custom action.
  message CustomAction {
    // Engagement mechanisms associated with the action to help end users
    // subscribe to push notifications and daily updates.
    // Note that the intent name specified in daily updates/push notifications
    // slot config needs to match the intent corresponding to this action for
    // end users to subscribe to these updates.
    Engagement engagement = 2;
  }

  // Map from intents to custom Actions to configure invocation for the project.
  // The invocation intents could either be system or custom intents defined
  // in the "custom/intents/" package. All intents defined here (system
  // intents & custom intents) must have a corresponding intent file in the
  // "custom/global/" package.
  map<string, CustomAction> custom = 3;
}
