# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "sdk_proto",
    srcs = [
        "account_linking.proto",
        "account_linking_secret.proto",
        "action.proto",
        "actions_sdk.proto",
        "actions_testing.proto",
        "config_file.proto",
        "data_file.proto",
        "event_logs.proto",
        "files.proto",
        "localized_settings.proto",
        "manifest.proto",
        "release_channel.proto",
        "settings.proto",
        "surface.proto",
        "theme_customization.proto",
        "validation_results.proto",
        "version.proto",
        "webhook.proto",
    ],
    deps = [
        "//google/actions/sdk/v2/conversation:conversation_proto",
        "//google/actions/sdk/v2/conversation/prompt:prompt_proto",
        "//google/actions/sdk/v2/conversation/prompt/content:content_proto",
        "//google/actions/sdk/v2/interactionmodel:interactionmodel_proto",
        "//google/actions/sdk/v2/interactionmodel/prompt:prompt_proto",
        "//google/actions/sdk/v2/interactionmodel/prompt/content:content_proto",
        "//google/actions/sdk/v2/interactionmodel/type:type_proto",
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "sdk_proto_with_info",
    deps = [
        ":sdk_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "sdk_nodejs_gapic",
    package_name = "@assistant/actions",
    src = ":sdk_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "actions_grpc_service_config.json",
    package = "google.actions.sdk.v2",
    rest_numeric_enums = True,
    service_yaml = "actions_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "actions-v2-nodejs",
    deps = [
        ":sdk_nodejs_gapic",
        ":sdk_proto",
        "//google/actions/sdk/v2/conversation:conversation_proto",
        "//google/actions/sdk/v2/conversation/prompt:prompt_proto",
        "//google/actions/sdk/v2/conversation/prompt/content:content_proto",
        "//google/actions/sdk/v2/interactionmodel:interactionmodel_proto",
        "//google/actions/sdk/v2/interactionmodel/prompt:prompt_proto",
        "//google/actions/sdk/v2/interactionmodel/prompt/content:content_proto",
        "//google/actions/sdk/v2/interactionmodel/type:type_proto",
    ],
)
