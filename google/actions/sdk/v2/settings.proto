// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/actions/sdk/v2/account_linking.proto";
import "google/actions/sdk/v2/localized_settings.proto";
import "google/actions/sdk/v2/surface.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "SettingsProto";
option java_package = "com.google.actions.sdk.v2";

// Represents settings of an Actions project that are not locale specific.
message Settings {
  // The category choices for an Actions project.
  enum Category {
    // Unknown / Unspecified.
    CATEGORY_UNSPECIFIED = 0;

    // Business and Finance category.
    BUSINESS_AND_FINANCE = 2;

    // Education and Reference category.
    EDUCATION_AND_REFERENCE = 3;

    // Food and Drink category.
    FOOD_AND_DRINK = 4;

    // Games and Trivia category.
    GAMES_AND_TRIVIA = 5;

    // Health and Fitness category.
    HEALTH_AND_FITNESS = 6;

    // Kids and Family category.
    KIDS_AND_FAMILY = 20;

    // Lifestyle category.
    LIFESTYLE = 7;

    // Local category.
    LOCAL = 8;

    // Movies and TV category.
    MOVIES_AND_TV = 9;

    // Music and Audio category.
    MUSIC_AND_AUDIO = 10;

    // News category,
    NEWS = 1;

    // Novelty and Humor category.
    NOVELTY_AND_HUMOR = 11;

    // Productivity category.
    PRODUCTIVITY = 12;

    // Shopping category.
    SHOPPING = 13;

    // Social category.
    SOCIAL = 14;

    // Sports category.
    SPORTS = 15;

    // Travel and Transportation category.
    TRAVEL_AND_TRANSPORTATION = 16;

    // Utilities category.
    UTILITIES = 17;

    // Weather category.
    WEATHER = 18;

    // Home Control category.
    HOME_CONTROL = 19;
  }

  // Actions project id.
  string project_id = 1;

  // Locale which is default for the project. For all files except under
  // `resources/` with no locale in the path, the localized data is attributed
  // to this `default_locale`. For files under `resources/` no locale means that
  // the resource is applicable to all locales.
  string default_locale = 2;

  // Represents the regions where users can invoke your Actions, which is
  // based on the user's location of presence. Cannot be set if
  // `disabled_regions` is set. If both `enabled_regions` and `disabled_regions`
  // are not specified, users can invoke your Actions in all regions. Each
  // region is represented using the Canonical Name of Adwords geotargets. See
  // https://developers.google.com/adwords/api/docs/appendix/geotargeting
  // Examples include:
  // - "Germany"
  // - "Ghana"
  // - "Greece"
  // - "Grenada"
  // - "United Kingdom"
  // - "United States"
  // - "United States Minor Outlying Islands"
  // - "Uruguay"
  repeated string enabled_regions = 3;

  // Represents the regions where your Actions are blocked, based on the user's
  // location of presence. Cannot be set if `enabled_regions` is set.
  // Each region is represented using the Canonical Name of Adwords geotargets.
  // See https://developers.google.com/adwords/api/docs/appendix/geotargeting
  // Examples include:
  // - "Germany"
  // - "Ghana"
  // - "Greece"
  // - "Grenada"
  // - "United Kingdom"
  // - "United States"
  // - "United States Minor Outlying Islands"
  // - "Uruguay"
  repeated string disabled_regions = 4;

  // The category for this Actions project.
  Category category = 5;

  // Whether Actions can use transactions (for example, making
  // reservations, taking orders, etc.). If false, then attempts to use the
  // Transactions APIs fail.
  bool uses_transactions_api = 6;

  // Whether Actions can perform transactions for digital goods.
  bool uses_digital_purchase_api = 7;

  // Whether Actions use Interactive Canvas.
  bool uses_interactive_canvas = 8;

  // Whether Actions use the home storage feature.
  bool uses_home_storage = 17;

  // Whether Actions content is designed for family (DFF).
  bool designed_for_family = 9;

  // Whether Actions contains alcohol or tobacco related content.
  bool contains_alcohol_or_tobacco_content = 11;

  // Whether Actions may leave mic open without an explicit prompt during
  // conversation.
  bool keeps_mic_open = 12;

  // The surface requirements that a client surface must support to invoke
  // Actions in this project.
  SurfaceRequirements surface_requirements = 13;

  // Free-form testing instructions for Actions reviewer (for example, account
  // linking instructions).
  string testing_instructions = 14;

  // Localized settings for the project's default locale. Every additional
  // locale should have its own settings file in its own directory.
  LocalizedSettings localized_settings = 15;

  // Allow users to create or link accounts through Google sign-in and/or your
  // own OAuth service.
  AccountLinking account_linking = 16;

  // Android apps selected to acccess Google Play purchases for transactions.
  // This is a selection from the Android apps connected to the actions project
  // to verify brand ownership and enable additional features. See
  // https://developers.google.com/assistant/console/brand-verification for more
  // information.
  repeated string selected_android_apps = 20;
}
