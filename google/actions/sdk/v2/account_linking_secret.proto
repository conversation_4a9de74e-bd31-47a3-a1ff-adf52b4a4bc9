// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "AccountLinkingSecretProto";
option java_package = "com.google.actions.sdk.v2";

// Information about the encrypted OAuth client secret used in account linking
// flows (for AUTH_CODE grant type).
message AccountLinkingSecret {
  // Encrypted account linking client secret ciphertext.
  bytes encrypted_client_secret = 1;

  // The version of the crypto key used to encrypt the account linking client
  // secret.
  // Note that this field is ignored in push, preview, and version creation
  // flows.
  string encryption_key_version = 2;
}
