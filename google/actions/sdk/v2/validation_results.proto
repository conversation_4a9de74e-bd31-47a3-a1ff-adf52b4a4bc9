// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "ValidationResultsProto";
option java_package = "com.google.actions.sdk.v2";

// Wrapper for repeated validation result.
message ValidationResults {
  // Multiple validation results.
  repeated ValidationResult results = 1;
}

// Represents a validation result associated with the app content.
message ValidationResult {
  // Context to identify the resource the validation message relates to.
  message ValidationContext {
    // Language code of the lozalized resource.
    // Empty if the error is for non-localized resource.
    // See the list of supported languages in
    // https://developers.google.com/assistant/console/languages-locales
    string language_code = 1;
  }

  // Holds the validation message.
  string validation_message = 1;

  // Context to identify the resource the validation message relates to.
  ValidationContext validation_context = 2;
}
