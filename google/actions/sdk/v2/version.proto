// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "VersionProto";
option java_package = "com.google.actions.sdk.v2";

// Definition of version resource.
message Version {
  option (google.api.resource) = {
    type: "actions.googleapis.com/Version"
    pattern: "projects/{project}/versions/{version}"
  };

  // Represents the current state of the version.
  message VersionState {
    // Enum indicating the states that a Version can take. This enum is not yet
    // frozen and values maybe added later.
    enum State {
      // Default value of State.
      STATE_UNSPECIFIED = 0;

      // The version creation is in progress.
      CREATION_IN_PROGRESS = 1;

      // The version creation failed.
      CREATION_FAILED = 2;

      // The version has been successfully created.
      CREATED = 3;

      // The version is under policy review (aka Approval).
      REVIEW_IN_PROGRESS = 4;

      // The version has been approved for policy review and can be deployed.
      APPROVED = 5;

      // The version has been conditionally approved but is pending final
      // review. It may be rolled back if final review is denied.
      CONDITIONALLY_APPROVED = 6;

      // The version has been denied for policy review.
      DENIED = 7;

      // The version is taken down as entire agent and all versions are taken
      // down.
      UNDER_TAKEDOWN = 8;

      // The version has been deleted.
      DELETED = 9;
    }

    // The current state of the version.
    State state = 1;

    // User-friendly message for the current state of the version.
    string message = 2;
  }

  // The unique identifier of the version in the following format.
  // `projects/{project}/versions/{version}`.
  string name = 1;

  // The current state of the version.
  VersionState version_state = 2;

  // Email of the user who created this version.
  string creator = 3;

  // Timestamp of the last change to this version.
  google.protobuf.Timestamp update_time = 4;
}
