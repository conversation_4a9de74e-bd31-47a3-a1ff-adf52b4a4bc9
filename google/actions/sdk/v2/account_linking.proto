// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "AccountLinkingProto";
option java_package = "com.google.actions.sdk.v2";

// AccountLinking allows Google to guide the user to sign-in to the App's web
// services.
//
// For Google Sign In and OAuth + Google Sign In linking types, Google generates
// a client ID identifying your App to Google ("Client ID issued by Google to
// your Actions" on Console UI). This field is read-only and can be checked by
// navigating to the Console UI's Account Linking page.
// See: https://developers.google.com/assistant/identity/google-sign-in
//
// Note: For all account linking setting types (except for Google Sign In), you
// must provide a username and password for a test account in
// Settings.testing_instructions for the review team to review the app (they
// will not be visible to users).
message AccountLinking {
  // The type of Account Linking to perform.
  enum LinkingType {
    // Unspecified.
    LINKING_TYPE_UNSPECIFIED = 0;

    // Google Sign In linking type.
    // If using this linking type, no OAuth-related fields need to be set below.
    GOOGLE_SIGN_IN = 1;

    // OAuth and Google Sign In linking type.
    OAUTH_AND_GOOGLE_SIGN_IN = 2;

    // OAuth linking type.
    OAUTH = 3;
  }

  // The OAuth2 grant type Google uses to guide the user to sign in to your
  // App's web service.
  enum AuthGrantType {
    // Unspecified.
    AUTH_GRANT_TYPE_UNSPECIFIED = 0;

    // Authorization code grant. Requires you to provide both
    // authentication URL and access token URL.
    AUTH_CODE = 1;

    // Implicit code grant. Only requires you to provide authentication
    // URL.
    IMPLICIT = 2;
  }

  // Required. If `true`, users are allowed to sign up for new accounts via voice.
  // If `false`, account creation is only allowed on your website. Select this
  // option if you want to display your terms of service or obtain user consents
  // during sign-up.
  // linking_type cannot be GOOGLE_SIGN_IN when this is `false`.
  // linking_type cannot be OAUTH when this is `true`.
  bool enable_account_creation = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The linking type to use.
  // See https://developers.google.com/assistant/identity for further details on
  // the linking types.
  LinkingType linking_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates the type of authentication for OAUTH linking_type.
  AuthGrantType auth_grant_type = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Client ID issued by your App to Google.
  // This is the OAuth2 Client ID identifying Google to your service.
  // Only set when using OAuth.
  string app_client_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Endpoint for your sign-in web page that supports OAuth2 code or
  // implicit flows.
  // URL must use HTTPS.
  // Only set when using OAuth.
  string authorization_url = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. OAuth2 endpoint for token exchange.
  // URL must use HTTPS.
  // This is not set when only using OAuth with IMPLICIT grant as the
  // linking type.
  // Only set when using OAuth.
  string token_url = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List of permissions the user must consent to in order to use
  // your service.
  // Only set when using OAuth.
  // Make sure to provide a Terms of Service in the directory information in
  // LocalizedSettings.terms_of_service_url section if specifying this field.
  repeated string scopes = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This is the web page on your service which describes the
  // permissions the user is granting to Google.
  // Only set if using OAuth and Google Sign In.
  // Make sure to provide a Terms of Service in the directory information in
  // LocalizedSettings.terms_of_service_url section if specifying this field.
  string learn_more_url = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, allow Google to transmit client ID and secret via HTTP
  // basic auth header. Otherwise, Google uses the client ID and secret inside
  // the post body.
  // Only set when using OAuth.
  // Make sure to provide a Terms of Service in the directory information in
  // LocalizedSettings.terms_of_service_url section if specifying this field.
  bool use_basic_auth_header = 9 [(google.api.field_behavior) = OPTIONAL];
}
