// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/api/resource.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "ReleaseChannelProto";
option java_package = "com.google.actions.sdk.v2";

// Definition of release channel resource.
message ReleaseChannel {
  option (google.api.resource) = {
    type: "actions.googleapis.com/ReleaseChannel"
    pattern: "projects/{project}/releaseChannels/{release_channel}"
  };

  // The unique name of the release channel in the following format.
  // `projects/{project}/releaseChannels/{release_channel}`.
  string name = 1;

  // Version currently deployed to this release channel in the following format:
  // `projects/{project}/versions/{version}`.
  string current_version = 2;

  // Version to be deployed to this release channel in the following format:
  // `projects/{project}/versions/{version}`.
  string pending_version = 3;
}
