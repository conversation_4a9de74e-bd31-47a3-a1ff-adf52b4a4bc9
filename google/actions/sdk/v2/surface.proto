// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "SurfaceProto";
option java_package = "com.google.actions.sdk.v2";

// Contains a set of requirements that the client surface must support to invoke
// Actions in your project.
message SurfaceRequirements {
  // The minimum set of capabilities needed to invoke the Actions in your
  // project. If the surface is missing any of these, the Action will not be
  // triggered.
  repeated CapabilityRequirement minimum_requirements = 1;
}

// Represents a requirement about the availability of a given capability.
message CapabilityRequirement {
  // Possible set of surface capabilities.
  enum SurfaceCapability {
    // Unknown / Unspecified.
    SURFACE_CAPABILITY_UNSPECIFIED = 0;

    // Surface supports audio output.
    AUDIO_OUTPUT = 1;

    // Surface supports screen/visual output.
    SCREEN_OUTPUT = 2;

    // Surface supports media response audio.
    MEDIA_RESPONSE_AUDIO = 3;

    // Surface supports web browsers.
    WEB_BROWSER = 4;

    // Surface supports account linking.
    ACCOUNT_LINKING = 7;

    // Surface supports Interactive Canvas.
    INTERACTIVE_CANVAS = 8;

    // Surface supports home storage.
    HOME_STORAGE = 9;
  }

  // The type of capability.
  SurfaceCapability capability = 1;
}
