// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "WebhookProto";
option java_package = "com.google.actions.sdk.v2";

// Metadata for different types of webhooks. If you're using
// `inlineCloudFunction`, your source code must be in a directory with the same
// name as the value for the `executeFunction` key.
// For example, a value of `my_webhook` for the`executeFunction` key would have
// a code structure like this:
//  - `/webhooks/my_webhook.yaml`
//  - `/webhooks/my_webhook/index.js`
//  - `/webhooks/my_webhook/package.json`
message Webhook {
  // Declares the name of the webhoook handler. A webhook can have
  // multiple handlers registered. These handlers can be called from multiple
  // places in your Actions project.
  message Handler {
    // Required. Name of the handler. Must be unique across all handlers the Actions
    // project. You can check the name of this handler to invoke the correct
    // function in your fulfillment source code.
    string name = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // REST endpoint to notify if you're not using the inline editor.
  message HttpsEndpoint {
    // The HTTPS base URL for your fulfillment endpoint (HTTP is not supported).
    // Handler names are appended to the base URL path after a colon
    // (following the style guide in
    // https://cloud.google.com/apis/design/custom_methods).
    // For example a base URL of 'https://gactions.service.com/api' would
    // receive requests with URL 'https://gactions.service.com/api:{method}'.
    string base_url = 1;

    // Map of HTTP parameters to be included in the POST request.
    map<string, string> http_headers = 2;

    // Version of the protocol used by the endpoint. This is the protocol shared
    // by all fulfillment types and not specific to Google fulfillment type.
    int32 endpoint_api_version = 3;
  }

  // Holds the metadata of an inline Cloud Function deployed from the
  // webhooks folder.
  message InlineCloudFunction {
    // The name of the Cloud Function entry point. The value of this field
    // should match the name of the method exported from the source code.
    string execute_function = 1;
  }

  // List of handlers for this webhook.
  repeated Handler handlers = 1;

  // Only one webhook type is supported.
  oneof webhook_type {
    // Custom webhook HTTPS endpoint.
    HttpsEndpoint https_endpoint = 2;

    // Metadata for cloud function deployed from code in the webhooks folder.
    InlineCloudFunction inline_cloud_function = 3;
  }
}
