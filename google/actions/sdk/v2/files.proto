// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/actions/sdk/v2/config_file.proto";
import "google/actions/sdk/v2/data_file.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "FilesProto";
option java_package = "com.google.actions.sdk.v2";

// Wrapper for a list of files.
message Files {
  // Only one type of files can be sent to the server at a time, config files or
  // data files.
  oneof file_type {
    // List of config files. This includes manifest, settings, interaction model
    // resource bundles and more.
    ConfigFiles config_files = 1;

    // List of data files. This includes image, audio file, cloud function
    // source code.
    DataFiles data_files = 2;
  }
}
