# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "conversation_proto",
    srcs = [
        "intent.proto",
        "scene.proto",
    ],
    deps = [
        "//google/actions/sdk/v2/conversation/prompt:prompt_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
