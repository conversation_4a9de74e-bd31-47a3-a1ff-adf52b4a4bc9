// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

import "google/protobuf/struct.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "IntentProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// Represents an intent.
message Intent {
  // Required. The name of the last matched intent.
  string name = 1;

  // Required. Represents parameters identified as part of intent matching.
  // This is a map of the name of the identified parameter to the value of the
  // parameter identified from user input. All parameters defined in
  // the matched intent that are identified will be surfaced here.
  map<string, IntentParameterValue> params = 2;

  // Optional. Typed or spoken input from the end user that matched this intent.
  // This will be populated when an intent is matched, based on the user input.
  string query = 3;
}

// Represents a value for intent parameter.
message IntentParameterValue {
  // Required. Original text value extracted from user utterance.
  string original = 1;

  // Required. Structured value for parameter extracted from user input.
  // This will only be populated if the parameter is defined in the matched
  // intent and the value of the parameter could be identified during intent
  // matching.
  google.protobuf.Value resolved = 2;
}
