// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "SimpleProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// Represents a simple prompt to be send to a user.
message Simple {
  // Optional. Represents the speech to be spoken to the user. Can be SSML or
  // text to speech.
  // If the "override" field in the containing prompt is "true", the speech
  // defined in this field replaces the previous Simple prompt's speech.
  string speech = 1;

  // Optional text to display in the chat bubble. If not given, a display
  // rendering of the speech field above will be used. Limited to 640
  // chars.
  // If the "override" field in the containing prompt is "true", the text
  // defined in this field replaces to the previous Simple prompt's text.
  string text = 2;
}
