# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "prompt_proto",
    srcs = [
        "prompt.proto",
        "simple.proto",
        "suggestion.proto",
    ],
    deps = [
        "//google/actions/sdk/v2/conversation/prompt/content:content_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
