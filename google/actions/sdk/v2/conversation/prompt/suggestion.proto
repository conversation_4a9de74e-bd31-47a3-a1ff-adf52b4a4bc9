// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "SuggestionProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// Input suggestion to be presented to the user.
message Suggestion {
  // Required. The text shown in the suggestion chip. When tapped, this text will be
  // posted back to the conversation verbatim as if the user had typed it.
  // Each title must be unique among the set of suggestion chips.
  // Max 25 chars
  string title = 1;
}
