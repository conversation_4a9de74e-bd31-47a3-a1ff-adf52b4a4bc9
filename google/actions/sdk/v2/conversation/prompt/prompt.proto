// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

import "google/actions/sdk/v2/conversation/prompt/content/canvas.proto";
import "google/actions/sdk/v2/conversation/prompt/content/content.proto";
import "google/actions/sdk/v2/conversation/prompt/content/link.proto";
import "google/actions/sdk/v2/conversation/prompt/simple.proto";
import "google/actions/sdk/v2/conversation/prompt/suggestion.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "PromptProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// Represent a response to a user.
message Prompt {
  // Optional. Mode for how this messages should be merged with previously
  // defined messages.
  // "false" will clear all previously defined messages (first and last
  // simple, content, suggestions link and canvas) and add messages defined in
  // this prompt.
  // "true" will add messages defined in this prompt to messages defined in
  // previous responses. Setting this field to "true" will also enable appending
  // to some fields inside Simple prompts, the Suggestion prompt and the Canvas
  // prompt (part of the Content prompt). The Content and Link messages will
  // always be overwritten if defined in the prompt.
  // Default value is "false".
  bool append = 1 [deprecated = true];

  // Optional. Mode for how this messages should be merged with previously
  // defined messages.
  // "true" clears all previously defined messages (first and last
  // simple, content, suggestions link and canvas) and adds messages defined in
  // this prompt.
  // "false" adds messages defined in this prompt to messages defined in
  // previous responses. Leaving this field to "false" also enables
  // appending to some fields inside Simple prompts, the Suggestions prompt,
  // and the Canvas prompt (part of the Content prompt). The Content and Link
  // messages are always overwritten if defined in the prompt. Default
  // value is "false".
  bool override = 8;

  // Optional. The first voice and text-only response.
  Simple first_simple = 2;

  // Optional. A content like a card, list or media to display to the user.
  Content content = 3;

  // Optional. The last voice and text-only response.
  Simple last_simple = 4;

  // Optional. Suggestions to be displayed to the user which will always appear
  // at the end of the response.
  // If the "override" field in the containing prompt is "false", the titles
  // defined in this field will be added to titles defined in any previously
  // defined suggestions prompts and duplicate values will be removed.
  repeated Suggestion suggestions = 5;

  // Optional. An additional suggestion chip that can link out to the associated app
  // or site.
  // The chip will be rendered with the title "Open <name>". Max 20 chars.
  Link link = 6;

  // Optional. Represents a Interactive Canvas response to be sent to the user.
  Canvas canvas = 9;
}
