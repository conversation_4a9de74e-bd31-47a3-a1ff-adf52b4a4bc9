// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "ListProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// A card for presenting a list of options to select from.
message List {
  // An item in the list
  message ListItem {
    // Required. The NLU key that matches the entry key name in the associated
    // Type.
    string key = 1;
  }

  // Title of the list. Optional.
  string title = 1;

  // Subtitle of the list. Optional.
  string subtitle = 2;

  // min: 2 max: 30
  repeated ListItem items = 3;
}
