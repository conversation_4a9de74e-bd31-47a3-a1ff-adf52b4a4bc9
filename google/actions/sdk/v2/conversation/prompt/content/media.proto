// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

import "google/actions/sdk/v2/conversation/prompt/content/image.proto";
import "google/protobuf/duration.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "MediaProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// Represents one media object.
// Contains information about the media, such as name, description, url, etc.
message Media {
  // Media type of this response.
  enum MediaType {
    // Unspecified media type.
    MEDIA_TYPE_UNSPECIFIED = 0;

    // Audio file.
    AUDIO = 1;

    // Response to acknowledge a media status report.
    MEDIA_STATUS_ACK = 2;
  }

  // Optional media control types the media response can support
  enum OptionalMediaControls {
    // Unspecified value
    OPTIONAL_MEDIA_CONTROLS_UNSPECIFIED = 0;

    // Paused event. Triggered when user pauses the media.
    PAUSED = 1;

    // Stopped event. Triggered when user exits out of 3p session during media
    // play.
    STOPPED = 2;
  }

  // Media type.
  MediaType media_type = 8;

  // Start offset of the first media object.
  google.protobuf.Duration start_offset = 5;

  // Optional media control types this media response session can support.
  // If set, request will be made to 3p when a certain media event happens.
  // If not set, 3p must still handle two default control type, FINISHED and
  // FAILED.
  repeated OptionalMediaControls optional_media_controls = 6;

  // List of Media Objects
  repeated MediaObject media_objects = 7;
}

// Represents a single media object
message MediaObject {
  // Name of this media object.
  string name = 1;

  // Description of this media object.
  string description = 2;

  // The url pointing to the media content.
  string url = 3;

  // Image to show with the media card.
  MediaImage image = 4;
}

// Image to show with the media card.
message MediaImage {
  // Image.
  oneof image {
    // A large image, such as the cover of the album, etc.
    Image large = 1;

    // A small image icon displayed on the right from the title.
    // It's resized to 36x36 dp.
    Image icon = 2;
  }
}
