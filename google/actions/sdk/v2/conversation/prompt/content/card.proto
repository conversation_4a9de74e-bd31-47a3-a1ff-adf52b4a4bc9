// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

import "google/actions/sdk/v2/conversation/prompt/content/image.proto";
import "google/actions/sdk/v2/conversation/prompt/content/link.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "CardProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// A basic card for displaying some information, e.g. an image and/or text.
message Card {
  // Overall title of the card.
  // Optional.
  string title = 1;

  // Optional.
  string subtitle = 2;

  // Body text of the card.
  // Supports a limited set of markdown syntax for formatting.
  // Required, unless image is present.
  string text = 3;

  // A hero image for the card. The height is fixed to 192dp.
  // Optional.
  Image image = 4;

  // How the image background will be filled. Optional.
  Image.ImageFill image_fill = 5;

  // Button.
  // Optional.
  Link button = 6;
}
