# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "content_proto",
    srcs = [
        "canvas.proto",
        "card.proto",
        "collection.proto",
        "content.proto",
        "image.proto",
        "link.proto",
        "list.proto",
        "media.proto",
        "table.proto",
    ],
    deps = [
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
