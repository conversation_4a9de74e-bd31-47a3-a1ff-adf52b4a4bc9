// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

import "google/actions/sdk/v2/conversation/prompt/content/image.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "CollectionProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// A card for presenting a collection of options to select from.
message Collection {
  // An item in the collection
  message CollectionItem {
    // Required. The NLU key that matches the entry key name in the associated
    // Type.
    string key = 1;
  }

  // Title of the collection. Optional.
  string title = 1;

  // Subtitle of the collection. Optional.
  string subtitle = 2;

  // min: 2 max: 10
  repeated CollectionItem items = 3;

  // How the image backgrounds of collection items will be filled. Optional.
  Image.ImageFill image_fill = 4;
}
