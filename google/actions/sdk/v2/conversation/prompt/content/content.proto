// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

import "google/actions/sdk/v2/conversation/prompt/content/canvas.proto";
import "google/actions/sdk/v2/conversation/prompt/content/card.proto";
import "google/actions/sdk/v2/conversation/prompt/content/collection.proto";
import "google/actions/sdk/v2/conversation/prompt/content/image.proto";
import "google/actions/sdk/v2/conversation/prompt/content/list.proto";
import "google/actions/sdk/v2/conversation/prompt/content/media.proto";
import "google/actions/sdk/v2/conversation/prompt/content/table.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "ContentProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// Content to be shown.
message Content {
  // Content.
  oneof content {
    // A basic card.
    Card card = 1;

    // An image.
    Image image = 2;

    // Table card.
    Table table = 3;

    // Response indicating a set of media to be played.
    Media media = 4;

    // A response to be used for interactive canvas experience.
    Canvas canvas = 5 [deprecated = true];

    // A card presenting a collection of options to select from.
    Collection collection = 6;

    // A card presenting a list of options to select from.
    List list = 7;
  }
}
