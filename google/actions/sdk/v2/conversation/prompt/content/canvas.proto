// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

import "google/protobuf/struct.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "CanvasProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// Represents an Interactive Canvas response to be sent to the user.
// This can be used in conjunction with the "first_simple" field in the
// containing prompt to speak to the user in addition to displaying a
// interactive canvas response. The maximum size of the response is 50k bytes.
message Canvas {
  // URL of the interactive canvas web app to load. If not set, the url from
  // current active canvas will be reused.
  string url = 1;

  // Optional. JSON data to be passed through to the immersive experience
  // web page as an event.
  // If the "override" field in the containing prompt is "false" data values
  // defined in this Canvas prompt will be added after data values defined in
  // previous Canvas prompts.
  repeated google.protobuf.Value data = 4;

  // Optional. Default value: false.
  bool suppress_mic = 3;

  // If `true` the canvas application occupies the full screen and won't
  // have a header at the top. A toast message will also be displayed on the
  // loading screen that includes the Action's display name, the developer's
  // name, and instructions for exiting the Action. Default value: `false`.
  bool enable_full_screen = 8;
}
