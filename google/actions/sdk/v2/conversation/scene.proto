// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2.conversation;

import "google/actions/sdk/v2/conversation/prompt/prompt.proto";
import "google/protobuf/struct.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2/conversation;conversation";
option java_multiple_files = true;
option java_outer_classname = "SceneProto";
option java_package = "com.google.actions.sdk.v2.conversation";

// Represents the current status of slot filling.
enum SlotFillingStatus {
  // Fallback value when the usage field is not populated.
  UNSPECIFIED = 0;

  // The slots have been initialized but slot filling has not started.
  INITIALIZED = 1;

  // The slot values are being collected.
  COLLECTING = 2;

  // All slot values are final and cannot be changed.
  FINAL = 4;
}

// Represents a slot.
message Slot {
  // Represents the mode of a slot, that is, if it is required or not.
  enum SlotMode {
    // Fallback value when the usage field is not populated.
    MODE_UNSPECIFIED = 0;

    // Indicates that the slot is not required to complete slot filling.
    OPTIONAL = 1;

    // Indicates that the slot is required to complete slot filling.
    REQUIRED = 2;
  }

  // Represents the status of a slot.
  enum SlotStatus {
    // Fallback value when the usage field is not populated.
    SLOT_UNSPECIFIED = 0;

    // Indicates that the slot does not have any values. This status cannot be
    // modified through the response.
    EMPTY = 1;

    // Indicates that the slot value is invalid. This status can be set
    // through the response.
    INVALID = 2;

    // Indicates that the slot has a value. This status cannot be modified
    // through the response.
    FILLED = 3;
  }

  // The mode of the slot (required or optional). Can be set by developer.
  SlotMode mode = 1;

  // The status of the slot.
  SlotStatus status = 2;

  // The value of the slot. Changing this value in the response, will
  // modify the value in slot filling.
  google.protobuf.Value value = 3;

  // Indicates if the slot value was collected on the last turn.
  // This field is read-only.
  bool updated = 4;

  // Optional. This prompt is sent to the user when needed to fill a required
  // slot. This prompt overrides the existing prompt defined in the console.
  // This field is not included in the webhook request.
  Prompt prompt = 5;
}
