// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/actions/sdk/v2/theme_customization.proto";
import "google/api/field_behavior.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "LocalizedSettingsProto";
option java_package = "com.google.actions.sdk.v2";

// Represents settings of an Actions project that are specific to a user locale.
// In this instance, user means the end user who invokes your Actions.
// **This message is localizable.**
message LocalizedSettings {
  // Required. The default display name for this Actions project (if there is no
  // translation available)
  string display_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The pronunciation of the display name to invoke it within a voice
  // (spoken) context.
  string pronunciation = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The default short description for the Actions project (if there is no
  // translation available). 80 character limit.
  string short_description = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The default long description for the Actions project (if there is no
  // translation available). 4000 character limit.
  string full_description = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. Small square image, 192 x 192 px.
  // This should be specified as a reference to the corresponding image in the
  // `resources/images/` directory. For example, `$resources.images.foo` (without the
  // extension) for image in `resources/images/foo.jpg`
  // When working on a project pulled from Console, the Google-managed URL
  // pulled could be used. URLs from external sources are not allowed.
  string small_logo_image = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. Large landscape image, 1920 x 1080 px.
  // This should be specified as a reference to the corresponding image in the
  // `resources/images/` directory. For example, `$resources.images.foo` (without the
  // extension) for image in `resources/images/foo.jpg`
  // When working on a project pulled from Console, the Google-managed URL
  // pulled could be used. URLs from external sources are not allowed.
  string large_banner_image = 6 [(google.api.field_behavior) = OPTIONAL];

  // Required. The name of the developer to be displayed to users.
  string developer_name = 7 [(google.api.field_behavior) = REQUIRED];

  // Required. The contact email address for the developer.
  string developer_email = 8 [(google.api.field_behavior) = REQUIRED];

  // Optional. The terms of service URL.
  string terms_of_service_url = 9 [(google.api.field_behavior) = OPTIONAL];

  // Required. The Google Assistant voice type that users hear when they interact with
  // your Actions. The supported values are "male_1", "male_2", "female_1", and
  // "female_2".
  string voice = 10 [(google.api.field_behavior) = REQUIRED];

  // Optional. The locale for the specified voice. If not specified, this resolves
  // to the user's Assistant locale. If specified, the voice locale must have
  // the same root language as the locale specified in LocalizedSettings.
  string voice_locale = 14 [(google.api.field_behavior) = OPTIONAL];

  // Required. The privacy policy URL.
  string privacy_policy_url = 11 [(google.api.field_behavior) = REQUIRED];

  // Optional. Sample invocation phrases displayed as part of your Actions project's
  // description in the Assistant directory. This will help users learn how to
  // use it.
  repeated string sample_invocations = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Theme customizations for visual components of your Actions.
  ThemeCustomization theme_customization = 13 [(google.api.field_behavior) = OPTIONAL];
}
