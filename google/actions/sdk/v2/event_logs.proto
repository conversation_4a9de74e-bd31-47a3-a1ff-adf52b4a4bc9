// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/actions/sdk/v2/conversation/intent.proto";
import "google/actions/sdk/v2/conversation/prompt/prompt.proto";
import "google/actions/sdk/v2/conversation/scene.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "EventLogsProto";
option java_package = "com.google.actions.sdk.v2";

// Contains information about execution event which happened during processing
// Actions Builder conversation request. For an overview of the stages involved
// in a conversation request, see
// https://developers.google.com/assistant/conversational/actions.
message ExecutionEvent {
  // Timestamp when the event happened.
  google.protobuf.Timestamp event_time = 1;

  // State of the execution during this event.
  ExecutionState execution_state = 2;

  // Resulting status of particular execution step.
  google.rpc.Status status = 3;

  // Detailed information specific to different of events that may be involved
  // in processing a conversation round. The field set here defines the type of
  // this event.
  oneof EventData {
    // User input handling event.
    UserConversationInput user_input = 4;

    // Intent matching event.
    IntentMatch intent_match = 5;

    // Condition evaluation event.
    ConditionsEvaluated conditions_evaluated = 6;

    // OnSceneEnter execution event.
    OnSceneEnter on_scene_enter = 7;

    // Webhook request dispatch event.
    WebhookRequest webhook_request = 8;

    // Webhook response receipt event.
    WebhookResponse webhook_response = 9;

    // Webhook-initiated transition event.
    WebhookInitiatedTransition webhook_initiated_transition = 10;

    // Slot matching event.
    SlotMatch slot_match = 11;

    // Slot requesting event.
    SlotRequested slot_requested = 12;

    // Slot validation event.
    SlotValidated slot_validated = 13;

    // Form filling event.
    FormFilled form_filled = 14;

    // Waiting-for-user-input event.
    WaitingForUserInput waiting_user_input = 15;

    // End-of-conversation event.
    EndConversation end_conversation = 16;
  }

  // List of warnings generated during execution of this Event. Warnings are
  // tips for the developer discovered during the conversation request. These
  // are usually non-critical and do not halt the execution of the request. For
  // example, a warnings might be generated when webhook tries to override a
  // custom Type which does not exist. Errors are reported as a failed status
  // code, but warnings can be present even when the status is OK.
  repeated string warning_messages = 17;
}

// Current state of the execution.
message ExecutionState {
  // ID of the scene which is currently  active.
  string current_scene_id = 1;

  // State of the session storage:
  // https://developers.google.com/assistant/conversational/storage-session
  google.protobuf.Struct session_storage = 2;

  // State of the slots filling, if applicable:
  // https://developers.google.com/assistant/conversational/scenes#slot_filling
  Slots slots = 5;

  // Prompt queue:
  // https://developers.google.com/assistant/conversational/prompts
  repeated google.actions.sdk.v2.conversation.Prompt prompt_queue = 7;

  // State of the user storage:
  // https://developers.google.com/assistant/conversational/storage-user
  google.protobuf.Struct user_storage = 6;

  // State of the home storage:
  // https://developers.google.com/assistant/conversational/storage-home
  google.protobuf.Struct household_storage = 8;
}

// Represents the current state of a the scene's slots.
message Slots {
  // The current status of slot filling.
  google.actions.sdk.v2.conversation.SlotFillingStatus status = 2;

  // The slots associated with the current scene.
  map<string, google.actions.sdk.v2.conversation.Slot> slots = 3;
}

// Information related to user input.
message UserConversationInput {
  // Type of user input. E.g. keyboard, voice, touch, etc.
  string type = 1;

  // Original text input from the user.
  string original_query = 2;
}

// Information about triggered intent match (global or within a scene):
// https://developers.google.com/assistant/conversational/intents
message IntentMatch {
  // Intent id which triggered this interaction.
  string intent_id = 1;

  // Parameters of intent which triggered this interaction.
  map<string, google.actions.sdk.v2.conversation.IntentParameterValue> intent_parameters = 5;

  // Name of the handler attached to this interaction.
  string handler = 3;

  // Scene to which this interaction leads to.
  string next_scene_id = 4;
}

// Results of conditions evaluation:
// https://developers.google.com/assistant/conversational/scenes#conditions
message ConditionsEvaluated {
  // List of conditions which were evaluated to 'false'.
  repeated Condition failed_conditions = 1;

  // The first condition which was evaluated to 'true', if any.
  Condition success_condition = 2;
}

// Evaluated condition.
message Condition {
  // Expression specified in this condition.
  string expression = 1;

  // Handler name specified in evaluated condition.
  string handler = 2;

  // Destination scene specified in evaluated condition.
  string next_scene_id = 3;
}

// Information about execution of onSceneEnter stage:
// https://developers.google.com/assistant/conversational/scenes#on_enter
message OnSceneEnter {
  // Handler name specified in onSceneEnter event.
  string handler = 1;
}

// Event triggered by destination scene returned from webhook:
// https://developers.google.com/assistant/conversational/webhooks#transition_scenes
message WebhookInitiatedTransition {
  // ID of the scene the transition is leading to.
  string next_scene_id = 1;
}

// Information about a request dispatched to the Action webhook:
// https://developers.google.com/assistant/conversational/webhooks#payloads
message WebhookRequest {
  // Payload of the webhook request.
  string request_json = 1;
}

// Information about a response received from the Action webhook:
// https://developers.google.com/assistant/conversational/webhooks#payloads
message WebhookResponse {
  // Payload of the webhook response.
  string response_json = 1;
}

// Information about matched slot(s):
// https://developers.google.com/assistant/conversational/scenes#slot_filling
message SlotMatch {
  // Parameters extracted by NLU from user input.
  map<string, google.actions.sdk.v2.conversation.IntentParameterValue> nlu_parameters = 2;
}

// Information about currently requested slot:
// https://developers.google.com/assistant/conversational/scenes#slot_filling
message SlotRequested {
  // Name of the requested slot.
  string slot = 1;

  // Slot prompt.
  google.actions.sdk.v2.conversation.Prompt prompt = 3;
}

// Event which happens after webhook validation was finished for slot(s):
// https://developers.google.com/assistant/conversational/scenes#slot_filling
message SlotValidated {

}

// Event which happens when form is fully filled:
// https://developers.google.com/assistant/conversational/scenes#slot_filling
message FormFilled {

}

// Event which happens when system needs user input:
// https://developers.google.com/assistant/conversational/scenes#input
message WaitingForUserInput {

}

// Event which informs that conversation with agent was ended.
message EndConversation {

}
