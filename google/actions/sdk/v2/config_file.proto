// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/actions/sdk/v2/account_linking_secret.proto";
import "google/actions/sdk/v2/action.proto";
import "google/actions/sdk/v2/interactionmodel/entity_set.proto";
import "google/actions/sdk/v2/interactionmodel/global_intent_event.proto";
import "google/actions/sdk/v2/interactionmodel/intent.proto";
import "google/actions/sdk/v2/interactionmodel/prompt/static_prompt.proto";
import "google/actions/sdk/v2/interactionmodel/scene.proto";
import "google/actions/sdk/v2/interactionmodel/type/type.proto";
import "google/actions/sdk/v2/manifest.proto";
import "google/actions/sdk/v2/settings.proto";
import "google/actions/sdk/v2/webhook.proto";
import "google/protobuf/struct.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "ConfigFileProto";
option java_package = "com.google.actions.sdk.v2";

// Wrapper for repeated config files. Repeated fields cannot exist in a oneof.
message ConfigFiles {
  // Multiple config files.
  repeated ConfigFile config_files = 1;
}

// Represents a single file which contains structured data. Developers can
// define most of their project using structured config including Actions,
// Settings, Fulfillment.
message ConfigFile {
  // Relative path of the config file from the project root in the SDK file
  // structure. Each file types below have an allowed file path.
  // Eg: settings/settings.yaml
  string file_path = 1;

  // Each type of config file should have a corresponding field in the oneof.
  oneof file {
    // Single manifest file.
    // Allowed file path: `manifest.yaml`
    Manifest manifest = 2;

    // Single actions file with all the actions defined.
    // Allowed file paths: `actions/{language}?/actions.yaml`
    Actions actions = 3;

    // Single settings config which includes non-localizable settings and
    // settings for the project's default locale (if specified).
    // For a locale override file, only localized_settings field will be
    // populated.
    // Allowed file paths: `settings/{language}?/settings.yaml`
    // Note that the non-localized settings file `settings/settings.yaml` must
    // be present in the write flow requests.
    Settings settings = 4;

    // Single webhook definition.
    // Allowed file path: `webhooks/{WebhookName}.yaml`
    Webhook webhook = 6;

    // Single intent definition.
    // Allowed file paths: `custom/intents/{language}?/{IntentName}.yaml`
    google.actions.sdk.v2.interactionmodel.Intent intent = 7;

    // Single type definition.
    // Allowed file paths: `custom/types/{language}?/{TypeName}.yaml`
    google.actions.sdk.v2.interactionmodel.type.Type type = 8;

    // Single entity set definition.
    // Allowed file paths: `custom/entitySets/{language}?/{EntitySetName}.yaml`
    google.actions.sdk.v2.interactionmodel.EntitySet entity_set = 15;

    // Single global intent event definition.
    // Allowed file paths: `custom/global/{GlobalIntentEventName}.yaml`
    // The file name (GlobalIntentEventName) should be the name of the intent
    // that this global intent event corresponds to.
    google.actions.sdk.v2.interactionmodel.GlobalIntentEvent global_intent_event = 9;

    // Single scene definition.
    // Allowed file paths: `custom/scenes/{SceneName}.yaml`
    google.actions.sdk.v2.interactionmodel.Scene scene = 10;

    // Single static prompt definition.
    // Allowed file paths: `custom/prompts/{language}?/{StaticPromptName}.yaml`
    google.actions.sdk.v2.interactionmodel.prompt.StaticPrompt static_prompt = 11;

    // Metadata corresponding to the client secret used in account linking.
    // Allowed file path: `settings/accountLinkingSecret.yaml`
    AccountLinkingSecret account_linking_secret = 13;

    // Single resource bundle, which is a map from a string to a string or list
    // of strings. Resource bundles could be used for localizing strings in
    // static prompts.
    // Allowed file paths: `resources/strings/{language}?/{multiple
    // directories}?/{BundleName}.yaml`
    google.protobuf.Struct resource_bundle = 12;
  }
}
