type: google.api.Service
config_version: 1
name: type.aog.googleapis.com
title: Common Actions on Google Types

types:
- name: google.actions.type.DateRange
- name: google.actions.type.DateTimeRange

documentation:
  summary: Defines common types for Actions on Google APIs.
  overview: |-
    This package contains definitions of common types for Actions on Google
    APIs, which are used in addition to the common Google API types in the
    google.type package. These types are not versioned, and must go through
    extensive review before being created.

    Like the types in google.type, all types defined in this package are
    suitable for different APIs to exchange data, and will never break binary
    compatibility. They should have design quality comparable to major
    programming languages like Java and C#.
