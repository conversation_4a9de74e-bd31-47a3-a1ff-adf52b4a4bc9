load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################

proto_library(
    name = "date_range_proto",
    srcs = ["date_range.proto"],
    deps = [
        "//google/type:date_proto",
    ],
)

proto_library(
    name = "datetime_range_proto",
    srcs = ["datetime_range.proto"],
    deps = [
        "//google/type:datetime_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_proto_library",
)

java_proto_library(
    name = "type_java_proto",
    deps = [
        ":date_range_proto",
        ":datetime_range_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "date_range_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/date_range",
    protos = [
        ":date_range_proto",
    ],
    deps = ["//google/type:date_go_proto"],
)

go_proto_library(
    name = "datetime_range_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/datetime_range",
    protos = [
        ":datetime_range_proto",
    ],
    deps = ["//google/type:datetime_go_proto"],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_proto_library",
)

py_proto_library(
    name = "date_range_py_proto",
    deps = [":date_range_proto"],
)

py_proto_library(
    name = "datetime_range_py_proto",
    deps = [":datetime_range_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "date_range_php_proto",
    deps = [":date_range_proto"],
)

php_proto_library(
    name = "datetime_range_php_proto",
    deps = [":datetime_range_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "date_range_ruby_proto",
    deps = [":date_range_proto"],
)

ruby_proto_library(
    name = "datetime_range_ruby_proto",
    deps = [":datetime_range_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "date_range_csharp_proto",
    deps = [":date_range_proto"],
)

csharp_proto_library(
    name = "datetime_range_csharp_proto",
    deps = [":datetime_range_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_proto_library",
)

cc_proto_library(
    name = "date_range_cc_proto",
    deps = [
        ":date_range_proto",
    ],
)

cc_proto_library(
    name = "datetime_range_cc_proto",
    deps = [
        ":datetime_range_proto",
    ],
)
