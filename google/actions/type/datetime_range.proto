// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.actions.type;

import "google/type/datetime.proto";

option go_package = "google.golang.org/genproto/googleapis/type/date_time_range;date_time_range";
option java_multiple_files = true;
option java_outer_classname = "DateTimeRangeProto";
option java_package = "com.google.actions.type";
option objc_class_prefix = "AOGTP";

// Represents a date and time range. This can represent:
//
// * A range between points in time with time zone or offset, e.g. the duration
//   of a flight which starts in the "America/New_York" time zone and ends in
//   the "Australia/Sydney" time zone
// * A range between points in time without time zone/offset info, e.g. an
//   appointment in local time
// * A range starting at a specific date and time, e.g. the range of time which
//   can be measured in milliseconds since the Unix epoch (period starting with
//   1970-01-01T00:00:00Z)
// * A range ending at a specific date and time, e.g. range of time before
//   a deadline
//
// When considering whether a DateTime falls within a DateTimeRange, the start
// of the range is inclusive and the end is exclusive.
//
// While [google.type.DateTime][google.type.DateTime] allows zero years, DateTimeRange does not.
// Year must always be non-zero.
//
// When both start and end are set, either both or neither must have a
// time_offset. When set, time_offset can be specified by either utc_offset or
// time_zone and must match for start and end, that is if start has utc_offset
// set then end must also have utc_offset set. The values of utc_offset or
// time_zone need not be the same for start and end.
//
// When both start and end are set, start must be chronologically less than or
// equal to end. When start and end are equal, the range is empty.
//
// The semantics of start and end are the same as those of
// [google.type.DateTime][google.type.DateTime].
message DateTimeRange {
  // DateTime at which the date range begins. If unset, the range has no
  // beginning bound.
  google.type.DateTime start = 1;

  // DateTime at which the date range ends. If unset, the range has no ending
  // bound.
  google.type.DateTime end = 2;
}
