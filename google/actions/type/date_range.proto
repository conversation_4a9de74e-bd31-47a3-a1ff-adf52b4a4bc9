// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.actions.type;

import "google/type/date.proto";

option go_package = "google.golang.org/genproto/googleapis/type/date_range;date_range";
option java_multiple_files = true;
option java_outer_classname = "DateRangeProto";
option java_package = "com.google.actions.type";
option objc_class_prefix = "AOGTP";

// Represents a range based on whole or partial calendar dates, e.g. the
// duration of a hotel reservation or the Common Era. This can represent:
//
// * A range between full dates, e.g. the duration of a hotel reservation
// * A range between years, e.g. a historical era
// * A range between year/month dates, e.g. the duration of a job on a resume
// * A range beginning in a year, e.g. the Common Era
// * A range ending on a specific date, e.g. the period of time before an event
//
// While [google.type.Date][google.type.Date] allows zero years, DateRange does not. Year must
// always be non-zero.
//
// End cannot be chronologically before start. For example, if start has year
// 2000, end cannot have year 1999.
//
// When both set, start and end must have exactly the same precision. That is,
// they must have the same fields populated with non-zero values. For example,
// if start specifies only year and month, then end must also specify only year
// and month (but not day).
//
// The date range is inclusive. That is, the dates specified by start and end
// are part of the date range. For example, the date January 1, 2000 falls
// within any date with start or end equal to January 1, 2000. When determining
// whether a date is inside a date range, the date should only be compared to
// start and end when those values are set.
//
// When a date and date range are specified to different degrees of precision,
// the rules for evaluating whether that date is inside the date range are as
// follows:
//
//  * When comparing the date to the start of the date range, unspecified months
//    should be replaced with 1, and unspecified days should be replaced with 1.
//    For example, the year 2000 is within the date range with start equal to
//    January 1, 2000 and no end. And the date January 1, 2000 is within the
//    date range with start equal to the year 2000 and no end.
//
//  * When comparing the date to the end of the date range, unspecified months
//    should be replaced with 12, and unspecified days should be replaced with
//    the last valid day for the month/year. For example, the year 2000 is
//    within the date range with start equal to January 1, 1999 and end equal to
//    December 31, 2000. And the date December 31, 2001 is within the date range
//    with start equal to the year 2000 and end equal to the year 2001.
//
// The semantics of start and end are the same as those of [google.type.Date][google.type.Date],
// except that year must always be non-zero in DateRange.
message DateRange {
  // Date at which the date range begins. If unset, the date range has no
  // beginning bound.
  google.type.Date start = 1;

  // Date at which the date range ends. If unset, the date range has no ending
  // bound.
  google.type.Date end = 2;
}
