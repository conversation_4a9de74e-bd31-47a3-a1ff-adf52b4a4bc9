# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "memorystore_proto",
    srcs = [
        "memorystore.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "memorystore_proto_with_info",
    deps = [
        ":memorystore_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "memorystore_java_proto",
    deps = [":memorystore_proto"],
)

java_grpc_library(
    name = "memorystore_java_grpc",
    srcs = [":memorystore_proto"],
    deps = [":memorystore_java_proto"],
)

java_gapic_library(
    name = "memorystore_java_gapic",
    srcs = [":memorystore_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "memorystore_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "memorystore_v1.yaml",
    test_deps = [
        ":memorystore_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "rest",
    deps = [
        ":memorystore_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "memorystore_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.memorystore.v1.MemorystoreClientTest",
    ],
    runtime_deps = [":memorystore_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-memorystore-v1-java",
    include_samples = True,
    transport = "rest",
    deps = [
        ":memorystore_java_gapic",
        ":memorystore_java_grpc",
        ":memorystore_java_proto",
        ":memorystore_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "memorystore_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/memorystore/apiv1/memorystorepb",
    protos = [":memorystore_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "memorystore_go_gapic",
    srcs = [":memorystore_proto_with_info"],
    grpc_service_config = "memorystore_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/memorystore/apiv1;memorystore",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "memorystore_v1.yaml",
    transport = "rest",
    deps = [
        ":memorystore_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-memorystore-v1-go",
    deps = [
        ":memorystore_go_gapic",
        ":memorystore_go_gapic_srcjar-metadata.srcjar",
        ":memorystore_go_gapic_srcjar-snippets.srcjar",
        ":memorystore_go_gapic_srcjar-test.srcjar",
        ":memorystore_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "memorystore_py_gapic",
    srcs = [":memorystore_proto"],
    grpc_service_config = "memorystore_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "memorystore_v1.yaml",
    transport = "rest",
    deps = [
    ],
)

py_test(
    name = "memorystore_py_gapic_test",
    srcs = [
        "memorystore_py_gapic_pytest.py",
        "memorystore_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":memorystore_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "memorystore-v1-py",
    deps = [
        ":memorystore_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "memorystore_php_proto",
    deps = [":memorystore_proto"],
)

php_gapic_library(
    name = "memorystore_php_gapic",
    srcs = [":memorystore_proto_with_info"],
    grpc_service_config = "memorystore_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "memorystore_v1.yaml",
    transport = "rest",
    deps = [
        ":memorystore_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-memorystore-v1-php",
    deps = [
        ":memorystore_php_gapic",
        ":memorystore_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "memorystore_nodejs_gapic",
    package_name = "@google-cloud/memorystore",
    src = ":memorystore_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "memorystore_v1_grpc_service_config.json",
    package = "google.cloud.memorystore.v1",
    rest_numeric_enums = True,
    service_yaml = "memorystore_v1.yaml",
    transport = "rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "memorystore-v1-nodejs",
    deps = [
        ":memorystore_nodejs_gapic",
        ":memorystore_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "memorystore_ruby_proto",
    deps = [":memorystore_proto"],
)

ruby_grpc_library(
    name = "memorystore_ruby_grpc",
    srcs = [":memorystore_proto"],
    deps = [":memorystore_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "memorystore_ruby_gapic",
    srcs = [":memorystore_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-memorystore-v1"],
    grpc_service_config = "memorystore_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "memorystore_v1.yaml",
    transport = "rest",
    deps = [
        ":memorystore_ruby_grpc",
        ":memorystore_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-memorystore-v1-ruby",
    deps = [
        ":memorystore_ruby_gapic",
        ":memorystore_ruby_grpc",
        ":memorystore_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "memorystore_csharp_proto",
    deps = [":memorystore_proto"],
)

csharp_grpc_library(
    name = "memorystore_csharp_grpc",
    srcs = [":memorystore_proto"],
    deps = [":memorystore_csharp_proto"],
)

csharp_gapic_library(
    name = "memorystore_csharp_gapic",
    srcs = [":memorystore_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "memorystore_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "memorystore_v1.yaml",
    transport = "rest",
    deps = [
        ":memorystore_csharp_grpc",
        ":memorystore_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-memorystore-v1-csharp",
    deps = [
        ":memorystore_csharp_gapic",
        ":memorystore_csharp_grpc",
        ":memorystore_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "memorystore_cc_proto",
    deps = [":memorystore_proto"],
)

cc_grpc_library(
    name = "memorystore_cc_grpc",
    srcs = [":memorystore_proto"],
    grpc_only = True,
    deps = [":memorystore_cc_proto"],
)
