{"methodConfig": [{"name": [{"service": "google.cloud.memorystore.v1.Memorystore", "method": "ListInstances"}, {"service": "google.cloud.memorystore.v1.Memorystore", "method": "GetInstance"}, {"service": "google.cloud.memorystore.v1.Memorystore", "method": "GetCertificateAuthority"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.memorystore.v1.Memorystore", "method": "CreateInstance"}, {"service": "google.cloud.memorystore.v1.Memorystore", "method": "UpdateInstance"}, {"service": "google.cloud.memorystore.v1.Memorystore", "method": "DeleteInstance"}], "timeout": "600s"}]}