{"methodConfig": [{"name": [{"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "ListSecrets"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "CreateSecret"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "AddSecretVersion"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "GetSecret"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "UpdateSecret"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "DeleteSecret"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "ListSecretVersions"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "GetSecretVersion"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "DisableSecretVersion"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "EnableSecretVersion"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "DestroySecretVersion"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "SetIamPolicy"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "GetIamPolicy"}, {"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "TestIamPermissions"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.secrets.v1beta1.SecretManagerService", "method": "AccessSecretVersion"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN"]}}]}