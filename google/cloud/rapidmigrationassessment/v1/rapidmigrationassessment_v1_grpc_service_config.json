{"methodConfig": [{"name": [{"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "GetAnnotation"}, {"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "GetCollector"}, {"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "ListCollectors"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "CreateAnnotation"}, {"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "CreateCollector"}, {"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "UpdateCollector"}, {"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "DeleteCollector"}, {"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "ResumeCollector"}, {"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "RegisterCollector"}, {"service": "google.cloud.rapidmigrationassessment.v1.RapidMigrationAssessment", "method": "PauseCollector"}], "timeout": "60s"}]}