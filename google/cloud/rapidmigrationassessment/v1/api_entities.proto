// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.rapidmigrationassessment.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.RapidMigrationAssessment.V1";
option go_package = "cloud.google.com/go/rapidmigrationassessment/apiv1/rapidmigrationassessmentpb;rapidmigrationassessmentpb";
option java_multiple_files = true;
option java_outer_classname = "ApiEntitiesProto";
option java_package = "com.google.cloud.rapidmigrationassessment.v1";
option php_namespace = "Google\\Cloud\\RapidMigrationAssessment\\V1";
option ruby_package = "Google::Cloud::RapidMigrationAssessment::V1";

// Message describing a MC Source of type Guest OS Scan.
message GuestOsScan {
  // reference to the corresponding Guest OS Scan in MC Source.
  string core_source = 1;
}

// Message describing a MC Source of type VSphere Scan.
message VSphereScan {
  // reference to the corresponding VSphere Scan in MC Source.
  string core_source = 1;
}

// Message describing Collector object.
message Collector {
  option (google.api.resource) = {
    type: "rapidmigrationassessment.googleapis.com/Collector"
    pattern: "projects/{project}/locations/{location}/collectors/{collector}"
  };

  // -- Using suggestion from API Linter Analyzer for nesting enum --
  // -- https://linter.aip.dev/216/nesting --
  // State of a Collector (server_side).
  // States are used for internal purposes and named to keep
  // convention of legacy product:
  // https://cloud.google.com/migrate/stratozone/docs/about-stratoprobe.
  enum State {
    // Collector state is not recognized.
    STATE_UNSPECIFIED = 0;

    // Collector started to create, but hasn't been completed MC source creation
    // and db object creation.
    STATE_INITIALIZING = 1;

    // Collector has been created, MC source creation and db object creation
    // completed.
    STATE_READY_TO_USE = 2;

    // Collector client has been registered with client.
    STATE_REGISTERED = 3;

    // Collector client is actively scanning.
    STATE_ACTIVE = 4;

    // Collector is not actively scanning.
    STATE_PAUSED = 5;

    // Collector is starting background job for deletion.
    STATE_DELETING = 6;

    // Collector completed all tasks for deletion.
    STATE_DECOMMISSIONED = 7;

    // Collector is in error state.
    STATE_ERROR = 8;
  }

  // name of resource.
  string name = 1;

  // Output only. Create time stamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time stamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // User specified name of the Collector.
  string display_name = 5;

  // User specified description of the Collector.
  string description = 6;

  // Service Account email used to ingest data to this Collector.
  string service_account = 7;

  // Output only. Store cloud storage bucket name (which is a guid) created with
  // this Collector.
  string bucket = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User specified expected asset count.
  int64 expected_asset_count = 9;

  // Output only. State of the Collector.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Client version.
  string client_version = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reference to MC Source Guest Os Scan.
  GuestOsScan guest_os_scan = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reference to MC Source vsphere_scan.
  VSphereScan vsphere_scan = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // How many days to collect data.
  int32 collection_days = 14;

  // Uri for EULA (End User License Agreement) from customer.
  string eula_uri = 15;
}

// Message describing an Annotation
message Annotation {
  option (google.api.resource) = {
    type: "rapidmigrationassessment.googleapis.com/Annotation"
    pattern: "projects/{project}/locations/{location}/annotations/{annotation}"
  };

  // Types for project level setting.
  enum Type {
    // Unknown type
    TYPE_UNSPECIFIED = 0;

    // Indicates that this project has opted into StratoZone export.
    TYPE_LEGACY_EXPORT_CONSENT = 1;

    // Indicates that this project is created by Qwiklab.
    TYPE_QWIKLAB = 2;
  }

  // name of resource.
  string name = 1;

  // Output only. Create time stamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time stamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // Type of an annotation.
  Type type = 5;
}
