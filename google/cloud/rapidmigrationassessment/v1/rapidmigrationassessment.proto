// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.rapidmigrationassessment.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/rapidmigrationassessment/v1/api_entities.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.RapidMigrationAssessment.V1";
option go_package = "cloud.google.com/go/rapidmigrationassessment/apiv1/rapidmigrationassessmentpb;rapidmigrationassessmentpb";
option java_multiple_files = true;
option java_outer_classname = "RapidMigrationAssessmentProto";
option java_package = "com.google.cloud.rapidmigrationassessment.v1";
option php_namespace = "Google\\Cloud\\RapidMigrationAssessment\\V1";
option ruby_package = "Google::Cloud::RapidMigrationAssessment::V1";

// Service describing handlers for resources.
service RapidMigrationAssessment {
  option (google.api.default_host) = "rapidmigrationassessment.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a Collector to manage the on-prem appliance which collects
  // information about Customer assets.
  rpc CreateCollector(CreateCollectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/collectors"
      body: "collector"
    };
    option (google.api.method_signature) = "parent,collector,collector_id";
    option (google.longrunning.operation_info) = {
      response_type: "Collector"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates an Annotation
  rpc CreateAnnotation(CreateAnnotationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/annotations"
      body: "annotation"
    };
    option (google.api.method_signature) = "parent,annotation";
    option (google.longrunning.operation_info) = {
      response_type: "Annotation"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single Annotation.
  rpc GetAnnotation(GetAnnotationRequest) returns (Annotation) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/annotations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Collectors in a given project and location.
  rpc ListCollectors(ListCollectorsRequest) returns (ListCollectorsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/collectors"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Collector.
  rpc GetCollector(GetCollectorRequest) returns (Collector) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/collectors/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the parameters of a single Collector.
  rpc UpdateCollector(UpdateCollectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{collector.name=projects/*/locations/*/collectors/*}"
      body: "collector"
    };
    option (google.api.method_signature) = "collector,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Collector"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Collector - changes state of collector to "Deleting".
  // Background jobs does final deletion thorugh producer api.
  rpc DeleteCollector(DeleteCollectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/collectors/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Collector"
      metadata_type: "OperationMetadata"
    };
  }

  // Resumes the given collector.
  rpc ResumeCollector(ResumeCollectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/collectors/*}:resume"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Collector"
      metadata_type: "OperationMetadata"
    };
  }

  // Registers the given collector.
  rpc RegisterCollector(RegisterCollectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/collectors/*}:register"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Collector"
      metadata_type: "OperationMetadata"
    };
  }

  // Pauses the given collector.
  rpc PauseCollector(PauseCollectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/collectors/*}:pause"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Collector"
      metadata_type: "OperationMetadata"
    };
  }
}

// Message for creating an AnnotationS.
message CreateAnnotationRequest {
  // Required. Name of the parent (project+location).
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The resource being created.
  Annotation annotation = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for getting a specific Annotation
message GetAnnotationRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "rapidmigrationassessment.googleapis.com/Annotation"
    }
  ];
}

// Message for creating a Collector.
message CreateCollectorRequest {
  // Required. Name of the parent (project+location).
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Id of the requesting object.
  string collector_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Collector collector = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Collectors.
message ListCollectorsRequest {
  // Required. Parent value for ListCollectorsRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Message for response to listing Collectors.
message ListCollectorsResponse {
  // The list of Collectors.
  repeated Collector collectors = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a specific Collector.
message GetCollectorRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "rapidmigrationassessment.googleapis.com/Collector"
    }
  ];
}

// Message for deleting a Collector.
message DeleteCollectorRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "rapidmigrationassessment.googleapis.com/Collector"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Collector.
message UpdateCollectorRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Collector resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Collector collector = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for resuming a Collector.
message ResumeCollectorRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "rapidmigrationassessment.googleapis.com/Collector"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for registering a Collector.
message RegisterCollectorRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "rapidmigrationassessment.googleapis.com/Collector"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for pausing a Collector.
message PauseCollectorRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "rapidmigrationassessment.googleapis.com/Collector"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
