# This build file includes a target for the Ruby wrapper library for
# google-cloud-rapid_migration_assessment.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for rapidmigrationassessment.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "rapidmigrationassessment_ruby_wrapper",
    srcs = ["//google/cloud/rapidmigrationassessment/v1:rapidmigrationassessment_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-rapid_migration_assessment",
        "ruby-cloud-wrapper-of=v1:0.3",
    ],
    service_yaml = "//google/cloud/rapidmigrationassessment/v1:rapidmigrationassessment_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-rapidmigrationassessment-ruby",
    deps = [
        ":rapidmigrationassessment_ruby_wrapper",
    ],
)