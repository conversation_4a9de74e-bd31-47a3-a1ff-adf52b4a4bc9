{"methodConfig": [{"name": [{"service": "google.cloud.resourcemanager.v3.Projects", "method": "ListProjects"}, {"service": "google.cloud.resourcemanager.v3.Projects", "method": "GetProject"}, {"service": "google.cloud.resourcemanager.v3.Projects", "method": "GetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "ListFolders"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "GetFolder"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "GetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.Organizations", "method": "GetOrganization"}, {"service": "google.cloud.resourcemanager.v3.Organizations", "method": "GetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.TagKeys", "method": "ListTagKeys"}, {"service": "google.cloud.resourcemanager.v3.TagKeys", "method": "GetTagKey"}, {"service": "google.cloud.resourcemanager.v3.TagKeys", "method": "GetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.TagValues", "method": "ListTagValues"}, {"service": "google.cloud.resourcemanager.v3.TagValues", "method": "GetTagValue"}, {"service": "google.cloud.resourcemanager.v3.TagValues", "method": "GetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.TagBindings", "method": "ListTagBindings"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.resourcemanager.v3.Projects", "method": "CreateProject"}, {"service": "google.cloud.resourcemanager.v3.Projects", "method": "DeleteProject"}, {"service": "google.cloud.resourcemanager.v3.Projects", "method": "UndeleteProject"}, {"service": "google.cloud.resourcemanager.v3.Projects", "method": "UpdateProject"}, {"service": "google.cloud.resourcemanager.v3.Projects", "method": "MoveProject"}, {"service": "google.cloud.resourcemanager.v3.Projects", "method": "SetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.Projects", "method": "SearchProjects"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "CreateFolder"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "DeleteFolder"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "UndeleteFolder"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "UpdateFolder"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "MoveFolder"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "SetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.Folders", "method": "SearchFolders"}, {"service": "google.cloud.resourcemanager.v3.Organizations", "method": "SearchOrganizations"}, {"service": "google.cloud.resourcemanager.v3.Organizations", "method": "SetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.TagKeys", "method": "CreateTagKey"}, {"service": "google.cloud.resourcemanager.v3.TagKeys", "method": "UpdateTagKey"}, {"service": "google.cloud.resourcemanager.v3.TagKeys", "method": "DeleteTagKey"}, {"service": "google.cloud.resourcemanager.v3.TagKeys", "method": "SetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.TagValues", "method": "CreateTagValue"}, {"service": "google.cloud.resourcemanager.v3.TagValues", "method": "UpdateTagValue"}, {"service": "google.cloud.resourcemanager.v3.TagValues", "method": "DeleteTagValue"}, {"service": "google.cloud.resourcemanager.v3.TagValues", "method": "SetIamPolicy"}, {"service": "google.cloud.resourcemanager.v3.TagBindings", "method": "CreateTagBinding"}, {"service": "google.cloud.resourcemanager.v3.TagBindings", "method": "DeleteTagBinding"}], "timeout": "60s"}]}