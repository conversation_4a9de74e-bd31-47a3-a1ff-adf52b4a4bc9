// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.resourcemanager.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ResourceManager.V3";
option go_package = "cloud.google.com/go/resourcemanager/apiv3/resourcemanagerpb;resourcemanagerpb";
option java_multiple_files = true;
option java_outer_classname = "TagValuesProto";
option java_package = "com.google.cloud.resourcemanager.v3";
option php_namespace = "Google\\Cloud\\ResourceManager\\V3";
option ruby_package = "Google::Cloud::ResourceManager::V3";

// Allow users to create and manage tag values.
service TagValues {
  option (google.api.default_host) = "cloudresourcemanager.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists all TagValues for a specific TagKey.
  rpc ListTagValues(ListTagValuesRequest) returns (ListTagValuesResponse) {
    option (google.api.http) = {
      get: "/v3/tagValues"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a TagValue. This method will return `PERMISSION_DENIED` if the
  // value does not exist or the user does not have permission to view it.
  rpc GetTagValue(GetTagValueRequest) returns (TagValue) {
    option (google.api.http) = {
      get: "/v3/{name=tagValues/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves a TagValue by its namespaced name.
  // This method will return `PERMISSION_DENIED` if the value does not exist
  // or the user does not have permission to view it.
  rpc GetNamespacedTagValue(GetNamespacedTagValueRequest) returns (TagValue) {
    option (google.api.http) = {
      get: "/v3/tagValues/namespaced"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a TagValue as a child of the specified TagKey. If a another
  // request with the same parameters is sent while the original request is in
  // process the second request will receive an error. A maximum of 1000
  // TagValues can exist under a TagKey at any given time.
  rpc CreateTagValue(CreateTagValueRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/tagValues"
      body: "tag_value"
    };
    option (google.api.method_signature) = "tag_value";
    option (google.longrunning.operation_info) = {
      response_type: "TagValue"
      metadata_type: "CreateTagValueMetadata"
    };
  }

  // Updates the attributes of the TagValue resource.
  rpc UpdateTagValue(UpdateTagValueRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v3/{tag_value.name=tagValues/*}"
      body: "tag_value"
    };
    option (google.api.method_signature) = "tag_value,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "TagValue"
      metadata_type: "UpdateTagValueMetadata"
    };
  }

  // Deletes a TagValue. The TagValue cannot have any bindings when it is
  // deleted.
  rpc DeleteTagValue(DeleteTagValueRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v3/{name=tagValues/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "TagValue"
      metadata_type: "DeleteTagValueMetadata"
    };
  }

  // Gets the access control policy for a TagValue. The returned policy may be
  // empty if no such policy or resource exists. The `resource` field should
  // be the TagValue's resource name. For example: `tagValues/1234`.
  // The caller must have the
  // `cloudresourcemanager.googleapis.com/tagValues.getIamPolicy` permission on
  // the identified TagValue to get the access control policy.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v3/{resource=tagValues/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Sets the access control policy on a TagValue, replacing any existing
  // policy. The `resource` field should be the TagValue's resource name.
  // For example: `tagValues/1234`.
  // The caller must have `resourcemanager.tagValues.setIamPolicy` permission
  // on the identified tagValue.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v3/{resource=tagValues/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Returns permissions that a caller has on the specified TagValue.
  // The `resource` field should be the TagValue's resource name. For example:
  // `tagValues/1234`.
  //
  // There are no permissions required for making this API call.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v3/{resource=tagValues/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }
}

// A TagValue is a child of a particular TagKey. This is used to group
// cloud resources for the purpose of controlling them using policies.
message TagValue {
  option (google.api.resource) = {
    type: "cloudresourcemanager.googleapis.com/TagValue"
    pattern: "tagValues/{tag_value}"
    style: DECLARATIVE_FRIENDLY
  };

  // Immutable. Resource name for TagValue in the format `tagValues/456`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The resource name of the new TagValue's parent TagKey.
  // Must be of the form `tagKeys/{tag_key_id}`.
  string parent = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Required. Immutable. User-assigned short name for TagValue. The short name
  // should be unique for TagValues within the same parent TagKey.
  //
  // The short name must be 63 characters or less, beginning and ending with
  // an alphanumeric character ([a-z0-9A-Z]) with dashes (-), underscores (_),
  // dots (.), and alphanumerics between.
  string short_name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. The namespaced name of the TagValue. Can be in the form
  // `{organization_id}/{tag_key_short_name}/{tag_value_short_name}` or
  // `{project_id}/{tag_key_short_name}/{tag_value_short_name}` or
  // `{project_number}/{tag_key_short_name}/{tag_value_short_name}`.
  string namespaced_name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User-assigned description of the TagValue.
  // Must not exceed 256 characters.
  //
  // Read-write.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Creation time.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Entity tag which users can pass to prevent race conditions. This
  // field is always set in server responses. See UpdateTagValueRequest for
  // details.
  string etag = 8 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for listing TagValues for the specified TagKey.
// Resource name for TagKey, parent of the TagValues to be listed,
// in the format `tagKeys/123`.
message ListTagValuesRequest {
  // Required.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "*" }
  ];

  // Optional. The maximum number of TagValues to return in the response. The
  // server allows a maximum of 300 TagValues to return. If unspecified, the
  // server will use 100 as the default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous call to
  // `ListTagValues` that indicates where this listing should continue from.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The ListTagValues response.
message ListTagValuesResponse {
  // A possibly paginated list of TagValues that are direct descendants of
  // the specified parent TagKey.
  repeated TagValue tag_values = 1;

  // A pagination token returned from a previous call to `ListTagValues`
  // that indicates from where listing should continue. This is currently not
  // used, but the server may at any point start supplying a valid token.
  string next_page_token = 2;
}

// The request message for getting a TagValue.
message GetTagValueRequest {
  // Required. Resource name for TagValue to be fetched in the format
  // `tagValues/456`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/TagValue"
    }
  ];
}

// The request message for getting a TagValue by its namespaced name.
message GetNamespacedTagValueRequest {
  // Required. A namespaced tag value name in the following format:
  //
  //   `{parentId}/{tagKeyShort}/{tagValueShort}`
  //
  // Examples:
  // - `42/foo/abc` for a value with short name "abc" under the key with short
  //   name "foo" under the organization with ID 42
  // - `r2-d2/bar/xyz` for a value with short name "xyz" under the key with
  //    short name "bar" under the project with ID "r2-d2"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/TagValue"
    }
  ];
}

// The request message for creating a TagValue.
message CreateTagValueRequest {
  // Required. The TagValue to be created. Only fields `short_name`,
  // `description`, and `parent` are considered during the creation request.
  TagValue tag_value = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Set as true to perform the validations necessary for creating the
  // resource, but not actually perform the action.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Runtime operation information for creating a TagValue.
message CreateTagValueMetadata {}

// The request message for updating a TagValue.
message UpdateTagValueRequest {
  // Required. The new definition of the TagValue. Only fields `description` and
  // `etag` fields can be updated by this request. If the `etag` field is
  // nonempty, it must match the `etag` field of the existing ControlGroup.
  // Otherwise, `ABORTED` will be returned.
  TagValue tag_value = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Fields to be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. True to perform validations necessary for updating the resource,
  // but not actually perform the action.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Runtime operation information for updating a TagValue.
message UpdateTagValueMetadata {}

// The request message for deleting a TagValue.
message DeleteTagValueRequest {
  // Required. Resource name for TagValue to be deleted in the format
  // tagValues/456.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/TagValue"
    }
  ];

  // Optional. Set as true to perform the validations necessary for deletion,
  // but not actually perform the action.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The etag known to the client for the expected state of the
  // TagValue. This is to be used for optimistic concurrency.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Runtime operation information for deleting a TagValue.
message DeleteTagValueMetadata {}
