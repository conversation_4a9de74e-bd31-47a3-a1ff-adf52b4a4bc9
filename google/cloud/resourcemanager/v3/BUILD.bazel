# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "resourcemanager_proto",
    srcs = [
        "folders.proto",
        "organizations.proto",
        "projects.proto",
        "tag_bindings.proto",
        "tag_holds.proto",
        "tag_keys.proto",
        "tag_values.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "resourcemanager_proto_with_info",
    deps = [
        ":resourcemanager_proto",
        # This line was manually modified since Projects, Folders, Organizations
        # are special cased since they are also common resources.
        #"//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "resourcemanager_java_proto",
    deps = [":resourcemanager_proto"],
)

java_grpc_library(
    name = "resourcemanager_java_grpc",
    srcs = [":resourcemanager_proto"],
    deps = [":resourcemanager_java_proto"],
)

java_gapic_library(
    name = "resourcemanager_java_gapic",
    srcs = [":resourcemanager_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloudresourcemanager_v3_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudresourcemanager_v3.yaml",
    test_deps = [
        ":resourcemanager_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":resourcemanager_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "resourcemanager_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.resourcemanager.v3.FoldersClientHttpJsonTest",
        "com.google.cloud.resourcemanager.v3.FoldersClientTest",
        "com.google.cloud.resourcemanager.v3.OrganizationsClientHttpJsonTest",
        "com.google.cloud.resourcemanager.v3.OrganizationsClientTest",
        "com.google.cloud.resourcemanager.v3.ProjectsClientHttpJsonTest",
        "com.google.cloud.resourcemanager.v3.ProjectsClientTest",
        "com.google.cloud.resourcemanager.v3.TagBindingsClientHttpJsonTest",
        "com.google.cloud.resourcemanager.v3.TagBindingsClientTest",
        "com.google.cloud.resourcemanager.v3.TagHoldsClientHttpJsonTest",
        "com.google.cloud.resourcemanager.v3.TagHoldsClientTest",
        "com.google.cloud.resourcemanager.v3.TagKeysClientHttpJsonTest",
        "com.google.cloud.resourcemanager.v3.TagKeysClientTest",
        "com.google.cloud.resourcemanager.v3.TagValuesClientHttpJsonTest",
        "com.google.cloud.resourcemanager.v3.TagValuesClientTest",
    ],
    runtime_deps = [":resourcemanager_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-resourcemanager-v3-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":resourcemanager_java_gapic",
        ":resourcemanager_java_grpc",
        ":resourcemanager_java_proto",
        ":resourcemanager_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "resourcemanager_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/resourcemanager/apiv3/resourcemanagerpb",
    protos = [":resourcemanager_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "resourcemanager_go_gapic",
    srcs = [":resourcemanager_proto_with_info"],
    grpc_service_config = "cloudresourcemanager_v3_grpc_service_config.json",
    importpath = "cloud.google.com/go/resourcemanager/apiv3;resourcemanager",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudresourcemanager_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":resourcemanager_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-resourcemanager-v3-go",
    deps = [
        ":resourcemanager_go_gapic",
        ":resourcemanager_go_gapic_srcjar-metadata.srcjar",
        ":resourcemanager_go_gapic_srcjar-snippets.srcjar",
        ":resourcemanager_go_gapic_srcjar-test.srcjar",
        ":resourcemanager_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "resourcemanager_py_gapic",
    srcs = [":resourcemanager_proto"],
    grpc_service_config = "cloudresourcemanager_v3_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-resource-manager"],
    rest_numeric_enums = True,
    service_yaml = "cloudresourcemanager_v3.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "resourcemanager_py_gapic_test",
    srcs = [
        "resourcemanager_py_gapic_pytest.py",
        "resourcemanager_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":resourcemanager_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "resourcemanager-v3-py",
    deps = [
        ":resourcemanager_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "resourcemanager_php_proto",
    deps = [":resourcemanager_proto"],
)

php_gapic_library(
    name = "resourcemanager_php_gapic",
    srcs = [":resourcemanager_proto_with_info"],
    grpc_service_config = "cloudresourcemanager_v3_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudresourcemanager_v3.yaml",
    transport = "grpc+rest",
    deps = [":resourcemanager_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-resourcemanager-v3-php",
    deps = [
        ":resourcemanager_php_gapic",
        ":resourcemanager_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "resourcemanager_nodejs_gapic",
    package_name = "@google-cloud/resource-manager",
    src = ":resourcemanager_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudresourcemanager_v3_grpc_service_config.json",
    package = "google.cloud.resourcemanager.v3",
    rest_numeric_enums = True,
    service_yaml = "cloudresourcemanager_v3.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "resourcemanager-v3-nodejs",
    deps = [
        ":resourcemanager_nodejs_gapic",
        ":resourcemanager_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "resourcemanager_ruby_proto",
    deps = [":resourcemanager_proto"],
)

ruby_grpc_library(
    name = "resourcemanager_ruby_grpc",
    srcs = [":resourcemanager_proto"],
    deps = [":resourcemanager_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "resourcemanager_ruby_gapic",
    srcs = [":resourcemanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=cloudresourcemanager.googleapis.com",
        "ruby-cloud-api-shortname=cloudresourcemanager",
        "ruby-cloud-env-prefix=RESOURCE_MANAGER",
        "ruby-cloud-gem-name=google-cloud-resource_manager-v3",
        "ruby-cloud-product-url=https://cloud.google.com/resource-manager/",
    ],
    grpc_service_config = "cloudresourcemanager_v3_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Google Cloud provides container resources such as organizations and projects that allow you to group and hierarchically organize other Google Cloud resources. This hierarchical organization helps you manage common aspects of your resources, such as access control and configuration settings. The Resource Manager API enables you to programmatically manage these container resources.",
    ruby_cloud_title = "Resource Manager V3",
    service_yaml = "cloudresourcemanager_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":resourcemanager_ruby_grpc",
        ":resourcemanager_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-resourcemanager-v3-ruby",
    deps = [
        ":resourcemanager_ruby_gapic",
        ":resourcemanager_ruby_grpc",
        ":resourcemanager_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "resourcemanager_csharp_proto",
    deps = [":resourcemanager_proto"],
)

csharp_grpc_library(
    name = "resourcemanager_csharp_grpc",
    srcs = [":resourcemanager_proto"],
    deps = [":resourcemanager_csharp_proto"],
)

csharp_gapic_library(
    name = "resourcemanager_csharp_gapic",
    srcs = [":resourcemanager_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudresourcemanager_v3_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudresourcemanager_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":resourcemanager_csharp_grpc",
        ":resourcemanager_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-resourcemanager-v3-csharp",
    deps = [
        ":resourcemanager_csharp_gapic",
        ":resourcemanager_csharp_grpc",
        ":resourcemanager_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "resourcemanager_cc_proto",
    deps = [":resourcemanager_proto"],
)

cc_grpc_library(
    name = "resourcemanager_cc_grpc",
    srcs = [":resourcemanager_proto"],
    grpc_only = True,
    deps = [":resourcemanager_cc_proto"],
)
