// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.resourcemanager.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ResourceManager.V3";
option go_package = "cloud.google.com/go/resourcemanager/apiv3/resourcemanagerpb;resourcemanagerpb";
option java_multiple_files = true;
option java_outer_classname = "OrganizationsProto";
option java_package = "com.google.cloud.resourcemanager.v3";
option php_namespace = "Google\\Cloud\\ResourceManager\\V3";
option ruby_package = "Google::Cloud::ResourceManager::V3";

// Allows users to manage their organization resources.
service Organizations {
  option (google.api.default_host) = "cloudresourcemanager.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Fetches an organization resource identified by the specified resource name.
  rpc GetOrganization(GetOrganizationRequest) returns (Organization) {
    option (google.api.http) = {
      get: "/v3/{name=organizations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Searches organization resources that are visible to the user and satisfy
  // the specified filter. This method returns organizations in an unspecified
  // order. New organizations do not necessarily appear at the end of the
  // results, and may take a small amount of time to appear.
  //
  // Search will only return organizations on which the user has the permission
  // `resourcemanager.organizations.get`
  rpc SearchOrganizations(SearchOrganizationsRequest)
      returns (SearchOrganizationsResponse) {
    option (google.api.http) = {
      get: "/v3/organizations:search"
    };
    option (google.api.method_signature) = "query";
  }

  // Gets the access control policy for an organization resource. The policy may
  // be empty if no such policy or resource exists. The `resource` field should
  // be the organization's resource name, for example: "organizations/123".
  //
  // Authorization requires the IAM permission
  // `resourcemanager.organizations.getIamPolicy` on the specified organization.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v3/{resource=organizations/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Sets the access control policy on an organization resource. Replaces any
  // existing policy. The `resource` field should be the organization's resource
  // name, for example: "organizations/123".
  //
  // Authorization requires the IAM permission
  // `resourcemanager.organizations.setIamPolicy` on the specified organization.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v3/{resource=organizations/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Returns the permissions that a caller has on the specified organization.
  // The `resource` field should be the organization's resource name,
  // for example: "organizations/123".
  //
  // There are no permissions required for making this API call.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v3/{resource=organizations/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }
}

// The root node in the resource hierarchy to which a particular entity's
// (a company, for example) resources belong.
message Organization {
  option (google.api.resource) = {
    type: "cloudresourcemanager.googleapis.com/Organization"
    pattern: "organizations/{organization}"
    style: DECLARATIVE_FRIENDLY
  };

  // Organization lifecycle states.
  enum State {
    // Unspecified state.  This is only useful for distinguishing unset values.
    STATE_UNSPECIFIED = 0;

    // The normal and active state.
    ACTIVE = 1;

    // The organization has been marked for deletion by the user.
    DELETE_REQUESTED = 2;
  }

  // Output only. The resource name of the organization. This is the
  // organization's relative path in the API. Its format is
  // "organizations/[organization_id]". For example, "organizations/1234".
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A human-readable string that refers to the organization in the
  // Google Cloud Console. This string is set by the server and cannot be
  // changed. The string will be set to the primary domain (for example,
  // "google.com") of the Google Workspace customer that owns the organization.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The owner of this organization. The owner should be specified on
  // creation. Once set, it cannot be changed.
  //
  // The lifetime of the organization and all of its descendants are bound to
  // the owner. If the owner is deleted, the organization and all its
  // descendants will be deleted.
  oneof owner {
    // Immutable. The G Suite / Workspace customer id used in the Directory API.
    string directory_customer_id = 3 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Output only. The organization's current lifecycle state.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the Organization was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the Organization was last modified.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the Organization was requested for deletion.
  google.protobuf.Timestamp delete_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A checksum computed by the server based on the current value
  // of the Organization resource. This may be sent on update and delete
  // requests to ensure the client has an up-to-date value before proceeding.
  string etag = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request sent to the `GetOrganization` method. The `name` field is
// required. `organization_id` is no longer accepted.
message GetOrganizationRequest {
  // Required. The resource name of the Organization to fetch. This is the
  // organization's relative path in the API, formatted as
  // "organizations/[organizationId]". For example, "organizations/1234".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];
}

// The request sent to the `SearchOrganizations` method.
message SearchOrganizationsRequest {
  // Optional. The maximum number of organizations to return in the response.
  // The server can return fewer organizations than requested. If unspecified,
  // server picks an appropriate default.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous call to
  // `SearchOrganizations` that indicates from where listing should continue.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional query string used to filter the Organizations to
  // return in the response. Query rules are case-insensitive.
  //
  //
  // ```
  // | Field            | Description                                |
  // |------------------|--------------------------------------------|
  // | directoryCustomerId, owner.directoryCustomerId | Filters by directory
  // customer id. |
  // | domain           | Filters by domain.                         |
  // ```
  //
  // Organizations may be queried by `directoryCustomerId` or by
  // `domain`, where the domain is a G Suite domain, for example:
  //
  // * Query `directorycustomerid:123456789` returns Organization
  // resources with `owner.directory_customer_id` equal to `123456789`.
  // * Query `domain:google.com` returns Organization resources corresponding
  // to the domain `google.com`.
  string query = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response returned from the `SearchOrganizations` method.
message SearchOrganizationsResponse {
  // The list of Organizations that matched the search query, possibly
  // paginated.
  repeated Organization organizations = 1;

  // A pagination token to be used to retrieve the next page of results. If the
  // result is too large to fit within the page size specified in the request,
  // this field will be set with a token that can be used to fetch the next page
  // of results. If this field is empty, it indicates that this response
  // contains the last page of results.
  string next_page_token = 2;
}

// A status object which is used as the `metadata` field for the operation
// returned by DeleteOrganization.
message DeleteOrganizationMetadata {}

// A status object which is used as the `metadata` field for the Operation
// returned by UndeleteOrganization.
message UndeleteOrganizationMetadata {}
