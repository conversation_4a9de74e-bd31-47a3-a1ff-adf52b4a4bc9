# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "resourcemanager_proto",
    srcs = [
        "folders.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "resourcemanager_proto_with_info",
    deps = [
        ":resourcemanager_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "resourcemanager_java_proto",
    deps = [":resourcemanager_proto"],
)

java_grpc_library(
    name = "resourcemanager_java_grpc",
    srcs = [":resourcemanager_proto"],
    deps = [":resourcemanager_java_proto"],
)

#############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "resourcemanager_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/resourcemanager/apiv2/resourcemanagerpb",
    protos = [":resourcemanager_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "resourcemanager_go_gapic",
    srcs = [":resourcemanager_proto_with_info"],
    importpath = "cloud.google.com/go/resourcemanager/apiv2;resourcemanager",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudresourcemanager_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":resourcemanager_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-resourcemanager-v2-go",
    deps = [
        ":resourcemanager_go_gapic",
        ":resourcemanager_go_gapic_srcjar-metadata.srcjar",
        ":resourcemanager_go_gapic_srcjar-snippets.srcjar",
        ":resourcemanager_go_gapic_srcjar-test.srcjar",
        ":resourcemanager_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "resourcemanager_moved_proto",
    srcs = [":resourcemanager_proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "resourcemanager_py_proto",
    deps = [":resourcemanager_moved_proto"],
)

py_grpc_library(
    name = "resourcemanager_py_grpc",
    srcs = [":resourcemanager_moved_proto"],
    deps = [":resourcemanager_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "resourcemanager_php_proto",
    deps = [":resourcemanager_proto"],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "resourcemanager_ruby_proto",
    deps = [":resourcemanager_proto"],
)

ruby_grpc_library(
    name = "resourcemanager_ruby_grpc",
    srcs = [":resourcemanager_proto"],
    deps = [":resourcemanager_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "resourcemanager_csharp_proto",
    deps = [":resourcemanager_proto"],
)

csharp_grpc_library(
    name = "resourcemanager_csharp_grpc",
    srcs = [":resourcemanager_proto"],
    deps = [":resourcemanager_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "resourcemanager_cc_proto",
    deps = [":resourcemanager_proto"],
)

cc_grpc_library(
    name = "resourcemanager_cc_grpc",
    srcs = [":resourcemanager_proto"],
    grpc_only = True,
    deps = [":resourcemanager_cc_proto"],
)
