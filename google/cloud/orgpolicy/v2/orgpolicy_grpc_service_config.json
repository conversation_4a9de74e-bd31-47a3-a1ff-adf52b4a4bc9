{"methodConfig": [{"name": [{"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "ListPolicies"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "GetEffectivePolicy"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "GetPolicy"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "ListConstraints"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "CreatePolicy"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "UpdatePolicy"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "DeletePolicy"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "GetCustomConstraint"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "CreateCustomConstraint"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "UpdateCustomConstraint"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "ListCustomConstraints"}, {"service": "google.cloud.orgpolicy.v2.OrgPolicy", "method": "DeleteCustomConstraint"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}