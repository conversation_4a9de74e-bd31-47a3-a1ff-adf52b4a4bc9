type: google.api.Service
config_version: 3
name: orgpolicy.googleapis.com
title: Organization Policy API

apis:
- name: google.cloud.orgpolicy.v2.OrgPolicy

documentation:
  summary: |-
    The Organization Policy API allows users to configure governance rules on
    their Google Cloud resources across the resource hierarchy.

authentication:
  rules:
  - selector: 'google.cloud.orgpolicy.v2.OrgPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
