# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "orgpolicy_proto",
    srcs = [
        "constraint.proto",
        "orgpolicy.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "orgpolicy_proto_with_info",
    deps = [
        ":orgpolicy_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "orgpolicy_java_proto",
    deps = [":orgpolicy_proto"],
)

java_grpc_library(
    name = "orgpolicy_java_grpc",
    srcs = [":orgpolicy_proto"],
    deps = [":orgpolicy_java_proto"],
)

java_gapic_library(
    name = "orgpolicy_java_gapic",
    srcs = [":orgpolicy_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "orgpolicy_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "orgpolicy_v2.yaml",
    test_deps = [
        ":orgpolicy_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":orgpolicy_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "orgpolicy_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.orgpolicy.v2.OrgPolicyClientHttpJsonTest",
        "com.google.cloud.orgpolicy.v2.OrgPolicyClientTest",
    ],
    runtime_deps = [":orgpolicy_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-orgpolicy-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":orgpolicy_java_gapic",
        ":orgpolicy_java_grpc",
        ":orgpolicy_java_proto",
        ":orgpolicy_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "orgpolicy_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/orgpolicy/apiv2/orgpolicypb",
    protos = [":orgpolicy_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "orgpolicy_go_gapic",
    srcs = [":orgpolicy_proto_with_info"],
    grpc_service_config = "orgpolicy_grpc_service_config.json",
    importpath = "cloud.google.com/go/orgpolicy/apiv2;orgpolicy",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "orgpolicy_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":orgpolicy_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-orgpolicy-v2-go",
    deps = [
        ":orgpolicy_go_gapic",
        ":orgpolicy_go_gapic_srcjar-metadata.srcjar",
        ":orgpolicy_go_gapic_srcjar-snippets.srcjar",
        ":orgpolicy_go_gapic_srcjar-test.srcjar",
        ":orgpolicy_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "orgpolicy_py_gapic",
    srcs = [":orgpolicy_proto"],
    grpc_service_config = "orgpolicy_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-org-policy"],
    rest_numeric_enums = True,
    service_yaml = "orgpolicy_v2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "orgpolicy_py_gapic_test",
    srcs = [
        "orgpolicy_py_gapic_pytest.py",
        "orgpolicy_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":orgpolicy_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "orgpolicy-v2-py",
    deps = [
        ":orgpolicy_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "orgpolicy_php_proto",
    deps = [":orgpolicy_proto"],
)

php_gapic_library(
    name = "orgpolicy_php_gapic",
    srcs = [":orgpolicy_proto_with_info"],
    grpc_service_config = "orgpolicy_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "orgpolicy_v2.yaml",
    transport = "grpc+rest",
    deps = [":orgpolicy_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-orgpolicy-v2-php",
    deps = [
        ":orgpolicy_php_gapic",
        ":orgpolicy_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "orgpolicy_nodejs_gapic",
    package_name = "@google-cloud/org-policy",
    src = ":orgpolicy_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "orgpolicy_grpc_service_config.json",
    package = "google.cloud.orgpolicy.v2",
    rest_numeric_enums = True,
    service_yaml = "orgpolicy_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "orgpolicy-v2-nodejs",
    deps = [
        ":orgpolicy_nodejs_gapic",
        ":orgpolicy_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "orgpolicy_ruby_proto",
    deps = [":orgpolicy_proto"],
)

ruby_grpc_library(
    name = "orgpolicy_ruby_grpc",
    srcs = [":orgpolicy_proto"],
    deps = [":orgpolicy_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "orgpolicy_ruby_gapic",
    srcs = [":orgpolicy_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=orgpolicy.googleapis.com",
        "ruby-cloud-api-shortname=orgpolicy",
        "ruby-cloud-env-prefix=ORG_POLICY",
        "ruby-cloud-gem-name=google-cloud-org_policy-v2",
        "ruby-cloud-product-url=https://cloud.google.com/resource-manager/docs/organization-policy/overview",
    ],
    grpc_service_config = "orgpolicy_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Cloud Org Policy service provides a simple mechanism for organizations to restrict the allowed configurations across their entire Cloud Resource hierarchy.",
    ruby_cloud_title = "Organization Policy V2",
    service_yaml = "orgpolicy_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":orgpolicy_ruby_grpc",
        ":orgpolicy_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-orgpolicy-v2-ruby",
    deps = [
        ":orgpolicy_ruby_gapic",
        ":orgpolicy_ruby_grpc",
        ":orgpolicy_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "orgpolicy_csharp_proto",
    deps = [":orgpolicy_proto"],
)

csharp_grpc_library(
    name = "orgpolicy_csharp_grpc",
    srcs = [":orgpolicy_proto"],
    deps = [":orgpolicy_csharp_proto"],
)

csharp_gapic_library(
    name = "orgpolicy_csharp_gapic",
    srcs = [":orgpolicy_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "orgpolicy_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "orgpolicy_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":orgpolicy_csharp_grpc",
        ":orgpolicy_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-orgpolicy-v2-csharp",
    deps = [
        ":orgpolicy_csharp_gapic",
        ":orgpolicy_csharp_grpc",
        ":orgpolicy_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "orgpolicy_cc_proto",
    deps = [":orgpolicy_proto"],
)

cc_grpc_library(
    name = "orgpolicy_cc_grpc",
    srcs = [":orgpolicy_proto"],
    grpc_only = True,
    deps = [":orgpolicy_cc_proto"],
)
