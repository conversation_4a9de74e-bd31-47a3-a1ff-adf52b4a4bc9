// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.orgpolicy.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.OrgPolicy.V2";
option go_package = "cloud.google.com/go/orgpolicy/apiv2/orgpolicypb;orgpolicypb";
option java_multiple_files = true;
option java_outer_classname = "ConstraintProto";
option java_package = "com.google.cloud.orgpolicy.v2";
option php_namespace = "Google\\Cloud\\OrgPolicy\\V2";
option ruby_package = "Google::Cloud::OrgPolicy::V2";

// A constraint describes a way to restrict resource's configuration. For
// example, you could enforce a constraint that controls which Google Cloud
// services can be activated across an organization, or whether a Compute Engine
// instance can have serial port connections established. Constraints can be
// configured by the organization policy administrator to fit the needs of the
// organization by setting a policy that includes constraints at different
// locations in the organization's resource hierarchy. Policies are inherited
// down the resource hierarchy from higher levels, but can also be overridden.
// For details about the inheritance rules please read about
// [`policies`][google.cloud.OrgPolicy.v2.Policy].
//
// Constraints have a default behavior determined by the `constraint_default`
// field, which is the enforcement behavior that is used in the absence of a
// policy being defined or inherited for the resource in question.
message Constraint {
  option (google.api.resource) = {
    type: "orgpolicy.googleapis.com/Constraint"
    pattern: "projects/{project}/constraints/{constraint}"
    pattern: "folders/{folder}/constraints/{constraint}"
    pattern: "organizations/{organization}/constraints/{constraint}"
  };

  // Specifies the default behavior in the absence of any policy for the
  // constraint. This must not be `CONSTRAINT_DEFAULT_UNSPECIFIED`.
  //
  // Immutable after creation.
  enum ConstraintDefault {
    // This is only used for distinguishing unset values and should never be
    // used.
    CONSTRAINT_DEFAULT_UNSPECIFIED = 0;

    // Indicate that all values are allowed for list constraints.
    // Indicate that enforcement is off for boolean constraints.
    ALLOW = 1;

    // Indicate that all values are denied for list constraints.
    // Indicate that enforcement is on for boolean constraints.
    DENY = 2;
  }

  // A constraint that allows or disallows a list of string values, which are
  // configured by an Organization Policy administrator with a policy.
  message ListConstraint {
    // Indicates whether values grouped into categories can be used in
    // `Policy.allowed_values` and `Policy.denied_values`. For example,
    // `"in:Python"` would match any value in the 'Python' group.
    bool supports_in = 1;

    // Indicates whether subtrees of the Resource Manager resource hierarchy
    // can be used in `Policy.allowed_values` and `Policy.denied_values`. For
    // example, `"under:folders/123"` would match any resource under the
    // 'folders/123' folder.
    bool supports_under = 2;
  }

  // A constraint that is either enforced or not.
  //
  // For example, a constraint `constraints/compute.disableSerialPortAccess`.
  // If it is enforced on a VM instance, serial port connections will not be
  // opened to that instance.
  message BooleanConstraint {}

  // Immutable. The resource name of the constraint. Must be in one of
  // the following forms:
  //
  // * `projects/{project_number}/constraints/{constraint_name}`
  // * `folders/{folder_id}/constraints/{constraint_name}`
  // * `organizations/{organization_id}/constraints/{constraint_name}`
  //
  // For example, "/projects/123/constraints/compute.disableSerialPortAccess".
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // The human readable name.
  //
  // Mutable.
  string display_name = 2;

  // Detailed description of what this constraint controls as well as how and
  // where it is enforced.
  //
  // Mutable.
  string description = 3;

  // The evaluation behavior of this constraint in the absence of a policy.
  ConstraintDefault constraint_default = 4;

  // The type of restrictions for this `Constraint`.
  //
  // Immutable after creation.
  oneof constraint_type {
    // Defines this constraint as being a ListConstraint.
    ListConstraint list_constraint = 5;

    // Defines this constraint as being a BooleanConstraint.
    BooleanConstraint boolean_constraint = 6;
  }

  // Shows if dry run is supported for this constraint or not.
  bool supports_dry_run = 7;
}

// A custom constraint defined by customers which can *only* be applied to the
// given resource types and organization.
//
// By creating a custom constraint, customers can apply policies of this
// custom constraint. *Creating a custom constraint itself does NOT apply any
// policy enforcement*.
message CustomConstraint {
  option (google.api.resource) = {
    type: "orgpolicy.googleapis.com/CustomConstraint"
    pattern: "organizations/{organization}/customConstraints/{custom_constraint}"
  };

  // The operation for which this constraint will be applied. To apply this
  // constraint only when creating new VMs, the `method_types` should be
  // `CREATE` only. To apply this constraint when creating or deleting
  // VMs, the `method_types` should be `CREATE` and `DELETE`.
  //
  // `UPDATE` only custom constraints are not supported. Use `CREATE` or
  // `CREATE, UPDATE`.
  enum MethodType {
    // Unspecified. Results in an error.
    METHOD_TYPE_UNSPECIFIED = 0;

    // Constraint applied when creating the resource.
    CREATE = 1;

    // Constraint applied when updating the resource.
    UPDATE = 2;

    // Constraint applied when deleting the resource.
    // Not supported yet.
    DELETE = 3;

    // Constraint applied when removing an IAM grant.
    REMOVE_GRANT = 4;

    // Constraint applied when enforcing forced tagging.
    GOVERN_TAGS = 5;
  }

  // Allow or deny type.
  enum ActionType {
    // Unspecified. Results in an error.
    ACTION_TYPE_UNSPECIFIED = 0;

    // Allowed action type.
    ALLOW = 1;

    // Deny action type.
    DENY = 2;
  }

  // Immutable. Name of the constraint. This is unique within the organization.
  // Format of the name should be
  //
  // * `organizations/{organization_id}/customConstraints/{custom_constraint_id}`
  //
  // Example: `organizations/123/customConstraints/custom.createOnlyE2TypeVms`
  //
  // The max length is 70 characters and the minimum length is 1. Note that the
  // prefix `organizations/{organization_id}/customConstraints/` is not counted.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The resource instance type on which this policy applies. Format
  // will be of the form : `<canonical service name>/<type>` Example:
  //
  //  * `compute.googleapis.com/Instance`.
  repeated string resource_types = 2 [(google.api.field_behavior) = IMMUTABLE];

  // All the operations being applied for this constraint.
  repeated MethodType method_types = 3;

  // Org policy condition/expression. For example:
  // `resource.instanceName.matches("[production|test]_.*_(\d)+")` or,
  // `resource.management.auto_upgrade == true`
  //
  // The max length of the condition is 1000 characters.
  string condition = 4;

  // Allow or deny type.
  ActionType action_type = 5;

  // One line display name for the UI.
  // The max length of the display_name is 200 characters.
  string display_name = 6;

  // Detailed information about this custom policy constraint.
  // The max length of the description is 2000 characters.
  string description = 7;

  // Output only. The last time this custom constraint was updated. This
  // represents the last time that the `CreateCustomConstraint` or
  // `UpdateCustomConstraint` RPC was called
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
