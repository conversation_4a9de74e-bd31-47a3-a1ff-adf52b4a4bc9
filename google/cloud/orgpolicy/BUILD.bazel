# This build file includes a target for the Ruby wrapper library for
# google-cloud-org_policy.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for orgpolicy.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "orgpolicy_ruby_wrapper",
    srcs = ["//google/cloud/orgpolicy/v2:orgpolicy_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-org_policy",
        "ruby-cloud-env-prefix=ORG_POLICY",
        "ruby-cloud-wrapper-of=v2:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/resource-manager/docs/organization-policy/overview",
        "ruby-cloud-api-id=orgpolicy.googleapis.com",
        "ruby-cloud-api-shortname=orgpolicy",
    ],
    ruby_cloud_description = "The Cloud Org Policy service provides a simple mechanism for organizations to restrict the allowed configurations across their entire Cloud Resource hierarchy.",
    ruby_cloud_title = "Organization Policy",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-orgpolicy-ruby",
    deps = [
        ":orgpolicy_ruby_wrapper",
    ],
)
