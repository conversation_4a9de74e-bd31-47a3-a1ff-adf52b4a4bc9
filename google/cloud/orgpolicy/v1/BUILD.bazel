# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "orgpolicy_proto",
    srcs = [
        "orgpolicy.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "orgpolicy_java_proto",
    deps = [":orgpolicy_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-orgpolicy-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":orgpolicy_java_proto",
        ":orgpolicy_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_proto_library",
)

go_proto_library(
    name = "orgpolicy_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/orgpolicy/apiv1/orgpolicypb",
    protos = [":orgpolicy_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-orgpolicy-v1-go",
    deps = [
        ":orgpolicy_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_proto_library",
)

py_proto_library(
    name = "orgpolicy_py_proto",
    deps = [":orgpolicy_proto"],
)

# Open Source Packages
# DO NOT REMOVE, this is needed to generate a Python package
# with the orgpolicy protos.
py_gapic_assembly_pkg(
    name = "orgpolicy-v1-py",
    deps = [
        ":orgpolicy_py_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "orgpolicy_php_proto",
    deps = [":orgpolicy_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "orgpolicy_ruby_proto",
    deps = [":orgpolicy_proto"],
)

ruby_grpc_library(
    name = "orgpolicy_ruby_grpc",
    srcs = [":orgpolicy_proto"],
    deps = [":orgpolicy_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "orgpolicy_csharp_proto",
    deps = [":orgpolicy_proto"],
)

csharp_grpc_library(
    name = "orgpolicy_csharp_grpc",
    srcs = [":orgpolicy_proto"],
    deps = [":orgpolicy_csharp_proto"],
)

csharp_gapic_assembly_pkg(
    name = "google-cloud-orgpolicy-v1-csharp",
    package_name = "Google.Cloud.OrgPolicy.V1",
    generate_nongapic_package = True,
    deps = [
        ":orgpolicy_csharp_grpc",
        ":orgpolicy_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "orgpolicy_cc_proto",
    deps = [":orgpolicy_proto"],
)

cc_grpc_library(
    name = "orgpolicy_cc_grpc",
    srcs = [":orgpolicy_proto"],
    grpc_only = True,
    deps = [":orgpolicy_cc_proto"],
)
