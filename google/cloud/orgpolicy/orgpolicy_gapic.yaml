type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.orgpolicy.v2
  python:
    package_name: google.cloud.orgpolicy_v2.gapic
  go:
    package_name: cloud.google.com/go/orgpolicy/apiv2
  csharp:
    package_name: Google.Cloud.OrgPolicy.V2
  ruby:
    package_name: Google::Cloud::OrgPolicy::V2
  php:
    package_name: Google\Cloud\OrgPolicy\V2
  nodejs:
    package_name: orgpolicy.v2
    domain_layer_location: google-cloud
