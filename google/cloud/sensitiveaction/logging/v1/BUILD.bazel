# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_proto_library",
    "java_grpc_library",
    "java_proto_library",
    "moved_proto_library",
    "php_proto_library",
    "py_grpc_library",
    "py_proto_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "logging_proto",
    srcs = [
        "sensitive_action_payload.proto",
    ],
    deps = [
        "//google/cloud/securitycenter/v1:securitycenter_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

java_proto_library(
    name = "logging_java_proto",
    deps = [":logging_proto"],
)

java_grpc_library(
    name = "logging_java_grpc",
    srcs = [":logging_proto"],
    deps = [":logging_java_proto"],
)

go_proto_library(
    name = "logging_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/sensitiveaction/logging/apiv1/loggingpb",
    protos = [":logging_proto"],
    deps = [
        "//google/cloud/securitycenter/v1:securitycenter_go_proto",
    ],
)

moved_proto_library(
    name = "logging_moved_proto",
    srcs = [":logging_proto"],
    deps = [
        "//google/cloud/securitycenter/v1:securitycenter_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "logging_py_proto",
    deps = [":logging_moved_proto"],
)

py_grpc_library(
    name = "logging_py_grpc",
    srcs = [":logging_moved_proto"],
    deps = [":logging_py_proto"],
)

php_proto_library(
    name = "logging_php_proto",
    deps = [":logging_proto"],
)

ruby_proto_library(
    name = "logging_ruby_proto",
    deps = [":logging_proto"],
)

ruby_grpc_library(
    name = "logging_ruby_grpc",
    srcs = [":logging_proto"],
    deps = [":logging_ruby_proto"],
)

csharp_proto_library(
    name = "logging_csharp_proto",
    deps = [":logging_proto"],
)

csharp_grpc_library(
    name = "logging_csharp_grpc",
    srcs = [":logging_proto"],
    deps = [":logging_csharp_proto"],
)

cc_proto_library(
    name = "logging_cc_proto",
    deps = [":logging_proto"],
)

cc_grpc_library(
    name = "logging_cc_grpc",
    srcs = [":logging_proto"],
    grpc_only = True,
    deps = [":logging_cc_proto"],
)
