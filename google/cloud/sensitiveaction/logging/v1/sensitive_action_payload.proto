// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sensitiveaction.logging.v1;

import "google/cloud/securitycenter/v1/access.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SensitiveAction.Logging.V1";
option go_package = "cloud.google.com/go/sensitiveaction/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "SensitiveActionPayloadProto";
option java_package = "com.google.cloud.sensitiveaction.logging.v1";
option php_namespace = "Google\\Cloud\\SensitiveAction\\Logging\\V1";
option ruby_package = "Google::Cloud::SensitiveAction::Logging::V1";

// Payload proto for sensitiveaction.googleapis.com/action
// Platform Log event that describes a sensitive action
message SensitiveAction {
  // Used to reference a specific Cloud Logging LogEntry.
  message SourceLogId {
    // The GCP resource (organization, folder, or project) that the LogEntry
    // came from.
    string resource_container = 1;

    // The timestamp of the LogEntry.
    google.protobuf.Timestamp log_time = 2;

    // The insert_id of the LogEntry.
    string insert_id = 3;

    // A link to the Cloud Logging dashboard with a query for the LogEntry.
    string query_uri = 4;
  }

  // The type of action (eg. "change_organization_policy").
  string action_type = 1;

  // The time this action was detected.
  google.protobuf.Timestamp action_time = 2;

  // GCP resources that are involved in the action.
  repeated string affected_resources = 3;

  // Cloud Logging entries that were used to identify the action.
  repeated SourceLogId source_log_ids = 4;

  // Link to documentation where you can learn more about sensitive actions.
  string learn_more_uri = 5;

  // Access details associated to the sensitive action.
  google.cloud.securitycenter.v1.Access access = 6;
}
