// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycentermanagement.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/expr.proto";

option csharp_namespace = "Google.Cloud.SecurityCenterManagement.V1";
option go_package = "cloud.google.com/go/securitycentermanagement/apiv1/securitycentermanagementpb;securitycentermanagementpb";
option java_multiple_files = true;
option java_outer_classname = "SecurityCenterManagementProto";
option java_package = "com.google.cloud.securitycentermanagement.v1";
option php_namespace = "Google\\Cloud\\SecurityCenterManagement\\V1";
option ruby_package = "Google::Cloud::SecurityCenterManagement::V1";
option (google.api.resource_definition) = {
  type: "securitycentermanagement.googleapis.com/OrganizationLocation"
  pattern: "organizations/{organization}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "securitycentermanagement.googleapis.com/FolderLocation"
  pattern: "folders/{folder}/locations/{location}"
};

// Service describing handlers for resources
service SecurityCenterManagement {
  option (google.api.default_host) = "securitycentermanagement.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns a list of all
  // [EffectiveSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.EffectiveSecurityHealthAnalyticsCustomModule]
  // resources for the given parent. This includes resident modules defined at
  // the scope of the parent, and inherited modules, inherited from ancestor
  // organizations, folders, and projects (no descendants).
  rpc ListEffectiveSecurityHealthAnalyticsCustomModules(
      ListEffectiveSecurityHealthAnalyticsCustomModulesRequest)
      returns (ListEffectiveSecurityHealthAnalyticsCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/effectiveSecurityHealthAnalyticsCustomModules"
      additional_bindings {
        get: "/v1/{parent=folders/*/locations/*}/effectiveSecurityHealthAnalyticsCustomModules"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*/locations/*}/effectiveSecurityHealthAnalyticsCustomModules"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single
  // [EffectiveSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.EffectiveSecurityHealthAnalyticsCustomModule].
  rpc GetEffectiveSecurityHealthAnalyticsCustomModule(
      GetEffectiveSecurityHealthAnalyticsCustomModuleRequest)
      returns (EffectiveSecurityHealthAnalyticsCustomModule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/effectiveSecurityHealthAnalyticsCustomModules/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/locations/*/effectiveSecurityHealthAnalyticsCustomModules/*}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/locations/*/effectiveSecurityHealthAnalyticsCustomModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of all
  // [SecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityHealthAnalyticsCustomModule]
  // resources for the given parent. This includes resident modules defined at
  // the scope of the parent, and inherited modules, inherited from ancestor
  // organizations, folders, and projects (no descendants).
  rpc ListSecurityHealthAnalyticsCustomModules(
      ListSecurityHealthAnalyticsCustomModulesRequest)
      returns (ListSecurityHealthAnalyticsCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/securityHealthAnalyticsCustomModules"
      additional_bindings {
        get: "/v1/{parent=folders/*/locations/*}/securityHealthAnalyticsCustomModules"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*/locations/*}/securityHealthAnalyticsCustomModules"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns a list of all resident
  // [SecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityHealthAnalyticsCustomModule]
  // resources under the given organization, folder, or project and all of its
  // descendants.
  rpc ListDescendantSecurityHealthAnalyticsCustomModules(
      ListDescendantSecurityHealthAnalyticsCustomModulesRequest)
      returns (ListDescendantSecurityHealthAnalyticsCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/securityHealthAnalyticsCustomModules:listDescendant"
      additional_bindings {
        get: "/v1/{parent=folders/*/locations/*}/securityHealthAnalyticsCustomModules:listDescendant"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*/locations/*}/securityHealthAnalyticsCustomModules:listDescendant"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a
  // [SecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityHealthAnalyticsCustomModule].
  rpc GetSecurityHealthAnalyticsCustomModule(
      GetSecurityHealthAnalyticsCustomModuleRequest)
      returns (SecurityHealthAnalyticsCustomModule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/securityHealthAnalyticsCustomModules/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/locations/*/securityHealthAnalyticsCustomModules/*}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/locations/*/securityHealthAnalyticsCustomModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a resident
  // [SecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityHealthAnalyticsCustomModule]
  // at the scope of the given organization, folder, or project, and also
  // creates inherited `SecurityHealthAnalyticsCustomModule` resources for all
  // folders and projects that are descendants of the given parent. These
  // modules are enabled by default.
  rpc CreateSecurityHealthAnalyticsCustomModule(
      CreateSecurityHealthAnalyticsCustomModuleRequest)
      returns (SecurityHealthAnalyticsCustomModule) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/securityHealthAnalyticsCustomModules"
      body: "security_health_analytics_custom_module"
      additional_bindings {
        post: "/v1/{parent=folders/*/locations/*}/securityHealthAnalyticsCustomModules"
        body: "security_health_analytics_custom_module"
      }
      additional_bindings {
        post: "/v1/{parent=organizations/*/locations/*}/securityHealthAnalyticsCustomModules"
        body: "security_health_analytics_custom_module"
      }
    };
    option (google.api.method_signature) =
        "parent,security_health_analytics_custom_module";
  }

  // Updates the
  // [SecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityHealthAnalyticsCustomModule]
  // under the given name based on the given update mask. Updating the
  // enablement state is supported on both resident and inherited modules
  // (though resident modules cannot have an enablement state of "inherited").
  // Updating the display name and custom configuration of a module is supported
  // on resident modules only.
  rpc UpdateSecurityHealthAnalyticsCustomModule(
      UpdateSecurityHealthAnalyticsCustomModuleRequest)
      returns (SecurityHealthAnalyticsCustomModule) {
    option (google.api.http) = {
      patch: "/v1/{security_health_analytics_custom_module.name=projects/*/locations/*/securityHealthAnalyticsCustomModules/*}"
      body: "security_health_analytics_custom_module"
      additional_bindings {
        patch: "/v1/{security_health_analytics_custom_module.name=folders/*/locations/*/securityHealthAnalyticsCustomModules/*}"
        body: "security_health_analytics_custom_module"
      }
      additional_bindings {
        patch: "/v1/{security_health_analytics_custom_module.name=organizations/*/locations/*/securityHealthAnalyticsCustomModules/*}"
        body: "security_health_analytics_custom_module"
      }
    };
    option (google.api.method_signature) =
        "security_health_analytics_custom_module,update_mask";
  }

  // Deletes the specified
  // [SecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityHealthAnalyticsCustomModule]
  // and all of its descendants in the resource hierarchy. This method is only
  // supported for resident custom modules.
  rpc DeleteSecurityHealthAnalyticsCustomModule(
      DeleteSecurityHealthAnalyticsCustomModuleRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/securityHealthAnalyticsCustomModules/*}"
      additional_bindings {
        delete: "/v1/{name=folders/*/locations/*/securityHealthAnalyticsCustomModules/*}"
      }
      additional_bindings {
        delete: "/v1/{name=organizations/*/locations/*/securityHealthAnalyticsCustomModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Simulates the result of using a
  // [SecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityHealthAnalyticsCustomModule]
  // to check a resource.
  rpc SimulateSecurityHealthAnalyticsCustomModule(
      SimulateSecurityHealthAnalyticsCustomModuleRequest)
      returns (SimulateSecurityHealthAnalyticsCustomModuleResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/securityHealthAnalyticsCustomModules:simulate"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=folders/*/locations/*}/securityHealthAnalyticsCustomModules:simulate"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{parent=organizations/*/locations/*}/securityHealthAnalyticsCustomModules:simulate"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,custom_config,resource";
  }

  // Lists all effective Event Threat Detection custom modules for the
  // given parent. This includes resident modules defined at the scope of the
  // parent along with modules inherited from its ancestors.
  rpc ListEffectiveEventThreatDetectionCustomModules(
      ListEffectiveEventThreatDetectionCustomModulesRequest)
      returns (ListEffectiveEventThreatDetectionCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/effectiveEventThreatDetectionCustomModules"
      additional_bindings {
        get: "/v1/{parent=folders/*/locations/*}/effectiveEventThreatDetectionCustomModules"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*/locations/*}/effectiveEventThreatDetectionCustomModules"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the effective Event Threat Detection custom module at the given level.
  //
  // The difference between an
  // [EffectiveEventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.EffectiveEventThreatDetectionCustomModule]
  // and an
  // [EventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.EventThreatDetectionCustomModule]
  // is that the fields for an `EffectiveEventThreatDetectionCustomModule` are
  // computed from ancestors if needed. For example, the enablement state for an
  // `EventThreatDetectionCustomModule` can be `ENABLED`, `DISABLED`, or
  // `INHERITED`. In contrast, the enablement state for an
  // `EffectiveEventThreatDetectionCustomModule` is always computed as `ENABLED`
  // or `DISABLED`.
  rpc GetEffectiveEventThreatDetectionCustomModule(
      GetEffectiveEventThreatDetectionCustomModuleRequest)
      returns (EffectiveEventThreatDetectionCustomModule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/effectiveEventThreatDetectionCustomModules/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/locations/*/effectiveEventThreatDetectionCustomModules/*}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/locations/*/effectiveEventThreatDetectionCustomModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all Event Threat Detection custom modules for the given organization,
  // folder, or project. This includes resident modules defined at the scope of
  // the parent along with modules inherited from ancestors.
  rpc ListEventThreatDetectionCustomModules(
      ListEventThreatDetectionCustomModulesRequest)
      returns (ListEventThreatDetectionCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/eventThreatDetectionCustomModules"
      additional_bindings {
        get: "/v1/{parent=folders/*/locations/*}/eventThreatDetectionCustomModules"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*/locations/*}/eventThreatDetectionCustomModules"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all resident Event Threat Detection custom modules for the given
  // organization, folder, or project and its descendants.
  rpc ListDescendantEventThreatDetectionCustomModules(
      ListDescendantEventThreatDetectionCustomModulesRequest)
      returns (ListDescendantEventThreatDetectionCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/eventThreatDetectionCustomModules:listDescendant"
      additional_bindings {
        get: "/v1/{parent=folders/*/locations/*}/eventThreatDetectionCustomModules:listDescendant"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*/locations/*}/eventThreatDetectionCustomModules:listDescendant"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets an Event Threat Detection custom module.
  rpc GetEventThreatDetectionCustomModule(
      GetEventThreatDetectionCustomModuleRequest)
      returns (EventThreatDetectionCustomModule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/eventThreatDetectionCustomModules/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/locations/*/eventThreatDetectionCustomModules/*}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/locations/*/eventThreatDetectionCustomModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a resident Event Threat Detection custom module at the scope of the
  // given organization, folder, or project, and creates inherited custom
  // modules for all descendants of the given parent. These modules are enabled
  // by default.
  rpc CreateEventThreatDetectionCustomModule(
      CreateEventThreatDetectionCustomModuleRequest)
      returns (EventThreatDetectionCustomModule) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/eventThreatDetectionCustomModules"
      body: "event_threat_detection_custom_module"
      additional_bindings {
        post: "/v1/{parent=folders/*/locations/*}/eventThreatDetectionCustomModules"
        body: "event_threat_detection_custom_module"
      }
      additional_bindings {
        post: "/v1/{parent=organizations/*/locations/*}/eventThreatDetectionCustomModules"
        body: "event_threat_detection_custom_module"
      }
    };
    option (google.api.method_signature) =
        "parent,event_threat_detection_custom_module";
  }

  // Updates the Event Threat Detection custom module with the given name based
  // on the given update mask. Updating the enablement state is supported for
  // both resident and inherited modules (though resident modules cannot have an
  // enablement state of "inherited"). Updating the display name or
  // configuration of a module is supported for resident modules only. The type
  // of a module cannot be changed.
  rpc UpdateEventThreatDetectionCustomModule(
      UpdateEventThreatDetectionCustomModuleRequest)
      returns (EventThreatDetectionCustomModule) {
    option (google.api.http) = {
      patch: "/v1/{event_threat_detection_custom_module.name=projects/*/locations/*/eventThreatDetectionCustomModules/*}"
      body: "event_threat_detection_custom_module"
      additional_bindings {
        patch: "/v1/{event_threat_detection_custom_module.name=folders/*/locations/*/eventThreatDetectionCustomModules/*}"
        body: "event_threat_detection_custom_module"
      }
      additional_bindings {
        patch: "/v1/{event_threat_detection_custom_module.name=organizations/*/locations/*/eventThreatDetectionCustomModules/*}"
        body: "event_threat_detection_custom_module"
      }
    };
    option (google.api.method_signature) =
        "event_threat_detection_custom_module,update_mask";
  }

  // Deletes the specified Event Threat Detection custom module and all of its
  // descendants in the resource hierarchy. This method is only supported for
  // resident custom modules.
  rpc DeleteEventThreatDetectionCustomModule(
      DeleteEventThreatDetectionCustomModuleRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/eventThreatDetectionCustomModules/*}"
      additional_bindings {
        delete: "/v1/{name=folders/*/locations/*/eventThreatDetectionCustomModules/*}"
      }
      additional_bindings {
        delete: "/v1/{name=organizations/*/locations/*/eventThreatDetectionCustomModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Validates the given Event Threat Detection custom module.
  rpc ValidateEventThreatDetectionCustomModule(
      ValidateEventThreatDetectionCustomModuleRequest)
      returns (ValidateEventThreatDetectionCustomModuleResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/eventThreatDetectionCustomModules:validate"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=folders/*/locations/*}/eventThreatDetectionCustomModules:validate"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{parent=organizations/*/locations/*}/eventThreatDetectionCustomModules:validate"
        body: "*"
      }
    };
  }

  // Gets service settings for the specified Security Command Center service.
  rpc GetSecurityCenterService(GetSecurityCenterServiceRequest)
      returns (SecurityCenterService) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/securityCenterServices/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/locations/*/securityCenterServices/*}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/locations/*/securityCenterServices/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of all Security Command Center services for the given
  // parent.
  rpc ListSecurityCenterServices(ListSecurityCenterServicesRequest)
      returns (ListSecurityCenterServicesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/securityCenterServices"
      additional_bindings {
        get: "/v1/{parent=folders/*/locations/*}/securityCenterServices"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*/locations/*}/securityCenterServices"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a Security Command Center service using the given update mask.
  rpc UpdateSecurityCenterService(UpdateSecurityCenterServiceRequest)
      returns (SecurityCenterService) {
    option (google.api.http) = {
      patch: "/v1/{security_center_service.name=projects/*/locations/*/securityCenterServices/*}"
      body: "security_center_service"
      additional_bindings {
        patch: "/v1/{security_center_service.name=folders/*/locations/*/securityCenterServices/*}"
        body: "security_center_service"
      }
      additional_bindings {
        patch: "/v1/{security_center_service.name=organizations/*/locations/*/securityCenterServices/*}"
        body: "security_center_service"
      }
    };
    option (google.api.method_signature) =
        "security_center_service,update_mask";
  }
}

// Represents a particular Security Command Center service. This includes
// settings information such as top-level enablement in addition to individual
// module settings. Service settings can be configured at the organization,
// folder, or project level. Service settings at the organization or folder
// level are inherited by those in descendant folders and projects.
message SecurityCenterService {
  option (google.api.resource) = {
    type: "securitycentermanagement.googleapis.com/SecurityCenterService"
    pattern: "projects/{project}/locations/{location}/securityCenterServices/{service}"
    pattern: "folders/{folder}/locations/{location}/securityCenterServices/{service}"
    pattern: "organizations/{organization}/locations/{location}/securityCenterServices/{service}"
    plural: "securityCenterServices"
    singular: "securityCenterService"
  };

  // The settings for individual modules.
  message ModuleSettings {
    // Optional. The intended enablement state for the module at its level of
    // the resource hierarchy.
    EnablementState intended_enablement_state = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Output only. The effective enablement state for the module at its level
    // of the resource hierarchy. If the intended state is set to `INHERITED`,
    // the effective state will be inherited from the enablement state of an
    // ancestor. This state may differ from the intended enablement state due to
    // billing eligibility or onboarding status.
    EnablementState effective_enablement_state = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Represents the possible enablement states for a service or module.
  enum EnablementState {
    // Default value. This value is unused.
    ENABLEMENT_STATE_UNSPECIFIED = 0;

    // State is inherited from the parent resource. Valid as an intended
    // enablement state, but not as an effective enablement state.
    INHERITED = 1;

    // State is enabled.
    ENABLED = 2;

    // State is disabled.
    DISABLED = 3;

    // Security Command Center is configured to ingest findings from this
    // service, but not to enable this service. This state indicates that
    // Security Command Center is misconfigured. You can't set this state
    // yourself.
    INGEST_ONLY = 4;
  }

  // Identifier. The name of the service, in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}/securityCenterServices/{service}`
  // * `folders/{folder}/locations/{location}/securityCenterServices/{service}`
  // * `projects/{project}/locations/{location}/securityCenterServices/{service}`
  //
  // The following values are valid for `{service}`:
  //
  // * `container-threat-detection`
  // * `event-threat-detection`
  // * `security-health-analytics`
  // * `vm-threat-detection`
  // * `web-security-scanner`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The intended enablement state for the service at its level of the
  // resource hierarchy. A `DISABLED` state will override all module enablement
  // states to `DISABLED`.
  EnablementState intended_enablement_state = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The effective enablement state for the service at its level of
  // the resource hierarchy. If the intended state is set to `INHERITED`, the
  // effective state will be inherited from the enablement state of an ancestor.
  // This state may differ from the intended enablement state due to billing
  // eligibility or onboarding status.
  EnablementState effective_enablement_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The module configurations, including the enablement state for the
  // service's modules. The absence of a module in the map implies that its
  // configuration is inherited from its parents.
  map<string, ModuleSettings> modules = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time the service was last updated. This could be due to an
  // explicit user update or due to a side effect of another system change, such
  // as billing subscription expiry.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Additional service-specific configuration. Not all services will
  // utilize this field.
  google.protobuf.Struct service_config = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// The representation of a Security Health Analytics custom module at a
// specified level of the resource hierarchy: organization, folder, or project.
// If a custom module is inherited from an ancestor organization or folder, then
// the enablement state is set to the value that is effective in the parent, not
// to `INHERITED`. For example, if the module is enabled in an organization or
// folder, then the effective enablement state for the module is `ENABLED` in
// all descendant folders or projects.
message EffectiveSecurityHealthAnalyticsCustomModule {
  option (google.api.resource) = {
    type: "securitycentermanagement.googleapis.com/EffectiveSecurityHealthAnalyticsCustomModule"
    pattern: "organizations/{organization}/locations/{location}/effectiveSecurityHealthAnalyticsCustomModules/{effective_security_health_analytics_custom_module}"
    pattern: "projects/{project}/locations/{location}/effectiveSecurityHealthAnalyticsCustomModules/{effective_security_health_analytics_custom_module}"
    pattern: "folders/{folder}/locations/{location}/effectiveSecurityHealthAnalyticsCustomModules/{effective_security_health_analytics_custom_module}"
    plural: "effectiveSecurityHealthAnalyticsCustomModules"
    singular: "effectiveSecurityHealthAnalyticsCustomModule"
  };

  // The enablement state of the module.
  enum EnablementState {
    // Default value. This value is unused.
    ENABLEMENT_STATE_UNSPECIFIED = 0;

    // The module is enabled at the given level.
    ENABLED = 1;

    // The module is disabled at the given level.
    DISABLED = 2;
  }

  // Identifier. The full resource name of the custom module, in one of the
  // following formats:
  //
  // * `organizations/organization/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom_module}`
  // * `folders/folder/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom_module}`
  // * `projects/project/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom_module}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The user-specified configuration for the module.
  CustomConfig custom_config = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The effective enablement state for the module at the given
  // level of the hierarchy.
  EnablementState enablement_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name for the custom module. The name must be
  // between 1 and 128 characters, start with a lowercase letter, and contain
  // alphanumeric characters or underscores only.
  string display_name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [SecurityCenterManagement.ListEffectiveSecurityHealthAnalyticsCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListEffectiveSecurityHealthAnalyticsCustomModules].
message ListEffectiveSecurityHealthAnalyticsCustomModulesRequest {
  // Required. Name of parent to list effective custom modules, in one of the
  // following formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/EffectiveSecurityHealthAnalyticsCustomModule"
    }
  ];

  // Optional. The maximum number of results to return in a single response.
  // Default is 10, minimum is 1, maximum is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous request. Provide this
  // token to retrieve the next page of results.
  //
  // When paginating, the rest of the request must match the request that
  // generated the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [SecurityCenterManagement.ListEffectiveSecurityHealthAnalyticsCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListEffectiveSecurityHealthAnalyticsCustomModules].
message ListEffectiveSecurityHealthAnalyticsCustomModulesResponse {
  // The list of effective Security Health Analytics custom modules.
  repeated EffectiveSecurityHealthAnalyticsCustomModule
      effective_security_health_analytics_custom_modules = 1;

  // A pagination token. To retrieve the next page of results, call the method
  // again with this token.
  string next_page_token = 2;
}

// Request message for
// [SecurityCenterManagement.GetEffectiveSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.GetEffectiveSecurityHealthAnalyticsCustomModule].
message GetEffectiveSecurityHealthAnalyticsCustomModuleRequest {
  // Required. The full resource name of the custom module, specified in one of
  // the following formats:
  //
  // * `organizations/organization/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom_module}`
  // * `folders/folder/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom_module}`
  // * `projects/project/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom_module}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/EffectiveSecurityHealthAnalyticsCustomModule"
    }
  ];
}

// Represents an instance of a Security Health Analytics custom module,
// including its full module name, display name, enablement state, and last
// updated time. You can create a custom module at the organization, folder, or
// project level. Custom modules that you create at the organization or folder
// level are inherited by the descendant folders and projects.
message SecurityHealthAnalyticsCustomModule {
  option (google.api.resource) = {
    type: "securitycentermanagement.googleapis.com/SecurityHealthAnalyticsCustomModule"
    pattern: "organizations/{organization}/locations/{location}/securityHealthAnalyticsCustomModules/{security_health_analytics_custom_module}"
    pattern: "projects/{project}/locations/{location}/securityHealthAnalyticsCustomModules/{security_health_analytics_custom_module}"
    pattern: "folders/{folder}/locations/{location}/securityHealthAnalyticsCustomModules/{security_health_analytics_custom_module}"
    plural: "securityHealthAnalyticsCustomModules"
    singular: "securityHealthAnalyticsCustomModule"
  };

  // Possible enablement states of a custom module.
  enum EnablementState {
    // Default value. This value is unused.
    ENABLEMENT_STATE_UNSPECIFIED = 0;

    // The module is enabled at the given organization, folder, or project.
    ENABLED = 1;

    // The module is disabled at the given organization, folder, or project.
    DISABLED = 2;

    // State is inherited from an ancestor module. The module will either
    // be effectively `ENABLED` or `DISABLED` based on its closest non-inherited
    // ancestor module in the resource hierarchy. If you try to set a top-level
    // module (a module with no parent) to the `INHERITED` state, you receive an
    // `INVALID_ARGUMENT` error.
    INHERITED = 3;
  }

  // Identifier. The full resource name of the custom module, in one of the
  // following formats:
  //
  // * `organizations/{organization}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
  // * `folders/{folder}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
  // * `projects/{project}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The display name of the Security Health Analytics custom module.
  // This display name becomes the finding category for all findings that are
  // returned by this custom module. The display name must be between 1 and 128
  // characters, start with a lowercase letter, and contain alphanumeric
  // characters or underscores only.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The enablement state of the custom module.
  EnablementState enablement_state = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time at which the custom module was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The editor that last updated the custom module.
  string last_editor = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Specifies the organization or folder from which the custom
  // module is inherited. If empty, indicates that the custom module was created
  // in the organization, folder, or project in which you are viewing the custom
  // module.
  string ancestor_module = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // Optional. The user-specified custom configuration for the module.
  CustomConfig custom_config = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Defines the properties in a custom module configuration for Security
// Health Analytics. Use the custom module configuration to create custom
// detectors that generate custom findings for resources that you specify.
message CustomConfig {
  // A set of optional name-value pairs that define custom source properties to
  // return with each finding that is generated by the custom module. The custom
  // source properties that are defined here are included in the finding.
  message CustomOutputSpec {
    // An individual name-value pair that defines a custom source property.
    message Property {
      // Optional. Name of the property for the custom output.
      string name = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The CEL expression for the custom output. A resource property
      // can be specified to return the value of the property or a text string
      // enclosed in quotation marks.
      google.type.Expr value_expression = 2
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. A list of custom output properties to add to the finding.
    repeated Property properties = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Resource for selecting resource type.
  message ResourceSelector {
    // Optional. The resource types to run the detector on.
    repeated string resource_types = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Defines the valid value options for the severity of a finding.
  enum Severity {
    // Default value. This value is unused.
    SEVERITY_UNSPECIFIED = 0;

    // Critical severity.
    CRITICAL = 1;

    // High severity.
    HIGH = 2;

    // Medium severity.
    MEDIUM = 3;

    // Low severity.
    LOW = 4;
  }

  // Optional. The Common Expression Language (CEL) expression to evaluate to
  // produce findings. When the expression evaluates to `true` against a
  // resource, a finding is generated.
  google.type.Expr predicate = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom output properties.
  CustomOutputSpec custom_output = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Cloud Asset Inventory resource types that the custom module
  // operates on. For information about resource types, see [Supported asset
  // types](https://cloud.google.com/asset-inventory/docs/supported-asset-types).
  // Each custom module can specify up to 5 resource types.
  ResourceSelector resource_selector = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The severity to assign to findings generated by the module.
  Severity severity = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Text that describes the vulnerability or misconfiguration that
  // the custom module detects. This explanation is returned with each finding
  // instance to help investigators understand the detected issue. The text must
  // be enclosed in quotation marks.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An explanation of the recommended steps that security teams can
  // take to resolve the detected issue. This explanation is returned with each
  // finding generated by this module.
  string recommendation = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [SecurityCenterManagement.ListSecurityHealthAnalyticsCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListSecurityHealthAnalyticsCustomModules].
message ListSecurityHealthAnalyticsCustomModulesRequest {
  // Required. Name of the parent organization, folder, or project in which to
  // list custom modules, in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // Optional. The maximum number of results to return in a single response.
  // Default is 10, minimum is 1, maximum is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous request. Provide this
  // token to retrieve the next page of results.
  //
  // When paginating, the rest of the request must match the request that
  // generated the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [SecurityCenterManagement.ListSecurityHealthAnalyticsCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListSecurityHealthAnalyticsCustomModules].
message ListSecurityHealthAnalyticsCustomModulesResponse {
  // The list of Security Health Analytics custom modules.
  repeated SecurityHealthAnalyticsCustomModule
      security_health_analytics_custom_modules = 1;

  // A pagination token. To retrieve the next page of results, call the method
  // again with this token.
  string next_page_token = 2;
}

// Request message for
// [SecurityCenterManagement.ListDescendantSecurityHealthAnalyticsCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListDescendantSecurityHealthAnalyticsCustomModules].
message ListDescendantSecurityHealthAnalyticsCustomModulesRequest {
  // Required. Name of the parent organization, folder, or project in which to
  // list custom modules, in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // Optional. The maximum number of results to return in a single response.
  // Default is 10, minimum is 1, maximum is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous request. Provide this
  // token to retrieve the next page of results.
  //
  // When paginating, the rest of the request must match the request that
  // generated the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [SecurityCenterManagement.ListDescendantSecurityHealthAnalyticsCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListDescendantSecurityHealthAnalyticsCustomModules].
message ListDescendantSecurityHealthAnalyticsCustomModulesResponse {
  // The list of SecurityHealthAnalyticsCustomModules
  repeated SecurityHealthAnalyticsCustomModule
      security_health_analytics_custom_modules = 1;

  // A pagination token. To retrieve the next page of results, call the method
  // again with this token.
  string next_page_token = 2;
}

// Request message for
// [SecurityCenterManagement.GetSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.GetSecurityHealthAnalyticsCustomModule].
message GetSecurityHealthAnalyticsCustomModuleRequest {
  // Required. Name of the resource, in the format
  // `projects/{project}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];
}

// Request message for
// [SecurityCenterManagement.CreateSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.CreateSecurityHealthAnalyticsCustomModule].
message CreateSecurityHealthAnalyticsCustomModuleRequest {
  // Required. Name of the parent organization, folder, or project of the
  // module, in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // Required. The resource being created.
  SecurityHealthAnalyticsCustomModule security_health_analytics_custom_module =
      2 [(google.api.field_behavior) = REQUIRED];

  // Optional. When set to `true`, the request will be validated (including IAM
  // checks), but no module will be created. An `OK` response indicates that the
  // request is valid, while an error response indicates that the request is
  // invalid.
  //
  // If the request is valid, a subsequent request to create the module could
  // still fail for one of the following reasons:
  //
  // *  The state of your cloud resources changed; for example, you lost a
  //    required IAM permission
  // *  An error occurred during creation of the module
  //
  // Defaults to `false`.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [SecurityCenterManagement.UpdateSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.UpdateSecurityHealthAnalyticsCustomModule].
message UpdateSecurityHealthAnalyticsCustomModuleRequest {
  // Required. The fields to update. The following values are valid:
  //
  // * `custom_config`
  // * `enablement_state`
  //
  // If you omit this field or set it to the wildcard value `*`, then all
  // eligible fields are updated.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  SecurityHealthAnalyticsCustomModule security_health_analytics_custom_module =
      2 [(google.api.field_behavior) = REQUIRED];

  // Optional. When set to `true`, the request will be validated (including IAM
  // checks), but no module will be updated. An `OK` response indicates that the
  // request is valid, while an error response indicates that the request is
  // invalid.
  //
  // If the request is valid, a subsequent request to update the module could
  // still fail for one of the following reasons:
  //
  // *  The state of your cloud resources changed; for example, you lost a
  //    required IAM permission
  // *  An error occurred during creation of the module
  //
  // Defaults to `false`.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [SecurityCenterManagement.DeleteSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.DeleteSecurityHealthAnalyticsCustomModule].
message DeleteSecurityHealthAnalyticsCustomModuleRequest {
  // Required. The resource name of the SHA custom module, in one of the
  // following formats:
  //
  //   * `organizations/{organization}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
  //   * `folders/{folder}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
  //   * `projects/{project}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // Optional. When set to `true`, the request will be validated (including IAM
  // checks), but no module will be deleted. An `OK` response indicates that the
  // request is valid, while an error response indicates that the request is
  // invalid.
  //
  // If the request is valid, a subsequent request to delete the module could
  // still fail for one of the following reasons:
  //
  // *  The state of your cloud resources changed; for example, you lost a
  //    required IAM permission
  // *  An error occurred during deletion of the module
  //
  // Defaults to `false`.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [SecurityCenterManagement.SimulateSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.SimulateSecurityHealthAnalyticsCustomModule].
// The maximum size of the request is 4 MiB.
message SimulateSecurityHealthAnalyticsCustomModuleRequest {
  // Manually constructed information about a resource.
  message SimulatedResource {
    // Required. The type of the resource. For example,
    // `compute.googleapis.com/Disk`.
    string resource_type = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. A representation of the Google Cloud resource. Should match the
    // Google Cloud resource JSON format.
    //
    // If the custom module evaluates only the IAM allow policy, then you can
    // omit this field.
    google.protobuf.Struct resource_data = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. A representation of the IAM allow policy.
    //
    // If the custom module evaluates only the resource data, then you can omit
    // this field.
    google.iam.v1.Policy iam_policy_data = 3
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The relative resource name of the organization, project, or
  // folder. For more information about relative resource names, see [AIP-122:
  // Resource names](https://google.aip.dev/122). Example:
  // `organizations/{organization_id}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The custom configuration that you need to test.
  CustomConfig custom_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Resource data to simulate custom module against.
  SimulatedResource resource = 3 [(google.api.field_behavior) = REQUIRED];
}

// The minimum set of fields needed to represent a simulated finding from a
// Security Health Analytics custom module.
message SimulatedFinding {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/Finding"
    pattern: "organizations/{organization}/sources/{source}/findings/{finding}"
    pattern: "folders/{folder}/sources/{source}/findings/{finding}"
    pattern: "projects/{project}/sources/{source}/findings/{finding}"
    plural: "findings"
    singular: "finding"
  };

  // The state of the finding.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The finding requires attention and has not been addressed yet.
    ACTIVE = 1;

    // The finding has been fixed, triaged as a non-issue, or otherwise
    // addressed and is no longer active.
    INACTIVE = 2;
  }

  // The severity of the finding.
  enum Severity {
    // Default value. This value is unused.
    SEVERITY_UNSPECIFIED = 0;

    // For vulnerabilities: A critical vulnerability is easily discoverable by
    // an external actor, exploitable, and results in the direct ability to
    // execute arbitrary code, exfiltrate data, and otherwise gain additional
    // access and privileges to cloud resources and workloads. Examples include
    // publicly accessible unprotected user data and public SSH access with weak
    // or no passwords.
    //
    // For threats: Indicates a threat that is able to access, modify, or delete
    // data or execute unauthorized code within existing resources.
    CRITICAL = 1;

    // For vulnerabilities: A high-risk vulnerability can be easily discovered
    // and exploited in combination with other vulnerabilities in order to gain
    // direct access and the ability to execute arbitrary code, exfiltrate data,
    // and otherwise gain additional access and privileges to cloud resources
    // and workloads. An example is a database with weak or no passwords that is
    // only accessible internally. This database could easily be compromised by
    // an actor that had access to the internal network.
    //
    // For threats: Indicates a threat that is able to create new computational
    // resources in an environment but not able to access data or execute code
    // in existing resources.
    HIGH = 2;

    // For vulnerabilities: A medium-risk vulnerability could be used by an
    // actor to gain access to resources or privileges that enable them to
    // eventually (through multiple steps or a complex exploit) gain access and
    // the ability to execute arbitrary code or exfiltrate data. An example is a
    // service account with access to more projects than it should have. If an
    // actor gains access to the service account, they could potentially use
    // that access to manipulate a project the service account was not intended
    // to.
    //
    // For threats: Indicates a threat that is able to cause operational impact
    // but may not access data or execute unauthorized code.
    MEDIUM = 3;

    // For vulnerabilities: A low-risk vulnerability hampers a security
    // organization's ability to detect vulnerabilities or active threats in
    // their deployment, or prevents the root cause investigation of security
    // issues. An example is monitoring and logs being disabled for resource
    // configurations and access.
    //
    // For threats: Indicates a threat that has obtained minimal access to an
    // environment but is not able to access data, execute code, or create
    // resources.
    LOW = 4;
  }

  // Represents what kind of finding it is.
  enum FindingClass {
    // Default value. This value is unused.
    FINDING_CLASS_UNSPECIFIED = 0;

    // Describes unwanted or malicious activity.
    THREAT = 1;

    // Describes a potential weakness in software that increases risk to
    // confidentiality, integrity, and availability.
    VULNERABILITY = 2;

    // Describes a potential weakness in cloud resource or asset configuration
    // that increases risk.
    MISCONFIGURATION = 3;

    // Describes a security observation that is for informational purposes.
    OBSERVATION = 4;

    // Describes an error that prevents Security Command Center from working
    // correctly.
    SCC_ERROR = 5;

    // Describes a potential security risk due to a change in the security
    // posture.
    POSTURE_VIOLATION = 6;

    // Describes a combination of security issues that represent a more severe
    // security problem when taken together.
    TOXIC_COMBINATION = 7;
  }

  // Identifier. The [relative resource name](https://google.aip.dev/122) of the
  // finding, in one of the following formats:
  //
  // * `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}`
  // * `folders/{folder_id}/sources/{source_id}/findings/{finding_id}`
  // * `projects/{project_id}/sources/{source_id}/findings/{finding_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The [relative resource name](https://google.aip.dev/122) of the source the
  // finding belongs to. For example,
  // `organizations/{organization_id}/sources/{source_id}`. This field is
  // immutable after creation time.
  string parent = 2;

  // For findings on Google Cloud resources, the
  // [full resource name](https://google.aip.dev/122#full-resource-names) of the
  // Google Cloud resource this finding is for. When the finding is for a
  // non-Google Cloud resource, the value can be a customer or partner defined
  // string. This field is immutable after creation time.
  string resource_name = 3;

  // The additional taxonomy group within findings from a given source. For
  // example, `XSS_FLASH_INJECTION`. This field is immutable after creation
  // time.
  string category = 4;

  // Output only. The state of the finding.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Source-specific properties. These properties are managed by the source
  // that writes the finding. The key names must be between 1 and 255
  // characters; they must start with a letter and contain alphanumeric
  // characters or underscores only.
  map<string, google.protobuf.Value> source_properties = 6;

  // The time the finding was first detected. If an existing finding is updated,
  // then this is the time the update occurred. If the finding is later
  // resolved, then this time reflects when the finding was resolved.
  //
  // For example, if the finding represents an open firewall, this property
  // captures the time the detector believes the firewall became open. The
  // accuracy is determined by the detector.
  //
  // The event time must not be set to a value greater than the current
  // timestamp.
  google.protobuf.Timestamp event_time = 7;

  // The severity of the finding. This field is managed by the source that
  // writes the finding.
  Severity severity = 8;

  // The class of the finding.
  FindingClass finding_class = 9;
}

// Response message for
// [SecurityCenterManagement.SimulateSecurityHealthAnalyticsCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.SimulateSecurityHealthAnalyticsCustomModule].
message SimulateSecurityHealthAnalyticsCustomModuleResponse {
  // Possible test result.
  message SimulatedResult {
    // The result of the simulation.
    oneof result {
      // Finding that would be published for the test case if a violation is
      // detected.
      SimulatedFinding finding = 1;

      // Indicates that the test case does not trigger any violation.
      google.protobuf.Empty no_violation = 2;

      // Error encountered during the test.
      google.rpc.Status error = 3;
    }
  }

  // Result for test case in the corresponding request.
  SimulatedResult result = 1;
}

// The representation of an
// [EventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.EventThreatDetectionCustomModule]
// at a given level, taking hierarchy into account and resolving various fields
// accordingly. For example, if the module is enabled at the ancestor level,
// then effective modules at all descendant levels will have their enablement
// state set to `ENABLED`. Similarly, if `module.inherited` is set, then the
// effective module's configuration will reflect the ancestor's configuration.
message EffectiveEventThreatDetectionCustomModule {
  option (google.api.resource) = {
    type: "securitycentermanagement.googleapis.com/EffectiveEventThreatDetectionCustomModule"
    pattern: "organizations/{organization}/locations/{location}/effectiveEventThreatDetectionCustomModules/{effective_event_threat_detection_custom_module}"
    pattern: "projects/{project}/locations/{location}/effectiveEventThreatDetectionCustomModules/{effective_event_threat_detection_custom_module}"
    pattern: "folders/{folder}/locations/{location}/effectiveEventThreatDetectionCustomModules/{effective_event_threat_detection_custom_module}"
    plural: "effectiveEventThreatDetectionCustomModules"
    singular: "effectiveEventThreatDetectionCustomModule"
  };

  // The enablement state of the module.
  enum EnablementState {
    // Default value. This value is unused.
    ENABLEMENT_STATE_UNSPECIFIED = 0;

    // The module is enabled at the given level.
    ENABLED = 1;

    // The module is disabled at the given level.
    DISABLED = 2;
  }

  // Identifier. The resource name of the Event Threat Detection custom module,
  // in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  // * `folders/{folder}/locations/{location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  // * `projects/{project}/locations/{location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Configuration for the effective module.
  google.protobuf.Struct config = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The effective state of enablement for the module at the given
  // level of the hierarchy.
  EnablementState enablement_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type for the module (for example, `CONFIGURABLE_BAD_IP`).
  string type = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The human-readable name of the module.
  string display_name = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A description of the module.
  string description = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [SecurityCenterManagement.ListEffectiveEventThreatDetectionCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListEffectiveEventThreatDetectionCustomModules].
message ListEffectiveEventThreatDetectionCustomModulesRequest {
  // Required. Name of parent to list effective custom modules, in one of the
  // following formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/EffectiveEventThreatDetectionCustomModule"
    }
  ];

  // Optional. The maximum number of results to return in a single response.
  // Default is 10, minimum is 1, maximum is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous request. Provide this
  // token to retrieve the next page of results.
  //
  // When paginating, the rest of the request must match the request that
  // generated the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [SecurityCenterManagement.ListEffectiveEventThreatDetectionCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListEffectiveEventThreatDetectionCustomModules].
message ListEffectiveEventThreatDetectionCustomModulesResponse {
  // The list of effective Event Threat Detection custom modules.
  repeated EffectiveEventThreatDetectionCustomModule
      effective_event_threat_detection_custom_modules = 1;

  // A pagination token. To retrieve the next page of results, call the method
  // again with this token.
  string next_page_token = 2;
}

// Request message for
// [SecurityCenterManagement.GetEffectiveEventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.GetEffectiveEventThreatDetectionCustomModule].
message GetEffectiveEventThreatDetectionCustomModuleRequest {
  // Required. The resource name of the Event Threat Detection custom module, in
  // one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  // * `folders/{folder}/locations/{location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  // * `projects/{project}/locations/{location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/EffectiveEventThreatDetectionCustomModule"
    }
  ];
}

// A Security Command Center resource that contains the configuration and
// enablement state of a custom module, which enables Event Threat Detection to
// write certain findings to Security Command Center.
message EventThreatDetectionCustomModule {
  option (google.api.resource) = {
    type: "securitycentermanagement.googleapis.com/EventThreatDetectionCustomModule"
    pattern: "organizations/{organization}/locations/{location}/eventThreatDetectionCustomModules/{event_threat_detection_custom_module}"
    pattern: "projects/{project}/locations/{location}/eventThreatDetectionCustomModules/{event_threat_detection_custom_module}"
    pattern: "folders/{folder}/locations/{location}/eventThreatDetectionCustomModules/{event_threat_detection_custom_module}"
    plural: "eventThreatDetectionCustomModules"
    singular: "eventThreatDetectionCustomModule"
  };

  // The enablement state of the module.
  enum EnablementState {
    // Unspecified enablement state.
    ENABLEMENT_STATE_UNSPECIFIED = 0;

    // The module is enabled at the given level.
    ENABLED = 1;

    // The module is disabled at the given level.
    DISABLED = 2;

    // State is inherited from an ancestor module. The module will either be
    // effectively `ENABLED` or `DISABLED` based on its closest non-inherited
    // ancestor module in the CRM hierarchy. If you try to set a top-level
    // module (a module with no parent) to the `INHERITED` state, you receive an
    // `INVALID_ARGUMENT` error.
    INHERITED = 3;
  }

  // Identifier. The resource name of the Event Threat Detection custom module,
  // in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  // * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  // * `projects/{project}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Configuration for the module. For the resident module, its
  // configuration value is defined at this level. For the inherited module, its
  // configuration value is inherited from the ancestor module.
  google.protobuf.Struct config = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The closest ancestor module that this module inherits the
  // enablement state from. If empty, indicates that the custom module was
  // created in the requesting parent organization, folder, or project. The
  // format is the same as the custom module's resource name.
  string ancestor_module = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // Optional. The state of enablement for the module at the given level of the
  // hierarchy.
  EnablementState enablement_state = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Type for the module. For example, `CONFIGURABLE_BAD_IP`.
  string type = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The human-readable name of the module.
  string display_name = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A description of the module.
  string description = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time the module was last updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The editor the module was last updated by.
  string last_editor = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [SecurityCenterManagement.ListEventThreatDetectionCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListEventThreatDetectionCustomModules].
message ListEventThreatDetectionCustomModulesRequest {
  // Required. Name of parent to list custom modules, in one of the following
  // formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // Optional. The maximum number of modules to return. The service may return
  // fewer than this value. If unspecified, at most 10 modules will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous request. Provide this
  // token to retrieve the next page of results.
  //
  // When paginating, the rest of the request must match the request that
  // generated the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [SecurityCenterManagement.ListEventThreatDetectionCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListEventThreatDetectionCustomModules].
message ListEventThreatDetectionCustomModulesResponse {
  // The list of custom modules.
  repeated EventThreatDetectionCustomModule
      event_threat_detection_custom_modules = 1;

  // A pagination token. To retrieve the next page of results, call the method
  // again with this token.
  string next_page_token = 2;
}

// Request message for
// [SecurityCenterManagement.ListDescendantEventThreatDetectionCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListDescendantEventThreatDetectionCustomModules].
message ListDescendantEventThreatDetectionCustomModulesRequest {
  // Required. Name of parent to list custom modules, in one of the following
  // formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // Optional. The maximum number of modules to return. The service may return
  // fewer than this value. If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous request. Provide this
  // token to retrieve the next page of results.
  //
  // When paginating, the rest of the request must match the request that
  // generated the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [SecurityCenterManagement.ListDescendantEventThreatDetectionCustomModules][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListDescendantEventThreatDetectionCustomModules].
message ListDescendantEventThreatDetectionCustomModulesResponse {
  // The list of custom modules.
  repeated EventThreatDetectionCustomModule
      event_threat_detection_custom_modules = 1;

  // A pagination token. To retrieve the next page of results, call the method
  // again with this token.
  string next_page_token = 2;
}

// Request message for
// [SecurityCenterManagement.GetEventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.GetEventThreatDetectionCustomModule].
message GetEventThreatDetectionCustomModuleRequest {
  // Required. The resource name of the Event Threat Detection custom module, in
  // one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  // * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  // * `projects/{project}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];
}

// Request message for
// [SecurityCenterManagement.CreateEventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.CreateEventThreatDetectionCustomModule].
message CreateEventThreatDetectionCustomModuleRequest {
  // Required. Name of parent for the module, in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // Required. The module to create. The
  // [EventThreatDetectionCustomModule.name][google.cloud.securitycentermanagement.v1.EventThreatDetectionCustomModule.name]
  // field is ignored; Security Command Center generates the name.
  EventThreatDetectionCustomModule event_threat_detection_custom_module = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. When set to `true`, the request will be validated (including IAM
  // checks), but no module will be created. An `OK` response indicates that the
  // request is valid, while an error response indicates that the request is
  // invalid.
  //
  // If the request is valid, a subsequent request to create the module could
  // still fail for one of the following reasons:
  //
  // *  The state of your cloud resources changed; for example, you lost a
  //    required IAM permission
  // *  An error occurred during creation of the module
  //
  // Defaults to `false`.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a EventThreatDetectionCustomModule
message UpdateEventThreatDetectionCustomModuleRequest {
  // Required. The fields to update. If omitted, then all fields are updated.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The module being updated.
  EventThreatDetectionCustomModule event_threat_detection_custom_module = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. When set to `true`, the request will be validated (including IAM
  // checks), but no module will be updated. An `OK` response indicates that the
  // request is valid, while an error response indicates that the request is
  // invalid.
  //
  // If the request is valid, a subsequent request to update the module could
  // still fail for one of the following reasons:
  //
  // *  The state of your cloud resources changed; for example, you lost a
  //    required IAM permission
  // *  An error occurred during creation of the module
  //
  // Defaults to `false`.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [SecurityCenterManagement.DeleteEventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.DeleteEventThreatDetectionCustomModule].
message DeleteEventThreatDetectionCustomModuleRequest {
  // Required. The resource name of the Event Threat Detection custom module, in
  // one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  // * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  // * `projects/{project}/locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // Optional. When set to `true`, the request will be validated (including IAM
  // checks), but no module will be deleted. An `OK` response indicates that the
  // request is valid, while an error response indicates that the request is
  // invalid.
  //
  // If the request is valid, a subsequent request to delete the module could
  // still fail for one of the following reasons:
  //
  // *  The state of your cloud resources changed; for example, you lost a
  //    required IAM permission
  // *  An error occurred during creation of the module
  //
  // Defaults to `false`.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [SecurityCenterManagement.ValidateEventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ValidateEventThreatDetectionCustomModule].
message ValidateEventThreatDetectionCustomModuleRequest {
  // Required. Resource name of the parent to validate the custom modules under,
  // in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // Required. The raw text of the module's contents. Used to generate error
  // messages.
  string raw_text = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the module. For example, `CONFIGURABLE_BAD_IP`.
  string type = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [SecurityCenterManagement.ValidateEventThreatDetectionCustomModule][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ValidateEventThreatDetectionCustomModule].
message ValidateEventThreatDetectionCustomModuleResponse {
  // An error encountered while validating the uploaded configuration of an
  // Event Threat Detection custom module.
  message CustomModuleValidationError {
    // A human-readable description of the error.
    string description = 1;

    // The path, in [RFC 6901: JSON
    // Pointer](https://datatracker.ietf.org/doc/html/rfc6901) format, to the
    // field that failed validation. Omitted if no specific field is affected.
    string field_path = 2;

    // The initial position of the error in the uploaded text version of the
    // module. Omitted if no specific position applies, or if the position could
    // not be computed.
    optional Position start = 3;

    // The end position of the error in the uploaded text version of the module.
    // Omitted if no specific position applies, or if the position could not be
    // computed.
    optional Position end = 4;
  }

  // A position in the uploaded text version of a module.
  message Position {
    // The line position in the text.
    int32 line_number = 1;

    // The column position in the line.
    int32 column_number = 2;
  }

  // A list of errors returned by the validator. If the list is empty, there
  // were no errors.
  repeated CustomModuleValidationError errors = 2;
}

// Request message for
// [SecurityCenterManagement.GetSecurityCenterService][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.GetSecurityCenterService].
message GetSecurityCenterServiceRequest {
  // Required. The Security Command Center service to retrieve, in one of the
  // following formats:
  //
  // * organizations/{organization}/locations/{location}/securityCenterServices/{service}
  // * folders/{folder}/locations/{location}/securityCenterServices/{service}
  // * projects/{project}/locations/{location}/securityCenterServices/{service}
  //
  // The following values are valid for `{service}`:
  //
  // * `container-threat-detection`
  // * `event-threat-detection`
  // * `security-health-analytics`
  // * `vm-threat-detection`
  // * `web-security-scanner`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycentermanagement.googleapis.com/SecurityCenterService"
    }
  ];

  // Set to `true` to show only modules that are in scope. By default, all
  // modules are shown.
  bool show_eligible_modules_only = 2;
}

// Request message for
// [SecurityCenterManagement.ListSecurityCenterServices][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListSecurityCenterServices].
message ListSecurityCenterServicesRequest {
  // Required. The name of the parent to list Security Command Center services,
  // in one of the following formats:
  //
  // * `organizations/{organization}/locations/{location}`
  // * `folders/{folder}/locations/{location}`
  // * `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycentermanagement.googleapis.com/SecurityCenterService"
    }
  ];

  // Optional. The maximum number of results to return in a single response.
  // Default is 10, minimum is 1, maximum is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous request. Provide this
  // token to retrieve the next page of results.
  //
  // When paginating, the rest of the request must match the request that
  // generated the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Flag that, when set, is used to filter the module settings that are shown.
  // The default setting is that all modules are shown.
  bool show_eligible_modules_only = 4;
}

// Response message for
// [SecurityCenterManagement.ListSecurityCenterServices][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.ListSecurityCenterServices].
message ListSecurityCenterServicesResponse {
  // The list of services.
  repeated SecurityCenterService security_center_services = 1;

  // A pagination token. To retrieve the next page of results, call the method
  // again with this token.
  string next_page_token = 2;
}

// Request message for
// [SecurityCenterManagement.UpdateSecurityCenterService][google.cloud.securitycentermanagement.v1.SecurityCenterManagement.UpdateSecurityCenterService].
message UpdateSecurityCenterServiceRequest {
  // Required. The updated service.
  SecurityCenterService security_center_service = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The fields to update. Accepts the following values:
  //
  // * `intended_enablement_state`
  // * `modules`
  //
  // If omitted, then all eligible fields are updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. When set to `true`, the request will be validated (including IAM
  // checks), but no service will be updated. An `OK` response indicates that
  // the request is valid, while an error response indicates that the request is
  // invalid.
  //
  // If the request is valid, a subsequent request to update the service could
  // still fail for one of the following reasons:
  //
  // *  The state of your cloud resources changed; for example, you lost a
  //    required IAM permission
  // *  An error occurred during update of the service
  //
  // Defaults to `false`.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}
