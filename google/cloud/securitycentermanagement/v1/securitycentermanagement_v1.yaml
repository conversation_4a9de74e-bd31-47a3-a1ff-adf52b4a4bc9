type: google.api.Service
config_version: 3
name: securitycentermanagement.googleapis.com
title: Security Command Center Management API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.securitycentermanagement.v1.SecurityCenterManagement

documentation:
  summary: |-
    Management API for Security Command Center, a built-in security and risk
    management solution for Google Cloud. Use this API to programmatically
    update the settings and configuration of Security Command Center.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.securitycentermanagement.v1.SecurityCenterManagement.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=709980
  documentation_uri: https://cloud.google.com/security-command-center/docs/reference/security-center-management/rest
  api_short_name: securitycentermanagement
  github_label: 'api: securitycentermanagement'
  doc_tag_prefix: securitycentermanagement
  organization: CLOUD
  library_settings:
  - version: google.cloud.securitycentermanagement.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/securitycentermanagement/docs/reference/rpc
