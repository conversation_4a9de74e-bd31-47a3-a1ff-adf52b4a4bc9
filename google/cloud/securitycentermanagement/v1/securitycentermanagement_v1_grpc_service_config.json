{"methodConfig": [{"name": [{"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "ListEffectiveSecurityHealthAnalyticsCustomModules"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "ListDescendantSecurityHealthAnalyticsCustomModules"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "ListSecurityHealthAnalyticsCustomModules"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "GetSecurityHealthAnalyticsCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "GetEffectiveSecurityHealthAnalyticsCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "SimulateSecurityHealthAnalyticsCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "ListEffectiveEventThreatDetectionCustomModules"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "ListDescendantEventThreatDetectionCustomModules"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "ListEventThreatDetectionCustomModules"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "GetEventThreatDetectionCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "GetEffectiveEventThreatDetectionCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "ValidateEventThreatDetectionCustomModule"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "CreateSecurityHealthAnalyticsCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "UpdateSecurityHealthAnalyticsCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "DeleteSecurityHealthAnalyticsCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "CreateEventThreatDetectionCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "UpdateEventThreatDetectionCustomModule"}, {"service": "google.cloud.securitycentermanagement.v1.SecurityCenterManagement", "method": "DeleteEventThreatDetectionCustomModule"}], "timeout": "60s"}]}