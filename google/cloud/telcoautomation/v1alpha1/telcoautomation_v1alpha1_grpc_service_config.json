{"methodConfig": [{"name": [{"service": "google.cloud.telcoautomation.v1alpha1.TelcoAutomation", "method": "ComputeDeploymentStatus"}, {"service": "google.cloud.telcoautomation.v1alpha1.TelcoAutomation", "method": "ListBlueprints"}, {"service": "google.cloud.telcoautomation.v1alpha1.TelcoAutomation", "method": "GetOrchestrationCluster"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.telcoautomation.v1alpha1.TelcoAutomation", "method": "CreateDeployment"}, {"service": "google.cloud.telcoautomation.v1alpha1.TelcoAutomation", "method": "UpdateDeployment"}, {"service": "google.cloud.telcoautomation.v1alpha1.TelcoAutomation", "method": "ApplyDeployment"}, {"service": "google.cloud.telcoautomation.v1alpha1.TelcoAutomation", "method": "DeleteDeployment"}], "timeout": "60s"}]}