type: google.api.Service
config_version: 3
name: telcoautomation.googleapis.com
title: Telco Automation API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.telcoautomation.v1alpha1.TelcoAutomation
- name: google.longrunning.Operations

types:
- name: google.cloud.telcoautomation.v1alpha1.OperationMetadata

documentation:
  summary: APIs to automate management of cloud infrastructure for network functions.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1alpha1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1alpha1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1alpha1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1alpha1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1alpha1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1alpha1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.telcoautomation.v1alpha1.TelcoAutomation.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=190865&template=1161103
  documentation_uri: https://cloud.google.com/telecom-network-automation
  api_short_name: telcoautomation
  github_label: 'api: telcoautomation'
  doc_tag_prefix: telcoautomation
  organization: CLOUD
  library_settings:
  - version: google.cloud.telcoautomation.v1alpha1
    launch_stage: EARLY_ACCESS
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
