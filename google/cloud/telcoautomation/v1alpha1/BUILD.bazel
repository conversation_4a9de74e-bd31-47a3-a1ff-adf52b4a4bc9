# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "telcoautomation_proto",
    srcs = [
        "telcoautomation.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "telcoautomation_proto_with_info",
    deps = [
        ":telcoautomation_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

java_proto_library(
    name = "telcoautomation_java_proto",
    deps = [":telcoautomation_proto"],
)

java_grpc_library(
    name = "telcoautomation_java_grpc",
    srcs = [":telcoautomation_proto"],
    deps = [":telcoautomation_java_proto"],
)

java_gapic_library(
    name = "telcoautomation_java_gapic",
    srcs = [":telcoautomation_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "telcoautomation_v1alpha1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "telcoautomation_v1alpha1.yaml",
    test_deps = [
        ":telcoautomation_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":telcoautomation_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "telcoautomation_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.telcoautomation.v1alpha1.TelcoAutomationClientHttpJsonTest",
        "com.google.cloud.telcoautomation.v1alpha1.TelcoAutomationClientTest",
    ],
    runtime_deps = [":telcoautomation_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-telcoautomation-v1alpha1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":telcoautomation_java_gapic",
        ":telcoautomation_java_grpc",
        ":telcoautomation_java_proto",
        ":telcoautomation_proto",
    ],
)

go_proto_library(
    name = "telcoautomation_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/telcoautomation/apiv1alpha1/telcoautomationpb",
    protos = [":telcoautomation_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "telcoautomation_go_gapic",
    srcs = [":telcoautomation_proto_with_info"],
    grpc_service_config = "telcoautomation_v1alpha1_grpc_service_config.json",
    importpath = "cloud.google.com/go/telcoautomation/apiv1alpha1;telcoautomation",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "telcoautomation_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":telcoautomation_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-telcoautomation-v1alpha1-go",
    deps = [
        ":telcoautomation_go_gapic",
        ":telcoautomation_go_gapic_srcjar-metadata.srcjar",
        ":telcoautomation_go_gapic_srcjar-snippets.srcjar",
        ":telcoautomation_go_gapic_srcjar-test.srcjar",
        ":telcoautomation_go_proto",
    ],
)

py_gapic_library(
    name = "telcoautomation_py_gapic",
    srcs = [":telcoautomation_proto"],
    grpc_service_config = "telcoautomation_v1alpha1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "telcoautomation_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "telcoautomation_py_gapic_test",
    srcs = [
        "telcoautomation_py_gapic_pytest.py",
        "telcoautomation_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":telcoautomation_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "telcoautomation-v1alpha1-py",
    deps = [
        ":telcoautomation_py_gapic",
    ],
)

php_proto_library(
    name = "telcoautomation_php_proto",
    deps = [":telcoautomation_proto"],
)

php_gapic_library(
    name = "telcoautomation_php_gapic",
    srcs = [":telcoautomation_proto_with_info"],
    grpc_service_config = "telcoautomation_v1alpha1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "telcoautomation_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":telcoautomation_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-telcoautomation-v1alpha1-php",
    deps = [
        ":telcoautomation_php_gapic",
        ":telcoautomation_php_proto",
    ],
)

nodejs_gapic_library(
    name = "telcoautomation_nodejs_gapic",
    package_name = "@google-cloud/telcoautomation",
    src = ":telcoautomation_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "telcoautomation_v1alpha1_grpc_service_config.json",
    package = "google.cloud.telcoautomation.v1alpha1",
    rest_numeric_enums = True,
    service_yaml = "telcoautomation_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "telcoautomation-v1alpha1-nodejs",
    deps = [
        ":telcoautomation_nodejs_gapic",
        ":telcoautomation_proto",
    ],
)

ruby_proto_library(
    name = "telcoautomation_ruby_proto",
    deps = [":telcoautomation_proto"],
)

ruby_grpc_library(
    name = "telcoautomation_ruby_grpc",
    srcs = [":telcoautomation_proto"],
    deps = [":telcoautomation_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "telcoautomation_ruby_gapic",
    srcs = [":telcoautomation_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-telcoautomation-v1alpha1",
    ],
    grpc_service_config = "telcoautomation_v1alpha1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "telcoautomation_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":telcoautomation_ruby_grpc",
        ":telcoautomation_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-telcoautomation-v1alpha1-ruby",
    deps = [
        ":telcoautomation_ruby_gapic",
        ":telcoautomation_ruby_grpc",
        ":telcoautomation_ruby_proto",
    ],
)

csharp_proto_library(
    name = "telcoautomation_csharp_proto",
    extra_opts = [],
    deps = [":telcoautomation_proto"],
)

csharp_grpc_library(
    name = "telcoautomation_csharp_grpc",
    srcs = [":telcoautomation_proto"],
    deps = [":telcoautomation_csharp_proto"],
)

csharp_gapic_library(
    name = "telcoautomation_csharp_gapic",
    srcs = [":telcoautomation_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "telcoautomation_v1alpha1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "telcoautomation_v1alpha1.yaml",
    deps = [
        ":telcoautomation_csharp_grpc",
        ":telcoautomation_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-telcoautomation-v1alpha1-csharp",
    deps = [
        ":telcoautomation_csharp_gapic",
        ":telcoautomation_csharp_grpc",
        ":telcoautomation_csharp_proto",
    ],
)

cc_proto_library(
    name = "telcoautomation_cc_proto",
    deps = [":telcoautomation_proto"],
)

cc_grpc_library(
    name = "telcoautomation_cc_grpc",
    srcs = [":telcoautomation_proto"],
    grpc_only = True,
    deps = [":telcoautomation_cc_proto"],
)
