// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.telcoautomation.v1alpha1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.TelcoAutomation.V1";
option go_package = "cloud.google.com/go/telcoautomation/apiv1alpha1/telcoautomationpb;telcoautomationpb";
option java_multiple_files = true;
option java_outer_classname = "TelcoautomationProto";
option java_package = "com.google.cloud.telcoautomation.v1alpha1";
option php_namespace = "Google\\Cloud\\TelcoAutomation\\V1";
option ruby_package = "Google::Cloud::TelcoAutomation::V1";

// TelcoAutomation Service manages the control plane cluster a.k.a.
// Orchestration Cluster (GKE cluster with config controller) of TNA. It also
// exposes blueprint APIs which manages the lifecycle of blueprints that control
// the infrastructure setup (e.g GDCE clusters) and deployment of network
// functions.
service TelcoAutomation {
  option (google.api.default_host) = "telcoautomation.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists OrchestrationClusters in a given project and location.
  rpc ListOrchestrationClusters(ListOrchestrationClustersRequest)
      returns (ListOrchestrationClustersResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*}/orchestrationClusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single OrchestrationCluster.
  rpc GetOrchestrationCluster(GetOrchestrationClusterRequest)
      returns (OrchestrationCluster) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new OrchestrationCluster in a given project and location.
  rpc CreateOrchestrationCluster(CreateOrchestrationClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*}/orchestrationClusters"
      body: "orchestration_cluster"
    };
    option (google.api.method_signature) =
        "parent,orchestration_cluster,orchestration_cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "OrchestrationCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single OrchestrationCluster.
  rpc DeleteOrchestrationCluster(DeleteOrchestrationClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists EdgeSlms in a given project and location.
  rpc ListEdgeSlms(ListEdgeSlmsRequest) returns (ListEdgeSlmsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*}/edgeSlms"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single EdgeSlm.
  rpc GetEdgeSlm(GetEdgeSlmRequest) returns (EdgeSlm) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/edgeSlms/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new EdgeSlm in a given project and location.
  rpc CreateEdgeSlm(CreateEdgeSlmRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*}/edgeSlms"
      body: "edge_slm"
    };
    option (google.api.method_signature) = "parent,edge_slm,edge_slm_id";
    option (google.longrunning.operation_info) = {
      response_type: "EdgeSlm"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single EdgeSlm.
  rpc DeleteEdgeSlm(DeleteEdgeSlmRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/edgeSlms/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a blueprint.
  rpc CreateBlueprint(CreateBlueprintRequest) returns (Blueprint) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/orchestrationClusters/*}/blueprints"
      body: "blueprint"
    };
    option (google.api.method_signature) = "parent,blueprint,blueprint_id";
  }

  // Updates a blueprint.
  rpc UpdateBlueprint(UpdateBlueprintRequest) returns (Blueprint) {
    option (google.api.http) = {
      patch: "/v1alpha1/{blueprint.name=projects/*/locations/*/orchestrationClusters/*/blueprints/*}"
      body: "blueprint"
    };
    option (google.api.method_signature) = "blueprint,update_mask";
  }

  // Returns the requested blueprint.
  rpc GetBlueprint(GetBlueprintRequest) returns (Blueprint) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/blueprints/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a blueprint and all its revisions.
  rpc DeleteBlueprint(DeleteBlueprintRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/blueprints/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List all blueprints.
  rpc ListBlueprints(ListBlueprintsRequest) returns (ListBlueprintsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/orchestrationClusters/*}/blueprints"
    };
    option (google.api.method_signature) = "parent";
  }

  // Approves a blueprint and commits a new revision.
  rpc ApproveBlueprint(ApproveBlueprintRequest) returns (Blueprint) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/blueprints/*}:approve"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Proposes a blueprint for approval of changes.
  rpc ProposeBlueprint(ProposeBlueprintRequest) returns (Blueprint) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/blueprints/*}:propose"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Rejects a blueprint revision proposal and flips it back to Draft state.
  rpc RejectBlueprint(RejectBlueprintRequest) returns (Blueprint) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/blueprints/*}:reject"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // List blueprint revisions of a given blueprint.
  rpc ListBlueprintRevisions(ListBlueprintRevisionsRequest)
      returns (ListBlueprintRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/blueprints/*}:listRevisions"
    };
    option (google.api.method_signature) = "name";
  }

  // Searches across blueprint revisions.
  rpc SearchBlueprintRevisions(SearchBlueprintRevisionsRequest)
      returns (SearchBlueprintRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/orchestrationClusters/*}/blueprints:searchRevisions"
    };
    option (google.api.method_signature) = "parent,query";
  }

  // Searches across deployment revisions.
  rpc SearchDeploymentRevisions(SearchDeploymentRevisionsRequest)
      returns (SearchDeploymentRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/orchestrationClusters/*}/deployments:searchRevisions"
    };
    option (google.api.method_signature) = "parent,query";
  }

  // Discards the changes in a blueprint and reverts the blueprint to the last
  // approved blueprint revision. No changes take place if a blueprint does not
  // have revisions.
  rpc DiscardBlueprintChanges(DiscardBlueprintChangesRequest)
      returns (DiscardBlueprintChangesResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/blueprints/*}:discard"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the blueprints in TNA's public catalog. Default page size = 20,
  // Max Page Size = 100.
  rpc ListPublicBlueprints(ListPublicBlueprintsRequest)
      returns (ListPublicBlueprintsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*}/publicBlueprints"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the requested public blueprint.
  rpc GetPublicBlueprint(GetPublicBlueprintRequest) returns (PublicBlueprint) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/publicBlueprints/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a deployment.
  rpc CreateDeployment(CreateDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/orchestrationClusters/*}/deployments"
      body: "deployment"
    };
    option (google.api.method_signature) = "parent,deployment,deployment_id";
  }

  // Updates a deployment.
  rpc UpdateDeployment(UpdateDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      patch: "/v1alpha1/{deployment.name=projects/*/locations/*/orchestrationClusters/*/deployments/*}"
      body: "deployment"
    };
    option (google.api.method_signature) = "deployment,update_mask";
  }

  // Returns the requested deployment.
  rpc GetDeployment(GetDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Removes the deployment by marking it as DELETING. Post which deployment and
  // it's revisions gets deleted.
  rpc RemoveDeployment(RemoveDeploymentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*}:remove"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // List all deployments.
  rpc ListDeployments(ListDeploymentsRequest)
      returns (ListDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/orchestrationClusters/*}/deployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // List deployment revisions of a given deployment.
  rpc ListDeploymentRevisions(ListDeploymentRevisionsRequest)
      returns (ListDeploymentRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*}:listRevisions"
    };
    option (google.api.method_signature) = "name";
  }

  // Discards the changes in a deployment and reverts the deployment to the last
  // approved deployment revision. No changes take place if a deployment does
  // not have revisions.
  rpc DiscardDeploymentChanges(DiscardDeploymentChangesRequest)
      returns (DiscardDeploymentChangesResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*}:discard"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Applies the deployment's YAML files to the parent orchestration cluster.
  rpc ApplyDeployment(ApplyDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*}:apply"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the requested deployment status.
  rpc ComputeDeploymentStatus(ComputeDeploymentStatusRequest)
      returns (ComputeDeploymentStatusResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*}:computeDeploymentStatus"
    };
    option (google.api.method_signature) = "name";
  }

  // Rollback the active deployment to the given past approved deployment
  // revision.
  rpc RollbackDeployment(RollbackDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*}:rollback"
      body: "*"
    };
    option (google.api.method_signature) = "name,revision_id";
  }

  // Returns the requested hydrated deployment.
  rpc GetHydratedDeployment(GetHydratedDeploymentRequest)
      returns (HydratedDeployment) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*/hydratedDeployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List all hydrated deployments present under a deployment.
  rpc ListHydratedDeployments(ListHydratedDeploymentsRequest)
      returns (ListHydratedDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/orchestrationClusters/*/deployments/*}/hydratedDeployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a hydrated deployment.
  rpc UpdateHydratedDeployment(UpdateHydratedDeploymentRequest)
      returns (HydratedDeployment) {
    option (google.api.http) = {
      patch: "/v1alpha1/{hydrated_deployment.name=projects/*/locations/*/orchestrationClusters/*/deployments/*/hydratedDeployments/*}"
      body: "hydrated_deployment"
    };
    option (google.api.method_signature) = "hydrated_deployment,update_mask";
  }

  // Applies a hydrated deployment to a workload cluster.
  rpc ApplyHydratedDeployment(ApplyHydratedDeploymentRequest)
      returns (HydratedDeployment) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/orchestrationClusters/*/deployments/*/hydratedDeployments/*}:apply"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// BlueprintView defines the type of view of the blueprint.
enum BlueprintView {
  // Unspecified enum value.
  BLUEPRINT_VIEW_UNSPECIFIED = 0;

  // View which only contains metadata.
  BLUEPRINT_VIEW_BASIC = 1;

  // View which contains metadata and files it encapsulates.
  BLUEPRINT_VIEW_FULL = 2;
}

// DeploymentView defines the type of view of the deployment.
enum DeploymentView {
  // Unspecified enum value.
  DEPLOYMENT_VIEW_UNSPECIFIED = 0;

  // View which only contains metadata.
  DEPLOYMENT_VIEW_BASIC = 1;

  // View which contains metadata and files it encapsulates.
  DEPLOYMENT_VIEW_FULL = 2;
}

// Represent type of CR.
enum ResourceType {
  // Unspecified resource type.
  RESOURCE_TYPE_UNSPECIFIED = 0;

  // User specified NF Deploy CR.
  NF_DEPLOY_RESOURCE = 1;

  // CRs that are part of a blueprint.
  DEPLOYMENT_RESOURCE = 2;
}

// Status of an entity (resource, deployment).
enum Status {
  // Unknown state.
  STATUS_UNSPECIFIED = 0;

  // Under progress.
  STATUS_IN_PROGRESS = 1;

  // Running and ready to serve traffic.
  STATUS_ACTIVE = 2;

  // Failed or stalled.
  STATUS_FAILED = 3;

  // Delete in progress.
  STATUS_DELETING = 4;

  // Deleted deployment.
  STATUS_DELETED = 5;

  // NFDeploy specific status. Peering in progress.
  STATUS_PEERING = 10;

  // K8s objects such as NetworkAttachmentDefinition don't have a defined
  // status.
  STATUS_NOT_APPLICABLE = 11;
}

// DeploymentLevel of a blueprint signifies where the blueprint will be
// applied.
enum DeploymentLevel {
  // Default unspecified deployment level.
  DEPLOYMENT_LEVEL_UNSPECIFIED = 0;

  // Blueprints at HYDRATION level cannot be used to create a Deployment
  // (A user cannot manually initate deployment of these blueprints on
  // orchestration or workload cluster).
  // These blueprints stay in a user's private catalog and are configured and
  // deployed by TNA automation.
  HYDRATION = 1;

  // Blueprints at SINGLE_DEPLOYMENT level can be
  // a) Modified in private catalog.
  // b) Used to create a deployment on orchestration cluster by the user, once
  // approved.
  SINGLE_DEPLOYMENT = 2;

  // Blueprints at MULTI_DEPLOYMENT level can be
  // a) Modified in private catalog.
  // b) Used to create a deployment on orchestration cluster which will create
  // further hydrated deployments.
  MULTI_DEPLOYMENT = 3;

  // Blueprints at WORKLOAD_CLUSTER_DEPLOYMENT level can be
  // a) Modified in private catalog.
  // b) Used to create a deployment on workload cluster by the user, once
  // approved.
  WORKLOAD_CLUSTER_DEPLOYMENT = 4;
}

// Orchestration cluster represents a GKE cluster with config controller and
// TNA specific components installed on it.
message OrchestrationCluster {
  option (google.api.resource) = {
    type: "telcoautomation.googleapis.com/OrchestrationCluster"
    pattern: "projects/{project}/locations/{location}/orchestrationClusters/{orchestration_cluster}"
  };

  // Possible states that the Orchestration Cluster can be in.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // OrchestrationCluster is being created.
    CREATING = 1;

    // OrchestrationCluster has been created and is ready for use.
    ACTIVE = 2;

    // OrchestrationCluster is being deleted.
    DELETING = 3;

    // OrchestrationCluster encountered an error and is in an indeterministic
    // state. User can still initiate a delete operation on this state.
    FAILED = 4;
  }

  // Name of the orchestration cluster. The name of orchestration cluster cannot
  // be more than 24 characters.
  string name = 1;

  // Management configuration of the underlying GKE cluster.
  ManagementConfig management_config = 5;

  // Output only. [Output only] Create time stamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update time stamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // Output only. Provides the TNA version installed on the cluster.
  string tna_version = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the Orchestration Cluster.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// EdgeSlm represents an SLM instance which manages the lifecycle of edge
// components installed on Workload clusters managed by an Orchestration
// Cluster.
message EdgeSlm {
  option (google.api.resource) = {
    type: "telcoautomation.googleapis.com/EdgeSlm"
    pattern: "projects/{project}/locations/{location}/edgeSlms/{edge_slm}"
    plural: "edgeSlms"
    singular: "edgeSlm"
  };

  // Possible states of the resource.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // EdgeSlm is being created.
    CREATING = 1;

    // EdgeSlm has been created and is ready for use.
    ACTIVE = 2;

    // EdgeSlm is being deleted.
    DELETING = 3;

    // EdgeSlm encountered an error and is in an indeterministic
    // state. User can still initiate a delete operation on this state.
    FAILED = 4;
  }

  // Workload clusters supported by TNA. New values will be added to the enum
  // list as TNA adds supports for new workload clusters in future.
  enum WorkloadClusterType {
    // Unspecified workload cluster.
    WORKLOAD_CLUSTER_TYPE_UNSPECIFIED = 0;

    // Workload cluster is a GDCE cluster.
    GDCE = 1;

    // Workload cluster is a GKE cluster.
    GKE = 2;
  }

  // Name of the EdgeSlm resource.
  string name = 1;

  // Immutable. Reference to the orchestration cluster on which templates for
  // this resources will be applied. This should be of format
  // projects/{project}/locations/{location}/orchestrationClusters/{orchestration_cluster}.
  string orchestration_cluster = 5 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/OrchestrationCluster"
    }
  ];

  // Output only. [Output only] Create time stamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update time stamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels as key value pairs. The key and value should contain
  // characters which are UTF-8 compliant and less than 50 characters.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Provides the active TNA version for this resource.
  string tna_version = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the EdgeSlm resource.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Type of workload cluster for which an EdgeSLM resource is
  // created.
  WorkloadClusterType workload_cluster_type = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// A Blueprint contains a collection of kubernetes resources in the form of
// YAML files. The file contents of a blueprint are collectively known as
// package. A blueprint can be
// a) imported from TNA's public catalog
// b) modified as per a user's need
// c) proposed and approved.
// On approval, a revision of blueprint is created which can be used to
// create a deployment on Orchestration or Workload Cluster.
message Blueprint {
  option (google.api.resource) = {
    type: "telcoautomation.googleapis.com/Blueprint"
    pattern: "projects/{project}/locations/{location}/orchestrationClusters/{orchestration_cluster}/blueprints/{blueprint}"
    plural: "blueprints"
    singular: "blueprint"
  };

  // Approval state indicates the state of a Blueprint in its approval
  // lifecycle.
  enum ApprovalState {
    // Unspecified state.
    APPROVAL_STATE_UNSPECIFIED = 0;

    // A blueprint starts in DRAFT state once it is created. All edits are made
    // to the blueprint in DRAFT state.
    DRAFT = 1;

    // When the edits are ready for review, blueprint can be proposed and moves
    // to PROPOSED state. Edits cannot be made to a blueprint in PROPOSED state.
    PROPOSED = 2;

    // When a proposed blueprint is approved, it moves to APPROVED state. A new
    // revision is committed. The latest committed revision can be used to
    // create a deployment on Orchestration or Workload Cluster. Edits to an
    // APPROVED blueprint changes its state back to DRAFT. The last committed
    // revision of a blueprint represents its latest APPROVED state.
    APPROVED = 3;
  }

  // The name of the blueprint. If unspecified, the name will be autogenerated
  // from server side. Name of the blueprint must not contain `@` character.
  string name = 1;

  // Output only. Immutable. The revision ID of the blueprint.
  // A new revision is committed whenever a blueprint is approved.
  string revision_id = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Required. Immutable. The public blueprint ID from which this blueprint was
  // created.
  string source_blueprint = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. The timestamp that the revision was created.
  google.protobuf.Timestamp revision_create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Approval state of the blueprint (DRAFT, PROPOSED, APPROVED)
  ApprovalState approval_state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Human readable name of a Blueprint.
  string display_name = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Name of the repository where the blueprint files are stored.
  string repository = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Files present in a blueprint.
  // When invoking UpdateBlueprint API, only the modified files should be
  // included in this. Files that are not included in the update of a blueprint
  // will not be changed.
  repeated File files = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels are key-value attributes that can be set on a blueprint
  // resource by the user.
  map<string, string> labels = 10 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Blueprint creation time.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the blueprint was updated.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source provider is the author of a public blueprint, from
  // which this blueprint is created.
  string source_provider = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. DeploymentLevel of a blueprint signifies where the blueprint
  // will be applied. e.g. [HYDRATION, SINGLE_DEPLOYMENT, MULTI_DEPLOYMENT]
  DeploymentLevel deployment_level = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates if the deployment created from this blueprint can be
  // rolled back.
  bool rollback_support = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A Blueprint contains a collection of kubernetes resources in the form of
// YAML files. The file contents of a blueprint are collectively known as
// package.
// Public blueprint is a TNA provided blueprint that in present in TNA's public
// catalog. A user can copy the public blueprint to their private catalog for
// further modifications.
message PublicBlueprint {
  option (google.api.resource) = {
    type: "telcoautomation.googleapis.com/PublicBlueprint"
    pattern: "projects/{project}/locations/{location}/publicBlueprints/{public_lueprint}"
    plural: "publicBlueprints"
    singular: "publicBlueprint"
  };

  // Name of the public blueprint.
  string name = 1;

  // The display name of the public blueprint.
  string display_name = 2;

  // The description of the public blueprint.
  string description = 3;

  // DeploymentLevel of a blueprint signifies where the blueprint will be
  // applied. e.g. [HYDRATION, SINGLE_DEPLOYMENT, MULTI_DEPLOYMENT]
  DeploymentLevel deployment_level = 4;

  // Source provider is the author of a public blueprint. e.g. Google, vendors
  string source_provider = 5;

  // Output only. Indicates if the deployment created from this blueprint can be
  // rolled back.
  bool rollback_support = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Deployment contains a collection of YAML files (This collection is also known
// as package) that can to applied on an orchestration cluster (GKE cluster with
// TNA addons) or a workload cluster.
message Deployment {
  option (google.api.resource) = {
    type: "telcoautomation.googleapis.com/Deployment"
    pattern: "projects/{project}/locations/{location}/orchestrationClusters/{orchestration_cluster}/deployments/{deployment}"
    plural: "deployments"
    singular: "deployment"
  };

  // State defines which state the current deployment is in.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // A deployment starts in DRAFT state. All edits are made in DRAFT state. A
    // deployment opened for editing after applying will be in draft state,
    // while its prevision revision will be its current applied version.
    DRAFT = 1;

    // This state means that the contents (YAML files containing kubernetes
    // resources) of the deployment have been applied to an Orchestration or
    // Workload Cluster. A revision is created when a deployment is applied.
    // This revision will represent the latest view of what is applied on the
    // cluster until the deployment is modified and applied again, which will
    // create a new revision.
    APPLIED = 2;

    // A deployment in DELETING state has been marked for deletion. Its
    // deletion status can be queried using `ComputeDeploymentStatus` API. No
    // updates are allowed to a deployment in DELETING state.
    DELETING = 3;
  }

  // The name of the deployment.
  string name = 1;

  // Output only. Immutable. The revision ID of the deployment.
  // A new revision is committed whenever a change in deployment is applied.
  string revision_id = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Required. The blueprint revision from which this deployment was created.
  string source_blueprint_revision = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. The timestamp that the revision was created.
  google.protobuf.Timestamp revision_create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the deployment (DRAFT, APPLIED, DELETING).
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Human readable name of a Deployment.
  string display_name = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Name of the repository where the deployment package files are
  // stored.
  string repository = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Files present in a deployment.
  // When invoking UpdateDeployment API, only the modified files should be
  // included in this. Files that are not included in the update of a deployment
  // will not be changed.
  repeated File files = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels are key-value attributes that can be set on a deployment
  // resource by the user.
  map<string, string> labels = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Deployment creation time.
  google.protobuf.Timestamp create_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the deployment was updated.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Source provider is the author of a public blueprint, from
  // which this deployment is created.
  string source_provider = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. The WorkloadCluster on which to create the Deployment.
  // This field should only be passed when the deployment_level of the source
  // blueprint specifies deployments on workload clusters e.g.
  // WORKLOAD_CLUSTER_DEPLOYMENT.
  string workload_cluster = 13 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Output only. Attributes to where the deployment can inflict changes. The
  // value can only be [SINGLE_DEPLOYMENT, MULTI_DEPLOYMENT].
  DeploymentLevel deployment_level = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates if the deployment can be rolled back, exported from
  // public blueprint.
  bool rollback_support = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A collection of kubernetes yaml files which are deployed on a Workload
// Cluster. Hydrated Deployments are created by TNA intent based automation.
message HydratedDeployment {
  option (google.api.resource) = {
    type: "telcoautomation.googleapis.com/HydratedDeployment"
    pattern: "projects/{project}/locations/{location}/orchestrationClusters/{orchestration_cluster}/deployments/{deployment}/hydratedDeployments/{hydrated_deployment}"
    plural: "hydratedDeployments"
    singular: "hydratedDeployment"
  };

  // State defines which state the current hydrated deployment is in.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // A hydrated deployment starts in DRAFT state. All edits are made in DRAFT
    // state.
    DRAFT = 1;

    // When the edit is applied, the hydrated deployment moves to APPLIED
    // state. No changes can be made once a hydrated deployment is applied.
    APPLIED = 2;
  }

  // Output only. The name of the hydrated deployment.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the hydrated deployment (DRAFT, APPLIED).
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. File contents of a hydrated deployment.
  // When invoking UpdateHydratedBlueprint API, only the modified files should
  // be included in this. Files that are not included in the update of a
  // hydrated deployment will not be changed.
  repeated File files = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. WorkloadCluster identifies which workload cluster will the
  // hydrated deployment will be deployed on.
  string workload_cluster = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of OrchestrationClusters.
message ListOrchestrationClustersRequest {
  // Required. Parent value for ListOrchestrationClustersRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/OrchestrationCluster"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Message for response to listing OrchestrationClusters.
message ListOrchestrationClustersResponse {
  // The list of OrchestrationCluster
  repeated OrchestrationCluster orchestration_clusters = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a OrchestrationCluster.
message GetOrchestrationClusterRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/OrchestrationCluster"
    }
  ];
}

// Message for creating a OrchestrationCluster.
message CreateOrchestrationClusterRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/OrchestrationCluster"
    }
  ];

  // Required. Id of the requesting object
  // If auto-generating Id server-side, remove this field and
  // orchestration_cluster_id from the method_signature of Create RPC
  string orchestration_cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  OrchestrationCluster orchestration_cluster = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a OrchestrationCluster.
message DeleteOrchestrationClusterRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/OrchestrationCluster"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of EdgeSlms
message ListEdgeSlmsRequest {
  // Required. Parent value for ListEdgeSlmsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/EdgeSlm"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing EdgeSlms.
message ListEdgeSlmsResponse {
  // The list of EdgeSlm
  repeated EdgeSlm edge_slms = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a EdgeSlm.
message GetEdgeSlmRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/EdgeSlm"
    }
  ];
}

// Message for creating a EdgeSlm.
message CreateEdgeSlmRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/EdgeSlm"
    }
  ];

  // Required. Id of the requesting object
  // If auto-generating Id server-side, remove this field and
  // edge_slm_id from the method_signature of Create RPC
  string edge_slm_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  EdgeSlm edge_slm = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a EdgeSlm.
message DeleteEdgeSlmRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/EdgeSlm"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request object for `CreateBlueprint`.
message CreateBlueprintRequest {
  // Required. The name of parent resource.
  // Format should be -
  // "projects/{project_id}/locations/{location_name}/orchestrationClusters/{orchestration_cluster}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];

  // Optional. The name of the blueprint.
  string blueprint_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The `Blueprint` to create.
  Blueprint blueprint = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request object for `UpdateBlueprint`.
message UpdateBlueprintRequest {
  // Required. The `blueprint` to update.
  Blueprint blueprint = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Update mask is used to specify the fields to be overwritten in
  // the `blueprint` resource by the update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request object for `GetBlueprint`.
message GetBlueprintRequest {
  // Required. The name of the blueprint.
  // Case 1: If the name provided in the request is
  // {blueprint_id}@{revision_id}, then the revision with revision_id will be
  // returned. Case 2: If the name provided in the request is {blueprint}, then
  // the current state of the blueprint is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];

  // Optional. Defines the type of view of the blueprint.
  // When field is not present BLUEPRINT_VIEW_BASIC is considered as default.
  BlueprintView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request object for `DeleteBlueprint`.
message DeleteBlueprintRequest {
  // Required. The name of blueprint to delete.
  // Blueprint name should be in the format {blueprint_id}, if
  // {blueprint_id}@{revision_id} is passed then the API throws invalid
  // argument.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];
}

// Request object for `ListBlueprints`.
message ListBlueprintsRequest {
  // Required. The name of parent orchestration cluster resource.
  // Format should be -
  // "projects/{project_id}/locations/{location_name}/orchestrationClusters/{orchestration_cluster}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];

  // Optional. Filtering only supports equality on blueprint state.
  // It should be in the form: "state = DRAFT". `OR` operator can be used to
  // get response for multiple states. e.g. "state = DRAFT OR state = PROPOSED".
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of blueprints to return per page.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token, received from a previous ListBlueprints call.
  // It can be provided to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for `ListBlueprints`.
message ListBlueprintsResponse {
  // The list of requested blueprints.
  repeated Blueprint blueprints = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request object for `ApproveBlueprint`.
message ApproveBlueprintRequest {
  // Required. The name of the blueprint to approve. The blueprint must be in
  // Proposed state. A new revision is committed on approval.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];
}

// Request object for `ProposeBlueprint`.
message ProposeBlueprintRequest {
  // Required. The name of the blueprint being proposed.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];
}

// Request object for `RejectBlueprint`.
message RejectBlueprintRequest {
  // Required. The name of the blueprint being rejected.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];
}

// Request object for `ListBlueprintRevisions`.
message ListBlueprintRevisionsRequest {
  // Required. The name of the blueprint to list revisions for.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];

  // The maximum number of revisions to return per page.
  int32 page_size = 2;

  // The page token, received from a previous ListBlueprintRevisions call
  // It can be provided to retrieve the subsequent page.
  string page_token = 3;
}

// Response object for `ListBlueprintRevisions`.
message ListBlueprintRevisionsResponse {
  // The revisions of the blueprint.
  repeated Blueprint blueprints = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request object for `SearchBlueprintRevisions`.
message SearchBlueprintRevisionsRequest {
  // Required. The name of parent orchestration cluster resource.
  // Format should be -
  // "projects/{project_id}/locations/{location_name}/orchestrationClusters/{orchestration_cluster}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];

  // Required. Supported queries:
  // 1. ""                       : Lists all revisions across all blueprints.
  // 2. "latest=true"            : Lists latest revisions across all blueprints.
  // 3. "name={name}"            : Lists all revisions of blueprint with name
  // {name}.
  // 4. "name={name} latest=true": Lists latest revision of blueprint with name
  // {name}
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of blueprints revisions to return per page.
  // max page size = 100, default page size = 20.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token, received from a previous search call.
  // It can be provided to retrieve the subsequent page.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for `SearchBlueprintRevisions`.
message SearchBlueprintRevisionsResponse {
  // The list of requested blueprint revisions.
  repeated Blueprint blueprints = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request object for `DiscardBlueprintChanges`.
message DiscardBlueprintChangesRequest {
  // Required. The name of the blueprint of which changes are being discarded.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];
}

// Response object for `DiscardBlueprintChanges`.
message DiscardBlueprintChangesResponse {}

// Request object for `ListPublicBlueprints`.
message ListPublicBlueprintsRequest {
  // Required. Parent value of public blueprint.
  // Format should be -
  // "projects/{project_id}/locations/{location_name}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/PublicBlueprint"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for `ListPublicBlueprints`.
message ListPublicBlueprintsResponse {
  // The list of public blueprints to return.
  repeated PublicBlueprint public_blueprints = 1;

  // Output only. A token identifying a page of results the server should
  // return.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request object for `GetPublicBlueprint`.
message GetPublicBlueprintRequest {
  // Required. The name of the public blueprint.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/PublicBlueprint"
    }
  ];
}

// Request object for `CreateDeployment`.
message CreateDeploymentRequest {
  // Required. The name of parent resource.
  // Format should be -
  // "projects/{project_id}/locations/{location_name}/orchestrationClusters/{orchestration_cluster}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/Deployment"
    }
  ];

  // Optional. The name of the deployment.
  string deployment_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The `Deployment` to create.
  Deployment deployment = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request object for `UpdateDeployment`.
message UpdateDeploymentRequest {
  // Required. The `deployment` to update.
  Deployment deployment = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Update mask is used to specify the fields to be overwritten in
  // the `deployment` resource by the update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request object for `GetDeployment`.
message GetDeploymentRequest {
  // Required. The name of the deployment.
  // Case 1: If the name provided in the request is
  // {deployment_id}@{revision_id}, then the revision with revision_id will be
  // returned.
  // Case 2: If the name provided in the request is {deployment}, then
  // the current state of the deployment is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Deployment"
    }
  ];

  // Optional. Defines the type of view of the deployment.
  // When field is not present VIEW_BASIC is considered as default.
  DeploymentView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request object for `RemoveDeployment`.
message RemoveDeploymentRequest {
  // Required. The name of deployment to initiate delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Deployment"
    }
  ];
}

// Request object for `ListDeployments`.
message ListDeploymentsRequest {
  // Required. The name of parent orchestration cluster resource.
  // Format should be -
  // "projects/{project_id}/locations/{location_name}/orchestrationClusters/{orchestration_cluster}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/Deployment"
    }
  ];

  // Optional. Filtering only supports equality on deployment state.
  // It should be in the form: "state = DRAFT". `OR` operator can be used to
  // get response for multiple states. e.g. "state = DRAFT OR state = APPLIED".
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of deployments to return per page.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token, received from a previous ListDeployments call.
  // It can be provided to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for `ListDeployments`.
message ListDeploymentsResponse {
  // The list of requested deployments.
  repeated Deployment deployments = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request for listing all revisions of a deployment.
message ListDeploymentRevisionsRequest {
  // Required. The name of the deployment to list revisions for.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Deployment"
    }
  ];

  // Optional. The maximum number of revisions to return per page.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token, received from a previous ListDeploymentRevisions
  // call Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// List of deployment revisions for a given deployment.
message ListDeploymentRevisionsResponse {
  // The revisions of the deployment.
  repeated Deployment deployments = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request object for `SearchDeploymentRevisions`.
message SearchDeploymentRevisionsRequest {
  // Required. The name of parent orchestration cluster resource.
  // Format should be -
  // "projects/{project_id}/locations/{location_name}/orchestrationClusters/{orchestration_cluster}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/Blueprint"
    }
  ];

  // Required. Supported queries:
  // 1. ""                       : Lists all revisions across all deployments.
  // 2. "latest=true"            : Lists latest revisions across all
  // deployments.
  // 3. "name={name}"            : Lists all revisions of deployment with name
  // {name}.
  // 4. "name={name} latest=true": Lists latest revision of deployment with name
  // {name}
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of deployment revisions to return per page.
  // max page size = 100, default page size = 20.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token, received from a previous search call.
  // It can be provided to retrieve the subsequent page.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for `SearchDeploymentRevisions`.
message SearchDeploymentRevisionsResponse {
  // The list of requested deployment revisions.
  repeated Deployment deployments = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request object for `DiscardDeploymentChanges`.
message DiscardDeploymentChangesRequest {
  // Required. The name of the deployment of which changes are being discarded.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Deployment"
    }
  ];
}

// Response object for `DiscardDeploymentChanges`.
message DiscardDeploymentChangesResponse {}

// Request object for `ApplyDeployment`. The resources in given deployment
// gets applied to Orchestration Cluster. A new revision is created when a
// deployment is applied.
message ApplyDeploymentRequest {
  // Required. The name of the deployment to apply to orchestration cluster.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Deployment"
    }
  ];
}

// Request object for `ComputeDeploymentStatus`.
message ComputeDeploymentStatusRequest {
  // Required. The name of the deployment without revisionID.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Deployment"
    }
  ];
}

// Response object for `ComputeDeploymentStatus`.
message ComputeDeploymentStatusResponse {
  // The name of the deployment.
  string name = 1;

  // Output only. Aggregated status of a deployment.
  Status aggregated_status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource level status details in deployments.
  repeated ResourceStatus resource_statuses = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request object for `RollbackDeployment`.
message RollbackDeploymentRequest {
  // Required. Name of the deployment.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/Deployment"
    }
  ];

  // Required. The revision id of deployment to roll back to.
  string revision_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request object for `GetHydratedDeployment`.
message GetHydratedDeploymentRequest {
  // Required. Name of the hydrated deployment.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/HydratedDeployment"
    }
  ];
}

// Request object for `ListHydratedDeployments`.
message ListHydratedDeploymentsRequest {
  // Required. The deployment managing the hydrated deployments.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "telcoautomation.googleapis.com/HydratedDeployment"
    }
  ];

  // Optional. The maximum number of hydrated deployments to return. The service
  // may return fewer than this value. If unspecified, at most 50 hydrated
  // deployments will be returned. The maximum value is 1000. Values above 1000
  // will be set to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token, received from a previous ListHydratedDeployments
  // call. Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for `ListHydratedDeployments`.
message ListHydratedDeploymentsResponse {
  // The list of hydrated deployments.
  repeated HydratedDeployment hydrated_deployments = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request object for `UpdateHydratedDeployment`.
message UpdateHydratedDeploymentRequest {
  // Required. The hydrated deployment to update.
  HydratedDeployment hydrated_deployment = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update. Update mask supports a special
  // value `*` which fully replaces (equivalent to PUT) the resource provided.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for applying a hydrated deployment.
message ApplyHydratedDeploymentRequest {
  // Required. The name of the hydrated deployment to apply.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "telcoautomation.googleapis.com/HydratedDeployment"
    }
  ];
}

// Configuration of the cluster management
message ManagementConfig {
  // The configuration can be one of StandardManagementConfig
  // and FullManagementConfig
  oneof oneof_config {
    // Configuration of the standard (GKE) cluster management
    StandardManagementConfig standard_management_config = 1;

    // Configuration of the full (Autopilot) cluster management. Full cluster
    // management is a preview feature.
    FullManagementConfig full_management_config = 2;
  }
}

// Configuration of the standard (GKE) cluster management.
message StandardManagementConfig {
  // Optional. Name of the VPC Network to put the GKE cluster and nodes in. The
  // VPC will be created if it doesn't exist.
  string network = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the subnet that the interface will be part of. Network
  // key must be specified and the subnet must be a subnetwork of the specified
  // network.
  string subnet = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The /28 network that the masters will use. It should be free
  // within the network.
  string master_ipv4_cidr_block = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The IP address range for the cluster pod IPs. Set to blank to
  // have a range chosen with the default size. Set to /netmask (e.g. /14) to
  // have a range chosen with a specific netmask. Set to a CIDR notation
  // (e.g. *********/14) from the RFC-1918 private networks (e.g. 10.0.0.0/8,
  // **********/12, ***********/16) to pick a specific range to use.
  string cluster_cidr_block = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The IP address range for the cluster service IPs. Set to blank to
  // have a range chosen with the default size. Set to /netmask (e.g. /14) to
  // have a range chosen with a specific netmask. Set to a CIDR notation (e.g.
  // *********/14) from the RFC-1918 private networks (e.g. 10.0.0.0/8,
  // **********/12, ***********/16) to pick a specific range to use.
  string services_cidr_block = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the existing secondary range in the cluster's
  // subnetwork to use for pod IP addresses. Alternatively, cluster_cidr_block
  // can be used to automatically create a GKE-managed one.
  string cluster_named_range = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the existing secondary range in the cluster's
  // subnetwork to use for service ClusterIPs. Alternatively,
  // services_cidr_block can be used to automatically create a GKE-managed one.
  string services_named_range = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Master Authorized Network that supports multiple CIDR blocks.
  // Allows access to the k8s master from multiple blocks. It cannot be set at
  // the same time with the field man_block.
  MasterAuthorizedNetworksConfig master_authorized_networks_config = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration of the full (Autopilot) cluster management
message FullManagementConfig {
  // Optional. Name of the VPC Network to put the GKE cluster and nodes in. The
  // VPC will be created if it doesn't exist.
  string network = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the subnet that the interface will be part of. Network
  // key must be specified and the subnet must be a subnetwork of the specified
  // network.
  string subnet = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The /28 network that the masters will use.
  string master_ipv4_cidr_block = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The IP address range for the cluster pod IPs. Set to blank to
  // have a range chosen with the default size. Set to /netmask (e.g. /14) to
  // have a range chosen with a specific netmask. Set to a CIDR notation
  // (e.g. *********/14) from the RFC-1918 private networks (e.g. 10.0.0.0/8,
  // **********/12, ***********/16) to pick a specific range to use.
  string cluster_cidr_block = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The IP address range for the cluster service IPs. Set to blank to
  // have a range chosen with the default size. Set to /netmask (e.g. /14) to
  // have a range chosen with a specific netmask. Set to a CIDR notation (e.g.
  // *********/14) from the RFC-1918 private networks (e.g. 10.0.0.0/8,
  // **********/12, ***********/16) to pick a specific range to use.
  string services_cidr_block = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the existing secondary range in the cluster's
  // subnetwork to use for pod IP addresses. Alternatively, cluster_cidr_block
  // can be used to automatically create a GKE-managed one.
  string cluster_named_range = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the existing secondary range in the cluster's
  // subnetwork to use for service ClusterIPs. Alternatively,
  // services_cidr_block can be used to automatically create a GKE-managed one.
  string services_named_range = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Master Authorized Network that supports multiple CIDR blocks.
  // Allows access to the k8s master from multiple blocks. It cannot be set at
  // the same time with the field man_block.
  MasterAuthorizedNetworksConfig master_authorized_networks_config = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration of the Master Authorized Network that support multiple CIDRs
message MasterAuthorizedNetworksConfig {
  // CidrBlock contains an optional name and one CIDR block.
  message CidrBlock {
    // Optional. display_name is an optional field for users to identify CIDR
    // blocks.
    string display_name = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. cidr_block must be specified in CIDR notation when using
    // master_authorized_networks_config. Currently, the user could still use
    // the deprecated man_block field, so this field is currently optional, but
    // will be required in the future.
    string cidr_block = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. cidr_blocks define up to 50 external networks that could access
  // Kubernetes master through HTTPS.
  repeated CidrBlock cidr_blocks = 1 [(google.api.field_behavior) = OPTIONAL];
}

// File represents a yaml file present in a blueprint's package.
message File {
  // Required. Path of the file in package.
  // e.g. `gdce/v1/cluster.yaml`
  string path = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The contents of a file in string format.
  string content = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Signifies whether a file is marked for deletion.
  bool deleted = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates whether changes are allowed to a file. If the field is
  // not set, the file cannot be edited.
  bool editable = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Status of a deployment resource.
message ResourceStatus {
  // Name of the resource.
  string name = 1;

  // Namespace of the resource.
  string resource_namespace = 2;

  // Group to which the resource belongs to.
  string group = 3;

  // Version of the resource.
  string version = 4;

  // Kind of the resource.
  string kind = 5;

  // Output only. Resource type.
  ResourceType resource_type = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of the resource.
  Status status = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Detailed status of NFDeploy.
  NFDeployStatus nf_deploy_status = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Deployment status of NFDeploy.
message NFDeployStatus {
  // Output only. Total number of NFs targeted by this deployment
  int32 targeted_nfs = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total number of NFs targeted by this deployment with a Ready
  // Condition set.
  int32 ready_nfs = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Per-Site Status.
  repeated NFDeploySiteStatus sites = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Per-Site Status.
message NFDeploySiteStatus {
  // Output only. Site id.
  string site = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If true, the Site Deletion is in progress.
  bool pending_deletion = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Hydration status.
  HydrationStatus hydration = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Workload status.
  WorkloadStatus workload = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Hydration status.
message HydrationStatus {
  // Output only. SiteVersion Hydration is targeting.
  SiteVersion site_version = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status.
  string status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// SiteVersion Hydration is targeting.
message SiteVersion {
  // Output only. NF vendor.
  string nf_vendor = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. NF vendor type.
  string nf_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. NF version.
  string nf_version = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Workload status.
message WorkloadStatus {
  // Output only. SiteVersion running in the workload cluster.
  SiteVersion site_version = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status.
  string status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
