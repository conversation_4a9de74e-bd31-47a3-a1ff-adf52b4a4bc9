{"methodConfig": [{"name": [{"service": "google.cloud.netapp.v1.NetApp", "method": "ListStoragePools"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetStoragePool"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ListVolumes"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetVolume"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ListSnapshots"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetSnapshot"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ListActiveDirectories"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetActiveDirectory"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ListKmsConfigs"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetKmsConfig"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ListReplications"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetReplication"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ListBackupVaults"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetBackupVault"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ListBackups"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetBackup"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ListBackupPolicies"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "GetBackupPolicy"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.netapp.v1.NetApp", "method": "CreateStoragePool"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteStoragePool"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateStoragePool"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "CreateVolume"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteVolume"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateVolume"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "RevertVolume"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "EncryptVolumes"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "CreateSnapshot"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteSnapshot"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateSnapshot"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "CreateActiveDirectory"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteActiveDirectory"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateActiveDirectory"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "CreateKmsConfig"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteKmsConfig"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateKmsConfig"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "VerifyKmsConfig"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "CreateReplication"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteReplication"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateReplication"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "StopReplication"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ResumeReplication"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "ReverseReplicationDirection"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "CreateBackupVault"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteBackupVault"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateBackupVault"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "CreateBackup"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteBackup"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateBackup"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "CreateBackupPolicy"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "DeleteBackupPolicy"}, {"service": "google.cloud.netapp.v1.NetApp", "method": "UpdateBackupPolicy"}], "timeout": "60s"}]}