// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// The service level of a storage pool and its volumes.
enum ServiceLevel {
  // Unspecified service level.
  SERVICE_LEVEL_UNSPECIFIED = 0;

  // Premium service level.
  PREMIUM = 1;

  // Extreme service level.
  EXTREME = 2;

  // Standard service level.
  STANDARD = 3;

  // Flex service level.
  FLEX = 4;
}

// The volume encryption key source.
enum EncryptionType {
  // The source of the encryption key is not specified.
  ENCRYPTION_TYPE_UNSPECIFIED = 0;

  // Google managed encryption key.
  SERVICE_MANAGED = 1;

  // Customer managed encryption key, which is stored in KMS.
  CLOUD_KMS = 2;
}

// Type of directory service
enum DirectoryServiceType {
  // Directory service type is not specified.
  DIRECTORY_SERVICE_TYPE_UNSPECIFIED = 0;

  // Active directory policy attached to the storage pool.
  ACTIVE_DIRECTORY = 1;
}

// Metadata for a given
// [google.cloud.location.Location][google.cloud.location.Location].
message LocationMetadata {
  // Output only. Supported service levels in a location.
  repeated ServiceLevel supported_service_levels = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
