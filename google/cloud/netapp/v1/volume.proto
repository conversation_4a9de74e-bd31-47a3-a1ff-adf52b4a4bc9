// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/netapp/v1/common.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "VolumeProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// Protocols is an enum of all the supported network protocols for a volume.
enum Protocols {
  // Unspecified protocol
  PROTOCOLS_UNSPECIFIED = 0;

  // NFS V3 protocol
  NFSV3 = 1;

  // NFS V4 protocol
  NFSV4 = 2;

  // SMB protocol
  SMB = 3;
}

// AccessType is an enum of all the supported access types for a volume.
enum AccessType {
  // Unspecified Access Type
  ACCESS_TYPE_UNSPECIFIED = 0;

  // Read Only
  READ_ONLY = 1;

  // Read Write
  READ_WRITE = 2;

  // None
  READ_NONE = 3;
}

// SMBSettings
// Modifies the behaviour of a SMB volume.
enum SMBSettings {
  // Unspecified default option
  SMB_SETTINGS_UNSPECIFIED = 0;

  // SMB setting encrypt data
  ENCRYPT_DATA = 1;

  // SMB setting browsable
  BROWSABLE = 2;

  // SMB setting notify change
  CHANGE_NOTIFY = 3;

  // SMB setting not to notify change
  NON_BROWSABLE = 4;

  // SMB setting oplocks
  OPLOCKS = 5;

  // SMB setting to show snapshots
  SHOW_SNAPSHOT = 6;

  // SMB setting to show previous versions
  SHOW_PREVIOUS_VERSIONS = 7;

  // SMB setting to access volume based on enumerartion
  ACCESS_BASED_ENUMERATION = 8;

  // Continuously available enumeration
  CONTINUOUSLY_AVAILABLE = 9;
}

// The security style of the volume, can be either UNIX or NTFS.
enum SecurityStyle {
  // SecurityStyle is unspecified
  SECURITY_STYLE_UNSPECIFIED = 0;

  // SecurityStyle uses NTFS
  NTFS = 1;

  // SecurityStyle uses UNIX
  UNIX = 2;
}

// Actions to be restricted for a volume.
enum RestrictedAction {
  // Unspecified restricted action
  RESTRICTED_ACTION_UNSPECIFIED = 0;

  // Prevent volume from being deleted when mounted.
  DELETE = 1;
}

// Message for requesting list of Volumes
message ListVolumesRequest {
  // Required. Parent value for ListVolumesRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/Volume"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, the server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing Volumes
message ListVolumesResponse {
  // The list of Volume
  repeated Volume volumes = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Volume
message GetVolumeRequest {
  // Required. Name of the volume
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Volume" }
  ];
}

// Message for creating a Volume
message CreateVolumeRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/Volume"
    }
  ];

  // Required. Id of the requesting volume. Must be unique within the parent
  // resource. Must contain only letters, numbers and hyphen, with the first
  // character a letter, the last a letter or a number,
  // and a 63 character maximum.
  string volume_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The volume being created.
  Volume volume = 3 [(google.api.field_behavior) = REQUIRED];
}

// Message for updating a Volume
message UpdateVolumeRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Volume resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The volume being updated
  Volume volume = 2 [(google.api.field_behavior) = REQUIRED];
}

// Message for deleting a Volume
message DeleteVolumeRequest {
  // Required. Name of the volume
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Volume" }
  ];

  // If this field is set as true, CCFE will not block the volume resource
  // deletion even if it has any snapshots resource. (Otherwise, the request
  // will only work if the volume has no snapshots.)
  bool force = 2;
}

// RevertVolumeRequest reverts the given volume to the specified snapshot.
message RevertVolumeRequest {
  // Required. The resource name of the volume, in the format of
  // projects/{project_id}/locations/{location}/volumes/{volume_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Volume" }
  ];

  // Required. The snapshot resource ID, in the format 'my-snapshot', where the
  // specified ID is the {snapshot_id} of the fully qualified name like
  // projects/{project_id}/locations/{location_id}/volumes/{volume_id}/snapshots/{snapshot_id}
  string snapshot_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Volume provides a filesystem that you can mount.
message Volume {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/Volume"
    pattern: "projects/{project}/locations/{location}/volumes/{volume}"
    plural: "volumes"
    singular: "volume"
  };

  // The volume states
  enum State {
    // Unspecified Volume State
    STATE_UNSPECIFIED = 0;

    // Volume State is Ready
    READY = 1;

    // Volume State is Creating
    CREATING = 2;

    // Volume State is Deleting
    DELETING = 3;

    // Volume State is Updating
    UPDATING = 4;

    // Volume State is Restoring
    RESTORING = 5;

    // Volume State is Disabled
    DISABLED = 6;

    // Volume State is Error
    ERROR = 7;

    // Volume State is Preparing. Note that this is different from CREATING
    // where CREATING means the volume is being created, while PREPARING means
    // the volume is created and now being prepared for the replication.
    PREPARING = 8;

    // Volume State is Read Only
    READ_ONLY = 9;
  }

  // Identifier. Name of the volume
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. State of the volume
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State details of the volume
  string state_details = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create time of the volume
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Share name of the volume
  string share_name = 5 [(google.api.field_behavior) = REQUIRED];

  // Output only. This field is not implemented. The values provided in this
  // field are ignored.
  string psa_range = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. StoragePool name of the volume
  string storage_pool = 7 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/StoragePool"
    }
  ];

  // Output only. VPC Network name.
  // Format: projects/{project}/global/networks/{network}
  string network = 8 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Output only. Service level of the volume
  ServiceLevel service_level = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Capacity in GIB of the volume
  int64 capacity_gib = 10 [(google.api.field_behavior) = REQUIRED];

  // Optional. Export policy of the volume
  ExportPolicy export_policy = 11 [(google.api.field_behavior) = OPTIONAL];

  // Required. Protocols required for the volume
  repeated Protocols protocols = 12 [(google.api.field_behavior) = REQUIRED];

  // Optional. SMB share settings for the volume.
  repeated SMBSettings smb_settings = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Mount options of this volume
  repeated MountOption mount_options = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Default unix style permission (e.g. 777) the mount point will be
  // created with. Applicable for NFS protocol types only.
  string unix_permissions = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels as key value pairs
  map<string, string> labels = 16 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the volume
  string description = 17 [(google.api.field_behavior) = OPTIONAL];

  // Optional. SnapshotPolicy for a volume.
  SnapshotPolicy snapshot_policy = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Snap_reserve specifies percentage of volume storage reserved for
  // snapshot storage. Default is 0 percent.
  double snap_reserve = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Snapshot_directory if enabled (true) the volume will contain a
  // read-only .snapshot directory which provides access to each of the volume's
  // snapshots.
  bool snapshot_directory = 20 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Used capacity in GIB of the volume. This is computed
  // periodically and it does not represent the realtime usage.
  int64 used_gib = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Security Style of the Volume
  SecurityStyle security_style = 22 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Flag indicating if the volume is a kerberos volume or not, export
  // policy rules control kerberos security modes (krb5, krb5i, krb5p).
  bool kerberos_enabled = 23 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Flag indicating if the volume is NFS LDAP enabled or not.
  bool ldap_enabled = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Specifies the ActiveDirectory name of a SMB volume.
  string active_directory = 25 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/ActiveDirectory"
    }
  ];

  // Optional. Specifies the source of the volume to be created from.
  RestoreParameters restore_parameters = 26
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Specifies the KMS config to be used for volume encryption.
  string kms_config = 27 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/KmsConfig"
    }
  ];

  // Output only. Specified the current volume encryption key source.
  EncryptionType encryption_type = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether the volume is part of a replication
  // relationship.
  bool has_replication = 29 [(google.api.field_behavior) = OUTPUT_ONLY];

  // BackupConfig of the volume.
  optional BackupConfig backup_config = 30;

  // Optional. List of actions that are restricted on this volume.
  repeated RestrictedAction restricted_actions = 31
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Flag indicating if the volume will be a large capacity volume or
  // a regular volume.
  bool large_capacity = 32 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Flag indicating if the volume will have an IP address per node
  // for volumes supporting multiple IP endpoints. Only the volume with
  // large_capacity will be allowed to have multiple endpoints.
  bool multiple_endpoints = 33 [(google.api.field_behavior) = OPTIONAL];

  // Tiering policy for the volume.
  optional TieringPolicy tiering_policy = 34;

  // Output only. Specifies the replica zone for regional volume.
  string replica_zone = 36 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Specifies the active zone for regional volume.
  string zone = 37 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Size of the volume cold tier data in GiB.
  int64 cold_tier_size_gib = 39 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The Hybrid Replication parameters for the volume.
  HybridReplicationParameters hybrid_replication_parameters = 40
      [(google.api.field_behavior) = OPTIONAL];
}

// Defines the export policy for the volume.
message ExportPolicy {
  // Required. List of export policy rules
  repeated SimpleExportPolicyRule rules = 1
      [(google.api.field_behavior) = REQUIRED];
}

// An export policy rule describing various export options.
message SimpleExportPolicyRule {
  // Comma separated list of allowed clients IP addresses
  optional string allowed_clients = 1;

  // Whether Unix root access will be granted.
  optional string has_root_access = 2;

  // Access type (ReadWrite, ReadOnly, None)
  optional AccessType access_type = 3;

  // NFS V3 protocol.
  optional bool nfsv3 = 4;

  // NFS V4 protocol.
  optional bool nfsv4 = 5;

  // If enabled (true) the rule defines a read only access for clients matching
  // the 'allowedClients' specification. It enables nfs clients to mount using
  // 'authentication' kerberos security mode.
  optional bool kerberos_5_read_only = 6;

  // If enabled (true) the rule defines read and write access for clients
  // matching the 'allowedClients' specification. It enables nfs clients to
  // mount using 'authentication' kerberos security mode. The
  // 'kerberos5ReadOnly' value be ignored if this is enabled.
  optional bool kerberos_5_read_write = 7;

  // If enabled (true) the rule defines a read only access for clients matching
  // the 'allowedClients' specification. It enables nfs clients to mount using
  // 'integrity' kerberos security mode.
  optional bool kerberos_5i_read_only = 8;

  // If enabled (true) the rule defines read and write access for clients
  // matching the 'allowedClients' specification. It enables nfs clients to
  // mount using 'integrity' kerberos security mode. The 'kerberos5iReadOnly'
  // value be ignored if this is enabled.
  optional bool kerberos_5i_read_write = 9;

  // If enabled (true) the rule defines a read only access for clients matching
  // the 'allowedClients' specification. It enables nfs clients to mount using
  // 'privacy' kerberos security mode.
  optional bool kerberos_5p_read_only = 10;

  // If enabled (true) the rule defines read and write access for clients
  // matching the 'allowedClients' specification. It enables nfs clients to
  // mount using 'privacy' kerberos security mode. The 'kerberos5pReadOnly'
  // value be ignored if this is enabled.
  optional bool kerberos_5p_read_write = 11;
}

// Snapshot Policy for a volume.
message SnapshotPolicy {
  // If enabled, make snapshots automatically according to the schedules.
  // Default is false.
  optional bool enabled = 1;

  // Hourly schedule policy.
  optional HourlySchedule hourly_schedule = 2;

  // Daily schedule policy.
  optional DailySchedule daily_schedule = 3;

  // Weekly schedule policy.
  optional WeeklySchedule weekly_schedule = 4;

  // Monthly schedule policy.
  optional MonthlySchedule monthly_schedule = 5;
}

// Make a snapshot every hour e.g. at 04:00, 05:00, 06:00.
message HourlySchedule {
  // The maximum number of Snapshots to keep for the hourly schedule
  optional double snapshots_to_keep = 1;

  // Set the minute of the hour to start the snapshot (0-59), defaults to the
  // top of the hour (0).
  optional double minute = 2;
}

// Make a snapshot every day e.g. at 04:00, 05:20, 23:50
message DailySchedule {
  // The maximum number of Snapshots to keep for the hourly schedule
  optional double snapshots_to_keep = 1;

  // Set the minute of the hour to start the snapshot (0-59), defaults to the
  // top of the hour (0).
  optional double minute = 2;

  // Set the hour to start the snapshot (0-23), defaults to midnight (0).
  optional double hour = 3;
}

// Make a snapshot every week e.g. at Monday 04:00, Wednesday 05:20, Sunday
// 23:50
message WeeklySchedule {
  // The maximum number of Snapshots to keep for the hourly schedule
  optional double snapshots_to_keep = 1;

  // Set the minute of the hour to start the snapshot (0-59), defaults to the
  // top of the hour (0).
  optional double minute = 2;

  // Set the hour to start the snapshot (0-23), defaults to midnight (0).
  optional double hour = 3;

  // Set the day or days of the week to make a snapshot. Accepts a comma
  // separated days of the week. Defaults to 'Sunday'.
  optional string day = 4;
}

// Make a snapshot once a month e.g. at 2nd 04:00, 7th 05:20, 24th 23:50
message MonthlySchedule {
  // The maximum number of Snapshots to keep for the hourly schedule
  optional double snapshots_to_keep = 1;

  // Set the minute of the hour to start the snapshot (0-59), defaults to the
  // top of the hour (0).
  optional double minute = 2;

  // Set the hour to start the snapshot (0-23), defaults to midnight (0).
  optional double hour = 3;

  // Set the day or days of the month to make a snapshot (1-31). Accepts a
  // comma separated number of days. Defaults to '1'.
  optional string days_of_month = 4;
}

// View only mount options for a volume.
message MountOption {
  // Export string
  string export = 1;

  // Full export string
  string export_full = 2;

  // Protocol to mount with.
  Protocols protocol = 3;

  // Instructions for mounting
  string instructions = 4;
}

// The RestoreParameters if volume is created from a snapshot or backup.
message RestoreParameters {
  // The source that the volume is created from.
  oneof source {
    // Full name of the snapshot resource.
    // Format:
    // projects/{project}/locations/{location}/volumes/{volume}/snapshots/{snapshot}
    string source_snapshot = 1;

    // Full name of the backup resource.
    // Format:
    // projects/{project}/locations/{location}/backupVaults/{backup_vault_id}/backups/{backup_id}
    string source_backup = 2;
  }
}

// BackupConfig contains backup related config on a volume.
message BackupConfig {
  // Optional. When specified, schedule backups will be created based on the
  // policy configuration.
  repeated string backup_policies = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/BackupPolicy"
    }
  ];

  // Optional. Name of backup vault.
  // Format:
  // projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}
  string backup_vault = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/BackupVault"
    }
  ];

  // Optional. When set to true, scheduled backup is enabled on the volume.
  // This field should be nil when there's no backup policy attached.
  optional bool scheduled_backup_enabled = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Total size of all backups in a chain in bytes = baseline
  // backup size + sum(incremental backup size).
  optional int64 backup_chain_bytes = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines tiering policy for the volume.
message TieringPolicy {
  // Tier action for the volume.
  enum TierAction {
    // Unspecified.
    TIER_ACTION_UNSPECIFIED = 0;

    // When tiering is enabled, new cold data will be tiered.
    ENABLED = 1;

    // When paused, tiering won't be performed on new data. Existing data stays
    // tiered until accessed.
    PAUSED = 2;
  }

  // Optional. Flag indicating if the volume has tiering policy enable/pause.
  // Default is PAUSED.
  optional TierAction tier_action = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Time in days to mark the volume's data block as cold and make it
  // eligible for tiering, can be range from 7-183. Default is 31.
  optional int32 cooling_threshold_days = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// The Hybrid Replication parameters for the volume.
message HybridReplicationParameters {
  // Required. Desired name for the replication of this volume.
  string replication = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/Replication"
    }
  ];

  // Required. Name of the user's local source volume to be peered with the
  // destination volume.
  string peer_volume_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the user's local source cluster to be peered with the
  // destination cluster.
  string peer_cluster_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the user's local source vserver svm to be peered with the
  // destination vserver svm.
  string peer_svm_name = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. List of node ip addresses to be peered with.
  repeated string peer_ip_addresses = 5
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Name of source cluster location associated with the Hybrid
  // replication. This is a free-form field for the display purpose only.
  string cluster_location = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the replication.
  string description = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels to be added to the replication as the key value pairs.
  map<string, string> labels = 8 [(google.api.field_behavior) = OPTIONAL];
}
