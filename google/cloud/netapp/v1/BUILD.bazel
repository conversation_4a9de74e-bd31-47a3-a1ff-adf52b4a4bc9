# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "netapp_proto",
    srcs = [
        "active_directory.proto",
        "backup.proto",
        "backup_policy.proto",
        "backup_vault.proto",
        "cloud_netapp_service.proto",
        "common.proto",
        "kms.proto",
        "replication.proto",
        "snapshot.proto",
        "storage_pool.proto",
        "volume.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "netapp_proto_with_info",
    deps = [
        ":netapp_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "netapp_java_proto",
    deps = [":netapp_proto"],
)

java_grpc_library(
    name = "netapp_java_grpc",
    srcs = [":netapp_proto"],
    deps = [":netapp_java_proto"],
)

java_gapic_library(
    name = "netapp_java_gapic",
    srcs = [":netapp_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "netapp_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "netapp_v1.yaml",
    test_deps = [
        ":netapp_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":netapp_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "netapp_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.netapp.v1.NetAppClientHttpJsonTest",
        "com.google.cloud.netapp.v1.NetAppClientTest",
    ],
    runtime_deps = [":netapp_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-netapp-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":netapp_java_gapic",
        ":netapp_java_grpc",
        ":netapp_java_proto",
        ":netapp_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "netapp_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/netapp/apiv1/netapppb",
    protos = [":netapp_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "netapp_go_gapic",
    srcs = [":netapp_proto_with_info"],
    grpc_service_config = "netapp_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/netapp/apiv1;netapp",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "netapp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":netapp_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-netapp-v1-go",
    deps = [
        ":netapp_go_gapic",
        ":netapp_go_gapic_srcjar-metadata.srcjar",
        ":netapp_go_gapic_srcjar-snippets.srcjar",
        ":netapp_go_gapic_srcjar-test.srcjar",
        ":netapp_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "netapp_py_gapic",
    srcs = [":netapp_proto"],
    grpc_service_config = "netapp_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "netapp_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "netapp_py_gapic_test",
    srcs = [
        "netapp_py_gapic_pytest.py",
        "netapp_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":netapp_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "netapp-v1-py",
    deps = [
        ":netapp_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "netapp_php_proto",
    deps = [":netapp_proto"],
)

php_gapic_library(
    name = "netapp_php_gapic",
    srcs = [":netapp_proto_with_info"],
    grpc_service_config = "netapp_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "netapp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":netapp_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-netapp-v1-php",
    deps = [
        ":netapp_php_gapic",
        ":netapp_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "netapp_nodejs_gapic",
    package_name = "@google-cloud/netapp",
    src = ":netapp_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "netapp_v1_grpc_service_config.json",
    package = "google.cloud.netapp.v1",
    rest_numeric_enums = True,
    service_yaml = "netapp_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "netapp-v1-nodejs",
    deps = [
        ":netapp_nodejs_gapic",
        ":netapp_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "netapp_ruby_proto",
    deps = [":netapp_proto"],
)

ruby_grpc_library(
    name = "netapp_ruby_grpc",
    srcs = [":netapp_proto"],
    deps = [":netapp_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "netapp_ruby_gapic",
    srcs = [":netapp_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-netapp-v1",
        "ruby-cloud-namespace-override=Netapp=NetApp",
        "ruby-cloud-path-override=net_app=netapp",
    ],
    grpc_service_config = "netapp_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "netapp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":netapp_ruby_grpc",
        ":netapp_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-netapp-v1-ruby",
    deps = [
        ":netapp_ruby_gapic",
        ":netapp_ruby_grpc",
        ":netapp_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "netapp_csharp_proto",
    deps = [":netapp_proto"],
)

csharp_grpc_library(
    name = "netapp_csharp_grpc",
    srcs = [":netapp_proto"],
    deps = [":netapp_csharp_proto"],
)

csharp_gapic_library(
    name = "netapp_csharp_gapic",
    srcs = [":netapp_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "netapp_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "netapp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":netapp_csharp_grpc",
        ":netapp_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-netapp-v1-csharp",
    deps = [
        ":netapp_csharp_gapic",
        ":netapp_csharp_grpc",
        ":netapp_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "netapp_cc_proto",
    deps = [":netapp_proto"],
)

cc_grpc_library(
    name = "netapp_cc_grpc",
    srcs = [":netapp_proto"],
    grpc_only = True,
    deps = [":netapp_cc_proto"],
)
