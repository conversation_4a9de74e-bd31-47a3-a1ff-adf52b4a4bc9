// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "BackupPolicyProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// Backup Policy.
message BackupPolicy {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/BackupPolicy"
    pattern: "projects/{project}/locations/{location}/backupPolicies/{backup_policy}"
    plural: "backupPolicies"
    singular: "backupPolicy"
  };

  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // BackupPolicy is being created.
    CREATING = 1;

    // BackupPolicy is available for use.
    READY = 2;

    // BackupPolicy is being deleted.
    DELETING = 3;

    // BackupPolicy is not valid and cannot be used.
    ERROR = 4;

    // BackupPolicy is being updated.
    UPDATING = 5;
  }

  // Identifier. The resource name of the backup policy.
  // Format:
  // `projects/{project_id}/locations/{location}/backupPolicies/{backup_policy_id}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Number of daily backups to keep. Note that the minimum daily backup limit
  // is 2.
  optional int32 daily_backup_limit = 2;

  // Number of weekly backups to keep. Note that the sum of daily, weekly and
  // monthly backups should be greater than 1.
  optional int32 weekly_backup_limit = 3;

  // Number of monthly backups to keep. Note that the sum of daily, weekly and
  // monthly backups should be greater than 1.
  optional int32 monthly_backup_limit = 4;

  // Description of the backup policy.
  optional string description = 5;

  // If enabled, make backups automatically according to the schedules.
  // This will be applied to all volumes that have this policy attached and
  // enforced on volume level. If not specified, default is true.
  optional bool enabled = 6;

  // Output only. The total number of volumes assigned by this backup policy.
  optional int32 assigned_volume_count = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the backup policy was created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource labels to represent user provided metadata.
  map<string, string> labels = 9;

  // Output only. The backup policy state.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CreateBackupPolicyRequest creates a backupPolicy.
message CreateBackupPolicyRequest {
  // Required. The location to create the backup policies of, in the format
  // `projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/BackupPolicy"
    }
  ];

  // Required. A backupPolicy resource
  BackupPolicy backup_policy = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the backup policy.
  // The ID must be unique within the specified location.
  // Must contain only letters, numbers and hyphen, with the first
  // character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string backup_policy_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// GetBackupPolicyRequest gets the state of a backupPolicy.
message GetBackupPolicyRequest {
  // Required. The backupPolicy resource name, in the format
  // `projects/{project_id}/locations/{location}/backupPolicies/{backup_policy_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/BackupPolicy"
    }
  ];
}

// ListBackupPoliciesRequest for requesting multiple backup policies.
message ListBackupPoliciesRequest {
  // Required. Parent value for ListBackupPoliciesRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/BackupPolicy"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, the server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// ListBackupPoliciesResponse contains all the backup policies requested.
message ListBackupPoliciesResponse {
  // The list of backup policies.
  repeated BackupPolicy backup_policies = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// UpdateBackupPolicyRequest for updating a backup policy.
message UpdateBackupPolicyRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Backup Policy resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The backup policy being updated
  BackupPolicy backup_policy = 2 [(google.api.field_behavior) = REQUIRED];
}

// DeleteBackupPolicyRequest deletes a backup policy.
message DeleteBackupPolicyRequest {
  // Required. The backup policy resource name, in the format
  // `projects/{project_id}/locations/{location}/backupPolicies/{backup_policy_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/BackupPolicy"
    }
  ];
}
