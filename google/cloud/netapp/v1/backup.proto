// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "BackupProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// A NetApp Backup.
message Backup {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/Backup"
    pattern: "projects/{project}/locations/{location}/backupVaults/{backup_vault}/backups/{backup}"
    plural: "backups"
    singular: "backup"
  };

  // The Backup States
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // Backup is being created. While in this state, the snapshot for the backup
    // point-in-time may not have been created yet, and so the point-in-time may
    // not have been fixed.
    CREATING = 1;

    // Backup is being uploaded. While in this state, none of the writes to the
    // volume will be included in the backup.
    UPLOADING = 2;

    // Backup is available for use.
    READY = 3;

    // Backup is being deleted.
    DELETING = 4;

    // Backup is not valid and cannot be used for creating new volumes or
    // restoring existing volumes.
    ERROR = 5;

    // Backup is being updated.
    UPDATING = 6;
  }

  // Backup types.
  enum Type {
    // Unspecified backup type.
    TYPE_UNSPECIFIED = 0;

    // Manual backup type.
    MANUAL = 1;

    // Scheduled backup type.
    SCHEDULED = 2;
  }

  // Identifier. The resource name of the backup.
  // Format:
  // `projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}/backups/{backup_id}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The backup state.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A description of the backup with 2048 characters or less.
  // Requests with longer descriptions will be rejected.
  string description = 3;

  // Output only. Size of the file system when the backup was created. When
  // creating a new volume from the backup, the volume capacity will have to be
  // at least as big.
  int64 volume_usage_bytes = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of backup, manually created or created by a backup
  // policy.
  Type backup_type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Volume full name of this backup belongs to.
  // Format:
  // `projects/{projects_id}/locations/{location}/volumes/{volume_id}`
  string source_volume = 6 [
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Volume" }
  ];

  // If specified, backup will be created from the given snapshot.
  // If not specified, there will be a new snapshot taken to initiate the backup
  // creation. Format:
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}/snapshots/{snapshot_id}`
  optional string source_snapshot = 7 [
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Snapshot" }
  ];

  // Output only. The time when the backup was created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource labels to represent user provided metadata.
  map<string, string> labels = 9;

  // Output only. Total size of all backups in a chain in bytes = baseline
  // backup size + sum(incremental backup size)
  int64 chain_storage_bytes = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ListBackupsRequest lists backups.
message ListBackupsRequest {
  // Required. The backupVault for which to retrieve backup information,
  // in the format
  // `projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}`.
  // To retrieve backup information for all locations, use "-" for the
  // `{location}` value.
  // To retrieve backup information for all backupVaults, use "-" for the
  // `{backup_vault_id}` value.
  // To retrieve backup information for a volume, use "-" for the
  // `{backup_vault_id}` value and specify volume full name with the filter.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/Backup"
    }
  ];

  // The maximum number of items to return. The service may return fewer
  // than this value. The maximum value
  // is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 4;

  // The standard list filter.
  // If specified, backups will be returned based on the attribute name that
  // matches the filter expression. If empty, then no backups are filtered out.
  // See https://google.aip.dev/160
  string filter = 5;
}

// ListBackupsResponse is the result of ListBackupsRequest.
message ListBackupsResponse {
  // A list of backups in the project.
  repeated Backup backups = 1;

  // The token you can use to retrieve the next page of results. Not returned
  // if there are no more results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// GetBackupRequest gets the state of a backup.
message GetBackupRequest {
  // Required. The backup resource name, in the format
  // `projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}/backups/{backup_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Backup" }
  ];
}

// CreateBackupRequest creates a backup.
message CreateBackupRequest {
  // Required. The NetApp backupVault to create the backups of, in the format
  // `projects/*/locations/*/backupVaults/{backup_vault_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/Backup"
    }
  ];

  // Required. The ID to use for the backup.
  // The ID must be unique within the specified backupVault.
  // Must contain only letters, numbers and hyphen, with the first
  // character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string backup_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A backup resource
  Backup backup = 3 [(google.api.field_behavior) = REQUIRED];
}

// DeleteBackupRequest deletes a backup.
message DeleteBackupRequest {
  // Required. The backup resource name, in the format
  // `projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}/backups/{backup_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Backup" }
  ];
}

// UpdateBackupRequest updates description and/or labels for a backup.
message UpdateBackupRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Backup resource to be updated.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The backup being updated
  Backup backup = 2 [(google.api.field_behavior) = REQUIRED];
}
