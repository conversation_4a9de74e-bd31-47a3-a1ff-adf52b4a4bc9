// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "BackupVaultProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// A NetApp BackupVault.
message BackupVault {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/BackupVault"
    pattern: "projects/{project}/locations/{location}/backupVaults/{backup_vault}"
    plural: "backupVaults"
    singular: "backupVault"
  };

  // The Backup Vault States
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // BackupVault is being created.
    CREATING = 1;

    // BackupVault is available for use.
    READY = 2;

    // BackupVault is being deleted.
    DELETING = 3;

    // BackupVault is not valid and cannot be used.
    ERROR = 4;

    // BackupVault is being updated.
    UPDATING = 5;
  }

  // Identifier. The resource name of the backup vault.
  // Format:
  // `projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The backup vault state.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create time of the backup vault.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Description of the backup vault.
  string description = 4;

  // Resource labels to represent user provided metadata.
  map<string, string> labels = 5;
}

// GetBackupVaultRequest gets the state of a backupVault.
message GetBackupVaultRequest {
  // Required. The backupVault resource name, in the format
  // `projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/BackupVault"
    }
  ];
}

// ListBackupVaultsRequest lists backupVaults.
message ListBackupVaultsRequest {
  // Required. The location for which to retrieve backupVault information,
  // in the format
  // `projects/{project_id}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/BackupVault"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 4;

  // List filter.
  string filter = 5;
}

// ListBackupVaultsResponse is the result of ListBackupVaultsRequest.
message ListBackupVaultsResponse {
  // A list of backupVaults in the project for the specified location.
  repeated BackupVault backup_vaults = 1;

  // The token you can use to retrieve the next page of results. Not returned
  // if there are no more results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// CreateBackupVaultRequest creates a backup vault.
message CreateBackupVaultRequest {
  // Required. The location to create the backup vaults, in the format
  // `projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/BackupVault"
    }
  ];

  // Required. The ID to use for the backupVault.
  // The ID must be unique within the specified location.
  // Must contain only letters, numbers and hyphen, with the first
  // character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string backup_vault_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A backupVault resource
  BackupVault backup_vault = 3 [(google.api.field_behavior) = REQUIRED];
}

// DeleteBackupVaultRequest deletes a backupVault.
message DeleteBackupVaultRequest {
  // Required. The backupVault resource name, in the format
  // `projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/BackupVault"
    }
  ];
}

// UpdateBackupVaultRequest updates description and/or labels for a backupVault.
message UpdateBackupVaultRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Backup resource to be updated.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The backupVault being updated
  BackupVault backup_vault = 2 [(google.api.field_behavior) = REQUIRED];
}
