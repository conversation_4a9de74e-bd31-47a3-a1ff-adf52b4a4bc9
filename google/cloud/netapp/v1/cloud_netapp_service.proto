// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/netapp/v1/active_directory.proto";
import "google/cloud/netapp/v1/backup.proto";
import "google/cloud/netapp/v1/backup_policy.proto";
import "google/cloud/netapp/v1/backup_vault.proto";
import "google/cloud/netapp/v1/kms.proto";
import "google/cloud/netapp/v1/replication.proto";
import "google/cloud/netapp/v1/snapshot.proto";
import "google/cloud/netapp/v1/storage_pool.proto";
import "google/cloud/netapp/v1/volume.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "CloudNetappServiceProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};

// NetApp Files Google Cloud Service
service NetApp {
  option (google.api.default_host) = "netapp.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns descriptions of all storage pools owned by the caller.
  rpc ListStoragePools(ListStoragePoolsRequest)
      returns (ListStoragePoolsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/storagePools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new storage pool.
  rpc CreateStoragePool(CreateStoragePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/storagePools"
      body: "storage_pool"
    };
    option (google.api.method_signature) =
        "parent,storage_pool,storage_pool_id";
    option (google.longrunning.operation_info) = {
      response_type: "StoragePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns the description of the specified storage pool by poolId.
  rpc GetStoragePool(GetStoragePoolRequest) returns (StoragePool) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/storagePools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the storage pool properties with the full spec
  rpc UpdateStoragePool(UpdateStoragePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{storage_pool.name=projects/*/locations/*/storagePools/*}"
      body: "storage_pool"
    };
    option (google.api.method_signature) = "storage_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "StoragePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Warning! This operation will permanently delete the storage pool.
  rpc DeleteStoragePool(DeleteStoragePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/storagePools/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // ValidateDirectoryService does a connectivity check for a directory service
  // policy attached to the storage pool.
  rpc ValidateDirectoryService(ValidateDirectoryServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/storagePools/*}:validateDirectoryService"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // This operation will switch the active/replica zone for a regional
  // storagePool.
  rpc SwitchActiveReplicaZone(SwitchActiveReplicaZoneRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/storagePools/*}:switch"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "StoragePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Volumes in a given project.
  rpc ListVolumes(ListVolumesRequest) returns (ListVolumesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/volumes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Volume.
  rpc GetVolume(GetVolumeRequest) returns (Volume) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/volumes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Volume in a given project and location.
  rpc CreateVolume(CreateVolumeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/volumes"
      body: "volume"
    };
    option (google.api.method_signature) = "parent,volume,volume_id";
    option (google.longrunning.operation_info) = {
      response_type: "Volume"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Volume.
  rpc UpdateVolume(UpdateVolumeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{volume.name=projects/*/locations/*/volumes/*}"
      body: "volume"
    };
    option (google.api.method_signature) = "volume,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Volume"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Volume.
  rpc DeleteVolume(DeleteVolumeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/volumes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Revert an existing volume to a specified snapshot.
  // Warning! This operation will permanently revert all changes made after the
  // snapshot was created.
  rpc RevertVolume(RevertVolumeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/volumes/*}:revert"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Volume"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns descriptions of all snapshots for a volume.
  rpc ListSnapshots(ListSnapshotsRequest) returns (ListSnapshotsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/volumes/*}/snapshots"
    };
    option (google.api.method_signature) = "parent";
  }

  // Describe a snapshot for a volume.
  rpc GetSnapshot(GetSnapshotRequest) returns (Snapshot) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/volumes/*/snapshots/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a new snapshot for a volume.
  rpc CreateSnapshot(CreateSnapshotRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/volumes/*}/snapshots"
      body: "snapshot"
    };
    option (google.api.method_signature) = "parent,snapshot,snapshot_id";
    option (google.longrunning.operation_info) = {
      response_type: "Snapshot"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a snapshot.
  rpc DeleteSnapshot(DeleteSnapshotRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/volumes/*/snapshots/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the settings of a specific snapshot.
  rpc UpdateSnapshot(UpdateSnapshotRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{snapshot.name=projects/*/locations/*/volumes/*/snapshots/*}"
      body: "snapshot"
    };
    option (google.api.method_signature) = "snapshot,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Snapshot"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists active directories.
  rpc ListActiveDirectories(ListActiveDirectoriesRequest)
      returns (ListActiveDirectoriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/activeDirectories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Describes a specified active directory.
  rpc GetActiveDirectory(GetActiveDirectoryRequest) returns (ActiveDirectory) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/activeDirectories/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // CreateActiveDirectory
  // Creates the active directory specified in the request.
  rpc CreateActiveDirectory(CreateActiveDirectoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/activeDirectories"
      body: "active_directory"
    };
    option (google.api.method_signature) =
        "parent,active_directory,active_directory_id";
    option (google.longrunning.operation_info) = {
      response_type: "ActiveDirectory"
      metadata_type: "OperationMetadata"
    };
  }

  // Update the parameters of an active directories.
  rpc UpdateActiveDirectory(UpdateActiveDirectoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{active_directory.name=projects/*/locations/*/activeDirectories/*}"
      body: "active_directory"
    };
    option (google.api.method_signature) = "active_directory,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ActiveDirectory"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete the active directory specified in the request.
  rpc DeleteActiveDirectory(DeleteActiveDirectoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/activeDirectories/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns descriptions of all KMS configs owned by the caller.
  rpc ListKmsConfigs(ListKmsConfigsRequest) returns (ListKmsConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/kmsConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new KMS config.
  rpc CreateKmsConfig(CreateKmsConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/kmsConfigs"
      body: "kms_config"
    };
    option (google.api.method_signature) = "parent,kms_config,kms_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "KmsConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns the description of the specified KMS config by kms_config_id.
  rpc GetKmsConfig(GetKmsConfigRequest) returns (KmsConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/kmsConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the Kms config properties with the full spec
  rpc UpdateKmsConfig(UpdateKmsConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{kms_config.name=projects/*/locations/*/kmsConfigs/*}"
      body: "kms_config"
    };
    option (google.api.method_signature) = "kms_config,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "KmsConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Encrypt the existing volumes without CMEK encryption with the desired the
  // KMS config for the whole region.
  rpc EncryptVolumes(EncryptVolumesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/kmsConfigs/*}:encrypt"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "KmsConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Verifies KMS config reachability.
  rpc VerifyKmsConfig(VerifyKmsConfigRequest)
      returns (VerifyKmsConfigResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/kmsConfigs/*}:verify"
      body: "*"
    };
  }

  // Warning! This operation will permanently delete the Kms config.
  rpc DeleteKmsConfig(DeleteKmsConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/kmsConfigs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns descriptions of all replications for a volume.
  rpc ListReplications(ListReplicationsRequest)
      returns (ListReplicationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/volumes/*}/replications"
    };
    option (google.api.method_signature) = "parent";
  }

  // Describe a replication for a volume.
  rpc GetReplication(GetReplicationRequest) returns (Replication) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/volumes/*/replications/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a new replication for a volume.
  rpc CreateReplication(CreateReplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/volumes/*}/replications"
      body: "replication"
    };
    option (google.api.method_signature) = "parent,replication,replication_id";
    option (google.longrunning.operation_info) = {
      response_type: "Replication"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a replication.
  rpc DeleteReplication(DeleteReplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/volumes/*/replications/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the settings of a specific replication.
  rpc UpdateReplication(UpdateReplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{replication.name=projects/*/locations/*/volumes/*/replications/*}"
      body: "replication"
    };
    option (google.api.method_signature) = "replication,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Replication"
      metadata_type: "OperationMetadata"
    };
  }

  // Stop Cross Region Replication.
  rpc StopReplication(StopReplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/volumes/*/replications/*}:stop"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Replication"
      metadata_type: "OperationMetadata"
    };
  }

  // Resume Cross Region Replication.
  rpc ResumeReplication(ResumeReplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/volumes/*/replications/*}:resume"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Replication"
      metadata_type: "OperationMetadata"
    };
  }

  // Reverses direction of replication. Source becomes destination and
  // destination becomes source.
  rpc ReverseReplicationDirection(ReverseReplicationDirectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/volumes/*/replications/*}:reverseDirection"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Replication"
      metadata_type: "OperationMetadata"
    };
  }

  // Establish replication peering.
  rpc EstablishPeering(EstablishPeeringRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/volumes/*/replications/*}:establishPeering"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Replication"
      metadata_type: "OperationMetadata"
    };
  }

  // Syncs the replication. This will invoke one time volume data transfer from
  // source to destination.
  rpc SyncReplication(SyncReplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/volumes/*/replications/*}:sync"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Replication"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates new backup vault
  rpc CreateBackupVault(CreateBackupVaultRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/backupVaults"
      body: "backup_vault"
    };
    option (google.api.method_signature) =
        "parent,backup_vault,backup_vault_id";
    option (google.longrunning.operation_info) = {
      response_type: "BackupVault"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns the description of the specified backup vault
  rpc GetBackupVault(GetBackupVaultRequest) returns (BackupVault) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupVaults/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns list of all available backup vaults.
  rpc ListBackupVaults(ListBackupVaultsRequest)
      returns (ListBackupVaultsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backupVaults"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the settings of a specific backup vault.
  rpc UpdateBackupVault(UpdateBackupVaultRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup_vault.name=projects/*/locations/*/backupVaults/*}"
      body: "backup_vault"
    };
    option (google.api.method_signature) = "backup_vault,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "BackupVault"
      metadata_type: "OperationMetadata"
    };
  }

  // Warning! This operation will permanently delete the backup vault.
  rpc DeleteBackupVault(DeleteBackupVaultRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupVaults/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a backup from the volume specified in the request
  // The backup can be created from the given snapshot if specified in the
  // request. If no snapshot specified, there'll be a new snapshot taken to
  // initiate the backup creation.
  rpc CreateBackup(CreateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/backupVaults/*}/backups"
      body: "backup"
    };
    option (google.api.method_signature) = "parent,backup,backup_id";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns the description of the specified backup
  rpc GetBackup(GetBackupRequest) returns (Backup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupVaults/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns descriptions of all backups for a backupVault.
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/backupVaults/*}/backups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Warning! This operation will permanently delete the backup.
  rpc DeleteBackup(DeleteBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupVaults/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Update backup with full spec.
  rpc UpdateBackup(UpdateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup.name=projects/*/locations/*/backupVaults/*/backups/*}"
      body: "backup"
    };
    option (google.api.method_signature) = "backup,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates new backup policy
  rpc CreateBackupPolicy(CreateBackupPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/backupPolicies"
      body: "backup_policy"
    };
    option (google.api.method_signature) =
        "parent,backup_policy,backup_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "BackupPolicy"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns the description of the specified backup policy by backup_policy_id.
  rpc GetBackupPolicy(GetBackupPolicyRequest) returns (BackupPolicy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns list of all available backup policies.
  rpc ListBackupPolicies(ListBackupPoliciesRequest)
      returns (ListBackupPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backupPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates settings of a specific backup policy.
  rpc UpdateBackupPolicy(UpdateBackupPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup_policy.name=projects/*/locations/*/backupPolicies/*}"
      body: "backup_policy"
    };
    option (google.api.method_signature) = "backup_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "BackupPolicy"
      metadata_type: "OperationMetadata"
    };
  }

  // Warning! This operation will permanently delete the backup policy.
  rpc DeleteBackupPolicy(DeleteBackupPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been canceled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
