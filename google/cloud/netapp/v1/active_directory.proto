// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "ActiveDirectoryProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// ListActiveDirectoriesRequest for requesting multiple active directories.
message ListActiveDirectoriesRequest {
  // Required. Parent value for ListActiveDirectoriesRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/ActiveDirectory"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, the server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// ListActiveDirectoriesResponse contains all the active directories requested.
message ListActiveDirectoriesResponse {
  // The list of active directories.
  repeated ActiveDirectory active_directories = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// GetActiveDirectory for getting a single active directory.
message GetActiveDirectoryRequest {
  // Required. Name of the active directory.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/ActiveDirectory"
    }
  ];
}

// CreateActiveDirectoryRequest for creating an active directory.
message CreateActiveDirectoryRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/ActiveDirectory"
    }
  ];

  // Required. Fields of the to be created active directory.
  ActiveDirectory active_directory = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the active directory to create. Must be unique within the
  // parent resource. Must contain only letters, numbers and hyphen, with the
  // first character a letter , the last a letter or a number, and a 63
  // character maximum.
  string active_directory_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// UpdateActiveDirectoryRequest for updating an active directory.
message UpdateActiveDirectoryRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Active Directory resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The volume being updated
  ActiveDirectory active_directory = 2 [(google.api.field_behavior) = REQUIRED];
}

// DeleteActiveDirectoryRequest for deleting a single active directory.
message DeleteActiveDirectoryRequest {
  // Required. Name of the active directory.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/ActiveDirectory"
    }
  ];
}

// ActiveDirectory is the public representation of the active directory config.
message ActiveDirectory {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/ActiveDirectory"
    pattern: "projects/{project}/locations/{location}/activeDirectories/{active_directory}"
    plural: "activeDirectories"
    singular: "activeDirectory"
  };

  // The Active Directory States
  enum State {
    // Unspecified Active Directory State
    STATE_UNSPECIFIED = 0;

    // Active Directory State is Creating
    CREATING = 1;

    // Active Directory State is Ready
    READY = 2;

    // Active Directory State is Updating
    UPDATING = 3;

    // Active Directory State is In use
    IN_USE = 4;

    // Active Directory State is Deleting
    DELETING = 5;

    // Active Directory State is Error
    ERROR = 6;

    // Active Directory State is Diagnosing.
    DIAGNOSING = 7;
  }

  // Identifier. The resource name of the active directory.
  // Format:
  // `projects/{project_number}/locations/{location_id}/activeDirectories/{active_directory_id}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Create time of the active directory.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the AD.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Name of the Active Directory domain
  string domain = 4 [(google.api.field_behavior) = REQUIRED];

  // The Active Directory site the service will limit Domain Controller
  // discovery too.
  string site = 5;

  // Required. Comma separated list of DNS server IP addresses for the Active
  // Directory domain.
  string dns = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. NetBIOSPrefix is used as a prefix for SMB server name.
  string net_bios_prefix = 7 [(google.api.field_behavior) = REQUIRED];

  // The Organizational Unit (OU) within the Windows Active Directory the user
  // belongs to.
  string organizational_unit = 8;

  // If enabled, AES encryption will be enabled for SMB communication.
  bool aes_encryption = 9;

  // Required. Username of the Active Directory domain administrator.
  string username = 10 [(google.api.field_behavior) = REQUIRED];

  // Required. Password of the Active Directory domain administrator.
  string password = 11 [(google.api.field_behavior) = REQUIRED];

  // Optional. Users to be added to the Built-in Backup Operator active
  // directory group.
  repeated string backup_operators = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Users to be added to the Built-in Admininstrators group.
  repeated string administrators = 22 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Domain users to be given the SeSecurityPrivilege.
  repeated string security_operators = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Name of the active directory machine. This optional parameter is used only
  // while creating kerberos volume
  string kdc_hostname = 14;

  // KDC server IP address for the active directory machine.
  string kdc_ip = 15;

  // If enabled, will allow access to local users and LDAP users. If access is
  // needed for only LDAP users, it has to be disabled.
  bool nfs_users_with_ldap = 16;

  // Description of the active directory.
  string description = 17;

  // Specifies whether or not the LDAP traffic needs to be signed.
  bool ldap_signing = 18;

  // If enabled, traffic between the SMB server to Domain Controller (DC) will
  // be encrypted.
  bool encrypt_dc_connections = 19;

  // Labels for the active directory.
  map<string, string> labels = 20;

  // Output only. The state details of the Active Directory.
  string state_details = 21 [(google.api.field_behavior) = OUTPUT_ONLY];
}
