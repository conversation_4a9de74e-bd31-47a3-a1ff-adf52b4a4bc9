// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "SnapshotProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// ListSnapshotsRequest lists snapshots.
message ListSnapshotsRequest {
  // Required. The volume for which to retrieve snapshot information,
  // in the format
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/Snapshot"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 4;

  // List filter.
  string filter = 5;
}

// ListSnapshotsResponse is the result of ListSnapshotsRequest.
message ListSnapshotsResponse {
  // A list of snapshots in the project for the specified volume.
  repeated Snapshot snapshots = 1;

  // The token you can use to retrieve the next page of results. Not returned
  // if there are no more results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// GetSnapshotRequest gets the state of a snapshot.
message GetSnapshotRequest {
  // Required. The snapshot resource name, in the format
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}/snapshots/{snapshot_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Snapshot" }
  ];
}

// CreateSnapshotRequest creates a snapshot.
message CreateSnapshotRequest {
  // Required. The NetApp volume to create the snapshots of, in the format
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/Snapshot"
    }
  ];

  // Required. A snapshot resource
  Snapshot snapshot = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the snapshot to create. Must be unique within the parent
  // resource. Must contain only letters, numbers and hyphen, with the first
  // character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string snapshot_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// DeleteSnapshotRequest deletes a snapshot.
message DeleteSnapshotRequest {
  // Required. The snapshot resource name, in the format
  // `projects/*/locations/*/volumes/*/snapshots/{snapshot_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Snapshot" }
  ];
}

// UpdateSnapshotRequest updates description and/or labels for a snapshot.
message UpdateSnapshotRequest {
  // Required. Mask of fields to update.  At least one path must be supplied in
  // this field.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A snapshot resource
  Snapshot snapshot = 2 [(google.api.field_behavior) = REQUIRED];
}

// Snapshot is a point-in-time version of a Volume's content.
message Snapshot {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/Snapshot"
    pattern: "projects/{project}/locations/{location}/volumes/{volume}/snapshots/{snapshot}"
    plural: "snapshots"
    singular: "snapshot"
  };

  // The Snapshot States
  enum State {
    // Unspecified Snapshot State
    STATE_UNSPECIFIED = 0;

    // Snapshot State is Ready
    READY = 1;

    // Snapshot State is Creating
    CREATING = 2;

    // Snapshot State is Deleting
    DELETING = 3;

    // Snapshot State is Updating
    UPDATING = 4;

    // Snapshot State is Disabled
    DISABLED = 5;

    // Snapshot State is Error
    ERROR = 6;
  }

  // Identifier. The resource name of the snapshot.
  // Format:
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}/snapshots/{snapshot_id}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The snapshot state.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State details of the storage pool
  string state_details = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A description of the snapshot with 2048 characters or less.
  // Requests with longer descriptions will be rejected.
  string description = 4;

  // Output only. Current storage usage for the snapshot in bytes.
  double used_bytes = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the snapshot was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource labels to represent user provided metadata.
  map<string, string> labels = 7;
}
