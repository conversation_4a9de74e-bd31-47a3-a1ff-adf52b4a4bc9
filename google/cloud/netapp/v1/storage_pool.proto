// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/netapp/v1/common.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "StoragePoolProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// GetStoragePoolRequest gets a Storage Pool.
message GetStoragePoolRequest {
  // Required. Name of the storage pool
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/StoragePool"
    }
  ];
}

// ListStoragePoolsRequest lists Storage Pools.
message ListStoragePoolsRequest {
  // Required. Parent value
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/StoragePool"
    }
  ];

  // Optional. The maximum number of items to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Sort results. Supported values are "name", "name desc" or ""
  // (unsorted).
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List filter.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// ListStoragePoolsResponse is the response to a ListStoragePoolsRequest.
message ListStoragePoolsResponse {
  // The list of StoragePools
  repeated StoragePool storage_pools = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// CreateStoragePoolRequest creates a Storage Pool.
message CreateStoragePoolRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/StoragePool"
    }
  ];

  // Required. Id of the requesting storage pool. Must be unique within the
  // parent resource. Must contain only letters, numbers and hyphen, with the
  // first character a letter, the last a letter or a number, and a 63 character
  // maximum.
  string storage_pool_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The required parameters to create a new storage pool.
  StoragePool storage_pool = 3 [(google.api.field_behavior) = REQUIRED];
}

// UpdateStoragePoolRequest updates a Storage Pool.
message UpdateStoragePoolRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // StoragePool resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The pool being updated
  StoragePool storage_pool = 2 [(google.api.field_behavior) = REQUIRED];
}

// DeleteStoragePoolRequest deletes a Storage Pool.
message DeleteStoragePoolRequest {
  // Required. Name of the storage pool
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/StoragePool"
    }
  ];
}

// SwitchActiveReplicaZoneRequest switch the active/replica zone for a regional
// storagePool.
message SwitchActiveReplicaZoneRequest {
  // Required. Name of the storage pool
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/StoragePool"
    }
  ];
}

// StoragePool is a container for volumes with a service level and capacity.
// Volumes can be created in a pool of sufficient available capacity.
// StoragePool capacity is what you are billed for.
message StoragePool {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/StoragePool"
    pattern: "projects/{project}/locations/{location}/storagePools/{storage_pool}"
    plural: "storagePools"
    singular: "storagePool"
  };

  // The Storage Pool States
  enum State {
    // Unspecified Storage Pool State
    STATE_UNSPECIFIED = 0;

    // Storage Pool State is Ready
    READY = 1;

    // Storage Pool State is Creating
    CREATING = 2;

    // Storage Pool State is Deleting
    DELETING = 3;

    // Storage Pool State is Updating
    UPDATING = 4;

    // Storage Pool State is Restoring
    RESTORING = 5;

    // Storage Pool State is Disabled
    DISABLED = 6;

    // Storage Pool State is Error
    ERROR = 7;
  }

  // Identifier. Name of the storage pool
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Service level of the storage pool
  ServiceLevel service_level = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Capacity in GIB of the pool
  int64 capacity_gib = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. Allocated size of all volumes in GIB in the storage pool
  int64 volume_capacity_gib = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Volume count of the storage pool
  int32 volume_count = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the storage pool
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State details of the storage pool
  string state_details = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create time of the storage pool
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description of the storage pool
  string description = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels as key value pairs
  map<string, string> labels = 10 [(google.api.field_behavior) = OPTIONAL];

  // Required. VPC Network name.
  // Format: projects/{project}/global/networks/{network}
  string network = 11 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Optional. Specifies the Active Directory to be used for creating a SMB
  // volume.
  string active_directory = 12 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/ActiveDirectory"
    }
  ];

  // Optional. Specifies the KMS config to be used for volume encryption.
  string kms_config = 13 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/KmsConfig"
    }
  ];

  // Optional. Flag indicating if the pool is NFS LDAP enabled or not.
  bool ldap_enabled = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field is not implemented. The values provided in this field
  // are ignored.
  string psa_range = 15 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Specifies the current pool encryption key source.
  EncryptionType encryption_type = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Deprecated. Used to allow SO pool to access AD or DNS server from other
  // regions.
  optional bool global_access_allowed = 17 [deprecated = true];

  // Optional. True if the storage pool supports Auto Tiering enabled volumes.
  // Default is false. Auto-tiering can be enabled after storage pool creation
  // but it can't be disabled once enabled.
  bool allow_auto_tiering = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the replica zone for regional storagePool.
  string replica_zone = 20 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the active zone for regional storagePool.
  string zone = 21 [(google.api.field_behavior) = OPTIONAL];
}

// ValidateDirectoryServiceRequest validates the directory service policy
// attached to the storage pool.
message ValidateDirectoryServiceRequest {
  // Required. Name of the storage pool
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/StoragePool"
    }
  ];

  // Type of directory service policy attached to the storage pool.
  DirectoryServiceType directory_service_type = 2;
}
