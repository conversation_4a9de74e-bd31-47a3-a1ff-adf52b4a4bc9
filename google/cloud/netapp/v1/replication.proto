// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/netapp/v1/volume.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "ReplicationProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// TransferStats reports all statistics related to replication transfer.
message TransferStats {
  // Cumulative bytes trasferred so far for the replication relatinonship.
  optional int64 transfer_bytes = 1;

  // Cumulative time taken across all transfers for the replication
  // relationship.
  optional google.protobuf.Duration total_transfer_duration = 2;

  // Last transfer size in bytes.
  optional int64 last_transfer_bytes = 3;

  // Time taken during last transfer.
  optional google.protobuf.Duration last_transfer_duration = 4;

  // Lag duration indicates the duration by which Destination region volume
  // content lags behind the primary region volume content.
  optional google.protobuf.Duration lag_duration = 5;

  // Time when progress was updated last.
  optional google.protobuf.Timestamp update_time = 6;

  // Time when last transfer completed.
  optional google.protobuf.Timestamp last_transfer_end_time = 7;

  // A message describing the cause of the last transfer failure.
  optional string last_transfer_error = 8;
}

// Replication is a nested resource under Volume, that describes a
// cross-region replication relationship between 2 volumes in different
// regions.
message Replication {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/Replication"
    pattern: "projects/{project}/locations/{location}/volumes/{volume}/replications/{replication}"
    plural: "replications"
    singular: "replication"
  };

  // The replication states
  // New enum values may be added in future to indicate possible new states.
  enum State {
    // Unspecified replication State
    STATE_UNSPECIFIED = 0;

    // Replication is creating.
    CREATING = 1;

    // Replication is ready.
    READY = 2;

    // Replication is updating.
    UPDATING = 3;

    // Replication is deleting.
    DELETING = 5;

    // Replication is in error state.
    ERROR = 6;

    // Replication is waiting for cluster peering to be established.
    PENDING_CLUSTER_PEERING = 8;

    // Replication is waiting for SVM peering to be established.
    PENDING_SVM_PEERING = 9;
  }

  // New enum values may be added in future to support different replication
  // topology.
  enum ReplicationRole {
    // Unspecified replication role
    REPLICATION_ROLE_UNSPECIFIED = 0;

    // Indicates Source volume.
    SOURCE = 1;

    // Indicates Destination volume.
    DESTINATION = 2;
  }

  // Schedule for Replication.
  // New enum values may be added in future to support different frequency of
  // replication.
  enum ReplicationSchedule {
    // Unspecified ReplicationSchedule
    REPLICATION_SCHEDULE_UNSPECIFIED = 0;

    // Replication happens once every 10 minutes.
    EVERY_10_MINUTES = 1;

    // Replication happens once every hour.
    HOURLY = 2;

    // Replication happens once every day.
    DAILY = 3;
  }

  // Mirroring states.
  // No new value is expected to be added in future.
  enum MirrorState {
    // Unspecified MirrorState
    MIRROR_STATE_UNSPECIFIED = 0;

    // Destination volume is being prepared.
    PREPARING = 1;

    // Destination volume has been initialized and is ready to receive
    // replication transfers.
    MIRRORED = 2;

    // Destination volume is not receiving replication transfers.
    STOPPED = 3;

    // Incremental replication is in progress.
    TRANSFERRING = 4;

    // Baseline replication is in progress.
    BASELINE_TRANSFERRING = 5;

    // Replication is aborted.
    ABORTED = 6;
  }

  // Hybrid replication type.
  enum HybridReplicationType {
    // Unspecified hybrid replication type.
    HYBRID_REPLICATION_TYPE_UNSPECIFIED = 0;

    // Hybrid replication type for migration.
    MIGRATION = 1;

    // Hybrid replication type for continuous replication.
    CONTINUOUS_REPLICATION = 2;
  }

  // Identifier. The resource name of the Replication.
  // Format:
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}/replications/{replication_id}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. State of the replication.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State details of the replication.
  string state_details = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether this points to source or destination.
  ReplicationRole role = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Indicates the schedule for replication.
  ReplicationSchedule replication_schedule = 5
      [(google.api.field_behavior) = REQUIRED];

  // Output only. Indicates the state of mirroring.
  MirrorState mirror_state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Condition of the relationship. Can be one of the following:
  // - true: The replication relationship is healthy. It has not missed the most
  // recent scheduled transfer.
  // - false: The replication relationship is not healthy. It has missed the
  // most recent scheduled transfer.
  optional bool healthy = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Replication create time.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Full name of destination volume resource.
  // Example : "projects/{project}/locations/{location}/volumes/{volume_id}"
  string destination_volume = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Volume" }
  ];

  // Output only. Replication transfer statistics.
  TransferStats transfer_stats = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource labels to represent user provided metadata.
  map<string, string> labels = 12;

  // A description about this replication relationship.
  optional string description = 13;

  // Required. Input only. Destination volume parameters
  DestinationVolumeParameters destination_volume_parameters = 14 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = REQUIRED
  ];

  // Output only. Full name of source volume resource.
  // Example : "projects/{project}/locations/{location}/volumes/{volume_id}"
  string source_volume = 15 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "netapp.googleapis.com/Volume" }
  ];

  // Output only. Hybrid peering details.
  HybridPeeringDetails hybrid_peering_details = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Location of the user cluster.
  string cluster_location = 18 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Type of the hybrid replication.
  HybridReplicationType hybrid_replication_type = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// HybridPeeringDetails contains details about the hybrid peering.
message HybridPeeringDetails {
  // Optional. IP address of the subnet.
  string subnet_ip = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Copy-paste-able commands to be used on user's ONTAP to accept
  // peering requests.
  string command = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expiration time for the peering command to be executed on user's
  // ONTAP.
  google.protobuf.Timestamp command_expiry_time = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Temporary passphrase generated to accept cluster peering command.
  string passphrase = 4 [(google.api.field_behavior) = OPTIONAL];
}

// ListReplications lists replications.
message ListReplicationsRequest {
  // Required. The volume for which to retrieve replication information,
  // in the format
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/Replication"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 4;

  // List filter.
  string filter = 5;
}

// ListReplicationsResponse is the result of ListReplicationsRequest.
message ListReplicationsResponse {
  // A list of replications in the project for the specified volume.
  repeated Replication replications = 1;

  // The token you can use to retrieve the next page of results. Not returned
  // if there are no more results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// GetReplicationRequest gets the state of a replication.
message GetReplicationRequest {
  // Required. The replication resource name, in the format
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}/replications/{replication_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/Replication"
    }
  ];
}

// DestinationVolumeParameters specify input parameters used for creating
// destination volume.
message DestinationVolumeParameters {
  // Required. Existing destination StoragePool name.
  string storage_pool = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/StoragePool"
    }
  ];

  // Desired destination volume resource id. If not specified, source volume's
  // resource id will be used.
  // This value must start with a lowercase letter followed by up to 62
  // lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
  string volume_id = 2;

  // Destination volume's share name. If not specified, source volume's share
  // name will be used.
  string share_name = 3;

  // Description for the destination volume.
  optional string description = 4;

  // Optional. Tiering policy for the volume.
  optional TieringPolicy tiering_policy = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// CreateReplicationRequest creates a replication.
message CreateReplicationRequest {
  // Required. The NetApp volume to create the replications of, in the format
  // `projects/{project_id}/locations/{location}/volumes/{volume_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/Replication"
    }
  ];

  // Required. A replication resource
  Replication replication = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the replication to create. Must be unique within the parent
  // resource. Must contain only letters, numbers and hyphen, with the first
  // character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string replication_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// DeleteReplicationRequest deletes a replication.
message DeleteReplicationRequest {
  // Required. The replication resource name, in the format
  // `projects/*/locations/*/volumes/*/replications/{replication_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/Replication"
    }
  ];
}

// UpdateReplicationRequest updates description and/or labels for a replication.
message UpdateReplicationRequest {
  // Required. Mask of fields to update.  At least one path must be supplied in
  // this field.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A replication resource
  Replication replication = 2 [(google.api.field_behavior) = REQUIRED];
}

// StopReplicationRequest stops a replication until resumed.
message StopReplicationRequest {
  // Required. The resource name of the replication, in the format of
  // projects/{project_id}/locations/{location}/volumes/{volume_id}/replications/{replication_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/Replication"
    }
  ];

  // Indicates whether to stop replication forcefully while data transfer is in
  // progress.
  // Warning! if force is true, this will abort any current transfers
  // and can lead to data loss due to partial transfer.
  // If force is false, stop replication will fail while data transfer is in
  // progress and you will need to retry later.
  bool force = 2;
}

// ResumeReplicationRequest resumes a stopped replication.
message ResumeReplicationRequest {
  // Required. The resource name of the replication, in the format of
  // projects/{project_id}/locations/{location}/volumes/{volume_id}/replications/{replication_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/Replication"
    }
  ];
}

// ReverseReplicationDirectionRequest reverses direction of replication. Source
// becomes destination and destination becomes source.
message ReverseReplicationDirectionRequest {
  // Required. The resource name of the replication, in the format of
  // projects/{project_id}/locations/{location}/volumes/{volume_id}/replications/{replication_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/Replication"
    }
  ];
}

// EstablishPeeringRequest establishes cluster and svm peerings between the
// source and the destination replications.
message EstablishPeeringRequest {
  // Required. The resource name of the replication, in the format of
  // projects/{project_id}/locations/{location}/volumes/{volume_id}/replications/{replication_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/Replication"
    }
  ];

  // Required. Name of the user's local source cluster to be peered with the
  // destination cluster.
  string peer_cluster_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the user's local source vserver svm to be peered with the
  // destination vserver svm.
  string peer_svm_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. List of IPv4 ip addresses to be used for peering.
  repeated string peer_ip_addresses = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Name of the user's local source volume to be peered with the
  // destination volume.
  string peer_volume_name = 5 [(google.api.field_behavior) = REQUIRED];
}

// SyncReplicationRequest syncs the replication from source to destination.
message SyncReplicationRequest {
  // Required. The resource name of the replication, in the format of
  // projects/{project_id}/locations/{location}/volumes/{volume_id}/replications/{replication_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/Replication"
    }
  ];
}
