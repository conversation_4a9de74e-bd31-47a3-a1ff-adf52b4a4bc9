# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "binaryauthorization_proto",
    srcs = [
        "continuous_validation_logging.proto",
        "resources.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "binaryauthorization_proto_with_info",
    deps = [
        ":binaryauthorization_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "binaryauthorization_java_proto",
    deps = [":binaryauthorization_proto"],
)

java_grpc_library(
    name = "binaryauthorization_java_grpc",
    srcs = [":binaryauthorization_proto"],
    deps = [":binaryauthorization_java_proto"],
)

java_gapic_library(
    name = "binaryauthorization_java_gapic",
    srcs = [":binaryauthorization_proto_with_info"],
    grpc_service_config = "binaryauthorization_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "binaryauthorization_v1beta1.yaml",
    test_deps = [
        ":binaryauthorization_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":binaryauthorization_java_proto",
    ],
)

java_gapic_test(
    name = "binaryauthorization_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.binaryauthorization.v1beta1.BinauthzManagementServiceV1Beta1ClientHttpJsonTest",
        "com.google.cloud.binaryauthorization.v1beta1.BinauthzManagementServiceV1Beta1ClientTest",
    ],
    runtime_deps = [":binaryauthorization_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-binaryauthorization-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":binaryauthorization_java_gapic",
        ":binaryauthorization_java_grpc",
        ":binaryauthorization_java_proto",
        ":binaryauthorization_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "binaryauthorization_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/binaryauthorization/apiv1beta1/binaryauthorizationpb",
    protos = [":binaryauthorization_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "binaryauthorization_go_gapic",
    srcs = [":binaryauthorization_proto_with_info"],
    grpc_service_config = "binaryauthorization_grpc_service_config.json",
    importpath = "cloud.google.com/go/binaryauthorization/apiv1beta1;binaryauthorization",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "binaryauthorization_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":binaryauthorization_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-binaryauthorization-v1beta1-go",
    deps = [
        ":binaryauthorization_go_gapic",
        ":binaryauthorization_go_gapic_srcjar-metadata.srcjar",
        ":binaryauthorization_go_gapic_srcjar-snippets.srcjar",
        ":binaryauthorization_go_gapic_srcjar-test.srcjar",
        ":binaryauthorization_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "binaryauthorization_py_gapic",
    srcs = [":binaryauthorization_proto"],
    grpc_service_config = "binaryauthorization_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-binary-authorization",
    ],
    rest_numeric_enums = True,
    service_yaml = "binaryauthorization_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "binaryauthorization_py_gapic_test",
    srcs = [
        "binaryauthorization_py_gapic_pytest.py",
        "binaryauthorization_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":binaryauthorization_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "binaryauthorization-v1beta1-py",
    deps = [
        ":binaryauthorization_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "binaryauthorization_php_proto",
    deps = [":binaryauthorization_proto"],
)

php_gapic_library(
    name = "binaryauthorization_php_gapic",
    srcs = [":binaryauthorization_proto_with_info"],
    grpc_service_config = "binaryauthorization_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "binaryauthorization_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":binaryauthorization_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-binaryauthorization-v1beta1-php",
    deps = [
        ":binaryauthorization_php_gapic",
        ":binaryauthorization_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "binaryauthorization_nodejs_gapic",
    package_name = "@google-cloud/binary-authorization",
    src = ":binaryauthorization_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "binaryauthorization_grpc_service_config.json",
    package = "google.cloud.binaryauthorization.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "binaryauthorization_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "binaryauthorization-v1beta1-nodejs",
    deps = [
        ":binaryauthorization_nodejs_gapic",
        ":binaryauthorization_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "binaryauthorization_ruby_proto",
    deps = [":binaryauthorization_proto"],
)

ruby_grpc_library(
    name = "binaryauthorization_ruby_grpc",
    srcs = [":binaryauthorization_proto"],
    deps = [":binaryauthorization_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "binaryauthorization_ruby_gapic",
    srcs = [":binaryauthorization_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=binaryauthorization.googleapis.com",
        "ruby-cloud-api-shortname=binaryauthorization",
        "ruby-cloud-env-prefix=BINARY_AUTHORIZATION",
        "ruby-cloud-gem-name=google-cloud-binary_authorization-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/binary-authorization/",
        "ruby-cloud-service-override=BinauthzManagementServiceV1Beta1=BinauthzManagementService;SystemPolicyV1Beta1=SystemPolicy",
    ],
    grpc_service_config = "binaryauthorization_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Binary Authorization is a service on Google Cloud that provides centralized software supply-chain security for applications that run on Google Kubernetes Engine (GKE) and GKE on-prem.",
    ruby_cloud_title = "Binary Authorization V1beta1",
    service_yaml = "binaryauthorization_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":binaryauthorization_ruby_grpc",
        ":binaryauthorization_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-binaryauthorization-v1beta1-ruby",
    deps = [
        ":binaryauthorization_ruby_gapic",
        ":binaryauthorization_ruby_grpc",
        ":binaryauthorization_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "binaryauthorization_csharp_proto",
    deps = [":binaryauthorization_proto"],
)

csharp_grpc_library(
    name = "binaryauthorization_csharp_grpc",
    srcs = [":binaryauthorization_proto"],
    deps = [":binaryauthorization_csharp_proto"],
)

csharp_gapic_library(
    name = "binaryauthorization_csharp_gapic",
    srcs = [":binaryauthorization_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "binaryauthorization_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "binaryauthorization_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":binaryauthorization_csharp_grpc",
        ":binaryauthorization_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-binaryauthorization-v1beta1-csharp",
    deps = [
        ":binaryauthorization_csharp_gapic",
        ":binaryauthorization_csharp_grpc",
        ":binaryauthorization_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
