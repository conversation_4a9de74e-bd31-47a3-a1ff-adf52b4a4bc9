// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.binaryauthorization.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.BinaryAuthorization.V1Beta1";
option go_package = "cloud.google.com/go/binaryauthorization/apiv1beta1/binaryauthorizationpb;binaryauthorizationpb";
option java_multiple_files = true;
option java_outer_classname = "BinaryAuthorizationResourcesProto";
option java_package = "com.google.cloud.binaryauthorization.v1beta1";
option php_namespace = "Google\\Cloud\\BinaryAuthorization\\V1beta1";
option ruby_package = "Google::Cloud::BinaryAuthorization::V1beta1";

// A [policy][google.cloud.binaryauthorization.v1beta1.Policy] for Binary Authorization.
message Policy {
  option (google.api.resource) = {
    type: "binaryauthorization.googleapis.com/Policy"
    pattern: "projects/{project}/policy"
    pattern: "locations/{location}/policy"
  };

  enum GlobalPolicyEvaluationMode {
    // Not specified: DISABLE is assumed.
    GLOBAL_POLICY_EVALUATION_MODE_UNSPECIFIED = 0;

    // Enables system policy evaluation.
    ENABLE = 1;

    // Disables system policy evaluation.
    DISABLE = 2;
  }

  // Output only. The resource name, in the format `projects/*/policy`. There is
  // at most one policy per project.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A descriptive comment.
  string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Controls the evaluation of a Google-maintained global admission
  // policy for common system-level images. Images not covered by the global
  // policy will be subject to the project admission policy. This setting
  // has no effect when specified inside a global admission policy.
  GlobalPolicyEvaluationMode global_policy_evaluation_mode = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Admission policy allowlisting. A matching admission request will
  // always be permitted. This feature is typically used to exclude Google or
  // third-party infrastructure images from Binary Authorization policies.
  repeated AdmissionWhitelistPattern admission_whitelist_patterns = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Per-cluster admission rules. Cluster spec format:
  // `location.clusterId`. There can be at most one admission rule per cluster
  // spec.
  // A `location` is either a compute zone (e.g. us-central1-a) or a region
  // (e.g. us-central1).
  // For `clusterId` syntax restrictions see
  // https://cloud.google.com/container-engine/reference/rest/v1/projects.zones.clusters.
  map<string, AdmissionRule> cluster_admission_rules = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Per-kubernetes-namespace admission rules. K8s namespace spec format:
  //   `[a-z.-]+`, e.g. `some-namespace`
  map<string, AdmissionRule> kubernetes_namespace_admission_rules = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Per-kubernetes-service-account admission rules. Service account
  // spec format: `namespace:serviceaccount`. e.g. `test-ns:default`
  map<string, AdmissionRule> kubernetes_service_account_admission_rules = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Per-istio-service-identity admission rules. Istio service
  // identity spec format:
  // `spiffe://<domain>/ns/<namespace>/sa/<serviceaccount>` or
  // `<domain>/ns/<namespace>/sa/<serviceaccount>`
  // e.g. `spiffe://example.com/ns/test-ns/sa/default`
  map<string, AdmissionRule> istio_service_identity_admission_rules = 9 [(google.api.field_behavior) = OPTIONAL];

  // Required. Default admission rule for a cluster without a per-cluster, per-
  // kubernetes-service-account, or per-istio-service-identity admission rule.
  AdmissionRule default_admission_rule = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. Time when the policy was last updated.
  google.protobuf.Timestamp update_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An [admission allowlist pattern][google.cloud.binaryauthorization.v1beta1.AdmissionWhitelistPattern] exempts images
// from checks by [admission rules][google.cloud.binaryauthorization.v1beta1.AdmissionRule].
message AdmissionWhitelistPattern {
  // An image name pattern to allowlist, in the form `registry/path/to/image`.
  // This supports a trailing `*` as a wildcard, but this is allowed only in
  // text after the `registry/` part. `*` wildcard does not match `/`, i.e.,
  // `gcr.io/nginx*` matches `gcr.io/nginx@latest`, but it does not match
  // `gcr.io/nginx/image`. This also supports a trailing `**` wildcard which
  // matches subdirectories, i.e., `gcr.io/nginx**` matches
  // `gcr.io/nginx/image`.
  string name_pattern = 1;
}

// An [admission rule][google.cloud.binaryauthorization.v1beta1.AdmissionRule] specifies either that all container images
// used in a pod creation request must be attested to by one or more
// [attestors][google.cloud.binaryauthorization.v1beta1.Attestor], that all pod creations will be allowed, or that all
// pod creations will be denied.
//
// Images matching an [admission allowlist pattern][google.cloud.binaryauthorization.v1beta1.AdmissionWhitelistPattern]
// are exempted from admission rules and will never block a pod creation.
message AdmissionRule {
  enum EvaluationMode {
    // Do not use.
    EVALUATION_MODE_UNSPECIFIED = 0;

    // This rule allows all all pod creations.
    ALWAYS_ALLOW = 1;

    // This rule allows a pod creation if all the attestors listed in
    // `require_attestations_by` have valid attestations for all of the
    // images in the pod spec.
    REQUIRE_ATTESTATION = 2;

    // This rule denies all pod creations.
    ALWAYS_DENY = 3;
  }

  // Defines the possible actions when a pod creation is denied by an admission
  // rule.
  enum EnforcementMode {
    // Do not use.
    ENFORCEMENT_MODE_UNSPECIFIED = 0;

    // Enforce the admission rule by blocking the pod creation.
    ENFORCED_BLOCK_AND_AUDIT_LOG = 1;

    // Dryrun mode: Audit logging only.  This will allow the pod creation as if
    // the admission request had specified break-glass.
    DRYRUN_AUDIT_LOG_ONLY = 2;
  }

  // Required. How this admission rule will be evaluated.
  EvaluationMode evaluation_mode = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The resource names of the attestors that must attest to
  // a container image, in the format `projects/*/attestors/*`. Each
  // attestor must exist before a policy can reference it.  To add an attestor
  // to a policy the principal issuing the policy change request must be able
  // to read the attestor resource.
  //
  // Note: this field must be non-empty when the evaluation_mode field specifies
  // REQUIRE_ATTESTATION, otherwise it must be empty.
  repeated string require_attestations_by = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The action when a pod creation is denied by the admission rule.
  EnforcementMode enforcement_mode = 3 [(google.api.field_behavior) = REQUIRED];
}

// An [attestor][google.cloud.binaryauthorization.v1beta1.Attestor] that attests to container image
// artifacts. An existing attestor cannot be modified except where
// indicated.
message Attestor {
  option (google.api.resource) = {
    type: "binaryauthorization.googleapis.com/Attestor"
    pattern: "projects/{project}/attestors/{attestor}"
  };

  // Required. The resource name, in the format:
  // `projects/*/attestors/*`. This field may not be updated.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A descriptive comment.  This field may be updated.
  // The field may be displayed in chooser dialogs.
  string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // Required. Identifies an [attestor][google.cloud.binaryauthorization.v1beta1.Attestor] that attests to a
  // container image artifact. This determines how an attestation will
  // be stored, and how it will be used during policy
  // enforcement. Updates may not change the attestor type, but individual
  // attestor fields may be updated.
  oneof attestor_type {
    // A Drydock ATTESTATION_AUTHORITY Note, created by the user.
    UserOwnedDrydockNote user_owned_drydock_note = 3;
  }

  // Output only. Time when the attestor was last updated.
  google.protobuf.Timestamp update_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An [user owned drydock note][google.cloud.binaryauthorization.v1beta1.UserOwnedDrydockNote] references a Drydock
// ATTESTATION_AUTHORITY Note created by the user.
message UserOwnedDrydockNote {
  // Required. The Drydock resource name of a ATTESTATION_AUTHORITY Note,
  // created by the user, in the format: `projects/*/notes/*` (or the legacy
  // `providers/*/notes/*`). This field may not be updated.
  //
  // An attestation by this attestor is stored as a Drydock
  // ATTESTATION_AUTHORITY Occurrence that names a container image and that
  // links to this Note. Drydock is an external dependency.
  string note_reference = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Public keys that verify attestations signed by this
  // attestor.  This field may be updated.
  //
  // If this field is non-empty, one of the specified public keys must
  // verify that an attestation was signed by this attestor for the
  // image specified in the admission request.
  //
  // If this field is empty, this attestor always returns that no
  // valid attestations exist.
  repeated AttestorPublicKey public_keys = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. This field will contain the service account email address
  // that this Attestor will use as the principal when querying Container
  // Analysis. Attestor administrators must grant this service account the
  // IAM role needed to read attestations from the [note_reference][Note] in
  // Container Analysis (`containeranalysis.notes.occurrences.viewer`).
  //
  // This email address is fixed for the lifetime of the Attestor, but callers
  // should not make any other assumptions about the service account email;
  // future versions may use an email based on a different naming pattern.
  string delegation_service_account_email = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A public key in the PkixPublicKey format (see
// https://tools.ietf.org/html/rfc5280#section-******* for details).
// Public keys of this type are typically textually encoded using the PEM
// format.
message PkixPublicKey {
  // Represents a signature algorithm and other information necessary to verify
  // signatures with a given public key.
  // This is based primarily on the public key types supported by Tink's
  // PemKeyType, which is in turn based on KMS's supported signing algorithms.
  // See https://cloud.google.com/kms/docs/algorithms. In the future, BinAuthz
  // might support additional public key types independently of Tink and/or KMS.
  enum SignatureAlgorithm {
    option allow_alias = true;

    // Not specified.
    SIGNATURE_ALGORITHM_UNSPECIFIED = 0;

    // RSASSA-PSS 2048 bit key with a SHA256 digest.
    RSA_PSS_2048_SHA256 = 1;

    // RSASSA-PSS 3072 bit key with a SHA256 digest.
    RSA_PSS_3072_SHA256 = 2;

    // RSASSA-PSS 4096 bit key with a SHA256 digest.
    RSA_PSS_4096_SHA256 = 3;

    // RSASSA-PSS 4096 bit key with a SHA512 digest.
    RSA_PSS_4096_SHA512 = 4;

    // RSASSA-PKCS1-v1_5 with a 2048 bit key and a SHA256 digest.
    RSA_SIGN_PKCS1_2048_SHA256 = 5;

    // RSASSA-PKCS1-v1_5 with a 3072 bit key and a SHA256 digest.
    RSA_SIGN_PKCS1_3072_SHA256 = 6;

    // RSASSA-PKCS1-v1_5 with a 4096 bit key and a SHA256 digest.
    RSA_SIGN_PKCS1_4096_SHA256 = 7;

    // RSASSA-PKCS1-v1_5 with a 4096 bit key and a SHA512 digest.
    RSA_SIGN_PKCS1_4096_SHA512 = 8;

    // ECDSA on the NIST P-256 curve with a SHA256 digest.
    ECDSA_P256_SHA256 = 9;

    // ECDSA on the NIST P-256 curve with a SHA256 digest.
    EC_SIGN_P256_SHA256 = 9;

    // ECDSA on the NIST P-384 curve with a SHA384 digest.
    ECDSA_P384_SHA384 = 10;

    // ECDSA on the NIST P-384 curve with a SHA384 digest.
    EC_SIGN_P384_SHA384 = 10;

    // ECDSA on the NIST P-521 curve with a SHA512 digest.
    ECDSA_P521_SHA512 = 11;

    // ECDSA on the NIST P-521 curve with a SHA512 digest.
    EC_SIGN_P521_SHA512 = 11;
  }

  // A PEM-encoded public key, as described in
  // https://tools.ietf.org/html/rfc7468#section-13
  string public_key_pem = 1;

  // The signature algorithm used to verify a message against a signature using
  // this key.
  // These signature algorithm must match the structure and any object
  // identifiers encoded in `public_key_pem` (i.e. this algorithm must match
  // that of the public key).
  SignatureAlgorithm signature_algorithm = 2;
}

// An [attestor public key][google.cloud.binaryauthorization.v1beta1.AttestorPublicKey] that will be used to verify
// attestations signed by this attestor.
message AttestorPublicKey {
  // Optional. A descriptive comment. This field may be updated.
  string comment = 1 [(google.api.field_behavior) = OPTIONAL];

  // The ID of this public key.
  // Signatures verified by BinAuthz must include the ID of the public key that
  // can be used to verify them, and that ID must match the contents of this
  // field exactly.
  // Additional restrictions on this field can be imposed based on which public
  // key type is encapsulated. See the documentation on `public_key` cases below
  // for details.
  string id = 2;

  // Required. A public key reference or serialized instance. This field may be
  // updated.
  oneof public_key {
    // ASCII-armored representation of a PGP public key, as the entire output by
    // the command `gpg --export --armor <EMAIL>` (either LF or CRLF
    // line endings).
    // When using this field, `id` should be left blank.  The BinAuthz API
    // handlers will calculate the ID and fill it in automatically.  BinAuthz
    // computes this ID as the OpenPGP RFC4880 V4 fingerprint, represented as
    // upper-case hex.  If `id` is provided by the caller, it will be
    // overwritten by the API-calculated ID.
    string ascii_armored_pgp_public_key = 3;

    // A raw PKIX SubjectPublicKeyInfo format public key.
    //
    // NOTE: `id` may be explicitly provided by the caller when using this
    // type of public key, but it MUST be a valid RFC3986 URI. If `id` is left
    // blank, a default one will be computed based on the digest of the DER
    // encoding of the public key.
    PkixPublicKey pkix_public_key = 5;
  }
}
