// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.binaryauthorization.v1beta1;

import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.BinaryAuthorization.V1Beta1";
option go_package = "cloud.google.com/go/binaryauthorization/apiv1beta1/binaryauthorizationpb;binaryauthorizationpb";
option java_multiple_files = true;
option java_outer_classname = "ContinuousValidationLoggingProto";
option java_package = "com.google.cloud.binaryauthorization.v1beta1";
option php_namespace = "Google\\Cloud\\BinaryAuthorization\\V1beta1";
option ruby_package = "Google::Cloud::BinaryAuthorization::V1beta1";

// Represents an auditing event from Continuous Validation.
message ContinuousValidationEvent {
  // An auditing event for one Pod.
  message ContinuousValidationPodEvent {
    // Audit time policy conformance verdict.
    enum PolicyConformanceVerdict {
      // We should always have a verdict. This is an error.
      POLICY_CONFORMANCE_VERDICT_UNSPECIFIED = 0;

      // The pod violates the policy.
      VIOLATES_POLICY = 1;
    }

    // Container image with auditing details.
    message ImageDetails {
      // The container type.
      enum ContainerType {
        // The container type should always be specified. This is an error.
        CONTAINER_TYPE_UNSPECIFIED = 0;

        // A regular deployment.
        CONTAINER = 1;

        // Init container defined as specified at
        // https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
        INIT_CONTAINER = 2;

        // Ephemeral container defined as specified at
        // https://kubernetes.io/docs/concepts/workloads/pods/ephemeral-containers/
        EPHEMERAL_CONTAINER = 3;
      }

      // Result of the audit.
      enum AuditResult {
        // Unspecified result. This is an error.
        AUDIT_RESULT_UNSPECIFIED = 0;

        // Image is allowed.
        ALLOW = 1;

        // Image is denied.
        DENY = 2;
      }

      message CheckResult {
        // A scope specifier for check sets.
        message CheckSetScope {
          oneof scope {
            // Matches a single Kubernetes service account, e.g.
            // 'my-namespace:my-service-account'.
            // `kubernetes_service_account` scope is always more specific than
            // `kubernetes_namespace` scope for the same namespace.
            string kubernetes_service_account = 1;

            // Matches all Kubernetes service accounts in the provided
            // namespace, unless a more specific `kubernetes_service_account`
            // scope already matched.
            string kubernetes_namespace = 2;
          }
        }

        // Result of evaluating one check.
        enum CheckVerdict {
          // We should always have a verdict. This is an error.
          CHECK_VERDICT_UNSPECIFIED = 0;

          // The check was successfully evaluated and the image did not satisfy
          // the check.
          NON_CONFORMANT = 1;
        }

        // The index of the check set.
        string check_set_index = 1;

        // The name of the check set.
        string check_set_name = 2;

        // The scope of the check set.
        CheckSetScope check_set_scope = 3;

        // The index of the check.
        string check_index = 4;

        // The name of the check.
        string check_name = 5;

        // The type of the check.
        string check_type = 6;

        // The verdict of this check.
        CheckVerdict verdict = 7;

        // User-friendly explanation of this check result.
        string explanation = 8;
      }

      // The name of the image.
      string image = 1;

      // The name of the container.
      string container_name = 5;

      // The container type that this image belongs to.
      ContainerType container_type = 6;

      // The result of the audit for this image.
      AuditResult result = 2;

      // Description of the above result.
      string description = 3;

      // List of check results.
      repeated CheckResult check_results = 4;
    }

    // The k8s namespace of the Pod.
    string pod_namespace = 7;

    // The name of the Pod.
    string pod = 1;

    // The name of the policy.
    string policy_name = 8;

    // Deploy time of the Pod from k8s.
    google.protobuf.Timestamp deploy_time = 2;

    // Termination time of the Pod from k8s, or nothing if still running.
    google.protobuf.Timestamp end_time = 3;

    // Auditing verdict for this Pod.
    PolicyConformanceVerdict verdict = 4;

    // List of images with auditing details.
    repeated ImageDetails images = 5;
  }

  // An event describing a user-actionable configuration issue that prevents CV
  // from auditing.
  message ConfigErrorEvent {
    // A description of the issue.
    string description = 1;
  }

  // Type of CV event.
  oneof event_type {
    // Pod event.
    ContinuousValidationPodEvent pod_event = 1;

    // Config error event.
    ConfigErrorEvent config_error_event = 4;
  }
}
