type: google.api.Service
config_version: 3
name: binaryauthorization.googleapis.com
title: Binary Authorization API

apis:
- name: google.cloud.binaryauthorization.v1.BinauthzManagementServiceV1
- name: google.cloud.binaryauthorization.v1.SystemPolicyV1
- name: google.cloud.binaryauthorization.v1.ValidationHelperV1

documentation:
  summary: |-
    The management interface for Binary Authorization, a system providing
    policy control for images deployed to Kubernetes Engine clusters, Anthos
    clusters on VMware, and Cloud Run.
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1/{resource=projects/*/policy}:getIamPolicy'
    additional_bindings:
    - get: '/v1/{resource=projects/*/attestors/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/policy}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/attestors/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/policy}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/attestors/*}:testIamPermissions'
      body: '*'

authentication:
  rules:
  - selector: 'google.cloud.binaryauthorization.v1.BinauthzManagementServiceV1.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.binaryauthorization.v1.SystemPolicyV1.GetSystemPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.binaryauthorization.v1.ValidationHelperV1.ValidateAttestationOccurrence
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
