// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.binaryauthorization.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/binaryauthorization/v1/resources.proto";
import "google/protobuf/empty.proto";
import "grafeas/v1/attestation.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.BinaryAuthorization.V1";
option go_package = "cloud.google.com/go/binaryauthorization/apiv1/binaryauthorizationpb;binaryauthorizationpb";
option java_multiple_files = false;
option java_package = "com.google.protos.google.cloud.binaryauthorization.v1";
option php_namespace = "Google\\Cloud\\BinaryAuthorization\\V1";
option ruby_package = "Google::Cloud::BinaryAuthorization::V1";

// Customer-facing API for Cloud Binary Authorization.

// Google Cloud Management Service for Binary Authorization admission policies
// and attestation authorities.
//
// This API implements a REST model with the following objects:
//
// * [Policy][google.cloud.binaryauthorization.v1.Policy]
// * [Attestor][google.cloud.binaryauthorization.v1.Attestor]
service BinauthzManagementServiceV1 {
  option (google.api.default_host) = "binaryauthorization.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // A [policy][google.cloud.binaryauthorization.v1.Policy] specifies the [attestors][google.cloud.binaryauthorization.v1.Attestor] that must attest to
  // a container image, before the project is allowed to deploy that
  // image. There is at most one policy per project. All image admission
  // requests are permitted if a project has no policy.
  //
  // Gets the [policy][google.cloud.binaryauthorization.v1.Policy] for this project. Returns a default
  // [policy][google.cloud.binaryauthorization.v1.Policy] if the project does not have one.
  rpc GetPolicy(GetPolicyRequest) returns (Policy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/policy}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates or updates a project's [policy][google.cloud.binaryauthorization.v1.Policy], and returns a copy of the
  // new [policy][google.cloud.binaryauthorization.v1.Policy]. A policy is always updated as a whole, to avoid race
  // conditions with concurrent policy enforcement (or management!)
  // requests. Returns NOT_FOUND if the project does not exist, INVALID_ARGUMENT
  // if the request is malformed.
  rpc UpdatePolicy(UpdatePolicyRequest) returns (Policy) {
    option (google.api.http) = {
      put: "/v1/{policy.name=projects/*/policy}"
      body: "policy"
    };
    option (google.api.method_signature) = "policy";
  }

  // Creates an [attestor][google.cloud.binaryauthorization.v1.Attestor], and returns a copy of the new
  // [attestor][google.cloud.binaryauthorization.v1.Attestor]. Returns NOT_FOUND if the project does not exist,
  // INVALID_ARGUMENT if the request is malformed, ALREADY_EXISTS if the
  // [attestor][google.cloud.binaryauthorization.v1.Attestor] already exists.
  rpc CreateAttestor(CreateAttestorRequest) returns (Attestor) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/attestors"
      body: "attestor"
    };
    option (google.api.method_signature) = "parent,attestor_id,attestor";
  }

  // Gets an [attestor][google.cloud.binaryauthorization.v1.Attestor].
  // Returns NOT_FOUND if the [attestor][google.cloud.binaryauthorization.v1.Attestor] does not exist.
  rpc GetAttestor(GetAttestorRequest) returns (Attestor) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/attestors/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an [attestor][google.cloud.binaryauthorization.v1.Attestor].
  // Returns NOT_FOUND if the [attestor][google.cloud.binaryauthorization.v1.Attestor] does not exist.
  rpc UpdateAttestor(UpdateAttestorRequest) returns (Attestor) {
    option (google.api.http) = {
      put: "/v1/{attestor.name=projects/*/attestors/*}"
      body: "attestor"
    };
    option (google.api.method_signature) = "attestor";
  }

  // Lists [attestors][google.cloud.binaryauthorization.v1.Attestor].
  // Returns INVALID_ARGUMENT if the project does not exist.
  rpc ListAttestors(ListAttestorsRequest) returns (ListAttestorsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/attestors"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes an [attestor][google.cloud.binaryauthorization.v1.Attestor]. Returns NOT_FOUND if the
  // [attestor][google.cloud.binaryauthorization.v1.Attestor] does not exist.
  rpc DeleteAttestor(DeleteAttestorRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/attestors/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// API for working with the system policy.
service SystemPolicyV1 {
  option (google.api.default_host) = "binaryauthorization.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Gets the current system policy in the specified location.
  rpc GetSystemPolicy(GetSystemPolicyRequest) returns (Policy) {
    option (google.api.http) = {
      get: "/v1/{name=locations/*/policy}"
    };
    option (google.api.method_signature) = "name";
  }
}

// BinAuthz Attestor verification
service ValidationHelperV1 {
  option (google.api.default_host) = "binaryauthorization.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Returns whether the given Attestation for the given image URI
  // was signed by the given Attestor
  rpc ValidateAttestationOccurrence(ValidateAttestationOccurrenceRequest) returns (ValidateAttestationOccurrenceResponse) {
    option (google.api.http) = {
      post: "/v1/{attestor=projects/*/attestors/*}:validateAttestationOccurrence"
      body: "*"
    };
  }
}

// Request message for [BinauthzManagementService.GetPolicy][].
message GetPolicyRequest {
  // Required. The resource name of the [policy][google.cloud.binaryauthorization.v1.Policy] to retrieve,
  // in the format `projects/*/policy`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "binaryauthorization.googleapis.com/Policy"
    }
  ];
}

// Request message for [BinauthzManagementService.UpdatePolicy][].
message UpdatePolicyRequest {
  // Required. A new or updated [policy][google.cloud.binaryauthorization.v1.Policy] value. The service will
  // overwrite the [policy name][google.cloud.binaryauthorization.v1.Policy.name] field with the resource name in
  // the request URL, in the format `projects/*/policy`.
  Policy policy = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for [BinauthzManagementService.CreateAttestor][].
message CreateAttestorRequest {
  // Required. The parent of this [attestor][google.cloud.binaryauthorization.v1.Attestor].
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The [attestors][google.cloud.binaryauthorization.v1.Attestor] ID.
  string attestor_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The initial [attestor][google.cloud.binaryauthorization.v1.Attestor] value. The service will
  // overwrite the [attestor name][google.cloud.binaryauthorization.v1.Attestor.name] field with the resource name,
  // in the format `projects/*/attestors/*`.
  Attestor attestor = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for [BinauthzManagementService.GetAttestor][].
message GetAttestorRequest {
  // Required. The name of the [attestor][google.cloud.binaryauthorization.v1.Attestor] to retrieve, in the format
  // `projects/*/attestors/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "binaryauthorization.googleapis.com/Attestor"
    }
  ];
}

// Request message for [BinauthzManagementService.UpdateAttestor][].
message UpdateAttestorRequest {
  // Required. The updated [attestor][google.cloud.binaryauthorization.v1.Attestor] value. The service will
  // overwrite the [attestor name][google.cloud.binaryauthorization.v1.Attestor.name] field with the resource name
  // in the request URL, in the format `projects/*/attestors/*`.
  Attestor attestor = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for [BinauthzManagementService.ListAttestors][].
message ListAttestorsRequest {
  // Required. The resource name of the project associated with the
  // [attestors][google.cloud.binaryauthorization.v1.Attestor], in the format `projects/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Requested page size. The server may return fewer results than requested. If
  // unspecified, the server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return. Typically,
  // this is the value of [ListAttestorsResponse.next_page_token][google.cloud.binaryauthorization.v1.ListAttestorsResponse.next_page_token] returned
  // from the previous call to the `ListAttestors` method.
  string page_token = 3;
}

// Response message for [BinauthzManagementService.ListAttestors][].
message ListAttestorsResponse {
  // The list of [attestors][google.cloud.binaryauthorization.v1.Attestor].
  repeated Attestor attestors = 1;

  // A token to retrieve the next page of results. Pass this value in the
  // [ListAttestorsRequest.page_token][google.cloud.binaryauthorization.v1.ListAttestorsRequest.page_token] field in the subsequent call to the
  // `ListAttestors` method to retrieve the next page of results.
  string next_page_token = 2;
}

// Request message for [BinauthzManagementService.DeleteAttestor][].
message DeleteAttestorRequest {
  // Required. The name of the [attestors][google.cloud.binaryauthorization.v1.Attestor] to delete, in the format
  // `projects/*/attestors/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "binaryauthorization.googleapis.com/Attestor"
    }
  ];
}

// Request to read the current system policy.
message GetSystemPolicyRequest {
  // Required. The resource name, in the format `locations/*/policy`.
  // Note that the system policy is not associated with a project.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "binaryauthorization.googleapis.com/Policy"
    }
  ];
}

// Request message for
// [ValidationHelperV1.ValidateAttestationOccurrence][google.cloud.binaryauthorization.v1.ValidationHelperV1.ValidateAttestationOccurrence].
message ValidateAttestationOccurrenceRequest {
  // Required. The resource name of the [Attestor][google.cloud.binaryauthorization.v1.Attestor] of the
  // [occurrence][grafeas.v1.Occurrence], in the format
  // `projects/*/attestors/*`.
  string attestor = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. An [AttestationOccurrence][grafeas.v1.AttestationOccurrence] to
  // be checked that it can be verified by the Attestor. It does not have to be
  // an existing entity in Container Analysis. It must otherwise be a valid
  // AttestationOccurrence.
  grafeas.v1.AttestationOccurrence attestation = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the [Note][grafeas.v1.Note] to which the
  // containing [Occurrence][grafeas.v1.Occurrence] is associated.
  string occurrence_note = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The URI of the artifact (e.g. container image) that is the
  // subject of the containing [Occurrence][grafeas.v1.Occurrence].
  string occurrence_resource_uri = 4 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [ValidationHelperV1.ValidateAttestationOccurrence][google.cloud.binaryauthorization.v1.ValidationHelperV1.ValidateAttestationOccurrence].
message ValidateAttestationOccurrenceResponse {
  // The enum returned in the "result" field.
  enum Result {
    // Unspecified.
    RESULT_UNSPECIFIED = 0;

    // The Attestation was able to verified by the Attestor.
    VERIFIED = 1;

    // The Attestation was not able to verified by the Attestor.
    ATTESTATION_NOT_VERIFIABLE = 2;
  }

  // The result of the Attestation validation.
  Result result = 1;

  // The reason for denial if the Attestation couldn't be validated.
  string denial_reason = 2;
}
