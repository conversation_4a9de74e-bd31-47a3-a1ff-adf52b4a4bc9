// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.ids.logging.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/ids/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggingProto";
option java_package = "com.google.cloud.ids.logging.v1";

// A threat detected by Cloud IDS.
message ThreatLog {
  // Describes the type of severity of the threat.
  enum Severity {
    // Default value - should never be used.
    SEVERITY_UNSPECIFIED = 0;

    LOW = 2;

    MEDIUM = 3;

    HIGH = 4;

    CRITICAL = 5;

    INFORMATIONAL = 6;
  }

  enum Direction {
    // Default value - permitted since Direction is optional.
    DIRECTION_UNDEFINED = 0;

    // Ingress traffic.
    CLIENT_TO_SERVER = 1;

    // Egress traffic.
    SERVER_TO_CLIENT = 2;
  }

  // Name of the threat, e,g. "Suspicious HTTP Evasion"
  string name = 1;

  // Unique ID of the threat.
  string threat_id = 13;

  // The time of the alert.
  google.protobuf.Timestamp alert_time = 2;

  // Severity of threat.
  Severity alert_severity = 19;

  // The type of the threat, e.g. "Spyware".
  string type = 4;

  // Category (sub-type) of the threat, e.g. "code-execution".
  string category = 18;

  // The source IP Address of the packet, e.g. "***********"
  string source_ip_address = 5;

  // The source port of the packet, e.g. 8080
  int32 source_port = 6;

  // The destination IP Address of the packet, e.g. "*************"
  string destination_ip_address = 7;

  // The destination port of the packet, e.g. 100
  int32 destination_port = 8;

  // The IP protocol of the packet, e.g. "TCP".
  string ip_protocol = 9;

  // The direction of the packet - an optional field.
  Direction direction = 10;

  // ID of the Layer 4 session of the threat.
  string session_id = 14;

  // Number of sessions with same source IP, destination IP, application, and
  // type seen within 5 seconds.
  string repeat_count = 15;

  // Application associated with the session.
  string application = 16;

  // Variable field. URI or filename of the relevant threat, if applicable.
  string uri_or_filename = 17;

  // CVE IDs of the threat.
  repeated string cves = 20;

  // Details of the threat reported by the IDS VM
  string details = 11;

  // The network associated with the IDS Endpoint.
  string network = 12;
}

// Traffic detected by Cloud IDS.
// Fields taken from:
// https://docs.paloaltonetworks.com/pan-os/8-1/pan-os-admin/monitoring/use-syslog-for-monitoring/syslog-field-descriptions/traffic-log-fields.html.
message TrafficLog {
  // Time of session start.
  google.protobuf.Timestamp start_time = 1;

  // Elapsed time of the session.
  google.protobuf.Duration elapsed_time = 2;

  // The network associated with the IDS Endpoint.
  string network = 3;

  // The source IP Address of the packet, e.g. "***********"
  string source_ip_address = 4;

  // The source port of the packet, e.g. 8080
  int32 source_port = 5;

  // The destination IP Address of the packet, e.g. "*************"
  string destination_ip_address = 6;

  // The destination port of the packet, e.g. 100
  int32 destination_port = 7;

  // The IP protocol of the packet, e.g. "TCP".
  string ip_protocol = 8;

  // Application associated with the session.
  string application = 9;

  // The direction of the packet.
  string session_id = 12;

  // Number of sessions with same source IP, destination IP, application, and
  // type seen within 5 seconds.
  string repeat_count = 13;

  // Total number of bytes transferred in the session.
  int64 total_bytes = 14;

  // Total number of packets transferred in the session.
  int64 total_packets = 15;
}
