{"methodConfig": [{"name": [{"service": "google.cloud.ids.v1.IDS", "method": "ListEndpoints"}, {"service": "google.cloud.ids.v1.IDS", "method": "GetEndpoint"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.250s", "maxBackoff": "32s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.ids.v1.IDS", "method": "CreateEndpoint"}], "timeout": "3600s"}, {"name": [{"service": "google.cloud.ids.v1.IDS", "method": "DeleteEndpoint"}], "timeout": "3600s"}]}