// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.ids.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/ids/apiv1/idspb;idspb";
option java_multiple_files = true;
option java_outer_classname = "IdsProto";
option java_package = "com.google.cloud.ids.v1";
option ruby_package = "Google::Cloud::IDS::V1";

// The IDS Service
service IDS {
  option (google.api.default_host) = "ids.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists Endpoints in a given project and location.
  rpc ListEndpoints(ListEndpointsRequest) returns (ListEndpointsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/endpoints"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Endpoint.
  rpc GetEndpoint(GetEndpointRequest) returns (Endpoint) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/endpoints/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Endpoint in a given project and location.
  rpc CreateEndpoint(CreateEndpointRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/endpoints"
      body: "endpoint"
    };
    option (google.api.method_signature) = "parent,endpoint,endpoint_id";
    option (google.longrunning.operation_info) = {
      response_type: "Endpoint"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Endpoint.
  rpc DeleteEndpoint(DeleteEndpointRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/endpoints/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Endpoint describes a single IDS endpoint. It defines a forwarding rule to
// which packets can be sent for IDS inspection.
message Endpoint {
  option (google.api.resource) = {
    type: "ids.googleapis.com/Endpoint"
    pattern: "projects/{project}/locations/{location}/endpoints/{endpoint}"
  };

  // Threat severity levels.
  enum Severity {
    // Not set.
    SEVERITY_UNSPECIFIED = 0;

    // Informational alerts.
    INFORMATIONAL = 1;

    // Low severity alerts.
    LOW = 2;

    // Medium severity alerts.
    MEDIUM = 3;

    // High severity alerts.
    HIGH = 4;

    // Critical severity alerts.
    CRITICAL = 5;
  }

  // Endpoint state
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // Being created.
    CREATING = 1;

    // Active and ready for traffic.
    READY = 2;

    // Being deleted.
    DELETING = 3;
  }

  // Output only. The name of the endpoint.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The create time timestamp.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update time timestamp.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels of the endpoint.
  map<string, string> labels = 4;

  // Required. The fully qualified URL of the network to which the IDS Endpoint is
  // attached.
  string network = 5 [(google.api.field_behavior) = REQUIRED];

  // Output only. The fully qualified URL of the endpoint's ILB Forwarding Rule.
  string endpoint_forwarding_rule = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The IP address of the IDS Endpoint's ILB.
  string endpoint_ip = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-provided description of the endpoint
  string description = 8;

  // Required. Lowest threat severity that this endpoint will alert on.
  Severity severity = 9 [(google.api.field_behavior) = REQUIRED];

  // Output only. Current state of the endpoint.
  State state = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether the endpoint should report traffic logs in addition to threat logs.
  bool traffic_logs = 13;
}

message ListEndpointsRequest {
  // Required. The parent, which owns this collection of endpoints.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "ids.googleapis.com/Endpoint"
    }
  ];

  // Optional. The maximum number of endpoints to return. The service may return fewer
  // than this value.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListEndpoints` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListEndpoints` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter expression, following the syntax outlined in
  // https://google.aip.dev/160.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. One or more fields to compare and use to sort the output.
  // See https://google.aip.dev/132#ordering.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

message ListEndpointsResponse {
  // The list of endpoints response.
  repeated Endpoint endpoints = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

message GetEndpointRequest {
  // Required. The name of the endpoint to retrieve.
  // Format: `projects/{project}/locations/{location}/endpoints/{endpoint}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "ids.googleapis.com/Endpoint"
    }
  ];
}

message CreateEndpointRequest {
  // Required. The endpoint's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "ids.googleapis.com/Endpoint"
    }
  ];

  // Required. The endpoint identifier. This will be part of the endpoint's
  // resource name.
  // This value must start with a lowercase letter followed by up to 62
  // lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
  // Values that do not match this pattern will trigger an INVALID_ARGUMENT
  // error.
  string endpoint_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The endpoint to create.
  Endpoint endpoint = 3 [(google.api.field_behavior) = REQUIRED];

  // An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

message DeleteEndpointRequest {
  // Required. The name of the endpoint to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "ids.googleapis.com/Endpoint"
    }
  ];

  // An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2;
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
