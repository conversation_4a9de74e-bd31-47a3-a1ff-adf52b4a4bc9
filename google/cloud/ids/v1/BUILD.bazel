# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "ids_proto",
    srcs = [
        "ids.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "ids_proto_with_info",
    deps = [
        ":ids_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "ids_java_proto",
    deps = [":ids_proto"],
)

java_grpc_library(
    name = "ids_java_grpc",
    srcs = [":ids_proto"],
    deps = [":ids_java_proto"],
)

java_gapic_library(
    name = "ids_java_gapic",
    srcs = [":ids_proto_with_info"],
    gapic_yaml = "ids_gapic.yaml",
    grpc_service_config = "ids_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "ids_v1.yaml",
    test_deps = [
        ":ids_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":ids_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "ids_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.ids.v1.IDSClientHttpJsonTest",
        "com.google.cloud.ids.v1.IDSClientTest",
    ],
    runtime_deps = [":ids_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-ids-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":ids_java_gapic",
        ":ids_java_grpc",
        ":ids_java_proto",
        ":ids_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "ids_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/ids/apiv1/idspb",
    protos = [":ids_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "ids_go_gapic",
    srcs = [":ids_proto_with_info"],
    grpc_service_config = "ids_grpc_service_config.json",
    importpath = "cloud.google.com/go/ids/apiv1;ids",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "ids_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":ids_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-ids-v1-go",
    deps = [
        ":ids_go_gapic",
        ":ids_go_gapic_srcjar-metadata.srcjar",
        ":ids_go_gapic_srcjar-snippets.srcjar",
        ":ids_go_gapic_srcjar-test.srcjar",
        ":ids_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "ids_py_gapic",
    srcs = [":ids_proto"],
    grpc_service_config = "ids_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "ids_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "ids_py_gapic_test",
    srcs = [
        "ids_py_gapic_pytest.py",
        "ids_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":ids_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "ids-v1-py",
    deps = [
        ":ids_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "ids_php_proto",
    deps = [":ids_proto"],
)

php_gapic_library(
    name = "ids_php_gapic",
    srcs = [":ids_proto_with_info"],
    gapic_yaml = "ids_gapic.yaml",
    grpc_service_config = "ids_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "ids_v1.yaml",
    transport = "grpc+rest",
    deps = [":ids_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-ids-v1-php",
    deps = [
        ":ids_php_gapic",
        ":ids_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "ids_nodejs_gapic",
    package_name = "@google-cloud/ids",
    src = ":ids_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "ids_grpc_service_config.json",
    package = "google.cloud.ids.v1",
    rest_numeric_enums = True,
    service_yaml = "ids_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "ids-v1-nodejs",
    deps = [
        ":ids_nodejs_gapic",
        ":ids_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "ids_ruby_proto",
    deps = [":ids_proto"],
)

ruby_grpc_library(
    name = "ids_ruby_grpc",
    srcs = [":ids_proto"],
    deps = [":ids_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "ids_ruby_gapic",
    srcs = [":ids_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-ids-v1",
        "ruby-cloud-product-url=https://cloud.google.com/intrusion-detection-system/",
        "ruby-cloud-api-id=ids.googleapis.com",
        "ruby-cloud-api-shortname=ids",
        "ruby-cloud-namespace-override=Ids=IDS",
    ],
    grpc_service_config = "ids_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud IDS is an intrusion detection service that provides threat detection for intrusions, malware, spyware, and command-and-control attacks on your network. Cloud IDS works by creating a Google-managed peered network with mirrored VMs. Traffic in the peered network is mirrored, and then inspected by Palo Alto Networks threat protection technologies to provide advanced threat detection. You can mirror all traffic or you can mirror filtered traffic, based on protocol, IP address range, or ingress and egress.",
    ruby_cloud_title = "Cloud IDS V1",
    service_yaml = "ids_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":ids_ruby_grpc",
        ":ids_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-ids-v1-ruby",
    deps = [
        ":ids_ruby_gapic",
        ":ids_ruby_grpc",
        ":ids_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "ids_csharp_proto",
    deps = [":ids_proto"],
)

csharp_grpc_library(
    name = "ids_csharp_grpc",
    srcs = [":ids_proto"],
    deps = [":ids_csharp_proto"],
)

csharp_gapic_library(
    name = "ids_csharp_gapic",
    srcs = [":ids_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "ids_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "ids_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":ids_csharp_grpc",
        ":ids_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-ids-v1-csharp",
    deps = [
        ":ids_csharp_gapic",
        ":ids_csharp_grpc",
        ":ids_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "ids_cc_proto",
    deps = [":ids_proto"],
)

cc_grpc_library(
    name = "ids_cc_grpc",
    srcs = [":ids_proto"],
    grpc_only = True,
    deps = [":ids_cc_proto"],
)
