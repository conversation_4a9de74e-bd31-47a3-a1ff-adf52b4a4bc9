# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "vision_proto",
    srcs = [
        "face.proto",
        "geometry.proto",
        "image_annotator.proto",
        "product_search.proto",
        "product_search_service.proto",
        "text_annotation.proto",
        "web_detection.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:color_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "vision_proto_with_info",
    deps = [
        ":vision_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "vision_java_proto",
    deps = [":vision_proto"],
)

java_grpc_library(
    name = "vision_java_grpc",
    srcs = [":vision_proto"],
    deps = [":vision_java_proto"],
)

java_gapic_library(
    name = "vision_java_gapic",
    srcs = [":vision_proto_with_info"],
    gapic_yaml = "vision_gapic.yaml",
    grpc_service_config = "vision_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "vision_v1p4beta1.yaml",
    test_deps = [
        ":vision_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":vision_java_proto",
    ],
)

java_gapic_test(
    name = "vision_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.vision.v1p4beta1.ImageAnnotatorClientHttpJsonTest",
        "com.google.cloud.vision.v1p4beta1.ImageAnnotatorClientTest",
        "com.google.cloud.vision.v1p4beta1.ProductSearchClientHttpJsonTest",
        "com.google.cloud.vision.v1p4beta1.ProductSearchClientTest",
    ],
    runtime_deps = [":vision_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-vision-v1p4beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":vision_java_gapic",
        ":vision_java_grpc",
        ":vision_java_proto",
        ":vision_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "vision_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/vision/apiv1p4beta1/visionpb",
    protos = [":vision_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:color_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "vision_go_gapic",
    srcs = [":vision_proto_with_info"],
    grpc_service_config = "vision_grpc_service_config.json",
    importpath = "cloud.google.com/go/vision/apiv1p4beta1;vision",
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "vision_v1p4beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":vision_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-vision-v1p4beta1-go",
    deps = [
        ":vision_go_gapic",
        ":vision_go_gapic_srcjar-snippets.srcjar",
        ":vision_go_gapic_srcjar-test.srcjar",
        ":vision_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "vision_py_gapic",
    srcs = [":vision_proto"],
    grpc_service_config = "vision_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "vision_v1p4beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "vision_py_gapic_test",
    srcs = [
        "vision_py_gapic_pytest.py",
        "vision_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":vision_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "vision-v1p4beta1-py",
    deps = [
        ":vision_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "vision_php_proto",
    deps = [":vision_proto"],
)

php_gapic_library(
    name = "vision_php_gapic",
    srcs = [":vision_proto_with_info"],
    gapic_yaml = "vision_gapic.yaml",
    grpc_service_config = "vision_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "vision_v1p4beta1.yaml",
    transport = "grpc+rest",
    deps = [":vision_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-vision-v1p4beta1-php",
    deps = [
        ":vision_php_gapic",
        ":vision_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "vision_nodejs_gapic",
    package_name = "@google-cloud/vision",
    src = ":vision_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "vision_grpc_service_config.json",
    main_service = "vision",
    package = "google.cloud.vision.v1p4beta1",
    rest_numeric_enums = True,
    service_yaml = "vision_v1p4beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "vision-v1p4beta1-nodejs",
    deps = [
        ":vision_nodejs_gapic",
        ":vision_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "vision_ruby_proto",
    deps = [":vision_proto"],
)

ruby_grpc_library(
    name = "vision_ruby_grpc",
    srcs = [":vision_proto"],
    deps = [":vision_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "vision_ruby_gapic",
    srcs = [":vision_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-vision-v1p4beta1",
        "ruby-cloud-env-prefix=VISION",
        "ruby-cloud-product-url=https://cloud.google.com/vision",
        "ruby-cloud-api-id=vision.googleapis.com",
        "ruby-cloud-api-shortname=vision",
    ],
    grpc_service_config = "vision_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Vision API allows developers to easily integrate vision detection features within applications, including image labeling, face and landmark detection, optical character recognition (OCR), and tagging of explicit content.",
    ruby_cloud_title = "Cloud Vision V1p4beta1",
    service_yaml = "vision_v1p4beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":vision_ruby_grpc",
        ":vision_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-vision-v1p4beta1-ruby",
    deps = [
        ":vision_ruby_gapic",
        ":vision_ruby_grpc",
        ":vision_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "vision_csharp_proto",
    deps = [":vision_proto"],
)

csharp_grpc_library(
    name = "vision_csharp_grpc",
    srcs = [":vision_proto"],
    deps = [":vision_csharp_proto"],
)

csharp_gapic_library(
    name = "vision_csharp_gapic",
    srcs = [":vision_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "vision_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "vision_v1p4beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":vision_csharp_grpc",
        ":vision_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-vision-v1p4beta1-csharp",
    deps = [
        ":vision_csharp_gapic",
        ":vision_csharp_grpc",
        ":vision_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
