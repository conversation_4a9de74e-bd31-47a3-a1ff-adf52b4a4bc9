type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
interfaces:
  # The fully qualified name of the API interface.
- name: google.cloud.vision.v1p4beta1.ProductSearch
  methods:
  - name: ImportProductSets
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 86400000

  # The fully qualified name of the API interface.
- name: google.cloud.vision.v1p4beta1.ImageAnnotator
  methods:
  - name: AsyncBatchAnnotateImages
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 300000
  - name: AsyncBatchAnnotateFiles
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 86400000
