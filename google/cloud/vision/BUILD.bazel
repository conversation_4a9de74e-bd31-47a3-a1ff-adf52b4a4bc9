# This build file includes a target for the Ruby wrapper library for
# google-cloud-vision.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for vision.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "vision_ruby_wrapper",
    srcs = ["//google/cloud/vision/v1:vision_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-vision",
        "ruby-cloud-env-prefix=VISION",
        "ruby-cloud-wrapper-of=v1:0.13;v1p3beta1:0.12",
        "ruby-cloud-product-url=https://cloud.google.com/vision",
        "ruby-cloud-api-id=vision.googleapis.com",
        "ruby-cloud-api-shortname=vision",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Cloud Vision API allows developers to easily integrate vision detection features within applications, including image labeling, face and landmark detection, optical character recognition (OCR), and tagging of explicit content.",
    ruby_cloud_title = "Cloud Vision",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-vision-ruby",
    deps = [
        ":vision_ruby_wrapper",
    ],
)
