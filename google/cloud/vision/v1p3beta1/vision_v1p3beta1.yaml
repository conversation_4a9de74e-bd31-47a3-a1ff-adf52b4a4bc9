type: google.api.Service
config_version: 3
name: vision.googleapis.com
title: Cloud Vision API

apis:
- name: google.cloud.vision.v1p3beta1.ImageAnnotator
- name: google.cloud.vision.v1p3beta1.ProductSearch

types:
- name: google.cloud.vision.v1p3beta1.AnnotateFileResponse
- name: google.cloud.vision.v1p3beta1.AsyncBatchAnnotateFilesResponse
- name: google.cloud.vision.v1p3beta1.BatchOperationMetadata
- name: google.cloud.vision.v1p3beta1.ImportProductSetsResponse
- name: google.cloud.vision.v1p3beta1.OperationMetadata

documentation:
  summary: |-
    Integrates Google Vision features, including image labeling, face, logo,
    and landmark detection, optical character recognition (OCR), and detection
    of explicit content, into applications.

authentication:
  rules:
  - selector: 'google.cloud.vision.v1p3beta1.ImageAnnotator.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-vision
  - selector: 'google.cloud.vision.v1p3beta1.ProductSearch.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-vision
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-vision
