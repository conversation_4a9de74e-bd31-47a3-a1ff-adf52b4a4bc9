{"methodConfig": [{"name": [{"service": "google.cloud.confidentialcomputing.v1alpha1.ConfidentialComputing", "method": "CreateChallenge"}, {"service": "google.cloud.confidentialcomputing.v1alpha1.ConfidentialComputing", "method": "VerifyAttestation"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.confidentialcomputing.v1alpha1.ConfidentialComputing"}], "timeout": "60s"}]}