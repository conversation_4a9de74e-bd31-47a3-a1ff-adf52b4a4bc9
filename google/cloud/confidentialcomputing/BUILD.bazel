# This build file includes a target for the Ruby wrapper library for
# google-cloud-confidential_computing.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for confidentialcomputing.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "confidentialcomputing_ruby_wrapper",
    srcs = ["//google/cloud/confidentialcomputing/v1:confidentialcomputing_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-confidential_computing",
        "ruby-cloud-wrapper-of=v1:0.7",
    ],
    service_yaml = "//google/cloud/confidentialcomputing/v1:confidentialcomputing_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-confidentialcomputing-ruby",
    deps = [
        ":confidentialcomputing_ruby_wrapper",
    ],
)
