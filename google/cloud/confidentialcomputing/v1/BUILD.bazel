# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "confidentialcomputing_proto",
    srcs = [
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "confidentialcomputing_proto_with_info",
    deps = [
        ":confidentialcomputing_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "confidentialcomputing_java_proto",
    deps = [":confidentialcomputing_proto"],
)

java_grpc_library(
    name = "confidentialcomputing_java_grpc",
    srcs = [":confidentialcomputing_proto"],
    deps = [":confidentialcomputing_java_proto"],
)

java_gapic_library(
    name = "confidentialcomputing_java_gapic",
    srcs = [":confidentialcomputing_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "confidentialcomputing_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "confidentialcomputing_v1.yaml",
    test_deps = [
        ":confidentialcomputing_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":confidentialcomputing_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "confidentialcomputing_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.confidentialcomputing.v1.ConfidentialComputingClientHttpJsonTest",
        "com.google.cloud.confidentialcomputing.v1.ConfidentialComputingClientTest",
    ],
    runtime_deps = [":confidentialcomputing_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-confidentialcomputing-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":confidentialcomputing_java_gapic",
        ":confidentialcomputing_java_grpc",
        ":confidentialcomputing_java_proto",
        ":confidentialcomputing_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "confidentialcomputing_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/confidentialcomputing/apiv1/confidentialcomputingpb",
    protos = [":confidentialcomputing_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "confidentialcomputing_go_gapic",
    srcs = [":confidentialcomputing_proto_with_info"],
    grpc_service_config = "confidentialcomputing_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/confidentialcomputing/apiv1;confidentialcomputing",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "confidentialcomputing_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":confidentialcomputing_go_proto",
        "//google/cloud/location:location_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-confidentialcomputing-v1-go",
    deps = [
        ":confidentialcomputing_go_gapic",
        ":confidentialcomputing_go_gapic_srcjar-metadata.srcjar",
        ":confidentialcomputing_go_gapic_srcjar-snippets.srcjar",
        ":confidentialcomputing_go_gapic_srcjar-test.srcjar",
        ":confidentialcomputing_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "confidentialcomputing_py_gapic",
    srcs = [":confidentialcomputing_proto"],
    grpc_service_config = "confidentialcomputing_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "confidentialcomputing_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "confidentialcomputing_py_gapic_test",
    srcs = [
        "confidentialcomputing_py_gapic_pytest.py",
        "confidentialcomputing_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":confidentialcomputing_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "confidentialcomputing-v1-py",
    deps = [
        ":confidentialcomputing_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "confidentialcomputing_php_proto",
    deps = [":confidentialcomputing_proto"],
)

php_gapic_library(
    name = "confidentialcomputing_php_gapic",
    srcs = [":confidentialcomputing_proto_with_info"],
    grpc_service_config = "confidentialcomputing_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "confidentialcomputing_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":confidentialcomputing_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-confidentialcomputing-v1-php",
    deps = [
        ":confidentialcomputing_php_gapic",
        ":confidentialcomputing_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "confidentialcomputing_nodejs_gapic",
    package_name = "@google-cloud/confidentialcomputing",
    src = ":confidentialcomputing_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "confidentialcomputing_v1_grpc_service_config.json",
    package = "google.cloud.confidentialcomputing.v1",
    rest_numeric_enums = True,
    service_yaml = "confidentialcomputing_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "confidentialcomputing-v1-nodejs",
    deps = [
        ":confidentialcomputing_nodejs_gapic",
        ":confidentialcomputing_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "confidentialcomputing_ruby_proto",
    deps = [":confidentialcomputing_proto"],
)

ruby_grpc_library(
    name = "confidentialcomputing_ruby_grpc",
    srcs = [":confidentialcomputing_proto"],
    deps = [":confidentialcomputing_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "confidentialcomputing_ruby_gapic",
    srcs = [":confidentialcomputing_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-confidential_computing-v1"],
    grpc_service_config = "confidentialcomputing_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "confidentialcomputing_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":confidentialcomputing_ruby_grpc",
        ":confidentialcomputing_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-confidentialcomputing-v1-ruby",
    deps = [
        ":confidentialcomputing_ruby_gapic",
        ":confidentialcomputing_ruby_grpc",
        ":confidentialcomputing_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "confidentialcomputing_csharp_proto",
    extra_opts = [],
    deps = [":confidentialcomputing_proto"],
)

csharp_grpc_library(
    name = "confidentialcomputing_csharp_grpc",
    srcs = [":confidentialcomputing_proto"],
    deps = [":confidentialcomputing_csharp_proto"],
)

csharp_gapic_library(
    name = "confidentialcomputing_csharp_gapic",
    srcs = [":confidentialcomputing_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "confidentialcomputing_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "confidentialcomputing_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":confidentialcomputing_csharp_grpc",
        ":confidentialcomputing_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-confidentialcomputing-v1-csharp",
    deps = [
        ":confidentialcomputing_csharp_gapic",
        ":confidentialcomputing_csharp_grpc",
        ":confidentialcomputing_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "confidentialcomputing_cc_proto",
    deps = [":confidentialcomputing_proto"],
)

cc_grpc_library(
    name = "confidentialcomputing_cc_grpc",
    srcs = [":confidentialcomputing_proto"],
    grpc_only = True,
    deps = [":confidentialcomputing_cc_proto"],
)
