type: google.api.Service
config_version: 3
name: confidentialcomputing.googleapis.com
title: Confidential Computing API

apis:
- name: google.cloud.confidentialcomputing.v1.ConfidentialComputing
- name: google.cloud.location.Locations

documentation:
  summary: Attestation verifier for Confidential Space.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'

authentication:
  rules:
  - selector: google.cloud.confidentialcomputing.v1.ConfidentialComputing.CreateChallenge
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.confidentialcomputing.v1.ConfidentialComputing.VerifyAttestation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1134314&template=1640550
  documentation_uri: https://cloud.google.com/confidential-computing
  api_short_name: confidentialcomputing
  github_label: 'api: confidentialcomputing'
  doc_tag_prefix: confidentialcomputing
  organization: CLOUD
  library_settings:
  - version: google.cloud.confidentialcomputing.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
