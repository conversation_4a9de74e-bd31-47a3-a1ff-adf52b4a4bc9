load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
proto_library(
    name = "common_resources_proto",
    srcs = ["common_resources.proto"],
    deps = ["//google/api:resource_proto"],
)

proto_library(
    name = "extended_operations_proto",
    srcs = ["extended_operations.proto"],
    deps = ["@com_google_protobuf//:descriptor_proto"],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "extended_operations_java_proto",
    deps = [
        ":extended_operations_proto",
    ],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-apps-script-type-java",
    transport = "grpc+rest",
    deps = [
        "extended_operations_java_proto",
        ":extended_operations_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "go_proto_library")

go_proto_library(
    name = "extended_operations_go_proto",
    importpath = "google.golang.org/genproto/googleapis/cloud/extendedops",
    protos = [
        ":extended_operations_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "py_gapic_assembly_pkg", "py_proto_library")

py_proto_library(
    name = "extended_operations_py_proto",
    deps = [":extended_operations_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "extended-operations-py",
    deps = [
        ":extended_operations_py_proto"
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "extended_operations_php_proto",
    deps = [":extended_operations_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate php files for these protos.
# Open Source Packages
php_gapic_assembly_pkg(
    name = "extended-operations-php",
    deps = [":extended_operations_php_proto"],
)

##############################################################################
# Node.js
##############################################################################
# Node does not have langauge-specific proto_library targets

##############################################################################
# Ruby
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "ruby_proto_library")

ruby_proto_library(
    name = "extended_operations_ruby_proto",
    deps = [":extended_operations_proto"],
)

##############################################################################
# C#
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "csharp_proto_library")

csharp_proto_library(
    name = "extended_operations_csharp_proto",
    deps = [":extended_operations_proto"],
)

##############################################################################
#  C++
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "cc_proto_library")

cc_proto_library(
    name = "extended_operations_cc_proto",
    deps = [":extended_operations_proto"],
)
