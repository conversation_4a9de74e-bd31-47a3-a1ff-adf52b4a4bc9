{"methodConfig": [{"name": [{"service": "google.cloud.storageinsights.v1.StorageInsights", "method": "GetReportConfig"}, {"service": "google.cloud.storageinsights.v1.StorageInsights", "method": "GetReportDetail"}, {"service": "google.cloud.storageinsights.v1.StorageInsights", "method": "ListReportConfigs"}, {"service": "google.cloud.storageinsights.v1.StorageInsights", "method": "ListReportDetails"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.storageinsights.v1.StorageInsights"}], "timeout": "60s"}]}