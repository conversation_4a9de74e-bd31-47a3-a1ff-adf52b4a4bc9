# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "storageinsights_proto",
    srcs = [
        "storageinsights.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:date_proto",
        "//google/type:datetime_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "storageinsights_proto_with_info",
    deps = [
        ":storageinsights_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "storageinsights_java_proto",
    deps = [":storageinsights_proto"],
)

java_grpc_library(
    name = "storageinsights_java_grpc",
    srcs = [":storageinsights_proto"],
    deps = [":storageinsights_java_proto"],
)

java_gapic_library(
    name = "storageinsights_java_gapic",
    srcs = [":storageinsights_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "storageinsights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storageinsights_v1.yaml",
    test_deps = [
        ":storageinsights_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":storageinsights_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "storageinsights_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.storageinsights.v1.StorageInsightsClientHttpJsonTest",
        "com.google.cloud.storageinsights.v1.StorageInsightsClientTest",
    ],
    runtime_deps = [":storageinsights_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-storageinsights-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":storageinsights_java_gapic",
        ":storageinsights_java_grpc",
        ":storageinsights_java_proto",
        ":storageinsights_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "storageinsights_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/storageinsights/apiv1/storageinsightspb",
    protos = [":storageinsights_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:date_go_proto",
        "//google/type:datetime_go_proto",
    ],
)

go_gapic_library(
    name = "storageinsights_go_gapic",
    srcs = [":storageinsights_proto_with_info"],
    grpc_service_config = "storageinsights_grpc_service_config.json",
    importpath = "cloud.google.com/go/storageinsights/apiv1;storageinsights",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "storageinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":storageinsights_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-storageinsights-v1-go",
    deps = [
        ":storageinsights_go_gapic",
        ":storageinsights_go_gapic_srcjar-metadata.srcjar",
        ":storageinsights_go_gapic_srcjar-snippets.srcjar",
        ":storageinsights_go_gapic_srcjar-test.srcjar",
        ":storageinsights_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "storageinsights_py_gapic",
    srcs = [":storageinsights_proto"],
    grpc_service_config = "storageinsights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storageinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "storageinsights_py_gapic_test",
    srcs = [
        "storageinsights_py_gapic_pytest.py",
        "storageinsights_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":storageinsights_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "storageinsights-v1-py",
    deps = [
        ":storageinsights_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "storageinsights_php_proto",
    deps = [":storageinsights_proto"],
)

php_gapic_library(
    name = "storageinsights_php_gapic",
    srcs = [":storageinsights_proto_with_info"],
    grpc_service_config = "storageinsights_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "storageinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [":storageinsights_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-storageinsights-v1-php",
    deps = [
        ":storageinsights_php_gapic",
        ":storageinsights_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "storageinsights_nodejs_gapic",
    package_name = "@google-cloud/storageinsights",
    src = ":storageinsights_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "storageinsights_grpc_service_config.json",
    package = "google.cloud.storageinsights.v1",
    rest_numeric_enums = True,
    service_yaml = "storageinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "storageinsights-v1-nodejs",
    deps = [
        ":storageinsights_nodejs_gapic",
        ":storageinsights_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "storageinsights_ruby_proto",
    deps = [":storageinsights_proto"],
)

ruby_grpc_library(
    name = "storageinsights_ruby_grpc",
    srcs = [":storageinsights_proto"],
    deps = [":storageinsights_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "storageinsights_ruby_gapic",
    srcs = [":storageinsights_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-storage_insights-v1"],
    grpc_service_config = "storageinsights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storageinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":storageinsights_ruby_grpc",
        ":storageinsights_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-storageinsights-v1-ruby",
    deps = [
        ":storageinsights_ruby_gapic",
        ":storageinsights_ruby_grpc",
        ":storageinsights_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "storageinsights_csharp_proto",
    deps = [":storageinsights_proto"],
)

csharp_grpc_library(
    name = "storageinsights_csharp_grpc",
    srcs = [":storageinsights_proto"],
    deps = [":storageinsights_csharp_proto"],
)

csharp_gapic_library(
    name = "storageinsights_csharp_gapic",
    srcs = [":storageinsights_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "storageinsights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storageinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":storageinsights_csharp_grpc",
        ":storageinsights_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-storageinsights-v1-csharp",
    deps = [
        ":storageinsights_csharp_gapic",
        ":storageinsights_csharp_grpc",
        ":storageinsights_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "storageinsights_cc_proto",
    deps = [":storageinsights_proto"],
)

cc_grpc_library(
    name = "storageinsights_cc_grpc",
    srcs = [":storageinsights_proto"],
    grpc_only = True,
    deps = [":storageinsights_cc_proto"],
)
