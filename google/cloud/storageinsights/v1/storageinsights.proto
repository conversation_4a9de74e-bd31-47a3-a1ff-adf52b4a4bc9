// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.storageinsights.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/date.proto";
import "google/type/datetime.proto";

option csharp_namespace = "Google.Cloud.StorageInsights.V1";
option go_package = "cloud.google.com/go/storageinsights/apiv1/storageinsightspb;storageinsightspb";
option java_multiple_files = true;
option java_outer_classname = "V1Proto";
option java_package = "com.google.cloud.storageinsights.v1";
option php_namespace = "Google\\Cloud\\StorageInsights\\V1";
option ruby_package = "Google::Cloud::StorageInsights::V1";

// Service describing handlers for resources
service StorageInsights {
  option (google.api.default_host) = "storageinsights.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists ReportConfigs in a given project and location.
  rpc ListReportConfigs(ListReportConfigsRequest)
      returns (ListReportConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/reportConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ReportConfig.
  rpc GetReportConfig(GetReportConfigRequest) returns (ReportConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/reportConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ReportConfig in a given project and location.
  rpc CreateReportConfig(CreateReportConfigRequest) returns (ReportConfig) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/reportConfigs"
      body: "report_config"
    };
    option (google.api.method_signature) = "parent,report_config";
  }

  // Updates the parameters of a single ReportConfig.
  rpc UpdateReportConfig(UpdateReportConfigRequest) returns (ReportConfig) {
    option (google.api.http) = {
      patch: "/v1/{report_config.name=projects/*/locations/*/reportConfigs/*}"
      body: "report_config"
    };
    option (google.api.method_signature) = "report_config,update_mask";
  }

  // Deletes a single ReportConfig.
  rpc DeleteReportConfig(DeleteReportConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/reportConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ReportDetails in a given project and location.
  rpc ListReportDetails(ListReportDetailsRequest)
      returns (ListReportDetailsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/reportConfigs/*}/reportDetails"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ReportDetail.
  rpc GetReportDetail(GetReportDetailRequest) returns (ReportDetail) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/reportConfigs/*/reportDetails/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Message for requesting list of ReportConfigs
message ListReportConfigsRequest {
  // Required. Parent value for ListReportConfigsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "storageinsights.googleapis.com/ReportConfig"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing ReportConfigs
message ListReportConfigsResponse {
  // The list of ReportConfig
  repeated ReportConfig report_configs = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a ReportConfig
message GetReportConfigRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "storageinsights.googleapis.com/ReportConfig"
    }
  ];
}

// Message for creating a ReportConfig
message CreateReportConfigRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "storageinsights.googleapis.com/ReportConfig"
    }
  ];

  // Required. The resource being created
  ReportConfig report_config = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a ReportConfig
message UpdateReportConfigRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // ReportConfig resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  ReportConfig report_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a ReportConfig
message DeleteReportConfigRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "storageinsights.googleapis.com/ReportConfig"
    }
  ];

  // Optional. If set, all ReportDetails for this ReportConfig will be deleted.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message describing ReportDetail object. ReportDetail represents metadata of
// generated reports for a ReportConfig.
// Next ID: 10
message ReportDetail {
  option (google.api.resource) = {
    type: "storageinsights.googleapis.com/ReportDetail"
    pattern: "projects/{project}/locations/{location}/reportConfigs/{report_config}/reportDetails/{report_detail}"
  };

  // Different metrics associated with the generated report.
  message Metrics {
    // Count of Cloud Storage objects which are part of the report.
    int64 processed_records_count = 1;
  }

  // Name of resource. It will be of form
  // projects/<project>/locations/<location>/reportConfigs/<report-config-id>/reportDetails/<report-detail-id>.
  string name = 1;

  // The snapshot time.
  // All the report data is referenced at this point of time.
  google.protobuf.Timestamp snapshot_time = 2;

  // Prefix of the object name of each report's shard. This will have full
  // prefix except the "extension" and "shard_id".
  // For example, if the `destination_path` is
  // `{{report-config-id}}/dt={{datetime}}`, the shard object name would be
  // `gs://my-insights/1A34-F2E456-12B456-1C3D/dt=2022-05-20T06:35/1A34-F2E456-12B456-1C3D_2022-05-20T06:35_5.csv`
  // and the value of `report_path_prefix` field would be
  // `gs://my-insights/1A34-F2E456-12B456-1C3D/dt=2022-05-20T06:35/1A34-F2E456-12B456-1C3D_2022-05-20T06:35_`.
  string report_path_prefix = 8;

  // Total shards generated for the report.
  int64 shards_count = 9;

  // Status of the ReportDetail.
  google.rpc.Status status = 4;

  // Labels as key value pairs
  map<string, string> labels = 5;

  // The date for which report is generated. The time part of target_datetime
  // will be zero till we support multiple reports per day.
  google.type.DateTime target_datetime = 6;

  // Metrics of the report.
  Metrics report_metrics = 7;
}

// Message for requesting list of ReportDetails
message ListReportDetailsRequest {
  // Required. Parent value for ListReportDetailsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "storageinsights.googleapis.com/ReportDetail"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing ReportDetails
message ListReportDetailsResponse {
  // The list of ReportDetail
  repeated ReportDetail report_details = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a ReportDetail
message GetReportDetailRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "storageinsights.googleapis.com/ReportDetail"
    }
  ];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ReportConfig Resource:
//
// Options to setup frequency of report generation.
message FrequencyOptions {
  // This ENUM specifies possible frequencies of report generation.
  enum Frequency {
    // Unspecified.
    FREQUENCY_UNSPECIFIED = 0;

    // Report will be generated daily.
    DAILY = 1;

    // Report will be generated weekly.
    WEEKLY = 2;
  }

  // Frequency of report generation.
  Frequency frequency = 1;

  // The date from which report generation should start.
  // UTC time zone.
  google.type.Date start_date = 2;

  // The date on which report generation should stop (Inclusive).
  // UTC time zone.
  google.type.Date end_date = 3;
}

// Options to configure CSV formatted reports.
message CSVOptions {
  // Record separator characters in CSV.
  string record_separator = 1;

  // Delimiter characters in CSV.
  string delimiter = 2;

  // If set, will include a header row in the CSV report.
  bool header_required = 3;
}

// Options to configure Parquet formatted reports.
message ParquetOptions {}

// Options to filter data on storage systems.
// Next ID: 2
message CloudStorageFilters {
  // Bucket for which the report will be generated.
  string bucket = 1;
}

// Options to store reports in storage systems.
// Next ID: 3
message CloudStorageDestinationOptions {
  // Destination bucket.
  string bucket = 1;

  // Destination path is the path in the bucket where the report should be
  // generated.
  string destination_path = 2;
}

// Report specification for exporting object metadata.
// Next ID: 4
message ObjectMetadataReportOptions {
  // Metadata fields to be included in the report.
  repeated string metadata_fields = 1;

  // Filter options for storage systems.
  oneof filter {
    // Cloud Storage as the storage system.
    CloudStorageFilters storage_filters = 2;
  }

  // Options on destination for storage systems.
  oneof destination_options {
    // Cloud Storage as the storage system.
    CloudStorageDestinationOptions storage_destination_options = 3;
  }
}

// Message describing ReportConfig object. ReportConfig is the configuration to
// generate reports.
// See
// https://cloud.google.com/storage/docs/insights/using-inventory-reports#create-config-rest
// for more details on how to set various fields.
// Next ID: 12
message ReportConfig {
  option (google.api.resource) = {
    type: "storageinsights.googleapis.com/ReportConfig"
    pattern: "projects/{project}/locations/{location}/reportConfigs/{report_config}"
  };

  // name of resource. It will be of form
  // projects/<project>/locations/<location>/reportConfigs/<report-config-id>.
  string name = 1;

  // Output only. [Output only] Create time stamp
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update time stamp
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The frequency of report generation.
  FrequencyOptions frequency_options = 5;

  // Format in which report will be published.
  oneof report_format {
    // Options for CSV formatted reports.
    CSVOptions csv_options = 6;

    // Options for Parquet formatted reports.
    ParquetOptions parquet_options = 7;
  }

  // Configuration options for report contents.
  oneof report_kind {
    // Report for exporting object metadata.
    ObjectMetadataReportOptions object_metadata_report_options = 8;
  }

  // Labels as key value pairs
  map<string, string> labels = 10;

  // User provided display name which can be empty and limited to 256 characters
  // that is editable.
  string display_name = 11;
}
