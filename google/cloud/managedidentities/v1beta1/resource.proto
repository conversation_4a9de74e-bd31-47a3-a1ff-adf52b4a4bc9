// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.managedidentities.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ManagedIdentities.V1Beta1";
option go_package = "cloud.google.com/go/managedidentities/apiv1beta1/managedidentitiespb;managedidentitiespb";
option java_multiple_files = true;
option java_outer_classname = "ResourceProto";
option java_package = "com.google.cloud.managedidentities.v1beta1";
option php_namespace = "Google\\Cloud\\ManagedIdentities\\V1beta1";
option ruby_package = "Google::Cloud::ManagedIdentities::V1beta1";

// Represents a managed Microsoft Active Directory domain.
// If the domain is being changed, it will be placed into the UPDATING state,
// which indicates that the resource is being reconciled. At this point, Get
// will reflect an intermediate state.
message Domain {
  option (google.api.resource) = {
    type: "managedidentities.googleapis.com/Domain"
    pattern: "projects/{project}/locations/{location}/domains/{domain}"
  };

  // Represents the different states of a managed domain.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // The domain is being created.
    CREATING = 1;

    // The domain has been created and is fully usable.
    READY = 2;

    // The domain's configuration is being updated.
    UPDATING = 3;

    // The domain is being deleted.
    DELETING = 4;

    // The domain is being repaired and may be unusable. Details
    // can be found in the `status_message` field.
    REPAIRING = 5;

    // The domain is undergoing maintenance.
    PERFORMING_MAINTENANCE = 6;

    // The domain is not serving requests.
    UNAVAILABLE = 7;
  }

  // Output only. The unique name of the domain using the form:
  // `projects/{project_id}/locations/global/domains/{domain_name}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels that can contain user-provided metadata.
  map<string, string> labels = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The full names of the Google Compute Engine
  // [networks](/compute/docs/networks-and-firewalls#networks) the domain
  // instance is connected to. Networks can be added using UpdateDomain.
  // The domain is only available on networks listed in `authorized_networks`.
  // If CIDR subnets overlap between networks, domain creation will fail.
  repeated string authorized_networks = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. The CIDR range of internal addresses that are reserved for this
  // domain. Reserved networks must be /24 or larger. Ranges must be
  // unique and non-overlapping with existing subnets in
  // [Domain].[authorized_networks].
  string reserved_ip_range = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. Locations where domain needs to be provisioned.
  // [regions][compute/docs/regions-zones/]
  // e.g. us-west1 or us-east4
  // Service supports up to 4 locations at once. Each location will use a /26
  // block.
  repeated string locations = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. The name of delegated administrator account used to perform
  // Active Directory operations. If not specified, `setupadmin` will be used.
  string admin = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The fully-qualified domain name of the exposed domain used by
  // clients to connect to the service. Similar to what would be chosen for an
  // Active Directory set up on an internal network.
  string fqdn = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the instance was created.
  google.protobuf.Timestamp create_time = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update time.
  google.protobuf.Timestamp update_time = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of this domain.
  State state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the current status of this
  // domain, if available.
  string status_message = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current trusts associated with the domain.
  repeated Trust trusts = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents a relationship between two domains. This allows a controller in
// one domain to authenticate a user in another domain.
message Trust {
  // Represents the different states of a domain trust.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // The domain trust is being created.
    CREATING = 1;

    // The domain trust is being updated.
    UPDATING = 2;

    // The domain trust is being deleted.
    DELETING = 3;

    // The domain trust is connected.
    CONNECTED = 4;

    // The domain trust is disconnected.
    DISCONNECTED = 5;
  }

  // Represents the different inter-forest trust types.
  enum TrustType {
    // Not set.
    TRUST_TYPE_UNSPECIFIED = 0;

    // The forest trust.
    FOREST = 1;

    // The external domain trust.
    EXTERNAL = 2;
  }

  // Represents the direction of trust.
  // See
  // [System.DirectoryServices.ActiveDirectory.TrustDirection](https://docs.microsoft.com/en-us/dotnet/api/system.directoryservices.activedirectory.trustdirection?view=netframework-4.7.2)
  // for more information.
  enum TrustDirection {
    // Not set.
    TRUST_DIRECTION_UNSPECIFIED = 0;

    // The inbound direction represents the trusting side.
    INBOUND = 1;

    // The outboud direction represents the trusted side.
    OUTBOUND = 2;

    // The bidirectional direction represents the trusted / trusting side.
    BIDIRECTIONAL = 3;
  }

  // The fully qualified target domain name which will be in trust with the
  // current domain.
  string target_domain_name = 1;

  // The type of trust represented by the trust resource.
  TrustType trust_type = 2;

  // The trust direction, which decides if the current domain is trusted,
  // trusting, or both.
  TrustDirection trust_direction = 3;

  // The trust authentication type, which decides whether the trusted side has
  // forest/domain wide access or selective access to an approved set of
  // resources.
  bool selective_authentication = 4;

  // The target DNS server IP addresses which can resolve the remote domain
  // involved in the trust.
  repeated string target_dns_ip_addresses = 5;

  // Input only. The trust secret used for the handshake
  // with the target domain. It will not be stored.
  string trust_handshake_secret = 6 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. The time the instance was created.
  google.protobuf.Timestamp create_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update time.
  google.protobuf.Timestamp update_time = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the trust.
  State state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the current state of the
  // trust, if available.
  string state_description = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last heartbeat time when the trust was known to be
  // connected.
  google.protobuf.Timestamp last_trust_heartbeat_time = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
