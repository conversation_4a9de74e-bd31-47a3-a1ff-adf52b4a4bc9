// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.managedidentities.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/managedidentities/v1beta1/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ManagedIdentities.V1Beta1";
option go_package = "cloud.google.com/go/managedidentities/apiv1beta1/managedidentitiespb;managedidentitiespb";
option java_multiple_files = true;
option java_outer_classname = "ManagedIdentitiesServiceProto";
option java_package = "com.google.cloud.managedidentities.v1beta1";
option objc_class_prefix = "GCMI";
option php_namespace = "Google\\Cloud\\ManagedIdentities\\V1beta1";
option ruby_package = "Google::Cloud::ManagedIdentities::V1beta1";

service ManagedIdentitiesService {
  option (google.api.default_host) = "managedidentities.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Microsoft AD domain.
  rpc CreateMicrosoftAdDomain(CreateMicrosoftAdDomainRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/global}/domains"
      body: "domain"
    };
    option (google.api.method_signature) = "parent,domain_name,domain";
    option (google.longrunning.operation_info) = {
      response_type: "Domain"
      metadata_type: "OpMetadata"
    };
  }

  // Resets a domain's administrator password.
  rpc ResetAdminPassword(ResetAdminPasswordRequest) returns (ResetAdminPasswordResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/global/domains/*}:resetAdminPassword"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists domains in a project.
  rpc ListDomains(ListDomainsRequest) returns (ListDomainsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/global}/domains"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets information about a domain.
  rpc GetDomain(GetDomainRequest) returns (Domain) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/global/domains/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the metadata and configuration of a domain.
  rpc UpdateDomain(UpdateDomainRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{domain.name=projects/*/locations/global/domains/*}"
      body: "domain"
    };
    option (google.api.method_signature) = "domain,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Domain"
      metadata_type: "OpMetadata"
    };
  }

  // Deletes a domain.
  rpc DeleteDomain(DeleteDomainRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/global/domains/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OpMetadata"
    };
  }

  // Adds an AD trust to a domain.
  rpc AttachTrust(AttachTrustRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/global/domains/*}:attachTrust"
      body: "*"
    };
    option (google.api.method_signature) = "name,trust";
    option (google.longrunning.operation_info) = {
      response_type: "Domain"
      metadata_type: "OpMetadata"
    };
  }

  // Updates the DNS conditional forwarder.
  rpc ReconfigureTrust(ReconfigureTrustRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/global/domains/*}:reconfigureTrust"
      body: "*"
    };
    option (google.api.method_signature) = "name,target_domain_name,target_dns_ip_addresses";
    option (google.longrunning.operation_info) = {
      response_type: "Domain"
      metadata_type: "OpMetadata"
    };
  }

  // Removes an AD trust.
  rpc DetachTrust(DetachTrustRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/global/domains/*}:detachTrust"
      body: "*"
    };
    option (google.api.method_signature) = "name,trust";
    option (google.longrunning.operation_info) = {
      response_type: "Domain"
      metadata_type: "OpMetadata"
    };
  }

  // Validates a trust state, that the target domain is reachable, and that the
  // target domain is able to accept incoming trust requests.
  rpc ValidateTrust(ValidateTrustRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/global/domains/*}:validateTrust"
      body: "*"
    };
    option (google.api.method_signature) = "name,trust";
    option (google.longrunning.operation_info) = {
      response_type: "Domain"
      metadata_type: "OpMetadata"
    };
  }
}

// Represents the metadata of the long-running operation.
message OpMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [CreateMicrosoftAdDomain][google.cloud.managedidentities.v1beta1.CreateMicrosoftAdDomain]
message CreateMicrosoftAdDomainRequest {
  // Required. The resource project name and location using the form:
  // `projects/{project_id}/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "managedidentities.googleapis.com/Domain"
    }
  ];

  // Required. A domain name, e.g. mydomain.myorg.com, with the following restrictions:
  //  * Must contain only lowercase letters, numbers, periods and hyphens.
  //  * Must start with a letter.
  //  * Must contain between 2-64 characters.
  //  * Must end with a number or a letter.
  //  * Must not start with period.
  //  * First segment length (mydomain form example above) shouldn't exceed
  //    15 chars.
  //  * The last segment cannot be fully numeric.
  //  * Must be unique within the customer project.
  string domain_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A Managed Identity domain resource.
  Domain domain = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ResetAdminPassword][google.cloud.managedidentities.v1beta1.ResetAdminPassword]
message ResetAdminPasswordRequest {
  // Required. The domain resource name using the form:
  // `projects/{project_id}/locations/global/domains/{domain_name}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "managedidentities.googleapis.com/Domain"
    }
  ];
}

// Response message for
// [ResetAdminPassword][google.cloud.managedidentities.v1beta1.ResetAdminPassword]
message ResetAdminPasswordResponse {
  // A random password. See [admin][google.cloud.managedidentities.v1beta1.Domain.admin] for more information.
  string password = 1;
}

// Request message for
// [ListDomains][google.cloud.managedidentities.v1beta1.ListDomains]
message ListDomainsRequest {
  // Required. The resource name of the domain location using the form:
  // `projects/{project_id}/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "managedidentities.googleapis.com/Domain"
    }
  ];

  // Optional. The maximum number of items to return.
  // If not specified, a default value of 1000 will be used.
  // Regardless of the page_size value, the response may include a partial list.
  // Callers should rely on a response's
  // [next_page_token][google.cloud.managedidentities.v1beta1.ListDomainsResponse.next_page_token]
  // to determine if there are additional results to list.
  int32 page_size = 2;

  // The `next_page_token` value returned from a previous ListDomainsRequest
  // request, if any.
  string page_token = 3;

  // Optional. A filter specifying constraints of a list operation.
  // For example, `Domain.fqdn="mydomain.myorginization"`.
  string filter = 4;

  // Optional. Specifies the ordering of results. See
  // [Sorting
  // order](https://cloud.google.com/apis/design/design_patterns#sorting_order)
  // for more information.
  string order_by = 5;
}

// Response message for
// [ListDomains][google.cloud.managedidentities.v1beta1.ListDomains]
message ListDomainsResponse {
  // A list of Managed Identities Service domains in the project.
  repeated Domain domains = 1;

  // A token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // A list of locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [GetDomain][google.cloud.managedidentities.v1beta1.GetDomain]
message GetDomainRequest {
  // Required. The domain resource name using the form:
  // `projects/{project_id}/locations/global/domains/{domain_name}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "managedidentities.googleapis.com/Domain"
    }
  ];
}

// Request message for
// [UpdateDomain][google.cloud.managedidentities.v1beta1.UpdateDomain]
message UpdateDomainRequest {
  // Required. Mask of fields to update. At least one path must be supplied in this
  // field. The elements of the repeated paths field may only include
  // fields from [Domain][google.cloud.managedidentities.v1beta1.Domain]:
  //  * `labels`
  //  * `locations`
  //  * `authorized_networks`
  //  * `audit_logs_enabled`
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Domain message with updated fields. Only supported fields specified in
  // update_mask are updated.
  Domain domain = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [DeleteDomain][google.cloud.managedidentities.v1beta1.DeleteDomain]
message DeleteDomainRequest {
  // Required. The domain resource name using the form:
  // `projects/{project_id}/locations/global/domains/{domain_name}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "managedidentities.googleapis.com/Domain"
    }
  ];
}

// Request message for
// [AttachTrust][google.cloud.managedidentities.v1beta1.AttachTrust]
message AttachTrustRequest {
  // Required. The resource domain name, project name and location using the form:
  // `projects/{project_id}/locations/global/domains/{domain_name}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "managedidentities.googleapis.com/Domain"
    }
  ];

  // Required. The domain trust resource.
  Trust trust = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ReconfigureTrust][google.cloud.managedidentities.v1beta1.ReconfigureTrust]
message ReconfigureTrustRequest {
  // Required. The resource domain name, project name and location using the form:
  // `projects/{project_id}/locations/global/domains/{domain_name}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "managedidentities.googleapis.com/Domain"
    }
  ];

  // Required. The fully-qualified target domain name which will be in trust with current
  // domain.
  string target_domain_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The target DNS server IP addresses to resolve the remote domain involved
  // in the trust.
  repeated string target_dns_ip_addresses = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [DetachTrust][google.cloud.managedidentities.v1beta1.DetachTrust]
message DetachTrustRequest {
  // Required. The resource domain name, project name, and location using the form:
  // `projects/{project_id}/locations/global/domains/{domain_name}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "managedidentities.googleapis.com/Domain"
    }
  ];

  // Required. The domain trust resource to removed.
  Trust trust = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ValidateTrust][google.cloud.managedidentities.v1beta1.ValidateTrust]
message ValidateTrustRequest {
  // Required. The resource domain name, project name, and location using the form:
  // `projects/{project_id}/locations/global/domains/{domain_name}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "managedidentities.googleapis.com/Domain"
    }
  ];

  // Required. The domain trust to validate trust state for.
  Trust trust = 2 [(google.api.field_behavior) = REQUIRED];
}
