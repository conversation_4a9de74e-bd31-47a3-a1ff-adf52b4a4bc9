# This build file includes a target for the Ruby wrapper library for
# google-cloud-managed_identities.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for managedidentities.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "managedidentities_ruby_wrapper",
    srcs = ["//google/cloud/managedidentities/v1:managedidentities_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-managed_identities",
        "ruby-cloud-env-prefix=MANAGED_IDENTITIES",
        "ruby-cloud-wrapper-of=v1:0.7",
        "ruby-cloud-product-url=https://cloud.google.com/managed-microsoft-ad/",
        "ruby-cloud-api-id=managedidentities.googleapis.com",
        "ruby-cloud-api-shortname=managedidentities",
    ],
    ruby_cloud_description = "The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory.",
    ruby_cloud_title = "Managed Service for Microsoft Active Directory API",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-managedidentities-ruby",
    deps = [
        ":managedidentities_ruby_wrapper",
    ],
)
