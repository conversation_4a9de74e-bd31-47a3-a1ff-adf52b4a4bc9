# This build file includes a target for the Ruby wrapper library for
# google-cloud-policy_troubleshooter.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for policytroubleshooter.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "policytroubleshooter_ruby_wrapper",
    srcs = ["//google/cloud/policytroubleshooter/v1:policytroubleshooter_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-policy_troubleshooter",
        "ruby-cloud-env-prefix=POLICY_TROUBLESHOOTER",
        "ruby-cloud-wrapper-of=v1:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/iam/docs/troubleshooting-access",
        "ruby-cloud-api-id=policytroubleshooter.googleapis.com",
        "ruby-cloud-api-shortname=policytroubleshooter",
    ],
    ruby_cloud_description = "Policy Troubleshooter makes it easier to understand why a user has access to a resource or doesn't have permission to call an API. Given an email, resource, and permission, Policy Troubleshooter will examine all IAM policies that apply to the resource. It then reveals whether the member's roles include the permission on that resource and, if so, which policies bind the member to those roles.",
    ruby_cloud_title = "IAM Policy Troubleshooter",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-ruby",
    deps = [
        ":policytroubleshooter_ruby_wrapper",
    ],
)
