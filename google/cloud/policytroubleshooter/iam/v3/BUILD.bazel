# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "iam_proto",
    srcs = [
        "troubleshooter.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/iam/v1:policy_proto",
        "//google/iam/v2:policy_proto",
        "//google/rpc:status_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "iam_proto_with_info",
    deps = [
        ":iam_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "iam_java_proto",
    deps = [":iam_proto"],
)

java_grpc_library(
    name = "iam_java_grpc",
    srcs = [":iam_proto"],
    deps = [":iam_java_proto"],
)

java_gapic_library(
    name = "iam_java_gapic",
    srcs = [":iam_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "policytroubleshooter_v3_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v3.yaml",
    test_deps = [
        "//google/iam/v1:iam_java_grpc",
        "//google/iam/v2:iam_java_grpc",
        ":iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":iam_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
        "//google/iam/v2:iam_java_proto",
    ],
)

java_gapic_test(
    name = "iam_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.policytroubleshooter.iam.v3.PolicyTroubleshooterClientHttpJsonTest",
        "com.google.cloud.policytroubleshooter.iam.v3.PolicyTroubleshooterClientTest",
    ],
    runtime_deps = [":iam_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-policytroubleshooter-iam-v3-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":iam_java_gapic",
        ":iam_java_grpc",
        ":iam_java_proto",
        ":iam_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "iam_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/policytroubleshooter/iam/apiv3/iampb",
    protos = [":iam_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/iam/v2:iam_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "iam_go_gapic",
    srcs = [":iam_proto_with_info"],
    grpc_service_config = "policytroubleshooter_v3_grpc_service_config.json",
    importpath = "cloud.google.com/go/policytroubleshooter/iam/apiv3;iam",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":iam_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/iam/v2:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-policytroubleshooter-iam-v3-go",
    deps = [
        ":iam_go_gapic",
        ":iam_go_gapic_srcjar-metadata.srcjar",
        ":iam_go_gapic_srcjar-snippets.srcjar",
        ":iam_go_gapic_srcjar-test.srcjar",
        ":iam_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "iam_py_gapic",
    srcs = [":iam_proto"],
    grpc_service_config = "policytroubleshooter_v3_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v3.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
        "//google/iam/v2:iam_policy_py_proto",
    ],
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=policytroubleshooter_iam",
    ],
)

py_test(
    name = "iam_py_gapic_test",
    srcs = [
        "iam_py_gapic_pytest.py",
        "iam_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":iam_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "policytroubleshooter-iam-v3-py",
    deps = [
        ":iam_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "iam_php_proto",
    deps = [":iam_proto"],
)

php_gapic_library(
    name = "iam_php_gapic",
    srcs = [":iam_proto_with_info"],
    grpc_service_config = "policytroubleshooter_v3_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":iam_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-iam-v3-php",
    deps = [
        ":iam_php_gapic",
        ":iam_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "iam_nodejs_gapic",
    package_name = "@google-cloud/iam",
    src = ":iam_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "policytroubleshooter_v3_grpc_service_config.json",
    package = "google.cloud.policytroubleshooter.iam.v3",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v3.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "policytroubleshooter-iam-v3-nodejs",
    deps = [
        ":iam_nodejs_gapic",
        ":iam_proto",
        "//google/iam/v1:policy_proto",
        "//google/iam/v2:policy_proto",
        "//google/rpc:status_proto",
        "//google/type:expr_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "iam_ruby_proto",
    deps = [":iam_proto"],
)

ruby_grpc_library(
    name = "iam_ruby_grpc",
    srcs = [":iam_proto"],
    deps = [":iam_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "iam_ruby_gapic",
    srcs = [":iam_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-extra-dependencies=google-iam-v1=>0.5+<2.a;google-iam-v2=>0.3+<2.a",
        "ruby-cloud-gem-name=google-cloud-policy_troubleshooter-iam-v3",
    ],
    grpc_service_config = "policytroubleshooter_v3_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":iam_ruby_grpc",
        ":iam_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-iam-v3-ruby",
    deps = [
        ":iam_ruby_gapic",
        ":iam_ruby_grpc",
        ":iam_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "iam_csharp_proto",
    deps = [":iam_proto"],
)

csharp_grpc_library(
    name = "iam_csharp_grpc",
    srcs = [":iam_proto"],
    deps = [":iam_csharp_proto"],
)

csharp_gapic_library(
    name = "iam_csharp_gapic",
    srcs = [":iam_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "policytroubleshooter_v3_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v3.yaml",
    deps = [
        ":iam_csharp_grpc",
        ":iam_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-iam-v3-csharp",
    deps = [
        ":iam_csharp_gapic",
        ":iam_csharp_grpc",
        ":iam_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "iam_cc_proto",
    deps = [":iam_proto"],
)

cc_grpc_library(
    name = "iam_cc_grpc",
    srcs = [":iam_proto"],
    grpc_only = True,
    deps = [":iam_cc_proto"],
)
