type: google.api.Service
config_version: 3
name: policytroubleshooter.googleapis.com
title: Policy Troubleshooter API

apis:
- name: google.cloud.policytroubleshooter.iam.v3.PolicyTroubleshooter

authentication:
  rules:
  - selector: google.cloud.policytroubleshooter.iam.v3.PolicyTroubleshooter.TroubleshootIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=690790&template=1814512
  documentation_uri: https://cloud.google.com/policy-intelligence/docs/troubleshoot-access
  api_short_name: policytroubleshooter
  github_label: 'api: policytroubleshooter'
  doc_tag_prefix: policytroubleshooter
  organization: CLOUD
  library_settings:
  - version: google.cloud.policytroubleshooter.iam.v3
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/policytroubleshooter/docs/reference/rpc
