# This build file includes a target for the Ruby wrapper library for
# google-cloud-policy_troubleshooter-iam.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for policytroubleshooter-iam.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v3 in this case.
ruby_cloud_gapic_library(
    name = "iam_ruby_wrapper",
    srcs = ["//google/cloud/policytroubleshooter/iam/v3:iam_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-policy_troubleshooter-iam",
        "ruby-cloud-wrapper-of=v3:0.3",
    ],
    service_yaml = "//google/cloud/policytroubleshooter/iam/v3:policytroubleshooter_v3.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-iam-ruby",
    deps = [
        ":iam_ruby_wrapper",
    ],
)
