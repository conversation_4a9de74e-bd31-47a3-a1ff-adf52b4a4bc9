# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "policytroubleshooter_proto",
    srcs = [
        "checker.proto",
        "explanations.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/iam/v1:policy_proto",
        "//google/rpc:status_proto",
        "//google/type:expr_proto",
    ],
)

proto_library_with_info(
    name = "policytroubleshooter_proto_with_info",
    deps = [
        ":policytroubleshooter_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "policytroubleshooter_java_proto",
    deps = [":policytroubleshooter_proto"],
)

java_grpc_library(
    name = "policytroubleshooter_java_grpc",
    srcs = [":policytroubleshooter_proto"],
    deps = [":policytroubleshooter_java_proto"],
)

java_gapic_library(
    name = "policytroubleshooter_java_gapic",
    srcs = [":policytroubleshooter_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "checker_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v1.yaml",
    test_deps = [
        "//google/iam/v1:iam_java_grpc",
        ":policytroubleshooter_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":policytroubleshooter_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "policytroubleshooter_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.policytroubleshooter.v1.IamCheckerClientHttpJsonTest",
        "com.google.cloud.policytroubleshooter.v1.IamCheckerClientTest",
    ],
    runtime_deps = [":policytroubleshooter_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-policytroubleshooter-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":policytroubleshooter_java_gapic",
        ":policytroubleshooter_java_grpc",
        ":policytroubleshooter_java_proto",
        ":policytroubleshooter_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "policytroubleshooter_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/policytroubleshooter/apiv1/policytroubleshooterpb",
    protos = [":policytroubleshooter_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "policytroubleshooter_go_gapic",
    srcs = [":policytroubleshooter_proto_with_info"],
    grpc_service_config = "checker_grpc_service_config.json",
    importpath = "cloud.google.com/go/policytroubleshooter/apiv1;policytroubleshooter",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":policytroubleshooter_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-policytroubleshooter-v1-go",
    deps = [
        ":policytroubleshooter_go_gapic",
        ":policytroubleshooter_go_gapic_srcjar-metadata.srcjar",
        ":policytroubleshooter_go_gapic_srcjar-snippets.srcjar",
        ":policytroubleshooter_go_gapic_srcjar-test.srcjar",
        ":policytroubleshooter_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "policytroubleshooter_py_gapic",
    srcs = [":policytroubleshooter_proto"],
    grpc_service_config = "checker_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-policy-troubleshooter"],
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "policytroubleshooter_py_gapic_test",
    srcs = [
        "policytroubleshooter_py_gapic_pytest.py",
        "policytroubleshooter_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":policytroubleshooter_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-v1-py",
    deps = [
        ":policytroubleshooter_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "policytroubleshooter_php_proto",
    deps = [":policytroubleshooter_proto"],
)

php_gapic_library(
    name = "policytroubleshooter_php_gapic",
    srcs = [":policytroubleshooter_proto_with_info"],
    grpc_service_config = "checker_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":policytroubleshooter_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-v1-php",
    deps = [
        ":policytroubleshooter_php_gapic",
        ":policytroubleshooter_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "policytroubleshooter_nodejs_gapic",
    package_name = "@google-cloud/policy-troubleshooter",
    src = ":policytroubleshooter_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "checker_grpc_service_config.json",
    package = "google.cloud.policytroubleshooter.v1",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "policytroubleshooter-v1-nodejs",
    deps = [
        ":policytroubleshooter_nodejs_gapic",
        ":policytroubleshooter_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "policytroubleshooter_ruby_proto",
    deps = [":policytroubleshooter_proto"],
)

ruby_grpc_library(
    name = "policytroubleshooter_ruby_grpc",
    srcs = [":policytroubleshooter_proto"],
    deps = [":policytroubleshooter_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "policytroubleshooter_ruby_gapic",
    srcs = [":policytroubleshooter_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=policytroubleshooter.googleapis.com",
        "ruby-cloud-api-shortname=policytroubleshooter",
        "ruby-cloud-env-prefix=POLICY_TROUBLESHOOTER",
        "ruby-cloud-gem-name=google-cloud-policy_troubleshooter-v1",
        "ruby-cloud-product-url=https://cloud.google.com/iam/docs/troubleshooting-access",
    ],
    grpc_service_config = "checker_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Policy Troubleshooter makes it easier to understand why a user has access to a resource or doesn't have permission to call an API. Given an email, resource, and permission, Policy Troubleshooter will examine all IAM policies that apply to the resource. It then reveals whether the member's roles include the permission on that resource and, if so, which policies bind the member to those roles.",
    ruby_cloud_title = "IAM Policy Troubleshooter V1",
    service_yaml = "policytroubleshooter_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":policytroubleshooter_ruby_grpc",
        ":policytroubleshooter_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-v1-ruby",
    deps = [
        ":policytroubleshooter_ruby_gapic",
        ":policytroubleshooter_ruby_grpc",
        ":policytroubleshooter_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "policytroubleshooter_csharp_proto",
    extra_opts = [],
    deps = [":policytroubleshooter_proto"],
)

csharp_grpc_library(
    name = "policytroubleshooter_csharp_grpc",
    srcs = [":policytroubleshooter_proto"],
    deps = [":policytroubleshooter_csharp_proto"],
)

csharp_gapic_library(
    name = "policytroubleshooter_csharp_gapic",
    srcs = [":policytroubleshooter_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "checker_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policytroubleshooter_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":policytroubleshooter_csharp_grpc",
        ":policytroubleshooter_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-policytroubleshooter-v1-csharp",
    deps = [
        ":policytroubleshooter_csharp_gapic",
        ":policytroubleshooter_csharp_grpc",
        ":policytroubleshooter_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "policytroubleshooter_cc_proto",
    deps = [":policytroubleshooter_proto"],
)

cc_grpc_library(
    name = "policytroubleshooter_cc_grpc",
    srcs = [":policytroubleshooter_proto"],
    grpc_only = True,
    deps = [":policytroubleshooter_cc_proto"],
)
