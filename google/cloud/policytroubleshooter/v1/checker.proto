// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.policytroubleshooter.v1;

import public "google/cloud/policytroubleshooter/v1/explanations.proto";

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/rpc/status.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.PolicyTroubleshooter.V1";
option go_package = "cloud.google.com/go/policytroubleshooter/apiv1/policytroubleshooterpb;policytroubleshooterpb";
option java_multiple_files = true;
option java_outer_classname = "IAMCheckerProto";
option java_package = "com.google.cloud.policytroubleshooter.v1";
option php_namespace = "Google\\Cloud\\PolicyTroubleshooter\\V1";
option ruby_package = "Google::Cloud::PolicyTroubleshooter::V1";

// IAM Policy Troubleshooter service.
//
// This service helps you troubleshoot access issues for Google Cloud resources.
service IamChecker {
  option (google.api.default_host) = "policytroubleshooter.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Checks whether a principal has a specific permission for a specific
  // resource, and explains why the principal does or does not have that
  // permission.
  rpc TroubleshootIamPolicy(TroubleshootIamPolicyRequest)
      returns (TroubleshootIamPolicyResponse) {
    option (google.api.http) = {
      post: "/v1/iam:troubleshoot"
      body: "*"
    };
  }
}

// Request for
// [TroubleshootIamPolicy][google.cloud.policytroubleshooter.v1.IamChecker.TroubleshootIamPolicy].
message TroubleshootIamPolicyRequest {
  // The information to use for checking whether a principal has a permission
  // for a resource.
  AccessTuple access_tuple = 1;
}

// Response for
// [TroubleshootIamPolicy][google.cloud.policytroubleshooter.v1.IamChecker.TroubleshootIamPolicy].
message TroubleshootIamPolicyResponse {
  // Indicates whether the principal has the specified permission for the
  // specified resource, based on evaluating all of the applicable IAM policies.
  AccessState access = 1;

  // List of IAM policies that were evaluated to check the principal's
  // permissions, with annotations to indicate how each policy contributed to
  // the final result.
  //
  // The list of policies can include the policy for the resource itself. It can
  // also include policies that are inherited from higher levels of the resource
  // hierarchy, including the organization, the folder, and the project.
  //
  // To learn more about the resource hierarchy, see
  // https://cloud.google.com/iam/help/resource-hierarchy.
  repeated ExplainedPolicy explained_policies = 2;

  // The general errors contained in the troubleshooting response.
  repeated google.rpc.Status errors = 3;
}
