type: google.api.Service
config_version: 3
name: policytroubleshooter.googleapis.com
title: Policy Troubleshooter API

apis:
- name: google.cloud.policytroubleshooter.v1.IamChecker

authentication:
  rules:
  - selector: google.cloud.policytroubleshooter.v1.IamChecker.TroubleshootIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=690790&template=1814512
  documentation_uri: https://cloud.google.com/policy-intelligence/docs/troubleshoot-access
  api_short_name: policytroubleshooter
  github_label: 'api: policytroubleshooter'
  doc_tag_prefix: policytroubleshooter
  organization: CLOUD
  proto_reference_documentation_uri: https://cloud.google.com/policytroubleshooter/docs/reference/rpc
