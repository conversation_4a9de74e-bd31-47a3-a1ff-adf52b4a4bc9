{"methodConfig": [{"name": [{"service": "google.cloud.dataqna.v1alpha.QuestionService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataqna.v1alpha.QuestionService", "method": "CreateQuestion"}, {"service": "google.cloud.dataqna.v1alpha.QuestionService", "method": "ExecuteQuestion"}, {"service": "google.cloud.dataqna.v1alpha.QuestionService", "method": "UpdateUserFeedback"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.dataqna.v1alpha.AutoSuggestionService"}], "timeout": "2s"}]}