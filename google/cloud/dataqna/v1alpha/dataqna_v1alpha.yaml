type: google.api.Service
config_version: 3
name: dataqna.googleapis.com
title: Data QnA API

apis:
- name: google.cloud.dataqna.v1alpha.AutoSuggestionService
- name: google.cloud.dataqna.v1alpha.QuestionService

documentation:
  summary: |-
    Data QnA is a natural language question and answer service for BigQuery
    data.

authentication:
  rules:
  - selector: google.cloud.dataqna.v1alpha.AutoSuggestionService.SuggestQueries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataqna.v1alpha.QuestionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
