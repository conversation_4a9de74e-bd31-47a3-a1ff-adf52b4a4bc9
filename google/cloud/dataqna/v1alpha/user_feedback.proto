// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataqna.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.DataQnA.V1Alpha";
option go_package = "cloud.google.com/go/dataqna/apiv1alpha/dataqnapb;dataqnapb";
option java_multiple_files = true;
option java_outer_classname = "UserFeedbackProto";
option java_package = "com.google.cloud.dataqna.v1alpha";
option php_namespace = "Google\\Cloud\\DataQnA\\V1alpha";
option ruby_package = "Google::Cloud::DataQnA::V1alpha";

// Feedback provided by a user.
message UserFeedback {
  option (google.api.resource) = {
    type: "dataqna.googleapis.com/UserFeedback"
    pattern: "projects/{project}/locations/{location}/questions/{question}/userFeedback"
  };

  // Enumeration of feedback ratings.
  enum UserFeedbackRating {
    // No rating was specified.
    USER_FEEDBACK_RATING_UNSPECIFIED = 0;

    // The user provided positive feedback.
    POSITIVE = 1;

    // The user provided negative feedback.
    NEGATIVE = 2;
  }

  // Required. The unique identifier for the user feedback.
  // User feedback is a singleton resource on a Question.
  // Example: `projects/foo/locations/bar/questions/1234/userFeedback`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Free form user feedback, such as a text box.
  string free_form_feedback = 2;

  // The user feedback rating
  UserFeedbackRating rating = 3;
}
