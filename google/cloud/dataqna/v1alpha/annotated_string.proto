// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataqna.v1alpha;

option csharp_namespace = "Google.Cloud.DataQnA.V1Alpha";
option go_package = "cloud.google.com/go/dataqna/apiv1alpha/dataqnapb;dataqnapb";
option java_multiple_files = true;
option java_outer_classname = "AnnotatedStringProto";
option java_package = "com.google.cloud.dataqna.v1alpha";
option php_namespace = "Google\\Cloud\\DataQnA\\V1alpha";
option ruby_package = "Google::Cloud::DataQnA::V1alpha";

// Describes string annotation from both semantic and formatting perspectives.
// Example:
//
// User Query:
//
//   top countries by population in Africa
//
//   0   4         14 17         28 31    37
//
// Table Data:
//
// + "country" - dimension
// + "population" - metric
// + "Africa" - value in the "continent" column
//
// text_formatted = `"top countries by population in Africa"`
//
// html_formatted =
//   `"top <b>countries</b> by <b>population</b> in <i>Africa</i>"`
//
// ```
// markups = [
//   {DIMENSION, 4, 12}, // 'countries'
//   {METRIC, 17, 26}, // 'population'
//   {FILTER, 31, 36}  // 'Africa'
// ]
// ```
//
// Note that html formattings for 'DIMENSION' and 'METRIC' are the same, while
// semantic markups are different.
message AnnotatedString {
  // Semantic markup denotes a substring (by index and length) with markup
  // information.
  message SemanticMarkup {
    // The semantic type of the markup substring.
    SemanticMarkupType type = 1;

    // Unicode character index of the query.
    int32 start_char_index = 2;

    // The length (number of unicode characters) of the markup substring.
    int32 length = 3;
  }

  // Semantic markup types.
  enum SemanticMarkupType {
    // No markup type was specified.
    MARKUP_TYPE_UNSPECIFIED = 0;

    // Markup for a substring denoting a metric.
    METRIC = 1;

    // Markup for a substring denoting a dimension.
    DIMENSION = 2;

    // Markup for a substring denoting a filter.
    FILTER = 3;

    // Markup for an unused substring.
    UNUSED = 4;

    // Markup for a substring containing blocked phrases.
    BLOCKED = 5;

    // Markup for a substring that contains terms for row.
    ROW = 6;
  }

  // Text version of the string.
  string text_formatted = 1;

  // HTML version of the string annotation.
  string html_formatted = 2;

  // Semantic version of the string annotation.
  repeated SemanticMarkup markups = 3;
}
