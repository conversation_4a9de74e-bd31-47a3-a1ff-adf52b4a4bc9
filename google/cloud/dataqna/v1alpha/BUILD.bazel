# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "dataqna_proto",
    srcs = [
        "annotated_string.proto",
        "auto_suggestion_service.proto",
        "question.proto",
        "question_service.proto",
        "user_feedback.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "dataqna_proto_with_info",
    deps = [
        ":dataqna_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "dataqna_java_proto",
    deps = [":dataqna_proto"],
)

java_grpc_library(
    name = "dataqna_java_grpc",
    srcs = [":dataqna_proto"],
    deps = [":dataqna_java_proto"],
)

java_gapic_library(
    name = "dataqna_java_gapic",
    srcs = [":dataqna_proto_with_info"],
    grpc_service_config = "dataqna_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "dataqna_v1alpha.yaml",
    test_deps = [
        ":dataqna_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":dataqna_java_proto",
    ],
)

java_gapic_test(
    name = "dataqna_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.dataqna.v1alpha.AutoSuggestionServiceClientHttpJsonTest",
        "com.google.cloud.dataqna.v1alpha.AutoSuggestionServiceClientTest",
        "com.google.cloud.dataqna.v1alpha.QuestionServiceClientHttpJsonTest",
        "com.google.cloud.dataqna.v1alpha.QuestionServiceClientTest",
    ],
    runtime_deps = [":dataqna_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-dataqna-v1alpha-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":dataqna_java_gapic",
        ":dataqna_java_grpc",
        ":dataqna_java_proto",
        ":dataqna_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "dataqna_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/dataqna/apiv1alpha/dataqnapb",
    protos = [":dataqna_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "dataqna_go_gapic",
    srcs = [":dataqna_proto_with_info"],
    grpc_service_config = "dataqna_grpc_service_config.json",
    importpath = "cloud.google.com/go/dataqna/apiv1alpha;dataqna",
    metadata = True,
    release_level = "alpha",
    rest_numeric_enums = False,
    service_yaml = "dataqna_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataqna_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-dataqna-v1alpha-go",
    deps = [
        ":dataqna_go_gapic",
        ":dataqna_go_gapic_srcjar-snippets.srcjar",
        ":dataqna_go_gapic_srcjar-test.srcjar",
        ":dataqna_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "dataqna_py_gapic",
    srcs = [":dataqna_proto"],
    grpc_service_config = "dataqna_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-data-qna",
    ],
    rest_numeric_enums = False,
    service_yaml = "dataqna_v1alpha.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "dataqna_py_gapic_test",
    srcs = [
        "dataqna_py_gapic_pytest.py",
        "dataqna_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":dataqna_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "dataqna-v1alpha-py",
    deps = [
        ":dataqna_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "dataqna_php_proto",
    deps = [":dataqna_proto"],
)

php_gapic_library(
    name = "dataqna_php_gapic",
    srcs = [":dataqna_proto_with_info"],
    grpc_service_config = "dataqna_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "dataqna_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [":dataqna_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-dataqna-v1alpha-php",
    deps = [
        ":dataqna_php_gapic",
        ":dataqna_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "dataqna_nodejs_gapic",
    package_name = "@google-cloud/data-qna",
    src = ":dataqna_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "dataqna_grpc_service_config.json",
    package = "google.cloud.dataqna.v1alpha",
    rest_numeric_enums = False,
    service_yaml = "dataqna_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "dataqna-v1alpha-nodejs",
    deps = [
        ":dataqna_nodejs_gapic",
        ":dataqna_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "dataqna_ruby_proto",
    deps = [":dataqna_proto"],
)

ruby_grpc_library(
    name = "dataqna_ruby_grpc",
    srcs = [":dataqna_proto"],
    deps = [":dataqna_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "dataqna_ruby_gapic",
    srcs = [":dataqna_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-dataqna-v1alpha",
        "ruby-cloud-env-prefix=BIGQUERY_DATAQNA",
        "ruby-cloud-path-override=data_qn_a=dataqna",
        "ruby-cloud-namespace-override=Dataqna=DataQnA",
        "ruby-cloud-api-id=dataqna.googleapis.com",
        "ruby-cloud-api-shortname=dataqna",
    ],
    grpc_service_config = "dataqna_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "Data QnA is a natural language question and answer service for BigQuery data.",
    ruby_cloud_title = "BigQuery Data QnA V1alpha",
    service_yaml = "dataqna_v1alpha.yaml",
    deps = [
        ":dataqna_ruby_grpc",
        ":dataqna_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataqna-v1alpha-ruby",
    deps = [
        ":dataqna_ruby_gapic",
        ":dataqna_ruby_grpc",
        ":dataqna_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "dataqna_csharp_proto",
    deps = [":dataqna_proto"],
)

csharp_grpc_library(
    name = "dataqna_csharp_grpc",
    srcs = [":dataqna_proto"],
    deps = [":dataqna_csharp_proto"],
)

csharp_gapic_library(
    name = "dataqna_csharp_gapic",
    srcs = [":dataqna_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "dataqna_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "dataqna_v1alpha.yaml",
    deps = [
        ":dataqna_csharp_grpc",
        ":dataqna_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-dataqna-v1alpha-csharp",
    deps = [
        ":dataqna_csharp_gapic",
        ":dataqna_csharp_grpc",
        ":dataqna_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
