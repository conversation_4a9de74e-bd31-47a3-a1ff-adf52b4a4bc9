// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataqna.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dataqna/v1alpha/question.proto";
import "google/cloud/dataqna/v1alpha/user_feedback.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.DataQnA.V1Alpha";
option go_package = "cloud.google.com/go/dataqna/apiv1alpha/dataqnapb;dataqnapb";
option java_multiple_files = true;
option java_outer_classname = "QuestionServiceProto";
option java_package = "com.google.cloud.dataqna.v1alpha";
option php_namespace = "Google\\Cloud\\DataQnA\\V1alpha";
option ruby_package = "Google::Cloud::DataQnA::V1alpha";

// Service to interpret natural language queries.
// The service allows to create `Question` resources that are interpreted and
// are filled with one or more interpretations if the question could be
// interpreted. Once a `Question` resource is created and has at least one
// interpretation, an interpretation can be chosen for execution, which
// triggers a query to the backend (for BigQuery, it will create a job).
// Upon successful execution of that interpretation, backend specific
// information will be returned so that the client can retrieve the results
// from the backend.
//
// The `Question` resources are named `projects/*/locations/*/questions/*`.
//
// The `Question` resource has a singletion sub-resource `UserFeedback` named
// `projects/*/locations/*/questions/*/userFeedback`, which allows access to
// user feedback.
service QuestionService {
  option (google.api.default_host) = "dataqna.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Gets a previously created question.
  rpc GetQuestion(GetQuestionRequest) returns (Question) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/questions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a question.
  rpc CreateQuestion(CreateQuestionRequest) returns (Question) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*}/questions"
      body: "question"
    };
    option (google.api.method_signature) = "parent,question";
  }

  // Executes an interpretation.
  rpc ExecuteQuestion(ExecuteQuestionRequest) returns (Question) {
    option (google.api.http) = {
      post: "/v1alpha/{name=projects/*/locations/*/questions/*}:execute"
      body: "*"
    };
    option (google.api.method_signature) = "name,interpretation_index";
  }

  // Gets previously created user feedback.
  rpc GetUserFeedback(GetUserFeedbackRequest) returns (UserFeedback) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/questions/*/userFeedback}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates user feedback. This creates user feedback if there was none before
  // (upsert).
  rpc UpdateUserFeedback(UpdateUserFeedbackRequest) returns (UserFeedback) {
    option (google.api.http) = {
      patch: "/v1alpha/{user_feedback.name=projects/*/locations/*/questions/*/userFeedback}"
      body: "user_feedback"
    };
    option (google.api.method_signature) = "user_feedback,update_mask";
  }
}

// A request to get a previously created question.
message GetQuestionRequest {
  // Required. The unique identifier for the question.
  // Example: `projects/foo/locations/bar/questions/1234`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataqna.googleapis.com/Question"
    }
  ];

  // The list of fields to be retrieved.
  google.protobuf.FieldMask read_mask = 2;
}

// Request to create a question resource.
message CreateQuestionRequest {
  // Required. The name of the project this data source reference belongs to.
  // Example: `projects/foo/locations/bar`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The question to create.
  Question question = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to execute an interpretation.
message ExecuteQuestionRequest {
  // Required. The unique identifier for the question.
  // Example: `projects/foo/locations/bar/questions/1234`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Index of the interpretation to execute.
  int32 interpretation_index = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to get user feedback.
message GetUserFeedbackRequest {
  // Required. The unique identifier for the user feedback.
  // User feedback is a singleton resource on a Question.
  // Example: `projects/foo/locations/bar/questions/1234/userFeedback`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataqna.googleapis.com/UserFeedback"
    }
  ];
}

// Request to updates user feedback.
message UpdateUserFeedbackRequest {
  // Required. The user feedback to update. This can be called even if there is no
  // user feedback so far.
  // The feedback's name field is used to identify the user feedback (and the
  // corresponding question) to update.
  UserFeedback user_feedback = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}
