# This build file includes a target for the Ruby wrapper library for
# google-cloud-dataqna.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for dataqna.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1alpha in this case.
ruby_cloud_gapic_library(
    name = "dataqna_ruby_wrapper",
    srcs = ["//google/cloud/dataqna/v1alpha:dataqna_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-dataqna",
        "ruby-cloud-env-prefix=BIGQUERY_DATAQNA",
        "ruby-cloud-wrapper-of=v1alpha:0.6",
        "ruby-cloud-path-override=data_qn_a=dataqna",
        "ruby-cloud-namespace-override=Dataqna=DataQnA",
        "ruby-cloud-api-id=dataqna.googleapis.com",
        "ruby-cloud-api-shortname=dataqna",
    ],
    ruby_cloud_description = "Data QnA is a natural language question and answer service for BigQuery data.",
    ruby_cloud_title = "BigQuery Data QnA",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataqna-ruby",
    deps = [
        ":dataqna_ruby_wrapper",
    ],
)
