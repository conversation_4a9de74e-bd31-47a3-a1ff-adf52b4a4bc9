{"methodConfig": [{"name": [{"service": "google.cloud.billing.v1.CloudBilling", "method": "CreateBillingAccount"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.billing.v1.CloudBilling", "method": "GetBillingAccount"}, {"service": "google.cloud.billing.v1.CloudBilling", "method": "ListBillingAccounts"}, {"service": "google.cloud.billing.v1.CloudBilling", "method": "UpdateBillingAccount"}, {"service": "google.cloud.billing.v1.CloudBilling", "method": "ListProjectBillingInfo"}, {"service": "google.cloud.billing.v1.CloudBilling", "method": "GetProjectBillingInfo"}, {"service": "google.cloud.billing.v1.CloudBilling", "method": "UpdateProjectBillingInfo"}, {"service": "google.cloud.billing.v1.CloudBilling", "method": "GetIamPolicy"}, {"service": "google.cloud.billing.v1.CloudBilling", "method": "SetIamPolicy"}, {"service": "google.cloud.billing.v1.CloudBilling", "method": "TestIamPermissions"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.billing.v1.CloudCatalog", "method": "ListServices"}, {"service": "google.cloud.billing.v1.CloudCatalog", "method": "ListSkus"}], "timeout": "60s"}]}