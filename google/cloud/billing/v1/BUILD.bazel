# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "billing_proto",
    srcs = [
        "cloud_billing.proto",
        "cloud_catalog.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "billing_proto_with_info",
    deps = [
        ":billing_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "billing_java_proto",
    deps = [":billing_proto"],
)

java_grpc_library(
    name = "billing_java_grpc",
    srcs = [":billing_proto"],
    deps = [":billing_java_proto"],
)

java_gapic_library(
    name = "billing_java_gapic",
    srcs = [":billing_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloud_billing_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudbilling_v1.yaml",
    test_deps = [
        ":billing_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":billing_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "billing_java_gapic_test_suite",
    test_classes = [
        # This test is temporarily disabled due to the issue:
        # https://github.com/googleapis/sdk-platform-java/issues/1780
        # "com.google.cloud.billing.v1.CloudBillingClientHttpJsonTest",
        "com.google.cloud.billing.v1.CloudBillingClientTest",
        "com.google.cloud.billing.v1.CloudCatalogClientHttpJsonTest",
        "com.google.cloud.billing.v1.CloudCatalogClientTest",
    ],
    runtime_deps = [":billing_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-billing-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":billing_java_gapic",
        ":billing_java_grpc",
        ":billing_java_proto",
        ":billing_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "billing_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/billing/apiv1/billingpb",
    protos = [":billing_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "billing_go_gapic",
    srcs = [":billing_proto_with_info"],
    grpc_service_config = "cloud_billing_grpc_service_config.json",
    importpath = "cloud.google.com/go/billing/apiv1;billing",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudbilling_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":billing_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-billing-v1-go",
    deps = [
        ":billing_go_gapic",
        ":billing_go_gapic_srcjar-metadata.srcjar",
        ":billing_go_gapic_srcjar-snippets.srcjar",
        ":billing_go_gapic_srcjar-test.srcjar",
        ":billing_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "billing_py_gapic",
    srcs = [":billing_proto"],
    grpc_service_config = "cloud_billing_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudbilling_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "billing_py_gapic_test",
    srcs = [
        "billing_py_gapic_pytest.py",
        "billing_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":billing_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "billing-v1-py",
    deps = [
        ":billing_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "billing_php_proto",
    deps = [":billing_proto"],
)

php_gapic_library(
    name = "billing_php_gapic",
    srcs = [":billing_proto_with_info"],
    grpc_service_config = "cloud_billing_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudbilling_v1.yaml",
    transport = "grpc+rest",
    deps = [":billing_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-billing-v1-php",
    deps = [
        ":billing_php_gapic",
        ":billing_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "billing_nodejs_gapic",
    package_name = "@google-cloud/billing",
    src = ":billing_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloud_billing_grpc_service_config.json",
    package = "google.cloud.billing.v1",
    rest_numeric_enums = True,
    service_yaml = "cloudbilling_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "billing-v1-nodejs",
    deps = [
        ":billing_nodejs_gapic",
        ":billing_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "billing_ruby_proto",
    deps = [":billing_proto"],
)

ruby_grpc_library(
    name = "billing_ruby_grpc",
    srcs = [":billing_proto"],
    deps = [":billing_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "billing_ruby_gapic",
    srcs = [":billing_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=cloudbilling.googleapis.com",
        "ruby-cloud-api-shortname=cloudbilling",
        "ruby-cloud-env-prefix=BILLING",
        "ruby-cloud-gem-name=google-cloud-billing-v1",
        "ruby-cloud-product-url=https://cloud.google.com/billing",
    ],
    grpc_service_config = "cloud_billing_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Allows developers to manage billing for their Google Cloud Platform projects programmatically.",
    ruby_cloud_title = "Billing V1",
    service_yaml = "cloudbilling_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":billing_ruby_grpc",
        ":billing_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-billing-v1-ruby",
    deps = [
        ":billing_ruby_gapic",
        ":billing_ruby_grpc",
        ":billing_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "billing_csharp_proto",
    deps = [":billing_proto"],
)

csharp_grpc_library(
    name = "billing_csharp_grpc",
    srcs = [":billing_proto"],
    deps = [":billing_csharp_proto"],
)

csharp_gapic_library(
    name = "billing_csharp_gapic",
    srcs = [":billing_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloud_billing_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudbilling_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":billing_csharp_grpc",
        ":billing_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-billing-v1-csharp",
    deps = [
        ":billing_csharp_gapic",
        ":billing_csharp_grpc",
        ":billing_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "billing_cc_proto",
    deps = [":billing_proto"],
)

cc_grpc_library(
    name = "billing_cc_grpc",
    srcs = [":billing_proto"],
    grpc_only = True,
    deps = [":billing_cc_proto"],
)
