type: google.api.Service
config_version: 3
name: cloudbilling.googleapis.com
title: Cloud Billing API

apis:
- name: google.cloud.billing.v1.CloudBilling
- name: google.cloud.billing.v1.CloudCatalog

documentation:
  summary: |-
    Allows developers to manage billing for their Google Cloud Platform
    projects     programmatically.

authentication:
  rules:
  - selector: 'google.cloud.billing.v1.CloudBilling.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-billing,
        https://www.googleapis.com/auth/cloud-billing.readonly,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.billing.v1.CloudBilling.CreateBillingAccount
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-billing,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.billing.v1.CloudBilling.MoveBillingAccount
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-billing,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.billing.v1.CloudBilling.SetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-billing,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.billing.v1.CloudBilling.UpdateBillingAccount
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-billing,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.billing.v1.CloudBilling.UpdateProjectBillingInfo
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-billing,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.billing.v1.CloudCatalog.ListServices
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-billing,
        https://www.googleapis.com/auth/cloud-billing.readonly,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.billing.v1.CloudCatalog.ListSkus
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-billing,
        https://www.googleapis.com/auth/cloud-billing.readonly,
        https://www.googleapis.com/auth/cloud-platform
