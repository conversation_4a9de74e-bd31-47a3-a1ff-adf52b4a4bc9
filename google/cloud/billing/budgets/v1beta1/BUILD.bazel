# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "budgets_proto",
    srcs = [
        "budget_model.proto",
        "budget_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/type:date_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library_with_info(
    name = "budgets_proto_with_info",
    deps = [
        ":budgets_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "budgets_java_proto",
    deps = [":budgets_proto"],
)

java_grpc_library(
    name = "budgets_java_grpc",
    srcs = [":budgets_proto"],
    deps = [":budgets_java_proto"],
)

java_gapic_library(
    name = "budgets_java_gapic",
    srcs = [":budgets_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "billingbudgets_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "billingbudgets.yaml",
    test_deps = [
        ":budgets_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":budgets_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "budgets_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.billing.budgets.v1beta1.BudgetServiceClientTest",
    ],
    runtime_deps = [":budgets_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-billing-budgets-v1beta1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":budgets_java_gapic",
        ":budgets_java_grpc",
        ":budgets_java_proto",
        ":budgets_proto",
    ],
)

go_proto_library(
    name = "budgets_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/billing/budgets/apiv1beta1/budgetspb",
    protos = [":budgets_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:date_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "budgets_go_gapic",
    srcs = [":budgets_proto_with_info"],
    grpc_service_config = "billingbudgets_grpc_service_config.json",
    importpath = "cloud.google.com/go/billing/budgets/apiv1beta1;budgets",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "billingbudgets.yaml",
    transport = "grpc+rest",
    deps = [
        ":budgets_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-billing-budgets-v1beta1-go",
    deps = [
        ":budgets_go_gapic",
        ":budgets_go_gapic_srcjar-metadata.srcjar",
        ":budgets_go_gapic_srcjar-snippets.srcjar",
        ":budgets_go_gapic_srcjar-test.srcjar",
        ":budgets_go_proto",
    ],
)

py_gapic_library(
    name = "budgets_py_gapic",
    srcs = [":budgets_proto"],
    grpc_service_config = "billingbudgets_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "billingbudgets.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "budgets_py_gapic_test",
    srcs = [
        "budgets_py_gapic_pytest.py",
        "budgets_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":budgets_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "billing-budgets-v1beta1-py",
    deps = [
        ":budgets_py_gapic",
    ],
)

php_proto_library(
    name = "budgets_php_proto",
    deps = [":budgets_proto"],
)

php_gapic_library(
    name = "budgets_php_gapic",
    srcs = [":budgets_proto_with_info"],
    grpc_service_config = "billingbudgets_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "billingbudgets.yaml",
    transport = "grpc+rest",
    deps = [
        ":budgets_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-billing-budgets-v1beta1-php",
    deps = [
        ":budgets_php_gapic",
        ":budgets_php_proto",
    ],
)

nodejs_gapic_library(
    name = "budgets_nodejs_gapic",
    package_name = "@google-cloud/billing-budgets",
    src = ":budgets_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "billingbudgets_grpc_service_config.json",
    package = "google.cloud.billing.budgets.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "billingbudgets.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "billing-budgets-v1beta1-nodejs",
    deps = [
        ":budgets_nodejs_gapic",
        ":budgets_proto",
    ],
)

ruby_proto_library(
    name = "budgets_ruby_proto",
    deps = [":budgets_proto"],
)

ruby_grpc_library(
    name = "budgets_ruby_grpc",
    srcs = [":budgets_proto"],
    deps = [":budgets_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "budgets_ruby_gapic",
    srcs = [":budgets_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=billingbudgets.googleapis.com",
        "ruby-cloud-api-shortname=billingbudgets",
        "ruby-cloud-env-prefix=BILLING_BUDGETS",
        "ruby-cloud-gem-name=google-cloud-billing-budgets-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/billing/docs/how-to/budget-api-overview",
    ],
    grpc_service_config = "billingbudgets_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Provides methods to view, create, and manage Cloud Billing budgets programmatically at scale.",
    ruby_cloud_title = "Billing Budgets V1beta1",
    service_yaml = "billingbudgets.yaml",
    transport = "grpc",
    deps = [
        ":budgets_ruby_grpc",
        ":budgets_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-billing-budgets-v1beta1-ruby",
    deps = [
        ":budgets_ruby_gapic",
        ":budgets_ruby_grpc",
        ":budgets_ruby_proto",
    ],
)

csharp_proto_library(
    name = "budgets_csharp_proto",
    deps = [":budgets_proto"],
)

csharp_grpc_library(
    name = "budgets_csharp_grpc",
    srcs = [":budgets_proto"],
    deps = [":budgets_csharp_proto"],
)

csharp_gapic_library(
    name = "budgets_csharp_gapic",
    srcs = [":budgets_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "billingbudgets_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "billingbudgets.yaml",
    transport = "grpc",
    deps = [
        ":budgets_csharp_grpc",
        ":budgets_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-billing-budgets-v1beta1-csharp",
    deps = [
        ":budgets_csharp_gapic",
        ":budgets_csharp_grpc",
        ":budgets_csharp_proto",
    ],
)

cc_proto_library(
    name = "budgets_cc_proto",
    deps = [":budgets_proto"],
)

cc_grpc_library(
    name = "budgets_cc_grpc",
    srcs = [":budgets_proto"],
    grpc_only = True,
    deps = [":budgets_cc_proto"],
)
