# This build file includes a target for the Ruby wrapper library for
# google-cloud-billing-budgets.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for billingbudgets.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "billingbudgets_ruby_wrapper",
    srcs = ["//google/cloud/billing/budgets/v1:budgets_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-billing-budgets",
        "ruby-cloud-env-prefix=BILLING_BUDGETS",
        "ruby-cloud-wrapper-of=v1:0.7;v1beta1:0.11",
        "ruby-cloud-product-url=https://cloud.google.com/billing/docs/how-to/budget-api-overview",
        "ruby-cloud-api-id=billingbudgets.googleapis.com",
        "ruby-cloud-api-shortname=billingbudgets",
    ],
    ruby_cloud_description = "Provides methods to view, create, and manage Cloud Billing budgets programmatically at scale.",
    ruby_cloud_title = "Billing Budgets",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-billing-budgets-ruby",
    deps = [
        ":billingbudgets_ruby_wrapper",
    ],
)
