# This build file includes a target for the Ruby wrapper library for
# google-cloud-billing.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudbilling.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "cloudbilling_ruby_wrapper",
    srcs = ["//google/cloud/billing/v1:billing_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-billing",
        "ruby-cloud-env-prefix=BILLING",
        "ruby-cloud-wrapper-of=v1:0.17",
        "ruby-cloud-product-url=https://cloud.google.com/billing",
        "ruby-cloud-api-id=cloudbilling.googleapis.com",
        "ruby-cloud-api-shortname=cloudbilling",
        "ruby-cloud-factory-method-suffix=_service",
    ],
    ruby_cloud_description = "Allows developers to manage billing for their Google Cloud Platform projects programmatically.",
    ruby_cloud_title = "Billing",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-billing-ruby",
    deps = [
        ":cloudbilling_ruby_wrapper",
    ],
)
