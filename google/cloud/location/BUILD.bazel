# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "location_proto",
    srcs = [
        "locations.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "@com_google_protobuf//:any_proto",
    ],
)

proto_library_with_info(
    name = "location_proto_with_info",
    deps = [
        ":location_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "location_java_proto",
    deps = [":location_proto"],
)

java_grpc_library(
    name = "location_java_grpc",
    srcs = [":location_proto"],
    deps = [":location_java_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-location-java",
    transport = "grpc+rest",
    deps = [
        ":location_java_grpc",
        ":location_java_proto",
        ":location_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "location_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/cloud/location",
    protos = [":location_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_grpc_library",
    "py_proto_library",
)

py_proto_library(
    name = "location_py_proto",
    deps = [":location_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "location-py",
    deps = [
        ":location_py_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "location_php_proto",
    deps = [":location_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate php files for these protos.
# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-location-php",
    deps = [":location_php_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "location_ruby_proto",
    deps = [":location_proto"],
)

ruby_grpc_library(
    name = "location_ruby_grpc",
    srcs = [":location_proto"],
    deps = [":location_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "location_ruby_gapic",
    srcs = [":location_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-location",
    ],
    rest_numeric_enums = True,
    ruby_cloud_description = "An add-on interface used by some Google API clients to provide location management calls.",
    ruby_cloud_title = "Locations",
    transport = "grpc+rest",
    deps = [
        ":location_ruby_grpc",
        ":location_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-location-ruby",
    deps = [
        ":location_ruby_gapic",
        ":location_ruby_grpc",
        ":location_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "location_csharp_proto",
    deps = [":location_proto"],
)

csharp_grpc_library(
    name = "location_csharp_grpc",
    srcs = [":location_proto"],
    deps = [":location_csharp_proto"],
)

csharp_gapic_library(
    name = "location_csharp_gapic",
    srcs = [":location_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    rest_numeric_enums = True,
    deps = [
        ":location_csharp_grpc",
        ":location_csharp_proto",
    ],
)

csharp_gapic_assembly_pkg(
    name = "google-cloud-location-csharp",
    deps = [
        ":location_csharp_gapic",
        ":location_csharp_grpc",
        ":location_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "location_cc_proto",
    deps = [":location_proto"],
)

cc_grpc_library(
    name = "location_cc_grpc",
    srcs = [":location_proto"],
    grpc_only = True,
    deps = [":location_cc_proto"],
)
