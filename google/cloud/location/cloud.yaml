type: google.api.Service
config_version: 3
name: cloud.googleapis.com
title: Cloud Metadata API

apis:
- name: google.cloud.location.Locations

documentation:
  summary: |-
    This API provides static metadata about Google Cloud Platform. Currently,
    it only provides basic information about Google Cloud locations, such as
    zones, regions, and countries.

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
