// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apigeeregistry.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ApigeeRegistry.V1";
option go_package = "cloud.google.com/go/apigeeregistry/apiv1/apigeeregistrypb;apigeeregistrypb";
option java_multiple_files = true;
option java_outer_classname = "RegistryModelsProto";
option java_package = "com.google.cloud.apigeeregistry.v1";
option php_namespace = "Google\\Cloud\\ApigeeRegistry\\V1";
option ruby_package = "Google::Cloud::ApigeeRegistry::V1";

// A top-level description of an API.
// Produced by producers and are commitments to provide services.
message Api {
  option (google.api.resource) = {
    type: "apigeeregistry.googleapis.com/Api"
    pattern: "projects/{project}/locations/{location}/apis/{api}"
  };

  // Resource name.
  string name = 1;

  // Human-meaningful name.
  string display_name = 2;

  // A detailed description.
  string description = 3;

  // Output only. Creation timestamp.
  google.protobuf.Timestamp create_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp.
  google.protobuf.Timestamp update_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A user-definable description of the availability of this service.
  // Format: free-form, but we expect single words that describe availability,
  // e.g., "NONE", "TESTING", "PREVIEW", "GENERAL", "DEPRECATED", "SHUTDOWN".
  string availability = 6;

  // The recommended version of the API.
  // Format: `apis/{api}/versions/{version}`
  string recommended_version = 7 [(google.api.resource_reference) = {
                                    type: "apigeeregistry.googleapis.com/ApiVersion"
                                  }];

  // The recommended deployment of the API.
  // Format: `apis/{api}/deployments/{deployment}`
  string recommended_deployment = 8 [(google.api.resource_reference) = {
                                       type: "apigeeregistry.googleapis.com/ApiDeployment"
                                     }];

  // Labels attach identifying metadata to resources. Identifying metadata can
  // be used to filter list operations.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores, and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one resource (System
  // labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with
  // `apigeeregistry.googleapis.com/` and cannot be changed.
  map<string, string> labels = 9;

  // Annotations attach non-identifying metadata to resources.
  //
  // Annotation keys and values are less restricted than those of labels, but
  // should be generally used for small values of broad interest. Larger, topic-
  // specific metadata should be stored in Artifacts.
  map<string, string> annotations = 10;
}

// Describes a particular version of an API.
// ApiVersions are what consumers actually use.
message ApiVersion {
  option (google.api.resource) = {
    type: "apigeeregistry.googleapis.com/ApiVersion"
    pattern: "projects/{project}/locations/{location}/apis/{api}/versions/{version}"
  };

  // Resource name.
  string name = 1;

  // Human-meaningful name.
  string display_name = 2;

  // A detailed description.
  string description = 3;

  // Output only. Creation timestamp.
  google.protobuf.Timestamp create_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp.
  google.protobuf.Timestamp update_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A user-definable description of the lifecycle phase of this API version.
  // Format: free-form, but we expect single words that describe API maturity,
  // e.g., "CONCEPT", "DESIGN", "DEVELOPMENT", "STAGING", "PRODUCTION",
  // "DEPRECATED", "RETIRED".
  string state = 6;

  // Labels attach identifying metadata to resources. Identifying metadata can
  // be used to filter list operations.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one resource (System
  // labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with
  // `apigeeregistry.googleapis.com/` and cannot be changed.
  map<string, string> labels = 7;

  // Annotations attach non-identifying metadata to resources.
  //
  // Annotation keys and values are less restricted than those of labels, but
  // should be generally used for small values of broad interest. Larger, topic-
  // specific metadata should be stored in Artifacts.
  map<string, string> annotations = 8;
}

// Describes a version of an API in a structured way.
// ApiSpecs provide formal descriptions that consumers can use to use a version.
// ApiSpec resources are intended to be fully-resolved descriptions of an
// ApiVersion. When specs consist of multiple files, these should be bundled
// together (e.g., in a zip archive) and stored as a unit. Multiple specs can
// exist to provide representations in different API description formats.
// Synchronization of these representations would be provided by tooling and
// background services.
message ApiSpec {
  option (google.api.resource) = {
    type: "apigeeregistry.googleapis.com/ApiSpec"
    pattern: "projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}"
  };

  // Resource name.
  string name = 1;

  // A possibly-hierarchical name used to refer to the spec from other specs.
  string filename = 2;

  // A detailed description.
  string description = 3;

  // Output only. Immutable. The revision ID of the spec.
  // A new revision is committed whenever the spec contents are changed.
  // The format is an 8-character hexadecimal string.
  string revision_id = 4 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Creation timestamp; when the spec resource was created.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Revision creation timestamp; when the represented revision was created.
  google.protobuf.Timestamp revision_create_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp: when the represented revision was last modified.
  google.protobuf.Timestamp revision_update_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A style (format) descriptor for this spec that is specified as a Media Type
  // (https://en.wikipedia.org/wiki/Media_type). Possible values include
  // `application/vnd.apigee.proto`, `application/vnd.apigee.openapi`, and
  // `application/vnd.apigee.graphql`, with possible suffixes representing
  // compression types. These hypothetical names are defined in the vendor tree
  // defined in RFC6838 (https://tools.ietf.org/html/rfc6838) and are not final.
  // Content types can specify compression. Currently only GZip compression is
  // supported (indicated with "+gzip").
  string mime_type = 8;

  // Output only. The size of the spec file in bytes. If the spec is gzipped, this is the
  // size of the uncompressed spec.
  int32 size_bytes = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A SHA-256 hash of the spec's contents. If the spec is gzipped, this is
  // the hash of the uncompressed spec.
  string hash = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The original source URI of the spec (if one exists).
  // This is an external location that can be used for reference purposes
  // but which may not be authoritative since this external resource may
  // change after the spec is retrieved.
  string source_uri = 11;

  // Input only. The contents of the spec.
  // Provided by API callers when specs are created or updated.
  // To access the contents of a spec, use GetApiSpecContents.
  bytes contents = 12 [(google.api.field_behavior) = INPUT_ONLY];

  // Labels attach identifying metadata to resources. Identifying metadata can
  // be used to filter list operations.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one resource (System
  // labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with
  // `apigeeregistry.googleapis.com/` and cannot be changed.
  map<string, string> labels = 14;

  // Annotations attach non-identifying metadata to resources.
  //
  // Annotation keys and values are less restricted than those of labels, but
  // should be generally used for small values of broad interest. Larger, topic-
  // specific metadata should be stored in Artifacts.
  map<string, string> annotations = 15;
}

// Describes a service running at particular address that
// provides a particular version of an API. ApiDeployments have revisions which
// correspond to different configurations of a single deployment in time.
// Revision identifiers should be updated whenever the served API spec or
// endpoint address changes.
message ApiDeployment {
  option (google.api.resource) = {
    type: "apigeeregistry.googleapis.com/ApiDeployment"
    pattern: "projects/{project}/locations/{location}/apis/{api}/deployments/{deployment}"
  };

  // Resource name.
  string name = 1;

  // Human-meaningful name.
  string display_name = 2;

  // A detailed description.
  string description = 3;

  // Output only. Immutable. The revision ID of the deployment.
  // A new revision is committed whenever the deployment contents are changed.
  // The format is an 8-character hexadecimal string.
  string revision_id = 4 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Creation timestamp; when the deployment resource was created.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Revision creation timestamp; when the represented revision was created.
  google.protobuf.Timestamp revision_create_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp: when the represented revision was last modified.
  google.protobuf.Timestamp revision_update_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The full resource name (including revision ID) of the spec of the API being
  // served by the deployment. Changes to this value will update the revision.
  // Format: `apis/{api}/deployments/{deployment}`
  string api_spec_revision = 8 [(google.api.resource_reference) = {
                                  type: "apigeeregistry.googleapis.com/ApiSpec"
                                }];

  // The address where the deployment is serving. Changes to this value will
  // update the revision.
  string endpoint_uri = 9;

  // The address of the external channel of the API (e.g., the Developer
  // Portal). Changes to this value will not affect the revision.
  string external_channel_uri = 10;

  // Text briefly identifying the intended audience of the API. Changes to this
  // value will not affect the revision.
  string intended_audience = 11;

  // Text briefly describing how to access the endpoint. Changes to this value
  // will not affect the revision.
  string access_guidance = 12;

  // Labels attach identifying metadata to resources. Identifying metadata can
  // be used to filter list operations.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one resource (System
  // labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with
  // `apigeeregistry.googleapis.com/` and cannot be changed.
  map<string, string> labels = 14;

  // Annotations attach non-identifying metadata to resources.
  //
  // Annotation keys and values are less restricted than those of labels, but
  // should be generally used for small values of broad interest. Larger, topic-
  // specific metadata should be stored in Artifacts.
  map<string, string> annotations = 15;
}

// Artifacts of resources. Artifacts are unique (single-value) per resource
// and are used to store metadata that is too large or numerous to be stored
// directly on the resource. Since artifacts are stored separately from parent
// resources, they should generally be used for metadata that is needed
// infrequently, i.e., not for display in primary views of the resource but
// perhaps displayed or downloaded upon request. The `ListArtifacts` method
// allows artifacts to be quickly enumerated and checked for presence without
// downloading their (potentially-large) contents.
message Artifact {
  option (google.api.resource) = {
    type: "apigeeregistry.googleapis.com/Artifact"
    pattern: "projects/{project}/locations/{location}/artifacts/{artifact}"
    pattern: "projects/{project}/locations/{location}/apis/{api}/artifacts/{artifact}"
    pattern: "projects/{project}/locations/{location}/apis/{api}/versions/{version}/artifacts/{artifact}"
    pattern: "projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}/artifacts/{artifact}"
    pattern: "projects/{project}/locations/{location}/apis/{api}/deployments/{deployment}/artifacts/{artifact}"
  };

  // Resource name.
  string name = 1;

  // Output only. Creation timestamp.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A content type specifier for the artifact.
  // Content type specifiers are Media Types
  // (https://en.wikipedia.org/wiki/Media_type) with a possible "schema"
  // parameter that specifies a schema for the stored information.
  // Content types can specify compression. Currently only GZip compression is
  // supported (indicated with "+gzip").
  string mime_type = 4;

  // Output only. The size of the artifact in bytes. If the artifact is gzipped, this is
  // the size of the uncompressed artifact.
  int32 size_bytes = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A SHA-256 hash of the artifact's contents. If the artifact is gzipped,
  // this is the hash of the uncompressed artifact.
  string hash = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. The contents of the artifact.
  // Provided by API callers when artifacts are created or replaced.
  // To access the contents of an artifact, use GetArtifactContents.
  bytes contents = 7 [(google.api.field_behavior) = INPUT_ONLY];
}
