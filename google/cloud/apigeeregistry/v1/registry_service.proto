// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apigeeregistry.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/api/resource.proto";
import "google/cloud/apigeeregistry/v1/registry_models.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.ApigeeRegistry.V1";
option go_package = "cloud.google.com/go/apigeeregistry/apiv1/apigeeregistrypb;apigeeregistrypb";
option java_multiple_files = true;
option java_outer_classname = "RegistryServiceProto";
option java_package = "com.google.cloud.apigeeregistry.v1";
option php_namespace = "Google\\Cloud\\ApigeeRegistry\\V1";
option ruby_package = "Google::Cloud::ApigeeRegistry::V1";

// The Registry service allows teams to manage descriptions of APIs.
service Registry {
  option (google.api.default_host) = "apigeeregistry.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Returns matching APIs.
  rpc ListApis(ListApisRequest) returns (ListApisResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/apis"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns a specified API.
  rpc GetApi(GetApiRequest) returns (Api) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a specified API.
  rpc CreateApi(CreateApiRequest) returns (Api) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/apis"
      body: "api"
    };
    option (google.api.method_signature) = "parent,api,api_id";
  }

  // Used to modify a specified API.
  rpc UpdateApi(UpdateApiRequest) returns (Api) {
    option (google.api.http) = {
      patch: "/v1/{api.name=projects/*/locations/*/apis/*}"
      body: "api"
    };
    option (google.api.method_signature) = "api,update_mask";
  }

  // Removes a specified API and all of the resources that it
  // owns.
  rpc DeleteApi(DeleteApiRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns matching versions.
  rpc ListApiVersions(ListApiVersionsRequest) returns (ListApiVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/apis/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns a specified version.
  rpc GetApiVersion(GetApiVersionRequest) returns (ApiVersion) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a specified version.
  rpc CreateApiVersion(CreateApiVersionRequest) returns (ApiVersion) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/apis/*}/versions"
      body: "api_version"
    };
    option (google.api.method_signature) = "parent,api_version,api_version_id";
  }

  // Used to modify a specified version.
  rpc UpdateApiVersion(UpdateApiVersionRequest) returns (ApiVersion) {
    option (google.api.http) = {
      patch: "/v1/{api_version.name=projects/*/locations/*/apis/*/versions/*}"
      body: "api_version"
    };
    option (google.api.method_signature) = "api_version,update_mask";
  }

  // Removes a specified version and all of the resources that
  // it owns.
  rpc DeleteApiVersion(DeleteApiVersionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns matching specs.
  rpc ListApiSpecs(ListApiSpecsRequest) returns (ListApiSpecsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/apis/*/versions/*}/specs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns a specified spec.
  rpc GetApiSpec(GetApiSpecRequest) returns (ApiSpec) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the contents of a specified spec.
  // If specs are stored with GZip compression, the default behavior
  // is to return the spec uncompressed (the mime_type response field
  // indicates the exact format returned).
  rpc GetApiSpecContents(GetApiSpecContentsRequest) returns (google.api.HttpBody) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}:getContents"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a specified spec.
  rpc CreateApiSpec(CreateApiSpecRequest) returns (ApiSpec) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/apis/*/versions/*}/specs"
      body: "api_spec"
    };
    option (google.api.method_signature) = "parent,api_spec,api_spec_id";
  }

  // Used to modify a specified spec.
  rpc UpdateApiSpec(UpdateApiSpecRequest) returns (ApiSpec) {
    option (google.api.http) = {
      patch: "/v1/{api_spec.name=projects/*/locations/*/apis/*/versions/*/specs/*}"
      body: "api_spec"
    };
    option (google.api.method_signature) = "api_spec,update_mask";
  }

  // Removes a specified spec, all revisions, and all child
  // resources (e.g., artifacts).
  rpc DeleteApiSpec(DeleteApiSpecRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Adds a tag to a specified revision of a spec.
  rpc TagApiSpecRevision(TagApiSpecRevisionRequest) returns (ApiSpec) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}:tagRevision"
      body: "*"
    };
  }

  // Lists all revisions of a spec.
  // Revisions are returned in descending order of revision creation time.
  rpc ListApiSpecRevisions(ListApiSpecRevisionsRequest) returns (ListApiSpecRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}:listRevisions"
    };
  }

  // Sets the current revision to a specified prior revision.
  // Note that this creates a new revision with a new revision ID.
  rpc RollbackApiSpec(RollbackApiSpecRequest) returns (ApiSpec) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}:rollback"
      body: "*"
    };
  }

  // Deletes a revision of a spec.
  rpc DeleteApiSpecRevision(DeleteApiSpecRevisionRequest) returns (ApiSpec) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}:deleteRevision"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns matching deployments.
  rpc ListApiDeployments(ListApiDeploymentsRequest) returns (ListApiDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/apis/*}/deployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns a specified deployment.
  rpc GetApiDeployment(GetApiDeploymentRequest) returns (ApiDeployment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a specified deployment.
  rpc CreateApiDeployment(CreateApiDeploymentRequest) returns (ApiDeployment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/apis/*}/deployments"
      body: "api_deployment"
    };
    option (google.api.method_signature) = "parent,api_deployment,api_deployment_id";
  }

  // Used to modify a specified deployment.
  rpc UpdateApiDeployment(UpdateApiDeploymentRequest) returns (ApiDeployment) {
    option (google.api.http) = {
      patch: "/v1/{api_deployment.name=projects/*/locations/*/apis/*/deployments/*}"
      body: "api_deployment"
    };
    option (google.api.method_signature) = "api_deployment,update_mask";
  }

  // Removes a specified deployment, all revisions, and all
  // child resources (e.g., artifacts).
  rpc DeleteApiDeployment(DeleteApiDeploymentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Adds a tag to a specified revision of a
  // deployment.
  rpc TagApiDeploymentRevision(TagApiDeploymentRevisionRequest) returns (ApiDeployment) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/apis/*/deployments/*}:tagRevision"
      body: "*"
    };
  }

  // Lists all revisions of a deployment.
  // Revisions are returned in descending order of revision creation time.
  rpc ListApiDeploymentRevisions(ListApiDeploymentRevisionsRequest) returns (ListApiDeploymentRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/deployments/*}:listRevisions"
    };
  }

  // Sets the current revision to a specified prior
  // revision. Note that this creates a new revision with a new revision ID.
  rpc RollbackApiDeployment(RollbackApiDeploymentRequest) returns (ApiDeployment) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/apis/*/deployments/*}:rollback"
      body: "*"
    };
  }

  // Deletes a revision of a deployment.
  rpc DeleteApiDeploymentRevision(DeleteApiDeploymentRevisionRequest) returns (ApiDeployment) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*/deployments/*}:deleteRevision"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns matching artifacts.
  rpc ListArtifacts(ListArtifactsRequest) returns (ListArtifactsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/artifacts"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/apis/*}/artifacts"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/apis/*/versions/*}/artifacts"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/apis/*/versions/*/specs/*}/artifacts"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/apis/*/deployments/*}/artifacts"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns a specified artifact.
  rpc GetArtifact(GetArtifactRequest) returns (Artifact) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/artifacts/*}"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/apis/*/artifacts/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/artifacts/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*/artifacts/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/apis/*/deployments/*/artifacts/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the contents of a specified artifact.
  // If artifacts are stored with GZip compression, the default behavior
  // is to return the artifact uncompressed (the mime_type response field
  // indicates the exact format returned).
  rpc GetArtifactContents(GetArtifactContentsRequest) returns (google.api.HttpBody) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/artifacts/*}:getContents"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/apis/*/artifacts/*}:getContents"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/artifacts/*}:getContents"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*/artifacts/*}:getContents"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/apis/*/deployments/*/artifacts/*}:getContents"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a specified artifact.
  rpc CreateArtifact(CreateArtifactRequest) returns (Artifact) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/artifacts"
      body: "artifact"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/apis/*}/artifacts"
        body: "artifact"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/apis/*/versions/*}/artifacts"
        body: "artifact"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/apis/*/versions/*/specs/*}/artifacts"
        body: "artifact"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/apis/*/deployments/*}/artifacts"
        body: "artifact"
      }
    };
    option (google.api.method_signature) = "parent,artifact,artifact_id";
  }

  // Used to replace a specified artifact.
  rpc ReplaceArtifact(ReplaceArtifactRequest) returns (Artifact) {
    option (google.api.http) = {
      put: "/v1/{artifact.name=projects/*/locations/*/artifacts/*}"
      body: "artifact"
      additional_bindings {
        put: "/v1/{artifact.name=projects/*/locations/*/apis/*/artifacts/*}"
        body: "artifact"
      }
      additional_bindings {
        put: "/v1/{artifact.name=projects/*/locations/*/apis/*/versions/*/artifacts/*}"
        body: "artifact"
      }
      additional_bindings {
        put: "/v1/{artifact.name=projects/*/locations/*/apis/*/versions/*/specs/*/artifacts/*}"
        body: "artifact"
      }
      additional_bindings {
        put: "/v1/{artifact.name=projects/*/locations/*/apis/*/deployments/*/artifacts/*}"
        body: "artifact"
      }
    };
    option (google.api.method_signature) = "artifact";
  }

  // Removes a specified artifact.
  rpc DeleteArtifact(DeleteArtifactRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/artifacts/*}"
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/apis/*/artifacts/*}"
      }
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/apis/*/versions/*/artifacts/*}"
      }
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*/artifacts/*}"
      }
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/apis/*/deployments/*/artifacts/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for ListApis.
message ListApisRequest {
  // Required. The parent, which owns this collection of APIs.
  // Format: `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/Api"
    }
  ];

  // The maximum number of APIs to return.
  // The service may return fewer than this value.
  // If unspecified, at most 50 values will be returned.
  // The maximum is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListApis` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListApis` must match
  // the call that provided the page token.
  string page_token = 3;

  // An expression that can be used to filter the list. Filters use the Common
  // Expression Language and can refer to all message fields.
  string filter = 4;
}

// Response message for ListApis.
message ListApisResponse {
  // The APIs from the specified publisher.
  repeated Api apis = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetApi.
message GetApiRequest {
  // Required. The name of the API to retrieve.
  // Format: `projects/*/locations/*/apis/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/Api"
    }
  ];
}

// Request message for CreateApi.
message CreateApiRequest {
  // Required. The parent, which owns this collection of APIs.
  // Format: `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/Api"
    }
  ];

  // Required. The API to create.
  Api api = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the API, which will become the final component of
  // the API's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  //
  // Following AIP-162, IDs must not have the form of a UUID.
  string api_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateApi.
message UpdateApiRequest {
  // Required. The API to update.
  //
  // The `name` field is used to identify the API to update.
  // Format: `projects/*/locations/*/apis/*`
  Api api = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated. If omitted, all fields are updated that
  // are set in the request message (fields set to default values are ignored).
  // If an asterisk "*" is specified, all fields are updated, including fields
  // that are unspecified/default in the request.
  google.protobuf.FieldMask update_mask = 2;

  // If set to true, and the API is not found, a new API will be created.
  // In this situation, `update_mask` is ignored.
  bool allow_missing = 3;
}

// Request message for DeleteApi.
message DeleteApiRequest {
  // Required. The name of the API to delete.
  // Format: `projects/*/locations/*/apis/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/Api"
    }
  ];

  // If set to true, any child resources will also be deleted.
  // (Otherwise, the request will only work if there are no child resources.)
  bool force = 2;
}

// Request message for ListApiVersions.
message ListApiVersionsRequest {
  // Required. The parent, which owns this collection of versions.
  // Format: `projects/*/locations/*/apis/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/ApiVersion"
    }
  ];

  // The maximum number of versions to return.
  // The service may return fewer than this value.
  // If unspecified, at most 50 values will be returned.
  // The maximum is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListApiVersions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListApiVersions` must
  // match the call that provided the page token.
  string page_token = 3;

  // An expression that can be used to filter the list. Filters use the Common
  // Expression Language and can refer to all message fields.
  string filter = 4;
}

// Response message for ListApiVersions.
message ListApiVersionsResponse {
  // The versions from the specified publisher.
  repeated ApiVersion api_versions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetApiVersion.
message GetApiVersionRequest {
  // Required. The name of the version to retrieve.
  // Format: `projects/*/locations/*/apis/*/versions/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiVersion"
    }
  ];
}

// Request message for CreateApiVersion.
message CreateApiVersionRequest {
  // Required. The parent, which owns this collection of versions.
  // Format: `projects/*/locations/*/apis/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/ApiVersion"
    }
  ];

  // Required. The version to create.
  ApiVersion api_version = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the version, which will become the final component of
  // the version's resource name.
  //
  // This value should be 1-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  //
  // Following AIP-162, IDs must not have the form of a UUID.
  string api_version_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateApiVersion.
message UpdateApiVersionRequest {
  // Required. The version to update.
  //
  // The `name` field is used to identify the version to update.
  // Format: `projects/*/locations/*/apis/*/versions/*`
  ApiVersion api_version = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated. If omitted, all fields are updated that
  // are set in the request message (fields set to default values are ignored).
  // If an asterisk "*" is specified, all fields are updated, including fields
  // that are unspecified/default in the request.
  google.protobuf.FieldMask update_mask = 2;

  // If set to true, and the version is not found, a new version will be
  // created. In this situation, `update_mask` is ignored.
  bool allow_missing = 3;
}

// Request message for DeleteApiVersion.
message DeleteApiVersionRequest {
  // Required. The name of the version to delete.
  // Format: `projects/*/locations/*/apis/*/versions/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiVersion"
    }
  ];

  // If set to true, any child resources will also be deleted.
  // (Otherwise, the request will only work if there are no child resources.)
  bool force = 2;
}

// Request message for ListApiSpecs.
message ListApiSpecsRequest {
  // Required. The parent, which owns this collection of specs.
  // Format: `projects/*/locations/*/apis/*/versions/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];

  // The maximum number of specs to return.
  // The service may return fewer than this value.
  // If unspecified, at most 50 values will be returned.
  // The maximum is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListApiSpecs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListApiSpecs` must match
  // the call that provided the page token.
  string page_token = 3;

  // An expression that can be used to filter the list. Filters use the Common
  // Expression Language and can refer to all message fields except contents.
  string filter = 4;
}

// Response message for ListApiSpecs.
message ListApiSpecsResponse {
  // The specs from the specified publisher.
  repeated ApiSpec api_specs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetApiSpec.
message GetApiSpecRequest {
  // Required. The name of the spec to retrieve.
  // Format: `projects/*/locations/*/apis/*/versions/*/specs/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];
}

// Request message for GetApiSpecContents.
message GetApiSpecContentsRequest {
  // Required. The name of the spec whose contents should be retrieved.
  // Format: `projects/*/locations/*/apis/*/versions/*/specs/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];
}

// Request message for CreateApiSpec.
message CreateApiSpecRequest {
  // Required. The parent, which owns this collection of specs.
  // Format: `projects/*/locations/*/apis/*/versions/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];

  // Required. The spec to create.
  ApiSpec api_spec = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the spec, which will become the final component of
  // the spec's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  //
  // Following AIP-162, IDs must not have the form of a UUID.
  string api_spec_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateApiSpec.
message UpdateApiSpecRequest {
  // Required. The spec to update.
  //
  // The `name` field is used to identify the spec to update.
  // Format: `projects/*/locations/*/apis/*/versions/*/specs/*`
  ApiSpec api_spec = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated. If omitted, all fields are updated that
  // are set in the request message (fields set to default values are ignored).
  // If an asterisk "*" is specified, all fields are updated, including fields
  // that are unspecified/default in the request.
  google.protobuf.FieldMask update_mask = 2;

  // If set to true, and the spec is not found, a new spec will be created.
  // In this situation, `update_mask` is ignored.
  bool allow_missing = 3;
}

// Request message for DeleteApiSpec.
message DeleteApiSpecRequest {
  // Required. The name of the spec to delete.
  // Format: `projects/*/locations/*/apis/*/versions/*/specs/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];

  // If set to true, any child resources will also be deleted.
  // (Otherwise, the request will only work if there are no child resources.)
  bool force = 2;
}

// Request message for TagApiSpecRevision.
message TagApiSpecRevisionRequest {
  // Required. The name of the spec to be tagged, including the revision ID.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];

  // Required. The tag to apply.
  // The tag should be at most 40 characters, and match `[a-z][a-z0-9-]{3,39}`.
  string tag = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListApiSpecRevisions.
message ListApiSpecRevisionsRequest {
  // Required. The name of the spec to list revisions for.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];

  // The maximum number of revisions to return per page.
  int32 page_size = 2;

  // The page token, received from a previous ListApiSpecRevisions call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3;
}

// Response message for ListApiSpecRevisionsResponse.
message ListApiSpecRevisionsResponse {
  // The revisions of the spec.
  repeated ApiSpec api_specs = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for RollbackApiSpec.
message RollbackApiSpecRequest {
  // Required. The spec being rolled back.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];

  // Required. The revision ID to roll back to.
  // It must be a revision of the same spec.
  //
  //   Example: `c7cfa2a8`
  string revision_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteApiSpecRevision.
message DeleteApiSpecRevisionRequest {
  // Required. The name of the spec revision to be deleted,
  // with a revision ID explicitly included.
  //
  // Example:
  // `projects/sample/locations/global/apis/petstore/versions/1.0.0/specs/openapi.yaml@c7cfa2a8`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiSpec"
    }
  ];
}

// Request message for ListApiDeployments.
message ListApiDeploymentsRequest {
  // Required. The parent, which owns this collection of deployments.
  // Format: `projects/*/locations/*/apis/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/ApiDeployment"
    }
  ];

  // The maximum number of deployments to return.
  // The service may return fewer than this value.
  // If unspecified, at most 50 values will be returned.
  // The maximum is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListApiDeployments` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListApiDeployments` must
  // match the call that provided the page token.
  string page_token = 3;

  // An expression that can be used to filter the list. Filters use the Common
  // Expression Language and can refer to all message fields.
  string filter = 4;
}

// Response message for ListApiDeployments.
message ListApiDeploymentsResponse {
  // The deployments from the specified publisher.
  repeated ApiDeployment api_deployments = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetApiDeployment.
message GetApiDeploymentRequest {
  // Required. The name of the deployment to retrieve.
  // Format: `projects/*/locations/*/apis/*/deployments/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiDeployment"
    }
  ];
}

// Request message for CreateApiDeployment.
message CreateApiDeploymentRequest {
  // Required. The parent, which owns this collection of deployments.
  // Format: `projects/*/locations/*/apis/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/ApiDeployment"
    }
  ];

  // Required. The deployment to create.
  ApiDeployment api_deployment = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the deployment, which will become the final component of
  // the deployment's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  //
  // Following AIP-162, IDs must not have the form of a UUID.
  string api_deployment_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateApiDeployment.
message UpdateApiDeploymentRequest {
  // Required. The deployment to update.
  //
  // The `name` field is used to identify the deployment to update.
  // Format: `projects/*/locations/*/apis/*/deployments/*`
  ApiDeployment api_deployment = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated. If omitted, all fields are updated that
  // are set in the request message (fields set to default values are ignored).
  // If an asterisk "*" is specified, all fields are updated, including fields
  // that are unspecified/default in the request.
  google.protobuf.FieldMask update_mask = 2;

  // If set to true, and the deployment is not found, a new deployment will be
  // created. In this situation, `update_mask` is ignored.
  bool allow_missing = 3;
}

// Request message for DeleteApiDeployment.
message DeleteApiDeploymentRequest {
  // Required. The name of the deployment to delete.
  // Format: `projects/*/locations/*/apis/*/deployments/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiDeployment"
    }
  ];

  // If set to true, any child resources will also be deleted.
  // (Otherwise, the request will only work if there are no child resources.)
  bool force = 2;
}

// Request message for TagApiDeploymentRevision.
message TagApiDeploymentRevisionRequest {
  // Required. The name of the deployment to be tagged, including the revision ID.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiDeployment"
    }
  ];

  // Required. The tag to apply.
  // The tag should be at most 40 characters, and match `[a-z][a-z0-9-]{3,39}`.
  string tag = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListApiDeploymentRevisions.
message ListApiDeploymentRevisionsRequest {
  // Required. The name of the deployment to list revisions for.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiDeployment"
    }
  ];

  // The maximum number of revisions to return per page.
  int32 page_size = 2;

  // The page token, received from a previous ListApiDeploymentRevisions call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3;
}

// Response message for ListApiDeploymentRevisionsResponse.
message ListApiDeploymentRevisionsResponse {
  // The revisions of the deployment.
  repeated ApiDeployment api_deployments = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for RollbackApiDeployment.
message RollbackApiDeploymentRequest {
  // Required. The deployment being rolled back.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiDeployment"
    }
  ];

  // Required. The revision ID to roll back to.
  // It must be a revision of the same deployment.
  //
  //   Example: `c7cfa2a8`
  string revision_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteApiDeploymentRevision.
message DeleteApiDeploymentRevisionRequest {
  // Required. The name of the deployment revision to be deleted,
  // with a revision ID explicitly included.
  //
  // Example:
  // `projects/sample/locations/global/apis/petstore/deployments/prod@c7cfa2a8`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/ApiDeployment"
    }
  ];
}

// Request message for ListArtifacts.
message ListArtifactsRequest {
  // Required. The parent, which owns this collection of artifacts.
  // Format: `{parent}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/Artifact"
    }
  ];

  // The maximum number of artifacts to return.
  // The service may return fewer than this value.
  // If unspecified, at most 50 values will be returned.
  // The maximum is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListArtifacts` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListArtifacts` must
  // match the call that provided the page token.
  string page_token = 3;

  // An expression that can be used to filter the list. Filters use the Common
  // Expression Language and can refer to all message fields except contents.
  string filter = 4;
}

// Response message for ListArtifacts.
message ListArtifactsResponse {
  // The artifacts from the specified publisher.
  repeated Artifact artifacts = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetArtifact.
message GetArtifactRequest {
  // Required. The name of the artifact to retrieve.
  // Format: `{parent}/artifacts/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/Artifact"
    }
  ];
}

// Request message for GetArtifactContents.
message GetArtifactContentsRequest {
  // Required. The name of the artifact whose contents should be retrieved.
  // Format: `{parent}/artifacts/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/Artifact"
    }
  ];
}

// Request message for CreateArtifact.
message CreateArtifactRequest {
  // Required. The parent, which owns this collection of artifacts.
  // Format: `{parent}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apigeeregistry.googleapis.com/Artifact"
    }
  ];

  // Required. The artifact to create.
  Artifact artifact = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the artifact, which will become the final component of
  // the artifact's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  //
  // Following AIP-162, IDs must not have the form of a UUID.
  string artifact_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ReplaceArtifact.
message ReplaceArtifactRequest {
  // Required. The artifact to replace.
  //
  // The `name` field is used to identify the artifact to replace.
  // Format: `{parent}/artifacts/*`
  Artifact artifact = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteArtifact.
message DeleteArtifactRequest {
  // Required. The name of the artifact to delete.
  // Format: `{parent}/artifacts/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/Artifact"
    }
  ];
}
