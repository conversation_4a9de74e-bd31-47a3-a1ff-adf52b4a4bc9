type: google.api.Service
config_version: 3
name: apigeeregistry.googleapis.com
title: Apigee Registry API

apis:
- name: google.cloud.apigeeregistry.v1.Provisioning
- name: google.cloud.apigeeregistry.v1.Registry
- name: google.cloud.location.Locations
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.apigeeregistry.v1.OperationMetadata

documentation:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: 'google.cloud.apigeeregistry.v1.Provisioning.*'
    deadline: 60.0
  - selector: 'google.cloud.apigeeregistry.v1.Registry.*'
    deadline: 60.0
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1/{resource=projects/*/locations/*/apis/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1/{resource=projects/*/locations/*/apis/*/deployments/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/apis/*/versions/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/specs/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/artifacts/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/apis/*/artifacts/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/artifacts/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/specs/*/artifacts/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/instances/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/runtime}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/locations/*/apis/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/apis/*/deployments/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/versions/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/specs/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/artifacts/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/artifacts/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/artifacts/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/specs/*/artifacts/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/instances/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/runtime}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/locations/*/apis/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/apis/*/deployments/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/versions/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/specs/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/artifacts/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/artifacts/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/artifacts/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/apis/*/versions/*/specs/*/artifacts/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/instances/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/runtime}:testIamPermissions'
      body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.apigeeregistry.v1.Provisioning.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.apigeeregistry.v1.Registry.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
