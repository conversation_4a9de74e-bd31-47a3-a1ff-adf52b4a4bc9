# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "apigeeregistry_proto",
    srcs = [
        "provisioning_service.proto",
        "registry_models.proto",
        "registry_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:httpbody_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "apigeeregistry_proto_with_info",
    deps = [
        ":apigeeregistry_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "apigeeregistry_java_proto",
    deps = [":apigeeregistry_proto"],
)

java_grpc_library(
    name = "apigeeregistry_java_grpc",
    srcs = [":apigeeregistry_proto"],
    deps = [":apigeeregistry_java_proto"],
)

java_gapic_library(
    name = "apigeeregistry_java_gapic",
    srcs = [":apigeeregistry_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "apigeeregistry_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apigeeregistry_v1.yaml",
    test_deps = [
        ":apigeeregistry_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":apigeeregistry_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "apigeeregistry_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.apigeeregistry.v1.ProvisioningClientHttpJsonTest",
        "com.google.cloud.apigeeregistry.v1.ProvisioningClientTest",
        "com.google.cloud.apigeeregistry.v1.RegistryClientHttpJsonTest",
        "com.google.cloud.apigeeregistry.v1.RegistryClientTest",
    ],
    runtime_deps = [":apigeeregistry_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-apigeeregistry-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":apigeeregistry_java_gapic",
        ":apigeeregistry_java_grpc",
        ":apigeeregistry_java_proto",
        ":apigeeregistry_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "apigeeregistry_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/apigeeregistry/apiv1/apigeeregistrypb",
    protos = [":apigeeregistry_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "apigeeregistry_go_gapic",
    srcs = [":apigeeregistry_proto_with_info"],
    grpc_service_config = "apigeeregistry_grpc_service_config.json",
    importpath = "cloud.google.com/go/apigeeregistry/apiv1;apigeeregistry",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "apigeeregistry_v1.yaml",
    deps = [
        ":apigeeregistry_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-apigeeregistry-v1-go",
    deps = [
        ":apigeeregistry_go_gapic",
        ":apigeeregistry_go_gapic_srcjar-metadata.srcjar",
        ":apigeeregistry_go_gapic_srcjar-snippets.srcjar",
        ":apigeeregistry_go_gapic_srcjar-test.srcjar",
        ":apigeeregistry_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "apigeeregistry_py_gapic",
    srcs = [":apigeeregistry_proto"],
    grpc_service_config = "apigeeregistry_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-apigee-registry",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=apigee_registry",
    ],
    rest_numeric_enums = True,
    service_yaml = "apigeeregistry_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "apigeeregistry_py_gapic_test",
    srcs = [
        "apigeeregistry_py_gapic_pytest.py",
        "apigeeregistry_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [
        ":apigeeregistry_py_gapic",
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "apigeeregistry-v1-py",
    deps = [
        ":apigeeregistry_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "apigeeregistry_php_proto",
    deps = [":apigeeregistry_proto"],
)

php_gapic_library(
    name = "apigeeregistry_php_gapic",
    srcs = [":apigeeregistry_proto_with_info"],
    grpc_service_config = "apigeeregistry_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "apigeeregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [":apigeeregistry_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-apigeeregistry-v1-php",
    deps = [
        ":apigeeregistry_php_gapic",
        ":apigeeregistry_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "apigeeregistry_nodejs_gapic",
    package_name = "@google-cloud/apigee-registry",
    src = ":apigeeregistry_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "apigeeregistry_grpc_service_config.json",
    package = "google.cloud.apigeeregistry.v1",
    rest_numeric_enums = True,
    service_yaml = "apigeeregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "apigeeregistry-v1-nodejs",
    deps = [
        ":apigeeregistry_nodejs_gapic",
        ":apigeeregistry_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "apigeeregistry_ruby_proto",
    deps = [":apigeeregistry_proto"],
)

ruby_grpc_library(
    name = "apigeeregistry_ruby_grpc",
    srcs = [":apigeeregistry_proto"],
    deps = [":apigeeregistry_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "apigeeregistry_ruby_gapic",
    srcs = [":apigeeregistry_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=apigeeregistry.googleapis.com",
        "ruby-cloud-api-shortname=apigeeregistry",
        "ruby-cloud-gem-name=google-cloud-apigee_registry-v1",
        "ruby-cloud-product-url=https://cloud.google.com/apigee/docs/api-hub/get-started-registry-api/",
    ],
    grpc_service_config = "apigeeregistry_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Apigee Registry API allows teams to upload and share machine-readable descriptions of APIs that are in use and in development. These descriptions include API specifications in standard formats like OpenAPI, the Google API Discovery Service Format, and the Protocol Buffers Language. These API specifications can be used by tools like linters, browsers, documentation generators, test runners, proxies, and API client and server generators. The Registry API itself can be seen as a machine-readable enterprise API catalog designed to back online directories, portals, and workflow managers.",
    ruby_cloud_title = "Apigee Registry V1",
    service_yaml = "apigeeregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apigeeregistry_ruby_grpc",
        ":apigeeregistry_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-apigeeregistry-v1-ruby",
    deps = [
        ":apigeeregistry_ruby_gapic",
        ":apigeeregistry_ruby_grpc",
        ":apigeeregistry_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "apigeeregistry_csharp_proto",
    deps = [":apigeeregistry_proto"],
)

csharp_grpc_library(
    name = "apigeeregistry_csharp_grpc",
    srcs = [":apigeeregistry_proto"],
    deps = [":apigeeregistry_csharp_proto"],
)

csharp_gapic_library(
    name = "apigeeregistry_csharp_gapic",
    srcs = [":apigeeregistry_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "apigeeregistry_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apigeeregistry_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apigeeregistry_csharp_grpc",
        ":apigeeregistry_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-apigeeregistry-v1-csharp",
    deps = [
        ":apigeeregistry_csharp_gapic",
        ":apigeeregistry_csharp_grpc",
        ":apigeeregistry_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "apigeeregistry_cc_proto",
    deps = [":apigeeregistry_proto"],
)

cc_grpc_library(
    name = "apigeeregistry_cc_grpc",
    srcs = [":apigeeregistry_proto"],
    grpc_only = True,
    deps = [":apigeeregistry_cc_proto"],
)
