// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apigeeregistry.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ApigeeRegistry.V1";
option go_package = "cloud.google.com/go/apigeeregistry/apiv1/apigeeregistrypb;apigeeregistrypb";
option java_multiple_files = true;
option java_outer_classname = "ProvisioningServiceProto";
option java_package = "com.google.cloud.apigeeregistry.v1";
option php_namespace = "Google\\Cloud\\ApigeeRegistry\\V1";
option ruby_package = "Google::Cloud::ApigeeRegistry::V1";

// The service that is used for managing the data plane provisioning of the
// Registry.
service Provisioning {
  option (google.api.default_host) = "apigeeregistry.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Provisions instance resources for the Registry.
  rpc CreateInstance(CreateInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes the Registry instance.
  rpc DeleteInstance(DeleteInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single Instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for CreateInstance.
message CreateInstanceRequest {
  // Required. Parent resource of the Instance, of the form: `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Identifier to assign to the Instance. Must be unique within scope of the
  // parent resource.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Instance.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteInstance.
message DeleteInstanceRequest {
  // Required. The name of the Instance to delete.
  // Format: `projects/*/locations/*/instances/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/Instance"
    }
  ];
}

// Request message for GetInstance.
message GetInstanceRequest {
  // Required. The name of the Instance to retrieve.
  // Format: `projects/*/locations/*/instances/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeregistry.googleapis.com/Instance"
    }
  ];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_message = 5;

  // Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool cancellation_requested = 6;

  // API version used to start the operation.
  string api_version = 7;
}

// An Instance represents the instance resources of the Registry.
// Currently, only one instance is allowed for each project.
message Instance {
  option (google.api.resource) = {
    type: "apigeeregistry.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
  };

  // State of the Instance.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The Instance has not been initialized or has been deleted.
    INACTIVE = 1;

    // The Instance is being created.
    CREATING = 2;

    // The Instance has been created and is ready for use.
    ACTIVE = 3;

    // The Instance is being updated.
    UPDATING = 4;

    // The Instance is being deleted.
    DELETING = 5;

    // The Instance encountered an error during a state change.
    FAILED = 6;
  }

  // Available configurations to provision an Instance.
  message Config {
    // Output only. The GCP location where the Instance resides.
    string location = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Required. The Customer Managed Encryption Key (CMEK) used for data encryption.
    // The CMEK name should follow the format of
    // `projects/([^/]+)/locations/([^/]+)/keyRings/([^/]+)/cryptoKeys/([^/]+)`,
    // where the `location` must match InstanceConfig.location.
    string cmek_key_name = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Format: `projects/*/locations/*/instance`.
  // Currently only `locations/global` is supported.
  string name = 1;

  // Output only. Creation timestamp.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the Instance.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Extra information of Instance.State if the state is `FAILED`.
  string state_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Config of the Instance.
  Config config = 6 [(google.api.field_behavior) = REQUIRED];
}
