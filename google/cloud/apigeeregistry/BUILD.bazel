# This build file includes a target for the Ruby wrapper library for
# google-cloud-apigee_registry.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for apigeeregistry.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "apigeeregistry_ruby_wrapper",
    srcs = ["//google/cloud/apigeeregistry/v1:apigeeregistry_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=apigeeregistry.googleapis.com",
        "ruby-cloud-api-shortname=apigeeregistry",
        "ruby-cloud-gem-name=google-cloud-apigee_registry",
        "ruby-cloud-product-url=https://cloud.google.com/apigee/docs/api-hub/get-started-registry-api/",
        "ruby-cloud-wrapper-of=v1:0.7",
    ],
    ruby_cloud_description = "The Apigee Registry API allows teams to upload and share machine-readable descriptions of APIs that are in use and in development. These descriptions include API specifications in standard formats like OpenAPI, the Google API Discovery Service Format, and the Protocol Buffers Language. These API specifications can be used by tools like linters, browsers, documentation generators, test runners, proxies, and API client and server generators. The Registry API itself can be seen as a machine-readable enterprise API catalog designed to back online directories, portals, and workflow managers.",
    ruby_cloud_title = "Apigee Registry",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-apigeeregistry-ruby",
    deps = [
        ":apigeeregistry_ruby_wrapper",
    ],
)
