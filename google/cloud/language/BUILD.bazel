# This build file includes a target for the Ruby wrapper library for
# google-cloud-language.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for language.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "language_ruby_wrapper",
    srcs = ["//google/cloud/language/v1:language_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-language",
        "ruby-cloud-env-prefix=LANGUAGE",
        "ruby-cloud-wrapper-of=v1:0.10;v1beta2:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/natural-language",
        "ruby-cloud-api-id=language.googleapis.com",
        "ruby-cloud-api-shortname=language",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Provides natural language understanding technologies, such as sentiment analysis, entity recognition, entity sentiment analysis, and other text annotations.",
    ruby_cloud_title = "Cloud Natural Language",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-language-ruby",
    deps = [
        ":language_ruby_wrapper",
    ],
)
