type: google.api.Service
config_version: 3
name: language.googleapis.com
title: Cloud Natural Language API

apis:
- name: google.cloud.language.v2.LanguageService

documentation:
  summary: |-
    Provides natural language understanding technologies, such as sentiment
    analysis, entity recognition, entity sentiment analysis, and other text
    annotations, to developers.

authentication:
  rules:
  - selector: 'google.cloud.language.v2.LanguageService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-language,
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=187079&template=1161334
  documentation_uri: https://cloud.google.com/natural-language/docs
  api_short_name: language
  github_label: 'api: language'
  doc_tag_prefix: language
  organization: CLOUD
  library_settings:
  - version: google.cloud.language.v2
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/natural-language/docs/reference/rpc
