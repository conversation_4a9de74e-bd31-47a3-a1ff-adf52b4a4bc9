type: google.api.Service
config_version: 3
name: language.googleapis.com
title: Cloud Natural Language API

apis:
- name: google.cloud.language.v1.LanguageService

documentation:
  summary: |-
    Provides natural language understanding technologies, such as sentiment
    analysis, entity recognition, entity sentiment analysis, and other text
    annotations, to developers.

authentication:
  rules:
  - selector: 'google.cloud.language.v1.LanguageService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-language,
        https://www.googleapis.com/auth/cloud-platform

publishing:
  documentation_uri: https://cloud.google.com/natural-language/docs
  github_label: 'api: language'
  organization: CLOUD
  library_settings:
  - version: google.cloud.language.v1
    launch_stage: GA
    dotnet_settings:
      handwritten_signatures:
      - LanguageService.AnalyzeEntities(document)
      - LanguageService.AnalyzeSyntax(document)
      - LanguageService.AnnotateText(document,features)
      - LanguageService.AnalyzeEntitySentiment(document)
  proto_reference_documentation_uri: https://cloud.google.com/natural-language/docs/reference/rpc
