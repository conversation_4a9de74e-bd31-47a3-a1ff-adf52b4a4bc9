type: google.api.Service
config_version: 3
name: language.googleapis.com
title: Cloud Natural Language API

apis:
- name: google.cloud.language.v1beta2.LanguageService

documentation:
  summary: |-
    Provides natural language understanding technologies, such as sentiment
    analysis, entity recognition, entity sentiment analysis, and other text
    annotations, to developers.

authentication:
  rules:
  - selector: 'google.cloud.language.v1beta2.LanguageService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-language,
        https://www.googleapis.com/auth/cloud-platform
