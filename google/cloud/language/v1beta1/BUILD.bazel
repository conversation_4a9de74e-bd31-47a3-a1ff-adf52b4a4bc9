# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "language_proto",
    srcs = [
        "language_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "language_java_proto",
    deps = [":language_proto"],
)

java_grpc_library(
    name = "language_java_grpc",
    srcs = [":language_proto"],
    deps = [":language_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "language_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/language/apiv1beta1/languagepb",
    protos = [":language_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "language_moved_proto",
    srcs = [":language_proto"],
    deps = [
        "//google/api:annotations_proto",
    ],
)

py_proto_library(
    name = "language_py_proto",
    deps = [":language_moved_proto"],
)

py_grpc_library(
    name = "language_py_grpc",
    srcs = [":language_moved_proto"],
    deps = [":language_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "language_php_proto",
    deps = [":language_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "language_ruby_proto",
    deps = [":language_proto"],
)

ruby_grpc_library(
    name = "language_ruby_grpc",
    srcs = [":language_proto"],
    deps = [":language_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "language_csharp_proto",
    deps = [":language_proto"],
)

csharp_grpc_library(
    name = "language_csharp_grpc",
    srcs = [":language_proto"],
    deps = [":language_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
