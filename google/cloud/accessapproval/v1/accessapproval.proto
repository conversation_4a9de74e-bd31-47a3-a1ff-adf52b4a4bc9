// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.accessapproval.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AccessApproval.V1";
option go_package = "cloud.google.com/go/accessapproval/apiv1/accessapprovalpb;accessapprovalpb";
option java_multiple_files = true;
option java_outer_classname = "AccessApprovalProto";
option java_package = "com.google.cloud.accessapproval.v1";
option php_namespace = "Google\\Cloud\\AccessApproval\\V1";
option ruby_package = "Google::Cloud::AccessApproval::V1";

// This API allows a customer to manage accesses to cloud resources by
// Google personnel. It defines the following resource model:
//
// - The API has a collection of
//   [ApprovalRequest][google.cloud.accessapproval.v1.ApprovalRequest]
//   resources, named `approvalRequests/{approval_request}`
// - The API has top-level settings per Project/Folder/Organization, named
//   `accessApprovalSettings`
//
// The service also periodically emails a list of recipients, defined at the
// Project/Folder/Organization level in the accessApprovalSettings, when there
// is a pending ApprovalRequest for them to act on. The ApprovalRequests can
// also optionally be published to a Pub/Sub topic owned by the customer
// (contact support if you would like to enable Pub/Sub notifications).
//
// ApprovalRequests can be approved or dismissed. Google personnel can only
// access the indicated resource or resources if the request is approved
// (subject to some exclusions:
// https://cloud.google.com/access-approval/docs/overview#exclusions).
//
// Note: Using Access Approval functionality will mean that Google may not be
// able to meet the SLAs for your chosen products, as any support response times
// may be dramatically increased. As such the SLAs do not apply to any service
// disruption to the extent impacted by Customer's use of Access Approval. Do
// not enable Access Approval for projects where you may require high service
// availability and rapid response by Google Cloud Support.
//
// After a request is approved or dismissed, no further action may be taken on
// it. Requests with the requested_expiration in the past or with no activity
// for 14 days are considered dismissed. When an approval expires, the request
// is considered dismissed.
//
// If a request is not approved or dismissed, we call it pending.
service AccessApproval {
  option (google.api.default_host) = "accessapproval.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists approval requests associated with a project, folder, or organization.
  // Approval requests can be filtered by state (pending, active, dismissed).
  // The order is reverse chronological.
  rpc ListApprovalRequests(ListApprovalRequestsMessage) returns (ListApprovalRequestsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/approvalRequests"
      additional_bindings {
        get: "/v1/{parent=folders/*}/approvalRequests"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*}/approvalRequests"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets an approval request. Returns NOT_FOUND if the request does not exist.
  rpc GetApprovalRequest(GetApprovalRequestMessage) returns (ApprovalRequest) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/approvalRequests/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/approvalRequests/*}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/approvalRequests/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Approves a request and returns the updated ApprovalRequest.
  //
  // Returns NOT_FOUND if the request does not exist. Returns
  // FAILED_PRECONDITION if the request exists but is not in a pending state.
  rpc ApproveApprovalRequest(ApproveApprovalRequestMessage) returns (ApprovalRequest) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/approvalRequests/*}:approve"
      body: "*"
      additional_bindings {
        post: "/v1/{name=folders/*/approvalRequests/*}:approve"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{name=organizations/*/approvalRequests/*}:approve"
        body: "*"
      }
    };
  }

  // Dismisses a request. Returns the updated ApprovalRequest.
  //
  // NOTE: This does not deny access to the resource if another request has been
  // made and approved. It is equivalent in effect to ignoring the request
  // altogether.
  //
  // Returns NOT_FOUND if the request does not exist.
  //
  // Returns FAILED_PRECONDITION if the request exists but is not in a pending
  // state.
  rpc DismissApprovalRequest(DismissApprovalRequestMessage) returns (ApprovalRequest) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/approvalRequests/*}:dismiss"
      body: "*"
      additional_bindings {
        post: "/v1/{name=folders/*/approvalRequests/*}:dismiss"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{name=organizations/*/approvalRequests/*}:dismiss"
        body: "*"
      }
    };
  }

  // Invalidates an existing ApprovalRequest. Returns the updated
  // ApprovalRequest.
  //
  // NOTE: This does not deny access to the resource if another request has been
  // made and approved. It only invalidates a single approval.
  //
  // Returns FAILED_PRECONDITION if the request exists but is not in an approved
  // state.
  rpc InvalidateApprovalRequest(InvalidateApprovalRequestMessage) returns (ApprovalRequest) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/approvalRequests/*}:invalidate"
      body: "*"
      additional_bindings {
        post: "/v1/{name=folders/*/approvalRequests/*}:invalidate"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{name=organizations/*/approvalRequests/*}:invalidate"
        body: "*"
      }
    };
  }

  // Gets the settings associated with a project, folder, or organization.
  rpc GetAccessApprovalSettings(GetAccessApprovalSettingsMessage) returns (AccessApprovalSettings) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/accessApprovalSettings}"
      additional_bindings {
        get: "/v1/{name=folders/*/accessApprovalSettings}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/accessApprovalSettings}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the settings associated with a project, folder, or organization.
  // Settings to update are determined by the value of field_mask.
  rpc UpdateAccessApprovalSettings(UpdateAccessApprovalSettingsMessage) returns (AccessApprovalSettings) {
    option (google.api.http) = {
      patch: "/v1/{settings.name=projects/*/accessApprovalSettings}"
      body: "settings"
      additional_bindings {
        patch: "/v1/{settings.name=folders/*/accessApprovalSettings}"
        body: "settings"
      }
      additional_bindings {
        patch: "/v1/{settings.name=organizations/*/accessApprovalSettings}"
        body: "settings"
      }
    };
    option (google.api.method_signature) = "settings,update_mask";
  }

  // Deletes the settings associated with a project, folder, or organization.
  // This will have the effect of disabling Access Approval for the project,
  // folder, or organization, but only if all ancestors also have Access
  // Approval disabled. If Access Approval is enabled at a higher level of the
  // hierarchy, then Access Approval will still be enabled at this level as
  // the settings are inherited.
  rpc DeleteAccessApprovalSettings(DeleteAccessApprovalSettingsMessage) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/accessApprovalSettings}"
      additional_bindings {
        delete: "/v1/{name=folders/*/accessApprovalSettings}"
      }
      additional_bindings {
        delete: "/v1/{name=organizations/*/accessApprovalSettings}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves the service account that is used by Access Approval to access KMS
  // keys for signing approved approval requests.
  rpc GetAccessApprovalServiceAccount(GetAccessApprovalServiceAccountMessage) returns (AccessApprovalServiceAccount) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/serviceAccount}"
      additional_bindings {
        get: "/v1/{name=folders/*/serviceAccount}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/serviceAccount}"
      }
    };
    option (google.api.method_signature) = "name";
  }
}

// Home office and physical location of the principal.
message AccessLocations {
  // The "home office" location of the principal. A two-letter country code
  // (ISO 3166-1 alpha-2), such as "US", "DE" or "GB" or a region code. In some
  // limited situations Google systems may refer refer to a region code instead
  // of a country code.
  // Possible Region Codes:
  //
  //   * ASI: Asia
  //   * EUR: Europe
  //   * OCE: Oceania
  //   * AFR: Africa
  //   * NAM: North America
  //   * SAM: South America
  //   * ANT: Antarctica
  //   * ANY: Any location
  string principal_office_country = 1;

  // Physical location of the principal at the time of the access. A
  // two-letter country code (ISO 3166-1 alpha-2), such as "US", "DE" or "GB" or
  // a region code. In some limited situations Google systems may refer refer to
  // a region code instead of a country code.
  // Possible Region Codes:
  //
  //   * ASI: Asia
  //   * EUR: Europe
  //   * OCE: Oceania
  //   * AFR: Africa
  //   * NAM: North America
  //   * SAM: South America
  //   * ANT: Antarctica
  //   * ANY: Any location
  string principal_physical_location_country = 2;
}

message AccessReason {
  // Type of access justification.
  enum Type {
    // Default value for proto, shouldn't be used.
    TYPE_UNSPECIFIED = 0;

    // Customer made a request or raised an issue that required the principal to
    // access customer data. `detail` is of the form ("#####" is the issue ID):
    //
    //   * "Feedback Report: #####"
    //   * "Case Number: #####"
    //   * "Case ID: #####"
    //   * "E-PIN Reference: #####"
    //   * "Google-#####"
    //   * "T-#####"
    CUSTOMER_INITIATED_SUPPORT = 1;

    // The principal accessed customer data in order to diagnose or resolve a
    // suspected issue in services. Often this access is used to confirm that
    // customers are not affected by a suspected service issue or to remediate a
    // reversible system issue.
    GOOGLE_INITIATED_SERVICE = 2;

    // Google initiated service for security, fraud, abuse, or compliance
    // purposes.
    GOOGLE_INITIATED_REVIEW = 3;

    // The principal was compelled to access customer data in order to respond
    // to a legal third party data request or process, including legal processes
    // from customers themselves.
    THIRD_PARTY_DATA_REQUEST = 4;

    // The principal accessed customer data in order to diagnose or resolve a
    // suspected issue in services or a known outage.
    GOOGLE_RESPONSE_TO_PRODUCTION_ALERT = 5;
  }

  // Type of access justification.
  Type type = 1;

  // More detail about certain reason types. See comments for each type above.
  string detail = 2;
}

// Information about the digital signature of the resource.
message SignatureInfo {
  // The digital signature.
  bytes signature = 1;

  // How this signature may be verified.
  oneof verification_info {
    // The public key for the Google default signing, encoded in PEM format. The
    // signature was created using a private key which may be verified using
    // this public key.
    string google_public_key_pem = 2;

    // The resource name of the customer CryptoKeyVersion used for signing.
    string customer_kms_key_version = 3;
  }
}

// A decision that has been made to approve access to a resource.
message ApproveDecision {
  // The time at which approval was granted.
  google.protobuf.Timestamp approve_time = 1;

  // The time at which the approval expires.
  google.protobuf.Timestamp expire_time = 2;

  // If set, denotes the timestamp at which the approval is invalidated.
  google.protobuf.Timestamp invalidate_time = 3;

  // The signature for the ApprovalRequest and details on how it was signed.
  SignatureInfo signature_info = 4;

  // True when the request has been auto-approved.
  bool auto_approved = 5;
}

// A decision that has been made to dismiss an approval request.
message DismissDecision {
  // The time at which the approval request was dismissed.
  google.protobuf.Timestamp dismiss_time = 1;

  // This field will be true if the ApprovalRequest was implicitly dismissed due
  // to inaction by the access approval approvers (the request is not acted
  // on by the approvers before the exiration time).
  bool implicit = 2;
}

// The properties associated with the resource of the request.
message ResourceProperties {
  // Whether an approval will exclude the descendants of the resource being
  // requested.
  bool excludes_descendants = 1;
}

// A request for the customer to approve access to a resource.
message ApprovalRequest {
  option (google.api.resource) = {
    type: "accessapproval.googleapis.com/ApprovalRequest"
    pattern: "projects/{project}/approvalRequests/{approval_request}"
    pattern: "folders/{folder}/approvalRequests/{approval_request}"
    pattern: "organizations/{organization}/approvalRequests/{approval_request}"
  };

  // The resource name of the request. Format is
  // "{projects|folders|organizations}/{id}/approvalRequests/{approval_request}".
  string name = 1;

  // The resource for which approval is being requested. The format of the
  // resource name is defined at
  // https://cloud.google.com/apis/design/resource_names. The resource name here
  // may either be a "full" resource name (e.g.
  // "//library.googleapis.com/shelves/shelf1/books/book2") or a "relative"
  // resource name (e.g. "shelves/shelf1/books/book2") as described in the
  // resource name specification.
  string requested_resource_name = 2;

  // Properties related to the resource represented by requested_resource_name.
  ResourceProperties requested_resource_properties = 9;

  // The justification for which approval is being requested.
  AccessReason requested_reason = 3;

  // The locations for which approval is being requested.
  AccessLocations requested_locations = 4;

  // The time at which approval was requested.
  google.protobuf.Timestamp request_time = 5;

  // The requested expiration for the approval. If the request is approved,
  // access will be granted from the time of approval until the expiration time.
  google.protobuf.Timestamp requested_expiration = 6;

  // The current decision on the approval request.
  oneof decision {
    // Access was approved.
    ApproveDecision approve = 7;

    // The request was dismissed.
    DismissDecision dismiss = 8;
  }
}

// Represents the type of enrollment for a given service to Access Approval.
enum EnrollmentLevel {
  // Default value for proto, shouldn't be used.
  ENROLLMENT_LEVEL_UNSPECIFIED = 0;

  // Service is enrolled in Access Approval for all requests
  BLOCK_ALL = 1;
}

// Represents the enrollment of a cloud resource into a specific service.
message EnrolledService {
  // The product for which Access Approval will be enrolled. Allowed values are
  // listed below (case-sensitive):
  //
  //   * all
  //   * GA
  //   * App Engine
  //   * BigQuery
  //   * Cloud Bigtable
  //   * Cloud Key Management Service
  //   * Compute Engine
  //   * Cloud Dataflow
  //   * Cloud Dataproc
  //   * Cloud DLP
  //   * Cloud EKM
  //   * Cloud HSM
  //   * Cloud Identity and Access Management
  //   * Cloud Logging
  //   * Cloud Pub/Sub
  //   * Cloud Spanner
  //   * Cloud SQL
  //   * Cloud Storage
  //   * Google Kubernetes Engine
  //   * Organization Policy Serivice
  //   * Persistent Disk
  //   * Resource Manager
  //   * Secret Manager
  //   * Speaker ID
  //
  // Note: These values are supported as input for legacy purposes, but will not
  // be returned from the API.
  //
  //   * all
  //   * ga-only
  //   * appengine.googleapis.com
  //   * bigquery.googleapis.com
  //   * bigtable.googleapis.com
  //   * container.googleapis.com
  //   * cloudkms.googleapis.com
  //   * cloudresourcemanager.googleapis.com
  //   * cloudsql.googleapis.com
  //   * compute.googleapis.com
  //   * dataflow.googleapis.com
  //   * dataproc.googleapis.com
  //   * dlp.googleapis.com
  //   * iam.googleapis.com
  //   * logging.googleapis.com
  //   * orgpolicy.googleapis.com
  //   * pubsub.googleapis.com
  //   * spanner.googleapis.com
  //   * secretmanager.googleapis.com
  //   * speakerid.googleapis.com
  //   * storage.googleapis.com
  //
  // Calls to UpdateAccessApprovalSettings using 'all' or any of the
  // XXX.googleapis.com will be translated to the associated product name
  // ('all', 'App Engine', etc.).
  //
  // Note: 'all' will enroll the resource in all products supported at both 'GA'
  // and 'Preview' levels.
  //
  // More information about levels of support is available at
  // https://cloud.google.com/access-approval/docs/supported-services
  string cloud_product = 1;

  // The enrollment level of the service.
  EnrollmentLevel enrollment_level = 2;
}

// Settings on a Project/Folder/Organization related to Access Approval.
message AccessApprovalSettings {
  option (google.api.resource) = {
    type: "accessapproval.googleapis.com/AccessApprovalSettings"
    pattern: "projects/{project}/accessApprovalSettings"
    pattern: "folders/{folder}/accessApprovalSettings"
    pattern: "organizations/{organization}/accessApprovalSettings"
  };

  // The resource name of the settings. Format is one of:
  //
  //   * "projects/{project}/accessApprovalSettings"
  //   * "folders/{folder}/accessApprovalSettings"
  //   * "organizations/{organization}/accessApprovalSettings"
  string name = 1 [(google.api.resource_reference) = {
                     type: "accessapproval.googleapis.com/AccessApprovalSettings"
                   }];

  // A list of email addresses to which notifications relating to approval
  // requests should be sent. Notifications relating to a resource will be sent
  // to all emails in the settings of ancestor resources of that resource. A
  // maximum of 50 email addresses are allowed.
  repeated string notification_emails = 2;

  // A list of Google Cloud Services for which the given resource has Access
  // Approval enrolled. Access requests for the resource given by name against
  // any of these services contained here will be required to have explicit
  // approval. If name refers to an organization, enrollment can be done for
  // individual services. If name refers to a folder or project, enrollment can
  // only be done on an all or nothing basis.
  //
  // If a cloud_product is repeated in this list, the first entry will be
  // honored and all following entries will be discarded. A maximum of 10
  // enrolled services will be enforced, to be expanded as the set of supported
  // services is expanded.
  repeated EnrolledService enrolled_services = 3;

  // Output only. This field is read only (not settable via
  // UpdateAccessApprovalSettings method). If the field is true, that
  // indicates that at least one service is enrolled for Access Approval in one
  // or more ancestors of the Project or Folder (this field will always be
  // unset for the organization since organizations do not have ancestors).
  bool enrolled_ancestor = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The asymmetric crypto key version to use for signing approval requests.
  // Empty active_key_version indicates that a Google-managed key should be used
  // for signing. This property will be ignored if set by an ancestor of this
  // resource, and new non-empty values may not be set.
  string active_key_version = 6;

  // Output only. This field is read only (not settable via UpdateAccessApprovalSettings
  // method). If the field is true, that indicates that an ancestor of this
  // Project or Folder has set active_key_version (this field will always be
  // unset for the organization since organizations do not have ancestors).
  bool ancestor_has_active_key_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field is read only (not settable via UpdateAccessApprovalSettings
  // method). If the field is true, that indicates that there is some
  // configuration issue with the active_key_version configured at this level in
  // the resource hierarchy (e.g. it doesn't exist or the Access Approval
  // service account doesn't have the correct permissions on it, etc.) This key
  // version is not necessarily the effective key version at this level, as key
  // versions are inherited top-down.
  bool invalid_key_version = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Access Approval service account related to a project/folder/organization.
message AccessApprovalServiceAccount {
  option (google.api.resource) = {
    type: "accessapproval.googleapis.com/AccessApprovalServiceAccount"
    pattern: "projects/{project}/serviceAccount"
    pattern: "folders/{folder}/serviceAccount"
    pattern: "organizations/{organization}/serviceAccount"
  };

  // The resource name of the Access Approval service account. Format is one of:
  //
  //   * "projects/{project}/serviceAccount"
  //   * "folders/{folder}/serviceAccount"
  //   * "organizations/{organization}/serviceAccount"
  string name = 1 [(google.api.resource_reference) = {
                     type: "accessapproval.googleapis.com/AccessApprovalServiceAccount"
                   }];

  // Email address of the service account.
  string account_email = 2;
}

// Request to list approval requests.
message ListApprovalRequestsMessage {
  // The parent resource. This may be "projects/{project}",
  // "folders/{folder}", or "organizations/{organization}".
  string parent = 1 [(google.api.resource_reference) = {
                       child_type: "accessapproval.googleapis.com/ApprovalRequest"
                     }];

  // A filter on the type of approval requests to retrieve. Must be one of the
  // following values:
  //
  //   * [not set]: Requests that are pending or have active approvals.
  //   * ALL: All requests.
  //   * PENDING: Only pending requests.
  //   * ACTIVE: Only active (i.e. currently approved) requests.
  //   * DISMISSED: Only requests that have been dismissed, or requests that
  //     are not approved and past expiration.
  //   * EXPIRED: Only requests that have been approved, and the approval has
  //     expired.
  //   * HISTORY: Active, dismissed and expired requests.
  string filter = 2;

  // Requested page size.
  int32 page_size = 3;

  // A token identifying the page of results to return.
  string page_token = 4;
}

// Response to listing of ApprovalRequest objects.
message ListApprovalRequestsResponse {
  // Approval request details.
  repeated ApprovalRequest approval_requests = 1;

  // Token to retrieve the next page of results, or empty if there are no more.
  string next_page_token = 2;
}

// Request to get an approval request.
message GetApprovalRequestMessage {
  // The name of the approval request to retrieve.
  // Format:
  // "{projects|folders|organizations}/{id}/approvalRequests/{approval_request}"
  string name = 1 [(google.api.resource_reference) = {
                     type: "accessapproval.googleapis.com/ApprovalRequest"
                   }];
}

// Request to approve an ApprovalRequest.
message ApproveApprovalRequestMessage {
  // Name of the approval request to approve.
  string name = 1 [(google.api.resource_reference) = {
                     type: "accessapproval.googleapis.com/ApprovalRequest"
                   }];

  // The expiration time of this approval.
  google.protobuf.Timestamp expire_time = 2;
}

// Request to dismiss an approval request.
message DismissApprovalRequestMessage {
  // Name of the ApprovalRequest to dismiss.
  string name = 1 [(google.api.resource_reference) = {
                     type: "accessapproval.googleapis.com/ApprovalRequest"
                   }];
}

// Request to invalidate an existing approval.
message InvalidateApprovalRequestMessage {
  // Name of the ApprovalRequest to invalidate.
  string name = 1 [(google.api.resource_reference) = {
                     type: "accessapproval.googleapis.com/ApprovalRequest"
                   }];
}

// Request to get access approval settings.
message GetAccessApprovalSettingsMessage {
  // The name of the AccessApprovalSettings to retrieve.
  // Format: "{projects|folders|organizations}/{id}/accessApprovalSettings"
  string name = 1 [(google.api.resource_reference) = {
                     type: "accessapproval.googleapis.com/AccessApprovalSettings"
                   }];
}

// Request to update access approval settings.
message UpdateAccessApprovalSettingsMessage {
  // The new AccessApprovalSettings.
  AccessApprovalSettings settings = 1;

  // The update mask applies to the settings. Only the top level fields of
  // AccessApprovalSettings (notification_emails & enrolled_services) are
  // supported. For each field, if it is included, the currently stored value
  // will be entirely overwritten with the value of the field passed in this
  // request.
  //
  // For the `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  // If this field is left unset, only the notification_emails field will be
  // updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request to delete access approval settings.
message DeleteAccessApprovalSettingsMessage {
  // Name of the AccessApprovalSettings to delete.
  string name = 1 [(google.api.resource_reference) = {
                     type: "accessapproval.googleapis.com/AccessApprovalSettings"
                   }];
}

// Request to get an Access Approval service account.
message GetAccessApprovalServiceAccountMessage {
  // Name of the AccessApprovalServiceAccount to retrieve.
  string name = 1;
}
