# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "accessapproval_proto",
    srcs = [
        "accessapproval.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "accessapproval_proto_with_info",
    deps = [
        ":accessapproval_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "accessapproval_java_proto",
    deps = [":accessapproval_proto"],
)

java_grpc_library(
    name = "accessapproval_java_grpc",
    srcs = [":accessapproval_proto"],
    deps = [":accessapproval_java_proto"],
)

java_gapic_library(
    name = "accessapproval_java_gapic",
    srcs = [":accessapproval_proto_with_info"],
    gapic_yaml = "accessapproval_gapic.yaml",
    grpc_service_config = "accessapproval_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "accessapproval_v1.yaml",
    test_deps = [
        ":accessapproval_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":accessapproval_java_proto",
    ],
)

java_gapic_test(
    name = "accessapproval_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.accessapproval.v1.AccessApprovalAdminClientHttpJsonTest",
        "com.google.cloud.accessapproval.v1.AccessApprovalAdminClientTest",
    ],
    runtime_deps = [":accessapproval_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-accessapproval-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":accessapproval_java_gapic",
        ":accessapproval_java_grpc",
        ":accessapproval_java_proto",
        ":accessapproval_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "accessapproval_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/accessapproval/apiv1/accessapprovalpb",
    protos = [":accessapproval_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "accessapproval_go_gapic",
    srcs = [":accessapproval_proto_with_info"],
    grpc_service_config = "accessapproval_grpc_service_config.json",
    importpath = "cloud.google.com/go/accessapproval/apiv1;accessapproval",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "accessapproval_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":accessapproval_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-accessapproval-v1-go",
    deps = [
        ":accessapproval_go_gapic",
        ":accessapproval_go_gapic_srcjar-snippets.srcjar",
        ":accessapproval_go_gapic_srcjar-test.srcjar",
        ":accessapproval_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "accessapproval_py_gapic",
    srcs = [":accessapproval_proto"],
    grpc_service_config = "accessapproval_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-access-approval",
    ],
    rest_numeric_enums = True,
    service_yaml = "accessapproval_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "accessapproval_py_gapic_test",
    srcs = [
        "accessapproval_py_gapic_pytest.py",
        "accessapproval_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":accessapproval_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "accessapproval-v1-py",
    deps = [
        ":accessapproval_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "accessapproval_php_proto",
    deps = [":accessapproval_proto"],
)

php_gapic_library(
    name = "accessapproval_php_gapic",
    srcs = [":accessapproval_proto_with_info"],
    grpc_service_config = "accessapproval_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "accessapproval_v1.yaml",
    transport = "grpc+rest",
    deps = [":accessapproval_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-accessapproval-v1-php",
    deps = [
        ":accessapproval_php_gapic",
        ":accessapproval_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "accessapproval_nodejs_gapic",
    package_name = "@google-cloud/access-approval",
    src = ":accessapproval_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "accessapproval_grpc_service_config.json",
    package = "google.cloud.accessapproval.v1",
    rest_numeric_enums = True,
    service_yaml = "accessapproval_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "accessapproval-v1-nodejs",
    deps = [
        ":accessapproval_nodejs_gapic",
        ":accessapproval_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "accessapproval_ruby_proto",
    deps = [":accessapproval_proto"],
)

ruby_grpc_library(
    name = "accessapproval_ruby_grpc",
    srcs = [":accessapproval_proto"],
    deps = [":accessapproval_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "accessapproval_ruby_gapic",
    srcs = [":accessapproval_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-access_approval-v1",
        "ruby-cloud-env-prefix=ACCESS_APPROVAL",
        "ruby-cloud-product-url=https://cloud.google.com/access-approval/",
        "ruby-cloud-api-id=accessapproval.googleapis.com",
        "ruby-cloud-api-shortname=accessapproval",
    ],
    grpc_service_config = "accessapproval_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "An API for controlling access to data by Google personnel.",
    ruby_cloud_title = "Access Approval V1",
    service_yaml = "accessapproval_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":accessapproval_ruby_grpc",
        ":accessapproval_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-accessapproval-v1-ruby",
    deps = [
        ":accessapproval_ruby_gapic",
        ":accessapproval_ruby_grpc",
        ":accessapproval_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "accessapproval_csharp_proto",
    deps = [":accessapproval_proto"],
)

csharp_grpc_library(
    name = "accessapproval_csharp_grpc",
    srcs = [":accessapproval_proto"],
    deps = [":accessapproval_csharp_proto"],
)

csharp_gapic_library(
    name = "accessapproval_csharp_gapic",
    srcs = [":accessapproval_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "accessapproval_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "accessapproval_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":accessapproval_csharp_grpc",
        ":accessapproval_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-accessapproval-v1-csharp",
    deps = [
        ":accessapproval_csharp_gapic",
        ":accessapproval_csharp_grpc",
        ":accessapproval_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "accessapproval_cc_proto",
    deps = [":accessapproval_proto"],
)

cc_grpc_library(
    name = "accessapproval_cc_grpc",
    srcs = [":accessapproval_proto"],
    grpc_only = True,
    deps = [":accessapproval_cc_proto"],
)
