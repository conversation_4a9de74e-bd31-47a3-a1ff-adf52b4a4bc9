# This build file includes a target for the Ruby wrapper library for
# google-cloud-access_approval.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for accessapproval.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "accessapproval_ruby_wrapper",
    srcs = ["//google/cloud/accessapproval/v1:accessapproval_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-access_approval",
        "ruby-cloud-env-prefix=ACCESS_APPROVAL",
        "ruby-cloud-wrapper-of=v1:0.11",
        "ruby-cloud-product-url=https://cloud.google.com/access-approval/",
        "ruby-cloud-api-id=accessapproval.googleapis.com",
        "ruby-cloud-api-shortname=accessapproval",
    ],
    ruby_cloud_description = "An API for controlling access to data by Google personnel.",
    ruby_cloud_title = "Access Approval",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-accessapproval-ruby",
    deps = [
        ":accessapproval_ruby_wrapper",
    ],
)
