// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securesourcemanager.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecureSourceManager.V1";
option go_package = "cloud.google.com/go/securesourcemanager/apiv1/securesourcemanagerpb;securesourcemanagerpb";
option java_multiple_files = true;
option java_outer_classname = "SecureSourceManagerProto";
option java_package = "com.google.cloud.securesourcemanager.v1";
option php_namespace = "Google\\Cloud\\SecureSourceManager\\V1";
option ruby_package = "Google::Cloud::SecureSourceManager::V1";
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};
option (google.api.resource_definition) = {
  type: "privateca.googleapis.com/CaPool"
  pattern: "projects/{project}/locations/{location}/caPools/{ca_pool}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/ServiceAttachment"
  pattern: "projects/{project}/regions/{region}/serviceAttachments/{service_attachment}"
};

// Secure Source Manager API
//
// Access Secure Source Manager instances, resources, and repositories.
//
// This API is split across two servers: the Control Plane and the Data Plane.
//
// Data Plane endpoints are hosted directly by your Secure Source Manager
// instance, so you must connect to your instance's API hostname to access
// them. The API hostname looks like the following:
//
//    https://[instance-id]-[project-number]-api.[location].sourcemanager.dev
//
// For example,
//
//    https://my-instance-702770452863-api.us-central1.sourcemanager.dev
//
// Data Plane endpoints are denoted with **Host: Data Plane**.
//
// All other endpoints are found in the normal Cloud API location, namely,
// `securcesourcemanager.googleapis.com`.
service SecureSourceManager {
  option (google.api.default_host) = "securesourcemanager.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Instances in a given project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new instance in a given project and location.
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single instance.
  rpc DeleteInstance(DeleteInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Repositories in a given project and location.
  //
  // **Host: Data Plane**
  rpc ListRepositories(ListRepositoriesRequest)
      returns (ListRepositoriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/repositories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets metadata of a repository.
  //
  // **Host: Data Plane**
  rpc GetRepository(GetRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new repository in a given project and location.
  //
  // **Host: Data Plane**
  rpc CreateRepository(CreateRepositoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/repositories"
      body: "repository"
    };
    option (google.api.method_signature) = "parent,repository,repository_id";
    option (google.longrunning.operation_info) = {
      response_type: "Repository"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a Repository.
  //
  // **Host: Data Plane**
  rpc DeleteRepository(DeleteRepositoryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Get IAM policy for a repository.
  rpc GetIamPolicyRepo(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      get: "/v1/{resource=projects/*/locations/*/repositories/*}:getIamPolicy"
    };
    option (google.api.method_signature) = "resource";
  }

  // Set IAM policy on a repository.
  rpc SetIamPolicyRepo(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/repositories/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Test IAM permissions on a repository.
  // IAM permission checks are not required on this method.
  rpc TestIamPermissionsRepo(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/repositories/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // CreateBranchRule creates a branch rule in a given repository.
  rpc CreateBranchRule(CreateBranchRuleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/repositories/*}/branchRules"
      body: "branch_rule"
    };
    option (google.api.method_signature) = "parent,branch_rule,branch_rule_id";
    option (google.longrunning.operation_info) = {
      response_type: "BranchRule"
      metadata_type: "OperationMetadata"
    };
  }

  // ListBranchRules lists branch rules in a given repository.
  rpc ListBranchRules(ListBranchRulesRequest)
      returns (ListBranchRulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/repositories/*}/branchRules"
    };
    option (google.api.method_signature) = "parent";
  }

  // GetBranchRule gets a branch rule.
  rpc GetBranchRule(GetBranchRuleRequest) returns (BranchRule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/repositories/*/branchRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // UpdateBranchRule updates a branch rule.
  rpc UpdateBranchRule(UpdateBranchRuleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{branch_rule.name=projects/*/locations/*/repositories/*/branchRules/*}"
      body: "branch_rule"
    };
    option (google.api.method_signature) = "branch_rule,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "BranchRule"
      metadata_type: "OperationMetadata"
    };
  }

  // DeleteBranchRule deletes a branch rule.
  rpc DeleteBranchRule(DeleteBranchRuleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/repositories/*/branchRules/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// A resource that represents a Secure Source Manager instance.
message Instance {
  option (google.api.resource) = {
    type: "securesourcemanager.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
    style: DECLARATIVE_FRIENDLY
  };

  // Secure Source Manager instance state.
  enum State {
    // Not set. This should only be the case for incoming requests.
    STATE_UNSPECIFIED = 0;

    // Instance is being created.
    CREATING = 1;

    // Instance is ready.
    ACTIVE = 2;

    // Instance is being deleted.
    DELETING = 3;

    // Instance is paused.
    PAUSED = 4;

    // Instance is unknown, we are not sure if it's functioning.
    UNKNOWN = 6;
  }

  // Provides information about the current instance state.
  enum StateNote {
    // STATE_NOTE_UNSPECIFIED as the first value of State.
    STATE_NOTE_UNSPECIFIED = 0;

    // CMEK access is unavailable.
    PAUSED_CMEK_UNAVAILABLE = 1;

    // INSTANCE_RESUMING indicates that the instance was previously paused
    // and is under the process of being brought back.
    INSTANCE_RESUMING = 2 [deprecated = true];
  }

  // HostConfig has different instance endpoints.
  message HostConfig {
    // Output only. HTML hostname.
    string html = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. API hostname. This is the hostname to use for **Host: Data
    // Plane** endpoints.
    string api = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Git HTTP hostname.
    string git_http = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Git SSH hostname.
    string git_ssh = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // PrivateConfig includes settings for private instance.
  message PrivateConfig {
    // Required. Immutable. Indicate if it's private instance.
    bool is_private = 1 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.field_behavior) = REQUIRED
    ];

    // Required. Immutable. CA pool resource, resource must in the format of
    // `projects/{project}/locations/{location}/caPools/{ca_pool}`.
    string ca_pool = 2 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "privateca.googleapis.com/CaPool"
      }
    ];

    // Output only. Service Attachment for HTTP, resource is in the format of
    // `projects/{project}/regions/{region}/serviceAttachments/{service_attachment}`.
    string http_service_attachment = 3 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/ServiceAttachment"
      }
    ];

    // Output only. Service Attachment for SSH, resource is in the format of
    // `projects/{project}/regions/{region}/serviceAttachments/{service_attachment}`.
    string ssh_service_attachment = 4 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/ServiceAttachment"
      }
    ];

    // Optional. Additional allowed projects for setting up PSC connections.
    // Instance host project is automatically allowed and does not need to be
    // included in this list.
    repeated string psc_allowed_projects = 6
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. A unique identifier for an instance. The name should be of the
  // format:
  // `projects/{project_number}/locations/{location_id}/instances/{instance_id}`
  //
  // `project_number`: Maps to a unique int64 id assigned to each project.
  //
  // `location_id`: Refers to the region where the instance will be deployed.
  // Since Secure Source Manager is a regional service, it must be one of the
  // valid GCP regions.
  //
  // `instance_id`: User provided name for the instance, must be unique for a
  // project_number and location_id combination.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Create timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels as key value pairs.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Private settings for private instance.
  PrivateConfig private_config = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Current state of the instance.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An optional field providing information about the current
  // instance state.
  StateNote state_note = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. Customer-managed encryption key name, in the format
  // projects/*/locations/*/keyRings/*/cryptoKeys/*.
  string kms_key = 11 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Output only. A list of hostnames for this instance.
  HostConfig host_config = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata of a Secure Source Manager repository.
message Repository {
  option (google.api.resource) = {
    type: "securesourcemanager.googleapis.com/Repository"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}"
  };

  // URIs for the repository.
  message URIs {
    // Output only. HTML is the URI for user to view the repository in a
    // browser.
    string html = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. git_https is the git HTTPS URI for git operations.
    string git_https = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. API is the URI for API access.
    string api = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Repository initialization configuration.
  message InitialConfig {
    // Default branch name of the repository.
    string default_branch = 1;

    // List of gitignore template names user can choose from.
    // Valid values: actionscript, ada, agda, android,
    // anjuta, ansible, appcelerator-titanium, app-engine, archives,
    // arch-linux-packages, atmel-studio, autotools, backup, bazaar, bazel,
    // bitrix, bricx-cc, c, cake-php, calabash, cf-wheels, chef-cookbook,
    // clojure, cloud9, c-make, code-igniter, code-kit, code-sniffer,
    // common-lisp, composer, concrete5, coq, cordova, cpp, craft-cms, cuda,
    // cvs, d, dart, dart-editor, delphi, diff, dm, dreamweaver, dropbox,
    // drupal, drupal-7, eagle, eclipse, eiffel-studio, elisp, elixir, elm,
    // emacs, ensime, epi-server, erlang, esp-idf, espresso, exercism,
    // expression-engine, ext-js, fancy, finale, flex-builder, force-dot-com,
    // fortran, fuel-php, gcov, git-book, gnome-shell-extension, go, godot, gpg,
    // gradle, grails, gwt, haskell, hugo, iar-ewarm, idris, igor-pro, images,
    // infor-cms, java, jboss, jboss-4, jboss-6, jdeveloper, jekyll,
    // jenkins-home, jenv, jet-brains, jigsaw, joomla, julia, jupyter-notebooks,
    // kate, kdevelop4, kentico, ki-cad, kohana, kotlin, lab-view, laravel,
    // lazarus, leiningen, lemon-stand, libre-office, lilypond, linux, lithium,
    // logtalk, lua, lyx, mac-os, magento, magento-1, magento-2, matlab, maven,
    // mercurial, mercury, metals, meta-programming-system, meteor,
    // microsoft-office, model-sim, momentics, mono-develop, nanoc, net-beans,
    // nikola, nim, ninja, node, notepad-pp, nwjs, objective--c, ocaml, octave,
    // opa, open-cart, openssl, oracle-forms, otto, packer, patch, perl, perl6,
    // phalcon, phoenix, pimcore, play-framework, plone, prestashop, processing,
    // psoc-creator, puppet, pure-script, putty, python, qooxdoo, qt, r, racket,
    // rails, raku, red, redcar, redis, rhodes-rhomobile, ros, ruby, rust, sam,
    // sass, sbt, scala, scheme, scons, scrivener, sdcc, seam-gen, sketch-up,
    // slick-edit, smalltalk, snap, splunk, stata, stella, sublime-text,
    // sugar-crm, svn, swift, symfony, symphony-cms, synopsys-vcs, tags,
    // terraform, tex, text-mate, textpattern, think-php, tortoise-git,
    // turbo-gears-2, typo3, umbraco, unity, unreal-engine, vagrant, vim,
    // virtual-env, virtuoso, visual-studio, visual-studio-code, vue, vvvv, waf,
    // web-methods, windows, word-press, xcode, xilinx, xilinx-ise, xojo,
    // yeoman, yii, zend-framework, zephir.
    repeated string gitignores = 2;

    // License template name user can choose from.
    // Valid values: license-0bsd, license-389-exception, aal, abstyles,
    // adobe-2006, adobe-glyph, adsl, afl-1-1, afl-1-2, afl-2-0, afl-2-1,
    // afl-3-0, afmparse, agpl-1-0, agpl-1-0-only, agpl-1-0-or-later,
    // agpl-3-0-only, agpl-3-0-or-later, aladdin, amdplpa, aml, ampas, antlr-pd,
    // antlr-pd-fallback, apache-1-0, apache-1-1, apache-2-0, apafml, apl-1-0,
    // apsl-1-0, apsl-1-1, apsl-1-2, apsl-2-0, artistic-1-0, artistic-1-0-cl8,
    // artistic-1-0-perl, artistic-2-0, autoconf-exception-2-0,
    // autoconf-exception-3-0, bahyph, barr, beerware, bison-exception-2-2,
    // bittorrent-1-0, bittorrent-1-1, blessing, blueoak-1-0-0,
    // bootloader-exception, borceux, bsd-1-clause, bsd-2-clause,
    // bsd-2-clause-freebsd, bsd-2-clause-netbsd, bsd-2-clause-patent,
    // bsd-2-clause-views, bsd-3-clause, bsd-3-clause-attribution,
    // bsd-3-clause-clear, bsd-3-clause-lbnl, bsd-3-clause-modification,
    // bsd-3-clause-no-nuclear-license, bsd-3-clause-no-nuclear-license-2014,
    // bsd-3-clause-no-nuclear-warranty, bsd-3-clause-open-mpi, bsd-4-clause,
    // bsd-4-clause-shortened, bsd-4-clause-uc, bsd-protection, bsd-source-code,
    // bsl-1-0, busl-1-1, cal-1-0, cal-1-0-combined-work-exception, caldera,
    // catosl-1-1, cc0-1-0, cc-by-1-0, cc-by-2-0, cc-by-3-0, cc-by-3-0-at,
    // cc-by-3-0-us, cc-by-4-0, cc-by-nc-1-0, cc-by-nc-2-0, cc-by-nc-3-0,
    // cc-by-nc-4-0, cc-by-nc-nd-1-0, cc-by-nc-nd-2-0, cc-by-nc-nd-3-0,
    // cc-by-nc-nd-3-0-igo, cc-by-nc-nd-4-0, cc-by-nc-sa-1-0, cc-by-nc-sa-2-0,
    // cc-by-nc-sa-3-0, cc-by-nc-sa-4-0, cc-by-nd-1-0, cc-by-nd-2-0,
    // cc-by-nd-3-0, cc-by-nd-4-0, cc-by-sa-1-0, cc-by-sa-2-0, cc-by-sa-2-0-uk,
    // cc-by-sa-2-1-jp, cc-by-sa-3-0, cc-by-sa-3-0-at, cc-by-sa-4-0, cc-pddc,
    // cddl-1-0, cddl-1-1, cdla-permissive-1-0, cdla-sharing-1-0, cecill-1-0,
    // cecill-1-1, cecill-2-0, cecill-2-1, cecill-b, cecill-c, cern-ohl-1-1,
    // cern-ohl-1-2, cern-ohl-p-2-0, cern-ohl-s-2-0, cern-ohl-w-2-0, clartistic,
    // classpath-exception-2-0, clisp-exception-2-0, cnri-jython, cnri-python,
    // cnri-python-gpl-compatible, condor-1-1, copyleft-next-0-3-0,
    // copyleft-next-0-3-1, cpal-1-0, cpl-1-0, cpol-1-02, crossword,
    // crystal-stacker, cua-opl-1-0, cube, c-uda-1-0, curl, d-fsl-1-0, diffmark,
    // digirule-foss-exception, doc, dotseqn, drl-1-0, dsdp, dvipdfm, ecl-1-0,
    // ecl-2-0, ecos-exception-2-0, efl-1-0, efl-2-0, egenix, entessa, epics,
    // epl-1-0, epl-2-0, erlpl-1-1, etalab-2-0, eu-datagrid, eupl-1-0, eupl-1-1,
    // eupl-1-2, eurosym, fair, fawkes-runtime-exception, fltk-exception,
    // font-exception-2-0, frameworx-1-0, freebsd-doc, freeimage,
    // freertos-exception-2-0, fsfap, fsful, fsfullr, ftl, gcc-exception-2-0,
    // gcc-exception-3-1, gd, gfdl-1-1-invariants-only,
    // gfdl-1-1-invariants-or-later, gfdl-1-1-no-invariants-only,
    // gfdl-1-1-no-invariants-or-later, gfdl-1-1-only, gfdl-1-1-or-later,
    // gfdl-1-2-invariants-only, gfdl-1-2-invariants-or-later,
    // gfdl-1-2-no-invariants-only, gfdl-1-2-no-invariants-or-later,
    // gfdl-1-2-only, gfdl-1-2-or-later, gfdl-1-3-invariants-only,
    // gfdl-1-3-invariants-or-later, gfdl-1-3-no-invariants-only,
    // gfdl-1-3-no-invariants-or-later, gfdl-1-3-only, gfdl-1-3-or-later,
    // giftware, gl2ps, glide, glulxe, glwtpl, gnu-javamail-exception, gnuplot,
    // gpl-1-0-only, gpl-1-0-or-later, gpl-2-0-only, gpl-2-0-or-later,
    // gpl-3-0-linking-exception, gpl-3-0-linking-source-exception,
    // gpl-3-0-only, gpl-3-0-or-later, gpl-cc-1-0, gsoap-1-3b, haskell-report,
    // hippocratic-2-1, hpnd, hpnd-sell-variant, htmltidy,
    // i2p-gpl-java-exception, ibm-pibs, icu, ijg, image-magick, imatix, imlib2,
    // info-zip, intel, intel-acpi, interbase-1-0, ipa, ipl-1-0, isc,
    // jasper-2-0, jpnic, json, lal-1-2, lal-1-3, latex2e, leptonica,
    // lgpl-2-0-only, lgpl-2-0-or-later, lgpl-2-1-only, lgpl-2-1-or-later,
    // lgpl-3-0-linking-exception, lgpl-3-0-only, lgpl-3-0-or-later, lgpllr,
    // libpng, libpng-2-0, libselinux-1-0, libtiff, libtool-exception,
    // liliq-p-1-1, liliq-r-1-1, liliq-rplus-1-1, linux-openib,
    // linux-syscall-note, llvm-exception, lpl-1-0, lpl-1-02, lppl-1-0,
    // lppl-1-1, lppl-1-2, lppl-1-3a, lppl-1-3c, lzma-exception, make-index,
    // mif-exception, miros, mit, mit-0, mit-advertising, mit-cmu, mit-enna,
    // mit-feh, mit-modern-variant, mitnfa, mit-open-group, motosoto, mpich2,
    // mpl-1-0, mpl-1-1, mpl-2-0, mpl-2-0-no-copyleft-exception, ms-pl, ms-rl,
    // mtll, mulanpsl-1-0, mulanpsl-2-0, multics, mup, naist-2003, nasa-1-3,
    // naumen, nbpl-1-0, ncgl-uk-2-0, ncsa, netcdf, net-snmp, newsletr, ngpl,
    // nist-pd, nist-pd-fallback, nlod-1-0, nlpl, nokia, nokia-qt-exception-1-1,
    // nosl, noweb, npl-1-0, npl-1-1, nposl-3-0, nrl, ntp, ntp-0,
    // ocaml-lgpl-linking-exception, occt-exception-1-0, occt-pl, oclc-2-0,
    // odbl-1-0, odc-by-1-0, ofl-1-0, ofl-1-0-no-rfn, ofl-1-0-rfn, ofl-1-1,
    // ofl-1-1-no-rfn, ofl-1-1-rfn, ogc-1-0, ogdl-taiwan-1-0, ogl-canada-2-0,
    // ogl-uk-1-0, ogl-uk-2-0, ogl-uk-3-0, ogtsl, oldap-1-1, oldap-1-2,
    // oldap-1-3, oldap-1-4, oldap-2-0, oldap-2-0-1, oldap-2-1, oldap-2-2,
    // oldap-2-2-1, oldap-2-2-2, oldap-2-3, oldap-2-4, oldap-2-7, oml,
    // openjdk-assembly-exception-1-0, openssl, openvpn-openssl-exception,
    // opl-1-0, oset-pl-2-1, osl-1-0, osl-1-1, osl-2-0, osl-2-1, osl-3-0,
    // o-uda-1-0, parity-6-0-0, parity-7-0-0, pddl-1-0, php-3-0, php-3-01,
    // plexus, polyform-noncommercial-1-0-0, polyform-small-business-1-0-0,
    // postgresql, psf-2-0, psfrag, ps-or-pdf-font-exception-20170817, psutils,
    // python-2-0, qhull, qpl-1-0, qt-gpl-exception-1-0, qt-lgpl-exception-1-1,
    // qwt-exception-1-0, rdisc, rhecos-1-1, rpl-1-1, rpsl-1-0, rsa-md, rscpl,
    // ruby, saxpath, sax-pd, scea, sendmail, sendmail-8-23, sgi-b-1-0,
    // sgi-b-1-1, sgi-b-2-0, shl-0-51, shl-2-0, shl-2-1, simpl-2-0, sissl,
    // sissl-1-2, sleepycat, smlnj, smppl, snia, spencer-86, spencer-94,
    // spencer-99, spl-1-0, ssh-openssh, ssh-short, sspl-1-0, sugarcrm-1-1-3,
    // swift-exception, swl, tapr-ohl-1-0, tcl, tcp-wrappers, tmate, torque-1-1,
    // tosl, tu-berlin-1-0, tu-berlin-2-0, u-boot-exception-2-0, ucl-1-0,
    // unicode-dfs-2015, unicode-dfs-2016, unicode-tou,
    // universal-foss-exception-1-0, unlicense, upl-1-0, vim, vostrom, vsl-1-0,
    // w3c, w3c-19980720, w3c-20150513, watcom-1-0, wsuipa, wtfpl,
    // wxwindows-exception-3-1, x11, xerox, xfree86-1-1, xinetd, xnet, xpp,
    // xskat, ypl-1-0, ypl-1-1, zed, zend-2-0, zimbra-1-3, zimbra-1-4, zlib,
    // zlib-acknowledgement, zpl-1-1, zpl-2-0, zpl-2-1.
    string license = 3;

    // README template name.
    // Valid template name(s) are: default.
    string readme = 4;
  }

  // Optional. A unique identifier for a repository. The name should be of the
  // format:
  // `projects/{project}/locations/{location_id}/repositories/{repository_id}`
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the repository, which cannot exceed 500
  // characters.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the instance in which the repository is hosted,
  // formatted as
  // `projects/{project_number}/locations/{location_id}/instances/{instance_id}`
  // When creating repository via
  // securesourcemanager.googleapis.com (Control Plane API), this field is used
  // as input. When creating repository via *.sourcemanager.dev (Data Plane
  // API), this field is output only.
  string instance = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "securesourcemanager.googleapis.com/Instance"
    }
  ];

  // Output only. Unique identifier of the repository.
  string uid = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create timestamp.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update timestamp.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. URIs for the repository.
  URIs uris = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. Initial configurations for the repository.
  InitialConfig initial_config = 10 [(google.api.field_behavior) = INPUT_ONLY];
}

// Metadata of a BranchRule. BranchRule is the protection rule to enforce
// pre-defined rules on desginated branches within a repository.
message BranchRule {
  option (google.api.resource) = {
    type: "securesourcemanager.googleapis.com/BranchRule"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/branchRules/{branch_rule}"
  };

  // Check is a type for status check.
  message Check {
    // Required. The context of the check.
    string context = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Optional. A unique identifier for a BranchRule. The name should be of the
  // format:
  // `projects/{project}/locations/{location}/repositories/{repository}/branchRules/{branch_rule}`
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Unique identifier of the repository.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create timestamp.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update timestamp.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User annotations. These attributes can only be set and used by
  // the user. See https://google.aip.dev/128#annotations for more details such
  // as format and size limitations.
  map<string, string> annotations = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The pattern of the branch that can match to this BranchRule.
  // Specified as regex.
  // .* for all branches. Examples: main, (main|release.*).
  // Current MVP phase only support `.*` for wildcard.
  string include_pattern = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Determines if the branch rule is disabled or not.
  bool disabled = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Determines if the branch rule requires a pull request or not.
  bool require_pull_request = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The minimum number of reviews required for the branch rule to be
  // matched.
  int32 minimum_reviews_count = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The minimum number of approvals required for the branch rule to
  // be matched.
  int32 minimum_approvals_count = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Determines if require comments resolved before merging to the
  // branch.
  bool require_comments_resolved = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Determines if allow stale reviews or approvals before merging to
  // the branch.
  bool allow_stale_reviews = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Determines if require linear history before merging to the
  // branch.
  bool require_linear_history = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List of required status checks before merging to the branch.
  repeated Check required_status_checks = 14
      [(google.api.field_behavior) = OPTIONAL];
}

// ListInstancesRequest is the request to list instances.
message ListInstancesRequest {
  // Required. Parent value for ListInstancesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securesourcemanager.googleapis.com/Instance"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filter for filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

message ListInstancesResponse {
  // The list of instances.
  repeated Instance instances = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// GetInstanceRequest is the request for getting an instance.
message GetInstanceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securesourcemanager.googleapis.com/Instance"
    }
  ];
}

// CreateInstanceRequest is the request for creating an instance.
message CreateInstanceRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securesourcemanager.googleapis.com/Instance"
    }
  ];

  // Required. ID of the instance to be created.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// DeleteInstanceRequest is the request for deleting an instance.
message DeleteInstanceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securesourcemanager.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ListRepositoriesRequest is request to list repositories.
message ListRepositoriesRequest {
  // Required. Parent value for ListRepositoriesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securesourcemanager.googleapis.com/Repository"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Optional. Filter results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the instance in which the repository is hosted,
  // formatted as
  // `projects/{project_number}/locations/{location_id}/instances/{instance_id}`.
  // When listing repositories via
  // securesourcemanager.googleapis.com (Control Plane API), this field is
  // required. When listing repositories via *.sourcemanager.dev (Data Plane
  // API), this field is ignored.
  string instance = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "securesourcemanager.googleapis.com/Instance"
    }
  ];
}

message ListRepositoriesResponse {
  // The list of repositories.
  repeated Repository repositories = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// GetRepositoryRequest is the request for getting a repository.
message GetRepositoryRequest {
  // Required. Name of the repository to retrieve.
  // The format is
  // `projects/{project_number}/locations/{location_id}/repositories/{repository_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securesourcemanager.googleapis.com/Repository"
    }
  ];
}

// CreateRepositoryRequest is the request for creating a repository.
message CreateRepositoryRequest {
  // Required. The project in which to create the repository. Values are of the
  // form `projects/{project_number}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securesourcemanager.googleapis.com/Repository"
    }
  ];

  // Required. The resource being created.
  Repository repository = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the repository, which will become the final
  // component of the repository's resource name. This value should be 4-63
  // characters, and valid characters are /[a-z][0-9]-/.
  string repository_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// DeleteRepositoryRequest is the request to delete a repository.
message DeleteRepositoryRequest {
  // Required. Name of the repository to delete.
  // The format is
  // projects/{project_number}/locations/{location_id}/repositories/{repository_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securesourcemanager.googleapis.com/Repository"
    }
  ];

  // Optional. If set to true, and the repository is not found, the request will
  // succeed but no action will be taken on the server.
  bool allow_missing = 2 [(google.api.field_behavior) = OPTIONAL];
}

// GetBranchRuleRequest is the request for getting a branch rule.
message GetBranchRuleRequest {
  // Required. Name of the repository to retrieve.
  // The format is
  // `projects/{project}/locations/{location}/repositories/{repository}/branchRules/{branch_rule}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securesourcemanager.googleapis.com/BranchRule"
    }
  ];
}

// CreateBranchRuleRequest is the request to create a branch rule.
message CreateBranchRuleRequest {
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securesourcemanager.googleapis.com/BranchRule"
    }
  ];

  BranchRule branch_rule = 2 [(google.api.field_behavior) = REQUIRED];

  string branch_rule_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// ListBranchRulesRequest is the request to list branch rules.
message ListBranchRulesRequest {
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securesourcemanager.googleapis.com/BranchRule"
    }
  ];

  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// DeleteBranchRuleRequest is the request to delete a branch rule.
message DeleteBranchRuleRequest {
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securesourcemanager.googleapis.com/BranchRule"
    }
  ];

  // Optional. If set to true, and the branch rule is not found, the request
  // will succeed but no action will be taken on the server.
  bool allow_missing = 2 [(google.api.field_behavior) = OPTIONAL];
}

// UpdateBranchRuleRequest is the request to update a branchRule.
message UpdateBranchRuleRequest {
  BranchRule branch_rule = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // actually post it.  (https://google.aip.dev/163, for declarative friendly)
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Field mask is used to specify the fields to be overwritten in the
  // branchRule resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask.
  // The special value "*" means full replacement.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = REQUIRED];
}

// ListBranchRulesResponse is the response to listing branchRules.
message ListBranchRulesResponse {
  // The list of branch rules.
  repeated BranchRule branch_rules = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}
