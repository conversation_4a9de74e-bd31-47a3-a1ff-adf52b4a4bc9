{"methodConfig": [{"name": [{"service": "google.cloud.securesourcemanager.v1.SecureSourceManager", "method": "ListInstances"}, {"service": "google.cloud.securesourcemanager.v1.SecureSourceManager", "method": "GetInstance"}, {"service": "google.cloud.securesourcemanager.v1.SecureSourceManager", "method": "ListRepositories"}, {"service": "google.cloud.securesourcemanager.v1.SecureSourceManager", "method": "GetRepository"}, {"service": "google.cloud.securesourcemanager.v1.SecureSourceManager", "method": "GetIamPolicyRepo"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}