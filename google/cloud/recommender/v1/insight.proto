// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommender.v1;

import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Recommender.V1";
option go_package = "cloud.google.com/go/recommender/apiv1/recommenderpb;recommenderpb";
option java_multiple_files = true;
option java_outer_classname = "InsightProto";
option java_package = "com.google.cloud.recommender.v1";
option objc_class_prefix = "CREC";
option (google.api.resource_definition) = {
  type: "recommender.googleapis.com/InsightType"
  pattern: "projects/{project}/locations/{location}/insightTypes/{insight_type}"
  pattern: "billingAccounts/{billing_account}/locations/{location}/insightTypes/{insight_type}"
  pattern: "folders/{folder}/locations/{location}/insightTypes/{insight_type}"
  pattern: "organizations/{organization}/locations/{location}/insightTypes/{insight_type}"
};

// An insight along with the information used to derive the insight. The insight
// may have associated recommendations as well.
message Insight {
  option (google.api.resource) = {
    type: "recommender.googleapis.com/Insight"
    pattern: "projects/{project}/locations/{location}/insightTypes/{insight_type}/insights/{insight}"
    pattern: "billingAccounts/{billing_account}/locations/{location}/insightTypes/{insight_type}/insights/{insight}"
    pattern: "folders/{folder}/locations/{location}/insightTypes/{insight_type}/insights/{insight}"
    pattern: "organizations/{organization}/locations/{location}/insightTypes/{insight_type}/insights/{insight}"
  };

  // Insight category.
  enum Category {
    // Unspecified category.
    CATEGORY_UNSPECIFIED = 0;

    // The insight is related to cost.
    COST = 1;

    // The insight is related to security.
    SECURITY = 2;

    // The insight is related to performance.
    PERFORMANCE = 3;

    // This insight is related to manageability.
    MANAGEABILITY = 4;

    // The insight is related to sustainability.
    SUSTAINABILITY = 5;

    // This insight is related to reliability.
    RELIABILITY = 6;
  }

  // Insight severity levels.
  enum Severity {
    // Insight has unspecified severity.
    SEVERITY_UNSPECIFIED = 0;

    // Insight has low severity.
    LOW = 1;

    // Insight has medium severity.
    MEDIUM = 2;

    // Insight has high severity.
    HIGH = 3;

    // Insight has critical severity.
    CRITICAL = 4;
  }

  // Reference to an associated recommendation.
  message RecommendationReference {
    // Recommendation resource name, e.g.
    // projects/[PROJECT_NUMBER]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]/recommendations/[RECOMMENDATION_ID]
    string recommendation = 1;
  }

  // Name of the insight.
  string name = 1;

  // Free-form human readable summary in English. The maximum length is 500
  // characters.
  string description = 2;

  // Fully qualified resource names that this insight is targeting.
  repeated string target_resources = 9;

  // Insight subtype. Insight content schema will be stable for a given subtype.
  string insight_subtype = 10;

  // A struct of custom fields to explain the insight.
  // Example: "grantedPermissionsCount": "1000"
  google.protobuf.Struct content = 3;

  // Timestamp of the latest data used to generate the insight.
  google.protobuf.Timestamp last_refresh_time = 4;

  // Observation period that led to the insight. The source data used to
  // generate the insight ends at last_refresh_time and begins at
  // (last_refresh_time - observation_period).
  google.protobuf.Duration observation_period = 5;

  // Information state and metadata.
  InsightStateInfo state_info = 6;

  // Category being targeted by the insight.
  Category category = 7;

  // Insight's severity.
  Severity severity = 15;

  // Fingerprint of the Insight. Provides optimistic locking when updating
  // states.
  string etag = 11;

  // Recommendations derived from this insight.
  repeated RecommendationReference associated_recommendations = 8;
}

// Information related to insight state.
message InsightStateInfo {
  // Represents insight state.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // Insight is active. Content for ACTIVE insights can be updated by Google.
    // ACTIVE insights can be marked DISMISSED OR ACCEPTED.
    ACTIVE = 1;

    // Some action has been taken based on this insight. Insights become
    // accepted when a recommendation derived from the insight has been marked
    // CLAIMED, SUCCEEDED, or FAILED. ACTIVE insights can also be marked
    // ACCEPTED explicitly. Content for ACCEPTED insights is immutable. ACCEPTED
    // insights can only be marked ACCEPTED (which may update state metadata).
    ACCEPTED = 2;

    // Insight is dismissed. Content for DISMISSED insights can be updated by
    // Google. DISMISSED insights can be marked as ACTIVE.
    DISMISSED = 3;
  }

  // Insight state.
  State state = 1;

  // A map of metadata for the state, provided by user or automations systems.
  map<string, string> state_metadata = 2;
}
