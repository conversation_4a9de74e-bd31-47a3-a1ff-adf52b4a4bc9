# This build file includes a target for the Ruby wrapper library for
# google-cloud-recommender.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for recommender.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "recommender_ruby_wrapper",
    srcs = ["//google/cloud/recommender/v1:recommender_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-recommender",
        "ruby-cloud-env-prefix=RECOMMENDER",
        "ruby-cloud-wrapper-of=v1:0.17",
        "ruby-cloud-product-url=https://cloud.google.com/recommender",
        "ruby-cloud-api-id=recommender.googleapis.com",
        "ruby-cloud-api-shortname=recommender",
        "ruby-cloud-factory-method-suffix=_service",
    ],
    ruby_cloud_description = "Recommender is a service on Google Cloud that provides usage recommendations for Cloud products and services.",
    ruby_cloud_title = "Recommender",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-recommender-ruby",
    deps = [
        ":recommender_ruby_wrapper",
    ],
)
