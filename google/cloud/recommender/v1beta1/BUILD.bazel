# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "recommender_proto",
    srcs = [
        "insight.proto",
        "insight_type_config.proto",
        "recommendation.proto",
        "recommender_config.proto",
        "recommender_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "recommender_proto_with_info",
    deps = [
        ":recommender_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "recommender_java_proto",
    deps = [":recommender_proto"],
)

java_grpc_library(
    name = "recommender_java_grpc",
    srcs = [":recommender_proto"],
    deps = [":recommender_java_proto"],
)

java_gapic_library(
    name = "recommender_java_gapic",
    srcs = [":recommender_proto_with_info"],
    gapic_yaml = "recommender_gapic.yaml",
    grpc_service_config = "recommender_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recommender_v1beta1.yaml",
    test_deps = [
        ":recommender_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":recommender_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "recommender_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.recommender.v1beta1.RecommenderClientHttpJsonTest",
        "com.google.cloud.recommender.v1beta1.RecommenderClientTest",
    ],
    runtime_deps = [":recommender_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-recommender-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":recommender_java_gapic",
        ":recommender_java_grpc",
        ":recommender_java_proto",
        ":recommender_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "recommender_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/recommender/apiv1beta1/recommenderpb",
    protos = [":recommender_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "recommender_go_gapic",
    srcs = [":recommender_proto_with_info"],
    grpc_service_config = "recommender_grpc_service_config.json",
    importpath = "cloud.google.com/go/recommender/apiv1beta1;recommender",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "recommender_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recommender_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-recommender-v1beta1-go",
    deps = [
        ":recommender_go_gapic",
        ":recommender_go_gapic_srcjar-metadata.srcjar",
        ":recommender_go_gapic_srcjar-snippets.srcjar",
        ":recommender_go_gapic_srcjar-test.srcjar",
        ":recommender_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "recommender_py_gapic",
    srcs = [":recommender_proto"],
    grpc_service_config = "recommender_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recommender_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "recommender_py_gapic_test",
    srcs = [
        "recommender_py_gapic_pytest.py",
        "recommender_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":recommender_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "recommender-v1beta1-py",
    deps = [
        ":recommender_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "recommender_php_proto",
    deps = [":recommender_proto"],
)

php_gapic_library(
    name = "recommender_php_gapic",
    srcs = [":recommender_proto_with_info"],
    grpc_service_config = "recommender_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recommender_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":recommender_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-recommender-v1beta1-php",
    deps = [
        ":recommender_php_gapic",
        ":recommender_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "recommender_nodejs_gapic",
    package_name = "@google-cloud/recommender",
    src = ":recommender_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "recommender_grpc_service_config.json",
    package = "google.cloud.recommender.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "recommender_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "recommender-v1beta1-nodejs",
    deps = [
        ":recommender_nodejs_gapic",
        ":recommender_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "recommender_ruby_proto",
    deps = [":recommender_proto"],
)

ruby_grpc_library(
    name = "recommender_ruby_grpc",
    srcs = [":recommender_proto"],
    deps = [":recommender_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "recommender_ruby_gapic",
    srcs = [":recommender_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-recommender-v1beta1"],
    grpc_service_config = "recommender_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recommender_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recommender_ruby_grpc",
        ":recommender_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-recommender-v1beta1-ruby",
    deps = [
        ":recommender_ruby_gapic",
        ":recommender_ruby_grpc",
        ":recommender_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "recommender_csharp_proto",
    deps = [":recommender_proto"],
)

csharp_grpc_library(
    name = "recommender_csharp_grpc",
    srcs = [":recommender_proto"],
    deps = [":recommender_csharp_proto"],
)

csharp_gapic_library(
    name = "recommender_csharp_gapic",
    srcs = [":recommender_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "recommender_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recommender_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recommender_csharp_grpc",
        ":recommender_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-recommender-v1beta1-csharp",
    deps = [
        ":recommender_csharp_gapic",
        ":recommender_csharp_grpc",
        ":recommender_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "recommender_cc_proto",
    deps = [":recommender_proto"],
)

cc_grpc_library(
    name = "recommender_cc_grpc",
    srcs = [":recommender_proto"],
    grpc_only = True,
    deps = [":recommender_cc_proto"],
)
