// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommender.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Recommender.V1Beta1";
option go_package = "cloud.google.com/go/recommender/apiv1beta1/recommenderpb;recommenderpb";
option java_multiple_files = true;
option java_outer_classname = "RecommenderConfigProto";
option java_package = "com.google.cloud.recommender.v1beta1";
option objc_class_prefix = "CREC";

// Configuration for a Recommender.
message RecommenderConfig {
  option (google.api.resource) = {
    type: "recommender.googleapis.com/RecommenderConfig"
    pattern: "projects/{project}/locations/{location}/recommenders/{recommender}/config"
    pattern: "organizations/{organization}/locations/{location}/recommenders/{recommender}/config"
  };

  // Name of recommender config.
  // Eg,
  // projects/[PROJECT_NUMBER]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]/config
  string name = 1;

  // RecommenderGenerationConfig which configures the Generation of
  // recommendations for this recommender.
  RecommenderGenerationConfig recommender_generation_config = 2;

  // Fingerprint of the RecommenderConfig. Provides optimistic locking when
  // updating.
  string etag = 3;

  // Last time when the config was updated.
  google.protobuf.Timestamp update_time = 4;

  // Output only. Immutable. The revision ID of the config.
  // A new revision is committed whenever the config is changed in any way.
  // The format is an 8-character hexadecimal string.
  string revision_id = 5 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Allows clients to store small amounts of arbitrary data. Annotations must
  // follow the Kubernetes syntax.
  // The total size of all keys and values combined is limited to 256k.
  // Key can have 2 segments: prefix (optional) and name (required),
  // separated by a slash (/).
  // Prefix must be a DNS subdomain.
  // Name must be 63 characters or less, begin and end with alphanumerics,
  // with dashes (-), underscores (_), dots (.), and alphanumerics between.
  map<string, string> annotations = 6;

  // A user-settable field to provide a human-readable name to be used in user
  // interfaces.
  string display_name = 7;
}

// A Configuration to customize the generation of recommendations.
// Eg, customizing the lookback period considered when generating a
// recommendation.
message RecommenderGenerationConfig {
  // Parameters for this RecommenderGenerationConfig. These configs can be used
  // by or are applied to all subtypes.
  google.protobuf.Struct params = 1;
}
