type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
# The settings of generated code in a specific language.
language_settings:
  python:
    package_name: google.cloud.recommender_v1beta1.gapic
  go:
    package_name: cloud.google.com/go/recommender/apiv1beta1
  csharp:
    package_name: Google.Cloud.Recommender.V1Beta1
  ruby:
    package_name: Google::Cloud::Recommender::V1beta1
  php:
    package_name: Google\Cloud\Recommender\V1beta1
  nodejs:
    package_name: recommender.v1beta1
