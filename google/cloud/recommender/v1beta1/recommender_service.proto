// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommender.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/recommender/v1beta1/insight.proto";
import "google/cloud/recommender/v1beta1/insight_type_config.proto";
import "google/cloud/recommender/v1beta1/recommendation.proto";
import "google/cloud/recommender/v1beta1/recommender_config.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Recommender.V1Beta1";
option go_package = "cloud.google.com/go/recommender/apiv1beta1/recommenderpb;recommenderpb";
option java_multiple_files = true;
option java_outer_classname = "RecommenderProto";
option java_package = "com.google.cloud.recommender.v1beta1";
option objc_class_prefix = "CREC";

// Provides insights and recommendations for cloud customers for various
// categories like performance optimization, cost savings, reliability, feature
// discovery, etc. Insights and recommendations are generated automatically
// based on analysis of user resources, configuration and monitoring metrics.
service Recommender {
  option (google.api.default_host) = "recommender.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists insights for the specified Cloud Resource. Requires the
  // recommender.*.list IAM permission for the specified insight type.
  rpc ListInsights(ListInsightsRequest) returns (ListInsightsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/insightTypes/*}/insights"
      additional_bindings {
        get: "/v1beta1/{parent=billingAccounts/*/locations/*/insightTypes/*}/insights"
      }
      additional_bindings {
        get: "/v1beta1/{parent=folders/*/locations/*/insightTypes/*}/insights"
      }
      additional_bindings {
        get: "/v1beta1/{parent=organizations/*/locations/*/insightTypes/*}/insights"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the requested insight. Requires the recommender.*.get IAM permission
  // for the specified insight type.
  rpc GetInsight(GetInsightRequest) returns (Insight) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/insightTypes/*/insights/*}"
      additional_bindings {
        get: "/v1beta1/{name=billingAccounts/*/locations/*/insightTypes/*/insights/*}"
      }
      additional_bindings {
        get: "/v1beta1/{name=folders/*/locations/*/insightTypes/*/insights/*}"
      }
      additional_bindings {
        get: "/v1beta1/{name=organizations/*/locations/*/insightTypes/*/insights/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Marks the Insight State as Accepted. Users can use this method to
  // indicate to the Recommender API that they have applied some action based
  // on the insight. This stops the insight content from being updated.
  //
  // MarkInsightAccepted can be applied to insights in ACTIVE state. Requires
  // the recommender.*.update IAM permission for the specified insight.
  rpc MarkInsightAccepted(MarkInsightAcceptedRequest) returns (Insight) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/insightTypes/*/insights/*}:markAccepted"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{name=billingAccounts/*/locations/*/insightTypes/*/insights/*}:markAccepted"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{name=folders/*/locations/*/insightTypes/*/insights/*}:markAccepted"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{name=organizations/*/locations/*/insightTypes/*/insights/*}:markAccepted"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,state_metadata,etag";
  }

  // Lists recommendations for the specified Cloud Resource. Requires the
  // recommender.*.list IAM permission for the specified recommender.
  rpc ListRecommendations(ListRecommendationsRequest)
      returns (ListRecommendationsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/recommenders/*}/recommendations"
      additional_bindings {
        get: "/v1beta1/{parent=billingAccounts/*/locations/*/recommenders/*}/recommendations"
      }
      additional_bindings {
        get: "/v1beta1/{parent=folders/*/locations/*/recommenders/*}/recommendations"
      }
      additional_bindings {
        get: "/v1beta1/{parent=organizations/*/locations/*/recommenders/*}/recommendations"
      }
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Gets the requested recommendation. Requires the recommender.*.get
  // IAM permission for the specified recommender.
  rpc GetRecommendation(GetRecommendationRequest) returns (Recommendation) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/recommenders/*/recommendations/*}"
      additional_bindings {
        get: "/v1beta1/{name=billingAccounts/*/locations/*/recommenders/*/recommendations/*}"
      }
      additional_bindings {
        get: "/v1beta1/{name=folders/*/locations/*/recommenders/*/recommendations/*}"
      }
      additional_bindings {
        get: "/v1beta1/{name=organizations/*/locations/*/recommenders/*/recommendations/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Marks the Recommendation State as Claimed. Users can use this method to
  // indicate to the Recommender API that they are starting to apply the
  // recommendation themselves. This stops the recommendation content from being
  // updated. Associated insights are frozen and placed in the ACCEPTED state.
  //
  // MarkRecommendationClaimed can be applied to recommendations in CLAIMED or
  // ACTIVE state.
  //
  // Requires the recommender.*.update IAM permission for the specified
  // recommender.
  rpc MarkRecommendationClaimed(MarkRecommendationClaimedRequest)
      returns (Recommendation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/recommenders/*/recommendations/*}:markClaimed"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{name=billingAccounts/*/locations/*/recommenders/*/recommendations/*}:markClaimed"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{name=folders/*/locations/*/recommenders/*/recommendations/*}:markClaimed"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{name=organizations/*/locations/*/recommenders/*/recommendations/*}:markClaimed"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,state_metadata,etag";
  }

  // Marks the Recommendation State as Succeeded. Users can use this method to
  // indicate to the Recommender API that they have applied the recommendation
  // themselves, and the operation was successful. This stops the recommendation
  // content from being updated. Associated insights are frozen and placed in
  // the ACCEPTED state.
  //
  // MarkRecommendationSucceeded can be applied to recommendations in ACTIVE,
  // CLAIMED, SUCCEEDED, or FAILED state.
  //
  // Requires the recommender.*.update IAM permission for the specified
  // recommender.
  rpc MarkRecommendationSucceeded(MarkRecommendationSucceededRequest)
      returns (Recommendation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/recommenders/*/recommendations/*}:markSucceeded"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{name=billingAccounts/*/locations/*/recommenders/*/recommendations/*}:markSucceeded"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{name=folders/*/locations/*/recommenders/*/recommendations/*}:markSucceeded"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{name=organizations/*/locations/*/recommenders/*/recommendations/*}:markSucceeded"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,state_metadata,etag";
  }

  // Marks the Recommendation State as Failed. Users can use this method to
  // indicate to the Recommender API that they have applied the recommendation
  // themselves, and the operation failed. This stops the recommendation content
  // from being updated. Associated insights are frozen and placed in the
  // ACCEPTED state.
  //
  // MarkRecommendationFailed can be applied to recommendations in ACTIVE,
  // CLAIMED, SUCCEEDED, or FAILED state.
  //
  // Requires the recommender.*.update IAM permission for the specified
  // recommender.
  rpc MarkRecommendationFailed(MarkRecommendationFailedRequest)
      returns (Recommendation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/recommenders/*/recommendations/*}:markFailed"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{name=billingAccounts/*/locations/*/recommenders/*/recommendations/*}:markFailed"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{name=folders/*/locations/*/recommenders/*/recommendations/*}:markFailed"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{name=organizations/*/locations/*/recommenders/*/recommendations/*}:markFailed"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,state_metadata,etag";
  }

  // Gets the requested Recommender Config. There is only one instance of the
  // config for each Recommender.
  rpc GetRecommenderConfig(GetRecommenderConfigRequest)
      returns (RecommenderConfig) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/recommenders/*/config}"
      additional_bindings {
        get: "/v1beta1/{name=organizations/*/locations/*/recommenders/*/config}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Recommender Config. This will create a new revision of the
  // config.
  rpc UpdateRecommenderConfig(UpdateRecommenderConfigRequest)
      returns (RecommenderConfig) {
    option (google.api.http) = {
      patch: "/v1beta1/{recommender_config.name=projects/*/locations/*/recommenders/*/config}"
      body: "recommender_config"
      additional_bindings {
        post: "/v1beta1/{recommender_config.name=organizations/*/locations/*/recommenders/*/config}"
        body: "recommender_config"
      }
    };
    option (google.api.method_signature) = "recommender_config,update_mask";
  }

  // Gets the requested InsightTypeConfig. There is only one instance of the
  // config for each InsightType.
  rpc GetInsightTypeConfig(GetInsightTypeConfigRequest)
      returns (InsightTypeConfig) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/insightTypes/*/config}"
      additional_bindings {
        get: "/v1beta1/{name=organizations/*/locations/*/insightTypes/*/config}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an InsightTypeConfig change. This will create a new revision of the
  // config.
  rpc UpdateInsightTypeConfig(UpdateInsightTypeConfigRequest)
      returns (InsightTypeConfig) {
    option (google.api.http) = {
      patch: "/v1beta1/{insight_type_config.name=projects/*/locations/*/insightTypes/*/config}"
      body: "insight_type_config"
      additional_bindings {
        post: "/v1beta1/{insight_type_config.name=organizations/*/locations/*/insightTypes/*/config}"
        body: "insight_type_config"
      }
    };
    option (google.api.method_signature) = "insight_type_config,update_mask";
  }

  // Lists all available Recommenders.
  // No IAM permissions are required.
  rpc ListRecommenders(ListRecommendersRequest)
      returns (ListRecommendersResponse) {
    option (google.api.http) = {
      get: "/v1beta1/recommenders"
    };
  }

  // Lists available InsightTypes.
  // No IAM permissions are required.
  rpc ListInsightTypes(ListInsightTypesRequest)
      returns (ListInsightTypesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/insightTypes"
    };
  }
}

// Request for the `ListInsights` method.
message ListInsightsRequest {
  // Required. The container resource on which to execute the request.
  // Acceptable formats:
  //
  // * `projects/[PROJECT_NUMBER]/locations/[LOCATION]/insightTypes/[INSIGHT_TYPE_ID]`
  //
  // * `projects/[PROJECT_ID]/locations/[LOCATION]/insightTypes/[INSIGHT_TYPE_ID]`
  //
  // * `billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION]/insightTypes/[INSIGHT_TYPE_ID]`
  //
  // * `folders/[FOLDER_ID]/locations/[LOCATION]/insightTypes/[INSIGHT_TYPE_ID]`
  //
  // * `organizations/[ORGANIZATION_ID]/locations/[LOCATION]/insightTypes/[INSIGHT_TYPE_ID]`
  //
  // LOCATION here refers to GCP Locations:
  // https://cloud.google.com/about/locations/
  // INSIGHT_TYPE_ID refers to supported insight types:
  // https://cloud.google.com/recommender/docs/insights/insight-types.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/InsightType"
    }
  ];

  // Optional. The maximum number of results to return from this request.
  // Non-positive values are ignored. If not specified, the server will
  // determine the number of results to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If present, retrieves the next batch of results from the
  // preceding call to this method. `page_token` must be the value of
  // `next_page_token` from the previous response. The values of other method
  // parameters must be identical to those in the previous call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter expression to restrict the insights returned. Supported
  // filter fields:
  //
  // * `stateInfo.state`
  //
  // * `insightSubtype`
  //
  // * `severity`
  //
  // Examples:
  //
  // * `stateInfo.state = ACTIVE OR stateInfo.state = DISMISSED`
  //
  // * `insightSubtype = PERMISSIONS_USAGE`
  //
  // * `severity = CRITICAL OR severity = HIGH`
  //
  // * `stateInfo.state = ACTIVE AND (severity = CRITICAL OR severity = HIGH)`
  //
  // (These expressions are based on the filter language described at
  // https://google.aip.dev/160)
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response to the `ListInsights` method.
message ListInsightsResponse {
  // The set of insights for the `parent` resource.
  repeated Insight insights = 1;

  // A token that can be used to request the next page of results. This field is
  // empty if there are no additional results.
  string next_page_token = 2;
}

// Request to the `GetInsight` method.
message GetInsightRequest {
  // Required. Name of the insight.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/Insight"
    }
  ];
}

// Request for the `MarkInsightAccepted` method.
message MarkInsightAcceptedRequest {
  // Required. Name of the insight.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/Insight"
    }
  ];

  // Optional. State properties user wish to include with this state.  Full
  // replace of the current state_metadata.
  map<string, string> state_metadata = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Fingerprint of the Insight. Provides optimistic locking.
  string etag = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `ListRecommendations` method.
message ListRecommendationsRequest {
  // Required. The container resource on which to execute the request.
  // Acceptable formats:
  //
  // * `projects/[PROJECT_NUMBER]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]`
  //
  // * `projects/[PROJECT_ID]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]`
  //
  // * `billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]`
  //
  // * `folders/[FOLDER_ID]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]`
  //
  // * `organizations/[ORGANIZATION_ID]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]`
  //
  // LOCATION here refers to GCP Locations:
  // https://cloud.google.com/about/locations/
  // RECOMMENDER_ID refers to supported recommenders:
  // https://cloud.google.com/recommender/docs/recommenders.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/Recommender"
    }
  ];

  // Optional. The maximum number of results to return from this request.
  // Non-positive values are ignored. If not specified, the server will
  // determine the number of results to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If present, retrieves the next batch of results from the
  // preceding call to this method. `page_token` must be the value of
  // `next_page_token` from the previous response. The values of other method
  // parameters must be identical to those in the previous call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Filter expression to restrict the recommendations returned. Supported
  // filter fields:
  //
  // * `state_info.state`
  //
  // * `recommenderSubtype`
  //
  // * `priority`
  //
  // Examples:
  //
  // * `stateInfo.state = ACTIVE OR stateInfo.state = DISMISSED`
  //
  // * `recommenderSubtype = REMOVE_ROLE OR recommenderSubtype = REPLACE_ROLE`
  //
  // * `priority = P1 OR priority = P2`
  //
  // * `stateInfo.state = ACTIVE AND (priority = P1 OR priority = P2)`
  //
  // (These expressions are based on the filter language described at
  // https://google.aip.dev/160)
  string filter = 5;
}

// Response to the `ListRecommendations` method.
message ListRecommendationsResponse {
  // The set of recommendations for the `parent` resource.
  repeated Recommendation recommendations = 1;

  // A token that can be used to request the next page of results. This field is
  // empty if there are no additional results.
  string next_page_token = 2;
}

// Request to the `GetRecommendation` method.
message GetRecommendationRequest {
  // Required. Name of the recommendation.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/Recommendation"
    }
  ];
}

// Request for the `MarkRecommendationClaimed` Method.
message MarkRecommendationClaimedRequest {
  // Required. Name of the recommendation.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/Recommendation"
    }
  ];

  // State properties to include with this state. Overwrites any existing
  // `state_metadata`.
  // Keys must match the regex `/^[a-z0-9][a-z0-9_.-]{0,62}$/`.
  // Values must match the regex `/^[a-zA-Z0-9_./-]{0,255}$/`.
  map<string, string> state_metadata = 2;

  // Required. Fingerprint of the Recommendation. Provides optimistic locking.
  string etag = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `MarkRecommendationSucceeded` Method.
message MarkRecommendationSucceededRequest {
  // Required. Name of the recommendation.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/Recommendation"
    }
  ];

  // State properties to include with this state. Overwrites any existing
  // `state_metadata`.
  // Keys must match the regex `/^[a-z0-9][a-z0-9_.-]{0,62}$/`.
  // Values must match the regex `/^[a-zA-Z0-9_./-]{0,255}$/`.
  map<string, string> state_metadata = 2;

  // Required. Fingerprint of the Recommendation. Provides optimistic locking.
  string etag = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `MarkRecommendationFailed` Method.
message MarkRecommendationFailedRequest {
  // Required. Name of the recommendation.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/Recommendation"
    }
  ];

  // State properties to include with this state. Overwrites any existing
  // `state_metadata`.
  // Keys must match the regex `/^[a-z0-9][a-z0-9_.-]{0,62}$/`.
  // Values must match the regex `/^[a-zA-Z0-9_./-]{0,255}$/`.
  map<string, string> state_metadata = 2;

  // Required. Fingerprint of the Recommendation. Provides optimistic locking.
  string etag = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the GetRecommenderConfig` method.
message GetRecommenderConfigRequest {
  // Required. Name of the Recommendation Config to get.
  //
  // Acceptable formats:
  //
  // * `projects/[PROJECT_NUMBER]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]/config`
  //
  // * `projects/[PROJECT_ID]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]/config`
  //
  // * `organizations/[ORGANIZATION_ID]/locations/[LOCATION]/recommenders/[RECOMMENDER_ID]/config`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/RecommenderConfig"
    }
  ];
}

// Request for the `UpdateRecommenderConfig` method.
message UpdateRecommenderConfigRequest {
  // Required. The RecommenderConfig to update.
  RecommenderConfig recommender_config = 1
      [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;

  // If true, validate the request and preview the change, but do not actually
  // update it.
  bool validate_only = 3;
}

// Request for the GetInsightTypeConfig` method.
message GetInsightTypeConfigRequest {
  // Required. Name of the InsightTypeConfig to get.
  //
  // Acceptable formats:
  //
  // * `projects/[PROJECT_NUMBER]/locations/global/recommenders/[INSIGHT_TYPE_ID]/config`
  //
  // * `projects/[PROJECT_ID]/locations/global/recommenders/[INSIGHT_TYPE_ID]/config`
  //
  // * `organizations/[ORGANIZATION_ID]/locations/global/recommenders/[INSIGHT_TYPE_ID]/config`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/InsightTypeConfig"
    }
  ];
}

// Request for the `UpdateInsightTypeConfig` method.
message UpdateInsightTypeConfigRequest {
  // Required. The InsightTypeConfig to update.
  InsightTypeConfig insight_type_config = 1
      [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;

  // If true, validate the request and preview the change, but do not actually
  // update it.
  bool validate_only = 3;
}

// Request for the `ListRecommender` method.
message ListRecommendersRequest {
  // The number of RecommenderTypes to return per page. The service may return
  // fewer than this value.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // A page token, received from a previous `ListRecommenders` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response for the `ListRecommender` method.
message ListRecommendersResponse {
  // The set of recommenders available
  repeated RecommenderType recommenders = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request for the `ListInsightTypes` method.
message ListInsightTypesRequest {
  // The number of InsightTypes to return per page. The service may return
  // fewer than this value.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // A page token, received from a previous `ListRecommenders` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response for the `ListInsightTypes` method.
message ListInsightTypesResponse {
  // The set of recommenders available
  repeated InsightType insight_types = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
