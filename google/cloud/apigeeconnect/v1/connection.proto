// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apigeeconnect.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.ApigeeConnect.V1";
option go_package = "cloud.google.com/go/apigeeconnect/apiv1/apigeeconnectpb;apigeeconnectpb";
option java_multiple_files = true;
option java_outer_classname = "ConnectionProto";
option java_package = "com.google.cloud.apigeeconnect.v1";
option php_namespace = "Google\\Cloud\\ApigeeConnect\\V1";
option ruby_package = "Google::Cloud::ApigeeConnect::V1";
option (google.api.resource_definition) = {
  type: "apigeeconnect.googleapis.com/Endpoint"
  pattern: "projects/{project}/endpoints/{endpoint}"
};

// Service Interface for the Apigee Connect connection management APIs.
service ConnectionService {
  option (google.api.default_host) = "apigeeconnect.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists connections that are currently active for the given Apigee Connect
  // endpoint.
  rpc ListConnections(ListConnectionsRequest) returns (ListConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/endpoints/*}/connections"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The request for [ListConnections][Management.ListConnections].
message ListConnectionsRequest {
  // Required. Parent name of the form:
  //     `projects/{project_number or project_id}/endpoints/{endpoint}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigeeconnect.googleapis.com/Endpoint"
    }
  ];

  // The maximum number of connections to return. The service may return fewer
  // than this value. If unspecified, at most 100 connections will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListConnections` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListConnections` must
  // match the call that provided the page token.
  string page_token = 3;
}

// The response for
// [ListConnections][Management.ListConnections].
message ListConnectionsResponse {
  // A list of clients.
  repeated Connection connections = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message Connection {
  // The endpoint that the connection is made against.
  // Format: `projects/{project_number}/endpoints/{endpoint}`
  string endpoint = 1;

  // Cluster information.
  Cluster cluster = 2;

  // The count of streams.
  int32 stream_count = 3;
}

message Cluster {
  // The name of the cluster.
  string name = 1;

  // The region of the cluster.
  string region = 2;
}
