# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "apigeeconnect_proto",
    srcs = [
        "connection.proto",
        "tether.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
    ],
)

proto_library_with_info(
    name = "apigeeconnect_proto_with_info",
    deps = [
        ":apigeeconnect_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "apigeeconnect_java_proto",
    deps = [":apigeeconnect_proto"],
)

java_grpc_library(
    name = "apigeeconnect_java_grpc",
    srcs = [":apigeeconnect_proto"],
    deps = [":apigeeconnect_java_proto"],
)

java_gapic_library(
    name = "apigeeconnect_java_gapic",
    srcs = [":apigeeconnect_proto_with_info"],
    grpc_service_config = "connection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apigeeconnect_v1.yaml",
    test_deps = [
        ":apigeeconnect_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":apigeeconnect_java_proto",
    ],
)

java_gapic_test(
    name = "apigeeconnect_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.apigeeconnect.v1.ConnectionServiceClientHttpJsonTest",
        "com.google.cloud.apigeeconnect.v1.ConnectionServiceClientTest",
        "com.google.cloud.apigeeconnect.v1.TetherClientTest",
    ],
    runtime_deps = [":apigeeconnect_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-apigeeconnect-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":apigeeconnect_java_gapic",
        ":apigeeconnect_java_grpc",
        ":apigeeconnect_java_proto",
        ":apigeeconnect_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "apigeeconnect_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/apigeeconnect/apiv1/apigeeconnectpb",
    protos = [":apigeeconnect_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "apigeeconnect_go_gapic",
    srcs = [":apigeeconnect_proto_with_info"],
    grpc_service_config = "connection_grpc_service_config.json",
    importpath = "cloud.google.com/go/apigeeconnect/apiv1;apigeeconnect",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "apigeeconnect_v1.yaml",
    deps = [
        ":apigeeconnect_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-apigeeconnect-v1-go",
    deps = [
        ":apigeeconnect_go_gapic",
        ":apigeeconnect_go_gapic_srcjar-metadata.srcjar",
        ":apigeeconnect_go_gapic_srcjar-snippets.srcjar",
        ":apigeeconnect_go_gapic_srcjar-test.srcjar",
        ":apigeeconnect_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "apigeeconnect_py_gapic",
    srcs = [":apigeeconnect_proto"],
    grpc_service_config = "connection_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-apigee-connect"],
    rest_numeric_enums = True,
    service_yaml = "apigeeconnect_v1.yaml",
    transport = "grpc",
)

py_test(
    name = "apigeeconnect_py_gapic_test",
    srcs = [
        "apigeeconnect_py_gapic_pytest.py",
        "apigeeconnect_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":apigeeconnect_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "apigeeconnect-v1-py",
    deps = [
        ":apigeeconnect_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "apigeeconnect_php_proto",
    deps = [":apigeeconnect_proto"],
)

php_gapic_library(
    name = "apigeeconnect_php_gapic",
    srcs = [":apigeeconnect_proto_with_info"],
    grpc_service_config = "connection_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "apigeeconnect_v1.yaml",
    transport = "grpc+rest",
    deps = [":apigeeconnect_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-apigeeconnect-v1-php",
    deps = [
        ":apigeeconnect_php_gapic",
        ":apigeeconnect_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "apigeeconnect_nodejs_gapic",
    package_name = "@google-cloud/apigee-connect",
    src = ":apigeeconnect_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "connection_grpc_service_config.json",
    package = "google.cloud.apigeeconnect.v1",
    rest_numeric_enums = True,
    service_yaml = "apigeeconnect_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "apigeeconnect-v1-nodejs",
    deps = [
        ":apigeeconnect_nodejs_gapic",
        ":apigeeconnect_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "apigeeconnect_ruby_proto",
    deps = [":apigeeconnect_proto"],
)

ruby_grpc_library(
    name = "apigeeconnect_ruby_grpc",
    srcs = [":apigeeconnect_proto"],
    deps = [":apigeeconnect_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "apigeeconnect_ruby_gapic",
    srcs = [":apigeeconnect_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-apigee_connect-v1",
        "ruby-cloud-env-prefix=APIGEE_CONNECT",
        "ruby-cloud-product-url=https://cloud.google.com/apigee/docs/hybrid/v1.4/apigee-connect",
        "ruby-cloud-api-id=apigeeconnect.googleapis.com",
        "ruby-cloud-api-shortname=apigeeconnect",
    ],
    grpc_service_config = "connection_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Apigee Connect allows the Apigee hybrid management plane to connect securely to the MART service in the runtime plane without requiring you to expose the MART endpoint on the internet. If you use Apigee Connect, you do not need to configure the MART ingress gateway with a host alias and an authorized DNS certificate.",
    ruby_cloud_title = "Apigee Connect V1",
    service_yaml = "apigeeconnect_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apigeeconnect_ruby_grpc",
        ":apigeeconnect_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-apigeeconnect-v1-ruby",
    deps = [
        ":apigeeconnect_ruby_gapic",
        ":apigeeconnect_ruby_grpc",
        ":apigeeconnect_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "apigeeconnect_csharp_proto",
    deps = [":apigeeconnect_proto"],
)

csharp_grpc_library(
    name = "apigeeconnect_csharp_grpc",
    srcs = [":apigeeconnect_proto"],
    deps = [":apigeeconnect_csharp_proto"],
)

csharp_gapic_library(
    name = "apigeeconnect_csharp_gapic",
    srcs = [":apigeeconnect_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "connection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apigeeconnect_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apigeeconnect_csharp_grpc",
        ":apigeeconnect_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-apigeeconnect-v1-csharp",
    deps = [
        ":apigeeconnect_csharp_gapic",
        ":apigeeconnect_csharp_grpc",
        ":apigeeconnect_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "apigeeconnect_cc_proto",
    deps = [":apigeeconnect_proto"],
)

cc_grpc_library(
    name = "apigeeconnect_cc_grpc",
    srcs = [":apigeeconnect_proto"],
    grpc_only = True,
    deps = [":apigeeconnect_cc_proto"],
)
