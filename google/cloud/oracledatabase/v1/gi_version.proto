// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "GiVersionProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the Oracle Grid Infrastructure (GI) version resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/GiVersionSummary/
message GiVersion {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/GiVersion"
    pattern: "projects/{project}/locations/{location}/giVersions/{gi_version}"
    plural: "giVersions"
    singular: "giVersion"
  };

  // Identifier. The name of the Oracle Grid Infrastructure (GI) version
  // resource with the format:
  // projects/{project}/locations/{region}/giVersions/{gi_versions}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. version
  string version = 2 [(google.api.field_behavior) = OPTIONAL];
}
