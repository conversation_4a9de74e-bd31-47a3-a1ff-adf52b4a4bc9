// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/type/datetime.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "CloudVmClusterProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the Cloud VM Cluster resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/CloudVmCluster/
message CloudVmCluster {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/CloudVmCluster"
    pattern: "projects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluster}"
    plural: "cloudVmClusters"
    singular: "cloudVmCluster"
  };

  // Identifier. The name of the VM Cluster resource with the format:
  // projects/{project}/locations/{region}/cloudVmClusters/{cloud_vm_cluster}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The name of the Exadata Infrastructure resource on which VM
  // cluster resource is created, in the following format:
  // projects/{project}/locations/{region}/cloudExadataInfrastuctures/{cloud_extradata_infrastructure}
  string exadata_infrastructure = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/CloudExadataInfrastructure"
    }
  ];

  // Optional. User friendly name for this resource.
  string display_name = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Google Cloud Platform location where Oracle Exadata is hosted.
  // It is same as Google Cloud Platform Oracle zone of Exadata infrastructure.
  string gcp_oracle_zone = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Various properties of the VM Cluster.
  CloudVmClusterProperties properties = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels or tags associated with the VM Cluster.
  map<string, string> labels = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The date and time that the VM cluster was created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Network settings. CIDR to use for cluster IP allocation.
  string cidr = 9 [(google.api.field_behavior) = REQUIRED];

  // Required. CIDR range of the backup subnet.
  string backup_subnet_cidr = 10 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the VPC network.
  // Format: projects/{project}/global/networks/{network}
  string network = 11 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];
}

// Various properties and settings associated with Exadata VM cluster.
message CloudVmClusterProperties {
  // Different licenses supported.
  enum LicenseType {
    // Unspecified
    LICENSE_TYPE_UNSPECIFIED = 0;

    // License included part of offer
    LICENSE_INCLUDED = 1;

    // Bring your own license
    BRING_YOUR_OWN_LICENSE = 2;
  }

  // Types of disk redundancy provided by Oracle.
  enum DiskRedundancy {
    // Unspecified.
    DISK_REDUNDANCY_UNSPECIFIED = 0;

    // High -  3 way mirror.
    HIGH = 1;

    // Normal - 2 way mirror.
    NORMAL = 2;
  }

  // The various lifecycle states of the VM cluster.
  enum State {
    // Default unspecified value.
    STATE_UNSPECIFIED = 0;

    // Indicates that the resource is in provisioning state.
    PROVISIONING = 1;

    // Indicates that the resource is in available state.
    AVAILABLE = 2;

    // Indicates that the resource is in updating state.
    UPDATING = 3;

    // Indicates that the resource is in terminating state.
    TERMINATING = 4;

    // Indicates that the resource is in terminated state.
    TERMINATED = 5;

    // Indicates that the resource is in failed state.
    FAILED = 6;

    // Indicates that the resource is in maintenance in progress state.
    MAINTENANCE_IN_PROGRESS = 7;
  }

  // Output only. Oracle Cloud Infrastructure ID of VM Cluster.
  string ocid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. License type of VM Cluster.
  LicenseType license_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Grid Infrastructure Version.
  string gi_version = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Time zone of VM Cluster to set. Defaults to UTC if not specified.
  google.type.TimeZone time_zone = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. SSH public keys to be stored with cluster.
  repeated string ssh_public_keys = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of database servers.
  int32 node_count = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Shape of VM Cluster.
  string shape = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. OCPU count per VM. Minimum is 0.1.
  float ocpu_count = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Memory allocated in GBs.
  int32 memory_size_gb = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Local storage per VM.
  int32 db_node_storage_size_gb = 10 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The storage allocation for the disk group, in gigabytes (GB).
  int32 storage_size_gb = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The data disk group size to be allocated in TBs.
  double data_storage_size_tb = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The type of redundancy.
  DiskRedundancy disk_redundancy = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Use exadata sparse snapshots.
  bool sparse_diskgroup_enabled = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Use local backup.
  bool local_backup_enabled = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Prefix for VM cluster host names.
  string hostname_prefix = 16 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Data collection options for diagnostics.
  DataCollectionOptions diagnostics_data_collection_options = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of the cluster.
  State state = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. SCAN listener port - TCP
  int32 scan_listener_port_tcp = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. SCAN listener port - TLS
  int32 scan_listener_port_tcp_ssl = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Parent DNS domain where SCAN DNS and hosts names are
  // qualified. ex: ocispdelegated.ocisp10jvnet.oraclevcn.com
  string domain = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. SCAN DNS name.
  // ex: sp2-yi0xq-scan.ocispdelegated.ocisp10jvnet.oraclevcn.com
  string scan_dns = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. host name without domain.
  // format: "<hostname_prefix>-" with some suffix.
  // ex: sp2-yi0xq where "sp2" is the hostname_prefix.
  string hostname = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Number of enabled CPU cores.
  int32 cpu_core_count = 26 [(google.api.field_behavior) = REQUIRED];

  // Optional. Operating system version of the image.
  string system_version = 27 [(google.api.field_behavior) = OPTIONAL];

  // Output only. OCIDs of scan IPs.
  repeated string scan_ip_ids = 28 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. OCID of scan DNS record.
  string scan_dns_record_id = 29 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Deep link to the OCI console to view this resource.
  string oci_url = 30 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. OCID of database servers.
  repeated string db_server_ocids = 31 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Compartment ID of cluster.
  string compartment_id = 32 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. DNS listener IP.
  string dns_listener_ip = 35 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. OCI Cluster name.
  string cluster_name = 36 [(google.api.field_behavior) = OPTIONAL];
}

// Data collection options for diagnostics.
message DataCollectionOptions {
  // Optional. Indicates whether diagnostic collection is enabled for the VM
  // cluster
  bool diagnostics_events_enabled = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates whether health monitoring is enabled for the VM cluster
  bool health_monitoring_enabled = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates whether incident logs and trace collection are enabled
  // for the VM cluster
  bool incident_logs_enabled = 3 [(google.api.field_behavior) = OPTIONAL];
}
