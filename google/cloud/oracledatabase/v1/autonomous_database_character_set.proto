// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "AutonomousDatabaseCharacterSetProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the Autonomous Database character set resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/AutonomousDatabaseCharacterSets/
message AutonomousDatabaseCharacterSet {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/AutonomousDatabaseCharacterSet"
    pattern: "projects/{project}/locations/{location}/autonomousDatabaseCharacterSets/{autonomous_database_character_set}"
    plural: "autonomousDatabaseCharacterSets"
    singular: "autonomousDatabaseCharacterSet"
  };

  // The type of character set an Autonomous Database can have.
  enum CharacterSetType {
    // Character set type is not specified.
    CHARACTER_SET_TYPE_UNSPECIFIED = 0;

    // Character set type is set to database.
    DATABASE = 1;

    // Character set type is set to national.
    NATIONAL = 2;
  }

  // Identifier. The name of the Autonomous Database Character Set resource in
  // the following format:
  // projects/{project}/locations/{region}/autonomousDatabaseCharacterSets/{autonomous_database_character_set}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The character set type for the Autonomous Database.
  CharacterSetType character_set_type = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The character set name for the Autonomous Database which is
  // the ID in the resource name.
  string character_set = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
