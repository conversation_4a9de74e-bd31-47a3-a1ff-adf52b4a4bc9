// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/oracledatabase/v1/common.proto";
import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "google/type/month.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "CloudExadataInfrastructureProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Represents CloudExadataInfrastructure resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/CloudExadataInfrastructure/
message CloudExadataInfrastructure {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/CloudExadataInfrastructure"
    pattern: "projects/{project}/locations/{location}/cloudExadataInfrastructures/{cloud_exadata_infrastructure}"
    plural: "cloudExadataInfrastructures"
    singular: "cloudExadataInfrastructure"
  };

  // Identifier. The name of the Exadata Infrastructure resource with the
  // format:
  // projects/{project}/locations/{region}/cloudExadataInfrastructures/{cloud_exadata_infrastructure}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. User friendly name for this resource.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Google Cloud Platform location where Oracle Exadata is hosted.
  string gcp_oracle_zone = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Entitlement ID of the private offer against which this
  // infrastructure resource is provisioned.
  string entitlement_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Various properties of the infra.
  CloudExadataInfrastructureProperties properties = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels or tags associated with the resource.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The date and time that the Exadata Infrastructure was created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Various properties of Exadata Infrastructure.
message CloudExadataInfrastructureProperties {
  // The various lifecycle states of the Exadata Infrastructure.
  enum State {
    // Default unspecified value.
    STATE_UNSPECIFIED = 0;

    // The Exadata Infrastructure is being provisioned.
    PROVISIONING = 1;

    // The Exadata Infrastructure is available for use.
    AVAILABLE = 2;

    // The Exadata Infrastructure is being updated.
    UPDATING = 3;

    // The Exadata Infrastructure is being terminated.
    TERMINATING = 4;

    // The Exadata Infrastructure is terminated.
    TERMINATED = 5;

    // The Exadata Infrastructure is in failed state.
    FAILED = 6;

    // The Exadata Infrastructure is in maintenance.
    MAINTENANCE_IN_PROGRESS = 7;
  }

  // Output only. OCID of created infra.
  // https://docs.oracle.com/en-us/iaas/Content/General/Concepts/identifiers.htm#Oracle
  string ocid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The number of compute servers for the Exadata Infrastructure.
  int32 compute_count = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of Cloud Exadata storage servers for the Exadata
  // Infrastructure.
  int32 storage_count = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The total storage allocated to the Exadata Infrastructure
  // resource, in gigabytes (GB).
  int32 total_storage_size_gb = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The available storage can be allocated to the Exadata
  // Infrastructure resource, in gigabytes (GB).
  int32 available_storage_size_gb = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Maintenance window for repair.
  MaintenanceWindow maintenance_window = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current lifecycle state of the Exadata Infrastructure.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The shape of the Exadata Infrastructure. The shape determines the
  // amount of CPU, storage, and memory resources allocated to the instance.
  string shape = 8 [(google.api.field_behavior) = REQUIRED];

  // Output only. Deep link to the OCI console to view this resource.
  string oci_url = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of enabled CPU cores.
  int32 cpu_count = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total number of CPU cores available.
  int32 max_cpu_count = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The memory allocated in GBs.
  int32 memory_size_gb = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total memory available in GBs.
  int32 max_memory_gb = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The local node storage allocated in GBs.
  int32 db_node_storage_size_gb = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total local node storage available in GBs.
  int32 max_db_node_storage_size_gb = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Size, in terabytes, of the DATA disk group.
  double data_storage_size_tb = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total available DATA disk group size.
  double max_data_storage_tb = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The requested number of additional storage servers activated
  // for the Exadata Infrastructure.
  int32 activated_storage_count = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The requested number of additional storage servers for the
  // Exadata Infrastructure.
  int32 additional_storage_count = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The software version of the database servers (dom0) in the
  // Exadata Infrastructure.
  string db_server_version = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The software version of the storage servers (cells) in the
  // Exadata Infrastructure.
  string storage_server_version = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The OCID of the next maintenance run.
  string next_maintenance_run_id = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the next maintenance run will occur.
  google.protobuf.Timestamp next_maintenance_run_time = 23
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the next security maintenance run will occur.
  google.protobuf.Timestamp next_security_maintenance_run_time = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The list of customer contacts.
  repeated CustomerContact customer_contacts = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The monthly software version of the storage servers (cells)
  // in the Exadata Infrastructure. Example: 20.1.15
  string monthly_storage_server_version = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The monthly software version of the database servers (dom0)
  // in the Exadata Infrastructure. Example: 20.1.15
  string monthly_db_server_version = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Maintenance window as defined by Oracle.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/datatypes/MaintenanceWindow
message MaintenanceWindow {
  // Maintenance window preference.
  enum MaintenanceWindowPreference {
    // Default unspecified value.
    MAINTENANCE_WINDOW_PREFERENCE_UNSPECIFIED = 0;

    // Custom preference.
    CUSTOM_PREFERENCE = 1;

    // No preference.
    NO_PREFERENCE = 2;
  }

  // Patching mode.
  enum PatchingMode {
    // Default unspecified value.
    PATCHING_MODE_UNSPECIFIED = 0;

    // Updates the Cloud Exadata database server hosts in a rolling fashion.
    ROLLING = 1;

    // The non-rolling maintenance method first updates your storage servers at
    // the same time, then your database servers at the same time.
    NON_ROLLING = 2;
  }

  // Optional. The maintenance window scheduling preference.
  MaintenanceWindowPreference preference = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Months during the year when maintenance should be performed.
  repeated google.type.Month months = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Weeks during the month when maintenance should be performed.
  // Weeks start on the 1st, 8th, 15th, and 22nd days of the month, and have a
  // duration of 7 days. Weeks start and end based on calendar dates, not days
  // of the week.
  repeated int32 weeks_of_month = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Days during the week when maintenance should be performed.
  repeated google.type.DayOfWeek days_of_week = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The window of hours during the day when maintenance should be
  // performed. The window is a 4 hour slot. Valid values are:
  //   0 - represents time slot 0:00 - 3:59 UTC
  //   4 - represents time slot 4:00 - 7:59 UTC
  //   8 - represents time slot 8:00 - 11:59 UTC
  //   12 - represents time slot 12:00 - 15:59 UTC
  //   16 - represents time slot 16:00 - 19:59 UTC
  //   20 - represents time slot 20:00 - 23:59 UTC
  repeated int32 hours_of_day = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Lead time window allows user to set a lead time to prepare for a
  // down time. The lead time is in weeks and valid value is between 1 to 4.
  int32 lead_time_week = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Cloud CloudExadataInfrastructure node patching method, either
  // "ROLLING"
  //  or "NONROLLING". Default value is ROLLING.
  PatchingMode patching_mode = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Determines the amount of time the system will wait before the
  // start of each database server patching operation. Custom action timeout is
  // in minutes and valid value is between 15 to 120 (inclusive).
  int32 custom_action_timeout_mins = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, enables the configuration of a custom action timeout
  // (waiting period) between database server patching operations.
  bool is_custom_action_timeout_enabled = 9
      [(google.api.field_behavior) = OPTIONAL];
}
