// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/oracledatabase/v1/autonomous_database.proto";
import "google/cloud/oracledatabase/v1/autonomous_database_character_set.proto";
import "google/cloud/oracledatabase/v1/autonomous_db_backup.proto";
import "google/cloud/oracledatabase/v1/autonomous_db_version.proto";
import "google/cloud/oracledatabase/v1/db_node.proto";
import "google/cloud/oracledatabase/v1/db_server.proto";
import "google/cloud/oracledatabase/v1/db_system_shape.proto";
import "google/cloud/oracledatabase/v1/entitlement.proto";
import "google/cloud/oracledatabase/v1/exadata_infra.proto";
import "google/cloud/oracledatabase/v1/gi_version.proto";
import "google/cloud/oracledatabase/v1/vm_cluster.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "V1mainProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};

// Service describing handlers for resources
service OracleDatabase {
  option (google.api.default_host) = "oracledatabase.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Exadata Infrastructures in a given project and location.
  rpc ListCloudExadataInfrastructures(ListCloudExadataInfrastructuresRequest)
      returns (ListCloudExadataInfrastructuresResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/cloudExadataInfrastructures"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Exadata Infrastructure.
  rpc GetCloudExadataInfrastructure(GetCloudExadataInfrastructureRequest)
      returns (CloudExadataInfrastructure) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/cloudExadataInfrastructures/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Exadata Infrastructure in a given project and location.
  rpc CreateCloudExadataInfrastructure(CreateCloudExadataInfrastructureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/cloudExadataInfrastructures"
      body: "cloud_exadata_infrastructure"
    };
    option (google.api.method_signature) =
        "parent,cloud_exadata_infrastructure,cloud_exadata_infrastructure_id";
    option (google.longrunning.operation_info) = {
      response_type: "CloudExadataInfrastructure"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Exadata Infrastructure.
  rpc DeleteCloudExadataInfrastructure(DeleteCloudExadataInfrastructureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/cloudExadataInfrastructures/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the VM Clusters in a given project and location.
  rpc ListCloudVmClusters(ListCloudVmClustersRequest)
      returns (ListCloudVmClustersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/cloudVmClusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single VM Cluster.
  rpc GetCloudVmCluster(GetCloudVmClusterRequest) returns (CloudVmCluster) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/cloudVmClusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new VM Cluster in a given project and location.
  rpc CreateCloudVmCluster(CreateCloudVmClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/cloudVmClusters"
      body: "cloud_vm_cluster"
    };
    option (google.api.method_signature) =
        "parent,cloud_vm_cluster,cloud_vm_cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "CloudVmCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single VM Cluster.
  rpc DeleteCloudVmCluster(DeleteCloudVmClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/cloudVmClusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the entitlements in a given project.
  rpc ListEntitlements(ListEntitlementsRequest)
      returns (ListEntitlementsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/entitlements"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the database servers of an Exadata Infrastructure instance.
  rpc ListDbServers(ListDbServersRequest) returns (ListDbServersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/cloudExadataInfrastructures/*}/dbServers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the database nodes of a VM Cluster.
  rpc ListDbNodes(ListDbNodesRequest) returns (ListDbNodesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/cloudVmClusters/*}/dbNodes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all the valid Oracle Grid Infrastructure (GI) versions for the given
  // project and location.
  rpc ListGiVersions(ListGiVersionsRequest) returns (ListGiVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/giVersions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the database system shapes available for the project and location.
  rpc ListDbSystemShapes(ListDbSystemShapesRequest)
      returns (ListDbSystemShapesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/dbSystemShapes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the Autonomous Databases in a given project and location.
  rpc ListAutonomousDatabases(ListAutonomousDatabasesRequest)
      returns (ListAutonomousDatabasesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/autonomousDatabases"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a single Autonomous Database.
  rpc GetAutonomousDatabase(GetAutonomousDatabaseRequest)
      returns (AutonomousDatabase) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/autonomousDatabases/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Autonomous Database in a given project and location.
  rpc CreateAutonomousDatabase(CreateAutonomousDatabaseRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/autonomousDatabases"
      body: "autonomous_database"
    };
    option (google.api.method_signature) =
        "parent,autonomous_database,autonomous_database_id";
    option (google.longrunning.operation_info) = {
      response_type: "AutonomousDatabase"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Autonomous Database.
  rpc DeleteAutonomousDatabase(DeleteAutonomousDatabaseRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/autonomousDatabases/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Restores a single Autonomous Database.
  rpc RestoreAutonomousDatabase(RestoreAutonomousDatabaseRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/autonomousDatabases/*}:restore"
      body: "*"
    };
    option (google.api.method_signature) = "name,restore_time";
    option (google.longrunning.operation_info) = {
      response_type: "AutonomousDatabase"
      metadata_type: "OperationMetadata"
    };
  }

  // Generates a wallet for an Autonomous Database.
  rpc GenerateAutonomousDatabaseWallet(GenerateAutonomousDatabaseWalletRequest)
      returns (GenerateAutonomousDatabaseWalletResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/autonomousDatabases/*}:generateWallet"
      body: "*"
    };
    option (google.api.method_signature) = "name,type,is_regional,password";
  }

  // Lists all the available Autonomous Database versions for a project and
  // location.
  rpc ListAutonomousDbVersions(ListAutonomousDbVersionsRequest)
      returns (ListAutonomousDbVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/autonomousDbVersions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists Autonomous Database Character Sets in a given project and location.
  rpc ListAutonomousDatabaseCharacterSets(
      ListAutonomousDatabaseCharacterSetsRequest)
      returns (ListAutonomousDatabaseCharacterSetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/autonomousDatabaseCharacterSets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the long-term and automatic backups of an Autonomous Database.
  rpc ListAutonomousDatabaseBackups(ListAutonomousDatabaseBackupsRequest)
      returns (ListAutonomousDatabaseBackupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/autonomousDatabaseBackups"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The request for `CloudExadataInfrastructures.List`.
message ListCloudExadataInfrastructuresRequest {
  // Required. The parent value for CloudExadataInfrastructure in the following
  // format: projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/CloudExadataInfrastructure"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, at most 50 Exadata infrastructures will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `CloudExadataInfrastructures.list`.
message ListCloudExadataInfrastructuresResponse {
  // The list of Exadata Infrastructures.
  repeated CloudExadataInfrastructure cloud_exadata_infrastructures = 1;

  // A token for fetching next page of response.
  string next_page_token = 2;
}

// The request for `CloudExadataInfrastructure.Get`.
message GetCloudExadataInfrastructureRequest {
  // Required. The name of the Cloud Exadata Infrastructure in the following
  // format:
  // projects/{project}/locations/{location}/cloudExadataInfrastructures/{cloud_exadata_infrastructure}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/CloudExadataInfrastructure"
    }
  ];
}

// The request for `CloudExadataInfrastructure.Create`.
message CreateCloudExadataInfrastructureRequest {
  // Required. The parent value for CloudExadataInfrastructure in the following
  // format: projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/CloudExadataInfrastructure"
    }
  ];

  // Required. The ID of the Exadata Infrastructure to create. This value is
  // restricted to (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$) and must be a maximum of
  // 63 characters in length. The value must start with a letter and end with a
  // letter or a number.
  string cloud_exadata_infrastructure_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. Details of the Exadata Infrastructure instance to create.
  CloudExadataInfrastructure cloud_exadata_infrastructure = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional ID to identify the request. This value is used to
  // identify duplicate requests. If you make a request with the same request ID
  // and the original request is still in progress or completed, the server
  // ignores the second request. This prevents clients from
  // accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// The request for `CloudExadataInfrastructure.Delete`.
message DeleteCloudExadataInfrastructureRequest {
  // Required. The name of the Cloud Exadata Infrastructure in the following
  // format:
  // projects/{project}/locations/{location}/cloudExadataInfrastructures/{cloud_exadata_infrastructure}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/CloudExadataInfrastructure"
    }
  ];

  // Optional. An optional ID to identify the request. This value is used to
  // identify duplicate requests. If you make a request with the same request ID
  // and the original request is still in progress or completed, the server
  // ignores the second request. This prevents clients from
  // accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. If set to true, all VM clusters for this Exadata Infrastructure
  // will be deleted. An Exadata Infrastructure can only be deleted once all its
  // VM clusters have been deleted.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request for `CloudVmCluster.List`.
message ListCloudVmClustersRequest {
  // Required. The name of the parent in the following format:
  // projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/CloudVmCluster"
    }
  ];

  // Optional. The number of VM clusters to return.
  // If unspecified, at most 50 VM clusters will be returned.
  // The maximum value is 1,000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying the page of results the server returns.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression for filtering the results of the request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `CloudVmCluster.List`.
message ListCloudVmClustersResponse {
  // The list of VM Clusters.
  repeated CloudVmCluster cloud_vm_clusters = 1;

  // A token to fetch the next page of results.
  string next_page_token = 2;
}

// The request for `CloudVmCluster.Get`.
message GetCloudVmClusterRequest {
  // Required. The name of the Cloud VM Cluster in the following format:
  // projects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluster}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/CloudVmCluster"
    }
  ];
}

// The request for `CloudVmCluster.Create`.
message CreateCloudVmClusterRequest {
  // Required. The name of the parent in the following format:
  // projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/CloudVmCluster"
    }
  ];

  // Required. The ID of the VM Cluster to create. This value is restricted
  // to (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$) and must be a maximum of 63
  // characters in length. The value must start with a letter and end with
  // a letter or a number.
  string cloud_vm_cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  CloudVmCluster cloud_vm_cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional ID to identify the request. This value is used to
  // identify duplicate requests. If you make a request with the same request ID
  // and the original request is still in progress or completed, the server
  // ignores the second request. This prevents clients from
  // accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// The request for `CloudVmCluster.Delete`.
message DeleteCloudVmClusterRequest {
  // Required. The name of the Cloud VM Cluster in the following format:
  // projects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluster}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/CloudVmCluster"
    }
  ];

  // Optional. An optional ID to identify the request. This value is used to
  // identify duplicate requests. If you make a request with the same request ID
  // and the original request is still in progress or completed, the server
  // ignores the second request. This prevents clients from
  // accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. If set to true, all child resources for the VM Cluster will be
  // deleted. A VM Cluster can only be deleted once all its child resources have
  // been deleted.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request for `Entitlement.List`.
message ListEntitlementsRequest {
  // Required. The parent value for the entitlement in the following format:
  // projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/Entitlement"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, a maximum of 50 entitlements will be returned.
  // The maximum value is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `Entitlement.List`.
message ListEntitlementsResponse {
  // The list of Entitlements
  repeated Entitlement entitlements = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// The request for `DbServer.List`.
message ListDbServersRequest {
  // Required. The parent value for database server in the following format:
  // projects/{project}/locations/{location}/cloudExadataInfrastructures/{cloudExadataInfrastructure}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/DbServer"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, a maximum of 50 db servers will be returned.
  // The maximum value is 1000; values above 1000 will be reset to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `DbServer.List`.
message ListDbServersResponse {
  // The list of database servers.
  repeated DbServer db_servers = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// The request for `DbNode.List`.
message ListDbNodesRequest {
  // Required. The parent value for database node in the following format:
  // projects/{project}/locations/{location}/cloudVmClusters/{cloudVmCluster}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/DbNode"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, at most 50 db nodes will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the node should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `DbNode.List`.
message ListDbNodesResponse {
  // The list of DB Nodes
  repeated DbNode db_nodes = 1;

  // A token identifying a page of results the node should return.
  string next_page_token = 2;
}

// The request for `GiVersion.List`.
message ListGiVersionsRequest {
  // Required. The parent value for Grid Infrastructure Version in the following
  // format: Format: projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/GiVersion"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, a maximum of 50 Oracle Grid Infrastructure (GI) versions
  // will be returned. The maximum value is 1000; values above 1000 will be
  // reset to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `GiVersion.List`.
message ListGiVersionsResponse {
  // The list of Oracle Grid Infrastructure (GI) versions.
  repeated GiVersion gi_versions = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// The request for `DbSystemShape.List`.
message ListDbSystemShapesRequest {
  // Required. The parent value for Database System Shapes in the following
  // format: projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/DbSystemShape"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, at most 50 database system shapes will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `DbSystemShape.List`.
message ListDbSystemShapesResponse {
  // The list of Database System shapes.
  repeated DbSystemShape db_system_shapes = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the operation.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An estimated percentage of the operation that has been
  // completed at a given moment of time, between 0 and 100.
  double percent_complete = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request for `AutonomousDatabase.List`.
message ListAutonomousDatabasesRequest {
  // Required. The parent value for the Autonomous Database in the following
  // format: projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/AutonomousDatabase"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, at most 50 Autonomous Database will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression for filtering the results of the request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression for ordering the results of the request.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `AutonomousDatabase.List`.
message ListAutonomousDatabasesResponse {
  // The list of Autonomous Databases.
  repeated AutonomousDatabase autonomous_databases = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// The request for `AutonomousDatabase.Get`.
message GetAutonomousDatabaseRequest {
  // Required. The name of the Autonomous Database in the following format:
  // projects/{project}/locations/{location}/autonomousDatabases/{autonomous_database}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/AutonomousDatabase"
    }
  ];
}

// The request for `AutonomousDatabase.Create`.
message CreateAutonomousDatabaseRequest {
  // Required. The name of the parent in the following format:
  // projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/AutonomousDatabase"
    }
  ];

  // Required. The ID of the Autonomous Database to create. This value is
  // restricted to (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$) and must be a maximum of
  // 63 characters in length. The value must start with a letter and end with a
  // letter or a number.
  string autonomous_database_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Autonomous Database being created.
  AutonomousDatabase autonomous_database = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional ID to identify the request. This value is used to
  // identify duplicate requests. If you make a request with the same request ID
  // and the original request is still in progress or completed, the server
  // ignores the second request. This prevents clients from
  // accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// The request for `AutonomousDatabase.Delete`.
message DeleteAutonomousDatabaseRequest {
  // Required. The name of the resource in the following format:
  // projects/{project}/locations/{location}/autonomousDatabases/{autonomous_database}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/AutonomousDatabase"
    }
  ];

  // Optional. An optional ID to identify the request. This value is used to
  // identify duplicate requests. If you make a request with the same request ID
  // and the original request is still in progress or completed, the server
  // ignores the second request. This prevents clients from
  // accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// The request for `AutonomousDatabase.Restore`.
message RestoreAutonomousDatabaseRequest {
  // Required. The name of the Autonomous Database in the following format:
  // projects/{project}/locations/{location}/autonomousDatabases/{autonomous_database}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/AutonomousDatabase"
    }
  ];

  // Required. The time and date to restore the database to.
  google.protobuf.Timestamp restore_time = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request for `AutonomousDatabase.GenerateWallet`.
message GenerateAutonomousDatabaseWalletRequest {
  // Required. The name of the Autonomous Database in the following format:
  // projects/{project}/locations/{location}/autonomousDatabases/{autonomous_database}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/AutonomousDatabase"
    }
  ];

  // Optional. The type of wallet generation for the Autonomous Database. The
  // default value is SINGLE.
  GenerateType type = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. True when requesting regional connection strings in PDB connect
  // info, applicable to cross-region Data Guard only.
  bool is_regional = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. The password used to encrypt the keys inside the wallet. The
  // password must be a minimum of 8 characters.
  string password = 4 [(google.api.field_behavior) = REQUIRED];
}

// The response for `AutonomousDatabase.GenerateWallet`.
message GenerateAutonomousDatabaseWalletResponse {
  // Output only. The base64 encoded wallet files.
  bytes archive_content = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request for `AutonomousDbVersion.List`.
message ListAutonomousDbVersionsRequest {
  // Required. The parent value for the Autonomous Database in the following
  // format: projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/AutonomousDbVersion"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, at most 50 Autonomous DB Versions will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `AutonomousDbVersion.List`.
message ListAutonomousDbVersionsResponse {
  // The list of Autonomous Database versions.
  repeated AutonomousDbVersion autonomous_db_versions = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// The request for `AutonomousDatabaseCharacterSet.List`.
message ListAutonomousDatabaseCharacterSetsRequest {
  // Required. The parent value for the Autonomous Database in the following
  // format: projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/AutonomousDatabaseCharacterSet"
    }
  ];

  // Optional. The maximum number of items to return.
  // If unspecified, at most 50 Autonomous DB Character Sets will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression for filtering the results of the request. Only the
  // **character_set_type** field is supported in the following format:
  // `character_set_type="{characterSetType}"`. Accepted values include
  // `DATABASE` and `NATIONAL`.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `AutonomousDatabaseCharacterSet.List`.
message ListAutonomousDatabaseCharacterSetsResponse {
  // The list of Autonomous Database Character Sets.
  repeated AutonomousDatabaseCharacterSet autonomous_database_character_sets =
      1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// The request for `AutonomousDatabaseBackup.List`.
message ListAutonomousDatabaseBackupsRequest {
  // Required. The parent value for ListAutonomousDatabaseBackups in the
  // following format: projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oracledatabase.googleapis.com/AutonomousDatabaseBackup"
    }
  ];

  // Optional. An expression for filtering the results of the request. Only the
  // **autonomous_database_id** field is supported in the following format:
  // `autonomous_database_id="{autonomous_database_id}"`. The accepted values
  // must be a valid Autonomous Database ID, limited to the naming
  // restrictions of the ID: ^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
  // The ID must start with a letter, end with a letter or a number, and be
  // a maximum of 63 characters.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of items to return.
  // If unspecified, at most 50 Autonomous DB Backups will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response for `AutonomousDatabaseBackup.List`.
message ListAutonomousDatabaseBackupsResponse {
  // The list of Autonomous Database Backups.
  repeated AutonomousDatabaseBackup autonomous_database_backups = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}
