type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  python:
    package_name: google.cloud.oracledatabase_v1.gapic
  java:
    package_name: com.google.cloud.oracledatabase.v1
  go:
    package_name: cloud.google.com/go/cloud/oracledatabase/apiv1
  csharp:
    package_name: Google.Cloud.OracleDatabase.V1
  ruby:
    package_name: Google::Cloud::OracleDatabase::V1
  php:
    package_name: Google\Cloud\OracleDatabase\V1
  nodejs:
    package_name: oracledatabase.v1
    domain_layer_location: google-cloud
interfaces:
- name: google.cloud.oracledatabase.v1.OracleDatabase
  methods:
  - name: CreateCloudExadataInfrastructure
    long_running:
      initial_poll_delay_millis:  60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 600000  # 10 minutes
      total_poll_timeout_millis: 432000000  # 5 days
  - name: DeleteCloudExadataInfrastructure
    long_running:
      initial_poll_delay_millis:  60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 600000  # 10 minutes
      total_poll_timeout_millis: 432000000  # 5 days
  - name: Create<PERSON>loudVmCluster
    long_running:
      initial_poll_delay_millis:  60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 600000  # 10 minutes
      total_poll_timeout_millis: 432000000  # 5 days
  - name: DeleteCloudVmCluster
    long_running:
      initial_poll_delay_millis:  60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 600000  # 10 minutes
      total_poll_timeout_millis: 432000000  # 5 days
  - name: CreateAutonomousDatabase
    long_running:
      initial_poll_delay_millis:  60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 600000  # 10 minutes
      total_poll_timeout_millis: 432000000  # 5 days
  - name: DeleteAutonomousDatabase
    long_running:
      initial_poll_delay_millis:  60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 600000  # 10 minutes
      total_poll_timeout_millis: 432000000  # 5 days
  - name: RestoreAutonomousDatabase
    long_running:
      initial_poll_delay_millis:  60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 600000  # 10 minutes
      total_poll_timeout_millis: 432000000  # 5 days
