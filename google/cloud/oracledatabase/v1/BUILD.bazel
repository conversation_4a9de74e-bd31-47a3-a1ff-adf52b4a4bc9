# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "oracledatabase_proto",
    srcs = [
        "autonomous_database.proto",
        "autonomous_database_character_set.proto",
        "autonomous_db_backup.proto",
        "autonomous_db_version.proto",
        "common.proto",
        "db_node.proto",
        "db_server.proto",
        "db_system_shape.proto",
        "entitlement.proto",
        "exadata_infra.proto",
        "gi_version.proto",
        "location_metadata.proto",
        "oracledatabase.proto",
        "vm_cluster.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:datetime_proto",
        "//google/type:dayofweek_proto",
        "//google/type:month_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "oracledatabase_proto_with_info",
    deps = [
        ":oracledatabase_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "oracledatabase_java_proto",
    deps = [":oracledatabase_proto"],
)

java_grpc_library(
    name = "oracledatabase_java_grpc",
    srcs = [":oracledatabase_proto"],
    deps = [":oracledatabase_java_proto"],
)

java_gapic_library(
    name = "oracledatabase_java_gapic",
    srcs = [":oracledatabase_proto_with_info"],
    gapic_yaml = "oracledatabase_gapic.yaml",
    grpc_service_config = "oracledatabase_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "oracledatabase_v1.yaml",
    test_deps = [
        ":oracledatabase_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "rest",
    deps = [
        ":oracledatabase_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "oracledatabase_java_gapic_test_suite",
    test_classes = [
        # "com.google.cloud.oracledatabase.v1.OracleDatabaseClientHttpJsonTest",
        "com.google.cloud.oracledatabase.v1.OracleDatabaseClientTest",
    ],
    runtime_deps = [":oracledatabase_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-oracledatabase-v1-java",
    include_samples = True,
    transport = "rest",
    deps = [
        ":oracledatabase_java_gapic",
        ":oracledatabase_java_grpc",
        ":oracledatabase_java_proto",
        ":oracledatabase_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "oracledatabase_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb",
    protos = [":oracledatabase_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:datetime_go_proto",
        "//google/type:dayofweek_go_proto",
        "//google/type:month_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "oracledatabase_go_gapic",
    srcs = [":oracledatabase_proto_with_info"],
    grpc_service_config = "oracledatabase_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/oracledatabase/apiv1;oracledatabase",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "oracledatabase_v1.yaml",
    transport = "rest",
    deps = [
        ":oracledatabase_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-oracledatabase-v1-go",
    deps = [
        ":oracledatabase_go_gapic",
        ":oracledatabase_go_gapic_srcjar-metadata.srcjar",
        ":oracledatabase_go_gapic_srcjar-snippets.srcjar",
        ":oracledatabase_go_gapic_srcjar-test.srcjar",
        ":oracledatabase_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "oracledatabase_py_gapic",
    srcs = [":oracledatabase_proto"],
    grpc_service_config = "oracledatabase_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "oracledatabase_v1.yaml",
    transport = "rest",
    deps = [
    ],
)

py_test(
    name = "oracledatabase_py_gapic_test",
    srcs = [
        "oracledatabase_py_gapic_pytest.py",
        "oracledatabase_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":oracledatabase_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "oracledatabase-v1-py",
    deps = [
        ":oracledatabase_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "oracledatabase_php_proto",
    deps = [":oracledatabase_proto"],
)

php_gapic_library(
    name = "oracledatabase_php_gapic",
    srcs = [":oracledatabase_proto_with_info"],
    grpc_service_config = "oracledatabase_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "oracledatabase_v1.yaml",
    gapic_yaml = "oracledatabase_gapic.yaml",
    transport = "rest",
    deps = [
        ":oracledatabase_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-oracledatabase-v1-php",
    deps = [
        ":oracledatabase_php_gapic",
        ":oracledatabase_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "oracledatabase_nodejs_gapic",
    package_name = "@google-cloud/oracledatabase",
    src = ":oracledatabase_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "oracledatabase_v1_grpc_service_config.json",
    package = "google.cloud.oracledatabase.v1",
    rest_numeric_enums = True,
    service_yaml = "oracledatabase_v1.yaml",
    transport = "rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "oracledatabase-v1-nodejs",
    deps = [
        ":oracledatabase_nodejs_gapic",
        ":oracledatabase_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "oracledatabase_ruby_proto",
    deps = [":oracledatabase_proto"],
)

ruby_grpc_library(
    name = "oracledatabase_ruby_grpc",
    srcs = [":oracledatabase_proto"],
    deps = [":oracledatabase_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "oracledatabase_ruby_gapic",
    srcs = [":oracledatabase_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-oracle_database-v1"],
    grpc_service_config = "oracledatabase_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "oracledatabase_v1.yaml",
    transport = "rest",
    deps = [
        ":oracledatabase_ruby_grpc",
        ":oracledatabase_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-oracledatabase-v1-ruby",
    deps = [
        ":oracledatabase_ruby_gapic",
        ":oracledatabase_ruby_grpc",
        ":oracledatabase_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "oracledatabase_csharp_proto",
    deps = [":oracledatabase_proto"],
)

csharp_grpc_library(
    name = "oracledatabase_csharp_grpc",
    srcs = [":oracledatabase_proto"],
    deps = [":oracledatabase_csharp_proto"],
)

csharp_gapic_library(
    name = "oracledatabase_csharp_gapic",
    srcs = [":oracledatabase_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "oracledatabase_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "oracledatabase_v1.yaml",
    transport = "rest",
    deps = [
        ":oracledatabase_csharp_grpc",
        ":oracledatabase_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-oracledatabase-v1-csharp",
    deps = [
        ":oracledatabase_csharp_gapic",
        ":oracledatabase_csharp_grpc",
        ":oracledatabase_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "oracledatabase_cc_proto",
    deps = [":oracledatabase_proto"],
)

cc_grpc_library(
    name = "oracledatabase_cc_grpc",
    srcs = [":oracledatabase_proto"],
    grpc_only = True,
    deps = [":oracledatabase_cc_proto"],
)
