// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "DbNodeProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the database node resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/DbNode/
message DbNode {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/DbNode"
    pattern: "projects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluster}/dbNodes/{db_node}"
    plural: "dbNodes"
    singular: "dbNode"
  };

  // Identifier. The name of the database node resource in the following format:
  // projects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluster}/dbNodes/{db_node}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Various properties of the database node.
  DbNodeProperties properties = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Various properties and settings associated with Db node.
message DbNodeProperties {
  // The various lifecycle states of the database node.
  enum State {
    // Default unspecified value.
    STATE_UNSPECIFIED = 0;

    // Indicates that the resource is in provisioning state.
    PROVISIONING = 1;

    // Indicates that the resource is in available state.
    AVAILABLE = 2;

    // Indicates that the resource is in updating state.
    UPDATING = 3;

    // Indicates that the resource is in stopping state.
    STOPPING = 4;

    // Indicates that the resource is in stopped state.
    STOPPED = 5;

    // Indicates that the resource is in starting state.
    STARTING = 6;

    // Indicates that the resource is in terminating state.
    TERMINATING = 7;

    // Indicates that the resource is in terminated state.
    TERMINATED = 8;

    // Indicates that the resource is in failed state.
    FAILED = 9;
  }

  // Output only. OCID of database node.
  string ocid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. OCPU count per database node.
  int32 ocpu_count = 2 [(google.api.field_behavior) = OPTIONAL];

  // Memory allocated in GBs.
  int32 memory_size_gb = 3;

  // Optional. Local storage per database node.
  int32 db_node_storage_size_gb = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Database server OCID.
  string db_server_ocid = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. DNS
  string hostname = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of the database node.
  State state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Total CPU core count of the database node.
  int32 total_cpu_core_count = 10;
}
