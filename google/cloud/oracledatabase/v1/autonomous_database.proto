// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/oracledatabase/v1/common.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "AutonomousDatabaseProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// The type of wallet generation.
enum GenerateType {
  // Default unspecified value.
  GENERATE_TYPE_UNSPECIFIED = 0;

  // Used to generate wallet for all databases in the region.
  ALL = 1;

  // Used to generate wallet for a single database.
  SINGLE = 2;
}

// The various lifecycle states of the Autonomous Database.
enum State {
  // Default unspecified value.
  STATE_UNSPECIFIED = 0;

  // Indicates that the Autonomous Database is in provisioning state.
  PROVISIONING = 1;

  // Indicates that the Autonomous Database is in available state.
  AVAILABLE = 2;

  // Indicates that the Autonomous Database is in stopping state.
  STOPPING = 3;

  // Indicates that the Autonomous Database is in stopped state.
  STOPPED = 4;

  // Indicates that the Autonomous Database is in starting state.
  STARTING = 5;

  // Indicates that the Autonomous Database is in terminating state.
  TERMINATING = 6;

  // Indicates that the Autonomous Database is in terminated state.
  TERMINATED = 7;

  // Indicates that the Autonomous Database is in unavailable state.
  UNAVAILABLE = 8;

  // Indicates that the Autonomous Database restore is in progress.
  RESTORE_IN_PROGRESS = 9;

  // Indicates that the Autonomous Database failed to restore.
  RESTORE_FAILED = 10;

  // Indicates that the Autonomous Database backup is in progress.
  BACKUP_IN_PROGRESS = 11;

  // Indicates that the Autonomous Database scale is in progress.
  SCALE_IN_PROGRESS = 12;

  // Indicates that the Autonomous Database is available but needs attention
  // state.
  AVAILABLE_NEEDS_ATTENTION = 13;

  // Indicates that the Autonomous Database is in updating state.
  UPDATING = 14;

  // Indicates that the Autonomous Database's maintenance is in progress state.
  MAINTENANCE_IN_PROGRESS = 15;

  // Indicates that the Autonomous Database is in restarting state.
  RESTARTING = 16;

  // Indicates that the Autonomous Database is in recreating state.
  RECREATING = 17;

  // Indicates that the Autonomous Database's role change is in progress state.
  ROLE_CHANGE_IN_PROGRESS = 18;

  // Indicates that the Autonomous Database is in upgrading state.
  UPGRADING = 19;

  // Indicates that the Autonomous Database is in inaccessible state.
  INACCESSIBLE = 20;

  // Indicates that the Autonomous Database is in standby state.
  STANDBY = 21;
}

// The state of the Operations Insights for this Autonomous Database.
enum OperationsInsightsState {
  // Default unspecified value.
  OPERATIONS_INSIGHTS_STATE_UNSPECIFIED = 0;

  // Enabling status for operation insights.
  ENABLING = 1;

  // Enabled status for operation insights.
  ENABLED = 2;

  // Disabling status for operation insights.
  DISABLING = 3;

  // Not Enabled status for operation insights.
  NOT_ENABLED = 4;

  // Failed enabling status for operation insights.
  FAILED_ENABLING = 5;

  // Failed disabling status for operation insights.
  FAILED_DISABLING = 6;
}

// The various states available for the Autonomous Database workload type.
enum DBWorkload {
  // Default unspecified value.
  DB_WORKLOAD_UNSPECIFIED = 0;

  // Autonomous Transaction Processing database.
  OLTP = 1;

  // Autonomous Data Warehouse database.
  DW = 2;

  // Autonomous JSON Database.
  AJD = 3;

  // Autonomous Database with the Oracle APEX Application Development workload
  // type.
  APEX = 4;
}

// Details of the Autonomous Database resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/AutonomousDatabase/
message AutonomousDatabase {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/AutonomousDatabase"
    pattern: "projects/{project}/locations/{location}/autonomousDatabases/{autonomous_database}"
    plural: "autonomousDatabases"
    singular: "autonomousDatabase"
  };

  // Identifier. The name of the Autonomous Database resource in the following
  // format:
  // projects/{project}/locations/{region}/autonomousDatabases/{autonomous_database}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The name of the Autonomous Database. The database name must be
  // unique in the project. The name must begin with a letter and can contain a
  // maximum of 30 alphanumeric characters.
  string database = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The display name for the Autonomous Database. The name does not
  // have to be unique within your project.
  string display_name = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The ID of the subscription entitlement associated with the
  // Autonomous Database.
  string entitlement_id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The password for the default ADMIN user.
  string admin_password = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The properties of the Autonomous Database.
  AutonomousDatabaseProperties properties = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels or tags associated with the Autonomous Database.
  map<string, string> labels = 8 [(google.api.field_behavior) = OPTIONAL];

  // Required. The name of the VPC network used by the Autonomous Database in
  // the following format: projects/{project}/global/networks/{network}
  string network = 9 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Required. The subnet CIDR range for the Autonmous Database.
  string cidr = 10 [(google.api.field_behavior) = REQUIRED];

  // Output only. The date and time that the Autonomous Database was created.
  google.protobuf.Timestamp create_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The properties of an Autonomous Database.
message AutonomousDatabaseProperties {
  // The editions available for the Autonomous Database.
  enum DatabaseEdition {
    // Default unspecified value.
    DATABASE_EDITION_UNSPECIFIED = 0;

    // Standard Database Edition
    STANDARD_EDITION = 1;

    // Enterprise Database Edition
    ENTERPRISE_EDITION = 2;
  }

  // The license types available for the Autonomous Database.
  enum LicenseType {
    // Unspecified
    LICENSE_TYPE_UNSPECIFIED = 0;

    // License included part of offer
    LICENSE_INCLUDED = 1;

    // Bring your own license
    BRING_YOUR_OWN_LICENSE = 2;
  }

  // The available maintenance schedules for the Autonomous Database.
  enum MaintenanceScheduleType {
    // Default unspecified value.
    MAINTENANCE_SCHEDULE_TYPE_UNSPECIFIED = 0;

    // An EARLY maintenance schedule patches the database before
    // the regular scheduled maintenance.
    EARLY = 1;

    // A REGULAR maintenance schedule follows the normal maintenance cycle.
    REGULAR = 2;
  }

  // The types of local disaster recovery available for an Autonomous Database.
  enum LocalDisasterRecoveryType {
    // Default unspecified value.
    LOCAL_DISASTER_RECOVERY_TYPE_UNSPECIFIED = 0;

    // Autonomous Data Guard recovery.
    ADG = 1;

    // Backup based recovery.
    BACKUP_BASED = 2;
  }

  // Varies states of the Data Safe registration for the Autonomous Database.
  enum DataSafeState {
    // Default unspecified value.
    DATA_SAFE_STATE_UNSPECIFIED = 0;

    // Registering data safe state.
    REGISTERING = 1;

    // Registered data safe state.
    REGISTERED = 2;

    // Deregistering data safe state.
    DEREGISTERING = 3;

    // Not registered data safe state.
    NOT_REGISTERED = 4;

    // Failed data safe state.
    FAILED = 5;
  }

  // The different states of database management for an Autonomous Database.
  enum DatabaseManagementState {
    // Default unspecified value.
    DATABASE_MANAGEMENT_STATE_UNSPECIFIED = 0;

    // Enabling Database Management state
    ENABLING = 1;

    // Enabled Database Management state
    ENABLED = 2;

    // Disabling Database Management state
    DISABLING = 3;

    // Not Enabled Database Management state
    NOT_ENABLED = 4;

    // Failed enabling Database Management state
    FAILED_ENABLING = 5;

    // Failed disabling Database Management state
    FAILED_DISABLING = 6;
  }

  // This field indicates the modes of an Autonomous Database.
  enum OpenMode {
    // Default unspecified value.
    OPEN_MODE_UNSPECIFIED = 0;

    // Read Only Mode
    READ_ONLY = 1;

    // Read Write Mode
    READ_WRITE = 2;
  }

  // The types of permission levels for an Autonomous Database.
  enum PermissionLevel {
    // Default unspecified value.
    PERMISSION_LEVEL_UNSPECIFIED = 0;

    // Restricted mode allows access only by admin users.
    RESTRICTED = 1;

    // Normal access.
    UNRESTRICTED = 2;
  }

  // The refresh mode of the cloned Autonomous Database.
  enum RefreshableMode {
    // The default unspecified value.
    REFRESHABLE_MODE_UNSPECIFIED = 0;

    // AUTOMATIC indicates that the cloned database is automatically
    // refreshed with data from the source Autonomous Database.
    AUTOMATIC = 1;

    // MANUAL indicates that the cloned database is manually refreshed with
    // data from the source Autonomous Database.
    MANUAL = 2;
  }

  // The refresh state of the cloned Autonomous Database.
  enum RefreshableState {
    // Default unspecified value.
    REFRESHABLE_STATE_UNSPECIFIED = 0;

    // Refreshing
    REFRESHING = 1;

    // Not refreshed
    NOT_REFRESHING = 2;
  }

  // The Data Guard role of the Autonomous Database.
  enum Role {
    // Default unspecified value.
    ROLE_UNSPECIFIED = 0;

    // Primary role
    PRIMARY = 1;

    // Standby role
    STANDBY = 2;

    // Disabled standby role
    DISABLED_STANDBY = 3;

    // Backup copy role
    BACKUP_COPY = 4;

    // Snapshot standby role
    SNAPSHOT_STANDBY = 5;
  }

  // Output only. OCID of the Autonomous Database.
  // https://docs.oracle.com/en-us/iaas/Content/General/Concepts/identifiers.htm#Oracle
  string ocid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The number of compute servers for the Autonomous Database.
  float compute_count = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of CPU cores to be made available to the database.
  int32 cpu_core_count = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The size of the data stored in the database, in terabytes.
  int32 data_storage_size_tb = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The size of the data stored in the database, in gigabytes.
  int32 data_storage_size_gb = 63 [(google.api.field_behavior) = OPTIONAL];

  // Required. The workload type of the Autonomous Database.
  DBWorkload db_workload = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. The edition of the Autonomous Databases.
  DatabaseEdition db_edition = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The character set for the Autonomous Database. The default is
  // AL32UTF8.
  string character_set = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The national character set for the Autonomous Database. The
  // default is AL16UTF16.
  string n_character_set = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The private endpoint IP address for the Autonomous Database.
  string private_endpoint_ip = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The private endpoint label for the Autonomous Database.
  string private_endpoint_label = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Oracle Database version for the Autonomous Database.
  string db_version = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field indicates if auto scaling is enabled for the
  // Autonomous Database CPU core count.
  bool is_auto_scaling_enabled = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field indicates if auto scaling is enabled for the
  // Autonomous Database storage.
  bool is_storage_auto_scaling_enabled = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The license type used for the Autonomous Database.
  LicenseType license_type = 16 [(google.api.field_behavior) = REQUIRED];

  // Optional. The list of customer contacts.
  repeated CustomerContact customer_contacts = 17
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The ID of the Oracle Cloud Infrastructure vault secret.
  string secret_id = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The ID of the Oracle Cloud Infrastructure vault.
  string vault_id = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maintenance schedule of the Autonomous Database.
  MaintenanceScheduleType maintenance_schedule_type = 20
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field specifies if the Autonomous Database requires mTLS
  // connections.
  bool mtls_connection_required = 34 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The retention period for the Autonomous Database. This field is
  // specified in days, can range from 1 day to 60 days, and has a default value
  // of 60 days.
  int32 backup_retention_period_days = 57
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The amount of storage currently being used for user and system
  // data, in terabytes.
  double actual_used_data_storage_size_tb = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The amount of storage currently allocated for the database
  // tables and billed for, rounded up in terabytes.
  double allocated_storage_size_tb = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The details for the Oracle APEX Application Development.
  AutonomousDatabaseApex apex_details = 23
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates the status of Data Guard and Access
  // control for the Autonomous Database. The field's value is null if Data
  // Guard is disabled or Access Control is disabled. The field's value is TRUE
  // if both Data Guard and Access Control are enabled, and the Autonomous
  // Database is using primary IP access control list (ACL) for standby. The
  // field's value is FALSE if both Data Guard and Access Control are enabled,
  // and the Autonomous Database is using a different IP access control list
  // (ACL) for standby compared to primary.
  optional bool are_primary_allowlisted_ips_used = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The details of the current lifestyle state of the Autonomous
  // Database.
  string lifecycle_details = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current lifecycle state of the Autonomous Database.
  State state = 26 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Autonomous Container Database OCID.
  string autonomous_container_database_id = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of available Oracle Database upgrade versions for an
  // Autonomous Database.
  repeated string available_upgrade_versions = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The connection strings used to connect to an Autonomous
  // Database.
  AutonomousDatabaseConnectionStrings connection_strings = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Oracle Connection URLs for an Autonomous Database.
  AutonomousDatabaseConnectionUrls connection_urls = 30
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates the number of seconds of data loss during
  // a Data Guard failover.
  google.protobuf.Duration failed_data_recovery_duration = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The memory assigned to in-memory tables in an Autonomous
  // Database.
  int32 memory_table_gbs = 32 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates whether the Autonomous Database has local
  // (in-region) Data Guard enabled.
  bool is_local_data_guard_enabled = 33
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates the maximum data loss limit for an
  // Autonomous Database, in seconds.
  int32 local_adg_auto_failover_max_data_loss_limit = 35
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The details of the Autonomous Data Guard standby database.
  AutonomousDatabaseStandbySummary local_standby_db = 36
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The amount of memory enabled per ECPU, in gigabytes.
  int32 memory_per_oracle_compute_unit_gbs = 37
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates the local disaster recovery (DR) type of
  // an Autonomous Database.
  LocalDisasterRecoveryType local_disaster_recovery_type = 38
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the Data Safe registration for the
  // Autonomous Database.
  DataSafeState data_safe_state = 39
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of database management for the Autonomous
  // Database.
  DatabaseManagementState database_management_state = 40
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates the current mode of the Autonomous
  // Database.
  OpenMode open_mode = 41 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates the state of Operations Insights for the
  // Autonomous Database.
  OperationsInsightsState operations_insights_state = 42
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of OCIDs of standby databases located in Autonomous
  // Data Guard remote regions that are associated with the source database.
  repeated string peer_db_ids = 43 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The permission level of the Autonomous Database.
  PermissionLevel permission_level = 44
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The private endpoint for the Autonomous Database.
  string private_endpoint = 45 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The refresh mode of the cloned Autonomous Database.
  RefreshableMode refreshable_mode = 46
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The refresh State of the clone.
  RefreshableState refreshable_state = 47
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Data Guard role of the Autonomous Database.
  Role role = 48 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list and details of the scheduled operations of the
  // Autonomous Database.
  repeated ScheduledOperationDetails scheduled_operation_details = 64
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The SQL Web Developer URL for the Autonomous Database.
  string sql_web_developer_url = 50 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of available regions that can be used to create a
  // clone for the Autonomous Database.
  repeated string supported_clone_regions = 51
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The storage space used by Autonomous Database, in gigabytes.
  int32 used_data_storage_size_tbs = 53
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Oracle Cloud Infrastructure link for the Autonomous
  // Database.
  string oci_url = 54 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The storage space used by automatic backups of Autonomous
  // Database, in gigabytes.
  float total_auto_backup_storage_size_gbs = 59
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The long term backup schedule of the Autonomous Database.
  google.protobuf.Timestamp next_long_term_backup_time = 60
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date and time when maintenance will begin.
  google.protobuf.Timestamp maintenance_begin_time = 65
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date and time when maintenance will end.
  google.protobuf.Timestamp maintenance_end_time = 66
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Oracle APEX Application Development.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/datatypes/AutonomousDatabaseApex
message AutonomousDatabaseApex {
  // Output only. The Oracle APEX Application Development version.
  string apex_version = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Oracle REST Data Services (ORDS) version.
  string ords_version = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The connection string used to connect to the Autonomous Database.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/datatypes/AutonomousDatabaseConnectionStrings
message AutonomousDatabaseConnectionStrings {
  // Output only. Returns all connection strings that can be used to connect to
  // the Autonomous Database.
  AllConnectionStrings all_connection_strings = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The database service provides the least level of resources to
  // each SQL statement, but supports the most number of concurrent SQL
  // statements.
  string dedicated = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The database service provides the highest level of resources
  // to each SQL statement.
  string high = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The database service provides the least level of resources to
  // each SQL statement.
  string low = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The database service provides a lower level of resources to
  // each SQL statement.
  string medium = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of connection string profiles to allow clients to
  // group, filter, and select values based on the structured metadata.
  repeated DatabaseConnectionStringProfile profiles = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The connection string profile to allow clients to group.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/datatypes/DatabaseConnectionStringProfile
message DatabaseConnectionStringProfile {
  // The various consumer groups available in the connection string profile.
  enum ConsumerGroup {
    // Default unspecified value.
    CONSUMER_GROUP_UNSPECIFIED = 0;

    // High consumer group.
    HIGH = 1;

    // Medium consumer group.
    MEDIUM = 2;

    // Low consumer group.
    LOW = 3;

    // TP consumer group.
    TP = 4;

    // TPURGENT consumer group.
    TPURGENT = 5;
  }

  // The host name format being used in the connection string.
  enum HostFormat {
    // Default unspecified value.
    HOST_FORMAT_UNSPECIFIED = 0;

    // FQDN
    FQDN = 1;

    // IP
    IP = 2;
  }

  // The protocol being used by the connection.
  enum Protocol {
    // Default unspecified value.
    PROTOCOL_UNSPECIFIED = 0;

    // Tcp
    TCP = 1;

    // Tcps
    TCPS = 2;
  }

  // The session mode of the connection.
  enum SessionMode {
    // Default unspecified value.
    SESSION_MODE_UNSPECIFIED = 0;

    // Direct
    DIRECT = 1;

    // Indirect
    INDIRECT = 2;
  }

  // Specifies syntax of the connection string.
  enum SyntaxFormat {
    // Default unspecified value.
    SYNTAX_FORMAT_UNSPECIFIED = 0;

    // Long
    LONG = 1;

    // Ezconnect
    EZCONNECT = 2;

    // Ezconnectplus
    EZCONNECTPLUS = 3;
  }

  // This field indicates the TLS authentication type of the connection.
  enum TLSAuthentication {
    // Default unspecified value.
    TLS_AUTHENTICATION_UNSPECIFIED = 0;

    // Server
    SERVER = 1;

    // Mutual
    MUTUAL = 2;
  }

  // Output only. The current consumer group being used by the connection.
  ConsumerGroup consumer_group = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name for the database connection.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The host name format being currently used in connection
  // string.
  HostFormat host_format = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates if the connection string is regional and
  // is only applicable for cross-region Data Guard.
  bool is_regional = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The protocol being used by the connection.
  Protocol protocol = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current session mode of the connection.
  SessionMode session_mode = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The syntax of the connection string.
  SyntaxFormat syntax_format = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field indicates the TLS authentication type of the
  // connection.
  TLSAuthentication tls_authentication = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The value of the connection string.
  string value = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A list of all connection strings that can be used to connect to the
// Autonomous Database.
message AllConnectionStrings {
  // Output only. The database service provides the highest level of resources
  // to each SQL statement.
  string high = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The database service provides the least level of resources to
  // each SQL statement.
  string low = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The database service provides a lower level of resources to
  // each SQL statement.
  string medium = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The URLs for accessing Oracle Application Express (APEX) and SQL Developer
// Web with a browser from a Compute instance.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/datatypes/AutonomousDatabaseConnectionUrls
message AutonomousDatabaseConnectionUrls {
  // Output only. Oracle Application Express (APEX) URL.
  string apex_uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of the Database Transforms for the Autonomous
  // Database.
  string database_transforms_uri = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of the Graph Studio for the Autonomous Database.
  string graph_studio_uri = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of the Oracle Machine Learning (OML) Notebook for the
  // Autonomous Database.
  string machine_learning_notebook_uri = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of Machine Learning user management the Autonomous
  // Database.
  string machine_learning_user_management_uri = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of the MongoDB API for the Autonomous Database.
  string mongo_db_uri = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Oracle REST Data Services (ORDS) URL of the Web Access for
  // the Autonomous Database.
  string ords_uri = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URL of the Oracle SQL Developer Web for the Autonomous
  // Database.
  string sql_dev_web_uri = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Autonomous Data Guard standby database details.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/datatypes/AutonomousDatabaseStandbySummary
message AutonomousDatabaseStandbySummary {
  // Output only. The amount of time, in seconds, that the data of the standby
  // database lags in comparison to the data of the primary database.
  google.protobuf.Duration lag_time_duration = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The additional details about the current lifecycle state of
  // the Autonomous Database.
  string lifecycle_details = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current lifecycle state of the Autonomous Database.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date and time the Autonomous Data Guard role was switched
  // for the standby Autonomous Database.
  google.protobuf.Timestamp data_guard_role_changed_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date and time the Disaster Recovery role was switched for
  // the standby Autonomous Database.
  google.protobuf.Timestamp disaster_recovery_role_changed_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Details of scheduled operation.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/datatypes/ScheduledOperationDetails
message ScheduledOperationDetails {
  // Output only. Day of week.
  google.type.DayOfWeek day_of_week = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Auto start time.
  google.type.TimeOfDay start_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Auto stop time.
  google.type.TimeOfDay stop_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
