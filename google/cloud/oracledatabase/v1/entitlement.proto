// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "EntitlementProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the Entitlement resource.
message Entitlement {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/Entitlement"
    pattern: "projects/{project}/locations/{location}/entitlements/{entitlement}"
    plural: "entitlements"
    singular: "entitlement"
  };

  // The various lifecycle states of the subscription.
  enum State {
    // Default unspecified value.
    STATE_UNSPECIFIED = 0;

    // Account not linked.
    ACCOUNT_NOT_LINKED = 1;

    // Account is linked but not active.
    ACCOUNT_NOT_ACTIVE = 2;

    // Entitlement and Account are active.
    ACTIVE = 3;

    // Account is suspended.
    ACCOUNT_SUSPENDED = 4;
  }

  // Identifier. The name of the Entitlement resource with the format:
  // projects/{project}/locations/{region}/entitlements/{entitlement}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Details of the OCI Cloud Account.
  CloudAccountDetails cloud_account_details = 2;

  // Output only. Google Cloud Marketplace order ID (aka entitlement ID)
  string entitlement_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Entitlement State.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Details of the OCI Cloud Account.
message CloudAccountDetails {
  // Output only. OCI account name.
  string cloud_account = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. OCI account home region.
  string cloud_account_home_region = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL to link an existing account.
  optional string link_existing_account_uri = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL to create a new account and link.
  optional string account_creation_uri = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
