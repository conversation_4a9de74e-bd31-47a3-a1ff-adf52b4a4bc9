// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "DbSystemShapeProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the Database System Shapes resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/DbSystemShapeSummary/
message DbSystemShape {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/DbSystemShape"
    pattern: "projects/{project}/locations/{location}/dbSystemShapes/{db_system_shape}"
    plural: "dbSystemShapes"
    singular: "dbSystemShape"
  };

  // Identifier. The name of the Database System Shape resource with the format:
  // projects/{project}/locations/{region}/dbSystemShapes/{db_system_shape}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. shape
  string shape = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum number of database servers.
  int32 min_node_count = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of database servers.
  int32 max_node_count = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum number of storage servers.
  int32 min_storage_count = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of storage servers.
  int32 max_storage_count = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of cores per node.
  int32 available_core_count_per_node = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Memory per database server node in gigabytes.
  int32 available_memory_per_node_gb = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Storage per storage server in terabytes.
  int32 available_data_storage_tb = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum core count per node.
  int32 min_core_count_per_node = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum memory per node in gigabytes.
  int32 min_memory_per_node_gb = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum node storage per database server in gigabytes.
  int32 min_db_node_storage_per_node_gb = 12
      [(google.api.field_behavior) = OPTIONAL];
}
