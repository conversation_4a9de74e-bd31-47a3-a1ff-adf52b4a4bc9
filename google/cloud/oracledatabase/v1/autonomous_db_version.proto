// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/oracledatabase/v1/autonomous_database.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "AutonomousDbVersionProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the Autonomous Database version.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/AutonomousDbVersionSummary/
message AutonomousDbVersion {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/AutonomousDbVersion"
    pattern: "projects/{project}/locations/{location}/autonomousDbVersions/{autonomous_db_version}"
    plural: "autonomousDbVersions"
    singular: "autonomousDbVersion"
  };

  // Identifier. The name of the Autonomous Database Version resource with the
  // format:
  // projects/{project}/locations/{region}/autonomousDbVersions/{autonomous_db_version}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. An Oracle Database version for Autonomous Database.
  string version = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Autonomous Database workload type.
  DBWorkload db_workload = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A URL that points to a detailed description of the Autonomous
  // Database version.
  string workload_uri = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
