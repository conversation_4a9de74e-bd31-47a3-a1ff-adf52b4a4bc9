{"methodConfig": [{"name": [{"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListCloudExadataInfrastructures"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "GetCloudExadataInfrastructure"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListCloudVmClusters"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "GetCloudVmCluster"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListEntitlements"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListDbServers"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListDbNodes"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListGiVersions"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListDbSystemShapes"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListAutonomousDatabases"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "GetAutonomousDatabase"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListAutonomousDbVersions"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListAutonomousDatabaseCharacterSets"}, {"service": "google.cloud.oracledatabase.v1.OracleDatabase", "method": "ListAutonomousDatabaseBackups"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["RESOURCE_EXHAUSTED", "UNAVAILABLE", "INTERNAL", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.oracledatabase.v1.OracleDatabase"}], "timeout": "60s"}]}