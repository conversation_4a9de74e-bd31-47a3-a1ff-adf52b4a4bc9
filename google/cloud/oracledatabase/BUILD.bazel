# This build file includes a target for the Ruby wrapper library for
# google-cloud-oracle_database.

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

# Generates a Ruby wrapper client for oracledatabase.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "oracledatabase_ruby_wrapper",
    srcs = ["//google/cloud/oracledatabase/v1:oracledatabase_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-oracle_database",
        "ruby-cloud-wrapper-of=v1:0.0",
    ],
    service_yaml = "//google/cloud/oracledatabase/v1:oracledatabase_v1.yaml",
    transport = "rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-oracle_database-ruby",
    deps = [
        ":oracledatabase_ruby_wrapper",
    ],
)
