# This build file includes a target for the Ruby wrapper library for
# google-cloud-channel.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudchannel.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "cloudchannel_ruby_wrapper",
    srcs = ["//google/cloud/channel/v1:channel_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-channel",
        "ruby-cloud-env-prefix=CHANNEL",
        "ruby-cloud-wrapper-of=v1:0.23",
        "ruby-cloud-product-url=https://cloud.google.com/channel",
        "ruby-cloud-api-id=cloudchannel.googleapis.com",
        "ruby-cloud-api-shortname=cloudchannel",
    ],
    ruby_cloud_description = "You can use Channel Services to manage your relationships with your partners and your customers. Channel Services include a console and APIs to view and provision links between distributors and resellers, customers and entitlements.",
    ruby_cloud_title = "Cloud Channel",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-channel-ruby",
    deps = [
        ":cloudchannel_ruby_wrapper",
    ],
)
