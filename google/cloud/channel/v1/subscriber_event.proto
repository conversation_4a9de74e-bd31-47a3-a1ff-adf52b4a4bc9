// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/resource.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "SubscriberEventProto";
option java_package = "com.google.cloud.channel.v1";

// Represents Pub/Sub message content describing customer update.
message CustomerEvent {
  // Type of customer event.
  enum Type {
    // Not used.
    TYPE_UNSPECIFIED = 0;

    // Primary domain for customer was changed.
    PRIMARY_DOMAIN_CHANGED = 1;

    // Primary domain of the customer has been verified.
    PRIMARY_DOMAIN_VERIFIED = 2;
  }

  // Resource name of the customer.
  // Format: accounts/{account_id}/customers/{customer_id}
  string customer = 1 [(google.api.resource_reference) = {
    type: "cloudchannel.googleapis.com/Customer"
  }];

  // Type of event which happened on the customer.
  Type event_type = 2;
}

// Represents Pub/Sub message content describing entitlement update.
message EntitlementEvent {
  // Type of entitlement event.
  enum Type {
    // Not used.
    TYPE_UNSPECIFIED = 0;

    // A new entitlement was created.
    CREATED = 1;

    // The offer type associated with an entitlement was changed.
    // This is not triggered if an entitlement converts from a commit offer to a
    // flexible offer as part of a renewal.
    PRICE_PLAN_SWITCHED = 3;

    // Annual commitment for a commit plan was changed.
    COMMITMENT_CHANGED = 4;

    // An annual entitlement was renewed.
    RENEWED = 5;

    // Entitlement was suspended.
    SUSPENDED = 6;

    // Entitlement was unsuspended.
    ACTIVATED = 7;

    // Entitlement was cancelled.
    CANCELLED = 8;

    // Entitlement was upgraded or downgraded (e.g. from Google Workspace
    // Business Standard to Google Workspace Business Plus).
    SKU_CHANGED = 9;

    // The renewal settings of an entitlement has changed.
    RENEWAL_SETTING_CHANGED = 10;

    // Paid service has started on trial entitlement.
    PAID_SERVICE_STARTED = 11;

    // License was assigned to or revoked from a user.
    LICENSE_ASSIGNMENT_CHANGED = 12;

    // License cap was changed for the entitlement.
    LICENSE_CAP_CHANGED = 13;
  }

  // Resource name of an entitlement of the form:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string entitlement = 1 [(google.api.resource_reference) = {
    type: "cloudchannel.googleapis.com/Entitlement"
  }];

  // Type of event which happened on the entitlement.
  Type event_type = 2;
}

// Represents information which resellers will get as part of notification from
// Pub/Sub.
message SubscriberEvent {
  // Specifies the Pub/Sub event provided to the partners.
  // This is a required field.
  oneof event {
    // Customer event sent as part of Pub/Sub event to partners.
    CustomerEvent customer_event = 1;

    // Entitlement event sent as part of Pub/Sub event to partners.
    EntitlementEvent entitlement_event = 2;
  }
}
