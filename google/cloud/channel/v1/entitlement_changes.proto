// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/channel/v1/entitlements.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "EntitlementChangesProto";
option java_package = "com.google.cloud.channel.v1";

// Change event entry for Entitlement order history
message EntitlementChange {
  // Specifies the type of change action
  enum ChangeType {
    // Not used.
    CHANGE_TYPE_UNSPECIFIED = 0;

    // New Entitlement was created.
    CREATED = 1;

    // Price plan associated with an Entitlement was changed.
    PRICE_PLAN_SWITCHED = 3;

    // Number of seats committed for a commitment Entitlement was changed.
    COMMITMENT_CHANGED = 4;

    // An annual Entitlement was renewed.
    RENEWED = 5;

    // Entitlement was suspended.
    SUSPENDED = 6;

    // Entitlement was activated.
    ACTIVATED = 7;

    // Entitlement was cancelled.
    CANCELLED = 8;

    // Entitlement was upgraded or downgraded for ex. from Google Workspace
    // Business Standard to Google Workspace Business Plus.
    SKU_CHANGED = 9;

    // The settings for renewal of an Entitlement have changed.
    RENEWAL_SETTING_CHANGED = 10;

    // Use for Google Workspace subscription.
    // Either a trial was converted to a paid subscription or a new subscription
    // with no trial is created.
    PAID_SUBSCRIPTION_STARTED = 11;

    // License cap was changed for the entitlement.
    LICENSE_CAP_CHANGED = 12;

    // The suspension details have changed (but it is still suspended).
    SUSPENSION_DETAILS_CHANGED = 13;

    // The trial end date was extended.
    TRIAL_END_DATE_EXTENDED = 14;

    // Entitlement started trial.
    TRIAL_STARTED = 15;
  }

  // Specifies the type of operator responsible for the change
  enum OperatorType {
    // Not used.
    OPERATOR_TYPE_UNSPECIFIED = 0;

    // Customer service representative.
    CUSTOMER_SERVICE_REPRESENTATIVE = 1;

    // System auto job.
    SYSTEM = 2;

    // Customer user.
    CUSTOMER = 3;

    // Reseller user.
    RESELLER = 4;
  }

  // Cancellation reason for the entitlement
  enum CancellationReason {
    // Not used.
    CANCELLATION_REASON_UNSPECIFIED = 0;

    // Reseller triggered a cancellation of the service.
    SERVICE_TERMINATED = 1;

    // Relationship between the reseller and customer has ended due to a
    // transfer.
    RELATIONSHIP_ENDED = 2;

    // Entitlement transferred away from reseller while still keeping other
    // entitlement(s) with the reseller.
    PARTIAL_TRANSFER = 3;
  }

  // The Entitlement's activation reason
  enum ActivationReason {
    // Not used.
    ACTIVATION_REASON_UNSPECIFIED = 0;

    // Reseller reactivated a suspended Entitlement.
    RESELLER_REVOKED_SUSPENSION = 1;

    // Customer accepted pending terms of service.
    CUSTOMER_ACCEPTED_PENDING_TOS = 2;

    // Reseller updated the renewal settings on an entitlement that was
    // suspended due to cancellation, and this update reactivated the
    // entitlement.
    RENEWAL_SETTINGS_CHANGED = 3;

    // Other reasons (Activated temporarily for cancellation, added a payment
    // plan to a trial entitlement, etc.)
    OTHER_ACTIVATION_REASON = 100;
  }

  // The reason the change was made
  oneof change_reason {
    // Suspension reason for the Entitlement.
    Entitlement.SuspensionReason suspension_reason = 9;

    // Cancellation reason for the Entitlement.
    CancellationReason cancellation_reason = 10;

    // The Entitlement's activation reason
    ActivationReason activation_reason = 11;

    // e.g. purchase_number change reason, entered by CRS.
    string other_change_reason = 100;
  }

  // Required. Resource name of an entitlement in the form:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string entitlement = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Entitlement"
    }
  ];

  // Required. Resource name of the Offer at the time of change.
  // Takes the form: accounts/{account_id}/offers/{offer_id}.
  string offer = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Offer"
    }
  ];

  // Service provisioned for an Entitlement.
  ProvisionedService provisioned_service = 3;

  // The change action type.
  ChangeType change_type = 4;

  // The submitted time of the change.
  google.protobuf.Timestamp create_time = 5;

  // Operator type responsible for the change.
  OperatorType operator_type = 6;

  // Extended parameters, such as:
  // purchase_order_number, gcp_details;
  // internal_correlation_id, long_running_operation_id, order_id;
  // etc.
  repeated Parameter parameters = 8;

  // Human-readable identifier that shows what operator made a change.
  // When the operator_type is RESELLER, this is the user's email address.
  // For all other operator types, this is empty.
  string operator = 12;
}
