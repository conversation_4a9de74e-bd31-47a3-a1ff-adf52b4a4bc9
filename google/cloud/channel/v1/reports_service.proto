// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/channel/v1/operations.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/datetime.proto";
import "google/type/decimal.proto";
import "google/type/money.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "ReportsServiceProto";
option java_package = "com.google.cloud.channel.v1";

// CloudChannelReportsService lets Google Cloud resellers and
// distributors retrieve and combine a variety of data in Cloud Channel for
// multiple products (Google Cloud, Google Voice, and Google Workspace.)
//
// Deprecated: This service is being deprecated. Please use [Export Channel
// Services data to
// BigQuery](https://cloud.google.com/channel/docs/rebilling/export-data-to-bigquery)
// instead.
service CloudChannelReportsService {
  option deprecated = true;
  option (google.api.default_host) = "cloudchannel.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/apps.reports.usage.readonly";

  // Begins generation of data for a given report. The report
  // identifier is a UID (for example, `613bf59q`).
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The user doesn't have access to this report.
  // * INVALID_ARGUMENT: Required request parameters are missing
  //   or invalid.
  // * NOT_FOUND: The report identifier was not found.
  // * INTERNAL: Any non-user error related to a technical issue
  //   in the backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue
  //   in the backend. Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata contains an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  //
  // To get the results of report generation, call
  // [CloudChannelReportsService.FetchReportResults][google.cloud.channel.v1.CloudChannelReportsService.FetchReportResults]
  // with the
  // [RunReportJobResponse.report_job][google.cloud.channel.v1.RunReportJobResponse.report_job].
  //
  // Deprecated: Please use [Export Channel Services data to
  // BigQuery](https://cloud.google.com/channel/docs/rebilling/export-data-to-bigquery)
  // instead.
  rpc RunReportJob(RunReportJobRequest) returns (google.longrunning.Operation) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1/{name=accounts/*/reports/*}:run"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "RunReportJobResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Retrieves data generated by
  // [CloudChannelReportsService.RunReportJob][google.cloud.channel.v1.CloudChannelReportsService.RunReportJob].
  //
  // Deprecated: Please use [Export Channel Services data to
  // BigQuery](https://cloud.google.com/channel/docs/rebilling/export-data-to-bigquery)
  // instead.
  rpc FetchReportResults(FetchReportResultsRequest)
      returns (FetchReportResultsResponse) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1/{report_job=accounts/*/reportJobs/*}:fetchReportResults"
      body: "*"
    };
    option (google.api.method_signature) = "report_job";
  }

  // Lists the reports that RunReportJob can run. These reports include an ID,
  // a description, and the list of columns that will be in the result.
  //
  // Deprecated: Please use [Export Channel Services data to
  // BigQuery](https://cloud.google.com/channel/docs/rebilling/export-data-to-bigquery)
  // instead.
  rpc ListReports(ListReportsRequest) returns (ListReportsResponse) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*}/reports"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [CloudChannelReportsService.RunReportJob][google.cloud.channel.v1.CloudChannelReportsService.RunReportJob].
message RunReportJobRequest {
  option deprecated = true;

  // Required. The report's resource name. Specifies the account and report used
  // to generate report data. The report_id identifier is a UID (for example,
  // `613bf59q`).
  // Name uses the format:
  // accounts/{account_id}/reports/{report_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Report"
    }
  ];

  // Optional. The range of usage or invoice dates to include in the result.
  DateRange date_range = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A structured string that defines conditions on dimension columns
  // to restrict the report output.
  //
  // Filters support logical operators (AND, OR, NOT) and conditional operators
  // (=, !=, <, >, <=, and >=) using `column_id` as keys.
  //
  // For example:
  // `(customer:"accounts/C123abc/customers/S456def" OR
  // customer:"accounts/C123abc/customers/S789ghi") AND
  // invoice_start_date.year >= 2022`
  string filter = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code, such as "en-US".  If specified, the
  // response is localized to the corresponding language code if the
  // original data sources support it.
  // Default is "en-US".
  string language_code = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelReportsService.RunReportJob][google.cloud.channel.v1.CloudChannelReportsService.RunReportJob].
message RunReportJobResponse {
  option deprecated = true;

  // Pass `report_job.name` to
  // [FetchReportResultsRequest.report_job][google.cloud.channel.v1.FetchReportResultsRequest.report_job]
  // to retrieve the report's results.
  ReportJob report_job = 1;

  // The metadata for the report's results (display name, columns, row count,
  // and date range). If you view this before the operation finishes,
  // you may see incomplete data.
  ReportResultsMetadata report_metadata = 2;
}

// Request message for
// [CloudChannelReportsService.FetchReportResults][google.cloud.channel.v1.CloudChannelReportsService.FetchReportResults].
message FetchReportResultsRequest {
  option deprecated = true;

  // Required. The report job created by
  // [CloudChannelReportsService.RunReportJob][google.cloud.channel.v1.CloudChannelReportsService.RunReportJob].
  // Report_job uses the format:
  // accounts/{account_id}/reportJobs/{report_job_id}
  string report_job = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/ReportJob"
    }
  ];

  // Optional. Requested page size of the report. The server may return fewer
  // results than requested. If you don't specify a page size, the server uses a
  // sensible default (may change over time).
  //
  // The maximum value is 30,000; the server will change larger values to
  // 30,000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token that specifies a page of results beyond the first page.
  // Obtained through
  // [FetchReportResultsResponse.next_page_token][google.cloud.channel.v1.FetchReportResultsResponse.next_page_token]
  // of the previous
  // [CloudChannelReportsService.FetchReportResults][google.cloud.channel.v1.CloudChannelReportsService.FetchReportResults]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List of keys specifying which report partitions to return.
  // If empty, returns all partitions.
  repeated string partition_keys = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelReportsService.FetchReportResults][google.cloud.channel.v1.CloudChannelReportsService.FetchReportResults].
// Contains a tabular representation of the report results.
message FetchReportResultsResponse {
  option deprecated = true;

  // The metadata for the report results (display name, columns, row count, and
  // date ranges).
  ReportResultsMetadata report_metadata = 1;

  // The report's lists of values. Each row follows the settings and ordering
  // of the columns from `report_metadata`.
  repeated Row rows = 2;

  // Pass this token to
  // [FetchReportResultsRequest.page_token][google.cloud.channel.v1.FetchReportResultsRequest.page_token]
  // to retrieve the next page of results.
  string next_page_token = 3;
}

// Request message for
// [CloudChannelReportsService.ListReports][google.cloud.channel.v1.CloudChannelReportsService.ListReports].
message ListReportsRequest {
  option deprecated = true;

  // Required. The resource name of the partner account to list available
  // reports for. Parent uses the format: accounts/{account_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Requested page size of the report. The server might return fewer
  // results than requested. If unspecified, returns 20 reports. The maximum
  // value is 100.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token that specifies a page of results beyond the first page.
  // Obtained through
  // [ListReportsResponse.next_page_token][google.cloud.channel.v1.ListReportsResponse.next_page_token]
  // of the previous
  // [CloudChannelReportsService.ListReports][google.cloud.channel.v1.CloudChannelReportsService.ListReports]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code, such as "en-US".  If specified, the
  // response is localized to the corresponding language code if the
  // original data sources support it.
  // Default is "en-US".
  string language_code = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelReportsService.ListReports][google.cloud.channel.v1.CloudChannelReportsService.ListReports].
message ListReportsResponse {
  option deprecated = true;

  // The reports available to the partner.
  repeated Report reports = 1;

  // Pass this token to
  // [FetchReportResultsRequest.page_token][google.cloud.channel.v1.FetchReportResultsRequest.page_token]
  // to retrieve the next page of results.
  string next_page_token = 2;
}

// The result of a [RunReportJob][] operation. Contains the name to use in
// [FetchReportResultsRequest.report_job][google.cloud.channel.v1.FetchReportResultsRequest.report_job]
// and the status of the operation.
message ReportJob {
  option deprecated = true;
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/ReportJob"
    pattern: "accounts/{account}/reportJobs/{report_job}"
  };

  // Required. The resource name of a report job.
  // Name uses the format:
  // `accounts/{account_id}/reportJobs/{report_job_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // The current status of report generation.
  ReportStatus report_status = 2;
}

// The features describing the data. Returned by
// [CloudChannelReportsService.RunReportJob][google.cloud.channel.v1.CloudChannelReportsService.RunReportJob]
// and
// [CloudChannelReportsService.FetchReportResults][google.cloud.channel.v1.CloudChannelReportsService.FetchReportResults].
message ReportResultsMetadata {
  option deprecated = true;

  // Details of the completed report.
  Report report = 1;

  // The total number of rows of data in the final report.
  int64 row_count = 2;

  // The date range of reported usage.
  DateRange date_range = 3;

  // The usage dates immediately preceding `date_range` with the same duration.
  // Use this to calculate trending usage and costs. This is only populated if
  // you request trending data.
  //
  // For example, if `date_range` is July 1-15, `preceding_date_range` will be
  // June 16-30.
  DateRange preceding_date_range = 4;
}

// The definition of a report column. Specifies the data properties
// in the corresponding position of the report rows.
message Column {
  option deprecated = true;

  // Available data types for columns. Corresponds to the fields in the
  // ReportValue `oneof` field.
  enum DataType {
    // Not used.
    DATA_TYPE_UNSPECIFIED = 0;

    // ReportValues for this column will use string_value.
    STRING = 1;

    // ReportValues for this column will use int_value.
    INT = 2;

    // ReportValues for this column will use decimal_value.
    DECIMAL = 3;

    // ReportValues for this column will use money_value.
    MONEY = 4;

    // ReportValues for this column will use date_value.
    DATE = 5;

    // ReportValues for this column will use date_time_value.
    DATE_TIME = 6;
  }

  // The unique name of the column (for example, customer_domain,
  // channel_partner, customer_cost). You can use column IDs in
  // [RunReportJobRequest.filter][google.cloud.channel.v1.RunReportJobRequest.filter].
  // To see all reports and their columns, call
  // [CloudChannelReportsService.ListReports][google.cloud.channel.v1.CloudChannelReportsService.ListReports].
  string column_id = 1;

  // The column's display name.
  string display_name = 2;

  // The type of the values for this column.
  DataType data_type = 3;
}

// A representation of usage or invoice date ranges.
message DateRange {
  option deprecated = true;

  // The earliest usage date time (inclusive).
  //
  // If you use time groupings (daily, weekly, etc), each group uses
  // midnight to midnight (Pacific time). The usage start date is
  // rounded down to include all usage from the specified date. We recommend
  // that clients pass `usage_start_date_time` in Pacific time.
  google.type.DateTime usage_start_date_time = 1;

  // The latest usage date time (exclusive).
  //
  // If you use time groupings (daily, weekly, etc), each group uses
  // midnight to midnight (Pacific time). The usage end date is
  // rounded down to include all usage from the specified date. We recommend
  // that clients pass `usage_start_date_time` in Pacific time.
  google.type.DateTime usage_end_date_time = 2;

  // The earliest invoice date (inclusive).
  //
  // If this value is not the first day of a month, this will move it back to
  // the first day of the given month.
  google.type.Date invoice_start_date = 3;

  // The latest invoice date (inclusive).
  //
  // If this value is not the last day of a month, this will move it forward to
  // the last day of the given month.
  google.type.Date invoice_end_date = 4;
}

// A row of report values.
message Row {
  option deprecated = true;

  // The list of values in the row.
  repeated ReportValue values = 1;

  // The key for the partition this row belongs to. This field is empty
  // if the report is not partitioned.
  string partition_key = 2;
}

// A single report value.
message ReportValue {
  option deprecated = true;

  // A single report value.
  oneof value {
    // A value of type `string`.
    string string_value = 1;

    // A value of type `int`.
    int64 int_value = 2;

    // A value of type `google.type.Decimal`, representing non-integer numeric
    // values.
    google.type.Decimal decimal_value = 3;

    // A value of type `google.type.Money` (currency code, whole units, decimal
    // units).
    google.type.Money money_value = 4;

    // A value of type `google.type.Date` (year, month, day).
    google.type.Date date_value = 5;

    // A value of type `google.type.DateTime` (year, month, day, hour, minute,
    // second, and UTC offset or timezone.)
    google.type.DateTime date_time_value = 6;
  }
}

// Status of a report generation process.
message ReportStatus {
  option deprecated = true;

  // Available states of report generation.
  enum State {
    // Not used.
    STATE_UNSPECIFIED = 0;

    // Report processing started.
    STARTED = 1;

    // Data generated from the report is being staged.
    WRITING = 2;

    // Report data is available for access.
    AVAILABLE = 3;

    // Report failed.
    FAILED = 4;
  }

  // The current state of the report generation process.
  State state = 1;

  // The report generation's start time.
  google.protobuf.Timestamp start_time = 2;

  // The report generation's completion time.
  google.protobuf.Timestamp end_time = 3;
}

// The ID and description of a report that was used to generate report data.
// For example, "Google Cloud Daily Spend", "Google Workspace License Activity",
// etc.
message Report {
  option deprecated = true;
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/Report"
    pattern: "accounts/{account}/reports/{report}"
  };

  // Required. The report's resource name. Specifies the account and report used
  // to generate report data. The report_id identifier is a UID (for example,
  // `613bf59q`).
  //
  // Name uses the format:
  // accounts/{account_id}/reports/{report_id}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // A human-readable name for this report.
  string display_name = 2;

  // The list of columns included in the report. This defines the schema of
  // the report results.
  repeated Column columns = 3;

  // A description of other aspects of the report, such as the products
  // it supports.
  string description = 4;
}
