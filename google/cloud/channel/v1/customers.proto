// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/channel/v1/common.proto";
import "google/protobuf/timestamp.proto";
import "google/type/postal_address.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "CustomersProto";
option java_package = "com.google.cloud.channel.v1";

// Entity representing a customer of a reseller or distributor.
message Customer {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/Customer"
    pattern: "accounts/{account}/customers/{customer}"
  };

  // Output only. Resource name of the customer.
  // Format: accounts/{account_id}/customers/{customer_id}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Name of the organization that the customer entity represents.
  string org_display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The organization address for the customer. To enforce US laws and
  // embargoes, we require a region, postal code, and address lines. You must
  // provide valid addresses for every customer. To set the customer's
  // language, use the Customer-level language code.
  google.type.PostalAddress org_postal_address = 3
      [(google.api.field_behavior) = REQUIRED];

  // Primary contact info.
  ContactInfo primary_contact_info = 4;

  // Secondary contact email. You need to provide an alternate email to create
  // different domains if a primary contact email already exists. Users will
  // receive a notification with credentials when you create an admin.google.com
  // account. Secondary emails are also recovery email addresses. Alternate
  // emails are optional when you create Team customers.
  string alternate_email = 5;

  // Required. The customer's primary domain. Must match the primary contact
  // email's domain.
  string domain = 6 [(google.api.field_behavior) = REQUIRED];

  // Output only. Time when the customer was created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the customer was updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The customer's Cloud Identity ID if the customer has a Cloud
  // Identity resource.
  string cloud_identity_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The BCP-47 language code, such as "en-US" or "sr-Latn". For more
  // information, see
  // https://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  string language_code = 10 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Cloud Identity information for the customer.
  // Populated only if a Cloud Identity account exists for this customer.
  CloudIdentityInfo cloud_identity_info = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Cloud Identity ID of the customer's channel partner.
  // Populated only if a channel partner exists for this customer.
  string channel_partner_id = 13;

  // Optional. External CRM ID for the customer.
  // Populated only if a CRM ID exists for this customer.
  string correlation_id = 14 [(google.api.field_behavior) = OPTIONAL];
}

// Contact information for a customer account.
message ContactInfo {
  // The customer account contact's first name. Optional for Team customers.
  string first_name = 1;

  // The customer account contact's last name. Optional for Team customers.
  string last_name = 2;

  // Output only. The customer account contact's display name, formatted as a
  // combination of the customer's first and last name.
  string display_name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The customer account's contact email. Required for entitlements that create
  // admin.google.com accounts, and serves as the customer's username for those
  // accounts. Use this email to invite Team customers.
  string email = 5;

  // Optional. The customer account contact's job title.
  string title = 6 [(google.api.field_behavior) = OPTIONAL];

  // The customer account's contact phone number.
  string phone = 7;
}
