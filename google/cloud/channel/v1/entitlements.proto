// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/channel/v1/common.proto";
import "google/cloud/channel/v1/offers.proto";
import "google/cloud/channel/v1/products.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "EntitlementsProto";
option java_package = "com.google.cloud.channel.v1";

// An entitlement is a representation of a customer's ability to use a service.
message Entitlement {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/Entitlement"
    pattern: "accounts/{account}/customers/{customer}/entitlements/{entitlement}"
  };

  // Indicates the current provisioning state of the entitlement.
  enum ProvisioningState {
    // Not used.
    PROVISIONING_STATE_UNSPECIFIED = 0;

    // The entitlement is currently active.
    ACTIVE = 1;

    // The entitlement is currently suspended.
    SUSPENDED = 5;
  }

  // Suspension reason for an entitlement if
  // [provisioning_state][google.cloud.channel.v1.Entitlement.provisioning_state]
  // = SUSPENDED.
  enum SuspensionReason {
    // Not used.
    SUSPENSION_REASON_UNSPECIFIED = 0;

    // Entitlement was manually suspended by the Reseller.
    RESELLER_INITIATED = 1;

    // Trial ended.
    TRIAL_ENDED = 2;

    // Entitlement renewal was canceled.
    RENEWAL_WITH_TYPE_CANCEL = 3;

    // Entitlement was automatically suspended on creation for pending ToS
    // acceptance on customer.
    PENDING_TOS_ACCEPTANCE = 4;

    // Other reasons (internal reasons, abuse, etc.).
    OTHER = 100;
  }

  // Output only. Resource name of an entitlement in the form:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the entitlement is created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the entitlement is updated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The offer resource name for which the entitlement is to be
  // created. Takes the form: accounts/{account_id}/offers/{offer_id}.
  string offer = 8 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Offer"
    }
  ];

  // Commitment settings for a commitment-based Offer.
  // Required for commitment based offers.
  CommitmentSettings commitment_settings = 12;

  // Output only. Current provisioning state of the entitlement.
  ProvisioningState provisioning_state = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Service provisioning details for the entitlement.
  ProvisionedService provisioned_service = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Enumerable of all current suspension reasons for an
  // entitlement.
  repeated SuspensionReason suspension_reasons = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. This purchase order (PO) information is for resellers to use for
  // their company tracking usage. If a purchaseOrderId value is given, it
  // appears in the API responses and shows up in the invoice. The property
  // accepts up to 80 plain text characters. This is only supported for Google
  // Workspace entitlements.
  string purchase_order_id = 19 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Settings for trial offers.
  TrialSettings trial_settings = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Association information to other entitlements.
  AssociationInfo association_info = 23;

  // Extended entitlement parameters. When creating an entitlement, valid
  // parameter names and values are defined in the
  // [Offer.parameter_definitions][google.cloud.channel.v1.Offer.parameter_definitions].
  //
  // For Google Workspace, the following Parameters may be accepted as input:
  //
  // - max_units: The maximum assignable units for a flexible offer
  //
  // OR
  //
  // - num_units: The total commitment for commitment-based offers
  //
  // The response may additionally include the following output-only Parameters:
  //
  // - assigned_units: The number of licenses assigned to users.
  //
  // For Google Cloud billing subaccounts, the following Parameter may be
  // accepted as input:
  //
  // - display_name: The display name of the billing subaccount.
  repeated Parameter parameters = 26;

  // Optional. The billing account resource name that is used to pay for this
  // entitlement.
  string billing_account = 28 [(google.api.field_behavior) = OPTIONAL];
}

// Definition for extended entitlement parameters.
message Parameter {
  // Name of the parameter.
  string name = 1;

  // Value of the parameter.
  Value value = 2;

  // Output only. Specifies whether this parameter is allowed to be changed. For
  // example, for a Google Workspace Business Starter entitlement in commitment
  // plan, num_units is editable when entitlement is active.
  bool editable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Association links that an entitlement has to other entitlements.
message AssociationInfo {
  // The name of the base entitlement, for which this entitlement is an add-on.
  string base_entitlement = 1 [(google.api.resource_reference) = {
    type: "cloudchannel.googleapis.com/Entitlement"
  }];
}

// Service provisioned for an entitlement.
message ProvisionedService {
  // Output only. Provisioning ID of the entitlement. For Google Workspace, this
  // is the underlying Subscription ID. For Google Cloud, this is the Billing
  // Account ID of the billing subaccount.
  string provisioning_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The product pertaining to the provisioning resource as
  // specified in the Offer.
  string product_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The SKU pertaining to the provisioning resource as specified
  // in the Offer.
  string sku_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Commitment settings for commitment-based offers.
message CommitmentSettings {
  // Output only. Commitment start timestamp.
  google.protobuf.Timestamp start_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Commitment end timestamp.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Renewal settings applicable for a commitment-based Offer.
  RenewalSettings renewal_settings = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Renewal settings for renewable Offers.
message RenewalSettings {
  // If false, the plan will be completed at the end date.
  bool enable_renewal = 1;

  // If true and enable_renewal = true, the unit (for example seats or licenses)
  // will be set to the number of active units at renewal time.
  bool resize_unit_count = 2;

  // Describes how a reseller will be billed.
  PaymentPlan payment_plan = 5;

  // Describes how frequently the reseller will be billed, such as
  // once per month.
  Period payment_cycle = 6;
}

// Settings for trial offers.
message TrialSettings {
  // Determines if the entitlement is in a trial or not:
  //
  // * `true` - The entitlement is in trial.
  // * `false` - The entitlement is not in trial.
  bool trial = 1;

  // Date when the trial ends. The value is in milliseconds
  // using the UNIX Epoch format. See an example [Epoch
  // converter](https://www.epochconverter.com).
  google.protobuf.Timestamp end_time = 2;
}

// TransferableSku represents information a reseller needs to view existing
// provisioned services for a customer that they do not own.
// Read-only.
message TransferableSku {
  // Describes the transfer eligibility of a SKU.
  TransferEligibility transfer_eligibility = 9;

  // The SKU pertaining to the provisioning resource as specified in the Offer.
  Sku sku = 11;

  // Optional. The customer to transfer has an entitlement with the populated
  // legacy SKU.
  Sku legacy_sku = 12 [(google.api.field_behavior) = OPTIONAL];
}

// Specifies transfer eligibility of a SKU.
message TransferEligibility {
  // Reason of ineligibility.
  enum Reason {
    // Not used.
    REASON_UNSPECIFIED = 0;

    // Reseller needs to accept TOS before transferring the SKU.
    PENDING_TOS_ACCEPTANCE = 1;

    // Reseller not eligible to sell the SKU.
    SKU_NOT_ELIGIBLE = 2;

    // SKU subscription is suspended
    SKU_SUSPENDED = 3;

    // The reseller is not authorized to transact on this Product. See
    // https://support.google.com/channelservices/answer/9759265
    CHANNEL_PARTNER_NOT_AUTHORIZED_FOR_SKU = 4;
  }

  // Whether reseller is eligible to transfer the SKU.
  bool is_eligible = 1;

  // Localized description if reseller is not eligible to transfer the SKU.
  string description = 2;

  // Specified the reason for ineligibility.
  Reason ineligibility_reason = 3;
}
