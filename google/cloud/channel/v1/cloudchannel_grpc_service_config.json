{"methodConfig": [{"name": [{"service": "google.cloud.channel.v1.CloudChannelService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.channel.v1.CloudChannelService", "method": "ProvisionCloudIdentity"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "CreateEntitlement"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "ChangeParameters"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "ChangeRenewalSettings"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "ChangeOffer"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "StartPaidService"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "SuspendEntitlement"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "CancelEntitlement"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "ActivateEntitlement"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "TransferEntitlements"}, {"service": "google.cloud.channel.v1.CloudChannelService", "method": "TransferEntitlementsToGoogle"}], "timeout": "60s"}]}