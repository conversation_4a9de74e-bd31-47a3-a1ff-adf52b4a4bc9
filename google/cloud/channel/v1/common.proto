// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/any.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.channel.v1";

// Required Edu Attributes
message EduData {
  // Enum to specify the institute type.
  enum InstituteType {
    // Not used.
    INSTITUTE_TYPE_UNSPECIFIED = 0;

    // Elementary/Secondary Schools & Districts
    K12 = 1;

    // Higher Education Universities & Colleges
    UNIVERSITY = 2;
  }

  // Number of students and staff the institute has.
  enum InstituteSize {
    // Not used.
    INSTITUTE_SIZE_UNSPECIFIED = 0;

    // 1 - 100
    SIZE_1_100 = 1;

    // 101 - 500
    SIZE_101_500 = 2;

    // 501 - 1,000
    SIZE_501_1000 = 3;

    // 1,001 - 2,000
    SIZE_1001_2000 = 4;

    // 2,001 - 5,000
    SIZE_2001_5000 = 5;

    // 5,001 - 10,000
    SIZE_5001_10000 = 6;

    // 10,001 +
    SIZE_10001_OR_MORE = 7;
  }

  // Designated institute type of customer.
  InstituteType institute_type = 1;

  // Size of the institute.
  InstituteSize institute_size = 2;

  // Web address for the edu customer's institution.
  string website = 3;
}

// Cloud Identity information for the Cloud Channel Customer.
message CloudIdentityInfo {
  // CustomerType of the customer
  enum CustomerType {
    // Not used.
    CUSTOMER_TYPE_UNSPECIFIED = 0;

    // Domain-owning customer which needs domain verification to use services.
    DOMAIN = 1;

    // Team customer which needs email verification to use services.
    TEAM = 2;
  }

  // CustomerType indicates verification type needed for using services.
  CustomerType customer_type = 1;

  // Output only. The primary domain name.
  string primary_domain = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the domain is verified.
  // This field is not returned for a Customer's cloud_identity_info resource.
  // Partners can use the domains.get() method of the Workspace SDK's
  // Directory API, or listen to the PRIMARY_DOMAIN_VERIFIED Pub/Sub event in
  // to track domain verification of their resolve Workspace customers.
  bool is_domain_verified = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The alternate email.
  string alternate_email = 6;

  // Phone number associated with the Cloud Identity.
  string phone_number = 7;

  // Language code.
  string language_code = 8;

  // Output only. URI of Customer's Admin console dashboard.
  string admin_console_uri = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Edu information about the customer.
  EduData edu_data = 22;
}

// Data type and value of a parameter.
message Value {
  // The kind of value.
  oneof kind {
    // Represents an int64 value.
    int64 int64_value = 1;

    // Represents a string value.
    string string_value = 2;

    // Represents a double value.
    double double_value = 3;

    // Represents an 'Any' proto value.
    google.protobuf.Any proto_value = 4;

    // Represents a boolean value.
    bool bool_value = 5;
  }
}

// Information needed to create an Admin User for Google Workspace.
message AdminUser {
  // Primary email of the admin user.
  string email = 1;

  // Given name of the admin user.
  string given_name = 2;

  // Family name of the admin user.
  string family_name = 3;
}
