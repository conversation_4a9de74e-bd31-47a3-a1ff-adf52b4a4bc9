// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/channel/v1/billing_accounts.proto";
import "google/cloud/channel/v1/channel_partner_links.proto";
import "google/cloud/channel/v1/common.proto";
import "google/cloud/channel/v1/customers.proto";
import "google/cloud/channel/v1/entitlement_changes.proto";
import "google/cloud/channel/v1/entitlements.proto";
import "google/cloud/channel/v1/offers.proto";
import "google/cloud/channel/v1/operations.proto";
import "google/cloud/channel/v1/products.proto";
import "google/cloud/channel/v1/repricing.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.channel.v1";

// CloudChannelService lets Google cloud resellers and distributors manage
// their customers, channel partners, entitlements, and reports.
//
// Using this service:
// 1. Resellers and distributors can manage a customer entity.
// 2. Distributors can register an authorized reseller in their channel and
//    provide them with delegated admin access.
// 3. Resellers and distributors can manage customer entitlements.
//
// CloudChannelService exposes the following resources:
// - [Customer][google.cloud.channel.v1.Customer]s: An entity-usually an
// enterprise-managed by a reseller or distributor.
//
// - [Entitlement][google.cloud.channel.v1.Entitlement]s: An entity that
// provides a customer with the means to use a service. Entitlements are created
// or updated as a result of a successful fulfillment.
//
// - [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink]s: An
// entity that identifies links between distributors and their indirect
// resellers in a channel.
service CloudChannelService {
  option (google.api.default_host) = "cloudchannel.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/apps.order";

  // List [Customer][google.cloud.channel.v1.Customer]s.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  //
  // Return value:
  // List of [Customer][google.cloud.channel.v1.Customer]s, or an empty list if
  // there are no customers.
  rpc ListCustomers(ListCustomersRequest) returns (ListCustomersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*}/customers"
      additional_bindings {
        get: "/v1/{parent=accounts/*/channelPartnerLinks/*}/customers"
      }
    };
  }

  // Returns the requested [Customer][google.cloud.channel.v1.Customer]
  // resource.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: The customer resource doesn't exist. Usually the result of an
  // invalid name parameter.
  //
  // Return value:
  // The [Customer][google.cloud.channel.v1.Customer] resource.
  rpc GetCustomer(GetCustomerRequest) returns (Customer) {
    option (google.api.http) = {
      get: "/v1/{name=accounts/*/customers/*}"
      additional_bindings {
        get: "/v1/{name=accounts/*/channelPartnerLinks/*/customers/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Confirms the existence of Cloud Identity accounts based on the domain and
  // if the Cloud Identity accounts are owned by the reseller.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * INVALID_VALUE: Invalid domain value in the request.
  //
  // Return value:
  // A list of
  // [CloudIdentityCustomerAccount][google.cloud.channel.v1.CloudIdentityCustomerAccount]
  // resources for the domain (may be empty)
  //
  // Note: in the v1alpha1 version of the API, a NOT_FOUND error returns if
  // no
  // [CloudIdentityCustomerAccount][google.cloud.channel.v1.CloudIdentityCustomerAccount]
  // resources match the domain.
  rpc CheckCloudIdentityAccountsExist(CheckCloudIdentityAccountsExistRequest)
      returns (CheckCloudIdentityAccountsExistResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}:checkCloudIdentityAccountsExist"
      body: "*"
    };
  }

  // Creates a new [Customer][google.cloud.channel.v1.Customer] resource under
  // the reseller or distributor account.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED:
  //     * The reseller account making the request is different from the
  //     reseller account in the API request.
  //     * You are not authorized to create a customer. See
  //     https://support.google.com/channelservices/answer/9759265
  // * INVALID_ARGUMENT:
  //     * Required request parameters are missing or invalid.
  //     * Domain field value doesn't match the primary email domain.
  //
  // Return value:
  // The newly created [Customer][google.cloud.channel.v1.Customer] resource.
  rpc CreateCustomer(CreateCustomerRequest) returns (Customer) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}/customers"
      body: "customer"
      additional_bindings {
        post: "/v1/{parent=accounts/*/channelPartnerLinks/*}/customers"
        body: "customer"
      }
    };
  }

  // Updates an existing [Customer][google.cloud.channel.v1.Customer] resource
  // for the reseller or distributor.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: No [Customer][google.cloud.channel.v1.Customer] resource found
  // for the name in the request.
  //
  // Return value:
  // The updated [Customer][google.cloud.channel.v1.Customer] resource.
  rpc UpdateCustomer(UpdateCustomerRequest) returns (Customer) {
    option (google.api.http) = {
      patch: "/v1/{customer.name=accounts/*/customers/*}"
      body: "customer"
      additional_bindings {
        patch: "/v1/{customer.name=accounts/*/channelPartnerLinks/*/customers/*}"
        body: "customer"
      }
    };
  }

  // Deletes the given [Customer][google.cloud.channel.v1.Customer] permanently.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The account making the request does not own
  // this customer.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * FAILED_PRECONDITION: The customer has existing entitlements.
  // * NOT_FOUND: No [Customer][google.cloud.channel.v1.Customer] resource found
  // for the name in the request.
  rpc DeleteCustomer(DeleteCustomerRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=accounts/*/customers/*}"
      additional_bindings {
        delete: "/v1/{name=accounts/*/channelPartnerLinks/*/customers/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Imports a [Customer][google.cloud.channel.v1.Customer] from the Cloud
  // Identity associated with the provided Cloud Identity ID or domain before a
  // TransferEntitlements call. If a linked Customer already exists and
  // overwrite_if_exists is true, it will update that Customer's data.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED:
  //     * The reseller account making the request is different from the
  //     reseller account in the API request.
  //     * You are not authorized to import the customer. See
  //     https://support.google.com/channelservices/answer/9759265
  // * NOT_FOUND: Cloud Identity doesn't exist or was deleted.
  // * INVALID_ARGUMENT: Required parameters are missing, or the auth_token is
  // expired or invalid.
  // * ALREADY_EXISTS: A customer already exists and has conflicting critical
  // fields. Requires an overwrite.
  //
  // Return value:
  // The [Customer][google.cloud.channel.v1.Customer].
  rpc ImportCustomer(ImportCustomerRequest) returns (Customer) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}/customers:import"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=accounts/*/channelPartnerLinks/*}/customers:import"
        body: "*"
      }
    };
  }

  // Creates a Cloud Identity for the given customer using the customer's
  // information, or the information provided here.
  //
  // Possible error codes:
  //
  // *  PERMISSION_DENIED:
  //      * The customer doesn't belong to the reseller.
  //      * You are not authorized to provision cloud identity id. See
  //      https://support.google.com/channelservices/answer/9759265
  // *  INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // *  NOT_FOUND: The customer was not found.
  // *  ALREADY_EXISTS: The customer's primary email already exists. Retry
  //    after changing the customer's primary contact email.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata contains an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc ProvisionCloudIdentity(ProvisionCloudIdentityRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{customer=accounts/*/customers/*}:provisionCloudIdentity"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Customer"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists [Entitlement][google.cloud.channel.v1.Entitlement]s belonging to a
  // customer.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  //
  // Return value:
  // A list of the customer's
  // [Entitlement][google.cloud.channel.v1.Entitlement]s.
  rpc ListEntitlements(ListEntitlementsRequest)
      returns (ListEntitlementsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*/customers/*}/entitlements"
    };
  }

  // List [TransferableSku][google.cloud.channel.v1.TransferableSku]s of a
  // customer based on the Cloud Identity ID or Customer Name in the request.
  //
  // Use this method to list the entitlements information of an
  // unowned customer. You should provide the customer's
  // Cloud Identity ID or Customer Name.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED:
  //     * The customer doesn't belong to the reseller and has no auth token.
  //     * The supplied auth token is invalid.
  //     * The reseller account making the request is different
  //     from the reseller account in the query.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  //
  // Return value:
  // A list of the customer's
  // [TransferableSku][google.cloud.channel.v1.TransferableSku].
  rpc ListTransferableSkus(ListTransferableSkusRequest)
      returns (ListTransferableSkusResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}:listTransferableSkus"
      body: "*"
    };
  }

  // List [TransferableOffer][google.cloud.channel.v1.TransferableOffer]s of a
  // customer based on Cloud Identity ID or Customer Name in the request.
  //
  // Use this method when a reseller gets the entitlement information of an
  // unowned customer. The reseller should provide the customer's
  // Cloud Identity ID or Customer Name.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED:
  //     * The customer doesn't belong to the reseller and has no auth token.
  //     * The customer provided incorrect reseller information when generating
  //     auth token.
  //     * The reseller account making the request is different
  //     from the reseller account in the query.
  //     * The reseller is not authorized to transact on this Product. See
  //     https://support.google.com/channelservices/answer/9759265
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  //
  // Return value:
  // List of [TransferableOffer][google.cloud.channel.v1.TransferableOffer] for
  // the given customer and SKU.
  rpc ListTransferableOffers(ListTransferableOffersRequest)
      returns (ListTransferableOffersResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}:listTransferableOffers"
      body: "*"
    };
  }

  // Returns the requested [Entitlement][google.cloud.channel.v1.Entitlement]
  // resource.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: The customer entitlement was not found.
  //
  // Return value:
  // The requested [Entitlement][google.cloud.channel.v1.Entitlement] resource.
  rpc GetEntitlement(GetEntitlementRequest) returns (Entitlement) {
    option (google.api.http) = {
      get: "/v1/{name=accounts/*/customers/*/entitlements/*}"
    };
  }

  // Creates an entitlement for a customer.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED:
  //     * The customer doesn't belong to the reseller.
  //     * The reseller is not authorized to transact on this Product. See
  //     https://support.google.com/channelservices/answer/9759265
  // * INVALID_ARGUMENT:
  //     * Required request parameters are missing or invalid.
  //     * There is already a customer entitlement for a SKU from the same
  //     product family.
  // * INVALID_VALUE: Make sure the OfferId is valid. If it is, contact
  // Google Channel support for further troubleshooting.
  // * NOT_FOUND: The customer or offer resource was not found.
  // * ALREADY_EXISTS:
  //     * The SKU was already purchased for the customer.
  //     * The customer's primary email already exists. Retry
  //     after changing the customer's primary contact email.
  // * CONDITION_NOT_MET or FAILED_PRECONDITION:
  //     * The domain required for purchasing a SKU has not been verified.
  //     * A pre-requisite SKU required to purchase an Add-On SKU is missing.
  //     For example, Google Workspace Business Starter is required to purchase
  //     Vault or Drive.
  //     * (Developer accounts only) Reseller and resold domain must meet the
  //     following naming requirements:
  //         * Domain names must start with goog-test.
  //         * Domain names must include the reseller domain.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc CreateEntitlement(CreateEntitlementRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*/customers/*}/entitlements"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Entitlement"
      metadata_type: "OperationMetadata"
    };
  }

  // Change parameters of the entitlement.
  //
  // An entitlement update is a long-running operation and it updates the
  // entitlement as a result of fulfillment.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // For example, the number of seats being changed is greater than the allowed
  // number of max seats, or decreasing seats for a commitment based plan.
  // * NOT_FOUND: Entitlement resource not found.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc ChangeParameters(ChangeParametersRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=accounts/*/customers/*/entitlements/*}:changeParameters"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Entitlement"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the renewal settings for an existing customer entitlement.
  //
  // An entitlement update is a long-running operation and it updates the
  // entitlement as a result of fulfillment.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: Entitlement resource not found.
  // * NOT_COMMITMENT_PLAN: Renewal Settings are only applicable for a
  // commitment plan. Can't enable or disable renewals for non-commitment plans.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  //   Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc ChangeRenewalSettings(ChangeRenewalSettingsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=accounts/*/customers/*/entitlements/*}:changeRenewalSettings"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Entitlement"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the Offer for an existing customer entitlement.
  //
  // An entitlement update is a long-running operation and it updates the
  // entitlement as a result of fulfillment.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: Offer or Entitlement resource not found.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc ChangeOffer(ChangeOfferRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=accounts/*/customers/*/entitlements/*}:changeOffer"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Entitlement"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts paid service for a trial entitlement.
  //
  // Starts paid service for a trial entitlement immediately. This method is
  // only applicable if a plan is set up for a trial entitlement but has some
  // trial days remaining.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: Entitlement resource not found.
  // * FAILED_PRECONDITION/NOT_IN_TRIAL: This method only works for
  // entitlement on trial plans.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc StartPaidService(StartPaidServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=accounts/*/customers/*/entitlements/*}:startPaidService"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Entitlement"
      metadata_type: "OperationMetadata"
    };
  }

  // Suspends a previously fulfilled entitlement.
  //
  // An entitlement suspension is a long-running operation.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: Entitlement resource not found.
  // * NOT_ACTIVE: Entitlement is not active.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc SuspendEntitlement(SuspendEntitlementRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=accounts/*/customers/*/entitlements/*}:suspend"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Entitlement"
      metadata_type: "OperationMetadata"
    };
  }

  // Cancels a previously fulfilled entitlement.
  //
  // An entitlement cancellation is a long-running operation.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * FAILED_PRECONDITION: There are Google Cloud projects linked to the
  // Google Cloud entitlement's Cloud Billing subaccount.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: Entitlement resource not found.
  // * DELETION_TYPE_NOT_ALLOWED: Cancel is only allowed for Google Workspace
  // add-ons, or entitlements for Google Cloud's development platform.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The response will contain
  // google.protobuf.Empty on success. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc CancelEntitlement(CancelEntitlementRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=accounts/*/customers/*/entitlements/*}:cancel"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Activates a previously suspended entitlement. Entitlements suspended for
  // pending ToS acceptance can't be activated using this method.
  //
  // An entitlement activation is a long-running operation and it updates
  // the state of the customer entitlement.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: Entitlement resource not found.
  // * SUSPENSION_NOT_RESELLER_INITIATED: Can only activate reseller-initiated
  // suspensions and entitlements that have accepted the TOS.
  // * NOT_SUSPENDED: Can only activate suspended entitlements not in an ACTIVE
  // state.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc ActivateEntitlement(ActivateEntitlementRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=accounts/*/customers/*/entitlements/*}:activate"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Entitlement"
      metadata_type: "OperationMetadata"
    };
  }

  // Transfers customer entitlements to new reseller.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED:
  //     * The customer doesn't belong to the reseller.
  //     * The reseller is not authorized to transact on this Product. See
  //     https://support.google.com/channelservices/answer/9759265
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: The customer or offer resource was not found.
  // * ALREADY_EXISTS: The SKU was already transferred for the customer.
  // * CONDITION_NOT_MET or FAILED_PRECONDITION:
  //     * The SKU requires domain verification to transfer, but the domain is
  //     not verified.
  //     * An Add-On SKU (example, Vault or Drive) is missing the
  //     pre-requisite SKU (example, G Suite Basic).
  //     * (Developer accounts only) Reseller and resold domain must meet the
  //     following naming requirements:
  //         * Domain names must start with goog-test.
  //         * Domain names must include the reseller domain.
  //     * Specify all transferring entitlements.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc TransferEntitlements(TransferEntitlementsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*/customers/*}:transferEntitlements"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "TransferEntitlementsResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Transfers customer entitlements from their current reseller to Google.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: The customer or offer resource was not found.
  // * ALREADY_EXISTS: The SKU was already transferred for the customer.
  // * CONDITION_NOT_MET or FAILED_PRECONDITION:
  //     * The SKU requires domain verification to transfer, but the domain is
  //     not verified.
  //     * An Add-On SKU (example, Vault or Drive) is missing the
  //     pre-requisite SKU (example, G Suite Basic).
  //     * (Developer accounts only) Reseller and resold domain must meet the
  //     following naming requirements:
  //         * Domain names must start with goog-test.
  //         * Domain names must include the reseller domain.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The ID of a long-running operation.
  //
  // To get the results of the operation, call the GetOperation method of
  // CloudChannelOperationsService. The response will contain
  // google.protobuf.Empty on success. The Operation metadata will contain an
  // instance of [OperationMetadata][google.cloud.channel.v1.OperationMetadata].
  rpc TransferEntitlementsToGoogle(TransferEntitlementsToGoogleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*/customers/*}:transferEntitlementsToGoogle"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // List [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink]s
  // belonging to a distributor. You must be a distributor to call this method.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  //
  // Return value:
  // The list of the distributor account's
  // [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink] resources.
  rpc ListChannelPartnerLinks(ListChannelPartnerLinksRequest)
      returns (ListChannelPartnerLinksResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*}/channelPartnerLinks"
    };
  }

  // Returns the requested
  // [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink] resource.
  // You must be a distributor to call this method.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: ChannelPartnerLink resource not found because of an
  // invalid channel partner link name.
  //
  // Return value:
  // The [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink]
  // resource.
  rpc GetChannelPartnerLink(GetChannelPartnerLinkRequest)
      returns (ChannelPartnerLink) {
    option (google.api.http) = {
      get: "/v1/{name=accounts/*/channelPartnerLinks/*}"
    };
  }

  // Initiates a channel partner link between a distributor and a reseller, or
  // between resellers in an n-tier reseller channel.
  // Invited partners need to follow the invite_link_uri provided in the
  // response to accept. After accepting the invitation, a link is set up
  // between the two parties.
  // You must be a distributor to call this method.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * ALREADY_EXISTS: The ChannelPartnerLink sent in the request already
  // exists.
  // * NOT_FOUND: No Cloud Identity customer exists for provided domain.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The new [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink]
  // resource.
  rpc CreateChannelPartnerLink(CreateChannelPartnerLinkRequest)
      returns (ChannelPartnerLink) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}/channelPartnerLinks"
      body: "channel_partner_link"
    };
  }

  // Updates a channel partner link. Distributors call this method to change a
  // link's status. For example, to suspend a partner link.
  // You must be a distributor to call this method.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request is different
  // from the reseller account in the API request.
  // * INVALID_ARGUMENT:
  //     * Required request parameters are missing or invalid.
  //     * Link state cannot change from invited to active or suspended.
  //     * Cannot send reseller_cloud_identity_id, invite_url, or name in update
  //     mask.
  // * NOT_FOUND: ChannelPartnerLink resource not found.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The updated
  // [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink] resource.
  rpc UpdateChannelPartnerLink(UpdateChannelPartnerLinkRequest)
      returns (ChannelPartnerLink) {
    option (google.api.http) = {
      patch: "/v1/{name=accounts/*/channelPartnerLinks/*}"
      body: "*"
    };
  }

  // Gets information about how a Reseller modifies their bill before sending
  // it to a Customer.
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different.
  // * NOT_FOUND: The
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // was not found.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // resource, otherwise returns an error.
  rpc GetCustomerRepricingConfig(GetCustomerRepricingConfigRequest)
      returns (CustomerRepricingConfig) {
    option (google.api.http) = {
      get: "/v1/{name=accounts/*/customers/*/customerRepricingConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists information about how a Reseller modifies their bill before sending
  // it to a Customer.
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different.
  // * NOT_FOUND: The
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // specified does not exist or is not associated with the given account.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // resources. The data for each resource is displayed in the ascending order
  // of:
  //
  // * Customer ID
  // * [RepricingConfig.EntitlementGranularity.entitlement][google.cloud.channel.v1.RepricingConfig.EntitlementGranularity.entitlement]
  // * [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // * [CustomerRepricingConfig.update_time][google.cloud.channel.v1.CustomerRepricingConfig.update_time]
  //
  // If unsuccessful, returns an error.
  rpc ListCustomerRepricingConfigs(ListCustomerRepricingConfigsRequest)
      returns (ListCustomerRepricingConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*/customers/*}/customerRepricingConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a CustomerRepricingConfig. Call this method to set modifications
  // for a specific customer's bill. You can only create configs if the
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // is a future month. If needed, you can create a config for the current
  // month, with some restrictions.
  //
  // When creating a config for a future month, make sure there are no existing
  // configs for that
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month].
  //
  // The following restrictions are for creating configs in the current month.
  //
  // * This functionality is reserved for recovering from an erroneous config,
  // and should not be used for regular business cases.
  // * The new config will not modify exports used with other configs.
  // Changes to the config may be immediate, but may take up to 24 hours.
  // * There is a limit of ten configs for any
  // [RepricingConfig.EntitlementGranularity.entitlement][google.cloud.channel.v1.RepricingConfig.EntitlementGranularity.entitlement],
  // for any
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month].
  // * The contained
  // [CustomerRepricingConfig.repricing_config][google.cloud.channel.v1.CustomerRepricingConfig.repricing_config]
  // value must be different from the value used in the current config for a
  // [RepricingConfig.EntitlementGranularity.entitlement][google.cloud.channel.v1.RepricingConfig.EntitlementGranularity.entitlement].
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different.
  // * INVALID_ARGUMENT: Missing or invalid required parameters in the
  // request. Also displays if the updated config is for the current month or
  // past months.
  // * NOT_FOUND: The
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // specified does not exist or is not associated with the given account.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the updated
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // resource, otherwise returns an error.
  rpc CreateCustomerRepricingConfig(CreateCustomerRepricingConfigRequest)
      returns (CustomerRepricingConfig) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*/customers/*}/customerRepricingConfigs"
      body: "customer_repricing_config"
    };
    option (google.api.method_signature) = "parent,customer_repricing_config";
  }

  // Updates a CustomerRepricingConfig. Call this method to set modifications
  // for a specific customer's bill. This method overwrites the existing
  // CustomerRepricingConfig.
  //
  // You can only update configs if the
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // is a future month. To make changes to configs for the current month, use
  // [CreateCustomerRepricingConfig][google.cloud.channel.v1.CloudChannelService.CreateCustomerRepricingConfig],
  // taking note of its restrictions. You cannot update the
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month].
  //
  // When updating a config in the future:
  //
  // * This config must already exist.
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different.
  // * INVALID_ARGUMENT: Missing or invalid required parameters in the
  // request. Also displays if the updated config is for the current month or
  // past months.
  // * NOT_FOUND: The
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // specified does not exist or is not associated with the given account.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the updated
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // resource, otherwise returns an error.
  rpc UpdateCustomerRepricingConfig(UpdateCustomerRepricingConfigRequest)
      returns (CustomerRepricingConfig) {
    option (google.api.http) = {
      patch: "/v1/{customer_repricing_config.name=accounts/*/customers/*/customerRepricingConfigs/*}"
      body: "customer_repricing_config"
    };
    option (google.api.method_signature) = "customer_repricing_config";
  }

  // Deletes the given
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // permanently. You can only delete configs if their
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // is set to a date after the current month.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The account making the request does not own
  // this customer.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * FAILED_PRECONDITION: The
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // is active or in the past.
  // * NOT_FOUND: No
  // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
  // found for the name in the request.
  rpc DeleteCustomerRepricingConfig(DeleteCustomerRepricingConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=accounts/*/customers/*/customerRepricingConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets information about how a Distributor modifies their bill before sending
  // it to a ChannelPartner.
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different.
  // * NOT_FOUND: The
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // was not found.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // resource, otherwise returns an error.
  rpc GetChannelPartnerRepricingConfig(GetChannelPartnerRepricingConfigRequest)
      returns (ChannelPartnerRepricingConfig) {
    option (google.api.http) = {
      get: "/v1/{name=accounts/*/channelPartnerLinks/*/channelPartnerRepricingConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists information about how a Reseller modifies their bill before sending
  // it to a ChannelPartner.
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different.
  // * NOT_FOUND: The
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // specified does not exist or is not associated with the given account.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // resources. The data for each resource is displayed in the ascending order
  // of:
  //
  // * Channel Partner ID
  // * [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // * [ChannelPartnerRepricingConfig.update_time][google.cloud.channel.v1.ChannelPartnerRepricingConfig.update_time]
  //
  // If unsuccessful, returns an error.
  rpc ListChannelPartnerRepricingConfigs(
      ListChannelPartnerRepricingConfigsRequest)
      returns (ListChannelPartnerRepricingConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*/channelPartnerLinks/*}/channelPartnerRepricingConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a ChannelPartnerRepricingConfig. Call this method to set
  // modifications for a specific ChannelPartner's bill. You can only create
  // configs if the
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // is a future month. If needed, you can create a config for the current
  // month, with some restrictions.
  //
  // When creating a config for a future month, make sure there are no existing
  // configs for that
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month].
  //
  // The following restrictions are for creating configs in the current month.
  //
  // * This functionality is reserved for recovering from an erroneous config,
  // and should not be used for regular business cases.
  // * The new config will not modify exports used with other configs.
  // Changes to the config may be immediate, but may take up to 24 hours.
  // * There is a limit of ten configs for any ChannelPartner or
  // [RepricingConfig.EntitlementGranularity.entitlement][google.cloud.channel.v1.RepricingConfig.EntitlementGranularity.entitlement],
  // for any
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month].
  // * The contained
  // [ChannelPartnerRepricingConfig.repricing_config][google.cloud.channel.v1.ChannelPartnerRepricingConfig.repricing_config]
  // value must be different from the value used in the current config for a
  // ChannelPartner.
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different.
  // * INVALID_ARGUMENT: Missing or invalid required parameters in the
  // request. Also displays if the updated config is for the current month or
  // past months.
  // * NOT_FOUND: The
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // specified does not exist or is not associated with the given account.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the updated
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // resource, otherwise returns an error.
  rpc CreateChannelPartnerRepricingConfig(
      CreateChannelPartnerRepricingConfigRequest)
      returns (ChannelPartnerRepricingConfig) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*/channelPartnerLinks/*}/channelPartnerRepricingConfigs"
      body: "channel_partner_repricing_config"
    };
    option (google.api.method_signature) =
        "parent,channel_partner_repricing_config";
  }

  // Updates a ChannelPartnerRepricingConfig. Call this method to set
  // modifications for a specific ChannelPartner's bill. This method overwrites
  // the existing CustomerRepricingConfig.
  //
  // You can only update configs if the
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // is a future month. To make changes to configs for the current month, use
  // [CreateChannelPartnerRepricingConfig][google.cloud.channel.v1.CloudChannelService.CreateChannelPartnerRepricingConfig],
  // taking note of its restrictions. You cannot update the
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month].
  //
  // When updating a config in the future:
  //
  // * This config must already exist.
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different.
  // * INVALID_ARGUMENT: Missing or invalid required parameters in the
  // request. Also displays if the updated config is for the current month or
  // past months.
  // * NOT_FOUND: The
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // specified does not exist or is not associated with the given account.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the updated
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // resource, otherwise returns an error.
  rpc UpdateChannelPartnerRepricingConfig(
      UpdateChannelPartnerRepricingConfigRequest)
      returns (ChannelPartnerRepricingConfig) {
    option (google.api.http) = {
      patch: "/v1/{channel_partner_repricing_config.name=accounts/*/channelPartnerLinks/*/channelPartnerRepricingConfigs/*}"
      body: "channel_partner_repricing_config"
    };
    option (google.api.method_signature) = "channel_partner_repricing_config";
  }

  // Deletes the given
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // permanently. You can only delete configs if their
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // is set to a date after the current month.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The account making the request does not own
  // this customer.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * FAILED_PRECONDITION: The
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // is active or in the past.
  // * NOT_FOUND: No
  // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
  // found for the name in the request.
  rpc DeleteChannelPartnerRepricingConfig(
      DeleteChannelPartnerRepricingConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=accounts/*/channelPartnerLinks/*/channelPartnerRepricingConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the Rebilling supported SKU groups the account is authorized to
  // sell.
  // Reference: https://cloud.google.com/skus/sku-groups
  //
  // Possible Error Codes:
  //
  // * PERMISSION_DENIED: If the account making the request and the account
  // being queried are different, or the account doesn't exist.
  // * INTERNAL: Any non-user error related to technical issues in the
  // backend. In this case, contact Cloud Channel support.
  //
  // Return Value:
  // If successful, the [SkuGroup][google.cloud.channel.v1.SkuGroup] resources.
  // The data for each resource is displayed in the alphabetical order of SKU
  // group display name.
  // The data for each resource is displayed in the ascending order of
  // [SkuGroup.display_name][google.cloud.channel.v1.SkuGroup.display_name]
  //
  // If unsuccessful, returns an error.
  rpc ListSkuGroups(ListSkuGroupsRequest) returns (ListSkuGroupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*}/skuGroups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the Billable SKUs in a given SKU group.
  //
  // Possible error codes:
  // PERMISSION_DENIED: If the account making the request and the account
  // being queried for are different, or the account doesn't exist.
  // INVALID_ARGUMENT: Missing or invalid required parameters in the
  // request.
  // INTERNAL: Any non-user error related to technical issue in the
  // backend. In this case, contact cloud channel support.
  //
  // Return Value:
  // If successful, the [BillableSku][google.cloud.channel.v1.BillableSku]
  // resources. The data for each resource is displayed in the ascending order
  // of:
  //
  // * [BillableSku.service_display_name][google.cloud.channel.v1.BillableSku.service_display_name]
  // * [BillableSku.sku_display_name][google.cloud.channel.v1.BillableSku.sku_display_name]
  //
  // If unsuccessful, returns an error.
  rpc ListSkuGroupBillableSkus(ListSkuGroupBillableSkusRequest)
      returns (ListSkuGroupBillableSkusResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*/skuGroups/*}/billableSkus"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the requested [Offer][google.cloud.channel.v1.Offer] resource.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The entitlement doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: Entitlement or offer was not found.
  //
  // Return value:
  // The [Offer][google.cloud.channel.v1.Offer] resource.
  rpc LookupOffer(LookupOfferRequest) returns (Offer) {
    option (google.api.http) = {
      get: "/v1/{entitlement=accounts/*/customers/*/entitlements/*}:lookupOffer"
    };
  }

  // Lists the Products the reseller is authorized to sell.
  //
  // Possible error codes:
  //
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  rpc ListProducts(ListProductsRequest) returns (ListProductsResponse) {
    option (google.api.http) = {
      get: "/v1/products"
    };
  }

  // Lists the SKUs for a product the reseller is authorized to sell.
  //
  // Possible error codes:
  //
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  rpc ListSkus(ListSkusRequest) returns (ListSkusResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=products/*}/skus"
    };
  }

  // Lists the Offers the reseller can sell.
  //
  // Possible error codes:
  //
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  rpc ListOffers(ListOffersRequest) returns (ListOffersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*}/offers"
    };
  }

  // Lists the following:
  //
  // * SKUs that you can purchase for a customer
  // * SKUs that you can upgrade or downgrade for an entitlement.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  rpc ListPurchasableSkus(ListPurchasableSkusRequest)
      returns (ListPurchasableSkusResponse) {
    option (google.api.http) = {
      get: "/v1/{customer=accounts/*/customers/*}:listPurchasableSkus"
    };
  }

  // Lists the following:
  //
  // * Offers that you can purchase for a customer.
  // * Offers that you can change for an entitlement.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED:
  //     * The customer doesn't belong to the reseller
  //     * The reseller is not authorized to transact on this Product. See
  //     https://support.google.com/channelservices/answer/9759265
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  rpc ListPurchasableOffers(ListPurchasableOffersRequest)
      returns (ListPurchasableOffersResponse) {
    option (google.api.http) = {
      get: "/v1/{customer=accounts/*/customers/*}:listPurchasableOffers"
    };
  }

  // Lists the billing accounts that are eligible to purchase particular SKUs
  // for a given customer.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The customer doesn't belong to the reseller.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  //
  // Return value:
  // Based on the provided list of SKUs, returns a list of SKU groups that must
  // be purchased using the same billing account and the billing accounts
  // eligible to purchase each SKU group.
  rpc QueryEligibleBillingAccounts(QueryEligibleBillingAccountsRequest)
      returns (QueryEligibleBillingAccountsResponse) {
    option (google.api.http) = {
      get: "/v1/{customer=accounts/*/customers/*}:queryEligibleBillingAccounts"
    };
  }

  // Registers a service account with subscriber privileges on the Cloud Pub/Sub
  // topic for this Channel Services account. After you create a
  // subscriber, you get the events through
  // [SubscriberEvent][google.cloud.channel.v1.SubscriberEvent]
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request and the
  // provided reseller account are different, or the impersonated user
  // is not a super admin.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The topic name with the registered service email address.
  rpc RegisterSubscriber(RegisterSubscriberRequest)
      returns (RegisterSubscriberResponse) {
    option (google.api.http) = {
      post: "/v1/{account=accounts/*}:register"
      body: "*"
    };
  }

  // Unregisters a service account with subscriber privileges on the Cloud
  // Pub/Sub topic created for this Channel Services account. If there are no
  // service accounts left with subscriber privileges, this deletes the topic.
  // You can call ListSubscribers to check for these accounts.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request and the
  // provided reseller account are different, or the impersonated user
  // is not a super admin.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: The topic resource doesn't exist.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // The topic name that unregistered the service email address.
  // Returns a success response if the service email address wasn't registered
  // with the topic.
  rpc UnregisterSubscriber(UnregisterSubscriberRequest)
      returns (UnregisterSubscriberResponse) {
    option (google.api.http) = {
      post: "/v1/{account=accounts/*}:unregister"
      body: "*"
    };
  }

  // Lists service accounts with subscriber privileges on the Cloud Pub/Sub
  // topic created for this Channel Services account.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request and the
  // provided reseller account are different, or the impersonated user
  // is not a super admin.
  // * INVALID_ARGUMENT: Required request parameters are missing or invalid.
  // * NOT_FOUND: The topic resource doesn't exist.
  // * INTERNAL: Any non-user error related to a technical issue in the
  // backend. Contact Cloud Channel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // Contact Cloud Channel support.
  //
  // Return value:
  // A list of service email addresses.
  rpc ListSubscribers(ListSubscribersRequest)
      returns (ListSubscribersResponse) {
    option (google.api.http) = {
      get: "/v1/{account=accounts/*}:listSubscribers"
    };
  }

  // List entitlement history.
  //
  // Possible error codes:
  //
  // * PERMISSION_DENIED: The reseller account making the request and the
  // provided reseller account are different.
  // * INVALID_ARGUMENT: Missing or invalid required fields in the request.
  // * NOT_FOUND: The parent resource doesn't exist. Usually the result of an
  // invalid name parameter.
  // * INTERNAL: Any non-user error related to a technical issue in the backend.
  // In this case, contact CloudChannel support.
  // * UNKNOWN: Any non-user error related to a technical issue in the backend.
  // In this case, contact Cloud Channel support.
  //
  // Return value:
  // List of [EntitlementChange][google.cloud.channel.v1.EntitlementChange]s.
  rpc ListEntitlementChanges(ListEntitlementChangesRequest)
      returns (ListEntitlementChangesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*/customers/*/entitlements/*}:listEntitlementChanges"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [CloudChannelService.CheckCloudIdentityAccountsExist][google.cloud.channel.v1.CloudChannelService.CheckCloudIdentityAccountsExist].
message CheckCloudIdentityAccountsExistRequest {
  // Required. The reseller account's resource name.
  // Parent uses the format: accounts/{account_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Domain to fetch for Cloud Identity account customers, including
  // domain and team customers. For team customers, please use the domain for
  // their emails.
  string domain = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Primary admin email to fetch for Cloud Identity account team
  // customer.
  string primary_admin_email = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Entity representing a Cloud Identity account that may be
// associated with a Channel Services API partner.
message CloudIdentityCustomerAccount {
  // Returns true if a Cloud Identity account exists for a specific domain.
  bool existing = 1;

  // Returns true if the Cloud Identity account is associated with a customer
  // of the Channel Services partner.
  bool owned = 2;

  // If owned = true, the name of the customer that owns the Cloud Identity
  // account.
  // Customer_name uses the format:
  // accounts/{account_id}/customers/{customer_id}
  string customer_name = 3;

  // If existing = true, the Cloud Identity ID of the customer.
  string customer_cloud_identity_id = 4;

  // If existing = true, the type of the customer.
  CloudIdentityInfo.CustomerType customer_type = 5;

  // If existing = true, and is 2-tier customer, the channel partner of the
  // customer.
  string channel_partner_cloud_identity_id = 6;
}

// Response message for
// [CloudChannelService.CheckCloudIdentityAccountsExist][google.cloud.channel.v1.CloudChannelService.CheckCloudIdentityAccountsExist].
message CheckCloudIdentityAccountsExistResponse {
  // The Cloud Identity accounts associated with the domain.
  repeated CloudIdentityCustomerAccount cloud_identity_accounts = 1;
}

// Request message for
// [CloudChannelService.ListCustomers][google.cloud.channel.v1.CloudChannelService.ListCustomers]
message ListCustomersRequest {
  // Required. The resource name of the reseller account to list customers from.
  // Parent uses the format: accounts/{account_id}.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of customers to return. The service may return
  // fewer than this value. If unspecified, returns at most 10 customers. The
  // maximum value is 50.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results other than the first page.
  // Obtained through
  // [ListCustomersResponse.next_page_token][google.cloud.channel.v1.ListCustomersResponse.next_page_token]
  // of the previous
  // [CloudChannelService.ListCustomers][google.cloud.channel.v1.CloudChannelService.ListCustomers]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filters applied to the [CloudChannelService.ListCustomers]
  // results. See
  // https://cloud.google.com/channel/docs/concepts/google-cloud/filter-customers
  // for more information.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelService.ListCustomers][google.cloud.channel.v1.CloudChannelService.ListCustomers].
message ListCustomersResponse {
  // The customers belonging to a reseller or distributor.
  repeated Customer customers = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListCustomersRequest.page_token][google.cloud.channel.v1.ListCustomersRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [CloudChannelService.GetCustomer][google.cloud.channel.v1.CloudChannelService.GetCustomer].
message GetCustomerRequest {
  // Required. The resource name of the customer to retrieve.
  // Name uses the format: accounts/{account_id}/customers/{customer_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];
}

// Request message for
// [CloudChannelService.CreateCustomer][google.cloud.channel.v1.CloudChannelService.CreateCustomer]
message CreateCustomerRequest {
  // Required. The resource name of reseller account in which to create the
  // customer. Parent uses the format: accounts/{account_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The customer to create.
  Customer customer = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [CloudChannelService.UpdateCustomer][google.cloud.channel.v1.CloudChannelService.UpdateCustomer].
message UpdateCustomerRequest {
  // Required. New contents of the customer.
  Customer customer = 2 [(google.api.field_behavior) = REQUIRED];

  // The update mask that applies to the resource.
  // Optional.
  google.protobuf.FieldMask update_mask = 3;
}

// Request message for
// [CloudChannelService.DeleteCustomer][google.cloud.channel.v1.CloudChannelService.DeleteCustomer].
message DeleteCustomerRequest {
  // Required. The resource name of the customer to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];
}

// Request message for
// [CloudChannelService.ImportCustomer][google.cloud.channel.v1.CloudChannelService.ImportCustomer]
message ImportCustomerRequest {
  // Specifies the identity of the transfer customer.
  // A customer's cloud_identity_id or domain is required to look up the
  // customer's Cloud Identity. For Team customers, only the cloud_identity_id
  // option is valid.
  oneof customer_identity {
    // Required. Customer domain.
    string domain = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Customer's Cloud Identity ID
    string cloud_identity_id = 3 [(google.api.field_behavior) = REQUIRED];

    // Required. Customer's primary admin email.
    string primary_admin_email = 8 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. The resource name of the reseller's account.
  // Parent takes the format: accounts/{account_id} or
  // accounts/{account_id}/channelPartnerLinks/{channel_partner_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The super admin of the resold customer generates this token to
  // authorize a reseller to access their Cloud Identity and purchase
  // entitlements on their behalf. You can omit this token after authorization.
  // See https://support.google.com/a/answer/7643790 for more details.
  string auth_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Choose to overwrite an existing customer if found.
  // This must be set to true if there is an existing customer with a
  // conflicting region code or domain.
  bool overwrite_if_exists = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. Cloud Identity ID of a channel partner who will be the direct
  // reseller for the customer's order. This field is required for 2-tier
  // transfer scenarios and can be provided via the request Parent binding as
  // well.
  string channel_partner_id = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the customer that will receive imported Cloud Identity
  // information.
  // Format: accounts/{account_id}/customers/{customer_id}
  string customer = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];
}

// Request message for
// [CloudChannelService.ProvisionCloudIdentity][google.cloud.channel.v1.CloudChannelService.ProvisionCloudIdentity]
message ProvisionCloudIdentityRequest {
  // Required. Resource name of the customer.
  // Format: accounts/{account_id}/customers/{customer_id}
  string customer = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];

  // CloudIdentity-specific customer information.
  CloudIdentityInfo cloud_identity_info = 2;

  // Admin user information.
  AdminUser user = 3;

  // Validate the request and preview the review, but do not post it.
  bool validate_only = 4;
}

// Request message for
// [CloudChannelService.ListEntitlements][google.cloud.channel.v1.CloudChannelService.ListEntitlements]
message ListEntitlementsRequest {
  // Required. The resource name of the reseller's customer account to list
  // entitlements for.
  // Parent uses the format: accounts/{account_id}/customers/{customer_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];

  // Optional. Requested page size. Server might return fewer results than
  // requested. If unspecified, return at most 50 entitlements. The maximum
  // value is 100; the server will coerce values above 100.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token for a page of results other than the first page.
  // Obtained using
  // [ListEntitlementsResponse.next_page_token][google.cloud.channel.v1.ListEntitlementsResponse.next_page_token]
  // of the previous
  // [CloudChannelService.ListEntitlements][google.cloud.channel.v1.CloudChannelService.ListEntitlements]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelService.ListEntitlements][google.cloud.channel.v1.CloudChannelService.ListEntitlements].
message ListEntitlementsResponse {
  // The reseller customer's entitlements.
  repeated Entitlement entitlements = 1;

  // A token to list the next page of results.
  // Pass to
  // [ListEntitlementsRequest.page_token][google.cloud.channel.v1.ListEntitlementsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [CloudChannelService.ListTransferableSkus][google.cloud.channel.v1.CloudChannelService.ListTransferableSkus]
message ListTransferableSkusRequest {
  // Specifies the identity of transferred customer.
  // Either a cloud_identity_id of the customer or the customer name is
  // required to look up transferable SKUs.
  oneof transferred_customer_identity {
    // Customer's Cloud Identity ID
    string cloud_identity_id = 4;

    // A reseller is required to create a customer and use the resource name of
    // the created customer here.
    // Customer_name uses the format:
    // accounts/{account_id}/customers/{customer_id}
    string customer_name = 7;
  }

  // Required. The reseller account's resource name.
  // Parent uses the format: accounts/{account_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The requested page size. Server might return fewer results than requested.
  // If unspecified, returns at most 100 SKUs.
  // The maximum value is 1000; the server will coerce values above 1000.
  // Optional.
  int32 page_size = 2;

  // A token for a page of results other than the first page.
  // Obtained using
  // [ListTransferableSkusResponse.next_page_token][google.cloud.channel.v1.ListTransferableSkusResponse.next_page_token]
  // of the previous
  // [CloudChannelService.ListTransferableSkus][google.cloud.channel.v1.CloudChannelService.ListTransferableSkus]
  // call. Optional.
  string page_token = 3;

  // Optional. The super admin of the resold customer generates this token to
  // authorize a reseller to access their Cloud Identity and purchase
  // entitlements on their behalf. You can omit this token after authorization.
  // See https://support.google.com/a/answer/7643790 for more details.
  string auth_token = 5 [(google.api.field_behavior) = OPTIONAL];

  // The BCP-47 language code. For example, "en-US". The
  // response will localize in the corresponding language code, if specified.
  // The default value is "en-US".
  // Optional.
  string language_code = 6;
}

// Response message for
// [CloudChannelService.ListTransferableSkus][google.cloud.channel.v1.CloudChannelService.ListTransferableSkus].
message ListTransferableSkusResponse {
  // Information about existing SKUs for a customer that needs a transfer.
  repeated TransferableSku transferable_skus = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListTransferableSkusRequest.page_token][google.cloud.channel.v1.ListTransferableSkusRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [CloudChannelService.ListTransferableOffers][google.cloud.channel.v1.CloudChannelService.ListTransferableOffers]
message ListTransferableOffersRequest {
  // Specifies the identity of transferred customer.
  // Either a cloud_identity_id of the customer or the customer name is
  // required to look up transferrable Offers.
  oneof transferred_customer_identity {
    // Customer's Cloud Identity ID
    string cloud_identity_id = 4;

    // A reseller should create a customer and use the resource name of
    // that customer here.
    string customer_name = 5;
  }

  // Required. The resource name of the reseller's account.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Requested page size. Server might return fewer results than requested.
  // If unspecified, returns at most 100 offers.
  // The maximum value is 1000; the server will coerce values above 1000.
  int32 page_size = 2;

  // A token for a page of results other than the first page.
  // Obtained using
  // [ListTransferableOffersResponse.next_page_token][google.cloud.channel.v1.ListTransferableOffersResponse.next_page_token]
  // of the previous
  // [CloudChannelService.ListTransferableOffers][google.cloud.channel.v1.CloudChannelService.ListTransferableOffers]
  // call.
  string page_token = 3;

  // Required. The SKU to look up Offers for.
  string sku = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. The BCP-47 language code. For example, "en-US". The
  // response will localize in the corresponding language code, if specified.
  // The default value is "en-US".
  string language_code = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Billing Account to look up Offers for. Format:
  // accounts/{account_id}/billingAccounts/{billing_account_id}.
  //
  // This field is only relevant for multi-currency accounts. It should be left
  // empty for single currency accounts.
  string billing_account = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelService.ListTransferableOffers][google.cloud.channel.v1.CloudChannelService.ListTransferableOffers].
message ListTransferableOffersResponse {
  // Information about Offers for a customer that can be used for
  // transfer.
  repeated TransferableOffer transferable_offers = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListTransferableOffersRequest.page_token][google.cloud.channel.v1.ListTransferableOffersRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// TransferableOffer represents an Offer that can be used in Transfer.
// Read-only.
message TransferableOffer {
  // Offer with parameter constraints updated to allow the Transfer.
  Offer offer = 1;
}

// Request message for
// [CloudChannelService.GetEntitlement][google.cloud.channel.v1.CloudChannelService.GetEntitlement].
message GetEntitlementRequest {
  // Required. The resource name of the entitlement to retrieve.
  // Name uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Entitlement"
    }
  ];
}

// Request message for
// [CloudChannelService.ListChannelPartnerLinks][google.cloud.channel.v1.CloudChannelService.ListChannelPartnerLinks]
message ListChannelPartnerLinksRequest {
  // Required. The resource name of the reseller account for listing channel
  // partner links. Parent uses the format: accounts/{account_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Requested page size. Server might return fewer results than
  // requested. If unspecified, server will pick a default size (25). The
  // maximum value is 200; the server will coerce values above 200.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token for a page of results other than the first page.
  // Obtained using
  // [ListChannelPartnerLinksResponse.next_page_token][google.cloud.channel.v1.ListChannelPartnerLinksResponse.next_page_token]
  // of the previous
  // [CloudChannelService.ListChannelPartnerLinks][google.cloud.channel.v1.CloudChannelService.ListChannelPartnerLinks]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The level of granularity the ChannelPartnerLink will display.
  ChannelPartnerLinkView view = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelService.ListChannelPartnerLinks][google.cloud.channel.v1.CloudChannelService.ListChannelPartnerLinks].
message ListChannelPartnerLinksResponse {
  // The Channel partner links for a reseller.
  repeated ChannelPartnerLink channel_partner_links = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListChannelPartnerLinksRequest.page_token][google.cloud.channel.v1.ListChannelPartnerLinksRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [CloudChannelService.GetChannelPartnerLink][google.cloud.channel.v1.CloudChannelService.GetChannelPartnerLink].
message GetChannelPartnerLinkRequest {
  // Required. The resource name of the channel partner link to retrieve.
  // Name uses the format: accounts/{account_id}/channelPartnerLinks/{id}
  // where {id} is the Cloud Identity ID of the partner.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The level of granularity the ChannelPartnerLink will display.
  ChannelPartnerLinkView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CloudChannelService.CreateChannelPartnerLink][google.cloud.channel.v1.CloudChannelService.CreateChannelPartnerLink]
message CreateChannelPartnerLinkRequest {
  // Required. Create a channel partner link for the provided reseller account's
  // resource name.
  // Parent uses the format: accounts/{account_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The channel partner link to create.
  // Either channel_partner_link.reseller_cloud_identity_id or domain can be
  // used to create a link.
  ChannelPartnerLink channel_partner_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [CloudChannelService.UpdateChannelPartnerLink][google.cloud.channel.v1.CloudChannelService.UpdateChannelPartnerLink]
message UpdateChannelPartnerLinkRequest {
  // Required. The resource name of the channel partner link to cancel.
  // Name uses the format: accounts/{account_id}/channelPartnerLinks/{id}
  // where {id} is the Cloud Identity ID of the partner.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The channel partner link to update. Only
  // channel_partner_link.link_state is allowed for updates.
  ChannelPartnerLink channel_partner_link = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask that applies to the resource.
  // The only allowable value for an update mask is
  // channel_partner_link.link_state.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [CloudChannelService.GetCustomerRepricingConfig][google.cloud.channel.v1.CloudChannelService.GetCustomerRepricingConfig].
message GetCustomerRepricingConfigRequest {
  // Required. The resource name of the CustomerRepricingConfig.
  // Format:
  // accounts/{account_id}/customers/{customer_id}/customerRepricingConfigs/{id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/CustomerRepricingConfig"
    }
  ];
}

// Request message for
// [CloudChannelService.ListCustomerRepricingConfigs][google.cloud.channel.v1.CloudChannelService.ListCustomerRepricingConfigs].
message ListCustomerRepricingConfigsRequest {
  // Required. The resource name of the customer.
  // Parent uses the format: accounts/{account_id}/customers/{customer_id}.
  // Supports accounts/{account_id}/customers/- to retrieve configs for all
  // customers.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];

  // Optional. The maximum number of repricing configs to return. The service
  // may return fewer than this value. If unspecified, returns a maximum of 50
  // rules. The maximum value is 100; values above 100 will be coerced to 100.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results beyond the first page.
  // Obtained through
  // [ListCustomerRepricingConfigsResponse.next_page_token][google.cloud.channel.v1.ListCustomerRepricingConfigsResponse.next_page_token]
  // of the previous
  // [CloudChannelService.ListCustomerRepricingConfigs][google.cloud.channel.v1.CloudChannelService.ListCustomerRepricingConfigs]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter for [CloudChannelService.ListCustomerRepricingConfigs]
  // results (customer only). You can use this filter when you support
  // a BatchGet-like query.
  // To use the filter, you must set `parent=accounts/{account_id}/customers/-`.
  //
  // Example: customer = accounts/account_id/customers/c1 OR
  // customer = accounts/account_id/customers/c2.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelService.ListCustomerRepricingConfigs][google.cloud.channel.v1.CloudChannelService.ListCustomerRepricingConfigs].
message ListCustomerRepricingConfigsResponse {
  // The repricing configs for this channel partner.
  repeated CustomerRepricingConfig customer_repricing_configs = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListCustomerRepricingConfigsRequest.page_token][google.cloud.channel.v1.ListCustomerRepricingConfigsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [CloudChannelService.CreateCustomerRepricingConfig][google.cloud.channel.v1.CloudChannelService.CreateCustomerRepricingConfig].
message CreateCustomerRepricingConfigRequest {
  // Required. The resource name of the customer that will receive this
  // repricing config. Parent uses the format:
  // accounts/{account_id}/customers/{customer_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];

  // Required. The CustomerRepricingConfig object to update.
  CustomerRepricingConfig customer_repricing_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [CloudChannelService.UpdateCustomerRepricingConfig][google.cloud.channel.v1.CloudChannelService.UpdateCustomerRepricingConfig].
message UpdateCustomerRepricingConfigRequest {
  // Required. The CustomerRepricingConfig object to update.
  CustomerRepricingConfig customer_repricing_config = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [CloudChannelService.DeleteCustomerRepricingConfig][google.cloud.channel.v1.CloudChannelService.DeleteCustomerRepricingConfig].
message DeleteCustomerRepricingConfigRequest {
  // Required. The resource name of the customer repricing config rule to
  // delete. Format:
  // accounts/{account_id}/customers/{customer_id}/customerRepricingConfigs/{id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/CustomerRepricingConfig"
    }
  ];
}

// Request message for
// [CloudChannelService.GetChannelPartnerRepricingConfig][google.cloud.channel.v1.CloudChannelService.GetChannelPartnerRepricingConfig]
message GetChannelPartnerRepricingConfigRequest {
  // Required. The resource name of the ChannelPartnerRepricingConfig
  // Format:
  // accounts/{account_id}/channelPartnerLinks/{channel_partner_id}/channelPartnerRepricingConfigs/{id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/ChannelPartnerRepricingConfig"
    }
  ];
}

// Request message for
// [CloudChannelService.ListChannelPartnerRepricingConfigs][google.cloud.channel.v1.CloudChannelService.ListChannelPartnerRepricingConfigs].
message ListChannelPartnerRepricingConfigsRequest {
  // Required. The resource name of the account's
  // [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink]. Parent
  // uses the format:
  // accounts/{account_id}/channelPartnerLinks/{channel_partner_id}.
  // Supports accounts/{account_id}/channelPartnerLinks/- to retrieve configs
  // for all channel partners.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/ChannelPartnerLink"
    }
  ];

  // Optional. The maximum number of repricing configs to return. The service
  // may return fewer than this value. If unspecified, returns a maximum of 50
  // rules. The maximum value is 100; values above 100 will be coerced to 100.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results beyond the first page.
  // Obtained through
  // [ListChannelPartnerRepricingConfigsResponse.next_page_token][google.cloud.channel.v1.ListChannelPartnerRepricingConfigsResponse.next_page_token]
  // of the previous
  // [CloudChannelService.ListChannelPartnerRepricingConfigs][google.cloud.channel.v1.CloudChannelService.ListChannelPartnerRepricingConfigs]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter for
  // [CloudChannelService.ListChannelPartnerRepricingConfigs] results
  // (channel_partner_link only). You can use this filter when you support a
  // BatchGet-like query. To use the filter, you must set
  // `parent=accounts/{account_id}/channelPartnerLinks/-`.
  //
  // Example: `channel_partner_link =
  // accounts/account_id/channelPartnerLinks/c1` OR `channel_partner_link =
  // accounts/account_id/channelPartnerLinks/c2`.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelService.ListChannelPartnerRepricingConfigs][google.cloud.channel.v1.CloudChannelService.ListChannelPartnerRepricingConfigs].
message ListChannelPartnerRepricingConfigsResponse {
  // The repricing configs for this channel partner.
  repeated ChannelPartnerRepricingConfig channel_partner_repricing_configs = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListChannelPartnerRepricingConfigsRequest.page_token][google.cloud.channel.v1.ListChannelPartnerRepricingConfigsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [CloudChannelService.CreateChannelPartnerRepricingConfig][google.cloud.channel.v1.CloudChannelService.CreateChannelPartnerRepricingConfig].
message CreateChannelPartnerRepricingConfigRequest {
  // Required. The resource name of the ChannelPartner that will receive the
  // repricing config. Parent uses the format:
  // accounts/{account_id}/channelPartnerLinks/{channel_partner_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/ChannelPartnerLink"
    }
  ];

  // Required. The ChannelPartnerRepricingConfig object to update.
  ChannelPartnerRepricingConfig channel_partner_repricing_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [CloudChannelService.UpdateChannelPartnerRepricingConfig][google.cloud.channel.v1.CloudChannelService.UpdateChannelPartnerRepricingConfig].
message UpdateChannelPartnerRepricingConfigRequest {
  // Required. The ChannelPartnerRepricingConfig object to update.
  ChannelPartnerRepricingConfig channel_partner_repricing_config = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteChannelPartnerRepricingConfig.
message DeleteChannelPartnerRepricingConfigRequest {
  // Required. The resource name of the channel partner repricing config rule to
  // delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/ChannelPartnerRepricingConfig"
    }
  ];
}

// Request message for ListSkuGroups.
message ListSkuGroupsRequest {
  // Required. The resource name of the account from which to list SKU groups.
  // Parent uses the format: accounts/{account}.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of SKU groups to return. The service may
  // return fewer than this value. If unspecified, returns a maximum of 1000 SKU
  // groups. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results beyond the first page.
  // Obtained through
  // [ListSkuGroups.next_page_token][] of the previous
  // [CloudChannelService.ListSkuGroups][google.cloud.channel.v1.CloudChannelService.ListSkuGroups]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListSkuGroupBillableSkus.
message ListSkuGroupBillableSkusRequest {
  // Required. Resource name of the SKU group.
  // Format: accounts/{account}/skuGroups/{sku_group}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/SkuGroup"
    }
  ];

  // Optional. The maximum number of SKUs to return. The service may return
  // fewer than this value. If unspecified, returns a maximum of 100000 SKUs.
  // The maximum value is 100000; values above 100000 will be coerced to 100000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results beyond the first page.
  // Obtained through
  // [ListSkuGroupBillableSkus.next_page_token][] of the previous
  // [CloudChannelService.ListSkuGroupBillableSkus][google.cloud.channel.v1.CloudChannelService.ListSkuGroupBillableSkus]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListSkuGroups.
message ListSkuGroupsResponse {
  // The list of SKU groups requested.
  repeated SkuGroup sku_groups = 1;

  // A token to retrieve the next page of results.
  // Pass to [ListSkuGroups.page_token][] to obtain that
  // page.
  string next_page_token = 2;
}

// Response message for ListSkuGroupBillableSkus.
message ListSkuGroupBillableSkusResponse {
  // The list of billable SKUs in the requested SKU group.
  repeated BillableSku billable_skus = 1;

  // A token to retrieve the next page of results.
  // Pass to [ListSkuGroupBillableSkus.page_token][] to obtain that
  // page.
  string next_page_token = 2;
}

// Represents the SKU group information.
message SkuGroup {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/SkuGroup"
    pattern: "accounts/{account}/skuGroups/{sku_group}"
  };

  // Resource name of SKU group. Format:
  // accounts/{account}/skuGroups/{sku_group}.
  // Example:
  // "accounts/C01234/skuGroups/3d50fd57-3157-4577-a5a9-a219b8490041".
  string name = 1;

  // Unique human readable identifier for the SKU group.
  string display_name = 2;
}

// Represents the Billable SKU information.
message BillableSku {
  // Resource name of Billable SKU. Format:
  // billableSkus/{sku}.
  // Example:
  // billableSkus/6E1B-6634-470F".
  string sku = 1;

  // Unique human readable name for the SKU.
  string sku_display_name = 2;

  // Resource name of Service which contains Repricing SKU. Format:
  // services/{service}.
  // Example:
  // "services/B7D9-FDCB-15D8".
  string service = 3;

  // Unique human readable name for the Service.
  string service_display_name = 4;
}

// Request message for
// [CloudChannelService.CreateEntitlement][google.cloud.channel.v1.CloudChannelService.CreateEntitlement]
message CreateEntitlementRequest {
  // Required. The resource name of the reseller's customer account in which to
  // create the entitlement. Parent uses the format:
  // accounts/{account_id}/customers/{customer_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];

  // Required. The entitlement to create.
  Entitlement entitlement = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CloudChannelService.TransferEntitlements][google.cloud.channel.v1.CloudChannelService.TransferEntitlements].
message TransferEntitlementsRequest {
  // Required. The resource name of the reseller's customer account that will
  // receive transferred entitlements. Parent uses the format:
  // accounts/{account_id}/customers/{customer_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The new entitlements to create or transfer.
  repeated Entitlement entitlements = 2
      [(google.api.field_behavior) = REQUIRED];

  // The super admin of the resold customer generates this token to
  // authorize a reseller to access their Cloud Identity and purchase
  // entitlements on their behalf. You can omit this token after authorization.
  // See https://support.google.com/a/answer/7643790 for more details.
  string auth_token = 4;

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelService.TransferEntitlements][google.cloud.channel.v1.CloudChannelService.TransferEntitlements].
// This is put in the response field of google.longrunning.Operation.
message TransferEntitlementsResponse {
  // The transferred entitlements.
  repeated Entitlement entitlements = 1;
}

// Request message for
// [CloudChannelService.TransferEntitlementsToGoogle][google.cloud.channel.v1.CloudChannelService.TransferEntitlementsToGoogle].
message TransferEntitlementsToGoogleRequest {
  // Required. The resource name of the reseller's customer account where the
  // entitlements transfer from. Parent uses the format:
  // accounts/{account_id}/customers/{customer_id}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entitlements to transfer to Google.
  repeated Entitlement entitlements = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for [CloudChannelService.ChangeParametersRequest][].
message ChangeParametersRequest {
  // Required. The name of the entitlement to update.
  // Name uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Entitlement parameters to update. You can only change editable
  // parameters.
  //
  // To view the available Parameters for a request, refer to the
  // [Offer.parameter_definitions][google.cloud.channel.v1.Offer.parameter_definitions]
  // from the desired offer.
  repeated Parameter parameters = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Purchase order ID provided by the reseller.
  string purchase_order_id = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CloudChannelService.ChangeRenewalSettings][google.cloud.channel.v1.CloudChannelService.ChangeRenewalSettings].
message ChangeRenewalSettingsRequest {
  // Required. The name of the entitlement to update.
  // Name uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. New renewal settings.
  RenewalSettings renewal_settings = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CloudChannelService.ChangeOffer][google.cloud.channel.v1.CloudChannelService.ChangeOffer].
message ChangeOfferRequest {
  // Required. The resource name of the entitlement to update.
  // Name uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. New Offer.
  // Format: accounts/{account_id}/offers/{offer_id}.
  string offer = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Offer"
    }
  ];

  // Optional. Parameters needed to purchase the Offer. To view the available
  // Parameters refer to the
  // [Offer.parameter_definitions][google.cloud.channel.v1.Offer.parameter_definitions]
  // from the desired offer.
  repeated Parameter parameters = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Purchase order id provided by the reseller.
  string purchase_order_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The billing account resource name that is used to pay for this
  // entitlement when setting up billing on a trial subscription.
  //
  // This field is only relevant for multi-currency accounts. It should be
  // left empty for single currency accounts.
  string billing_account = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CloudChannelService.StartPaidService][google.cloud.channel.v1.CloudChannelService.StartPaidService].
message StartPaidServiceRequest {
  // Required. The name of the entitlement to start a paid service for.
  // Name uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CloudChannelService.CancelEntitlement][google.cloud.channel.v1.CloudChannelService.CancelEntitlement].
message CancelEntitlementRequest {
  // Required. The resource name of the entitlement to cancel.
  // Name uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CloudChannelService.SuspendEntitlement][google.cloud.channel.v1.CloudChannelService.SuspendEntitlement].
message SuspendEntitlementRequest {
  // Required. The resource name of the entitlement to suspend.
  // Name uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CloudChannelService.ActivateEntitlement][google.cloud.channel.v1.CloudChannelService.ActivateEntitlement].
message ActivateEntitlementRequest {
  // Required. The resource name of the entitlement to activate.
  // Name uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. You can specify an optional unique request ID, and if you need to
  // retry your request, the server will know to ignore the request if it's
  // complete.
  //
  // For example, you make an initial request and the request times out. If you
  // make the request again with the same request ID, the server can check if
  // it received the original operation with the same request ID. If it did, it
  // will ignore the second request.
  //
  // The request ID must be a valid [UUID](https://tools.ietf.org/html/rfc4122)
  // with the exception that zero UUID is not supported
  // (`********-0000-0000-0000-************`).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for LookupOffer.
message LookupOfferRequest {
  // Required. The resource name of the entitlement to retrieve the Offer.
  // Entitlement uses the format:
  // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  string entitlement = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Entitlement"
    }
  ];
}

// Request message for ListProducts.
message ListProductsRequest {
  // Required. The resource name of the reseller account.
  // Format: accounts/{account_id}.
  string account = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Requested page size. Server might return fewer results than
  // requested. If unspecified, returns at most 100 Products. The maximum value
  // is 1000; the server will coerce values above 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token for a page of results other than the first page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code. For example, "en-US". The
  // response will localize in the corresponding language code, if specified.
  // The default value is "en-US".
  string language_code = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListProducts.
message ListProductsResponse {
  // List of Products requested.
  repeated Product products = 1;

  // A token to retrieve the next page of results.
  string next_page_token = 2;
}

// Request message for ListSkus.
message ListSkusRequest {
  // Required. The resource name of the Product to list SKUs for.
  // Parent uses the format: products/{product_id}.
  // Supports products/- to retrieve SKUs for all products.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Product"
    }
  ];

  // Required. Resource name of the reseller.
  // Format: accounts/{account_id}.
  string account = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Requested page size. Server might return fewer results than
  // requested. If unspecified, returns at most 100 SKUs. The maximum value is
  // 1000; the server will coerce values above 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token for a page of results other than the first page.
  // Optional.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code. For example, "en-US". The
  // response will localize in the corresponding language code, if specified.
  // The default value is "en-US".
  string language_code = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListSkus.
message ListSkusResponse {
  // The list of SKUs requested.
  repeated Sku skus = 1;

  // A token to retrieve the next page of results.
  string next_page_token = 2;
}

// Request message for ListOffers.
message ListOffersRequest {
  // Required. The resource name of the reseller account from which to list
  // Offers. Parent uses the format: accounts/{account_id}.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Requested page size. Server might return fewer results than
  // requested. If unspecified, returns at most 500 Offers. The maximum value is
  // 1000; the server will coerce values above 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token for a page of results other than the first page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The expression to filter results by name (name of
  // the Offer), sku.name (name of the SKU), or sku.product.name (name of the
  // Product).
  // Example 1: sku.product.name=products/p1 AND sku.name!=products/p1/skus/s1
  // Example 2: name=accounts/a1/offers/o1
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code. For example, "en-US". The
  // response will localize in the corresponding language code, if specified.
  // The default value is "en-US".
  string language_code = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A boolean flag that determines if a response returns future
  // offers 30 days from now. If the show_future_offers is true, the response
  // will only contain offers that are scheduled to be available 30 days from
  // now.
  bool show_future_offers = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListOffers.
message ListOffersResponse {
  // The list of Offers requested.
  repeated Offer offers = 1;

  // A token to retrieve the next page of results.
  string next_page_token = 2;
}

// Request message for ListPurchasableSkus.
message ListPurchasableSkusRequest {
  // List SKUs for a new entitlement. Make the purchase using
  // [CloudChannelService.CreateEntitlement][google.cloud.channel.v1.CloudChannelService.CreateEntitlement].
  message CreateEntitlementPurchase {
    // Required. List SKUs belonging to this Product.
    // Format: products/{product_id}.
    // Supports products/- to retrieve SKUs for all products.
    string product = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // List SKUs for upgrading or downgrading an entitlement. Make the purchase
  // using
  // [CloudChannelService.ChangeOffer][google.cloud.channel.v1.CloudChannelService.ChangeOffer].
  message ChangeOfferPurchase {
    // Change Type enum.
    enum ChangeType {
      // Not used.
      CHANGE_TYPE_UNSPECIFIED = 0;

      // SKU is an upgrade on the current entitlement.
      UPGRADE = 1;

      // SKU is a downgrade on the current entitlement.
      DOWNGRADE = 2;
    }

    // Required. Resource name of the entitlement.
    // Format:
    // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
    string entitlement = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Change Type for the entitlement.
    ChangeType change_type = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Defines the intended purchase.
  oneof purchase_option {
    // List SKUs for CreateEntitlement purchase.
    CreateEntitlementPurchase create_entitlement_purchase = 2;

    // List SKUs for ChangeOffer purchase with a new SKU.
    ChangeOfferPurchase change_offer_purchase = 3;
  }

  // Required. The resource name of the customer to list SKUs for.
  // Format: accounts/{account_id}/customers/{customer_id}.
  string customer = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];

  // Optional. Requested page size. Server might return fewer results than
  // requested. If unspecified, returns at most 100 SKUs. The maximum value is
  // 1000; the server will coerce values above 1000.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token for a page of results other than the first page.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code. For example, "en-US". The
  // response will localize in the corresponding language code, if specified.
  // The default value is "en-US".
  string language_code = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListPurchasableSkus.
message ListPurchasableSkusResponse {
  // The list of SKUs requested.
  repeated PurchasableSku purchasable_skus = 1;

  // A token to retrieve the next page of results.
  string next_page_token = 2;
}

// SKU that you can purchase. This is used in ListPurchasableSku API
// response.
message PurchasableSku {
  // SKU
  Sku sku = 1;
}

// Request message for ListPurchasableOffers.
message ListPurchasableOffersRequest {
  // List Offers for CreateEntitlement purchase.
  message CreateEntitlementPurchase {
    // Required. SKU that the result should be restricted to.
    // Format: products/{product_id}/skus/{sku_id}.
    string sku = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Billing account that the result should be restricted to.
    // Format: accounts/{account_id}/billingAccounts/{billing_account_id}.
    string billing_account = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // List Offers for ChangeOffer purchase.
  message ChangeOfferPurchase {
    // Required. Resource name of the entitlement.
    // Format:
    // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
    string entitlement = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Resource name of the new target SKU. Provide this SKU when
    // upgrading or downgrading an entitlement. Format:
    // products/{product_id}/skus/{sku_id}
    string new_sku = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Resource name of the new target Billing Account. Provide this
    // Billing Account when setting up billing for a trial subscription. Format:
    // accounts/{account_id}/billingAccounts/{billing_account_id}.
    //
    // This field is only relevant for multi-currency accounts. It should be
    // left empty for single currency accounts.
    string billing_account = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Defines the intended purchase.
  oneof purchase_option {
    // List Offers for CreateEntitlement purchase.
    CreateEntitlementPurchase create_entitlement_purchase = 2;

    // List Offers for ChangeOffer purchase.
    ChangeOfferPurchase change_offer_purchase = 3;
  }

  // Required. The resource name of the customer to list Offers for.
  // Format: accounts/{account_id}/customers/{customer_id}.
  string customer = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];

  // Optional. Requested page size. Server might return fewer results than
  // requested. If unspecified, returns at most 100 Offers. The maximum value is
  // 1000; the server will coerce values above 1000.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token for a page of results other than the first page.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code. For example, "en-US". The
  // response will localize in the corresponding language code, if specified.
  // The default value is "en-US".
  string language_code = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListPurchasableOffers.
message ListPurchasableOffersResponse {
  // The list of Offers requested.
  repeated PurchasableOffer purchasable_offers = 1;

  // A token to retrieve the next page of results.
  string next_page_token = 2;
}

// Offer that you can purchase for a customer. This is used in the
// ListPurchasableOffer API response.
message PurchasableOffer {
  // Offer.
  Offer offer = 1;
}

// Request message for QueryEligibleBillingAccounts.
message QueryEligibleBillingAccountsRequest {
  // Required. The resource name of the customer to list eligible billing
  // accounts for. Format: accounts/{account_id}/customers/{customer_id}.
  string customer = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Customer"
    }
  ];

  // Required. List of SKUs to list eligible billing accounts for. At least one
  // SKU is required. Format: products/{product_id}/skus/{sku_id}.
  repeated string skus = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for QueryEligibleBillingAccounts.
message QueryEligibleBillingAccountsResponse {
  // List of SKU purchase groups where each group represents a set of SKUs that
  // must be purchased using the same billing account. Each SKU from
  // [QueryEligibleBillingAccountsRequest.skus] will appear in exactly one SKU
  // group.
  repeated SkuPurchaseGroup sku_purchase_groups = 1;
}

// Represents a set of SKUs that must be purchased using the same billing
// account.
message SkuPurchaseGroup {
  // Resource names of the SKUs included in this group.
  // Format: products/{product_id}/skus/{sku_id}.
  repeated string skus = 1;

  // List of billing accounts that are eligible to purhcase these SKUs.
  repeated BillingAccountPurchaseInfo billing_account_purchase_infos = 2;
}

// Represents a billing account that can be used to make a purchase.
message BillingAccountPurchaseInfo {
  // The billing account resource.
  BillingAccount billing_account = 1;
}

// Request Message for RegisterSubscriber.
message RegisterSubscriberRequest {
  // Required. Resource name of the account.
  string account = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Service account that provides subscriber access to the registered
  // topic.
  string service_account = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response Message for RegisterSubscriber.
message RegisterSubscriberResponse {
  // Name of the topic the subscriber will listen to.
  string topic = 1;
}

// Request Message for UnregisterSubscriber.
message UnregisterSubscriberRequest {
  // Required. Resource name of the account.
  string account = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Service account to unregister from subscriber access to the
  // topic.
  string service_account = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response Message for UnregisterSubscriber.
message UnregisterSubscriberResponse {
  // Name of the topic the service account subscriber access was removed from.
  string topic = 1;
}

// Request Message for ListSubscribers.
message ListSubscribersRequest {
  // Required. Resource name of the account.
  string account = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of service accounts to return. The service may
  // return fewer than this value. If unspecified, returns at most 100 service
  // accounts. The maximum value is 1000; the server will coerce values above
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListSubscribers` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListSubscribers` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response Message for ListSubscribers.
message ListSubscribersResponse {
  // Name of the topic registered with the reseller.
  string topic = 1;

  // List of service accounts which have subscriber access to the topic.
  repeated string service_accounts = 2;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 3;
}

// Request message for
// [CloudChannelService.ListEntitlementChanges][google.cloud.channel.v1.CloudChannelService.ListEntitlementChanges]
message ListEntitlementChangesRequest {
  // Required. The resource name of the entitlement for which to list
  // entitlement changes. The `-` wildcard may be used to match entitlements
  // across a customer. Formats:
  //
  //   * accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
  //   * accounts/{account_id}/customers/{customer_id}/entitlements/-
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Entitlement"
    }
  ];

  // Optional. The maximum number of entitlement changes to return. The service
  // may return fewer than this value. If unspecified, returns at most 10
  // entitlement changes. The maximum value is 50; the server will coerce values
  // above 50.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // [CloudChannelService.ListEntitlementChanges][google.cloud.channel.v1.CloudChannelService.ListEntitlementChanges]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [CloudChannelService.ListEntitlementChanges][google.cloud.channel.v1.CloudChannelService.ListEntitlementChanges]
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filters applied to the list results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CloudChannelService.ListEntitlementChanges][google.cloud.channel.v1.CloudChannelService.ListEntitlementChanges]
message ListEntitlementChangesResponse {
  // The list of entitlement changes.
  repeated EntitlementChange entitlement_changes = 1;

  // A token to list the next page of results.
  string next_page_token = 2;
}
