// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "OperationsProto";
option java_package = "com.google.cloud.channel.v1";

// Provides contextual information about a
// [google.longrunning.Operation][google.longrunning.Operation].
message OperationMetadata {
  // RPCs that return a Long Running Operation.
  enum OperationType {
    // Not used.
    OPERATION_TYPE_UNSPECIFIED = 0;

    // Long Running Operation was triggered by CreateEntitlement.
    CREATE_ENTITLEMENT = 1;

    // Long Running Operation was triggered by ChangeRenewalSettings.
    CHANGE_RENEWAL_SETTINGS = 3;

    // Long Running Operation was triggered by StartPaidService.
    START_PAID_SERVICE = 5;

    // Long Running Operation was triggered by ActivateEntitlement.
    ACTIVATE_ENTITLEMENT = 7;

    // Long Running Operation was triggered by SuspendEntitlement.
    SUSPEND_ENTITLEMENT = 8;

    // Long Running Operation was triggered by CancelEntitlement.
    CANCEL_ENTITLEMENT = 9;

    // Long Running Operation was triggered by TransferEntitlements.
    TRANSFER_ENTITLEMENTS = 10;

    // Long Running Operation was triggered by TransferEntitlementsToGoogle.
    TRANSFER_ENTITLEMENTS_TO_GOOGLE = 11;

    // Long Running Operation was triggered by ChangeOffer.
    CHANGE_OFFER = 14;

    // Long Running Operation was triggered by ChangeParameters.
    CHANGE_PARAMETERS = 15;

    // Long Running Operation was triggered by ProvisionCloudIdentity.
    PROVISION_CLOUD_IDENTITY = 16;
  }

  // The RPC that initiated this Long Running Operation.
  OperationType operation_type = 1;
}
