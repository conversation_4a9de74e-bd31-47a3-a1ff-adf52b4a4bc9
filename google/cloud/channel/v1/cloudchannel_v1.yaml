type: google.api.Service
config_version: 3
name: cloudchannel.googleapis.com
title: Cloud Channel API

apis:
- name: google.cloud.channel.v1.CloudChannelReportsService
- name: google.cloud.channel.v1.CloudChannelService
- name: google.longrunning.Operations

types:
- name: google.cloud.channel.v1.CustomerEvent
- name: google.cloud.channel.v1.EntitlementEvent
- name: google.cloud.channel.v1.OperationMetadata
- name: google.cloud.channel.v1.RunReportJobResponse
- name: google.cloud.channel.v1.SubscriberEvent
- name: google.cloud.channel.v1.TransferEntitlementsResponse

documentation:
  summary: |-
    The Cloud Channel API enables Google Cloud partners to have a single
    unified resale platform and APIs across all of Google Cloud including GCP,
    Workspace, Maps and Chrome.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=operations/**}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=operations/**}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=operations/**}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=operations}'

authentication:
  rules:
  - selector: 'google.cloud.channel.v1.CloudChannelReportsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/apps.reports.usage.readonly
  - selector: 'google.cloud.channel.v1.CloudChannelService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/apps.order
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/apps.order
