# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "channel_proto",
    srcs = [
        "billing_accounts.proto",
        "channel_partner_links.proto",
        "common.proto",
        "customers.proto",
        "entitlement_changes.proto",
        "entitlements.proto",
        "offers.proto",
        "operations.proto",
        "products.proto",
        "reports_service.proto",
        "repricing.proto",
        "service.proto",
        "subscriber_event.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "//google/type:datetime_proto",
        "//google/type:decimal_proto",
        "//google/type:money_proto",
        "//google/type:postal_address_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "channel_proto_with_info",
    deps = [
        ":channel_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "channel_java_proto",
    deps = [":channel_proto"],
)

java_grpc_library(
    name = "channel_java_grpc",
    srcs = [":channel_proto"],
    deps = [":channel_java_proto"],
)

java_gapic_library(
    name = "channel_java_gapic",
    srcs = [":channel_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloudchannel_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudchannel_v1.yaml",
    test_deps = [
        ":channel_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":channel_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "channel_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.channel.v1.CloudChannelServiceClientHttpJsonTest",
        "com.google.cloud.channel.v1.CloudChannelServiceClientTest",
    ],
    runtime_deps = [":channel_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-channel-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":channel_java_gapic",
        ":channel_java_grpc",
        ":channel_java_proto",
        ":channel_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "channel_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/channel/apiv1/channelpb",
    protos = [":channel_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
        "//google/type:datetime_go_proto",
        "//google/type:decimal_go_proto",
        "//google/type:money_go_proto",
        "//google/type:postaladdress_go_proto",
    ],
)

go_gapic_library(
    name = "channel_go_gapic",
    srcs = [":channel_proto_with_info"],
    grpc_service_config = "cloudchannel_grpc_service_config.json",
    importpath = "cloud.google.com/go/channel/apiv1;channel",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudchannel_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":channel_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-channel-v1-go",
    deps = [
        ":channel_go_gapic",
        ":channel_go_gapic_srcjar-metadata.srcjar",
        ":channel_go_gapic_srcjar-snippets.srcjar",
        ":channel_go_gapic_srcjar-test.srcjar",
        ":channel_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "channel_py_gapic",
    srcs = [":channel_proto"],
    grpc_service_config = "cloudchannel_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudchannel_v1.yaml",
    transport = "grpc",
)

py_test(
    name = "channel_py_gapic_test",
    srcs = [
        "channel_py_gapic_pytest.py",
        "channel_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":channel_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "channel-v1-py",
    deps = [
        ":channel_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "channel_php_proto",
    deps = [":channel_proto"],
)

php_gapic_library(
    name = "channel_php_gapic",
    srcs = [":channel_proto_with_info"],
    grpc_service_config = "cloudchannel_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudchannel_v1.yaml",
    transport = "grpc+rest",
    deps = [":channel_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-channel-v1-php",
    deps = [
        ":channel_php_gapic",
        ":channel_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "channel_nodejs_gapic",
    package_name = "@google-cloud/channel",
    src = ":channel_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudchannel_grpc_service_config.json",
    package = "google.cloud.channel.v1",
    rest_numeric_enums = True,
    service_yaml = "cloudchannel_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "channel-v1-nodejs",
    deps = [
        ":channel_nodejs_gapic",
        ":channel_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "channel_ruby_proto",
    deps = [":channel_proto"],
)

ruby_grpc_library(
    name = "channel_ruby_grpc",
    srcs = [":channel_proto"],
    deps = [":channel_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "channel_ruby_gapic",
    srcs = [":channel_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=cloudchannel.googleapis.com",
        "ruby-cloud-api-shortname=cloudchannel",
        "ruby-cloud-env-prefix=CHANNEL",
        "ruby-cloud-gem-name=google-cloud-channel-v1",
        "ruby-cloud-product-url=https://cloud.google.com/channel",
    ],
    grpc_service_config = "cloudchannel_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "You can use Channel Services to manage your relationships with your partners and your customers. Channel Services include a console and APIs to view and provision links between distributors and resellers, customers and entitlements.",
    ruby_cloud_title = "Cloud Channel V1",
    service_yaml = "cloudchannel_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":channel_ruby_grpc",
        ":channel_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-channel-v1-ruby",
    deps = [
        ":channel_ruby_gapic",
        ":channel_ruby_grpc",
        ":channel_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "channel_csharp_proto",
    deps = [":channel_proto"],
)

csharp_grpc_library(
    name = "channel_csharp_grpc",
    srcs = [":channel_proto"],
    deps = [":channel_csharp_proto"],
)

csharp_gapic_library(
    name = "channel_csharp_gapic",
    srcs = [":channel_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudchannel_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudchannel_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":channel_csharp_grpc",
        ":channel_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-channel-v1-csharp",
    deps = [
        ":channel_csharp_gapic",
        ":channel_csharp_grpc",
        ":channel_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "channel_cc_proto",
    deps = [":channel_proto"],
)

cc_grpc_library(
    name = "channel_cc_grpc",
    srcs = [":channel_proto"],
    grpc_only = True,
    deps = [":channel_cc_proto"],
)
