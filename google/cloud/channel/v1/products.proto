// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/resource.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "ProductsProto";
option java_package = "com.google.cloud.channel.v1";

// Type of media used.
enum MediaType {
  // Not used.
  MEDIA_TYPE_UNSPECIFIED = 0;

  // Type of image.
  MEDIA_TYPE_IMAGE = 1;
}

// A Product is the entity a customer uses when placing an order. For example,
// Google Workspace, Google Voice, etc.
message Product {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/Product"
    pattern: "products/{product}"
  };

  // Resource Name of the Product.
  // Format: products/{product_id}
  string name = 1;

  // Marketing information for the product.
  MarketingInfo marketing_info = 2;
}

// Represents a product's purchasable Stock Keeping Unit (SKU).
// SKUs represent the different variations of the product. For example, Google
// Workspace Business Standard and Google Workspace Business Plus are Google
// Workspace product SKUs.
message Sku {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/Sku"
    pattern: "products/{product}/skus/{sku}"
  };

  // Resource Name of the SKU.
  // Format: products/{product_id}/skus/{sku_id}
  string name = 1;

  // Marketing information for the SKU.
  MarketingInfo marketing_info = 2;

  // Product the SKU is associated with.
  Product product = 3;
}

// Represents the marketing information for a Product, SKU or Offer.
message MarketingInfo {
  // Human readable name.
  string display_name = 1;

  // Human readable description. Description can contain HTML.
  string description = 2;

  // Default logo.
  Media default_logo = 3;
}

// Represents media information.
message Media {
  // Title of the media.
  string title = 1;

  // URL of the media.
  string content = 2;

  // Type of the media.
  MediaType type = 3;
}
