// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "BillingAccountsProto";
option java_package = "com.google.cloud.channel.v1";

// Represents a billing account.
message BillingAccount {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/BillingAccount"
    pattern: "accounts/{account}/billingAccounts/{billing_account}"
  };

  // Output only. Resource name of the billing account.
  // Format: accounts/{account_id}/billingAccounts/{billing_account_id}.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Display name of the billing account.
  string display_name = 2;

  // Output only. The time when this billing account was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The 3-letter currency code defined in ISO 4217.
  string currency_code = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The CLDR region code.
  string region_code = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
