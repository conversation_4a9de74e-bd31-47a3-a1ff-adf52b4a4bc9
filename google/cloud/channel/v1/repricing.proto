// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/decimal.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "RepricingProto";
option java_package = "com.google.cloud.channel.v1";

// Specifies the different costs that the modified bill can be based on.
enum RebillingBasis {
  // Not used.
  REBILLING_BASIS_UNSPECIFIED = 0;

  // Use the list cost, also known as the MSRP.
  COST_AT_LIST = 1;

  // Pass through all discounts except the Reseller Program Discount. If this is
  // the default cost base and no adjustments are specified, the output cost
  // will be exactly what the customer would see if they viewed the bill in the
  // Google Cloud Console.
  DIRECT_CUSTOMER_COST = 2;
}

// Configuration for how a reseller will reprice a Customer.
message CustomerRepricingConfig {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/CustomerRepricingConfig"
    pattern: "accounts/{account}/customers/{customer}/customerRepricingConfigs/{customer_repricing_config}"
  };

  // Output only. Resource name of the CustomerRepricingConfig.
  // Format:
  // accounts/{account_id}/customers/{customer_id}/customerRepricingConfigs/{id}.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The configuration for bill modifications made by a reseller
  // before sending it to customers.
  RepricingConfig repricing_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp of an update to the repricing rule. If `update_time`
  // is after
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // then it indicates this was set mid-month.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for how a distributor will rebill a channel partner
// (also known as a distributor-authorized reseller).
message ChannelPartnerRepricingConfig {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/ChannelPartnerRepricingConfig"
    pattern: "accounts/{account}/channelPartnerLinks/{channel_partner}/channelPartnerRepricingConfigs/{channel_partner_repricing_config}"
  };

  // Output only. Resource name of the ChannelPartnerRepricingConfig.
  // Format:
  // accounts/{account_id}/channelPartnerLinks/{channel_partner_id}/channelPartnerRepricingConfigs/{id}.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The configuration for bill modifications made by a reseller
  // before sending it to ChannelPartner.
  RepricingConfig repricing_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp of an update to the repricing rule. If `update_time`
  // is after
  // [RepricingConfig.effective_invoice_month][google.cloud.channel.v1.RepricingConfig.effective_invoice_month]
  // then it indicates this was set mid-month.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for repricing a Google bill over a period of time.
message RepricingConfig {
  // Applies the repricing configuration at the entitlement level.
  message EntitlementGranularity {
    // Resource name of the entitlement.
    // Format:
    // accounts/{account_id}/customers/{customer_id}/entitlements/{entitlement_id}
    string entitlement = 1 [(google.api.resource_reference) = {
      type: "cloudchannel.googleapis.com/Entitlement"
    }];
  }

  // Applies the repricing configuration at the channel partner level.
  // The channel partner value is derived from the resource name. Takes an
  // empty json object.
  // Deprecated: This is no longer supported. Use
  // [RepricingConfig.EntitlementGranularity][google.cloud.channel.v1.RepricingConfig.EntitlementGranularity]
  // instead.
  message ChannelPartnerGranularity {
    option deprecated = true;
  }

  // Required. Defines the granularity for repricing.
  oneof granularity {
    // Applies the repricing configuration at the entitlement level.
    //
    // Note: If a
    // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
    // using
    // [RepricingConfig.EntitlementGranularity][google.cloud.channel.v1.RepricingConfig.EntitlementGranularity]
    // becomes effective, then no existing or future
    // [RepricingConfig.ChannelPartnerGranularity][google.cloud.channel.v1.RepricingConfig.ChannelPartnerGranularity]
    // will apply to the
    // [RepricingConfig.EntitlementGranularity.entitlement][google.cloud.channel.v1.RepricingConfig.EntitlementGranularity.entitlement].
    // This is the recommended value for both
    // [CustomerRepricingConfig][google.cloud.channel.v1.CustomerRepricingConfig]
    // and
    // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig].
    EntitlementGranularity entitlement_granularity = 4;

    // Applies the repricing configuration at the channel partner level.
    // Only
    // [ChannelPartnerRepricingConfig][google.cloud.channel.v1.ChannelPartnerRepricingConfig]
    // supports this value. Deprecated: This is no longer supported. Use
    // [RepricingConfig.entitlement_granularity][google.cloud.channel.v1.RepricingConfig.entitlement_granularity]
    // instead.
    ChannelPartnerGranularity channel_partner_granularity = 5
        [deprecated = true];
  }

  // Required. The YearMonth when these adjustments activate. The Day field
  // needs to be "0" since we only accept YearMonth repricing boundaries.
  google.type.Date effective_invoice_month = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Information about the adjustment.
  RepricingAdjustment adjustment = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The [RebillingBasis][google.cloud.channel.v1.RebillingBasis] to
  // use for this bill. Specifies the relative cost based on repricing costs you
  // will apply.
  RebillingBasis rebilling_basis = 3 [(google.api.field_behavior) = REQUIRED];

  // The conditional overrides to apply for this configuration. If you list
  // multiple overrides, only the first valid override is used.  If you don't
  // list any overrides, the API uses the normal adjustment and rebilling basis.
  repeated ConditionalOverride conditional_overrides = 6;
}

// A type that represents the various adjustments you can apply to a bill.
message RepricingAdjustment {
  // A oneof that represents the different types for this adjustment.
  oneof adjustment {
    // Flat markup or markdown on an entire bill.
    PercentageAdjustment percentage_adjustment = 2;
  }
}

// An adjustment that applies a flat markup or markdown to an entire bill.
message PercentageAdjustment {
  // The percentage of the bill to adjust.
  // For example:
  // Mark down by 1% => "-1.00"
  // Mark up by 1%   => "1.00"
  // Pass-Through    => "0.00"
  google.type.Decimal percentage = 2;
}

// Specifies the override to conditionally apply.
message ConditionalOverride {
  // Required. Information about the applied override's adjustment.
  RepricingAdjustment adjustment = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The [RebillingBasis][google.cloud.channel.v1.RebillingBasis] to
  // use for the applied override. Shows the relative cost based on your
  // repricing costs.
  RebillingBasis rebilling_basis = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Specifies the condition which, if met, will apply the override.
  RepricingCondition repricing_condition = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Represents the various repricing conditions you can use for a conditional
// override.
message RepricingCondition {
  // Represents the types of existing conditional statements.
  oneof condition {
    // SKU Group condition for override.
    SkuGroupCondition sku_group_condition = 1;
  }
}

// A condition that applies the override if a line item SKU is found in the SKU
// group.
message SkuGroupCondition {
  // Specifies a SKU group (https://cloud.google.com/skus/sku-groups).
  // Resource name of SKU group. Format:
  // accounts/{account}/skuGroups/{sku_group}.
  // Example:
  // "accounts/C01234/skuGroups/3d50fd57-3157-4577-a5a9-a219b8490041".
  string sku_group = 1;
}
