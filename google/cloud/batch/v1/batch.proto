// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.batch.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/batch/v1/job.proto";
import "google/cloud/batch/v1/task.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Batch.V1";
option go_package = "cloud.google.com/go/batch/apiv1/batchpb;batchpb";
option java_multiple_files = true;
option java_outer_classname = "BatchProto";
option java_package = "com.google.cloud.batch.v1";
option objc_class_prefix = "GCB";
option php_namespace = "Google\\Cloud\\Batch\\V1";
option ruby_package = "Google::Cloud::Batch::V1";

// Google Batch Service.
// The service manages user submitted batch jobs and allocates Google Compute
// Engine VM instances to run the jobs.
service BatchService {
  option (google.api.default_host) = "batch.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a Job.
  rpc CreateJob(CreateJobRequest) returns (Job) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/jobs"
      body: "job"
    };
    option (google.api.method_signature) = "parent,job,job_id";
  }

  // Get a Job specified by its resource name.
  rpc GetJob(GetJobRequest) returns (Job) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/jobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Delete a Job.
  rpc DeleteJob(DeleteJobRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/jobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.batch.v1.OperationMetadata"
    };
  }

  // List all Jobs for a project within a region.
  rpc ListJobs(ListJobsRequest) returns (ListJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/jobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Return a single Task.
  rpc GetTask(GetTaskRequest) returns (Task) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/jobs/*/taskGroups/*/tasks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List Tasks associated with a job.
  rpc ListTasks(ListTasksRequest) returns (ListTasksResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/jobs/*/taskGroups/*}/tasks"
    };
    option (google.api.method_signature) = "parent";
  }
}

// CreateJob Request.
message CreateJobRequest {
  // Required. The parent resource name where the Job will be created.
  // Pattern: "projects/{project}/locations/{location}"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "batch.googleapis.com/Job" }
  ];

  // ID used to uniquely identify the Job within its parent scope.
  // This field should contain at most 63 characters and must start with
  // lowercase characters.
  // Only lowercase characters, numbers and '-' are accepted.
  // The '-' character cannot be the first or the last one.
  // A system generated ID will be used if the field is not set.
  //
  // The job.name field in the request will be ignored and the created resource
  // name of the Job will be "{parent}/jobs/{job_id}".
  string job_id = 2;

  // Required. The Job to create.
  Job job = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// GetJob Request.
message GetJobRequest {
  // Required. Job name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "batch.googleapis.com/Job" }
  ];
}

// DeleteJob Request.
message DeleteJobRequest {
  // Job name.
  string name = 1;

  // Optional. Reason for this deletion.
  string reason = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// ListJob Request.
message ListJobsRequest {
  // Parent path.
  string parent = 1;

  // List filter.
  string filter = 4;

  // Optional. Sort results. Supported are "name", "name desc", "create_time",
  // and "create_time desc".
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;
}

// ListJob Response.
message ListJobsResponse {
  // Jobs.
  repeated Job jobs = 1;

  // Next page token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// ListTasks Request.
message ListTasksRequest {
  // Required. Name of a TaskGroup from which Tasks are being requested.
  // Pattern:
  // "projects/{project}/locations/{location}/jobs/{job}/taskGroups/{task_group}"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "batch.googleapis.com/TaskGroup" }
  ];

  // Task filter, null filter matches all Tasks.
  // Filter string should be of the format State=TaskStatus.State e.g.
  // State=RUNNING
  string filter = 2;

  // Page size.
  int32 page_size = 3;

  // Page token.
  string page_token = 4;
}

// ListTasks Response.
message ListTasksResponse {
  // Tasks.
  repeated Task tasks = 1;

  // Next page token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for a single Task by name.
message GetTaskRequest {
  // Required. Task name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "batch.googleapis.com/Task" }
  ];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have
  // [google.longrunning.Operation.error][google.longrunning.Operation.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
