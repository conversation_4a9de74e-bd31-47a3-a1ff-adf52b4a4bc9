{"methodConfig": [{"name": [{"service": "google.cloud.batch.v1alpha.BatchService", "method": "ListJobs"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "ListTasks"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "ListResourceAllowances"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "ListNodePools"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "Get<PERSON>ob"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "GetTask"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "GetResourceAllowance"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "GetNodePool"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.batch.v1alpha.BatchService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "DeleteJob"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "Update<PERSON><PERSON>"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "CreateResourceAllowance"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "DeleteResourceAllowance"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "UpdateResourceAllowance"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "CancelTasks"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "CreateNodePool"}, {"service": "google.cloud.batch.v1alpha.BatchService", "method": "DeleteNodePool"}], "timeout": "60s"}]}