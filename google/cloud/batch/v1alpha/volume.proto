// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.batch.v1alpha;

option csharp_namespace = "Google.Cloud.Batch.V1Alpha";
option go_package = "cloud.google.com/go/batch/apiv1alpha/batchpb;batchpb";
option java_multiple_files = true;
option java_outer_classname = "VolumeProto";
option java_package = "com.google.cloud.batch.v1alpha";
option objc_class_prefix = "GCB";
option php_namespace = "Google\\Cloud\\Batch\\V1alpha";
option ruby_package = "Google::Cloud::Batch::V1alpha";

// Volume describes a volume and parameters for it to be mounted to a VM.
message Volume {
  // The source for the volume.
  oneof source {
    // A Network File System (NFS) volume. For example, a
    // Filestore file share.
    NFS nfs = 1;

    // Deprecated: please use device_name instead.
    PD pd = 2 [deprecated = true];

    // A Google Cloud Storage (GCS) volume.
    GCS gcs = 3;

    // Device name of an attached disk volume, which should align with a
    // device_name specified by
    // job.allocation_policy.instances[0].policy.disks[i].device_name or
    // defined by the given instance template in
    // job.allocation_policy.instances[0].instance_template.
    string device_name = 6;
  }

  // The mount path for the volume, e.g. /mnt/disks/share.
  string mount_path = 4;

  // Mount options vary based on the type of storage volume:
  //
  // * For a Cloud Storage bucket, all the mount options provided
  // by
  //   the [`gcsfuse` tool](https://cloud.google.com/storage/docs/gcsfuse-cli)
  //   are supported.
  // * For an existing persistent disk, all mount options provided by the
  //   [`mount` command](https://man7.org/linux/man-pages/man8/mount.8.html)
  //   except writing are supported. This is due to restrictions of
  //   [multi-writer
  //   mode](https://cloud.google.com/compute/docs/disks/sharing-disks-between-vms).
  // * For any other disk or a Network File System (NFS), all the
  //   mount options provided by the `mount` command are supported.
  repeated string mount_options = 5;
}

// Represents an NFS volume.
message NFS {
  // The IP address of the NFS.
  string server = 1;

  // Remote source path exported from the NFS, e.g., "/share".
  string remote_path = 2;
}

// Deprecated: please use device_name instead.
message PD {
  // PD disk name, e.g. pd-1.
  string disk = 1;

  // PD device name, e.g. persistent-disk-1.
  string device = 2;

  // Whether this is an existing PD. Default is false. If false, i.e., new
  // PD, we will format it into ext4 and mount to the given path. If true, i.e.,
  // existing PD, it should be in ext4 format and we will mount it to the given
  // path.
  bool existing = 3 [deprecated = true];
}

// Represents a Google Cloud Storage volume.
message GCS {
  // Remote path, either a bucket name or a subdirectory of a bucket, e.g.:
  // bucket_name, bucket_name/subdirectory/
  string remote_path = 1;
}
