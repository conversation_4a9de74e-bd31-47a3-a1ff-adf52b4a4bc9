# This build file includes a target for the Ruby wrapper library for
# google-cloud-batch.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for batch.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "batch_ruby_wrapper",
    srcs = ["//google/cloud/batch/v1:batch_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=batch.googleapis.com",
        "ruby-cloud-api-shortname=batch",
        "ruby-cloud-gem-name=google-cloud-batch",
        "ruby-cloud-wrapper-of=v1:0.15",
    ],
    ruby_cloud_description = "Google Cloud Batch is a fully managed service used by scientists, VFX artists, developers to easily and efficiently run batch workloads on Google Cloud. This service manages provisioning of resources to satisfy the requirements of the batch jobs for a variety of workloads including ML, HPC, VFX rendering, transcoding, genomics and others.",
    ruby_cloud_title = "Batch",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-batch-ruby",
    deps = [
        ":batch_ruby_wrapper",
    ],
)
