// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.servicehealth.logging.v1;

import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/servicehealth/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "EventLogProto";
option java_package = "com.google.cloud.servicehealth.logging.v1";

// Message describing the payload of service health logs.
message EventLog {
  // The category of the event. This enum lists all possible categories of
  // event.
  enum EventCategory {
    // Unspecified category.
    EVENT_CATEGORY_UNSPECIFIED = 0;

    // Event category for service outage or degradation.
    INCIDENT = 2;
  }

  // The state of the event. This enum lists all possible states of event.
  enum State {
    // Unspecified state
    STATE_UNSPECIFIED = 0;

    // Event is actively affecting a Google Cloud service and will continue to
    // receive updates.
    ACTIVE = 1;

    // Event is no longer affecting the Google Cloud service or has been merged
    // with another event.
    CLOSED = 2;
  }

  // The detailed state of the event. This enum lists all possible detail states
  // of event.
  enum DetailedState {
    // Unspecified detail state.
    DETAILED_STATE_UNSPECIFIED = 0;

    // Google engineers are actively investigating the event to determine the
    // impact.
    EMERGING = 1;

    // The event is confirmed and impacting at least one Google Cloud service.
    // Ongoing status updates will be provided until it is resolved.
    CONFIRMED = 2;

    // The event is no longer affecting any Google Cloud service, and there will
    // be no further updates.
    RESOLVED = 3;

    // Event was merged into a parent event. All further updates will be
    // published to the parent only. The `parent_event` field contains the name
    // of the parent.
    MERGED = 4;
  }

  // Communicates why a given event is deemed relevant in the context of a given
  // project. This enum lists all possible detailed states of relevance.
  enum Relevance {
    // Unspecified relevance.
    RELEVANCE_UNSPECIFIED = 0;

    // The relevance of the event to the project is unknown.
    UNKNOWN = 2;

    // The event does not impact the project.
    NOT_IMPACTED = 6;

    // We determined that the event is linked to a product that is used by
    // the project, but we have no information (either positive
    // or negative) whether the project is affected.
    PARTIALLY_RELATED = 7;

    // The event has a connection to your project and it may be impacted
    RELATED = 8;

    // The event is impacting your project
    IMPACTED = 9;
  }

  // Brief description for the event.
  string title = 1;

  // Free-form, human-readable description.
  string description = 2;

  // Identifies the category of the event.
  EventCategory category = 3;

  // The current state of the event.
  State state = 4;

  // The current detailed state of the event.
  DetailedState detailed_state = 14;

  // Google Cloud products known to be affected by the event, in JSON serialized
  // format. List of all supported [Google Cloud
  // products](../resources/products-locations.md).
  //
  // Example: "`['Google Cloud SQL', 'Cloud Compute Engine']`".
  string impacted_products = 15;

  // Locations known to be impacted by the event, in JSON serialized format. See
  // possible [values](products-locations.md), which are subject to where the
  // service is running.
  //
  // Example: "`['us-central1', 'us-west1']`".
  string impacted_locations = 6;

  // Communicates why a given event is deemed relevant in the context of a given
  // project.
  Relevance relevance = 7;

  // When `detailed_state`=`MERGED`, `parent_event` contains the name of the
  // parent event. All further updates will be published to the parent event.
  string parent_event = 8;

  // The time when the event was last modified.
  google.protobuf.Timestamp update_time = 10;

  // The start time of the event, if applicable.
  google.protobuf.Timestamp start_time = 11;

  // The end time of the event, if applicable.
  google.protobuf.Timestamp end_time = 12;

  // Incident-only field. The time when the next update can be expected.
  google.protobuf.Timestamp next_update_time = 13;
}
