// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.servicehealth.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ServiceHealth.V1";
option go_package = "cloud.google.com/go/servicehealth/apiv1/servicehealthpb;servicehealthpb";
option java_multiple_files = true;
option java_outer_classname = "EventResourcesProto";
option java_package = "com.google.cloud.servicehealth.v1";
option php_namespace = "Google\\Cloud\\ServiceHealth\\V1";
option ruby_package = "Google::Cloud::ServiceHealth::V1";
option (google.api.resource_definition) = {
  type: "servicehealth.googleapis.com/OrganizationLocation"
  pattern: "organizations/{organization}/locations/{location}"
};

// Represents service health events that may affect Google Cloud products.
// Event resource is a read-only view and does not allow any modifications. All
// fields are output only.
message Event {
  option (google.api.resource) = {
    type: "servicehealth.googleapis.com/Event"
    pattern: "projects/{project}/locations/{location}/events/{event}"
    plural: "events"
    singular: "event"
  };

  // The category of the event. This enum lists all possible categories of
  // event.
  enum EventCategory {
    // Unspecified category.
    EVENT_CATEGORY_UNSPECIFIED = 0;

    // Event category for service outage or degradation.
    INCIDENT = 2;
  }

  // The detailed category of an event. Contains all possible states for all
  // event categories.
  enum DetailedCategory {
    // Unspecified detailed category.
    DETAILED_CATEGORY_UNSPECIFIED = 0;

    // Indicates an event with category INCIDENT has a confirmed impact to at
    // least one Google Cloud product.
    CONFIRMED_INCIDENT = 1;

    // Indicates an event with category INCIDENT is under investigation to
    // determine if it has a confirmed impact on any Google Cloud products.
    EMERGING_INCIDENT = 2;
  }

  // The state of the event. This enum lists all possible states of event.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // Event is actively affecting a Google Cloud product and will continue to
    // receive updates.
    ACTIVE = 1;

    // Event is no longer affecting the Google Cloud product or has been merged
    // with another event.
    CLOSED = 2;
  }

  // The detailed state of the incident. This enum lists all possible detailed
  // states of an incident.
  enum DetailedState {
    // Unspecified detail state.
    DETAILED_STATE_UNSPECIFIED = 0;

    // Google engineers are actively investigating the event to determine the
    // impact.
    EMERGING = 1;

    // The incident is confirmed and impacting at least one Google Cloud
    // product. Ongoing status updates will be provided until it is resolved.
    CONFIRMED = 2;

    // The incident is no longer affecting any Google Cloud product, and there
    // will be no further updates.
    RESOLVED = 3;

    // The incident was merged into a parent incident. All further updates will
    // be published to the parent only. The `parent_event` field contains the
    // name of the parent.
    MERGED = 4;

    // The incident was automatically closed because of the following reasons:
    //
    //  * The impact of the incident could not be confirmed.
    //  * The incident was intermittent or resolved itself.
    //
    //  The incident does not have a resolution because no action or
    //  investigation happened. If it is intermittent, the incident may reopen.
    AUTO_CLOSED = 9;

    // Upon investigation, Google engineers concluded that the incident is not
    // affecting a Google Cloud product. This state can change if the incident
    // is reviewed again.
    FALSE_POSITIVE = 10;
  }

  // Communicates why a given incident is deemed relevant in the context of a
  // given project. This enum lists all possible detailed states of relevance.
  enum Relevance {
    // Unspecified relevance.
    RELEVANCE_UNSPECIFIED = 0;

    // The relevance of the incident to the project is unknown.
    UNKNOWN = 2;

    // The incident does not impact the project.
    NOT_IMPACTED = 6;

    // The incident is associated with a Google Cloud product your project uses,
    // but the incident may not be impacting your project. For example, the
    // incident may be impacting a Google Cloud product that your project uses,
    // but in a location that your project does not use.
    PARTIALLY_RELATED = 7;

    // The incident has a direct connection with your project and impacts a
    // Google Cloud product in a location your project uses.
    RELATED = 8;

    // The incident is verified to be impacting your project.
    IMPACTED = 9;
  }

  // Output only. Identifier. Name of the event. Unique name of the event in
  // this scope including project and location using the form
  // `projects/{project_id}/locations/{location}/events/{event_id}`.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. Brief description for the event.
  string title = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Free-form, human-readable description.
  string description = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The category of the event.
  EventCategory category = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The detailed category of the event.
  DetailedCategory detailed_category = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the event.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current detailed state of the incident.
  DetailedState detailed_state = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Google Cloud products and locations impacted by the event.
  repeated EventImpact event_impacts = 20;

  // Output only. Communicates why a given event is deemed relevant in the
  // context of a given project.
  Relevance relevance = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Event updates are correspondence from Google.
  repeated EventUpdate updates = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When `detailed_state`=`MERGED`, `parent_event` contains the
  // name of the parent event. All further updates will be published to the
  // parent event.
  string parent_event = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the event was last modified.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The start time of the event, if applicable.
  google.protobuf.Timestamp start_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The end time of the event, if applicable.
  google.protobuf.Timestamp end_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the next update can be expected.
  google.protobuf.Timestamp next_update_time = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents service health events that may affect Google Cloud products used
// across the organization. It is a read-only view and does not allow any
// modifications.
message OrganizationEvent {
  option (google.api.resource) = {
    type: "servicehealth.googleapis.com/OrganizationEvent"
    pattern: "organizations/{organization}/locations/{location}/organizationEvents/{event}"
    plural: "organizationEvents"
    singular: "organizationEvent"
  };

  // The category of the event. This enum lists all possible categories of
  // event.
  enum EventCategory {
    // Unspecified category.
    EVENT_CATEGORY_UNSPECIFIED = 0;

    // Event category for service outage or degradation.
    INCIDENT = 2;
  }

  // The detailed category of an event. Contains all possible states for all
  // event categories.
  enum DetailedCategory {
    // Unspecified detailed category.
    DETAILED_CATEGORY_UNSPECIFIED = 0;

    // Indicates an event with category INCIDENT has a confirmed impact to at
    // least one Google Cloud product.
    CONFIRMED_INCIDENT = 1;

    // Indicates an event with category INCIDENT is under investigation to
    // determine if it has a confirmed impact on any Google Cloud products.
    EMERGING_INCIDENT = 2;
  }

  // The state of the organization event. This enum lists all possible states of
  // event.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // Event is actively affecting a Google Cloud product and will continue to
    // receive updates.
    ACTIVE = 1;

    // Event is no longer affecting the Google Cloud product or has been merged
    // with another event.
    CLOSED = 2;
  }

  // The detailed state of the incident. This enum lists all possible detailed
  // states of an incident.
  enum DetailedState {
    // Unspecified detail state.
    DETAILED_STATE_UNSPECIFIED = 0;

    // Google engineers are actively investigating the incident to determine the
    // impact.
    EMERGING = 1;

    // The incident is confirmed and impacting at least one Google Cloud
    // product. Ongoing status updates will be provided until it is resolved.
    CONFIRMED = 2;

    // The incident is no longer affecting any Google Cloud product, and there
    // will be no further updates.
    RESOLVED = 3;

    // The incident was merged into a parent event. All further updates will be
    // published to the parent only. The `parent_event` contains the name of the
    // parent.
    MERGED = 4;

    // The incident was automatically closed because of the following reasons:
    //
    //  * The impact of the incident could not be confirmed.
    //  * The incident was intermittent or resolved itself.
    //
    //  The incident does not have a resolution because no action or
    //  investigation happened. If it is intermittent, the incident may reopen.
    AUTO_CLOSED = 9;

    // Upon investigation, Google engineers concluded that the incident is not
    // affecting a Google Cloud product. This state can change if the incident
    // is reviewed again.
    FALSE_POSITIVE = 10;
  }

  // Output only. Identifier. Name of the event. Unique name of the event in
  // this scope including organization ID and location using the form
  // `organizations/{organization_id}/locations/{location}/organizationEvents/{event_id}`.
  //
  // `organization_id` - see [Getting your organization resource
  // ID](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).<br>
  // `location` - The location to get the service health events from.<br>
  // `event_id` - Organization event ID to retrieve.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. Brief description for the event.
  string title = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Free-form, human-readable description.
  string description = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The category of the event.
  EventCategory category = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The detailed category of the event.
  DetailedCategory detailed_category = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the event.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current detailed state of the incident.
  DetailedState detailed_state = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents the Google Cloud products and locations impacted by
  // the event.
  repeated EventImpact event_impacts = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Incident-only field. Event updates are correspondence from
  // Google.
  repeated EventUpdate updates = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When `detailed_state`=`MERGED`, `parent_event` contains the
  // name of the parent event. All further updates will be published to the
  // parent event.
  string parent_event = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the update was posted.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The start time of the event, if applicable.
  google.protobuf.Timestamp start_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The end time of the event, if applicable.
  google.protobuf.Timestamp end_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Incident-only field. The time when the next update can be
  // expected.
  google.protobuf.Timestamp next_update_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Records an update made to the event.
message EventUpdate {
  // Output only. The time the update was posted.
  google.protobuf.Timestamp update_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Brief title for the event.
  string title = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Free-form, human-readable description.
  string description = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Symptoms of the event, if available.
  string symptom = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Workaround steps to remediate the event impact, if available.
  string workaround = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the locations impacted by the event.
message Location {
  // Location impacted by the event. Example: `"us-central1"`
  string location_name = 1;
}

// Represents the Google Cloud product impacted by the event.
message Product {
  // Google Cloud product impacted by the event. Example: `"Google Cloud SQL"`
  string product_name = 1;

  // Unique identifier for the product.
  string id = 2;
}

// Represents the Google Cloud products and locations impacted by the event.
message EventImpact {
  // Google Cloud product impacted by the event.
  Product product = 1;

  // Location impacted by the event.
  Location location = 2;
}

// Represents impact to assets at organizational level. It is a read-only view
// and does not allow any modifications.
message OrganizationImpact {
  option (google.api.resource) = {
    type: "servicehealth.googleapis.com/OrganizationImpact"
    pattern: "organizations/{organization}/locations/{location}/organizationImpacts/{organization_impact}"
    plural: "organizationImpacts"
    singular: "organizationImpact"
  };

  // Output only. Identifier. Unique name of the organization impact in this
  // scope including organization and location using the form
  // `organizations/{organization_id}/locations/{location}/organizationImpacts/{organization_impact_id}`.
  //
  // `organization_id` - ID (number) of the organization that contains the
  // event. To get your `organization_id`, see
  // [Getting your organization resource
  // ID](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).<br>
  // `organization_impact_id` - ID of the [OrganizationImpact
  // resource](/service-health/docs/reference/rest/v1beta/organizations.locations.organizationImpacts#OrganizationImpact).
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. A list of event names impacting the asset.
  repeated string events = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "servicehealth.googleapis.com/Event"
    }
  ];

  // Output only. Google Cloud asset possibly impacted by the specified events.
  Asset asset = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the affected project was last modified.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the asset impacted by the events.
message Asset {
  // Output only. Full name of the resource as defined in
  // [Resource
  // Names](https://cloud.google.com/apis/design/resource_names#full_resource_name).
  string asset_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the asset. Example:
  // `"cloudresourcemanager.googleapis.com/Project"`
  string asset_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Requests list of events.
message ListEventsRequest {
  // Required. Parent value using the form
  // `projects/{project_id}/locations/{location}/events`.
  //
  // `project_id` - ID of the project for which to list service health
  // events.
  // `location` - The location to get the service health events from.
  // To retrieve service health events of category = INCIDENT, use `location` =
  // `global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "servicehealth.googleapis.com/Event"
    }
  ];

  // Optional. The maximum number of events that should be returned.  Acceptable
  // values are 1 to 100, inclusive. (The default value is 10.) If more results
  // are available, the service returns a next_page_token that you can use to
  // get the next page of results in subsequent list requests. The service may
  // return fewer events than the requested page_size.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  // Provide Page token returned by a previous `ListEvents` call to retrieve the
  // next page of results. When paginating, all other parameters provided to
  // `ListEvents` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter expression that filters resources listed in the
  // response. The expression takes the following forms: <br>
  // *   field=value for `category` and `state`<br>
  // *   field &lt;, >, &lt;=, or >= value for `update_time` <br>
  // Examples: `category=INCIDENT`, `update_time>="2000-01-01T11:30:00-04:00"`,
  // `event_impacts.product.product_name:"Eventarc"`
  // <br>
  //
  // Multiple filter queries are separated by spaces. Example:
  // `category=INCIDENT state=ACTIVE`.
  //
  // By default, each expression is an AND expression. However, you can include
  // AND and OR expressions explicitly.
  //
  // Filter is supported for the following fields: `category`, `state`,
  // `update_time`, `event_impacts.product.product_name`
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Event fields to include in response.
  EventView view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response to request for listing events.
message ListEventsResponse {
  // Output only. List of events.
  repeated Event events = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The continuation token, used to page through large result
  // sets. Provide this value in a subsequent request as page_token to retrieve
  // the next page.
  //
  // If this field is not present, there are no subsequent results.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Gets information about a specific event.
message GetEventRequest {
  // Required. Unique name of the event in this scope including project
  // and location using the form
  // `projects/{project_id}/locations/{location}/events/{event_id}`.
  //
  // `project_id` - Project ID of the project that contains the event. <br>
  // `location` - The location to get the service health events from. <br>
  // `event_id` - Event ID to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "servicehealth.googleapis.com/Event"
    }
  ];
}

// Requests list of events that affect an organization.
message ListOrganizationEventsRequest {
  // Required. Parent value using the form
  // `organizations/{organization_id}/locations/{location}/organizationEvents`.
  //
  // `organization_id` - ID (number) of the project that contains the event. To
  // get your `organization_id`, see
  // [Getting your organization resource
  // ID](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).<br>
  // `location` - The location to get the service health events from. To
  // retrieve service health events of category = INCIDENT, use `location` =
  // `global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "servicehealth.googleapis.com/OrganizationEvent"
    }
  ];

  // Optional. The maximum number of events that should be returned.  Acceptable
  // values are `1` to `100`, inclusive. (The default value is `10`.) If more
  // results are available, the service returns a `next_page_token` that you can
  // use to get the next page of results in subsequent list requests. The
  // service may return fewer events than the requested `page_size`.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  //
  // Provide Page token returned by a previous `ListOrganizationEvents` call to
  // retrieve the next page of results.
  //
  // When paginating, all other parameters provided to
  // `ListOrganizationEvents` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter expression that filters resources listed in the
  // response. The expression takes the following forms:
  //
  // *   field=value for `category` and `state`
  // *   field &lt;, >, &lt;=, or >= value for `update_time`
  //
  // Examples: `category=INCIDENT`, `update_time>="2000-01-01T11:30:00-04:00"`
  //
  // Multiple filter queries are space-separated. Example:
  // `category=INCIDENT state=ACTIVE`.
  //
  // By default, each expression is an AND expression. However, you can include
  // AND and OR expressions explicitly.
  //
  // Filter is supported for the following fields: `category`, `state`,
  // `update_time`
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. OrganizationEvent fields to include in response.
  OrganizationEventView view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response to request for listing organization events.
message ListOrganizationEventsResponse {
  // Output only. List of organization events affecting an organization.
  repeated OrganizationEvent organization_events = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The continuation token, used to page through large result
  // sets. Provide this value in a subsequent request as `page_token` to
  // retrieve the next page.
  //
  // If this field is not present, there are no subsequent results.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Gets information about a specific event affecting an organization.
message GetOrganizationEventRequest {
  // Required. Unique name of the event in this scope including organization and
  // event ID using the form
  // `organizations/{organization_id}/locations/locations/global/organizationEvents/{event_id}`.
  //
  // `organization_id` - ID (number) of the project that contains the event. To
  // get your `organization_id`, see
  // [Getting your organization resource
  // ID](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).<br>
  // `event_id` - Organization event ID to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "servicehealth.googleapis.com/OrganizationEvent"
    }
  ];
}

// Requests list of projects under an organization affected by an event.
message ListOrganizationImpactsRequest {
  // Required. Parent value using the form
  // `organizations/{organization_id}/locations/{location}/organizationImpacts`.
  //
  // `organization_id` - ID (number) of the project that contains the event. To
  // get your `organization_id`, see
  // [Getting your organization resource
  // ID](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "servicehealth.googleapis.com/OrganizationImpact"
    }
  ];

  // Optional. The maximum number of events that should be returned. Acceptable
  // values are `1` to `100`, inclusive. The default value is `10`.
  //
  //  If more results are available, the service returns a
  // `next_page_token` that can be used to get the next page of results in
  // subsequent list requests. The service may return fewer
  // [impacts](/service-health/docs/reference/rest/v1beta/organizations.locations.organizationImpacts#OrganizationImpact)
  // than the requested `page_size`.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  //
  // Provide `page_token` returned by a previous `ListOrganizationImpacts` call
  // to retrieve the next page of results.
  //
  // When paginating, all other parameters provided to `ListOrganizationImpacts`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter expression that filters resources listed in the
  // response. The expression is in the form of `field:value` for checking if a
  // repeated field contains a value.
  //
  // Example:
  // `events:organizations%2F{organization_id}%2Flocations%2Fglobal%2ForganizationEvents%2Fevent-id`
  //
  // To get your `{organization_id}`, see
  // [Getting your organization resource
  // ID](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).
  //
  // Multiple filter queries are separated by spaces.
  //
  // By default, each expression is an AND expression. However, you can include
  // AND and OR expressions explicitly.
  // Filter is supported for the following fields: `events`.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response to request for listing projects under an organization affected by an
// event.
message ListOrganizationImpactsResponse {
  // Output only. List of
  // [impacts](/service-health/docs/reference/rest/v1beta/organizations.locations.organizationImpacts#OrganizationImpact)
  // for an organization affected by service health events.
  repeated OrganizationImpact organization_impacts = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The continuation token, used to page through large result
  // sets. Provide this value in a subsequent request as `page_token` to
  // retrieve the next page.
  //
  // If this field is not present, there are no subsequent results.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Gets information about an event that affects a project under an organization.
message GetOrganizationImpactRequest {
  // Required. Name of the resource using the form
  // `organizations/{organization_id}/locations/global/organizationImpacts/{organization_impact_id}`.
  //
  // `organization_id` - ID (number) of the organization that contains the
  // event. To get your `organization_id`, see
  // [Getting your organization resource
  // ID](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).<br>
  // `organization_impact_id` - ID of the [OrganizationImpact
  // resource](/service-health/docs/reference/rest/v1beta/organizations.locations.organizationImpacts#OrganizationImpact).
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "servicehealth.googleapis.com/OrganizationImpact"
    }
  ];
}

// The event fields to include in ListEvents API response. This enum lists all
// possible event views.
enum EventView {
  // Unspecified event view. Default to `EVENT_VIEW_BASIC`.
  EVENT_VIEW_UNSPECIFIED = 0;

  // Includes all fields except `updates`. This view is the default for
  // ListEvents API.
  EVENT_VIEW_BASIC = 1;

  // Includes all event fields.
  EVENT_VIEW_FULL = 2;
}

// The organization event fields to include in ListOrganizationEvents API
// response. This enum lists all possible organization event views.
enum OrganizationEventView {
  // Unspecified event view. Default to `ORGANIZATION_EVENT_VIEW_BASIC`.
  ORGANIZATION_EVENT_VIEW_UNSPECIFIED = 0;

  // Includes all organization event fields except `updates`. This view is the
  // default for ListOrganizationEvents API.
  ORGANIZATION_EVENT_VIEW_BASIC = 1;

  // Includes all organization event fields.
  ORGANIZATION_EVENT_VIEW_FULL = 2;
}
