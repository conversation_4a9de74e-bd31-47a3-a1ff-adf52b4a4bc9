// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.servicehealth.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/servicehealth/v1/event_resources.proto";

option csharp_namespace = "Google.Cloud.ServiceHealth.V1";
option go_package = "cloud.google.com/go/servicehealth/apiv1/servicehealthpb;servicehealthpb";
option java_multiple_files = true;
option java_outer_classname = "EventServiceProto";
option java_package = "com.google.cloud.servicehealth.v1";
option php_namespace = "Google\\Cloud\\ServiceHealth\\V1";
option ruby_package = "Google::Cloud::ServiceHealth::V1";

// Request service health events relevant to your Google Cloud project.
service ServiceHealth {
  option (google.api.default_host) = "servicehealth.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists events under a given project and location.
  rpc ListEvents(ListEventsRequest) returns (ListEventsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/events"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a resource containing information about an event.
  rpc GetEvent(GetEventRequest) returns (Event) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/events/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists organization events under a given organization and location.
  rpc ListOrganizationEvents(ListOrganizationEventsRequest)
      returns (ListOrganizationEventsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*}/organizationEvents"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a resource containing information about an event affecting an
  // organization .
  rpc GetOrganizationEvent(GetOrganizationEventRequest)
      returns (OrganizationEvent) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/organizationEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists assets impacted by organization events under a given organization and
  // location.
  rpc ListOrganizationImpacts(ListOrganizationImpactsRequest)
      returns (ListOrganizationImpactsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*}/organizationImpacts"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a resource containing information about impact to an asset under
  // an organization affected by a service health event.
  rpc GetOrganizationImpact(GetOrganizationImpactRequest)
      returns (OrganizationImpact) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/organizationImpacts/*}"
    };
    option (google.api.method_signature) = "name";
  }
}
