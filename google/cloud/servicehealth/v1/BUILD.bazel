# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "servicehealth_proto",
    srcs = [
        "event_resources.proto",
        "event_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "servicehealth_proto_with_info",
    deps = [
        ":servicehealth_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "servicehealth_java_proto",
    deps = [":servicehealth_proto"],
)

java_grpc_library(
    name = "servicehealth_java_grpc",
    srcs = [":servicehealth_proto"],
    deps = [":servicehealth_java_proto"],
)

java_gapic_library(
    name = "servicehealth_java_gapic",
    srcs = [":servicehealth_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "servicehealth_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "servicehealth_v1.yaml",
    test_deps = [
        ":servicehealth_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":servicehealth_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "servicehealth_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.servicehealth.v1.ServiceHealthClientHttpJsonTest",
        "com.google.cloud.servicehealth.v1.ServiceHealthClientTest",
    ],
    runtime_deps = [":servicehealth_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-servicehealth-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":servicehealth_java_gapic",
        ":servicehealth_java_grpc",
        ":servicehealth_java_proto",
        ":servicehealth_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "servicehealth_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/servicehealth/apiv1/servicehealthpb",
    protos = [":servicehealth_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "servicehealth_go_gapic",
    srcs = [":servicehealth_proto_with_info"],
    grpc_service_config = "servicehealth_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/servicehealth/apiv1;servicehealth",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "servicehealth_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicehealth_go_proto",
        "//google/cloud/location:location_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-servicehealth-v1-go",
    deps = [
        ":servicehealth_go_gapic",
        ":servicehealth_go_gapic_srcjar-metadata.srcjar",
        ":servicehealth_go_gapic_srcjar-snippets.srcjar",
        ":servicehealth_go_gapic_srcjar-test.srcjar",
        ":servicehealth_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "servicehealth_py_gapic",
    srcs = [":servicehealth_proto"],
    grpc_service_config = "servicehealth_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "servicehealth_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "servicehealth_py_gapic_test",
    srcs = [
        "servicehealth_py_gapic_pytest.py",
        "servicehealth_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":servicehealth_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "servicehealth-v1-py",
    deps = [
        ":servicehealth_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "servicehealth_php_proto",
    deps = [":servicehealth_proto"],
)

php_gapic_library(
    name = "servicehealth_php_gapic",
    srcs = [":servicehealth_proto_with_info"],
    grpc_service_config = "servicehealth_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "servicehealth_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicehealth_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-servicehealth-v1-php",
    deps = [
        ":servicehealth_php_gapic",
        ":servicehealth_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "servicehealth_nodejs_gapic",
    package_name = "@google-cloud/servicehealth",
    src = ":servicehealth_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "servicehealth_v1_grpc_service_config.json",
    package = "google.cloud.servicehealth.v1",
    rest_numeric_enums = True,
    service_yaml = "servicehealth_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "servicehealth-v1-nodejs",
    deps = [
        ":servicehealth_nodejs_gapic",
        ":servicehealth_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "servicehealth_ruby_proto",
    deps = [":servicehealth_proto"],
)

ruby_grpc_library(
    name = "servicehealth_ruby_grpc",
    srcs = [":servicehealth_proto"],
    deps = [":servicehealth_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "servicehealth_ruby_gapic",
    srcs = [":servicehealth_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-service_health-v1",
    ],
    grpc_service_config = "servicehealth_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "servicehealth_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicehealth_ruby_grpc",
        ":servicehealth_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-servicehealth-v1-ruby",
    deps = [
        ":servicehealth_ruby_gapic",
        ":servicehealth_ruby_grpc",
        ":servicehealth_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "servicehealth_csharp_proto",
    extra_opts = [],
    deps = [":servicehealth_proto"],
)

csharp_grpc_library(
    name = "servicehealth_csharp_grpc",
    srcs = [":servicehealth_proto"],
    deps = [":servicehealth_csharp_proto"],
)

csharp_gapic_library(
    name = "servicehealth_csharp_gapic",
    srcs = [":servicehealth_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "servicehealth_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "servicehealth_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicehealth_csharp_grpc",
        ":servicehealth_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-servicehealth-v1-csharp",
    deps = [
        ":servicehealth_csharp_gapic",
        ":servicehealth_csharp_grpc",
        ":servicehealth_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "servicehealth_cc_proto",
    deps = [":servicehealth_proto"],
)

cc_grpc_library(
    name = "servicehealth_cc_grpc",
    srcs = [":servicehealth_proto"],
    grpc_only = True,
    deps = [":servicehealth_cc_proto"],
)
