{"methodConfig": [{"name": [{"service": "google.cloud.servicehealth.v1.ServiceHealth", "method": "GetEvent"}, {"service": "google.cloud.servicehealth.v1.ServiceHealth", "method": "GetOrganizationEvent"}, {"service": "google.cloud.servicehealth.v1.ServiceHealth", "method": "GetOrganizationImpact"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.servicehealth.v1.ServiceHealth", "method": "ListEvents"}, {"service": "google.cloud.servicehealth.v1.ServiceHealth", "method": "ListOrganizationEvents"}, {"service": "google.cloud.servicehealth.v1.ServiceHealth", "method": "ListOrganizationImpacts"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}