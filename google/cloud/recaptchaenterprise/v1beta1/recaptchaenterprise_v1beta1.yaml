type: google.api.Service
config_version: 3
name: recaptchaenterprise.googleapis.com
title: reCAPTCHA Enterprise API

apis:
- name: google.cloud.recaptchaenterprise.v1beta1.RecaptchaEnterpriseServiceV1Beta1

documentation:
  summary: |-
    Help protect your website from fraudulent activity, spam, and abuse without
    creating friction.

authentication:
  rules:
  - selector: google.cloud.recaptchaenterprise.v1beta1.RecaptchaEnterpriseServiceV1Beta1.AnnotateAssessment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.recaptchaenterprise.v1beta1.RecaptchaEnterpriseServiceV1Beta1.CreateAssessment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
