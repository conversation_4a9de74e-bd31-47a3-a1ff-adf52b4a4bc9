// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recaptchaenterprise.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.RecaptchaEnterprise.V1Beta1";
option go_package = "cloud.google.com/go/recaptchaenterprise/v2/apiv1beta1/recaptchaenterprisepb;recaptchaenterprisepb";
option java_multiple_files = true;
option java_outer_classname = "RecaptchaEnterpriseProto";
option java_package = "com.google.recaptchaenterprise.v1beta1";
option objc_class_prefix = "GCRE";
option php_namespace = "Google\\Cloud\\RecaptchaEnterprise\\V1beta1";
option ruby_package = "Google::Cloud::RecaptchaEnterprise::V1beta1";

// Service to determine the likelihood an event is legitimate.
service RecaptchaEnterpriseServiceV1Beta1 {
  option (google.api.default_host) = "recaptchaenterprise.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates an Assessment of the likelihood an event is legitimate.
  rpc CreateAssessment(CreateAssessmentRequest) returns (Assessment) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*}/assessments"
      body: "assessment"
    };
    option (google.api.method_signature) = "parent,assessment";
  }

  // Annotates a previously created Assessment to provide additional information
  // on whether the event turned out to be authentic or fradulent.
  rpc AnnotateAssessment(AnnotateAssessmentRequest)
      returns (AnnotateAssessmentResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/assessments/*}:annotate"
      body: "*"
    };
    option (google.api.method_signature) = "name,annotation";
  }
}

// The create assessment request message.
message CreateAssessmentRequest {
  // Required. The name of the project in which the assessment is created,
  // in the format `projects/{project_number}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The assessment details.
  Assessment assessment = 2 [(google.api.field_behavior) = REQUIRED];
}

// Describes an event in the lifecycle of a payment transaction.
message TransactionEvent {
  // Enum that represents an event in the payment transaction lifecycle.
  enum TransactionEventType {
    // Default, unspecified event type.
    TRANSACTION_EVENT_TYPE_UNSPECIFIED = 0;

    // Indicates that the transaction is approved by the merchant. The
    // accompanying reasons can include terms such as 'INHOUSE', 'ACCERTIFY',
    // 'CYBERSOURCE', or 'MANUAL_REVIEW'.
    MERCHANT_APPROVE = 1;

    // Indicates that the transaction is denied and concluded due to risks
    // detected by the merchant. The accompanying reasons can include terms such
    // as 'INHOUSE',  'ACCERTIFY',  'CYBERSOURCE', or 'MANUAL_REVIEW'.
    MERCHANT_DENY = 2;

    // Indicates that the transaction is being evaluated by a human, due to
    // suspicion or risk.
    MANUAL_REVIEW = 3;

    // Indicates that the authorization attempt with the card issuer succeeded.
    AUTHORIZATION = 4;

    // Indicates that the authorization attempt with the card issuer failed.
    // The accompanying reasons can include Visa's '54' indicating that the card
    // is expired, or '82' indicating that the CVV is incorrect.
    AUTHORIZATION_DECLINE = 5;

    // Indicates that the transaction is completed because the funds were
    // settled.
    PAYMENT_CAPTURE = 6;

    // Indicates that the transaction could not be completed because the funds
    // were not settled.
    PAYMENT_CAPTURE_DECLINE = 7;

    // Indicates that the transaction has been canceled. Specify the reason
    // for the cancellation. For example, 'INSUFFICIENT_INVENTORY'.
    CANCEL = 8;

    // Indicates that the merchant has received a chargeback inquiry due to
    // fraud for the transaction, requesting additional information before a
    // fraud chargeback is officially issued and a formal chargeback
    // notification is sent.
    CHARGEBACK_INQUIRY = 9;

    // Indicates that the merchant has received a chargeback alert due to fraud
    // for the transaction. The process of resolving the dispute without
    // involving the payment network is started.
    CHARGEBACK_ALERT = 10;

    // Indicates that a fraud notification is issued for the transaction, sent
    // by the payment instrument's issuing bank because the transaction appears
    // to be fraudulent. We recommend including TC40 or SAFE data in the
    // `reason` field for this event type. For partial chargebacks, we recommend
    // that you include an amount in the `value` field.
    FRAUD_NOTIFICATION = 11;

    // Indicates that the merchant is informed by the payment network that the
    // transaction has entered the chargeback process due to fraud. Reason code
    // examples include Discover's '6005' and '6041'. For partial chargebacks,
    // we recommend that you include an amount in the `value` field.
    CHARGEBACK = 12;

    // Indicates that the transaction has entered the chargeback process due to
    // fraud, and that the merchant has chosen to enter representment. Reason
    // examples include Discover's '6005' and '6041'. For partial chargebacks,
    // we recommend that you include an amount in the `value` field.
    CHARGEBACK_REPRESENTMENT = 13;

    // Indicates that the transaction has had a fraud chargeback which was
    // illegitimate and was reversed as a result. For partial chargebacks, we
    // recommend that you include an amount in the `value` field.
    CHARGEBACK_REVERSE = 14;

    // Indicates that the merchant has received a refund for a completed
    // transaction. For partial refunds, we recommend that you include an amount
    // in the `value` field. Reason example: 'TAX_EXEMPT' (partial refund of
    // exempt tax)
    REFUND_REQUEST = 15;

    // Indicates that the merchant has received a refund request for this
    // transaction, but that they have declined it. For partial refunds, we
    // recommend that you include an amount in the `value` field. Reason
    // example: 'TAX_EXEMPT' (partial refund of exempt tax)
    REFUND_DECLINE = 16;

    // Indicates that the completed transaction was refunded by the merchant.
    // For partial refunds, we recommend that you include an amount in the
    // `value` field. Reason example: 'TAX_EXEMPT' (partial refund of exempt
    // tax)
    REFUND = 17;

    // Indicates that the completed transaction was refunded by the merchant,
    // and that this refund was reversed. For partial refunds, we recommend that
    // you include an amount in the `value` field.
    REFUND_REVERSE = 18;
  }

  // Optional. The type of this transaction event.
  TransactionEventType event_type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The reason or standardized code that corresponds with this
  // transaction event, if one exists. For example, a CHARGEBACK event with code
  // 6005.
  string reason = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value that corresponds with this transaction event, if one
  // exists. For example, a refund event where $5.00 was refunded. Currency is
  // obtained from the original transaction data.
  double value = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Timestamp when this transaction event occurred; otherwise assumed
  // to be the time of the API call.
  google.protobuf.Timestamp event_time = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// The request message to annotate an Assessment.
message AnnotateAssessmentRequest {
  // Enum that represents the types of annotations.
  enum Annotation {
    // Default unspecified type.
    ANNOTATION_UNSPECIFIED = 0;

    // Provides information that the event turned out to be legitimate.
    LEGITIMATE = 1;

    // Provides information that the event turned out to be fraudulent.
    FRAUDULENT = 2;

    // Provides information that the event was related to a login event in which
    // the user typed the correct password. Deprecated, prefer indicating
    // CORRECT_PASSWORD through the reasons field instead.
    PASSWORD_CORRECT = 3 [deprecated = true];

    // Provides information that the event was related to a login event in which
    // the user typed the incorrect password. Deprecated, prefer indicating
    // INCORRECT_PASSWORD through the reasons field instead.
    PASSWORD_INCORRECT = 4 [deprecated = true];
  }

  // Enum that represents potential reasons for annotating an assessment.
  enum Reason {
    // Default unspecified reason.
    REASON_UNSPECIFIED = 0;

    // Indicates that the transaction had a chargeback issued with no other
    // details. When possible, specify the type by using CHARGEBACK_FRAUD or
    // CHARGEBACK_DISPUTE instead.
    CHARGEBACK = 1;

    // Indicates that the transaction had a chargeback issued related to an
    // alleged unauthorized transaction from the cardholder's perspective (for
    // example, the card number was stolen).
    CHARGEBACK_FRAUD = 8;

    // Indicates that the transaction had a chargeback issued related to the
    // cardholder having provided their card details but allegedly not being
    // satisfied with the purchase (for example, misrepresentation, attempted
    // cancellation).
    CHARGEBACK_DISPUTE = 9;

    // Indicates that the completed payment transaction was refunded by the
    // seller.
    REFUND = 10;

    // Indicates that the completed payment transaction was determined to be
    // fraudulent by the seller, and was cancelled and refunded as a result.
    REFUND_FRAUD = 11;

    // Indicates that the payment transaction was accepted, and the user was
    // charged.
    TRANSACTION_ACCEPTED = 12;

    // Indicates that the payment transaction was declined, for example due to
    // invalid card details.
    TRANSACTION_DECLINED = 13;

    // Indicates the transaction associated with the assessment is suspected of
    // being fraudulent based on the payment method, billing details, shipping
    // address or other transaction information.
    PAYMENT_HEURISTICS = 2;

    // Indicates that the user was served a 2FA challenge. An old assessment
    // with `ENUM_VALUES.INITIATED_TWO_FACTOR` reason that has not been
    // overwritten with `PASSED_TWO_FACTOR` is treated as an abandoned 2FA flow.
    // This is equivalent to `FAILED_TWO_FACTOR`.
    INITIATED_TWO_FACTOR = 7;

    // Indicates that the user passed a 2FA challenge.
    PASSED_TWO_FACTOR = 3;

    // Indicates that the user failed a 2FA challenge.
    FAILED_TWO_FACTOR = 4;

    // Indicates the user provided the correct password.
    CORRECT_PASSWORD = 5;

    // Indicates the user provided an incorrect password.
    INCORRECT_PASSWORD = 6;

    // Indicates that the user sent unwanted and abusive messages to other users
    // of the platform, such as spam, scams, phishing, or social engineering.
    SOCIAL_SPAM = 14;
  }

  // Required. The resource name of the Assessment, in the format
  // `projects/{project_number}/assessments/{assessment_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recaptchaenterprise.googleapis.com/Assessment"
    }
  ];

  // Optional. The annotation that is assigned to the Event. This field can be
  // left empty to provide reasons that apply to an event without concluding
  // whether the event is legitimate or fraudulent.
  Annotation annotation = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Reasons for the annotation that are assigned to the event.
  repeated Reason reasons = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Unique stable hashed user identifier to apply to the assessment.
  // This is an alternative to setting the `hashed_account_id` in
  // `CreateAssessment`, for example, when the account identifier is not yet
  // known in the initial request. It is recommended that the identifier is
  // hashed using hmac-sha256 with stable secret.
  bytes hashed_account_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If the assessment is part of a payment transaction, provide
  // details on payment lifecycle events that occur in the transaction.
  TransactionEvent transaction_event = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Empty response for AnnotateAssessment.
message AnnotateAssessmentResponse {}

// Password leak verification info.
message PasswordLeakVerification {
  // Optional. Scrypt hash of the username+password that the customer wants to
  // verify against a known password leak.
  bytes hashed_user_credentials = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether or not the user's credentials are present in a known
  // leak.
  bool credentials_leaked = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The username part of the user credentials for which we want to
  // trigger a leak check in canonicalized form. This is the same data used to
  // create the hashed_user_credentials on the customer side.
  string canonicalized_username = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A reCAPTCHA Enterprise assessment resource.
message Assessment {
  option (google.api.resource) = {
    type: "recaptchaenterprise.googleapis.com/Assessment"
    pattern: "projects/{project}/assessments/{assessment}"
  };

  // Reasons contributing to the risk analysis verdict.
  enum ClassificationReason {
    // Default unspecified type.
    CLASSIFICATION_REASON_UNSPECIFIED = 0;

    // Interactions matched the behavior of an automated agent.
    AUTOMATION = 1;

    // The event originated from an illegitimate environment.
    UNEXPECTED_ENVIRONMENT = 2;

    // Traffic volume from the event source is higher than normal.
    TOO_MUCH_TRAFFIC = 3;

    // Interactions with the site were significantly different than expected
    // patterns.
    UNEXPECTED_USAGE_PATTERNS = 4;

    // Too little traffic has been received from this site thus far to generate
    // quality risk analysis.
    LOW_CONFIDENCE_SCORE = 5;

    // The request matches behavioral characteristics of a carding attack.
    SUSPECTED_CARDING = 6;

    // The request matches behavioral characteristics of chargebacks for fraud.
    SUSPECTED_CHARGEBACK = 7;
  }

  // Output only. The resource name for the Assessment in the format
  // `projects/{project_number}/assessments/{assessment_id}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The event being assessed.
  Event event = 2;

  // Output only. Legitimate event score from 0.0 to 1.0.
  // (1.0 means very likely legitimate traffic while 0.0 means very likely
  // non-legitimate traffic).
  float score = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Properties of the provided event token.
  TokenProperties token_properties = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reasons contributing to the risk analysis verdict.
  repeated ClassificationReason reasons = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Information about the user's credentials used to check for leaks.
  // This feature is part of the Early Access Program (EAP). Exercise caution,
  // and do not deploy integrations based on this feature in a production
  // environment.
  PasswordLeakVerification password_leak_verification = 7;

  // Assessment returned by account defender when a hashed_account_id is
  // provided.
  AccountDefenderAssessment account_defender_assessment = 8;

  // Assessment returned by Fraud Prevention when TransactionData is provided.
  FraudPreventionAssessment fraud_prevention_assessment = 11;
}

message Event {
  // Setting that controls Fraud Prevention assessments.
  enum FraudPrevention {
    // Default, unspecified setting. `fraud_prevention_assessment` is returned
    // if `transaction_data` is present in `Event` and Fraud Prevention is
    // enabled in the Google Cloud console.
    FRAUD_PREVENTION_UNSPECIFIED = 0;

    // Enable Fraud Prevention for this assessment, if Fraud Prevention is
    // enabled in the Google Cloud console.
    ENABLED = 1;

    // Disable Fraud Prevention for this assessment, regardless of Google Cloud
    // console settings.
    DISABLED = 2;
  }

  // Optional. The user response token provided by the reCAPTCHA client-side
  // integration on your site.
  string token = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The site key that was used to invoke reCAPTCHA on your site and
  // generate the token.
  string site_key = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The user agent present in the request from the user's device
  // related to this event.
  string user_agent = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The IP address in the request from the user's device related to
  // this event.
  string user_ip_address = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The expected action for this type of event. This should be the
  // same action provided at token generation time on client-side platforms
  // already integrated with reCAPTCHA.
  string expected_action = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Unique stable hashed user identifier for the request. The
  // identifier must be hashed using hmac-sha256 with stable secret.
  bytes hashed_account_id = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Data describing a payment transaction to be assessed. Sending
  // this data enables reCAPTCHA Fraud Prevention and the
  // FraudPreventionAssessment component in the response.
  TransactionData transaction_data = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Fraud Prevention setting for this Assessment.
  FraudPrevention fraud_prevention = 17
      [(google.api.field_behavior) = OPTIONAL];
}

// Transaction data associated with a payment protected by reCAPTCHA.
message TransactionData {
  // Structured address format for billing and shipping addresses.
  message Address {
    // The recipient name, potentially including information such as "care of".
    string recipient = 1;

    // The first lines of the address. The first line generally contains the
    // street name and number, and further lines may include information such as
    // an apartment number.
    repeated string address = 2;

    // The town/city of the address.
    string locality = 3;

    // The state, province, or otherwise administrative area of the address.
    string administrative_area = 4;

    // The CLDR country/region of the address.
    string region_code = 5;

    // The postal or ZIP code of the address.
    string postal_code = 6;
  }

  // Details about a user's account involved in the transaction.
  message User {
    // Unique account identifier for this user. If using account defender,
    // this should match the hashed_account_id field. Otherwise, a unique and
    // persistent identifier for this account.
    string account_id = 6;

    // The epoch milliseconds of the user's account creation.
    int64 creation_ms = 1;

    // The email address of the user.
    string email = 2;

    // Whether the email has been verified to be accessible by the user (OTP or
    // similar).
    bool email_verified = 3;

    // The phone number of the user, with country code.
    string phone_number = 4;

    // Whether the phone number has been verified to be accessible by the user
    // (OTP or similar).
    bool phone_verified = 5;
  }

  // Line items being purchased in this transaction.
  message Item {
    // The full name of the item.
    string name = 1;

    // The value per item that the user is paying, in the transaction currency,
    // after discounts.
    double value = 2;

    // The quantity of this item that is being purchased.
    int64 quantity = 3;

    // When a merchant is specified, its corresponding account_id. Necessary to
    // populate marketplace-style transactions.
    string merchant_account_id = 4;
  }

  // Details about the transaction from the gateway.
  message GatewayInfo {
    // Name of the gateway service (for example, stripe, square, paypal).
    string name = 1;

    // Gateway response code describing the state of the transaction.
    string gateway_response_code = 2;

    // AVS response code from the gateway
    // (available only when reCAPTCHA is called after authorization).
    string avs_response_code = 3;

    // CVV response code from the gateway
    // (available only when reCAPTCHA is called after authorization).
    string cvv_response_code = 4;
  }

  // Unique identifier for the transaction. This custom identifier can be used
  // to reference this transaction in the future, for example, labeling a refund
  // or chargeback event. Two attempts at the same transaction should use the
  // same transaction id.
  optional string transaction_id = 11;

  // The payment method for the transaction. The allowed values are:
  //
  // * credit-card
  // * debit-card
  // * gift-card
  // * processor-{name} (If a third-party is used, for example,
  // processor-paypal)
  // * custom-{name} (If an alternative method is used, for example,
  // custom-crypto)
  string payment_method = 1;

  // The Bank Identification Number - generally the first 6 or 8 digits of the
  // card.
  string card_bin = 2;

  // The last four digits of the card.
  string card_last_four = 3;

  // The currency code in ISO-4217 format.
  string currency_code = 4;

  // The decimal value of the transaction in the specified currency.
  double value = 5;

  // The value of shipping in the specified currency. 0 for free or no shipping.
  double shipping_value = 12;

  // Destination address if this transaction involves shipping a physical item.
  Address shipping_address = 6;

  // Address associated with the payment method when applicable.
  Address billing_address = 7;

  // Information about the user paying/initiating the transaction.
  User user = 8;

  // Information about the user or users fulfilling the transaction.
  repeated User merchants = 13;

  // Items purchased in this transaction.
  repeated Item items = 14;

  // Information about the payment gateway's response to the transaction.
  GatewayInfo gateway_info = 10;
}

message TokenProperties {
  // Enum that represents the types of invalid token reasons.
  enum InvalidReason {
    // Default unspecified type.
    INVALID_REASON_UNSPECIFIED = 0;

    // If the failure reason was not accounted for.
    UNKNOWN_INVALID_REASON = 1;

    // The provided user verification token was malformed.
    MALFORMED = 2;

    // The user verification token had expired.
    EXPIRED = 3;

    // The user verification had already been seen.
    DUPE = 4;

    // The user verification token did not match the provided site key.
    // This may be a configuration error (for example, development keys used in
    // production) or end users trying to use verification tokens from other
    // sites.
    SITE_MISMATCH = 5 [deprecated = true];

    // The user verification token was not present.  It is a required input.
    MISSING = 6;

    // A retriable error (such as network failure) occurred on the browser.
    // Could easily be simulated by an attacker.
    BROWSER_ERROR = 7;
  }

  // Whether the provided user response token is valid. When valid = false, the
  // reason could be specified in invalid_reason or it could also be due to
  // a user failing to solve a challenge or a sitekey mismatch (i.e the sitekey
  // used to generate the token was different than the one specified in the
  // assessment).
  bool valid = 1;

  // Reason associated with the response when valid = false.
  InvalidReason invalid_reason = 2;

  // The timestamp corresponding to the generation of the token.
  google.protobuf.Timestamp create_time = 3;

  // The hostname of the page on which the token was generated.
  string hostname = 4;

  // Action name provided at token generation.
  string action = 5;
}

// Assessment for Fraud Prevention.
message FraudPreventionAssessment {
  // Information about stolen instrument fraud, where the user is not the
  // legitimate owner of the instrument being used for the purchase.
  message StolenInstrumentVerdict {
    // Probability (0-1) of this transaction being executed with a stolen
    // instrument.
    float risk = 1;
  }

  // Information about card testing fraud, where an adversary is testing
  // fraudulently obtained cards or brute forcing their details.
  message CardTestingVerdict {
    // Probability (0-1) of this transaction attempt being part of a card
    // testing attack.
    float risk = 1;
  }

  // Information about behavioral trust of the transaction.
  message BehavioralTrustVerdict {
    // Probability (0-1) of this transaction attempt being executed in a
    // behaviorally trustworthy way.
    float trust = 1;
  }

  // Probability (0-1) of this transaction being fraudulent. Summarizes the
  // combined risk of attack vectors below.
  float transaction_risk = 1;

  // Assessment of this transaction for risk of a stolen instrument.
  StolenInstrumentVerdict stolen_instrument_verdict = 2;

  // Assessment of this transaction for risk of being part of a card testing
  // attack.
  CardTestingVerdict card_testing_verdict = 3;

  // Assessment of this transaction for behavioral trust.
  BehavioralTrustVerdict behavioral_trust_verdict = 4;
}

// Account defender risk assessment.
message AccountDefenderAssessment {
  // Labels returned by account defender for this request.
  enum AccountDefenderLabel {
    // Default unspecified type.
    ACCOUNT_DEFENDER_LABEL_UNSPECIFIED = 0;

    // The request matches a known good profile for the user.
    PROFILE_MATCH = 1;

    // The request is potentially a suspicious login event and should be further
    // verified either via multi-factor authentication or another system.
    SUSPICIOUS_LOGIN_ACTIVITY = 2;

    // The request matched a profile that previously had suspicious account
    // creation behavior. This could mean this is a fake account.
    SUSPICIOUS_ACCOUNT_CREATION = 3;

    // The account in the request has a high number of related accounts. It does
    // not necessarily imply that the account is bad but could require
    // investigating.
    RELATED_ACCOUNTS_NUMBER_HIGH = 4;
  }

  // Labels for this request.
  repeated AccountDefenderLabel labels = 1;
}
