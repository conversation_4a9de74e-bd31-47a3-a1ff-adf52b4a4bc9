# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "recaptchaenterprise_proto",
    srcs = [
        "recaptchaenterprise.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "recaptchaenterprise_proto_with_info",
    deps = [
        ":recaptchaenterprise_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "recaptchaenterprise_java_proto",
    deps = [":recaptchaenterprise_proto"],
)

java_grpc_library(
    name = "recaptchaenterprise_java_grpc",
    srcs = [":recaptchaenterprise_proto"],
    deps = [":recaptchaenterprise_java_proto"],
)

java_gapic_library(
    name = "recaptchaenterprise_java_gapic",
    srcs = [":recaptchaenterprise_proto_with_info"],
    gapic_yaml = "recaptchaenterprise_gapic.yaml",
    grpc_service_config = "recaptchaenterprise_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recaptchaenterprise_v1beta1.yaml",
    test_deps = [
        ":recaptchaenterprise_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":recaptchaenterprise_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "recaptchaenterprise_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.recaptchaenterprise.v1beta1.RecaptchaEnterpriseServiceV1Beta1ClientHttpJsonTest",
        "com.google.cloud.recaptchaenterprise.v1beta1.RecaptchaEnterpriseServiceV1Beta1ClientTest",
    ],
    runtime_deps = [":recaptchaenterprise_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-recaptchaenterprise-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":recaptchaenterprise_java_gapic",
        ":recaptchaenterprise_java_grpc",
        ":recaptchaenterprise_java_proto",
        ":recaptchaenterprise_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "recaptchaenterprise_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/recaptchaenterprise/v2/apiv1beta1/recaptchaenterprisepb",
    protos = [":recaptchaenterprise_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "recaptchaenterprise_go_gapic",
    srcs = [":recaptchaenterprise_proto_with_info"],
    grpc_service_config = "recaptchaenterprise_grpc_service_config.json",
    importpath = "cloud.google.com/go/recaptchaenterprise/v2/apiv1beta1;recaptchaenterprise",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "recaptchaenterprise_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recaptchaenterprise_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-recaptchaenterprise-v1beta1-go",
    deps = [
        ":recaptchaenterprise_go_gapic",
        ":recaptchaenterprise_go_gapic_srcjar-metadata.srcjar",
        ":recaptchaenterprise_go_gapic_srcjar-snippets.srcjar",
        ":recaptchaenterprise_go_gapic_srcjar-test.srcjar",
        ":recaptchaenterprise_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "recaptchaenterprise_py_gapic",
    srcs = [":recaptchaenterprise_proto"],
    grpc_service_config = "recaptchaenterprise_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-recaptcha-enterprise"],
    rest_numeric_enums = True,
    service_yaml = "recaptchaenterprise_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "recaptchaenterprise_py_gapic_test",
    srcs = [
        "recaptchaenterprise_py_gapic_pytest.py",
        "recaptchaenterprise_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":recaptchaenterprise_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "recaptchaenterprise-v1beta1-py",
    deps = [
        ":recaptchaenterprise_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "recaptchaenterprise_php_proto",
    deps = [":recaptchaenterprise_proto"],
)

php_gapic_library(
    name = "recaptchaenterprise_php_gapic",
    srcs = [":recaptchaenterprise_proto_with_info"],
    grpc_service_config = "recaptchaenterprise_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "recaptchaenterprise_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recaptchaenterprise_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-recaptchaenterprise-v1beta1-php",
    deps = [
        ":recaptchaenterprise_php_gapic",
        ":recaptchaenterprise_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "recaptchaenterprise_nodejs_gapic",
    package_name = "@google-cloud/recaptcha-enterprise",
    src = ":recaptchaenterprise_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "recaptchaenterprise_grpc_service_config.json",
    package = "google.cloud.recaptchaenterprise.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "recaptchaenterprise_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "recaptchaenterprise-v1beta1-nodejs",
    deps = [
        ":recaptchaenterprise_nodejs_gapic",
        ":recaptchaenterprise_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "recaptchaenterprise_ruby_proto",
    deps = [":recaptchaenterprise_proto"],
)

ruby_grpc_library(
    name = "recaptchaenterprise_ruby_grpc",
    srcs = [":recaptchaenterprise_proto"],
    deps = [":recaptchaenterprise_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "recaptchaenterprise_ruby_gapic",
    srcs = [":recaptchaenterprise_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=recaptchaenterprise.googleapis.com",
        "ruby-cloud-api-shortname=recaptchaenterprise",
        "ruby-cloud-env-prefix=RECAPTCHA_ENTERPRISE",
        "ruby-cloud-gem-name=google-cloud-recaptcha_enterprise-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/recaptcha-enterprise",
        "ruby-cloud-service-override=RecaptchaEnterpriseServiceV1Beta1=RecaptchaEnterpriseService",
    ],
    grpc_service_config = "recaptchaenterprise_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "reCAPTCHA Enterprise is a service that protects your site from spam and abuse.",
    ruby_cloud_title = "reCAPTCHA Enterprise V1beta1",
    service_yaml = "recaptchaenterprise_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recaptchaenterprise_ruby_grpc",
        ":recaptchaenterprise_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-recaptchaenterprise-v1beta1-ruby",
    deps = [
        ":recaptchaenterprise_ruby_gapic",
        ":recaptchaenterprise_ruby_grpc",
        ":recaptchaenterprise_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "recaptchaenterprise_csharp_proto",
    extra_opts = [],
    deps = [":recaptchaenterprise_proto"],
)

csharp_grpc_library(
    name = "recaptchaenterprise_csharp_grpc",
    srcs = [":recaptchaenterprise_proto"],
    deps = [":recaptchaenterprise_csharp_proto"],
)

csharp_gapic_library(
    name = "recaptchaenterprise_csharp_gapic",
    srcs = [":recaptchaenterprise_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "recaptchaenterprise_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recaptchaenterprise_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recaptchaenterprise_csharp_grpc",
        ":recaptchaenterprise_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-recaptchaenterprise-v1beta1-csharp",
    deps = [
        ":recaptchaenterprise_csharp_gapic",
        ":recaptchaenterprise_csharp_grpc",
        ":recaptchaenterprise_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "recaptchaenterprise_cc_proto",
    deps = [":recaptchaenterprise_proto"],
)

cc_grpc_library(
    name = "recaptchaenterprise_cc_grpc",
    srcs = [":recaptchaenterprise_proto"],
    grpc_only = True,
    deps = [":recaptchaenterprise_cc_proto"],
)
