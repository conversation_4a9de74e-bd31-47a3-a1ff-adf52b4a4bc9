type: google.api.Service
config_version: 3
name: recaptchaenterprise.googleapis.com
title: reCAPTCHA Enterprise API

apis:
- name: google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService

documentation:
  summary: |-
    Help protect your website from fraudulent activity, spam, and abuse without
    creating friction.

authentication:
  rules:
  - selector: 'google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
