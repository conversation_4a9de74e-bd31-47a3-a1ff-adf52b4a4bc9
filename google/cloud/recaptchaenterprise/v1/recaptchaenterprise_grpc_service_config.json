{"methodConfig": [{"name": [{"service": "google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService", "method": "CreateAssessment"}, {"service": "google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService", "method": "AnnotateAssessment"}, {"service": "google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService", "method": "ListKeys"}, {"service": "google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService", "method": "Get<PERSON><PERSON>"}, {"service": "google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService", "method": "DeleteKey"}, {"service": "google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseService", "method": "Update<PERSON><PERSON>"}], "timeout": "600s"}]}