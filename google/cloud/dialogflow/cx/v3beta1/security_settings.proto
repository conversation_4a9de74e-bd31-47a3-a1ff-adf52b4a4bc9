// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "SecuritySettingsProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";
option (google.api.resource_definition) = {
  type: "dlp.googleapis.com/InspectTemplate"
  pattern: "organizations/{organization}/locations/{location}/inspectTemplates/{inspect_template}"
  pattern: "projects/{project}/locations/{location}/inspectTemplates/{inspect_template}"
};
option (google.api.resource_definition) = {
  type: "dlp.googleapis.com/DeidentifyTemplate"
  pattern: "organizations/{organization}/locations/{location}/deidentifyTemplates/{deidentify_template}"
  pattern: "projects/{project}/locations/{location}/deidentifyTemplates/{deidentify_template}"
};

// Service for managing security settings for Dialogflow.
service SecuritySettingsService {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Create security settings in the specified location.
  rpc CreateSecuritySettings(CreateSecuritySettingsRequest)
      returns (SecuritySettings) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*}/securitySettings"
      body: "security_settings"
    };
    option (google.api.method_signature) = "parent,security_settings";
  }

  // Retrieves the specified
  // [SecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettings].
  // The returned settings may be stale by up to 1 minute.
  rpc GetSecuritySettings(GetSecuritySettingsRequest)
      returns (SecuritySettings) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/securitySettings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the specified
  // [SecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettings].
  rpc UpdateSecuritySettings(UpdateSecuritySettingsRequest)
      returns (SecuritySettings) {
    option (google.api.http) = {
      patch: "/v3beta1/{security_settings.name=projects/*/locations/*/securitySettings/*}"
      body: "security_settings"
    };
    option (google.api.method_signature) = "security_settings,update_mask";
  }

  // Returns the list of all security settings in the specified location.
  rpc ListSecuritySettings(ListSecuritySettingsRequest)
      returns (ListSecuritySettingsResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*}/securitySettings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes the specified
  // [SecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettings].
  rpc DeleteSecuritySettings(DeleteSecuritySettingsRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/securitySettings/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// The request message for
// [SecuritySettingsService.GetSecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettingsService.GetSecuritySettings].
message GetSecuritySettingsRequest {
  // Required. Resource name of the settings.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/securitySettings/<securitysettingsID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/SecuritySettings"
    }
  ];
}

// The request message for
// [SecuritySettingsService.UpdateSecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettingsService.UpdateSecuritySettings].
message UpdateSecuritySettingsRequest {
  // Required. [SecuritySettings] object that contains values for each of the
  // fields to update.
  SecuritySettings security_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The mask to control which fields get updated. If the mask is not
  // present, all fields will be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for [SecuritySettings.ListSecuritySettings][].
message ListSecuritySettingsRequest {
  // Required. The location to list all security settings for.
  // Format: `projects/<ProjectID>/locations/<LocationID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/SecuritySettings"
    }
  ];

  // The maximum number of items to return in a single page. By default 20 and
  // at most 100.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for [SecuritySettings.ListSecuritySettings][].
message ListSecuritySettingsResponse {
  // The list of security settings.
  repeated SecuritySettings security_settings = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for [SecuritySettings.CreateSecuritySettings][].
message CreateSecuritySettingsRequest {
  // Required. The location to create an
  // [SecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettings]
  // for. Format: `projects/<ProjectID>/locations/<LocationID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/SecuritySettings"
    }
  ];

  // Required. The security settings to create.
  SecuritySettings security_settings = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for [SecuritySettings.DeleteSecuritySettings][].
message DeleteSecuritySettingsRequest {
  // Required. The name of the
  // [SecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettings] to
  // delete. Format:
  // `projects/<ProjectID>/locations/<LocationID>/securitySettings/<SecuritySettingsID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/SecuritySettings"
    }
  ];
}

// Represents the settings related to security issues, such as data redaction
// and data retention. It may take hours for updates on the settings to
// propagate to all the related components and take effect.
message SecuritySettings {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/SecuritySettings"
    pattern: "projects/{project}/locations/{location}/securitySettings/{security_settings}"
  };

  // Defines how we redact data.
  enum RedactionStrategy {
    // Do not redact.
    REDACTION_STRATEGY_UNSPECIFIED = 0;

    // Call redaction service to clean up the data to be persisted.
    REDACT_WITH_SERVICE = 1;
  }

  // Defines what types of data to redact.
  enum RedactionScope {
    // Don't redact any kind of data.
    REDACTION_SCOPE_UNSPECIFIED = 0;

    // On data to be written to disk or similar devices that are capable of
    // holding data even if power is disconnected. This includes data that are
    // temporarily saved on disk.
    REDACT_DISK_STORAGE = 2;
  }

  // Defines how long we retain persisted data that contains sensitive info.
  enum RetentionStrategy {
    // Retains the persisted data with Dialogflow's internal default 365d TTLs.
    RETENTION_STRATEGY_UNSPECIFIED = 0;

    // Removes data when the conversation ends. If there is no
    // [Conversation][google.cloud.dialogflow.cx.v3beta1.Conversation]
    // explicitly established, a default conversation ends when the
    // corresponding Dialogflow session ends.
    REMOVE_AFTER_CONVERSATION = 1;
  }

  // Type of data we purge after retention settings triggers purge.
  enum PurgeDataType {
    // Unspecified. Do not use.
    PURGE_DATA_TYPE_UNSPECIFIED = 0;

    // Dialogflow history. This does not include Cloud logging, which is
    // owned by the user - not Dialogflow.
    DIALOGFLOW_HISTORY = 1;
  }

  // Settings for exporting audio.
  message AudioExportSettings {
    // File format for exported audio file. Currently only in telephony
    // recordings.
    enum AudioFormat {
      // Unspecified. Do not use.
      AUDIO_FORMAT_UNSPECIFIED = 0;

      // G.711 mu-law PCM with 8kHz sample rate.
      MULAW = 1;

      // MP3 file format.
      MP3 = 2;

      // OGG Vorbis.
      OGG = 3;
    }

    // Cloud Storage bucket to export audio record to.
    // Setting this field would grant the Storage Object Creator role to
    // the Dialogflow Service Agent.
    // API caller that tries to modify this field should have the permission of
    // storage.buckets.setIamPolicy.
    string gcs_bucket = 1;

    // Filename pattern for exported audio.
    string audio_export_pattern = 2;

    // Enable audio redaction if it is true.
    // Note that this only redacts end-user audio data;
    // Synthesised audio from the virtual agent is not redacted.
    bool enable_audio_redaction = 3;

    // File format for exported audio file. Currently only in telephony
    // recordings.
    AudioFormat audio_format = 4;

    // Whether to store TTS audio. By default, TTS audio from the virtual agent
    // is not exported.
    bool store_tts_audio = 6;
  }

  // Settings for exporting conversations to
  // [Insights](https://cloud.google.com/contact-center/insights/docs).
  message InsightsExportSettings {
    // If enabled, we will automatically exports
    // conversations to Insights and Insights runs its analyzers.
    bool enable_insights_export = 1;
  }

  // Resource name of the settings.
  // Required for the
  // [SecuritySettingsService.UpdateSecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettingsService.UpdateSecuritySettings]
  // method.
  // [SecuritySettingsService.CreateSecuritySettings][google.cloud.dialogflow.cx.v3beta1.SecuritySettingsService.CreateSecuritySettings]
  // populates the name automatically. Format:
  // `projects/<ProjectID>/locations/<LocationID>/securitySettings/<SecuritySettingsID>`.
  string name = 1;

  // Required. The human-readable name of the security settings, unique within
  // the location.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Strategy that defines how we do redaction.
  RedactionStrategy redaction_strategy = 3;

  // Defines the data for which Dialogflow applies redaction. Dialogflow does
  // not redact data that it does not have access to – for example, Cloud
  // logging.
  RedactionScope redaction_scope = 4;

  // [DLP](https://cloud.google.com/dlp/docs) inspect template name. Use this
  // template to define inspect base settings.
  //
  // The `DLP Inspect Templates Reader` role is needed on the Dialogflow
  // service identity service account (has the form
  // `<EMAIL>`)
  // for your agent's project.
  //
  // If empty, we use the default DLP inspect config.
  //
  // The template name will have one of the following formats:
  // `projects/<ProjectID>/locations/<LocationID>/inspectTemplates/<TemplateID>`
  // OR
  // `organizations/<OrganizationID>/locations/<LocationID>/inspectTemplates/<TemplateID>`
  //
  // Note: `inspect_template` must be located in the same region as the
  // `SecuritySettings`.
  string inspect_template = 9 [(google.api.resource_reference) = {
    type: "dlp.googleapis.com/InspectTemplate"
  }];

  // [DLP](https://cloud.google.com/dlp/docs) deidentify template name. Use this
  // template to define de-identification configuration for the content.
  //
  // The `DLP De-identify Templates Reader` role is needed on the Dialogflow
  // service identity service account (has the form
  // `<EMAIL>`)
  // for your agent's project.
  //
  // If empty, Dialogflow replaces sensitive info with `[redacted]` text.
  //
  // The template name will have one of the following formats:
  // `projects/<ProjectID>/locations/<LocationID>/deidentifyTemplates/<TemplateID>`
  // OR
  // `organizations/<OrganizationID>/locations/<LocationID>/deidentifyTemplates/<TemplateID>`
  //
  // Note: `deidentify_template` must be located in the same region as the
  // `SecuritySettings`.
  string deidentify_template = 17 [(google.api.resource_reference) = {
    type: "dlp.googleapis.com/DeidentifyTemplate"
  }];

  // Specifies how data is retained. Note that even if the data is
  // purged due to retention policy, we may still hold it in backup storage for
  // a few days without allowing direct readings.
  oneof data_retention {
    // Retains data in interaction logging for the specified number of days.
    // This does not apply to Cloud logging, which is owned by the user - not
    // Dialogflow.
    // User must set a value lower than Dialogflow's default 365d TTL (30 days
    // for Agent Assist traffic), higher value will be ignored and use default.
    // Setting a value higher than that has no effect. A missing value or
    // setting to 0 also means we use default TTL.
    // When data retention configuration is changed, it only applies to the data
    // created after the change; the TTL of existing data created before the
    // change stays intact.
    int32 retention_window_days = 6;

    // Specifies the retention behavior defined by
    // [SecuritySettings.RetentionStrategy][google.cloud.dialogflow.cx.v3beta1.SecuritySettings.RetentionStrategy].
    RetentionStrategy retention_strategy = 7;
  }

  // List of types of data to remove when retention settings triggers purge.
  repeated PurgeDataType purge_data_types = 8;

  // Controls audio export settings for post-conversation analytics when
  // ingesting audio to conversations via [Participants.AnalyzeContent][] or
  // [Participants.StreamingAnalyzeContent][].
  //
  // If
  // [retention_strategy][google.cloud.dialogflow.cx.v3beta1.SecuritySettings.retention_strategy]
  // is set to REMOVE_AFTER_CONVERSATION or [audio_export_settings.gcs_bucket][]
  // is empty, audio export is disabled.
  //
  // If audio export is enabled, audio is recorded and saved to
  // [audio_export_settings.gcs_bucket][], subject to retention policy of
  // [audio_export_settings.gcs_bucket][].
  //
  // This setting won't effect audio input for implicit sessions via
  // [Sessions.DetectIntent][google.cloud.dialogflow.cx.v3beta1.Sessions.DetectIntent]
  // or
  // [Sessions.StreamingDetectIntent][google.cloud.dialogflow.cx.v3beta1.Sessions.StreamingDetectIntent].
  AudioExportSettings audio_export_settings = 12;

  // Controls conversation exporting settings to Insights after conversation is
  // completed.
  //
  // If
  // [retention_strategy][google.cloud.dialogflow.cx.v3beta1.SecuritySettings.retention_strategy]
  // is set to REMOVE_AFTER_CONVERSATION, Insights export is disabled no matter
  // what you configure here.
  InsightsExportSettings insights_export_settings = 13;
}
