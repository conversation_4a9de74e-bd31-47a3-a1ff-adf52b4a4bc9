// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "SafetySettingsProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Settings for Generative Safety.
message SafetySettings {
  // Text input which can be used for prompt or banned phrases.
  message Phrase {
    // Required. Text input which can be used for prompt or banned phrases.
    string text = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Language code of the phrase.
    string language_code = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Strategy for matching phrases.
  enum PhraseMatchStrategy {
    // Unspecified, defaults to PARTIAL_MATCH.
    PHRASE_MATCH_STRATEGY_UNSPECIFIED = 0;

    // Text that contains the phrase as a substring will be matched, e.g. "foo"
    // will match "afoobar".
    PARTIAL_MATCH = 1;

    // Text that contains the tokenized words of the phrase will be matched,
    // e.g. "foo" will match "a foo bar" and "foo bar", but not "foobar".
    WORD_MATCH = 2;
  }

  // Optional. Default phrase match strategy for banned phrases.
  PhraseMatchStrategy default_banned_phrase_match_strategy = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Banned phrases for generated text.
  repeated Phrase banned_phrases = 1;
}
