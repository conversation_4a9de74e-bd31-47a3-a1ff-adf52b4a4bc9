// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "DataStoreConnectionProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// A data store connection. It represents a data store in Discovery Engine and
// the type of the contents it contains.
message DataStoreConnection {
  // The type of the connected data store.
  DataStoreType data_store_type = 1;

  // The full name of the referenced data store.
  // Formats:
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`
  // `projects/{project}/locations/{location}/dataStores/{data_store}`
  string data_store = 2;
}

// Type of a data store.
// Determines how search is performed in the data store.
enum DataStoreType {
  // Not specified. This value indicates that the data store type is not
  // specified, so it will not be used during search.
  DATA_STORE_TYPE_UNSPECIFIED = 0;

  // A data store that contains public web content.
  PUBLIC_WEB = 1;

  // A data store that contains unstructured private data.
  UNSTRUCTURED = 2;

  // A data store that contains structured data (for example FAQ).
  STRUCTURED = 3;
}

// Data store connection feature output signals.
// Might be only partially field if processing stop before the final answer.
// Reasons for this can be, but are not limited to: empty UCS search results,
// positive RAI check outcome, grounding failure, ...
message DataStoreConnectionSignals {
  // Diagnostic info related to the rewriter model call.
  message RewriterModelCallSignals {
    // Prompt as sent to the model.
    string rendered_prompt = 1;

    // Output of the generative model.
    string model_output = 2;

    // Name of the generative model. For example, "gemini-ultra", "gemini-pro",
    // "gemini-1.5-flash" etc. Defaults to "Other" if the model is unknown.
    string model = 3;
  }

  // Search snippet details.
  message SearchSnippet {
    // Title of the enclosing document.
    string document_title = 1;

    // Uri for the document. Present if specified for the document.
    string document_uri = 2;

    // Text included in the prompt.
    string text = 3;
  }

  // Diagnostic info related to the answer generation model call.
  message AnswerGenerationModelCallSignals {
    // Prompt as sent to the model.
    string rendered_prompt = 1;

    // Output of the generative model.
    string model_output = 2;

    // Name of the generative model. For example, "gemini-ultra", "gemini-pro",
    // "gemini-1.5-flash" etc. Defaults to "Other" if the model is unknown.
    string model = 3;
  }

  // Answer part with citation.
  message AnswerPart {
    // Substring of the answer.
    string text = 1;

    // Citations for this answer part. Indices of `search_snippets`.
    repeated int32 supporting_indices = 2;
  }

  // Snippet cited by the answer generation model.
  message CitedSnippet {
    // Details of the snippet.
    SearchSnippet search_snippet = 1;

    // Index of the snippet in `search_snippets` field.
    int32 snippet_index = 2;
  }

  // Grounding signals.
  message GroundingSignals {
    // Represents the decision of the grounding check.
    enum GroundingDecision {
      // Decision not specified.
      GROUNDING_DECISION_UNSPECIFIED = 0;

      // Grounding have accepted the answer.
      ACCEPTED_BY_GROUNDING = 1;

      // Grounding have rejected the answer.
      REJECTED_BY_GROUNDING = 2;
    }

    // Grounding score buckets.
    enum GroundingScoreBucket {
      // Score not specified.
      GROUNDING_SCORE_BUCKET_UNSPECIFIED = 0;

      // We have very low confidence that the answer is grounded.
      VERY_LOW = 1;

      // We have low confidence that the answer is grounded.
      LOW = 3;

      // We have medium confidence that the answer is grounded.
      MEDIUM = 4;

      // We have high confidence that the answer is grounded.
      HIGH = 5;

      // We have very high confidence that the answer is grounded.
      VERY_HIGH = 6;
    }

    // Represents the decision of the grounding check.
    GroundingDecision decision = 1;

    // Grounding score bucket setting.
    GroundingScoreBucket score = 2;
  }

  // Safety check results.
  message SafetySignals {
    // Safety decision.
    // All kinds of check are incorporated into this final decision, including
    // banned phrases check.
    enum SafetyDecision {
      // Decision not specified.
      SAFETY_DECISION_UNSPECIFIED = 0;

      // No manual or automatic safety check fired.
      ACCEPTED_BY_SAFETY_CHECK = 1;

      // One ore more safety checks fired.
      REJECTED_BY_SAFETY_CHECK = 2;
    }

    // Specifies banned phrase match subject.
    enum BannedPhraseMatch {
      // No banned phrase check was executed.
      BANNED_PHRASE_MATCH_UNSPECIFIED = 0;

      // All banned phrase checks led to no match.
      BANNED_PHRASE_MATCH_NONE = 1;

      // A banned phrase matched the query.
      BANNED_PHRASE_MATCH_QUERY = 2;

      // A banned phrase matched the response.
      BANNED_PHRASE_MATCH_RESPONSE = 3;
    }

    // Safety decision.
    SafetyDecision decision = 1;

    // Specifies banned phrase match subject.
    BannedPhraseMatch banned_phrase_match = 2;

    // The matched banned phrase if there was a match.
    string matched_banned_phrase = 3;
  }

  // Optional. Diagnostic info related to the rewriter model call.
  RewriterModelCallSignals rewriter_model_call_signals = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Rewritten string query used for search.
  string rewritten_query = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Search snippets included in the answer generation prompt.
  repeated SearchSnippet search_snippets = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Diagnostic info related to the answer generation model call.
  AnswerGenerationModelCallSignals answer_generation_model_call_signals = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The final compiled answer.
  string answer = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Answer parts with relevant citations.
  // Concatenation of texts should add up the `answer` (not counting
  // whitespaces).
  repeated AnswerPart answer_parts = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Snippets cited by the answer generation model from the most to
  // least relevant.
  repeated CitedSnippet cited_snippets = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Grounding signals.
  GroundingSignals grounding_signals = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Safety check result.
  SafetySignals safety_signals = 9 [(google.api.field_behavior) = OPTIONAL];
}
