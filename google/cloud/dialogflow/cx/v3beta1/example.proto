// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "ExampleProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";

// Service for managing [Examples][google.cloud.dialogflow.cx.v3beta1.Example].
service Examples {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Creates an example in the specified playbook.
  rpc CreateExample(CreateExampleRequest) returns (Example) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*/playbooks/*}/examples"
      body: "example"
    };
    option (google.api.method_signature) = "parent,example";
  }

  // Deletes the specified example.
  rpc DeleteExample(DeleteExampleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/agents/*/playbooks/*/examples/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of examples in the specified playbook.
  rpc ListExamples(ListExamplesRequest) returns (ListExamplesResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*/playbooks/*}/examples"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified example.
  rpc GetExample(GetExampleRequest) returns (Example) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/playbooks/*/examples/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update the specified example.
  rpc UpdateExample(UpdateExampleRequest) returns (Example) {
    option (google.api.http) = {
      patch: "/v3beta1/{example.name=projects/*/locations/*/agents/*/playbooks/*/examples/*}"
      body: "example"
    };
    option (google.api.method_signature) = "example,update_mask";
  }
}

// The request message for
// [Examples.CreateExample][google.cloud.dialogflow.cx.v3beta1.Examples.CreateExample].
message CreateExampleRequest {
  // Required. The playbook to create an example for.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Example"
    }
  ];

  // Required. The example to create.
  Example example = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Examples.DeleteExample][google.cloud.dialogflow.cx.v3beta1.Examples.DeleteExample].
message DeleteExampleRequest {
  // Required. The name of the example to delete.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>/examples/<ExampleID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Example"
    }
  ];
}

// The request message for
// [Examples.ListExamples][google.cloud.dialogflow.cx.v3beta1.Examples.ListExamples].
message ListExamplesRequest {
  // Required. The playbook to list the examples from.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Example"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The [next_page_token][ListExampleResponse.next_page_token] value
  // returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The language to list examples for.
  // If not specified, list all examples under the playbook.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Examples.ListExamples][google.cloud.dialogflow.cx.v3beta1.Examples.ListExamples].
message ListExamplesResponse {
  // The list of examples. There will be a maximum number of items returned
  // based on the
  // [page_size][google.cloud.dialogflow.cx.v3beta1.ListExamplesRequest.page_size]
  // field in the request.
  repeated Example examples = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Examples.GetExample][google.cloud.dialogflow.cx.v3beta1.Examples.GetExample].
message GetExampleRequest {
  // Required. The name of the example.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>/examples/<ExampleID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Example"
    }
  ];
}

// The request message for
// [Examples.UpdateExample][google.cloud.dialogflow.cx.v3beta1.Examples.UpdateExample].
message UpdateExampleRequest {
  // Required. The example to update.
  Example example = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The mask to control which fields get updated. If the mask is not
  // present, all fields will be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Example represents a sample execution of the playbook in the conversation.
//
// An example consists of a list of ordered actions performed by end user
// or Dialogflow agent according the playbook instructions to fulfill the task.
message Example {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Example"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/playbooks/{playbook}/examples/{example}"
    plural: "examples"
    singular: "example"
  };

  // The unique identifier of the playbook example.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>/examples/<ExampleID>`.
  string name = 1;

  // Optional. The input to the playbook in the example.
  PlaybookInput playbook_input = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The output of the playbook in the example.
  PlaybookOutput playbook_output = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The ordered list of actions performed by the end user and the
  // Dialogflow agent.
  repeated Action actions = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The display name of the example.
  string display_name = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. The high level concise description of the example. The max number
  // of characters is 200.
  string description = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Estimated number of tokes current example takes when sent to
  // the LLM.
  int64 token_count = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of initial example creation.
  google.protobuf.Timestamp create_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last time the example was updated.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Example's output state.
  OutputState conversation_state = 12 [(google.api.field_behavior) = REQUIRED];

  // Optional. The language code of the example.
  // If not specified, the agent's default language is used.
  // Note: languages must be enabled in the agent before they can be used.
  // Note: example's language code is not currently used in dialogflow agents.
  string language_code = 13 [(google.api.field_behavior) = OPTIONAL];
}

// Input of the playbook.
message PlaybookInput {
  // Optional. Summary string of the preceding conversation for the child
  // playbook invocation.
  string preceding_conversation_summary = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of input parameters for the action.
  google.protobuf.Struct action_parameters = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Output of the playbook.
message PlaybookOutput {
  // Optional. Summary string of the execution result of the child playbook.
  string execution_summary = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A Struct object of output parameters for the action.
  google.protobuf.Struct action_parameters = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Action performed by end user or Dialogflow agent in the conversation.
message Action {
  // Action details.
  oneof action {
    // Optional. Agent obtained a message from the customer.
    UserUtterance user_utterance = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Action performed by the agent as a message.
    AgentUtterance agent_utterance = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Action performed on behalf of the agent by calling a plugin
    // tool.
    ToolUse tool_use = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Action performed on behalf of the agent by invoking a child
    // playbook.
    PlaybookInvocation playbook_invocation = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Action performed on behalf of the agent by invoking a CX flow.
    FlowInvocation flow_invocation = 5 [(google.api.field_behavior) = OPTIONAL];
  }
}

// UserUtterance represents one message sent by the customer.
message UserUtterance {
  // Required. Message content in text.
  string text = 1 [(google.api.field_behavior) = REQUIRED];
}

// AgentUtterance represents one message sent by the agent.
message AgentUtterance {
  // Required. Message content in text.
  string text = 1 [(google.api.field_behavior) = REQUIRED];
}

// Stores metadata of the invocation of an action supported by a tool.
message ToolUse {
  // Required. The [tool][google.cloud.dialogflow.cx.v3beta1.Tool] that should
  // be used. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/tools/<ToolID>`.
  string tool = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Tool" }
  ];

  // Optional. Name of the action to be called during the tool use.
  string action = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of input parameters for the action.
  google.protobuf.Struct input_action_parameters = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of output parameters generated by the action.
  google.protobuf.Struct output_action_parameters = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Stores metadata of the invocation of a child playbook.
message PlaybookInvocation {
  // Required. The unique identifier of the playbook.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>`.
  string playbook = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Playbook"
    }
  ];

  // Optional. Input of the child playbook invocation.
  PlaybookInput playbook_input = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output of the child playbook invocation.
  PlaybookOutput playbook_output = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Playbook invocation's output state.
  OutputState playbook_state = 4 [(google.api.field_behavior) = REQUIRED];
}

// Stores metadata of the invocation of a CX flow.
message FlowInvocation {
  // Required. The unique identifier of the flow.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string flow = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];

  // Optional. A list of input parameters for the flow.
  google.protobuf.Struct input_action_parameters = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of output parameters generated by the flow invocation.
  google.protobuf.Struct output_action_parameters = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Flow invocation's output state.
  OutputState flow_state = 4 [(google.api.field_behavior) = REQUIRED];
}

// Output state.
enum OutputState {
  // Unspecified output.
  OUTPUT_STATE_UNSPECIFIED = 0;

  // Succeeded.
  OUTPUT_STATE_OK = 1;

  // Cancelled.
  OUTPUT_STATE_CANCELLED = 2;

  // Failed.
  OUTPUT_STATE_FAILED = 3;

  // Escalated.
  OUTPUT_STATE_ESCALATED = 4;

  // Pending.
  OUTPUT_STATE_PENDING = 5;
}
