// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "ChangelogProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Service for managing
// [Changelogs][google.cloud.dialogflow.cx.v3beta1.Changelog].
service Changelogs {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of Changelogs.
  rpc ListChangelogs(ListChangelogsRequest) returns (ListChangelogsResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*}/changelogs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified Changelog.
  rpc GetChangelog(GetChangelogRequest) returns (Changelog) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/changelogs/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// The request message for
// [Changelogs.ListChangelogs][google.cloud.dialogflow.cx.v3beta1.Changelogs.ListChangelogs].
message ListChangelogsRequest {
  // Required. The agent containing the changelogs.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Changelog"
    }
  ];

  // The filter string. Supports filter by user_email, resource, type and
  // create_time. Some examples:
  //   1. By user email:
  //        user_email = "<EMAIL>"
  //   2. By resource name:
  //        resource = "projects/123/locations/global/agents/456/flows/789"
  //   3. By resource display name:
  //        display_name = "my agent"
  //   4. By action:
  //        action = "Create"
  //   5. By type:
  //        type = "flows"
  //   6. By create time. Currently predicates on `create_time` and
  //      `create_time_epoch_seconds` are supported:
  //        create_time_epoch_seconds > 1551790877 AND create_time <=
  //        2017-01-15T01:30:15.01Z
  //   7. Combination of above filters:
  //        resource = "projects/123/locations/global/agents/456/flows/789"
  //          AND user_email = "<EMAIL>"
  //          AND create_time <= 2017-01-15T01:30:15.01Z
  string filter = 2;

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 3;

  // The next_page_token value returned from a previous list request.
  string page_token = 4;
}

// The response message for
// [Changelogs.ListChangelogs][google.cloud.dialogflow.cx.v3beta1.Changelogs.ListChangelogs].
message ListChangelogsResponse {
  // The list of changelogs. There will be a maximum number of items returned
  // based on the page_size field in the request. The changelogs will be ordered
  // by timestamp.
  repeated Changelog changelogs = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Changelogs.GetChangelog][google.cloud.dialogflow.cx.v3beta1.Changelogs.GetChangelog].
message GetChangelogRequest {
  // Required. The name of the changelog to get.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/changelogs/<ChangelogID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Changelog"
    }
  ];
}

// Changelogs represents a change made to a given agent.
message Changelog {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Changelog"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/changelogs/{changelog}"
  };

  // The unique identifier of the changelog.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/changelogs/<ChangelogID>`.
  string name = 1;

  // Email address of the authenticated user.
  string user_email = 2;

  // The affected resource display name of the change.
  string display_name = 7;

  // The action of the change.
  string action = 11;

  // The affected resource type.
  string type = 8;

  // The affected resource name of the change.
  string resource = 3;

  // The timestamp of the change.
  google.protobuf.Timestamp create_time = 4;

  // The affected language code of the change.
  string language_code = 14;
}
