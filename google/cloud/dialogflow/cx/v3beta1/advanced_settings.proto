// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/field_behavior.proto";
import "google/cloud/dialogflow/cx/v3beta1/gcs.proto";
import "google/protobuf/duration.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "AdvancedSettingsProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Hierarchical advanced settings for agent/flow/page/fulfillment/parameter.
// Settings exposed at lower level overrides the settings exposed at higher
// level. Overriding occurs at the sub-setting level. For example, the
// playback_interruption_settings at fulfillment level only overrides the
// playback_interruption_settings at the agent level, leaving other settings
// at the agent level unchanged.
//
// DTMF settings does not override each other. DTMF settings set at different
// levels define DTMF detections running in parallel.
//
// Hierarchy: Agent->Flow->Page->Fulfillment/Parameter.
message AdvancedSettings {
  // Define behaviors of speech to text detection.
  message SpeechSettings {
    // Sensitivity of the speech model that detects the end of speech.
    // Scale from 0 to 100.
    int32 endpointer_sensitivity = 1;

    // Timeout before detecting no speech.
    google.protobuf.Duration no_speech_timeout = 2;

    // Use timeout based endpointing, interpreting endpointer sensitivy as
    // seconds of timeout value.
    bool use_timeout_based_endpointing = 3;

    // Mapping from language to Speech-to-Text model. The mapped Speech-to-Text
    // model will be selected for requests from its corresponding language.
    // For more information, see
    // [Speech
    // models](https://cloud.google.com/dialogflow/cx/docs/concept/speech-models).
    map<string, string> models = 5;
  }

  // Define behaviors for DTMF (dual tone multi frequency).
  message DtmfSettings {
    // If true, incoming audio is processed for DTMF (dual tone multi frequency)
    // events. For example, if the caller presses a button on their telephone
    // keypad and DTMF processing is enabled, Dialogflow will detect the
    // event (e.g. a "3" was pressed) in the incoming audio and pass the event
    // to the bot to drive business logic (e.g. when 3 is pressed, return the
    // account balance).
    bool enabled = 1;

    // Max length of DTMF digits.
    int32 max_digits = 2;

    // The digit that terminates a DTMF digit sequence.
    string finish_digit = 3;

    // Interdigit timeout setting for matching dtmf input to regex.
    google.protobuf.Duration interdigit_timeout_duration = 6;

    // Endpoint timeout setting for matching dtmf input to regex.
    google.protobuf.Duration endpointing_timeout_duration = 7;
  }

  // Define behaviors on logging.
  message LoggingSettings {
    // Enables Google Cloud Logging.
    bool enable_stackdriver_logging = 2;

    // Enables DF Interaction logging.
    bool enable_interaction_logging = 3;

    // Enables consent-based end-user input redaction, if true, a pre-defined
    // session parameter `$session.params.conversation-redaction` will be
    // used to determine if the utterance should be redacted.
    bool enable_consent_based_redaction = 4;
  }

  // If present, incoming audio is exported by Dialogflow to the configured
  // Google Cloud Storage destination.
  // Exposed at the following levels:
  // - Agent level
  // - Flow level
  GcsDestination audio_export_gcs_destination = 2;

  // Settings for speech to text detection.
  // Exposed at the following levels:
  // - Agent level
  // - Flow level
  // - Page level
  // - Parameter level
  SpeechSettings speech_settings = 3;

  // Settings for DTMF.
  // Exposed at the following levels:
  // - Agent level
  // - Flow level
  // - Page level
  // - Parameter level.
  DtmfSettings dtmf_settings = 5;

  // Settings for logging.
  // Settings for Dialogflow History, Contact Center messages, StackDriver logs,
  // and speech logging.
  // Exposed at the following levels:
  // - Agent level.
  LoggingSettings logging_settings = 6;
}
