// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3beta1/data_store_connection.proto";
import "google/cloud/dialogflow/cx/v3beta1/inline.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "ToolProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";

// Service for managing [Tools][google.cloud.dialogflow.cx.v3beta1.Tool].
service Tools {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Creates a [Tool][google.cloud.dialogflow.cx.v3beta1.Tool] in the specified
  // agent.
  rpc CreateTool(CreateToolRequest) returns (Tool) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*}/tools"
      body: "tool"
    };
    option (google.api.method_signature) = "parent,tool";
  }

  // Returns a list of [Tools][google.cloud.dialogflow.cx.v3beta1.Tool] in the
  // specified agent.
  rpc ListTools(ListToolsRequest) returns (ListToolsResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*}/tools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Exports the selected tools.
  rpc ExportTools(ExportToolsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*}/tools:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportToolsResponse"
      metadata_type: "ExportToolsMetadata"
    };
  }

  // Retrieves the specified [Tool][google.cloud.dialogflow.cx.v3beta1.Tool].
  rpc GetTool(GetToolRequest) returns (Tool) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/tools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update the specified [Tool][google.cloud.dialogflow.cx.v3beta1.Tool].
  rpc UpdateTool(UpdateToolRequest) returns (Tool) {
    option (google.api.http) = {
      patch: "/v3beta1/{tool.name=projects/*/locations/*/agents/*/tools/*}"
      body: "tool"
    };
    option (google.api.method_signature) = "tool,update_mask";
  }

  // Deletes a specified [Tool][google.cloud.dialogflow.cx.v3beta1.Tool].
  rpc DeleteTool(DeleteToolRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/agents/*/tools/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// The request message for
// [Tools.CreateTool][google.cloud.dialogflow.cx.v3beta1.Tools.CreateTool].
message CreateToolRequest {
  // Required. The agent to create a Tool for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Tool"
    }
  ];

  // Required. The Tool to be created.
  Tool tool = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Tools.ListTools][google.cloud.dialogflow.cx.v3beta1.Tools.ListTools].
message ListToolsRequest {
  // Required. The agent to list the Tools from.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Tool"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Tools.ListTools][google.cloud.dialogflow.cx.v3beta1.Tools.ListTools].
message ListToolsResponse {
  // The list of Tools. There will be a maximum number of items returned
  // based on the page_size field in the request.
  repeated Tool tools = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Tools.GetTool][google.cloud.dialogflow.cx.v3beta1.Tools.GetTool].
message GetToolRequest {
  // Required. The name of the Tool.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/tools/<ToolID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Tool" }
  ];
}

// The request message for
// [Tools.ExportTools][google.cloud.dialogflow.cx.v3beta1.Tools.ExportTools].
message ExportToolsRequest {
  // Data format of the exported tools.
  enum DataFormat {
    // Unspecified format. Treated as `BLOB`.
    DATA_FORMAT_UNSPECIFIED = 0;

    // Tools will be exported as raw bytes.
    BLOB = 1;

    // Tools will be exported in JSON format.
    JSON = 2;
  }

  // Required. The agent to export tools from.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Tool"
    }
  ];

  // Required. The name of the tools to export.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/tools/<ToolID>`.
  repeated string tools = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Tool" }
  ];

  // The destination to export.
  oneof destination {
    // Optional. The [Google Cloud
    // Storage](https://cloud.google.com/storage/docs/) URI to export the tools
    // to. The format of this URI must be `gs://<bucket-name>/<object-name>`.
    //
    // Dialogflow performs a write operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have write permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string tools_uri = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The option to return the serialized tools inline.
    bool tools_content_inline = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The data format of the exported tools. If not specified, `BLOB`
  // is assumed.
  DataFormat data_format = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Tools.ExportTools][google.cloud.dialogflow.cx.v3beta1.Tools.ExportTools].
message ExportToolsResponse {
  // The exported tools.
  oneof tools {
    // The URI to a file containing the exported tools. This field is
    // populated only if `tools_uri` is specified in
    // [ExportToolsRequest][google.cloud.dialogflow.cx.v3beta1.ExportToolsRequest].
    string tools_uri = 1;

    // Uncompressed byte content for tools. This field is populated only if
    // `tools_content_inline` is set to true in
    // [ExportToolsRequest][google.cloud.dialogflow.cx.v3beta1.ExportToolsRequest].
    InlineDestination tools_content = 2;
  }
}

// The request message for
// [Tools.UpdateTool][google.cloud.dialogflow.cx.v3beta1.Tools.UpdateTool].
message UpdateToolRequest {
  // Required. The Tool to be updated.
  Tool tool = 1 [(google.api.field_behavior) = REQUIRED];

  // The mask to control which fields get updated. If the mask is not present,
  // all fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request message for
// [Tools.DeleteTool][google.cloud.dialogflow.cx.v3beta1.Tools.DeleteTool].
message DeleteToolRequest {
  // Required. The name of the Tool to be deleted.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/tools/<ToolID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Tool" }
  ];

  // This field has no effect for Tools not being used.
  // For Tools that are used:
  //
  // *  If `force` is set to false, an error will be returned with message
  //    indicating the referenced resources.
  // *  If `force` is set to true, Dialogflow will remove the tool, as well
  //    as any references to the tool.
  bool force = 2;
}

// A tool provides a list of actions which are available to the
// [Playbook][google.cloud.dialogflow.cx.v3beta1.Playbook] to attain its goal. A
// Tool consists of a description of the tool's usage and a specification of the
// tool which contains the schema and authentication information.
message Tool {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Tool"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/tools/{tool}"
  };

  // An OpenAPI tool is a way to provide the Tool specifications in the Open API
  // schema format.
  message OpenApiTool {
    // Schema representation.
    oneof schema {
      // Required. The OpenAPI schema specified as a text.
      string text_schema = 1 [(google.api.field_behavior) = REQUIRED];
    }

    // Optional. Authentication information required by the API.
    Authentication authentication = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. TLS configuration for the HTTPS verification.
    TLSConfig tls_config = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Service Directory configuration.
    ServiceDirectoryConfig service_directory_config = 4
        [(google.api.field_behavior) = OPTIONAL];
  }

  // A DataStoreTool is a way to provide specifications needed to search a
  // list of data stores.
  message DataStoreTool {
    // A FallbackPrompt is a way to provide specifications for the Data Store
    // fallback prompt when generating responses.
    message FallbackPrompt {}

    // Required. List of data stores to search.
    repeated DataStoreConnection data_store_connections = 1
        [(google.api.field_behavior) = REQUIRED];

    // Required. Fallback prompt configurations to use.
    FallbackPrompt fallback_prompt = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // An ExtensionTool is a way to use Vertex Extensions as a tool.
  message ExtensionTool {
    // Required. The full name of the referenced vertex extension.
    // Formats:
    // `projects/{project}/locations/{location}/extensions/{extension}`
    string name = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // A Function tool describes the functions to be invoked on the client side.
  message FunctionTool {
    // Optional. The JSON schema is encapsulated in a
    // [google.protobuf.Struct][google.protobuf.Struct] to describe the input of
    // the function. This input is a JSON object that contains the function's
    // parameters as properties of the object.
    google.protobuf.Struct input_schema = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The JSON schema is encapsulated in a
    // [google.protobuf.Struct][google.protobuf.Struct] to describe the output
    // of the function. This output is a JSON object that contains the
    // function's parameters as properties of the object.
    google.protobuf.Struct output_schema = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Authentication information required for API calls
  message Authentication {
    // Config for authentication with API key.
    message ApiKeyConfig {
      // Required. The parameter name or the header name of the API key.
      // E.g., If the API request is "https://example.com/act?X-Api-Key=<API
      // KEY>", "X-Api-Key" would be the parameter name.
      string key_name = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. The API key.
      string api_key = 2 [(google.api.field_behavior) = REQUIRED];

      // Required. Key location in the request.
      RequestLocation request_location = 3
          [(google.api.field_behavior) = REQUIRED];
    }

    // The location of the API key in the request.
    enum RequestLocation {
      // Default value. This value is unused.
      REQUEST_LOCATION_UNSPECIFIED = 0;

      // Represents the key in http header.
      HEADER = 1;

      // Represents the key in query string.
      QUERY_STRING = 2;
    }

    // Config for authentication with OAuth.
    message OAuthConfig {
      // OAuth grant types. Only [client credential
      // grant](https://oauth.net/2/grant-types/client-credentials) is
      // supported.
      enum OauthGrantType {
        // Default value. This value is unused.
        OAUTH_GRANT_TYPE_UNSPECIFIED = 0;

        // Represents the [client credential
        // flow](https://oauth.net/2/grant-types/client-credentials).
        CLIENT_CREDENTIAL = 1;
      }

      // Required. OAuth grant types.
      OauthGrantType oauth_grant_type = 1
          [(google.api.field_behavior) = REQUIRED];

      // Required. The client ID from the OAuth provider.
      string client_id = 2 [(google.api.field_behavior) = REQUIRED];

      // Required. The client secret from the OAuth provider.
      string client_secret = 3 [(google.api.field_behavior) = REQUIRED];

      // Required. The token endpoint in the OAuth provider to exchange for an
      // access token.
      string token_endpoint = 4 [(google.api.field_behavior) = REQUIRED];

      // Optional. The OAuth scopes to grant.
      repeated string scopes = 5 [(google.api.field_behavior) = OPTIONAL];
    }

    // Config for auth using [Diglogflow service
    // agent](https://cloud.google.com/iam/docs/service-agents#dialogflow-service-agent).
    message ServiceAgentAuthConfig {
      // Indicate the auth token type generated from the [Diglogflow service
      // agent](https://cloud.google.com/iam/docs/service-agents#dialogflow-service-agent).
      enum ServiceAgentAuth {
        // Service agent auth type unspecified. Default to ID_TOKEN.
        SERVICE_AGENT_AUTH_UNSPECIFIED = 0;

        // Use [ID
        // token](https://cloud.google.com/docs/authentication/token-types#id)
        // generated from service agent. This can be used to access Cloud
        // Function and Cloud Run after you grant Invoker role to
        // `service-<PROJECT-NUMBER>@gcp-sa-dialogflow.iam.gserviceaccount.com`.
        ID_TOKEN = 1;

        // Use [access
        // token](https://cloud.google.com/docs/authentication/token-types#access)
        // generated from service agent. This can be used to access other Google
        // Cloud APIs after you grant required roles to
        // `service-<PROJECT-NUMBER>@gcp-sa-dialogflow.iam.gserviceaccount.com`.
        ACCESS_TOKEN = 2;
      }

      // Optional. Indicate the auth token type generated from the [Diglogflow
      // service
      // agent](https://cloud.google.com/iam/docs/service-agents#dialogflow-service-agent).
      // The generated token is sent in the Authorization header.
      ServiceAgentAuth service_agent_auth = 1
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Config for authentication using bearer token.
    message BearerTokenConfig {
      // Required. The text token appended to the text `Bearer` to the request
      // Authorization header.
      // [Session parameters
      // reference](https://cloud.google.com/dialogflow/cx/docs/concept/parameter#session-ref)
      // can be used to pass the token dynamically, e.g.
      // `$session.params.parameter-id`.
      string token = 1 [(google.api.field_behavior) = REQUIRED];
    }

    // The auth configuration.
    oneof auth_config {
      // Config for API key auth.
      ApiKeyConfig api_key_config = 1;

      // Config for OAuth.
      OAuthConfig oauth_config = 2;

      // Config for [Diglogflow service
      // agent](https://cloud.google.com/iam/docs/service-agents#dialogflow-service-agent)
      // auth.
      ServiceAgentAuthConfig service_agent_auth_config = 3;

      // Config for bearer token auth.
      BearerTokenConfig bearer_token_config = 4;
    }
  }

  // The TLS configuration.
  message TLSConfig {
    // The CA certificate.
    message CACert {
      // Required. The name of the allowed custom CA certificates. This
      // can be used to disambiguate the custom CA certificates.
      string display_name = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. The allowed custom CA certificates (in DER format) for
      // HTTPS verification. This overrides the default SSL trust store. If this
      // is empty or unspecified, Dialogflow will use Google's default trust
      // store to verify certificates. N.B. Make sure the HTTPS server
      // certificates are signed with "subject alt name". For instance a
      // certificate can be self-signed using the following command,
      //    openssl x509 -req -days 200 -in example.com.csr \
      //      -signkey example.com.key \
      //      -out example.com.crt \
      //      -extfile <(printf "\nsubjectAltName='DNS:www.example.com'")
      bytes cert = 2 [(google.api.field_behavior) = REQUIRED];
    }

    // Required. Specifies a list of allowed custom CA certificates for HTTPS
    // verification.
    repeated CACert ca_certs = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Configuration for tools using Service Directory.
  message ServiceDirectoryConfig {
    // Required. The name of [Service
    // Directory](https://cloud.google.com/service-directory) service.
    // Format:
    // `projects/<ProjectID>/locations/<LocationID>/namespaces/<NamespaceID>/services/<ServiceID>`.
    // `LocationID` of the service directory must be the same as the location
    // of the agent.
    string service = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "servicedirectory.googleapis.com/Service"
      }
    ];
  }

  // Represents the type of the tool.
  enum ToolType {
    // Default value. This value is unused.
    TOOL_TYPE_UNSPECIFIED = 0;

    // Customer provided tool.
    CUSTOMIZED_TOOL = 1;

    // First party built-in tool created by Dialogflow which cannot be modified.
    BUILTIN_TOOL = 2;
  }

  // The unique identifier of the Tool.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/tools/<ToolID>`.
  string name = 1;

  // Required. The human-readable name of the Tool, unique within an agent.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. High level description of the Tool and its usage.
  string description = 3 [(google.api.field_behavior) = REQUIRED];

  // Specification of the Tool.
  oneof specification {
    // OpenAPI specification of the Tool.
    OpenApiTool open_api_spec = 4;

    // Data store search tool specification.
    DataStoreTool data_store_spec = 8;

    // Vertex extension tool specification.
    ExtensionTool extension_spec = 11;

    // Client side executed function specification.
    FunctionTool function_spec = 13;
  }

  // Output only. The tool type.
  ToolType tool_type = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata returned for the
// [Tools.ExportTools][google.cloud.dialogflow.cx.v3beta1.Tools.ExportTools]
// long running operation.
message ExportToolsMetadata {}
