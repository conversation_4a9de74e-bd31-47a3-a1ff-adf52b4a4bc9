// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "ToolCallProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Represents a call of a specific tool's action with the specified inputs.
message ToolCall {
  // The [tool][Tool] associated with this call.
  // Format: `projects/<Project ID>/locations/<Location ID>/agents/<Agent
  // ID>/tools/<Tool ID>`.
  string tool = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Tool" }
  ];
  // The name of the tool's action associated with this call.
  string action = 2 [(google.api.field_behavior) = REQUIRED];
  // The action's input parameters.
  google.protobuf.Struct input_parameters = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// The result of calling a tool's action that has been executed by the client.
message ToolCallResult {
  // The [tool][Tool] associated with this call.
  // Format: `projects/<Project ID>/locations/<Location ID>/agents/<Agent
  // ID>/tools/<Tool ID>`.
  string tool = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Tool" }
  ];
  // The name of the tool's action associated with this call.
  string action = 2 [(google.api.field_behavior) = REQUIRED];
  // An error produced by the tool call.
  message Error {
    // The error message of the function.
    string message = 1 [(google.api.field_behavior) = OPTIONAL];
  }
  // The tool call's result.
  oneof result {
    // The tool call's error.
    Error error = 3;
    // The tool call's output parameters.
    google.protobuf.Struct output_parameters = 4;
  }
}
