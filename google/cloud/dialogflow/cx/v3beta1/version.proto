// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3beta1/flow.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "VersionProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Service for managing [Versions][google.cloud.dialogflow.cx.v3beta1.Version].
service Versions {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all versions in the specified
  // [Flow][google.cloud.dialogflow.cx.v3beta1.Flow].
  rpc ListVersions(ListVersionsRequest) returns (ListVersionsResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*/flows/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified
  // [Version][google.cloud.dialogflow.cx.v3beta1.Version].
  rpc GetVersion(GetVersionRequest) returns (Version) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/flows/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a [Version][google.cloud.dialogflow.cx.v3beta1.Version] in the
  // specified [Flow][google.cloud.dialogflow.cx.v3beta1.Flow].
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [CreateVersionOperationMetadata][google.cloud.dialogflow.cx.v3beta1.CreateVersionOperationMetadata]
  // - `response`: [Version][google.cloud.dialogflow.cx.v3beta1.Version]
  rpc CreateVersion(CreateVersionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*/flows/*}/versions"
      body: "version"
    };
    option (google.api.method_signature) = "parent,version";
    option (google.longrunning.operation_info) = {
      response_type: "Version"
      metadata_type: "CreateVersionOperationMetadata"
    };
  }

  // Updates the specified
  // [Version][google.cloud.dialogflow.cx.v3beta1.Version].
  rpc UpdateVersion(UpdateVersionRequest) returns (Version) {
    option (google.api.http) = {
      patch: "/v3beta1/{version.name=projects/*/locations/*/agents/*/flows/*/versions/*}"
      body: "version"
    };
    option (google.api.method_signature) = "version,update_mask";
  }

  // Deletes the specified
  // [Version][google.cloud.dialogflow.cx.v3beta1.Version].
  rpc DeleteVersion(DeleteVersionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/agents/*/flows/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Loads resources in the specified version to the draft flow.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  rpc LoadVersion(LoadVersionRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3beta1/{name=projects/*/locations/*/agents/*/flows/*/versions/*}:load"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Compares the specified base version with target version.
  rpc CompareVersions(CompareVersionsRequest)
      returns (CompareVersionsResponse) {
    option (google.api.http) = {
      post: "/v3beta1/{base_version=projects/*/locations/*/agents/*/flows/*/versions/*}:compareVersions"
      body: "*"
    };
    option (google.api.method_signature) = "base_version";
  }
}

// Metadata associated with the long running operation for
// [Versions.CreateVersion][google.cloud.dialogflow.cx.v3beta1.Versions.CreateVersion].
message CreateVersionOperationMetadata {
  // Name of the created version.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>`.
  string version = 1 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/Version"
  }];
}

// Represents a version of a flow.
message Version {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Version"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/flows/{flow}/versions/{version}"
  };

  // The state of the version.
  enum State {
    // Not specified. This value is not used.
    STATE_UNSPECIFIED = 0;

    // Version is not ready to serve (e.g. training is running).
    RUNNING = 1;

    // Training has succeeded and this version is ready to serve.
    SUCCEEDED = 2;

    // Version training failed.
    FAILED = 3;
  }

  // Format:
  // projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>.
  // Version ID is a self-increasing number generated by Dialogflow upon version
  // creation.
  string name = 1;

  // Required. The human-readable name of the version. Limit of 64 characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // The description of the version. The maximum length is 500 characters. If
  // exceeded, the request is rejected.
  string description = 3;

  // Output only. The NLU settings of the flow at version creation.
  NluSettings nlu_settings = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create time of the version.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of this version. This field is read-only and cannot
  // be set by create and update methods.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request message for
// [Versions.ListVersions][google.cloud.dialogflow.cx.v3beta1.Versions.ListVersions].
message ListVersionsRequest {
  // Required. The [Flow][google.cloud.dialogflow.cx.v3beta1.Flow] to list all
  // versions for. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Version"
    }
  ];

  // The maximum number of items to return in a single page. By default 20 and
  // at most 100.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Versions.ListVersions][google.cloud.dialogflow.cx.v3beta1.Versions.ListVersions].
message ListVersionsResponse {
  // A list of versions. There will be a maximum number of items returned based
  // on the page_size field in the request. The list may in some cases be empty
  // or contain fewer entries than page_size even if this isn't the last page.
  repeated Version versions = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Versions.GetVersion][google.cloud.dialogflow.cx.v3beta1.Versions.GetVersion].
message GetVersionRequest {
  // Required. The name of the
  // [Version][google.cloud.dialogflow.cx.v3beta1.Version]. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];
}

// The request message for
// [Versions.CreateVersion][google.cloud.dialogflow.cx.v3beta1.Versions.CreateVersion].
message CreateVersionRequest {
  // Required. The [Flow][google.cloud.dialogflow.cx.v3beta1.Flow] to create an
  // [Version][google.cloud.dialogflow.cx.v3beta1.Version] for. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Version"
    }
  ];

  // Required. The version to create.
  Version version = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Versions.UpdateVersion][google.cloud.dialogflow.cx.v3beta1.Versions.UpdateVersion].
message UpdateVersionRequest {
  // Required. The version to update.
  Version version = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The mask to control which fields get updated. Currently only
  // `description` and `display_name` can be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Versions.DeleteVersion][google.cloud.dialogflow.cx.v3beta1.Versions.DeleteVersion].
message DeleteVersionRequest {
  // Required. The name of the
  // [Version][google.cloud.dialogflow.cx.v3beta1.Version] to delete. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];
}

// The request message for
// [Versions.LoadVersion][google.cloud.dialogflow.cx.v3beta1.Versions.LoadVersion].
message LoadVersionRequest {
  // Required. The [Version][google.cloud.dialogflow.cx.v3beta1.Version] to be
  // loaded to draft flow. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];

  // This field is used to prevent accidental overwrite of other agent
  // resources, which can potentially impact other flow's behavior. If
  // `allow_override_agent_resources` is false, conflicted agent-level resources
  // will not be overridden (i.e. intents, entities, webhooks).
  bool allow_override_agent_resources = 2;
}

// The request message for
// [Versions.CompareVersions][google.cloud.dialogflow.cx.v3beta1.Versions.CompareVersions].
message CompareVersionsRequest {
  // Required. Name of the base flow version to compare with the target version.
  // Use version ID `0` to indicate the draft version of the specified flow.
  //
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>`.
  string base_version = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];

  // Required. Name of the target flow version to compare with the
  // base version. Use version ID `0` to indicate the draft version of the
  // specified flow. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>`.
  string target_version = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];

  // The language to compare the flow versions for.
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/docs/reference/language) are
  // supported. Note: languages must be enabled in the agent before they can be
  // used.
  string language_code = 3;
}

// The response message for
// [Versions.CompareVersions][google.cloud.dialogflow.cx.v3beta1.Versions.CompareVersions].
message CompareVersionsResponse {
  // JSON representation of the base version content.
  string base_version_content_json = 1;

  // JSON representation of the target version content.
  string target_version_content_json = 2;

  // The timestamp when the two version compares.
  google.protobuf.Timestamp compare_time = 3;
}
