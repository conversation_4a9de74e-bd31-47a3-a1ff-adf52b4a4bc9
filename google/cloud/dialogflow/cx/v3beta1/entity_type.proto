// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3beta1/inline.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "EntityTypeProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Service for managing
// [EntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityType].
service EntityTypes {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Retrieves the specified entity type.
  rpc GetEntityType(GetEntityTypeRequest) returns (EntityType) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/entityTypes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an entity type in the specified agent.
  rpc CreateEntityType(CreateEntityTypeRequest) returns (EntityType) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*}/entityTypes"
      body: "entity_type"
    };
    option (google.api.method_signature) = "parent,entity_type";
  }

  // Updates the specified entity type.
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc UpdateEntityType(UpdateEntityTypeRequest) returns (EntityType) {
    option (google.api.http) = {
      patch: "/v3beta1/{entity_type.name=projects/*/locations/*/agents/*/entityTypes/*}"
      body: "entity_type"
    };
    option (google.api.method_signature) = "entity_type,update_mask";
  }

  // Deletes the specified entity type.
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc DeleteEntityType(DeleteEntityTypeRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/agents/*/entityTypes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the list of all entity types in the specified agent.
  rpc ListEntityTypes(ListEntityTypesRequest)
      returns (ListEntityTypesResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*}/entityTypes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Exports the selected entity types.
  rpc ExportEntityTypes(ExportEntityTypesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*}/entityTypes:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportEntityTypesResponse"
      metadata_type: "ExportEntityTypesMetadata"
    };
  }

  // Imports the specified entitytypes into the agent.
  rpc ImportEntityTypes(ImportEntityTypesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*}/entityTypes:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ImportEntityTypesResponse"
      metadata_type: "ImportEntityTypesMetadata"
    };
  }
}

// Entities are extracted from user input and represent parameters that are
// meaningful to your application. For example, a date range, a proper name
// such as a geographic location or landmark, and so on. Entities represent
// actionable data for your application.
//
// When you define an entity, you can also include synonyms that all map to
// that entity. For example, "soft drink", "soda", "pop", and so on.
//
// There are three types of entities:
//
// *   **System** - entities that are defined by the Dialogflow API for common
//     data types such as date, time, currency, and so on. A system entity is
//     represented by the `EntityType` type.
//
// *   **Custom** - entities that are defined by you that represent
//     actionable data that is meaningful to your application. For example,
//     you could define a `pizza.sauce` entity for red or white pizza sauce,
//     a `pizza.cheese` entity for the different types of cheese on a pizza,
//     a `pizza.topping` entity for different toppings, and so on. A custom
//     entity is represented by the `EntityType` type.
//
// *   **User** - entities that are built for an individual user such as
//     favorites, preferences, playlists, and so on. A user entity is
//     represented by the
//     [SessionEntityType][google.cloud.dialogflow.cx.v3beta1.SessionEntityType]
//     type.
//
// For more information about entity types, see the [Dialogflow
// documentation](https://cloud.google.com/dialogflow/docs/entities-overview).
message EntityType {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/EntityType"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/entityTypes/{entity_type}"
  };

  // Represents kinds of entities.
  enum Kind {
    // Not specified. This value should be never used.
    KIND_UNSPECIFIED = 0;

    // Map entity types allow mapping of a group of synonyms to a canonical
    // value.
    KIND_MAP = 1;

    // List entity types contain a set of entries that do not map to canonical
    // values. However, list entity types can contain references to other entity
    // types (with or without aliases).
    KIND_LIST = 2;

    // Regexp entity types allow to specify regular expressions in entries
    // values.
    KIND_REGEXP = 3;
  }

  // Represents different entity type expansion modes. Automated expansion
  // allows an agent to recognize values that have not been explicitly listed in
  // the entity (for example, new kinds of shopping list items).
  enum AutoExpansionMode {
    // Auto expansion disabled for the entity.
    AUTO_EXPANSION_MODE_UNSPECIFIED = 0;

    // Allows an agent to recognize values that have not been explicitly
    // listed in the entity.
    AUTO_EXPANSION_MODE_DEFAULT = 1;
  }

  // An **entity entry** for an associated entity type.
  message Entity {
    // Required. The primary value associated with this entity entry.
    // For example, if the entity type is *vegetable*, the value could be
    // *scallions*.
    //
    // For `KIND_MAP` entity types:
    //
    // *   A canonical value to be used in place of synonyms.
    //
    // For `KIND_LIST` entity types:
    //
    // *   A string that can contain references to other entity types (with or
    //     without aliases).
    string value = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. A collection of value synonyms. For example, if the entity type
    // is *vegetable*, and `value` is *scallions*, a synonym could be *green
    // onions*.
    //
    // For `KIND_LIST` entity types:
    //
    // *   This collection must contain exactly one synonym equal to `value`.
    repeated string synonyms = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // An excluded entity phrase that should not be matched.
  message ExcludedPhrase {
    // Required. The word or phrase to be excluded.
    string value = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // The unique identifier of the entity type.
  // Required for
  // [EntityTypes.UpdateEntityType][google.cloud.dialogflow.cx.v3beta1.EntityTypes.UpdateEntityType].
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/entityTypes/<EntityTypeID>`.
  string name = 1;

  // Required. The human-readable name of the entity type, unique within the
  // agent.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Indicates the kind of entity type.
  Kind kind = 3 [(google.api.field_behavior) = REQUIRED];

  // Indicates whether the entity type can be automatically expanded.
  AutoExpansionMode auto_expansion_mode = 4;

  // The collection of entity entries associated with the entity type.
  repeated Entity entities = 5;

  // Collection of exceptional words and phrases that shouldn't be matched.
  // For example, if you have a size entity type with entry `giant`(an
  // adjective), you might consider adding `giants`(a noun) as an exclusion.
  // If the kind of entity type is `KIND_MAP`, then the phrases specified by
  // entities and excluded phrases should be mutually exclusive.
  repeated ExcludedPhrase excluded_phrases = 6;

  // Enables fuzzy entity extraction during classification.
  bool enable_fuzzy_extraction = 7;

  // Indicates whether parameters of the entity type should be redacted in log.
  // If redaction is enabled, page parameters and intent parameters referring to
  // the entity type will be replaced by parameter name during logging.
  bool redact = 9;
}

// The request message for
// [EntityTypes.ExportEntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityTypes.ExportEntityTypes].
message ExportEntityTypesRequest {
  // Data format of the exported entity types.
  enum DataFormat {
    // Unspecified format. Treated as `BLOB`.
    DATA_FORMAT_UNSPECIFIED = 0;

    // EntityTypes will be exported as raw bytes.
    BLOB = 1;

    // EntityTypes will be exported in JSON Package format.
    JSON_PACKAGE = 5;
  }

  // Required. The name of the parent agent to export entity types.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Required. The name of the entity types to export.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/entityTypes/<EntityTypeID>`.
  repeated string entity_types = 2 [(google.api.field_behavior) = REQUIRED];

  // The destination to export.
  oneof destination {
    // Optional. The [Google Cloud
    // Storage](https://cloud.google.com/storage/docs/) URI to export the entity
    // types to. The format of this URI must be
    // `gs://<bucket-name>/<object-name>`.
    //
    // Dialogflow performs a write operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have write permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string entity_types_uri = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The option to return the serialized entity types inline.
    bool entity_types_content_inline = 4
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The data format of the exported entity types. If not specified,
  // `BLOB` is assumed.
  DataFormat data_format = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The language to retrieve the entity type for. The following
  // fields are language dependent:
  //
  // *   `EntityType.entities.value`
  // *   `EntityType.entities.synonyms`
  // *   `EntityType.excluded_phrases.value`
  //
  // If not specified, all language dependent fields will be retrieved.
  // [Many
  // languages](https://cloud.google.com/dialogflow/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 6 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [EntityTypes.ExportEntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityTypes.ExportEntityTypes].
message ExportEntityTypesResponse {
  // Exported entity types can be either in cloud storage or local download.
  oneof exported_entity_types {
    // The URI to a file containing the exported entity types. This field is
    // populated only if `entity_types_uri` is specified in
    // [ExportEntityTypesRequest][google.cloud.dialogflow.cx.v3beta1.ExportEntityTypesRequest].
    string entity_types_uri = 1;

    // Uncompressed byte content for entity types. This field is populated only
    // if `entity_types_content_inline` is set to true in
    // [ExportEntityTypesRequest][google.cloud.dialogflow.cx.v3beta1.ExportEntityTypesRequest].
    InlineDestination entity_types_content = 2;
  }
}

// Metadata returned for the
// [EntityTypes.ExportEntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityTypes.ExportEntityTypes]
// long running operation.
message ExportEntityTypesMetadata {}

// The request message for
// [EntityTypes.ImportEntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityTypes.ImportEntityTypes].
message ImportEntityTypesRequest {
  // Merge option when display name conflicts exist during import.
  enum MergeOption {
    // Unspecified. If used, system uses REPORT_CONFLICT as default.
    MERGE_OPTION_UNSPECIFIED = 0;

    // Replace the original entity type in the agent with the new entity type
    // when display name conflicts exist.
    REPLACE = 1;

    // Merge the original entity type with the new entity type when display name
    // conflicts exist.
    MERGE = 2;

    // Create new entity types with new display names to differentiate them from
    // the existing entity types when display name conflicts exist.
    RENAME = 3;

    // Report conflict information if display names conflict is detected.
    // Otherwise, import entity types.
    REPORT_CONFLICT = 4;

    // Keep the original entity type and discard the conflicting new entity type
    // when display name conflicts exist.
    KEEP = 5;
  }

  // Required. The agent to import the entity types into.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Required. The entity types to import.
  oneof entity_types {
    // The [Google Cloud Storage](https://cloud.google.com/storage/docs/) URI
    // to import entity types from. The format of this URI must be
    // `gs://<bucket-name>/<object-name>`.
    //
    // Dialogflow performs a read operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have read permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string entity_types_uri = 2;

    // Uncompressed byte content of entity types.
    InlineSource entity_types_content = 3;
  }

  // Required. Merge option for importing entity types.
  MergeOption merge_option = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The target entity type to import into.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/entity_types/<EntityTypeID>`.
  //  If set, there should be only one entity type included in
  //  [entity_types][google.cloud.dialogflow.cx.v3beta1.ImportEntityTypesRequest.entity_types],
  //  of which the type should match the type of the target entity type. All
  //  [entities][google.cloud.dialogflow.cx.v3beta1.EntityType.entities] in the
  //  imported entity type will be added to the target entity type.
  string target_entity_type = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EntityType"
    }
  ];
}

// The response message for
// [EntityTypes.ImportEntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityTypes.ImportEntityTypes].
message ImportEntityTypesResponse {
  // Conflicting resources detected during the import process. Only filled when
  // [REPORT_CONFLICT][ImportEntityTypesResponse.REPORT_CONFLICT] is set in the
  // request and there are conflicts in the display names.
  message ConflictingResources {
    // Display names of conflicting entity types.
    repeated string entity_type_display_names = 1;

    // Display names of conflicting entities.
    repeated string entity_display_names = 2;
  }

  // The unique identifier of the imported entity types.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/entity_types/<EntityTypeID>`.
  repeated string entity_types = 1 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/EntityType"
  }];

  // Info which resources have conflicts when
  // [REPORT_CONFLICT][ImportEntityTypesResponse.REPORT_CONFLICT] merge_option
  // is set in ImportEntityTypesRequest.
  ConflictingResources conflicting_resources = 2;
}

// Metadata returned for the
// [EntityTypes.ImportEntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityTypes.ImportEntityTypes]
// long running operation.
message ImportEntityTypesMetadata {}

// The request message for
// [EntityTypes.ListEntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityTypes.ListEntityTypes].
message ListEntityTypesRequest {
  // Required. The agent to list all entity types for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // The language to list entity types for. The following fields are language
  // dependent:
  //
  // *   `EntityType.entities.value`
  // *   `EntityType.entities.synonyms`
  // *   `EntityType.excluded_phrases.value`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 2;

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 3;

  // The next_page_token value returned from a previous list request.
  string page_token = 4;
}

// The response message for
// [EntityTypes.ListEntityTypes][google.cloud.dialogflow.cx.v3beta1.EntityTypes.ListEntityTypes].
message ListEntityTypesResponse {
  // The list of entity types. There will be a maximum number of items returned
  // based on the page_size field in the request.
  repeated EntityType entity_types = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [EntityTypes.GetEntityType][google.cloud.dialogflow.cx.v3beta1.EntityTypes.GetEntityType].
message GetEntityTypeRequest {
  // Required. The name of the entity type.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/entityTypes/<EntityTypeID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // The language to retrieve the entity type for. The following fields are
  // language dependent:
  //
  // *   `EntityType.entities.value`
  // *   `EntityType.entities.synonyms`
  // *   `EntityType.excluded_phrases.value`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 2;
}

// The request message for
// [EntityTypes.CreateEntityType][google.cloud.dialogflow.cx.v3beta1.EntityTypes.CreateEntityType].
message CreateEntityTypeRequest {
  // Required. The agent to create a entity type for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Required. The entity type to create.
  EntityType entity_type = 2 [(google.api.field_behavior) = REQUIRED];

  // The language of the following fields in `entity_type`:
  //
  // *   `EntityType.entities.value`
  // *   `EntityType.entities.synonyms`
  // *   `EntityType.excluded_phrases.value`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 3;
}

// The request message for
// [EntityTypes.UpdateEntityType][google.cloud.dialogflow.cx.v3beta1.EntityTypes.UpdateEntityType].
message UpdateEntityTypeRequest {
  // Required. The entity type to update.
  EntityType entity_type = 1 [(google.api.field_behavior) = REQUIRED];

  // The language of the following fields in `entity_type`:
  //
  // *   `EntityType.entities.value`
  // *   `EntityType.entities.synonyms`
  // *   `EntityType.excluded_phrases.value`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 2;

  // The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 3;
}

// The request message for
// [EntityTypes.DeleteEntityType][google.cloud.dialogflow.cx.v3beta1.EntityTypes.DeleteEntityType].
message DeleteEntityTypeRequest {
  // Required. The name of the entity type to delete.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/entityTypes/<EntityTypeID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // This field has no effect for entity type not being used.
  // For entity types that are used by intents or pages:
  //
  // *  If `force` is set to false, an error will be returned with message
  //    indicating the referencing resources.
  // *  If `force` is set to true, Dialogflow will remove the entity type, as
  //    well as any references to the entity type (i.e. Page
  //    [parameter][google.cloud.dialogflow.cx.v3beta1.Form.Parameter] of the
  //    entity type will be changed to
  //    '@sys.any' and intent
  //    [parameter][google.cloud.dialogflow.cx.v3beta1.Intent.Parameter] of the
  //    entity type will be removed).
  bool force = 2;
}
