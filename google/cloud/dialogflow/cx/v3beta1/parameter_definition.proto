// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/field_behavior.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "ParameterDefinitionProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Defines the properties of a parameter.
// Used to define parameters used in the agent and the
// input / output parameters for each fulfillment.
// (-- Next Id: 4 --)
// (-- api-linter: core::0123::resource-annotation=disabled
//     aip.dev/not-precedent: ParameterDefinition is not an exposed resource.
//     --)
message ParameterDefinition {
  // Parameter types are used for validation.
  // These types are consistent with [google.protobuf.Value][].
  enum ParameterType {
    // Not specified. No validation will be performed.
    PARAMETER_TYPE_UNSPECIFIED = 0;
    // Represents any string value.
    STRING = 1;
    // Represents any number value.
    NUMBER = 2;
    // Represents a boolean value.
    BOOLEAN = 3;
    // Represents a null value.
    NULL = 4;
    // Represents any object value.
    OBJECT = 5;
    // Represents a repeated value.
    LIST = 6;
  }

  // Name of parameter.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Type of parameter.
  ParameterType type = 2 [(google.api.field_behavior) = REQUIRED];

  // Human-readable description of the parameter. Limited to 300 characters.
  string description = 3;
}
