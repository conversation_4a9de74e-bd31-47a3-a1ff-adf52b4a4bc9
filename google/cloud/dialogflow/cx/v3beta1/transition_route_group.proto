// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3beta1/page.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "TransitionRouteGroupProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Service for managing
// [TransitionRouteGroups][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup].
service TransitionRouteGroups {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all transition route groups in the specified flow.
  rpc ListTransitionRouteGroups(ListTransitionRouteGroupsRequest)
      returns (ListTransitionRouteGroupsResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*/flows/*}/transitionRouteGroups"
      additional_bindings {
        get: "/v3beta1/{parent=projects/*/locations/*/agents/*}/transitionRouteGroups"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified
  // [TransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup].
  rpc GetTransitionRouteGroup(GetTransitionRouteGroupRequest)
      returns (TransitionRouteGroup) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/flows/*/transitionRouteGroups/*}"
      additional_bindings {
        get: "/v3beta1/{name=projects/*/locations/*/agents/*/transitionRouteGroups/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an
  // [TransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup]
  // in the specified flow.
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc CreateTransitionRouteGroup(CreateTransitionRouteGroupRequest)
      returns (TransitionRouteGroup) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*/flows/*}/transitionRouteGroups"
      body: "transition_route_group"
      additional_bindings {
        post: "/v3beta1/{parent=projects/*/locations/*/agents/*}/transitionRouteGroups"
        body: "transition_route_group"
      }
    };
    option (google.api.method_signature) = "parent,transition_route_group";
  }

  // Updates the specified
  // [TransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup].
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc UpdateTransitionRouteGroup(UpdateTransitionRouteGroupRequest)
      returns (TransitionRouteGroup) {
    option (google.api.http) = {
      patch: "/v3beta1/{transition_route_group.name=projects/*/locations/*/agents/*/flows/*/transitionRouteGroups/*}"
      body: "transition_route_group"
      additional_bindings {
        patch: "/v3beta1/{transition_route_group.name=projects/*/locations/*/agents/*/transitionRouteGroups/*}"
        body: "transition_route_group"
      }
    };
    option (google.api.method_signature) = "transition_route_group,update_mask";
  }

  // Deletes the specified
  // [TransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup].
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc DeleteTransitionRouteGroup(DeleteTransitionRouteGroupRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/agents/*/flows/*/transitionRouteGroups/*}"
      additional_bindings {
        delete: "/v3beta1/{name=projects/*/locations/*/agents/*/transitionRouteGroups/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }
}

// A TransitionRouteGroup represents a group of
// [`TransitionRoutes`][google.cloud.dialogflow.cx.v3beta1.TransitionRoute] to
// be used by a [Page][google.cloud.dialogflow.cx.v3beta1.Page].
message TransitionRouteGroup {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/TransitionRouteGroup"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/flows/{flow}/transitionRouteGroups/{transition_route_group}"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/transitionRouteGroups/{transition_route_group}"
    plural: "transitionRouteGroups"
    singular: "transitionRouteGroup"
  };

  // The unique identifier of the transition route group.
  // [TransitionRouteGroups.CreateTransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups.CreateTransitionRouteGroup]
  // populates the name automatically. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/transitionRouteGroups/<TransitionRouteGroupID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/transitionRouteGroups/<TransitionRouteGroupID>`
  // for agent-level groups.
  string name = 1;

  // Required. The human-readable name of the transition route group, unique
  // within the flow. The display name can be no longer than 30 characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Transition routes associated with the
  // [TransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup].
  repeated TransitionRoute transition_routes = 5;
}

// The request message for
// [TransitionRouteGroups.ListTransitionRouteGroups][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups.ListTransitionRouteGroups].
message ListTransitionRouteGroupsRequest {
  // Required. The flow to list all transition route groups for.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`
  //  or `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/TransitionRouteGroup"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;

  // The language to list transition route groups for. The following fields are
  // language dependent:
  //
  // *  `TransitionRouteGroup.transition_routes.trigger_fulfillment.messages`
  // *
  // `TransitionRouteGroup.transition_routes.trigger_fulfillment.conditional_cases`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 4;
}

// The response message for
// [TransitionRouteGroups.ListTransitionRouteGroups][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups.ListTransitionRouteGroups].
message ListTransitionRouteGroupsResponse {
  // The list of transition route groups. There will be a maximum number of
  // items returned based on the page_size field in the request. The list may in
  // some cases be empty or contain fewer entries than page_size even if this
  // isn't the last page.
  repeated TransitionRouteGroup transition_route_groups = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [TransitionRouteGroups.GetTransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups.GetTransitionRouteGroup].
message GetTransitionRouteGroupRequest {
  // Required. The name of the
  // [TransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup].
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/transitionRouteGroups/<TransitionRouteGroupID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/transitionRouteGroups/<TransitionRouteGroupID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/TransitionRouteGroup"
    }
  ];

  // The language to retrieve the transition route group for. The following
  // fields are language dependent:
  //
  // *  `TransitionRouteGroup.transition_routes.trigger_fulfillment.messages`
  // *
  // `TransitionRouteGroup.transition_routes.trigger_fulfillment.conditional_cases`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 2;
}

// The request message for
// [TransitionRouteGroups.CreateTransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups.CreateTransitionRouteGroup].
message CreateTransitionRouteGroupRequest {
  // Required. The flow to create an
  // [TransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup]
  // for. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`
  // or `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`
  // for agent-level groups.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/TransitionRouteGroup"
    }
  ];

  // Required. The transition route group to create.
  TransitionRouteGroup transition_route_group = 2
      [(google.api.field_behavior) = REQUIRED];

  // The language of the following fields in `TransitionRouteGroup`:
  //
  // *  `TransitionRouteGroup.transition_routes.trigger_fulfillment.messages`
  // *
  // `TransitionRouteGroup.transition_routes.trigger_fulfillment.conditional_cases`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 3;
}

// The request message for
// [TransitionRouteGroups.UpdateTransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups.UpdateTransitionRouteGroup].
message UpdateTransitionRouteGroupRequest {
  // Required. The transition route group to update.
  TransitionRouteGroup transition_route_group = 1
      [(google.api.field_behavior) = REQUIRED];

  // The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 2;

  // The language of the following fields in `TransitionRouteGroup`:
  //
  // *  `TransitionRouteGroup.transition_routes.trigger_fulfillment.messages`
  // *
  // `TransitionRouteGroup.transition_routes.trigger_fulfillment.conditional_cases`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 3;
}

// The request message for
// [TransitionRouteGroups.DeleteTransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups.DeleteTransitionRouteGroup].
message DeleteTransitionRouteGroupRequest {
  // Required. The name of the
  // [TransitionRouteGroup][google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroup]
  // to delete. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/transitionRouteGroups/<TransitionRouteGroupID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/transitionRouteGroups/<TransitionRouteGroupID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/TransitionRouteGroup"
    }
  ];

  // This field has no effect for transition route group that no page is using.
  // If the transition route group is referenced by any page:
  //
  // *  If `force` is set to false, an error will be returned with message
  //    indicating pages that reference the transition route group.
  // *  If `force` is set to true, Dialogflow will remove the transition route
  //    group, as well as any reference to it.
  bool force = 2;
}
