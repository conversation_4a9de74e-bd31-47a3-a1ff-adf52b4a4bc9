{"methodConfig": [{"name": [{"service": "google.cloud.dialogflow.cx.v3beta1.Agents"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Changelogs"}, {"service": "google.cloud.dialogflow.cx.v3beta1.ConversationHistory"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Deployments"}, {"service": "google.cloud.dialogflow.cx.v3beta1.EncryptionSpecService"}, {"service": "google.cloud.dialogflow.cx.v3beta1.EntityTypes"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Environments"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Experiments"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Flows"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Fulfillments"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Generators"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Intents"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Pages"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Playbooks"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Examples"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Tools"}, {"service": "google.cloud.dialogflow.cx.v3beta1.SecuritySettingsService"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Sessions"}, {"service": "google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes"}, {"service": "google.cloud.dialogflow.cx.v3beta1.TestCases"}, {"service": "google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Versions"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Webhooks"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.cx.v3beta1.Agents", "method": "CreateAgent"}], "timeout": "180s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.cx.v3beta1.Sessions", "method": "DetectIntent"}], "timeout": "220s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.cx.v3beta1.Sessions", "method": "ServerStreamingDetectIntent"}, {"service": "google.cloud.dialogflow.cx.v3beta1.Sessions", "method": "StreamingDetectIntent"}], "timeout": "220s"}]}