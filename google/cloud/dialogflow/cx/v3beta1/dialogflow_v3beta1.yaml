type: google.api.Service
config_version: 3
name: dialogflow.googleapis.com
title: Dialogflow API

apis:
- name: google.cloud.dialogflow.cx.v3beta1.Agents
- name: google.cloud.dialogflow.cx.v3beta1.Changelogs
- name: google.cloud.dialogflow.cx.v3beta1.ConversationHistory
- name: google.cloud.dialogflow.cx.v3beta1.Deployments
- name: google.cloud.dialogflow.cx.v3beta1.EntityTypes
- name: google.cloud.dialogflow.cx.v3beta1.Environments
- name: google.cloud.dialogflow.cx.v3beta1.Examples
- name: google.cloud.dialogflow.cx.v3beta1.Experiments
- name: google.cloud.dialogflow.cx.v3beta1.Flows
- name: google.cloud.dialogflow.cx.v3beta1.Generators
- name: google.cloud.dialogflow.cx.v3beta1.Intents
- name: google.cloud.dialogflow.cx.v3beta1.Pages
- name: google.cloud.dialogflow.cx.v3beta1.Playbooks
- name: google.cloud.dialogflow.cx.v3beta1.SecuritySettingsService
- name: google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes
- name: google.cloud.dialogflow.cx.v3beta1.Sessions
- name: google.cloud.dialogflow.cx.v3beta1.TestCases
- name: google.cloud.dialogflow.cx.v3beta1.Tools
- name: google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups
- name: google.cloud.dialogflow.cx.v3beta1.Versions
- name: google.cloud.dialogflow.cx.v3beta1.Webhooks
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.dialogflow.cx.v3beta1.BatchRunTestCasesMetadata
- name: google.cloud.dialogflow.cx.v3beta1.BatchRunTestCasesResponse
- name: google.cloud.dialogflow.cx.v3beta1.CreateVersionOperationMetadata
- name: google.cloud.dialogflow.cx.v3beta1.DeployFlowMetadata
- name: google.cloud.dialogflow.cx.v3beta1.DeployFlowResponse
- name: google.cloud.dialogflow.cx.v3beta1.ExportAgentResponse
- name: google.cloud.dialogflow.cx.v3beta1.ExportEntityTypesMetadata
- name: google.cloud.dialogflow.cx.v3beta1.ExportEntityTypesResponse
- name: google.cloud.dialogflow.cx.v3beta1.ExportFlowResponse
- name: google.cloud.dialogflow.cx.v3beta1.ExportIntentsMetadata
- name: google.cloud.dialogflow.cx.v3beta1.ExportIntentsResponse
- name: google.cloud.dialogflow.cx.v3beta1.ExportTestCasesMetadata
- name: google.cloud.dialogflow.cx.v3beta1.ExportTestCasesResponse
- name: google.cloud.dialogflow.cx.v3beta1.ImportEntityTypesMetadata
- name: google.cloud.dialogflow.cx.v3beta1.ImportEntityTypesResponse
- name: google.cloud.dialogflow.cx.v3beta1.ImportFlowResponse
- name: google.cloud.dialogflow.cx.v3beta1.ImportIntentsMetadata
- name: google.cloud.dialogflow.cx.v3beta1.ImportIntentsResponse
- name: google.cloud.dialogflow.cx.v3beta1.ImportTestCasesMetadata
- name: google.cloud.dialogflow.cx.v3beta1.ImportTestCasesResponse
- name: google.cloud.dialogflow.cx.v3beta1.RunContinuousTestMetadata
- name: google.cloud.dialogflow.cx.v3beta1.RunContinuousTestResponse
- name: google.cloud.dialogflow.cx.v3beta1.RunTestCaseMetadata
- name: google.cloud.dialogflow.cx.v3beta1.RunTestCaseResponse
- name: google.cloud.dialogflow.cx.v3beta1.TestError
- name: google.cloud.dialogflow.cx.v3beta1.WebhookRequest
- name: google.cloud.dialogflow.cx.v3beta1.WebhookResponse

documentation:
  summary: |-
    Builds conversational interfaces (for example, chatbots, and voice-powered
    apps and devices).
  overview: |-
    <!-- mdformat off(presubmit failing, mdformat is as well) --> Dialogflow is
    a natural language understanding platform that makes it easy
    to design and integrate a conversational user interface into your mobile
    app, web application, device, bot, interactive voice response system, and
    so on. Using Dialogflow, you can provide new and engaging ways for
    users to interact with your product.

    Dialogflow can analyze multiple types of input from your customers,
    including text or audio inputs (like from a phone or voice recording).
    It can also respond to your customers in a couple of ways, either through
    text or with synthetic speech.

    For more information, see the
    [Dialogflow documentation](https://cloud.google.com/dialogflow/docs).
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v3beta1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v3beta1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v3beta1/{name=projects/*/operations/*}:cancel'
    additional_bindings:
    - post: '/v3beta1/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v3beta1/{name=projects/*/operations/*}'
    additional_bindings:
    - get: '/v3beta1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v3beta1/{name=projects/*}/operations'
    additional_bindings:
    - get: '/v3beta1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Agents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.cx.v3beta1.Changelogs.GetChangelog
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.cx.v3beta1.Changelogs.ListChangelogs
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.ConversationHistory.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.cx.v3beta1.Deployments.GetDeployment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.cx.v3beta1.Deployments.ListDeployments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.EntityTypes.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Environments.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Examples.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Experiments.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Flows.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Generators.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Intents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Pages.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Playbooks.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.SecuritySettingsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Sessions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.TestCases.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Tools.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.TransitionRouteGroups.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Versions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3beta1.Webhooks.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
