// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3/advanced_settings.proto";
import "google/cloud/dialogflow/cx/v3/audio_config.proto";
import "google/cloud/dialogflow/cx/v3/flow.proto";
import "google/cloud/dialogflow/cx/v3/generative_settings.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "AgentProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/SecretVersion"
  pattern: "projects/{project}/secrets/{secret}/versions/{version}"
};

// Service for managing [Agents][google.cloud.dialogflow.cx.v3.Agent].
service Agents {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all agents in the specified location.
  rpc ListAgents(ListAgentsRequest) returns (ListAgentsResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*}/agents"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified agent.
  rpc GetAgent(GetAgentRequest) returns (Agent) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an agent in the specified location.
  //
  // Note: You should always train flows prior to sending them queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc CreateAgent(CreateAgentRequest) returns (Agent) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*}/agents"
      body: "agent"
    };
    option (google.api.method_signature) = "parent,agent";
  }

  // Updates the specified agent.
  //
  // Note: You should always train flows prior to sending them queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc UpdateAgent(UpdateAgentRequest) returns (Agent) {
    option (google.api.http) = {
      patch: "/v3/{agent.name=projects/*/locations/*/agents/*}"
      body: "agent"
    };
    option (google.api.method_signature) = "agent,update_mask";
  }

  // Deletes the specified agent.
  rpc DeleteAgent(DeleteAgentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3/{name=projects/*/locations/*/agents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Exports the specified agent to a binary file.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`:
  // [ExportAgentResponse][google.cloud.dialogflow.cx.v3.ExportAgentResponse]
  rpc ExportAgent(ExportAgentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*/locations/*/agents/*}:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportAgentResponse"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Restores the specified agent from a binary file.
  //
  // Replaces the current agent with a new one. Note that all existing resources
  // in agent (e.g. intents, entity types, flows) will be removed.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  //
  // Note: You should always train flows prior to sending them queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc RestoreAgent(RestoreAgentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*/locations/*/agents/*}:restore"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Validates the specified agent and creates or updates validation results.
  // The agent in draft version is validated. Please call this API after the
  // training is completed to get the complete validation results.
  rpc ValidateAgent(ValidateAgentRequest) returns (AgentValidationResult) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*/locations/*/agents/*}:validate"
      body: "*"
    };
  }

  // Gets the latest agent validation result. Agent validation is performed
  // when ValidateAgent is called.
  rpc GetAgentValidationResult(GetAgentValidationResultRequest)
      returns (AgentValidationResult) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/validationResult}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the generative settings for the agent.
  rpc GetGenerativeSettings(GetGenerativeSettingsRequest)
      returns (GenerativeSettings) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/generativeSettings}"
    };
    option (google.api.method_signature) = "name,language_code";
  }

  // Updates the generative settings for the agent.
  rpc UpdateGenerativeSettings(UpdateGenerativeSettingsRequest)
      returns (GenerativeSettings) {
    option (google.api.http) = {
      patch: "/v3/{generative_settings.name=projects/*/locations/*/agents/*/generativeSettings}"
      body: "generative_settings"
    };
    option (google.api.method_signature) = "generative_settings,update_mask";
  }
}

// Settings related to speech recognition.
message SpeechToTextSettings {
  // Whether to use speech adaptation for speech recognition.
  bool enable_speech_adaptation = 1;
}

// Agents are best described as Natural Language Understanding (NLU) modules
// that transform user requests into actionable data. You can include agents
// in your app, product, or service to determine user intent and respond to the
// user in a natural way.
//
// After you create an agent, you can add
// [Intents][google.cloud.dialogflow.cx.v3.Intent], [Entity
// Types][google.cloud.dialogflow.cx.v3.EntityType],
// [Flows][google.cloud.dialogflow.cx.v3.Flow],
// [Fulfillments][google.cloud.dialogflow.cx.v3.Fulfillment],
// [Webhooks][google.cloud.dialogflow.cx.v3.Webhook],
// [TransitionRouteGroups][google.cloud.dialogflow.cx.v3.TransitionRouteGroup]
// and so on to manage the conversation flows.
message Agent {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Agent"
    pattern: "projects/{project}/locations/{location}/agents/{agent}"
  };

  // Settings for connecting to Git repository for an agent.
  message GitIntegrationSettings {
    // Settings of integration with GitHub.
    message GithubSettings {
      // The unique repository display name for the GitHub repository.
      string display_name = 1;

      // The GitHub repository URI related to the agent.
      string repository_uri = 2;

      // The branch of the GitHub repository tracked for this agent.
      string tracking_branch = 3;

      // The access token used to authenticate the access to the GitHub
      // repository.
      string access_token = 4;

      // A list of branches configured to be used from Dialogflow.
      repeated string branches = 5;
    }

    // The git settings to specific systems.
    oneof git_settings {
      // GitHub settings.
      GithubSettings github_settings = 1;
    }
  }

  // Settings for Gen App Builder.
  message GenAppBuilderSettings {
    // Required. The full name of the Gen App Builder engine related to this
    // agent if there is one. Format: `projects/{Project ID}/locations/{Location
    // ID}/collections/{Collection ID}/engines/{Engine ID}`
    string engine = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Settings for answer feedback collection.
  message AnswerFeedbackSettings {
    // Optional. If enabled, end users will be able to provide
    // [answer feedback][google.cloud.dialogflow.cx.v3.AnswerFeedback] to
    // Dialogflow responses. Feature works only if interaction logging is
    // enabled in the Dialogflow agent.
    bool enable_answer_feedback = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Settings for end user personalization.
  message PersonalizationSettings {
    // Optional. Default end user metadata, used when processing DetectIntent
    // requests. Recommended to be filled as a template instead of hard-coded
    // value, for example { "age": "$session.params.age" }. The data will be
    // merged with the
    // [QueryParameters.end_user_metadata][google.cloud.dialogflow.cx.v3.QueryParameters.end_user_metadata]
    // in
    // [DetectIntentRequest.query_params][google.cloud.dialogflow.cx.v3.DetectIntentRequest.query_params]
    // during query processing.
    google.protobuf.Struct default_end_user_metadata = 1
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Settings for custom client certificates.
  message ClientCertificateSettings {
    // Required. The ssl certificate encoded in PEM format. This string must
    // include the begin header and end footer lines.
    string ssl_certificate = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The name of the SecretManager secret version resource storing
    // the private key encoded in PEM format. Format:
    // `projects/{project}/secrets/{secret}/versions/{version}`
    string private_key = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "secretmanager.googleapis.com/SecretVersion"
      }
    ];

    // Optional. The name of the SecretManager secret version resource storing
    // the passphrase. 'passphrase' should be left unset if the private key is
    // not encrypted.
    // Format: `projects/{project}/secrets/{secret}/versions/{version}`
    string passphrase = 3 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "secretmanager.googleapis.com/SecretVersion"
      }
    ];
  }

  // The unique identifier of the agent.
  // Required for the
  // [Agents.UpdateAgent][google.cloud.dialogflow.cx.v3.Agents.UpdateAgent]
  // method.
  // [Agents.CreateAgent][google.cloud.dialogflow.cx.v3.Agents.CreateAgent]
  // populates the name automatically.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string name = 1;

  // Required. The human-readable name of the agent, unique within the location.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. The default language of the agent as a language tag.
  // See [Language
  // Support](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // for a list of the currently supported language codes.
  // This field cannot be set by the
  // [Agents.UpdateAgent][google.cloud.dialogflow.cx.v3.Agents.UpdateAgent]
  // method.
  string default_language_code = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The list of all languages supported by the agent (except for the
  // `default_language_code`).
  repeated string supported_language_codes = 4;

  // Required. The time zone of the agent from the [time zone
  // database](https://www.iana.org/time-zones), e.g., America/New_York,
  // Europe/Paris.
  string time_zone = 5 [(google.api.field_behavior) = REQUIRED];

  // The description of the agent. The maximum length is 500 characters. If
  // exceeded, the request is rejected.
  string description = 6;

  // The URI of the agent's avatar. Avatars are used throughout the Dialogflow
  // console and in the self-hosted [Web
  // Demo](https://cloud.google.com/dialogflow/docs/integrations/web-demo)
  // integration.
  string avatar_uri = 7;

  // Speech recognition related settings.
  SpeechToTextSettings speech_to_text_settings = 13;

  // Immutable. Name of the start flow in this agent. A start flow will be
  // automatically created when the agent is created, and can only be deleted by
  // deleting the agent. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string start_flow = 16 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];

  // Name of the
  // [SecuritySettings][google.cloud.dialogflow.cx.v3.SecuritySettings]
  // reference for the agent. Format:
  // `projects/<ProjectID>/locations/<LocationID>/securitySettings/<SecuritySettingsID>`.
  string security_settings = 17 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/SecuritySettings"
  }];

  // Indicates if stackdriver logging is enabled for the agent.
  // Please use
  // [agent.advanced_settings][google.cloud.dialogflow.cx.v3.AdvancedSettings.LoggingSettings]
  // instead.
  bool enable_stackdriver_logging = 18 [deprecated = true];

  // Indicates if automatic spell correction is enabled in detect intent
  // requests.
  bool enable_spell_correction = 20;

  // Optional. Enable training multi-lingual models for this agent. These models
  // will be trained on all the languages supported by the agent.
  bool enable_multi_language_training = 40
      [(google.api.field_behavior) = OPTIONAL];

  // Indicates whether the agent is locked for changes. If the agent is locked,
  // modifications to the agent will be rejected except for [RestoreAgent][].
  bool locked = 27;

  // Hierarchical advanced settings for this agent. The settings exposed at the
  // lower level overrides the settings exposed at the higher level.
  AdvancedSettings advanced_settings = 22;

  // Git integration settings for this agent.
  GitIntegrationSettings git_integration_settings = 30;

  // Settings on instructing the speech synthesizer on how to generate the
  // output audio content.
  TextToSpeechSettings text_to_speech_settings = 31;

  // Gen App Builder-related agent-level settings.
  optional GenAppBuilderSettings gen_app_builder_settings = 33;

  // Optional. Answer feedback collection settings.
  AnswerFeedbackSettings answer_feedback_settings = 38
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Settings for end user personalization.
  PersonalizationSettings personalization_settings = 42
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Settings for custom client certificates.
  ClientCertificateSettings client_certificate_settings = 43
      [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [Agents.ListAgents][google.cloud.dialogflow.cx.v3.Agents.ListAgents].
message ListAgentsRequest {
  // Required. The location to list all agents for.
  // Format: `projects/<ProjectID>/locations/<LocationID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Agents.ListAgents][google.cloud.dialogflow.cx.v3.Agents.ListAgents].
message ListAgentsResponse {
  // The list of agents. There will be a maximum number of items returned based
  // on the page_size field in the request.
  repeated Agent agents = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Agents.GetAgent][google.cloud.dialogflow.cx.v3.Agents.GetAgent].
message GetAgentRequest {
  // Required. The name of the agent.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Agent"
    }
  ];
}

// The request message for
// [Agents.CreateAgent][google.cloud.dialogflow.cx.v3.Agents.CreateAgent].
message CreateAgentRequest {
  // Required. The location to create a agent for.
  // Format: `projects/<ProjectID>/locations/<LocationID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Required. The agent to create.
  Agent agent = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Agents.UpdateAgent][google.cloud.dialogflow.cx.v3.Agents.UpdateAgent].
message UpdateAgentRequest {
  // Required. The agent to update.
  Agent agent = 1 [(google.api.field_behavior) = REQUIRED];

  // The mask to control which fields get updated. If the mask is not present,
  // all fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request message for
// [Agents.DeleteAgent][google.cloud.dialogflow.cx.v3.Agents.DeleteAgent].
message DeleteAgentRequest {
  // Required. The name of the agent to delete.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Agent"
    }
  ];
}

// The request message for
// [Agents.ExportAgent][google.cloud.dialogflow.cx.v3.Agents.ExportAgent].
message ExportAgentRequest {
  // Data format of the exported agent.
  enum DataFormat {
    // Unspecified format.
    DATA_FORMAT_UNSPECIFIED = 0;

    // Agent content will be exported as raw bytes.
    BLOB = 1;

    // Agent content will be exported in JSON Package format.
    JSON_PACKAGE = 4;
  }

  // Settings for exporting to a git branch.
  message GitDestination {
    // Tracking branch for the git push.
    string tracking_branch = 1;

    // Commit message for the git push.
    string commit_message = 2;
  }

  // Required. The name of the agent to export.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Optional. The [Google Cloud
  // Storage](https://cloud.google.com/storage/docs/) URI to export the agent
  // to. The format of this URI must be `gs://<bucket-name>/<object-name>`. If
  // left unspecified, the serialized agent is returned inline.
  //
  // Dialogflow performs a write operation for the Cloud Storage object
  // on the caller's behalf, so your request authentication must
  // have write permissions for the object. For more information, see
  // [Dialogflow access
  // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
  string agent_uri = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The data format of the exported agent. If not specified, `BLOB`
  // is assumed.
  DataFormat data_format = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Environment name. If not set, draft environment is assumed.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string environment = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];

  // Optional. The Git branch to export the agent to.
  GitDestination git_destination = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether to include BigQuery Export setting.
  bool include_bigquery_export_settings = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Agents.ExportAgent][google.cloud.dialogflow.cx.v3.Agents.ExportAgent].
message ExportAgentResponse {
  // The exported agent.
  oneof agent {
    // The URI to a file containing the exported agent. This field is populated
    // if `agent_uri` is specified in
    // [ExportAgentRequest][google.cloud.dialogflow.cx.v3.ExportAgentRequest].
    string agent_uri = 1;

    // Uncompressed raw byte content for agent. This field is populated
    // if none of `agent_uri` and `git_destination` are specified in
    // [ExportAgentRequest][google.cloud.dialogflow.cx.v3.ExportAgentRequest].
    bytes agent_content = 2;

    // Commit SHA of the git push. This field is populated if
    // `git_destination` is specified in
    // [ExportAgentRequest][google.cloud.dialogflow.cx.v3.ExportAgentRequest].
    string commit_sha = 3;
  }
}

// The request message for
// [Agents.RestoreAgent][google.cloud.dialogflow.cx.v3.Agents.RestoreAgent].
message RestoreAgentRequest {
  // Settings for restoring from a git branch
  message GitSource {
    // tracking branch for the git pull
    string tracking_branch = 1;
  }

  // Restore option.
  enum RestoreOption {
    // Unspecified. Treated as KEEP.
    RESTORE_OPTION_UNSPECIFIED = 0;

    // Always respect the settings from the exported agent file. It may cause
    // a restoration failure if some settings (e.g. model type) are not
    // supported in the target agent.
    KEEP = 1;

    // Fallback to default settings if some settings are not supported in the
    // target agent.
    FALLBACK = 2;
  }

  // Required. The name of the agent to restore into.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Required. The agent to restore.
  oneof agent {
    // The [Google Cloud Storage](https://cloud.google.com/storage/docs/) URI
    // to restore agent from. The format of this URI must be
    // `gs://<bucket-name>/<object-name>`.
    //
    // Dialogflow performs a read operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have read permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string agent_uri = 2;

    // Uncompressed raw byte content for agent.
    bytes agent_content = 3;

    // Setting for restoring from a git branch
    GitSource git_source = 6;
  }

  // Agent restore mode. If not specified, `KEEP` is assumed.
  RestoreOption restore_option = 5;
}

// The request message for
// [Agents.ValidateAgent][google.cloud.dialogflow.cx.v3.Agents.ValidateAgent].
message ValidateAgentRequest {
  // Required. The agent to validate.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // If not specified, the agent's default language is used.
  string language_code = 2;
}

// The request message for
// [Agents.GetAgentValidationResult][google.cloud.dialogflow.cx.v3.Agents.GetAgentValidationResult].
message GetAgentValidationResultRequest {
  // Required. The agent name.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/validationResult`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/AgentValidationResult"
    }
  ];

  // If not specified, the agent's default language is used.
  string language_code = 2;
}

// The response message for
// [Agents.GetAgentValidationResult][google.cloud.dialogflow.cx.v3.Agents.GetAgentValidationResult].
message AgentValidationResult {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/AgentValidationResult"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/validationResult"
  };

  // The unique identifier of the agent validation result.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/validationResult`.
  string name = 1;

  // Contains all flow validation results.
  repeated FlowValidationResult flow_validation_results = 2;
}

// Request for
// [GetGenerativeSettings][google.cloud.dialogflow.cx.v3.Agents.GetGenerativeSettings]
// RPC.
message GetGenerativeSettingsRequest {
  // Required. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/generativeSettings`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/AgentGenerativeSettings"
    }
  ];

  // Required. Language code of the generative settings.
  string language_code = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for
// [UpdateGenerativeSettings][google.cloud.dialogflow.cx.v3.Agents.UpdateGenerativeSettings]
// RPC.
message UpdateGenerativeSettingsRequest {
  // Required. Generative settings to update.
  GenerativeSettings generative_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The mask to control which fields get updated. If the mask is not
  // present, all fields will be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}
