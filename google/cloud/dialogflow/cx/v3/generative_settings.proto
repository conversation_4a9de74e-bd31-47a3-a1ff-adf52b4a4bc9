// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3/safety_settings.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "GenerativeSettingsProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";

// Settings for Generative AI.
message GenerativeSettings {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/AgentGenerativeSettings"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/generativeSettings"
  };

  // Settings for Generative Fallback.
  message FallbackSettings {
    // Prompt template.
    message PromptTemplate {
      // Prompt name.
      string display_name = 1;

      // Prompt text that is sent to a LLM on no-match default, placeholders are
      // filled downstream. For example: "Here is a conversation $conversation,
      // a response is: "
      string prompt_text = 2;

      // If the flag is true, the prompt is frozen and cannot be modified by
      // users.
      bool frozen = 3;
    }

    // Display name of the selected prompt.
    string selected_prompt = 3;

    // Stored prompts that can be selected, for example default templates like
    // "conservative" or "chatty", or user defined ones.
    repeated PromptTemplate prompt_templates = 4;
  }

  // Settings for knowledge connector. These parameters are used for LLM prompt
  // like "You are <agent>. You are a helpful and verbose <agent_identity> at
  // <business>, <business_description>. Your task is to help humans on
  // <agent_scope>".
  message KnowledgeConnectorSettings {
    // Name of the company, organization or other entity that the agent
    // represents. Used for knowledge connector LLM prompt and for knowledge
    // search.
    string business = 1;

    // Name of the virtual agent. Used for LLM prompt. Can be left empty.
    string agent = 2;

    // Identity of the agent, e.g. "virtual agent", "AI assistant".
    string agent_identity = 3;

    // Company description, used for LLM prompt, e.g. "a family company selling
    // freshly roasted coffee beans".
    string business_description = 4;

    // Agent scope, e.g. "Example company website", "internal Example
    // company website for employees", "manual of car owner".
    string agent_scope = 5;

    // Whether to disable fallback to Data Store search results (in case the LLM
    // couldn't pick a proper answer). Per default the feature is enabled.
    bool disable_data_store_fallback = 8;
  }

  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/generativeSettings`.
  string name = 5;

  // Settings for Generative Fallback.
  FallbackSettings fallback_settings = 1;

  // Settings for Generative Safety.
  SafetySettings generative_safety_settings = 3;

  // Settings for knowledge connector.
  KnowledgeConnectorSettings knowledge_connector_settings = 7;

  // Language for this settings.
  string language_code = 4;
}
