// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3/response_message.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "WebhookProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";
option (google.api.resource_definition) = {
  type: "servicedirectory.googleapis.com/Service"
  pattern: "projects/{project}/locations/{location}/namespaces/{namespace}/services/{service}"
};

// Service for managing [Webhooks][google.cloud.dialogflow.cx.v3.Webhook].
service Webhooks {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all webhooks in the specified agent.
  rpc ListWebhooks(ListWebhooksRequest) returns (ListWebhooksResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*/agents/*}/webhooks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified webhook.
  rpc GetWebhook(GetWebhookRequest) returns (Webhook) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/webhooks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a webhook in the specified agent.
  rpc CreateWebhook(CreateWebhookRequest) returns (Webhook) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*/agents/*}/webhooks"
      body: "webhook"
    };
    option (google.api.method_signature) = "parent,webhook";
  }

  // Updates the specified webhook.
  rpc UpdateWebhook(UpdateWebhookRequest) returns (Webhook) {
    option (google.api.http) = {
      patch: "/v3/{webhook.name=projects/*/locations/*/agents/*/webhooks/*}"
      body: "webhook"
    };
    option (google.api.method_signature) = "webhook,update_mask";
  }

  // Deletes the specified webhook.
  rpc DeleteWebhook(DeleteWebhookRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3/{name=projects/*/locations/*/agents/*/webhooks/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Webhooks host the developer's business logic. During a session, webhooks
// allow the developer to use the data extracted by Dialogflow's natural
// language processing to generate dynamic responses, validate collected data,
// or trigger actions on the backend.
message Webhook {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Webhook"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/webhooks/{webhook}"
  };

  // Represents configuration for a generic web service.
  message GenericWebService {
    // Represents configuration of OAuth client credential flow for 3rd party
    // API authentication.
    message OAuthConfig {
      // Required. The client ID provided by the 3rd party platform.
      string client_id = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. The client secret provided by the 3rd party platform.
      string client_secret = 2 [(google.api.field_behavior) = REQUIRED];

      // Required. The token endpoint provided by the 3rd party platform to
      // exchange an access token.
      string token_endpoint = 3 [(google.api.field_behavior) = REQUIRED];

      // Optional. The OAuth scopes to grant.
      repeated string scopes = 4 [(google.api.field_behavior) = OPTIONAL];
    }

    // Indicate the auth token type generated from the [Diglogflow service
    // agent](https://cloud.google.com/iam/docs/service-agents#dialogflow-service-agent).
    enum ServiceAgentAuth {
      // Service agent auth type unspecified. Default to ID_TOKEN.
      SERVICE_AGENT_AUTH_UNSPECIFIED = 0;

      // No token used.
      NONE = 1;

      // Use [ID
      // token](https://cloud.google.com/docs/authentication/token-types#id)
      // generated from service agent. This can be used to access Cloud Function
      // and Cloud Run after you grant Invoker role to
      // `service-<PROJECT-NUMBER>@gcp-sa-dialogflow.iam.gserviceaccount.com`.
      ID_TOKEN = 2;

      // Use [access
      // token](https://cloud.google.com/docs/authentication/token-types#access)
      // generated from service agent. This can be used to access other Google
      // Cloud APIs after you grant required roles to
      // `service-<PROJECT-NUMBER>@gcp-sa-dialogflow.iam.gserviceaccount.com`.
      ACCESS_TOKEN = 3;
    }

    // Represents the type of webhook configuration.
    enum WebhookType {
      // Default value. This value is unused.
      WEBHOOK_TYPE_UNSPECIFIED = 0;

      // Represents a standard webhook.
      STANDARD = 1;

      // Represents a flexible webhook.
      FLEXIBLE = 2;
    }

    // HTTP method to use when calling webhooks.
    enum HttpMethod {
      // HTTP method not specified.
      HTTP_METHOD_UNSPECIFIED = 0;

      // HTTP POST Method.
      POST = 1;

      // HTTP GET Method.
      GET = 2;

      // HTTP HEAD Method.
      HEAD = 3;

      // HTTP PUT Method.
      PUT = 4;

      // HTTP DELETE Method.
      DELETE = 5;

      // HTTP PATCH Method.
      PATCH = 6;

      // HTTP OPTIONS Method.
      OPTIONS = 7;
    }

    // Required. The webhook URI for receiving POST requests. It must use https
    // protocol.
    string uri = 1 [(google.api.field_behavior) = REQUIRED];

    // The user name for HTTP Basic authentication.
    string username = 2 [deprecated = true];

    // The password for HTTP Basic authentication.
    string password = 3 [deprecated = true];

    // The HTTP request headers to send together with webhook
    // requests.
    map<string, string> request_headers = 4;

    // Optional. Specifies a list of allowed custom CA certificates (in DER
    // format) for HTTPS verification. This overrides the default SSL trust
    // store. If this is empty or unspecified, Dialogflow will use Google's
    // default trust store to verify certificates. N.B. Make sure the HTTPS
    // server certificates are signed with "subject alt name". For instance a
    // certificate can be self-signed using the following command,
    // ```
    //    openssl x509 -req -days 200 -in example.com.csr \
    //      -signkey example.com.key \
    //      -out example.com.crt \
    //      -extfile <(printf "\nsubjectAltName='DNS:www.example.com'")
    // ```
    repeated bytes allowed_ca_certs = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The OAuth configuration of the webhook. If specified,
    // Dialogflow will initiate the OAuth client credential flow to exchange an
    // access token from the 3rd party platform and put it in the auth header.
    OAuthConfig oauth_config = 11 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Indicate the auth token type generated from the [Diglogflow
    // service
    // agent](https://cloud.google.com/iam/docs/service-agents#dialogflow-service-agent).
    // The generated token is sent in the Authorization header.
    ServiceAgentAuth service_agent_auth = 12
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Type of the webhook.
    WebhookType webhook_type = 6 [(google.api.field_behavior) = OPTIONAL];

    // Optional. HTTP method for the flexible webhook calls. Standard webhook
    // always uses POST.
    HttpMethod http_method = 7 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Defines a custom JSON object as request body to send to
    // flexible webhook.
    string request_body = 8 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Maps the values extracted from specific fields of the flexible
    // webhook response into session parameters.
    // - Key: session parameter name
    // - Value: field path in the webhook response
    map<string, string> parameter_mapping = 9
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Represents configuration for a [Service
  // Directory](https://cloud.google.com/service-directory) service.
  message ServiceDirectoryConfig {
    // Required. The name of [Service
    // Directory](https://cloud.google.com/service-directory) service.
    // Format:
    // `projects/<ProjectID>/locations/<LocationID>/namespaces/<NamespaceID>/services/<ServiceID>`.
    // `Location ID` of the service directory must be the same as the location
    // of the agent.
    string service = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "servicedirectory.googleapis.com/Service"
      }
    ];

    // Generic Service configuration of this webhook.
    GenericWebService generic_web_service = 2;
  }

  // The unique identifier of the webhook.
  // Required for the
  // [Webhooks.UpdateWebhook][google.cloud.dialogflow.cx.v3.Webhooks.UpdateWebhook]
  // method.
  // [Webhooks.CreateWebhook][google.cloud.dialogflow.cx.v3.Webhooks.CreateWebhook]
  // populates the name automatically. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/webhooks/<WebhookID>`.
  string name = 1;

  // Required. The human-readable name of the webhook, unique within the agent.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The webhook configuration.
  oneof webhook {
    // Configuration for a generic web service.
    GenericWebService generic_web_service = 4;

    // Configuration for a [Service
    // Directory](https://cloud.google.com/service-directory) service.
    ServiceDirectoryConfig service_directory = 7;
  }

  // Webhook execution timeout. Execution is considered failed if Dialogflow
  // doesn't receive a response from webhook at the end of the timeout period.
  // Defaults to 5 seconds, maximum allowed timeout is 30 seconds.
  google.protobuf.Duration timeout = 6;

  // Indicates whether the webhook is disabled.
  bool disabled = 5;
}

// The request message for
// [Webhooks.ListWebhooks][google.cloud.dialogflow.cx.v3.Webhooks.ListWebhooks].
message ListWebhooksRequest {
  // Required. The agent to list all webhooks for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Webhook"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Webhooks.ListWebhooks][google.cloud.dialogflow.cx.v3.Webhooks.ListWebhooks].
message ListWebhooksResponse {
  // The list of webhooks. There will be a maximum number of items returned
  // based on the page_size field in the request.
  repeated Webhook webhooks = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Webhooks.GetWebhook][google.cloud.dialogflow.cx.v3.Webhooks.GetWebhook].
message GetWebhookRequest {
  // Required. The name of the webhook.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/webhooks/<WebhookID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Webhook"
    }
  ];
}

// The request message for
// [Webhooks.CreateWebhook][google.cloud.dialogflow.cx.v3.Webhooks.CreateWebhook].
message CreateWebhookRequest {
  // Required. The agent to create a webhook for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Webhook"
    }
  ];

  // Required. The webhook to create.
  Webhook webhook = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Webhooks.UpdateWebhook][google.cloud.dialogflow.cx.v3.Webhooks.UpdateWebhook].
message UpdateWebhookRequest {
  // Required. The webhook to update.
  Webhook webhook = 1 [(google.api.field_behavior) = REQUIRED];

  // The mask to control which fields get updated. If the mask is not present,
  // all fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request message for
// [Webhooks.DeleteWebhook][google.cloud.dialogflow.cx.v3.Webhooks.DeleteWebhook].
message DeleteWebhookRequest {
  // Required. The name of the webhook to delete.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/webhooks/<WebhookID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Webhook"
    }
  ];

  // This field has no effect for webhook not being used.
  // For webhooks that are used by pages/flows/transition route groups:
  //
  // *  If `force` is set to false, an error will be returned with message
  //    indicating the referenced resources.
  // *  If `force` is set to true, Dialogflow will remove the webhook, as well
  //    as any references to the webhook (i.e.
  //    [Webhook][google.cloud.dialogflow.cx.v3.Fulfillment.webhook] and
  //    [tag][google.cloud.dialogflow.cx.v3.Fulfillment.tag]in fulfillments that
  //    point to this webhook will be removed).
  bool force = 2;
}

// The request message for a webhook call. The request is sent as a JSON object
// and the field names will be presented in camel cases.
//
// You may see undocumented fields in an actual request. These fields are used
// internally by Dialogflow and should be ignored.
message WebhookRequest {
  // Represents fulfillment information communicated to the webhook.
  message FulfillmentInfo {
    // Always present.
    // The value of the
    // [Fulfillment.tag][google.cloud.dialogflow.cx.v3.Fulfillment.tag] field
    // will be populated in this field by Dialogflow when the associated webhook
    // is called. The tag is typically used by the webhook service to identify
    // which fulfillment is being called, but it could be used for other
    // purposes.
    string tag = 1;
  }

  // Represents intent information communicated to the webhook.
  message IntentInfo {
    // Represents a value for an intent parameter.
    message IntentParameterValue {
      // Always present. Original text value extracted from user utterance.
      string original_value = 1;

      // Always present. Structured value for the parameter extracted from user
      // utterance.
      google.protobuf.Value resolved_value = 2;
    }

    // Always present. The unique identifier of the last matched
    // [intent][google.cloud.dialogflow.cx.v3.Intent].
    // Format:
    // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/intents/<IntentID>`.
    string last_matched_intent = 1 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Intent"
    }];

    // Always present. The display name of the last matched
    // [intent][google.cloud.dialogflow.cx.v3.Intent].
    string display_name = 3;

    // Parameters identified as a result of intent matching. This is a map of
    // the name of the identified parameter to the value of the parameter
    // identified from the user's utterance. All parameters defined in the
    // matched intent that are identified will be surfaced here.
    map<string, IntentParameterValue> parameters = 2;

    // The confidence of the matched intent. Values range from 0.0 (completely
    // uncertain) to 1.0 (completely certain).
    float confidence = 4;
  }

  // Represents the result of sentiment analysis.
  message SentimentAnalysisResult {
    // Sentiment score between -1.0 (negative sentiment) and 1.0 (positive
    //  sentiment).
    float score = 1;

    // A non-negative number in the [0, +inf) range, which represents the
    // absolute magnitude of sentiment, regardless of score (positive or
    // negative).
    float magnitude = 2;
  }

  // Always present. The unique identifier of the
  // [DetectIntentResponse][google.cloud.dialogflow.cx.v3.DetectIntentResponse]
  // that will be returned to the API caller.
  string detect_intent_response_id = 1;

  // The original conversational query.
  oneof query {
    // If [natural language text][google.cloud.dialogflow.cx.v3.TextInput] was
    // provided as input, this field will contain a copy of the text.
    string text = 10;

    // If an [intent][google.cloud.dialogflow.cx.v3.IntentInput] was provided as
    // input, this field will contain a copy of the intent identifier. Format:
    // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/intents/<IntentID>`.
    string trigger_intent = 11 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Intent"
    }];

    // If [natural language speech
    // audio][google.cloud.dialogflow.cx.v3.AudioInput] was provided as input,
    // this field will contain the transcript for the audio.
    string transcript = 12;

    // If an [event][google.cloud.dialogflow.cx.v3.EventInput] was provided as
    // input, this field will contain the name of the event.
    string trigger_event = 14;

    // If [DTMF][google.cloud.dialogflow.cx.v3.DtmfInput] was provided as input,
    // this field will contain the DTMF digits.
    string dtmf_digits = 17;
  }

  // The language code specified in the [original
  // request][QueryInput.language_code].
  string language_code = 15;

  // Always present. Information about the fulfillment that triggered this
  // webhook call.
  FulfillmentInfo fulfillment_info = 6;

  // Information about the last matched intent.
  IntentInfo intent_info = 3;

  // Information about page status.
  PageInfo page_info = 4;

  // Information about session status.
  SessionInfo session_info = 5;

  // The list of rich message responses to present to the user. Webhook can
  // choose to append or replace this list in
  // [WebhookResponse.fulfillment_response][google.cloud.dialogflow.cx.v3.WebhookResponse.fulfillment_response];
  repeated ResponseMessage messages = 7;

  // Custom data set in
  // [QueryParameters.payload][google.cloud.dialogflow.cx.v3.QueryParameters.payload].
  google.protobuf.Struct payload = 8;

  // The sentiment analysis result of the current user request. The field is
  // filled when sentiment analysis is configured to be enabled for the request.
  SentimentAnalysisResult sentiment_analysis_result = 9;

  // Information about the language of the request.
  LanguageInfo language_info = 18;
}

// The response message for a webhook call.
message WebhookResponse {
  // Represents a fulfillment response to the user.
  message FulfillmentResponse {
    // Defines merge behavior for `messages`.
    enum MergeBehavior {
      // Not specified. `APPEND` will be used.
      MERGE_BEHAVIOR_UNSPECIFIED = 0;

      // `messages` will be appended to the list of messages waiting to be sent
      // to the user.
      APPEND = 1;

      // `messages` will replace the list of messages waiting to be sent to the
      // user.
      REPLACE = 2;
    }

    // The list of rich message responses to present to the user.
    repeated ResponseMessage messages = 1;

    // Merge behavior for `messages`.
    MergeBehavior merge_behavior = 2;
  }

  // The fulfillment response to send to the user. This field can be omitted by
  // the webhook if it does not intend to send any response to the user.
  FulfillmentResponse fulfillment_response = 1;

  // Information about page status. This field can be omitted by the webhook if
  // it does not intend to modify page status.
  PageInfo page_info = 2;

  // Information about session status. This field can be omitted by the webhook
  // if it does not intend to modify session status.
  SessionInfo session_info = 3;

  // Value to append directly to
  // [QueryResult.webhook_payloads][google.cloud.dialogflow.cx.v3.QueryResult.webhook_payloads].
  google.protobuf.Struct payload = 4;

  // The target to transition to. This can be set optionally to indicate an
  // immediate transition to a different page in the same host flow, or a
  // different flow in the same agent.
  oneof transition {
    // The target page to transition to.
    // Format:
    // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/pages/<PageID>`.
    string target_page = 5 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Page"
    }];

    // The target flow to transition to.
    // Format:
    // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
    string target_flow = 6 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Flow"
    }];
  }
}

// Represents page information communicated to and from the webhook.
message PageInfo {
  // Represents form information.
  message FormInfo {
    // Represents parameter information.
    message ParameterInfo {
      // Represents the state of a parameter.
      enum ParameterState {
        // Not specified. This value should be never used.
        PARAMETER_STATE_UNSPECIFIED = 0;

        // Indicates that the parameter does not have a value.
        EMPTY = 1;

        // Indicates that the parameter value is invalid. This field can be used
        // by the webhook to invalidate the parameter and ask the server to
        // collect it from the user again.
        INVALID = 2;

        // Indicates that the parameter has a value.
        FILLED = 3;
      }

      // Always present for
      // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest].
      // Required for
      // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse].
      // The human-readable name of the parameter, unique within the form. This
      // field cannot be modified by the webhook.
      string display_name = 1;

      // Optional for both
      // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest] and
      // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse].
      // Indicates whether the parameter is required. Optional parameters will
      // not trigger prompts; however, they are filled if the user specifies
      // them. Required parameters must be filled before form filling concludes.
      bool required = 2;

      // Always present for
      // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest].
      // Required for
      // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse]. The
      // state of the parameter. This field can be set to
      // [INVALID][google.cloud.dialogflow.cx.v3.PageInfo.FormInfo.ParameterInfo.ParameterState.INVALID]
      // by the webhook to invalidate the parameter; other values set by the
      // webhook will be ignored.
      ParameterState state = 3;

      // Optional for both
      // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest] and
      // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse]. The
      // value of the parameter. This field can be set by the webhook to change
      // the parameter value.
      google.protobuf.Value value = 4;

      // Optional for
      // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest]. Ignored
      // for [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse].
      // Indicates if the parameter value was just collected on the last
      // conversation turn.
      bool just_collected = 5;
    }

    // Optional for both
    // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest] and
    // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse]. The
    // parameters contained in the form. Note that the webhook cannot add or
    // remove any form parameter.
    repeated ParameterInfo parameter_info = 2;
  }

  // Always present for
  // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest]. Ignored for
  // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse]. The
  // unique identifier of the current page. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/pages/<PageID>`.
  string current_page = 1 [
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Page" }
  ];

  // Always present for
  // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest]. Ignored for
  // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse]. The
  // display name of the current page.
  string display_name = 4;

  // Optional for both
  // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest] and
  // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse].
  // Information about the form.
  FormInfo form_info = 3;
}

// Represents session information communicated to and from the webhook.
message SessionInfo {
  // Always present for
  // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest]. Ignored for
  // [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse]. The
  // unique identifier of the
  // [session][google.cloud.dialogflow.cx.v3.DetectIntentRequest.session]. This
  // field can be used by the webhook to identify a session.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/sessions/<SessionID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/sessions/<SessionID>`
  // if environment is specified.
  string session = 1 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/Session"
  }];

  // Optional for
  // [WebhookRequest][google.cloud.dialogflow.cx.v3.WebhookRequest]. Optional
  // for [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse]. All
  // parameters collected from forms and intents during the session. Parameters
  // can be created, updated, or removed by the webhook. To remove a parameter
  // from the session, the webhook should explicitly set the parameter value to
  // null in [WebhookResponse][google.cloud.dialogflow.cx.v3.WebhookResponse].
  // The map is keyed by parameters' display names.
  map<string, google.protobuf.Value> parameters = 2;
}

// Represents the language information of the request.
message LanguageInfo {
  // The language code specified in the original
  // [request][google.cloud.dialogflow.cx.v3.QueryInput.language_code].
  string input_language_code = 1;

  // The language code detected for this request based on the user
  // conversation.
  string resolved_language_code = 2;

  // The confidence score of the detected language between 0 and 1.
  float confidence_score = 3;
}
