// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "ImportStrategyProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";

// Import strategies for the conflict resolution of resources (i.e. intents,
// entities, and webhooks) with identical display names during import
// operations.
enum ImportStrategy {
  // Unspecified. Treated as 'CREATE_NEW'.
  IMPORT_STRATEGY_UNSPECIFIED = 0;

  // Create a new resource with a numeric suffix appended to the end of the
  // existing display name.
  IMPORT_STRATEGY_CREATE_NEW = 1;

  // Replace existing resource with incoming resource in the content to be
  // imported.
  IMPORT_STRATEGY_REPLACE = 2;

  // Keep existing resource and discard incoming resource in the content to be
  // imported.
  IMPORT_STRATEGY_KEEP = 3;

  // Combine existing and incoming resources when a conflict is encountered.
  IMPORT_STRATEGY_MERGE = 4;

  // Throw error if a conflict is encountered.
  IMPORT_STRATEGY_THROW_ERROR = 5;
}
