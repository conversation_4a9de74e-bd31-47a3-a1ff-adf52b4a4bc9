// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

import "google/api/field_behavior.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "InlineProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";

// Inline destination for a Dialogflow operation that writes or exports objects
// (e.g. [intents][google.cloud.dialogflow.cx.v3.Intent]) outside of Dialogflow.
message InlineDestination {
  // Output only. The uncompressed byte content for the objects.
  // Only populated in responses.
  bytes content = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Inline source for a Dialogflow operation that reads or imports objects
// (e.g. [intents][google.cloud.dialogflow.cx.v3.Intent]) into Dialogflow.
message InlineSource {
  // The uncompressed byte content for the objects.
  bytes content = 1;
}
