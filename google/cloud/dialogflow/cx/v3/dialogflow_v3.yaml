type: google.api.Service
config_version: 3
name: dialogflow.googleapis.com
title: Dialogflow API

apis:
- name: google.cloud.dialogflow.cx.v3.Agents
- name: google.cloud.dialogflow.cx.v3.Changelogs
- name: google.cloud.dialogflow.cx.v3.Deployments
- name: google.cloud.dialogflow.cx.v3.EntityTypes
- name: google.cloud.dialogflow.cx.v3.Environments
- name: google.cloud.dialogflow.cx.v3.Experiments
- name: google.cloud.dialogflow.cx.v3.Flows
- name: google.cloud.dialogflow.cx.v3.Generators
- name: google.cloud.dialogflow.cx.v3.Intents
- name: google.cloud.dialogflow.cx.v3.Pages
- name: google.cloud.dialogflow.cx.v3.SecuritySettingsService
- name: google.cloud.dialogflow.cx.v3.SessionEntityTypes
- name: google.cloud.dialogflow.cx.v3.Sessions
- name: google.cloud.dialogflow.cx.v3.TestCases
- name: google.cloud.dialogflow.cx.v3.TransitionRouteGroups
- name: google.cloud.dialogflow.cx.v3.Versions
- name: google.cloud.dialogflow.cx.v3.Webhooks
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.dialogflow.cx.v3.BatchRunTestCasesMetadata
- name: google.cloud.dialogflow.cx.v3.BatchRunTestCasesResponse
- name: google.cloud.dialogflow.cx.v3.CreateVersionOperationMetadata
- name: google.cloud.dialogflow.cx.v3.DeployFlowMetadata
- name: google.cloud.dialogflow.cx.v3.DeployFlowResponse
- name: google.cloud.dialogflow.cx.v3.ExportAgentResponse
- name: google.cloud.dialogflow.cx.v3.ExportEntityTypesMetadata
- name: google.cloud.dialogflow.cx.v3.ExportEntityTypesResponse
- name: google.cloud.dialogflow.cx.v3.ExportFlowResponse
- name: google.cloud.dialogflow.cx.v3.ExportIntentsMetadata
- name: google.cloud.dialogflow.cx.v3.ExportIntentsResponse
- name: google.cloud.dialogflow.cx.v3.ExportTestCasesMetadata
- name: google.cloud.dialogflow.cx.v3.ExportTestCasesResponse
- name: google.cloud.dialogflow.cx.v3.ImportEntityTypesMetadata
- name: google.cloud.dialogflow.cx.v3.ImportEntityTypesResponse
- name: google.cloud.dialogflow.cx.v3.ImportFlowResponse
- name: google.cloud.dialogflow.cx.v3.ImportIntentsMetadata
- name: google.cloud.dialogflow.cx.v3.ImportIntentsResponse
- name: google.cloud.dialogflow.cx.v3.ImportTestCasesMetadata
- name: google.cloud.dialogflow.cx.v3.ImportTestCasesResponse
- name: google.cloud.dialogflow.cx.v3.RunContinuousTestMetadata
- name: google.cloud.dialogflow.cx.v3.RunContinuousTestResponse
- name: google.cloud.dialogflow.cx.v3.RunTestCaseMetadata
- name: google.cloud.dialogflow.cx.v3.RunTestCaseResponse
- name: google.cloud.dialogflow.cx.v3.WebhookRequest
- name: google.cloud.dialogflow.cx.v3.WebhookResponse

documentation:
  summary: |-
    Builds conversational interfaces (for example, chatbots, and voice-powered
    apps and devices).
  overview: |-
    <!-- mdformat off(presubmit failing, mdformat is as well) --> Dialogflow is
    a natural language understanding platform that makes it easy
    to design and integrate a conversational user interface into your mobile
    app, web application, device, bot, interactive voice response system, and
    so on. Using Dialogflow, you can provide new and engaging ways for
    users to interact with your product.

    Dialogflow can analyze multiple types of input from your customers,
    including text or audio inputs (like from a phone or voice recording).
    It can also respond to your customers in a couple of ways, either through
    text or with synthetic speech.

    For more information, see the
    [Dialogflow documentation](https://cloud.google.com/dialogflow/docs).
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v3/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v3/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v3/{name=projects/*/operations/*}:cancel'
    additional_bindings:
    - post: '/v3/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v3/{name=projects/*/operations/*}'
    additional_bindings:
    - get: '/v3/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v3/{name=projects/*}/operations'
    additional_bindings:
    - get: '/v3/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.dialogflow.cx.v3.Agents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.cx.v3.Changelogs.GetChangelog
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.cx.v3.Changelogs.ListChangelogs
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.cx.v3.Deployments.GetDeployment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.cx.v3.Deployments.ListDeployments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.EntityTypes.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Environments.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Experiments.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Flows.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Generators.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Intents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Pages.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.SecuritySettingsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.SessionEntityTypes.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Sessions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.TestCases.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.TransitionRouteGroups.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Versions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.cx.v3.Webhooks.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
