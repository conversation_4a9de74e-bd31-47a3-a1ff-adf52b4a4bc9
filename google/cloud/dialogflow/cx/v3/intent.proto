// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3/inline.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "IntentProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";

// Service for managing [Intents][google.cloud.dialogflow.cx.v3.Intent].
service Intents {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all intents in the specified agent.
  rpc ListIntents(ListIntentsRequest) returns (ListIntentsResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*/agents/*}/intents"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified intent.
  rpc GetIntent(GetIntentRequest) returns (Intent) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/intents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an intent in the specified agent.
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc CreateIntent(CreateIntentRequest) returns (Intent) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*/agents/*}/intents"
      body: "intent"
    };
    option (google.api.method_signature) = "parent,intent";
  }

  // Updates the specified intent.
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc UpdateIntent(UpdateIntentRequest) returns (Intent) {
    option (google.api.http) = {
      patch: "/v3/{intent.name=projects/*/locations/*/agents/*/intents/*}"
      body: "intent"
    };
    option (google.api.method_signature) = "intent,update_mask";
  }

  // Deletes the specified intent.
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc DeleteIntent(DeleteIntentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3/{name=projects/*/locations/*/agents/*/intents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Imports the specified intents into the agent.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [ImportIntentsMetadata][google.cloud.dialogflow.cx.v3.ImportIntentsMetadata]
  // - `response`:
  // [ImportIntentsResponse][google.cloud.dialogflow.cx.v3.ImportIntentsResponse]
  rpc ImportIntents(ImportIntentsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*/agents/*}/intents:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ImportIntentsResponse"
      metadata_type: "ImportIntentsMetadata"
    };
  }

  // Exports the selected intents.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [ExportIntentsMetadata][google.cloud.dialogflow.cx.v3.ExportIntentsMetadata]
  // - `response`:
  // [ExportIntentsResponse][google.cloud.dialogflow.cx.v3.ExportIntentsResponse]
  rpc ExportIntents(ExportIntentsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*/agents/*}/intents:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportIntentsResponse"
      metadata_type: "ExportIntentsMetadata"
    };
  }
}

// An intent represents a user's intent to interact with a conversational agent.
//
// You can provide information for the Dialogflow API to use to match user input
// to an intent by adding training phrases (i.e., examples of user input) to
// your intent.
message Intent {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Intent"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/intents/{intent}"
  };

  // Represents an example that the agent is trained on to identify the intent.
  message TrainingPhrase {
    // Represents a part of a training phrase.
    message Part {
      // Required. The text for this part.
      string text = 1 [(google.api.field_behavior) = REQUIRED];

      // The [parameter][google.cloud.dialogflow.cx.v3.Intent.Parameter] used to
      // annotate this part of the training phrase. This field is required for
      // annotated parts of the training phrase.
      string parameter_id = 2;
    }

    // Output only. The unique identifier of the training phrase.
    string id = 1;

    // Required. The ordered list of training phrase parts.
    // The parts are concatenated in order to form the training phrase.
    //
    // Note: The API does not automatically annotate training phrases like the
    // Dialogflow Console does.
    //
    // Note: Do not forget to include whitespace at part boundaries, so the
    // training phrase is well formatted when the parts are concatenated.
    //
    // If the training phrase does not need to be annotated with parameters,
    // you just need a single part with only the
    // [Part.text][google.cloud.dialogflow.cx.v3.Intent.TrainingPhrase.Part.text]
    // field set.
    //
    // If you want to annotate the training phrase, you must create multiple
    // parts, where the fields of each part are populated in one of two ways:
    //
    // -   `Part.text` is set to a part of the phrase that has no parameters.
    // -   `Part.text` is set to a part of the phrase that you want to annotate,
    //     and the `parameter_id` field is set.
    repeated Part parts = 2 [(google.api.field_behavior) = REQUIRED];

    // Indicates how many times this example was added to the intent.
    int32 repeat_count = 3;
  }

  // Represents an intent parameter.
  message Parameter {
    // Required. The unique identifier of the parameter. This field
    // is used by [training
    // phrases][google.cloud.dialogflow.cx.v3.Intent.TrainingPhrase] to annotate
    // their [parts][google.cloud.dialogflow.cx.v3.Intent.TrainingPhrase.Part].
    string id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The entity type of the parameter.
    // Format:
    // `projects/-/locations/-/agents/-/entityTypes/<SystemEntityTypeID>` for
    // system entity types (for example,
    // `projects/-/locations/-/agents/-/entityTypes/sys.date`), or
    // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/entityTypes/<EntityTypeID>`
    // for developer entity types.
    string entity_type = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "dialogflow.googleapis.com/EntityType"
      }
    ];

    // Indicates whether the parameter represents a list of values.
    bool is_list = 3;

    // Indicates whether the parameter content should be redacted in log. If
    // redaction is enabled, the parameter content will be replaced by parameter
    // name during logging.
    // Note: the parameter content is subject to redaction if either parameter
    // level redaction or [entity type level
    // redaction][google.cloud.dialogflow.cx.v3.EntityType.redact] is enabled.
    bool redact = 4;
  }

  // The unique identifier of the intent.
  // Required for the
  // [Intents.UpdateIntent][google.cloud.dialogflow.cx.v3.Intents.UpdateIntent]
  // method.
  // [Intents.CreateIntent][google.cloud.dialogflow.cx.v3.Intents.CreateIntent]
  // populates the name automatically.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/intents/<IntentID>`.
  string name = 1;

  // Required. The human-readable name of the intent, unique within the agent.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // The collection of training phrases the agent is trained on to identify the
  // intent.
  repeated TrainingPhrase training_phrases = 3;

  // The collection of parameters associated with the intent.
  repeated Parameter parameters = 4;

  // The priority of this intent. Higher numbers represent higher
  // priorities.
  //
  // - If the supplied value is unspecified or 0, the service
  //   translates the value to 500,000, which corresponds to the
  //   `Normal` priority in the console.
  // - If the supplied value is negative, the intent is ignored
  //   in runtime detect intent requests.
  int32 priority = 5;

  // Indicates whether this is a fallback intent. Currently only default
  // fallback intent is allowed in the agent, which is added upon agent
  // creation.
  // Adding training phrases to fallback intent is useful in the case of
  // requests that are mistakenly matched, since training phrases assigned to
  // fallback intents act as negative examples that triggers no-match event.
  bool is_fallback = 6;

  // The key/value metadata to label an intent. Labels can contain
  // lowercase letters, digits and the symbols '-' and '_'. International
  // characters are allowed, including letters from unicase alphabets. Keys must
  // start with a letter. Keys and values can be no longer than 63 characters
  // and no more than 128 bytes.
  //
  // Prefix "sys-" is reserved for Dialogflow defined labels. Currently allowed
  // Dialogflow defined labels include:
  // * sys-head
  // * sys-contextual
  // The above labels do not require value. "sys-head" means the intent is a
  // head intent. "sys.contextual" means the intent is a contextual intent.
  map<string, string> labels = 7;

  // Human readable description for better understanding an intent like its
  // scope, content, result etc. Maximum character limit: 140 characters.
  string description = 8;
}

// The request message for
// [Intents.ListIntents][google.cloud.dialogflow.cx.v3.Intents.ListIntents].
message ListIntentsRequest {
  // Required. The agent to list all intents for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Intent"
    }
  ];

  // The language to list intents for. The following fields are language
  // dependent:
  //
  // *   `Intent.training_phrases.parts.text`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 2;

  // The resource view to apply to the returned intent.
  IntentView intent_view = 5;

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 3;

  // The next_page_token value returned from a previous list request.
  string page_token = 4;
}

// The response message for
// [Intents.ListIntents][google.cloud.dialogflow.cx.v3.Intents.ListIntents].
message ListIntentsResponse {
  // The list of intents. There will be a maximum number of items returned based
  // on the page_size field in the request.
  repeated Intent intents = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Intents.GetIntent][google.cloud.dialogflow.cx.v3.Intents.GetIntent].
message GetIntentRequest {
  // Required. The name of the intent.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/intents/<IntentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Intent"
    }
  ];

  // The language to retrieve the intent for. The following fields are language
  // dependent:
  //
  // *   `Intent.training_phrases.parts.text`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 2;
}

// The request message for
// [Intents.CreateIntent][google.cloud.dialogflow.cx.v3.Intents.CreateIntent].
message CreateIntentRequest {
  // Required. The agent to create an intent for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Intent"
    }
  ];

  // Required. The intent to create.
  Intent intent = 2 [(google.api.field_behavior) = REQUIRED];

  // The language of the following fields in `intent`:
  //
  // *   `Intent.training_phrases.parts.text`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 3;
}

// The request message for
// [Intents.UpdateIntent][google.cloud.dialogflow.cx.v3.Intents.UpdateIntent].
message UpdateIntentRequest {
  // Required. The intent to update.
  Intent intent = 1 [(google.api.field_behavior) = REQUIRED];

  // The language of the following fields in `intent`:
  //
  // *   `Intent.training_phrases.parts.text`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 2;

  // The mask to control which fields get updated. If the mask is not present,
  // all fields will be updated.
  google.protobuf.FieldMask update_mask = 3;
}

// The request message for
// [Intents.DeleteIntent][google.cloud.dialogflow.cx.v3.Intents.DeleteIntent].
message DeleteIntentRequest {
  // Required. The name of the intent to delete.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/intents/<IntentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Intent"
    }
  ];
}

// Represents the options for views of an intent.
// An intent can be a sizable object. Therefore, we provide a resource view that
// does not return training phrases in the response.
enum IntentView {
  // Not specified. Treated as INTENT_VIEW_FULL.
  INTENT_VIEW_UNSPECIFIED = 0;

  // Training phrases field is not populated in the response.
  INTENT_VIEW_PARTIAL = 1;

  // All fields are populated.
  INTENT_VIEW_FULL = 2;
}

// The request message for
// [Intents.ImportIntents][google.cloud.dialogflow.cx.v3.Intents.ImportIntents].
message ImportIntentsRequest {
  // Merge option when display name conflicts exist during import.
  enum MergeOption {
    // Unspecified. Should not be used.
    MERGE_OPTION_UNSPECIFIED = 0;

    // DEPRECATED: Please use
    // [REPORT_CONFLICT][ImportIntentsRequest.REPORT_CONFLICT] instead.
    // Fail the request if there are intents whose display names conflict with
    // the display names of intents in the agent.
    REJECT = 1 [deprecated = true];

    // Replace the original intent in the agent with the new intent when display
    // name conflicts exist.
    REPLACE = 2;

    // Merge the original intent with the new intent when display name conflicts
    // exist.
    MERGE = 3;

    // Create new intents with new display names to differentiate them from the
    // existing intents when display name conflicts exist.
    RENAME = 4;

    // Report conflict information if display names conflict is detected.
    // Otherwise, import intents.
    REPORT_CONFLICT = 5;

    // Keep the original intent and discard the conflicting new intent when
    // display name conflicts exist.
    KEEP = 6;
  }

  // Required. The agent to import the intents into.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Intent"
    }
  ];

  // Required. The intents to import.
  oneof intents {
    // The [Google Cloud Storage](https://cloud.google.com/storage/docs/) URI
    // to import intents from. The format of this URI must be
    // `gs://<bucket-name>/<object-name>`.
    //
    // Dialogflow performs a read operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have read permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string intents_uri = 2;

    // Uncompressed byte content of intents.
    InlineSource intents_content = 3;
  }

  // Merge option for importing intents. If not specified, `REJECT` is assumed.
  MergeOption merge_option = 4;
}

// The response message for
// [Intents.ImportIntents][google.cloud.dialogflow.cx.v3.Intents.ImportIntents].
message ImportIntentsResponse {
  // Conflicting resources detected during the import process. Only filled when
  // [REPORT_CONFLICT][ImportIntentsResponse.REPORT_CONFLICT] is set in the
  // request and there are conflicts in the display names.
  message ConflictingResources {
    // Display names of conflicting intents.
    repeated string intent_display_names = 1;

    // Display names of conflicting entities.
    repeated string entity_display_names = 2;
  }

  // The unique identifier of the imported intents.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/intents/<IntentID>`.
  repeated string intents = 1 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/Intent"
  }];

  // Info which resources have conflicts when
  // [REPORT_CONFLICT][ImportIntentsResponse.REPORT_CONFLICT] merge_option is
  // set in ImportIntentsRequest.
  ConflictingResources conflicting_resources = 2;
}

// Metadata returned for the
// [Intents.ImportIntents][google.cloud.dialogflow.cx.v3.Intents.ImportIntents]
// long running operation.
message ImportIntentsMetadata {}

// The request message for
// [Intents.ExportIntents][google.cloud.dialogflow.cx.v3.Intents.ExportIntents].
message ExportIntentsRequest {
  // Data format of the exported intents.
  enum DataFormat {
    // Unspecified format. Treated as `BLOB`.
    DATA_FORMAT_UNSPECIFIED = 0;

    // Intents will be exported as raw bytes.
    BLOB = 1;

    // Intents will be exported in JSON format.
    JSON = 2;

    // Intents will be exported in CSV format.
    CSV = 3;
  }

  // Required. The name of the parent agent to export intents.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Intent"
    }
  ];

  // Required. The name of the intents to export.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/intents/<IntentID>`.
  repeated string intents = 2 [(google.api.field_behavior) = REQUIRED];

  // The destination to export.
  oneof destination {
    // Optional. The [Google Cloud
    // Storage](https://cloud.google.com/storage/docs/) URI to export the
    // intents to. The format of this URI must be
    // `gs://<bucket-name>/<object-name>`.
    //
    // Dialogflow performs a write operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have write permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string intents_uri = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The option to return the serialized intents inline.
    bool intents_content_inline = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The data format of the exported intents. If not specified, `BLOB`
  // is assumed.
  DataFormat data_format = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Intents.ExportIntents][google.cloud.dialogflow.cx.v3.Intents.ExportIntents].
message ExportIntentsResponse {
  // The exported intents.
  oneof intents {
    // The URI to a file containing the exported intents. This field is
    // populated only if `intents_uri` is specified in
    // [ExportIntentsRequest][google.cloud.dialogflow.cx.v3.ExportIntentsRequest].
    string intents_uri = 1;

    // Uncompressed byte content for intents. This field is populated only if
    // `intents_content_inline` is set to true in
    // [ExportIntentsRequest][google.cloud.dialogflow.cx.v3.ExportIntentsRequest].
    InlineDestination intents_content = 2;
  }
}

// Metadata returned for the
// [Intents.ExportIntents][google.cloud.dialogflow.cx.v3.Intents.ExportIntents]
// long running operation.
message ExportIntentsMetadata {}
