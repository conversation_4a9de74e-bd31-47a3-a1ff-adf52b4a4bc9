// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "ValidationMessageProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";

// Agent/flow validation message.
message ValidationMessage {
  // Resource types.
  enum ResourceType {
    // Unspecified.
    RESOURCE_TYPE_UNSPECIFIED = 0;

    // Agent.
    AGENT = 1;

    // Intent.
    INTENT = 2;

    // Intent training phrase.
    INTENT_TRAINING_PHRASE = 8;

    // Intent parameter.
    INTENT_PARAMETER = 9;

    // Multiple intents.
    INTENTS = 10;

    // Multiple training phrases.
    INTENT_TRAINING_PHRASES = 11;

    // Entity type.
    ENTITY_TYPE = 3;

    // Multiple entity types.
    ENTITY_TYPES = 12;

    // Webhook.
    WEBHOOK = 4;

    // Flow.
    FLOW = 5;

    // Page.
    PAGE = 6;

    // Multiple pages.
    PAGES = 13;

    // Transition route group.
    TRANSITION_ROUTE_GROUP = 7;

    // Agent transition route group.
    AGENT_TRANSITION_ROUTE_GROUP = 14;
  }

  // Severity level.
  enum Severity {
    // Unspecified.
    SEVERITY_UNSPECIFIED = 0;

    // The agent doesn't follow Dialogflow best practices.
    INFO = 1;

    // The agent may not behave as expected.
    WARNING = 2;

    // The agent may experience failures.
    ERROR = 3;
  }

  // The type of the resources where the message is found.
  ResourceType resource_type = 1;

  // The names of the resources where the message is found.
  repeated string resources = 2 [deprecated = true];

  // The resource names of the resources where the message is found.
  repeated ResourceName resource_names = 6;

  // Indicates the severity of the message.
  Severity severity = 3;

  // The message detail.
  string detail = 4;
}

// Resource name and display name.
message ResourceName {
  // Name.
  string name = 1;

  // Display name.
  string display_name = 2;
}
