// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3/advanced_settings.proto";
import "google/cloud/dialogflow/cx/v3/import_strategy.proto";
import "google/cloud/dialogflow/cx/v3/page.proto";
import "google/cloud/dialogflow/cx/v3/validation_message.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "FlowProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";

// Service for managing [Flows][google.cloud.dialogflow.cx.v3.Flow].
service Flows {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Creates a flow in the specified agent.
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc CreateFlow(CreateFlowRequest) returns (Flow) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*/agents/*}/flows"
      body: "flow"
    };
    option (google.api.method_signature) = "parent,flow";
  }

  // Deletes a specified flow.
  rpc DeleteFlow(DeleteFlowRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3/{name=projects/*/locations/*/agents/*/flows/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the list of all flows in the specified agent.
  rpc ListFlows(ListFlowsRequest) returns (ListFlowsResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*/agents/*}/flows"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified flow.
  rpc GetFlow(GetFlowRequest) returns (Flow) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/flows/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the specified flow.
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc UpdateFlow(UpdateFlowRequest) returns (Flow) {
    option (google.api.http) = {
      patch: "/v3/{flow.name=projects/*/locations/*/agents/*/flows/*}"
      body: "flow"
    };
    option (google.api.method_signature) = "flow,update_mask";
  }

  // Trains the specified flow. Note that only the flow in 'draft' environment
  // is trained.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc TrainFlow(TrainFlowRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*/locations/*/agents/*/flows/*}:train"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Validates the specified flow and creates or updates validation results.
  // Please call this API after the training is completed to get the complete
  // validation results.
  rpc ValidateFlow(ValidateFlowRequest) returns (FlowValidationResult) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*/locations/*/agents/*/flows/*}:validate"
      body: "*"
    };
  }

  // Gets the latest flow validation result. Flow validation is performed
  // when ValidateFlow is called.
  rpc GetFlowValidationResult(GetFlowValidationResultRequest)
      returns (FlowValidationResult) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/flows/*/validationResult}"
    };
    option (google.api.method_signature) = "name";
  }

  // Imports the specified flow to the specified agent from a binary file.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`:
  // [ImportFlowResponse][google.cloud.dialogflow.cx.v3.ImportFlowResponse]
  //
  // Note: You should always train a flow prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/cx/docs/concept/training).
  rpc ImportFlow(ImportFlowRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*/agents/*}/flows:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ImportFlowResponse"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Exports the specified flow to a binary file.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`:
  // [ExportFlowResponse][google.cloud.dialogflow.cx.v3.ExportFlowResponse]
  //
  // Note that resources (e.g. intents, entities, webhooks) that the flow
  // references will also be exported.
  rpc ExportFlow(ExportFlowRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*/locations/*/agents/*/flows/*}:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportFlowResponse"
      metadata_type: "google.protobuf.Struct"
    };
  }
}

// Settings related to NLU.
message NluSettings {
  // NLU model type.
  enum ModelType {
    // Not specified. `MODEL_TYPE_STANDARD` will be used.
    MODEL_TYPE_UNSPECIFIED = 0;

    // Use standard NLU model.
    MODEL_TYPE_STANDARD = 1;

    // Use advanced NLU model.
    MODEL_TYPE_ADVANCED = 3;
  }

  // NLU model training mode.
  enum ModelTrainingMode {
    // Not specified. `MODEL_TRAINING_MODE_AUTOMATIC` will be used.
    MODEL_TRAINING_MODE_UNSPECIFIED = 0;

    // NLU model training is automatically triggered when a flow gets modified.
    // User can also manually trigger model training in this mode.
    MODEL_TRAINING_MODE_AUTOMATIC = 1;

    // User needs to manually trigger NLU model training. Best for large flows
    // whose models take long time to train.
    MODEL_TRAINING_MODE_MANUAL = 2;
  }

  // Indicates the type of NLU model.
  ModelType model_type = 1;

  // To filter out false positive results and still get variety in matched
  // natural language inputs for your agent, you can tune the machine learning
  // classification threshold. If the returned score value is less than the
  // threshold value, then a no-match event will be triggered. The score values
  // range from 0.0 (completely uncertain) to 1.0 (completely certain). If set
  // to 0.0, the default of 0.3 is used. You can set a separate classification
  // threshold for the flow in each language enabled for the agent.
  float classification_threshold = 3;

  // Indicates NLU model training mode.
  ModelTrainingMode model_training_mode = 4;
}

// Flows represents the conversation flows when you build your chatbot agent.
//
// A flow consists of many pages connected by the transition routes.
// Conversations always start with the built-in Start Flow (with an all-0 ID).
// Transition routes can direct the conversation session from the current flow
// (parent flow) to another flow (sub flow). When the sub flow is finished,
// Dialogflow will bring the session back to the parent flow, where the sub flow
// is started.
//
// Usually, when a transition route is followed by a matched intent, the intent
// will be "consumed". This means the intent won't activate more transition
// routes. However, when the followed transition route moves the conversation
// session into a different flow, the matched intent can be carried over and to
// be consumed in the target flow.
message Flow {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Flow"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/flows/{flow}"
  };

  // Settings for multi-lingual agents.
  message MultiLanguageSettings {
    // Optional. Enable multi-language detection for this flow. This can be set
    // only if [agent level multi language
    // setting][Agent.enable_multi_language_training] is enabled.
    bool enable_multi_language_detection = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Agent will respond in the detected language if the detected
    // language code is in the supported resolved languages for this flow. This
    // will be used only if multi-language training is enabled in the
    // [agent][google.cloud.dialogflow.cx.v3.Agent.enable_multi_language_training]
    // and multi-language detection is enabled in the
    // [flow][google.cloud.dialogflow.cx.v3.Flow.MultiLanguageSettings.enable_multi_language_detection].
    // The supported languages must be a subset of the languages supported by
    // the agent.
    repeated string supported_response_language_codes = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // The unique identifier of the flow.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string name = 1;

  // Required. The human-readable name of the flow.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // The description of the flow. The maximum length is 500 characters. If
  // exceeded, the request is rejected.
  string description = 3;

  // A flow's transition routes serve two purposes:
  //
  // *   They are responsible for matching the user's first utterances in the
  // flow.
  // *   They are inherited by every page's [transition
  // routes][Page.transition_routes] and can support use cases such as the user
  // saying "help" or "can I talk to a human?", which can be handled in a common
  // way regardless of the current page. Transition routes defined in the page
  // have higher priority than those defined in the flow.
  //
  // TransitionRoutes are evalauted in the following order:
  //
  // *   TransitionRoutes with intent specified.
  // *   TransitionRoutes with only condition specified.
  //
  // TransitionRoutes with intent specified are inherited by pages in the flow.
  repeated TransitionRoute transition_routes = 4;

  // A flow's event handlers serve two purposes:
  //
  // *   They are responsible for handling events (e.g. no match,
  // webhook errors) in the flow.
  // *   They are inherited by every page's [event
  // handlers][Page.event_handlers], which can be used to handle common events
  // regardless of the current page. Event handlers defined in the page
  // have higher priority than those defined in the flow.
  //
  // Unlike
  // [transition_routes][google.cloud.dialogflow.cx.v3.Flow.transition_routes],
  // these handlers are evaluated on a first-match basis. The first one that
  // matches the event get executed, with the rest being ignored.
  repeated EventHandler event_handlers = 10;

  // A flow's transition route group serve two purposes:
  //
  // *   They are responsible for matching the user's first utterances in the
  // flow.
  // *   They are inherited by every page's [transition
  // route groups][Page.transition_route_groups]. Transition route groups
  // defined in the page have higher priority than those defined in the flow.
  //
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/transitionRouteGroups/<TransitionRouteGroupID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/transitionRouteGroups/<TransitionRouteGroupID>`
  // for agent-level groups.
  repeated string transition_route_groups = 15
      [(google.api.resource_reference) = {
        type: "dialogflow.googleapis.com/TransitionRouteGroup"
      }];

  // NLU related settings of the flow.
  NluSettings nlu_settings = 11;

  // Hierarchical advanced settings for this flow. The settings exposed at the
  // lower level overrides the settings exposed at the higher level.
  AdvancedSettings advanced_settings = 14;

  // Optional. Knowledge connector configuration.
  KnowledgeConnectorSettings knowledge_connector_settings = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Multi-lingual agent settings for this flow.
  MultiLanguageSettings multi_language_settings = 28
      [(google.api.field_behavior) = OPTIONAL];

  // Indicates whether the flow is locked for changes. If the flow is locked,
  // modifications to the flow will be rejected.
  bool locked = 30;
}

// The request message for
// [Flows.CreateFlow][google.cloud.dialogflow.cx.v3.Flows.CreateFlow].
message CreateFlowRequest {
  // Required. The agent to create a flow for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Flow"
    }
  ];

  // Required. The flow to create.
  Flow flow = 2 [(google.api.field_behavior) = REQUIRED];

  // The language of the following fields in `flow`:
  //
  // *  `Flow.event_handlers.trigger_fulfillment.messages`
  // *  `Flow.event_handlers.trigger_fulfillment.conditional_cases`
  // *  `Flow.transition_routes.trigger_fulfillment.messages`
  // *  `Flow.transition_routes.trigger_fulfillment.conditional_cases`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 3;
}

// The request message for
// [Flows.DeleteFlow][google.cloud.dialogflow.cx.v3.Flows.DeleteFlow].
message DeleteFlowRequest {
  // Required. The name of the flow to delete.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];

  // This field has no effect for flows with no incoming transitions.
  // For flows with incoming transitions:
  //
  // *  If `force` is set to false, an error will be returned with message
  //    indicating the incoming transitions.
  // *  If `force` is set to true, Dialogflow will remove the flow, as well as
  //    any transitions to the flow (i.e. [Target
  //    flow][EventHandler.target_flow] in event handlers or [Target
  //    flow][TransitionRoute.target_flow] in transition routes that point to
  //    this flow will be cleared).
  bool force = 2;
}

// The request message for
// [Flows.ListFlows][google.cloud.dialogflow.cx.v3.Flows.ListFlows].
message ListFlowsRequest {
  // Required. The agent containing the flows.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Flow"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;

  // The language to list flows for. The following fields are language
  // dependent:
  //
  // *  `Flow.event_handlers.trigger_fulfillment.messages`
  // *  `Flow.event_handlers.trigger_fulfillment.conditional_cases`
  // *  `Flow.transition_routes.trigger_fulfillment.messages`
  // *  `Flow.transition_routes.trigger_fulfillment.conditional_cases`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 4;
}

// The response message for
// [Flows.ListFlows][google.cloud.dialogflow.cx.v3.Flows.ListFlows].
message ListFlowsResponse {
  // The list of flows. There will be a maximum number of items returned based
  // on the page_size field in the request.
  repeated Flow flows = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The response message for
// [Flows.GetFlow][google.cloud.dialogflow.cx.v3.Flows.GetFlow].
message GetFlowRequest {
  // Required. The name of the flow to get.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];

  // The language to retrieve the flow for. The following fields are language
  // dependent:
  //
  // *  `Flow.event_handlers.trigger_fulfillment.messages`
  // *  `Flow.event_handlers.trigger_fulfillment.conditional_cases`
  // *  `Flow.transition_routes.trigger_fulfillment.messages`
  // *  `Flow.transition_routes.trigger_fulfillment.conditional_cases`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 2;
}

// The request message for
// [Flows.UpdateFlow][google.cloud.dialogflow.cx.v3.Flows.UpdateFlow].
message UpdateFlowRequest {
  // Required. The flow to update.
  Flow flow = 1 [(google.api.field_behavior) = REQUIRED];

  // The mask to control which fields get updated. If the mask is not present,
  // all fields will be updated.
  google.protobuf.FieldMask update_mask = 2;

  // The language of the following fields in `flow`:
  //
  // *  `Flow.event_handlers.trigger_fulfillment.messages`
  // *  `Flow.event_handlers.trigger_fulfillment.conditional_cases`
  // *  `Flow.transition_routes.trigger_fulfillment.messages`
  // *  `Flow.transition_routes.trigger_fulfillment.conditional_cases`
  //
  // If not specified, the agent's default language is used.
  // [Many
  // languages](https://cloud.google.com/dialogflow/cx/docs/reference/language)
  // are supported.
  // Note: languages must be enabled in the agent before they can be used.
  string language_code = 3;
}

// The request message for
// [Flows.TrainFlow][google.cloud.dialogflow.cx.v3.Flows.TrainFlow].
message TrainFlowRequest {
  // Required. The flow to train.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];
}

// The request message for
// [Flows.ValidateFlow][google.cloud.dialogflow.cx.v3.Flows.ValidateFlow].
message ValidateFlowRequest {
  // Required. The flow to validate.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];

  // If not specified, the agent's default language is used.
  string language_code = 2;
}

// The request message for
// [Flows.GetFlowValidationResult][google.cloud.dialogflow.cx.v3.Flows.GetFlowValidationResult].
message GetFlowValidationResultRequest {
  // Required. The flow name.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/validationResult`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/FlowValidationResult"
    }
  ];

  // If not specified, the agent's default language is used.
  string language_code = 2;
}

// The response message for
// [Flows.GetFlowValidationResult][google.cloud.dialogflow.cx.v3.Flows.GetFlowValidationResult].
message FlowValidationResult {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/FlowValidationResult"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/flows/{flow}/validationResult"
  };

  // The unique identifier of the flow validation result.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/validationResult`.
  string name = 1;

  // Contains all validation messages.
  repeated ValidationMessage validation_messages = 2;

  // Last time the flow was validated.
  google.protobuf.Timestamp update_time = 3;
}

// The request message for
// [Flows.ImportFlow][google.cloud.dialogflow.cx.v3.Flows.ImportFlow].
message ImportFlowRequest {
  // Import option.
  enum ImportOption {
    // Unspecified. Treated as `KEEP`.
    IMPORT_OPTION_UNSPECIFIED = 0;

    // Always respect settings in exported flow content. It may cause a
    // import failure if some settings (e.g. custom NLU) are not supported in
    // the agent to import into.
    KEEP = 1;

    // Fallback to default settings if some settings are not supported in the
    // agent to import into. E.g. Standard NLU will be used if custom NLU is
    // not available.
    FALLBACK = 2;
  }

  // Required. The agent to import the flow into.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Flow"
    }
  ];

  // Required. The flow to import.
  oneof flow {
    // The [Google Cloud Storage](https://cloud.google.com/storage/docs/) URI
    // to import flow from. The format of this URI must be
    // `gs://<bucket-name>/<object-name>`.
    //
    // Dialogflow performs a read operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have read permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string flow_uri = 2;

    // Uncompressed raw byte content for flow.
    bytes flow_content = 3;
  }

  // Flow import mode. If not specified, `KEEP` is assumed.
  ImportOption import_option = 4;

  // Optional. Specifies the import strategy used when resolving resource
  // conflicts.
  FlowImportStrategy flow_import_strategy = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// The flow import strategy used for resource conflict resolution associated
// with an [ImportFlowRequest][google.cloud.dialogflow.cx.v3.ImportFlowRequest].
message FlowImportStrategy {
  // Optional. Import strategy for resource conflict resolution, applied
  // globally throughout the flow. It will be applied for all display name
  // conflicts in the imported content. If not specified, 'CREATE_NEW' is
  // assumed.
  ImportStrategy global_import_strategy = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Flows.ImportFlow][google.cloud.dialogflow.cx.v3.Flows.ImportFlow].
message ImportFlowResponse {
  // The unique identifier of the new flow.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string flow = 1 [
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];
}

// The request message for
// [Flows.ExportFlow][google.cloud.dialogflow.cx.v3.Flows.ExportFlow].
message ExportFlowRequest {
  // Required. The name of the flow to export.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];

  // Optional. The [Google Cloud
  // Storage](https://cloud.google.com/storage/docs/) URI to export the flow to.
  // The format of this URI must be `gs://<bucket-name>/<object-name>`. If left
  // unspecified, the serialized flow is returned inline.
  //
  // Dialogflow performs a write operation for the Cloud Storage object
  // on the caller's behalf, so your request authentication must
  // have write permissions for the object. For more information, see
  // [Dialogflow access
  // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
  string flow_uri = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether to export flows referenced by the specified flow.
  bool include_referenced_flows = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Flows.ExportFlow][google.cloud.dialogflow.cx.v3.Flows.ExportFlow].
message ExportFlowResponse {
  // The exported flow.
  oneof flow {
    // The URI to a file containing the exported flow. This field is populated
    // only if `flow_uri` is specified in
    // [ExportFlowRequest][google.cloud.dialogflow.cx.v3.ExportFlowRequest].
    string flow_uri = 1;

    // Uncompressed raw byte content for flow.
    bytes flow_content = 2;
  }
}
