# This build file includes a target for the Ruby wrapper library for
# google-cloud-dialogflow-cx.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for dialogflow.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v3 in this case.
ruby_cloud_gapic_library(
    name = "dialogflow_ruby_wrapper",
    srcs = ["//google/cloud/dialogflow/cx/v3:cx_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-dialogflow-cx",
        "ruby-cloud-env-prefix=DIALOGFLOW",
        "ruby-cloud-wrapper-of=v3:0.24",
        "ruby-cloud-product-url=https://cloud.google.com/dialogflow",
        "ruby-cloud-api-id=dialogflow.googleapis.com",
        "ruby-cloud-api-shortname=dialogflow",
        "ruby-cloud-namespace-override=Cx=CX",
    ],
    ruby_cloud_description = "Dialogflow is an end-to-end, build-once deploy-everywhere development suite for creating conversational interfaces for websites, mobile applications, popular messaging platforms, and IoT devices. You can use it to build interfaces (such as chatbots and conversational IVR) that enable natural and rich interactions between your users and your business. This client is for Dialogflow CX, providing an advanced agent type suitable for large or very complex agents.",
    ruby_cloud_title = "Dialogflow CX",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-dialogflow-cx-ruby",
    deps = [
        ":dialogflow_ruby_wrapper",
    ],
)
