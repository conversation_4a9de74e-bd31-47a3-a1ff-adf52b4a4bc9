// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "ConversationModelProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Manages a collection of models for human agent assistant.
service ConversationModels {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Creates a model.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [CreateConversationModelOperationMetadata][google.cloud.dialogflow.v2.CreateConversationModelOperationMetadata]
  // - `response`:
  // [ConversationModel][google.cloud.dialogflow.v2.ConversationModel]
  rpc CreateConversationModel(CreateConversationModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/conversationModels"
      body: "conversation_model"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/conversationModels"
        body: "conversation_model"
      }
    };
    option (google.api.method_signature) = "parent,conversation_model";
    option (google.longrunning.operation_info) = {
      response_type: "ConversationModel"
      metadata_type: "CreateConversationModelOperationMetadata"
    };
  }

  // Gets conversation model.
  rpc GetConversationModel(GetConversationModelRequest)
      returns (ConversationModel) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/conversationModels/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/conversationModels/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists conversation models.
  rpc ListConversationModels(ListConversationModelsRequest)
      returns (ListConversationModelsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/conversationModels"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*}/conversationModels"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a model.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [DeleteConversationModelOperationMetadata][google.cloud.dialogflow.v2.DeleteConversationModelOperationMetadata]
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  rpc DeleteConversationModel(DeleteConversationModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/conversationModels/*}"
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/conversationModels/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteConversationModelOperationMetadata"
    };
  }

  // Deploys a model. If a model is already deployed, deploying it
  // has no effect. A model can only serve prediction requests after it gets
  // deployed. For article suggestion, custom model will not be used unless
  // it is deployed.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [DeployConversationModelOperationMetadata][google.cloud.dialogflow.v2.DeployConversationModelOperationMetadata]
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  rpc DeployConversationModel(DeployConversationModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/conversationModels/*}:deploy"
      body: "*"
      additional_bindings {
        post: "/v2/{name=projects/*/locations/*/conversationModels/*}:deploy"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeployConversationModelOperationMetadata"
    };
  }

  // Undeploys a model. If the model is not deployed this method has no effect.
  // If the model is currently being used:
  //   - For article suggestion, article suggestion will fallback to the default
  //     model if model is undeployed.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [UndeployConversationModelOperationMetadata][google.cloud.dialogflow.v2.UndeployConversationModelOperationMetadata]
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  rpc UndeployConversationModel(UndeployConversationModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/conversationModels/*}:undeploy"
      body: "*"
      additional_bindings {
        post: "/v2/{name=projects/*/locations/*/conversationModels/*}:undeploy"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "UndeployConversationModelOperationMetadata"
    };
  }

  // Gets an evaluation of conversation model.
  rpc GetConversationModelEvaluation(GetConversationModelEvaluationRequest)
      returns (ConversationModelEvaluation) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/conversationModels/*/evaluations/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/conversationModels/*/evaluations/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists evaluations of a conversation model.
  rpc ListConversationModelEvaluations(ListConversationModelEvaluationsRequest)
      returns (ListConversationModelEvaluationsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/conversationModels/*}/evaluations"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*/conversationModels/*}/evaluations"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates evaluation of a conversation model.
  rpc CreateConversationModelEvaluation(
      CreateConversationModelEvaluationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*/conversationModels/*}/evaluations"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,conversation_model_evaluation";
    option (google.longrunning.operation_info) = {
      response_type: "ConversationModelEvaluation"
      metadata_type: "CreateConversationModelEvaluationOperationMetadata"
    };
  }
}

// Represents a conversation model.
message ConversationModel {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/ConversationModel"
    pattern: "projects/{project}/locations/{location}/conversationModels/{conversation_model}"
    pattern: "projects/{project}/conversationModels/{conversation_model}"
  };

  // State of the model.
  enum State {
    // Should not be used, an un-set enum has this value by default.
    STATE_UNSPECIFIED = 0;

    // Model being created.
    CREATING = 1;

    // Model is not deployed but ready to deploy.
    UNDEPLOYED = 2;

    // Model is deploying.
    DEPLOYING = 3;

    // Model is deployed and ready to use.
    DEPLOYED = 4;

    // Model is undeploying.
    UNDEPLOYING = 5;

    // Model is deleting.
    DELETING = 6;

    // Model is in error state. Not ready to deploy and use.
    FAILED = 7;

    // Model is being created but the training has not started,
    // The model may remain in this state until there is enough capacity to
    // start training.
    PENDING = 8;
  }

  // Model type.
  enum ModelType {
    // ModelType unspecified.
    MODEL_TYPE_UNSPECIFIED = 0;

    // ModelType smart reply dual encoder model.
    SMART_REPLY_DUAL_ENCODER_MODEL = 2;

    // ModelType smart reply bert model.
    SMART_REPLY_BERT_MODEL = 6;
  }

  // ConversationModel resource name. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model ID>`
  string name = 1;

  // Required. The display name of the model. At most 64 bytes long.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Creation time of this model.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Datasets used to create model.
  repeated InputDataset datasets = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. State of the model. A model can only serve prediction requests
  // after it gets deployed.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Language code for the conversation model. If not specified, the language
  // is en-US. Language at ConversationModel should be set for all non en-us
  // languages.
  // This should be a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt)
  // language tag. Example: "en-US".
  string language_code = 19;

  // Required.
  // The model metadata that is specific to the problem type.
  // Must match the metadata type of the dataset used to train the model.
  oneof model_metadata {
    // Metadata for article suggestion models.
    ArticleSuggestionModelMetadata article_suggestion_model_metadata = 8;

    // Metadata for smart reply models.
    SmartReplyModelMetadata smart_reply_model_metadata = 9;
  }

  // Output only. A read only boolean field reflecting Zone Separation
  // status of the model.
  optional bool satisfies_pzs = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A read only boolean field reflecting Zone Isolation status
  // of the model.
  optional bool satisfies_pzi = 26 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents evaluation result of a conversation model.
message ConversationModelEvaluation {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/ConversationModelEvaluation"
    pattern: "projects/{project}/conversationModels/{conversation_model}/evaluations/{evaluation}"
    pattern: "projects/{project}/locations/{location}/conversationModels/{conversation_model}/evaluations/{evaluation}"
  };

  // The resource name of the evaluation. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model
  // ID>/evaluations/<Evaluation ID>`
  string name = 1;

  // Optional. The display name of the model evaluation. At most 64 bytes long.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The configuration of the evaluation task.
  EvaluationConfig evaluation_config = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Creation time of this model.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Metrics details.
  oneof metrics {
    // Output only. Only available when model is for smart reply.
    SmartReplyMetrics smart_reply_metrics = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Human eval template in csv format.
  // It tooks real-world conversations provided through input dataset, generates
  // example suggestions for customer to verify quality of the model.
  // For Smart Reply, the generated csv file contains columns of
  // Context, (Suggestions,Q1,Q2)*3, Actual reply.
  // Context contains at most 10 latest messages in the conversation prior to
  // the current suggestion.
  // Q1: "Would you send it as the next message of agent?"
  // Evaluated based on whether the suggest is appropriate to be sent by
  // agent in current context.
  // Q2: "Does the suggestion move the conversation closer to resolution?"
  // Evaluated based on whether the suggestion provide solutions, or answers
  // customer's question or collect information from customer to resolve the
  // customer's issue.
  // Actual reply column contains the actual agent reply sent in the context.
  string raw_human_eval_template_csv = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The configuration for model evaluation.
message EvaluationConfig {
  // Smart reply specific configuration for evaluation job.
  message SmartReplyConfig {
    // The allowlist document resource name.
    // Format: `projects/<Project ID>/knowledgeBases/<Knowledge Base
    // ID>/documents/<Document ID>`. Only used for smart reply model.
    string allowlist_document = 1 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Document"
    }];

    // Required. The model to be evaluated can return multiple results with
    // confidence score on each query. These results will be sorted by the
    // descending order of the scores and we only keep the first
    // max_result_count results as the final results to evaluate.
    int32 max_result_count = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Smart compose specific configuration for evaluation job.
  message SmartComposeConfig {
    // The allowlist document resource name.
    // Format: `projects/<Project ID>/knowledgeBases/<Knowledge Base
    // ID>/documents/<Document ID>`. Only used for smart compose model.
    string allowlist_document = 1 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Document"
    }];

    // Required. The model to be evaluated can return multiple results with
    // confidence score on each query. These results will be sorted by the
    // descending order of the scores and we only keep the first
    // max_result_count results as the final results to evaluate.
    int32 max_result_count = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Datasets used for evaluation.
  repeated InputDataset datasets = 3 [(google.api.field_behavior) = REQUIRED];

  // Specific configurations for different models in order to do evaluation.
  oneof model_specific_config {
    // Configuration for smart reply model evalution.
    SmartReplyConfig smart_reply_config = 2;

    // Configuration for smart compose model evalution.
    SmartComposeConfig smart_compose_config = 4;
  }
}

// InputDataset used to create model or do evaluation.
// NextID:5
message InputDataset {
  // Required. ConversationDataset resource name. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationDatasets/<Conversation Dataset ID>`
  string dataset = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationDataset"
    }
  ];
}

// Metadata for article suggestion models.
message ArticleSuggestionModelMetadata {
  // Optional. Type of the article suggestion model. If not provided, model_type
  // is used.
  ConversationModel.ModelType training_model_type = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Metadata for smart reply models.
message SmartReplyModelMetadata {
  // Optional. Type of the smart reply model. If not provided, model_type is
  // used.
  ConversationModel.ModelType training_model_type = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// The evaluation metrics for smart reply model.
message SmartReplyMetrics {
  // Evaluation metrics when retrieving `n` smart replies with the model.
  message TopNMetrics {
    // Number of retrieved smart replies. For example, when `n` is 3, this
    // evaluation contains metrics for when Dialogflow retrieves 3 smart replies
    // with the model.
    int32 n = 1;

    // Defined as `number of queries whose top n smart replies have at least one
    // similar (token match similarity above the defined threshold) reply as the
    // real reply` divided by `number of queries with at least one smart reply`.
    // Value ranges from 0.0 to 1.0 inclusive.
    float recall = 2;
  }

  // Percentage of target participant messages in the evaluation dataset for
  // which similar messages have appeared at least once in the allowlist. Should
  // be [0, 1].
  float allowlist_coverage = 1;

  // Metrics of top n smart replies, sorted by [TopNMetric.n][].
  repeated TopNMetrics top_n_metrics = 2;

  // Total number of conversations used to generate this metric.
  int64 conversation_count = 3;
}

// The request message for
// [ConversationModels.CreateConversationModel][google.cloud.dialogflow.v2.ConversationModels.CreateConversationModel]
message CreateConversationModelRequest {
  // The project to create conversation model for. Format:
  // `projects/<Project ID>`
  string parent = 1;

  // Required. The conversation model to create.
  ConversationModel conversation_model = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationModels.GetConversationModel][google.cloud.dialogflow.v2.ConversationModels.GetConversationModel]
message GetConversationModelRequest {
  // Required. The conversation model to retrieve. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model ID>`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationModels.ListConversationModels][google.cloud.dialogflow.v2.ConversationModels.ListConversationModels]
message ListConversationModelsRequest {
  // Required. The project to list all conversation models for.
  // Format: `projects/<Project ID>`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Maximum number of conversation models to return in a single
  // page. By default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [ConversationModels.ListConversationModels][google.cloud.dialogflow.v2.ConversationModels.ListConversationModels]
message ListConversationModelsResponse {
  // The list of models to return.
  repeated ConversationModel conversation_models = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [ConversationModels.DeleteConversationModel][google.cloud.dialogflow.v2.ConversationModels.DeleteConversationModel]
message DeleteConversationModelRequest {
  // Required. The conversation model to delete. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model ID>`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationModels.DeployConversationModel][google.cloud.dialogflow.v2.ConversationModels.DeployConversationModel]
message DeployConversationModelRequest {
  // Required. The conversation model to deploy. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model ID>`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationModels.UndeployConversationModel][google.cloud.dialogflow.v2.ConversationModels.UndeployConversationModel]
message UndeployConversationModelRequest {
  // Required. The conversation model to undeploy. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model ID>`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationModels.GetConversationModelEvaluation][google.cloud.dialogflow.v2.ConversationModels.GetConversationModelEvaluation]
message GetConversationModelEvaluationRequest {
  // Required. The conversation model evaluation resource name. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model
  // ID>/evaluations/<Evaluation ID>`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationModels.ListConversationModelEvaluations][google.cloud.dialogflow.v2.ConversationModels.ListConversationModelEvaluations]
message ListConversationModelEvaluationsRequest {
  // Required. The conversation model resource name. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model ID>`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Maximum number of evaluations to return in a
  // single page. By default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [ConversationModels.ListConversationModelEvaluations][google.cloud.dialogflow.v2.ConversationModels.ListConversationModelEvaluations]
message ListConversationModelEvaluationsResponse {
  // The list of evaluations to return.
  repeated ConversationModelEvaluation conversation_model_evaluations = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [ConversationModels.CreateConversationModelEvaluation][google.cloud.dialogflow.v2.ConversationModels.CreateConversationModelEvaluation]
message CreateConversationModelEvaluationRequest {
  // Required. The conversation model resource name. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationModels/<Conversation Model ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/ConversationModelEvaluation"
    }
  ];

  // Required. The conversation model evaluation to be created.
  ConversationModelEvaluation conversation_model_evaluation = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Metadata for a
// [ConversationModels.CreateConversationModel][google.cloud.dialogflow.v2.ConversationModels.CreateConversationModel]
// operation.
message CreateConversationModelOperationMetadata {
  // State of CreateConversationModel operation.
  enum State {
    // Invalid.
    STATE_UNSPECIFIED = 0;

    // Request is submitted, but training has not started yet.
    // The model may remain in this state until there is enough capacity to
    // start training.
    PENDING = 1;

    // The training has succeeded.
    SUCCEEDED = 2;

    // The training has succeeded.
    FAILED = 3;

    // The training has been cancelled.
    CANCELLED = 4;

    // The training is in cancelling state.
    CANCELLING = 5;

    // Custom model is training.
    TRAINING = 6;
  }

  // The resource name of the conversation model. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model Id>`
  string conversation_model = 1;

  // State of CreateConversationModel operation.
  State state = 2;

  // Timestamp when the request to create conversation model is submitted. The
  // time is measured on server side.
  google.protobuf.Timestamp create_time = 3;
}

// Metadata for a
// [ConversationModels.DeployConversationModel][google.cloud.dialogflow.v2.ConversationModels.DeployConversationModel]
// operation.
message DeployConversationModelOperationMetadata {
  // The resource name of the conversation model. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model Id>`
  string conversation_model = 1;

  // Timestamp when request to deploy conversation model was submitted. The time
  // is measured on server side.
  google.protobuf.Timestamp create_time = 3;
}

// Metadata for a
// [ConversationModels.UndeployConversationModel][google.cloud.dialogflow.v2.ConversationModels.UndeployConversationModel]
// operation.
message UndeployConversationModelOperationMetadata {
  // The resource name of the conversation model. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model Id>`
  string conversation_model = 1;

  // Timestamp when the request to undeploy conversation model was submitted.
  // The time is measured on server side.
  google.protobuf.Timestamp create_time = 3;
}

// Metadata for a
// [ConversationModels.DeleteConversationModel][google.cloud.dialogflow.v2.ConversationModels.DeleteConversationModel]
// operation.
message DeleteConversationModelOperationMetadata {
  // The resource name of the conversation model. Format:
  // `projects/<Project ID>/conversationModels/<Conversation Model Id>`
  string conversation_model = 1;

  // Timestamp when delete conversation model request was created. The time is
  // measured on server side.
  google.protobuf.Timestamp create_time = 3;
}

// Metadata for a
// [ConversationModels.CreateConversationModelEvaluation][google.cloud.dialogflow.v2.ConversationModels.CreateConversationModelEvaluation]
// operation.
message CreateConversationModelEvaluationOperationMetadata {
  // State of CreateConversationModel operation.
  enum State {
    // Operation status not specified.
    STATE_UNSPECIFIED = 0;

    // The operation is being prepared.
    INITIALIZING = 1;

    // The operation is running.
    RUNNING = 2;

    // The operation is cancelled.
    CANCELLED = 3;

    // The operation has succeeded.
    SUCCEEDED = 4;

    // The operation has failed.
    FAILED = 5;
  }

  // The resource name of the conversation model. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationModels/<Conversation Model Id>/evaluations/<Evaluation Id>`
  string conversation_model_evaluation = 1;

  // The resource name of the conversation model. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationModels/<Conversation Model Id>`
  string conversation_model = 4;

  // State of CreateConversationModel operation.
  State state = 2;

  // Timestamp when the request to create conversation model was submitted. The
  // time is measured on server side.
  google.protobuf.Timestamp create_time = 3;
}
