{"methodConfig": [{"name": [{"service": "google.cloud.dialogflow.v2.Agents"}, {"service": "google.cloud.dialogflow.v2.AnswerRecords"}, {"service": "google.cloud.dialogflow.v2.Contexts"}, {"service": "google.cloud.dialogflow.v2.ConversationDatasets"}, {"service": "google.cloud.dialogflow.v2.ConversationProfiles"}, {"service": "google.cloud.dialogflow.v2.Conversations"}, {"service": "google.cloud.dialogflow.v2.ConversationModels"}, {"service": "google.cloud.dialogflow.v2.Documents"}, {"service": "google.cloud.dialogflow.v2.DocumentCollections"}, {"service": "google.cloud.dialogflow.v2.EntityTypes"}, {"service": "google.cloud.dialogflow.v2.Environments"}, {"service": "google.cloud.dialogflow.v2.FeatureCreationFlows"}, {"service": "google.cloud.dialogflow.v2.Fulfillments"}, {"service": "google.cloud.dialogflow.v2.Generators"}, {"service": "google.cloud.dialogflow.v2.Intents"}, {"service": "google.cloud.dialogflow.v2.KnowledgeBases"}, {"service": "google.cloud.dialogflow.v2.Participants"}, {"service": "google.cloud.dialogflow.v2.PhoneNumbers"}, {"service": "google.cloud.dialogflow.v2.PhoneNumberOrders"}, {"service": "google.cloud.dialogflow.v2.Sessions"}, {"service": "google.cloud.dialogflow.v2.SessionEntityTypes"}, {"service": "google.cloud.dialogflow.v2.Tools"}, {"service": "google.cloud.dialogflow.v2.Versions"}, {"service": "google.cloud.dialogflow.v2.EncryptionSpecService"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.v2.Participants", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.dialogflow.v2.Sessions", "method": "DetectIntent"}], "timeout": "220s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.v2.Participants", "method": "StreamingAnalyzeContent"}, {"service": "google.cloud.dialogflow.v2.Sessions", "method": "StreamingDetectIntent"}], "timeout": "220s"}]}