// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "VersionProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Service for managing [Versions][google.cloud.dialogflow.v2.Version].
service Versions {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all versions of the specified agent.
  rpc ListVersions(ListVersionsRequest) returns (ListVersionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/agent}/versions"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*/agent}/versions"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified agent version.
  rpc GetVersion(GetVersionRequest) returns (Version) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/agent/versions/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/agent/versions/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an agent version.
  //
  // The new version points to the agent instance in the "default" environment.
  rpc CreateVersion(CreateVersionRequest) returns (Version) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/agent}/versions"
      body: "version"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*/agent}/versions"
        body: "version"
      }
    };
    option (google.api.method_signature) = "parent,version";
  }

  // Updates the specified agent version.
  //
  // Note that this method does not allow you to update the state of the agent
  // the given version points to. It allows you to update only mutable
  // properties of the version resource.
  rpc UpdateVersion(UpdateVersionRequest) returns (Version) {
    option (google.api.http) = {
      patch: "/v2/{version.name=projects/*/agent/versions/*}"
      body: "version"
      additional_bindings {
        patch: "/v2/{version.name=projects/*/locations/*/agent/versions/*}"
        body: "version"
      }
    };
    option (google.api.method_signature) = "version,update_mask";
  }

  // Delete the specified agent version.
  rpc DeleteVersion(DeleteVersionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/agent/versions/*}"
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/agent/versions/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }
}

// You can create multiple versions of your agent and publish them to separate
// environments.
//
// When you edit an agent, you are editing the draft agent. At any point, you
// can save the draft agent as an agent version, which is an immutable snapshot
// of your agent.
//
// When you save the draft agent, it is published to the default environment.
// When you create agent versions, you can publish them to custom environments.
// You can create a variety of custom environments for:
//
// - testing
// - development
// - production
// - etc.
//
// For more information, see the [versions and environments
// guide](https://cloud.google.com/dialogflow/docs/agents-versions).
message Version {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Version"
    pattern: "projects/{project}/agent/versions/{version}"
    pattern: "projects/{project}/locations/{location}/agent/versions/{version}"
  };

  // The status of a version.
  enum VersionStatus {
    // Not specified. This value is not used.
    VERSION_STATUS_UNSPECIFIED = 0;

    // Version is not ready to serve (e.g. training is in progress).
    IN_PROGRESS = 1;

    // Version is ready to serve.
    READY = 2;

    // Version training failed.
    FAILED = 3;
  }

  // Output only. The unique identifier of this agent version.
  // Supported formats:
  //
  // - `projects/<Project ID>/agent/versions/<Version ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/versions/<Version
  //   ID>`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The developer-provided description of this version.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The sequential number of this version. This field is read-only
  // which means it cannot be set by create and update methods.
  int32 version_number = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The creation time of this version. This field is read-only,
  // i.e., it cannot be set by create and update methods.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of this version. This field is read-only and cannot
  // be set by create and update methods.
  VersionStatus status = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request message for
// [Versions.ListVersions][google.cloud.dialogflow.v2.Versions.ListVersions].
message ListVersionsRequest {
  // Required. The agent to list all versions from.
  // Supported formats:
  //
  // - `projects/<Project ID>/agent`
  // - `projects/<Project ID>/locations/<Location ID>/agent`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Version"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Versions.ListVersions][google.cloud.dialogflow.v2.Versions.ListVersions].
message ListVersionsResponse {
  // The list of agent versions. There will be a maximum number of items
  // returned based on the page_size field in the request.
  repeated Version versions = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [Versions.GetVersion][google.cloud.dialogflow.v2.Versions.GetVersion].
message GetVersionRequest {
  // Required. The name of the version.
  // Supported formats:
  //
  // - `projects/<Project ID>/agent/versions/<Version ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/versions/<Version
  //   ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];
}

// The request message for
// [Versions.CreateVersion][google.cloud.dialogflow.v2.Versions.CreateVersion].
message CreateVersionRequest {
  // Required. The agent to create a version for.
  // Supported formats:
  //
  // - `projects/<Project ID>/agent`
  // - `projects/<Project ID>/locations/<Location ID>/agent`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Version"
    }
  ];

  // Required. The version to create.
  Version version = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Versions.UpdateVersion][google.cloud.dialogflow.v2.Versions.UpdateVersion].
message UpdateVersionRequest {
  // Required. The version to update.
  // Supported formats:
  //
  // - `projects/<Project ID>/agent/versions/<Version ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/versions/<Version
  //   ID>`
  Version version = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Versions.DeleteVersion][google.cloud.dialogflow.v2.Versions.DeleteVersion].
message DeleteVersionRequest {
  // Required. The name of the version to delete.
  // Supported formats:
  //
  // - `projects/<Project ID>/agent/versions/<Version ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/versions/<Version
  //   ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];
}
