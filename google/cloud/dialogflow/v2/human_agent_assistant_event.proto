// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/cloud/dialogflow/v2/participant.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "HumanAgentAssistantEventProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Represents a notification sent to Cloud Pub/Sub subscribers for
// human agent assistant events in a specific conversation.
message HumanAgentAssistantEvent {
  // The conversation this notification refers to.
  // Format: `projects/<Project ID>/conversations/<Conversation ID>`.
  string conversation = 1;

  // The participant that the suggestion is compiled for.
  // Format: `projects/<Project ID>/conversations/<Conversation
  // ID>/participants/<Participant ID>`. It will not be set in legacy workflow.
  string participant = 3;

  // The suggestion results payload that this notification refers to.
  repeated SuggestionResult suggestion_results = 5;
}
