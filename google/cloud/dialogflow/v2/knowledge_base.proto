// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "KnowledgeBaseProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Service for managing
// [KnowledgeBases][google.cloud.dialogflow.v2.KnowledgeBase].
service KnowledgeBases {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all knowledge bases of the specified agent.
  rpc ListKnowledgeBases(ListKnowledgeBasesRequest)
      returns (ListKnowledgeBasesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/knowledgeBases"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*}/knowledgeBases"
      }
      additional_bindings {
        get: "/v2/{parent=projects/*/agent}/knowledgeBases"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified knowledge base.
  rpc GetKnowledgeBase(GetKnowledgeBaseRequest) returns (KnowledgeBase) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/knowledgeBases/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/knowledgeBases/*}"
      }
      additional_bindings {
        get: "/v2/{name=projects/*/agent/knowledgeBases/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a knowledge base.
  rpc CreateKnowledgeBase(CreateKnowledgeBaseRequest) returns (KnowledgeBase) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/knowledgeBases"
      body: "knowledge_base"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/knowledgeBases"
        body: "knowledge_base"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*/agent}/knowledgeBases"
        body: "knowledge_base"
      }
    };
    option (google.api.method_signature) = "parent,knowledge_base";
  }

  // Deletes the specified knowledge base.
  rpc DeleteKnowledgeBase(DeleteKnowledgeBaseRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/knowledgeBases/*}"
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/knowledgeBases/*}"
      }
      additional_bindings {
        delete: "/v2/{name=projects/*/agent/knowledgeBases/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the specified knowledge base.
  rpc UpdateKnowledgeBase(UpdateKnowledgeBaseRequest) returns (KnowledgeBase) {
    option (google.api.http) = {
      patch: "/v2/{knowledge_base.name=projects/*/knowledgeBases/*}"
      body: "knowledge_base"
      additional_bindings {
        patch: "/v2/{knowledge_base.name=projects/*/locations/*/knowledgeBases/*}"
        body: "knowledge_base"
      }
      additional_bindings {
        patch: "/v2/{knowledge_base.name=projects/*/agent/knowledgeBases/*}"
        body: "knowledge_base"
      }
    };
    option (google.api.method_signature) = "knowledge_base,update_mask";
  }
}

// A knowledge base represents a collection of knowledge documents that you
// provide to Dialogflow. Your knowledge documents contain information that may
// be useful during conversations with end-users. Some Dialogflow features use
// knowledge bases when looking for a response to an end-user input.
//
// For more information, see the [knowledge base
// guide](https://cloud.google.com/dialogflow/docs/how/knowledge-bases).
//
// Note: The `projects.agent.knowledgeBases` resource is deprecated;
// only use `projects.knowledgeBases`.
message KnowledgeBase {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/KnowledgeBase"
    pattern: "projects/{project}/knowledgeBases/{knowledge_base}"
    pattern: "projects/{project}/locations/{location}/knowledgeBases/{knowledge_base}"
  };

  // The knowledge base resource name.
  // The name must be empty when creating a knowledge base.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>`.
  string name = 1;

  // Required. The display name of the knowledge base. The name must be 1024
  // bytes or less; otherwise, the creation request fails.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Language which represents the KnowledgeBase. When the KnowledgeBase is
  // created/updated, expect this to be present for non en-us languages. When
  // unspecified, the default language code en-us applies.
  string language_code = 4;
}

// Request message for
// [KnowledgeBases.ListKnowledgeBases][google.cloud.dialogflow.v2.KnowledgeBases.ListKnowledgeBases].
message ListKnowledgeBasesRequest {
  // Required. The project to list of knowledge bases for.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/KnowledgeBase"
    }
  ];

  // The maximum number of items to return in a single page. By
  // default 10 and at most 100.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;

  // The filter expression used to filter knowledge bases returned by the list
  // method. The expression has the following syntax:
  //
  //   <field> <operator> <value> [AND <field> <operator> <value>] ...
  //
  // The following fields and operators are supported:
  //
  // * display_name with has(:) operator
  // * language_code with equals(=) operator
  //
  // Examples:
  //
  // * 'language_code=en-us' matches knowledge bases with en-us language code.
  // * 'display_name:articles' matches knowledge bases whose display name
  //   contains "articles".
  // * 'display_name:"Best Articles"' matches knowledge bases whose display
  //   name contains "Best Articles".
  // * 'language_code=en-gb AND display_name=articles' matches all knowledge
  //   bases whose display name contains "articles" and whose language code is
  //   "en-gb".
  //
  // Note: An empty filter string (i.e. "") is a no-op and will result in no
  // filtering.
  //
  // For more information about filtering, see
  // [API Filtering](https://aip.dev/160).
  string filter = 4;
}

// Response message for
// [KnowledgeBases.ListKnowledgeBases][google.cloud.dialogflow.v2.KnowledgeBases.ListKnowledgeBases].
message ListKnowledgeBasesResponse {
  // The list of knowledge bases.
  repeated KnowledgeBase knowledge_bases = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request message for
// [KnowledgeBases.GetKnowledgeBase][google.cloud.dialogflow.v2.KnowledgeBases.GetKnowledgeBase].
message GetKnowledgeBaseRequest {
  // Required. The name of the knowledge base to retrieve.
  // Format `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/KnowledgeBase"
    }
  ];
}

// Request message for
// [KnowledgeBases.CreateKnowledgeBase][google.cloud.dialogflow.v2.KnowledgeBases.CreateKnowledgeBase].
message CreateKnowledgeBaseRequest {
  // Required. The project to create a knowledge base for.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/KnowledgeBase"
    }
  ];

  // Required. The knowledge base to create.
  KnowledgeBase knowledge_base = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [KnowledgeBases.DeleteKnowledgeBase][google.cloud.dialogflow.v2.KnowledgeBases.DeleteKnowledgeBase].
message DeleteKnowledgeBaseRequest {
  // Required. The name of the knowledge base to delete.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/KnowledgeBase"
    }
  ];

  // Optional. Force deletes the knowledge base. When set to true, any documents
  // in the knowledge base are also deleted.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KnowledgeBases.UpdateKnowledgeBase][google.cloud.dialogflow.v2.KnowledgeBases.UpdateKnowledgeBase].
message UpdateKnowledgeBaseRequest {
  // Required. The knowledge base to update.
  KnowledgeBase knowledge_base = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Not specified means `update all`.
  // Currently, only `display_name` can be updated, an InvalidArgument will be
  // returned for attempting to update other fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}
