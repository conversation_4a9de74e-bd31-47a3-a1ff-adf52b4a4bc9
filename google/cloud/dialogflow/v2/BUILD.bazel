# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "dialogflow_proto",
    srcs = [
        "agent.proto",
        "answer_record.proto",
        "audio_config.proto",
        "context.proto",
        "conversation.proto",
        "conversation_dataset.proto",
        "conversation_event.proto",
        "conversation_model.proto",
        "conversation_profile.proto",
        "document.proto",
        "encryption_spec.proto",
        "entity_type.proto",
        "environment.proto",
        "fulfillment.proto",
        "gcs.proto",
        "generator.proto",
        "human_agent_assistant_event.proto",
        "intent.proto",
        "knowledge_base.proto",
        "participant.proto",
        "session.proto",
        "session_entity_type.proto",
        "validation_result.proto",
        "version.proto",
        "webhook.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "dialogflow_proto_with_info",
    deps = [
        ":dialogflow_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "dialogflow_java_proto",
    deps = [":dialogflow_proto"],
)

java_grpc_library(
    name = "dialogflow_java_grpc",
    srcs = [":dialogflow_proto"],
    deps = [":dialogflow_java_proto"],
)

java_gapic_library(
    name = "dialogflow_java_gapic",
    srcs = [":dialogflow_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "dialogflow_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v2.yaml",
    test_deps = [
        ":dialogflow_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":dialogflow_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "dialogflow_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.dialogflow.v2.AgentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.AgentsClientTest",
        "com.google.cloud.dialogflow.v2.AnswerRecordsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.AnswerRecordsClientTest",
        "com.google.cloud.dialogflow.v2.ContextsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.ContextsClientTest",
        "com.google.cloud.dialogflow.v2.ConversationDatasetsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.ConversationDatasetsClientTest",
        "com.google.cloud.dialogflow.v2.ConversationModelsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.ConversationModelsClientTest",
        "com.google.cloud.dialogflow.v2.ConversationProfilesClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.ConversationProfilesClientTest",
        "com.google.cloud.dialogflow.v2.ConversationsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.ConversationsClientTest",
        "com.google.cloud.dialogflow.v2.DocumentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.DocumentsClientTest",
        "com.google.cloud.dialogflow.v2.EncryptionSpecServiceClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.EncryptionSpecServiceClientTest",
        "com.google.cloud.dialogflow.v2.EntityTypesClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.EntityTypesClientTest",
        "com.google.cloud.dialogflow.v2.EnvironmentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.EnvironmentsClientTest",
        "com.google.cloud.dialogflow.v2.FulfillmentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.FulfillmentsClientTest",
        "com.google.cloud.dialogflow.v2.GeneratorsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.GeneratorsClientTest",
        "com.google.cloud.dialogflow.v2.IntentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.IntentsClientTest",
        "com.google.cloud.dialogflow.v2.KnowledgeBasesClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.KnowledgeBasesClientTest",
        "com.google.cloud.dialogflow.v2.ParticipantsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.ParticipantsClientTest",
        "com.google.cloud.dialogflow.v2.SessionEntityTypesClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.SessionEntityTypesClientTest",
        "com.google.cloud.dialogflow.v2.SessionsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.SessionsClientTest",
        "com.google.cloud.dialogflow.v2.VersionsClientHttpJsonTest",
        "com.google.cloud.dialogflow.v2.VersionsClientTest",
    ],
    runtime_deps = [":dialogflow_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-dialogflow-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":dialogflow_java_gapic",
        ":dialogflow_java_grpc",
        ":dialogflow_java_proto",
        ":dialogflow_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "dialogflow_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb",
    protos = [":dialogflow_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "dialogflow_go_gapic",
    srcs = [":dialogflow_proto_with_info"],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    importpath = "cloud.google.com/go/dialogflow/apiv2;dialogflow",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":dialogflow_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-dialogflow-v2-go",
    deps = [
        ":dialogflow_go_gapic",
        ":dialogflow_go_gapic_srcjar-metadata.srcjar",
        ":dialogflow_go_gapic_srcjar-snippets.srcjar",
        ":dialogflow_go_gapic_srcjar-test.srcjar",
        ":dialogflow_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "dialogflow_py_gapic",
    srcs = [":dialogflow_proto"],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "dialogflow_py_gapic_test",
    srcs = [
        "dialogflow_py_gapic_pytest.py",
        "dialogflow_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":dialogflow_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "dialogflow-v2-py",
    deps = [
        ":dialogflow_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "dialogflow_php_proto",
    deps = [":dialogflow_proto"],
)

php_gapic_library(
    name = "dialogflow_php_gapic",
    srcs = [":dialogflow_proto_with_info"],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":dialogflow_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-dialogflow-v2-php",
    deps = [
        ":dialogflow_php_gapic",
        ":dialogflow_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "dialogflow_nodejs_gapic",
    package_name = "@google-cloud/dialogflow",
    src = ":dialogflow_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    main_service = "dialogflow",
    package = "google.cloud.dialogflow.v2",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "dialogflow-v2-nodejs",
    deps = [
        ":dialogflow_nodejs_gapic",
        ":dialogflow_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "dialogflow_ruby_proto",
    deps = [":dialogflow_proto"],
)

ruby_grpc_library(
    name = "dialogflow_ruby_grpc",
    srcs = [":dialogflow_proto"],
    deps = [":dialogflow_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "dialogflow_ruby_gapic",
    srcs = [":dialogflow_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=dialogflow.googleapis.com",
        "ruby-cloud-api-shortname=dialogflow",
        "ruby-cloud-env-prefix=DIALOGFLOW",
        "ruby-cloud-gem-name=google-cloud-dialogflow-v2",
        "ruby-cloud-product-url=https://cloud.google.com/dialogflow",
    ],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Dialogflow is an end-to-end, build-once deploy-everywhere development suite for creating conversational interfaces for websites, mobile applications, popular messaging platforms, and IoT devices. You can use it to build interfaces (such as chatbots and conversational IVR) that enable natural and rich interactions between your users and your business. This client is for Dialogflow ES, providing the standard agent type suitable for small and simple agents.",
    ruby_cloud_title = "Dialogflow V2",
    service_yaml = "dialogflow_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":dialogflow_ruby_grpc",
        ":dialogflow_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-dialogflow-v2-ruby",
    deps = [
        ":dialogflow_ruby_gapic",
        ":dialogflow_ruby_grpc",
        ":dialogflow_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "dialogflow_csharp_proto",
    extra_opts = [],
    deps = [":dialogflow_proto"],
)

csharp_grpc_library(
    name = "dialogflow_csharp_grpc",
    srcs = [":dialogflow_proto"],
    deps = [":dialogflow_csharp_proto"],
)

csharp_gapic_library(
    name = "dialogflow_csharp_gapic",
    srcs = [":dialogflow_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "dialogflow_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":dialogflow_csharp_grpc",
        ":dialogflow_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-dialogflow-v2-csharp",
    deps = [
        ":dialogflow_csharp_gapic",
        ":dialogflow_csharp_grpc",
        ":dialogflow_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "dialogflow_cc_proto",
    deps = [":dialogflow_proto"],
)

cc_grpc_library(
    name = "dialogflow_cc_grpc",
    srcs = [":dialogflow_proto"],
    grpc_only = True,
    deps = [":dialogflow_cc_proto"],
)
