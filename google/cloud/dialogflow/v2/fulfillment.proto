// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "FulfillmentProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Service for managing [Fulfillments][google.cloud.dialogflow.v2.Fulfillment].
service Fulfillments {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Retrieves the fulfillment.
  rpc GetFulfillment(GetFulfillmentRequest) returns (Fulfillment) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/agent/fulfillment}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/agent/fulfillment}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the fulfillment.
  rpc UpdateFulfillment(UpdateFulfillmentRequest) returns (Fulfillment) {
    option (google.api.http) = {
      patch: "/v2/{fulfillment.name=projects/*/agent/fulfillment}"
      body: "fulfillment"
      additional_bindings {
        patch: "/v2/{fulfillment.name=projects/*/locations/*/agent/fulfillment}"
        body: "fulfillment"
      }
    };
    option (google.api.method_signature) = "fulfillment,update_mask";
  }
}

// By default, your agent responds to a matched intent with a static response.
// As an alternative, you can provide a more dynamic response by using
// fulfillment. When you enable fulfillment for an intent, Dialogflow responds
// to that intent by calling a service that you define. For example, if an
// end-user wants to schedule a haircut on Friday, your service can check your
// database and respond to the end-user with availability information for
// Friday.
//
// For more information, see the [fulfillment
// guide](https://cloud.google.com/dialogflow/docs/fulfillment-overview).
message Fulfillment {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Fulfillment"
    pattern: "projects/{project}/agent/fulfillment"
    pattern: "projects/{project}/locations/{location}/agent/fulfillment"
  };

  // Represents configuration for a generic web service.
  // Dialogflow supports two mechanisms for authentications:
  //
  // - Basic authentication with username and password.
  // - Authentication with additional authentication headers.
  //
  // More information could be found at:
  // https://cloud.google.com/dialogflow/docs/fulfillment-configure.
  message GenericWebService {
    // Required. The fulfillment URI for receiving POST requests.
    // It must use https protocol.
    string uri = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. The user name for HTTP Basic authentication.
    string username = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The password for HTTP Basic authentication.
    string password = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The HTTP request headers to send together with fulfillment
    // requests.
    map<string, string> request_headers = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Indicates if generic web service is created through Cloud
    // Functions integration. Defaults to false.
    //
    // is_cloud_function is deprecated. Cloud functions can be configured by
    // its uri as a regular web service now.
    bool is_cloud_function = 5
        [deprecated = true, (google.api.field_behavior) = OPTIONAL];
  }

  // Whether fulfillment is enabled for the specific feature.
  message Feature {
    // The type of the feature.
    enum Type {
      // Feature type not specified.
      TYPE_UNSPECIFIED = 0;

      // Fulfillment is enabled for SmallTalk.
      SMALLTALK = 1;
    }

    // The type of the feature that enabled for fulfillment.
    Type type = 1;
  }

  // Required. The unique identifier of the fulfillment.
  // Supported formats:
  //
  // - `projects/<Project ID>/agent/fulfillment`
  // - `projects/<Project ID>/locations/<Location ID>/agent/fulfillment`
  //
  // This field is not used for Fulfillment in an Environment.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The human-readable name of the fulfillment, unique within the
  // agent.
  //
  // This field is not used for Fulfillment in an Environment.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The fulfillment configuration.
  oneof fulfillment {
    // Configuration for a generic web service.
    GenericWebService generic_web_service = 3;
  }

  // Optional. Whether fulfillment is enabled.
  bool enabled = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The field defines whether the fulfillment is enabled for certain
  // features.
  repeated Feature features = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [Fulfillments.GetFulfillment][google.cloud.dialogflow.v2.Fulfillments.GetFulfillment].
message GetFulfillmentRequest {
  // Required. The name of the fulfillment.
  // Format: `projects/<Project ID>/agent/fulfillment`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Fulfillment"
    }
  ];
}

// The request message for
// [Fulfillments.UpdateFulfillment][google.cloud.dialogflow.v2.Fulfillments.UpdateFulfillment].
message UpdateFulfillmentRequest {
  // Required. The fulfillment to update.
  Fulfillment fulfillment = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The mask to control which fields get updated. If the mask is not
  // present, all fields will be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}
