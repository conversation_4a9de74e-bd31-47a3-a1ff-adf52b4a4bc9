type: google.api.Service
config_version: 3
name: dialogflow.googleapis.com
title: Dialogflow API

apis:
- name: google.cloud.dialogflow.v2.Agents
- name: google.cloud.dialogflow.v2.AnswerRecords
- name: google.cloud.dialogflow.v2.Contexts
- name: google.cloud.dialogflow.v2.ConversationDatasets
- name: google.cloud.dialogflow.v2.ConversationModels
- name: google.cloud.dialogflow.v2.ConversationProfiles
- name: google.cloud.dialogflow.v2.Conversations
- name: google.cloud.dialogflow.v2.Documents
- name: google.cloud.dialogflow.v2.EncryptionSpecService
- name: google.cloud.dialogflow.v2.EntityTypes
- name: google.cloud.dialogflow.v2.Environments
- name: google.cloud.dialogflow.v2.Fulfillments
- name: google.cloud.dialogflow.v2.Generators
- name: google.cloud.dialogflow.v2.Intents
- name: google.cloud.dialogflow.v2.KnowledgeBases
- name: google.cloud.dialogflow.v2.Participants
- name: google.cloud.dialogflow.v2.SessionEntityTypes
- name: google.cloud.dialogflow.v2.Sessions
- name: google.cloud.dialogflow.v2.Versions
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.dialogflow.v2.BatchUpdateEntityTypesResponse
- name: google.cloud.dialogflow.v2.BatchUpdateIntentsResponse
- name: google.cloud.dialogflow.v2.ClearSuggestionFeatureConfigOperationMetadata
- name: google.cloud.dialogflow.v2.ConversationEvent
- name: google.cloud.dialogflow.v2.ConversationModel
- name: google.cloud.dialogflow.v2.CreateConversationDatasetOperationMetadata
- name: google.cloud.dialogflow.v2.CreateConversationModelEvaluationOperationMetadata
- name: google.cloud.dialogflow.v2.CreateConversationModelOperationMetadata
- name: google.cloud.dialogflow.v2.DeleteConversationDatasetOperationMetadata
- name: google.cloud.dialogflow.v2.DeleteConversationModelOperationMetadata
- name: google.cloud.dialogflow.v2.DeployConversationModelOperationMetadata
- name: google.cloud.dialogflow.v2.ExportAgentResponse
- name: google.cloud.dialogflow.v2.HumanAgentAssistantEvent
- name: google.cloud.dialogflow.v2.ImportConversationDataOperationMetadata
- name: google.cloud.dialogflow.v2.ImportConversationDataOperationResponse
- name: google.cloud.dialogflow.v2.ImportDocumentsResponse
- name: google.cloud.dialogflow.v2.InitializeEncryptionSpecMetadata
- name: google.cloud.dialogflow.v2.KnowledgeOperationMetadata
- name: google.cloud.dialogflow.v2.OriginalDetectIntentRequest
- name: google.cloud.dialogflow.v2.SetSuggestionFeatureConfigOperationMetadata
- name: google.cloud.dialogflow.v2.UndeployConversationModelOperationMetadata
- name: google.cloud.dialogflow.v2.WebhookRequest
- name: google.cloud.dialogflow.v2.WebhookResponse

documentation:
  summary: |-
    Builds conversational interfaces (for example, chatbots, and voice-powered
    apps and devices).
  overview: |-
    <!-- mdformat off(presubmit failing, mdformat is as well) --> Dialogflow is
    a natural language understanding platform that makes it easy
    to design and integrate a conversational user interface into your mobile
    app, web application, device, bot, interactive voice response system, and
    so on. Using Dialogflow, you can provide new and engaging ways for
    users to interact with your product.

    Dialogflow can analyze multiple types of input from your customers,
    including text or audio inputs (like from a phone or voice recording).
    It can also respond to your customers in a couple of ways, either through
    text or with synthetic speech.

    For more information, see the
    [Dialogflow documentation](https://cloud.google.com/dialogflow/docs).
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v2/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v2/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v2/{name=projects/*/operations/*}:cancel'
    additional_bindings:
    - post: '/v2/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=projects/*/operations/*}'
    additional_bindings:
    - get: '/v2/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=projects/*}/operations'
    additional_bindings:
    - get: '/v2/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.dialogflow.v2.Agents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2.AnswerRecords.ListAnswerRecords
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2.AnswerRecords.UpdateAnswerRecord
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.Contexts.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.ConversationDatasets.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.ConversationModels.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.ConversationProfiles.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.Conversations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.Documents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2.EncryptionSpecService.GetEncryptionSpec
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2.EncryptionSpecService.InitializeEncryptionSpec
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.EntityTypes.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.Environments.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2.Fulfillments.GetFulfillment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2.Fulfillments.UpdateFulfillment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.Generators.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.Intents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.KnowledgeBases.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.Participants.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.SessionEntityTypes.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2.Sessions.DetectIntent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2.Sessions.StreamingDetectIntent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2.Versions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
