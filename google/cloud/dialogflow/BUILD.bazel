# This build file includes a target for the Ruby wrapper library for
# google-cloud-dialogflow.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for dialogflow.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "dialogflow_ruby_wrapper",
    srcs = ["//google/cloud/dialogflow/v2:dialogflow_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-dialogflow",
        "ruby-cloud-env-prefix=DIALOGFLOW",
        "ruby-cloud-wrapper-of=v2:0.32",
        "ruby-cloud-product-url=https://cloud.google.com/dialogflow",
        "ruby-cloud-api-id=dialogflow.googleapis.com",
        "ruby-cloud-api-shortname=dialogflow",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Dialogflow is an end-to-end, build-once deploy-everywhere development suite for creating conversational interfaces for websites, mobile applications, popular messaging platforms, and IoT devices. You can use it to build interfaces (such as chatbots and conversational IVR) that enable natural and rich interactions between your users and your business. This client is for Dialogflow ES, providing the standard agent type suitable for small and simple agents.",
    ruby_cloud_title = "Dialogflow",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-dialogflow-ruby",
    deps = [
        ":dialogflow_ruby_wrapper",
    ],
)
