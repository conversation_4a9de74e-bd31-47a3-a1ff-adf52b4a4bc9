// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/v2beta1/conversation_profile.proto";
import "google/cloud/dialogflow/v2beta1/generator.proto";
import "google/cloud/dialogflow/v2beta1/participant.proto";
import "google/cloud/dialogflow/v2beta1/session.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "ConversationProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";
option (google.api.resource_definition) = {
  type: "discoveryengine.googleapis.com/DataStore"
  pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}"
  pattern: "projects/{project}/locations/{location}/dataStores/{data_store}"
};

// Service for managing
// [Conversations][google.cloud.dialogflow.v2beta1.Conversation].
service Conversations {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Creates a new conversation. Conversations are auto-completed after 24
  // hours.
  //
  // Conversation Lifecycle:
  // There are two stages during a conversation: Automated Agent Stage and
  // Assist Stage.
  //
  // For Automated Agent Stage, there will be a dialogflow agent responding to
  // user queries.
  //
  // For Assist Stage, there's no dialogflow agent responding to user queries.
  // But we will provide suggestions which are generated from conversation.
  //
  // If
  // [Conversation.conversation_profile][google.cloud.dialogflow.v2beta1.Conversation.conversation_profile]
  // is configured for a dialogflow agent, conversation will start from
  // `Automated Agent Stage`, otherwise, it will start from `Assist Stage`. And
  // during `Automated Agent Stage`, once an
  // [Intent][google.cloud.dialogflow.v2beta1.Intent] with
  // [Intent.live_agent_handoff][google.cloud.dialogflow.v2beta1.Intent.live_agent_handoff]
  // is triggered, conversation will transfer to Assist Stage.
  rpc CreateConversation(CreateConversationRequest) returns (Conversation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*}/conversations"
      body: "conversation"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*}/conversations"
        body: "conversation"
      }
    };
    option (google.api.method_signature) = "parent,conversation";
  }

  // Returns the list of all conversations in the specified project.
  rpc ListConversations(ListConversationsRequest)
      returns (ListConversationsResponse) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*}/conversations"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*}/conversations"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specific conversation.
  rpc GetConversation(GetConversationRequest) returns (Conversation) {
    option (google.api.http) = {
      get: "/v2beta1/{name=projects/*/conversations/*}"
      additional_bindings {
        get: "/v2beta1/{name=projects/*/locations/*/conversations/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Completes the specified conversation. Finished conversations are purged
  // from the database after 30 days.
  rpc CompleteConversation(CompleteConversationRequest) returns (Conversation) {
    option (google.api.http) = {
      post: "/v2beta1/{name=projects/*/conversations/*}:complete"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{name=projects/*/locations/*/conversations/*}:complete"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Batch ingests messages to conversation. Customers can use this RPC to
  // ingest historical messages to conversation.
  rpc BatchCreateMessages(BatchCreateMessagesRequest)
      returns (BatchCreateMessagesResponse) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/conversations/*}/messages:batchCreate"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/conversations/*}/messages:batchCreate"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,requests";
  }

  // Lists messages that belong to a given conversation.
  // `messages` are ordered by `create_time` in descending order. To fetch
  // updates without duplication, send request with filter
  // `create_time_epoch_microseconds >
  // [first item's create_time of previous request]` and empty page_token.
  rpc ListMessages(ListMessagesRequest) returns (ListMessagesResponse) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*/conversations/*}/messages"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*/conversations/*}/messages"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Suggest summary for a conversation based on specific historical messages.
  // The range of the messages to be used for summary can be specified in the
  // request.
  rpc SuggestConversationSummary(SuggestConversationSummaryRequest)
      returns (SuggestConversationSummaryResponse) {
    option (google.api.http) = {
      post: "/v2beta1/{conversation=projects/*/conversations/*}/suggestions:suggestConversationSummary"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{conversation=projects/*/locations/*/conversations/*}/suggestions:suggestConversationSummary"
        body: "*"
      }
    };
    option (google.api.method_signature) = "conversation";
  }

  // Generates and returns a summary for a conversation that does not have a
  // resource created for it.
  rpc GenerateStatelessSummary(GenerateStatelessSummaryRequest)
      returns (GenerateStatelessSummaryResponse) {
    option (google.api.http) = {
      post: "/v2beta1/{stateless_conversation.parent=projects/*}/suggestions:generateStatelessSummary"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{stateless_conversation.parent=projects/*/locations/*}/suggestions:generateStatelessSummary"
        body: "*"
      }
    };
  }

  // Generates and returns a suggestion for a conversation that does not have a
  // resource created for it.
  rpc GenerateStatelessSuggestion(GenerateStatelessSuggestionRequest)
      returns (GenerateStatelessSuggestionResponse) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/locations/*}/statelessSuggestion:generate"
      body: "*"
    };
  }

  // Get answers for the given query based on knowledge documents.
  rpc SearchKnowledge(SearchKnowledgeRequest)
      returns (SearchKnowledgeResponse) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*}/suggestions:searchKnowledge"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*}/suggestions:searchKnowledge"
        body: "*"
      }
      additional_bindings {
        post: "/v2beta1/{conversation=projects/*/conversations/*}/suggestions:searchKnowledge"
        body: "*"
      }
      additional_bindings {
        post: "/v2beta1/{conversation=projects/*/locations/*/conversations/*}/suggestions:searchKnowledge"
        body: "*"
      }
    };
  }
}

// Represents a conversation.
// A conversation is an interaction between an agent, including live agents
// and Dialogflow agents, and a support customer. Conversations can
// include phone calls and text-based chat sessions.
message Conversation {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Conversation"
    pattern: "projects/{project}/conversations/{conversation}"
    pattern: "projects/{project}/locations/{location}/conversations/{conversation}"
    plural: "conversations"
    singular: "conversation"
  };

  // Enumeration of the completion status of the conversation.
  enum LifecycleState {
    // Unknown.
    LIFECYCLE_STATE_UNSPECIFIED = 0;

    // Conversation is currently open for media analysis.
    IN_PROGRESS = 1;

    // Conversation has been completed.
    COMPLETED = 2;
  }

  // Enumeration of the different conversation stages a conversation can be in.
  // Reference:
  // https://cloud.google.com/dialogflow/priv/docs/contact-center/basics#stages
  enum ConversationStage {
    // Unknown. Should never be used after a conversation is successfully
    // created.
    CONVERSATION_STAGE_UNSPECIFIED = 0;

    // The conversation should return virtual agent responses into the
    // conversation.
    VIRTUAL_AGENT_STAGE = 1;

    // The conversation should not provide responses, just listen and provide
    // suggestions.
    HUMAN_ASSIST_STAGE = 2;
  }

  // Output only. Identifier. The unique identifier of this conversation.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>`.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. The current state of the Conversation.
  LifecycleState lifecycle_state = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The Conversation Profile to be used to configure this
  // Conversation. This field cannot be updated.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`.
  string conversation_profile = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationProfile"
    }
  ];

  // Output only. Required if the conversation is to be connected over
  // telephony.
  ConversationPhoneNumber phone_number = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The stage of a conversation. It indicates whether the virtual
  // agent or a human agent is handling the conversation.
  //
  // If the conversation is created with the conversation profile that has
  // Dialogflow config set, defaults to
  // [ConversationStage.VIRTUAL_AGENT_STAGE][google.cloud.dialogflow.v2beta1.Conversation.ConversationStage.VIRTUAL_AGENT_STAGE];
  // Otherwise, defaults to
  // [ConversationStage.HUMAN_ASSIST_STAGE][google.cloud.dialogflow.v2beta1.Conversation.ConversationStage.HUMAN_ASSIST_STAGE].
  //
  // If the conversation is created with the conversation profile that has
  // Dialogflow config set but explicitly sets conversation_stage to
  // [ConversationStage.HUMAN_ASSIST_STAGE][google.cloud.dialogflow.v2beta1.Conversation.ConversationStage.HUMAN_ASSIST_STAGE],
  // it skips
  // [ConversationStage.VIRTUAL_AGENT_STAGE][google.cloud.dialogflow.v2beta1.Conversation.ConversationStage.VIRTUAL_AGENT_STAGE]
  // stage and directly goes to
  // [ConversationStage.HUMAN_ASSIST_STAGE][google.cloud.dialogflow.v2beta1.Conversation.ConversationStage.HUMAN_ASSIST_STAGE].
  ConversationStage conversation_stage = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time the conversation was started.
  google.protobuf.Timestamp start_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the conversation was finished.
  google.protobuf.Timestamp end_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents a phone number for telephony integration. It allows for connecting
// a particular conversation over telephony.
message ConversationPhoneNumber {
  // Output only. The phone number to connect to this conversation.
  string phone_number = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request message for
// [Conversations.CreateConversation][google.cloud.dialogflow.v2beta1.Conversations.CreateConversation].
message CreateConversationRequest {
  // Required. Resource identifier of the project creating the conversation.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Conversation"
    }
  ];

  // Required. The conversation to create.
  Conversation conversation = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Identifier of the conversation. Generally it's auto generated by
  // Google. Only set it if you cannot wait for the response to return a
  // auto-generated one to you.
  //
  // The conversation ID must be compliant with the regression formula
  // `[a-zA-Z][a-zA-Z0-9_-]*` with the characters length in range of [3,64].
  // If the field is provided, the caller is responsible for
  // 1. the uniqueness of the ID, otherwise the request will be rejected.
  // 2. the consistency for whether to use custom ID or not under a project to
  // better ensure uniqueness.
  string conversation_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [Conversations.ListConversations][google.cloud.dialogflow.v2beta1.Conversations.ListConversations].
message ListConversationsRequest {
  // Required. The project from which to list all conversation.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Conversation"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter expression that filters conversations listed in the
  // response. Only `lifecycle_state` can be filtered on in this way. For
  // example, the following expression only returns `COMPLETED` conversations:
  //
  // `lifecycle_state = "COMPLETED"`
  //
  // For more information about filtering, see
  // [API Filtering](https://aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Conversations.ListConversations][google.cloud.dialogflow.v2beta1.Conversations.ListConversations].
message ListConversationsResponse {
  // The list of conversations. There will be a maximum number of items
  // returned based on the page_size field in the request.
  repeated Conversation conversations = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [Conversations.GetConversation][google.cloud.dialogflow.v2beta1.Conversations.GetConversation].
message GetConversationRequest {
  // Required. The name of the conversation. Format:
  // `projects/<Project ID>/locations/<Location ID>/conversations/<Conversation
  // ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Conversation"
    }
  ];
}

// The request message for
// [Conversations.CompleteConversation][google.cloud.dialogflow.v2beta1.Conversations.CompleteConversation].
message CompleteConversationRequest {
  // Required. Resource identifier of the conversation to close.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Conversation"
    }
  ];
}

// The request message to create one Message. Currently it is only used in
// BatchCreateMessagesRequest.
message CreateMessageRequest {
  // Required. Resource identifier of the conversation to create message.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Conversation"
    }
  ];

  // Required. The message to create.
  // [Message.participant][google.cloud.dialogflow.v2beta1.Message.participant]
  // is required.
  Message message = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for [Conversations.BatchCreateMessagesRequest][].
message BatchCreateMessagesRequest {
  // Required. Resource identifier of the conversation to create message.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Conversation"
    }
  ];

  // Required. A maximum of 300 messages can be created in a batch.
  // [CreateMessageRequest.message.send_time][] is required. All created
  // messages will have identical
  // [Message.create_time][google.cloud.dialogflow.v2beta1.Message.create_time].
  repeated CreateMessageRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for [Conversations.BatchCreateMessagesResponse][].
message BatchCreateMessagesResponse {
  // Messages created.
  repeated Message messages = 1;
}

// The request message for
// [Conversations.ListMessages][google.cloud.dialogflow.v2beta1.Conversations.ListMessages].
message ListMessagesRequest {
  // Required. The name of the conversation to list messages for.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Message"
    }
  ];

  // Optional. Filter on message fields. Currently predicates on `create_time`
  // and `create_time_epoch_microseconds` are supported. `create_time` only
  // support milliseconds accuracy. E.g.,
  // `create_time_epoch_microseconds > 1551790877964485` or
  // `create_time > "2017-01-15T01:30:15.01Z"`.
  //
  // For more information about filtering, see
  // [API Filtering](https://aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Conversations.ListMessages][google.cloud.dialogflow.v2beta1.Conversations.ListMessages].
message ListMessagesResponse {
  // Required. The list of messages. There will be a maximum number of items
  // returned based on the page_size field in the request.
  // `messages` is sorted by `create_time` in descending order.
  repeated Message messages = 1;

  // Optional. Token to retrieve the next page of results, or empty if there are
  // no more results in the list.
  string next_page_token = 2;
}

// The request message for
// [Conversations.SuggestConversationSummary][google.cloud.dialogflow.v2beta1.Conversations.SuggestConversationSummary].
message SuggestConversationSummaryRequest {
  // Required. The conversation to fetch suggestion for.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>`.
  string conversation = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Conversation"
    }
  ];

  // Optional. The name of the latest conversation message used as context for
  // compiling suggestion. If empty, the latest message of the conversation will
  // be used.
  //
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>/messages/<Message ID>`.
  string latest_message = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Message"
    }
  ];

  // Optional. Max number of messages prior to and including
  // [latest_message] to use as context when compiling the
  // suggestion. By default 500 and at most 1000.
  int32 context_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Parameters for a human assist query. Only used for POC/demo
  // purpose.
  AssistQueryParameters assist_query_params = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Conversations.SuggestConversationSummary][google.cloud.dialogflow.v2beta1.Conversations.SuggestConversationSummary].
message SuggestConversationSummaryResponse {
  // Generated summary for a conversation.
  message Summary {
    // The summary content that is concatenated into one string.
    string text = 1;

    // The summary content that is divided into sections. The key is the
    // section's name and the value is the section's content. There is no
    // specific format for the key or value.
    map<string, string> text_sections = 4;

    // The name of the answer record. Format:
    // "projects/<Project ID>/answerRecords/<Answer Record ID>"
    string answer_record = 3 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/AnswerRecord"
    }];

    // The baseline model version used to generate this summary. It is empty if
    // a baseline model was not used to generate this summary.
    string baseline_model_version = 5;
  }

  // Generated summary.
  Summary summary = 1;

  // The name of the latest conversation message used as context for
  // compiling suggestion.
  //
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>/messages/<Message ID>`.
  string latest_message = 2 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/Message"
  }];

  // Number of messages prior to and including
  // [last_conversation_message][] used to compile the suggestion. It may be
  // smaller than the [SuggestSummaryRequest.context_size][] field in the
  // request if there weren't that many messages in the conversation.
  int32 context_size = 3;
}

// The request message for
// [Conversations.GenerateStatelessSummary][google.cloud.dialogflow.v2beta1.Conversations.GenerateStatelessSummary].
message GenerateStatelessSummaryRequest {
  // The minimum amount of information required to generate a Summary without
  // having a Conversation resource created.
  message MinimalConversation {
    // Required. The messages that the Summary will be generated from. It is
    // expected that this message content is already redacted and does not
    // contain any PII. Required fields: {content, language_code, participant,
    // participant_role} Optional fields: {send_time} If send_time is not
    // provided, then the messages must be provided in chronological order.
    repeated Message messages = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The parent resource to charge for the Summary's generation.
    // Format: `projects/<Project ID>/locations/<Location ID>`.
    string parent = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "locations.googleapis.com/Location"
      }
    ];
  }

  // Required. The conversation to suggest a summary for.
  MinimalConversation stateless_conversation = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A ConversationProfile containing information required for Summary
  // generation.
  // Required fields: {language_code, security_settings}
  // Optional fields: {agent_assistant_config}
  ConversationProfile conversation_profile = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The name of the latest conversation message used as context for
  // generating a Summary. If empty, the latest message of the conversation will
  // be used. The format is specific to the user and the names of the messages
  // provided.
  string latest_message = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Message"
    }
  ];

  // Optional. Max number of messages prior to and including
  // [latest_message] to use as context when compiling the
  // suggestion. By default 500 and at most 1000.
  int32 max_context_size = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Conversations.GenerateStatelessSummary][google.cloud.dialogflow.v2beta1.Conversations.GenerateStatelessSummary].
message GenerateStatelessSummaryResponse {
  // Generated summary for a conversation.
  message Summary {
    // The summary content that is concatenated into one string.
    string text = 1;

    // The summary content that is divided into sections. The key is the
    // section's name and the value is the section's content. There is no
    // specific format for the key or value.
    map<string, string> text_sections = 2;

    // The baseline model version used to generate this summary. It is empty if
    // a baseline model was not used to generate this summary.
    string baseline_model_version = 4;
  }

  // Generated summary.
  Summary summary = 1;

  // The name of the latest conversation message used as context for
  // compiling suggestion. The format is specific to the user and the names of
  // the messages provided.
  string latest_message = 2 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/Message"
  }];

  // Number of messages prior to and including
  // [last_conversation_message][] used to compile the suggestion. It may be
  // smaller than the [GenerateStatelessSummaryRequest.context_size][] field in
  // the request if there weren't that many messages in the conversation.
  int32 context_size = 3;
}

// The request message for
// [Conversations.GenerateStatelessSuggestion][google.cloud.dialogflow.v2beta1.Conversations.GenerateStatelessSuggestion].
message GenerateStatelessSuggestionRequest {
  // Required. The parent resource to charge for the Suggestion's generation.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Generator.
  oneof generator_resource {
    // Uncreated generator. It should be a complete generator that includes all
    // information about the generator.
    Generator generator = 2;

    // The resource name of the existing created generator. Format:
    // `projects/<Project ID>/locations/<Location ID>/generators/<Generator ID>`
    string generator_name = 3;
  }

  // Optional. Context of the conversation, including transcripts.
  ConversationContext conversation_context = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of trigger events. Generator will be triggered only if
  // it's trigger event is included here.
  repeated TriggerEvent trigger_events = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Conversations.GenerateStatelessSuggestion][google.cloud.dialogflow.v2beta1.Conversations.GenerateStatelessSuggestion].
message GenerateStatelessSuggestionResponse {
  // Required. Generated suggestion for a conversation.
  GeneratorSuggestion generator_suggestion = 1
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Conversations.SearchKnowledge][google.cloud.dialogflow.v2beta1.Conversations.SearchKnowledge].
message SearchKnowledgeRequest {
  // The source of the query. We use QuerySource to distinguish queries directly
  // entered by agents and suggested queries from
  // [Participants.SuggestKnowledgeAssist][google.cloud.dialogflow.v2beta1.Participants.SuggestKnowledgeAssist].
  // If SUGGESTED_QUERY source is specified, we will treat it as a continuation
  // of a SuggestKnowledgeAssist call.
  enum QuerySource {
    // Unknown query source.
    QUERY_SOURCE_UNSPECIFIED = 0;

    // The query is from agents.
    AGENT_QUERY = 1;

    // The query is a suggested query from
    // [Participants.SuggestKnowledgeAssist][google.cloud.dialogflow.v2beta1.Participants.SuggestKnowledgeAssist].
    SUGGESTED_QUERY = 2;
  }

  // Configuration specific to search queries with data stores.
  message SearchConfig {
    // Boost specifications for data stores.
    message BoostSpecs {
      // Boost specification to boost certain documents.
      // A copy of google.cloud.discoveryengine.v1main.BoostSpec, field
      // documentation is available at
      // https://cloud.google.com/generative-ai-app-builder/docs/reference/rest/v1alpha/BoostSpec
      message BoostSpec {
        // Boost applies to documents which match a condition.
        message ConditionBoostSpec {
          // Specification for custom ranking based on customer specified
          // attribute
          // value. It provides more controls for customized ranking than the
          // simple (condition, boost) combination above.
          message BoostControlSpec {
            // The attribute(or function) for which the custom ranking is to be
            // applied.
            enum AttributeType {
              // Unspecified AttributeType.
              ATTRIBUTE_TYPE_UNSPECIFIED = 0;

              // The value of the numerical field will be used to dynamically
              // update the boost amount. In this case, the attribute_value (the
              // x value) of the control point will be the actual value of the
              // numerical field for which the boost_amount is specified.
              NUMERICAL = 1;

              // For the freshness use case the attribute value will be the
              // duration between the current time and the date in the datetime
              // field specified. The value must be formatted as an XSD
              // `dayTimeDuration` value (a restricted subset of an ISO 8601
              // duration value). The pattern for this is:
              // `[nD][T[nH][nM][nS]]`. E.g. `5D`, `3DT12H30M`, `T24H`.
              FRESHNESS = 2;
            }

            // The interpolation type to be applied. Default will be linear
            // (Piecewise Linear).
            enum InterpolationType {
              // Interpolation type is unspecified. In this case, it defaults to
              // Linear.
              INTERPOLATION_TYPE_UNSPECIFIED = 0;

              // Piecewise linear interpolation will be applied.
              LINEAR = 1;
            }

            // The control points used to define the curve. The curve defined
            // through these control points can only be monotonically increasing
            // or decreasing(constant values are acceptable).
            message ControlPoint {}

            // Optional. The name of the field whose value will be used to
            // determine the boost amount.
            string field_name = 1 [(google.api.field_behavior) = OPTIONAL];

            // Optional. The attribute type to be used to determine the boost
            // amount. The attribute value can be derived from the field value
            // of the specified field_name. In the case of numerical it is
            // straightforward i.e. attribute_value = numerical_field_value. In
            // the case of freshness however, attribute_value = (time.now() -
            // datetime_field_value).
            AttributeType attribute_type = 2
                [(google.api.field_behavior) = OPTIONAL];

            // Optional. The interpolation type to be applied to connect the
            // control points listed below.
            InterpolationType interpolation_type = 3
                [(google.api.field_behavior) = OPTIONAL];

            // Optional. The control points used to define the curve. The
            // monotonic function (defined through the interpolation_type above)
            // passes through the control points listed here.
            repeated ControlPoint control_points = 4
                [(google.api.field_behavior) = OPTIONAL];
          }

          // Optional. An expression which specifies a boost condition. The
          // syntax and supported fields are the same as a filter expression.
          // Examples:
          //
          // * To boost documents with document ID "doc_1" or "doc_2", and
          // color
          //   "Red" or "Blue":
          //     * (id: ANY("doc_1", "doc_2")) AND (color: ANY("Red","Blue"))
          string condition = 1 [(google.api.field_behavior) = OPTIONAL];

          // Optional. Strength of the condition boost, which should be in [-1,
          // 1]. Negative boost means demotion. Default is 0.0.
          //
          // Setting to 1.0 gives the document a big promotion. However, it does
          // not necessarily mean that the boosted document will be the top
          // result at all times, nor that other documents will be excluded.
          // Results could still be shown even when none of them matches the
          // condition. And results that are significantly more relevant to the
          // search query can still trump your heavily favored but irrelevant
          // documents.
          //
          // Setting to -1.0 gives the document a big demotion. However, results
          // that are deeply relevant might still be shown. The document will
          // have an upstream battle to get a fairly high ranking, but it is not
          // blocked out completely.
          //
          // Setting to 0.0 means no boost applied. The boosting condition is
          // ignored.
          float boost = 2 [(google.api.field_behavior) = OPTIONAL];

          // Optional. Complex specification for custom ranking based on
          // customer defined attribute value.
          BoostControlSpec boost_control_spec = 4
              [(google.api.field_behavior) = OPTIONAL];
        }

        // Optional. Condition boost specifications. If a document matches
        // multiple conditions in the specifictions, boost scores from these
        // specifications are all applied and combined in a non-linear way.
        // Maximum number of specifications is 20.
        repeated ConditionBoostSpec condition_boost_specs = 1
            [(google.api.field_behavior) = OPTIONAL];
      }

      // Optional. Data Stores where the boosting configuration is applied. The
      // full names of the referenced data stores. Formats:
      // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`
      // `projects/{project}/locations/{location}/dataStores/{data_store}`
      repeated string data_stores = 1 [
        (google.api.field_behavior) = OPTIONAL,
        (google.api.resource_reference) = {
          type: "discoveryengine.googleapis.com/DataStore"
        }
      ];

      // Optional. A list of boosting specifications.
      repeated BoostSpec spec = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // Filter specification for data store queries.
    message FilterSpecs {
      // Optional. The data store where the filter configuration is applied.
      // Full resource name of data store, such as
      // projects/{project}/locations/{location}/collections/{collectionId}/
      // dataStores/{dataStoreId}.
      repeated string data_stores = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The filter expression to be applied.
      // Expression syntax is documented at
      // https://cloud.google.com/generative-ai-app-builder/docs/filter-search-metadata#filter-expression-syntax
      string filter = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. Boost specifications for data stores.
    repeated BoostSpecs boost_specs = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Filter specification for data store queries.
    repeated FilterSpecs filter_specs = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The parent resource contains the conversation profile
  // Format: 'projects/<Project ID>' or `projects/<Project
  // ID>/locations/<Location ID>`.
  string parent = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. The natural language text query for knowledge search.
  TextInput query = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The conversation profile used to configure the search.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`.
  string conversation_profile = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationProfile"
    }
  ];

  // Required. The ID of the search session.
  // The session_id can be combined with Dialogflow V3 Agent ID retrieved from
  // conversation profile or on its own to identify a search session. The search
  // history of the same session will impact the search result. It's up to the
  // API caller to choose an appropriate `Session ID`. It can be a random number
  // or some type of session identifiers (preferably hashed). The length must
  // not exceed 36 characters.
  string session_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The conversation (between human agent and end user) where the
  // search request is triggered. Format: `projects/<Project
  // ID>/locations/<Location ID>/conversations/<Conversation ID>`.
  string conversation = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Conversation"
    }
  ];

  // Optional. The name of the latest conversation message when the request is
  // triggered.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversations/<Conversation ID>/messages/<Message ID>`.
  string latest_message = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Message"
    }
  ];

  // Optional. The source of the query in the request.
  QuerySource query_source = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Information about the end-user to improve the relevance and
  // accuracy of generative answers.
  //
  // This will be interpreted and used by a language model, so, for good
  // results, the data should be self-descriptive, and in a simple structure.
  //
  // Example:
  //
  // ```json
  // {
  //   "subscription plan": "Business Premium Plus",
  //   "devices owned": [
  //     {"model": "Google Pixel 7"},
  //     {"model": "Google Pixel Tablet"}
  //   ]
  // }
  // ```
  google.protobuf.Struct end_user_metadata = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration specific to search queries with data stores.
  SearchConfig search_config = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether to search the query exactly without query rewrite.
  bool exact_search = 14 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Conversations.SearchKnowledge][google.cloud.dialogflow.v2beta1.Conversations.SearchKnowledge].
message SearchKnowledgeResponse {
  // Most relevant snippets extracted from articles in the given knowledge base,
  // ordered by confidence.
  repeated SearchKnowledgeAnswer answers = 2;

  // The rewritten query used to search knowledge.
  string rewritten_query = 3;
}

// Represents a SearchKnowledge answer.
message SearchKnowledgeAnswer {
  // The type of the answer.
  enum AnswerType {
    // The answer has a unspecified type.
    ANSWER_TYPE_UNSPECIFIED = 0;

    // The answer is from FAQ documents.
    FAQ = 1;

    // The answer is from generative model.
    GENERATIVE = 2;

    // The answer is from intent matching.
    INTENT = 3;
  }

  // The sources of the answers.
  message AnswerSource {
    // The title of the article.
    string title = 1;

    // The URI of the article.
    string uri = 2;

    // The relevant snippet of the article.
    string snippet = 3;

    // Metadata associated with the article.
    google.protobuf.Struct metadata = 5;
  }

  // The piece of text from the knowledge base documents that answers
  // the search query
  string answer = 1;

  // The type of the answer.
  AnswerType answer_type = 2;

  // All sources used to generate the answer.
  repeated AnswerSource answer_sources = 3;

  // The name of the answer record.
  // Format: `projects/<Project ID>/locations/<location ID>/answer
  // Records/<Answer Record ID>`
  string answer_record = 5;
}
