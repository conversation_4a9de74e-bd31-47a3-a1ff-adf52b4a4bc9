{"methodConfig": [{"name": [{"service": "google.cloud.dialogflow.v2beta1.Agents"}, {"service": "google.cloud.dialogflow.v2beta1.AnswerRecords"}, {"service": "google.cloud.dialogflow.v2beta1.AsynchronousFulfillment"}, {"service": "google.cloud.dialogflow.v2beta1.Contexts"}, {"service": "google.cloud.dialogflow.v2beta1.ConversationalTrainingAssignments"}, {"service": "google.cloud.dialogflow.v2beta1.ConversationalTrainingMembers"}, {"service": "google.cloud.dialogflow.v2beta1.ConversationalTrainingModules"}, {"service": "google.cloud.dialogflow.v2beta1.ConversationalTrainingTeams"}, {"service": "google.cloud.dialogflow.v2beta1.ConversationProfiles"}, {"service": "google.cloud.dialogflow.v2beta1.Conversations"}, {"service": "google.cloud.dialogflow.v2beta1.ConversationDatasets"}, {"service": "google.cloud.dialogflow.v2beta1.ConversationModels"}, {"service": "google.cloud.dialogflow.v2beta1.ConversationProfiles"}, {"service": "google.cloud.dialogflow.v2beta1.Documents"}, {"service": "google.cloud.dialogflow.v2beta1.DocumentCollections"}, {"service": "google.cloud.dialogflow.v2beta1.EntityTypes"}, {"service": "google.cloud.dialogflow.v2beta1.Environments"}, {"service": "google.cloud.dialogflow.v2beta1.FeatureCreationFlows"}, {"service": "google.cloud.dialogflow.v2beta1.Fulfillments"}, {"service": "google.cloud.dialogflow.v2beta1.Generators"}, {"service": "google.cloud.dialogflow.v2beta1.HumanAgentAssistants"}, {"service": "google.cloud.dialogflow.v2beta1.HumanAssistService"}, {"service": "google.cloud.dialogflow.v2beta1.Integrations"}, {"service": "google.cloud.dialogflow.v2beta1.Intents"}, {"service": "google.cloud.dialogflow.v2beta1.KnowledgeBases"}, {"service": "google.cloud.dialogflow.v2beta1.ModelEvaluations"}, {"service": "google.cloud.dialogflow.v2beta1.Participants"}, {"service": "google.cloud.dialogflow.v2beta1.PhoneNumbers"}, {"service": "google.cloud.dialogflow.v2beta1.PhoneNumberOrders"}, {"service": "google.cloud.dialogflow.v2beta1.SecuritySettingsService"}, {"service": "google.cloud.dialogflow.v2beta1.Sessions"}, {"service": "google.cloud.dialogflow.v2beta1.SessionEntityTypes"}, {"service": "google.cloud.dialogflow.v2beta1.SipTrunks"}, {"service": "google.cloud.dialogflow.v2beta1.Tools"}, {"service": "google.cloud.dialogflow.v2beta1.Versions"}, {"service": "google.cloud.dialogflow.v2beta1.InternalDatasetService"}, {"service": "google.cloud.dialogflow.v2beta1.IssueModelService"}, {"service": "google.cloud.dialogflow.v2beta1.AogService"}, {"service": "google.cloud.dialogflow.v2beta1.Tiers"}, {"service": "google.cloud.dialogflow.v2beta1.EncryptionSpecService"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.aam.v2beta1.AamAdminTool", "method": "ExportIntent"}, {"service": "google.cloud.dialogflow.v2beta1.SessionHistory", "method": "SearchSessionConversations"}], "timeout": "180s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.v2beta1.Participants", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.dialogflow.v2beta1.Sessions", "method": "DetectIntent"}], "timeout": "220s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.v2beta1.Participants", "method": "StreamingAnalyzeContent"}, {"service": "google.cloud.dialogflow.v2beta1.Sessions", "method": "StreamingDetectIntent"}], "timeout": "220s"}, {"name": [{"service": "google.cloud.dialogflow.v2beta1.Conversations", "method": "StreamingListUpcomingCallCompanionEvents"}], "timeout": "600s"}, {"name": [{"service": "google.cloud.dialogflow.v2beta1.Conversations", "method": "StreamingListCallCompanionEvents"}], "timeout": "600s"}]}