// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "EncryptionSpecProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";

// Manages encryption spec settings for Dialogflow and Agent Assist.
service EncryptionSpecService {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Gets location-level encryption key specification.
  rpc GetEncryptionSpec(GetEncryptionSpecRequest) returns (EncryptionSpec) {
    option (google.api.http) = {
      get: "/v2beta1/{name=projects/*/locations/*/encryptionSpec}"
    };
    option (google.api.method_signature) = "name";
  }

  // Initializes a location-level encryption key specification.  An error will
  // be thrown if the location has resources already created before the
  // initialization. Once the encryption specification is initialized at a
  // location, it is immutable and all newly created resources under the
  // location will be encrypted with the existing specification.
  rpc InitializeEncryptionSpec(InitializeEncryptionSpecRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{encryption_spec.name=projects/*/locations/*/encryptionSpec}:initialize"
      body: "*"
    };
    option (google.api.method_signature) = "encryption_spec";
    option (google.longrunning.operation_info) = {
      response_type: "InitializeEncryptionSpecResponse"
      metadata_type: "InitializeEncryptionSpecMetadata"
    };
  }
}

// The request to get location-level encryption specification.
message GetEncryptionSpecRequest {
  // Required. The name of the encryption spec resource to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EncryptionSpec"
    }
  ];
}

// A customer-managed encryption key specification that can be applied to all
// created resources (e.g. Conversation).
message EncryptionSpec {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/EncryptionSpec"
    pattern: "projects/{project}/locations/{location}/encryptionSpec"
    plural: "encryptionSpecs"
    singular: "encryptionSpec"
  };

  // Immutable. The resource name of the encryption key specification resource.
  // Format:
  // projects/{project}/locations/{location}/encryptionSpec
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. The name of customer-managed encryption key that is used to
  // secure a resource and its sub-resources. If empty, the resource is secured
  // by the default Google encryption key. Only the key in the same location as
  // this resource is allowed to be used for encryption. Format:
  // `projects/{project}/locations/{location}/keyRings/{keyRing}/cryptoKeys/{key}`
  string kms_key = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request to initialize a location-level encryption specification.
message InitializeEncryptionSpecRequest {
  // Required. The encryption spec used for CMEK encryption. It is required that
  // the kms key is in the same region as the endpoint. The same key will be
  // used for all provisioned resources, if encryption is available. If the
  // kms_key_name is left empty, no encryption will be enforced.
  EncryptionSpec encryption_spec = 1 [(google.api.field_behavior) = REQUIRED];
}

// The response to initialize a location-level encryption specification.
message InitializeEncryptionSpecResponse {}

// Metadata for initializing a location-level encryption specification.
message InitializeEncryptionSpecMetadata {
  // Output only. The original request for initialization.
  InitializeEncryptionSpecRequest request = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
