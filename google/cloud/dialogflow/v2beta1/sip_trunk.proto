// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "SipTrunkProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";

// Service for managing [SipTrunks][google.cloud.dialogflow.v2beta1.SipTrunk].
service SipTrunks {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Creates a SipTrunk for a specified location.
  rpc CreateSipTrunk(CreateSipTrunkRequest) returns (SipTrunk) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/locations/*}/sipTrunks"
      body: "sip_trunk"
    };
    option (google.api.method_signature) = "parent,sip_trunk";
  }

  // Deletes a specified SipTrunk.
  rpc DeleteSipTrunk(DeleteSipTrunkRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2beta1/{name=projects/*/locations/*/sipTrunks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of SipTrunks in the specified location.
  rpc ListSipTrunks(ListSipTrunksRequest) returns (ListSipTrunksResponse) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*/locations/*}/sipTrunks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified SipTrunk.
  rpc GetSipTrunk(GetSipTrunkRequest) returns (SipTrunk) {
    option (google.api.http) = {
      get: "/v2beta1/{name=projects/*/locations/*/sipTrunks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the specified SipTrunk.
  rpc UpdateSipTrunk(UpdateSipTrunkRequest) returns (SipTrunk) {
    option (google.api.http) = {
      patch: "/v2beta1/{sip_trunk.name=projects/*/locations/*/sipTrunks/*}"
      body: "sip_trunk"
    };
    option (google.api.method_signature) = "sip_trunk,update_mask";
  }
}

// The request message for
// [SipTrunks.CreateSipTrunk][google.cloud.dialogflow.v2beta1.SipTrunks.CreateSipTrunk].
message CreateSipTrunkRequest {
  // Required. The location to create a SIP trunk for.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/SipTrunk"
    }
  ];

  // Required. The SIP trunk to create.
  SipTrunk sip_trunk = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [SipTrunks.DeleteSipTrunk][google.cloud.dialogflow.v2beta1.SipTrunks.DeleteSipTrunk].
message DeleteSipTrunkRequest {
  // Required. The name of the SIP trunk to delete.
  // Format: `projects/<Project ID>/locations/<Location ID>/sipTrunks/<SipTrunk
  // ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/SipTrunk"
    }
  ];
}

// The request message for
// [SipTrunks.ListSipTrunks][google.cloud.dialogflow.v2beta1.SipTrunks.ListSipTrunks].
message ListSipTrunksRequest {
  // Required. The location to list SIP trunks from.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/SipTrunk"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [SipTrunks.ListSipTrunks][google.cloud.dialogflow.v2beta1.SipTrunks.ListSipTrunks].
message ListSipTrunksResponse {
  // The list of SIP trunks.
  repeated SipTrunk sip_trunks = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [SipTrunks.GetSipTrunk][google.cloud.dialogflow.v2beta1.SipTrunks.GetSipTrunk].
message GetSipTrunkRequest {
  // Required. The name of the SIP trunk to delete.
  // Format: `projects/<Project ID>/locations/<Location ID>/sipTrunks/<SipTrunk
  // ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/SipTrunk"
    }
  ];
}

// The request message for
// [SipTrunks.UpdateSipTrunk][google.cloud.dialogflow.v2beta1.SipTrunks.UpdateSipTrunk].
message UpdateSipTrunkRequest {
  // Required. The SipTrunk to update.
  SipTrunk sip_trunk = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The mask to control which fields get updated. If the mask is not
  // present, all fields will be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// SipTrunk is the resource that represents a SIP trunk to connect to Google
// Telephony platform SIP trunking service.
message SipTrunk {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/SipTrunk"
    pattern: "projects/{project}/locations/{location}/sipTrunks/{siptrunk}"
    plural: "sipTrunks"
    singular: "sipTrunk"
  };

  // Identifier. The unique identifier of the SIP trunk.
  // Format: `projects/<Project ID>/locations/<Location ID>/sipTrunks/<SipTrunk
  // ID>`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The expected hostnames in the peer certificate from partner that
  // is used for TLS authentication.
  repeated string expected_hostname = 2
      [(google.api.field_behavior) = REQUIRED];

  // Output only. Connections of the SIP trunk.
  repeated Connection connections = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Human readable alias for this trunk.
  string display_name = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Represents a connection for SIP Trunk.
message Connection {
  // The state of Sip Trunk connection.
  enum State {
    // SIP Trunk connection state is Not specified.
    STATE_UNSPECIFIED = 0;

    // SIP Trunk connection is connected.
    CONNECTED = 1;

    // SIP Trunk connection is disconnected.
    DISCONNECTED = 2;

    // SIP Trunk connection has authentication error.
    AUTHENTICATION_FAILED = 3;

    // SIP Trunk connection is keepalive.
    KEEPALIVE = 4;
  }

  // The state of Sip Trunk certificate authentication.
  enum CertificateState {
    // Certificate state is not specified.
    CERTIFICATE_STATE_UNSPECIFIED = 0;

    // Certificate is valid.
    CERTIFICATE_VALID = 1;

    // Catch all for any error not specified.
    CERTIFICATE_INVALID = 2;

    // Certificate leaf node has expired.
    CERTIFICATE_EXPIRED = 3;

    // There is no hostname defined to authenticate in SipTrunkingServer.
    CERTIFICATE_HOSTNAME_NOT_FOUND = 4;

    // No path found from the leaf certificate to any root.
    CERTIFICATE_UNAUTHENTICATED = 5;

    // Trust store does not exist.
    CERTIFICATE_TRUST_STORE_NOT_FOUND = 6;

    // Hostname has invalid format.
    CERTIFICATE_HOSTNAME_INVALID_FORMAT = 7;

    // Certificate has exhausted its quota.
    CERTIFICATE_QUOTA_EXCEEDED = 8;
  }

  // The error details of Sip Trunk connection authentication.
  message ErrorDetails {
    // Output only. The status of the certificate authentication.
    optional CertificateState certificate_state = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // The error message provided from SIP trunking auth service
    optional string error_message = 2;
  }

  // Output only. The unique identifier of the SIP Trunk connection.
  string connection_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the connection.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the connection status changed.
  optional google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The error details for the connection. Only populated when
  // authentication errors occur.
  optional ErrorDetails error_details = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
