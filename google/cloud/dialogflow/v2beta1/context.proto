// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "ContextProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";

// Service for managing [Contexts][google.cloud.dialogflow.v2beta1.Context].
service Contexts {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all contexts in the specified session.
  rpc ListContexts(ListContextsRequest) returns (ListContextsResponse) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*/agent/sessions/*}/contexts"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/agent/environments/*/users/*/sessions/*}/contexts"
      }
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*/agent/sessions/*}/contexts"
      }
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*/agent/environments/*/users/*/sessions/*}/contexts"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified context.
  rpc GetContext(GetContextRequest) returns (Context) {
    option (google.api.http) = {
      get: "/v2beta1/{name=projects/*/agent/sessions/*/contexts/*}"
      additional_bindings {
        get: "/v2beta1/{name=projects/*/agent/environments/*/users/*/sessions/*/contexts/*}"
      }
      additional_bindings {
        get: "/v2beta1/{name=projects/*/locations/*/agent/sessions/*/contexts/*}"
      }
      additional_bindings {
        get: "/v2beta1/{name=projects/*/locations/*/agent/environments/*/users/*/sessions/*/contexts/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a context.
  //
  // If the specified context already exists, overrides the context.
  rpc CreateContext(CreateContextRequest) returns (Context) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/agent/sessions/*}/contexts"
      body: "context"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/agent/environments/*/users/*/sessions/*}/contexts"
        body: "context"
      }
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent/sessions/*}/contexts"
        body: "context"
      }
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent/environments/*/users/*/sessions/*}/contexts"
        body: "context"
      }
    };
    option (google.api.method_signature) = "parent,context";
  }

  // Updates the specified context.
  rpc UpdateContext(UpdateContextRequest) returns (Context) {
    option (google.api.http) = {
      patch: "/v2beta1/{context.name=projects/*/agent/sessions/*/contexts/*}"
      body: "context"
      additional_bindings {
        patch: "/v2beta1/{context.name=projects/*/agent/environments/*/users/*/sessions/*/contexts/*}"
        body: "context"
      }
      additional_bindings {
        patch: "/v2beta1/{context.name=projects/*/locations/*/agent/sessions/*/contexts/*}"
        body: "context"
      }
      additional_bindings {
        patch: "/v2beta1/{context.name=projects/*/locations/*/agent/environments/*/users/*/sessions/*/contexts/*}"
        body: "context"
      }
    };
    option (google.api.method_signature) = "context,update_mask";
    option (google.api.method_signature) = "context";
  }

  // Deletes the specified context.
  rpc DeleteContext(DeleteContextRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2beta1/{name=projects/*/agent/sessions/*/contexts/*}"
      additional_bindings {
        delete: "/v2beta1/{name=projects/*/agent/environments/*/users/*/sessions/*/contexts/*}"
      }
      additional_bindings {
        delete: "/v2beta1/{name=projects/*/locations/*/agent/sessions/*/contexts/*}"
      }
      additional_bindings {
        delete: "/v2beta1/{name=projects/*/locations/*/agent/environments/*/users/*/sessions/*/contexts/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes all active contexts in the specified session.
  rpc DeleteAllContexts(DeleteAllContextsRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2beta1/{parent=projects/*/agent/sessions/*}/contexts"
      additional_bindings {
        delete: "/v2beta1/{parent=projects/*/agent/environments/*/users/*/sessions/*}/contexts"
      }
      additional_bindings {
        delete: "/v2beta1/{parent=projects/*/locations/*/agent/sessions/*}/contexts"
      }
      additional_bindings {
        delete: "/v2beta1/{parent=projects/*/locations/*/agent/environments/*/users/*/sessions/*}/contexts"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// Dialogflow contexts are similar to natural language context. If a person says
// to you "they are orange", you need context in order to understand what "they"
// is referring to. Similarly, for Dialogflow to handle an end-user expression
// like that, it needs to be provided with context in order to correctly match
// an intent.
//
// Using contexts, you can control the flow of a conversation. You can configure
// contexts for an intent by setting input and output contexts, which are
// identified by string names. When an intent is matched, any configured output
// contexts for that intent become active. While any contexts are active,
// Dialogflow is more likely to match intents that are configured with input
// contexts that correspond to the currently active contexts.
//
// For more information about context, see the
// [Contexts guide](https://cloud.google.com/dialogflow/docs/contexts-overview).
message Context {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Context"
    pattern: "projects/{project}/agent/sessions/{session}/contexts/{context}"
    pattern: "projects/{project}/agent/environments/{environment}/users/{user}/sessions/{session}/contexts/{context}"
    pattern: "projects/{project}/locations/{location}/agent/sessions/{session}/contexts/{context}"
    pattern: "projects/{project}/locations/{location}/agent/environments/{environment}/users/{user}/sessions/{session}/contexts/{context}"
  };

  // Required. The unique identifier of the context. Supported formats:
  // - `projects/<Project ID>/agent/sessions/<Session ID>/contexts/<Context
  //   ID>`,
  // - `projects/<Project ID>/locations/<Location ID>/agent/sessions/<Session
  //   ID>/contexts/<Context ID>`,
  // - `projects/<Project ID>/agent/environments/<Environment ID>/users/<User
  //   ID>/sessions/<Session ID>/contexts/<Context ID>`,
  // - `projects/<Project ID>/locations/<Location
  //   ID>/agent/environments/<Environment ID>/users/<User ID>/sessions/<Session
  //   ID>/contexts/<Context ID>`,
  //
  // The `Context ID` is always converted to lowercase, may only contain
  // characters in `a-zA-Z0-9_-%` and may be at most 250 bytes long.
  //
  // If `Environment ID` is not specified, we assume default 'draft'
  // environment. If `User ID` is not specified, we assume default '-' user.
  //
  // The following context names are reserved for internal use by Dialogflow.
  // You should not use these contexts or create contexts with these names:
  //
  // * `__system_counters__`
  // * `*_id_dialog_context`
  // * `*_dialog_params_size`
  string name = 1;

  // Optional. The number of conversational query requests after which the
  // context expires. The default is `0`. If set to `0`, the context expires
  // immediately. Contexts expire automatically after 20 minutes if there
  // are no matching queries.
  int32 lifespan_count = 2;

  // Optional. The collection of parameters associated with this context.
  //
  // Depending on your protocol or client library language, this is a
  // map, associative array, symbol table, dictionary, or JSON object
  // composed of a collection of (MapKey, MapValue) pairs:
  //
  // * MapKey type: string
  // * MapKey value: parameter name
  // * MapValue type: If parameter's entity type is a composite entity then use
  // map, otherwise, depending on the parameter value type, it could be one of
  // string, number, boolean, null, list or map.
  // * MapValue value: If parameter's entity type is a composite entity then use
  // map from composite entity property names to property values, otherwise,
  // use parameter value.
  google.protobuf.Struct parameters = 3;
}

// The request message for
// [Contexts.ListContexts][google.cloud.dialogflow.v2beta1.Contexts.ListContexts].
message ListContextsRequest {
  // Required. The session to list all contexts from. Supported formats:
  // - `projects/<Project ID>/agent/sessions/<Session ID>,
  // - `projects/<Project ID>/locations/<Location ID>/agent/sessions/<Session
  //   ID>`,
  // - `projects/<Project ID>/agent/environments/<Environment ID>/users/<User
  //   ID>/sessions/<Session ID>`,
  // - `projects/<Project ID>/locations/<Location
  //   ID>/agent/environments/<Environment ID>/users/<User ID>/sessions/<Session
  //   ID>`,
  //
  // If `Location ID` is not specified we assume default 'us' location. If
  // `Environment ID` is not specified, we assume default 'draft' environment.
  // If `User ID` is not specified, we assume default '-' user.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Context"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2;

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Contexts.ListContexts][google.cloud.dialogflow.v2beta1.Contexts.ListContexts].
message ListContextsResponse {
  // The list of contexts. There will be a maximum number of items
  // returned based on the page_size field in the request.
  repeated Context contexts = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [Contexts.GetContext][google.cloud.dialogflow.v2beta1.Contexts.GetContext].
message GetContextRequest {
  // Required. The name of the context. Supported formats:
  // - `projects/<Project ID>/agent/sessions/<Session ID>/contexts/<Context
  //   ID>`,
  // - `projects/<Project ID>/locations/<Location ID>/agent/sessions/<Session
  //   ID>/contexts/<Context ID>`,
  // - `projects/<Project ID>/agent/environments/<Environment ID>/users/<User
  //   ID>/sessions/<Session ID>/contexts/<Context ID>`,
  // - `projects/<Project ID>/locations/<Location
  //   ID>/agent/environments/<Environment ID>/users/<User ID>/sessions/<Session
  //   ID>/contexts/<Context ID>`,
  //
  // If `Location ID` is not specified we assume default 'us' location. If
  // `Environment ID` is not specified, we assume default 'draft' environment.
  // If `User ID` is not specified, we assume default '-' user.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Context"
    }
  ];
}

// The request message for
// [Contexts.CreateContext][google.cloud.dialogflow.v2beta1.Contexts.CreateContext].
message CreateContextRequest {
  // Required. The session to create a context for. Supported formats:
  // - `projects/<Project ID>/agent/sessions/<Session ID>,
  // - `projects/<Project ID>/locations/<Location ID>/agent/sessions/<Session
  //   ID>`,
  // - `projects/<Project ID>/agent/environments/<Environment ID>/users/<User
  //   ID>/sessions/<Session ID>`,
  // - `projects/<Project ID>/locations/<Location
  //   ID>/agent/environments/<Environment ID>/users/<User ID>/sessions/<Session
  //   ID>`,
  //
  // If `Location ID` is not specified we assume default 'us' location. If
  // `Environment ID` is not specified, we assume default 'draft' environment.
  // If `User ID` is not specified, we assume default '-' user.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Context"
    }
  ];

  // Required. The context to create.
  Context context = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Contexts.UpdateContext][google.cloud.dialogflow.v2beta1.Contexts.UpdateContext].
message UpdateContextRequest {
  // Required. The context to update.
  Context context = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [Contexts.DeleteContext][google.cloud.dialogflow.v2beta1.Contexts.DeleteContext].
message DeleteContextRequest {
  // Required. The name of the context to delete. Supported formats:
  // - `projects/<Project ID>/agent/sessions/<Session ID>/contexts/<Context
  //   ID>`,
  // - `projects/<Project ID>/locations/<Location ID>/agent/sessions/<Session
  //   ID>/contexts/<Context ID>`,
  // - `projects/<Project ID>/agent/environments/<Environment ID>/users/<User
  //   ID>/sessions/<Session ID>/contexts/<Context ID>`,
  // - `projects/<Project ID>/locations/<Location
  //   ID>/agent/environments/<Environment ID>/users/<User ID>/sessions/<Session
  //   ID>/contexts/<Context ID>`,
  //
  // If `Location ID` is not specified we assume default 'us' location. If
  // `Environment ID` is not specified, we assume default 'draft' environment.
  // If `User ID` is not specified, we assume default '-' user.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Context"
    }
  ];
}

// The request message for
// [Contexts.DeleteAllContexts][google.cloud.dialogflow.v2beta1.Contexts.DeleteAllContexts].
message DeleteAllContextsRequest {
  // Required. The name of the session to delete all contexts from. Supported
  // formats:
  // - `projects/<Project ID>/agent/sessions/<Session ID>,
  // - `projects/<Project ID>/locations/<Location ID>/agent/sessions/<Session
  //   ID>`,
  // - `projects/<Project ID>/agent/environments/<Environment ID>/users/<User
  //   ID>/sessions/<Session ID>`,
  // - `projects/<Project ID>/locations/<Location
  //   ID>/agent/environments/<Environment ID>/users/<User ID>/sessions/<Session
  //   ID>`,
  //
  // If `Location ID` is not specified we assume default 'us' location. If
  // `Environment ID` is not specified we assume default 'draft' environment. If
  // `User ID` is not specified, we assume default '-' user.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Context"
    }
  ];
}
