type: google.api.Service
config_version: 3
name: dialogflow.googleapis.com
title: Dialogflow API

apis:
- name: google.cloud.dialogflow.v2beta1.Agents
- name: google.cloud.dialogflow.v2beta1.AnswerRecords
- name: google.cloud.dialogflow.v2beta1.Contexts
- name: google.cloud.dialogflow.v2beta1.ConversationProfiles
- name: google.cloud.dialogflow.v2beta1.Conversations
- name: google.cloud.dialogflow.v2beta1.Documents
- name: google.cloud.dialogflow.v2beta1.EncryptionSpecService
- name: google.cloud.dialogflow.v2beta1.EntityTypes
- name: google.cloud.dialogflow.v2beta1.Environments
- name: google.cloud.dialogflow.v2beta1.Fulfillments
- name: google.cloud.dialogflow.v2beta1.Generators
- name: google.cloud.dialogflow.v2beta1.Intents
- name: google.cloud.dialogflow.v2beta1.KnowledgeBases
- name: google.cloud.dialogflow.v2beta1.Participants
- name: google.cloud.dialogflow.v2beta1.SessionEntityTypes
- name: google.cloud.dialogflow.v2beta1.Sessions
- name: google.cloud.dialogflow.v2beta1.SipTrunks
- name: google.cloud.dialogflow.v2beta1.Versions
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.dialogflow.v2beta1.BatchUpdateEntityTypesResponse
- name: google.cloud.dialogflow.v2beta1.BatchUpdateIntentsResponse
- name: google.cloud.dialogflow.v2beta1.ClearSuggestionFeatureConfigOperationMetadata
- name: google.cloud.dialogflow.v2beta1.ConversationEvent
- name: google.cloud.dialogflow.v2beta1.ExportAgentResponse
- name: google.cloud.dialogflow.v2beta1.HumanAgentAssistantEvent
- name: google.cloud.dialogflow.v2beta1.ImportDocumentsResponse
- name: google.cloud.dialogflow.v2beta1.InitializeEncryptionSpecMetadata
- name: google.cloud.dialogflow.v2beta1.KnowledgeOperationMetadata
- name: google.cloud.dialogflow.v2beta1.OriginalDetectIntentRequest
- name: google.cloud.dialogflow.v2beta1.SetSuggestionFeatureConfigOperationMetadata
- name: google.cloud.dialogflow.v2beta1.WebhookRequest
- name: google.cloud.dialogflow.v2beta1.WebhookResponse

documentation:
  summary: |-
    Builds conversational interfaces (for example, chatbots, and voice-powered
    apps and devices).
  overview: |-
    <!-- mdformat off(presubmit failing, mdformat is as well) --> Dialogflow is
    a natural language understanding platform that makes it easy
    to design and integrate a conversational user interface into your mobile
    app, web application, device, bot, interactive voice response system, and
    so on. Using Dialogflow, you can provide new and engaging ways for
    users to interact with your product.

    Dialogflow can analyze multiple types of input from your customers,
    including text or audio inputs (like from a phone or voice recording).
    It can also respond to your customers in a couple of ways, either through
    text or with synthetic speech.

    For more information, see the
    [Dialogflow documentation](https://cloud.google.com/dialogflow/docs).
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v2beta1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v2beta1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v2beta1/{name=projects/*/operations/*}:cancel'
    additional_bindings:
    - post: '/v2beta1/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2beta1/{name=projects/*/operations/*}'
    additional_bindings:
    - get: '/v2beta1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2beta1/{name=projects/*}/operations'
    additional_bindings:
    - get: '/v2beta1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.dialogflow.v2beta1.Agents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.AnswerRecords.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.Contexts.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.ConversationProfiles.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.Conversations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.Documents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2beta1.EncryptionSpecService.GetEncryptionSpec
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2beta1.EncryptionSpecService.InitializeEncryptionSpec
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.EntityTypes.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.Environments.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2beta1.Fulfillments.GetFulfillment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2beta1.Fulfillments.UpdateFulfillment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.Generators.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.Intents.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.KnowledgeBases.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.Participants.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.SessionEntityTypes.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2beta1.Sessions.DetectIntent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.dialogflow.v2beta1.Sessions.StreamingDetectIntent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.SipTrunks.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.cloud.dialogflow.v2beta1.Versions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/dialogflow
