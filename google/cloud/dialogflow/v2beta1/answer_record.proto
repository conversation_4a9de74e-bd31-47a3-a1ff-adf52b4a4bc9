// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/v2beta1/participant.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "AnswerRecordsProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";

// Service for managing
// [AnswerRecords][google.cloud.dialogflow.v2beta1.AnswerRecord].
service AnswerRecords {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Deprecated.
  // Retrieves a specific answer record.
  rpc GetAnswerRecord(GetAnswerRecordRequest) returns (AnswerRecord) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v2beta1/{name=projects/*/answerRecords/*}"
      additional_bindings {
        get: "/v2beta1/{name=projects/*/locations/*/answerRecords/*}"
      }
    };
  }

  // Returns the list of all answer records in the specified project in reverse
  // chronological order.
  rpc ListAnswerRecords(ListAnswerRecordsRequest)
      returns (ListAnswerRecordsResponse) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*}/answerRecords"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*}/answerRecords"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the specified answer record.
  rpc UpdateAnswerRecord(UpdateAnswerRecordRequest) returns (AnswerRecord) {
    option (google.api.http) = {
      patch: "/v2beta1/{answer_record.name=projects/*/answerRecords/*}"
      body: "answer_record"
      additional_bindings {
        patch: "/v2beta1/{answer_record.name=projects/*/locations/*/answerRecords/*}"
        body: "answer_record"
      }
    };
    option (google.api.method_signature) = "answer_record,update_mask";
  }
}

// Answer records are records to manage answer history and feedbacks for
// Dialogflow.
//
// Currently, answer record includes:
//
// - human agent assistant article suggestion
// - human agent assistant faq article
//
// It doesn't include:
//
// - `DetectIntent` intent matching
// - `DetectIntent` knowledge
//
// Answer records are not related to the conversation history in the
// Dialogflow Console. A Record is generated even when the end-user disables
// conversation history in the console. Records are created when there's a human
// agent assistant suggestion generated.
//
// A typical workflow for customers provide feedback to an answer is:
//
// 1. For human agent assistant, customers get suggestion via ListSuggestions
//    API. Together with the answers,
//    [AnswerRecord.name][google.cloud.dialogflow.v2beta1.AnswerRecord.name] are
//    returned to the customers.
// 2. The customer uses the
// [AnswerRecord.name][google.cloud.dialogflow.v2beta1.AnswerRecord.name] to
// call the
//    [UpdateAnswerRecord][] method to send feedback about a specific answer
//    that they believe is wrong.
message AnswerRecord {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/AnswerRecord"
    pattern: "projects/{project}/answerRecords/{answer_record}"
    pattern: "projects/{project}/locations/{location}/answerRecords/{answer_record}"
  };

  // The unique identifier of this answer record.
  // Required for
  // [AnswerRecords.UpdateAnswerRecord][google.cloud.dialogflow.v2beta1.AnswerRecords.UpdateAnswerRecord]
  // method. Format: `projects/<Project ID>/locations/<Location
  // ID>/answerRecords/<Answer Record ID>`.
  string name = 1;

  // Optional. The AnswerFeedback for this record. You can set this with
  // [AnswerRecords.UpdateAnswerRecord][google.cloud.dialogflow.v2beta1.AnswerRecords.UpdateAnswerRecord]
  // in order to give us feedback about this answer.
  AnswerFeedback answer_feedback = 3;

  // Output only. The record for this answer.
  oneof record {
    // Output only. The record for human agent assistant.
    AgentAssistantRecord agent_assistant_record = 4;
  }
}

// Represents a record of a human agent assistant answer.
message AgentAssistantRecord {
  // Output only. The agent assistant answer.
  oneof answer {
    // Output only. The article suggestion answer.
    ArticleAnswer article_suggestion_answer = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The FAQ answer.
    FaqAnswer faq_answer = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Dialogflow assist answer.
    DialogflowAssistAnswer dialogflow_assist_answer = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// Represents feedback the customer has about the quality & correctness of a
// certain answer in a conversation.
message AnswerFeedback {
  // The correctness level of an answer.
  enum CorrectnessLevel {
    // Correctness level unspecified.
    CORRECTNESS_LEVEL_UNSPECIFIED = 0;

    // Answer is totally wrong.
    NOT_CORRECT = 1;

    // Answer is partially correct.
    PARTIALLY_CORRECT = 2;

    // Answer is fully correct.
    FULLY_CORRECT = 3;
  }

  // The correctness level of the specific answer.
  CorrectnessLevel correctness_level = 1;

  // Normally, detail feedback is provided when answer is not fully correct.
  oneof detail_feedback {
    // Optional. Detail feedback of agent assistant suggestions.
    AgentAssistantFeedback agent_assistant_detail_feedback = 2;
  }

  // Indicates whether the answer/item was clicked by the human agent
  // or not. Default to false.
  // For knowledge search, the answer record is considered to be clicked if the
  // answer was copied or any URI was clicked.
  bool clicked = 3;

  // Time when the answer/item was clicked.
  google.protobuf.Timestamp click_time = 5;

  // Indicates whether the answer/item was displayed to the human
  // agent in the agent desktop UI. Default to false.
  bool displayed = 4;

  // Time when the answer/item was displayed.
  google.protobuf.Timestamp display_time = 6;
}

// Detail feedback of Agent Assistant result.
message AgentAssistantFeedback {
  // Relevance of an answer.
  enum AnswerRelevance {
    // Answer relevance unspecified.
    ANSWER_RELEVANCE_UNSPECIFIED = 0;

    // Answer is irrelevant to query.
    IRRELEVANT = 1;

    // Answer is relevant to query.
    RELEVANT = 2;
  }

  // Correctness of document.
  enum DocumentCorrectness {
    // Document correctness unspecified.
    DOCUMENT_CORRECTNESS_UNSPECIFIED = 0;

    // Information in document is incorrect.
    INCORRECT = 1;

    // Information in document is correct.
    CORRECT = 2;
  }

  // Efficiency of document.
  enum DocumentEfficiency {
    // Document efficiency unspecified.
    DOCUMENT_EFFICIENCY_UNSPECIFIED = 0;

    // Document is inefficient.
    INEFFICIENT = 1;

    // Document is efficient.
    EFFICIENT = 2;
  }

  // Feedback for conversation summarization.
  message SummarizationFeedback {
    // Timestamp when composing of the summary starts.
    google.protobuf.Timestamp start_timestamp = 1;

    // Timestamp when the summary was submitted.
    google.protobuf.Timestamp submit_timestamp = 2;

    // Text of actual submitted summary.
    string summary_text = 3;

    // Optional. Actual text sections of submitted summary.
    map<string, string> text_sections = 4
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Feedback for knowledge search.
  message KnowledgeSearchFeedback {
    // Whether the answer was copied by the human agent or not.
    // If the value is set to be true,
    // [AnswerFeedback.clicked][google.cloud.dialogflow.v2beta1.AnswerFeedback.clicked]
    // will be updated to be true.
    bool answer_copied = 1;

    // The URIs clicked by the human agent. The value is appended for each
    // UpdateAnswerRecordRequest.
    // If the value is not empty,
    // [AnswerFeedback.clicked][google.cloud.dialogflow.v2beta1.AnswerFeedback.clicked]
    // will be updated to be true.
    repeated string clicked_uris = 2;
  }

  // Feedback for knowledge assist.
  message KnowledgeAssistFeedback {
    // Whether the suggested answer was copied by the human agent.
    // If the value is set to be true,
    // [AnswerFeedback.clicked][google.cloud.dialogflow.v2beta1.AnswerFeedback.clicked]
    // will be updated to be true.
    bool answer_copied = 1;

    // The URIs clicked by the human agent. The value is appended for each
    // [UpdateAnswerRecordRequest][google.cloud.dialogflow.v2beta1.UpdateAnswerRecordRequest].
    // If the value is not empty,
    // [AnswerFeedback.clicked][google.cloud.dialogflow.v2beta1.AnswerFeedback.clicked]
    // will be updated to be true.
    repeated string clicked_uris = 2;
  }

  // Optional. Whether or not the suggested answer is relevant.
  //
  // For example:
  //
  // * Query: "Can I change my mailing address?"
  // * Suggested document says: "Items must be returned/exchanged within 60
  //   days of the purchase date."
  // * [answer_relevance][google.cloud.dialogflow.v2beta1.AgentAssistantFeedback.answer_relevance]: [AnswerRelevance.IRRELEVANT][google.cloud.dialogflow.v2beta1.AgentAssistantFeedback.AnswerRelevance.IRRELEVANT]
  AnswerRelevance answer_relevance = 1;

  // Optional. Whether or not the information in the document is correct.
  //
  // For example:
  //
  // * Query: "Can I return the package in 2 days once received?"
  // * Suggested document says: "Items must be returned/exchanged within 60
  //   days of the purchase date."
  // * Ground truth: "No return or exchange is allowed."
  // * [document_correctness]: INCORRECT
  DocumentCorrectness document_correctness = 2;

  // Optional. Whether or not the suggested document is efficient. For example,
  // if the document is poorly written, hard to understand, hard to use or
  // too long to find useful information,
  // [document_efficiency][google.cloud.dialogflow.v2beta1.AgentAssistantFeedback.document_efficiency]
  // is
  // [DocumentEfficiency.INEFFICIENT][google.cloud.dialogflow.v2beta1.AgentAssistantFeedback.DocumentEfficiency.INEFFICIENT].
  DocumentEfficiency document_efficiency = 3;

  // Feedback for conversation summarization.
  SummarizationFeedback summarization_feedback = 4;

  // Optional. Feedback for knowledge search.
  KnowledgeSearchFeedback knowledge_search_feedback = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Feedback for knowledge assist.
  KnowledgeAssistFeedback knowledge_assist_feedback = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [AnswerRecords.GetAnswerRecord][google.cloud.dialogflow.v2beta1.AnswerRecords.GetAnswerRecord].
message GetAnswerRecordRequest {
  // Required. The name of the answer record to retrieve.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/answerRecords/<Answer Record Id>`.
  string name = 1;
}

// Request message for
// [AnswerRecords.ListAnswerRecords][google.cloud.dialogflow.v2beta1.AnswerRecords.ListAnswerRecords].
message ListAnswerRecordsRequest {
  // Required. The project to list all answer records for in reverse
  // chronological order. Format: `projects/<Project ID>/locations/<Location
  // ID>`.
  string parent = 1 [(google.api.resource_reference) = {
    child_type: "dialogflow.googleapis.com/AnswerRecord"
  }];

  // Optional. Filters to restrict results to specific answer records.
  //
  // For more information about filtering, see
  // [API Filtering](https://aip.dev/160).
  string filter = 2 [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of records to return in a single page.
  // The server may return fewer records than this. If unspecified, we use 10.
  // The maximum is 100.
  int32 page_size = 3;

  // Optional. The
  // [ListAnswerRecordsResponse.next_page_token][google.cloud.dialogflow.v2beta1.ListAnswerRecordsResponse.next_page_token]
  // value returned from a previous list request used to continue listing on
  // the next page.
  string page_token = 4;
}

// Response message for
// [AnswerRecords.ListAnswerRecords][google.cloud.dialogflow.v2beta1.AnswerRecords.ListAnswerRecords].
message ListAnswerRecordsResponse {
  // The list of answer records.
  repeated AnswerRecord answer_records = 1;

  // A token to retrieve next page of results. Or empty if there are no more
  // results.
  // Pass this value in the
  // [ListAnswerRecordsRequest.page_token][google.cloud.dialogflow.v2beta1.ListAnswerRecordsRequest.page_token]
  // field in the subsequent call to `ListAnswerRecords` method to retrieve the
  // next page of results.
  string next_page_token = 2;
}

// Request message for
// [AnswerRecords.UpdateAnswerRecord][google.cloud.dialogflow.v2beta1.AnswerRecords.UpdateAnswerRecord].
message UpdateAnswerRecordRequest {
  // Required. Answer record to update.
  AnswerRecord answer_record = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 2;
}
