// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.kms.inventory.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/kms/v1/resources.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Kms.Inventory.V1";
option go_package = "cloud.google.com/go/kms/inventory/apiv1/inventorypb;inventorypb";
option java_multiple_files = true;
option java_outer_classname = "KeyDashboardServiceProto";
option java_package = "com.google.cloud.kms.inventory.v1";
option php_namespace = "Google\\Cloud\\Kms\\Inventory\\V1";

// Provides a cross-region view of all Cloud KMS keys in a given Cloud project.
service KeyDashboardService {
  option (google.api.default_host) = "kmsinventory.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns cryptographic keys managed by Cloud KMS in a given Cloud project.
  // Note that this data is sourced from snapshots, meaning it may not
  // completely reflect the actual state of key metadata at call time.
  rpc ListCryptoKeys(ListCryptoKeysRequest) returns (ListCryptoKeysResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/cryptoKeys"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [KeyDashboardService.ListCryptoKeys][google.cloud.kms.inventory.v1.KeyDashboardService.ListCryptoKeys].
message ListCryptoKeysRequest {
  // Required. The Google Cloud project for which to retrieve key metadata, in
  // the format `projects/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Optional. The maximum number of keys to return. The service may return
  // fewer than this value. If unspecified, at most 1000 keys will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pass this into a subsequent request in order to receive the next
  // page of results.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [KeyDashboardService.ListCryptoKeys][google.cloud.kms.inventory.v1.KeyDashboardService.ListCryptoKeys].
message ListCryptoKeysResponse {
  // The list of [CryptoKeys][google.cloud.kms.v1.CryptoKey].
  repeated google.cloud.kms.v1.CryptoKey crypto_keys = 1;

  // The page token returned from the previous response if the next page is
  // desired.
  string next_page_token = 2;
}
