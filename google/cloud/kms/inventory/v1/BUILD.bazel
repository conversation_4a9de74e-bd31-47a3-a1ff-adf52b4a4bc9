# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "inventory_proto",
    srcs = [
        "key_dashboard_service.proto",
        "key_tracking_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/cloud/kms/v1:kms_proto",  # manual change to reference kms_proto
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "inventory_proto_with_info",
    deps = [
        ":inventory_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "inventory_java_proto",
    deps = [":inventory_proto"],
)

java_grpc_library(
    name = "inventory_java_grpc",
    srcs = [":inventory_proto"],
    deps = [":inventory_java_proto"],
)

java_gapic_library(
    name = "inventory_java_gapic",
    srcs = [":inventory_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "kmsinventory_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "kmsinventory_v1.yaml",
    test_deps = [
        ":inventory_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":inventory_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/kms/v1:kms_java_proto",  # manually add kms_java_proto dependency
    ],
)

java_gapic_test(
    name = "inventory_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.kms.inventory.v1.KeyDashboardServiceClientHttpJsonTest",
        "com.google.cloud.kms.inventory.v1.KeyDashboardServiceClientTest",
        "com.google.cloud.kms.inventory.v1.KeyTrackingServiceClientHttpJsonTest",
        "com.google.cloud.kms.inventory.v1.KeyTrackingServiceClientTest",
    ],
    runtime_deps = [":inventory_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-kms-inventory-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":inventory_java_gapic",
        ":inventory_java_grpc",
        ":inventory_java_proto",
        ":inventory_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "inventory_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/kms/inventory/apiv1/inventorypb",
    protos = [":inventory_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/cloud/kms/v1:kms_go_proto",  # manual change to kms_go_proto
    ],
)

go_gapic_library(
    name = "inventory_go_gapic",
    srcs = [":inventory_proto_with_info"],
    grpc_service_config = "kmsinventory_grpc_service_config.json",
    importpath = "cloud.google.com/go/kms/inventory/apiv1;inventory",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "kmsinventory_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":inventory_go_proto",
        "//google/cloud/kms/v1:kms_go_proto",  # manually add kms_go_proto dep
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-kms-inventory-v1-go",
    deps = [
        ":inventory_go_gapic",
        ":inventory_go_gapic_srcjar-metadata.srcjar",
        ":inventory_go_gapic_srcjar-snippets.srcjar",
        ":inventory_go_gapic_srcjar-test.srcjar",
        ":inventory_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "py_import",
)

py_import(
    name = "kms",
    srcs = [
        "//google/cloud/kms/v1:kms_py_gapic",
    ],
)

py_gapic_library(
    name = "inventory_py_gapic",
    srcs = [":inventory_proto"],
    grpc_service_config = "kmsinventory_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "kmsinventory_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":kms",
    ],
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=kms_inventory",
        "proto-plus-deps=google.cloud.kms.v1",
    ],
)

py_test(
    name = "inventory_py_gapic_test",
    srcs = [
        "inventory_py_gapic_pytest.py",
        "inventory_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [
        ":inventory_py_gapic",
        "//google/cloud/kms/v1:kms_py_proto",  # manually add kms_py_proto dep
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "kms-inventory-v1-py",
    deps = [
        ":inventory_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "inventory_php_proto",
    deps = [":inventory_proto"],
)

php_gapic_library(
    name = "inventory_php_gapic",
    srcs = [":inventory_proto_with_info"],
    grpc_service_config = "kmsinventory_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "kmsinventory_v1.yaml",
    transport = "grpc+rest",
    deps = [":inventory_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-kms-inventory-v1-php",
    deps = [
        ":inventory_php_gapic",
        ":inventory_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "inventory_nodejs_gapic",
    package_name = "@google-cloud/kms-inventory",
    src = ":inventory_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "kmsinventory_grpc_service_config.json",
    package = "google.cloud.kms.inventory.v1",
    rest_numeric_enums = True,
    service_yaml = "kmsinventory_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "kms-inventory-v1-nodejs",
    deps = [
        ":inventory_nodejs_gapic",
        ":inventory_proto",
        "//google/cloud/kms/v1:kms_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "inventory_ruby_proto",
    deps = [":inventory_proto"],
)

ruby_grpc_library(
    name = "inventory_ruby_grpc",
    srcs = [":inventory_proto"],
    deps = [":inventory_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "inventory_ruby_gapic",
    srcs = [":inventory_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-kms-inventory-v1",
        "ruby-cloud-extra-dependencies=google-cloud-kms-v1=>0.0+<2.a",
    ],
    grpc_service_config = "kmsinventory_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "kmsinventory_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":inventory_ruby_grpc",
        ":inventory_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-kms-inventory-v1-ruby",
    deps = [
        ":inventory_ruby_gapic",
        ":inventory_ruby_grpc",
        ":inventory_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "inventory_csharp_proto",
    deps = [":inventory_proto"],
)

csharp_grpc_library(
    name = "inventory_csharp_grpc",
    srcs = [":inventory_proto"],
    deps = [":inventory_csharp_proto"],
)

csharp_gapic_library(
    name = "inventory_csharp_gapic",
    srcs = [":inventory_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "kmsinventory_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "kmsinventory_v1.yaml",
    deps = [
        ":inventory_csharp_grpc",
        ":inventory_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-kms-inventory-v1-csharp",
    deps = [
        ":inventory_csharp_gapic",
        ":inventory_csharp_grpc",
        ":inventory_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "inventory_cc_proto",
    deps = [":inventory_proto"],
)

cc_grpc_library(
    name = "inventory_cc_grpc",
    srcs = [":inventory_proto"],
    grpc_only = True,
    deps = [":inventory_cc_proto"],
)
