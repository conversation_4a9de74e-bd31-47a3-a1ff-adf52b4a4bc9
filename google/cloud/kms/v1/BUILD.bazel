# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "kms_proto",
    srcs = [
        "autokey.proto",
        "autokey_admin.proto",
        "ekm_service.proto",
        "resources.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "kms_proto_with_info",
    deps = [
        ":kms_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "kms_java_proto",
    deps = [":kms_proto"],
)

java_grpc_library(
    name = "kms_java_grpc",
    srcs = [":kms_proto"],
    deps = [":kms_java_proto"],
)

java_gapic_library(
    name = "kms_java_gapic",
    srcs = [":kms_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloudkms_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudkms_v1.yaml",
    test_deps = [
        ":kms_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":kms_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "kms_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.kms.v1.AutokeyAdminClientHttpJsonTest",
        "com.google.cloud.kms.v1.AutokeyAdminClientTest",
        "com.google.cloud.kms.v1.AutokeyClientHttpJsonTest",
        "com.google.cloud.kms.v1.AutokeyClientTest",
        "com.google.cloud.kms.v1.EkmServiceClientHttpJsonTest",
        "com.google.cloud.kms.v1.EkmServiceClientTest",
        "com.google.cloud.kms.v1.KeyManagementServiceClientHttpJsonTest",
        "com.google.cloud.kms.v1.KeyManagementServiceClientTest",
    ],
    runtime_deps = [":kms_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-kms-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":kms_java_gapic",
        ":kms_java_grpc",
        ":kms_java_proto",
        ":kms_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "kms_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/kms/apiv1/kmspb",
    protos = [":kms_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "kms_go_gapic",
    srcs = [":kms_proto_with_info"],
    grpc_service_config = "cloudkms_grpc_service_config.json",
    importpath = "cloud.google.com/go/kms/apiv1;kms",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudkms_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":kms_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-kms-v1-go",
    deps = [
        ":kms_go_gapic",
        ":kms_go_gapic_srcjar-metadata.srcjar",
        ":kms_go_gapic_srcjar-snippets.srcjar",
        ":kms_go_gapic_srcjar-test.srcjar",
        ":kms_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",  # manually load py_proto_libary
    "py_test",
)

# manually add this target for kms/inventory/v1
py_proto_library(
    name = "kms_py_proto",
    deps = [":kms_proto"],
)

py_gapic_library(
    name = "kms_py_gapic",
    srcs = [":kms_proto"],
    grpc_service_config = "cloudkms_grpc_service_config.json",
    opt_args = ["add-iam-methods"],
    rest_numeric_enums = True,
    service_yaml = "cloudkms_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "kms_py_gapic_test",
    srcs = [
        "kms_py_gapic_pytest.py",
        "kms_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":kms_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "kms-v1-py",
    deps = [
        ":kms_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "kms_php_proto",
    deps = [":kms_proto"],
)

php_gapic_library(
    name = "kms_php_gapic",
    srcs = [":kms_proto_with_info"],
    grpc_service_config = "cloudkms_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudkms_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":kms_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-kms-v1-php",
    deps = [
        ":kms_php_gapic",
        ":kms_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "kms_nodejs_gapic",
    package_name = "@google-cloud/kms",
    src = ":kms_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudkms_grpc_service_config.json",
    package = "google.cloud.kms.v1",
    rest_numeric_enums = True,
    service_yaml = "cloudkms_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "kms-v1-nodejs",
    deps = [
        ":kms_nodejs_gapic",
        ":kms_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "kms_ruby_proto",
    deps = [":kms_proto"],
)

ruby_grpc_library(
    name = "kms_ruby_grpc",
    srcs = [":kms_proto"],
    deps = [":kms_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "kms_ruby_gapic",
    srcs = [":kms_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=cloudkms.googleapis.com",
        "ruby-cloud-api-shortname=cloudkms",
        "ruby-cloud-env-prefix=KMS",
        "ruby-cloud-gem-name=google-cloud-kms-v1",
        "ruby-cloud-product-url=https://cloud.google.com/kms",
    ],
    grpc_service_config = "cloudkms_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Manages keys and performs cryptographic operations in a central cloud service, for direct use by other cloud resources and applications.",
    ruby_cloud_title = "Cloud Key Management Service (KMS) V1",
    service_yaml = "cloudkms_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":kms_ruby_grpc",
        ":kms_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-kms-v1-ruby",
    deps = [
        ":kms_ruby_gapic",
        ":kms_ruby_grpc",
        ":kms_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "kms_csharp_proto",
    extra_opts = [],
    deps = [":kms_proto"],
)

csharp_grpc_library(
    name = "kms_csharp_grpc",
    srcs = [":kms_proto"],
    deps = [":kms_csharp_proto"],
)

csharp_gapic_library(
    name = "kms_csharp_gapic",
    srcs = [":kms_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudkms_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudkms_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":kms_csharp_grpc",
        ":kms_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-kms-v1-csharp",
    deps = [
        ":kms_csharp_gapic",
        ":kms_csharp_grpc",
        ":kms_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "kms_cc_proto",
    deps = [":kms_proto"],
)

cc_grpc_library(
    name = "kms_cc_grpc",
    srcs = [":kms_proto"],
    grpc_only = True,
    deps = [":kms_cc_proto"],
)
