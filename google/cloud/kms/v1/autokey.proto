// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.kms.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";

option go_package = "cloud.google.com/go/kms/apiv1/kmspb;kmspb";
option java_multiple_files = true;
option java_outer_classname = "AutokeyProto";
option java_package = "com.google.cloud.kms.v1";

// Provides interfaces for using [Cloud KMS
// Autokey](https://cloud.google.com/kms/help/autokey) to provision new
// [CryptoKeys][google.cloud.kms.v1.CryptoKey], ready for Customer Managed
// Encryption Key (CMEK) use, on-demand. To support certain client tooling, this
// feature is modeled around a [KeyHandle][google.cloud.kms.v1.KeyHandle]
// resource: creating a [KeyHandle][google.cloud.kms.v1.KeyHandle] in a resource
// project and given location triggers Cloud KMS Autokey to provision a
// [CryptoKey][google.cloud.kms.v1.CryptoKey] in the configured key project and
// the same location.
//
// Prior to use in a given resource project,
// [UpdateAutokeyConfig][google.cloud.kms.v1.AutokeyAdmin.UpdateAutokeyConfig]
// should have been called on an ancestor folder, setting the key project where
// Cloud KMS Autokey should create new
// [CryptoKeys][google.cloud.kms.v1.CryptoKey]. See documentation for additional
// prerequisites. To check what key project, if any, is currently configured on
// a resource project's ancestor folder, see
// [ShowEffectiveAutokeyConfig][google.cloud.kms.v1.AutokeyAdmin.ShowEffectiveAutokeyConfig].
service Autokey {
  option (google.api.default_host) = "cloudkms.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloudkms";

  // Creates a new [KeyHandle][google.cloud.kms.v1.KeyHandle], triggering the
  // provisioning of a new [CryptoKey][google.cloud.kms.v1.CryptoKey] for CMEK
  // use with the given resource type in the configured key project and the same
  // location. [GetOperation][google.longrunning.Operations.GetOperation] should
  // be used to resolve the resulting long-running operation and get the
  // resulting [KeyHandle][google.cloud.kms.v1.KeyHandle] and
  // [CryptoKey][google.cloud.kms.v1.CryptoKey].
  rpc CreateKeyHandle(CreateKeyHandleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/keyHandles"
      body: "key_handle"
    };
    option (google.api.method_signature) = "parent,key_handle,key_handle_id";
    option (google.longrunning.operation_info) = {
      response_type: "KeyHandle"
      metadata_type: "CreateKeyHandleMetadata"
    };
  }

  // Returns the [KeyHandle][google.cloud.kms.v1.KeyHandle].
  rpc GetKeyHandle(GetKeyHandleRequest) returns (KeyHandle) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/keyHandles/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [KeyHandles][google.cloud.kms.v1.KeyHandle].
  rpc ListKeyHandles(ListKeyHandlesRequest) returns (ListKeyHandlesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/keyHandles"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [Autokey.CreateKeyHandle][google.cloud.kms.v1.Autokey.CreateKeyHandle].
message CreateKeyHandleRequest {
  // Required. Name of the resource project and location to create the
  // [KeyHandle][google.cloud.kms.v1.KeyHandle] in, e.g.
  // `projects/{PROJECT_ID}/locations/{LOCATION}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Id of the [KeyHandle][google.cloud.kms.v1.KeyHandle]. Must be
  // unique to the resource project and location. If not provided by the caller,
  // a new UUID is used.
  string key_handle_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. [KeyHandle][google.cloud.kms.v1.KeyHandle] to create.
  KeyHandle key_handle = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for [GetKeyHandle][google.cloud.kms.v1.Autokey.GetKeyHandle].
message GetKeyHandleRequest {
  // Required. Name of the [KeyHandle][google.cloud.kms.v1.KeyHandle] resource,
  // e.g.
  // `projects/{PROJECT_ID}/locations/{LOCATION}/keyHandles/{KEY_HANDLE_ID}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/KeyHandle"
    }
  ];
}

// Resource-oriented representation of a request to Cloud KMS Autokey and the
// resulting provisioning of a [CryptoKey][google.cloud.kms.v1.CryptoKey].
message KeyHandle {
  option (google.api.resource) = {
    type: "cloudkms.googleapis.com/KeyHandle"
    pattern: "projects/{project}/locations/{location}/keyHandles/{key_handle}"
    plural: "keyHandles"
    singular: "keyHandle"
  };

  // Identifier. Name of the [KeyHandle][google.cloud.kms.v1.KeyHandle]
  // resource, e.g.
  // `projects/{PROJECT_ID}/locations/{LOCATION}/keyHandles/{KEY_HANDLE_ID}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Name of a [CryptoKey][google.cloud.kms.v1.CryptoKey] that has
  // been provisioned for Customer Managed Encryption Key (CMEK) use in the
  // [KeyHandle][google.cloud.kms.v1.KeyHandle] project and location for the
  // requested resource type. The [CryptoKey][google.cloud.kms.v1.CryptoKey]
  // project will reflect the value configured in the
  // [AutokeyConfig][google.cloud.kms.v1.AutokeyConfig] on the resource
  // project's ancestor folder at the time of the
  // [KeyHandle][google.cloud.kms.v1.KeyHandle] creation. If more than one
  // ancestor folder has a configured
  // [AutokeyConfig][google.cloud.kms.v1.AutokeyConfig], the nearest of these
  // configurations is used.
  string kms_key = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Required. Indicates the resource type that the resulting
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] is meant to protect, e.g.
  // `{SERVICE}.googleapis.com/{TYPE}`. See documentation for supported resource
  // types.
  string resource_type_selector = 4 [(google.api.field_behavior) = REQUIRED];
}

// Metadata message for
// [CreateKeyHandle][google.cloud.kms.v1.Autokey.CreateKeyHandle] long-running
// operation response.
message CreateKeyHandleMetadata {}

// Request message for
// [Autokey.ListKeyHandles][google.cloud.kms.v1.Autokey.ListKeyHandles].
message ListKeyHandlesRequest {
  // Required. Name of the resource project and location from which to list
  // [KeyHandles][google.cloud.kms.v1.KeyHandle], e.g.
  // `projects/{PROJECT_ID}/locations/{LOCATION}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Optional limit on the number of
  // [KeyHandles][google.cloud.kms.v1.KeyHandle] to include in the response. The
  // service may return fewer than this value. Further
  // [KeyHandles][google.cloud.kms.v1.KeyHandle] can subsequently be obtained by
  // including the
  // [ListKeyHandlesResponse.next_page_token][google.cloud.kms.v1.ListKeyHandlesResponse.next_page_token]
  // in a subsequent request.  If unspecified, at most 100
  // [KeyHandles][google.cloud.kms.v1.KeyHandle] will be returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional pagination token, returned earlier via
  // [ListKeyHandlesResponse.next_page_token][google.cloud.kms.v1.ListKeyHandlesResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter to apply when listing
  // [KeyHandles][google.cloud.kms.v1.KeyHandle], e.g.
  // `resource_type_selector="{SERVICE}.googleapis.com/{TYPE}"`.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [Autokey.ListKeyHandles][google.cloud.kms.v1.Autokey.ListKeyHandles].
message ListKeyHandlesResponse {
  // Resulting [KeyHandles][google.cloud.kms.v1.KeyHandle].
  repeated KeyHandle key_handles = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListKeyHandlesRequest.page_token][google.cloud.kms.v1.ListKeyHandlesRequest.page_token]
  // to retrieve the next page of results.
  string next_page_token = 2;
}
