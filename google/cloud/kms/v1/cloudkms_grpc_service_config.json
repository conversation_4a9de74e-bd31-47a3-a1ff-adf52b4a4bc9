{"methodConfig": [{"name": [{"service": "google.cloud.kms.v1.KeyManagementService", "method": "CreateCryptoKeyVersion"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "ImportCryptoKeyVersion"}, {"service": "google.cloud.kms.v1.Autokey", "method": "CreateKeyHandle"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.kms.v1.EkmService", "method": "ListEkmConnections"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "ListKeyRings"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "ListImportJobs"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "ListCryptoKeys"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "ListCryptoKeyVersions"}, {"service": "google.cloud.kms.v1.EkmService", "method": "GetEkmConnection"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "GetKeyRing"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "GetImportJob"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "GetCryptoKey"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "GetCryptoKeyVersion"}, {"service": "google.cloud.kms.v1.EkmService", "method": "CreateEkmConnection"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "CreateKeyRing"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "CreateImportJob"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "CreateCryptoKey"}, {"service": "google.cloud.kms.v1.EkmService", "method": "UpdateEkmConnection"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "UpdateCryptoKey"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "UpdateCryptoKeyVersion"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "Encrypt"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "Decrypt"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "UpdateCryptoKeyPrimaryVersion"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "DestroyCryptoKeyVersion"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "RestoreCryptoKeyVersion"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "GetPublicKey"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "AsymmetricDecrypt"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "AsymmetricSign"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "MacSign"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "MacVerify"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "GenerateRandomBytes"}, {"service": "google.cloud.kms.v1.EkmService", "method": "SetIamPolicy"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "SetIamPolicy"}, {"service": "google.cloud.kms.v1.EkmService", "method": "GetIamPolicy"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "GetIamPolicy"}, {"service": "google.cloud.kms.v1.EkmService", "method": "TestIamPermissions"}, {"service": "google.cloud.kms.v1.KeyManagementService", "method": "TestIamPermissions"}, {"service": "google.cloud.kms.v1.AutokeyAdmin", "method": "UpdateAutokeyConfig"}, {"service": "google.cloud.kms.v1.AutokeyAdmin", "method": "GetAutokeyConfig"}, {"service": "google.cloud.kms.v1.AutokeyAdmin", "method": "ShowEffectiveAutokeyConfig"}, {"service": "google.cloud.kms.v1.Autokey", "method": "GetKeyHandle"}, {"service": "google.cloud.kms.v1.Autokey", "method": "ListKeyHandles"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "maxAttempts": 5, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}