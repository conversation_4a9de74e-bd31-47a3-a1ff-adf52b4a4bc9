// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.kms.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Kms.V1";
option go_package = "cloud.google.com/go/kms/apiv1/kmspb;kmspb";
option java_multiple_files = true;
option java_outer_classname = "EkmServiceProto";
option java_package = "com.google.cloud.kms.v1";
option php_namespace = "Google\\Cloud\\Kms\\V1";
option (google.api.resource_definition) = {
  type: "servicedirectory.googleapis.com/Service"
  pattern: "projects/{project}/locations/{location}/namespaces/{namespace}/services/{service}"
};

// Google Cloud Key Management EKM Service
//
// Manages external cryptographic keys and operations using those keys.
// Implements a REST model with the following objects:
// * [EkmConnection][google.cloud.kms.v1.EkmConnection]
service EkmService {
  option (google.api.default_host) = "cloudkms.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloudkms";

  // Lists [EkmConnections][google.cloud.kms.v1.EkmConnection].
  rpc ListEkmConnections(ListEkmConnectionsRequest)
      returns (ListEkmConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/ekmConnections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns metadata for a given
  // [EkmConnection][google.cloud.kms.v1.EkmConnection].
  rpc GetEkmConnection(GetEkmConnectionRequest) returns (EkmConnection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/ekmConnections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new [EkmConnection][google.cloud.kms.v1.EkmConnection] in a given
  // Project and Location.
  rpc CreateEkmConnection(CreateEkmConnectionRequest) returns (EkmConnection) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/ekmConnections"
      body: "ekm_connection"
    };
    option (google.api.method_signature) =
        "parent,ekm_connection_id,ekm_connection";
  }

  // Updates an [EkmConnection][google.cloud.kms.v1.EkmConnection]'s metadata.
  rpc UpdateEkmConnection(UpdateEkmConnectionRequest) returns (EkmConnection) {
    option (google.api.http) = {
      patch: "/v1/{ekm_connection.name=projects/*/locations/*/ekmConnections/*}"
      body: "ekm_connection"
    };
    option (google.api.method_signature) = "ekm_connection,update_mask";
  }

  // Returns the [EkmConfig][google.cloud.kms.v1.EkmConfig] singleton resource
  // for a given project and location.
  rpc GetEkmConfig(GetEkmConfigRequest) returns (EkmConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/ekmConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the [EkmConfig][google.cloud.kms.v1.EkmConfig] singleton resource
  // for a given project and location.
  rpc UpdateEkmConfig(UpdateEkmConfigRequest) returns (EkmConfig) {
    option (google.api.http) = {
      patch: "/v1/{ekm_config.name=projects/*/locations/*/ekmConfig}"
      body: "ekm_config"
    };
    option (google.api.method_signature) = "ekm_config,update_mask";
  }

  // Verifies that Cloud KMS can successfully connect to the external key
  // manager specified by an [EkmConnection][google.cloud.kms.v1.EkmConnection].
  // If there is an error connecting to the EKM, this method returns a
  // FAILED_PRECONDITION status containing structured information as described
  // at https://cloud.google.com/kms/docs/reference/ekm_errors.
  rpc VerifyConnectivity(VerifyConnectivityRequest)
      returns (VerifyConnectivityResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/ekmConnections/*}:verifyConnectivity"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for
// [EkmService.ListEkmConnections][google.cloud.kms.v1.EkmService.ListEkmConnections].
message ListEkmConnectionsRequest {
  // Required. The resource name of the location associated with the
  // [EkmConnections][google.cloud.kms.v1.EkmConnection] to list, in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Optional limit on the number of
  // [EkmConnections][google.cloud.kms.v1.EkmConnection] to include in the
  // response. Further [EkmConnections][google.cloud.kms.v1.EkmConnection] can
  // subsequently be obtained by including the
  // [ListEkmConnectionsResponse.next_page_token][google.cloud.kms.v1.ListEkmConnectionsResponse.next_page_token]
  // in a subsequent request. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional pagination token, returned earlier via
  // [ListEkmConnectionsResponse.next_page_token][google.cloud.kms.v1.ListEkmConnectionsResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response. For
  // more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted. If not specified, the
  // results will be sorted in the default order.  For more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [EkmService.ListEkmConnections][google.cloud.kms.v1.EkmService.ListEkmConnections].
message ListEkmConnectionsResponse {
  // The list of [EkmConnections][google.cloud.kms.v1.EkmConnection].
  repeated EkmConnection ekm_connections = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListEkmConnectionsRequest.page_token][google.cloud.kms.v1.ListEkmConnectionsRequest.page_token]
  // to retrieve the next page of results.
  string next_page_token = 2;

  // The total number of [EkmConnections][google.cloud.kms.v1.EkmConnection]
  // that matched the query.
  int32 total_size = 3;
}

// Request message for
// [EkmService.GetEkmConnection][google.cloud.kms.v1.EkmService.GetEkmConnection].
message GetEkmConnectionRequest {
  // Required. The [name][google.cloud.kms.v1.EkmConnection.name] of the
  // [EkmConnection][google.cloud.kms.v1.EkmConnection] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/EkmConnection"
    }
  ];
}

// Request message for
// [EkmService.CreateEkmConnection][google.cloud.kms.v1.EkmService.CreateEkmConnection].
message CreateEkmConnectionRequest {
  // Required. The resource name of the location associated with the
  // [EkmConnection][google.cloud.kms.v1.EkmConnection], in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. It must be unique within a location and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`.
  string ekm_connection_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. An [EkmConnection][google.cloud.kms.v1.EkmConnection] with
  // initial field values.
  EkmConnection ekm_connection = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [EkmService.UpdateEkmConnection][google.cloud.kms.v1.EkmService.UpdateEkmConnection].
message UpdateEkmConnectionRequest {
  // Required. [EkmConnection][google.cloud.kms.v1.EkmConnection] with updated
  // values.
  EkmConnection ekm_connection = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [EkmService.GetEkmConfig][google.cloud.kms.v1.EkmService.GetEkmConfig].
message GetEkmConfigRequest {
  // Required. The [name][google.cloud.kms.v1.EkmConfig.name] of the
  // [EkmConfig][google.cloud.kms.v1.EkmConfig] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/EkmConfig"
    }
  ];
}

// Request message for
// [EkmService.UpdateEkmConfig][google.cloud.kms.v1.EkmService.UpdateEkmConfig].
message UpdateEkmConfigRequest {
  // Required. [EkmConfig][google.cloud.kms.v1.EkmConfig] with updated values.
  EkmConfig ekm_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A [Certificate][google.cloud.kms.v1.Certificate] represents an X.509
// certificate used to authenticate HTTPS connections to EKM replicas.
message Certificate {
  // Required. The raw certificate bytes in DER format.
  bytes raw_der = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. True if the certificate was parsed successfully.
  bool parsed = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The issuer distinguished name in RFC 2253 format. Only present
  // if [parsed][google.cloud.kms.v1.Certificate.parsed] is true.
  string issuer = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The subject distinguished name in RFC 2253 format. Only
  // present if [parsed][google.cloud.kms.v1.Certificate.parsed] is true.
  string subject = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The subject Alternative DNS names. Only present if
  // [parsed][google.cloud.kms.v1.Certificate.parsed] is true.
  repeated string subject_alternative_dns_names = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The certificate is not valid before this time. Only present if
  // [parsed][google.cloud.kms.v1.Certificate.parsed] is true.
  google.protobuf.Timestamp not_before_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The certificate is not valid after this time. Only present if
  // [parsed][google.cloud.kms.v1.Certificate.parsed] is true.
  google.protobuf.Timestamp not_after_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The certificate serial number as a hex string. Only present if
  // [parsed][google.cloud.kms.v1.Certificate.parsed] is true.
  string serial_number = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The SHA-256 certificate fingerprint as a hex string. Only
  // present if [parsed][google.cloud.kms.v1.Certificate.parsed] is true.
  string sha256_fingerprint = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An [EkmConnection][google.cloud.kms.v1.EkmConnection] represents an
// individual EKM connection. It can be used for creating
// [CryptoKeys][google.cloud.kms.v1.CryptoKey] and
// [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion] with a
// [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of
// [EXTERNAL_VPC][google.cloud.kms.v1.ProtectionLevel.EXTERNAL_VPC], as well as
// performing cryptographic operations using keys created within the
// [EkmConnection][google.cloud.kms.v1.EkmConnection].
message EkmConnection {
  option (google.api.resource) = {
    type: "cloudkms.googleapis.com/EkmConnection"
    pattern: "projects/{project}/locations/{location}/ekmConnections/{ekm_connection}"
  };

  // A [ServiceResolver][google.cloud.kms.v1.EkmConnection.ServiceResolver]
  // represents an EKM replica that can be reached within an
  // [EkmConnection][google.cloud.kms.v1.EkmConnection].
  message ServiceResolver {
    // Required. The resource name of the Service Directory service pointing to
    // an EKM replica, in the format
    // `projects/*/locations/*/namespaces/*/services/*`.
    string service_directory_service = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "servicedirectory.googleapis.com/Service"
      }
    ];

    // Optional. The filter applied to the endpoints of the resolved service. If
    // no filter is specified, all endpoints will be considered. An endpoint
    // will be chosen arbitrarily from the filtered list for each request.
    //
    // For endpoint filter syntax and examples, see
    // https://cloud.google.com/service-directory/docs/reference/rpc/google.cloud.servicedirectory.v1#resolveservicerequest.
    string endpoint_filter = 2 [(google.api.field_behavior) = OPTIONAL];

    // Required. The hostname of the EKM replica used at TLS and HTTP layers.
    string hostname = 3 [(google.api.field_behavior) = REQUIRED];

    // Required. A list of leaf server certificates used to authenticate HTTPS
    // connections to the EKM replica. Currently, a maximum of 10
    // [Certificate][google.cloud.kms.v1.Certificate] is supported.
    repeated Certificate server_certificates = 4
        [(google.api.field_behavior) = REQUIRED];
  }

  // [KeyManagementMode][google.cloud.kms.v1.EkmConnection.KeyManagementMode]
  // describes who can perform control plane cryptographic operations using this
  // [EkmConnection][google.cloud.kms.v1.EkmConnection].
  enum KeyManagementMode {
    // Not specified.
    KEY_MANAGEMENT_MODE_UNSPECIFIED = 0;

    // EKM-side key management operations on
    // [CryptoKeys][google.cloud.kms.v1.CryptoKey] created with this
    // [EkmConnection][google.cloud.kms.v1.EkmConnection] must be initiated from
    // the EKM directly and cannot be performed from Cloud KMS. This means that:
    // * When creating a
    // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] associated with
    // this
    //   [EkmConnection][google.cloud.kms.v1.EkmConnection], the caller must
    //   supply the key path of pre-existing external key material that will be
    //   linked to the [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion].
    // * Destruction of external key material cannot be requested via the
    //   Cloud KMS API and must be performed directly in the EKM.
    // * Automatic rotation of key material is not supported.
    MANUAL = 1;

    // All [CryptoKeys][google.cloud.kms.v1.CryptoKey] created with this
    // [EkmConnection][google.cloud.kms.v1.EkmConnection] use EKM-side key
    // management operations initiated from Cloud KMS. This means that:
    //
    // * When a [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]
    // associated with this [EkmConnection][google.cloud.kms.v1.EkmConnection]
    // is
    //   created, the EKM automatically generates new key material and a new
    //   key path. The caller cannot supply the key path of pre-existing
    //   external key material.
    // * Destruction of external key material associated with this
    //   [EkmConnection][google.cloud.kms.v1.EkmConnection] can be requested by
    //   calling
    //   [DestroyCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.DestroyCryptoKeyVersion].
    // * Automatic rotation of key material is supported.
    CLOUD_KMS = 2;
  }

  // Output only. The resource name for the
  // [EkmConnection][google.cloud.kms.v1.EkmConnection] in the format
  // `projects/*/locations/*/ekmConnections/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the
  // [EkmConnection][google.cloud.kms.v1.EkmConnection] was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A list of
  // [ServiceResolvers][google.cloud.kms.v1.EkmConnection.ServiceResolver] where
  // the EKM can be reached. There should be one ServiceResolver per EKM
  // replica. Currently, only a single
  // [ServiceResolver][google.cloud.kms.v1.EkmConnection.ServiceResolver] is
  // supported.
  repeated ServiceResolver service_resolvers = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Etag of the currently stored
  // [EkmConnection][google.cloud.kms.v1.EkmConnection].
  string etag = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes who can perform control plane operations on the EKM. If
  // unset, this defaults to
  // [MANUAL][google.cloud.kms.v1.EkmConnection.KeyManagementMode.MANUAL].
  KeyManagementMode key_management_mode = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Identifies the EKM Crypto Space that this
  // [EkmConnection][google.cloud.kms.v1.EkmConnection] maps to. Note: This
  // field is required if
  // [KeyManagementMode][google.cloud.kms.v1.EkmConnection.KeyManagementMode] is
  // [CLOUD_KMS][google.cloud.kms.v1.EkmConnection.KeyManagementMode.CLOUD_KMS].
  string crypto_space_path = 7 [(google.api.field_behavior) = OPTIONAL];
}

// An [EkmConfig][google.cloud.kms.v1.EkmConfig] is a singleton resource that
// represents configuration parameters that apply to all
// [CryptoKeys][google.cloud.kms.v1.CryptoKey] and
// [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion] with a
// [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of
// [EXTERNAL_VPC][google.cloud.kms.v1.ProtectionLevel.EXTERNAL_VPC] in a given
// project and location.
message EkmConfig {
  option (google.api.resource) = {
    type: "cloudkms.googleapis.com/EkmConfig"
    pattern: "projects/{project}/locations/{location}/ekmConfig"
  };

  // Output only. The resource name for the
  // [EkmConfig][google.cloud.kms.v1.EkmConfig] in the format
  // `projects/*/locations/*/ekmConfig`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource name of the default
  // [EkmConnection][google.cloud.kms.v1.EkmConnection]. Setting this field to
  // the empty string removes the default.
  string default_ekm_connection = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/EkmConnection"
    }
  ];
}

// Request message for
// [EkmService.VerifyConnectivity][google.cloud.kms.v1.EkmService.VerifyConnectivity].
message VerifyConnectivityRequest {
  // Required. The [name][google.cloud.kms.v1.EkmConnection.name] of the
  // [EkmConnection][google.cloud.kms.v1.EkmConnection] to verify.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/EkmConnection"
    }
  ];
}

// Response message for
// [EkmService.VerifyConnectivity][google.cloud.kms.v1.EkmService.VerifyConnectivity].
message VerifyConnectivityResponse {}
