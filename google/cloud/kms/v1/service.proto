// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.kms.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/kms/v1/resources.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.Kms.V1";
option go_package = "cloud.google.com/go/kms/apiv1/kmspb;kmspb";
option java_multiple_files = true;
option java_outer_classname = "KmsProto";
option java_package = "com.google.cloud.kms.v1";
option php_namespace = "Google\\Cloud\\Kms\\V1";

// Google Cloud Key Management Service
//
// Manages cryptographic keys and operations using those keys. Implements a REST
// model with the following objects:
//
// * [KeyRing][google.cloud.kms.v1.KeyRing]
// * [CryptoKey][google.cloud.kms.v1.CryptoKey]
// * [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]
// * [ImportJob][google.cloud.kms.v1.ImportJob]
//
// If you are using manual gRPC libraries, see
// [Using gRPC with Cloud KMS](https://cloud.google.com/kms/docs/grpc).
service KeyManagementService {
  option (google.api.default_host) = "cloudkms.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloudkms";

  // Lists [KeyRings][google.cloud.kms.v1.KeyRing].
  rpc ListKeyRings(ListKeyRingsRequest) returns (ListKeyRingsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/keyRings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists [CryptoKeys][google.cloud.kms.v1.CryptoKey].
  rpc ListCryptoKeys(ListCryptoKeysRequest) returns (ListCryptoKeysResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/keyRings/*}/cryptoKeys"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion].
  rpc ListCryptoKeyVersions(ListCryptoKeyVersionsRequest)
      returns (ListCryptoKeyVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/keyRings/*/cryptoKeys/*}/cryptoKeyVersions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists [ImportJobs][google.cloud.kms.v1.ImportJob].
  rpc ListImportJobs(ListImportJobsRequest) returns (ListImportJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/keyRings/*}/importJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns metadata for a given [KeyRing][google.cloud.kms.v1.KeyRing].
  rpc GetKeyRing(GetKeyRingRequest) returns (KeyRing) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/keyRings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns metadata for a given [CryptoKey][google.cloud.kms.v1.CryptoKey], as
  // well as its [primary][google.cloud.kms.v1.CryptoKey.primary]
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion].
  rpc GetCryptoKey(GetCryptoKeyRequest) returns (CryptoKey) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns metadata for a given
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion].
  rpc GetCryptoKeyVersion(GetCryptoKeyVersionRequest)
      returns (CryptoKeyVersion) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the public key for the given
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]. The
  // [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose] must be
  // [ASYMMETRIC_SIGN][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose.ASYMMETRIC_SIGN]
  // or
  // [ASYMMETRIC_DECRYPT][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose.ASYMMETRIC_DECRYPT].
  rpc GetPublicKey(GetPublicKeyRequest) returns (PublicKey) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}/publicKey"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns metadata for a given [ImportJob][google.cloud.kms.v1.ImportJob].
  rpc GetImportJob(GetImportJobRequest) returns (ImportJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/keyRings/*/importJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a new [KeyRing][google.cloud.kms.v1.KeyRing] in a given Project and
  // Location.
  rpc CreateKeyRing(CreateKeyRingRequest) returns (KeyRing) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/keyRings"
      body: "key_ring"
    };
    option (google.api.method_signature) = "parent,key_ring_id,key_ring";
  }

  // Create a new [CryptoKey][google.cloud.kms.v1.CryptoKey] within a
  // [KeyRing][google.cloud.kms.v1.KeyRing].
  //
  // [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose] and
  // [CryptoKey.version_template.algorithm][google.cloud.kms.v1.CryptoKeyVersionTemplate.algorithm]
  // are required.
  rpc CreateCryptoKey(CreateCryptoKeyRequest) returns (CryptoKey) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/keyRings/*}/cryptoKeys"
      body: "crypto_key"
    };
    option (google.api.method_signature) = "parent,crypto_key_id,crypto_key";
  }

  // Create a new [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] in a
  // [CryptoKey][google.cloud.kms.v1.CryptoKey].
  //
  // The server will assign the next sequential id. If unset,
  // [state][google.cloud.kms.v1.CryptoKeyVersion.state] will be set to
  // [ENABLED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.ENABLED].
  rpc CreateCryptoKeyVersion(CreateCryptoKeyVersionRequest)
      returns (CryptoKeyVersion) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/keyRings/*/cryptoKeys/*}/cryptoKeyVersions"
      body: "crypto_key_version"
    };
    option (google.api.method_signature) = "parent,crypto_key_version";
  }

  // Import wrapped key material into a
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion].
  //
  // All requests must specify a [CryptoKey][google.cloud.kms.v1.CryptoKey]. If
  // a [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] is additionally
  // specified in the request, key material will be reimported into that
  // version. Otherwise, a new version will be created, and will be assigned the
  // next sequential id within the [CryptoKey][google.cloud.kms.v1.CryptoKey].
  rpc ImportCryptoKeyVersion(ImportCryptoKeyVersionRequest)
      returns (CryptoKeyVersion) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/keyRings/*/cryptoKeys/*}/cryptoKeyVersions:import"
      body: "*"
    };
  }

  // Create a new [ImportJob][google.cloud.kms.v1.ImportJob] within a
  // [KeyRing][google.cloud.kms.v1.KeyRing].
  //
  // [ImportJob.import_method][google.cloud.kms.v1.ImportJob.import_method] is
  // required.
  rpc CreateImportJob(CreateImportJobRequest) returns (ImportJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/keyRings/*}/importJobs"
      body: "import_job"
    };
    option (google.api.method_signature) = "parent,import_job_id,import_job";
  }

  // Update a [CryptoKey][google.cloud.kms.v1.CryptoKey].
  rpc UpdateCryptoKey(UpdateCryptoKeyRequest) returns (CryptoKey) {
    option (google.api.http) = {
      patch: "/v1/{crypto_key.name=projects/*/locations/*/keyRings/*/cryptoKeys/*}"
      body: "crypto_key"
    };
    option (google.api.method_signature) = "crypto_key,update_mask";
  }

  // Update a [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]'s
  // metadata.
  //
  // [state][google.cloud.kms.v1.CryptoKeyVersion.state] may be changed between
  // [ENABLED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.ENABLED]
  // and
  // [DISABLED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.DISABLED]
  // using this method. See
  // [DestroyCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.DestroyCryptoKeyVersion]
  // and
  // [RestoreCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.RestoreCryptoKeyVersion]
  // to move between other states.
  rpc UpdateCryptoKeyVersion(UpdateCryptoKeyVersionRequest)
      returns (CryptoKeyVersion) {
    option (google.api.http) = {
      patch: "/v1/{crypto_key_version.name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}"
      body: "crypto_key_version"
    };
    option (google.api.method_signature) = "crypto_key_version,update_mask";
  }

  // Update the version of a [CryptoKey][google.cloud.kms.v1.CryptoKey] that
  // will be used in
  // [Encrypt][google.cloud.kms.v1.KeyManagementService.Encrypt].
  //
  // Returns an error if called on a key whose purpose is not
  // [ENCRYPT_DECRYPT][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose.ENCRYPT_DECRYPT].
  rpc UpdateCryptoKeyPrimaryVersion(UpdateCryptoKeyPrimaryVersionRequest)
      returns (CryptoKey) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*}:updatePrimaryVersion"
      body: "*"
    };
    option (google.api.method_signature) = "name,crypto_key_version_id";
  }

  // Schedule a [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] for
  // destruction.
  //
  // Upon calling this method,
  // [CryptoKeyVersion.state][google.cloud.kms.v1.CryptoKeyVersion.state] will
  // be set to
  // [DESTROY_SCHEDULED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.DESTROY_SCHEDULED],
  // and [destroy_time][google.cloud.kms.v1.CryptoKeyVersion.destroy_time] will
  // be set to the time
  // [destroy_scheduled_duration][google.cloud.kms.v1.CryptoKey.destroy_scheduled_duration]
  // in the future. At that time, the
  // [state][google.cloud.kms.v1.CryptoKeyVersion.state] will automatically
  // change to
  // [DESTROYED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.DESTROYED],
  // and the key material will be irrevocably destroyed.
  //
  // Before the
  // [destroy_time][google.cloud.kms.v1.CryptoKeyVersion.destroy_time] is
  // reached,
  // [RestoreCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.RestoreCryptoKeyVersion]
  // may be called to reverse the process.
  rpc DestroyCryptoKeyVersion(DestroyCryptoKeyVersionRequest)
      returns (CryptoKeyVersion) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}:destroy"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Restore a [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] in the
  // [DESTROY_SCHEDULED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.DESTROY_SCHEDULED]
  // state.
  //
  // Upon restoration of the CryptoKeyVersion,
  // [state][google.cloud.kms.v1.CryptoKeyVersion.state] will be set to
  // [DISABLED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.DISABLED],
  // and [destroy_time][google.cloud.kms.v1.CryptoKeyVersion.destroy_time] will
  // be cleared.
  rpc RestoreCryptoKeyVersion(RestoreCryptoKeyVersionRequest)
      returns (CryptoKeyVersion) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}:restore"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Encrypts data, so that it can only be recovered by a call to
  // [Decrypt][google.cloud.kms.v1.KeyManagementService.Decrypt]. The
  // [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose] must be
  // [ENCRYPT_DECRYPT][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose.ENCRYPT_DECRYPT].
  rpc Encrypt(EncryptRequest) returns (EncryptResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/**}:encrypt"
      body: "*"
    };
    option (google.api.method_signature) = "name,plaintext";
  }

  // Decrypts data that was protected by
  // [Encrypt][google.cloud.kms.v1.KeyManagementService.Encrypt]. The
  // [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose] must be
  // [ENCRYPT_DECRYPT][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose.ENCRYPT_DECRYPT].
  rpc Decrypt(DecryptRequest) returns (DecryptResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*}:decrypt"
      body: "*"
    };
    option (google.api.method_signature) = "name,ciphertext";
  }

  // Encrypts data using portable cryptographic primitives. Most users should
  // choose [Encrypt][google.cloud.kms.v1.KeyManagementService.Encrypt] and
  // [Decrypt][google.cloud.kms.v1.KeyManagementService.Decrypt] rather than
  // their raw counterparts. The
  // [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose] must be
  // [RAW_ENCRYPT_DECRYPT][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose.RAW_ENCRYPT_DECRYPT].
  rpc RawEncrypt(RawEncryptRequest) returns (RawEncryptResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}:rawEncrypt"
      body: "*"
    };
  }

  // Decrypts data that was originally encrypted using a raw cryptographic
  // mechanism. The [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose]
  // must be
  // [RAW_ENCRYPT_DECRYPT][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose.RAW_ENCRYPT_DECRYPT].
  rpc RawDecrypt(RawDecryptRequest) returns (RawDecryptResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}:rawDecrypt"
      body: "*"
    };
  }

  // Signs data using a [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]
  // with [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose]
  // ASYMMETRIC_SIGN, producing a signature that can be verified with the public
  // key retrieved from
  // [GetPublicKey][google.cloud.kms.v1.KeyManagementService.GetPublicKey].
  rpc AsymmetricSign(AsymmetricSignRequest) returns (AsymmetricSignResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}:asymmetricSign"
      body: "*"
    };
    option (google.api.method_signature) = "name,digest";
  }

  // Decrypts data that was encrypted with a public key retrieved from
  // [GetPublicKey][google.cloud.kms.v1.KeyManagementService.GetPublicKey]
  // corresponding to a [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]
  // with [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose]
  // ASYMMETRIC_DECRYPT.
  rpc AsymmetricDecrypt(AsymmetricDecryptRequest)
      returns (AsymmetricDecryptResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}:asymmetricDecrypt"
      body: "*"
    };
    option (google.api.method_signature) = "name,ciphertext";
  }

  // Signs data using a [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]
  // with [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose] MAC,
  // producing a tag that can be verified by another source with the same key.
  rpc MacSign(MacSignRequest) returns (MacSignResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}:macSign"
      body: "*"
    };
    option (google.api.method_signature) = "name,data";
  }

  // Verifies MAC tag using a
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] with
  // [CryptoKey.purpose][google.cloud.kms.v1.CryptoKey.purpose] MAC, and returns
  // a response that indicates whether or not the verification was successful.
  rpc MacVerify(MacVerifyRequest) returns (MacVerifyResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*}:macVerify"
      body: "*"
    };
    option (google.api.method_signature) = "name,data,mac";
  }

  // Generate random bytes using the Cloud KMS randomness source in the provided
  // location.
  rpc GenerateRandomBytes(GenerateRandomBytesRequest)
      returns (GenerateRandomBytesResponse) {
    option (google.api.http) = {
      post: "/v1/{location=projects/*/locations/*}:generateRandomBytes"
      body: "*"
    };
    option (google.api.method_signature) =
        "location,length_bytes,protection_level";
  }
}

// Request message for
// [KeyManagementService.ListKeyRings][google.cloud.kms.v1.KeyManagementService.ListKeyRings].
message ListKeyRingsRequest {
  // Required. The resource name of the location associated with the
  // [KeyRings][google.cloud.kms.v1.KeyRing], in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Optional limit on the number of
  // [KeyRings][google.cloud.kms.v1.KeyRing] to include in the response. Further
  // [KeyRings][google.cloud.kms.v1.KeyRing] can subsequently be obtained by
  // including the
  // [ListKeyRingsResponse.next_page_token][google.cloud.kms.v1.ListKeyRingsResponse.next_page_token]
  // in a subsequent request.  If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional pagination token, returned earlier via
  // [ListKeyRingsResponse.next_page_token][google.cloud.kms.v1.ListKeyRingsResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response. For
  // more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted. If not specified, the
  // results will be sorted in the default order.  For more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.ListCryptoKeys][google.cloud.kms.v1.KeyManagementService.ListCryptoKeys].
message ListCryptoKeysRequest {
  // Required. The resource name of the [KeyRing][google.cloud.kms.v1.KeyRing]
  // to list, in the format `projects/*/locations/*/keyRings/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/KeyRing"
    }
  ];

  // Optional. Optional limit on the number of
  // [CryptoKeys][google.cloud.kms.v1.CryptoKey] to include in the response.
  // Further [CryptoKeys][google.cloud.kms.v1.CryptoKey] can subsequently be
  // obtained by including the
  // [ListCryptoKeysResponse.next_page_token][google.cloud.kms.v1.ListCryptoKeysResponse.next_page_token]
  // in a subsequent request.  If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional pagination token, returned earlier via
  // [ListCryptoKeysResponse.next_page_token][google.cloud.kms.v1.ListCryptoKeysResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // The fields of the primary version to include in the response.
  CryptoKeyVersion.CryptoKeyVersionView version_view = 4;

  // Optional. Only include resources that match the filter in the response. For
  // more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted. If not specified, the
  // results will be sorted in the default order. For more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.ListCryptoKeyVersions][google.cloud.kms.v1.KeyManagementService.ListCryptoKeyVersions].
message ListCryptoKeyVersionsRequest {
  // Required. The resource name of the
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] to list, in the format
  // `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Optional. Optional limit on the number of
  // [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion] to include in the
  // response. Further [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion]
  // can subsequently be obtained by including the
  // [ListCryptoKeyVersionsResponse.next_page_token][google.cloud.kms.v1.ListCryptoKeyVersionsResponse.next_page_token]
  // in a subsequent request. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional pagination token, returned earlier via
  // [ListCryptoKeyVersionsResponse.next_page_token][google.cloud.kms.v1.ListCryptoKeyVersionsResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // The fields to include in the response.
  CryptoKeyVersion.CryptoKeyVersionView view = 4;

  // Optional. Only include resources that match the filter in the response. For
  // more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted. If not specified, the
  // results will be sorted in the default order. For more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.ListImportJobs][google.cloud.kms.v1.KeyManagementService.ListImportJobs].
message ListImportJobsRequest {
  // Required. The resource name of the [KeyRing][google.cloud.kms.v1.KeyRing]
  // to list, in the format `projects/*/locations/*/keyRings/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/KeyRing"
    }
  ];

  // Optional. Optional limit on the number of
  // [ImportJobs][google.cloud.kms.v1.ImportJob] to include in the response.
  // Further [ImportJobs][google.cloud.kms.v1.ImportJob] can subsequently be
  // obtained by including the
  // [ListImportJobsResponse.next_page_token][google.cloud.kms.v1.ListImportJobsResponse.next_page_token]
  // in a subsequent request. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional pagination token, returned earlier via
  // [ListImportJobsResponse.next_page_token][google.cloud.kms.v1.ListImportJobsResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response. For
  // more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted. If not specified, the
  // results will be sorted in the default order. For more information, see
  // [Sorting and filtering list
  // results](https://cloud.google.com/kms/docs/sorting-and-filtering).
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [KeyManagementService.ListKeyRings][google.cloud.kms.v1.KeyManagementService.ListKeyRings].
message ListKeyRingsResponse {
  // The list of [KeyRings][google.cloud.kms.v1.KeyRing].
  repeated KeyRing key_rings = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListKeyRingsRequest.page_token][google.cloud.kms.v1.ListKeyRingsRequest.page_token]
  // to retrieve the next page of results.
  string next_page_token = 2;

  // The total number of [KeyRings][google.cloud.kms.v1.KeyRing] that matched
  // the query.
  int32 total_size = 3;
}

// Response message for
// [KeyManagementService.ListCryptoKeys][google.cloud.kms.v1.KeyManagementService.ListCryptoKeys].
message ListCryptoKeysResponse {
  // The list of [CryptoKeys][google.cloud.kms.v1.CryptoKey].
  repeated CryptoKey crypto_keys = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCryptoKeysRequest.page_token][google.cloud.kms.v1.ListCryptoKeysRequest.page_token]
  // to retrieve the next page of results.
  string next_page_token = 2;

  // The total number of [CryptoKeys][google.cloud.kms.v1.CryptoKey] that
  // matched the query.
  int32 total_size = 3;
}

// Response message for
// [KeyManagementService.ListCryptoKeyVersions][google.cloud.kms.v1.KeyManagementService.ListCryptoKeyVersions].
message ListCryptoKeyVersionsResponse {
  // The list of [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion].
  repeated CryptoKeyVersion crypto_key_versions = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCryptoKeyVersionsRequest.page_token][google.cloud.kms.v1.ListCryptoKeyVersionsRequest.page_token]
  // to retrieve the next page of results.
  string next_page_token = 2;

  // The total number of
  // [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion] that matched the
  // query.
  int32 total_size = 3;
}

// Response message for
// [KeyManagementService.ListImportJobs][google.cloud.kms.v1.KeyManagementService.ListImportJobs].
message ListImportJobsResponse {
  // The list of [ImportJobs][google.cloud.kms.v1.ImportJob].
  repeated ImportJob import_jobs = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListImportJobsRequest.page_token][google.cloud.kms.v1.ListImportJobsRequest.page_token]
  // to retrieve the next page of results.
  string next_page_token = 2;

  // The total number of [ImportJobs][google.cloud.kms.v1.ImportJob] that
  // matched the query.
  int32 total_size = 3;
}

// Request message for
// [KeyManagementService.GetKeyRing][google.cloud.kms.v1.KeyManagementService.GetKeyRing].
message GetKeyRingRequest {
  // Required. The [name][google.cloud.kms.v1.KeyRing.name] of the
  // [KeyRing][google.cloud.kms.v1.KeyRing] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/KeyRing"
    }
  ];
}

// Request message for
// [KeyManagementService.GetCryptoKey][google.cloud.kms.v1.KeyManagementService.GetCryptoKey].
message GetCryptoKeyRequest {
  // Required. The [name][google.cloud.kms.v1.CryptoKey.name] of the
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];
}

// Request message for
// [KeyManagementService.GetCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.GetCryptoKeyVersion].
message GetCryptoKeyVersionRequest {
  // Required. The [name][google.cloud.kms.v1.CryptoKeyVersion.name] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];
}

// Request message for
// [KeyManagementService.GetPublicKey][google.cloud.kms.v1.KeyManagementService.GetPublicKey].
message GetPublicKeyRequest {
  // Required. The [name][google.cloud.kms.v1.CryptoKeyVersion.name] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] public key to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];
}

// Request message for
// [KeyManagementService.GetImportJob][google.cloud.kms.v1.KeyManagementService.GetImportJob].
message GetImportJobRequest {
  // Required. The [name][google.cloud.kms.v1.ImportJob.name] of the
  // [ImportJob][google.cloud.kms.v1.ImportJob] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/ImportJob"
    }
  ];
}

// Request message for
// [KeyManagementService.CreateKeyRing][google.cloud.kms.v1.KeyManagementService.CreateKeyRing].
message CreateKeyRingRequest {
  // Required. The resource name of the location associated with the
  // [KeyRings][google.cloud.kms.v1.KeyRing], in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. It must be unique within a location and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`
  string key_ring_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A [KeyRing][google.cloud.kms.v1.KeyRing] with initial field
  // values.
  KeyRing key_ring = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [KeyManagementService.CreateCryptoKey][google.cloud.kms.v1.KeyManagementService.CreateCryptoKey].
message CreateCryptoKeyRequest {
  // Required. The [name][google.cloud.kms.v1.KeyRing.name] of the KeyRing
  // associated with the [CryptoKeys][google.cloud.kms.v1.CryptoKey].
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/KeyRing"
    }
  ];

  // Required. It must be unique within a KeyRing and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`
  string crypto_key_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A [CryptoKey][google.cloud.kms.v1.CryptoKey] with initial field
  // values.
  CryptoKey crypto_key = 3 [(google.api.field_behavior) = REQUIRED];

  // If set to true, the request will create a
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] without any
  // [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion]. You must
  // manually call
  // [CreateCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.CreateCryptoKeyVersion]
  // or
  // [ImportCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.ImportCryptoKeyVersion]
  // before you can use this [CryptoKey][google.cloud.kms.v1.CryptoKey].
  bool skip_initial_version_creation = 5;
}

// Request message for
// [KeyManagementService.CreateCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.CreateCryptoKeyVersion].
message CreateCryptoKeyVersionRequest {
  // Required. The [name][google.cloud.kms.v1.CryptoKey.name] of the
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] associated with the
  // [CryptoKeyVersions][google.cloud.kms.v1.CryptoKeyVersion].
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Required. A [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] with
  // initial field values.
  CryptoKeyVersion crypto_key_version = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [KeyManagementService.ImportCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.ImportCryptoKeyVersion].
message ImportCryptoKeyVersionRequest {
  // Required. The [name][google.cloud.kms.v1.CryptoKey.name] of the
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] to be imported into.
  //
  // The create permission is only required on this key when creating a new
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion].
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Optional. The optional [name][google.cloud.kms.v1.CryptoKeyVersion.name] of
  // an existing [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to
  // target for an import operation. If this field is not present, a new
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] containing the
  // supplied key material is created.
  //
  // If this field is present, the supplied key material is imported into
  // the existing [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]. To
  // import into an existing
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion], the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] must be a child of
  // [ImportCryptoKeyVersionRequest.parent][google.cloud.kms.v1.ImportCryptoKeyVersionRequest.parent],
  // have been previously created via
  // [ImportCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.ImportCryptoKeyVersion],
  // and be in
  // [DESTROYED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.DESTROYED]
  // or
  // [IMPORT_FAILED][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionState.IMPORT_FAILED]
  // state. The key material and algorithm must match the previous
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] exactly if the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] has ever contained
  // key material.
  string crypto_key_version = 6 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];

  // Required. The
  // [algorithm][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionAlgorithm]
  // of the key being imported. This does not need to match the
  // [version_template][google.cloud.kms.v1.CryptoKey.version_template] of the
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] this version imports into.
  CryptoKeyVersion.CryptoKeyVersionAlgorithm algorithm = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The [name][google.cloud.kms.v1.ImportJob.name] of the
  // [ImportJob][google.cloud.kms.v1.ImportJob] that was used to wrap this key
  // material.
  string import_job = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The wrapped key material to import.
  //
  // Before wrapping, key material must be formatted. If importing symmetric key
  // material, the expected key material format is plain bytes. If importing
  // asymmetric key material, the expected key material format is PKCS#8-encoded
  // DER (the PrivateKeyInfo structure from RFC 5208).
  //
  // When wrapping with import methods
  // ([RSA_OAEP_3072_SHA1_AES_256][google.cloud.kms.v1.ImportJob.ImportMethod.RSA_OAEP_3072_SHA1_AES_256]
  // or
  // [RSA_OAEP_4096_SHA1_AES_256][google.cloud.kms.v1.ImportJob.ImportMethod.RSA_OAEP_4096_SHA1_AES_256]
  // or
  // [RSA_OAEP_3072_SHA256_AES_256][google.cloud.kms.v1.ImportJob.ImportMethod.RSA_OAEP_3072_SHA256_AES_256]
  // or
  // [RSA_OAEP_4096_SHA256_AES_256][google.cloud.kms.v1.ImportJob.ImportMethod.RSA_OAEP_4096_SHA256_AES_256]),
  //
  // this field must contain the concatenation of:
  // <ol>
  //   <li>An ephemeral AES-256 wrapping key wrapped with the
  //       [public_key][google.cloud.kms.v1.ImportJob.public_key] using
  //       RSAES-OAEP with SHA-1/SHA-256, MGF1 with SHA-1/SHA-256, and an empty
  //       label.
  //   </li>
  //   <li>The formatted key to be imported, wrapped with the ephemeral AES-256
  //       key using AES-KWP (RFC 5649).
  //   </li>
  // </ol>
  //
  // This format is the same as the format produced by PKCS#11 mechanism
  // CKM_RSA_AES_KEY_WRAP.
  //
  // When wrapping with import methods
  // ([RSA_OAEP_3072_SHA256][google.cloud.kms.v1.ImportJob.ImportMethod.RSA_OAEP_3072_SHA256]
  // or
  // [RSA_OAEP_4096_SHA256][google.cloud.kms.v1.ImportJob.ImportMethod.RSA_OAEP_4096_SHA256]),
  //
  // this field must contain the formatted key to be imported, wrapped with the
  // [public_key][google.cloud.kms.v1.ImportJob.public_key] using RSAES-OAEP
  // with SHA-256, MGF1 with SHA-256, and an empty label.
  bytes wrapped_key = 8 [(google.api.field_behavior) = OPTIONAL];

  // This field is legacy. Use the field
  // [wrapped_key][google.cloud.kms.v1.ImportCryptoKeyVersionRequest.wrapped_key]
  // instead.
  oneof wrapped_key_material {
    // Optional. This field has the same meaning as
    // [wrapped_key][google.cloud.kms.v1.ImportCryptoKeyVersionRequest.wrapped_key].
    // Prefer to use that field in new work. Either that field or this field
    // (but not both) must be specified.
    bytes rsa_aes_wrapped_key = 5 [(google.api.field_behavior) = OPTIONAL];
  }
}

// Request message for
// [KeyManagementService.CreateImportJob][google.cloud.kms.v1.KeyManagementService.CreateImportJob].
message CreateImportJobRequest {
  // Required. The [name][google.cloud.kms.v1.KeyRing.name] of the
  // [KeyRing][google.cloud.kms.v1.KeyRing] associated with the
  // [ImportJobs][google.cloud.kms.v1.ImportJob].
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/KeyRing"
    }
  ];

  // Required. It must be unique within a KeyRing and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`
  string import_job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. An [ImportJob][google.cloud.kms.v1.ImportJob] with initial field
  // values.
  ImportJob import_job = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [KeyManagementService.UpdateCryptoKey][google.cloud.kms.v1.KeyManagementService.UpdateCryptoKey].
message UpdateCryptoKeyRequest {
  // Required. [CryptoKey][google.cloud.kms.v1.CryptoKey] with updated values.
  CryptoKey crypto_key = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [KeyManagementService.UpdateCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.UpdateCryptoKeyVersion].
message UpdateCryptoKeyVersionRequest {
  // Required. [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] with
  // updated values.
  CryptoKeyVersion crypto_key_version = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [KeyManagementService.UpdateCryptoKeyPrimaryVersion][google.cloud.kms.v1.KeyManagementService.UpdateCryptoKeyPrimaryVersion].
message UpdateCryptoKeyPrimaryVersionRequest {
  // Required. The resource name of the
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] to update.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Required. The id of the child
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to use as primary.
  string crypto_key_version_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [KeyManagementService.DestroyCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.DestroyCryptoKeyVersion].
message DestroyCryptoKeyVersionRequest {
  // Required. The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to destroy.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];
}

// Request message for
// [KeyManagementService.RestoreCryptoKeyVersion][google.cloud.kms.v1.KeyManagementService.RestoreCryptoKeyVersion].
message RestoreCryptoKeyVersionRequest {
  // Required. The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to restore.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];
}

// Request message for
// [KeyManagementService.Encrypt][google.cloud.kms.v1.KeyManagementService.Encrypt].
message EncryptRequest {
  // Required. The resource name of the
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] or
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to use for
  // encryption.
  //
  // If a [CryptoKey][google.cloud.kms.v1.CryptoKey] is specified, the server
  // will use its [primary version][google.cloud.kms.v1.CryptoKey.primary].
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // Required. The data to encrypt. Must be no larger than 64KiB.
  //
  // The maximum size depends on the key version's
  // [protection_level][google.cloud.kms.v1.CryptoKeyVersionTemplate.protection_level].
  // For [SOFTWARE][google.cloud.kms.v1.ProtectionLevel.SOFTWARE],
  // [EXTERNAL][google.cloud.kms.v1.ProtectionLevel.EXTERNAL], and
  // [EXTERNAL_VPC][google.cloud.kms.v1.ProtectionLevel.EXTERNAL_VPC] keys, the
  // plaintext must be no larger than 64KiB. For
  // [HSM][google.cloud.kms.v1.ProtectionLevel.HSM] keys, the combined length of
  // the plaintext and additional_authenticated_data fields must be no larger
  // than 8KiB.
  bytes plaintext = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Optional data that, if specified, must also be provided during
  // decryption through
  // [DecryptRequest.additional_authenticated_data][google.cloud.kms.v1.DecryptRequest.additional_authenticated_data].
  //
  // The maximum size depends on the key version's
  // [protection_level][google.cloud.kms.v1.CryptoKeyVersionTemplate.protection_level].
  // For [SOFTWARE][google.cloud.kms.v1.ProtectionLevel.SOFTWARE],
  // [EXTERNAL][google.cloud.kms.v1.ProtectionLevel.EXTERNAL], and
  // [EXTERNAL_VPC][google.cloud.kms.v1.ProtectionLevel.EXTERNAL_VPC] keys the
  // AAD must be no larger than 64KiB. For
  // [HSM][google.cloud.kms.v1.ProtectionLevel.HSM] keys, the combined length of
  // the plaintext and additional_authenticated_data fields must be no larger
  // than 8KiB.
  bytes additional_authenticated_data = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [EncryptRequest.plaintext][google.cloud.kms.v1.EncryptRequest.plaintext].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received
  // [EncryptRequest.plaintext][google.cloud.kms.v1.EncryptRequest.plaintext]
  // using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([EncryptRequest.plaintext][google.cloud.kms.v1.EncryptRequest.plaintext])
  // is equal to
  // [EncryptRequest.plaintext_crc32c][google.cloud.kms.v1.EncryptRequest.plaintext_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value plaintext_crc32c = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [EncryptRequest.additional_authenticated_data][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received
  // [EncryptRequest.additional_authenticated_data][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data]
  // using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([EncryptRequest.additional_authenticated_data][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data])
  // is equal to
  // [EncryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value additional_authenticated_data_crc32c = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.Decrypt][google.cloud.kms.v1.KeyManagementService.Decrypt].
message DecryptRequest {
  // Required. The resource name of the
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] to use for decryption. The
  // server will choose the appropriate version.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Required. The encrypted data originally returned in
  // [EncryptResponse.ciphertext][google.cloud.kms.v1.EncryptResponse.ciphertext].
  bytes ciphertext = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Optional data that must match the data originally supplied in
  // [EncryptRequest.additional_authenticated_data][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data].
  bytes additional_authenticated_data = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [DecryptRequest.ciphertext][google.cloud.kms.v1.DecryptRequest.ciphertext].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received
  // [DecryptRequest.ciphertext][google.cloud.kms.v1.DecryptRequest.ciphertext]
  // using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([DecryptRequest.ciphertext][google.cloud.kms.v1.DecryptRequest.ciphertext])
  // is equal to
  // [DecryptRequest.ciphertext_crc32c][google.cloud.kms.v1.DecryptRequest.ciphertext_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value ciphertext_crc32c = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [DecryptRequest.additional_authenticated_data][google.cloud.kms.v1.DecryptRequest.additional_authenticated_data].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received
  // [DecryptRequest.additional_authenticated_data][google.cloud.kms.v1.DecryptRequest.additional_authenticated_data]
  // using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([DecryptRequest.additional_authenticated_data][google.cloud.kms.v1.DecryptRequest.additional_authenticated_data])
  // is equal to
  // [DecryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.DecryptRequest.additional_authenticated_data_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value additional_authenticated_data_crc32c = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.RawEncrypt][google.cloud.kms.v1.KeyManagementService.RawEncrypt].
message RawEncryptRequest {
  // Required. The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to use for
  // encryption.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The data to encrypt. Must be no larger than 64KiB.
  //
  // The maximum size depends on the key version's
  // [protection_level][google.cloud.kms.v1.CryptoKeyVersionTemplate.protection_level].
  // For [SOFTWARE][google.cloud.kms.v1.ProtectionLevel.SOFTWARE] keys, the
  // plaintext must be no larger than 64KiB. For
  // [HSM][google.cloud.kms.v1.ProtectionLevel.HSM] keys, the combined length of
  // the plaintext and additional_authenticated_data fields must be no larger
  // than 8KiB.
  bytes plaintext = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Optional data that, if specified, must also be provided during
  // decryption through
  // [RawDecryptRequest.additional_authenticated_data][google.cloud.kms.v1.RawDecryptRequest.additional_authenticated_data].
  //
  // This field may only be used in conjunction with an
  // [algorithm][google.cloud.kms.v1.CryptoKeyVersion.algorithm] that accepts
  // additional authenticated data (for example, AES-GCM).
  //
  // The maximum size depends on the key version's
  // [protection_level][google.cloud.kms.v1.CryptoKeyVersionTemplate.protection_level].
  // For [SOFTWARE][google.cloud.kms.v1.ProtectionLevel.SOFTWARE] keys, the
  // plaintext must be no larger than 64KiB. For
  // [HSM][google.cloud.kms.v1.ProtectionLevel.HSM] keys, the combined length of
  // the plaintext and additional_authenticated_data fields must be no larger
  // than 8KiB.
  bytes additional_authenticated_data = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [RawEncryptRequest.plaintext][google.cloud.kms.v1.RawEncryptRequest.plaintext].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received plaintext using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that CRC32C(plaintext) is equal
  // to plaintext_crc32c, and if so, perform a limited number of retries. A
  // persistent mismatch may indicate an issue in your computation of the CRC32C
  // checksum. Note: This field is defined as int64 for reasons of compatibility
  // across different languages. However, it is a non-negative integer, which
  // will never exceed 2^32-1, and can be safely downconverted to uint32 in
  // languages that support this type.
  google.protobuf.Int64Value plaintext_crc32c = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [RawEncryptRequest.additional_authenticated_data][google.cloud.kms.v1.RawEncryptRequest.additional_authenticated_data].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received additional_authenticated_data using
  // this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C(additional_authenticated_data) is equal to
  // additional_authenticated_data_crc32c, and if so, perform
  // a limited number of retries. A persistent mismatch may indicate an issue in
  // your computation of the CRC32C checksum.
  // Note: This field is defined as int64 for reasons of compatibility across
  // different languages. However, it is a non-negative integer, which will
  // never exceed 2^32-1, and can be safely downconverted to uint32 in languages
  // that support this type.
  google.protobuf.Int64Value additional_authenticated_data_crc32c = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A customer-supplied initialization vector that will be used for
  // encryption. If it is not provided for AES-CBC and AES-CTR, one will be
  // generated. It will be returned in
  // [RawEncryptResponse.initialization_vector][google.cloud.kms.v1.RawEncryptResponse.initialization_vector].
  bytes initialization_vector = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [RawEncryptRequest.initialization_vector][google.cloud.kms.v1.RawEncryptRequest.initialization_vector].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received initialization_vector using this
  // checksum. [KeyManagementService][google.cloud.kms.v1.KeyManagementService]
  // will report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C(initialization_vector) is equal to
  // initialization_vector_crc32c, and if so, perform
  // a limited number of retries. A persistent mismatch may indicate an issue in
  // your computation of the CRC32C checksum.
  // Note: This field is defined as int64 for reasons of compatibility across
  // different languages. However, it is a non-negative integer, which will
  // never exceed 2^32-1, and can be safely downconverted to uint32 in languages
  // that support this type.
  google.protobuf.Int64Value initialization_vector_crc32c = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.RawDecrypt][google.cloud.kms.v1.KeyManagementService.RawDecrypt].
message RawDecryptRequest {
  // Required. The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to use for
  // decryption.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The encrypted data originally returned in
  // [RawEncryptResponse.ciphertext][google.cloud.kms.v1.RawEncryptResponse.ciphertext].
  bytes ciphertext = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Optional data that must match the data originally supplied in
  // [RawEncryptRequest.additional_authenticated_data][google.cloud.kms.v1.RawEncryptRequest.additional_authenticated_data].
  bytes additional_authenticated_data = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The initialization vector (IV) used during encryption, which must
  // match the data originally provided in
  // [RawEncryptResponse.initialization_vector][google.cloud.kms.v1.RawEncryptResponse.initialization_vector].
  bytes initialization_vector = 4 [(google.api.field_behavior) = REQUIRED];

  // The length of the authentication tag that is appended to the end of
  // the ciphertext. If unspecified (0), the default value for the key's
  // algorithm will be used (for AES-GCM, the default value is 16).
  int32 tag_length = 5;

  // Optional. An optional CRC32C checksum of the
  // [RawDecryptRequest.ciphertext][google.cloud.kms.v1.RawDecryptRequest.ciphertext].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received ciphertext using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that CRC32C(ciphertext) is equal
  // to ciphertext_crc32c, and if so, perform a limited number of retries. A
  // persistent mismatch may indicate an issue in your computation of the CRC32C
  // checksum. Note: This field is defined as int64 for reasons of compatibility
  // across different languages. However, it is a non-negative integer, which
  // will never exceed 2^32-1, and can be safely downconverted to uint32 in
  // languages that support this type.
  google.protobuf.Int64Value ciphertext_crc32c = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [RawDecryptRequest.additional_authenticated_data][google.cloud.kms.v1.RawDecryptRequest.additional_authenticated_data].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received additional_authenticated_data using
  // this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C(additional_authenticated_data) is equal to
  // additional_authenticated_data_crc32c, and if so, perform
  // a limited number of retries. A persistent mismatch may indicate an issue in
  // your computation of the CRC32C checksum.
  // Note: This field is defined as int64 for reasons of compatibility across
  // different languages. However, it is a non-negative integer, which will
  // never exceed 2^32-1, and can be safely downconverted to uint32 in languages
  // that support this type.
  google.protobuf.Int64Value additional_authenticated_data_crc32c = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [RawDecryptRequest.initialization_vector][google.cloud.kms.v1.RawDecryptRequest.initialization_vector].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received initialization_vector using this
  // checksum. [KeyManagementService][google.cloud.kms.v1.KeyManagementService]
  // will report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C(initialization_vector) is equal to initialization_vector_crc32c, and
  // if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum.
  // Note: This field is defined as int64 for reasons of compatibility across
  // different languages. However, it is a non-negative integer, which will
  // never exceed 2^32-1, and can be safely downconverted to uint32 in languages
  // that support this type.
  google.protobuf.Int64Value initialization_vector_crc32c = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.AsymmetricSign][google.cloud.kms.v1.KeyManagementService.AsymmetricSign].
message AsymmetricSignRequest {
  // Required. The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to use for
  // signing.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];

  // Optional. The digest of the data to sign. The digest must be produced with
  // the same digest algorithm as specified by the key version's
  // [algorithm][google.cloud.kms.v1.CryptoKeyVersion.algorithm].
  //
  // This field may not be supplied if
  // [AsymmetricSignRequest.data][google.cloud.kms.v1.AsymmetricSignRequest.data]
  // is supplied.
  Digest digest = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [AsymmetricSignRequest.digest][google.cloud.kms.v1.AsymmetricSignRequest.digest].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received
  // [AsymmetricSignRequest.digest][google.cloud.kms.v1.AsymmetricSignRequest.digest]
  // using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([AsymmetricSignRequest.digest][google.cloud.kms.v1.AsymmetricSignRequest.digest])
  // is equal to
  // [AsymmetricSignRequest.digest_crc32c][google.cloud.kms.v1.AsymmetricSignRequest.digest_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value digest_crc32c = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The data to sign.
  // It can't be supplied if
  // [AsymmetricSignRequest.digest][google.cloud.kms.v1.AsymmetricSignRequest.digest]
  // is supplied.
  bytes data = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional CRC32C checksum of the
  // [AsymmetricSignRequest.data][google.cloud.kms.v1.AsymmetricSignRequest.data].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received
  // [AsymmetricSignRequest.data][google.cloud.kms.v1.AsymmetricSignRequest.data]
  // using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([AsymmetricSignRequest.data][google.cloud.kms.v1.AsymmetricSignRequest.data])
  // is equal to
  // [AsymmetricSignRequest.data_crc32c][google.cloud.kms.v1.AsymmetricSignRequest.data_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value data_crc32c = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.AsymmetricDecrypt][google.cloud.kms.v1.KeyManagementService.AsymmetricDecrypt].
message AsymmetricDecryptRequest {
  // Required. The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to use for
  // decryption.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];

  // Required. The data encrypted with the named
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion]'s public key using
  // OAEP.
  bytes ciphertext = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional CRC32C checksum of the
  // [AsymmetricDecryptRequest.ciphertext][google.cloud.kms.v1.AsymmetricDecryptRequest.ciphertext].
  // If specified,
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // verify the integrity of the received
  // [AsymmetricDecryptRequest.ciphertext][google.cloud.kms.v1.AsymmetricDecryptRequest.ciphertext]
  // using this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([AsymmetricDecryptRequest.ciphertext][google.cloud.kms.v1.AsymmetricDecryptRequest.ciphertext])
  // is equal to
  // [AsymmetricDecryptRequest.ciphertext_crc32c][google.cloud.kms.v1.AsymmetricDecryptRequest.ciphertext_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value ciphertext_crc32c = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.MacSign][google.cloud.kms.v1.KeyManagementService.MacSign].
message MacSignRequest {
  // Required. The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to use for
  // signing.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];

  // Required. The data to sign. The MAC tag is computed over this data field
  // based on the specific algorithm.
  bytes data = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional CRC32C checksum of the
  // [MacSignRequest.data][google.cloud.kms.v1.MacSignRequest.data]. If
  // specified, [KeyManagementService][google.cloud.kms.v1.KeyManagementService]
  // will verify the integrity of the received
  // [MacSignRequest.data][google.cloud.kms.v1.MacSignRequest.data] using this
  // checksum. [KeyManagementService][google.cloud.kms.v1.KeyManagementService]
  // will report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([MacSignRequest.data][google.cloud.kms.v1.MacSignRequest.data]) is
  // equal to
  // [MacSignRequest.data_crc32c][google.cloud.kms.v1.MacSignRequest.data_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value data_crc32c = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.MacVerify][google.cloud.kms.v1.KeyManagementService.MacVerify].
message MacVerifyRequest {
  // Required. The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] to use for
  // verification.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];

  // Required. The data used previously as a
  // [MacSignRequest.data][google.cloud.kms.v1.MacSignRequest.data] to generate
  // the MAC tag.
  bytes data = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional CRC32C checksum of the
  // [MacVerifyRequest.data][google.cloud.kms.v1.MacVerifyRequest.data]. If
  // specified, [KeyManagementService][google.cloud.kms.v1.KeyManagementService]
  // will verify the integrity of the received
  // [MacVerifyRequest.data][google.cloud.kms.v1.MacVerifyRequest.data] using
  // this checksum.
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] will
  // report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([MacVerifyRequest.data][google.cloud.kms.v1.MacVerifyRequest.data])
  // is equal to
  // [MacVerifyRequest.data_crc32c][google.cloud.kms.v1.MacVerifyRequest.data_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value data_crc32c = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The signature to verify.
  bytes mac = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional CRC32C checksum of the
  // [MacVerifyRequest.mac][google.cloud.kms.v1.MacVerifyRequest.mac]. If
  // specified, [KeyManagementService][google.cloud.kms.v1.KeyManagementService]
  // will verify the integrity of the received
  // [MacVerifyRequest.mac][google.cloud.kms.v1.MacVerifyRequest.mac] using this
  // checksum. [KeyManagementService][google.cloud.kms.v1.KeyManagementService]
  // will report an error if the checksum verification fails. If you receive a
  // checksum error, your client should verify that
  // CRC32C([MacVerifyRequest.mac][google.cloud.kms.v1.MacVerifyRequest.mac]) is
  // equal to
  // [MacVerifyRequest.mac_crc32c][google.cloud.kms.v1.MacVerifyRequest.mac_crc32c],
  // and if so, perform a limited number of retries. A persistent mismatch may
  // indicate an issue in your computation of the CRC32C checksum. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value mac_crc32c = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [KeyManagementService.GenerateRandomBytes][google.cloud.kms.v1.KeyManagementService.GenerateRandomBytes].
message GenerateRandomBytesRequest {
  // The project-specific location in which to generate random bytes.
  // For example, "projects/my-project/locations/us-central1".
  string location = 1;

  // The length in bytes of the amount of randomness to retrieve.  Minimum 8
  // bytes, maximum 1024 bytes.
  int32 length_bytes = 2;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] to use when
  // generating the random data. Currently, only
  // [HSM][google.cloud.kms.v1.ProtectionLevel.HSM] protection level is
  // supported.
  ProtectionLevel protection_level = 3;
}

// Response message for
// [KeyManagementService.Encrypt][google.cloud.kms.v1.KeyManagementService.Encrypt].
message EncryptResponse {
  // The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used in
  // encryption. Check this field to verify that the intended resource was used
  // for encryption.
  string name = 1;

  // The encrypted data.
  bytes ciphertext = 2;

  // Integrity verification field. A CRC32C checksum of the returned
  // [EncryptResponse.ciphertext][google.cloud.kms.v1.EncryptResponse.ciphertext].
  // An integrity check of
  // [EncryptResponse.ciphertext][google.cloud.kms.v1.EncryptResponse.ciphertext]
  // can be performed by computing the CRC32C checksum of
  // [EncryptResponse.ciphertext][google.cloud.kms.v1.EncryptResponse.ciphertext]
  // and comparing your results to this field. Discard the response in case of
  // non-matching checksum values, and perform a limited number of retries. A
  // persistent mismatch may indicate an issue in your computation of the CRC32C
  // checksum. Note: This field is defined as int64 for reasons of compatibility
  // across different languages. However, it is a non-negative integer, which
  // will never exceed 2^32-1, and can be safely downconverted to uint32 in
  // languages that support this type.
  google.protobuf.Int64Value ciphertext_crc32c = 4;

  // Integrity verification field. A flag indicating whether
  // [EncryptRequest.plaintext_crc32c][google.cloud.kms.v1.EncryptRequest.plaintext_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the
  // [plaintext][google.cloud.kms.v1.EncryptRequest.plaintext]. A false value of
  // this field indicates either that
  // [EncryptRequest.plaintext_crc32c][google.cloud.kms.v1.EncryptRequest.plaintext_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [EncryptRequest.plaintext_crc32c][google.cloud.kms.v1.EncryptRequest.plaintext_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_plaintext_crc32c = 5;

  // Integrity verification field. A flag indicating whether
  // [EncryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the
  // [AAD][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data]. A
  // false value of this field indicates either that
  // [EncryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [EncryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.EncryptRequest.additional_authenticated_data_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_additional_authenticated_data_crc32c = 6;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used in
  // encryption.
  ProtectionLevel protection_level = 7;
}

// Response message for
// [KeyManagementService.Decrypt][google.cloud.kms.v1.KeyManagementService.Decrypt].
message DecryptResponse {
  // The decrypted data originally supplied in
  // [EncryptRequest.plaintext][google.cloud.kms.v1.EncryptRequest.plaintext].
  bytes plaintext = 1;

  // Integrity verification field. A CRC32C checksum of the returned
  // [DecryptResponse.plaintext][google.cloud.kms.v1.DecryptResponse.plaintext].
  // An integrity check of
  // [DecryptResponse.plaintext][google.cloud.kms.v1.DecryptResponse.plaintext]
  // can be performed by computing the CRC32C checksum of
  // [DecryptResponse.plaintext][google.cloud.kms.v1.DecryptResponse.plaintext]
  // and comparing your results to this field. Discard the response in case of
  // non-matching checksum values, and perform a limited number of retries. A
  // persistent mismatch may indicate an issue in your computation of the CRC32C
  // checksum. Note: receiving this response message indicates that
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] is able to
  // successfully decrypt the
  // [ciphertext][google.cloud.kms.v1.DecryptRequest.ciphertext]. Note: This
  // field is defined as int64 for reasons of compatibility across different
  // languages. However, it is a non-negative integer, which will never exceed
  // 2^32-1, and can be safely downconverted to uint32 in languages that support
  // this type.
  google.protobuf.Int64Value plaintext_crc32c = 2;

  // Whether the Decryption was performed using the primary key version.
  bool used_primary = 3;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used in
  // decryption.
  ProtectionLevel protection_level = 4;
}

// Response message for
// [KeyManagementService.RawEncrypt][google.cloud.kms.v1.KeyManagementService.RawEncrypt].
message RawEncryptResponse {
  // The encrypted data. In the case of AES-GCM, the authentication tag
  // is the [tag_length][google.cloud.kms.v1.RawEncryptResponse.tag_length]
  // bytes at the end of this field.
  bytes ciphertext = 1;

  // The initialization vector (IV) generated by the service during
  // encryption. This value must be stored and provided in
  // [RawDecryptRequest.initialization_vector][google.cloud.kms.v1.RawDecryptRequest.initialization_vector]
  // at decryption time.
  bytes initialization_vector = 2;

  // The length of the authentication tag that is appended to
  // the end of the ciphertext.
  int32 tag_length = 3;

  // Integrity verification field. A CRC32C checksum of the returned
  // [RawEncryptResponse.ciphertext][google.cloud.kms.v1.RawEncryptResponse.ciphertext].
  // An integrity check of ciphertext can be performed by computing the CRC32C
  // checksum of ciphertext and comparing your results to this field. Discard
  // the response in case of non-matching checksum values, and perform a limited
  // number of retries. A persistent mismatch may indicate an issue in your
  // computation of the CRC32C checksum. Note: This field is defined as int64
  // for reasons of compatibility across different languages. However, it is a
  // non-negative integer, which will never exceed 2^32-1, and can be safely
  // downconverted to uint32 in languages that support this type.
  google.protobuf.Int64Value ciphertext_crc32c = 4;

  // Integrity verification field. A CRC32C checksum of the returned
  // [RawEncryptResponse.initialization_vector][google.cloud.kms.v1.RawEncryptResponse.initialization_vector].
  // An integrity check of initialization_vector can be performed by computing
  // the CRC32C checksum of initialization_vector and comparing your results to
  // this field. Discard the response in case of non-matching checksum values,
  // and perform a limited number of retries. A persistent mismatch may indicate
  // an issue in your computation of the CRC32C checksum. Note: This field is
  // defined as int64 for reasons of compatibility across different languages.
  // However, it is a non-negative integer, which will never exceed 2^32-1, and
  // can be safely downconverted to uint32 in languages that support this type.
  google.protobuf.Int64Value initialization_vector_crc32c = 5;

  // Integrity verification field. A flag indicating whether
  // [RawEncryptRequest.plaintext_crc32c][google.cloud.kms.v1.RawEncryptRequest.plaintext_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the plaintext. A false value of this
  // field indicates either that
  // [RawEncryptRequest.plaintext_crc32c][google.cloud.kms.v1.RawEncryptRequest.plaintext_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [RawEncryptRequest.plaintext_crc32c][google.cloud.kms.v1.RawEncryptRequest.plaintext_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_plaintext_crc32c = 6;

  // Integrity verification field. A flag indicating whether
  // [RawEncryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.RawEncryptRequest.additional_authenticated_data_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of additional_authenticated_data. A false
  // value of this field indicates either that //
  // [RawEncryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.RawEncryptRequest.additional_authenticated_data_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [RawEncryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.RawEncryptRequest.additional_authenticated_data_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_additional_authenticated_data_crc32c = 7;

  // Integrity verification field. A flag indicating whether
  // [RawEncryptRequest.initialization_vector_crc32c][google.cloud.kms.v1.RawEncryptRequest.initialization_vector_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of initialization_vector. A false value of
  // this field indicates either that
  // [RawEncryptRequest.initialization_vector_crc32c][google.cloud.kms.v1.RawEncryptRequest.initialization_vector_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [RawEncryptRequest.initialization_vector_crc32c][google.cloud.kms.v1.RawEncryptRequest.initialization_vector_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_initialization_vector_crc32c = 10;

  // The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used in
  // encryption. Check this field to verify that the intended resource was used
  // for encryption.
  string name = 8;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used in
  // encryption.
  ProtectionLevel protection_level = 9;
}

// Response message for
// [KeyManagementService.RawDecrypt][google.cloud.kms.v1.KeyManagementService.RawDecrypt].
message RawDecryptResponse {
  // The decrypted data.
  bytes plaintext = 1;

  // Integrity verification field. A CRC32C checksum of the returned
  // [RawDecryptResponse.plaintext][google.cloud.kms.v1.RawDecryptResponse.plaintext].
  // An integrity check of plaintext can be performed by computing the CRC32C
  // checksum of plaintext and comparing your results to this field. Discard the
  // response in case of non-matching checksum values, and perform a limited
  // number of retries. A persistent mismatch may indicate an issue in your
  // computation of the CRC32C checksum. Note: receiving this response message
  // indicates that
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] is able to
  // successfully decrypt the
  // [ciphertext][google.cloud.kms.v1.RawDecryptRequest.ciphertext].
  // Note: This field is defined as int64 for reasons of compatibility across
  // different languages. However, it is a non-negative integer, which will
  // never exceed 2^32-1, and can be safely downconverted to uint32 in languages
  // that support this type.
  google.protobuf.Int64Value plaintext_crc32c = 2;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used in
  // decryption.
  ProtectionLevel protection_level = 3;

  // Integrity verification field. A flag indicating whether
  // [RawDecryptRequest.ciphertext_crc32c][google.cloud.kms.v1.RawDecryptRequest.ciphertext_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the ciphertext. A false value of this
  // field indicates either that
  // [RawDecryptRequest.ciphertext_crc32c][google.cloud.kms.v1.RawDecryptRequest.ciphertext_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [RawDecryptRequest.ciphertext_crc32c][google.cloud.kms.v1.RawDecryptRequest.ciphertext_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_ciphertext_crc32c = 4;

  // Integrity verification field. A flag indicating whether
  // [RawDecryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.RawDecryptRequest.additional_authenticated_data_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of additional_authenticated_data. A false
  // value of this field indicates either that //
  // [RawDecryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.RawDecryptRequest.additional_authenticated_data_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [RawDecryptRequest.additional_authenticated_data_crc32c][google.cloud.kms.v1.RawDecryptRequest.additional_authenticated_data_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_additional_authenticated_data_crc32c = 5;

  // Integrity verification field. A flag indicating whether
  // [RawDecryptRequest.initialization_vector_crc32c][google.cloud.kms.v1.RawDecryptRequest.initialization_vector_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of initialization_vector. A false value of
  // this field indicates either that
  // [RawDecryptRequest.initialization_vector_crc32c][google.cloud.kms.v1.RawDecryptRequest.initialization_vector_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [RawDecryptRequest.initialization_vector_crc32c][google.cloud.kms.v1.RawDecryptRequest.initialization_vector_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_initialization_vector_crc32c = 6;
}

// Response message for
// [KeyManagementService.AsymmetricSign][google.cloud.kms.v1.KeyManagementService.AsymmetricSign].
message AsymmetricSignResponse {
  // The created signature.
  bytes signature = 1;

  // Integrity verification field. A CRC32C checksum of the returned
  // [AsymmetricSignResponse.signature][google.cloud.kms.v1.AsymmetricSignResponse.signature].
  // An integrity check of
  // [AsymmetricSignResponse.signature][google.cloud.kms.v1.AsymmetricSignResponse.signature]
  // can be performed by computing the CRC32C checksum of
  // [AsymmetricSignResponse.signature][google.cloud.kms.v1.AsymmetricSignResponse.signature]
  // and comparing your results to this field. Discard the response in case of
  // non-matching checksum values, and perform a limited number of retries. A
  // persistent mismatch may indicate an issue in your computation of the CRC32C
  // checksum. Note: This field is defined as int64 for reasons of compatibility
  // across different languages. However, it is a non-negative integer, which
  // will never exceed 2^32-1, and can be safely downconverted to uint32 in
  // languages that support this type.
  google.protobuf.Int64Value signature_crc32c = 2;

  // Integrity verification field. A flag indicating whether
  // [AsymmetricSignRequest.digest_crc32c][google.cloud.kms.v1.AsymmetricSignRequest.digest_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the
  // [digest][google.cloud.kms.v1.AsymmetricSignRequest.digest]. A false value
  // of this field indicates either that
  // [AsymmetricSignRequest.digest_crc32c][google.cloud.kms.v1.AsymmetricSignRequest.digest_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [AsymmetricSignRequest.digest_crc32c][google.cloud.kms.v1.AsymmetricSignRequest.digest_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_digest_crc32c = 3;

  // The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used for signing.
  // Check this field to verify that the intended resource was used for signing.
  string name = 4;

  // Integrity verification field. A flag indicating whether
  // [AsymmetricSignRequest.data_crc32c][google.cloud.kms.v1.AsymmetricSignRequest.data_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the
  // [data][google.cloud.kms.v1.AsymmetricSignRequest.data]. A false value of
  // this field indicates either that
  // [AsymmetricSignRequest.data_crc32c][google.cloud.kms.v1.AsymmetricSignRequest.data_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [AsymmetricSignRequest.data_crc32c][google.cloud.kms.v1.AsymmetricSignRequest.data_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_data_crc32c = 5;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used for signing.
  ProtectionLevel protection_level = 6;
}

// Response message for
// [KeyManagementService.AsymmetricDecrypt][google.cloud.kms.v1.KeyManagementService.AsymmetricDecrypt].
message AsymmetricDecryptResponse {
  // The decrypted data originally encrypted with the matching public key.
  bytes plaintext = 1;

  // Integrity verification field. A CRC32C checksum of the returned
  // [AsymmetricDecryptResponse.plaintext][google.cloud.kms.v1.AsymmetricDecryptResponse.plaintext].
  // An integrity check of
  // [AsymmetricDecryptResponse.plaintext][google.cloud.kms.v1.AsymmetricDecryptResponse.plaintext]
  // can be performed by computing the CRC32C checksum of
  // [AsymmetricDecryptResponse.plaintext][google.cloud.kms.v1.AsymmetricDecryptResponse.plaintext]
  // and comparing your results to this field. Discard the response in case of
  // non-matching checksum values, and perform a limited number of retries. A
  // persistent mismatch may indicate an issue in your computation of the CRC32C
  // checksum. Note: This field is defined as int64 for reasons of compatibility
  // across different languages. However, it is a non-negative integer, which
  // will never exceed 2^32-1, and can be safely downconverted to uint32 in
  // languages that support this type.
  google.protobuf.Int64Value plaintext_crc32c = 2;

  // Integrity verification field. A flag indicating whether
  // [AsymmetricDecryptRequest.ciphertext_crc32c][google.cloud.kms.v1.AsymmetricDecryptRequest.ciphertext_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the
  // [ciphertext][google.cloud.kms.v1.AsymmetricDecryptRequest.ciphertext]. A
  // false value of this field indicates either that
  // [AsymmetricDecryptRequest.ciphertext_crc32c][google.cloud.kms.v1.AsymmetricDecryptRequest.ciphertext_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [AsymmetricDecryptRequest.ciphertext_crc32c][google.cloud.kms.v1.AsymmetricDecryptRequest.ciphertext_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_ciphertext_crc32c = 3;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used in
  // decryption.
  ProtectionLevel protection_level = 4;
}

// Response message for
// [KeyManagementService.MacSign][google.cloud.kms.v1.KeyManagementService.MacSign].
message MacSignResponse {
  // The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used for signing.
  // Check this field to verify that the intended resource was used for signing.
  string name = 1;

  // The created signature.
  bytes mac = 2;

  // Integrity verification field. A CRC32C checksum of the returned
  // [MacSignResponse.mac][google.cloud.kms.v1.MacSignResponse.mac]. An
  // integrity check of
  // [MacSignResponse.mac][google.cloud.kms.v1.MacSignResponse.mac] can be
  // performed by computing the CRC32C checksum of
  // [MacSignResponse.mac][google.cloud.kms.v1.MacSignResponse.mac] and
  // comparing your results to this field. Discard the response in case of
  // non-matching checksum values, and perform a limited number of retries. A
  // persistent mismatch may indicate an issue in your computation of the CRC32C
  // checksum. Note: This field is defined as int64 for reasons of compatibility
  // across different languages. However, it is a non-negative integer, which
  // will never exceed 2^32-1, and can be safely downconverted to uint32 in
  // languages that support this type.
  google.protobuf.Int64Value mac_crc32c = 3;

  // Integrity verification field. A flag indicating whether
  // [MacSignRequest.data_crc32c][google.cloud.kms.v1.MacSignRequest.data_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the
  // [data][google.cloud.kms.v1.MacSignRequest.data]. A false value of this
  // field indicates either that
  // [MacSignRequest.data_crc32c][google.cloud.kms.v1.MacSignRequest.data_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [MacSignRequest.data_crc32c][google.cloud.kms.v1.MacSignRequest.data_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_data_crc32c = 4;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used for signing.
  ProtectionLevel protection_level = 5;
}

// Response message for
// [KeyManagementService.MacVerify][google.cloud.kms.v1.KeyManagementService.MacVerify].
message MacVerifyResponse {
  // The resource name of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used for
  // verification. Check this field to verify that the intended resource was
  // used for verification.
  string name = 1;

  // This field indicates whether or not the verification operation for
  // [MacVerifyRequest.mac][google.cloud.kms.v1.MacVerifyRequest.mac] over
  // [MacVerifyRequest.data][google.cloud.kms.v1.MacVerifyRequest.data] was
  // successful.
  bool success = 2;

  // Integrity verification field. A flag indicating whether
  // [MacVerifyRequest.data_crc32c][google.cloud.kms.v1.MacVerifyRequest.data_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the
  // [data][google.cloud.kms.v1.MacVerifyRequest.data]. A false value of this
  // field indicates either that
  // [MacVerifyRequest.data_crc32c][google.cloud.kms.v1.MacVerifyRequest.data_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [MacVerifyRequest.data_crc32c][google.cloud.kms.v1.MacVerifyRequest.data_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_data_crc32c = 3;

  // Integrity verification field. A flag indicating whether
  // [MacVerifyRequest.mac_crc32c][google.cloud.kms.v1.MacVerifyRequest.mac_crc32c]
  // was received by
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService] and used
  // for the integrity verification of the
  // [data][google.cloud.kms.v1.MacVerifyRequest.mac]. A false value of this
  // field indicates either that
  // [MacVerifyRequest.mac_crc32c][google.cloud.kms.v1.MacVerifyRequest.mac_crc32c]
  // was left unset or that it was not delivered to
  // [KeyManagementService][google.cloud.kms.v1.KeyManagementService]. If you've
  // set
  // [MacVerifyRequest.mac_crc32c][google.cloud.kms.v1.MacVerifyRequest.mac_crc32c]
  // but this field is still false, discard the response and perform a limited
  // number of retries.
  bool verified_mac_crc32c = 4;

  // Integrity verification field. This value is used for the integrity
  // verification of [MacVerifyResponse.success]. If the value of this field
  // contradicts the value of [MacVerifyResponse.success], discard the response
  // and perform a limited number of retries.
  bool verified_success_integrity = 5;

  // The [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] of the
  // [CryptoKeyVersion][google.cloud.kms.v1.CryptoKeyVersion] used for
  // verification.
  ProtectionLevel protection_level = 6;
}

// Response message for
// [KeyManagementService.GenerateRandomBytes][google.cloud.kms.v1.KeyManagementService.GenerateRandomBytes].
message GenerateRandomBytesResponse {
  // The generated data.
  bytes data = 1;

  // Integrity verification field. A CRC32C checksum of the returned
  // [GenerateRandomBytesResponse.data][google.cloud.kms.v1.GenerateRandomBytesResponse.data].
  // An integrity check of
  // [GenerateRandomBytesResponse.data][google.cloud.kms.v1.GenerateRandomBytesResponse.data]
  // can be performed by computing the CRC32C checksum of
  // [GenerateRandomBytesResponse.data][google.cloud.kms.v1.GenerateRandomBytesResponse.data]
  // and comparing your results to this field. Discard the response in case of
  // non-matching checksum values, and perform a limited number of retries. A
  // persistent mismatch may indicate an issue in your computation of the CRC32C
  // checksum. Note: This field is defined as int64 for reasons of compatibility
  // across different languages. However, it is a non-negative integer, which
  // will never exceed 2^32-1, and can be safely downconverted to uint32 in
  // languages that support this type.
  google.protobuf.Int64Value data_crc32c = 3;
}

// A [Digest][google.cloud.kms.v1.Digest] holds a cryptographic message digest.
message Digest {
  // Required. The message digest.
  oneof digest {
    // A message digest produced with the SHA-256 algorithm.
    bytes sha256 = 1;

    // A message digest produced with the SHA-384 algorithm.
    bytes sha384 = 2;

    // A message digest produced with the SHA-512 algorithm.
    bytes sha512 = 3;
  }
}

// Cloud KMS metadata for the given
// [google.cloud.location.Location][google.cloud.location.Location].
message LocationMetadata {
  // Indicates whether [CryptoKeys][google.cloud.kms.v1.CryptoKey] with
  // [protection_level][google.cloud.kms.v1.CryptoKeyVersionTemplate.protection_level]
  // [HSM][google.cloud.kms.v1.ProtectionLevel.HSM] can be created in this
  // location.
  bool hsm_available = 1;

  // Indicates whether [CryptoKeys][google.cloud.kms.v1.CryptoKey] with
  // [protection_level][google.cloud.kms.v1.CryptoKeyVersionTemplate.protection_level]
  // [EXTERNAL][google.cloud.kms.v1.ProtectionLevel.EXTERNAL] can be created in
  // this location.
  bool ekm_available = 2;
}
