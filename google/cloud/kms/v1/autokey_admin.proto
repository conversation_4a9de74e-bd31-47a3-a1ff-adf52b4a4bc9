// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.kms.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/kms/apiv1/kmspb;kmspb";
option java_multiple_files = true;
option java_outer_classname = "AutokeyAdminProto";
option java_package = "com.google.cloud.kms.v1";

// Provides interfaces for managing [Cloud KMS
// Autokey](https://cloud.google.com/kms/help/autokey) folder-level
// configurations. A configuration is inherited by all descendent projects. A
// configuration at one folder overrides any other configurations in its
// ancestry. Setting a configuration on a folder is a prerequisite for Cloud KMS
// Autokey, so that users working in a descendant project can request
// provisioned [CryptoKeys][google.cloud.kms.v1.CryptoKey], ready for Customer
// Managed Encryption Key (CMEK) use, on-demand.
service AutokeyAdmin {
  option (google.api.default_host) = "cloudkms.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloudkms";

  // Updates the [AutokeyConfig][google.cloud.kms.v1.AutokeyConfig] for a
  // folder. The caller must have both `cloudkms.autokeyConfigs.update`
  // permission on the parent folder and `cloudkms.cryptoKeys.setIamPolicy`
  // permission on the provided key project. A
  // [KeyHandle][google.cloud.kms.v1.KeyHandle] creation in the folder's
  // descendant projects will use this configuration to determine where to
  // create the resulting [CryptoKey][google.cloud.kms.v1.CryptoKey].
  rpc UpdateAutokeyConfig(UpdateAutokeyConfigRequest) returns (AutokeyConfig) {
    option (google.api.http) = {
      patch: "/v1/{autokey_config.name=folders/*/autokeyConfig}"
      body: "autokey_config"
    };
    option (google.api.method_signature) = "autokey_config,update_mask";
  }

  // Returns the [AutokeyConfig][google.cloud.kms.v1.AutokeyConfig] for a
  // folder.
  rpc GetAutokeyConfig(GetAutokeyConfigRequest) returns (AutokeyConfig) {
    option (google.api.http) = {
      get: "/v1/{name=folders/*/autokeyConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the effective Cloud KMS Autokey configuration for a given project.
  rpc ShowEffectiveAutokeyConfig(ShowEffectiveAutokeyConfigRequest)
      returns (ShowEffectiveAutokeyConfigResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}:showEffectiveAutokeyConfig"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [UpdateAutokeyConfig][google.cloud.kms.v1.AutokeyAdmin.UpdateAutokeyConfig].
message UpdateAutokeyConfigRequest {
  // Required. [AutokeyConfig][google.cloud.kms.v1.AutokeyConfig] with values to
  // update.
  AutokeyConfig autokey_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Masks which fields of the
  // [AutokeyConfig][google.cloud.kms.v1.AutokeyConfig] to update, e.g.
  // `keyProject`.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [GetAutokeyConfig][google.cloud.kms.v1.AutokeyAdmin.GetAutokeyConfig].
message GetAutokeyConfigRequest {
  // Required. Name of the [AutokeyConfig][google.cloud.kms.v1.AutokeyConfig]
  // resource, e.g. `folders/{FOLDER_NUMBER}/autokeyConfig`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/AutokeyConfig"
    }
  ];
}

// Cloud KMS Autokey configuration for a folder.
message AutokeyConfig {
  option (google.api.resource) = {
    type: "cloudkms.googleapis.com/AutokeyConfig"
    pattern: "folders/{folder}/autokeyConfig"
    plural: "autokeyConfigs"
    singular: "autokeyConfig"
  };

  // The states AutokeyConfig can be in.
  enum State {
    // The state of the AutokeyConfig is unspecified.
    STATE_UNSPECIFIED = 0;

    // The AutokeyConfig is currently active.
    ACTIVE = 1;

    // A previously configured key project has been deleted and the current
    // AutokeyConfig is unusable.
    KEY_PROJECT_DELETED = 2;

    // The AutokeyConfig is not yet initialized or has been reset to its default
    // uninitialized state.
    UNINITIALIZED = 3;
  }

  // Identifier. Name of the [AutokeyConfig][google.cloud.kms.v1.AutokeyConfig]
  // resource, e.g. `folders/{FOLDER_NUMBER}/autokeyConfig`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Name of the key project, e.g. `projects/{PROJECT_ID}` or
  // `projects/{PROJECT_NUMBER}`, where Cloud KMS Autokey will provision a new
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] when a
  // [KeyHandle][google.cloud.kms.v1.KeyHandle] is created. On
  // [UpdateAutokeyConfig][google.cloud.kms.v1.AutokeyAdmin.UpdateAutokeyConfig],
  // the caller will require `cloudkms.cryptoKeys.setIamPolicy` permission on
  // this key project. Once configured, for Cloud KMS Autokey to function
  // properly, this key project must have the Cloud KMS API activated and the
  // Cloud KMS Service Agent for this key project must be granted the
  // `cloudkms.admin` role (or pertinent permissions). A request with an empty
  // key project field will clear the configuration.
  string key_project = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The state for the AutokeyConfig.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [ShowEffectiveAutokeyConfig][google.cloud.kms.v1.AutokeyAdmin.ShowEffectiveAutokeyConfig].
message ShowEffectiveAutokeyConfigRequest {
  // Required. Name of the resource project to the show effective Cloud KMS
  // Autokey configuration for. This may be helpful for interrogating the effect
  // of nested folder configurations on a given resource project.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];
}

// Response message for
// [ShowEffectiveAutokeyConfig][google.cloud.kms.v1.AutokeyAdmin.ShowEffectiveAutokeyConfig].
message ShowEffectiveAutokeyConfigResponse {
  // Name of the key project configured in the resource project's folder
  // ancestry.
  string key_project = 1;
}
