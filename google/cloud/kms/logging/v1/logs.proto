// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.kms.logging.v1;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/kms/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LogsProto";
option java_package = "com.google.cloud.kms.logging.v1";

// This file defines the format of Cloud KMS Platform Logging Logs.

// Log message used to send to Platform Logging for asynchronous
// CryptoKey events.
message CryptoKeyEvent {
  // The event emitted by K<PERSON> when performing a scheduled automatic CryptoKey
  // rotation. See https://cloud.google.com/kms/docs/rotating-keys#automatic
  message RotationEvent {
    // The result of the scheduled key rotation. The 'details' field of the
    // status may contain a google.rpc.PreconditionFailure.
    google.rpc.Status status = 1;
  }

  // An event for rotating the primary CryptoKeyVersion of a CryptoKey.
  RotationEvent rotation_event = 1;
}

// Log message used to send to Platform Logging for asynchronous
// CryptoKeyVersion events.
message CryptoKeyVersionEvent {
  // The event emitted by KMS when destroying a CryptoKeyVersion scheduled for
  // destruction. See https://cloud.google.com/kms/docs/destroy-restore#destroy
  message ScheduledDestructionEvent {
    // The result of the scheduled key version destruction. The 'details' field
    // of the status may contain a google.rpc.PreconditionFailure. For EKM keys,
    // the 'violations' field of a PreconditionFailure will also include EKM
    // errors.
    google.rpc.Status status = 1;

    // The Key Access Justification (KAJ) reason associated with the request.
    // This field is only populated for KAJ enrolled customers for EKM keys.
    string key_access_justification_reason = 2;
  }

  // The event emitted by KMS when generating a CryptoKeyVersion. See
  // https://cloud.google.com/kms/docs/key-states
  message KeyGenerationEvent {
    // The result of the key version generation. The 'details' field of the
    // status may contain a google.rpc.PreconditionFailure. For EKM keys, the
    // 'violations' field of a PreconditionFailure will also include EKM errors.
    google.rpc.Status status = 1;

    // The Key Access Justification (KAJ) reason associated with the request.
    // This field is only populated for KAJ enrolled customers for EKM keys.
    string key_access_justification_reason = 2;
  }

  // The event emitted by KMS when importing a CryptoKeyVersion. See
  // https://cloud.google.com/kms/docs/importing-a-key
  message ImportEvent {
    // The result of the key version import. The 'details' field of the status
    // may contain a google.rpc.PreconditionFailure.
    google.rpc.Status status = 1;
  }

  oneof event {
    // An event for the scheduled destruction of a CryptoKeyVersion.
    ScheduledDestructionEvent scheduled_destruction_event = 1;

    // An event for the generation of a CryptoKeyVersion.
    KeyGenerationEvent key_generation_event = 2;

    // An event for the import of key material for a CryptoKeyVersion.
    ImportEvent import_event = 3;
  }
}
