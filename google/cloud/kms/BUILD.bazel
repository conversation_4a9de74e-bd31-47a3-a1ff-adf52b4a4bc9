# This build file includes a target for the Ruby wrapper library for
# google-cloud-kms.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudkms.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "cloudkms_ruby_wrapper",
    srcs = ["//google/cloud/kms/v1:kms_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-kms",
        "ruby-cloud-env-prefix=KMS",
        "ruby-cloud-wrapper-of=v1:0.26",
        "ruby-cloud-product-url=https://cloud.google.com/kms",
        "ruby-cloud-api-id=cloudkms.googleapis.com",
        "ruby-cloud-api-shortname=cloudkms",
        "ruby-cloud-migration-version=2.0",
    ],
    ruby_cloud_description = "Manages keys and performs cryptographic operations in a central cloud service, for direct use by other cloud resources and applications.",
    ruby_cloud_title = "Cloud Key Management Service (KMS)",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-kms-ruby",
    deps = [
        ":cloudkms_ruby_wrapper",
    ],
)
