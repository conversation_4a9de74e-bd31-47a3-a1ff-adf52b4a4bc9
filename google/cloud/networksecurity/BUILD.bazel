# This build file includes a target for the Ruby wrapper library for
# google-cloud-network_security.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for networksecurity.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "networksecurity_ruby_wrapper",
    srcs = ["//google/cloud/networksecurity/v1beta1:networksecurity_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-network_security",
        "ruby-cloud-wrapper-of=v1beta1:0.7",
        "ruby-cloud-product-url=https://cloud.google.com/traffic-director/docs/reference/network-security/rest/",
        "ruby-cloud-api-id=networksecurity.googleapis.com",
        "ruby-cloud-api-shortname=networksecurity",
    ],
    ruby_cloud_description = "The client library for the Google Network Security V1beta1 API.",
    ruby_cloud_title = "Network Security",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-networksecurity-ruby",
    deps = [
        ":networksecurity_ruby_wrapper",
    ],
)
