// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networksecurity.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.NetworkSecurity.V1";
option go_package = "cloud.google.com/go/networksecurity/apiv1/networksecuritypb;networksecuritypb";
option java_multiple_files = true;
option java_outer_classname = "TlsProto";
option java_package = "com.google.cloud.networksecurity.v1";
option php_namespace = "Google\\Cloud\\NetworkSecurity\\V1";
option ruby_package = "Google::Cloud::NetworkSecurity::V1";

// Specification of the GRPC Endpoint.
message GrpcEndpoint {
  // Required. The target URI of the gRPC endpoint. Only UDS path is supported, and
  // should start with "unix:".
  string target_uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// Specification of ValidationCA. Defines the mechanism to obtain the
// Certificate Authority certificate to validate the peer certificate.
message ValidationCA {
  // The type of certificate provider which provides the CA certificate.
  oneof type {
    // gRPC specific configuration to access the gRPC server to
    // obtain the CA certificate.
    GrpcEndpoint grpc_endpoint = 2;

    // The certificate provider instance specification that will be passed to
    // the data plane, which will be used to load necessary credential
    // information.
    CertificateProviderInstance certificate_provider_instance = 3;
  }
}

// Specification of a TLS certificate provider instance. Workloads may have one
// or more CertificateProvider instances (plugins) and one of them is enabled
// and configured by specifying this message. Workloads use the values from this
// message to locate and load the CertificateProvider instance configuration.
message CertificateProviderInstance {
  // Required. Plugin instance name, used to locate and load CertificateProvider instance
  // configuration. Set to "google_cloud_private_spiffe" to use Certificate
  // Authority Service certificate provider instance.
  string plugin_instance = 1 [(google.api.field_behavior) = REQUIRED];
}

// Specification of certificate provider. Defines the mechanism to obtain the
// certificate and private key for peer to peer authentication.
message CertificateProvider {
  // The type of certificate provider which provides the certificates and
  // private keys.
  oneof type {
    // gRPC specific configuration to access the gRPC server to
    // obtain the cert and private key.
    GrpcEndpoint grpc_endpoint = 2;

    // The certificate provider instance specification that will be passed to
    // the data plane, which will be used to load necessary credential
    // information.
    CertificateProviderInstance certificate_provider_instance = 3;
  }
}
