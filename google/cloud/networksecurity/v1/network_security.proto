// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networksecurity.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/networksecurity/v1/authorization_policy.proto";
import "google/cloud/networksecurity/v1/client_tls_policy.proto";
import "google/cloud/networksecurity/v1/server_tls_policy.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.NetworkSecurity.V1";
option go_package = "cloud.google.com/go/networksecurity/apiv1/networksecuritypb;networksecuritypb";
option java_multiple_files = true;
option java_package = "com.google.cloud.networksecurity.v1";
option php_namespace = "Google\\Cloud\\NetworkSecurity\\V1";
option ruby_package = "Google::Cloud::NetworkSecurity::V1";

// Network Security API provides resources to configure authentication and
// authorization policies. Refer to per API resource documentation for more
// information.
service NetworkSecurity {
  option (google.api.default_host) = "networksecurity.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists AuthorizationPolicies in a given project and location.
  rpc ListAuthorizationPolicies(ListAuthorizationPoliciesRequest) returns (ListAuthorizationPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/authorizationPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single AuthorizationPolicy.
  rpc GetAuthorizationPolicy(GetAuthorizationPolicyRequest) returns (AuthorizationPolicy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/authorizationPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new AuthorizationPolicy in a given project and location.
  rpc CreateAuthorizationPolicy(CreateAuthorizationPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/authorizationPolicies"
      body: "authorization_policy"
    };
    option (google.api.method_signature) = "parent,authorization_policy,authorization_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "AuthorizationPolicy"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single AuthorizationPolicy.
  rpc UpdateAuthorizationPolicy(UpdateAuthorizationPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{authorization_policy.name=projects/*/locations/*/authorizationPolicies/*}"
      body: "authorization_policy"
    };
    option (google.api.method_signature) = "authorization_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AuthorizationPolicy"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }

  // Deletes a single AuthorizationPolicy.
  rpc DeleteAuthorizationPolicy(DeleteAuthorizationPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/authorizationPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }

  // Lists ServerTlsPolicies in a given project and location.
  rpc ListServerTlsPolicies(ListServerTlsPoliciesRequest) returns (ListServerTlsPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/serverTlsPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ServerTlsPolicy.
  rpc GetServerTlsPolicy(GetServerTlsPolicyRequest) returns (ServerTlsPolicy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/serverTlsPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ServerTlsPolicy in a given project and location.
  rpc CreateServerTlsPolicy(CreateServerTlsPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/serverTlsPolicies"
      body: "server_tls_policy"
    };
    option (google.api.method_signature) = "parent,server_tls_policy,server_tls_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "ServerTlsPolicy"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single ServerTlsPolicy.
  rpc UpdateServerTlsPolicy(UpdateServerTlsPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{server_tls_policy.name=projects/*/locations/*/serverTlsPolicies/*}"
      body: "server_tls_policy"
    };
    option (google.api.method_signature) = "server_tls_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ServerTlsPolicy"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }

  // Deletes a single ServerTlsPolicy.
  rpc DeleteServerTlsPolicy(DeleteServerTlsPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/serverTlsPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }

  // Lists ClientTlsPolicies in a given project and location.
  rpc ListClientTlsPolicies(ListClientTlsPoliciesRequest) returns (ListClientTlsPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/clientTlsPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ClientTlsPolicy.
  rpc GetClientTlsPolicy(GetClientTlsPolicyRequest) returns (ClientTlsPolicy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clientTlsPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ClientTlsPolicy in a given project and location.
  rpc CreateClientTlsPolicy(CreateClientTlsPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/clientTlsPolicies"
      body: "client_tls_policy"
    };
    option (google.api.method_signature) = "parent,client_tls_policy,client_tls_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "ClientTlsPolicy"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single ClientTlsPolicy.
  rpc UpdateClientTlsPolicy(UpdateClientTlsPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{client_tls_policy.name=projects/*/locations/*/clientTlsPolicies/*}"
      body: "client_tls_policy"
    };
    option (google.api.method_signature) = "client_tls_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ClientTlsPolicy"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }

  // Deletes a single ClientTlsPolicy.
  rpc DeleteClientTlsPolicy(DeleteClientTlsPolicyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clientTlsPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networksecurity.v1.OperationMetadata"
    };
  }
}
