// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networksecurity.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/networksecurity/v1/tls.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkSecurity.V1";
option go_package = "cloud.google.com/go/networksecurity/apiv1/networksecuritypb;networksecuritypb";
option java_multiple_files = true;
option java_outer_classname = "ClientTlsPolicyProto";
option java_package = "com.google.cloud.networksecurity.v1";
option php_namespace = "Google\\Cloud\\NetworkSecurity\\V1";
option ruby_package = "Google::Cloud::NetworkSecurity::V1";

// ClientTlsPolicy is a resource that specifies how a client should authenticate
// connections to backends of a service. This resource itself does not affect
// configuration unless it is attached to a backend service resource.
message ClientTlsPolicy {
  option (google.api.resource) = {
    type: "networksecurity.googleapis.com/ClientTlsPolicy"
    pattern: "projects/{project}/locations/{location}/clientTlsPolicies/{client_tls_policy}"
  };

  // Required. Name of the ClientTlsPolicy resource. It matches the pattern
  // `projects/*/locations/{location}/clientTlsPolicies/{client_tls_policy}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Free-text description of the resource.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Set of label tags associated with the resource.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Server Name Indication string to present to the server during TLS
  // handshake. E.g: "secure.example.com".
  string sni = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines a mechanism to provision client identity (public and private keys)
  // for peer to peer authentication. The presence of this dictates mTLS.
  CertificateProvider client_certificate = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines the mechanism to obtain the Certificate Authority certificate to
  // validate the server certificate. If empty, client does not validate the
  // server certificate.
  repeated ValidationCA server_validation_ca = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Request used by the ListClientTlsPolicies method.
message ListClientTlsPoliciesRequest {
  // Required. The project and location from which the ClientTlsPolicies should
  // be listed, specified in the format `projects/*/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of ClientTlsPolicies to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListClientTlsPoliciesResponse`
  // Indicates that this is a continuation of a prior
  // `ListClientTlsPolicies` call, and that the system
  // should return the next page of data.
  string page_token = 3;
}

// Response returned by the ListClientTlsPolicies method.
message ListClientTlsPoliciesResponse {
  // List of ClientTlsPolicy resources.
  repeated ClientTlsPolicy client_tls_policies = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used by the GetClientTlsPolicy method.
message GetClientTlsPolicyRequest {
  // Required. A name of the ClientTlsPolicy to get. Must be in the format
  // `projects/*/locations/{location}/clientTlsPolicies/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/ClientTlsPolicy"
    }
  ];
}

// Request used by the CreateClientTlsPolicy method.
message CreateClientTlsPolicyRequest {
  // Required. The parent resource of the ClientTlsPolicy. Must be in
  // the format `projects/*/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networksecurity.googleapis.com/ClientTlsPolicy"
    }
  ];

  // Required. Short name of the ClientTlsPolicy resource to be created. This value should
  // be 1-63 characters long, containing only letters, numbers, hyphens, and
  // underscores, and should not start with a number. E.g. "client_mtls_policy".
  string client_tls_policy_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. ClientTlsPolicy resource to be created.
  ClientTlsPolicy client_tls_policy = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request used by UpdateClientTlsPolicy method.
message UpdateClientTlsPolicyRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // ClientTlsPolicy resource by the update.  The fields
  // specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the
  // mask. If the user does not provide a mask then all fields will be
  // overwritten.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Updated ClientTlsPolicy resource.
  ClientTlsPolicy client_tls_policy = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the DeleteClientTlsPolicy method.
message DeleteClientTlsPolicyRequest {
  // Required. A name of the ClientTlsPolicy to delete. Must be in
  // the format `projects/*/locations/{location}/clientTlsPolicies/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/ClientTlsPolicy"
    }
  ];
}
