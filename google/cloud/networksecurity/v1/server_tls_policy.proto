// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networksecurity.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/networksecurity/v1/tls.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkSecurity.V1";
option go_package = "cloud.google.com/go/networksecurity/apiv1/networksecuritypb;networksecuritypb";
option java_multiple_files = true;
option java_outer_classname = "ServerTlsPolicyProto";
option java_package = "com.google.cloud.networksecurity.v1";
option php_namespace = "Google\\Cloud\\NetworkSecurity\\V1";
option ruby_package = "Google::Cloud::NetworkSecurity::V1";

// ServerTlsPolicy is a resource that specifies how a server should authenticate
// incoming requests. This resource itself does not affect configuration unless
// it is attached to a target https proxy or endpoint config selector resource.
message ServerTlsPolicy {
  option (google.api.resource) = {
    type: "networksecurity.googleapis.com/ServerTlsPolicy"
    pattern: "projects/{project}/locations/{location}/serverTlsPolicies/{server_tls_policy}"
  };

  // Specification of the MTLSPolicy.
  message MTLSPolicy {
    //
    // Defines the mechanism to obtain the Certificate Authority certificate to
    // validate the client certificate.
    repeated ValidationCA client_validation_ca = 1;
  }

  // Required. Name of the ServerTlsPolicy resource. It matches the pattern
  // `projects/*/locations/{location}/serverTlsPolicies/{server_tls_policy}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Free-text description of the resource.
  string description = 2;

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Set of label tags associated with the resource.
  map<string, string> labels = 5;

  //
  // Determines if server allows plaintext connections. If set to true, server
  // allows plain text connections. By default, it is set to false. This setting
  // is not exclusive of other encryption modes. For example, if `allow_open`
  // and `mtls_policy` are set, server allows both plain text and mTLS
  // connections. See documentation of other encryption modes to confirm
  // compatibility.
  //
  // Consider using it if you wish to upgrade in place your deployment to TLS
  // while having mixed TLS and non-TLS traffic reaching port :80.
  bool allow_open = 6;

  //
  // Defines a mechanism to provision server identity (public and private keys).
  // Cannot be combined with `allow_open` as a permissive mode that allows both
  // plain text and TLS is not supported.
  CertificateProvider server_certificate = 7;

  //
  // Defines a mechanism to provision peer validation certificates for peer to
  // peer authentication (Mutual TLS - mTLS). If not specified, client
  // certificate will not be requested. The connection is treated as TLS and not
  // mTLS. If `allow_open` and `mtls_policy` are set, server allows both plain
  // text and mTLS connections.
  MTLSPolicy mtls_policy = 8;
}

// Request used by the ListServerTlsPolicies method.
message ListServerTlsPoliciesRequest {
  // Required. The project and location from which the ServerTlsPolicies should
  // be listed, specified in the format `projects/*/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of ServerTlsPolicies to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListServerTlsPoliciesResponse`
  // Indicates that this is a continuation of a prior
  // `ListServerTlsPolicies` call, and that the system
  // should return the next page of data.
  string page_token = 3;
}

// Response returned by the ListServerTlsPolicies method.
message ListServerTlsPoliciesResponse {
  // List of ServerTlsPolicy resources.
  repeated ServerTlsPolicy server_tls_policies = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used by the GetServerTlsPolicy method.
message GetServerTlsPolicyRequest {
  // Required. A name of the ServerTlsPolicy to get. Must be in the format
  // `projects/*/locations/{location}/serverTlsPolicies/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/ServerTlsPolicy"
    }
  ];
}

// Request used by the CreateServerTlsPolicy method.
message CreateServerTlsPolicyRequest {
  // Required. The parent resource of the ServerTlsPolicy. Must be in
  // the format `projects/*/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networksecurity.googleapis.com/ServerTlsPolicy"
    }
  ];

  // Required. Short name of the ServerTlsPolicy resource to be created. This value should
  // be 1-63 characters long, containing only letters, numbers, hyphens, and
  // underscores, and should not start with a number. E.g. "server_mtls_policy".
  string server_tls_policy_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. ServerTlsPolicy resource to be created.
  ServerTlsPolicy server_tls_policy = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request used by UpdateServerTlsPolicy method.
message UpdateServerTlsPolicyRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // ServerTlsPolicy resource by the update.  The fields
  // specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the
  // mask. If the user does not provide a mask then all fields will be
  // overwritten.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Updated ServerTlsPolicy resource.
  ServerTlsPolicy server_tls_policy = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the DeleteServerTlsPolicy method.
message DeleteServerTlsPolicyRequest {
  // Required. A name of the ServerTlsPolicy to delete. Must be in
  // the format `projects/*/locations/{location}/serverTlsPolicies/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/ServerTlsPolicy"
    }
  ];
}
