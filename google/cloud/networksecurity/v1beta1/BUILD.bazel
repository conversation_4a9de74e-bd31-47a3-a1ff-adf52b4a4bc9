# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "networksecurity_proto",
    srcs = [
        "authorization_policy.proto",
        "client_tls_policy.proto",
        "common.proto",
        "network_security.proto",
        "server_tls_policy.proto",
        "tls.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "networksecurity_proto_with_info",
    deps = [
        ":networksecurity_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "networksecurity_java_proto",
    deps = [":networksecurity_proto"],
)

java_grpc_library(
    name = "networksecurity_java_grpc",
    srcs = [":networksecurity_proto"],
    deps = [":networksecurity_java_proto"],
)

java_gapic_library(
    name = "networksecurity_java_gapic",
    srcs = [":networksecurity_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "networksecurity_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networksecurity_v1beta1.yaml",
    test_deps = [
        ":networksecurity_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":networksecurity_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "networksecurity_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.networksecurity.v1beta1.NetworkSecurityClientHttpJsonTest",
        "com.google.cloud.networksecurity.v1beta1.NetworkSecurityClientTest",
    ],
    runtime_deps = [":networksecurity_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-networksecurity-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":networksecurity_java_gapic",
        ":networksecurity_java_grpc",
        ":networksecurity_java_proto",
        ":networksecurity_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "networksecurity_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/networksecurity/apiv1beta1/networksecuritypb",
    protos = [":networksecurity_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "networksecurity_go_gapic",
    srcs = [":networksecurity_proto_with_info"],
    grpc_service_config = "networksecurity_v1beta1_grpc_service_config.json",
    importpath = "cloud.google.com/go/networksecurity/apiv1beta1;networksecurity",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "networksecurity_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networksecurity_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-networksecurity-v1beta1-go",
    deps = [
        ":networksecurity_go_gapic",
        ":networksecurity_go_gapic_srcjar-metadata.srcjar",
        ":networksecurity_go_gapic_srcjar-snippets.srcjar",
        ":networksecurity_go_gapic_srcjar-test.srcjar",
        ":networksecurity_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "networksecurity_py_gapic",
    srcs = [":networksecurity_proto"],
    grpc_service_config = "networksecurity_v1beta1_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-network-security",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=network_security",
    ],
    rest_numeric_enums = True,
    service_yaml = "networksecurity_v1beta1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "networksecurity_py_gapic_test",
    srcs = [
        "networksecurity_py_gapic_pytest.py",
        "networksecurity_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":networksecurity_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "networksecurity-v1beta1-py",
    deps = [
        ":networksecurity_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "networksecurity_php_proto",
    deps = [":networksecurity_proto"],
)

php_gapic_library(
    name = "networksecurity_php_gapic",
    srcs = [":networksecurity_proto_with_info"],
    grpc_service_config = "networksecurity_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networksecurity_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":networksecurity_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-networksecurity-v1beta1-php",
    deps = [
        ":networksecurity_php_gapic",
        ":networksecurity_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "networksecurity_nodejs_gapic",
    package_name = "@google-cloud/network-security",
    src = ":networksecurity_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "networksecurity_v1beta1_grpc_service_config.json",
    package = "google.cloud.networksecurity.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "networksecurity_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "networksecurity-v1beta1-nodejs",
    deps = [
        ":networksecurity_nodejs_gapic",
        ":networksecurity_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "networksecurity_ruby_proto",
    deps = [":networksecurity_proto"],
)

ruby_grpc_library(
    name = "networksecurity_ruby_grpc",
    srcs = [":networksecurity_proto"],
    deps = [":networksecurity_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "networksecurity_ruby_gapic",
    srcs = [":networksecurity_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=networksecurity.googleapis.com",
        "ruby-cloud-api-shortname=networksecurity",
        "ruby-cloud-gem-name=google-cloud-network_security-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/traffic-director/docs/reference/network-security/rest/",
    ],
    grpc_service_config = "networksecurity_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The client library for the Google Network Security V1beta1 API.",
    ruby_cloud_title = "Network Security V1beta1",
    service_yaml = "networksecurity_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networksecurity_ruby_grpc",
        ":networksecurity_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-networksecurity-v1beta1-ruby",
    deps = [
        ":networksecurity_ruby_gapic",
        ":networksecurity_ruby_grpc",
        ":networksecurity_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "networksecurity_csharp_proto",
    deps = [":networksecurity_proto"],
)

csharp_grpc_library(
    name = "networksecurity_csharp_grpc",
    srcs = [":networksecurity_proto"],
    deps = [":networksecurity_csharp_proto"],
)

csharp_gapic_library(
    name = "networksecurity_csharp_gapic",
    srcs = [":networksecurity_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "networksecurity_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networksecurity_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networksecurity_csharp_grpc",
        ":networksecurity_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-networksecurity-v1beta1-csharp",
    deps = [
        ":networksecurity_csharp_gapic",
        ":networksecurity_csharp_grpc",
        ":networksecurity_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "networksecurity_cc_proto",
    deps = [":networksecurity_proto"],
)

cc_grpc_library(
    name = "networksecurity_cc_grpc",
    srcs = [":networksecurity_proto"],
    grpc_only = True,
    deps = [":networksecurity_cc_proto"],
)
