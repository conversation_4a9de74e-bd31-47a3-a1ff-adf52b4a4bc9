// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networksecurity.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkSecurity.V1Beta1";
option go_package = "cloud.google.com/go/networksecurity/apiv1beta1/networksecuritypb;networksecuritypb";
option java_multiple_files = true;
option java_outer_classname = "AuthorizationPolicyProto";
option java_package = "com.google.cloud.networksecurity.v1beta1";
option php_namespace = "Google\\Cloud\\NetworkSecurity\\V1beta1";
option ruby_package = "Google::Cloud::NetworkSecurity::V1beta1";

// AuthorizationPolicy is a resource that specifies how a server
// should authorize incoming connections. This resource in itself does
// not change the configuration unless it's attached to a target https
// proxy or endpoint config selector resource.
message AuthorizationPolicy {
  option (google.api.resource) = {
    type: "networksecurity.googleapis.com/AuthorizationPolicy"
    pattern: "projects/{project}/locations/{location}/authorizationPolicies/{authorization_policy}"
  };

  // Specification of rules.
  message Rule {
    // Specification of traffic source attributes.
    message Source {
      // Optional. List of peer identities to match for authorization. At least
      // one principal should match. Each peer can be an exact match, or a
      // prefix match (example, "namespace/*") or a suffix match (example,
      // "*/service-account") or a presence match "*". Authorization based on
      // the principal name without certificate validation (configured by
      // ServerTlsPolicy resource) is considered insecure.
      repeated string principals = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. List of CIDR ranges to match based on source IP address. At
      // least one IP block should match. Single IP (e.g., "*******") and CIDR
      // (e.g., "*******/24") are supported. Authorization based on source IP
      // alone should be avoided. The IP addresses of any load balancers or
      // proxies should be considered untrusted.
      repeated string ip_blocks = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // Specification of traffic destination attributes.
    message Destination {
      // Specification of HTTP header match attributes.
      message HttpHeaderMatch {
        oneof type {
          // Required. The value of the header must match the regular expression
          // specified in regexMatch. For regular expression grammar,
          // please see: en.cppreference.com/w/cpp/regex/ecmascript
          // For matching against a port specified in the HTTP
          // request, use a headerMatch with headerName set to Host
          // and a regular expression that satisfies the RFC2616 Host
          // header's port specifier.
          string regex_match = 2 [(google.api.field_behavior) = REQUIRED];
        }

        // Required. The name of the HTTP header to match. For matching
        // against the HTTP request's authority, use a headerMatch
        // with the header name ":authority". For matching a
        // request's method, use the headerName ":method".
        string header_name = 1 [(google.api.field_behavior) = REQUIRED];
      }

      // Required. List of host names to match. Matched against the ":authority"
      // header in http requests. At least one host should match. Each host can
      // be an exact match, or a prefix match (example "mydomain.*") or a suffix
      // match (example "*.myorg.com") or a presence (any) match "*".
      repeated string hosts = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. List of destination ports to match. At least one port should
      // match.
      repeated uint32 ports = 2 [(google.api.field_behavior) = REQUIRED];

      // Optional. A list of HTTP methods to match. At least one method should
      // match. Should not be set for gRPC services.
      repeated string methods = 4 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Match against key:value pair in http header. Provides a
      // flexible match based on HTTP headers, for potentially advanced use
      // cases. At least one header should match. Avoid using header matches to
      // make authorization decisions unless there is a strong guarantee that
      // requests arrive through a trusted client or proxy.
      HttpHeaderMatch http_header_match = 5
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. List of attributes for the traffic source. All of the sources
    // must match. A source is a match if both principals and ip_blocks match.
    // If not set, the action specified in the 'action' field will be applied
    // without any rule checks for the source.
    repeated Source sources = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. List of attributes for the traffic destination. All of the
    // destinations must match. A destination is a match if a request matches
    // all the specified hosts, ports, methods and headers. If not set, the
    // action specified in the 'action' field will be applied without any rule
    // checks for the destination.
    repeated Destination destinations = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Possible values that define what action to take.
  enum Action {
    // Default value.
    ACTION_UNSPECIFIED = 0;

    // Grant access.
    ALLOW = 1;

    // Deny access.
    // Deny rules should be avoided unless they are used to provide a default
    // "deny all" fallback.
    DENY = 2;
  }

  // Required. Name of the AuthorizationPolicy resource. It matches pattern
  // `projects/{project}/locations/{location}/authorizationPolicies/<authorization_policy>`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Free-text description of the resource.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Set of label tags associated with the AuthorizationPolicy
  // resource.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. The action to take when a rule match is found. Possible values
  // are "ALLOW" or "DENY".
  Action action = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. List of rules to match. Note that at least one of the rules must
  // match in order for the action specified in the 'action' field to be taken.
  // A rule is a match if there is a matching source and destination. If left
  // blank, the action specified in the `action` field will be applied on every
  // request.
  repeated Rule rules = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Request used with the ListAuthorizationPolicies method.
message ListAuthorizationPoliciesRequest {
  // Required. The project and location from which the AuthorizationPolicies
  // should be listed, specified in the format
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of AuthorizationPolicies to return per call.
  int32 page_size = 2;

  // The value returned by the last
  // `ListAuthorizationPoliciesResponse` Indicates that this is a
  // continuation of a prior `ListAuthorizationPolicies` call, and
  // that the system should return the next page of data.
  string page_token = 3;
}

// Response returned by the ListAuthorizationPolicies method.
message ListAuthorizationPoliciesResponse {
  // List of AuthorizationPolicies resources.
  repeated AuthorizationPolicy authorization_policies = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used by the GetAuthorizationPolicy method.
message GetAuthorizationPolicyRequest {
  // Required. A name of the AuthorizationPolicy to get. Must be in the format
  // `projects/{project}/locations/{location}/authorizationPolicies/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/AuthorizationPolicy"
    }
  ];
}

// Request used by the CreateAuthorizationPolicy method.
message CreateAuthorizationPolicyRequest {
  // Required. The parent resource of the AuthorizationPolicy. Must be in the
  // format `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networksecurity.googleapis.com/AuthorizationPolicy"
    }
  ];

  // Required. Short name of the AuthorizationPolicy resource to be created.
  // This value should be 1-63 characters long, containing only
  // letters, numbers, hyphens, and underscores, and should not start
  // with a number. E.g. "authz_policy".
  string authorization_policy_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. AuthorizationPolicy resource to be created.
  AuthorizationPolicy authorization_policy = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request used by the UpdateAuthorizationPolicy method.
message UpdateAuthorizationPolicyRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // AuthorizationPolicy resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Updated AuthorizationPolicy resource.
  AuthorizationPolicy authorization_policy = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request used by the DeleteAuthorizationPolicy method.
message DeleteAuthorizationPolicyRequest {
  // Required. A name of the AuthorizationPolicy to delete. Must be in the
  // format `projects/{project}/locations/{location}/authorizationPolicies/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/AuthorizationPolicy"
    }
  ];
}
