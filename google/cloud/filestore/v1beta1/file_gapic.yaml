type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
# A list of API interface configurations.
interfaces:
# The fully qualified name of the API interface.
- name: google.cloud.filestore.v1beta1.CloudFilestoreManager
  methods:
  # Both CreateInstance and DeleteSnapshot can take a long time to complete.
  # The default client-side timeouts cause the client to give up on the request early,
  # so the following configuration increases it:
  # See go/client-self-service#configure-long-running-operation-polling-timeouts-optional
  # See also retry timeouts in: google/cloud/filer/v1beta1/file_grpc_service_config.json
  - name: CreateInstance
    long_running:
      initial_poll_delay_millis: 30000  # 30 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: DeleteSnapshot
    long_running:
      initial_poll_delay_millis: 30000  # 30 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: DeleteInstance
    long_running:
      initial_poll_delay_millis: 30000  # 30 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 1200000  # 20 minutes
  - name: UpdateInstance
    long_running:
      initial_poll_delay_millis: 30000  # 30 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 900000  # 15 minutes
  - name: RestoreInstance
    long_running:
      initial_poll_delay_millis: 30000  # 30 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: RevertInstance
    long_running:
      initial_poll_delay_millis: 30000  # 30 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: CreateBackup
    long_running:
      initial_poll_delay_millis: 30000  # 30 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: DeleteBackup
    long_running:
      initial_poll_delay_millis: 30000  # 30 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: UpdateBackup
    long_running:
      initial_poll_delay_millis: 10000  # 10 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 660000  # 11 minutes
  - name: CreateSnapshot
    long_running:
      initial_poll_delay_millis: 10000  # 10 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 660000  # 11 minutes
  - name: UpdateSnapshot
    long_running:
      initial_poll_delay_millis: 10000  # 10 seconds
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute
      total_poll_timeout_millis: 660000  # 11 minutes
