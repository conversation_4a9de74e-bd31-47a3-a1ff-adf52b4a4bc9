{"methodConfig": [{"name": [{"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "ListInstances"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "GetInstance"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "ListBackups"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "GetBackup"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "ListSnapshots"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "GetSnapshots"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.250s", "maxBackoff": "32s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "CreateInstance"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "RestoreInstance"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "CreateBackup"}], "timeout": "60000s"}, {"name": [{"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "UpdateInstance"}], "timeout": "14400s"}, {"name": [{"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "DeleteInstance"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "DeleteBackup"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "UpdateBackup"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "CreateSnapshot"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "DeleteSnapshot"}, {"service": "google.cloud.filestore.v1beta1.CloudFilestoreManager", "method": "UpdateSnapshot"}], "timeout": "600s"}]}