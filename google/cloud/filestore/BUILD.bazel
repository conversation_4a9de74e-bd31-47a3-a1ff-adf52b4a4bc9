# This build file includes a target for the Ruby wrapper library for
# google-cloud-filestore.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for file.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "file_ruby_wrapper",
    srcs = ["//google/cloud/filestore/v1:filestore_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-filestore",
        "ruby-cloud-wrapper-of=v1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/filestore/",
        "ruby-cloud-api-id=file.googleapis.com",
        "ruby-cloud-api-shortname=file",
    ],
    ruby_cloud_description = "Filestore instances are fully managed NFS file servers on Google Cloud for use with applications running on Compute Engine virtual machines (VMs) instances or Google Kubernetes Engine clusters.",
    ruby_cloud_title = "Filestore",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-filestore-ruby",
    deps = [
        ":file_ruby_wrapper",
    ],
)
