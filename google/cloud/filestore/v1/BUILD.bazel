# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "filestore_proto",
    srcs = [
        "cloud_filestore_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/cloud/common:common_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "filestore_proto_with_info",
    deps = [
        ":filestore_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "filestore_java_proto",
    deps = [":filestore_proto"],
)

java_grpc_library(
    name = "filestore_java_grpc",
    srcs = [":filestore_proto"],
    deps = [":filestore_java_proto"],
)

java_gapic_library(
    name = "filestore_java_gapic",
    srcs = [":filestore_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "file_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "file_v1.yaml",
    test_deps = [
        ":filestore_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":filestore_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/common:common_java_proto",
    ],
)

java_gapic_test(
    name = "filestore_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.filestore.v1.CloudFilestoreManagerClientHttpJsonTest",
        "com.google.cloud.filestore.v1.CloudFilestoreManagerClientTest",
    ],
    runtime_deps = [":filestore_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-filestore-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":filestore_java_gapic",
        ":filestore_java_grpc",
        ":filestore_java_proto",
        ":filestore_proto",
        "//google/cloud/common:common_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "filestore_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/filestore/apiv1/filestorepb",
    protos = [":filestore_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/cloud/common:common_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "filestore_go_gapic",
    srcs = [":filestore_proto_with_info"],
    grpc_service_config = "file_grpc_service_config.json",
    importpath = "cloud.google.com/go/filestore/apiv1;filestore",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "file_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":filestore_go_proto",
        "//google/cloud/common:common_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-filestore-v1-go",
    deps = [
        ":filestore_go_gapic",
        ":filestore_go_gapic_srcjar-metadata.srcjar",
        ":filestore_go_gapic_srcjar-snippets.srcjar",
        ":filestore_go_gapic_srcjar-test.srcjar",
        ":filestore_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "py_test",
)

py_import(
    name = "common",
    srcs = [
        "//google/cloud/common:common_py_gapic",
    ],
)

py_gapic_library(
    name = "filestore_py_gapic",
    srcs = [":filestore_proto"],
    grpc_service_config = "file_grpc_service_config.json",
    opt_args = ["proto-plus-deps=google.cloud.common"],
    rest_numeric_enums = True,
    service_yaml = "file_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":common",
    ],
)

py_test(
    name = "filestore_py_gapic_test",
    srcs = [
        "filestore_py_gapic_pytest.py",
        "filestore_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":filestore_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "filestore-v1-py",
    deps = [
        ":filestore_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "filestore_php_proto",
    deps = [":filestore_proto"],
)

php_gapic_library(
    name = "filestore_php_gapic",
    srcs = [":filestore_proto_with_info"],
    grpc_service_config = "file_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "file_v1.yaml",
    transport = "grpc+rest",
    deps = [":filestore_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-filestore-v1-php",
    deps = [
        ":filestore_php_gapic",
        ":filestore_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "filestore_nodejs_gapic",
    package_name = "@google-cloud/filestore",
    src = ":filestore_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "file_grpc_service_config.json",
    package = "google.cloud.filestore.v1",
    rest_numeric_enums = True,
    service_yaml = "file_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "filestore-v1-nodejs",
    deps = [
        ":filestore_nodejs_gapic",
        ":filestore_proto",
        "//google/cloud/common:common_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "filestore_ruby_proto",
    deps = [":filestore_proto"],
)

ruby_grpc_library(
    name = "filestore_ruby_grpc",
    srcs = [":filestore_proto"],
    deps = [":filestore_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "filestore_ruby_gapic",
    srcs = [":filestore_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=file.googleapis.com",
        "ruby-cloud-api-shortname=file",
        "ruby-cloud-gem-name=google-cloud-filestore-v1",
        "ruby-cloud-product-url=https://cloud.google.com/filestore/",
        "ruby-cloud-extra-dependencies=google-cloud-common=~>1.0",
    ],
    grpc_service_config = "file_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Filestore instances are fully managed NFS file servers on Google Cloud for use with applications running on Compute Engine virtual machine (VM) instances, Google Kubernetes Engine clusters, external datastores such as Google Cloud VMware Engine, or your on-premises machines.",
    ruby_cloud_title = "Filestore V1",
    service_yaml = "file_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":filestore_ruby_grpc",
        ":filestore_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-filestore-v1-ruby",
    deps = [
        ":filestore_ruby_gapic",
        ":filestore_ruby_grpc",
        ":filestore_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "filestore_csharp_proto",
    deps = [":filestore_proto"],
)

csharp_grpc_library(
    name = "filestore_csharp_grpc",
    srcs = [":filestore_proto"],
    deps = [":filestore_csharp_proto"],
)

csharp_gapic_library(
    name = "filestore_csharp_gapic",
    srcs = [":filestore_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "file_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "file_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":filestore_csharp_grpc",
        ":filestore_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-filestore-v1-csharp",
    deps = [
        ":filestore_csharp_gapic",
        ":filestore_csharp_grpc",
        ":filestore_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "filestore_cc_proto",
    deps = [":filestore_proto"],
)

cc_grpc_library(
    name = "filestore_cc_grpc",
    srcs = [":filestore_proto"],
    grpc_only = True,
    deps = [":filestore_cc_proto"],
)
