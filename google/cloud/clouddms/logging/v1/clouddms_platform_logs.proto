// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.clouddms.logging.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.CloudDms.Logging.V1";
option go_package = "cloud.google.com/go/clouddms/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ClouddmsPlatformLogsProto";
option java_package = "com.google.cloud.clouddms.logging.v1";
option php_namespace = "Google\\Cloud\\CloudDms\\Logging\\V1";
option ruby_package = "Google::Cloud::CloudDMS::Logging::V1";

// The database engines.
enum DatabaseEngine {
  // The source database engine of the migration job is unknown.
  DATABASE_ENGINE_UNSPECIFIED = 0;

  // The source engine is MySQL.
  MYSQL = 1;

  // The source engine is PostgreSQL.
  POSTGRESQL = 2;

  // The source engine is SQL Server.
  SQLSERVER = 3;

  // The source engine is Oracle (for heterogeneous migrations).
  ORACLE = 4;

  // The engine is Spanner (for heterogeneous migrations).
  SPANNER = 5;
}

// The database providers.
enum DatabaseProvider {
  // The database provider is unknown.
  DATABASE_PROVIDER_UNSPECIFIED = 0;

  // CloudSQL runs the database.
  CLOUDSQL = 1;

  // RDS runs the database.
  RDS = 2;

  // Amazon Aurora.
  AURORA = 3;

  // AlloyDB.
  ALLOYDB = 4;
}

// A message defining the database engine and provider.
message DatabaseType {
  // The database provider.
  DatabaseProvider provider = 1;

  // The database engine.
  DatabaseEngine engine = 2;
}

// Migration job as stored in Cloud Logging logs.
// NEXT_TAG = 36.
message LoggedMigrationJob {
  // The migration job states enum.
  enum State {
    // The state of the migration job is unknown.
    STATE_UNSPECIFIED = 0;

    // The migration job is down for maintenance.
    MAINTENANCE = 1;

    // The migration job is in draft mode and fully editable.
    DRAFT = 2;

    // The migration job is being created.
    CREATING = 3;

    // The migration job is created and not started.
    NOT_STARTED = 4;

    // The migration job is running
    RUNNING = 5;

    // The migration job failed.
    FAILED = 6;

    // The migration job has been completed.
    COMPLETED = 7;

    // The migration job is being deleted.
    DELETING = 8;

    // The migration job is being stopped.
    STOPPING = 9;

    // The migration job is currently stopped.
    STOPPED = 10;

    // The migration job has been deleted.
    DELETED = 11;

    // The migration job is being updated.
    UPDATING = 12;

    // The migration job is starting.
    STARTING = 13;

    // The migration job is restarting.
    RESTARTING = 14;

    // The migration job is resuming.
    RESUMING = 15;
  }

  // The migration job phases enum.
  enum Phase {
    // The phase of the migration job is unknown.
    PHASE_UNSPECIFIED = 0;

    // The migration job is in the full dump phase.
    FULL_DUMP = 1;

    // The migration job is CDC phase.
    CDC = 2;

    // The migration job is running the promote phase.
    PROMOTE_IN_PROGRESS = 3;

    // Only RDS flow - waiting for source writes to stop
    WAITING_FOR_SOURCE_WRITES_TO_STOP = 4;

    // Only RDS flow - the sources writes stopped, waiting for dump to begin
    PREPARING_THE_DUMP = 5;
  }

  // The migration job types.
  enum Type {
    // The type of the migration job is unknown.
    TYPE_UNSPECIFIED = 0;

    // The migration job is a one time migration.
    ONE_TIME = 1;

    // The migration job is a continuous migration.
    CONTINUOUS = 2;
  }

  // Type of connectivity to source database.
  enum ConnectivityType {
    // No data defined.
    CONNECTIVITY_TYPE_UNSPECIFIED = 0;

    // Connect using static IO
    STATIC_IP = 1;

    // Use reverse SSH connectivity.
    REVERSE_SSH = 2;

    // Use VPC Peering connectivity.
    VPC_PEERING = 3;
  }

  // Required. The unique identifier for a migration job.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Labels.
  map<string, string> labels = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The display name.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The current migration job state.
  State state = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The current migration job phase.
  Phase phase = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The migration job type.
  Type type = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional dump path (gs://[BUCKET_NAME]/[OBJECT_NAME]).
  string dump_path = 7 [(google.api.field_behavior) = OPTIONAL];

  // Required. The migration job source connection profile name.
  string source = 8 [(google.api.field_behavior) = REQUIRED];

  // Required. The migration job destination connection profile name.
  string destination = 9 [(google.api.field_behavior) = REQUIRED];

  // Required. the migration job duration in seconds.
  google.protobuf.Duration duration = 10 [(google.api.field_behavior) = REQUIRED];

  // Required. Type of connectivity to source database.
  ConnectivityType connectivity_type = 11 [(google.api.field_behavior) = REQUIRED];

  // Required. The error details in case of state FAILED.
  google.rpc.Status error = 12 [(google.api.field_behavior) = REQUIRED];

  // Required. The time when this migration job was completed. Should only be set when the
  // phase of the migration job is COMPLETED.
  google.protobuf.Timestamp end_time = 13 [(google.api.field_behavior) = REQUIRED];

  // Required. The indicative source database.
  DatabaseType source_database = 14 [(google.api.field_behavior) = REQUIRED];

  // Required. The indicative destination database.
  DatabaseType destination_database = 15 [(google.api.field_behavior) = REQUIRED];
}

// An MySQL database connection profile.
message MySqlConnectionProfile {
  // The MySQL database version.
  enum Version {
    // Unspecified version.
    VERSION_UNSPECIFIED = 0;

    // MySQL 5.5.
    V5_5 = 1;

    // MySQL 5.6.
    V5_6 = 2;

    // MySQL 5.7.
    V5_7 = 3;

    // MySQL 8.0.
    V8_0 = 4;
  }

  // The database version.
  Version version = 1;

  // The Cloud SQL id for a Cloud SQL instance.
  string cloud_sql_id = 2;
}

// An PostgreSQL connection profile.
message PostgreSqlConnectionProfile {
  // The PostgreSQL database version.
  enum Version {
    // Unspecified version.
    VERSION_UNSPECIFIED = 0;

    // PostgreSQL 9.6.
    V9_6 = 1;

    // PostgreSQL 11.
    V11 = 2;

    // PostgreSQL 10.
    V10 = 3;

    // PostgreSQL 12.
    V12 = 4;

    // PostgreSQL 13.
    V13 = 5;
  }

  // The database version.
  Version version = 1;

  // The Cloud SQL id for a Cloud SQL instance.
  string cloud_sql_id = 2;
}

// A CloudSQL connection profile.
message CloudSqlConnectionProfile {
  // The Cloud SQL id.
  string cloud_sql_id = 1;
}

// An Oracle connection profile.
message OracleConnectionProfile {
  // Connectivity options used to establish a connection to the profile.
  enum ConnectivityType {
    // No data defined.
    CONNECTIVITY_TYPE_UNSPECIFIED = 0;

    // Static Service IP connectivity.
    STATIC_SERVICE_IP = 1;

    // Forward SSH tunnel connectivity.
    FORWARD_SSH_TUNNEL = 2;

    // Private connectivity.
    PRIVATE_CONNECTIVITY = 3;
  }

  // Required. Type of connectivity to source database.
  ConnectivityType connectivity_type = 1 [(google.api.field_behavior) = REQUIRED];
}

// An producer connection profile definition.
// NEXT_TAG = 8.
message LoggedConnectionProfile {
  // The connection profile states enum.
  enum State {
    // The state of the connection profile is unknown.
    STATE_UNSPECIFIED = 0;

    // The connection profile is in draft mode and fully editable.
    DRAFT = 1;

    // The connection profile is being created.
    CREATING = 2;

    // The connection profile is ready.
    READY = 3;

    // The connection profile is being updated.
    UPDATING = 4;

    // The connection profile is being deleted.
    DELETING = 5;

    // The connection profile has been deleted.
    DELETED = 6;

    // The last action on the connection profile failed.
    FAILED = 7;
  }

  // The unique identifier for a connection profile.
  string name = 1;

  // Labels.
  map<string, string> labels = 2;

  // The current connection profile state.
  State state = 3;

  // The display name.
  string display_name = 4;

  // The connection profile definition
  oneof connection_profile {
    // A MySQL database connection profile.
    MySqlConnectionProfile mysql = 100;

    // A PostgreSQL database connection profile.
    PostgreSqlConnectionProfile postgresql = 101;

    // A CloudSQL database connection profile.
    CloudSqlConnectionProfile cloudsql = 102;

    // An Oracle database connection profile.
    OracleConnectionProfile oracle = 103;
  }

  // The error details in case of state FAILED.
  google.rpc.Status error = 5;

  // The database provider.
  DatabaseProvider provider = 6;
}

// Log definition for Migration Job event
message MigrationJobEventLog {
  // The migration job resource
  LoggedMigrationJob migration_job = 1;

  // Timestamp of the event
  google.protobuf.Timestamp occurrence_timestamp = 2;

  // Event code
  int32 code = 3;

  // Event message
  string text_message = 4;

  // Original event data
  oneof original_cause {
    // Original event code
    int32 original_code = 200;

    // Original event message
    string original_message = 201;
  }
}

// Log definition for Connection Profile event
message ConnectionProfileEventLog {
  // The connection profilr resource
  LoggedConnectionProfile connection_profile = 1;

  // Timestamp of the event
  google.protobuf.Timestamp occurrence_timestamp = 2;

  // Event code
  int32 code = 3;

  // Event message
  string text_message = 4;

  // Original event data
  oneof original_cause {
    // Original event code
    int32 original_code = 200;

    // Original event message
    string original_message = 201;
  }
}

// The PrivateConnection resource is used to establish private connectivity
// with the customer's network.
message LoggedPrivateConnection {
  // Private Connection state.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The private connection is in creation state - creating resources.
    CREATING = 1;

    // The private connection has been created with all of its resources.
    CREATED = 2;

    // The private connection creation has failed.
    FAILED = 3;

    // The private connection is being deleted.
    DELETING = 4;

    // Delete request has failed, resource is in invalid state.
    FAILED_TO_DELETE = 5;

    // The private connection has been deleted.
    DELETED = 6;
  }

  // The resource's name.
  string name = 1;

  // Labels.
  map<string, string> labels = 2;

  // Display name.
  string display_name = 3;

  // The state of the Private Connection.
  State state = 4;

  // The error details in case of state FAILED.
  google.rpc.Status error = 5;

  // VPC Peering Config.
  VpcPeeringConfig vpc_peering_config = 100;
}

// The VPC Peering configuration is used to create VPC peering between
// the data plane and the consumer's VPC.
message VpcPeeringConfig {
  // Fully qualified name of the VPC DMS will peer to.
  string vpc_name = 1;

  // The subnet that will be peered to. (CIDR of /29).
  string subnet = 2;
}

// Log definition for a Private Connection event.
message PrivateConnectionEventLog {
  // The private connection resource.
  LoggedPrivateConnection private_connection = 1;

  // Timestamp of the event.
  google.protobuf.Timestamp occurrence_timestamp = 2;

  // Event code.
  int32 code = 3;

  // Event message.
  string text_message = 4;

  // Original event data.
  oneof original_cause {
    // Original event code.
    int32 original_code = 200;

    // Original event message.
    string original_message = 201;
  }
}
