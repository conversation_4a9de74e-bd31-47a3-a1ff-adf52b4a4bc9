# This build file includes a target for the Ruby wrapper library for
# google-cloud-cloud_dms.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for datamigration.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "datamigration_ruby_wrapper",
    srcs = ["//google/cloud/clouddms/v1:clouddms_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-cloud_dms",
        "ruby-cloud-env-prefix=DATABASE_MIGRATION",
        "ruby-cloud-wrapper-of=v1:0.7",
        "ruby-cloud-product-url=https://cloud.google.com/database-migration/",
        "ruby-cloud-api-id=datamigration.googleapis.com",
        "ruby-cloud-api-shortname=datamigration",
        "ruby-cloud-namespace-override=CloudDms=CloudDMS",
    ],
    ruby_cloud_description = "Database Migration Service makes it easier for you to migrate your data to Google Cloud. Database Migration Service helps you lift and shift your MySQL and PostgreSQL workloads into Cloud SQL. Database Migration Service streamlines networking workflow, manages the initial snapshot and ongoing replication, and provides a status of the migration operation.",
    ruby_cloud_title = "Cloud Database Migration Service",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-clouddms-ruby",
    deps = [
        ":datamigration_ruby_wrapper",
    ],
)
