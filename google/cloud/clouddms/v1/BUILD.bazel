# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "clouddms_proto",
    srcs = [
        "clouddms.proto",
        "clouddms_resources.proto",
        "conversionworkspace_resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "clouddms_proto_with_info",
    deps = [
        ":clouddms_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "clouddms_java_proto",
    deps = [":clouddms_proto"],
)

java_grpc_library(
    name = "clouddms_java_grpc",
    srcs = [":clouddms_proto"],
    deps = [":clouddms_java_proto"],
)

java_gapic_library(
    name = "clouddms_java_gapic",
    srcs = [":clouddms_proto_with_info"],
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datamigration_v1.yaml",
    test_deps = [
        ":clouddms_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":clouddms_java_proto",
    ],
)

java_gapic_test(
    name = "clouddms_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.clouddms.v1.DataMigrationServiceClientTest",
    ],
    runtime_deps = [":clouddms_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-clouddms-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":clouddms_java_gapic",
        ":clouddms_java_grpc",
        ":clouddms_java_proto",
        ":clouddms_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "clouddms_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/clouddms/apiv1/clouddmspb",
    protos = [":clouddms_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "clouddms_go_gapic",
    srcs = [":clouddms_proto_with_info"],
    grpc_service_config = "library_grpc_service_config.json",
    importpath = "cloud.google.com/go/clouddms/apiv1;clouddms",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "datamigration_v1.yaml",
    transport = "grpc",
    deps = [
        ":clouddms_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-clouddms-v1-go",
    deps = [
        ":clouddms_go_gapic",
        ":clouddms_go_gapic_srcjar-metadata.srcjar",
        ":clouddms_go_gapic_srcjar-snippets.srcjar",
        ":clouddms_go_gapic_srcjar-test.srcjar",
        ":clouddms_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "clouddms_py_gapic",
    srcs = [":clouddms_proto"],
    grpc_service_config = "library_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-dms"],
    rest_numeric_enums = True,
    service_yaml = "datamigration_v1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "clouddms_py_gapic_test",
    srcs = [
        "clouddms_py_gapic_pytest.py",
        "clouddms_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":clouddms_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "clouddms-v1-py",
    deps = [
        ":clouddms_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "clouddms_php_proto",
    deps = [":clouddms_proto"],
)

php_gapic_library(
    name = "clouddms_php_gapic",
    srcs = [":clouddms_proto_with_info"],
    grpc_service_config = "library_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "datamigration_v1.yaml",
    transport = "grpc+rest",
    deps = [":clouddms_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-clouddms-v1-php",
    deps = [
        ":clouddms_php_gapic",
        ":clouddms_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "clouddms_nodejs_gapic",
    package_name = "@google-cloud/dms",
    src = ":clouddms_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "library_grpc_service_config.json",
    package = "google.cloud.clouddms.v1",
    rest_numeric_enums = True,
    service_yaml = "datamigration_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "clouddms-v1-nodejs",
    deps = [
        ":clouddms_nodejs_gapic",
        ":clouddms_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "clouddms_ruby_proto",
    deps = [":clouddms_proto"],
)

ruby_grpc_library(
    name = "clouddms_ruby_grpc",
    srcs = [":clouddms_proto"],
    deps = [":clouddms_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "clouddms_ruby_gapic",
    srcs = [":clouddms_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-cloud_dms-v1",
        "ruby-cloud-gem-namespace=Google::Cloud::CloudDMS::V1",
        "ruby-cloud-env-prefix=DATABASE_MIGRATION",
        "ruby-cloud-product-url=https://cloud.google.com/database-migration/",
        "ruby-cloud-api-id=datamigration.googleapis.com",
        "ruby-cloud-api-shortname=datamigration",
        "ruby-cloud-namespace-override=CloudDms=CloudDMS",
    ],
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Database Migration Service makes it easier for you to migrate your data to Google Cloud. Database Migration Service helps you lift and shift your MySQL and PostgreSQL workloads into Cloud SQL. Database Migration Service streamlines networking workflow, manages the initial snapshot and ongoing replication, and provides a status of the migration operation.",
    ruby_cloud_title = "Cloud Database Migration Service V1",
    service_yaml = "datamigration_v1.yaml",
    transport = "grpc",
    deps = [
        ":clouddms_ruby_grpc",
        ":clouddms_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-clouddms-v1-ruby",
    deps = [
        ":clouddms_ruby_gapic",
        ":clouddms_ruby_grpc",
        ":clouddms_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "clouddms_csharp_proto",
    deps = [":clouddms_proto"],
)

csharp_grpc_library(
    name = "clouddms_csharp_grpc",
    srcs = [":clouddms_proto"],
    deps = [":clouddms_csharp_proto"],
)

csharp_gapic_library(
    name = "clouddms_csharp_gapic",
    srcs = [":clouddms_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datamigration_v1.yaml",
    transport = "grpc",
    deps = [
        ":clouddms_csharp_grpc",
        ":clouddms_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-clouddms-v1-csharp",
    deps = [
        ":clouddms_csharp_gapic",
        ":clouddms_csharp_grpc",
        ":clouddms_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "clouddms_cc_proto",
    deps = [":clouddms_proto"],
)

cc_grpc_library(
    name = "clouddms_cc_grpc",
    srcs = [":clouddms_proto"],
    grpc_only = True,
    deps = [":clouddms_cc_proto"],
)
