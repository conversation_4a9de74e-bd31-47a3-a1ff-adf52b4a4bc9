// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.clouddms.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.CloudDms.V1";
option go_package = "cloud.google.com/go/clouddms/apiv1/clouddmspb;clouddmspb";
option java_multiple_files = true;
option java_outer_classname = "ClouddmsResourcesProto";
option java_package = "com.google.cloud.clouddms.v1";
option php_namespace = "Google\\Cloud\\CloudDms\\V1";
option ruby_package = "Google::Cloud::CloudDMS::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Networks"
  pattern: "projects/{project}/global/networks/{network}"
};

// SSL configuration information.
message SslConfig {
  // Specifies The kind of ssl configuration used.
  enum SslType {
    // Unspecified.
    SSL_TYPE_UNSPECIFIED = 0;

    // Only 'ca_certificate' specified.
    SERVER_ONLY = 1;

    // Both server ('ca_certificate'), and client ('client_key',
    // 'client_certificate') specified.
    SERVER_CLIENT = 2;
  }

  // Output only. The ssl config type according to 'client_key',
  // 'client_certificate' and 'ca_certificate'.
  SslType type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. The unencrypted PKCS#1 or PKCS#8 PEM-encoded private key
  // associated with the Client Certificate. If this field is used then the
  // 'client_certificate' field is mandatory.
  string client_key = 2 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The x509 PEM-encoded certificate that will be used by the
  // replica to authenticate against the source database server.If this field is
  // used then the 'client_key' field is mandatory.
  string client_certificate = 3 [(google.api.field_behavior) = INPUT_ONLY];

  // Required. Input only. The x509 PEM-encoded certificate of the CA that
  // signed the source database server's certificate. The replica will use this
  // certificate to verify it's connecting to the right host.
  string ca_certificate = 4 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = REQUIRED
  ];
}

// Specifies connection parameters required specifically for MySQL databases.
message MySqlConnectionProfile {
  // Required. The IP or hostname of the source MySQL database.
  string host = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The network port of the source MySQL database.
  int32 port = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The username that Database Migration Service will use to connect
  // to the database. The value is encrypted when stored in Database Migration
  // Service.
  string username = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Input only. The password for the user that Database Migration
  // Service will be using to connect to the database. This field is not
  // returned on request, and the value is encrypted when stored in Database
  // Migration Service.
  string password = 4 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = REQUIRED
  ];

  // Output only. Indicates If this connection profile password is stored.
  bool password_set = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // SSL configuration for the destination to connect to the source database.
  SslConfig ssl = 6;

  // If the source is a Cloud SQL database, use this field to
  // provide the Cloud SQL instance ID of the source.
  string cloud_sql_id = 7;
}

enum NetworkArchitecture {
  NETWORK_ARCHITECTURE_UNSPECIFIED = 0;

  // Instance is in Cloud SQL's old producer network architecture.
  NETWORK_ARCHITECTURE_OLD_CSQL_PRODUCER = 1;

  // Instance is in Cloud SQL's new producer network architecture.
  NETWORK_ARCHITECTURE_NEW_CSQL_PRODUCER = 2;
}

// Specifies connection parameters required specifically for PostgreSQL
// databases.
message PostgreSqlConnectionProfile {
  // Required. The IP or hostname of the source PostgreSQL database.
  string host = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The network port of the source PostgreSQL database.
  int32 port = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The username that Database Migration Service will use to connect
  // to the database. The value is encrypted when stored in Database Migration
  // Service.
  string username = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Input only. The password for the user that Database Migration
  // Service will be using to connect to the database. This field is not
  // returned on request, and the value is encrypted when stored in Database
  // Migration Service.
  string password = 4 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = REQUIRED
  ];

  // Output only. Indicates If this connection profile password is stored.
  bool password_set = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // SSL configuration for the destination to connect to the source database.
  SslConfig ssl = 6;

  // If the source is a Cloud SQL database, use this field to
  // provide the Cloud SQL instance ID of the source.
  string cloud_sql_id = 7;

  // Output only. If the source is a Cloud SQL database, this field indicates
  // the network architecture it's associated with.
  NetworkArchitecture network_architecture = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Connectivity options used to establish a connection to the database server.
  oneof connectivity {
    // Static ip connectivity data (default, no additional details needed).
    StaticIpConnectivity static_ip_connectivity = 100;

    // Private service connect connectivity.
    PrivateServiceConnectConnectivity private_service_connect_connectivity =
        101;
  }
}

// Specifies connection parameters required specifically for Oracle
// databases.
message OracleConnectionProfile {
  // Required. The IP or hostname of the source Oracle database.
  string host = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The network port of the source Oracle database.
  int32 port = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The username that Database Migration Service will use to connect
  // to the database. The value is encrypted when stored in Database Migration
  // Service.
  string username = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Input only. The password for the user that Database Migration
  // Service will be using to connect to the database. This field is not
  // returned on request, and the value is encrypted when stored in Database
  // Migration Service.
  string password = 4 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = REQUIRED
  ];

  // Output only. Indicates whether a new password is included in the request.
  bool password_set = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Database service for the Oracle connection.
  string database_service = 6 [(google.api.field_behavior) = REQUIRED];

  // SSL configuration for the connection to the source Oracle database.
  //
  //  * Only `SERVER_ONLY` configuration is supported for Oracle SSL.
  //  * SSL is supported for Oracle versions 12 and above.
  SslConfig ssl = 7;

  // Connectivity options used to establish a connection to the database server.
  oneof connectivity {
    // Static Service IP connectivity.
    StaticServiceIpConnectivity static_service_ip_connectivity = 100;

    // Forward SSH tunnel connectivity.
    ForwardSshTunnelConnectivity forward_ssh_connectivity = 101;

    // Private connectivity.
    PrivateConnectivity private_connectivity = 102;
  }
}

// Specifies required connection parameters, and, optionally, the parameters
// required to create a Cloud SQL destination database instance.
message CloudSqlConnectionProfile {
  // Output only. The Cloud SQL instance ID that this connection profile is
  // associated with.
  string cloud_sql_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Metadata used to create the destination Cloud SQL database.
  CloudSqlSettings settings = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The Cloud SQL database instance's private IP.
  string private_ip = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Cloud SQL database instance's public IP.
  string public_ip = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Cloud SQL database instance's additional (outgoing) public
  // IP. Used when the Cloud SQL database availability type is REGIONAL (i.e.
  // multiple zones / highly available).
  string additional_public_ip = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Specifies required connection parameters, and the parameters
// required to create an AlloyDB destination cluster.
message AlloyDbConnectionProfile {
  // Required. The AlloyDB cluster ID that this connection profile is associated
  // with.
  string cluster_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Immutable. Metadata used to create the destination AlloyDB cluster.
  AlloyDbSettings settings = 2 [(google.api.field_behavior) = IMMUTABLE];
}

// An entry for an Access Control list.
message SqlAclEntry {
  // The allowlisted value for the access control list.
  string value = 1;

  // The access control entry entry expiration.
  oneof expiration {
    // The time when this access control entry expires in
    // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example:
    // `2012-11-15T16:19:00.094Z`.
    google.protobuf.Timestamp expire_time = 10;

    // Input only. The time-to-leave of this access control entry.
    google.protobuf.Duration ttl = 11
        [(google.api.field_behavior) = INPUT_ONLY];
  }

  // A label to identify this entry.
  string label = 3;
}

// IP Management configuration.
message SqlIpConfig {
  // Whether the instance should be assigned an IPv4 address or not.
  google.protobuf.BoolValue enable_ipv4 = 1;

  // The resource link for the VPC network from which the Cloud SQL instance is
  // accessible for private IP. For example,
  // `projects/myProject/global/networks/default`. This setting can
  // be updated, but it cannot be removed after it is set.
  string private_network = 2;

  // Optional. The name of the allocated IP address range for the private IP
  // Cloud SQL instance. This name refers to an already allocated IP range
  // address. If set, the instance IP address will be created in the allocated
  // range. Note that this IP address range can't be modified after the instance
  // is created. If you change the VPC when configuring connectivity settings
  // for the migration job, this field is not relevant.
  string allocated_ip_range = 5 [(google.api.field_behavior) = OPTIONAL];

  // Whether SSL connections over IP should be enforced or not.
  google.protobuf.BoolValue require_ssl = 3;

  // The list of external networks that are allowed to connect to the instance
  // using the IP. See
  // https://en.wikipedia.org/wiki/CIDR_notation#CIDR_notation, also known as
  // 'slash' notation (e.g. `*************/24`).
  repeated SqlAclEntry authorized_networks = 4;
}

// Settings for creating a Cloud SQL database instance.
message CloudSqlSettings {
  // Specifies when the instance should be activated.
  enum SqlActivationPolicy {
    // unspecified policy.
    SQL_ACTIVATION_POLICY_UNSPECIFIED = 0;

    // The instance is always up and running.
    ALWAYS = 1;

    // The instance should never spin up.
    NEVER = 2;
  }

  // The storage options for Cloud SQL databases.
  enum SqlDataDiskType {
    // Unspecified.
    SQL_DATA_DISK_TYPE_UNSPECIFIED = 0;

    // SSD disk.
    PD_SSD = 1;

    // HDD disk.
    PD_HDD = 2;
  }

  // The database engine type and version.
  enum SqlDatabaseVersion {
    // Unspecified version.
    SQL_DATABASE_VERSION_UNSPECIFIED = 0;

    // MySQL 5.6.
    MYSQL_5_6 = 1;

    // MySQL 5.7.
    MYSQL_5_7 = 2;

    // PostgreSQL 9.6.
    POSTGRES_9_6 = 3;

    // PostgreSQL 11.
    POSTGRES_11 = 4;

    // PostgreSQL 10.
    POSTGRES_10 = 5;

    // MySQL 8.0.
    MYSQL_8_0 = 6;

    // PostgreSQL 12.
    POSTGRES_12 = 7;

    // PostgreSQL 13.
    POSTGRES_13 = 8;

    // PostgreSQL 14.
    POSTGRES_14 = 17;

    // PostgreSQL 15.
    POSTGRES_15 = 18;
  }

  // The availability type of the given Cloud SQL instance.
  enum SqlAvailabilityType {
    // This is an unknown Availability type.
    SQL_AVAILABILITY_TYPE_UNSPECIFIED = 0;

    // Zonal availablility instance.
    ZONAL = 1;

    // Regional availability instance.
    REGIONAL = 2;
  }

  // The edition of the given Cloud SQL instance.
  // Can be ENTERPRISE or ENTERPRISE_PLUS.
  enum Edition {
    // The instance did not specify the edition.
    EDITION_UNSPECIFIED = 0;

    // The instance is an enterprise edition.
    ENTERPRISE = 2;

    // The instance is an enterprise plus edition.
    ENTERPRISE_PLUS = 3;
  }

  // The database engine type and version.
  SqlDatabaseVersion database_version = 1;

  // The resource labels for a Cloud SQL instance to use to annotate any related
  // underlying resources such as Compute Engine VMs.
  // An object containing a list of "key": "value" pairs.
  //
  // Example: `{ "name": "wrench", "mass": "18kg", "count": "3" }`.
  map<string, string> user_labels = 2;

  // The tier (or machine type) for this instance, for example:
  // `db-n1-standard-1` (MySQL instances) or
  // `db-custom-1-3840` (PostgreSQL instances).
  // For more information, see
  // [Cloud SQL Instance
  // Settings](https://cloud.google.com/sql/docs/mysql/instance-settings).
  string tier = 3;

  // The maximum size to which storage capacity can be automatically increased.
  // The default value is 0, which specifies that there is no limit.
  google.protobuf.Int64Value storage_auto_resize_limit = 4;

  // The activation policy specifies when the instance is activated; it is
  // applicable only when the instance state is 'RUNNABLE'. Valid values:
  //
  // 'ALWAYS': The instance is on, and remains so even in
  // the absence of connection requests.
  //
  // `NEVER`: The instance is off; it is not activated, even if a
  // connection request arrives.
  SqlActivationPolicy activation_policy = 5;

  // The settings for IP Management. This allows to enable or disable the
  // instance IP and manage which external networks can connect to the instance.
  // The IPv4 address cannot be disabled.
  SqlIpConfig ip_config = 6;

  // [default: ON] If you enable this setting, Cloud SQL checks your available
  // storage every 30 seconds. If the available storage falls below a threshold
  // size, Cloud SQL automatically adds additional storage capacity. If the
  // available storage repeatedly falls below the threshold size, Cloud SQL
  // continues to add storage until it reaches the maximum of 30 TB.
  google.protobuf.BoolValue auto_storage_increase = 7;

  // The database flags passed to the Cloud SQL instance at startup.
  // An object containing a list of "key": value pairs.
  // Example: { "name": "wrench", "mass": "1.3kg", "count": "3" }.
  map<string, string> database_flags = 8;

  // The type of storage: `PD_SSD` (default) or `PD_HDD`.
  SqlDataDiskType data_disk_type = 9;

  // The storage capacity available to the database, in GB.
  // The minimum (and default) size is 10GB.
  google.protobuf.Int64Value data_disk_size_gb = 10;

  // The Google Cloud Platform zone where your Cloud SQL database instance is
  // located.
  string zone = 11;

  // Optional. The Google Cloud Platform zone where the failover Cloud SQL
  // database instance is located. Used when the Cloud SQL database availability
  // type is REGIONAL (i.e. multiple zones / highly available).
  string secondary_zone = 18 [(google.api.field_behavior) = OPTIONAL];

  // The Database Migration Service source connection profile ID,
  // in the format:
  // `projects/my_project_name/locations/us-central1/connectionProfiles/connection_profile_ID`
  string source_id = 12;

  // Input only. Initial root password.
  string root_password = 13 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Indicates If this connection profile root password is stored.
  bool root_password_set = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The Cloud SQL default instance level collation.
  string collation = 15;

  // The KMS key name used for the csql instance.
  string cmek_key_name = 16;

  // Optional. Availability type. Potential values:
  // *  `ZONAL`: The instance serves data from only one zone. Outages in that
  // zone affect data availability.
  // *  `REGIONAL`: The instance can serve data from more than one zone in a
  // region (it is highly available).
  SqlAvailabilityType availability_type = 17
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The edition of the given Cloud SQL instance.
  Edition edition = 19 [(google.api.field_behavior) = OPTIONAL];
}

// Settings for creating an AlloyDB cluster.
message AlloyDbSettings {
  // The username/password for a database user. Used for specifying initial
  // users at cluster creation time.
  message UserPassword {
    // The database username.
    string user = 1;

    // The initial password for the user.
    string password = 2;

    // Output only. Indicates if the initial_user.password field has been set.
    bool password_set = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Settings for the cluster's primary instance
  message PrimaryInstanceSettings {
    // MachineConfig describes the configuration of a machine.
    message MachineConfig {
      // The number of CPU's in the VM instance.
      int32 cpu_count = 1;
    }

    // Required. The ID of the AlloyDB primary instance. The ID must satisfy the
    // regex expression "[a-z0-9-]+".
    string id = 1 [(google.api.field_behavior) = REQUIRED];

    // Configuration for the machines that host the underlying
    // database engine.
    MachineConfig machine_config = 2;

    // Database flags to pass to AlloyDB when DMS is creating the AlloyDB
    // cluster and instances. See the AlloyDB documentation for how these can be
    // used.
    map<string, string> database_flags = 6;

    // Labels for the AlloyDB primary instance created by DMS. An object
    // containing a list of 'key', 'value' pairs.
    map<string, string> labels = 7;

    // Output only. The private IP address for the Instance.
    // This is the connection endpoint for an end-user application.
    string private_ip = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // EncryptionConfig describes the encryption config of a cluster that is
  // encrypted with a CMEK (customer-managed encryption key).
  message EncryptionConfig {
    // The fully-qualified resource name of the KMS key.
    // Each Cloud KMS key is regionalized and has the following format:
    // projects/[PROJECT]/locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]
    string kms_key_name = 1;
  }

  // Required. Input only. Initial user to setup during cluster creation.
  // Required.
  UserPassword initial_user = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Required. The resource link for the VPC network in which cluster resources
  // are created and from which they are accessible via Private IP. The network
  // must belong to the same project as the cluster. It is specified in the
  // form: "projects/{project_number}/global/networks/{network_id}". This is
  // required to create a cluster.
  string vpc_network = 2 [(google.api.field_behavior) = REQUIRED];

  // Labels for the AlloyDB cluster created by DMS. An object containing a list
  // of 'key', 'value' pairs.
  map<string, string> labels = 3;

  PrimaryInstanceSettings primary_instance_settings = 4;

  // Optional. The encryption config can be specified to encrypt the data disks
  // and other persistent data resources of a cluster with a
  // customer-managed encryption key (CMEK). When this field is not
  // specified, the cluster will then use default encryption scheme to
  // protect the user data.
  EncryptionConfig encryption_config = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// The source database will allow incoming connections from the public IP of the
// destination database. You can retrieve the public IP of the Cloud SQL
// instance from the Cloud SQL console or using Cloud SQL APIs. No additional
// configuration is required.
message StaticIpConnectivity {}

// [Private Service Connect
// connectivity](https://cloud.google.com/vpc/docs/private-service-connect#service-attachments)
message PrivateServiceConnectConnectivity {
  // Required. A service attachment that exposes a database, and has the
  // following format:
  // projects/{project}/regions/{region}/serviceAttachments/{service_attachment_name}
  string service_attachment = 1 [(google.api.field_behavior) = REQUIRED];
}

// The details needed to configure a reverse SSH tunnel between the source and
// destination databases. These details will be used when calling the
// generateSshScript method (see
// https://cloud.google.com/database-migration/docs/reference/rest/v1/projects.locations.migrationJobs/generateSshScript)
// to produce the script that will help set up the reverse SSH tunnel, and to
// set up the VPC peering between the Cloud SQL private network and the VPC.
message ReverseSshConnectivity {
  // Required. The IP of the virtual machine (Compute Engine) used as the
  // bastion server for the SSH tunnel.
  string vm_ip = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The forwarding port of the virtual machine (Compute Engine) used
  // as the bastion server for the SSH tunnel.
  int32 vm_port = 2 [(google.api.field_behavior) = REQUIRED];

  // The name of the virtual machine (Compute Engine) used as the bastion server
  // for the SSH tunnel.
  string vm = 3;

  // The name of the VPC to peer with the Cloud SQL private network.
  string vpc = 4;
}

// The details of the VPC where the source database is located in Google Cloud.
// We will use this information to set up the VPC peering connection between
// Cloud SQL and this VPC.
message VpcPeeringConnectivity {
  // The name of the VPC network to peer with the Cloud SQL private network.
  string vpc = 1;
}

// Forward SSH Tunnel connectivity.
message ForwardSshTunnelConnectivity {
  // Required. Hostname for the SSH tunnel.
  string hostname = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Username for the SSH tunnel.
  string username = 2 [(google.api.field_behavior) = REQUIRED];

  // Port for the SSH tunnel, default value is 22.
  int32 port = 3;

  oneof authentication_method {
    // Input only. SSH password.
    string password = 100 [(google.api.field_behavior) = INPUT_ONLY];

    // Input only. SSH private key.
    string private_key = 101 [(google.api.field_behavior) = INPUT_ONLY];
  }
}

// Static IP address connectivity configured on service project.
message StaticServiceIpConnectivity {}

// Private Connectivity.
message PrivateConnectivity {
  // Required. The resource name (URI) of the private connection.
  string private_connection = 1 [(google.api.field_behavior) = REQUIRED];
}

// A message defining the database engine and provider.
message DatabaseType {
  // The database provider.
  DatabaseProvider provider = 1;

  // The database engine.
  DatabaseEngine engine = 2;
}

// Represents a Database Migration Service migration job object.
message MigrationJob {
  option (google.api.resource) = {
    type: "datamigration.googleapis.com/MigrationJob"
    pattern: "projects/{project}/locations/{location}/migrationJobs/{migration_job}"
  };

  // The current migration job states.
  enum State {
    // The state of the migration job is unknown.
    STATE_UNSPECIFIED = 0;

    // The migration job is down for maintenance.
    MAINTENANCE = 1;

    // The migration job is in draft mode and no resources are created.
    DRAFT = 2;

    // The migration job is being created.
    CREATING = 3;

    // The migration job is created and not started.
    NOT_STARTED = 4;

    // The migration job is running.
    RUNNING = 5;

    // The migration job failed.
    FAILED = 6;

    // The migration job has been completed.
    COMPLETED = 7;

    // The migration job is being deleted.
    DELETING = 8;

    // The migration job is being stopped.
    STOPPING = 9;

    // The migration job is currently stopped.
    STOPPED = 10;

    // The migration job has been deleted.
    DELETED = 11;

    // The migration job is being updated.
    UPDATING = 12;

    // The migration job is starting.
    STARTING = 13;

    // The migration job is restarting.
    RESTARTING = 14;

    // The migration job is resuming.
    RESUMING = 15;
  }

  // The current migration job phase.
  enum Phase {
    // The phase of the migration job is unknown.
    PHASE_UNSPECIFIED = 0;

    // The migration job is in the full dump phase.
    FULL_DUMP = 1;

    // The migration job is CDC phase.
    CDC = 2;

    // The migration job is running the promote phase.
    PROMOTE_IN_PROGRESS = 3;

    // Only RDS flow - waiting for source writes to stop
    WAITING_FOR_SOURCE_WRITES_TO_STOP = 4;

    // Only RDS flow - the sources writes stopped, waiting for dump to begin
    PREPARING_THE_DUMP = 5;
  }

  // The type of migration job (one-time or continuous).
  enum Type {
    // The type of the migration job is unknown.
    TYPE_UNSPECIFIED = 0;

    // The migration job is a one time migration.
    ONE_TIME = 1;

    // The migration job is a continuous migration.
    CONTINUOUS = 2;
  }

  // Dump flag definition.
  message DumpFlag {
    // The name of the flag
    string name = 1;

    // The value of the flag.
    string value = 2;
  }

  // Dump flags definition.
  message DumpFlags {
    // The flags for the initial dump.
    repeated DumpFlag dump_flags = 1;
  }

  // Performance configuration definition.
  message PerformanceConfig {
    // Describes the parallelism level during initial dump.
    enum DumpParallelLevel {
      // Unknown dump parallel level. Will be defaulted to OPTIMAL.
      DUMP_PARALLEL_LEVEL_UNSPECIFIED = 0;

      // Minimal parallel level.
      MIN = 1;

      // Optimal parallel level.
      OPTIMAL = 2;

      // Maximum parallel level.
      MAX = 3;
    }

    // Initial dump parallelism level.
    DumpParallelLevel dump_parallel_level = 1;
  }

  // The name (URI) of this migration job resource, in the form of:
  // projects/{project}/locations/{location}/migrationJobs/{migrationJob}.
  string name = 1;

  // Output only. The timestamp when the migration job resource was created.
  // A timestamp in RFC3339 UTC "Zulu" format, accurate to nanoseconds.
  // Example: "2014-10-02T15:01:23.045123456Z".
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the migration job resource was last
  // updated. A timestamp in RFC3339 UTC "Zulu" format, accurate to nanoseconds.
  // Example: "2014-10-02T15:01:23.045123456Z".
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The resource labels for migration job to use to annotate any related
  // underlying resources such as Compute Engine VMs. An object containing a
  // list of "key": "value" pairs.
  //
  // Example: `{ "name": "wrench", "mass": "1.3kg", "count": "3" }`.
  map<string, string> labels = 4;

  // The migration job display name.
  string display_name = 5;

  // The current migration job state.
  State state = 6;

  // Output only. The current migration job phase.
  Phase phase = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The migration job type.
  Type type = 8 [(google.api.field_behavior) = REQUIRED];

  // The path to the dump file in Google Cloud Storage,
  // in the format: (gs://[BUCKET_NAME]/[OBJECT_NAME]).
  // This field and the "dump_flags" field are mutually exclusive.
  string dump_path = 9;

  // The initial dump flags.
  // This field and the "dump_path" field are mutually exclusive.
  DumpFlags dump_flags = 17;

  // Required. The resource name (URI) of the source connection profile.
  string source = 10 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name (URI) of the destination connection profile.
  string destination = 11 [(google.api.field_behavior) = REQUIRED];

  // The connectivity method.
  oneof connectivity {
    // The details needed to communicate to the source over Reverse SSH
    // tunnel connectivity.
    ReverseSshConnectivity reverse_ssh_connectivity = 101;

    // The details of the VPC network that the source database is located in.
    VpcPeeringConnectivity vpc_peering_connectivity = 102;

    // static ip connectivity data (default, no additional details needed).
    StaticIpConnectivity static_ip_connectivity = 103;
  }

  // Output only. The duration of the migration job (in seconds). A duration in
  // seconds with up to nine fractional digits, terminated by 's'. Example:
  // "3.5s".
  google.protobuf.Duration duration = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The error details in case of state FAILED.
  google.rpc.Status error = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The database engine type and provider of the source.
  DatabaseType source_database = 14;

  // The database engine type and provider of the destination.
  DatabaseType destination_database = 15;

  // Output only. If the migration job is completed, the time when it was
  // completed.
  google.protobuf.Timestamp end_time = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The conversion workspace used by the migration.
  ConversionWorkspaceInfo conversion_workspace = 18;

  // This field can be used to select the entities to migrate as part of
  // the migration job. It uses AIP-160 notation to select a subset of the
  // entities configured on the associated conversion-workspace. This field
  // should not be set on migration-jobs that are not associated with a
  // conversion workspace.
  string filter = 20;

  // The CMEK (customer-managed encryption key) fully qualified key name used
  // for the migration job.
  // This field supports all migration jobs types except for:
  // * Mysql to Mysql (use the cmek field in the cloudsql connection profile
  // instead).
  // * PostrgeSQL to PostgreSQL (use the cmek field in the cloudsql
  // connection profile instead).
  // * PostgreSQL to AlloyDB (use the kms_key_name field in the alloydb
  // connection profile instead).
  // Each Cloud CMEK key has the following format:
  // projects/[PROJECT]/locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]
  string cmek_key_name = 21;

  // Optional. Data dump parallelism settings used by the migration.
  // Currently applicable only for MySQL to Cloud SQL for MySQL migrations only.
  PerformanceConfig performance_config = 22
      [(google.api.field_behavior) = OPTIONAL];
}

// A conversion workspace's version.
message ConversionWorkspaceInfo {
  // The resource name (URI) of the conversion workspace.
  string name = 1;

  // The commit ID of the conversion workspace.
  string commit_id = 2;
}

// A connection profile definition.
message ConnectionProfile {
  option (google.api.resource) = {
    type: "datamigration.googleapis.com/ConnectionProfile"
    pattern: "projects/{project}/locations/{location}/connectionProfiles/{connection_profile}"
  };

  // The current connection profile state (e.g. DRAFT, READY, or FAILED).
  enum State {
    // The state of the connection profile is unknown.
    STATE_UNSPECIFIED = 0;

    // The connection profile is in draft mode and fully editable.
    DRAFT = 1;

    // The connection profile is being created.
    CREATING = 2;

    // The connection profile is ready.
    READY = 3;

    // The connection profile is being updated.
    UPDATING = 4;

    // The connection profile is being deleted.
    DELETING = 5;

    // The connection profile has been deleted.
    DELETED = 6;

    // The last action on the connection profile failed.
    FAILED = 7;
  }

  // The name of this connection profile resource in the form of
  // projects/{project}/locations/{location}/connectionProfiles/{connectionProfile}.
  string name = 1;

  // Output only. The timestamp when the resource was created.
  // A timestamp in RFC3339 UTC "Zulu" format, accurate to nanoseconds.
  // Example: "2014-10-02T15:01:23.045123456Z".
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was last updated.
  // A timestamp in RFC3339 UTC "Zulu" format, accurate to nanoseconds.
  // Example: "2014-10-02T15:01:23.045123456Z".
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The resource labels for connection profile to use to annotate any related
  // underlying resources such as Compute Engine VMs. An object containing a
  // list of "key": "value" pairs.
  //
  // Example: `{ "name": "wrench", "mass": "1.3kg", "count": "3" }`.
  map<string, string> labels = 4;

  // The current connection profile state (e.g. DRAFT, READY, or FAILED).
  State state = 5;

  // The connection profile display name.
  string display_name = 6;

  // The connection profile definition.
  oneof connection_profile {
    // A MySQL database connection profile.
    MySqlConnectionProfile mysql = 100;

    // A PostgreSQL database connection profile.
    PostgreSqlConnectionProfile postgresql = 101;

    // An Oracle database connection profile.
    OracleConnectionProfile oracle = 104;

    // A CloudSQL database connection profile.
    CloudSqlConnectionProfile cloudsql = 102;

    // An AlloyDB cluster connection profile.
    AlloyDbConnectionProfile alloydb = 105;
  }

  // Output only. The error details in case of state FAILED.
  google.rpc.Status error = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The database provider.
  DatabaseProvider provider = 8;
}

// Error message of a verification Migration job.
message MigrationJobVerificationError {
  // A general error code describing the type of error that occurred.
  enum ErrorCode {
    // An unknown error occurred
    ERROR_CODE_UNSPECIFIED = 0;

    // We failed to connect to one of the connection profile.
    CONNECTION_FAILURE = 1;

    // We failed to authenticate to one of the connection profile.
    AUTHENTICATION_FAILURE = 2;

    // One of the involved connection profiles has an invalid configuration.
    INVALID_CONNECTION_PROFILE_CONFIG = 3;

    // The versions of the source and the destination are incompatible.
    VERSION_INCOMPATIBILITY = 4;

    // The types of the source and the destination are incompatible.
    CONNECTION_PROFILE_TYPES_INCOMPATIBILITY = 5;

    // No pglogical extension installed on databases, applicable for postgres.
    NO_PGLOGICAL_INSTALLED = 7;

    // pglogical node already exists on databases, applicable for postgres.
    PGLOGICAL_NODE_ALREADY_EXISTS = 8;

    // The value of parameter wal_level is not set to logical.
    INVALID_WAL_LEVEL = 9;

    // The value of parameter shared_preload_libraries does not include
    // pglogical.
    INVALID_SHARED_PRELOAD_LIBRARY = 10;

    // The value of parameter max_replication_slots is not sufficient.
    INSUFFICIENT_MAX_REPLICATION_SLOTS = 11;

    // The value of parameter max_wal_senders is not sufficient.
    INSUFFICIENT_MAX_WAL_SENDERS = 12;

    // The value of parameter max_worker_processes is not sufficient.
    INSUFFICIENT_MAX_WORKER_PROCESSES = 13;

    // Extensions installed are either not supported or having unsupported
    // versions.
    UNSUPPORTED_EXTENSIONS = 14;

    // Unsupported migration type.
    UNSUPPORTED_MIGRATION_TYPE = 15;

    // Invalid RDS logical replication.
    INVALID_RDS_LOGICAL_REPLICATION = 16;

    // The gtid_mode is not supported, applicable for MySQL.
    UNSUPPORTED_GTID_MODE = 17;

    // The table definition is not support due to missing primary key or replica
    // identity.
    UNSUPPORTED_TABLE_DEFINITION = 18;

    // The definer is not supported.
    UNSUPPORTED_DEFINER = 19;

    // Migration is already running at the time of restart request.
    CANT_RESTART_RUNNING_MIGRATION = 21;

    // The source already has a replication setup.
    SOURCE_ALREADY_SETUP = 23;

    // The source has tables with limited support.
    // E.g. PostgreSQL tables without primary keys.
    TABLES_WITH_LIMITED_SUPPORT = 24;

    // The source uses an unsupported locale.
    UNSUPPORTED_DATABASE_LOCALE = 25;

    // The source uses an unsupported Foreign Data Wrapper configuration.
    UNSUPPORTED_DATABASE_FDW_CONFIG = 26;

    // There was an underlying RDBMS error.
    ERROR_RDBMS = 27;

    // The source DB size in Bytes exceeds a certain threshold. The migration
    // might require an increase of quota, or might not be supported.
    SOURCE_SIZE_EXCEEDS_THRESHOLD = 28;

    // The destination DB contains existing databases that are conflicting with
    // those in the source DB.
    EXISTING_CONFLICTING_DATABASES = 29;

    // Insufficient privilege to enable the parallelism configuration.
    PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE = 30;
  }

  // Output only. An instance of ErrorCode specifying the error that occurred.
  ErrorCode error_code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A formatted message with further details about the error and a
  // CTA.
  string error_message = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A specific detailed error message, if supplied by the engine.
  string error_detail_message = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The database engine types.
enum DatabaseEngine {
  // The source database engine of the migration job is unknown.
  DATABASE_ENGINE_UNSPECIFIED = 0;

  // The source engine is MySQL.
  MYSQL = 1;

  // The source engine is PostgreSQL.
  POSTGRESQL = 2;

  // The source engine is Oracle.
  ORACLE = 4;
}

// The database providers.
enum DatabaseProvider {
  // The database provider is unknown.
  DATABASE_PROVIDER_UNSPECIFIED = 0;

  // CloudSQL runs the database.
  CLOUDSQL = 1;

  // RDS runs the database.
  RDS = 2;

  // Amazon Aurora.
  AURORA = 3;

  // AlloyDB.
  ALLOYDB = 4;
}

// The PrivateConnection resource is used to establish private connectivity
// with the customer's network.
message PrivateConnection {
  option (google.api.resource) = {
    type: "datamigration.googleapis.com/PrivateConnection"
    pattern: "projects/{project}/locations/{location}/privateConnections/{private_connection}"
  };

  // Private Connection state.
  enum State {
    STATE_UNSPECIFIED = 0;

    // The private connection is in creation state - creating resources.
    CREATING = 1;

    // The private connection has been created with all of its resources.
    CREATED = 2;

    // The private connection creation has failed.
    FAILED = 3;

    // The private connection is being deleted.
    DELETING = 4;

    // Delete request has failed, resource is in invalid state.
    FAILED_TO_DELETE = 5;

    // The private connection has been deleted.
    DELETED = 6;
  }

  // The name of the resource.
  string name = 1;

  // Output only. The create time of the resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update time of the resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The resource labels for private connections to use to annotate any related
  // underlying resources such as Compute Engine VMs. An object containing a
  // list of "key": "value" pairs.
  //
  // Example: `{ "name": "wrench", "mass": "1.3kg", "count": "3" }`.
  map<string, string> labels = 4;

  // The private connection display name.
  string display_name = 5;

  // Output only. The state of the private connection.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The error details in case of state FAILED.
  google.rpc.Status error = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  oneof connectivity {
    // VPC peering configuration.
    VpcPeeringConfig vpc_peering_config = 100;
  }
}

// The VPC peering configuration is used to create VPC peering with the
// consumer's VPC.
message VpcPeeringConfig {
  // Required. Fully qualified name of the VPC that Database Migration Service
  // will peer to.
  string vpc_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/Networks"
    }
  ];

  // Required. A free subnet for peering. (CIDR of /29)
  string subnet = 2 [(google.api.field_behavior) = REQUIRED];
}
