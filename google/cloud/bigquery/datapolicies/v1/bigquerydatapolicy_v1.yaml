type: google.api.Service
config_version: 3
name: bigquerydatapolicy.googleapis.com
title: BigQuery Data Policy API

apis:
- name: google.cloud.bigquery.datapolicies.v1.DataPolicyService

documentation:
  summary: Allows users to manage BigQuery data policies.

backend:
  rules:
  - selector: 'google.cloud.bigquery.datapolicies.v1.DataPolicyService.*'
    deadline: 60.0

authentication:
  rules:
  - selector: 'google.cloud.bigquery.datapolicies.v1.DataPolicyService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
