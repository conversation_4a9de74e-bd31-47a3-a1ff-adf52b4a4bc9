# This build file includes a target for the Ruby wrapper library for
# google-cloud-bigquery-data_policies.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for bigquerydatapolicy.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "bigquerydatapolicy_ruby_wrapper",
    srcs = ["//google/cloud/bigquery/datapolicies/v1:datapolicies_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigquery-data_policies",
        "ruby-cloud-wrapper-of=v1:0.7;v1beta1:0.4",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery",
        "ruby-cloud-api-id=bigquerydatapolicy.googleapis.com",
        "ruby-cloud-api-shortname=bigquerydatapolicy",
    ],
    ruby_cloud_description = "The Data Policy Service provides APIs for managing the BigQuery label-policy bindings.",
    ruby_cloud_title = "BigQuery Data Policy Service V1beta1",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-datapolicies-ruby",
    deps = [
        ":bigquerydatapolicy_ruby_wrapper",
    ],
)
