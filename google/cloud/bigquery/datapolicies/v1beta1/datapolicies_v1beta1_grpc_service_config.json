{"methodConfig": [{"name": [{"service": "google.cloud.bigquery.datapolicies.v1beta1.DataPolicyService", "method": "CreateDataPolicy"}, {"service": "google.cloud.bigquery.datapolicies.v1beta1.DataPolicyService", "method": "UpdateDataPolicy"}, {"service": "google.cloud.bigquery.datapolicies.v1beta1.DataPolicyService", "method": "DeleteDataPolicy"}, {"service": "google.cloud.bigquery.datapolicies.v1beta1.DataPolicyService", "method": "GetDataPolicy"}, {"service": "google.cloud.bigquery.datapolicies.v1beta1.DataPolicyService", "method": "ListDataPolicies"}, {"service": "google.cloud.bigquery.datapolicies.v1beta1.DataPolicyService", "method": "GetIamPolicy"}, {"service": "google.cloud.bigquery.datapolicies.v1beta1.DataPolicyService", "method": "SetIamPolicy"}, {"service": "google.cloud.bigquery.datapolicies.v1beta1.DataPolicyService", "method": "TestIamPermissions"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}