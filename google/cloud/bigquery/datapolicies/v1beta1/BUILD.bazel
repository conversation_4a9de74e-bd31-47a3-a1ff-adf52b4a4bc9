# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "datapolicies_proto",
    srcs = [
        "datapolicy.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

proto_library_with_info(
    name = "datapolicies_proto_with_info",
    deps = [
        ":datapolicies_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "datapolicies_java_proto",
    deps = [":datapolicies_proto"],
)

java_grpc_library(
    name = "datapolicies_java_grpc",
    srcs = [":datapolicies_proto"],
    deps = [":datapolicies_java_proto"],
)

java_gapic_library(
    name = "datapolicies_java_gapic",
    srcs = [":datapolicies_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "datapolicies_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "bigquerydatapolicy_v1beta1.yaml",
    test_deps = [
        ":datapolicies_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":datapolicies_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "datapolicies_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.datapolicies.v1beta1.DataPolicyServiceClientHttpJsonTest",
        "com.google.cloud.bigquery.datapolicies.v1beta1.DataPolicyServiceClientTest",
    ],
    runtime_deps = [":datapolicies_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-datapolicies-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":datapolicies_java_gapic",
        ":datapolicies_java_grpc",
        ":datapolicies_java_proto",
        ":datapolicies_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "datapolicies_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/datapolicies/apiv1beta1/datapoliciespb",
    protos = [":datapolicies_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

go_gapic_library(
    name = "datapolicies_go_gapic",
    srcs = [":datapolicies_proto_with_info"],
    grpc_service_config = "datapolicies_v1beta1_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/datapolicies/apiv1beta1;datapolicies",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = False,
    service_yaml = "bigquerydatapolicy_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datapolicies_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-datapolicies-v1beta1-go",
    deps = [
        ":datapolicies_go_gapic",
        ":datapolicies_go_gapic_srcjar-metadata.srcjar",
        ":datapolicies_go_gapic_srcjar-snippets.srcjar",
        ":datapolicies_go_gapic_srcjar-test.srcjar",
        ":datapolicies_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "datapolicies_py_gapic",
    srcs = [":datapolicies_proto"],
    grpc_service_config = "datapolicies_v1beta1_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=bigquery_datapolicies",
    ],
    rest_numeric_enums = False,
    service_yaml = "bigquerydatapolicy_v1beta1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "datapolicies_py_gapic_test",
    srcs = [
        "datapolicies_py_gapic_pytest.py",
        "datapolicies_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":datapolicies_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-datapolicies-v1beta1-py",
    deps = [
        ":datapolicies_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "datapolicies_php_proto",
    deps = [":datapolicies_proto"],
)

php_gapic_library(
    name = "datapolicies_php_gapic",
    srcs = [":datapolicies_proto_with_info"],
    grpc_service_config = "datapolicies_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "bigquerydatapolicy_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":datapolicies_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-datapolicies-v1beta1-php",
    deps = [
        ":datapolicies_php_gapic",
        ":datapolicies_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "datapolicies_nodejs_gapic",
    package_name = "@google-cloud/bigquery-datapolicies",
    src = ":datapolicies_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "datapolicies_v1beta1_grpc_service_config.json",
    package = "google.cloud.bigquery.datapolicies.v1beta1",
    rest_numeric_enums = False,
    service_yaml = "bigquerydatapolicy_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-datapolicies-v1beta1-nodejs",
    deps = [
        ":datapolicies_nodejs_gapic",
        ":datapolicies_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "datapolicies_ruby_proto",
    deps = [":datapolicies_proto"],
)

ruby_grpc_library(
    name = "datapolicies_ruby_grpc",
    srcs = [":datapolicies_proto"],
    deps = [":datapolicies_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "datapolicies_ruby_gapic",
    srcs = [":datapolicies_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=bigquerydatapolicy.googleapis.com",
        "ruby-cloud-api-shortname=bigquerydatapolicy",
        "ruby-cloud-gem-name=google-cloud-bigquery-data_policies-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs",
    ],
    grpc_service_config = "datapolicies_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "The Data Policy Service provides APIs for managing the BigQuery label-policy bindings.",
    ruby_cloud_title = "BigQuery Data Policy Service V1beta1",
    service_yaml = "bigquerydatapolicy_v1beta1.yaml",
    deps = [
        ":datapolicies_ruby_grpc",
        ":datapolicies_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-datapolicies-v1beta1-ruby",
    deps = [
        ":datapolicies_ruby_gapic",
        ":datapolicies_ruby_grpc",
        ":datapolicies_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "datapolicies_csharp_proto",
    deps = [":datapolicies_proto"],
)

csharp_grpc_library(
    name = "datapolicies_csharp_grpc",
    srcs = [":datapolicies_proto"],
    deps = [":datapolicies_csharp_proto"],
)

csharp_gapic_library(
    name = "datapolicies_csharp_gapic",
    srcs = [":datapolicies_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "datapolicies_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "bigquerydatapolicy_v1beta1.yaml",
    deps = [
        ":datapolicies_csharp_grpc",
        ":datapolicies_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-datapolicies-v1beta1-csharp",
    deps = [
        ":datapolicies_csharp_gapic",
        ":datapolicies_csharp_grpc",
        ":datapolicies_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "datapolicies_cc_proto",
    deps = [":datapolicies_proto"],
)

cc_grpc_library(
    name = "datapolicies_cc_grpc",
    srcs = [":datapolicies_proto"],
    grpc_only = True,
    deps = [":datapolicies_cc_proto"],
)
