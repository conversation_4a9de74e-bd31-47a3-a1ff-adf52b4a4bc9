# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "bigquery_proto",
    srcs = [
        "biglake_config.proto",
        "clustering.proto",
        "data_format_options.proto",
        "dataset.proto",
        "dataset_reference.proto",
        "decimal_target_types.proto",
        "encryption_config.proto",
        "error.proto",
        "external_catalog_dataset_options.proto",
        "external_catalog_table_options.proto",
        "external_data_config.proto",
        "external_dataset_reference.proto",
        "file_set_specification_type.proto",
        "hive_partitioning.proto",
        "job.proto",
        "job_config.proto",
        "job_creation_reason.proto",
        "job_reference.proto",
        "job_stats.proto",
        "job_status.proto",
        "json_extension.proto",
        "location_metadata.proto",
        "map_target_type.proto",
        "model.proto",
        "model_reference.proto",
        "partitioning_definition.proto",
        "privacy_policy.proto",
        "project.proto",
        "query_parameter.proto",
        "range_partitioning.proto",
        "restriction_config.proto",
        "routine.proto",
        "routine_reference.proto",
        "row_access_policy.proto",
        "row_access_policy_reference.proto",
        "session_info.proto",
        "standard_sql.proto",
        "system_variable.proto",
        "table.proto",
        "table_constraints.proto",
        "table_reference.proto",
        "table_schema.proto",
        "time_partitioning.proto",
        "udf_resource.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "bigquery_proto_with_info",
    deps = [
        ":bigquery_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_proto_library",
)

java_proto_library(
    name = "bigquery_java_proto",
    deps = [":bigquery_proto"],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "bigquery_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/apiv2/bigquerypb",
    protos = [":bigquery_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "bigquery_go_gapic",
    srcs = [":bigquery_proto_with_info"],
    grpc_service_config = "bigquery_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/apiv2;bigquery",
    metadata = True,
    release_level = "alpha",
    rest_numeric_enums = False,
    service_yaml = "bigquery_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":bigquery_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-v2-go",
    deps = [
        ":bigquery_go_gapic",
        ":bigquery_go_gapic_srcjar-metadata.srcjar",
        ":bigquery_go_gapic_srcjar-snippets.srcjar",
        ":bigquery_go_gapic_srcjar-test.srcjar",
        ":bigquery_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "bigquery_py_gapic",
    srcs = [":bigquery_proto"],
    grpc_service_config = "bigquery_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigquery_v2.yaml",
    transport = "rest",
    deps = [
    ],
)

py_test(
    name = "bigquery_py_gapic_test",
    srcs = [
        "bigquery_py_gapic_pytest.py",
        "bigquery_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":bigquery_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-v2-py",
    deps = [
        ":bigquery_py_gapic",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "bigquery_nodejs_gapic",
    package_name = "@google-cloud/bigquery",
    src = ":bigquery_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "bigquery_grpc_service_config.json",
    package = "google.cloud.bigquery.v2",
    rest_numeric_enums = True,
    service_yaml = "bigquery_v2.yaml",
    transport = "rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-v2-nodejs",
    deps = [
        ":bigquery_nodejs_gapic",
        ":bigquery_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "bigquery_cc_proto",
    deps = [":bigquery_proto"],
)
