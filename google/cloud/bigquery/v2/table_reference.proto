// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "TableReferenceProto";
option java_package = "com.google.cloud.bigquery.v2";

message TableReference {
  // Required. The ID of the project containing this table.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the dataset containing this table.
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the table. The ID can contain Unicode characters in
  // category L (letter), M (mark), N (number), Pc (connector, including
  // underscore), Pd (dash), and Zs (space). For more information, see [General
  // Category](https://wikipedia.org/wiki/Unicode_character_property#General_Category).
  // The maximum length is 1,024 characters.  Certain operations allow suffixing
  // of the table ID with a partition decorator, such as
  // `sample_table$20190123`.
  string table_id = 3 [(google.api.field_behavior) = REQUIRED];
}
