// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";
import "google/cloud/bigquery/v2/table_reference.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "TableConstraintsProto";
option java_package = "com.google.cloud.bigquery.v2";

// Represents the primary key constraint on a table's columns.
message PrimaryKey {
  // Required. The columns that are composed of the primary key constraint.
  repeated string columns = 1 [(google.api.field_behavior) = REQUIRED];
}

// The pair of the foreign key column and primary key column.
message ColumnReference {
  // Required. The column that composes the foreign key.
  string referencing_column = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The column in the primary key that are referenced by the
  // referencing_column.
  string referenced_column = 2 [(google.api.field_behavior) = REQUIRED];
}

// Represents a foreign key constraint on a table's columns.
message ForeignKey {
  // Optional. Set only if the foreign key constraint is named.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The table that holds the primary key and is referenced by this
  // foreign key.
  TableReference referenced_table = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The columns that compose the foreign key.
  repeated ColumnReference column_references = 3
      [(google.api.field_behavior) = REQUIRED];
}

// The TableConstraints defines the primary key and foreign key.
message TableConstraints {
  // Optional. Represents a primary key constraint on a table's columns.
  // Present only if the table has a primary key.
  // The primary key is not enforced.
  PrimaryKey primary_key = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Present only if the table has a foreign key.
  // The foreign key is not enforced.
  repeated ForeignKey foreign_keys = 2 [(google.api.field_behavior) = OPTIONAL];
}
