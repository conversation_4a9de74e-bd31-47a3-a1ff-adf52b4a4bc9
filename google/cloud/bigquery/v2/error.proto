// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_package = "com.google.cloud.bigquery.v2";

// Error details.
message ErrorProto {
  // A short error code that summarizes the error.
  string reason = 1;

  // Specifies where the error occurred, if present.
  string location = 2;

  // Debugging information. This property is internal to Google and should not
  // be used.
  string debug_info = 3;

  // A human-readable description of the error.
  string message = 4;
}
