// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "RestrictionConfigProto";
option java_package = "com.google.cloud.bigquery.v2";

message RestrictionConfig {
  // RestrictionType specifies the type of dataset/table restriction.
  enum RestrictionType {
    // Should never be used.
    RESTRICTION_TYPE_UNSPECIFIED = 0;

    // Restrict data egress. See [Data
    // egress](https://cloud.google.com/bigquery/docs/analytics-hub-introduction#data_egress)
    // for more details.
    RESTRICTED_DATA_EGRESS = 1;
  }

  // Output only. Specifies the type of dataset/table restriction.
  RestrictionType type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}
