// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "StandardSqlProto";
option java_package = "com.google.cloud.bigquery.v2";

// The data type of a variable such as a function argument.
// Examples include:
//
// * INT64: `{"typeKind": "INT64"}`
//
// * ARRAY<STRING>:
//
//     {
//       "typeKind": "ARRAY",
//       "arrayElementType": {"typeKind": "STRING"}
//     }
//
// * STRUCT<x STRING, y ARRAY<DATE>>:
//
//     {
//       "typeKind": "STRUCT",
//       "structType":
//       {
//         "fields":
//         [
//           {
//             "name": "x",
//             "type": {"typeKind": "STRING"}
//           },
//           {
//             "name": "y",
//             "type":
//             {
//               "typeKind": "ARRAY",
//               "arrayElementType": {"typeKind": "DATE"}
//             }
//           }
//         ]
//       }
//     }
//
// * RANGE<DATE>:
//
//     {
//       "typeKind": "RANGE",
//       "rangeElementType": {"typeKind": "DATE"}
//     }
message StandardSqlDataType {
  // The kind of the datatype.
  enum TypeKind {
    // Invalid type.
    TYPE_KIND_UNSPECIFIED = 0;

    // Encoded as a string in decimal format.
    INT64 = 2;

    // Encoded as a boolean "false" or "true".
    BOOL = 5;

    // Encoded as a number, or string "NaN", "Infinity" or "-Infinity".
    FLOAT64 = 7;

    // Encoded as a string value.
    STRING = 8;

    // Encoded as a base64 string per RFC 4648, section 4.
    BYTES = 9;

    // Encoded as an RFC 3339 timestamp with mandatory "Z" time zone string:
    // 1985-04-12T23:20:50.52Z
    TIMESTAMP = 19;

    // Encoded as RFC 3339 full-date format string: 1985-04-12
    DATE = 10;

    // Encoded as RFC 3339 partial-time format string: 23:20:50.52
    TIME = 20;

    // Encoded as RFC 3339 full-date "T" partial-time: 1985-04-12T23:20:50.52
    DATETIME = 21;

    // Encoded as fully qualified 3 part: 0-5 15 2:30:45.6
    INTERVAL = 26;

    // Encoded as WKT
    GEOGRAPHY = 22;

    // Encoded as a decimal string.
    NUMERIC = 23;

    // Encoded as a decimal string.
    BIGNUMERIC = 24;

    // Encoded as a string.
    JSON = 25;

    // Encoded as a list with types matching Type.array_type.
    ARRAY = 16;

    // Encoded as a list with fields of type Type.struct_type[i]. List is used
    // because a JSON object cannot have duplicate field names.
    STRUCT = 17;

    // Encoded as a pair with types matching range_element_type. Pairs must
    // begin with "[", end with ")", and be separated by ", ".
    RANGE = 29;
  }

  // Required. The top level type of this field.
  // Can be any GoogleSQL data type (e.g., "INT64", "DATE", "ARRAY").
  TypeKind type_kind = 1 [(google.api.field_behavior) = REQUIRED];

  // For complex types, the sub type information.
  oneof sub_type {
    // The type of the array's elements, if type_kind = "ARRAY".
    StandardSqlDataType array_element_type = 2;

    // The fields of this struct, in order, if type_kind = "STRUCT".
    StandardSqlStructType struct_type = 3;

    // The type of the range's elements, if type_kind = "RANGE".
    StandardSqlDataType range_element_type = 4;
  }
}

// A field or a column.
message StandardSqlField {
  // Optional. The name of this field. Can be absent for struct fields.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The type of this parameter. Absent if not explicitly
  // specified (e.g., CREATE FUNCTION statement can omit the return type;
  // in this case the output parameter does not have this "type" field).
  StandardSqlDataType type = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The representation of a SQL STRUCT type.
message StandardSqlStructType {
  // Fields within the struct.
  repeated StandardSqlField fields = 1;
}

// A table type
message StandardSqlTableType {
  // The columns in this table type
  repeated StandardSqlField columns = 1;
}
