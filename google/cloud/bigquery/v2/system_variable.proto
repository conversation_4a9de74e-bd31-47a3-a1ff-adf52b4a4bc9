// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";
import "google/cloud/bigquery/v2/standard_sql.proto";
import "google/protobuf/struct.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "SystemVariableProto";
option java_package = "com.google.cloud.bigquery.v2";

// System variables given to a query.
message SystemVariables {
  // Output only. Data type for each system variable.
  map<string, StandardSqlDataType> types = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Value for each system variable.
  google.protobuf.Struct values = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
