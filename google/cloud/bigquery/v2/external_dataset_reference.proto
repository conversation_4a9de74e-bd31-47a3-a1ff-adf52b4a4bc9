// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "ExternalDatasetReferenceProto";
option java_package = "com.google.cloud.bigquery.v2";
option (google.api.resource_definition) = {
  type: "bigqueryconnection.googleapis.com/Connection"
  pattern: "projects/{project}/locations/{location}/connections/{connection}"
};

// Configures the access a dataset defined in an external metadata storage.
message ExternalDatasetReference {
  // Required. External source that backs this dataset.
  string external_source = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The connection id that is used to access the external_source.
  //
  // Format:
  //   projects/{project_id}/locations/{location_id}/connections/{connection_id}
  string connection = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryconnection.googleapis.com/Connection"
    }
  ];
}
