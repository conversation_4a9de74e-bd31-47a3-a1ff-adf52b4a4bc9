// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "QueryParameterProto";
option java_package = "com.google.cloud.bigquery.v2";

// The type of a struct parameter.
message QueryParameterStructType {
  // Optional. The name of this field.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The type of this field.
  QueryParameterType type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Human-oriented description of the field.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The type of a query parameter.
message QueryParameterType {
  // Required. The top level type of this field.
  string type = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The type of the array's elements, if this is an array.
  QueryParameterType array_type = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The types of the fields of this struct, in order, if this is a
  // struct.
  repeated QueryParameterStructType struct_types = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The element type of the range, if this is a range.
  QueryParameterType range_element_type = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Represents the value of a range.
message RangeValue {
  // Optional. The start value of the range. A missing value represents an
  // unbounded start.
  QueryParameterValue start = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The end value of the range. A missing value represents an
  // unbounded end.
  QueryParameterValue end = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The value of a query parameter.
message QueryParameterValue {
  // Optional. The value of this value, if a simple scalar type.
  google.protobuf.StringValue value = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The array values, if this is an array type.
  repeated QueryParameterValue array_values = 2
      [(google.api.field_behavior) = OPTIONAL];

  // The struct field values.
  map<string, QueryParameterValue> struct_values = 3;

  // Optional. The range value, if this is a range type.
  RangeValue range_value = 6 [(google.api.field_behavior) = OPTIONAL];

  // This field should not be used.
  repeated google.protobuf.Value alt_struct_values = 5;
}

// A parameter given to a query.
message QueryParameter {
  // Optional. If unset, this is a positional parameter. Otherwise, should be
  // unique within a query.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The type of this parameter.
  QueryParameterType parameter_type = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The value of this parameter.
  QueryParameterValue parameter_value = 3
      [(google.api.field_behavior) = REQUIRED];
}
