type: google.api.Service
config_version: 3
name: bigquery.googleapis.com
title: BigQuery API

apis:
- name: google.cloud.bigquery.v2.DatasetService
- name: google.cloud.bigquery.v2.JobService
- name: google.cloud.bigquery.v2.ModelService
- name: google.cloud.bigquery.v2.ProjectService
- name: google.cloud.bigquery.v2.RoutineService
- name: google.cloud.bigquery.v2.RowAccessPolicyService
- name: google.cloud.bigquery.v2.TableService

types:
- name: google.cloud.bigquery.v2.LocationMetadata

documentation:
  summary: 'A data platform for customers to create, manage, share and query data.'
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    post: '/bigquery/v2/{resource=projects/*/datasets/*}:getIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/bigquery/v2/{resource=projects/*/datasets/*/tables/*}:getIamPolicy'
      body: '*'
    - post: '/bigquery/v2/{resource=projects/*/datasets/*/tables/*/rowAccessPolicies/*}:getIamPolicy'
      body: '*'
    - post: '/bigquery/v2/{resource=projects/*/datasets/*/routines/*}:getIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/bigquery/v2/{resource=projects/*/datasets/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/bigquery/v2/{resource=projects/*/datasets/*/tables/*}:setIamPolicy'
      body: '*'
    - post: '/bigquery/v2/{resource=projects/*/datasets/*/routines/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/bigquery/v2/{resource=projects/*/datasets/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/bigquery/v2/{resource=projects/*/datasets/*/tables/*}:testIamPermissions'
      body: '*'
    - post: '/bigquery/v2/{resource=projects/*/datasets/*/tables/*/rowAccessPolicies/*}:testIamPermissions'
      body: '*'

authentication:
  rules:
  - selector: 'google.cloud.bigquery.v2.DatasetService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.bigquery.v2.DatasetService.GetDataset
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.bigquery.v2.DatasetService.ListDatasets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.cloud.bigquery.v2.JobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.bigquery.v2.JobService.CancelJob
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.bigquery.v2.JobService.DeleteJob
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.bigquery.v2.JobService.InsertJob
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/devstorage.full_control,
        https://www.googleapis.com/auth/devstorage.read_only,
        https://www.googleapis.com/auth/devstorage.read_write
  - selector: google.cloud.bigquery.v2.ModelService.DeleteModel
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.bigquery.v2.ModelService.GetModel
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.bigquery.v2.ModelService.ListModels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.bigquery.v2.ModelService.PatchModel
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.bigquery.v2.ProjectService.GetServiceAccount
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.cloud.bigquery.v2.RoutineService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.bigquery.v2.RoutineService.GetRoutine
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.bigquery.v2.RoutineService.ListRoutines
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.bigquery.v2.RowAccessPolicyService.ListRowAccessPolicies
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.cloud.bigquery.v2.TableService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.bigquery.v2.TableService.GetTable
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.bigquery.v2.TableService.ListTables
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
