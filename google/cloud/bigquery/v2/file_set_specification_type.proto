// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "FileSetSpecificationTypeProto";
option java_package = "com.google.cloud.bigquery.v2";

// This enum defines how to interpret source URIs for load jobs and external
// tables.
enum FileSetSpecType {
  // This option expands source URIs by listing files from the object store. It
  // is the default behavior if FileSetSpecType is not set.
  FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH = 0;

  // This option indicates that the provided URIs are newline-delimited manifest
  // files, with one URI per line. Wildcard URIs are not supported.
  FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST = 1;
}
