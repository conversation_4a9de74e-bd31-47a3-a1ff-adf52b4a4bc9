// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "ModelReferenceProto";
option java_package = "com.google.cloud.bigquery.v2";

// Id path of a model.
message ModelReference {
  // Required. The ID of the project containing this model.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the dataset containing this model.
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the model. The ID must contain only
  // letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum
  // length is 1,024 characters.
  string model_id = 3 [(google.api.field_behavior) = REQUIRED];
}
