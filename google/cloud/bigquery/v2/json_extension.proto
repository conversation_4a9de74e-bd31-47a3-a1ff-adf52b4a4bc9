// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "JsonExtensionProto";
option java_package = "com.google.cloud.bigquery.v2";

// Used to indicate that a JSON variant, rather than normal JSON, is being used
// as the source_format. This should only be used in combination with the
// JSON source format.
enum JsonExtension {
  // The default if provided value is not one included in the enum, or the value
  // is not specified. The source formate is parsed without any modification.
  JSON_EXTENSION_UNSPECIFIED = 0;

  // Use GeoJSON variant of JSON. See https://tools.ietf.org/html/rfc7946.
  GEOJSON = 1;
}
