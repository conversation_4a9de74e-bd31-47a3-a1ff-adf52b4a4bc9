// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "BigLakeConfigProto";
option java_package = "com.google.cloud.bigquery.v2";

// Configuration for BigLake managed tables.
message BigLakeConfiguration {
  // Supported file formats for BigLake tables.
  enum FileFormat {
    // Default Value.
    FILE_FORMAT_UNSPECIFIED = 0;

    // Apache Parquet format.
    PARQUET = 1;
  }

  // Supported table formats for BigLake tables.
  enum TableFormat {
    // Default Value.
    TABLE_FORMAT_UNSPECIFIED = 0;

    // Apache Iceberg format.
    ICEBERG = 1;
  }

  // Optional. The connection specifying the credentials to be used to read and
  // write to external storage, such as Cloud Storage. The connection_id can
  // have the form `{project}.{location}.{connection_id}` or
  // `projects/{project}/locations/{location}/connections/{connection_id}".
  string connection_id = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The fully qualified location prefix of the external folder where
  // table data is stored. The '*' wildcard character is not allowed. The URI
  // should be in the format `gs://bucket/path_to_table/`
  string storage_uri = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The file format the table data is stored in.
  FileFormat file_format = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The table format the metadata only snapshots are stored in.
  TableFormat table_format = 4 [(google.api.field_behavior) = OPTIONAL];
}
