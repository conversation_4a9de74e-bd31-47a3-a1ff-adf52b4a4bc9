// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "RangePartitioningProto";
option java_package = "com.google.cloud.bigquery.v2";

message RangePartitioning {
  // Defines the ranges for range partitioning.
  message Range {
    // Required. The start of range partitioning, inclusive. This field is an
    // INT64 value represented as a string.
    string start = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The end of range partitioning, exclusive. This field is an
    // INT64 value represented as a string.
    string end = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. The width of each interval. This field is an INT64 value
    // represented as a string.
    string interval = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. The name of the column to partition the table on. It must be a
  // top-level, INT64 column whose mode is NULLABLE or REQUIRED.
  string field = 1 [(google.api.field_behavior) = REQUIRED];

  // Defines the ranges for range partitioning.
  Range range = 2;
}
