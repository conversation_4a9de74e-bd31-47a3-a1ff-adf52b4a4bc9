// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "ExternalCatalogTableOptionsProto";
option java_package = "com.google.cloud.bigquery.v2";

// Metadata about open source compatible table. The fields contained in
// these options correspond to hive metastore's table level properties.
message ExternalCatalogTableOptions {
  // Optional. A map of key value pairs defining the parameters and properties
  // of the open source table. Corresponds with hive meta store table
  // parameters. Maximum size of 4Mib.
  map<string, string> parameters = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A storage descriptor containing information about the physical
  // storage of this table.
  StorageDescriptor storage_descriptor = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The connection specifying the credentials to be used to read
  // external storage, such as Azure Blob, Cloud Storage, or S3. The connection
  // is needed to read the open source table from BigQuery Engine. The
  // connection_id can have the form
  // `<project_id>.<location_id>.<connection_id>` or
  // `projects/<project_id>/locations/<location_id>/connections/<connection_id>`.
  string connection_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Contains information about how a table's data is stored and accessed by open
// source query engines.
message StorageDescriptor {
  // Optional. The physical location of the table
  // (e.g. `gs://spark-dataproc-data/pangea-data/case_sensitive/` or
  // `gs://spark-dataproc-data/pangea-data/*`).
  // The maximum length is 2056 bytes.
  string location_uri = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the fully qualified class name of the InputFormat
  // (e.g. "org.apache.hadoop.hive.ql.io.orc.OrcInputFormat").
  // The maximum length is 128 characters.
  string input_format = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the fully qualified class name of the OutputFormat
  // (e.g. "org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat").
  // The maximum length is 128 characters.
  string output_format = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Serializer and deserializer information.
  SerDeInfo serde_info = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Serializer and deserializer information.
message SerDeInfo {
  // Optional. Name of the SerDe.
  // The maximum length is 256 characters.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Specifies a fully-qualified class name of the serialization
  // library that is responsible for the translation of data between table
  // representation and the underlying low-level input and output format
  // structures. The maximum length is 256 characters.
  string serialization_library = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Key-value pairs that define the initialization parameters for the
  // serialization library.
  // Maximum size 10 Kib.
  map<string, string> parameters = 3 [(google.api.field_behavior) = OPTIONAL];
}
