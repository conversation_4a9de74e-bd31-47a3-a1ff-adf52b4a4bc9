// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "EncryptionConfigProto";
option java_package = "com.google.cloud.bigquery.v2";

// Configuration for Cloud KMS encryption settings.
message EncryptionConfiguration {
  // Optional. Describes the Cloud KMS encryption key that will be used to
  // protect destination BigQuery table. The BigQuery Service Account associated
  // with your project requires access to this encryption key.
  google.protobuf.StringValue kms_key_name = 1
      [(google.api.field_behavior) = OPTIONAL];
}
