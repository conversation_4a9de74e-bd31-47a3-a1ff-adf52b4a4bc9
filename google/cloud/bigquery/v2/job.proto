// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/bigquery/v2/data_format_options.proto";
import "google/cloud/bigquery/v2/dataset_reference.proto";
import "google/cloud/bigquery/v2/error.proto";
import "google/cloud/bigquery/v2/job_config.proto";
import "google/cloud/bigquery/v2/job_creation_reason.proto";
import "google/cloud/bigquery/v2/job_reference.proto";
import "google/cloud/bigquery/v2/job_stats.proto";
import "google/cloud/bigquery/v2/job_status.proto";
import "google/cloud/bigquery/v2/query_parameter.proto";
import "google/cloud/bigquery/v2/session_info.proto";
import "google/cloud/bigquery/v2/table_schema.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "JobProto";
option java_package = "com.google.cloud.bigquery.v2";

service JobService {
  option (google.api.default_host) = "bigquery.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only,"
      "https://www.googleapis.com/auth/devstorage.full_control,"
      "https://www.googleapis.com/auth/devstorage.read_only,"
      "https://www.googleapis.com/auth/devstorage.read_write";

  // Requests that a job be cancelled. This call will return immediately, and
  // the client will need to poll for the job status to see if the cancel
  // completed successfully. Cancelled jobs may still incur costs.
  rpc CancelJob(CancelJobRequest) returns (JobCancelResponse) {
    option (google.api.http) = {
      post: "/bigquery/v2/projects/{project_id=*}/jobs/{job_id=*}/cancel"
    };
  }

  // Returns information about a specific job. Job information is available for
  // a six month period after creation. Requires that you're the person who ran
  // the job, or have the Is Owner project role.
  rpc GetJob(GetJobRequest) returns (Job) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/jobs/{job_id=*}"
    };
  }

  // Starts a new asynchronous job.
  //
  // This API has two different kinds of endpoint URIs, as this method supports
  // a variety of use cases.
  //
  // * The *Metadata* URI is used for most interactions, as it accepts the job
  //   configuration directly.
  // * The *Upload* URI is ONLY for the case when you're sending both a load job
  //   configuration and a data stream together.  In this case, the Upload URI
  //   accepts the job configuration and the data as two distinct multipart MIME
  //   parts.
  rpc InsertJob(InsertJobRequest) returns (Job) {
    option (google.api.http) = {
      post: "/bigquery/v2/projects/{project_id=*}/jobs"
      body: "job"
    };
  }

  // Requests the deletion of the metadata of a job. This call returns when the
  // job's metadata is deleted.
  rpc DeleteJob(DeleteJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/bigquery/v2/projects/{project_id=*}/jobs/{job_id=*}/delete"
    };
  }

  // Lists all jobs that you started in the specified project. Job information
  // is available for a six month period after creation. The job list is sorted
  // in reverse chronological order, by job creation time. Requires the Can View
  // project role, or the Is Owner project role if you set the allUsers
  // property.
  rpc ListJobs(ListJobsRequest) returns (JobList) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/jobs"
    };
  }

  // RPC to get the results of a query job.
  rpc GetQueryResults(GetQueryResultsRequest)
      returns (GetQueryResultsResponse) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/queries/{job_id=*}"
    };
  }

  // Runs a BigQuery SQL query synchronously and returns query results if the
  // query completes within a specified timeout.
  rpc Query(PostQueryRequest) returns (QueryResponse) {
    option (google.api.http) = {
      post: "/bigquery/v2/projects/{project_id=*}/queries"
      body: "query_request"
    };
  }
}

message Job {
  // Output only. The type of the resource.
  string kind = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A hash of this resource.
  string etag = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Opaque ID field of the job.
  string id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A URL that can be used to access the resource again.
  string self_link = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the user who ran the job.
  string user_email = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Describes the job configuration.
  JobConfiguration configuration = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Reference describing the unique-per-user name of the job.
  JobReference job_reference = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Information about the job, including starting time and ending
  // time of the job.
  JobStatistics statistics = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of this job. Examine this value when polling an
  // asynchronous job to see if the job is complete.
  JobStatus status = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Full-projection-only] String representation of identity of
  // requesting party. Populated for both first- and third-party identities.
  // Only present for APIs that support third-party identities.
  string principal_subject = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The reason why a Job was created.
  // [Preview](https://cloud.google.com/products/#product-launch-stages)
  JobCreationReason job_creation_reason = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Describes format of a jobs cancellation request.
message CancelJobRequest {
  // Required. Project ID of the job to cancel
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Job ID of the job to cancel
  string job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The geographic location of the job. You must specify the location to run
  // the job for the following scenarios:
  //
  // * If the location to run a job is not in the `us` or
  //   the `eu` multi-regional location
  // * If the job's location is in a single region (for example,
  //   `us-central1`)
  //
  // For more information, see
  // https://cloud.google.com/bigquery/docs/locations#specifying_your_location.
  string location = 3;
}

// Describes format of a jobs cancellation response.
message JobCancelResponse {
  // The resource type of the response.
  string kind = 1;

  // The final state of the job.
  Job job = 2;
}

// Describes format of a jobs get request.
message GetJobRequest {
  // Required. Project ID of the requested job.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Job ID of the requested job.
  string job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The geographic location of the job. You must specify the location to run
  // the job for the following scenarios:
  //
  // * If the location to run a job is not in the `us` or
  //   the `eu` multi-regional location
  // * If the job's location is in a single region (for example,
  //   `us-central1`)
  //
  // For more information, see
  // https://cloud.google.com/bigquery/docs/locations#specifying_your_location.
  string location = 3;
}

// Describes format of a job insertion request.
message InsertJobRequest {
  // Project ID of project that will be billed for the job.
  string project_id = 1;

  // Jobs resource to insert.
  Job job = 3;
}

// Describes the format of a jobs deletion request.
message DeleteJobRequest {
  // Required. Project ID of the job for which metadata is to be deleted.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Job ID of the job for which metadata is to be deleted. If this is
  // a parent job which has child jobs, the metadata from all child jobs will be
  // deleted as well. Direct deletion of the metadata of child jobs is not
  // allowed.
  string job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The geographic location of the job. Required.
  // See details at:
  // https://cloud.google.com/bigquery/docs/locations#specifying_your_location.
  string location = 3;
}

// Describes the format of the list jobs request.
message ListJobsRequest {
  // Projection is used to control what job information is returned.
  enum Projection {
    option allow_alias = true;

    // Does not include the job configuration
    minimal = 0;

    // Does not include the job configuration
    MINIMAL = 0;

    // Includes all job data
    full = 1;

    // Includes all job data
    FULL = 1;
  }

  // StateFilter allows filtration by job execution state.
  enum StateFilter {
    option allow_alias = true;

    // Finished jobs
    done = 0;

    // Finished jobs
    DONE = 0;

    // Pending jobs
    pending = 1;

    // Pending jobs
    PENDING = 1;

    // Running jobs
    running = 2;

    // Running jobs.
    RUNNING = 2;
  }

  // Project ID of the jobs to list.
  string project_id = 1;

  // Whether to display jobs owned by all users in the project. Default False.
  bool all_users = 2;

  // The maximum number of results to return in a single response page.
  // Leverage the page tokens to iterate through the entire collection.
  google.protobuf.Int32Value max_results = 3;

  // Min value for job creation time, in milliseconds since the POSIX epoch.
  // If set, only jobs created after or at this timestamp are returned.
  uint64 min_creation_time = 4;

  // Max value for job creation time, in milliseconds since the POSIX epoch.
  // If set, only jobs created before or at this timestamp are returned.
  google.protobuf.UInt64Value max_creation_time = 5;

  // Page token, returned by a previous call, to request the next page of
  // results.
  string page_token = 6;

  // Restrict information returned to a set of selected fields
  Projection projection = 7;

  // Filter for job state
  repeated StateFilter state_filter = 8;

  // If set, show only child jobs of the specified parent.  Otherwise, show all
  // top-level jobs.
  string parent_job_id = 9;
}

// ListFormatJob is a partial projection of job information returned as part
// of a jobs.list response.
message ListFormatJob {
  // Unique opaque ID of the job.
  string id = 1;

  // The resource type.
  string kind = 2;

  // Unique opaque ID of the job.
  JobReference job_reference = 3;

  // Running state of the job. When the state is DONE, errorResult can be
  // checked to determine whether the job succeeded or failed.
  string state = 4;

  // A result object that will be present only if the job has failed.
  ErrorProto error_result = 5;

  // Output only. Information about the job, including starting time and ending
  // time of the job.
  JobStatistics statistics = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Describes the job configuration.
  JobConfiguration configuration = 7 [(google.api.field_behavior) = REQUIRED];

  // [Full-projection-only] Describes the status of this job.
  JobStatus status = 8;

  // [Full-projection-only] Email address of the user who ran the job.
  string user_email = 9;

  // [Full-projection-only] String representation of identity of requesting
  // party. Populated for both first- and third-party identities. Only present
  // for APIs that support third-party identities.
  string principal_subject = 10;
}

// JobList is the response format for a jobs.list call.
message JobList {
  // A hash of this page of results.
  string etag = 1;

  // The resource type of the response.
  string kind = 2;

  // A token to request the next page of results.
  string next_page_token = 3;

  // List of jobs that were requested.
  repeated ListFormatJob jobs = 4;

  // A list of skipped locations that were unreachable. For more information
  // about BigQuery locations, see:
  // https://cloud.google.com/bigquery/docs/locations. Example: "europe-west5"
  repeated string unreachable = 5;
}

// Request object of GetQueryResults.
message GetQueryResultsRequest {
  // Required. Project ID of the query job.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Job ID of the query job.
  string job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Zero-based index of the starting row.
  google.protobuf.UInt64Value start_index = 3;

  // Page token, returned by a previous call, to request the next page of
  // results.
  string page_token = 4;

  // Maximum number of results to read.
  google.protobuf.UInt32Value max_results = 5;

  // Optional: Specifies the maximum amount of time, in milliseconds, that the
  // client is willing to wait for the query to complete. By default, this limit
  // is 10 seconds (10,000 milliseconds). If the query is complete, the
  // jobComplete field in the response is true. If the query has not yet
  // completed, jobComplete is false.
  //
  // You can request a longer timeout period in the timeoutMs field.  However,
  // the call is not guaranteed to wait for the specified timeout; it typically
  // returns after around 200 seconds (200,000 milliseconds), even if the query
  // is not complete.
  //
  // If jobComplete is false, you can continue to wait for the query to complete
  // by calling the getQueryResults method until the jobComplete field in the
  // getQueryResults response is true.
  google.protobuf.UInt32Value timeout_ms = 6;

  // The geographic location of the job. You must specify the location to run
  // the job for the following scenarios:
  //
  // * If the location to run a job is not in the `us` or
  //   the `eu` multi-regional location
  // * If the job's location is in a single region (for example,
  // `us-central1`)
  //
  // For more information, see
  // https://cloud.google.com/bigquery/docs/locations#specifying_your_location.
  string location = 7;

  // Optional. Output format adjustments.
  DataFormatOptions format_options = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Response object of GetQueryResults.
message GetQueryResultsResponse {
  // The resource type of the response.
  string kind = 1;

  // A hash of this response.
  string etag = 2;

  // The schema of the results. Present only when the query completes
  // successfully.
  TableSchema schema = 3;

  // Reference to the BigQuery Job that was created to run the query. This field
  // will be present even if the original request timed out, in which case
  // GetQueryResults can be used to read the results once the query has
  // completed. Since this API only returns the first page of results,
  // subsequent pages can be fetched via the same mechanism (GetQueryResults).
  JobReference job_reference = 4;

  // The total number of rows in the complete query result set, which can be
  // more than the number of rows in this single page of results. Present only
  // when the query completes successfully.
  google.protobuf.UInt64Value total_rows = 5;

  // A token used for paging results.  When this token is non-empty, it
  // indicates additional results are available.
  string page_token = 6;

  // An object with as many results as can be contained within the maximum
  // permitted reply size. To get any additional rows, you can call
  // GetQueryResults and specify the jobReference returned above. Present only
  // when the query completes successfully.
  //
  // The REST-based representation of this data leverages a series of
  // JSON f,v objects for indicating fields and values.
  repeated google.protobuf.Struct rows = 7;

  // The total number of bytes processed for this query.
  google.protobuf.Int64Value total_bytes_processed = 8;

  // Whether the query has completed or not. If rows or totalRows are present,
  // this will always be true. If this is false, totalRows will not be
  // available.
  google.protobuf.BoolValue job_complete = 9;

  // Output only. The first errors or warnings encountered during the running
  // of the job. The final message includes the number of errors that caused the
  // process to stop. Errors here do not necessarily mean that the job has
  // completed or was unsuccessful. For more information about error messages,
  // see [Error
  // messages](https://cloud.google.com/bigquery/docs/error-messages).
  repeated ErrorProto errors = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether the query result was fetched from the query cache.
  google.protobuf.BoolValue cache_hit = 11;

  // Output only. The number of rows affected by a DML statement. Present only
  // for DML statements INSERT, UPDATE or DELETE.
  google.protobuf.Int64Value num_dml_affected_rows = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request format for the query request.
message PostQueryRequest {
  // Required. Project ID of the query request.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // The query request body.
  QueryRequest query_request = 2;
}

// Describes the format of the jobs.query request.
message QueryRequest {
  // Job Creation Mode provides different options on job creation.
  enum JobCreationMode {
    // If unspecified JOB_CREATION_REQUIRED is the default.
    JOB_CREATION_MODE_UNSPECIFIED = 0;

    // Default. Job creation is always required.
    JOB_CREATION_REQUIRED = 1;

    // Job creation is optional. Returning immediate results is prioritized.
    // BigQuery will automatically determine if a Job needs to be created.
    // The conditions under which BigQuery can decide to not create a Job are
    // subject to change. If Job creation is required, JOB_CREATION_REQUIRED
    // mode should be used, which is the default.
    JOB_CREATION_OPTIONAL = 2;
  }

  // The resource type of the request.
  string kind = 2;

  // Required. A query string to execute, using Google Standard SQL or legacy
  // SQL syntax. Example: "SELECT COUNT(f1) FROM
  // myProjectId.myDatasetId.myTableId".
  string query = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of rows of data to return per page of
  // results. Setting this flag to a small value such as 1000 and then paging
  // through results might improve reliability when the query result set is
  // large. In addition to this limit, responses are also limited to 10 MB. By
  // default, there is no maximum row count, and only the byte limit applies.
  google.protobuf.UInt32Value max_results = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the default datasetId and projectId to assume for any
  // unqualified table names in the query. If not set, all table names in the
  // query string must be qualified in the format 'datasetId.tableId'.
  DatasetReference default_dataset = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional: Specifies the maximum amount of time, in milliseconds,
  // that the client is willing to wait for the query to complete. By default,
  // this limit is 10 seconds (10,000 milliseconds). If the query is complete,
  // the jobComplete field in the response is true. If the query has not yet
  // completed, jobComplete is false.
  //
  // You can request a longer timeout period in the timeoutMs field.  However,
  // the call is not guaranteed to wait for the specified timeout; it typically
  // returns after around 200 seconds (200,000 milliseconds), even if the query
  // is not complete.
  //
  // If jobComplete is false, you can continue to wait for the query to complete
  // by calling the getQueryResults method until the jobComplete field in the
  // getQueryResults response is true.
  google.protobuf.UInt32Value timeout_ms = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, BigQuery doesn't run the job. Instead, if the
  // query is valid, BigQuery returns statistics about the job such as how many
  // bytes would be processed. If the query is invalid, an error returns. The
  // default value is false.
  bool dry_run = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether to look for the result in the query cache. The query
  // cache is a best-effort cache that will be flushed whenever tables in the
  // query are modified. The default value is true.
  google.protobuf.BoolValue use_query_cache = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Specifies whether to use BigQuery's legacy SQL dialect for this query. The
  // default value is true. If set to false, the query will use BigQuery's
  // GoogleSQL: https://cloud.google.com/bigquery/sql-reference/ When
  // useLegacySql is set to false, the value of flattenResults is ignored; query
  // will be run as if flattenResults is false.
  google.protobuf.BoolValue use_legacy_sql = 10;

  // GoogleSQL only. Set to POSITIONAL to use positional (?) query parameters
  // or to NAMED to use named (@myparam) query parameters in this query.
  string parameter_mode = 11;

  // Query parameters for GoogleSQL queries.
  repeated QueryParameter query_parameters = 12;

  // The geographic location where the job should run. See details at
  // https://cloud.google.com/bigquery/docs/locations#specifying_your_location.
  string location = 13;

  // Optional. Output format adjustments.
  DataFormatOptions format_options = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Connection properties which can modify the query behavior.
  repeated ConnectionProperty connection_properties = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels associated with this query.
  // Labels can be used to organize and group query jobs.
  // Label keys and values can be no longer than 63 characters, can only contain
  // lowercase letters, numeric characters, underscores and dashes.
  // International characters are allowed. Label keys must start with a letter
  // and each label in the list must have a different key.
  map<string, string> labels = 17 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Limits the bytes billed for this query. Queries with
  // bytes billed above this limit will fail (without incurring a charge).
  // If unspecified, the project default is used.
  google.protobuf.Int64Value maximum_bytes_billed = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A unique user provided identifier to ensure idempotent behavior
  // for queries. Note that this is different from the job_id. It has the
  // following properties:
  //
  // 1. It is case-sensitive, limited to up to 36 ASCII characters. A UUID is
  //    recommended.
  //
  // 2. Read only queries can ignore this token since they are nullipotent by
  //    definition.
  //
  // 3. For the purposes of idempotency ensured by the request_id, a request
  //    is considered duplicate of another only if they have the same request_id
  //    and are actually duplicates. When determining whether a request is a
  //    duplicate of another request, all parameters in the request that
  //    may affect the result are considered. For example, query,
  //    connection_properties, query_parameters, use_legacy_sql are parameters
  //    that affect the result and are considered when determining whether a
  //    request is a duplicate, but properties like timeout_ms don't
  //    affect the result and are thus not considered. Dry run query
  //    requests are never considered duplicate of another request.
  //
  // 4. When a duplicate mutating query request is detected, it returns:
  //    a. the results of the mutation if it completes successfully within
  //       the timeout.
  //    b. the running operation if it is still in progress at the end of the
  //        timeout.
  //
  // 5. Its lifetime is limited to 15 minutes. In other words, if two
  //    requests are sent with the same request_id, but more than 15 minutes
  //    apart, idempotency is not guaranteed.
  string request_id = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, creates a new session using a randomly generated
  // session_id. If false, runs query with an existing session_id passed in
  // ConnectionProperty, otherwise runs query in non-session mode.
  //
  // The session location will be set to QueryRequest.location if it is present,
  // otherwise it's set to the default location based on existing routing logic.
  google.protobuf.BoolValue create_session = 20
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If not set, jobs are always required.
  //
  // If set, the query request will follow the behavior described
  // JobCreationMode.
  // [Preview](https://cloud.google.com/products/#product-launch-stages)
  JobCreationMode job_creation_mode = 22
      [(google.api.field_behavior) = OPTIONAL];
}

message QueryResponse {
  // The resource type.
  string kind = 1;

  // The schema of the results. Present only when the query completes
  // successfully.
  TableSchema schema = 2;

  // Reference to the Job that was created to run the query. This field will be
  // present even if the original request timed out, in which case
  // GetQueryResults can be used to read the results once the query has
  // completed. Since this API only returns the first page of results,
  // subsequent pages can be fetched via the same mechanism (GetQueryResults).
  //
  // If job_creation_mode was set to `JOB_CREATION_OPTIONAL` and the query
  // completes without creating a job, this field will be empty.
  JobReference job_reference = 3;

  // Optional. The reason why a Job was created.
  //
  // Only relevant when a job_reference is present in the response.
  // If job_reference is not present it will always be unset.
  // [Preview](https://cloud.google.com/products/#product-launch-stages)
  JobCreationReason job_creation_reason = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Auto-generated ID for the query.
  // [Preview](https://cloud.google.com/products/#product-launch-stages)
  string query_id = 14;

  // The total number of rows in the complete query result set, which can be
  // more than the number of rows in this single page of results.
  google.protobuf.UInt64Value total_rows = 4;

  // A token used for paging results. A non-empty token indicates that
  // additional results are available. To see additional results,
  // query the
  // [`jobs.getQueryResults`](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/getQueryResults)
  // method. For more information, see [Paging through table
  // data](https://cloud.google.com/bigquery/docs/paging-results).
  string page_token = 5;

  // An object with as many results as can be contained within the maximum
  // permitted reply size. To get any additional rows, you can call
  // GetQueryResults and specify the jobReference returned above.
  repeated google.protobuf.Struct rows = 6;

  // The total number of bytes processed for this query. If this query was a dry
  // run, this is the number of bytes that would be processed if the query were
  // run.
  google.protobuf.Int64Value total_bytes_processed = 7;

  // Whether the query has completed or not. If rows or totalRows are present,
  // this will always be true. If this is false, totalRows will not be
  // available.
  google.protobuf.BoolValue job_complete = 8;

  // Output only. The first errors or warnings encountered during the running of
  // the job. The final message includes the number of errors that caused the
  // process to stop. Errors here do not necessarily mean that the job has
  // completed or was unsuccessful. For more information about error messages,
  // see [Error
  // messages](https://cloud.google.com/bigquery/docs/error-messages).
  repeated ErrorProto errors = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether the query result was fetched from the query cache.
  google.protobuf.BoolValue cache_hit = 10;

  // Output only. The number of rows affected by a DML statement. Present only
  // for DML statements INSERT, UPDATE or DELETE.
  google.protobuf.Int64Value num_dml_affected_rows = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Information of the session if this job is part of one.
  SessionInfo session_info = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Detailed statistics for DML statements INSERT, UPDATE, DELETE,
  // MERGE or TRUNCATE.
  DmlStats dml_stats = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}
