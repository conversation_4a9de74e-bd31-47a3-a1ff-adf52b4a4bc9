// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "ProjectProto";
option java_package = "com.google.cloud.bigquery.v2";

// This service provides access to BigQuery functionality related to projects.
service ProjectService {
  option (google.api.default_host) = "bigquery.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // RPC to get the service account for a project used for interactions with
  // Google Cloud KMS
  rpc GetServiceAccount(GetServiceAccountRequest)
      returns (GetServiceAccountResponse) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/serviceAccount"
    };
  }
}

// Request object of GetServiceAccount
message GetServiceAccountRequest {
  // Required. ID of the project.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response object of GetServiceAccount
message GetServiceAccountResponse {
  // The resource type of the response.
  string kind = 1;

  // The service account email address.
  string email = 2;
}
