// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "TimePartitioningProto";
option java_package = "com.google.cloud.bigquery.v2";

message TimePartitioning {
  // Required. The supported types are <PERSON>AY, <PERSON>OUR, <PERSON>ONTH, and YEAR, which will
  // generate one partition per day, hour, month, and year, respectively.
  string type = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Number of milliseconds for which to keep the storage for a
  // partition.
  // A wrapper is used here because 0 is an invalid value.
  google.protobuf.Int64Value expiration_ms = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If not set, the table is partitioned by pseudo
  // column '_PARTITIONTIME'; if set, the table is partitioned by this field.
  // The field must be a top-level TIMESTAMP or DATE field. Its mode must be
  // NULLABLE or REQUIRED.
  // A wrapper is used here because an empty string is an invalid value.
  google.protobuf.StringValue field = 3
      [(google.api.field_behavior) = OPTIONAL];
}
