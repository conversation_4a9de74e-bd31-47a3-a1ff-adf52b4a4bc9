// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "ExternalCatalogDatasetOptionsProto";
option java_package = "com.google.cloud.bigquery.v2";

// Options defining open source compatible datasets living in the BigQuery
// catalog. Contains metadata of open source database, schema
// or namespace represented by the current dataset.
message ExternalCatalogDatasetOptions {
  // Optional. A map of key value pairs defining the parameters and properties
  // of the open source schema. Maximum size of 2Mib.
  map<string, string> parameters = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The storage location URI for all tables in the dataset.
  // Equivalent to hive metastore's database locationUri. Maximum length of 1024
  // characters.
  string default_storage_location_uri = 2
      [(google.api.field_behavior) = OPTIONAL];
}
