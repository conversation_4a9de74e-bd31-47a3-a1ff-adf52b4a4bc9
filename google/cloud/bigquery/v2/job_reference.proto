// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "JobReferenceProto";
option java_package = "com.google.cloud.bigquery.v2";

// A job reference is a fully qualified identifier for referring to a job.
message JobReference {
  // Required. The ID of the project containing this job.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the job. The ID must contain only letters (a-z, A-Z),
  // numbers (0-9), underscores (_), or dashes (-). The maximum length is 1,024
  // characters.
  string job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The geographic location of the job. The default value is US.
  //
  // For more information about BigQuery locations, see:
  // https://cloud.google.com/bigquery/docs/locations
  google.protobuf.StringValue location = 3
      [(google.api.field_behavior) = OPTIONAL];

  // This field should not be used.
  repeated string location_alternative = 5;
}
