// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/bigquery/v2/biglake_config.proto";
import "google/cloud/bigquery/v2/clustering.proto";
import "google/cloud/bigquery/v2/encryption_config.proto";
import "google/cloud/bigquery/v2/error.proto";
import "google/cloud/bigquery/v2/external_catalog_table_options.proto";
import "google/cloud/bigquery/v2/external_data_config.proto";
import "google/cloud/bigquery/v2/partitioning_definition.proto";
import "google/cloud/bigquery/v2/privacy_policy.proto";
import "google/cloud/bigquery/v2/range_partitioning.proto";
import "google/cloud/bigquery/v2/restriction_config.proto";
import "google/cloud/bigquery/v2/table_constraints.proto";
import "google/cloud/bigquery/v2/table_reference.proto";
import "google/cloud/bigquery/v2/table_schema.proto";
import "google/cloud/bigquery/v2/time_partitioning.proto";
import "google/cloud/bigquery/v2/udf_resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "TableProto";
option java_package = "com.google.cloud.bigquery.v2";

// TableService provides methods for managing BigQuery tables and table-like
// entities such as views and snapshots.
service TableService {
  option (google.api.default_host) = "bigquery.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Gets the specified table resource by table ID.
  // This method does not return the data in the table, it only returns the
  // table resource, which describes the structure of this table.
  rpc GetTable(GetTableRequest) returns (Table) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}/tables/{table_id=*}"
    };
  }

  // Creates a new, empty table in the dataset.
  rpc InsertTable(InsertTableRequest) returns (Table) {
    option (google.api.http) = {
      post: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}/tables"
      body: "table"
    };
  }

  // Updates information in an existing table. The update method replaces the
  // entire table resource, whereas the patch method only replaces fields that
  // are provided in the submitted table resource.
  // This method supports RFC5789 patch semantics.
  rpc PatchTable(UpdateOrPatchTableRequest) returns (Table) {
    option (google.api.http) = {
      patch: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}/tables/{table_id=*}"
      body: "table"
    };
  }

  // Updates information in an existing table. The update method replaces the
  // entire Table resource, whereas the patch method only replaces fields that
  // are provided in the submitted Table resource.
  rpc UpdateTable(UpdateOrPatchTableRequest) returns (Table) {
    option (google.api.http) = {
      put: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}/tables/{table_id=*}"
      body: "table"
    };
  }

  // Deletes the table specified by tableId from the dataset.
  // If the table contains data, all the data will be deleted.
  rpc DeleteTable(DeleteTableRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}/tables/{table_id=*}"
    };
  }

  // Lists all tables in the specified dataset. Requires the READER dataset
  // role.
  rpc ListTables(ListTablesRequest) returns (TableList) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}/tables"
    };
  }
}

// Replication info of a table created using `AS REPLICA` DDL like:
// `CREATE MATERIALIZED VIEW mv1 AS REPLICA OF src_mv`
message TableReplicationInfo {
  // Replication status of the table created using `AS REPLICA` like:
  // `CREATE MATERIALIZED VIEW mv1 AS REPLICA OF src_mv`
  enum ReplicationStatus {
    // Default value.
    REPLICATION_STATUS_UNSPECIFIED = 0;

    // Replication is Active with no errors.
    ACTIVE = 1;

    // Source object is deleted.
    SOURCE_DELETED = 2;

    // Source revoked replication permissions.
    PERMISSION_DENIED = 3;

    // Source configuration doesn’t allow replication.
    UNSUPPORTED_CONFIGURATION = 4;
  }

  // Required. Source table reference that is replicated.
  TableReference source_table = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Specifies the interval at which the source table is polled for
  // updates.
  // It's Optional. If not specified, default replication interval would be
  // applied.
  int64 replication_interval_ms = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output only. If source is a materialized view, this field
  // signifies the last refresh time of the source.
  int64 replicated_source_last_refresh_time = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Output only. Replication status of configured replication.
  ReplicationStatus replication_status = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Output only. Replication error that will permanently stopped
  // table replication.
  ErrorProto replication_error = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Describes the definition of a logical view.
message ViewDefinition {
  // Required. A query that BigQuery executes when the view is referenced.
  string query = 1 [(google.api.field_behavior) = REQUIRED];

  // Describes user-defined function resources used in the query.
  repeated UserDefinedFunctionResource user_defined_function_resources = 2;

  // Specifies whether to use BigQuery's legacy SQL for this view.
  // The default value is true. If set to false, the view will use
  // BigQuery's GoogleSQL:
  // https://cloud.google.com/bigquery/sql-reference/
  //
  // Queries and views that reference this view must use the same flag value.
  // A wrapper is used here because the default value is True.
  google.protobuf.BoolValue use_legacy_sql = 3;

  // True if the column names are explicitly specified. For example by using the
  // 'CREATE VIEW v(c1, c2) AS ...' syntax.
  // Can only be set for GoogleSQL views.
  bool use_explicit_column_names = 4;

  // Optional. Specifices the privacy policy for the view.
  PrivacyPolicy privacy_policy = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Foreign view representations.
  repeated ForeignViewDefinition foreign_definitions = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// A view can be represented in multiple ways. Each representation has its own
// dialect. This message stores the metadata required for these representations.
message ForeignViewDefinition {
  // Required. The query that defines the view.
  string query = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Represents the dialect of the query.
  string dialect = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Definition and configuration of a materialized view.
message MaterializedViewDefinition {
  // Required. A query whose results are persisted.
  string query = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The time when this materialized view was last refreshed, in
  // milliseconds since the epoch.
  int64 last_refresh_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Enable automatic refresh of the materialized view when the base
  // table is updated. The default value is "true".
  google.protobuf.BoolValue enable_refresh = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum frequency at which this materialized view will be
  // refreshed. The default value is "1800000" (30 minutes).
  google.protobuf.UInt64Value refresh_interval_ms = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. This option declares the intention to construct a materialized
  // view that isn't refreshed incrementally.
  google.protobuf.BoolValue allow_non_incremental_definition = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Status of a materialized view.
// The last refresh timestamp status is omitted here, but is present in the
// MaterializedViewDefinition message.
message MaterializedViewStatus {
  // Output only. Refresh watermark of materialized view. The base tables' data
  // were collected into the materialized view cache until this time.
  google.protobuf.Timestamp refresh_watermark = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Error result of the last automatic refresh. If present,
  // indicates that the last automatic refresh was unsuccessful.
  ErrorProto last_refresh_status = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Information about base table and snapshot time of the snapshot.
message SnapshotDefinition {
  // Required. Reference describing the ID of the table that was snapshot.
  TableReference base_table_reference = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The time at which the base table was snapshot. This value is
  // reported in the JSON response using RFC3339 format.
  google.protobuf.Timestamp snapshot_time = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Information about base table and clone time of a table clone.
message CloneDefinition {
  // Required. Reference describing the ID of the table that was cloned.
  TableReference base_table_reference = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The time at which the base table was cloned. This value is
  // reported in the JSON response using RFC3339 format.
  google.protobuf.Timestamp clone_time = 2
      [(google.api.field_behavior) = REQUIRED];
}

message Streamingbuffer {
  // Output only. A lower-bound estimate of the number of bytes currently in
  // the streaming buffer.
  uint64 estimated_bytes = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A lower-bound estimate of the number of rows currently in the
  // streaming buffer.
  uint64 estimated_rows = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Contains the timestamp of the oldest entry in the streaming
  // buffer, in milliseconds since the epoch, if the streaming buffer is
  // available.
  fixed64 oldest_entry_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message Table {
  // The type of resource ID.
  string kind = 1;

  // Output only. A hash of this resource.
  string etag = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An opaque ID uniquely identifying the table.
  string id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A URL that can be used to access this resource again.
  string self_link = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Reference describing the ID of this table.
  TableReference table_reference = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. A descriptive name for this table.
  google.protobuf.StringValue friendly_name = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A user-friendly description of this table.
  google.protobuf.StringValue description = 7
      [(google.api.field_behavior) = OPTIONAL];

  // The labels associated with this table. You can use these to organize and
  // group your tables. Label keys and values can be no longer than 63
  // characters, can only contain lowercase letters, numeric characters,
  // underscores and dashes. International characters are allowed. Label values
  // are optional. Label keys must start with a letter and each label in the
  // list must have a different key.
  map<string, string> labels = 8;

  // Optional. Describes the schema of this table.
  TableSchema schema = 9 [(google.api.field_behavior) = OPTIONAL];

  // If specified, configures time-based partitioning for this table.
  TimePartitioning time_partitioning = 10;

  // If specified, configures range partitioning for this table.
  RangePartitioning range_partitioning = 27;

  // Clustering specification for the table. Must be specified with time-based
  // partitioning, data in the table will be first partitioned and subsequently
  // clustered.
  Clustering clustering = 23;

  // Optional. If set to true, queries over this table require
  // a partition filter that can be used for partition elimination to be
  // specified.
  google.protobuf.BoolValue require_partition_filter = 28
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The partition information for all table formats, including
  // managed partitioned tables, hive partitioned tables, iceberg partitioned,
  // and metastore partitioned tables. This field is only populated for
  // metastore partitioned tables. For other table formats, this is an output
  // only field.
  optional PartitioningDefinition partition_definition = 51
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The size of this table in logical bytes, excluding any data in
  // the streaming buffer.
  google.protobuf.Int64Value num_bytes = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The physical size of this table in bytes. This includes
  // storage used for time travel.
  google.protobuf.Int64Value num_physical_bytes = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of logical bytes in the table that are considered
  // "long-term storage".
  google.protobuf.Int64Value num_long_term_bytes = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of rows of data in this table, excluding any data
  // in the streaming buffer.
  google.protobuf.UInt64Value num_rows = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when this table was created, in milliseconds since
  // the epoch.
  int64 creation_time = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The time when this table expires, in milliseconds since the
  // epoch. If not present, the table will persist indefinitely. Expired tables
  // will be deleted and their storage reclaimed.  The defaultTableExpirationMs
  // property of the encapsulating dataset can be used to set a default
  // expirationTime on newly created tables.
  google.protobuf.Int64Value expiration_time = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time when this table was last modified, in milliseconds
  // since the epoch.
  fixed64 last_modified_time = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Describes the table type. The following values are supported:
  //
  // * `TABLE`: A normal BigQuery table.
  // * `VIEW`: A virtual table defined by a SQL query.
  // * `EXTERNAL`: A table that references data stored in an external storage
  //   system, such as Google Cloud Storage.
  // * `MATERIALIZED_VIEW`: A precomputed view defined by a SQL query.
  // * `SNAPSHOT`: An immutable BigQuery table that preserves the contents of a
  //   base table at a particular time. See additional information on
  //   [table
  //   snapshots](https://cloud.google.com/bigquery/docs/table-snapshots-intro).
  //
  // The default value is `TABLE`.
  string type = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The view definition.
  ViewDefinition view = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The materialized view definition.
  MaterializedViewDefinition materialized_view = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The materialized view status.
  MaterializedViewStatus materialized_view_status = 42
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Describes the data format, location, and other properties of
  // a table stored outside of BigQuery. By defining these properties, the data
  // source can then be queried as if it were a standard BigQuery table.
  ExternalDataConfiguration external_data_configuration = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the configuration of a BigLake managed table.
  BigLakeConfiguration biglake_configuration = 45
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The geographic location where the table resides. This value
  // is inherited from the dataset.
  string location = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Contains information regarding this table's streaming buffer,
  // if one is present. This field will be absent if the table is not being
  // streamed to or if there is no data in the streaming buffer.
  Streamingbuffer streaming_buffer = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Custom encryption configuration (e.g., Cloud KMS keys).
  EncryptionConfiguration encryption_configuration = 22;

  // Output only. Contains information about the snapshot. This value is set via
  // snapshot creation.
  SnapshotDefinition snapshot_definition = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Defines the default collation specification of new STRING fields
  // in the table. During table creation or update, if a STRING field is added
  // to this table without explicit collation specified, then the table inherits
  // the table default collation. A change to this field affects only fields
  // added afterwards, and does not alter the existing fields.
  // The following values are supported:
  //
  // * 'und:ci': undetermined locale, case insensitive.
  // * '': empty string. Default to case-sensitive behavior.
  google.protobuf.StringValue default_collation = 30
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines the default rounding mode specification of new decimal
  // fields (NUMERIC OR BIGNUMERIC) in the table. During table creation or
  // update, if a decimal field is added to this table without an explicit
  // rounding mode specified, then the field inherits the table default
  // rounding mode. Changing this field doesn't affect existing fields.
  TableFieldSchema.RoundingMode default_rounding_mode = 44
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Contains information about the clone. This value is set via
  // the clone operation.
  CloneDefinition clone_definition = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of physical bytes used by time travel storage (deleted
  // or changed data). This data is not kept in real time, and might be delayed
  // by a few seconds to a few minutes.
  google.protobuf.Int64Value num_time_travel_physical_bytes = 33
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total number of logical bytes in the table or materialized
  // view.
  google.protobuf.Int64Value num_total_logical_bytes = 34
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of logical bytes that are less than 90 days old.
  google.protobuf.Int64Value num_active_logical_bytes = 35
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of logical bytes that are more than 90 days old.
  google.protobuf.Int64Value num_long_term_logical_bytes = 36
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of physical bytes used by current live data storage.
  // This data is not kept in real time, and might be delayed by a few seconds
  // to a few minutes.
  google.protobuf.Int64Value num_current_physical_bytes = 53
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The physical size of this table in bytes. This also includes
  // storage used for time travel. This data is not kept in real time, and might
  // be delayed by a few seconds to a few minutes.
  google.protobuf.Int64Value num_total_physical_bytes = 37
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of physical bytes less than 90 days old. This data is
  // not kept in real time, and might be delayed by a few seconds to a few
  // minutes.
  google.protobuf.Int64Value num_active_physical_bytes = 38
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of physical bytes more than 90 days old.
  // This data is not kept in real time, and might be delayed by a few seconds
  // to a few minutes.
  google.protobuf.Int64Value num_long_term_physical_bytes = 39
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of partitions present in the table or materialized
  // view. This data is not kept in real time, and might be delayed by a few
  // seconds to a few minutes.
  google.protobuf.Int64Value num_partitions = 40
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The maximum staleness of data that could be returned when the
  // table (or stale MV) is queried. Staleness encoded as a string encoding
  // of sql IntervalValue type.
  string max_staleness = 41 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output only. Restriction config for table. If set, restrict
  // certain accesses on the table based on the config. See [Data
  // egress](https://cloud.google.com/bigquery/docs/analytics-hub-introduction#data_egress)
  // for more details.
  RestrictionConfig restrictions = 46 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. Tables Primary Key and Foreign Key information
  TableConstraints table_constraints = 47
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The [tags](https://cloud.google.com/bigquery/docs/tags) attached
  // to this table. Tag keys are globally unique. Tag key is expected to be in
  // the namespaced format, for example "123456789012/environment" where
  // 123456789012 is the ID of the parent organization or project resource for
  // this tag key. Tag value is expected to be the short name, for example
  // "Production". See [Tag
  // definitions](https://cloud.google.com/iam/docs/tags-access-control#definitions)
  // for more details.
  map<string, string> resource_tags = 48
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Table replication info for table created `AS REPLICA` DDL like:
  // `CREATE MATERIALIZED VIEW mv1 AS REPLICA OF src_mv`
  TableReplicationInfo table_replication_info = 49
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output only. Table references of all replicas currently active on
  // the table.
  repeated TableReference replicas = 50 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. Options defining open source compatible table.
  ExternalCatalogTableOptions external_catalog_table_options = 54
      [(google.api.field_behavior) = OPTIONAL];
}

// Request format for getting table metadata.
message GetTableRequest {
  // TableMetadataView specifies which table information is returned.
  enum TableMetadataView {
    // The default value.
    // Default to the STORAGE_STATS view.
    TABLE_METADATA_VIEW_UNSPECIFIED = 0;

    // Includes basic table information including schema and
    // partitioning specification. This view does not include storage statistics
    // such as numRows or numBytes. This view is significantly more efficient
    // and should be used to support high query rates.
    BASIC = 1;

    // Includes all information in the BASIC view as well as storage statistics
    // (numBytes, numLongTermBytes, numRows and lastModifiedTime).
    STORAGE_STATS = 2;

    // Includes all table information, including storage statistics.
    // It returns same information as STORAGE_STATS view, but may contain
    // additional information in the future.
    FULL = 3;
  }

  // Required. Project ID of the requested table
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of the requested table
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Table ID of the requested table
  string table_id = 3 [(google.api.field_behavior) = REQUIRED];

  // List of table schema fields to return (comma-separated).
  // If unspecified, all fields are returned.
  // A fieldMask cannot be used here because the fields will automatically be
  // converted from camelCase to snake_case and the conversion will fail if
  // there are underscores. Since these are fields in BigQuery table schemas,
  // underscores are allowed.
  string selected_fields = 4;

  // Optional. Specifies the view that determines which table information is
  // returned. By default, basic table information and storage statistics
  // (STORAGE_STATS) are returned.
  TableMetadataView view = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request format for inserting table metadata.
message InsertTableRequest {
  // Required. Project ID of the new table
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of the new table
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A tables resource to insert
  Table table = 4 [(google.api.field_behavior) = REQUIRED];
}

message UpdateOrPatchTableRequest {
  // Required. Project ID of the table to update
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of the table to update
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Table ID of the table to update
  string table_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. A tables resource which will replace or patch the specified table
  Table table = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. When true will autodetect schema, else will keep original schema.
  bool autodetect_schema = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request format for deleting a table.
message DeleteTableRequest {
  // Required. Project ID of the table to delete
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of the table to delete
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Table ID of the table to delete
  string table_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request format for enumerating tables.
message ListTablesRequest {
  // Required. Project ID of the tables to list
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of the tables to list
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of results to return in a single response page.
  // Leverage the page tokens to iterate through the entire collection.
  google.protobuf.UInt32Value max_results = 3;

  // Page token, returned by a previous call, to request the next page of
  // results
  string page_token = 4;
}

// Information about a logical view.
message ListFormatView {
  // True if view is defined in legacy SQL dialect,
  // false if in GoogleSQL.
  google.protobuf.BoolValue use_legacy_sql = 1;

  // Specifices the privacy policy for the view.
  PrivacyPolicy privacy_policy = 2;
}

message ListFormatTable {
  // The resource type.
  string kind = 1;

  // An opaque ID of the table.
  string id = 2;

  // A reference uniquely identifying table.
  TableReference table_reference = 3;

  // The user-friendly name for this table.
  google.protobuf.StringValue friendly_name = 4;

  // The type of table.
  string type = 5;

  // The time-based partitioning for this table.
  TimePartitioning time_partitioning = 6;

  // The range partitioning for this table.
  RangePartitioning range_partitioning = 12;

  // Clustering specification for this table, if configured.
  Clustering clustering = 11;

  // The labels associated with this table. You can use these to organize
  // and group your tables.
  map<string, string> labels = 7;

  // Additional details for a view.
  ListFormatView view = 8;

  // Output only. The time when this table was created, in milliseconds since
  // the epoch.
  int64 creation_time = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The time when this table expires, in milliseconds since the
  // epoch. If not present, the table will persist indefinitely. Expired tables
  // will be deleted and their storage reclaimed.
  int64 expiration_time = 10;

  // Optional. If set to true, queries including this table must specify a
  // partition filter. This filter is used for partition elimination.
  google.protobuf.BoolValue require_partition_filter = 14
      [(google.api.field_behavior) = OPTIONAL];
}

// Partial projection of the metadata for a given table in a list response.
message TableList {
  // The type of list.
  string kind = 1;

  // A hash of this page of results.
  string etag = 2;

  // A token to request the next page of results.
  string next_page_token = 3;

  // Tables in the requested dataset.
  repeated ListFormatTable tables = 4;

  // The total number of tables in the dataset.
  google.protobuf.Int32Value total_items = 5;
}
