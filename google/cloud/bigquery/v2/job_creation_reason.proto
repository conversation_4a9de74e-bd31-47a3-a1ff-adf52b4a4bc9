// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "JobCreationReasonProto";
option java_package = "com.google.cloud.bigquery.v2";

// Reason about why a Job was created from a
// [`jobs.query`](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/query)
// method when used with `JOB_CREATION_OPTIONAL` Job creation mode.
//
// For
// [`jobs.insert`](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/insert)
// method calls it will always be `REQUESTED`.
//
// [Preview](https://cloud.google.com/products/#product-launch-stages)
message JobCreationReason {
  // Indicates the high level reason why a job was created.
  enum Code {
    // Reason is not specified.
    CODE_UNSPECIFIED = 0;

    // Job creation was requested.
    REQUESTED = 1;

    // The query request ran beyond a system defined timeout specified by the
    // [timeoutMs field in the
    // QueryRequest](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/query#queryrequest).
    // As a result it was considered a long running operation for which a job
    // was created.
    LONG_RUNNING = 2;

    // The results from the query cannot fit in the response.
    LARGE_RESULTS = 3;

    // BigQuery has determined that the query needs to be executed as a Job.
    OTHER = 4;
  }

  // Output only. Specifies the high level reason why a Job was created.
  Code code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}
