// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/bigquery/v2/row_access_policy_reference.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "RowAccessPolicyProto";
option java_package = "com.google.cloud.bigquery.v2";

// Service for interacting with row access policies.
service RowAccessPolicyService {
  option (google.api.default_host) = "bigquery.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists all row access policies on the specified table.
  rpc ListRowAccessPolicies(ListRowAccessPoliciesRequest)
      returns (ListRowAccessPoliciesResponse) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}/tables/{table_id=*}/rowAccessPolicies"
    };
  }
}

// Request message for the ListRowAccessPolicies method.
message ListRowAccessPoliciesRequest {
  // Required. Project ID of the row access policies to list.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of row access policies to list.
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Table ID of the table to list row access policies.
  string table_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Page token, returned by a previous call, to request the next page of
  // results.
  string page_token = 4;

  // The maximum number of results to return in a single response page. Leverage
  // the page tokens to iterate through the entire collection.
  int32 page_size = 5;
}

// Response message for the ListRowAccessPolicies method.
message ListRowAccessPoliciesResponse {
  // Row access policies on the requested table.
  repeated RowAccessPolicy row_access_policies = 1;

  // A token to request the next page of results.
  string next_page_token = 2;
}

// Represents access on a subset of rows on the specified table, defined by its
// filter predicate. Access to the subset of rows is controlled by its IAM
// policy.
message RowAccessPolicy {
  // Output only. A hash of this resource.
  string etag = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Reference describing the ID of this row access policy.
  RowAccessPolicyReference row_access_policy_reference = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. A SQL boolean expression that represents the rows defined by this
  // row access policy, similar to the boolean expression in a WHERE clause of a
  // SELECT query on a table.
  // References to other tables, routines, and temporary functions are not
  // supported.
  //
  // Examples: region="EU"
  //           date_field = CAST('2019-9-27' as DATE)
  //           nullable_field is not NULL
  //           numeric_field BETWEEN 1.0 AND 5.0
  string filter_predicate = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. The time when this row access policy was created, in
  // milliseconds since the epoch.
  google.protobuf.Timestamp creation_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when this row access policy was last modified, in
  // milliseconds since the epoch.
  google.protobuf.Timestamp last_modified_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
