// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";
import "google/cloud/bigquery/v2/error.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "JobStatusProto";
option java_package = "com.google.cloud.bigquery.v2";

message JobStatus {
  // Output only. Final error result of the job. If present, indicates that the
  // job has completed and was unsuccessful.
  ErrorProto error_result = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The first errors encountered during the running of the job.
  // The final message includes the number of errors that caused the process to
  // stop. Errors here do not necessarily mean that the job has not completed or
  // was unsuccessful.
  repeated ErrorProto errors = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Running state of the job.  Valid states include 'PENDING',
  // 'RUNNING', and 'DONE'.
  string state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
