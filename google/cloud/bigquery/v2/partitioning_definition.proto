// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "PartitioningDefinitionProto";
option java_package = "com.google.cloud.bigquery.v2";

// The partitioning information, which includes managed table, external table
// and metastore partitioned table partition information.
message PartitioningDefinition {
  // Optional. Details about each partitioning column. This field is output only
  // for all partitioning types other than metastore partitioned tables.
  // BigQuery native tables only support 1 partitioning column. Other table
  // types may support 0, 1 or more partitioning columns.
  // For metastore partitioned tables, the order must match the definition order
  // in the Hive Metastore, where it must match the physical layout of the
  // table. For example,
  //
  // CREATE TABLE a_table(id BIGINT, name STRING)
  // PARTITIONED BY (city STRING, state STRING).
  //
  // In this case the values must be ['city', 'state'] in that order.
  repeated PartitionedColumn partitioned_column = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// The partitioning column information.
message PartitionedColumn {
  // Required. The name of the partition column.
  optional string field = 1 [(google.api.field_behavior) = REQUIRED];
}
