// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "ClusteringProto";
option java_package = "com.google.cloud.bigquery.v2";

// Configures table clustering.
message Clustering {
  // One or more fields on which data should be clustered. Only top-level,
  // non-repeated, simple-type fields are supported. The ordering of the
  // clustering fields should be prioritized from most to least important
  // for filtering purposes.
  //
  // Additional information on limitations can be found here:
  // https://cloud.google.com/bigquery/docs/creating-clustered-tables#limitations
  repeated string fields = 1;
}
