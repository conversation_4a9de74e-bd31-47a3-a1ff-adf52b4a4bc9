// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/bigquery/v2/dataset_reference.proto";
import "google/cloud/bigquery/v2/encryption_config.proto";
import "google/cloud/bigquery/v2/external_catalog_dataset_options.proto";
import "google/cloud/bigquery/v2/external_dataset_reference.proto";
import "google/cloud/bigquery/v2/restriction_config.proto";
import "google/cloud/bigquery/v2/routine_reference.proto";
import "google/cloud/bigquery/v2/table_reference.proto";
import "google/cloud/bigquery/v2/table_schema.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/expr.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "DatasetProto";
option java_package = "com.google.cloud.bigquery.v2";

// DatasetService provides methods for managing BigQuery datasets.
service DatasetService {
  option (google.api.default_host) = "bigquery.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Returns the dataset specified by datasetID.
  rpc GetDataset(GetDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}"
    };
  }

  // Creates a new empty dataset.
  rpc InsertDataset(InsertDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      post: "/bigquery/v2/projects/{project_id=*}/datasets"
      body: "dataset"
    };
  }

  // Updates information in an existing dataset. The update method replaces the
  // entire dataset resource, whereas the patch method only replaces fields that
  // are provided in the submitted dataset resource.
  // This method supports RFC5789 patch semantics.
  rpc PatchDataset(UpdateOrPatchDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      patch: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}"
      body: "dataset"
    };
  }

  // Updates information in an existing dataset. The update method replaces the
  // entire dataset resource, whereas the patch method only replaces fields that
  // are provided in the submitted dataset resource.
  rpc UpdateDataset(UpdateOrPatchDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      put: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}"
      body: "dataset"
    };
  }

  // Deletes the dataset specified by the datasetId value. Before you can delete
  // a dataset, you must delete all its tables, either manually or by specifying
  // deleteContents. Immediately after deletion, you can create another dataset
  // with the same name.
  rpc DeleteDataset(DeleteDatasetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}"
    };
  }

  // Lists all datasets in the specified project to which the user has been
  // granted the READER dataset role.
  rpc ListDatasets(ListDatasetsRequest) returns (DatasetList) {
    option (google.api.http) = {
      get: "/bigquery/v2/projects/{project_id=*}/datasets"
    };
  }

  // Undeletes a dataset which is within time travel window based on datasetId.
  // If a time is specified, the dataset version deleted at that time is
  // undeleted, else the last live version is undeleted.
  rpc UndeleteDataset(UndeleteDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      post: "/bigquery/v2/projects/{project_id=*}/datasets/{dataset_id=*}:undelete"
      body: "*"
    };
  }
}

// Grants all resources of particular types in a particular dataset read access
// to the current dataset.
//
// Similar to how individually authorized views work, updates to any resource
// granted through its dataset (including creation of new resources) requires
// read permission to referenced resources, plus write permission to the
// authorizing dataset.
message DatasetAccessEntry {
  // Indicates the type of resources in a dataset that the entry applies to.
  enum TargetType {
    // Do not use. You must set a target type explicitly.
    TARGET_TYPE_UNSPECIFIED = 0;

    // This entry applies to views in the dataset.
    VIEWS = 1;

    // This entry applies to routines in the dataset.
    ROUTINES = 2;
  }

  // The dataset this entry applies to
  DatasetReference dataset = 1;

  // Which resources in the dataset this entry applies to. Currently, only
  // views are supported, but additional target types may be added in the
  // future.
  repeated TargetType target_types = 2;
}

// An object that defines dataset access for an entity.
message Access {
  // An IAM role ID that should be granted to the user, group,
  // or domain specified in this access entry.
  // The following legacy mappings will be applied:
  //
  // * `OWNER`: `roles/bigquery.dataOwner`
  // * `WRITER`: `roles/bigquery.dataEditor`
  // * `READER`: `roles/bigquery.dataViewer`
  //
  // This field will accept any of the above formats, but will return only
  // the legacy format. For example, if you set this field to
  // "roles/bigquery.dataOwner", it will be returned back as "OWNER".
  string role = 1;

  // [Pick one] An email address of a user to grant access to. For example:
  // <EMAIL>. Maps to IAM policy member "user:EMAIL" or
  // "serviceAccount:EMAIL".
  string user_by_email = 2;

  // [Pick one] An email address of a Google Group to grant access to.
  // Maps to IAM policy member "group:GROUP".
  string group_by_email = 3;

  // [Pick one] A domain to grant access to. Any users signed in with the domain
  // specified will be granted the specified access. Example: "example.com".
  // Maps to IAM policy member "domain:DOMAIN".
  string domain = 4;

  // [Pick one] A special group to grant access to. Possible values include:
  //
  //   * projectOwners: Owners of the enclosing project.
  //   * projectReaders: Readers of the enclosing project.
  //   * projectWriters: Writers of the enclosing project.
  //   * allAuthenticatedUsers: All authenticated BigQuery users.
  //
  // Maps to similarly-named IAM members.
  string special_group = 5;

  // [Pick one] Some other type of member that appears in the IAM Policy but
  // isn't a user, group, domain, or special group.
  string iam_member = 7;

  // [Pick one] A view from a different dataset to grant access to. Queries
  // executed against that view will have read access to views/tables/routines
  // in this dataset.
  // The role field is not required when this field is set. If that view is
  // updated by any user, access to the view needs to be granted again via an
  // update operation.
  TableReference view = 6;

  // [Pick one] A routine from a different dataset to grant access to. Queries
  // executed against that routine will have read access to
  // views/tables/routines in this dataset. Only UDF is supported for now.
  // The role field is not required when this field is set. If that routine is
  // updated by any user, access to the routine needs to be granted again via
  // an update operation.
  RoutineReference routine = 8;

  // [Pick one] A grant authorizing all resources of a particular type in a
  // particular dataset access to this dataset. Only views are supported for
  // now. The role field is not required when this field is set. If that dataset
  // is deleted and re-created, its access needs to be granted again via an
  // update operation.
  DatasetAccessEntry dataset = 9;

  // Optional. condition for the binding. If CEL expression in this field is
  // true, this access binding will be considered
  google.type.Expr condition = 10 [(google.api.field_behavior) = OPTIONAL];
}

// Represents a BigQuery dataset.
message Dataset {
  // Indicates the billing model that will be applied to the dataset.
  enum StorageBillingModel {
    // Value not set.
    STORAGE_BILLING_MODEL_UNSPECIFIED = 0;

    // Billing for logical bytes.
    LOGICAL = 1;

    // Billing for physical bytes.
    PHYSICAL = 2;
  }

  // Output only. The resource type.
  string kind = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A hash of the resource.
  string etag = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The fully-qualified unique name of the dataset in the format
  // projectId:datasetId. The dataset name without the project name is given in
  // the datasetId field. When creating a new dataset, leave this field blank,
  // and instead specify the datasetId field.
  string id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A URL that can be used to access the resource again. You can
  // use this URL in Get or Update requests to the resource.
  string self_link = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. A reference that identifies the dataset.
  DatasetReference dataset_reference = 5
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A descriptive name for the dataset.
  google.protobuf.StringValue friendly_name = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A user-friendly description of the dataset.
  google.protobuf.StringValue description = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The default lifetime of all tables in the dataset, in
  // milliseconds. The minimum lifetime value is 3600000 milliseconds (one
  // hour). To clear an existing default expiration with a PATCH request, set to
  // 0. Once this property is set, all newly-created tables in the dataset will
  // have an expirationTime property set to the creation time plus the value in
  // this property, and changing the value will only affect new tables, not
  // existing ones. When the expirationTime for a given table is reached, that
  // table will be deleted automatically.
  // If a table's expirationTime is modified or removed before the table
  // expires, or if you provide an explicit expirationTime when creating a
  // table, that value takes precedence over the default expiration time
  // indicated by this property.
  google.protobuf.Int64Value default_table_expiration_ms = 8
      [(google.api.field_behavior) = OPTIONAL];

  // This default partition expiration, expressed in milliseconds.
  //
  // When new time-partitioned tables are created in a dataset where this
  // property is set, the table will inherit this value, propagated as the
  // `TimePartitioning.expirationMs` property on the new table.  If you set
  // `TimePartitioning.expirationMs` explicitly when creating a table,
  // the `defaultPartitionExpirationMs` of the containing dataset is ignored.
  //
  // When creating a partitioned table, if `defaultPartitionExpirationMs`
  // is set, the `defaultTableExpirationMs` value is ignored and the table
  // will not be inherit a table expiration deadline.
  google.protobuf.Int64Value default_partition_expiration_ms = 14;

  // The labels associated with this dataset. You can use these
  // to organize and group your datasets.
  // You can set this property when inserting or updating a dataset.
  // See [Creating and Updating Dataset
  // Labels](https://cloud.google.com/bigquery/docs/creating-managing-labels#creating_and_updating_dataset_labels)
  // for more information.
  map<string, string> labels = 9;

  // Optional. An array of objects that define dataset access for one or more
  // entities. You can set this property when inserting or updating a dataset in
  // order to control who is allowed to access the data. If unspecified at
  // dataset creation time, BigQuery adds default dataset access for the
  // following entities: access.specialGroup: projectReaders; access.role:
  // READER; access.specialGroup: projectWriters; access.role: WRITER;
  // access.specialGroup: projectOwners; access.role: OWNER;
  // access.userByEmail: [dataset creator email]; access.role: OWNER;
  // If you patch a dataset, then this field is overwritten by the patched
  // dataset's access field. To add entities, you must supply the entire
  // existing access array in addition to any new entities that you want to add.
  repeated Access access = 10 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time when this dataset was created, in milliseconds since
  // the epoch.
  int64 creation_time = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date when this dataset was last modified, in milliseconds
  // since the epoch.
  int64 last_modified_time = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The geographic location where the dataset should reside. See
  // https://cloud.google.com/bigquery/docs/locations for supported
  // locations.
  string location = 13;

  // The default encryption key for all tables in the dataset.
  // After this property is set, the encryption key of all newly-created tables
  // in the dataset is set to this value unless the table creation request or
  // query explicitly overrides the key.
  EncryptionConfiguration default_encryption_configuration = 16;

  // Output only. Reserved for future use.
  google.protobuf.BoolValue satisfies_pzs = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  google.protobuf.BoolValue satisfies_pzi = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Same as `type` in `ListFormatDataset`.
  // The type of the dataset, one of:
  //
  // * DEFAULT - only accessible by owner and authorized accounts,
  // * PUBLIC - accessible by everyone,
  // * LINKED - linked dataset,
  // * EXTERNAL - dataset with definition in external metadata catalog.
  string type = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The source dataset reference when the dataset is of type LINKED.
  // For all other dataset types it is not set. This field cannot be updated
  // once it is set. Any attempt to update this field using Update and Patch API
  // Operations will be ignored.
  LinkedDatasetSource linked_dataset_source = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Metadata about the LinkedDataset. Filled out when the dataset
  // type is LINKED.
  LinkedDatasetMetadata linked_dataset_metadata = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Reference to a read-only external dataset defined in data
  // catalogs outside of BigQuery. Filled out when the dataset type is EXTERNAL.
  ExternalDatasetReference external_dataset_reference = 20
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Options defining open source compatible datasets living in the
  // BigQuery catalog. Contains metadata of open source database, schema or
  // namespace represented by the current dataset.
  ExternalCatalogDatasetOptions external_catalog_dataset_options = 32
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. TRUE if the dataset and its table names are case-insensitive,
  // otherwise FALSE. By default, this is FALSE, which means the dataset and its
  // table names are case-sensitive. This field does not affect routine
  // references.
  google.protobuf.BoolValue is_case_insensitive = 21
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines the default collation specification of future tables
  // created in the dataset. If a table is created in this dataset without
  // table-level default collation, then the table inherits the dataset default
  // collation, which is applied to the string fields that do not have explicit
  // collation specified. A change to this field affects only tables created
  // afterwards, and does not alter the existing tables.
  // The following values are supported:
  //
  // * 'und:ci': undetermined locale, case insensitive.
  // * '': empty string. Default to case-sensitive behavior.
  google.protobuf.StringValue default_collation = 22
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines the default rounding mode specification of new tables
  // created within this dataset. During table creation, if this field is
  // specified, the table within this dataset will inherit the default rounding
  // mode of the dataset. Setting the default rounding mode on a table overrides
  // this option. Existing tables in the dataset are unaffected.
  // If columns are defined during that table creation,
  // they will immediately inherit the table's default rounding mode,
  // unless otherwise specified.
  TableFieldSchema.RoundingMode default_rounding_mode = 26
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines the time travel window in hours. The value can be from 48
  // to 168 hours (2 to 7 days). The default value is 168 hours if this is not
  // set.
  google.protobuf.Int64Value max_time_travel_hours = 23
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Tags for the dataset. To provide tags as inputs, use the
  // `resourceTags` field.
  repeated GcpTag tags = 24
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Updates storage_billing_model for the dataset.
  StorageBillingModel storage_billing_model = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output only. Restriction config for all tables and dataset. If
  // set, restrict certain accesses on the dataset and all its tables based on
  // the config. See [Data
  // egress](https://cloud.google.com/bigquery/docs/analytics-hub-introduction#data_egress)
  // for more details.
  RestrictionConfig restrictions = 27 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. The [tags](https://cloud.google.com/bigquery/docs/tags) attached
  // to this dataset. Tag keys are globally unique. Tag key is expected to be in
  // the namespaced format, for example "123456789012/environment" where
  // 123456789012 is the ID of the parent organization or project resource for
  // this tag key. Tag value is expected to be the short name, for example
  // "Production". See [Tag
  // definitions](https://cloud.google.com/iam/docs/tags-access-control#definitions)
  // for more details.
  map<string, string> resource_tags = 30
      [(google.api.field_behavior) = OPTIONAL];
}

// A global tag managed by Resource Manager.
// https://cloud.google.com/iam/docs/tags-access-control#definitions
message GcpTag {
  // Required. The namespaced friendly name of the tag key, e.g.
  // "12345/environment" where 12345 is org id.
  string tag_key = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The friendly short name of the tag value, e.g. "production".
  string tag_value = 2 [(google.api.field_behavior) = REQUIRED];
}

// A dataset source type which refers to another BigQuery dataset.
message LinkedDatasetSource {
  // The source dataset reference contains project numbers and not project ids.
  DatasetReference source_dataset = 1;
}

// Metadata about the Linked Dataset.
message LinkedDatasetMetadata {
  // Specifies whether Linked Dataset is currently in a linked state or not.
  enum LinkState {
    // The default value.
    // Default to the LINKED state.
    LINK_STATE_UNSPECIFIED = 0;

    // Normal Linked Dataset state. Data is queryable via the Linked Dataset.
    LINKED = 1;

    // Data publisher or owner has unlinked this Linked Dataset. It means you
    // can no longer query or see the data in the Linked Dataset.
    UNLINKED = 2;
  }

  // Output only. Specifies whether Linked Dataset is currently in a linked
  // state or not.
  LinkState link_state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request format for getting information about a dataset.
message GetDatasetRequest {
  // DatasetView specifies which dataset information is returned.
  enum DatasetView {
    // The default value.
    // Default to the FULL view.
    DATASET_VIEW_UNSPECIFIED = 0;

    // Includes metadata information for the dataset, such as location,
    // etag, lastModifiedTime, etc.
    METADATA = 1;

    // Includes ACL information for the dataset, which defines dataset access
    // for one or more entities.
    ACL = 2;

    // Includes both dataset metadata and ACL information.
    FULL = 3;
  }

  // Required. Project ID of the requested dataset
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of the requested dataset
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Specifies the view that determines which dataset information is
  // returned. By default, metadata and ACL information are returned.
  DatasetView dataset_view = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The version of the access policy schema to fetch.
  // Valid values are 0, 1, and 3. Requests specifying an invalid value will be
  // rejected.
  //
  // Requests for conditional access policy binding in datasets must specify
  // version 3. Dataset with no conditional role bindings in access policy may
  // specify any valid value or leave the field unset.
  //
  // This field will be maped to [IAM Policy version]
  // (https://cloud.google.com/iam/docs/policies#versions) and will be used to
  // fetch policy from IAM.
  //
  // If unset or if 0 or 1 value is used for dataset with conditional bindings,
  // access entry with condition will have role string appended by
  // 'withcond' string followed by a hash value. For example :
  // {
  //   "access": [
  //      {
  //         "role":
  //         "roles/bigquery.dataViewer_with_conditionalbinding_7a34awqsda",
  //         "userByEmail": "<EMAIL>",
  //      }
  //   ]
  // }
  // Please refer https://cloud.google.com/iam/docs/troubleshooting-withcond for
  // more details.
  int32 access_policy_version = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request format for inserting a dataset.
message InsertDatasetRequest {
  // Required. Project ID of the new dataset
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Datasets resource to use for the new dataset
  Dataset dataset = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The version of the provided access policy schema.
  // Valid values are 0, 1, and 3. Requests specifying an invalid value will be
  // rejected.
  //
  // This version refers to the schema version of the access policy and not the
  // version of access policy. This field's value can be equal or more
  // than the access policy schema provided in the request.
  // For example,
  //   * Requests with conditional access policy binding in datasets must
  //   specify
  //     version 3.
  //   * But dataset with no conditional role bindings in access policy
  //     may specify any valid value or leave the field unset.
  // If unset or if 0 or 1 value is used for dataset with conditional
  // bindings, request will be rejected.
  //
  // This field will be maped to IAM Policy version
  // (https://cloud.google.com/iam/docs/policies#versions) and will be used to
  // set policy in IAM.
  int32 access_policy_version = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating or patching a dataset.
message UpdateOrPatchDatasetRequest {
  // Required. Project ID of the dataset being updated
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of the dataset being updated
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Datasets resource which will replace or patch the specified
  // dataset.
  Dataset dataset = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The version of the provided access policy schema.
  // Valid values are 0, 1, and 3. Requests specifying an invalid value will be
  // rejected.
  //
  // This version refers to the schema version of the access policy and not the
  // version of access policy. This field's value can be equal or more
  // than the access policy schema provided in the request.
  // For example,
  //   * Operations updating conditional access policy binding in datasets must
  //   specify
  //     version 3. Some of the operations are :
  //       -  Adding a new access policy entry with condition.
  //       -  Removing an access policy entry with condition.
  //       -  Updating an access policy entry with condition.
  //   * But dataset with no conditional role bindings in access policy
  //     may specify any valid value or leave the field unset.
  // If unset or if 0 or 1 value is used for dataset with conditional
  // bindings, request will be rejected.
  //
  // This field will be maped to IAM Policy version
  // (https://cloud.google.com/iam/docs/policies#versions) and will be used to
  // set policy in IAM.
  int32 access_policy_version = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request format for deleting a dataset.
message DeleteDatasetRequest {
  // Required. Project ID of the dataset being deleted
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of dataset being deleted
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // If True, delete all the tables in the dataset.
  // If False and the dataset contains tables, the request will fail.
  // Default is False
  bool delete_contents = 3;
}

message ListDatasetsRequest {
  // Required. Project ID of the datasets to be listed
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of results to return in a single response page.
  // Leverage the page tokens to iterate through the entire collection.
  google.protobuf.UInt32Value max_results = 2;

  // Page token, returned by a previous call, to request the next page of
  // results
  string page_token = 3;

  // Whether to list all datasets, including hidden ones
  bool all = 4;

  // An expression for filtering the results of the request by label.
  // The syntax is `labels.<name>[:<value>]`.
  // Multiple filters can be ANDed together by connecting with a space.
  // Example: `labels.department:receiving labels.active`.
  // See [Filtering datasets using
  // labels](https://cloud.google.com/bigquery/docs/filtering-labels#filtering_datasets_using_labels)
  // for details.
  string filter = 5;
}

// A dataset resource with only a subset of fields, to be returned in a list of
// datasets.
message ListFormatDataset {
  // The resource type.
  // This property always returns the value "bigquery#dataset"
  string kind = 1;

  // The fully-qualified, unique, opaque ID of the dataset.
  string id = 2;

  // The dataset reference.
  // Use this property to access specific parts of the dataset's ID, such as
  // project ID or dataset ID.
  DatasetReference dataset_reference = 3;

  // The labels associated with this dataset.
  // You can use these to organize and group your datasets.
  map<string, string> labels = 4;

  // An alternate name for the dataset.  The friendly name is purely
  // decorative in nature.
  google.protobuf.StringValue friendly_name = 5;

  // The geographic location where the dataset resides.
  string location = 6;
}

// Response format for a page of results when listing datasets.
message DatasetList {
  // Output only. The resource type.
  // This property always returns the value "bigquery#datasetList"
  string kind = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A hash value of the results page. You can use this property to
  // determine if the page has changed since the last request.
  string etag = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A token that can be used to request the next results page. This property is
  // omitted on the final results page.
  string next_page_token = 3;

  // An array of the dataset resources in the project.
  // Each resource contains basic information.
  // For full information about a particular dataset resource, use the Datasets:
  // get method. This property is omitted when there are no datasets in the
  // project.
  repeated ListFormatDataset datasets = 4;

  // A list of skipped locations that were unreachable. For more information
  // about BigQuery locations, see:
  // https://cloud.google.com/bigquery/docs/locations. Example: "europe-west5"
  repeated string unreachable = 5;
}

// Request format for undeleting a dataset.
message UndeleteDatasetRequest {
  // Required. Project ID of the dataset to be undeleted
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Dataset ID of dataset being deleted
  string dataset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The exact time when the dataset was deleted. If not specified,
  // the most recently deleted version is undeleted. Undeleting a dataset
  // using deletion time is not supported.
  google.protobuf.Timestamp deletion_time = 3
      [(google.api.field_behavior) = OPTIONAL];
}
