{"methodConfig": [{"name": [{"service": "google.cloud.bigquery.v2.DatasetService", "method": "GetDataset"}, {"service": "google.cloud.bigquery.v2.DatasetService", "method": "InsertDataset"}, {"service": "google.cloud.bigquery.v2.DatasetService", "method": "PatchDataset"}, {"service": "google.cloud.bigquery.v2.DatasetService", "method": "UpdateDataset"}, {"service": "google.cloud.bigquery.v2.JobService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.bigquery.v2.JobService", "method": "Get<PERSON>ob"}, {"service": "google.cloud.bigquery.v2.JobService", "method": "DeleteJob"}, {"service": "google.cloud.bigquery.v2.ModelService", "method": "ListModels"}, {"service": "google.cloud.bigquery.v2.ModelService", "method": "PatchModel"}, {"service": "google.cloud.bigquery.v2.ModelService", "method": "DeleteModel"}, {"service": "google.cloud.bigquery.v2.ProjectService", "method": "GetServiceAccount"}, {"service": "google.cloud.bigquery.v2.RoutineService", "method": "GetRoutine"}, {"service": "google.cloud.bigquery.v2.RoutineService", "method": "ListRoutines"}, {"service": "google.cloud.bigquery.v2.RowAccessPolicyService", "method": "BatchDeleteRowAccessPolicies"}, {"service": "google.cloud.bigquery.v2.RowAccessPolicyService", "method": "CreateRowAccessPolicy"}, {"service": "google.cloud.bigquery.v2.RowAccessPolicyService", "method": "DeleteRowAccessPolicy"}, {"service": "google.cloud.bigquery.v2.RowAccessPolicyService", "method": "GetRowAccessPolicy"}, {"service": "google.cloud.bigquery.v2.RowAccessPolicyService", "method": "ListRowAccessPolicies"}, {"service": "google.cloud.bigquery.v2.RowAccessPolicyService", "method": "UpdateRowAccessPolicy"}, {"service": "google.cloud.bigquery.v2.TableService", "method": "GetTable"}, {"service": "google.cloud.bigquery.v2.TableService", "method": "ListTables"}], "timeout": "64s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}, {"name": [{"service": "google.cloud.bigquery.v2.DatasetService", "method": "DeleteDataset"}, {"service": "google.cloud.bigquery.v2.DatasetService", "method": "ListDatasets"}, {"service": "google.cloud.bigquery.v2.DatasetService", "method": "UndeleteDataset"}, {"service": "google.cloud.bigquery.v2.JobService", "method": "InsertJob"}, {"service": "google.cloud.bigquery.v2.JobService", "method": "ListJobs"}, {"service": "google.cloud.bigquery.v2.JobService", "method": "Query"}, {"service": "google.cloud.bigquery.v2.JobService", "method": "GetQueryResults"}, {"service": "google.cloud.bigquery.v2.RoutineService", "method": "InsertRoutine"}, {"service": "google.cloud.bigquery.v2.RoutineService", "method": "UpdateRoutine"}, {"service": "google.cloud.bigquery.v2.RoutineService", "method": "PatchRoutine"}, {"service": "google.cloud.bigquery.v2.RoutineService", "method": "DeleteRoutine"}, {"service": "google.cloud.bigquery.v2.TableDataService", "method": "List"}, {"service": "google.cloud.bigquery.v2.TableService", "method": "InsertTable"}, {"service": "google.cloud.bigquery.v2.TableService", "method": "PatchTable"}, {"service": "google.cloud.bigquery.v2.TableService", "method": "UpdateTable"}, {"service": "google.cloud.bigquery.v2.TableService", "method": "DeleteTable"}], "timeout": "240s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}]}