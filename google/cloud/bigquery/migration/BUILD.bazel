# This build file includes a target for the Ruby wrapper library for
# google-cloud-bigquery-migration.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for bigquerymigration.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "migration_ruby_wrapper",
    srcs = ["//google/cloud/bigquery/migration/v2:migration_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigquery-migration",
        "ruby-cloud-wrapper-of=v2:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs/reference/migration",
        "ruby-cloud-api-id=bigquerymigration.googleapis.com",
        "ruby-cloud-api-shortname=bigquerymigration",
    ],
    ruby_cloud_description = "The BigQuery Migration Service is a comprehensive solution for migrating your data warehouse to BigQuery.",
    ruby_cloud_title = "BigQuery Migration",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-migration-ruby",
    deps = [
        ":migration_ruby_wrapper",
    ],
)
