type: google.api.Service
config_version: 3
name: bigquerymigration.googleapis.com
title: BigQuery Migration API

apis:
- name: google.cloud.bigquery.migration.v2alpha.MigrationService
- name: google.cloud.bigquery.migration.v2alpha.SqlTranslationService

documentation:
  summary: |-
    The migration service, exposing apis for migration jobs operations, and
    agent management.

backend:
  rules:
  - selector: 'google.cloud.bigquery.migration.v2alpha.MigrationService.*'
    deadline: 60.0
  - selector: google.cloud.bigquery.migration.v2alpha.SqlTranslationService.TranslateQuery
    deadline: 60.0

authentication:
  rules:
  - selector: 'google.cloud.bigquery.migration.v2alpha.MigrationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.bigquery.migration.v2alpha.SqlTranslationService.TranslateQuery
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
