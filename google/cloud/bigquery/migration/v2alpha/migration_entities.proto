// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/bigquery/migration/v2alpha/assessment_task.proto";
import "google/cloud/bigquery/migration/v2alpha/migration_error_details.proto";
import "google/cloud/bigquery/migration/v2alpha/migration_metrics.proto";
import "google/cloud/bigquery/migration/v2alpha/translation_task.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/error_details.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2Alpha";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2alpha/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "MigrationEntitiesProto";
option java_package = "com.google.cloud.bigquery.migration.v2alpha";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2alpha";

// A migration workflow which specifies what needs to be done for an EDW
// migration.
message MigrationWorkflow {
  option (google.api.resource) = {
    type: "bigquerymigration.googleapis.com/MigrationWorkflow"
    pattern: "projects/{project}/locations/{location}/workflows/{workflow}"
  };

  // Possible migration workflow states.
  enum State {
    // Workflow state is unspecified.
    STATE_UNSPECIFIED = 0;

    // Workflow is in draft status, i.e. tasks are not yet eligible for
    // execution.
    DRAFT = 1;

    // Workflow is running (i.e. tasks are eligible for execution).
    RUNNING = 2;

    // Workflow is paused. Tasks currently in progress may continue, but no
    // further tasks will be scheduled.
    PAUSED = 3;

    // Workflow is complete. There should not be any task in a non-terminal
    // state, but if they are (e.g. forced termination), they will not be
    // scheduled.
    COMPLETED = 4;
  }

  // Output only. Immutable. The unique identifier for the migration workflow. The ID is
  // server-generated.
  //
  // Example: `projects/123/locations/us/workflows/345`
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The display name of the workflow. This can be set to give a workflow
  // a descriptive name. There is no guarantee or enforcement of uniqueness.
  string display_name = 6;

  // The tasks in a workflow in a named map. The name (i.e. key) has no
  // meaning and is merely a convenient way to address a specific task
  // in a workflow.
  map<string, MigrationTask> tasks = 2;

  // Output only. That status of the workflow.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Time when the workflow was created.
  google.protobuf.Timestamp create_time = 4;

  // Time when the workflow was last updated.
  google.protobuf.Timestamp last_update_time = 5;
}

// A single task for a migration which has details about the configuration of
// the task.
message MigrationTask {
  // Possible states of a migration task.
  enum State {
    // The state is unspecified.
    STATE_UNSPECIFIED = 0;

    // The task is waiting for orchestration.
    PENDING = 1;

    // The task is assigned to an orchestrator.
    ORCHESTRATING = 2;

    // The task is running, i.e. its subtasks are ready for execution.
    RUNNING = 3;

    // Tha task is paused. Assigned subtasks can continue, but no new subtasks
    // will be scheduled.
    PAUSED = 4;

    // The task finished successfully.
    SUCCEEDED = 5;

    // The task finished unsuccessfully.
    FAILED = 6;
  }

  // The details of the task.
  oneof task_details {
    // Task configuration for Assessment.
    AssessmentTaskDetails assessment_task_details = 12;

    // Task configuration for Batch/Offline SQL Translation.
    TranslationTaskDetails translation_task_details = 13;
  }

  // Output only. Immutable. The unique identifier for the migration task. The ID is server-generated.
  string id = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The type of the task. This must be a supported task type.
  string type = 2;

  // DEPRECATED! Use one of the task_details below.
  // The details of the task. The type URL must be one of the supported task
  // details messages and correspond to the Task's type.
  google.protobuf.Any details = 3;

  // Output only. The current state of the task.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An explanation that may be populated when the task is in FAILED state.
  google.rpc.ErrorInfo processing_error = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Time when the task was created.
  google.protobuf.Timestamp create_time = 6;

  // Time when the task was last updated.
  google.protobuf.Timestamp last_update_time = 7;

  // Output only. Additional information about the orchestration.
  MigrationTaskOrchestrationResult orchestration_result = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A subtask for a migration which carries details about the configuration of
// the subtask. The content of the details should not matter to the end user,
// but is a contract between the subtask creator and subtask worker.
message MigrationSubtask {
  option (google.api.resource) = {
    type: "bigquerymigration.googleapis.com/MigrationSubtask"
    pattern: "projects/{project}/locations/{location}/workflows/{workflow}/subtasks/{subtask}"
  };

  // Possible states of a migration subtask.
  enum State {
    // The state is unspecified.
    STATE_UNSPECIFIED = 0;

    // The subtask is ready, i.e. it is ready for execution.
    ACTIVE = 1;

    // The subtask is running, i.e. it is assigned to a worker for execution.
    RUNNING = 2;

    // The subtask finished successfully.
    SUCCEEDED = 3;

    // The subtask finished unsuccessfully.
    FAILED = 4;

    // The subtask is paused, i.e., it will not be scheduled. If it was already
    // assigned,it might still finish but no new lease renewals will be granted.
    PAUSED = 5;
  }

  // Output only. Immutable. The resource name for the migration subtask. The ID is
  // server-generated.
  //
  // Example: `projects/123/locations/us/workflows/345/subtasks/678`
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The unique ID of the task to which this subtask belongs.
  string task_id = 2;

  // The type of the Subtask. The migration service does not check whether this
  // is a known type. It is up to the task creator (i.e. orchestrator or worker)
  // to ensure it only creates subtasks for which there are compatible workers
  // polling for Subtasks.
  string type = 3;

  // Output only. The current state of the subtask.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An explanation that may be populated when the task is in FAILED state.
  google.rpc.ErrorInfo processing_error = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides details to errors and issues encountered while processing the
  // subtask. Presence of error details does not mean that the subtask failed.
  repeated ResourceErrorDetail resource_error_details = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The number or resources with errors. Note: This is not the total
  // number of errors as each resource can have more than one error.
  // This is used to indicate truncation by having a `resource_error_count`
  // that is higher than the size of `resource_error_details`.
  int32 resource_error_count = 13;

  // Time when the subtask was created.
  google.protobuf.Timestamp create_time = 7;

  // Time when the subtask was last updated.
  google.protobuf.Timestamp last_update_time = 8;

  // The metrics for the subtask.
  repeated TimeSeries metrics = 11;
}

// Additional information from the orchestrator when it is done with the
// task orchestration.
message MigrationTaskOrchestrationResult {
  // Details specific to the task type.
  oneof details {
    // Details specific to assessment task types.
    AssessmentOrchestrationResultDetails assessment_details = 1;
  }
}
