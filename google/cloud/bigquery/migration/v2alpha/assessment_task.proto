// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2alpha;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2Alpha";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2alpha/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "AssessmentTaskProto";
option java_package = "com.google.cloud.bigquery.migration.v2alpha";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2alpha";

// Assessment task config.
message AssessmentTaskDetails {
  // Required. The Cloud Storage path for assessment input files.
  string input_path = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The BigQuery dataset for output.
  string output_dataset = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional Cloud Storage path to write the query logs (which is
  // then used as an input path on the translation task)
  string querylogs_path = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. The data source or data warehouse type (eg: TERADATA/REDSHIFT)
  // from which the input data is extracted.
  string data_source = 4 [(google.api.field_behavior) = REQUIRED];
}

// Details for an assessment task orchestration result.
message AssessmentOrchestrationResultDetails {
  // Optional. The version used for the output table schemas.
  string output_tables_schema_version = 1 [(google.api.field_behavior) = OPTIONAL];
}
