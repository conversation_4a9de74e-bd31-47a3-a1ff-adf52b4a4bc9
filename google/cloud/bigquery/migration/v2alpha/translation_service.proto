// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2Alpha";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2alpha/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "TranslationServiceProto";
option java_package = "com.google.cloud.bigquery.migration.v2alpha";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2alpha";

// Provides other SQL dialects to GoogleSQL translation operations.
service SqlTranslationService {
  option (google.api.default_host) = "bigquerymigration.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Translates input queries from source dialects to GoogleSQL.
  rpc TranslateQuery(TranslateQueryRequest) returns (TranslateQueryResponse) {
    option (google.api.http) = {
      post: "/v2alpha/{parent=projects/*/locations/*}:translateQuery"
      body: "*"
    };
    option (google.api.method_signature) = "parent,source_dialect,query";
  }
}

// The request of translating a SQL query to Standard SQL.
message TranslateQueryRequest {
  // Supported SQL translation source dialects.
  enum SqlTranslationSourceDialect {
    // SqlTranslationSourceDialect not specified.
    SQL_TRANSLATION_SOURCE_DIALECT_UNSPECIFIED = 0;

    // Teradata SQL.
    TERADATA = 1;
  }

  // Required. The name of the project to which this translation request belongs.
  // Example: `projects/foo/locations/bar`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The source SQL dialect of `queries`.
  SqlTranslationSourceDialect source_dialect = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The query to be translated.
  string query = 3 [(google.api.field_behavior) = REQUIRED];
}

// The response of translating a SQL query to Standard SQL.
message TranslateQueryResponse {
  // Output only. Immutable. The unique identifier for the SQL translation job.
  // Example: `projects/123/locations/us/translation/1234`
  string translation_job = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The translated result. This will be empty if the translation fails.
  string translated_query = 1;

  // The list of errors encountered during the translation, if present.
  repeated SqlTranslationError errors = 2;

  // The list of warnings encountered during the translation, if present,
  // indicates non-semantically correct translation.
  repeated SqlTranslationWarning warnings = 3;
}

// Structured error object capturing the error message and the location in the
// source text where the error occurs.
message SqlTranslationErrorDetail {
  // Specifies the row from the source text where the error occurred.
  int64 row = 1;

  // Specifie the column from the source texts where the error occurred.
  int64 column = 2;

  // A human-readable description of the error.
  string message = 3;
}

// The detailed error object if the SQL translation job fails.
message SqlTranslationError {
  // The error type of the SQL translation job.
  enum SqlTranslationErrorType {
    // SqlTranslationErrorType not specified.
    SQL_TRANSLATION_ERROR_TYPE_UNSPECIFIED = 0;

    // Failed to parse the input text as a SQL query.
    SQL_PARSE_ERROR = 1;

    // Found unsupported functions in the input SQL query that are not able to
    // translate.
    UNSUPPORTED_SQL_FUNCTION = 2;
  }

  // The type of SQL translation error.
  SqlTranslationErrorType error_type = 1;

  // Specifies the details of the error, including the error message and
  // location from the source text.
  SqlTranslationErrorDetail error_detail = 2;
}

// The detailed warning object if the SQL translation job is completed but not
// semantically correct.
message SqlTranslationWarning {
  // Specifies the details of the warning, including the warning message and
  // location from the source text.
  SqlTranslationErrorDetail warning_detail = 1;
}
