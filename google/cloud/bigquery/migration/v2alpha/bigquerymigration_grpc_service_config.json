{"methodConfig": [{"name": [{"service": "google.cloud.bigquery.migration.v2alpha.MigrationService", "method": "GetMigrationWorkflow"}, {"service": "google.cloud.bigquery.migration.v2alpha.MigrationService", "method": "ListMigrationWorkflows"}, {"service": "google.cloud.bigquery.migration.v2alpha.MigrationService", "method": "StartMigrationWorkflow"}, {"service": "google.cloud.bigquery.migration.v2alpha.MigrationService", "method": "GetMigrationSubtask"}, {"service": "google.cloud.bigquery.migration.v2alpha.MigrationService", "method": "ListMigrationSubtasks"}], "timeout": "120s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.bigquery.migration.v2alpha.MigrationService", "method": "CreateMigrationWorkflow"}, {"service": "google.cloud.bigquery.migration.v2alpha.MigrationService", "method": "DeleteMigrationWorkflow"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.bigquery.migration.v2alpha.SqlTranslationService", "method": "Translate"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 3, "initialBackoff": "0.100s", "maxBackoff": "1s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}