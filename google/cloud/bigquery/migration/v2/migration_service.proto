// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/bigquery/migration/v2/migration_entities.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "MigrationServiceProto";
option java_package = "com.google.cloud.bigquery.migration.v2";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2";

// Service to handle EDW migrations.
service MigrationService {
  option (google.api.default_host) = "bigquerymigration.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a migration workflow.
  rpc CreateMigrationWorkflow(CreateMigrationWorkflowRequest)
      returns (MigrationWorkflow) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/workflows"
      body: "migration_workflow"
    };
    option (google.api.method_signature) = "parent,migration_workflow";
  }

  // Gets a previously created migration workflow.
  rpc GetMigrationWorkflow(GetMigrationWorkflowRequest)
      returns (MigrationWorkflow) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/workflows/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists previously created migration workflow.
  rpc ListMigrationWorkflows(ListMigrationWorkflowsRequest)
      returns (ListMigrationWorkflowsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/workflows"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a migration workflow by name.
  rpc DeleteMigrationWorkflow(DeleteMigrationWorkflowRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/workflows/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Starts a previously created migration workflow. I.e., the state transitions
  // from DRAFT to RUNNING. This is a no-op if the state is already RUNNING.
  // An error will be signaled if the state is anything other than DRAFT or
  // RUNNING.
  rpc StartMigrationWorkflow(StartMigrationWorkflowRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/workflows/*}:start"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a previously created migration subtask.
  rpc GetMigrationSubtask(GetMigrationSubtaskRequest)
      returns (MigrationSubtask) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/workflows/*/subtasks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists previously created migration subtasks.
  rpc ListMigrationSubtasks(ListMigrationSubtasksRequest)
      returns (ListMigrationSubtasksResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/workflows/*}/subtasks"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request to create a migration workflow resource.
message CreateMigrationWorkflowRequest {
  // Required. The name of the project to which this migration workflow belongs.
  // Example: `projects/foo/locations/bar`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The migration workflow to create.
  MigrationWorkflow migration_workflow = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A request to get a previously created migration workflow.
message GetMigrationWorkflowRequest {
  // Required. The unique identifier for the migration workflow.
  // Example: `projects/123/locations/us/workflows/1234`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigquerymigration.googleapis.com/MigrationWorkflow"
    }
  ];

  // The list of fields to be retrieved.
  google.protobuf.FieldMask read_mask = 2;
}

// A request to list previously created migration workflows.
message ListMigrationWorkflowsRequest {
  // Required. The project and location of the migration workflows to list.
  // Example: `projects/123/locations/us`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The list of fields to be retrieved.
  google.protobuf.FieldMask read_mask = 2;

  // The maximum number of migration workflows to return. The service may return
  // fewer than this number.
  int32 page_size = 3;

  // A page token, received from previous `ListMigrationWorkflows` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListMigrationWorkflows`
  // must match the call that provided the page token.
  string page_token = 4;
}

// Response object for a `ListMigrationWorkflows` call.
message ListMigrationWorkflowsResponse {
  // The migration workflows for the specified project / location.
  repeated MigrationWorkflow migration_workflows = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// A request to delete a previously created migration workflow.
message DeleteMigrationWorkflowRequest {
  // Required. The unique identifier for the migration workflow.
  // Example: `projects/123/locations/us/workflows/1234`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigquerymigration.googleapis.com/MigrationWorkflow"
    }
  ];
}

// A request to start a previously created migration workflow.
message StartMigrationWorkflowRequest {
  // Required. The unique identifier for the migration workflow.
  // Example: `projects/123/locations/us/workflows/1234`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigquerymigration.googleapis.com/MigrationWorkflow"
    }
  ];
}

// A request to get a previously created migration subtasks.
message GetMigrationSubtaskRequest {
  // Required. The unique identifier for the migration subtask.
  // Example: `projects/123/locations/us/workflows/1234/subtasks/543`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigquerymigration.googleapis.com/MigrationSubtask"
    }
  ];

  // Optional. The list of fields to be retrieved.
  google.protobuf.FieldMask read_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// A request to list previously created migration subtasks.
message ListMigrationSubtasksRequest {
  // Required. The migration task of the subtasks to list.
  // Example: `projects/123/locations/us/workflows/1234`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigquerymigration.googleapis.com/MigrationWorkflow"
    }
  ];

  // Optional. The list of fields to be retrieved.
  google.protobuf.FieldMask read_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of migration tasks to return. The service may
  // return fewer than this number.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from previous `ListMigrationSubtasks`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListMigrationSubtasks`
  // must match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter to apply. This can be used to get the subtasks of a
  // specific tasks in a workflow, e.g. `migration_task = "ab012"` where
  // `"ab012"` is the task ID (not the name in the named map).
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response object for a `ListMigrationSubtasks` call.
message ListMigrationSubtasksResponse {
  // The migration subtasks for the specified task.
  repeated MigrationSubtask migration_subtasks = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
