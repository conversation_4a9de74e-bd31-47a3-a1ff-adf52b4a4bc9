// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "TranslationSuggestionProto";
option java_package = "com.google.cloud.bigquery.migration.v2";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2";

// Details about a record.
message TranslationReportRecord {
  // The severity type of the record.
  enum Severity {
    // SeverityType not specified.
    SEVERITY_UNSPECIFIED = 0;

    // INFO type.
    INFO = 1;

    // WARNING type. The translated query may still provide useful information
    // if all the report records are WARNING.
    WARNING = 2;

    // ERROR type. Translation failed.
    ERROR = 3;
  }

  // Severity of the translation record.
  Severity severity = 1;

  // Specifies the row from the source text where the error occurred (0 based).
  // Example: 2
  int32 script_line = 2;

  // Specifies the column from the source texts where the error occurred. (0
  // based) example: 6
  int32 script_column = 3;

  // Category of the error/warning. Example: SyntaxError
  string category = 4;

  // Detailed message of the record.
  string message = 5;
}
