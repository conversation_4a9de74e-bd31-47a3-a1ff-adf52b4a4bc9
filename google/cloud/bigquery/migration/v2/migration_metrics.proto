// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2;

import "google/api/distribution.proto";
import "google/api/field_behavior.proto";
import "google/api/metric.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "MigrationMetricsProto";
option java_package = "com.google.cloud.bigquery.migration.v2";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2";

// The metrics object for a SubTask.
message TimeSeries {
  // Required. The name of the metric.
  //
  // If the metric is not known by the service yet, it will be auto-created.
  string metric = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The value type of the time series.
  google.api.MetricDescriptor.ValueType value_type = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The metric kind of the time series.
  //
  // If present, it must be the same as the metric kind of the associated
  // metric. If the associated metric's descriptor must be auto-created, then
  // this field specifies the metric kind of the new descriptor and must be
  // either `GAUGE` (the default) or `CUMULATIVE`.
  google.api.MetricDescriptor.MetricKind metric_kind = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The data points of this time series. When listing time series,
  // points are returned in reverse time order.
  //
  // When creating a time series, this field must contain exactly one point and
  // the point's type must be the same as the value type of the associated
  // metric. If the associated metric's descriptor must be auto-created, then
  // the value type of the descriptor is determined by the point's type, which
  // must be `BOOL`, `INT64`, `DOUBLE`, or `DISTRIBUTION`.
  repeated Point points = 4 [(google.api.field_behavior) = REQUIRED];
}

// A single data point in a time series.
message Point {
  // The time interval to which the data point applies.  For `GAUGE` metrics,
  // the start time does not need to be supplied, but if it is supplied, it must
  // equal the end time.  For `DELTA` metrics, the start and end time should
  // specify a non-zero interval, with subsequent points specifying contiguous
  // and non-overlapping intervals.  For `CUMULATIVE` metrics, the start and end
  // time should specify a non-zero interval, with subsequent points specifying
  // the same start time and increasing end times, until an event resets the
  // cumulative value to zero and sets a new start time for the following
  // points.
  TimeInterval interval = 1;

  // The value of the data point.
  TypedValue value = 2;
}

// A time interval extending just after a start time through an end time.
// If the start time is the same as the end time, then the interval
// represents a single point in time.
message TimeInterval {
  // Optional. The beginning of the time interval.  The default value
  // for the start time is the end time. The start time must not be
  // later than the end time.
  google.protobuf.Timestamp start_time = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The end of the time interval.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A single strongly-typed value.
message TypedValue {
  // The typed value field.
  oneof value {
    // A Boolean value: `true` or `false`.
    bool bool_value = 1;

    // A 64-bit integer. Its range is approximately `+/-9.2x10^18`.
    int64 int64_value = 2;

    // A 64-bit double-precision floating-point number. Its magnitude
    // is approximately `+/-10^(+/-300)` and it has 16 significant digits of
    // precision.
    double double_value = 3;

    // A variable-length string value.
    string string_value = 4;

    // A distribution value.
    google.api.Distribution distribution_value = 5;
  }
}
