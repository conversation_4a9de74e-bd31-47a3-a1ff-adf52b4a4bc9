// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "TranslationConfigProto";
option java_package = "com.google.cloud.bigquery.migration.v2";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2";

// The translation config to capture necessary settings for a translation task
// and subtask.
message TranslationConfigDetails {
  // The chosen path where the source for input files will be found.
  oneof source_location {
    // The Cloud Storage path for a directory of files to translate in a task.
    string gcs_source_path = 1;
  }

  // The chosen path where the destination for output files will be found.
  oneof target_location {
    // The Cloud Storage path to write back the corresponding input files to.
    string gcs_target_path = 2;
  }

  // The mapping of full SQL object names from their current state to the
  // desired output.
  oneof output_name_mapping {
    // The mapping of objects to their desired output names in list form.
    ObjectNameMappingList name_mapping_list = 5;
  }

  // The dialect of the input files.
  Dialect source_dialect = 3;

  // The target dialect for the engine to translate the input to.
  Dialect target_dialect = 4;

  // The default source environment values for the translation.
  SourceEnv source_env = 6;

  // The indicator to show translation request initiator.
  string request_source = 8;

  // The types of output to generate, e.g. sql, metadata etc. If not specified,
  // a default set of targets will be generated. Some additional target types
  // may be slower to generate. See the documentation for the set of available
  // target types.
  repeated string target_types = 9;
}

// The possible dialect options for translation.
message Dialect {
  // The possible dialect options that this message represents.
  oneof dialect_value {
    // The BigQuery dialect
    BigQueryDialect bigquery_dialect = 1;

    // The HiveQL dialect
    HiveQLDialect hiveql_dialect = 2;

    // The Redshift dialect
    RedshiftDialect redshift_dialect = 3;

    // The Teradata dialect
    TeradataDialect teradata_dialect = 4;

    // The Oracle dialect
    OracleDialect oracle_dialect = 5;

    // The SparkSQL dialect
    SparkSQLDialect sparksql_dialect = 6;

    // The Snowflake dialect
    SnowflakeDialect snowflake_dialect = 7;

    // The Netezza dialect
    NetezzaDialect netezza_dialect = 8;

    // The Azure Synapse dialect
    AzureSynapseDialect azure_synapse_dialect = 9;

    // The Vertica dialect
    VerticaDialect vertica_dialect = 10;

    // The SQL Server dialect
    SQLServerDialect sql_server_dialect = 11;

    // The Postgresql dialect
    PostgresqlDialect postgresql_dialect = 12;

    // The Presto dialect
    PrestoDialect presto_dialect = 13;

    // The MySQL dialect
    MySQLDialect mysql_dialect = 14;

    // DB2 dialect
    DB2Dialect db2_dialect = 15;

    // SQLite dialect
    SQLiteDialect sqlite_dialect = 16;

    // Greenplum dialect
    GreenplumDialect greenplum_dialect = 17;
  }
}

// The dialect definition for BigQuery.
message BigQueryDialect {}

// The dialect definition for HiveQL.
message HiveQLDialect {}

// The dialect definition for Redshift.
message RedshiftDialect {}

// The dialect definition for Teradata.
message TeradataDialect {
  // The sub-dialect options for Teradata.
  enum Mode {
    // Unspecified mode.
    MODE_UNSPECIFIED = 0;

    // Teradata SQL mode.
    SQL = 1;

    // BTEQ mode (which includes SQL).
    BTEQ = 2;
  }

  // Which Teradata sub-dialect mode the user specifies.
  Mode mode = 1;
}

// The dialect definition for Oracle.
message OracleDialect {}

// The dialect definition for SparkSQL.
message SparkSQLDialect {}

// The dialect definition for Snowflake.
message SnowflakeDialect {}

// The dialect definition for Netezza.
message NetezzaDialect {}

// The dialect definition for Azure Synapse.
message AzureSynapseDialect {}

// The dialect definition for Vertica.
message VerticaDialect {}

// The dialect definition for SQL Server.
message SQLServerDialect {}

// The dialect definition for Postgresql.
message PostgresqlDialect {}

// The dialect definition for Presto.
message PrestoDialect {}

// The dialect definition for MySQL.
message MySQLDialect {}

// The dialect definition for DB2.
message DB2Dialect {}

// The dialect definition for SQLite.
message SQLiteDialect {}

// The dialect definition for Greenplum.
message GreenplumDialect {}

// Represents a map of name mappings using a list of key:value proto messages of
// existing name to desired output name.
message ObjectNameMappingList {
  // The elements of the object name map.
  repeated ObjectNameMapping name_map = 1;
}

// Represents a key-value pair of NameMappingKey to NameMappingValue to
// represent the mapping of SQL names from the input value to desired output.
message ObjectNameMapping {
  // The name of the object in source that is being mapped.
  NameMappingKey source = 1;

  // The desired target name of the object that is being mapped.
  NameMappingValue target = 2;
}

// The potential components of a full name mapping that will be mapped
// during translation in the source data warehouse.
message NameMappingKey {
  // The type of the object that is being mapped.
  enum Type {
    // Unspecified name mapping type.
    TYPE_UNSPECIFIED = 0;

    // The object being mapped is a database.
    DATABASE = 1;

    // The object being mapped is a schema.
    SCHEMA = 2;

    // The object being mapped is a relation.
    RELATION = 3;

    // The object being mapped is an attribute.
    ATTRIBUTE = 4;

    // The object being mapped is a relation alias.
    RELATION_ALIAS = 5;

    // The object being mapped is a an attribute alias.
    ATTRIBUTE_ALIAS = 6;

    // The object being mapped is a function.
    FUNCTION = 7;
  }

  // The type of object that is being mapped.
  Type type = 1;

  // The database name (BigQuery project ID equivalent in the source data
  // warehouse).
  string database = 2;

  // The schema name (BigQuery dataset equivalent in the source data warehouse).
  string schema = 3;

  // The relation name (BigQuery table or view equivalent in the source data
  // warehouse).
  string relation = 4;

  // The attribute name (BigQuery column equivalent in the source data
  // warehouse).
  string attribute = 5;
}

// The potential components of a full name mapping that will be mapped
// during translation in the target data warehouse.
message NameMappingValue {
  // The database name (BigQuery project ID equivalent in the target data
  // warehouse).
  string database = 1;

  // The schema name (BigQuery dataset equivalent in the target data warehouse).
  string schema = 2;

  // The relation name (BigQuery table or view equivalent in the target data
  // warehouse).
  string relation = 3;

  // The attribute name (BigQuery column equivalent in the target data
  // warehouse).
  string attribute = 4;
}

// Represents the default source environment values for the translation.
message SourceEnv {
  // The default database name to fully qualify SQL objects when their database
  // name is missing.
  string default_database = 1;

  // The schema search path. When SQL objects are missing schema name,
  // translation engine will search through this list to find the value.
  repeated string schema_search_path = 2;

  // Optional. Expects a valid BigQuery dataset ID that exists, e.g.,
  // project-123.metadata_store_123.  If specified, translation will search and
  // read the required schema information from a metadata store in this dataset.
  // If metadata store doesn't exist, translation will parse the metadata file
  // and upload the schema info to a temp table in the dataset to speed up
  // future translation jobs.
  string metadata_store_dataset = 3 [(google.api.field_behavior) = OPTIONAL];
}
