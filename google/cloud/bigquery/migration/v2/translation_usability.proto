// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "TranslationUsabilityProto";
option java_package = "com.google.cloud.bigquery.migration.v2";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2";

// A record in the aggregate CSV report for a migration workflow
message GcsReportLogMessage {
  // Severity of the translation record.
  string severity = 1;

  // Category of the error/warning. Example: SyntaxError
  string category = 2;

  // The file path in which the error occurred
  string file_path = 3;

  // The file name in which the error occurred
  string filename = 4;

  // Specifies the row from the source text where the error occurred (0 based,
  // -1 for messages without line location). Example: 2
  int32 source_script_line = 5;

  // Specifies the column from the source texts where the error occurred. (0
  // based, -1 for messages without column location) example: 6
  int32 source_script_column = 6;

  // Detailed message of the record.
  string message = 7;

  // The script context (obfuscated) in which the error occurred
  string script_context = 8;

  // Category of the error/warning. Example: SyntaxError
  string action = 9;

  // Effect of the error/warning. Example: COMPATIBILITY
  string effect = 10;

  // Name of the affected object in the log message.
  string object_name = 11;
}
