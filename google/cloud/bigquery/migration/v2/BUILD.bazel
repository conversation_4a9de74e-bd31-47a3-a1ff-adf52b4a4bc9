# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "migration_proto",
    srcs = [
        "migration_entities.proto",
        "migration_error_details.proto",
        "migration_metrics.proto",
        "migration_service.proto",
        "translation_config.proto",
        "translation_details.proto",
        "translation_suggestion.proto",
        "translation_usability.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:distribution_proto",
        "//google/api:field_behavior_proto",
        "//google/api:metric_proto",
        "//google/api:resource_proto",
        "//google/rpc:error_details_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "migration_proto_with_info",
    deps = [
        ":migration_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "migration_java_proto",
    deps = [":migration_proto"],
)

java_grpc_library(
    name = "migration_java_grpc",
    srcs = [":migration_proto"],
    deps = [":migration_java_proto"],
)

java_gapic_library(
    name = "migration_java_gapic",
    srcs = [":migration_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "bigquerymigration_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "bigquerymigration_v2.yaml",
    test_deps = [
        ":migration_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":migration_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "migration_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.migration.v2.MigrationServiceClientHttpJsonTest",
        "com.google.cloud.bigquery.migration.v2.MigrationServiceClientTest",
    ],
    runtime_deps = [":migration_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-migration-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":migration_java_gapic",
        ":migration_java_grpc",
        ":migration_java_proto",
        ":migration_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "migration_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/migration/apiv2/migrationpb",
    protos = [":migration_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:distribution_go_proto",
        "//google/api:metric_go_proto",
        "//google/rpc:errdetails_go_proto",
    ],
)

go_gapic_library(
    name = "migration_go_gapic",
    srcs = [":migration_proto_with_info"],
    grpc_service_config = "bigquerymigration_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/migration/apiv2;migration",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "bigquerymigration_v2.yaml",
    transport = "grpc",
    deps = [
        ":migration_go_proto",
        "//google/api:metric_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-migration-v2-go",
    deps = [
        ":migration_go_gapic",
        ":migration_go_gapic_srcjar-metadata.srcjar",
        ":migration_go_gapic_srcjar-snippets.srcjar",
        ":migration_go_gapic_srcjar-test.srcjar",
        ":migration_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "migration_py_gapic",
    srcs = [":migration_proto"],
    grpc_service_config = "bigquerymigration_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=bigquery_migration",
        "python-gapic-namespace=google.cloud",
    ],
    rest_numeric_enums = False,
    service_yaml = "bigquerymigration_v2.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "migration_py_gapic_test",
    srcs = [
        "migration_py_gapic_pytest.py",
        "migration_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":migration_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-migration-v2-py",
    deps = [
        ":migration_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "migration_php_proto",
    deps = [":migration_proto"],
)

php_gapic_library(
    name = "migration_php_gapic",
    srcs = [":migration_proto_with_info"],
    grpc_service_config = "bigquerymigration_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "bigquerymigration_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":migration_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-migration-v2-php",
    deps = [
        ":migration_php_gapic",
        ":migration_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "migration_nodejs_gapic",
    package_name = "@google-cloud/bigquery-migration",
    src = ":migration_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "bigquerymigration_grpc_service_config.json",
    package = "google.cloud.bigquery.migration.v2",
    rest_numeric_enums = False,
    service_yaml = "bigquerymigration_v2.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-migration-v2-nodejs",
    deps = [
        ":migration_nodejs_gapic",
        ":migration_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "migration_ruby_proto",
    deps = [":migration_proto"],
)

ruby_grpc_library(
    name = "migration_ruby_grpc",
    srcs = [":migration_proto"],
    deps = [":migration_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "migration_ruby_gapic",
    srcs = [":migration_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=bigquerymigration.googleapis.com",
        "ruby-cloud-api-shortname=bigquerymigration",
        "ruby-cloud-gem-name=google-cloud-bigquery-migration-v2",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs/reference/migration",
    ],
    grpc_service_config = "bigquerymigration_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "The BigQuery Migration Service is a comprehensive solution for migrating your data warehouse to BigQuery.",
    ruby_cloud_title = "BigQuery Migration V2",
    service_yaml = "bigquerymigration_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":migration_ruby_grpc",
        ":migration_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-migration-v2-ruby",
    deps = [
        ":migration_ruby_gapic",
        ":migration_ruby_grpc",
        ":migration_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "migration_csharp_proto",
    extra_opts = [],
    deps = [":migration_proto"],
)

csharp_grpc_library(
    name = "migration_csharp_grpc",
    srcs = [":migration_proto"],
    deps = [":migration_csharp_proto"],
)

csharp_gapic_library(
    name = "migration_csharp_gapic",
    srcs = [":migration_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "bigquerymigration_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "bigquerymigration_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":migration_csharp_grpc",
        ":migration_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-migration-v2-csharp",
    deps = [
        ":migration_csharp_gapic",
        ":migration_csharp_grpc",
        ":migration_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "migration_cc_proto",
    deps = [":migration_proto"],
)

cc_grpc_library(
    name = "migration_cc_grpc",
    srcs = [":migration_proto"],
    grpc_only = True,
    deps = [":migration_cc_proto"],
)
