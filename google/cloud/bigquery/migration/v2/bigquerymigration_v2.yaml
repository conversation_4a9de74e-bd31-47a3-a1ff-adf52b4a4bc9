type: google.api.Service
config_version: 3
name: bigquerymigration.googleapis.com
title: BigQuery Migration API

apis:
- name: google.cloud.bigquery.migration.v2.MigrationService

documentation:
  summary: |-
    The migration service, exposing apis for migration jobs operations, and
    agent management.

backend:
  rules:
  - selector: 'google.cloud.bigquery.migration.v2.MigrationService.*'
    deadline: 60.0

authentication:
  rules:
  - selector: 'google.cloud.bigquery.migration.v2.MigrationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
