// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2;

import "google/api/field_behavior.proto";
import "google/rpc/error_details.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "MigrationErrorDetailsProto";
option java_package = "com.google.cloud.bigquery.migration.v2";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2";

// Provides details for errors and the corresponding resources.
message ResourceErrorDetail {
  // Required. Information about the resource where the error is located.
  google.rpc.ResourceInfo resource_info = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The error details for the resource.
  repeated ErrorDetail error_details = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. How many errors there are in total for the resource. Truncation
  // can be indicated by having an `error_count` that is higher than the size of
  // `error_details`.
  int32 error_count = 3 [(google.api.field_behavior) = REQUIRED];
}

// Provides details for errors, e.g. issues that where encountered when
// processing a subtask.
message ErrorDetail {
  // Optional. The exact location within the resource (if applicable).
  ErrorLocation location = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Describes the cause of the error with structured detail.
  google.rpc.ErrorInfo error_info = 2 [(google.api.field_behavior) = REQUIRED];
}

// Holds information about where the error is located.
message ErrorLocation {
  // Optional. If applicable, denotes the line where the error occurred. A zero
  // value means that there is no line information.
  int32 line = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If applicable, denotes the column where the error occurred. A
  // zero value means that there is no columns information.
  int32 column = 2 [(google.api.field_behavior) = OPTIONAL];
}
