type: google.api.Service
config_version: 3
name: bigquerystorage.googleapis.com
title: BigQuery Storage API

apis:
- name: google.cloud.bigquery.storage.v1alpha.MetastorePartitionService

authentication:
  rules:
  - selector: 'google.cloud.bigquery.storage.v1alpha.MetastorePartitionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=187149&template=1162659
  documentation_uri: https://cloud.google.com/bigquery/docs/reference/storage/
  api_short_name: bigquerystorage
  github_label: 'api: bigquerystorage'
  doc_tag_prefix: bigquerystorage
  organization: CLOUD
  library_settings:
  - version: google.cloud.bigquery.storage.v1alpha
    launch_stage: ALPHA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
