{"methodConfig": [{"name": [{"service": "google.cloud.bigquery.storage.v1alpha.MetastorePartitionService", "method": "BatchCreateMetastorePartitions"}, {"service": "google.cloud.bigquery.storage.v1alpha.MetastorePartitionService", "method": "BatchUpdateMetastorePartitions"}, {"service": "google.cloud.bigquery.storage.v1alpha.MetastorePartitionService", "method": "BatchDeleteMetastorePartitions"}, {"service": "google.cloud.bigquery.storage.v1alpha.MetastorePartitionService", "method": "ListMetastorePartitions"}], "timeout": "240s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.bigquery.storage.v1alpha.MetastorePartitionService", "method": "StreamMetastorePartitions"}], "timeout": "240s"}]}