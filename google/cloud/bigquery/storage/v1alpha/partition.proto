// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Storage.V1Alpha";
option go_package = "cloud.google.com/go/bigquery/storage/apiv1alpha/storagepb;storagepb";
option java_multiple_files = true;
option java_outer_classname = "MetastorePartitionProto";
option java_package = "com.google.cloud.bigquery.storage.v1alpha";
option php_namespace = "Google\\Cloud\\BigQuery\\Storage\\V1alpha";

// Schema description of a metastore partition column.
message FieldSchema {
  // Required. The name of the column.
  // The maximum length of the name is 1024 characters
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the metastore partition column. Maximum allowed
  // length is 1024 characters.
  string type = 2 [(google.api.field_behavior) = REQUIRED];
}

// Contains information about the physical storage of the data in the metastore
// partition.
message StorageDescriptor {
  // Optional. The physical location of the metastore partition
  // (e.g. `gs://spark-dataproc-data/pangea-data/case_sensitive/` or
  // `gs://spark-dataproc-data/pangea-data/*`).
  string location_uri = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the fully qualified class name of the InputFormat
  // (e.g. "org.apache.hadoop.hive.ql.io.orc.OrcInputFormat").
  // The maximum length is 128 characters.
  string input_format = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the fully qualified class name of the OutputFormat
  // (e.g. "org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat").
  // The maximum length is 128 characters.
  string output_format = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Serializer and deserializer information.
  SerDeInfo serde_info = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Serializer and deserializer information.
message SerDeInfo {
  // Optional. Name of the SerDe.
  // The maximum length is 256 characters.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Specifies a fully-qualified class name of the serialization
  // library that is responsible for the translation of data between table
  // representation and the underlying low-level input and output format
  // structures. The maximum length is 256 characters.
  string serialization_library = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Key-value pairs that define the initialization parameters for the
  // serialization library.
  // Maximum size 10 Kib.
  map<string, string> parameters = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Information about a Hive partition.
message MetastorePartition {
  // Required. Represents the values of the partition keys, where each value
  // corresponds to a specific partition key in the order in which the keys are
  // defined. Each value is limited to 1024 characters.
  repeated string values = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The creation time of the partition.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Contains information about the physical storage of the data in
  // the partition.
  StorageDescriptor storage_descriptor = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Additional parameters or metadata associated with the partition.
  // Maximum size 10 KiB.
  map<string, string> parameters = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List of columns.
  repeated FieldSchema fields = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List of metastore partitions.
message MetastorePartitionList {
  // Required. List of partitions.
  repeated MetastorePartition partitions = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Information about a single stream that is used to read partitions.
message ReadStream {
  option (google.api.resource) = {
    type: "bigquerystorage.googleapis.com/ReadStream"
    pattern: "projects/{project}/locations/{location}/sessions/{session}/streams/{stream}"
    plural: "readStreams"
    singular: "readStream"
  };

  // Output only. Identifier. Name of the stream, in the form
  // `projects/{project_id}/locations/{location}/sessions/{session_id}/streams/{stream_id}`.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];
}

// List of streams.
message StreamList {
  // Output only. List of streams.
  repeated ReadStream streams = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the values of a metastore partition.
message MetastorePartitionValues {
  // Required. The values of the partition keys, where each value corresponds to
  // a specific partition key in the order in which the keys are defined.
  repeated string values = 1 [(google.api.field_behavior) = REQUIRED];
}
