// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1beta1;

option go_package = "cloud.google.com/go/bigquery/storage/apiv1beta1/storagepb;storagepb";
option java_outer_classname = "ArrowProto";
option java_package = "com.google.cloud.bigquery.storage.v1beta1";

// Arrow schema.
message ArrowSchema {
  // IPC serialized Arrow schema.
  bytes serialized_schema = 1;
}

// Arrow RecordBatch.
message ArrowRecordBatch {
  // IPC serialized Arrow RecordBatch.
  bytes serialized_record_batch = 1;

  // The count of rows in the returning block.
  int64 row_count = 2;
}
