type: google.api.Service
config_version: 3
name: bigquerystorage.googleapis.com
title: BigQuery Storage API

apis:
- name: google.cloud.bigquery.storage.v1beta1.BigQueryStorage

backend:
  rules:
  - selector: 'google.cloud.bigquery.storage.v1beta1.BigQueryStorage.*'
    deadline: 120.0
  - selector: google.cloud.bigquery.storage.v1beta1.BigQueryStorage.ReadRows
    deadline: 21600.0

authentication:
  rules:
  - selector: 'google.cloud.bigquery.storage.v1beta1.BigQueryStorage.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
