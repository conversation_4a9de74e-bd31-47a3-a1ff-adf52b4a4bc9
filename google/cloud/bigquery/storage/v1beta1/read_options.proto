// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1beta1;

option go_package = "cloud.google.com/go/bigquery/storage/apiv1beta1/storagepb;storagepb";
option java_package = "com.google.cloud.bigquery.storage.v1beta1";

// Options dictating how we read a table.
message TableReadOptions {
  // Optional. The names of the fields in the table to be returned. If no
  // field names are specified, then all fields in the table are returned.
  //
  // Nested fields -- the child elements of a STRUCT field -- can be selected
  // individually using their fully-qualified names, and will be returned as
  // record fields containing only the selected nested fields. If a STRUCT
  // field is specified in the selected fields list, all of the child elements
  // will be returned.
  //
  // As an example, consider a table with the following schema:
  //
  //   {
  //       "name": "struct_field",
  //       "type": "RECORD",
  //       "mode": "NULLABLE",
  //       "fields": [
  //           {
  //               "name": "string_field1",
  //               "type": "STRING",
  // .              "mode": "NULLABLE"
  //           },
  //           {
  //               "name": "string_field2",
  //               "type": "STRING",
  //               "mode": "NULLABLE"
  //           }
  //       ]
  //   }
  //
  // Specifying "struct_field" in the selected fields list will result in a
  // read session schema with the following logical structure:
  //
  //   struct_field {
  //       string_field1
  //       string_field2
  //   }
  //
  // Specifying "struct_field.string_field1" in the selected fields list will
  // result in a read session schema with the following logical structure:
  //
  //   struct_field {
  //       string_field1
  //   }
  //
  // The order of the fields in the read session schema is derived from the
  // table schema and does not correspond to the order in which the fields are
  // specified in this list.
  repeated string selected_fields = 1;

  // Optional. SQL text filtering statement, similar to a WHERE clause in
  // a SQL query. Aggregates are not supported.
  //
  // Examples: "int_field > 5"
  //           "date_field = CAST('2014-9-27' as DATE)"
  //           "nullable_field is not NULL"
  //           "st_equals(geo_field, st_geofromtext("POINT(2, 2)"))"
  //           "numeric_field BETWEEN 1.0 AND 5.0"
  //
  // Restricted to a maximum length for 1 MB.
  string row_restriction = 2;
}
