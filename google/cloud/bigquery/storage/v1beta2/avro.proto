// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1beta2;

option go_package = "cloud.google.com/go/bigquery/storage/apiv1beta2/storagepb;storagepb";
option java_multiple_files = true;
option java_outer_classname = "AvroProto";
option java_package = "com.google.cloud.bigquery.storage.v1beta2";

// Avro schema.
message AvroSchema {
  // Json serialized schema, as described at
  // https://avro.apache.org/docs/1.8.1/spec.html.
  string schema = 1;
}

// Avro rows.
message AvroRows {
  // Binary serialized rows in a block.
  bytes serialized_binary_rows = 1;
}
