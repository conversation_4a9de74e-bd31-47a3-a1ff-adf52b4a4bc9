// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1beta2;

import "google/protobuf/descriptor.proto";

option go_package = "cloud.google.com/go/bigquery/storage/apiv1beta2/storagepb;storagepb";
option java_multiple_files = true;
option java_outer_classname = "ProtoBufProto";
option java_package = "com.google.cloud.bigquery.storage.v1beta2";

// ProtoSchema describes the schema of the serialized protocol buffer data rows.
message ProtoSchema {
  // Descriptor for input message. The descriptor has to be self contained,
  // including all the nested types, excepted for proto buffer well known types
  // (https://developers.google.com/protocol-buffers/docs/reference/google.protobuf).
  google.protobuf.DescriptorProto proto_descriptor = 1;
}

message ProtoRows {
  // A sequence of rows serialized as a Protocol Buffer.
  //
  // See https://developers.google.com/protocol-buffers/docs/overview for more
  // information on deserializing this field.
  repeated bytes serialized_rows = 1;
}
