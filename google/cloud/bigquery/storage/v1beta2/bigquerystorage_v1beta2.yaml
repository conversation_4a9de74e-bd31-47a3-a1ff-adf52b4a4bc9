type: google.api.Service
config_version: 3
name: bigquerystorage.googleapis.com
title: BigQuery Storage API

apis:
- name: google.cloud.bigquery.storage.v1beta2.BigQueryRead
- name: google.cloud.bigquery.storage.v1beta2.BigQueryWrite

backend:
  rules:
  - selector: google.cloud.bigquery.storage.v1beta2.BigQueryRead.CreateReadSession
    deadline: 120.0
  - selector: google.cloud.bigquery.storage.v1beta2.BigQueryRead.ReadRows
    deadline: 21600.0
  - selector: google.cloud.bigquery.storage.v1beta2.BigQueryRead.SplitReadStream
    deadline: 120.0
  - selector: 'google.cloud.bigquery.storage.v1beta2.BigQueryWrite.*'
    deadline: 120.0

authentication:
  rules:
  - selector: 'google.cloud.bigquery.storage.v1beta2.BigQueryRead.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/bigquery.readonly,
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.bigquery.storage.v1beta2.BigQueryWrite.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/bigquery.insertdata,
        https://www.googleapis.com/auth/cloud-platform
