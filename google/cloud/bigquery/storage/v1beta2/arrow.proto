// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1beta2;

option go_package = "cloud.google.com/go/bigquery/storage/apiv1beta2/storagepb;storagepb";
option java_multiple_files = true;
option java_outer_classname = "ArrowProto";
option java_package = "com.google.cloud.bigquery.storage.v1beta2";

// Arrow schema as specified in
// https://arrow.apache.org/docs/python/api/datatypes.html
// and serialized to bytes using IPC:
// https://arrow.apache.org/docs/format/Columnar.html#serialization-and-interprocess-communication-ipc
//
// See code samples on how this message can be deserialized.
message ArrowSchema {
  // IPC serialized Arrow schema.
  bytes serialized_schema = 1;
}

// Arrow RecordBatch.
message ArrowRecordBatch {
  // IPC-serialized Arrow RecordBatch.
  bytes serialized_record_batch = 1;
}

// Contains options specific to Arrow Serialization.
message ArrowSerializationOptions {
  // The IPC format to use when serializing Arrow streams.
  enum Format {
    // If unspecied the IPC format as of 0.15 release will be used.
    FORMAT_UNSPECIFIED = 0;

    // Use the legacy IPC message format as of Apache Arrow Release 0.14.
    ARROW_0_14 = 1;

    // Use the message format as of Apache Arrow Release 0.15.
    ARROW_0_15 = 2;
  }

  // The Arrow IPC format to use.
  Format format = 1;
}
