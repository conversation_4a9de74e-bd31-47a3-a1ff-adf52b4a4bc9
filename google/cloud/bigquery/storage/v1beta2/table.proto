// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1beta2;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/bigquery/storage/apiv1beta2/storagepb;storagepb";
option java_multiple_files = true;
option java_outer_classname = "TableProto";
option java_package = "com.google.cloud.bigquery.storage.v1beta2";

// Schema of a table
message TableSchema {
  // Describes the fields in a table.
  repeated TableFieldSchema fields = 1;
}

// A field in TableSchema
message TableFieldSchema {
  enum Type {
    // Illegal value
    TYPE_UNSPECIFIED = 0;

    // 64K, UTF8
    STRING = 1;

    // 64-bit signed
    INT64 = 2;

    // 64-bit IEEE floating point
    DOUBLE = 3;

    // Aggregate type
    STRUCT = 4;

    // 64K, Binary
    BYTES = 5;

    // 2-valued
    BOOL = 6;

    // 64-bit signed usec since UTC epoch
    TIMESTAMP = 7;

    // Civil date - Year, Month, Day
    DATE = 8;

    // Civil time - Hour, Minute, Second, Microseconds
    TIME = 9;

    // Combination of civil date and civil time
    DATETIME = 10;

    // Geography object
    GEOGRAPHY = 11;

    // Numeric value
    NUMERIC = 12;

    // BigNumeric value
    BIGNUMERIC = 13;

    // Interval
    INTERVAL = 14;

    // JSON, String
    JSON = 15;
  }

  enum Mode {
    // Illegal value
    MODE_UNSPECIFIED = 0;

    NULLABLE = 1;

    REQUIRED = 2;

    REPEATED = 3;
  }

  // Required. The field name. The name must contain only letters (a-z, A-Z),
  // numbers (0-9), or underscores (_), and must start with a letter or
  // underscore. The maximum length is 128 characters.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The field data type.
  Type type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The field mode. The default value is NULLABLE.
  Mode mode = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes the nested schema fields if the type property is set to STRUCT.
  repeated TableFieldSchema fields = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The field description. The maximum length is 1,024 characters.
  string description = 6 [(google.api.field_behavior) = OPTIONAL];
}
