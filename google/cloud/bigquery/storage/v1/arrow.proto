// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1;

option csharp_namespace = "Google.Cloud.BigQuery.Storage.V1";
option go_package = "cloud.google.com/go/bigquery/storage/apiv1/storagepb;storagepb";
option java_multiple_files = true;
option java_outer_classname = "ArrowProto";
option java_package = "com.google.cloud.bigquery.storage.v1";
option php_namespace = "Google\\Cloud\\BigQuery\\Storage\\V1";

// Arrow schema as specified in
// https://arrow.apache.org/docs/python/api/datatypes.html
// and serialized to bytes using IPC:
// https://arrow.apache.org/docs/format/Columnar.html#serialization-and-interprocess-communication-ipc
//
// See code samples on how this message can be deserialized.
message ArrowSchema {
  // IPC serialized Arrow schema.
  bytes serialized_schema = 1;
}

// Arrow RecordBatch.
message ArrowRecordBatch {
  // IPC-serialized Arrow RecordBatch.
  bytes serialized_record_batch = 1;

  // [Deprecated] The count of rows in `serialized_record_batch`.
  // Please use the format-independent ReadRowsResponse.row_count instead.
  int64 row_count = 2 [deprecated = true];
}

// Contains options specific to Arrow Serialization.
message ArrowSerializationOptions {
  // Compression codec's supported by Arrow.
  enum CompressionCodec {
    // If unspecified no compression will be used.
    COMPRESSION_UNSPECIFIED = 0;

    // LZ4 Frame (https://github.com/lz4/lz4/blob/dev/doc/lz4_Frame_format.md)
    LZ4_FRAME = 1;

    // Zstandard compression.
    ZSTD = 2;
  }

  // The compression codec to use for Arrow buffers in serialized record
  // batches.
  CompressionCodec buffer_compression = 2;
}
