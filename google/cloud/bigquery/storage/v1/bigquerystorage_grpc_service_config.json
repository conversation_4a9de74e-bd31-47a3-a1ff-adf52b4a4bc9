{"methodConfig": [{"name": [{"service": "google.cloud.bigquery.storage.v1.BigQueryRead", "method": "CreateReadSession"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.bigquery.storage.v1.BigQueryRead", "method": "ReadRows"}], "timeout": "86400s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.bigquery.storage.v1.BigQueryRead", "method": "SplitReadStream"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.bigquery.storage.v1.BigQueryWrite", "method": "AppendRows"}], "timeout": "86400s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.bigquery.storage.v1.BigQueryWrite", "method": "BatchCommitWriteStreams"}, {"service": "google.cloud.bigquery.storage.v1.BigQueryWrite", "method": "FinalizeWriteStream"}, {"service": "google.cloud.bigquery.storage.v1.BigQueryWrite", "method": "GetWriteStream"}, {"service": "google.cloud.bigquery.storage.v1.BigQueryWrite", "method": "FlushRows"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}, {"name": [{"service": "google.cloud.bigquery.storage.v1.BigQueryWrite", "method": "CreateWriteStream"}], "timeout": "1200s", "retryPolicy": {"initialBackoff": "10s", "maxBackoff": "120s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}]}