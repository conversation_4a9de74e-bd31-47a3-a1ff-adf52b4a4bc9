##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "storage_proto",
    srcs = [
        "annotations.proto",
        "arrow.proto",
        "avro.proto",
        "protobuf.proto",
        "storage.proto",
        "stream.proto",
        "table.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:descriptor_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "storage_proto_with_info",
    deps = [
        ":storage_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "storage_java_proto",
    deps = [":storage_proto"],
)

java_grpc_library(
    name = "storage_java_grpc",
    srcs = [":storage_proto"],
    deps = [":storage_java_proto"],
)

java_gapic_library(
    name = "storage_java_gapic",
    srcs = [":storage_proto_with_info"],
    gapic_yaml = "bigquerystorage_gapic.yaml",
    grpc_service_config = "bigquerystorage_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "bigquerystorage_v1.yaml",
    test_deps = [
        ":storage_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":storage_java_proto",
    ],
)

java_gapic_test(
    name = "storage_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.storage.v1.BaseBigQueryReadClientTest",
    ],
    runtime_deps = [":storage_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-storage-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":storage_java_gapic",
        ":storage_java_grpc",
        ":storage_java_proto",
        ":storage_proto",
    ],
)

go_proto_library(
    name = "storage_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/storage/apiv1/storagepb",
    protos = [":storage_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "storage_go_gapic",
    srcs = [":storage_proto_with_info"],
    grpc_service_config = "bigquerystorage_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/storage/apiv1;storage",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "bigquerystorage_v1.yaml",
    transport = "grpc",
    deps = [
        ":storage_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-storage-v1-go",
    deps = [
        ":storage_go_gapic",
        ":storage_go_gapic_srcjar-snippets.srcjar",
        ":storage_go_gapic_srcjar-test.srcjar",
        ":storage_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################

py_gapic_library(
    name = "storage_py_gapic",
    srcs = [":storage_proto"],
    grpc_service_config = "bigquerystorage_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=bigquery_storage",
    ],
    rest_numeric_enums = False,
    service_yaml = "bigquerystorage_v1.yaml",
    transport = "grpc",
)

py_test(
    name = "storage_py_gapic_test",
    srcs = [
        "storage_py_gapic_pytest.py",
        "storage_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":storage_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "bigquery-storage-v1-py",
    deps = [
        ":storage_py_gapic",
    ],
)

php_proto_library(
    name = "storage_php_proto",
    deps = [":storage_proto"],
)

php_gapic_library(
    name = "storage_php_gapic",
    srcs = [":storage_proto_with_info"],
    grpc_service_config = "bigquerystorage_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "bigquerystorage_v1.yaml",
    transport = "grpc+rest",
    deps = [":storage_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-storage-v1-php",
    deps = [
        ":storage_php_gapic",
        ":storage_php_proto",
    ],
)

nodejs_gapic_library(
    name = "storage_nodejs_gapic",
    package_name = "@google-cloud/bigquery-storage",
    src = ":storage_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "bigquerystorage_grpc_service_config.json",
    package = "google.cloud.bigquery.storage.v1",
    rest_numeric_enums = False,
    service_yaml = "bigquerystorage_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-storage-v1-nodejs",
    deps = [
        ":storage_nodejs_gapic",
        ":storage_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################

ruby_proto_library(
    name = "storage_ruby_proto",
    deps = [":storage_proto"],
)

ruby_grpc_library(
    name = "storage_ruby_grpc",
    srcs = [":storage_proto"],
    deps = [":storage_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "storage_ruby_gapic",
    srcs = [":storage_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigquery-storage-v1",
        "ruby-cloud-env-prefix=BIGQUERY_STORAGE",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs/reference/storage",
        "ruby-cloud-api-id=bigquerystorage.googleapis.com",
        "ruby-cloud-api-shortname=bigquerystorage",
    ],
    grpc_service_config = "bigquerystorage_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "The BigQuery Storage API provides fast access to BigQuery managed storage.",
    ruby_cloud_title = "BigQuery Storage V1",
    service_yaml = "bigquerystorage_v1.yaml",
    deps = [
        ":storage_ruby_grpc",
        ":storage_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-storage-v1-ruby",
    deps = [
        ":storage_ruby_gapic",
        ":storage_ruby_grpc",
        ":storage_ruby_proto",
    ],
)

csharp_proto_library(
    name = "storage_csharp_proto",
    deps = [":storage_proto"],
)

csharp_grpc_library(
    name = "storage_csharp_grpc",
    srcs = [":storage_proto"],
    deps = [":storage_csharp_proto"],
)

csharp_gapic_library(
    name = "storage_csharp_gapic",
    srcs = [":storage_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "bigquerystorage_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "bigquerystorage_v1.yaml",
    deps = [
        ":storage_csharp_grpc",
        ":storage_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-storage-v1-csharp",
    deps = [
        ":storage_csharp_gapic",
        ":storage_csharp_grpc",
        ":storage_csharp_proto",
    ],
)

cc_proto_library(
    name = "storage_cc_proto",
    deps = [":storage_proto"],
)

cc_grpc_library(
    name = "storage_cc_grpc",
    srcs = [":storage_proto"],
    generate_mocks = True,
    grpc_only = True,
    deps = [":storage_cc_proto"],
)
