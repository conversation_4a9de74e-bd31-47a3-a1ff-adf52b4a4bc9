syntax = "proto3";

package google.cloud.bigquery.storage.v1;

import "google/protobuf/descriptor.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Storage.V1";
option go_package = "cloud.google.com/go/bigquery/storage/apiv1/storagepb;storagepb";
option java_package = "com.google.cloud.bigquery.storage.v1";
option java_multiple_files = true;
option java_outer_classname = "AnnotationsProto";
option php_namespace = "Google\\Cloud\\BigQuery\\Storage\\V1";

extend google.protobuf.FieldOptions {
  // Setting the column_name extension allows users to reference
  // bigquery column independently of the field name in the protocol buffer
  // message.
  //
  // The intended use of this annotation is to reference a destination column
  // named using characters unavailable for protobuf field names (e.g. unicode
  // characters).
  //
  // More details about BigQuery naming limitations can be found here:
  // https://cloud.google.com/bigquery/docs/schemas#column_names
  //
  // This extension is currently experimental.
  optional string column_name = 454943157;
}
