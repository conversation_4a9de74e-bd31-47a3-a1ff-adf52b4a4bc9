// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.storage.v1;

option csharp_namespace = "Google.Cloud.BigQuery.Storage.V1";
option go_package = "cloud.google.com/go/bigquery/storage/apiv1/storagepb;storagepb";
option java_multiple_files = true;
option java_outer_classname = "AvroProto";
option java_package = "com.google.cloud.bigquery.storage.v1";
option php_namespace = "Google\\Cloud\\BigQuery\\Storage\\V1";

// Avro schema.
message AvroSchema {
  // Json serialized schema, as described at
  // https://avro.apache.org/docs/1.8.1/spec.html.
  string schema = 1;
}

// Avro rows.
message AvroRows {
  // Binary serialized rows in a block.
  bytes serialized_binary_rows = 1;

  // [Deprecated] The count of rows in the returning block.
  // Please use the format-independent ReadRowsResponse.row_count instead.
  int64 row_count = 2 [deprecated = true];
}

// Contains options specific to Avro Serialization.
message AvroSerializationOptions {
  // Enable displayName attribute in Avro schema.
  //
  // The Avro specification requires field names to be alphanumeric.  By
  // default, in cases when column names do not conform to these requirements
  // (e.g. non-ascii unicode codepoints) and Avro is requested as an output
  // format, the CreateReadSession call will fail.
  //
  // Setting this field to true, populates avro field names with a placeholder
  // value and populates a "displayName" attribute for every avro field with the
  // original column name.
  bool enable_display_name_attribute = 1;
}
