# This build file includes a target for the Ruby wrapper library for
# google-cloud-bigquery-storage.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for bigquerystorage.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "bigquerystorage_ruby_wrapper",
    srcs = ["//google/cloud/bigquery/storage/v1:storage_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigquery-storage",
        "ruby-cloud-env-prefix=BIGQUERY_STORAGE",
        "ruby-cloud-wrapper-of=v1:0.25",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs/reference/storage",
        "ruby-cloud-api-id=bigquerystorage.googleapis.com",
        "ruby-cloud-api-shortname=bigquerystorage",
    ],
    ruby_cloud_description = "The BigQuery Storage API provides fast access to BigQuery managed storage.",
    ruby_cloud_title = "BigQuery Storage",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-storage-ruby",
    deps = [
        ":bigquerystorage_ruby_wrapper",
    ],
)
