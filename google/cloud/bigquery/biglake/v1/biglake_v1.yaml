type: google.api.Service
config_version: 3
name: biglake.googleapis.com
title: BigLake API

apis:
- name: google.cloud.bigquery.biglake.v1.MetastoreService

documentation:
  summary: |-
    The BigLake API provides access to BigLake Metastore, a serverless, fully
    managed, and highly available metastore for open-source data that can be
    used for querying Apache Iceberg tables in BigQuery.

backend:
  rules:
  - selector: 'google.cloud.bigquery.biglake.v1.MetastoreService.*'
    deadline: 30.0

authentication:
  rules:
  - selector: 'google.cloud.bigquery.biglake.v1.MetastoreService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=187149&template=1019829
  documentation_uri: https://cloud.google.com/bigquery/docs/iceberg-tables#create-using-biglake-metastore
  api_short_name: biglake
  github_label: 'api: biglake'
  doc_tag_prefix: biglake
  organization: CLOUD
  library_settings:
  - version: google.cloud.bigquery.biglake.v1
    launch_stage: GA
    rest_numeric_enums: true
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
