# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "biglake_proto",
    srcs = [
        "metastore.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "biglake_proto_with_info",
    deps = [
        ":biglake_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "biglake_java_proto",
    deps = [":biglake_proto"],
)

java_grpc_library(
    name = "biglake_java_grpc",
    srcs = [":biglake_proto"],
    deps = [":biglake_java_proto"],
)

java_gapic_library(
    name = "biglake_java_gapic",
    srcs = [":biglake_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "biglake_v1alpha1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "biglake_v1alpha1.yaml",
    test_deps = [
        ":biglake_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":biglake_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "biglake_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.biglake.v1alpha1.MetastoreServiceClientHttpJsonTest",
        "com.google.cloud.bigquery.biglake.v1alpha1.MetastoreServiceClientTest",
    ],
    runtime_deps = [":biglake_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-biglake-v1alpha1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":biglake_java_gapic",
        ":biglake_java_grpc",
        ":biglake_java_proto",
        ":biglake_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "biglake_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/biglake/apiv1alpha1/biglakepb",
    protos = [":biglake_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "biglake_go_gapic",
    srcs = [":biglake_proto_with_info"],
    grpc_service_config = "biglake_v1alpha1_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/biglake/apiv1alpha1;biglake",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "biglake_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":biglake_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-biglake-v1alpha1-go",
    deps = [
        ":biglake_go_gapic",
        ":biglake_go_gapic_srcjar-metadata.srcjar",
        ":biglake_go_gapic_srcjar-snippets.srcjar",
        ":biglake_go_gapic_srcjar-test.srcjar",
        ":biglake_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "biglake_py_gapic",
    srcs = [":biglake_proto"],
    grpc_service_config = "biglake_v1alpha1_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=bigquery_biglake",
    ],
    rest_numeric_enums = True,
    service_yaml = "biglake_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "biglake_py_gapic_test",
    srcs = [
        "biglake_py_gapic_pytest.py",
        "biglake_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":biglake_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-biglake-v1alpha1-py",
    deps = [
        ":biglake_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "biglake_php_proto",
    deps = [":biglake_proto"],
)

php_gapic_library(
    name = "biglake_php_gapic",
    srcs = [":biglake_proto_with_info"],
    grpc_service_config = "biglake_v1alpha1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "biglake_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [":biglake_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-biglake-v1alpha1-php",
    deps = [
        ":biglake_php_gapic",
        ":biglake_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "biglake_nodejs_gapic",
    package_name = "@google-cloud/biglake",
    src = ":biglake_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "biglake_v1alpha1_grpc_service_config.json",
    package = "google.cloud.bigquery.biglake.v1alpha1",
    rest_numeric_enums = True,
    service_yaml = "biglake_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-biglake-v1alpha1-nodejs",
    deps = [
        ":biglake_nodejs_gapic",
        ":biglake_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "biglake_ruby_proto",
    deps = [":biglake_proto"],
)

ruby_grpc_library(
    name = "biglake_ruby_grpc",
    srcs = [":biglake_proto"],
    deps = [":biglake_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "biglake_ruby_gapic",
    srcs = [":biglake_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-bigquery-biglake-v1alpha1"],
    grpc_service_config = "biglake_v1alpha1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "biglake_v1alpha1.yaml",
    deps = [
        ":biglake_ruby_grpc",
        ":biglake_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-biglake-v1alpha1-ruby",
    deps = [
        ":biglake_ruby_gapic",
        ":biglake_ruby_grpc",
        ":biglake_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "biglake_csharp_proto",
    deps = [":biglake_proto"],
)

csharp_grpc_library(
    name = "biglake_csharp_grpc",
    srcs = [":biglake_proto"],
    deps = [":biglake_csharp_proto"],
)

csharp_gapic_library(
    name = "biglake_csharp_gapic",
    srcs = [":biglake_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "biglake_v1alpha1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "biglake_v1alpha1.yaml",
    deps = [
        ":biglake_csharp_grpc",
        ":biglake_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-biglake-v1alpha1-csharp",
    deps = [
        ":biglake_csharp_gapic",
        ":biglake_csharp_grpc",
        ":biglake_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "biglake_cc_proto",
    deps = [":biglake_proto"],
)

cc_grpc_library(
    name = "biglake_cc_grpc",
    srcs = [":biglake_proto"],
    grpc_only = True,
    deps = [":biglake_cc_proto"],
)
