// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.biglake.v1alpha1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/bigquery/biglake/apiv1alpha1/biglakepb;biglakepb";
option java_multiple_files = true;
option java_outer_classname = "MetastoreProto";
option java_package = "com.google.cloud.bigquery.biglake.v1alpha1";

// BigLake Metastore is a serverless, highly available, multi-tenant runtime
// metastore for Google Cloud Data Analytics products.
//
// The BigLake Metastore API defines the following resource model:
//
// * A collection of Google Cloud projects: `/projects/*`
// * Each project has a collection of available locations: `/locations/*`
// * Each location has a collection of catalogs: `/catalogs/*`
// * Each catalog has a collection of databases: `/databases/*`
// * Each database has a collection of tables: `/tables/*`
service MetastoreService {
  option (google.api.default_host) = "biglake.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new catalog.
  rpc CreateCatalog(CreateCatalogRequest) returns (Catalog) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*}/catalogs"
      body: "catalog"
    };
    option (google.api.method_signature) = "parent,catalog,catalog_id";
  }

  // Deletes an existing catalog specified by the catalog ID.
  rpc DeleteCatalog(DeleteCatalogRequest) returns (Catalog) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/catalogs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the catalog specified by the resource name.
  rpc GetCatalog(GetCatalogRequest) returns (Catalog) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/catalogs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List all catalogs in a specified project.
  rpc ListCatalogs(ListCatalogsRequest) returns (ListCatalogsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*}/catalogs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new database.
  rpc CreateDatabase(CreateDatabaseRequest) returns (Database) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/catalogs/*}/databases"
      body: "database"
    };
    option (google.api.method_signature) = "parent,database,database_id";
  }

  // Deletes an existing database specified by the database ID.
  rpc DeleteDatabase(DeleteDatabaseRequest) returns (Database) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/catalogs/*/databases/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an existing database specified by the database ID.
  rpc UpdateDatabase(UpdateDatabaseRequest) returns (Database) {
    option (google.api.http) = {
      patch: "/v1alpha1/{database.name=projects/*/locations/*/catalogs/*/databases/*}"
      body: "database"
    };
    option (google.api.method_signature) = "database,update_mask";
  }

  // Gets the database specified by the resource name.
  rpc GetDatabase(GetDatabaseRequest) returns (Database) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/catalogs/*/databases/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List all databases in a specified catalog.
  rpc ListDatabases(ListDatabasesRequest) returns (ListDatabasesResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/catalogs/*}/databases"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new table.
  rpc CreateTable(CreateTableRequest) returns (Table) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/catalogs/*/databases/*}/tables"
      body: "table"
    };
    option (google.api.method_signature) = "parent,table,table_id";
  }

  // Deletes an existing table specified by the table ID.
  rpc DeleteTable(DeleteTableRequest) returns (Table) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/catalogs/*/databases/*/tables/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an existing table specified by the table ID.
  rpc UpdateTable(UpdateTableRequest) returns (Table) {
    option (google.api.http) = {
      patch: "/v1alpha1/{table.name=projects/*/locations/*/catalogs/*/databases/*/tables/*}"
      body: "table"
    };
    option (google.api.method_signature) = "table,update_mask";
  }

  // Renames an existing table specified by the table ID.
  rpc RenameTable(RenameTableRequest) returns (Table) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/catalogs/*/databases/*/tables/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_name";
  }

  // Gets the table specified by the resource name.
  rpc GetTable(GetTableRequest) returns (Table) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/catalogs/*/databases/*/tables/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List all tables in a specified database.
  rpc ListTables(ListTablesRequest) returns (ListTablesResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/catalogs/*/databases/*}/tables"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new lock.
  rpc CreateLock(CreateLockRequest) returns (Lock) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/catalogs/*/databases/*}/locks"
      body: "lock"
    };
    option (google.api.method_signature) = "parent,lock";
  }

  // Deletes an existing lock specified by the lock ID.
  rpc DeleteLock(DeleteLockRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/catalogs/*/databases/*/locks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Checks the state of a lock specified by the lock ID.
  rpc CheckLock(CheckLockRequest) returns (Lock) {
    option (google.api.http) = {
      post: "/v1alpha1/{name=projects/*/locations/*/catalogs/*/databases/*/locks/*}:check"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // List all locks in a specified database.
  rpc ListLocks(ListLocksRequest) returns (ListLocksResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/catalogs/*/databases/*}/locks"
    };
    option (google.api.method_signature) = "parent";
  }
}

// View on Table. Represents which fields will be populated for calls that
// return Table objects.
enum TableView {
  // Default value. The API will default to the BASIC view.
  TABLE_VIEW_UNSPECIFIED = 0;

  // Include only table names.
  // This is the default value.
  BASIC = 1;

  // Include everything.
  FULL = 2;
}

// Catalog is the container of databases.
message Catalog {
  option (google.api.resource) = {
    type: "biglake.googleapis.com/Catalog"
    pattern: "projects/{project}/locations/{location}/catalogs/{catalog}"
  };

  // Output only. The resource name.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Catalog" }
  ];

  // Output only. The creation time of the catalog.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last modification time of the catalog.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The deletion time of the catalog. Only set after the catalog
  // is deleted.
  google.protobuf.Timestamp delete_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when this catalog is considered expired. Only set
  // after the catalog is deleted.
  google.protobuf.Timestamp expire_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Database is the container of tables.
message Database {
  option (google.api.resource) = {
    type: "biglake.googleapis.com/Database"
    pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/databases/{database}"
  };

  // The database type.
  enum Type {
    // The type is not specified.
    TYPE_UNSPECIFIED = 0;

    // Represents a database storing tables compatible with Hive Metastore
    // tables.
    HIVE = 1;
  }

  // Options specified for the database type.
  oneof options {
    // Options of a Hive database.
    HiveDatabaseOptions hive_options = 7;
  }

  // Output only. The resource name.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "biglake.googleapis.com/Database"
    }
  ];

  // Output only. The creation time of the database.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last modification time of the database.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The deletion time of the database. Only set after the database
  // is deleted.
  google.protobuf.Timestamp delete_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when this database is considered expired. Only set
  // after the database is deleted.
  google.protobuf.Timestamp expire_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The database type.
  Type type = 6;
}

// Represents a table.
message Table {
  option (google.api.resource) = {
    type: "biglake.googleapis.com/Table"
    pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/databases/{database}/tables/{table}"
  };

  // The table type.
  enum Type {
    // The type is not specified.
    TYPE_UNSPECIFIED = 0;

    // Represents a table compatible with Hive Metastore tables.
    HIVE = 1;
  }

  // Options specified for the table type.
  oneof options {
    // Options of a Hive table.
    HiveTableOptions hive_options = 7;
  }

  // Output only. The resource name.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/tables/{table_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Table" }
  ];

  // Output only. The creation time of the table.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last modification time of the table.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The deletion time of the table. Only set after the table is
  // deleted.
  google.protobuf.Timestamp delete_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when this table is considered expired. Only set after
  // the table is deleted.
  google.protobuf.Timestamp expire_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The table type.
  Type type = 6;

  // The checksum of a table object computed by the server based on the value of
  // other fields. It may be sent on update requests to ensure the client has an
  // up-to-date value before proceeding. It is only checked for update table
  // operations.
  string etag = 8;
}

// Represents a lock.
message Lock {
  option (google.api.resource) = {
    type: "biglake.googleapis.com/Lock"
    pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/databases/{database}/locks/{lock}"
  };

  // The lock type.
  enum Type {
    // The type is not specified.
    TYPE_UNSPECIFIED = 0;

    // An exclusive lock prevents another lock from being created on the same
    // resource.
    EXCLUSIVE = 1;
  }

  // The lock state.
  enum State {
    // The state is not specified.
    STATE_UNSPECIFIED = 0;

    // Waiting to acquire the lock.
    WAITING = 1;

    // The lock has been acquired.
    ACQUIRED = 2;
  }

  // The resource that the lock will be created on.
  oneof resources {
    // The table ID (not fully qualified name) in the same database that the
    // lock will be created on. The table must exist.
    string table_id = 5;
  }

  // Output only. The resource name.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/locks/{lock_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Lock" }
  ];

  // Output only. The creation time of the lock.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The lock type.
  Type type = 3;

  // Output only. The lock state.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for the CreateCatalog method.
message CreateCatalogRequest {
  // Required. The parent resource where this catalog will be created.
  // Format: projects/{project_id_or_number}/locations/{location_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The catalog to create.
  // The `name` field does not need to be provided.
  Catalog catalog = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the catalog, which will become the final
  // component of the catalog's resource name.
  string catalog_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the DeleteCatalog method.
message DeleteCatalogRequest {
  // Required. The name of the catalog to delete.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Catalog" }
  ];
}

// Request message for the GetCatalog method.
message GetCatalogRequest {
  // Required. The name of the catalog to retrieve.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Catalog" }
  ];
}

// Request message for the ListCatalogs method.
message ListCatalogsRequest {
  // Required. The parent, which owns this collection of catalogs.
  // Format: projects/{project_id_or_number}/locations/{location_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of catalogs to return. The service may return fewer than
  // this value.
  // If unspecified, at most 50 catalogs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListCatalogs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCatalogs` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Response message for the ListCatalogs method.
message ListCatalogsResponse {
  // The catalogs from the specified project.
  repeated Catalog catalogs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the CreateDatabase method.
message CreateDatabaseRequest {
  // Required. The parent resource where this database will be created.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Catalog" }
  ];

  // Required. The database to create.
  // The `name` field does not need to be provided.
  Database database = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the database, which will become the final
  // component of the database's resource name.
  string database_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the DeleteDatabase method.
message DeleteDatabaseRequest {
  // Required. The name of the database to delete.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "biglake.googleapis.com/Database"
    }
  ];
}

// Request message for the UpdateDatabase method.
message UpdateDatabaseRequest {
  // Required. The database to update.
  //
  // The database's `name` field is used to identify the database to update.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}
  Database database = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  //
  // For the `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  // If not set, defaults to all of the fields that are allowed to update.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for the GetDatabase method.
message GetDatabaseRequest {
  // Required. The name of the database to retrieve.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "biglake.googleapis.com/Database"
    }
  ];
}

// Request message for the ListDatabases method.
message ListDatabasesRequest {
  // Required. The parent, which owns this collection of databases.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Catalog" }
  ];

  // The maximum number of databases to return. The service may return fewer
  // than this value.
  // If unspecified, at most 50 databases will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListDatabases` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDatabases` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for the ListDatabases method.
message ListDatabasesResponse {
  // The databases from the specified catalog.
  repeated Database databases = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the CreateTable method.
message CreateTableRequest {
  // Required. The parent resource where this table will be created.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "biglake.googleapis.com/Database"
    }
  ];

  // Required. The table to create. The `name` field does not need to be
  // provided for the table creation.
  Table table = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the table, which will become the final
  // component of the table's resource name.
  string table_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the DeleteTable method.
message DeleteTableRequest {
  // Required. The name of the table to delete.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/tables/{table_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Table" }
  ];
}

// Request message for the UpdateTable method.
message UpdateTableRequest {
  // Required. The table to update.
  //
  // The table's `name` field is used to identify the table to update.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/tables/{table_id}
  Table table = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  //
  // For the `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  // If not set, defaults to all of the fields that are allowed to update.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for the RenameTable method in MetastoreService
message RenameTableRequest {
  // Required. The table's `name` field is used to identify the table to rename.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/tables/{table_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Table" }
  ];

  // Required. The new `name` for the specified table, must be in the same
  // database. Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/tables/{table_id}
  string new_name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Table" }
  ];
}

// Request message for the GetTable method.
message GetTableRequest {
  // Required. The name of the table to retrieve.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/tables/{table_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Table" }
  ];
}

// Request message for the ListTables method.
message ListTablesRequest {
  // Required. The parent, which owns this collection of tables.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "biglake.googleapis.com/Database"
    }
  ];

  // The maximum number of tables to return. The service may return fewer than
  // this value.
  // If unspecified, at most 50 tables will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListTables` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListTables` must match
  // the call that provided the page token.
  string page_token = 3;

  // The view for the returned tables.
  TableView view = 4;
}

// Response message for the ListTables method.
message ListTablesResponse {
  // The tables from the specified database.
  repeated Table tables = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the CreateLock method.
message CreateLockRequest {
  // Required. The parent resource where this lock will be created.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "biglake.googleapis.com/Database"
    }
  ];

  // Required. The lock to create. The `name` field does not need to be provided
  // for the lock creation.
  Lock lock = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the DeleteLock method.
message DeleteLockRequest {
  // Required. The name of the lock to delete.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/locks/{lock_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Lock" }
  ];
}

// Request message for the CheckLock method.
message CheckLockRequest {
  // Required. The name of the lock to check.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}/locks/{lock_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "biglake.googleapis.com/Lock" }
  ];
}

// Request message for the ListLocks method.
message ListLocksRequest {
  // Required. The parent, which owns this collection of locks.
  // Format:
  // projects/{project_id_or_number}/locations/{location_id}/catalogs/{catalog_id}/databases/{database_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "biglake.googleapis.com/Database"
    }
  ];

  // The maximum number of locks to return. The service may return fewer than
  // this value.
  // If unspecified, at most 50 locks will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListLocks` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListLocks` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Response message for the ListLocks method.
message ListLocksResponse {
  // The locks from the specified database.
  repeated Lock locks = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Options of a Hive database.
message HiveDatabaseOptions {
  // Cloud Storage folder URI where the database data is stored, starting with
  // "gs://".
  string location_uri = 1;

  // Stores user supplied Hive database parameters.
  map<string, string> parameters = 2;
}

// Options of a Hive table.
message HiveTableOptions {
  // Serializer and deserializer information.
  message SerDeInfo {
    // The fully qualified Java class name of the serialization library.
    string serialization_lib = 1;
  }

  // Stores physical storage information of the data.
  message StorageDescriptor {
    // Cloud Storage folder URI where the table data is stored, starting with
    // "gs://".
    string location_uri = 1;

    // The fully qualified Java class name of the input format.
    string input_format = 2;

    // The fully qualified Java class name of the output format.
    string output_format = 3;

    // Serializer and deserializer information.
    SerDeInfo serde_info = 4;
  }

  // Stores user supplied Hive table parameters.
  map<string, string> parameters = 1;

  // Hive table type. For example, MANAGED_TABLE, EXTERNAL_TABLE.
  string table_type = 2;

  // Stores physical storage information of the data.
  StorageDescriptor storage_descriptor = 3;
}
