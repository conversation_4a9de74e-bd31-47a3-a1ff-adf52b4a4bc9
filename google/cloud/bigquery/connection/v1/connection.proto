// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.connection.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Connection.V1";
option go_package = "cloud.google.com/go/bigquery/connection/apiv1/connectionpb;connectionpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.bigquery.connection.v1";
option php_namespace = "Google\\Cloud\\BigQuery\\Connection\\V1";
option (google.api.resource_definition) = {
  type: "dataproc.googleapis.com/Cluster"
  pattern: "projects/{project}/regions/{region}/clusters/{cluster}"
};
option (google.api.resource_definition) = {
  type: "metastore.googleapis.com/Service"
  pattern: "projects/{project}/locations/{location}/services/{service}"
};

// Manages external data source connections and credentials.
service ConnectionService {
  option (google.api.default_host) = "bigqueryconnection.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new connection.
  rpc CreateConnection(CreateConnectionRequest) returns (Connection) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/connections"
      body: "connection"
    };
    option (google.api.method_signature) = "parent,connection,connection_id";
  }

  // Returns specified connection.
  rpc GetConnection(GetConnectionRequest) returns (Connection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/connections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of connections in the given project.
  rpc ListConnections(ListConnectionsRequest)
      returns (ListConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/connections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the specified connection. For security reasons, also resets
  // credential if connection properties are in the update field mask.
  rpc UpdateConnection(UpdateConnectionRequest) returns (Connection) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/connections/*}"
      body: "connection"
    };
    option (google.api.method_signature) = "name,connection,update_mask";
  }

  // Deletes connection and associated credential.
  rpc DeleteConnection(DeleteConnectionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/connections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the access control policy for a resource.
  // Returns an empty policy if the resource exists and does not have a policy
  // set.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/connections/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,options";
  }

  // Sets the access control policy on the specified resource. Replaces any
  // existing policy.
  //
  // Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/connections/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Returns permissions that a caller has on the specified resource.
  // If the resource does not exist, this will return an empty set of
  // permissions, not a `NOT_FOUND` error.
  //
  // Note: This operation is designed to be used for building permission-aware
  // UIs and command-line tools, not for authorization checking. This operation
  // may "fail open" without warning.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/connections/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }
}

// The request for
// [ConnectionService.CreateConnection][google.cloud.bigquery.connection.v1.ConnectionService.CreateConnection].
message CreateConnectionRequest {
  // Required. Parent resource name.
  // Must be in the format `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Connection id that should be assigned to the created connection.
  string connection_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Connection to create.
  Connection connection = 3 [(google.api.field_behavior) = REQUIRED];
}

// The request for
// [ConnectionService.GetConnection][google.cloud.bigquery.connection.v1.ConnectionService.GetConnection].
message GetConnectionRequest {
  // Required. Name of the requested connection, for example:
  // `projects/{project_id}/locations/{location_id}/connections/{connection_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryconnection.googleapis.com/Connection"
    }
  ];
}

// The request for
// [ConnectionService.ListConnections][google.cloud.bigquery.connection.v1.ConnectionService.ListConnections].
message ListConnectionsRequest {
  // Required. Parent resource name.
  // Must be in the form: `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Page size.
  int32 page_size = 4 [(google.api.field_behavior) = REQUIRED];

  // Page token.
  string page_token = 3;
}

// The response for
// [ConnectionService.ListConnections][google.cloud.bigquery.connection.v1.ConnectionService.ListConnections].
message ListConnectionsResponse {
  // Next page token.
  string next_page_token = 1;

  // List of connections.
  repeated Connection connections = 2;
}

// The request for
// [ConnectionService.UpdateConnection][google.cloud.bigquery.connection.v1.ConnectionService.UpdateConnection].
message UpdateConnectionRequest {
  // Required. Name of the connection to update, for example:
  // `projects/{project_id}/locations/{location_id}/connections/{connection_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryconnection.googleapis.com/Connection"
    }
  ];

  // Required. Connection containing the updated fields.
  Connection connection = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Update mask for the connection fields to be updated.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = REQUIRED];
}

// The request for [ConnectionService.DeleteConnectionRequest][].
message DeleteConnectionRequest {
  // Required. Name of the deleted connection, for example:
  // `projects/{project_id}/locations/{location_id}/connections/{connection_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryconnection.googleapis.com/Connection"
    }
  ];
}

// Configuration parameters to establish connection with an external data
// source, except the credential attributes.
message Connection {
  option (google.api.resource) = {
    type: "bigqueryconnection.googleapis.com/Connection"
    pattern: "projects/{project}/locations/{location}/connections/{connection}"
  };

  // The resource name of the connection in the form of:
  // `projects/{project_id}/locations/{location_id}/connections/{connection_id}`
  string name = 1;

  // User provided display name for the connection.
  string friendly_name = 2;

  // User provided description.
  string description = 3;

  // Properties specific to the underlying data source.
  oneof properties {
    // Cloud SQL properties.
    CloudSqlProperties cloud_sql = 4;

    // Amazon Web Services (AWS) properties.
    AwsProperties aws = 8;

    // Azure properties.
    AzureProperties azure = 11;

    // Cloud Spanner properties.
    CloudSpannerProperties cloud_spanner = 21;

    // Cloud Resource properties.
    CloudResourceProperties cloud_resource = 22;

    // Spark properties.
    SparkProperties spark = 23;

    // Optional. Salesforce DataCloud properties. This field is intended for
    // use only by Salesforce partner projects. This field contains properties
    // for your Salesforce DataCloud connection.
    SalesforceDataCloudProperties salesforce_data_cloud = 24
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. The creation timestamp of the connection.
  int64 creation_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of the connection.
  int64 last_modified_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True, if credential is configured for this connection.
  bool has_credential = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Connection properties specific to the Cloud SQL.
message CloudSqlProperties {
  // Supported Cloud SQL database types.
  enum DatabaseType {
    // Unspecified database type.
    DATABASE_TYPE_UNSPECIFIED = 0;

    // Cloud SQL for PostgreSQL.
    POSTGRES = 1;

    // Cloud SQL for MySQL.
    MYSQL = 2;
  }

  // Cloud SQL instance ID in the form `project:location:instance`.
  string instance_id = 1;

  // Database name.
  string database = 2;

  // Type of the Cloud SQL database.
  DatabaseType type = 3;

  // Input only. Cloud SQL credential.
  CloudSqlCredential credential = 4 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. The account ID of the service used for the purpose of this
  // connection.
  //
  // When the connection is used in the context of an operation in
  // BigQuery, this service account will serve as the identity being used for
  // connecting to the CloudSQL instance specified in this connection.
  string service_account_id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Credential info for the Cloud SQL.
message CloudSqlCredential {
  // The username for the credential.
  string username = 1;

  // The password for the credential.
  string password = 2;
}

// Connection properties specific to Cloud Spanner.
message CloudSpannerProperties {
  // Cloud Spanner database in the form `project/instance/database'
  string database = 1;

  // If parallelism should be used when reading from Cloud Spanner
  bool use_parallelism = 2;

  // Allows setting max parallelism per query when executing on Spanner
  // independent compute resources. If unspecified, default values of
  // parallelism are chosen that are dependent on the Cloud Spanner instance
  // configuration.
  //
  // REQUIRES: `use_parallelism` must be set.
  // REQUIRES: Either `use_data_boost` or `use_serverless_analytics` must be
  // set.
  int32 max_parallelism = 5;

  // If the serverless analytics service should be used to read data from Cloud
  // Spanner.
  // Note: `use_parallelism` must be set when using serverless analytics.
  bool use_serverless_analytics = 3;

  // If set, the request will be executed via Spanner independent compute
  // resources.
  // REQUIRES: `use_parallelism` must be set.
  //
  // NOTE: `use_serverless_analytics` will be deprecated. Prefer
  // `use_data_boost` over `use_serverless_analytics`.
  bool use_data_boost = 6;

  // Optional. Cloud Spanner database role for fine-grained access control.
  // The Cloud Spanner admin should have provisioned the database role with
  // appropriate permissions, such as `SELECT` and `INSERT`. Other users should
  // only use roles provided by their Cloud Spanner admins.
  //
  // For more details, see [About fine-grained access control]
  // (https://cloud.google.com/spanner/docs/fgac-about).
  //
  // REQUIRES: The database role name must start with a letter, and can only
  // contain letters, numbers, and underscores.
  string database_role = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Connection properties specific to Amazon Web Services (AWS).
message AwsProperties {
  // Authentication method chosen at connection creation.
  oneof authentication_method {
    // Authentication using Google owned AWS IAM user's access key to assume
    // into customer's AWS IAM Role.
    // Deprecated, do not use.
    AwsCrossAccountRole cross_account_role = 2 [deprecated = true];

    // Authentication using Google owned service account to assume into
    // customer's AWS IAM Role.
    AwsAccessRole access_role = 3;
  }
}

// Authentication method for Amazon Web Services (AWS) that uses Google owned
// AWS IAM user's access key to assume into customer's AWS IAM Role.
message AwsCrossAccountRole {
  // The user’s AWS IAM Role that trusts the Google-owned AWS IAM user
  // Connection.
  string iam_role_id = 1;

  // Output only. Google-owned AWS IAM User for a Connection.
  string iam_user_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A Google-generated id for representing Connection’s identity
  // in AWS. External Id is also used for preventing the Confused Deputy
  // Problem. See
  // https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_create_for-user_externalid.html
  string external_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Authentication method for Amazon Web Services (AWS) that uses Google owned
// Google service account to assume into customer's AWS IAM Role.
message AwsAccessRole {
  // The user’s AWS IAM Role that trusts the Google-owned AWS IAM user
  // Connection.
  string iam_role_id = 1;

  // A unique Google-owned and Google-generated identity for the Connection.
  // This identity will be used to access the user's AWS IAM Role.
  string identity = 2;
}

// Container for connection properties specific to Azure.
message AzureProperties {
  // Output only. The name of the Azure Active Directory Application.
  string application = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The client id of the Azure Active Directory Application.
  string client_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The object id of the Azure Active Directory Application.
  string object_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The id of customer's directory that host the data.
  string customer_tenant_id = 4;

  // The URL user will be redirected to after granting consent during connection
  // setup.
  string redirect_uri = 5;

  // The client ID of the user's Azure Active Directory Application used for a
  // federated connection.
  string federated_application_client_id = 6;

  // Output only. A unique Google-owned and Google-generated identity for the
  // Connection. This identity will be used to access the user's Azure Active
  // Directory Application.
  string identity = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Container for connection properties for delegation of access to GCP
// resources.
message CloudResourceProperties {
  // Output only. The account ID of the service created for the purpose of this
  // connection.
  //
  // The service account does not have any permissions associated with it
  // when it is created. After creation, customers delegate permissions
  // to the service account. When the connection is used in the context of an
  // operation in BigQuery, the service account will be used to connect to the
  // desired resources in GCP.
  //
  // The account ID is in the form of:
  //   <service-1234>@gcp-sa-bigquery-cloudresource.iam.gserviceaccount.com
  string service_account_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration of the Dataproc Metastore Service.
message MetastoreServiceConfig {
  // Optional. Resource name of an existing Dataproc Metastore service.
  //
  // Example:
  //
  // * `projects/[project_id]/locations/[region]/services/[service_id]`
  string metastore_service = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Service"
    }
  ];
}

// Configuration of the Spark History Server.
message SparkHistoryServerConfig {
  // Optional. Resource name of an existing Dataproc Cluster to act as a Spark
  // History Server for the connection.
  //
  // Example:
  //
  // * `projects/[project_id]/regions/[region]/clusters/[cluster_name]`
  string dataproc_cluster = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dataproc.googleapis.com/Cluster"
    }
  ];
}

// Container for connection properties to execute stored procedures for Apache
// Spark.
message SparkProperties {
  // Output only. The account ID of the service created for the purpose of this
  // connection.
  //
  // The service account does not have any permissions associated with it when
  // it is created. After creation, customers delegate permissions to the
  // service account. When the connection is used in the context of a stored
  // procedure for Apache Spark in BigQuery, the service account is used to
  // connect to the desired resources in Google Cloud.
  //
  // The account ID is in the form of:
  // bqcx-<projectnumber>-<uniqueid>@gcp-sa-bigquery-consp.iam.gserviceaccount.com
  string service_account_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Dataproc Metastore Service configuration for the connection.
  MetastoreServiceConfig metastore_service_config = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Spark History Server configuration for the connection.
  SparkHistoryServerConfig spark_history_server_config = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Connection properties specific to Salesforce DataCloud. This is intended for
// use only by Salesforce partner projects.
message SalesforceDataCloudProperties {
  // The URL to the user's Salesforce DataCloud instance.
  string instance_uri = 1;

  // Output only. A unique Google-owned and Google-generated service account
  // identity for the connection.
  string identity = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The ID of the user's Salesforce tenant.
  string tenant_id = 3;
}
