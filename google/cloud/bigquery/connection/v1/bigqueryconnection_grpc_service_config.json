{"methodConfig": [{"name": [{"service": "google.cloud.bigquery.connection.v1.ConnectionService", "method": "CreateConnection"}, {"service": "google.cloud.bigquery.connection.v1.ConnectionService", "method": "UpdateConnection"}, {"service": "google.cloud.bigquery.connection.v1.ConnectionService", "method": "GetIamPolicy"}, {"service": "google.cloud.bigquery.connection.v1.ConnectionService", "method": "SetIamPolicy"}, {"service": "google.cloud.bigquery.connection.v1.ConnectionService", "method": "TestIamPermissions"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.bigquery.connection.v1.ConnectionService", "method": "GetConnection"}, {"service": "google.cloud.bigquery.connection.v1.ConnectionService", "method": "ListConnections"}, {"service": "google.cloud.bigquery.connection.v1.ConnectionService", "method": "DeleteConnection"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}