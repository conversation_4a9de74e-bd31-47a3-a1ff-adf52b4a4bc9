# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "connection_proto",
    srcs = [
        "connection.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "connection_proto_with_info",
    deps = [
        ":connection_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "connection_java_proto",
    deps = [":connection_proto"],
)

java_grpc_library(
    name = "connection_java_grpc",
    srcs = [":connection_proto"],
    deps = [":connection_java_proto"],
)

java_gapic_library(
    name = "connection_java_gapic",
    srcs = [":connection_proto_with_info"],
    grpc_service_config = "bigqueryconnection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigqueryconnection_v1beta1.yaml",
    test_deps = [
        ":connection_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":connection_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "connection_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.connection.v1beta1.ConnectionServiceClientHttpJsonTest",
        "com.google.cloud.bigquery.connection.v1beta1.ConnectionServiceClientTest",
    ],
    runtime_deps = [":connection_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-connection-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":connection_java_gapic",
        ":connection_java_grpc",
        ":connection_java_proto",
        ":connection_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "connection_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/connection/apiv1beta1/connectionpb",
    protos = [":connection_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

go_gapic_library(
    name = "connection_go_gapic",
    srcs = [":connection_proto_with_info"],
    grpc_service_config = "bigqueryconnection_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/connection/apiv1beta1;connection",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "bigqueryconnection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":connection_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-connection-v1beta1-go",
    deps = [
        ":connection_go_gapic",
        ":connection_go_gapic_srcjar-snippets.srcjar",
        ":connection_go_gapic_srcjar-test.srcjar",
        ":connection_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
)

py_gapic_library(
    name = "connection_py_gapic",
    srcs = [":connection_proto"],
    grpc_service_config = "bigqueryconnection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigqueryconnection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "connection_py_gapic_test",
    srcs = [
        "connection_py_gapic_pytest.py",
        "connection_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":connection_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-connection-v1beta1-py",
    deps = [
        ":connection_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "connection_php_proto",
    deps = [":connection_proto"],
)

php_gapic_library(
    name = "connection_php_gapic",
    srcs = [":connection_proto_with_info"],
    grpc_service_config = "bigqueryconnection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigqueryconnection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":connection_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-connection-v1beta1-php",
    deps = [
        ":connection_php_gapic",
        ":connection_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "connection_nodejs_gapic",
    src = ":connection_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "bigqueryconnection_grpc_service_config.json",
    package = "google.cloud.bigquery.connection.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "bigqueryconnection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-connection-v1beta1-nodejs",
    deps = [
        ":connection_nodejs_gapic",
        ":connection_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "connection_ruby_proto",
    deps = [":connection_proto"],
)

ruby_grpc_library(
    name = "connection_ruby_grpc",
    srcs = [":connection_proto"],
    deps = [":connection_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "connection_ruby_gapic",
    srcs = [":connection_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-connection-v1beta1"],
    rest_numeric_enums = True,
    service_yaml = "bigqueryconnection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":connection_ruby_grpc",
        ":connection_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-connection-v1beta1-ruby",
    deps = [
        ":connection_ruby_gapic",
        ":connection_ruby_grpc",
        ":connection_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "connection_csharp_proto",
    deps = [":connection_proto"],
)

csharp_grpc_library(
    name = "connection_csharp_grpc",
    srcs = [":connection_proto"],
    deps = [":connection_csharp_proto"],
)

csharp_gapic_library(
    name = "connection_csharp_gapic",
    srcs = [":connection_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "bigqueryconnection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigqueryconnection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":connection_csharp_grpc",
        ":connection_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-connection-v1beta1-csharp",
    deps = [
        ":connection_csharp_gapic",
        ":connection_csharp_grpc",
        ":connection_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
