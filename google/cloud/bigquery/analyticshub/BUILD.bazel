# This build file includes a target for the Ruby wrapper library for
# google-cloud-bigquery-analytics_hub.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for analyticshub.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "analyticshub_ruby_wrapper",
    srcs = ["//google/cloud/bigquery/analyticshub/v1:analyticshub_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigquery-analytics_hub",
        "ruby-cloud-wrapper-of=v1:0.5",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery",
        "ruby-cloud-api-id=analyticshub.googleapis.com",
        "ruby-cloud-api-shortname=analyticshub",
    ],
    ruby_cloud_description = "Analytics Hub is a data exchange platform that enables you to share data and insights at scale across organizational boundaries with a robust security and privacy framework. With Analytics Hub, you can discover and access a data library curated by various data providers.",
    ruby_cloud_title = "Analytics Hub V1",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-analyticshub-ruby",
    deps = [
        ":analyticshub_ruby_wrapper",
    ],
)
