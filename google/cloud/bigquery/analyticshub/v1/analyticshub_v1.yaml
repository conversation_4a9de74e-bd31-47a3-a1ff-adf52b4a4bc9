type: google.api.Service
config_version: 3
name: analyticshub.googleapis.com
title: Analytics Hub API

apis:
- name: google.cloud.bigquery.analyticshub.v1.AnalyticsHubService
- name: google.longrunning.Operations

types:
- name: google.cloud.bigquery.analyticshub.v1.OperationMetadata
- name: google.cloud.bigquery.analyticshub.v1.RefreshSubscriptionResponse
- name: google.cloud.bigquery.analyticshub.v1.SubscribeDataExchangeResponse

documentation:
  summary: Exchange data and analytics assets securely and efficiently.

authentication:
  rules:
  - selector: 'google.cloud.bigquery.analyticshub.v1.AnalyticsHubService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
