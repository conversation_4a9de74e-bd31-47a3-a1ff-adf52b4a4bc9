# This build file includes a target for the Ruby wrapper library for
# google-cloud-bigquery-reservation.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for bigqueryreservation.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "bigqueryreservation_ruby_wrapper",
    srcs = ["//google/cloud/bigquery/reservation/v1:reservation_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigquery-reservation",
        "ruby-cloud-env-prefix=BIGQUERY_RESERVATION",
        "ruby-cloud-wrapper-of=v1:0.11",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs/reference/reservations",
        "ruby-cloud-api-id=bigqueryreservation.googleapis.com",
        "ruby-cloud-api-shortname=bigqueryreservation",
    ],
    ruby_cloud_description = "The BigQuery Reservation API provides the mechanisms by which enterprise users can provision and manage dedicated resources such as slots and BigQuery BI Engine memory allocations.",
    ruby_cloud_title = "BigQuery Reservation",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-reservation-ruby",
    deps = [
        ":bigqueryreservation_ruby_wrapper",
    ],
)
