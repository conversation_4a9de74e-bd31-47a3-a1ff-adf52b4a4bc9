# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "reservation_proto",
    srcs = [
        "reservation.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "reservation_proto_with_info",
    deps = [
        ":reservation_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "reservation_java_proto",
    deps = [":reservation_proto"],
)

java_grpc_library(
    name = "reservation_java_grpc",
    srcs = [":reservation_proto"],
    deps = [":reservation_java_proto"],
)

java_gapic_library(
    name = "reservation_java_gapic",
    srcs = [":reservation_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "bigqueryreservation_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigqueryreservation_v1.yaml",
    test_deps = [
        ":reservation_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":reservation_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "reservation_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.reservation.v1.ReservationServiceClientHttpJsonTest",
        "com.google.cloud.bigquery.reservation.v1.ReservationServiceClientTest",
    ],
    runtime_deps = [":reservation_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-reservation-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":reservation_java_gapic",
        ":reservation_java_grpc",
        ":reservation_java_proto",
        ":reservation_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "reservation_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/reservation/apiv1/reservationpb",
    protos = [":reservation_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "reservation_go_gapic",
    srcs = [":reservation_proto_with_info"],
    grpc_service_config = "bigqueryreservation_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/reservation/apiv1;reservation",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "bigqueryreservation_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":reservation_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-reservation-v1-go",
    deps = [
        ":reservation_go_gapic",
        ":reservation_go_gapic_srcjar-metadata.srcjar",
        ":reservation_go_gapic_srcjar-snippets.srcjar",
        ":reservation_go_gapic_srcjar-test.srcjar",
        ":reservation_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "reservation_py_gapic",
    srcs = [":reservation_proto"],
    grpc_service_config = "bigqueryreservation_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=bigquery_reservation",
        "python-gapic-namespace=google.cloud",
    ],
    rest_numeric_enums = True,
    service_yaml = "bigqueryreservation_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "reservation_py_gapic_test",
    srcs = [
        "reservation_py_gapic_pytest.py",
        "reservation_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":reservation_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-reservation-v1-py",
    deps = [
        ":reservation_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "reservation_php_proto",
    deps = [":reservation_proto"],
)

php_gapic_library(
    name = "reservation_php_gapic",
    srcs = [":reservation_proto_with_info"],
    grpc_service_config = "bigqueryreservation_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "bigqueryreservation_v1.yaml",
    transport = "grpc+rest",
    deps = [":reservation_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-reservation-v1-php",
    deps = [
        ":reservation_php_gapic",
        ":reservation_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "reservation_nodejs_gapic",
    package_name = "@google-cloud/bigquery-reservation",
    src = ":reservation_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "bigqueryreservation_grpc_service_config.json",
    package = "google.cloud.bigquery.reservation.v1",
    rest_numeric_enums = True,
    service_yaml = "bigqueryreservation_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-reservation-v1-nodejs",
    deps = [
        ":reservation_nodejs_gapic",
        ":reservation_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "reservation_ruby_proto",
    deps = [":reservation_proto"],
)

ruby_grpc_library(
    name = "reservation_ruby_grpc",
    srcs = [":reservation_proto"],
    deps = [":reservation_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "reservation_ruby_gapic",
    srcs = [":reservation_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=bigqueryreservation.googleapis.com",
        "ruby-cloud-api-shortname=bigqueryreservation",
        "ruby-cloud-env-prefix=BIGQUERY_RESERVATION",
        "ruby-cloud-gem-name=google-cloud-bigquery-reservation-v1",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs/reference/reservations",
    ],
    grpc_service_config = "bigqueryreservation_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The BigQuery Reservation API provides the mechanisms by which enterprise users can provision and manage dedicated resources such as slots and BigQuery BI Engine memory allocations.",
    ruby_cloud_title = "BigQuery Reservation V1",
    service_yaml = "bigqueryreservation_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":reservation_ruby_grpc",
        ":reservation_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-reservation-v1-ruby",
    deps = [
        ":reservation_ruby_gapic",
        ":reservation_ruby_grpc",
        ":reservation_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "reservation_csharp_proto",
    deps = [":reservation_proto"],
)

csharp_grpc_library(
    name = "reservation_csharp_grpc",
    srcs = [":reservation_proto"],
    deps = [":reservation_csharp_proto"],
)

csharp_gapic_library(
    name = "reservation_csharp_gapic",
    srcs = [":reservation_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "bigqueryreservation_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigqueryreservation_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":reservation_csharp_grpc",
        ":reservation_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-reservation-v1-csharp",
    deps = [
        ":reservation_csharp_gapic",
        ":reservation_csharp_grpc",
        ":reservation_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "reservation_cc_proto",
    deps = [":reservation_proto"],
)

cc_grpc_library(
    name = "reservation_cc_grpc",
    srcs = [":reservation_proto"],
    grpc_only = True,
    deps = [":reservation_cc_proto"],
)
