// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.reservation.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Reservation.V1";
option go_package = "cloud.google.com/go/bigquery/reservation/apiv1/reservationpb;reservationpb";
option java_multiple_files = true;
option java_outer_classname = "ReservationProto";
option java_package = "com.google.cloud.bigquery.reservation.v1";
option php_namespace = "Google\\Cloud\\BigQuery\\Reservation\\V1";

// This API allows users to manage their BigQuery reservations.
//
// A reservation provides computational resource guarantees, in the form of
// [slots](https://cloud.google.com/bigquery/docs/slots), to users. A slot is a
// unit of computational power in BigQuery, and serves as the basic unit of
// parallelism. In a scan of a multi-partitioned table, a single slot operates
// on a single partition of the table. A reservation resource exists as a child
// resource of the admin project and location, e.g.:
//   `projects/myproject/locations/US/reservations/reservationName`.
//
// A capacity commitment is a way to purchase compute capacity for BigQuery jobs
// (in the form of slots) with some committed period of usage. A capacity
// commitment resource exists as a child resource of the admin project and
// location, e.g.:
//   `projects/myproject/locations/US/capacityCommitments/id`.
service ReservationService {
  option (google.api.default_host) = "bigqueryreservation.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new reservation resource.
  rpc CreateReservation(CreateReservationRequest) returns (Reservation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/reservations"
      body: "reservation"
    };
    option (google.api.method_signature) = "parent,reservation,reservation_id";
  }

  // Lists all the reservations for the project in the specified location.
  rpc ListReservations(ListReservationsRequest)
      returns (ListReservationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/reservations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns information about the reservation.
  rpc GetReservation(GetReservationRequest) returns (Reservation) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/reservations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a reservation.
  // Returns `google.rpc.Code.FAILED_PRECONDITION` when reservation has
  // assignments.
  rpc DeleteReservation(DeleteReservationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/reservations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an existing reservation resource.
  rpc UpdateReservation(UpdateReservationRequest) returns (Reservation) {
    option (google.api.http) = {
      patch: "/v1/{reservation.name=projects/*/locations/*/reservations/*}"
      body: "reservation"
    };
    option (google.api.method_signature) = "reservation,update_mask";
  }

  // Fail over a reservation to the secondary location. The operation should be
  // done in the current secondary location, which will be promoted to the
  // new primary location for the reservation.
  // Attempting to failover a reservation in the current primary location will
  // fail with the error code `google.rpc.Code.FAILED_PRECONDITION`.
  rpc FailoverReservation(FailoverReservationRequest) returns (Reservation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/reservations/*}:failoverReservation"
      body: "*"
    };
  }

  // Creates a new capacity commitment resource.
  rpc CreateCapacityCommitment(CreateCapacityCommitmentRequest)
      returns (CapacityCommitment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/capacityCommitments"
      body: "capacity_commitment"
    };
    option (google.api.method_signature) = "parent,capacity_commitment";
  }

  // Lists all the capacity commitments for the admin project.
  rpc ListCapacityCommitments(ListCapacityCommitmentsRequest)
      returns (ListCapacityCommitmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/capacityCommitments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns information about the capacity commitment.
  rpc GetCapacityCommitment(GetCapacityCommitmentRequest)
      returns (CapacityCommitment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/capacityCommitments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a capacity commitment. Attempting to delete capacity commitment
  // before its commitment_end_time will fail with the error code
  // `google.rpc.Code.FAILED_PRECONDITION`.
  rpc DeleteCapacityCommitment(DeleteCapacityCommitmentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/capacityCommitments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an existing capacity commitment.
  //
  // Only `plan` and `renewal_plan` fields can be updated.
  //
  // Plan can only be changed to a plan of a longer commitment period.
  // Attempting to change to a plan with shorter commitment period will fail
  // with the error code `google.rpc.Code.FAILED_PRECONDITION`.
  rpc UpdateCapacityCommitment(UpdateCapacityCommitmentRequest)
      returns (CapacityCommitment) {
    option (google.api.http) = {
      patch: "/v1/{capacity_commitment.name=projects/*/locations/*/capacityCommitments/*}"
      body: "capacity_commitment"
    };
    option (google.api.method_signature) = "capacity_commitment,update_mask";
  }

  // Splits capacity commitment to two commitments of the same plan and
  // `commitment_end_time`.
  //
  // A common use case is to enable downgrading commitments.
  //
  // For example, in order to downgrade from 10000 slots to 8000, you might
  // split a 10000 capacity commitment into commitments of 2000 and 8000. Then,
  // you delete the first one after the commitment end time passes.
  rpc SplitCapacityCommitment(SplitCapacityCommitmentRequest)
      returns (SplitCapacityCommitmentResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/capacityCommitments/*}:split"
      body: "*"
    };
    option (google.api.method_signature) = "name,slot_count";
  }

  // Merges capacity commitments of the same plan into a single commitment.
  //
  // The resulting capacity commitment has the greater commitment_end_time
  // out of the to-be-merged capacity commitments.
  //
  // Attempting to merge capacity commitments of different plan will fail
  // with the error code `google.rpc.Code.FAILED_PRECONDITION`.
  rpc MergeCapacityCommitments(MergeCapacityCommitmentsRequest)
      returns (CapacityCommitment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/capacityCommitments:merge"
      body: "*"
    };
    option (google.api.method_signature) = "parent,capacity_commitment_ids";
  }

  // Creates an assignment object which allows the given project to submit jobs
  // of a certain type using slots from the specified reservation.
  //
  // Currently a
  // resource (project, folder, organization) can only have one assignment per
  // each (job_type, location) combination, and that reservation will be used
  // for all jobs of the matching type.
  //
  // Different assignments can be created on different levels of the
  // projects, folders or organization hierarchy.  During query execution,
  // the assignment is looked up at the project, folder and organization levels
  // in that order. The first assignment found is applied to the query.
  //
  // When creating assignments, it does not matter if other assignments exist at
  // higher levels.
  //
  // Example:
  //
  // * The organization `organizationA` contains two projects, `project1`
  //   and `project2`.
  // * Assignments for all three entities (`organizationA`, `project1`, and
  //   `project2`) could all be created and mapped to the same or different
  //   reservations.
  //
  // "None" assignments represent an absence of the assignment. Projects
  // assigned to None use on-demand pricing. To create a "None" assignment, use
  // "none" as a reservation_id in the parent. Example parent:
  // `projects/myproject/locations/US/reservations/none`.
  //
  // Returns `google.rpc.Code.PERMISSION_DENIED` if user does not have
  // 'bigquery.admin' permissions on the project using the reservation
  // and the project that owns this reservation.
  //
  // Returns `google.rpc.Code.INVALID_ARGUMENT` when location of the assignment
  // does not match location of the reservation.
  rpc CreateAssignment(CreateAssignmentRequest) returns (Assignment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/reservations/*}/assignments"
      body: "assignment"
    };
    option (google.api.method_signature) = "parent,assignment";
  }

  // Lists assignments.
  //
  // Only explicitly created assignments will be returned.
  //
  // Example:
  //
  // * Organization `organizationA` contains two projects, `project1` and
  //   `project2`.
  // * Reservation `res1` exists and was created previously.
  // * CreateAssignment was used previously to define the following
  //   associations between entities and reservations: `<organizationA, res1>`
  //   and `<project1, res1>`
  //
  // In this example, ListAssignments will just return the above two assignments
  // for reservation `res1`, and no expansion/merge will happen.
  //
  // The wildcard "-" can be used for
  // reservations in the request. In that case all assignments belongs to the
  // specified project and location will be listed.
  //
  // **Note** "-" cannot be used for projects nor locations.
  rpc ListAssignments(ListAssignmentsRequest)
      returns (ListAssignmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/reservations/*}/assignments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a assignment. No expansion will happen.
  //
  // Example:
  //
  // * Organization `organizationA` contains two projects, `project1` and
  //   `project2`.
  // * Reservation `res1` exists and was created previously.
  // * CreateAssignment was used previously to define the following
  //   associations between entities and reservations: `<organizationA, res1>`
  //   and `<project1, res1>`
  //
  // In this example, deletion of the `<organizationA, res1>` assignment won't
  // affect the other assignment `<project1, res1>`. After said deletion,
  // queries from `project1` will still use `res1` while queries from
  // `project2` will switch to use on-demand mode.
  rpc DeleteAssignment(DeleteAssignmentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/reservations/*/assignments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deprecated: Looks up assignments for a specified resource for a particular
  // region. If the request is about a project:
  //
  // 1. Assignments created on the project will be returned if they exist.
  // 2. Otherwise assignments created on the closest ancestor will be
  //    returned.
  // 3. Assignments for different JobTypes will all be returned.
  //
  // The same logic applies if the request is about a folder.
  //
  // If the request is about an organization, then assignments created on the
  // organization will be returned (organization doesn't have ancestors).
  //
  // Comparing to ListAssignments, there are some behavior
  // differences:
  //
  // 1. permission on the assignee will be verified in this API.
  // 2. Hierarchy lookup (project->folder->organization) happens in this API.
  // 3. Parent here is `projects/*/locations/*`, instead of
  //    `projects/*/locations/*reservations/*`.
  //
  // **Note** "-" cannot be used for projects
  // nor locations.
  rpc SearchAssignments(SearchAssignmentsRequest)
      returns (SearchAssignmentsResponse) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}:searchAssignments"
    };
    option (google.api.method_signature) = "parent,query";
  }

  // Looks up assignments for a specified resource for a particular region.
  // If the request is about a project:
  //
  // 1. Assignments created on the project will be returned if they exist.
  // 2. Otherwise assignments created on the closest ancestor will be
  //    returned.
  // 3. Assignments for different JobTypes will all be returned.
  //
  // The same logic applies if the request is about a folder.
  //
  // If the request is about an organization, then assignments created on the
  // organization will be returned (organization doesn't have ancestors).
  //
  // Comparing to ListAssignments, there are some behavior
  // differences:
  //
  // 1. permission on the assignee will be verified in this API.
  // 2. Hierarchy lookup (project->folder->organization) happens in this API.
  // 3. Parent here is `projects/*/locations/*`, instead of
  //    `projects/*/locations/*reservations/*`.
  rpc SearchAllAssignments(SearchAllAssignmentsRequest)
      returns (SearchAllAssignmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}:searchAllAssignments"
    };
    option (google.api.method_signature) = "parent,query";
  }

  // Moves an assignment under a new reservation.
  //
  // This differs from removing an existing assignment and recreating a new one
  // by providing a transactional change that ensures an assignee always has an
  // associated reservation.
  rpc MoveAssignment(MoveAssignmentRequest) returns (Assignment) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/reservations/*/assignments/*}:move"
      body: "*"
    };
    option (google.api.method_signature) = "name,destination_id";
  }

  // Updates an existing assignment.
  //
  // Only the `priority` field can be updated.
  rpc UpdateAssignment(UpdateAssignmentRequest) returns (Assignment) {
    option (google.api.http) = {
      patch: "/v1/{assignment.name=projects/*/locations/*/reservations/*/assignments/*}"
      body: "assignment"
    };
    option (google.api.method_signature) = "assignment,update_mask";
  }

  // Retrieves a BI reservation.
  rpc GetBiReservation(GetBiReservationRequest) returns (BiReservation) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/biReservation}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a BI reservation.
  //
  // Only fields specified in the `field_mask` are updated.
  //
  // A singleton BI reservation always exists with default size 0.
  // In order to reserve BI capacity it needs to be updated to an amount
  // greater than 0. In order to release BI capacity reservation size
  // must be set to 0.
  rpc UpdateBiReservation(UpdateBiReservationRequest) returns (BiReservation) {
    option (google.api.http) = {
      patch: "/v1/{bi_reservation.name=projects/*/locations/*/biReservation}"
      body: "bi_reservation"
    };
    option (google.api.method_signature) = "bi_reservation,update_mask";
  }
}

// A reservation is a mechanism used to guarantee slots to users.
message Reservation {
  option (google.api.resource) = {
    type: "bigqueryreservation.googleapis.com/Reservation"
    pattern: "projects/{project}/locations/{location}/reservations/{reservation}"
  };

  // Auto scaling settings.
  message Autoscale {
    // Output only. The slot capacity added to this reservation when autoscale
    // happens. Will be between [0, max_slots]. Note: after users reduce
    // max_slots, it may take a while before it can be propagated, so
    // current_slots may stay in the original value and could be larger than
    // max_slots for that brief period (less than one minute)
    int64 current_slots = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Number of slots to be scaled when needed.
    int64 max_slots = 2;
  }

  // The resource name of the reservation, e.g.,
  // `projects/*/locations/*/reservations/team1-prod`.
  // The reservation_id must only contain lower case alphanumeric characters or
  // dashes. It must start with a letter and must not end with a dash. Its
  // maximum length is 64 characters.
  string name = 1;

  // Baseline slots available to this reservation. A slot is a unit of
  // computational power in BigQuery, and serves as the unit of parallelism.
  //
  // Queries using this reservation might use more slots during runtime if
  // ignore_idle_slots is set to false, or autoscaling is enabled.
  //
  // If edition is EDITION_UNSPECIFIED and total slot_capacity of the
  // reservation and its siblings exceeds the total slot_count of all capacity
  // commitments, the request will fail with
  // `google.rpc.Code.RESOURCE_EXHAUSTED`.
  //
  // If edition is any value but EDITION_UNSPECIFIED, then the above requirement
  // is not needed. The total slot_capacity of the reservation and its siblings
  // may exceed the total slot_count of capacity commitments. In that case, the
  // exceeding slots will be charged with the autoscale SKU. You can increase
  // the number of baseline slots in a reservation every few minutes. If you
  // want to decrease your baseline slots, you are limited to once an hour if
  // you have recently changed your baseline slot capacity and your baseline
  // slots exceed your committed slots. Otherwise, you can decrease your
  // baseline slots every few minutes.
  int64 slot_capacity = 2;

  // If false, any query or pipeline job using this reservation will use idle
  // slots from other reservations within the same admin project. If true, a
  // query or pipeline job using this reservation will execute with the slot
  // capacity specified in the slot_capacity field at most.
  bool ignore_idle_slots = 4;

  // The configuration parameters for the auto scaling feature.
  Autoscale autoscale = 7;

  // Job concurrency target which sets a soft upper bound on the number of jobs
  // that can run concurrently in this reservation. This is a soft target due to
  // asynchronous nature of the system and various optimizations for small
  // queries.
  // Default value is 0 which means that concurrency target will be
  // automatically computed by the system.
  // NOTE: this field is exposed as target job concurrency in the Information
  // Schema, DDL and BigQuery CLI.
  int64 concurrency = 16;

  // Output only. Creation time of the reservation.
  google.protobuf.Timestamp creation_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of the reservation.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Applicable only for reservations located within one of the BigQuery
  // multi-regions (US or EU).
  //
  // If set to true, this reservation is placed in the organization's
  // secondary region which is designated for disaster recovery purposes.
  // If false, this reservation is placed in the organization's default region.
  //
  // NOTE: this is a preview feature. Project must be allow-listed in order to
  // set this field.
  bool multi_region_auxiliary = 14;

  // Edition of the reservation.
  Edition edition = 17;

  // Optional. The current location of the reservation's primary replica. This
  // field is only set for reservations using the managed disaster recovery
  // feature.
  string primary_location = 18 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The current location of the reservation's secondary replica. This
  // field is only set for reservations using the managed disaster recovery
  // feature. Users can set this in create reservation calls
  // to create a failover reservation or in update reservation calls to convert
  // a non-failover reservation to a failover reservation(or vice versa).
  string secondary_location = 19 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The location where the reservation was originally created. This
  // is set only during the failover reservation's creation. All billing charges
  // for the failover reservation will be applied to this location.
  string original_primary_location = 20 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Capacity commitment is a way to purchase compute capacity for BigQuery jobs
// (in the form of slots) with some committed period of usage. Annual
// commitments renew by default. Commitments can be removed after their
// commitment end time passes.
//
// In order to remove annual commitment, its plan needs to be changed
// to monthly or flex first.
//
// A capacity commitment resource exists as a child resource of the admin
// project.
message CapacityCommitment {
  option (google.api.resource) = {
    type: "bigqueryreservation.googleapis.com/CapacityCommitment"
    pattern: "projects/{project}/locations/{location}/capacityCommitments/{capacity_commitment}"
  };

  // Commitment plan defines the current committed period. Capacity commitment
  // cannot be deleted during it's committed period.
  enum CommitmentPlan {
    // Invalid plan value. Requests with this value will be rejected with
    // error code `google.rpc.Code.INVALID_ARGUMENT`.
    COMMITMENT_PLAN_UNSPECIFIED = 0;

    // Flex commitments have committed period of 1 minute after becoming ACTIVE.
    // After that, they are not in a committed period anymore and can be removed
    // any time.
    FLEX = 3;

    // Same as FLEX, should only be used if flat-rate commitments are still
    // available.
    FLEX_FLAT_RATE = 7 [deprecated = true];

    // Trial commitments have a committed period of 182 days after becoming
    // ACTIVE. After that, they are converted to a new commitment based on the
    // `renewal_plan`. Default `renewal_plan` for Trial commitment is Flex so
    // that it can be deleted right after committed period ends.
    TRIAL = 5 [deprecated = true];

    // Monthly commitments have a committed period of 30 days after becoming
    // ACTIVE. After that, they are not in a committed period anymore and can be
    // removed any time.
    MONTHLY = 2;

    // Same as MONTHLY, should only be used if flat-rate commitments are still
    // available.
    MONTHLY_FLAT_RATE = 8 [deprecated = true];

    // Annual commitments have a committed period of 365 days after becoming
    // ACTIVE. After that they are converted to a new commitment based on the
    // renewal_plan.
    ANNUAL = 4;

    // Same as ANNUAL, should only be used if flat-rate commitments are still
    // available.
    ANNUAL_FLAT_RATE = 9 [deprecated = true];

    // 3-year commitments have a committed period of 1095(3 * 365) days after
    // becoming ACTIVE. After that they are converted to a new commitment based
    // on the renewal_plan.
    THREE_YEAR = 10;

    // Should only be used for `renewal_plan` and is only meaningful if
    // edition is specified to values other than EDITION_UNSPECIFIED. Otherwise
    // CreateCapacityCommitmentRequest or UpdateCapacityCommitmentRequest will
    // be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. If the
    // renewal_plan is NONE, capacity commitment will be removed at the end of
    // its commitment period.
    NONE = 6;
  }

  // Capacity commitment can either become ACTIVE right away or transition
  // from PENDING to ACTIVE or FAILED.
  enum State {
    // Invalid state value.
    STATE_UNSPECIFIED = 0;

    // Capacity commitment is pending provisioning. Pending capacity commitment
    // does not contribute to the project's slot_capacity.
    PENDING = 1;

    // Once slots are provisioned, capacity commitment becomes active.
    // slot_count is added to the project's slot_capacity.
    ACTIVE = 2;

    // Capacity commitment is failed to be activated by the backend.
    FAILED = 3;
  }

  // Output only. The resource name of the capacity commitment, e.g.,
  // `projects/myproject/locations/US/capacityCommitments/123`
  // The commitment_id must only contain lower case alphanumeric characters or
  // dashes. It must start with a letter and must not end
  // with a dash. Its maximum length is 64 characters.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Number of slots in this commitment.
  int64 slot_count = 2;

  // Capacity commitment commitment plan.
  CommitmentPlan plan = 3;

  // Output only. State of the commitment.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The start of the current commitment period. It is applicable
  // only for ACTIVE capacity commitments. Note after the commitment is renewed,
  // commitment_start_time won't be changed. It refers to the start time of the
  // original commitment.
  google.protobuf.Timestamp commitment_start_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The end of the current commitment period. It is applicable
  // only for ACTIVE capacity commitments. Note after renewal,
  // commitment_end_time is the time the renewed commitment expires. So it would
  // be at a time after commitment_start_time + committed period, because we
  // don't change commitment_start_time ,
  google.protobuf.Timestamp commitment_end_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For FAILED commitment plan, provides the reason of failure.
  google.rpc.Status failure_status = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The plan this capacity commitment is converted to after commitment_end_time
  // passes. Once the plan is changed, committed period is extended according to
  // commitment plan. Only applicable for ANNUAL and TRIAL commitments.
  CommitmentPlan renewal_plan = 8;

  // Applicable only for commitments located within one of the BigQuery
  // multi-regions (US or EU).
  //
  // If set to true, this commitment is placed in the organization's
  // secondary region which is designated for disaster recovery purposes.
  // If false, this commitment is placed in the organization's default region.
  //
  // NOTE: this is a preview feature. Project must be allow-listed in order to
  // set this field.
  bool multi_region_auxiliary = 10;

  // Edition of the capacity commitment.
  Edition edition = 12;

  // Output only. If true, the commitment is a flat-rate commitment, otherwise,
  // it's an edition commitment.
  bool is_flat_rate = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request for
// [ReservationService.CreateReservation][google.cloud.bigquery.reservation.v1.ReservationService.CreateReservation].
message CreateReservationRequest {
  // Required. Project, location. E.g.,
  // `projects/myproject/locations/US`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "bigqueryreservation.googleapis.com/Reservation"
    }
  ];

  // The reservation ID. It must only contain lower case alphanumeric
  // characters or dashes. It must start with a letter and must not end
  // with a dash. Its maximum length is 64 characters.
  string reservation_id = 2;

  // Definition of the new reservation to create.
  Reservation reservation = 3;
}

// The request for
// [ReservationService.ListReservations][google.cloud.bigquery.reservation.v1.ReservationService.ListReservations].
message ListReservationsRequest {
  // Required. The parent resource name containing project and location, e.g.:
  //   `projects/myproject/locations/US`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "bigqueryreservation.googleapis.com/Reservation"
    }
  ];

  // The maximum number of items to return per page.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// The response for
// [ReservationService.ListReservations][google.cloud.bigquery.reservation.v1.ReservationService.ListReservations].
message ListReservationsResponse {
  // List of reservations visible to the user.
  repeated Reservation reservations = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request for
// [ReservationService.GetReservation][google.cloud.bigquery.reservation.v1.ReservationService.GetReservation].
message GetReservationRequest {
  // Required. Resource name of the reservation to retrieve. E.g.,
  //    `projects/myproject/locations/US/reservations/team1-prod`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/Reservation"
    }
  ];
}

// The request for
// [ReservationService.DeleteReservation][google.cloud.bigquery.reservation.v1.ReservationService.DeleteReservation].
message DeleteReservationRequest {
  // Required. Resource name of the reservation to retrieve. E.g.,
  //    `projects/myproject/locations/US/reservations/team1-prod`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/Reservation"
    }
  ];
}

// The request for
// [ReservationService.UpdateReservation][google.cloud.bigquery.reservation.v1.ReservationService.UpdateReservation].
message UpdateReservationRequest {
  // Content of the reservation to update.
  Reservation reservation = 1;

  // Standard field mask for the set of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request for ReservationService.FailoverReservation.
message FailoverReservationRequest {
  // Required. Resource name of the reservation to failover. E.g.,
  //    `projects/myproject/locations/US/reservations/team1-prod`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/Reservation"
    }
  ];
}

// The request for
// [ReservationService.CreateCapacityCommitment][google.cloud.bigquery.reservation.v1.ReservationService.CreateCapacityCommitment].
message CreateCapacityCommitmentRequest {
  // Required. Resource name of the parent reservation. E.g.,
  //    `projects/myproject/locations/US`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "bigqueryreservation.googleapis.com/CapacityCommitment"
    }
  ];

  // Content of the capacity commitment to create.
  CapacityCommitment capacity_commitment = 2;

  // If true, fail the request if another project in the organization has a
  // capacity commitment.
  bool enforce_single_admin_project_per_org = 4;

  // The optional capacity commitment ID. Capacity commitment name will be
  // generated automatically if this field is empty.
  // This field must only contain lower case alphanumeric characters or dashes.
  // The first and last character cannot be a dash. Max length is 64 characters.
  // NOTE: this ID won't be kept if the capacity commitment is split or merged.
  string capacity_commitment_id = 5;
}

// The request for
// [ReservationService.ListCapacityCommitments][google.cloud.bigquery.reservation.v1.ReservationService.ListCapacityCommitments].
message ListCapacityCommitmentsRequest {
  // Required. Resource name of the parent reservation. E.g.,
  //    `projects/myproject/locations/US`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "bigqueryreservation.googleapis.com/CapacityCommitment"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// The response for
// [ReservationService.ListCapacityCommitments][google.cloud.bigquery.reservation.v1.ReservationService.ListCapacityCommitments].
message ListCapacityCommitmentsResponse {
  // List of capacity commitments visible to the user.
  repeated CapacityCommitment capacity_commitments = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request for
// [ReservationService.GetCapacityCommitment][google.cloud.bigquery.reservation.v1.ReservationService.GetCapacityCommitment].
message GetCapacityCommitmentRequest {
  // Required. Resource name of the capacity commitment to retrieve. E.g.,
  //    `projects/myproject/locations/US/capacityCommitments/123`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/CapacityCommitment"
    }
  ];
}

// The request for
// [ReservationService.DeleteCapacityCommitment][google.cloud.bigquery.reservation.v1.ReservationService.DeleteCapacityCommitment].
message DeleteCapacityCommitmentRequest {
  // Required. Resource name of the capacity commitment to delete. E.g.,
  //    `projects/myproject/locations/US/capacityCommitments/123`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/CapacityCommitment"
    }
  ];

  // Can be used to force delete commitments even if assignments exist. Deleting
  // commitments with assignments may cause queries to fail if they no longer
  // have access to slots.
  bool force = 3;
}

// The request for
// [ReservationService.UpdateCapacityCommitment][google.cloud.bigquery.reservation.v1.ReservationService.UpdateCapacityCommitment].
message UpdateCapacityCommitmentRequest {
  // Content of the capacity commitment to update.
  CapacityCommitment capacity_commitment = 1;

  // Standard field mask for the set of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request for
// [ReservationService.SplitCapacityCommitment][google.cloud.bigquery.reservation.v1.ReservationService.SplitCapacityCommitment].
message SplitCapacityCommitmentRequest {
  // Required. The resource name e.g.,:
  //  `projects/myproject/locations/US/capacityCommitments/123`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/CapacityCommitment"
    }
  ];

  // Number of slots in the capacity commitment after the split.
  int64 slot_count = 2;
}

// The response for
// [ReservationService.SplitCapacityCommitment][google.cloud.bigquery.reservation.v1.ReservationService.SplitCapacityCommitment].
message SplitCapacityCommitmentResponse {
  // First capacity commitment, result of a split.
  CapacityCommitment first = 1;

  // Second capacity commitment, result of a split.
  CapacityCommitment second = 2;
}

// The request for
// [ReservationService.MergeCapacityCommitments][google.cloud.bigquery.reservation.v1.ReservationService.MergeCapacityCommitments].
message MergeCapacityCommitmentsRequest {
  // Parent resource that identifies admin project and location e.g.,
  //  `projects/myproject/locations/us`
  string parent = 1 [(google.api.resource_reference) = {
    child_type: "bigqueryreservation.googleapis.com/CapacityCommitment"
  }];

  // Ids of capacity commitments to merge.
  // These capacity commitments must exist under admin project and location
  // specified in the parent.
  // ID is the last portion of capacity commitment name e.g., 'abc' for
  // projects/myproject/locations/US/capacityCommitments/abc
  repeated string capacity_commitment_ids = 2;
}

// An assignment allows a project to submit jobs
// of a certain type using slots from the specified reservation.
message Assignment {
  option (google.api.resource) = {
    type: "bigqueryreservation.googleapis.com/Assignment"
    pattern: "projects/{project}/locations/{location}/reservations/{reservation}/assignments/{assignment}"
    plural: "assignments"
    singular: "assignment"
  };

  // Types of job, which could be specified when using the reservation.
  enum JobType {
    // Invalid type. Requests with this value will be rejected with
    // error code `google.rpc.Code.INVALID_ARGUMENT`.
    JOB_TYPE_UNSPECIFIED = 0;

    // Pipeline (load/export) jobs from the project will use the reservation.
    PIPELINE = 1;

    // Query jobs from the project will use the reservation.
    QUERY = 2;

    // BigQuery ML jobs that use services external to BigQuery for model
    // training. These jobs will not utilize idle slots from other reservations.
    ML_EXTERNAL = 3;

    // Background jobs that BigQuery runs for the customers in the background.
    BACKGROUND = 4;
  }

  // Assignment will remain in PENDING state if no active capacity commitment is
  // present. It will become ACTIVE when some capacity commitment becomes
  // active.
  enum State {
    // Invalid state value.
    STATE_UNSPECIFIED = 0;

    // Queries from assignee will be executed as on-demand, if related
    // assignment is pending.
    PENDING = 1;

    // Assignment is ready.
    ACTIVE = 2;
  }

  // Output only. Name of the resource. E.g.:
  // `projects/myproject/locations/US/reservations/team1-prod/assignments/123`.
  // The assignment_id must only contain lower case alphanumeric characters or
  // dashes and the max length is 64 characters.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The resource which will use the reservation. E.g.
  // `projects/myproject`, `folders/123`, or `organizations/456`.
  string assignee = 4;

  // Which type of jobs will use the reservation.
  JobType job_type = 3;

  // Output only. State of the assignment.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request for
// [ReservationService.CreateAssignment][google.cloud.bigquery.reservation.v1.ReservationService.CreateAssignment].
// Note: "bigquery.reservationAssignments.create" permission is required on the
// related assignee.
message CreateAssignmentRequest {
  // Required. The parent resource name of the assignment
  // E.g. `projects/myproject/locations/US/reservations/team1-prod`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "bigqueryreservation.googleapis.com/Assignment"
    }
  ];

  // Assignment resource to create.
  Assignment assignment = 2;

  // The optional assignment ID. Assignment name will be generated automatically
  // if this field is empty.
  // This field must only contain lower case alphanumeric characters or dashes.
  // Max length is 64 characters.
  string assignment_id = 4;
}

// The request for
// [ReservationService.ListAssignments][google.cloud.bigquery.reservation.v1.ReservationService.ListAssignments].
message ListAssignmentsRequest {
  // Required. The parent resource name e.g.:
  //
  // `projects/myproject/locations/US/reservations/team1-prod`
  //
  // Or:
  //
  // `projects/myproject/locations/US/reservations/-`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "bigqueryreservation.googleapis.com/Assignment"
    }
  ];

  // The maximum number of items to return per page.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// The response for
// [ReservationService.ListAssignments][google.cloud.bigquery.reservation.v1.ReservationService.ListAssignments].
message ListAssignmentsResponse {
  // List of assignments visible to the user.
  repeated Assignment assignments = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request for
// [ReservationService.DeleteAssignment][google.cloud.bigquery.reservation.v1.ReservationService.DeleteAssignment].
// Note: "bigquery.reservationAssignments.delete" permission is required on the
// related assignee.
message DeleteAssignmentRequest {
  // Required. Name of the resource, e.g.
  //   `projects/myproject/locations/US/reservations/team1-prod/assignments/123`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/Assignment"
    }
  ];
}

// The request for
// [ReservationService.SearchAssignments][google.cloud.bigquery.reservation.v1.ReservationService.SearchAssignments].
// Note: "bigquery.reservationAssignments.search" permission is required on the
// related assignee.
message SearchAssignmentsRequest {
  // Required. The resource name of the admin project(containing project and
  // location), e.g.:
  //   `projects/myproject/locations/US`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Please specify resource name as assignee in the query.
  //
  // Examples:
  //
  // * `assignee=projects/myproject`
  // * `assignee=folders/123`
  // * `assignee=organizations/456`
  string query = 2;

  // The maximum number of items to return per page.
  int32 page_size = 3;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 4;
}

// The request for
// [ReservationService.SearchAllAssignments][google.cloud.bigquery.reservation.v1.ReservationService.SearchAllAssignments].
// Note: "bigquery.reservationAssignments.search" permission is required on the
// related assignee.
message SearchAllAssignmentsRequest {
  // Required. The resource name with location (project name could be the
  // wildcard '-'), e.g.:
  //   `projects/-/locations/US`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Please specify resource name as assignee in the query.
  //
  // Examples:
  //
  // * `assignee=projects/myproject`
  // * `assignee=folders/123`
  // * `assignee=organizations/456`
  string query = 2;

  // The maximum number of items to return per page.
  int32 page_size = 3;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 4;
}

// The response for
// [ReservationService.SearchAssignments][google.cloud.bigquery.reservation.v1.ReservationService.SearchAssignments].
message SearchAssignmentsResponse {
  // List of assignments visible to the user.
  repeated Assignment assignments = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The response for
// [ReservationService.SearchAllAssignments][google.cloud.bigquery.reservation.v1.ReservationService.SearchAllAssignments].
message SearchAllAssignmentsResponse {
  // List of assignments visible to the user.
  repeated Assignment assignments = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request for
// [ReservationService.MoveAssignment][google.cloud.bigquery.reservation.v1.ReservationService.MoveAssignment].
//
// **Note**: "bigquery.reservationAssignments.create" permission is required on
// the destination_id.
//
// **Note**: "bigquery.reservationAssignments.create" and
// "bigquery.reservationAssignments.delete" permission are required on the
// related assignee.
message MoveAssignmentRequest {
  // Required. The resource name of the assignment,
  // e.g.
  // `projects/myproject/locations/US/reservations/team1-prod/assignments/123`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/Assignment"
    }
  ];

  // The new reservation ID, e.g.:
  //   `projects/myotherproject/locations/US/reservations/team2-prod`
  string destination_id = 3 [(google.api.resource_reference) = {
    child_type: "bigqueryreservation.googleapis.com/Assignment"
  }];

  // The optional assignment ID. A new assignment name is generated if this
  // field is empty.
  //
  // This field can contain only lowercase alphanumeric characters or dashes.
  // Max length is 64 characters.
  string assignment_id = 5;
}

// The request for
// [ReservationService.UpdateAssignment][google.cloud.bigquery.reservation.v1.ReservationService.UpdateAssignment].
message UpdateAssignmentRequest {
  // Content of the assignment to update.
  Assignment assignment = 1;

  // Standard field mask for the set of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Fully qualified reference to BigQuery table.
// Internally stored as google.cloud.bi.v1.BqTableReference.
message TableReference {
  // The assigned project ID of the project.
  string project_id = 1;

  // The ID of the dataset in the above project.
  string dataset_id = 2;

  // The ID of the table in the above dataset.
  string table_id = 3;
}

// Represents a BI Reservation.
message BiReservation {
  option (google.api.resource) = {
    type: "bigqueryreservation.googleapis.com/BiReservation"
    pattern: "projects/{project}/locations/{location}/biReservation"
  };

  // The resource name of the singleton BI reservation.
  // Reservation names have the form
  // `projects/{project_id}/locations/{location_id}/biReservation`.
  string name = 1;

  // Output only. The last update timestamp of a reservation.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Size of a reservation, in bytes.
  int64 size = 4;

  // Preferred tables to use BI capacity for.
  repeated TableReference preferred_tables = 5;
}

// A request to get a singleton BI reservation.
message GetBiReservationRequest {
  // Required. Name of the requested reservation, for example:
  // `projects/{project_id}/locations/{location_id}/biReservation`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigqueryreservation.googleapis.com/BiReservation"
    }
  ];
}

// A request to update a BI reservation.
message UpdateBiReservationRequest {
  // A reservation to update.
  BiReservation bi_reservation = 1;

  // A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2;
}

// The type of editions.
// Different features and behaviors are provided to different editions
// Capacity commitments and reservations are linked to editions.
enum Edition {
  // Default value, which will be treated as ENTERPRISE.
  EDITION_UNSPECIFIED = 0;

  // Standard edition.
  STANDARD = 1;

  // Enterprise edition.
  ENTERPRISE = 2;

  // Enterprise Plus edition.
  ENTERPRISE_PLUS = 3;
}
