{"methodConfig": [{"name": [{"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "CreateReservation"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "UpdateReservation"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "CreateCapacityCommitment"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "UpdateCapacityCommitment"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "SplitCapacityCommitment"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "MergeCapacityCommitments"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "CreateAssignment"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "MoveAssignment"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "UpdateBiReservation"}], "timeout": "300s"}, {"name": [{"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "ListReservations"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "GetReservation"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "DeleteReservation"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "ListCapacityCommitments"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "GetCapacityCommitment"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "DeleteCapacityCommitment"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "ListAssignments"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "DeleteAssignment"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "SearchAssignments"}, {"service": "google.cloud.bigquery.reservation.v1.ReservationService", "method": "GetBiReservation"}], "timeout": "300s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}