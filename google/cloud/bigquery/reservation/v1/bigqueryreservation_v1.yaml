type: google.api.Service
config_version: 3
name: bigqueryreservation.googleapis.com
title: BigQuery Reservation API

apis:
- name: google.cloud.bigquery.reservation.v1.ReservationService

documentation:
  summary: 'A service to modify your BigQuery flat-rate reservations.'

authentication:
  rules:
  - selector: 'google.cloud.bigquery.reservation.v1.ReservationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
