// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.dataexchange.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.BigQuery.DataExchange.V1Beta1";
option go_package = "cloud.google.com/go/bigquery/dataexchange/apiv1beta1/dataexchangepb;dataexchangepb";
option java_multiple_files = true;
option java_outer_classname = "DataExchangeProto";
option java_package = "com.google.cloud.bigquery.dataexchange.v1beta1";
option php_namespace = "Google\\Cloud\\BigQuery\\DataExchange\\V1beta1";
option ruby_package = "Google::Cloud::Bigquery::DataExchange::V1beta1";
option (google.api.resource_definition) = {
  type: "bigquery.googleapis.com/Dataset"
  pattern: "projects/{project}/datasets/{dataset}"
};

// The `AnalyticsHubService` API facilitates data sharing within and across
// organizations. It allows data providers to publish listings that reference
// shared datasets. With Analytics Hub, users can discover and search for
// listings that they have access to. Subscribers can view and subscribe to
// listings. When you subscribe to a listing, Analytics Hub creates a linked
// dataset in your project.
service AnalyticsHubService {
  option (google.api.default_host) = "analyticshub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigquery,"
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all data exchanges in a given project and location.
  rpc ListDataExchanges(ListDataExchangesRequest) returns (ListDataExchangesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/dataExchanges"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all data exchanges from projects in a given organization and
  // location.
  rpc ListOrgDataExchanges(ListOrgDataExchangesRequest) returns (ListOrgDataExchangesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{organization=organizations/*/locations/*}/dataExchanges"
    };
    option (google.api.method_signature) = "organization";
  }

  // Gets the details of a data exchange.
  rpc GetDataExchange(GetDataExchangeRequest) returns (DataExchange) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/dataExchanges/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new data exchange.
  rpc CreateDataExchange(CreateDataExchangeRequest) returns (DataExchange) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/dataExchanges"
      body: "data_exchange"
    };
    option (google.api.method_signature) = "parent,data_exchange";
  }

  // Updates an existing data exchange.
  rpc UpdateDataExchange(UpdateDataExchangeRequest) returns (DataExchange) {
    option (google.api.http) = {
      patch: "/v1beta1/{data_exchange.name=projects/*/locations/*/dataExchanges/*}"
      body: "data_exchange"
    };
    option (google.api.method_signature) = "data_exchange,update_mask";
  }

  // Deletes an existing data exchange.
  rpc DeleteDataExchange(DeleteDataExchangeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/dataExchanges/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all listings in a given project and location.
  rpc ListListings(ListListingsRequest) returns (ListListingsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/dataExchanges/*}/listings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a listing.
  rpc GetListing(GetListingRequest) returns (Listing) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/dataExchanges/*/listings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new listing.
  rpc CreateListing(CreateListingRequest) returns (Listing) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/dataExchanges/*}/listings"
      body: "listing"
    };
    option (google.api.method_signature) = "parent,listing";
  }

  // Updates an existing listing.
  rpc UpdateListing(UpdateListingRequest) returns (Listing) {
    option (google.api.http) = {
      patch: "/v1beta1/{listing.name=projects/*/locations/*/dataExchanges/*/listings/*}"
      body: "listing"
    };
    option (google.api.method_signature) = "listing,update_mask";
  }

  // Deletes a listing.
  rpc DeleteListing(DeleteListingRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/dataExchanges/*/listings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Subscribes to a listing.
  //
  // Currently, with Analytics Hub, you can create listings that
  // reference only BigQuery datasets.
  // Upon subscription to a listing for a BigQuery dataset, Analytics Hub
  // creates a linked dataset in the subscriber's project.
  rpc SubscribeListing(SubscribeListingRequest) returns (SubscribeListingResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/dataExchanges/*/listings/*}:subscribe"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the IAM policy.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/dataExchanges/*}:getIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/dataExchanges/*/listings/*}:getIamPolicy"
        body: "*"
      }
    };
  }

  // Sets the IAM policy.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/dataExchanges/*}:setIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/dataExchanges/*/listings/*}:setIamPolicy"
        body: "*"
      }
    };
  }

  // Returns the permissions that a caller has.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest) returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/dataExchanges/*}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/dataExchanges/*/listings/*}:testIamPermissions"
        body: "*"
      }
    };
  }
}

// A data exchange is a container that lets you share data. Along with the
// descriptive information about the data exchange, it contains listings that
// reference shared datasets.
message DataExchange {
  option (google.api.resource) = {
    type: "analyticshub.googleapis.com/DataExchange"
    pattern: "projects/{project}/locations/{location}/dataExchanges/{data_exchange}"
  };

  // Output only. The resource name of the data exchange.
  // e.g. `projects/myproject/locations/US/dataExchanges/123`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Human-readable display name of the data exchange. The display name must
  // contain only Unicode letters, numbers (0-9), underscores (_), dashes (-),
  // spaces ( ), ampersands (&) and must not start or end with spaces.
  // Default value is an empty string.
  // Max length: 63 bytes.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description of the data exchange. The description must not contain Unicode
  // non-characters as well as C0 and C1 control codes except tabs (HT),
  // new lines (LF), carriage returns (CR), and page breaks (FF).
  // Default value is an empty string.
  // Max length: 2000 bytes.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Email or URL of the primary point of contact of the data exchange.
  // Max Length: 1000 bytes.
  string primary_contact = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Documentation describing the data exchange.
  string documentation = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Number of listings contained in the data exchange.
  int32 listing_count = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Base64 encoded image representing the data exchange. Max Size: 3.0MiB
  // Expected image dimensions are 512x512 pixels, however the API only
  // performs validation on size of the encoded data.
  // Note: For byte fields, the content of the fields are base64-encoded (which
  // increases the size of the data by 33-36%) when using JSON on the wire.
  bytes icon = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Contains details of the data provider.
message DataProvider {
  // Optional. Name of the data provider.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Email or URL of the data provider.
  // Max Length: 1000 bytes.
  string primary_contact = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Contains details of the listing publisher.
message Publisher {
  // Optional. Name of the listing publisher.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Email or URL of the listing publisher.
  // Max Length: 1000 bytes.
  string primary_contact = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Contains the reference that identifies a destination bigquery dataset.
message DestinationDatasetReference {
  // Required. A unique ID for this dataset, without the project name. The ID
  // must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_).
  // The maximum length is 1,024 characters.
  string dataset_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the project containing this dataset.
  string project_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Defines the destination bigquery dataset.
message DestinationDataset {
  // Required. A reference that identifies the destination dataset.
  DestinationDatasetReference dataset_reference = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A descriptive name for the dataset.
  google.protobuf.StringValue friendly_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A user-friendly description of the dataset.
  google.protobuf.StringValue description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels associated with this dataset. You can use these
  // to organize and group your datasets.
  // You can set this property when inserting or updating a dataset.
  // See https://cloud.google.com/resource-manager/docs/creating-managing-labels
  // for more information.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The geographic location where the dataset should reside. See
  // https://cloud.google.com/bigquery/docs/locations for supported
  // locations.
  string location = 5 [(google.api.field_behavior) = REQUIRED];
}

// A listing is what gets published into a data exchange that a subscriber can
// subscribe to. It contains a reference to the data source along with
// descriptive information that will help subscribers find and subscribe the
// data.
message Listing {
  option (google.api.resource) = {
    type: "analyticshub.googleapis.com/Listing"
    pattern: "projects/{project}/locations/{location}/dataExchanges/{data_exchange}/listings/{listing}"
  };

  // A reference to a shared dataset. It is an existing BigQuery dataset with a
  // collection of objects such as tables and views that you want to share
  // with subscribers.
  // When subscriber's subscribe to a listing, Analytics Hub creates a linked
  // dataset in
  // the subscriber's project. A Linked dataset is an opaque, read-only BigQuery
  // dataset that serves as a _symbolic link_ to a shared dataset.
  message BigQueryDatasetSource {
    // Resource name of the dataset source for this listing.
    // e.g. `projects/myproject/datasets/123`
    string dataset = 1 [(google.api.resource_reference) = {
                          type: "bigquery.googleapis.com/Dataset"
                        }];
  }

  // State of the listing.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // Subscribable state. Users with dataexchange.listings.subscribe permission
    // can subscribe to this listing.
    ACTIVE = 1;
  }

  // Listing categories.
  enum Category {
    CATEGORY_UNSPECIFIED = 0;

    CATEGORY_OTHERS = 1;

    CATEGORY_ADVERTISING_AND_MARKETING = 2;

    CATEGORY_COMMERCE = 3;

    CATEGORY_CLIMATE_AND_ENVIRONMENT = 4;

    CATEGORY_DEMOGRAPHICS = 5;

    CATEGORY_ECONOMICS = 6;

    CATEGORY_EDUCATION = 7;

    CATEGORY_ENERGY = 8;

    CATEGORY_FINANCIAL = 9;

    CATEGORY_GAMING = 10;

    CATEGORY_GEOSPATIAL = 11;

    CATEGORY_HEALTHCARE_AND_LIFE_SCIENCE = 12;

    CATEGORY_MEDIA = 13;

    CATEGORY_PUBLIC_SECTOR = 14;

    CATEGORY_RETAIL = 15;

    CATEGORY_SPORTS = 16;

    CATEGORY_SCIENCE_AND_RESEARCH = 17;

    CATEGORY_TRANSPORTATION_AND_LOGISTICS = 18;

    CATEGORY_TRAVEL_AND_TOURISM = 19;
  }

  // Listing source.
  oneof source {
    // Required. Shared dataset i.e. BigQuery dataset source.
    BigQueryDatasetSource bigquery_dataset = 6 [(google.api.field_behavior) = REQUIRED];
  }

  // Output only. The resource name of the listing.
  // e.g. `projects/myproject/locations/US/dataExchanges/123/listings/456`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Human-readable display name of the listing. The display name must contain
  // only Unicode letters, numbers (0-9), underscores (_), dashes (-), spaces
  // ( ), ampersands (&) and can't start or end with spaces.
  // Default value is an empty string.
  // Max length: 63 bytes.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Short description of the listing. The description must not contain
  // Unicode non-characters and C0 and C1 control codes except tabs (HT),
  // new lines (LF), carriage returns (CR), and page breaks (FF).
  // Default value is an empty string.
  // Max length: 2000 bytes.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Email or URL of the primary point of contact of the listing.
  // Max Length: 1000 bytes.
  string primary_contact = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Documentation describing the listing.
  string documentation = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Current state of the listing.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Base64 encoded image representing the listing. Max Size: 3.0MiB
  // Expected image dimensions are 512x512 pixels, however the API only
  // performs validation on size of the encoded data.
  // Note: For byte fields, the contents of the field are base64-encoded (which
  // increases the size of the data by 33-36%) when using JSON on the wire.
  bytes icon = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Details of the data provider who owns the source data.
  DataProvider data_provider = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Categories of the listing. Up to two categories are allowed.
  repeated Category categories = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Details of the publisher who owns the listing and who can share
  // the source data.
  Publisher publisher = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Email or URL of the request access of the listing.
  // Subscribers can use this reference to request access.
  // Max Length: 1000 bytes.
  string request_access = 12 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting the list of data exchanges.
message ListDataExchangesRequest {
  // Required. The parent resource path of the data exchanges.
  // e.g. `projects/myproject/locations/US`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of results to return in a single response page. Leverage
  // the page tokens to iterate through the entire collection.
  int32 page_size = 2;

  // Page token, returned by a previous call, to request the next page of
  // results.
  string page_token = 3;
}

// Message for response to the list of data exchanges.
message ListDataExchangesResponse {
  // The list of data exchanges.
  repeated DataExchange data_exchanges = 1;

  // A token to request the next page of results.
  string next_page_token = 2;
}

// Message for requesting the list of data exchanges from projects in an
// organization and location.
message ListOrgDataExchangesRequest {
  // Required. The organization resource path of the projects containing DataExchanges.
  // e.g. `organizations/myorg/locations/US`.
  string organization = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of results to return in a single response page. Leverage
  // the page tokens to iterate through the entire collection.
  int32 page_size = 2;

  // Page token, returned by a previous call, to request the next page of
  // results.
  string page_token = 3;
}

// Message for response to listing data exchanges in an organization and
// location.
message ListOrgDataExchangesResponse {
  // The list of data exchanges.
  repeated DataExchange data_exchanges = 1;

  // A token to request the next page of results.
  string next_page_token = 2;
}

// Message for getting a data exchange.
message GetDataExchangeRequest {
  // Required. The resource name of the data exchange.
  // e.g. `projects/myproject/locations/US/dataExchanges/123`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticshub.googleapis.com/DataExchange"
    }
  ];
}

// Message for creating a data exchange.
message CreateDataExchangeRequest {
  // Required. The parent resource path of the data exchange.
  // e.g. `projects/myproject/locations/US`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The ID of the data exchange.
  // Must contain only Unicode letters, numbers (0-9), underscores (_).
  // Should not use characters that require URL-escaping, or characters
  // outside of ASCII, spaces.
  // Max length: 100 bytes.
  string data_exchange_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The data exchange to create.
  DataExchange data_exchange = 3 [(google.api.field_behavior) = REQUIRED];
}

// Message for updating a data exchange.
message UpdateDataExchangeRequest {
  // Required. Field mask specifies the fields to update in the data exchange
  // resource. The fields specified in the
  // `updateMask` are relative to the resource and are not a full request.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The data exchange to update.
  DataExchange data_exchange = 2 [(google.api.field_behavior) = REQUIRED];
}

// Message for deleting a data exchange.
message DeleteDataExchangeRequest {
  // Required. The full name of the data exchange resource that you want to delete.
  // For example, `projects/myproject/locations/US/dataExchanges/123`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticshub.googleapis.com/DataExchange"
    }
  ];
}

// Message for requesting the list of listings.
message ListListingsRequest {
  // Required. The parent resource path of the listing.
  // e.g. `projects/myproject/locations/US/dataExchanges/123`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticshub.googleapis.com/DataExchange"
    }
  ];

  // The maximum number of results to return in a single response page. Leverage
  // the page tokens to iterate through the entire collection.
  int32 page_size = 2;

  // Page token, returned by a previous call, to request the next page of
  // results.
  string page_token = 3;
}

// Message for response to the list of Listings.
message ListListingsResponse {
  // The list of Listing.
  repeated Listing listings = 1;

  // A token to request the next page of results.
  string next_page_token = 2;
}

// Message for getting a listing.
message GetListingRequest {
  // Required. The resource name of the listing.
  // e.g. `projects/myproject/locations/US/dataExchanges/123/listings/456`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticshub.googleapis.com/Listing"
    }
  ];
}

// Message for creating a listing.
message CreateListingRequest {
  // Required. The parent resource path of the listing.
  // e.g. `projects/myproject/locations/US/dataExchanges/123`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticshub.googleapis.com/DataExchange"
    }
  ];

  // Required. The ID of the listing to create.
  // Must contain only Unicode letters, numbers (0-9), underscores (_).
  // Should not use characters that require URL-escaping, or characters
  // outside of ASCII, spaces.
  // Max length: 100 bytes.
  string listing_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The listing to create.
  Listing listing = 3 [(google.api.field_behavior) = REQUIRED];
}

// Message for updating a Listing.
message UpdateListingRequest {
  // Required. Field mask specifies the fields to update in the listing resource. The
  // fields specified in the `updateMask` are relative to the resource and are
  // not a full request.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The listing to update.
  Listing listing = 2 [(google.api.field_behavior) = REQUIRED];
}

// Message for deleting a listing.
message DeleteListingRequest {
  // Required. Resource name of the listing to delete.
  // e.g. `projects/myproject/locations/US/dataExchanges/123/listings/456`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticshub.googleapis.com/Listing"
    }
  ];
}

// Message for subscribing to a listing.
message SubscribeListingRequest {
  // Resulting destination of the listing that you subscribed to.
  oneof destination {
    // BigQuery destination dataset to create for the subscriber.
    DestinationDataset destination_dataset = 3;
  }

  // Required. Resource name of the listing that you want to subscribe to.
  // e.g. `projects/myproject/locations/US/dataExchanges/123/listings/456`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticshub.googleapis.com/Listing"
    }
  ];
}

// Message for response when you subscribe to a listing.
message SubscribeListingResponse {

}
