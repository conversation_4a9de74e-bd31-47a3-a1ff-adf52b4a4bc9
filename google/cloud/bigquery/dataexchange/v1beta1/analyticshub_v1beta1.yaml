type: google.api.Service
config_version: 3
name: analyticshub.googleapis.com
title: Analytics Hub API

apis:
- name: google.cloud.bigquery.dataexchange.v1beta1.AnalyticsHubService
- name: google.cloud.location.Locations

documentation:
  summary: Exchange data and analytics assets securely and efficiently.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: 'google.cloud.bigquery.dataexchange.v1beta1.AnalyticsHubService.*'
    deadline: 60.0
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1beta1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1beta1/{name=projects/*}/locations'

authentication:
  rules:
  - selector: 'google.cloud.bigquery.dataexchange.v1beta1.AnalyticsHubService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/bigquery,
        https://www.googleapis.com/auth/cloud-platform
