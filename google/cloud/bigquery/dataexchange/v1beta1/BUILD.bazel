# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "dataexchange_proto",
    srcs = [
        "dataexchange.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "dataexchange_proto_with_info",
    deps = [
        ":dataexchange_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "dataexchange_java_proto",
    deps = [":dataexchange_proto"],
)

java_grpc_library(
    name = "dataexchange_java_grpc",
    srcs = [":dataexchange_proto"],
    deps = [":dataexchange_java_proto"],
)

java_gapic_library(
    name = "dataexchange_java_gapic",
    srcs = [":dataexchange_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "analyticshub_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1beta1.yaml",
    test_deps = [
        ":dataexchange_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":dataexchange_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "dataexchange_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.dataexchange.v1beta1.AnalyticsHubServiceClientHttpJsonTest",
        "com.google.cloud.bigquery.dataexchange.v1beta1.AnalyticsHubServiceClientTest",
    ],
    runtime_deps = [":dataexchange_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-dataexchange-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":dataexchange_java_gapic",
        ":dataexchange_java_grpc",
        ":dataexchange_java_proto",
        ":dataexchange_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "dataexchange_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/dataexchange/apiv1beta1/dataexchangepb",
    protos = [":dataexchange_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

go_gapic_library(
    name = "dataexchange_go_gapic",
    srcs = [":dataexchange_proto_with_info"],
    grpc_service_config = "analyticshub_v1beta1_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/dataexchange/apiv1beta1;dataexchange",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataexchange_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-dataexchange-v1beta1-go",
    deps = [
        ":dataexchange_go_gapic",
        ":dataexchange_go_gapic_srcjar-metadata.srcjar",
        ":dataexchange_go_gapic_srcjar-snippets.srcjar",
        ":dataexchange_go_gapic_srcjar-test.srcjar",
        ":dataexchange_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "dataexchange_py_gapic",
    srcs = [":dataexchange_proto"],
    grpc_service_config = "analyticshub_v1beta1_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=bigquery_data_exchange",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-bigquery-data-exchange",
    ],
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1beta1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "dataexchange_py_gapic_test",
    srcs = [
        "dataexchange_py_gapic_pytest.py",
        "dataexchange_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":dataexchange_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-dataexchange-v1beta1-py",
    deps = [
        ":dataexchange_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "dataexchange_php_proto",
    deps = [":dataexchange_proto"],
)

php_gapic_library(
    name = "dataexchange_php_gapic",
    srcs = [":dataexchange_proto_with_info"],
    grpc_service_config = "analyticshub_v1beta1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":dataexchange_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-dataexchange-v1beta1-php",
    deps = [
        ":dataexchange_php_gapic",
        ":dataexchange_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "dataexchange_nodejs_gapic",
    package_name = "@google-cloud/bigquery-data-exchange",
    src = ":dataexchange_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "analyticshub_v1beta1_grpc_service_config.json",
    package = "google.cloud.bigquery.dataexchange.v1beta1",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-dataexchange-v1beta1-nodejs",
    deps = [
        ":dataexchange_nodejs_gapic",
        ":dataexchange_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "dataexchange_ruby_proto",
    deps = [":dataexchange_proto"],
)

ruby_grpc_library(
    name = "dataexchange_ruby_grpc",
    srcs = [":dataexchange_proto"],
    deps = [":dataexchange_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "dataexchange_ruby_gapic",
    srcs = [":dataexchange_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=analyticshub.googleapis.com",
        "ruby-cloud-api-shortname=analyticshub",
        "ruby-cloud-gem-name=google-cloud-bigquery-data_exchange-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/analytics-hub/",
    ],
    grpc_service_config = "analyticshub_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "Analytics Hub is a data exchange that allows you to efficiently and securely exchange data assets across organizations to address challenges of data reliability and cost. Curate a library of internal and external assets, including unique datasets like Google Trends, backed by the power of BigQuery.",
    ruby_cloud_title = "Analytics Hub V1beta1",
    service_yaml = "analyticshub_v1beta1.yaml",
    deps = [
        ":dataexchange_ruby_grpc",
        ":dataexchange_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-dataexchange-v1beta1-ruby",
    deps = [
        ":dataexchange_ruby_gapic",
        ":dataexchange_ruby_grpc",
        ":dataexchange_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "dataexchange_csharp_proto",
    deps = [":dataexchange_proto"],
)

csharp_grpc_library(
    name = "dataexchange_csharp_grpc",
    srcs = [":dataexchange_proto"],
    deps = [":dataexchange_csharp_proto"],
)

csharp_gapic_library(
    name = "dataexchange_csharp_gapic",
    srcs = [":dataexchange_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "analyticshub_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1beta1.yaml",
    deps = [
        ":dataexchange_csharp_grpc",
        ":dataexchange_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-dataexchange-v1beta1-csharp",
    deps = [
        ":dataexchange_csharp_gapic",
        ":dataexchange_csharp_grpc",
        ":dataexchange_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "dataexchange_cc_proto",
    deps = [":dataexchange_proto"],
)

cc_grpc_library(
    name = "dataexchange_cc_grpc",
    srcs = [":dataexchange_proto"],
    grpc_only = True,
    deps = [":dataexchange_cc_proto"],
)
