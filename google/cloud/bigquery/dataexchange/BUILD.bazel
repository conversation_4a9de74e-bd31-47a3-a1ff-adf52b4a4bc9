# This build file includes a target for the Ruby wrapper library for
# google-cloud-bigquery-data_exchange.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for analyticshub.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "dataexchange_ruby_wrapper",
    srcs = ["//google/cloud/bigquery/dataexchange/v1beta1:dataexchange_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=analyticshub.googleapis.com",
        "ruby-cloud-api-shortname=analyticshub",
        "ruby-cloud-gem-name=google-cloud-bigquery-data_exchange",
        "ruby-cloud-product-url=https://cloud.google.com/analytics-hub/",
        "ruby-cloud-wrapper-of=v1beta1:0.6",
    ],
    ruby_cloud_description = "Analytics Hub is a data exchange that allows you to efficiently and securely exchange data assets across organizations to address challenges of data reliability and cost. Curate a library of internal and external assets, including unique datasets like Google Trends, backed by the power of BigQuery.",
    ruby_cloud_title = "Analytics Hub",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataexchange-ruby",
    deps = [
        ":dataexchange_ruby_wrapper",
    ],
)
