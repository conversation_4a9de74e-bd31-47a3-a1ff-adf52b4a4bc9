# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "datatransfer_proto",
    srcs = [
        "datatransfer.proto",
        "transfer.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "datatransfer_proto_with_info",
    deps = [
        ":datatransfer_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "datatransfer_java_proto",
    deps = [":datatransfer_proto"],
)

java_grpc_library(
    name = "datatransfer_java_grpc",
    srcs = [":datatransfer_proto"],
    deps = [":datatransfer_java_proto"],
)

java_gapic_library(
    name = "datatransfer_java_gapic",
    srcs = [":datatransfer_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "bigquerydatatransfer_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigquerydatatransfer_v1.yaml",
    test_deps = [
        ":datatransfer_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":datatransfer_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "datatransfer_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.datatransfer.v1.DataTransferServiceClientHttpJsonTest",
        "com.google.cloud.bigquery.datatransfer.v1.DataTransferServiceClientTest",
    ],
    runtime_deps = [":datatransfer_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-datatransfer-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":datatransfer_java_gapic",
        ":datatransfer_java_grpc",
        ":datatransfer_java_proto",
        ":datatransfer_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "datatransfer_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/datatransfer/apiv1/datatransferpb",
    protos = [":datatransfer_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "datatransfer_go_gapic",
    srcs = [":datatransfer_proto_with_info"],
    grpc_service_config = "bigquerydatatransfer_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/datatransfer/apiv1;datatransfer",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "bigquerydatatransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datatransfer_go_proto",
        "//google/cloud/location:location_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-datatransfer-v1-go",
    deps = [
        ":datatransfer_go_gapic",
        ":datatransfer_go_gapic_srcjar-metadata.srcjar",
        ":datatransfer_go_gapic_srcjar-snippets.srcjar",
        ":datatransfer_go_gapic_srcjar-test.srcjar",
        ":datatransfer_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "datatransfer_py_gapic",
    srcs = [":datatransfer_proto"],
    grpc_service_config = "bigquerydatatransfer_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=bigquery_datatransfer",
        "python-gapic-namespace=google.cloud",
    ],
    rest_numeric_enums = True,
    service_yaml = "bigquerydatatransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "datatransfer_py_gapic_test",
    srcs = [
        "datatransfer_py_gapic_pytest.py",
        "datatransfer_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":datatransfer_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-datatransfer-v1-py",
    deps = [
        ":datatransfer_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "datatransfer_php_proto",
    deps = [":datatransfer_proto"],
)

php_gapic_library(
    name = "datatransfer_php_gapic",
    srcs = [":datatransfer_proto_with_info"],
    grpc_service_config = "bigquerydatatransfer_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "bigquerydatatransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [":datatransfer_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-datatransfer-v1-php",
    deps = [
        ":datatransfer_php_gapic",
        ":datatransfer_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "datatransfer_nodejs_gapic",
    package_name = "@google-cloud/bigquery-data-transfer",
    src = ":datatransfer_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "bigquerydatatransfer_grpc_service_config.json",
    package = "google.cloud.bigquery.datatransfer.v1",
    rest_numeric_enums = True,
    service_yaml = "bigquerydatatransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-datatransfer-v1-nodejs",
    deps = [
        ":datatransfer_nodejs_gapic",
        ":datatransfer_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "datatransfer_ruby_proto",
    deps = [":datatransfer_proto"],
)

ruby_grpc_library(
    name = "datatransfer_ruby_grpc",
    srcs = [":datatransfer_proto"],
    deps = [":datatransfer_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "datatransfer_ruby_gapic",
    srcs = [":datatransfer_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=bigquerydatatransfer.googleapis.com",
        "ruby-cloud-api-shortname=bigquerydatatransfer",
        "ruby-cloud-env-prefix=DATA_TRANSFER",
        "ruby-cloud-gem-name=google-cloud-bigquery-data_transfer-v1",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/transfer",
        "ruby-cloud-yard-strict=false",
    ],
    grpc_service_config = "bigquerydatatransfer_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Schedules queries and transfers external data from SaaS applications to Google BigQuery on a regular basis.",
    ruby_cloud_title = "BigQuery Data Transfer Service V1",
    service_yaml = "bigquerydatatransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datatransfer_ruby_grpc",
        ":datatransfer_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-datatransfer-v1-ruby",
    deps = [
        ":datatransfer_ruby_gapic",
        ":datatransfer_ruby_grpc",
        ":datatransfer_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "datatransfer_csharp_proto",
    deps = [":datatransfer_proto"],
)

csharp_grpc_library(
    name = "datatransfer_csharp_grpc",
    srcs = [":datatransfer_proto"],
    deps = [":datatransfer_csharp_proto"],
)

csharp_gapic_library(
    name = "datatransfer_csharp_gapic",
    srcs = [":datatransfer_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "bigquerydatatransfer_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "bigquerydatatransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datatransfer_csharp_grpc",
        ":datatransfer_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-datatransfer-v1-csharp",
    deps = [
        ":datatransfer_csharp_gapic",
        ":datatransfer_csharp_grpc",
        ":datatransfer_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "datatransfer_cc_proto",
    deps = [":datatransfer_proto"],
)

cc_grpc_library(
    name = "datatransfer_cc_grpc",
    srcs = [":datatransfer_proto"],
    grpc_only = True,
    deps = [":datatransfer_cc_proto"],
)
