# This build file includes a target for the Ruby wrapper library for
# google-cloud-bigquery-data_transfer.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for bigquerydatatransfer.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "bigquerydatatransfer_ruby_wrapper",
    srcs = ["//google/cloud/bigquery/datatransfer/v1:datatransfer_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigquery-data_transfer",
        "ruby-cloud-env-prefix=DATA_TRANSFER",
        "ruby-cloud-wrapper-of=v1:0.12",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/transfer",
        "ruby-cloud-api-id=bigquerydatatransfer.googleapis.com",
        "ruby-cloud-api-shortname=bigquerydatatransfer",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Schedules queries and transfers external data from SaaS applications to Google BigQuery on a regular basis.",
    ruby_cloud_title = "BigQuery Data Transfer Service",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-datatransfer-ruby",
    deps = [
        ":bigquerydatatransfer_ruby_wrapper",
    ],
)
