type: google.api.Service
config_version: 3
name: sqladmin.googleapis.com
title: Cloud SQL Admin API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.sql.v1beta4.SqlBackupRunsService
- name: google.cloud.sql.v1beta4.SqlConnectService
- name: google.cloud.sql.v1beta4.SqlDatabasesService
- name: google.cloud.sql.v1beta4.SqlFlagsService
- name: google.cloud.sql.v1beta4.SqlInstancesService
- name: google.cloud.sql.v1beta4.SqlOperationsService
- name: google.cloud.sql.v1beta4.SqlSslCertsService
- name: google.cloud.sql.v1beta4.SqlTiersService
- name: google.cloud.sql.v1beta4.SqlUsersService
- name: google.longrunning.Operations

documentation:
  summary: API for Cloud SQL database instance management
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.sql.v1beta4.SqlBackupRunsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1beta4.SqlConnectService.GenerateEphemeralCert
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1beta4.SqlConnectService.GetConnectSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.cloud.sql.v1beta4.SqlDatabasesService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1beta4.SqlFlagsService.List
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.cloud.sql.v1beta4.SqlInstancesService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1beta4.SqlInstancesService.Export
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.sql.v1beta4.SqlInstancesService.Import
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.sql.v1beta4.SqlOperationsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.cloud.sql.v1beta4.SqlSslCertsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1beta4.SqlTiersService.List
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.cloud.sql.v1beta4.SqlUsersService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
