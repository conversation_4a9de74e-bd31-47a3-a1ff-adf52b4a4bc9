// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1beta4;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/sql/v1beta4/cloud_sql_resources.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/sql/apiv1beta4/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlConnectProto";
option java_package = "com.google.cloud.sql.v1beta4";

// LINT: LEGACY_NAMES

// Cloud SQL connect service.
service SqlConnectService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Retrieves connect settings about a Cloud SQL instance.
  rpc GetConnectSettings(GetConnectSettingsRequest) returns (ConnectSettings) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/connectSettings"
    };
  }

  // Generates a short-lived X509 certificate containing the provided public key
  // and signed by a private key specific to the target instance. Users may use
  // the certificate to authenticate as themselves when connecting to the
  // database.
  rpc GenerateEphemeralCert(GenerateEphemeralCertRequest)
      returns (GenerateEphemeralCertResponse) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}:generateEphemeralCert"
      body: "*"
    };
  }
}

// Connect settings retrieval request.
message GetConnectSettingsRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Optional. Optional snapshot read timestamp to trade freshness for
  // performance.
  google.protobuf.Timestamp read_time = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// Connect settings retrieval response.
message ConnectSettings {
  // This is always `sql#connectSettings`.
  string kind = 1;

  // SSL configuration.
  SslCert server_ca_cert = 2;

  // The assigned IP addresses for the instance.
  repeated IpMapping ip_addresses = 3;

  // The cloud region for the instance. e.g. `us-central1`, `europe-west1`.
  // The region cannot be changed after instance creation.
  string region = 4;

  // The database engine type and version. The `databaseVersion`
  // field cannot be changed after instance creation.
  //   MySQL instances: `MYSQL_8_0`, `MYSQL_5_7` (default),
  // or `MYSQL_5_6`.
  //   PostgreSQL instances: `POSTGRES_9_6`, `POSTGRES_10`,
  // `POSTGRES_11` or `POSTGRES_12` (default), `POSTGRES_13`, or `POSTGRES_14`.
  //   SQL Server instances: `SQLSERVER_2017_STANDARD` (default),
  // `SQLSERVER_2017_ENTERPRISE`, `SQLSERVER_2017_EXPRESS`,
  // `SQLSERVER_2017_WEB`, `SQLSERVER_2019_STANDARD`,
  // `SQLSERVER_2019_ENTERPRISE`, `SQLSERVER_2019_EXPRESS`, or
  // `SQLSERVER_2019_WEB`.
  SqlDatabaseVersion database_version = 31;

  // `SECOND_GEN`: Cloud SQL database instance.
  // `EXTERNAL`: A database server that is not managed by Google.
  // This property is read-only; use the `tier` property in the `settings`
  // object to determine the database type.
  SqlBackendType backend_type = 32;

  // Whether PSC connectivity is enabled for this instance.
  bool psc_enabled = 33;

  // The dns name of the instance.
  string dns_name = 34;
}

// Ephemeral certificate creation request.
message GenerateEphemeralCertRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // PEM encoded public key to include in the signed certificate.
  string public_key = 3;

  // Optional. Access token to include in the signed certificate.
  string access_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional snapshot read timestamp to trade freshness for
  // performance.
  google.protobuf.Timestamp read_time = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, it will contain the cert valid duration.
  google.protobuf.Duration valid_duration = 12
      [(google.api.field_behavior) = OPTIONAL];
}

// Ephemeral certificate creation request.
message GenerateEphemeralCertResponse {
  // Generated cert
  SslCert ephemeral_cert = 1;
}
