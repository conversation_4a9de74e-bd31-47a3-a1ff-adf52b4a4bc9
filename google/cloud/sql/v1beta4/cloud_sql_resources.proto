// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1beta4;

import "google/api/field_behavior.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/sql/apiv1beta4/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlResourcesProto";
option java_package = "com.google.cloud.sql.v1beta4";

// An entry for an Access Control list.
message AclEntry {
  // The allowlisted value for the access control list.
  string value = 1;

  // The time when this access control entry expires in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp expiration_time = 2;

  // Optional. A label to identify this entry.
  string name = 3 [(google.api.field_behavior) = OPTIONAL];

  // This is always `sql#aclEntry`.
  string kind = 4;
}

// An Admin API warning message.
message ApiWarning {
  enum SqlApiWarningCode {
    // An unknown or unset warning type from Cloud SQL API.
    SQL_API_WARNING_CODE_UNSPECIFIED = 0;

    // Warning when one or more regions are not reachable.  The returned result
    // set may be incomplete.
    REGION_UNREACHABLE = 1;

    // Warning when user provided maxResults parameter exceeds the limit.  The
    // returned result set may be incomplete.
    MAX_RESULTS_EXCEEDS_LIMIT = 2;

    // Warning when user tries to create/update a user with credentials that
    // have previously been compromised by a public data breach.
    COMPROMISED_CREDENTIALS = 3;

    // Warning when the operation succeeds but some non-critical workflow state
    // failed.
    INTERNAL_STATE_FAILURE = 4;
  }

  // Code to uniquely identify the warning type.
  SqlApiWarningCode code = 1;

  // The warning message.
  string message = 2;

  // The region name for REGION_UNREACHABLE warning.
  string region = 3;
}

// We currently only support backup retention by specifying the number
// of backups we will retain.
message BackupRetentionSettings {
  // The units that retained_backups specifies, we only support COUNT.
  enum RetentionUnit {
    // Backup retention unit is unspecified, will be treated as COUNT.
    RETENTION_UNIT_UNSPECIFIED = 0;

    // Retention will be by count, eg. "retain the most recent 7 backups".
    COUNT = 1;
  }

  // The unit that 'retained_backups' represents.
  RetentionUnit retention_unit = 1;

  // Depending on the value of retention_unit, this is used to determine
  // if a backup needs to be deleted.  If retention_unit is 'COUNT', we will
  // retain this many backups.
  google.protobuf.Int32Value retained_backups = 2;
}

// Database instance backup configuration.
message BackupConfiguration {
  // This value contains the storage location of the transactional logs
  // used to perform point-in-time recovery (PITR) for the database.
  enum TransactionalLogStorageState {
    // Unspecified.
    TRANSACTIONAL_LOG_STORAGE_STATE_UNSPECIFIED = 0;

    // The transaction logs used for PITR for the instance are stored
    // on a data disk.
    DISK = 1;

    // The transaction logs used for PITR for the instance are switching from
    // being stored on a data disk to being stored in Cloud Storage.
    // Only applicable to MySQL.
    SWITCHING_TO_CLOUD_STORAGE = 2;

    // The transaction logs used for PITR for the instance are now stored
    // in Cloud Storage. Previously, they were stored on a data disk.
    // Only applicable to MySQL.
    SWITCHED_TO_CLOUD_STORAGE = 3;

    // The transaction logs used for PITR for the instance are stored in
    // Cloud Storage. Only applicable to MySQL and PostgreSQL.
    CLOUD_STORAGE = 4;
  }

  // Start time for the daily backup configuration in UTC timezone in the 24
  // hour format - `HH:MM`.
  string start_time = 1;

  // Whether this configuration is enabled.
  google.protobuf.BoolValue enabled = 2;

  // This is always `sql#backupConfiguration`.
  string kind = 3;

  // (MySQL only) Whether binary log is enabled. If backup configuration is
  // disabled, binarylog must be disabled as well.
  google.protobuf.BoolValue binary_log_enabled = 4;

  // Reserved for future use.
  google.protobuf.BoolValue replication_log_archiving_enabled = 5;

  // Location of the backup
  string location = 6;

  // Whether point in time recovery is enabled.
  google.protobuf.BoolValue point_in_time_recovery_enabled = 7;

  // The number of days of transaction logs we retain for point in time
  // restore, from 1-7.
  google.protobuf.Int32Value transaction_log_retention_days = 9;

  // Backup retention settings.
  BackupRetentionSettings backup_retention_settings = 10;

  // Output only. This value contains the storage location of transactional logs
  // for the database for point-in-time recovery.
  optional TransactionalLogStorageState transactional_log_storage_state = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A BackupRun resource.
message BackupRun {
  // This is always `sql#backupRun`.
  string kind = 1;

  // The status of this run.
  SqlBackupRunStatus status = 2;

  // The time the run was enqueued in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp enqueued_time = 3;

  // The identifier for this backup run. Unique only for a specific Cloud SQL
  // instance.
  int64 id = 4;

  // The time the backup operation actually started in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp start_time = 5;

  // The time the backup operation completed in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp end_time = 6;

  // Information about why the backup operation failed. This is only present if
  // the run has the FAILED status.
  OperationError error = 7;

  // The type of this run; can be either "AUTOMATED" or "ON_DEMAND" or "FINAL".
  // This field defaults to "ON_DEMAND" and is ignored, when specified for
  // insert requests.
  SqlBackupRunType type = 8;

  // The description of this run, only applicable to on-demand backups.
  string description = 9;

  // The start time of the backup window during which this the backup was
  // attempted in [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for
  // example `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp window_start_time = 10;

  // Name of the database instance.
  string instance = 11;

  // The URI of this resource.
  string self_link = 12;

  // Location of the backups.
  string location = 13;

  // Encryption configuration specific to a backup.
  DiskEncryptionConfiguration disk_encryption_configuration = 16;

  // Encryption status specific to a backup.
  DiskEncryptionStatus disk_encryption_status = 17;

  // Specifies the kind of backup, PHYSICAL or DEFAULT_SNAPSHOT.
  SqlBackupKind backup_kind = 19;

  // Backup time zone to prevent restores to an instance with
  // a different time zone. Now relevant only for SQL Server.
  string time_zone = 23;
}

// Backup run list results.
message BackupRunsListResponse {
  // This is always `sql#backupRunsList`.
  string kind = 1;

  // A list of backup runs in reverse chronological order of the enqueued time.
  repeated BackupRun items = 2;

  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 3;
}

// Binary log coordinates.
message BinLogCoordinates {
  // Name of the binary log file for a Cloud SQL instance.
  string bin_log_file_name = 1;

  // Position (offset) within the binary log file.
  int64 bin_log_position = 2;

  // This is always `sql#binLogCoordinates`.
  string kind = 3;
}

// Backup context.
message BackupContext {
  // The identifier of the backup.
  int64 backup_id = 1;

  // This is always `sql#backupContext`.
  string kind = 2;
}

// Database instance clone context.
message CloneContext {
  // This is always `sql#cloneContext`.
  string kind = 1;

  // Reserved for future use.
  int64 pitr_timestamp_ms = 2;

  // Name of the Cloud SQL instance to be created as a clone.
  string destination_instance_name = 3;

  // Binary log coordinates, if specified, identify the position up to which the
  // source instance is cloned. If not specified, the source instance is
  // cloned up to the most recent binary log coordinates.
  BinLogCoordinates bin_log_coordinates = 4;

  // Timestamp, if specified, identifies the time to which the source instance
  // is cloned.
  google.protobuf.Timestamp point_in_time = 5;

  // The name of the allocated ip range for the private ip Cloud SQL instance.
  // For example: "google-managed-services-default". If set, the cloned instance
  // ip will be created in the allocated range. The range name must comply with
  // [RFC 1035](https://tools.ietf.org/html/rfc1035). Specifically, the name
  // must be 1-63 characters long and match the regular expression
  // [a-z]([-a-z0-9]*[a-z0-9])?.
  // Reserved for future use.
  string allocated_ip_range = 6;

  // (SQL Server only) Clone only the specified databases from the source
  // instance. Clone all databases if empty.
  repeated string database_names = 9;

  // Optional. Copy clone and point-in-time recovery clone of an instance to the
  // specified zone. If no zone is specified, clone to the same primary zone as
  // the source instance.
  optional string preferred_zone = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Copy clone and point-in-time recovery clone of a regional
  // instance in the specified zones. If not specified, clone to the same
  // secondary zone as the source instance. This value cannot be the same as the
  // preferred_zone field.
  optional string preferred_secondary_zone = 11
      [(google.api.field_behavior) = OPTIONAL];
}

// Represents a SQL database on the Cloud SQL instance.
message Database {
  // This is always `sql#database`.
  string kind = 1;

  // The Cloud SQL charset value.
  string charset = 2;

  // The Cloud SQL collation value.
  string collation = 3;

  // This field is deprecated and will be removed from a future version of the
  // API.
  string etag = 4;

  // The name of the database in the Cloud SQL instance. This does not include
  // the project ID or instance name.
  string name = 5;

  // The name of the Cloud SQL instance. This does not include the project ID.
  string instance = 6;

  // The URI of this resource.
  string self_link = 7;

  // The project ID of the project containing the Cloud SQL database. The Google
  // apps domain is prefixed if applicable.
  string project = 8;

  oneof database_details {
    SqlServerDatabaseDetails sqlserver_database_details = 9;
  }
}

// Represents a Sql Server database on the Cloud SQL instance.
message SqlServerDatabaseDetails {
  // The version of SQL Server with which the database is to be made compatible
  int32 compatibility_level = 1;

  // The recovery model of a SQL Server database
  string recovery_model = 2;
}

// Database flags for Cloud SQL instances.
message DatabaseFlags {
  // The name of the flag. These flags are passed at instance startup, so
  // include both server options and system variables. Flags are
  // specified with underscores, not hyphens. For more information, see
  // [Configuring Database Flags](https://cloud.google.com/sql/docs/mysql/flags)
  // in the Cloud SQL documentation.
  string name = 1;

  // The value of the flag. Boolean flags are set to `on` for true
  // and `off` for false. This field must be omitted if the flag
  // doesn't take a value.
  string value = 2;
}

// Initial sync flags for certain Cloud SQL APIs.
// Currently used for the MySQL external server initial dump.
message SyncFlags {
  // The name of the flag.
  string name = 1;

  // The value of the flag. This field must be omitted if the flag
  // doesn't take a value.
  string value = 2;
}

// Reference to another Cloud SQL instance.
message InstanceReference {
  // The name of the Cloud SQL instance being referenced.
  // This does not include the project ID.
  string name = 1;

  // The region of the Cloud SQL instance being referenced.
  string region = 2;

  // The project ID of the Cloud SQL instance being referenced.
  // The default is the same project ID as the instance references it.
  string project = 3;
}

// A Cloud SQL instance resource.
message DatabaseInstance {
  // The current serving state of the database instance.
  enum SqlInstanceState {
    // The state of the instance is unknown.
    SQL_INSTANCE_STATE_UNSPECIFIED = 0;

    // The instance is running, or has been stopped by owner.
    RUNNABLE = 1;

    // The instance is not available, for example due to problems with billing.
    SUSPENDED = 2;

    // The instance is being deleted.
    PENDING_DELETE = 3;

    // The instance is being created.
    PENDING_CREATE = 4;

    // The instance is down for maintenance.
    MAINTENANCE = 5;

    // The creation of the instance failed or a fatal error occurred during
    // maintenance.
    FAILED = 6;

    // Deprecated
    ONLINE_MAINTENANCE = 7 [deprecated = true];
  }

  message SqlFailoverReplica {
    // The name of the failover replica. If specified at instance creation, a
    // failover replica is created for the instance. The name
    // doesn't include the project ID.
    string name = 1;

    // The availability status of the failover replica. A false status indicates
    // that the failover replica is out of sync. The primary instance can only
    // failover to the failover replica when the status is true.
    google.protobuf.BoolValue available = 2;
  }

  // Any scheduled maintenance for this instance.
  message SqlScheduledMaintenance {
    // The start time of any upcoming scheduled maintenance for this instance.
    google.protobuf.Timestamp start_time = 1;

    bool can_defer = 2 [deprecated = true];

    // If the scheduled maintenance can be rescheduled.
    bool can_reschedule = 3;

    // Maintenance cannot be rescheduled to start beyond this deadline.
    optional google.protobuf.Timestamp schedule_deadline_time = 4;
  }

  // This message wraps up the information written by out-of-disk detection job.
  message SqlOutOfDiskReport {
    // This enum lists all possible states regarding out-of-disk issues.
    enum SqlOutOfDiskState {
      // Unspecified state
      SQL_OUT_OF_DISK_STATE_UNSPECIFIED = 0;

      // The instance has plenty space on data disk
      NORMAL = 1;

      // Data disk is almost used up. It is shutdown to prevent data
      // corruption.
      SOFT_SHUTDOWN = 2;
    }

    // This field represents the state generated by the proactive database
    // wellness job for OutOfDisk issues.
    // *  Writers:
    //   *  the proactive database wellness job for OOD.
    // *  Readers:
    //   *  the proactive database wellness job
    optional SqlOutOfDiskState sql_out_of_disk_state = 1;

    // The minimum recommended increase size in GigaBytes
    // This field is consumed by the frontend
    // *  Writers:
    //   *  the proactive database wellness job for OOD.
    // *  Readers:
    optional int32 sql_min_recommended_increase_size_gb = 2;
  }

  // The SQL network architecture for the instance.
  enum SqlNetworkArchitecture {
    SQL_NETWORK_ARCHITECTURE_UNSPECIFIED = 0;

    // The instance uses the new network architecture.
    NEW_NETWORK_ARCHITECTURE = 1;

    // The instance uses the old network architecture.
    OLD_NETWORK_ARCHITECTURE = 2;
  }

  // This is always `sql#instance`.
  string kind = 1;

  // The current serving state of the Cloud SQL instance.
  SqlInstanceState state = 2;

  // The database engine type and version. The `databaseVersion` field cannot
  // be changed after instance creation.
  SqlDatabaseVersion database_version = 3;

  // The user settings.
  Settings settings = 4;

  // This field is deprecated and will be removed from a future version of the
  // API. Use the `settings.settingsVersion` field instead.
  string etag = 5;

  // The name and status of the failover replica.
  SqlFailoverReplica failover_replica = 6;

  // The name of the instance which will act as primary in the replication
  // setup.
  string master_instance_name = 7;

  // The replicas of the instance.
  repeated string replica_names = 8;

  // The maximum disk size of the instance in bytes.
  google.protobuf.Int64Value max_disk_size = 9 [deprecated = true];

  // The current disk usage of the instance in bytes. This property has been
  // deprecated. Use the
  // "cloudsql.googleapis.com/database/disk/bytes_used" metric in Cloud
  // Monitoring API instead. Please see [this
  // announcement](https://groups.google.com/d/msg/google-cloud-sql-announce/I_7-F9EBhT0/BtvFtdFeAgAJ)
  // for details.
  google.protobuf.Int64Value current_disk_size = 10 [deprecated = true];

  // The assigned IP addresses for the instance.
  repeated IpMapping ip_addresses = 11;

  // SSL configuration.
  SslCert server_ca_cert = 12;

  // The instance type.
  SqlInstanceType instance_type = 13;

  // The project ID of the project containing the Cloud SQL instance. The Google
  // apps domain is prefixed if applicable.
  string project = 14;

  // The IPv6 address assigned to the instance.
  // (Deprecated) This property was applicable only
  // to First Generation instances.
  string ipv6_address = 15 [deprecated = true];

  // The service account email address assigned to the instance. \This
  // property is read-only.
  string service_account_email_address = 16;

  // Configuration specific to on-premises instances.
  OnPremisesConfiguration on_premises_configuration = 17;

  // Configuration specific to failover replicas and read replicas.
  ReplicaConfiguration replica_configuration = 18;

  // The backend type.
  // `SECOND_GEN`: Cloud SQL database instance.
  // `EXTERNAL`: A database server that is not managed by Google.
  //
  // This property is read-only; use the `tier` property in the `settings`
  // object to determine the database type.
  SqlBackendType backend_type = 19;

  // The URI of this resource.
  string self_link = 20;

  // If the instance state is SUSPENDED, the reason for the suspension.
  repeated SqlSuspensionReason suspension_reason = 21;

  // Connection name of the Cloud SQL instance used in connection strings.
  string connection_name = 22;

  // Name of the Cloud SQL instance. This does not include the project ID.
  string name = 23;

  // The geographical region of the Cloud SQL instance.
  //
  // It can be one of the
  // [regions](https://cloud.google.com/sql/docs/mysql/locations#location-r)
  // where Cloud SQL operates:
  //
  // For example,  `asia-east1`, `europe-west1`, and  `us-central1`.
  // The default value is `us-central1`.
  string region = 24;

  // The Compute Engine zone that the instance is currently serving from. This
  // value could be different from the zone that was specified when the instance
  // was created if the instance has failed over to its secondary zone. WARNING:
  // Changing this might restart the instance.
  string gce_zone = 25;

  // The Compute Engine zone that the failover instance is currently serving
  // from for a regional instance. This value could be different
  // from the zone that was specified when the instance
  // was created if the instance has failed over to its secondary/failover zone.
  string secondary_gce_zone = 34;

  // Disk encryption configuration specific to an instance.
  DiskEncryptionConfiguration disk_encryption_configuration = 26;

  // Disk encryption status specific to an instance.
  DiskEncryptionStatus disk_encryption_status = 27;

  // Initial root password. Use only on creation. You must set root passwords
  // before you can connect to PostgreSQL instances.
  string root_password = 29;

  // The start time of any upcoming scheduled maintenance for this instance.
  SqlScheduledMaintenance scheduled_maintenance = 30;

  // This status indicates whether the instance satisfies PZS.
  //
  // The status is reserved for future use.
  google.protobuf.BoolValue satisfies_pzs = 35;

  // Output only. Stores the current database version running on the instance
  // including minor version such as `MYSQL_8_0_18`.
  string database_installed_version = 40
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // This field represents the report generated by the proactive database
  // wellness job for OutOfDisk issues.
  // *  Writers:
  //   *  the proactive database wellness job for OOD.
  // *  Readers:
  //   *  the proactive database wellness job
  optional SqlOutOfDiskReport out_of_disk_report = 38;

  // Output only. The time when the instance was created in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp create_time = 39
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List all maintenance versions applicable on the instance
  repeated string available_maintenance_versions = 41
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The current software version on the instance.
  string maintenance_version = 42;

  // Output only. All database versions that are available for upgrade.
  repeated AvailableDatabaseVersion upgradable_database_versions = 45
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The SQL network architecture for the instance.
  optional SqlNetworkArchitecture sql_network_architecture = 47;

  // Output only. The link to service attachment of PSC instance.
  optional string psc_service_attachment_link = 48
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The dns name of the instance.
  optional string dns_name = 49 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. DEPRECATED: please use write_endpoint instead.
  optional string primary_dns_name = 51
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The dns name of the primary instance in a replication group.
  optional string write_endpoint = 52
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // A primary instance and disaster recovery (DR) replica pair.
  // A DR replica is a cross-region replica that you designate
  // for failover in the event that the primary instance
  // experiences regional failure. Only applicable to MySQL.
  optional ReplicationCluster replication_cluster = 54;

  // Gemini instance configuration.
  optional GeminiInstanceConfig gemini_config = 55;
}

// Gemini instance configuration.
message GeminiInstanceConfig {
  // Output only. Whether Gemini is enabled.
  optional bool entitled = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the vacuum management is enabled.
  optional bool google_vacuum_mgmt_enabled = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether canceling the out-of-memory (OOM) session is enabled.
  optional bool oom_session_cancel_enabled = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the active query is enabled.
  optional bool active_query_enabled = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the index advisor is enabled.
  optional bool index_advisor_enabled = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the flag recommender is enabled.
  optional bool flag_recommender_enabled = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A primary instance and disaster recovery (DR) replica pair.
// A DR replica is a cross-region replica that you designate for failover
// in the event that the primary instance has regional failure.
// Only applicable to MySQL.
message ReplicationCluster {
  // Output only. If set, it indicates this instance has a private service
  // access (PSA) dns endpoint that is pointing to the primary instance of the
  // cluster. If this instance is the primary, the dns should be pointing to
  // this instance. After Switchover or Replica failover, this DNS endpoint
  // points to the promoted instance. This is a read-only field, returned to the
  // user as information. This field can exist even if a standalone instance
  // does not yet have a replica, or had a DR replica that was deleted.
  optional string psa_write_endpoint = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. If the instance is a primary instance, then this field identifies
  // the disaster recovery (DR) replica. A DR replica is an optional
  // configuration for Enterprise Plus edition instances. If the instance is a
  // read replica, then the field is not set. Set this field to a replica name
  // to designate a DR replica for a primary instance. Remove the replica name
  // to remove the DR replica designation.
  optional string failover_dr_replica_name = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Read-only field that indicates whether the replica is a DR
  // replica. This field is not set if the instance is a primary instance.
  optional bool dr_replica = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An available database version. It can be a major or a minor version.
message AvailableDatabaseVersion {
  // The version's major version name.
  optional string major_version = 3;

  // The database version name. For MySQL 8.0, this string provides the database
  // major and minor version.
  optional string name = 8;

  // The database version's display name.
  optional string display_name = 9;
}

// Database list response.
message DatabasesListResponse {
  // This is always `sql#databasesList`.
  string kind = 1;

  // List of database resources in the instance.
  repeated Database items = 2;
}

// Read-replica configuration for connecting to the on-premises primary
// instance.
message DemoteMasterConfiguration {
  // This is always `sql#demoteMasterConfiguration`.
  string kind = 1;

  // MySQL specific configuration when replicating from a MySQL on-premises
  // primary instance. Replication configuration information such as the
  // username, password, certificates, and keys are not stored in the instance
  // metadata. The configuration information is used only to set up the
  // replication connection and is stored by MySQL in a file named
  // `master.info` in the data directory.
  DemoteMasterMySqlReplicaConfiguration mysql_replica_configuration = 2;
}

// Database instance demote primary instance context.
message DemoteMasterContext {
  // This is always `sql#demoteMasterContext`.
  string kind = 1;

  // Verify the GTID consistency for demote operation. Default value:
  // `True`. Setting this flag to `false` enables you to bypass the GTID
  // consistency check between on-premises primary instance and Cloud SQL
  // instance during the demotion operation but also exposes you to the risk of
  // future replication failures. Change the value only if you know the reason
  // for the GTID divergence and are confident that doing so will not cause any
  // replication issues.
  google.protobuf.BoolValue verify_gtid_consistency = 2;

  // The name of the instance which will act as on-premises primary instance
  // in the replication setup.
  string master_instance_name = 3;

  // Configuration specific to read-replicas replicating from the on-premises
  // primary instance.
  DemoteMasterConfiguration replica_configuration = 4;

  // Flag to skip replication setup on the instance.
  bool skip_replication_setup = 5;
}

// Read-replica configuration specific to MySQL databases.
message DemoteMasterMySqlReplicaConfiguration {
  // This is always `sql#demoteMasterMysqlReplicaConfiguration`.
  string kind = 1;

  // The username for the replication connection.
  string username = 2;

  // The password for the replication connection.
  string password = 3;

  // PEM representation of the replica's private key. The corresponsing public
  // key is encoded in the client's certificate. The format of the replica's
  // private key can be either PKCS #1 or PKCS #8.
  string client_key = 4;

  // PEM representation of the replica's x509 certificate.
  string client_certificate = 5;

  // PEM representation of the trusted CA's x509 certificate.
  string ca_certificate = 6;
}

// This context is used to demote an existing standalone instance to be
// a Cloud SQL read replica for an external database server.
message DemoteContext {
  // This is always `sql#demoteContext`.
  string kind = 1;

  // Required. The name of the instance which acts as an on-premises primary
  // instance in the replication setup.
  string source_representative_instance_name = 2
      [(google.api.field_behavior) = REQUIRED];
}

enum SqlFileType {
  // Unknown file type.
  SQL_FILE_TYPE_UNSPECIFIED = 0;

  // File containing SQL statements.
  SQL = 1;

  // File in CSV format.
  CSV = 2;

  BAK = 4;
}

// Database instance export context.
message ExportContext {
  message SqlCsvExportOptions {
    // The select query used to extract the data.
    string select_query = 1;

    // Specifies the character that should appear before a data character that
    // needs to be escaped.
    string escape_character = 2;

    // Specifies the quoting character to be used when a data value is quoted.
    string quote_character = 3;

    // Specifies the character that separates columns within each row (line) of
    // the file.
    string fields_terminated_by = 4;

    // This is used to separate lines. If a line does not contain all fields,
    // the rest of the columns are set to their default values.
    string lines_terminated_by = 6;
  }

  message SqlExportOptions {
    // Options for exporting from MySQL.
    message MysqlExportOptions {
      // Option to include SQL statement required to set up replication. If set
      // to `1`, the dump file includes a CHANGE MASTER TO statement with the
      // binary log coordinates, and --set-gtid-purged is set to ON. If set to
      // `2`, the CHANGE MASTER TO statement is written as a SQL comment and
      // has no effect. If set to any value other than `1`, --set-gtid-purged
      // is set to OFF.
      google.protobuf.Int32Value master_data = 1;
    }

    // Tables to export, or that were exported, from the specified database. If
    // you specify tables, specify one and only one database. For PostgreSQL
    // instances, you can specify only one table.
    repeated string tables = 1;

    // Export only schemas.
    google.protobuf.BoolValue schema_only = 2;

    MysqlExportOptions mysql_export_options = 3;

    // Optional. The number of threads to use for parallel export.
    google.protobuf.Int32Value threads = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Whether or not the export should be parallel.
    google.protobuf.BoolValue parallel = 5
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Options for exporting BAK files (SQL Server-only)
  message SqlBakExportOptions {
    // Whether or not the export should be striped.
    google.protobuf.BoolValue striped = 1;

    // Option for specifying how many stripes to use for the export.
    // If blank, and the value of the striped field is true,
    // the number of stripes is automatically chosen.
    google.protobuf.Int32Value stripe_count = 2;

    // Type of this bak file will be export, FULL or DIFF, SQL Server only
    BakType bak_type = 4;

    // Deprecated: copy_only is deprecated. Use differential_base instead
    google.protobuf.BoolValue copy_only = 5 [deprecated = true];

    // Whether or not the backup can be used as a differential base
    // copy_only backup can not be served as differential base
    google.protobuf.BoolValue differential_base = 6;
  }

  // The path to the file in Google Cloud Storage where the export will be
  // stored. The URI is in the form `gs://bucketName/fileName`. If the file
  // already exists, the request succeeds, but the operation fails. If
  // `fileType` is `SQL` and the filename ends with .gz,
  // the contents are compressed.
  string uri = 1;

  // Databases to be exported. <br /> `MySQL instances:` If
  // `fileType` is `SQL` and no database is specified, all
  // databases are exported, except for the `mysql` system database.
  // If `fileType` is `CSV`, you can specify one database,
  // either by using this property or by using the
  // `csvExportOptions.selectQuery` property, which takes precedence
  // over this property. <br /> `PostgreSQL instances:` You must specify
  // one database to be exported. If `fileType` is `CSV`,
  // this database must match the one specified in the
  // `csvExportOptions.selectQuery` property. <br /> `SQL Server
  // instances:` You must specify one database to be exported, and the
  // `fileType` must be `BAK`.
  repeated string databases = 2;

  // This is always `sql#exportContext`.
  string kind = 3;

  // Options for exporting data as SQL statements.
  SqlExportOptions sql_export_options = 4;

  // Options for exporting data as CSV. `MySQL` and `PostgreSQL`
  // instances only.
  SqlCsvExportOptions csv_export_options = 5;

  // The file type for the specified uri.
  SqlFileType file_type = 6;

  // Option for export offload.
  google.protobuf.BoolValue offload = 8;

  // Options for exporting data as BAK files.
  SqlBakExportOptions bak_export_options = 9;
}

// Database instance failover context.
message FailoverContext {
  // The current settings version of this instance. Request will be rejected if
  // this version doesn't match the current settings version.
  int64 settings_version = 1;

  // This is always `sql#failoverContext`.
  string kind = 2;
}

// A flag resource.
message Flag {
  // This is the name of the flag. Flag names always use underscores, not
  // hyphens, for example: `max_allowed_packet`
  string name = 1;

  // The type of the flag. Flags are typed to being `BOOLEAN`, `STRING`,
  // `INTEGER` or `NONE`. `NONE` is used for flags which do not take a
  // value, such as `skip_grant_tables`.
  SqlFlagType type = 2;

  // The database version this flag applies to. Can be
  // MySQL instances: `MYSQL_8_0`, `MYSQL_8_0_18`, `MYSQL_8_0_26`, `MYSQL_5_7`,
  // or `MYSQL_5_6`. PostgreSQL instances: `POSTGRES_9_6`, `POSTGRES_10`,
  // `POSTGRES_11` or `POSTGRES_12`. SQL Server instances:
  // `SQLSERVER_2017_STANDARD`, `SQLSERVER_2017_ENTERPRISE`,
  // `SQLSERVER_2017_EXPRESS`, `SQLSERVER_2017_WEB`, `SQLSERVER_2019_STANDARD`,
  // `SQLSERVER_2019_ENTERPRISE`, `SQLSERVER_2019_EXPRESS`, or
  // `SQLSERVER_2019_WEB`.
  // See [the complete
  // list](/sql/docs/mysql/admin-api/rest/v1/SqlDatabaseVersion).
  repeated SqlDatabaseVersion applies_to = 3;

  // For `STRING` flags, a list of strings that the value can be set to.
  repeated string allowed_string_values = 4;

  // For `INTEGER` flags, the minimum allowed value.
  google.protobuf.Int64Value min_value = 5;

  // For `INTEGER` flags, the maximum allowed value.
  google.protobuf.Int64Value max_value = 6;

  // Indicates whether changing this flag will trigger a database restart. Only
  // applicable to Second Generation instances.
  google.protobuf.BoolValue requires_restart = 7;

  // This is always `sql#flag`.
  string kind = 8;

  // Whether or not the flag is considered in beta.
  google.protobuf.BoolValue in_beta = 9;

  // Use this field if only certain integers are accepted. Can be combined
  // with min_value and max_value to add additional values.
  repeated int64 allowed_int_values = 10;
}

// Flags list response.
message FlagsListResponse {
  // This is always `sql#flagsList`.
  string kind = 1;

  // List of flags.
  repeated Flag items = 2;
}

// Database instance import context.
message ImportContext {
  message SqlImportOptions {
    // Optional. The number of threads to use for parallel import.
    google.protobuf.Int32Value threads = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Whether or not the import should be parallel.
    google.protobuf.BoolValue parallel = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  message SqlCsvImportOptions {
    // The table to which CSV data is imported.
    string table = 1;

    // The columns to which CSV data is imported. If not specified, all columns
    // of the database table are loaded with CSV data.
    repeated string columns = 2;

    // Specifies the character that should appear before a data character that
    // needs to be escaped.
    string escape_character = 4;

    // Specifies the quoting character to be used when a data value is quoted.
    string quote_character = 5;

    // Specifies the character that separates columns within each row (line) of
    // the file.
    string fields_terminated_by = 6;

    // This is used to separate lines. If a line does not contain all fields,
    // the rest of the columns are set to their default values.
    string lines_terminated_by = 8;
  }

  message SqlBakImportOptions {
    message EncryptionOptions {
      // Path to the Certificate (.cer) in Cloud Storage, in the form
      // `gs://bucketName/fileName`. The instance must have write permissions
      // to the bucket and read access to the file.
      string cert_path = 1;

      // Path to the Certificate Private Key (.pvk)  in Cloud Storage, in the
      // form `gs://bucketName/fileName`. The instance must have write
      // permissions to the bucket and read access to the file.
      string pvk_path = 2;

      // Password that encrypts the private key
      string pvk_password = 3;
    }

    EncryptionOptions encryption_options = 1;

    // Whether or not the backup set being restored is striped.
    // Applies only to Cloud SQL for SQL Server.
    google.protobuf.BoolValue striped = 2;

    // Whether or not the backup importing will restore database
    // with NORECOVERY option
    // Applies only to Cloud SQL for SQL Server.
    google.protobuf.BoolValue no_recovery = 4;

    // Whether or not the backup importing request will just bring database
    // online without downloading Bak content only one of "no_recovery" and
    // "recovery_only" can be true otherwise error will return. Applies only to
    // Cloud SQL for SQL Server.
    google.protobuf.BoolValue recovery_only = 5;

    // Type of the bak content, FULL or DIFF.
    BakType bak_type = 6;

    // Optional. The timestamp when the import should stop. This timestamp is in
    // the [RFC 3339](https://tools.ietf.org/html/rfc3339) format (for example,
    // `2023-10-01T16:19:00.094`). This field is equivalent to the STOPAT
    // keyword and applies to Cloud SQL for SQL Server only.
    google.protobuf.Timestamp stop_at = 7
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The marked transaction where the import should stop. This field
    // is equivalent to the STOPATMARK keyword and applies to Cloud SQL for SQL
    // Server only.
    string stop_at_mark = 8 [(google.api.field_behavior) = OPTIONAL];
  }

  // Path to the import file in Cloud Storage, in the form
  // `gs://bucketName/fileName`. Compressed gzip files (.gz) are supported
  // when `fileType` is `SQL`. The instance must have
  // write permissions to the bucket and read access to the file.
  string uri = 1;

  // The target database for the import. If `fileType` is `SQL`, this field
  // is required only if the import file does not specify a database, and is
  // overridden by any database specification in the import file. If
  // `fileType` is `CSV`, one database must be specified.
  string database = 2;

  // This is always `sql#importContext`.
  string kind = 3;

  // The file type for the specified uri.
  // *  `SQL`: The file contains SQL statements.
  // *  `CSV`: The file contains CSV data.
  // *  `BAK`: The file contains backup data for a SQL Server instance.
  SqlFileType file_type = 4;

  // Options for importing data as CSV.
  SqlCsvImportOptions csv_import_options = 5;

  // The PostgreSQL user for this import operation. PostgreSQL instances only.
  string import_user = 6;

  // Import parameters specific to SQL Server .BAK files
  SqlBakImportOptions bak_import_options = 7;

  // Optional. Options for importing data from SQL statements.
  SqlImportOptions sql_import_options = 8
      [(google.api.field_behavior) = OPTIONAL];
}

enum BakType {
  // Default type.
  BAK_TYPE_UNSPECIFIED = 0;

  // Full backup.
  FULL = 1;

  // Differential backup.
  DIFF = 2;

  // SQL Server Transaction Log
  TLOG = 3;
}

// Database instance clone request.
message InstancesCloneRequest {
  // Contains details about the clone operation.
  CloneContext clone_context = 1;
}

// Database demote primary instance request.
message InstancesDemoteMasterRequest {
  // Contains details about the demoteMaster operation.
  DemoteMasterContext demote_master_context = 1;
}

// This request is used to demote an existing standalone instance to be a
// Cloud SQL read replica for an external database server.
message InstancesDemoteRequest {
  // Required. This context is used to demote an existing standalone instance to
  // be a Cloud SQL read replica for an external database server.
  DemoteContext demote_context = 1 [(google.api.field_behavior) = REQUIRED];
}

// Database instance export request.
message InstancesExportRequest {
  // Contains details about the export operation.
  ExportContext export_context = 1;
}

// Instance failover request.
message InstancesFailoverRequest {
  // Failover Context.
  FailoverContext failover_context = 1;
}

// Database instance import request.
message InstancesImportRequest {
  // Contains details about the import operation.
  ImportContext import_context = 1;
}

// MySQL-specific external server sync settings.
message MySqlSyncConfig {
  // Flags to use for the initial dump.
  repeated SyncFlags initial_sync_flags = 1;
}

// Database instances list response.
message InstancesListResponse {
  // This is always `sql#instancesList`.
  string kind = 1;

  // List of warnings that occurred while handling the request.
  repeated ApiWarning warnings = 2;

  // List of database instance resources.
  repeated DatabaseInstance items = 3;

  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 4;
}

// Instances ListServerCas response.
message InstancesListServerCasResponse {
  // List of server CA certificates for the instance.
  repeated SslCert certs = 1;

  string active_version = 2;

  // This is always `sql#instancesListServerCas`.
  string kind = 3;
}

// Database instance restore backup request.
message InstancesRestoreBackupRequest {
  // Parameters required to perform the restore backup operation.
  RestoreBackupContext restore_backup_context = 1;
}

// Rotate Server CA request.
message InstancesRotateServerCaRequest {
  // Contains details about the rotate server CA operation.
  RotateServerCaContext rotate_server_ca_context = 1;
}

// Instance truncate log request.
message InstancesTruncateLogRequest {
  // Contains details about the truncate log operation.
  TruncateLogContext truncate_log_context = 1;
}

// Request to acquire an SSRS lease for an instance.
message InstancesAcquireSsrsLeaseRequest {
  // Contains details about the acquire SSRS lease operation.
  AcquireSsrsLeaseContext acquire_ssrs_lease_context = 1;
}

// Perform disk shrink context.
message PerformDiskShrinkContext {
  // The target disk shrink size in GigaBytes.
  int64 target_size_gb = 1;
}

// Instance get disk shrink config response.
message SqlInstancesGetDiskShrinkConfigResponse {
  // This is always `sql#getDiskShrinkConfig`.
  string kind = 1;

  // The minimum size to which a disk can be shrunk in GigaBytes.
  int64 minimal_target_size_gb = 2;

  // Additional message to customers.
  string message = 3;
}

// Instance verify external sync settings response.
message SqlInstancesVerifyExternalSyncSettingsResponse {
  // This is always `sql#migrationSettingErrorList`.
  string kind = 1;

  // List of migration violations.
  repeated SqlExternalSyncSettingError errors = 2;

  // List of migration warnings.
  repeated SqlExternalSyncSettingError warnings = 3;
}

// External primary instance migration setting error/warning.
message SqlExternalSyncSettingError {
  enum SqlExternalSyncSettingErrorType {
    SQL_EXTERNAL_SYNC_SETTING_ERROR_TYPE_UNSPECIFIED = 0;

    CONNECTION_FAILURE = 1;

    BINLOG_NOT_ENABLED = 2;

    INCOMPATIBLE_DATABASE_VERSION = 3;

    REPLICA_ALREADY_SETUP = 4;

    // The replication user is missing privileges that are required.
    INSUFFICIENT_PRIVILEGE = 5;

    // Unsupported migration type.
    UNSUPPORTED_MIGRATION_TYPE = 6;

    // No pglogical extension installed on databases, applicable for postgres.
    NO_PGLOGICAL_INSTALLED = 7;

    // pglogical node already exists on databases, applicable for postgres.
    PGLOGICAL_NODE_ALREADY_EXISTS = 8;

    // The value of parameter wal_level is not set to logical.
    INVALID_WAL_LEVEL = 9;

    // The value of parameter shared_preload_libraries does not include
    // pglogical.
    INVALID_SHARED_PRELOAD_LIBRARY = 10;

    // The value of parameter max_replication_slots is not sufficient.
    INSUFFICIENT_MAX_REPLICATION_SLOTS = 11;

    // The value of parameter max_wal_senders is not sufficient.
    INSUFFICIENT_MAX_WAL_SENDERS = 12;

    // The value of parameter max_worker_processes is not sufficient.
    INSUFFICIENT_MAX_WORKER_PROCESSES = 13;

    // Extensions installed are either not supported or having unsupported
    // versions
    UNSUPPORTED_EXTENSIONS = 14;

    // The value of parameter rds.logical_replication is not set to 1.
    INVALID_RDS_LOGICAL_REPLICATION = 15;

    // The primary instance logging setup doesn't allow EM sync.
    INVALID_LOGGING_SETUP = 16;

    // The primary instance database parameter setup doesn't allow EM sync.
    INVALID_DB_PARAM = 17;

    // The gtid_mode is not supported, applicable for MySQL.
    UNSUPPORTED_GTID_MODE = 18;

    // SQL Server Agent is not running.
    SQLSERVER_AGENT_NOT_RUNNING = 19;

    // The table definition is not support due to missing primary key or replica
    // identity, applicable for postgres.
    UNSUPPORTED_TABLE_DEFINITION = 20;

    // The customer has a definer that will break EM setup.
    UNSUPPORTED_DEFINER = 21;

    // SQL Server @@SERVERNAME does not match actual host name.
    SQLSERVER_SERVERNAME_MISMATCH = 22;

    // The primary instance has been setup and will fail the setup.
    PRIMARY_ALREADY_SETUP = 23;

    // The primary instance has unsupported binary log format.
    UNSUPPORTED_BINLOG_FORMAT = 24;

    // The primary instance's binary log retention setting.
    BINLOG_RETENTION_SETTING = 25;

    // The primary instance has tables with unsupported storage engine.
    UNSUPPORTED_STORAGE_ENGINE = 26;

    // Source has tables with limited support
    // eg: PostgreSQL tables without primary keys.
    LIMITED_SUPPORT_TABLES = 27;

    // The replica instance contains existing data.
    EXISTING_DATA_IN_REPLICA = 28;

    // The replication user is missing privileges that are optional.
    MISSING_OPTIONAL_PRIVILEGES = 29;

    // Additional BACKUP_ADMIN privilege is granted to the replication user
    // which may lock source MySQL 8 instance for DDLs during initial sync.
    RISKY_BACKUP_ADMIN_PRIVILEGE = 30;

    // The Cloud Storage bucket is missing necessary permissions.
    INSUFFICIENT_GCS_PERMISSIONS = 31;

    // The Cloud Storage bucket has an error in the file or contains invalid
    // file information.
    INVALID_FILE_INFO = 32;

    // The source instance has unsupported database settings for migration.
    UNSUPPORTED_DATABASE_SETTINGS = 33;

    // The replication user is missing parallel import specific privileges.
    // (e.g. LOCK TABLES) for MySQL.
    MYSQL_PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE = 34;

    // The global variable local_infile is off on external server replica.
    LOCAL_INFILE_OFF = 35;

    // This code instructs customers to turn on point-in-time recovery manually
    // for the instance after promoting the Cloud SQL for PostgreSQL instance.
    TURN_ON_PITR_AFTER_PROMOTE = 36;

    // The minor version of replica database is incompatible with the source.
    INCOMPATIBLE_DATABASE_MINOR_VERSION = 37;

    // This warning message indicates that Cloud SQL uses the maximum number of
    // subscriptions to migrate data from the source to the destination.
    SOURCE_MAX_SUBSCRIPTIONS = 38;

    // Unable to verify definers on the source for MySQL.
    UNABLE_TO_VERIFY_DEFINERS = 39;

    // If a time out occurs while the subscription counts are calculated, then
    // this value is set to 1. Otherwise, this value is set to 2.
    SUBSCRIPTION_CALCULATION_STATUS = 40;

    // Count of subscriptions needed to sync source data for PostgreSQL
    // database.
    PG_SUBSCRIPTION_COUNT = 41;

    // Final parallel level that is used to do migration.
    PG_SYNC_PARALLEL_LEVEL = 42;

    // The disk size of the replica instance is smaller than the data size of
    // the source instance.
    INSUFFICIENT_DISK_SIZE = 43;

    // The data size of the source instance is greater than 1 TB, the number of
    // cores of the replica instance is less than 8, and the memory of the
    // replica is less than 32 GB.
    INSUFFICIENT_MACHINE_TIER = 44;

    // The warning message indicates the unsupported extensions will not be
    // migrated to the destination.
    UNSUPPORTED_EXTENSIONS_NOT_MIGRATED = 45;

    // The warning message indicates the pg_cron extension and settings will not
    // be migrated to the destination.
    EXTENSIONS_NOT_MIGRATED = 46;

    // The error message indicates that pg_cron flags are enabled on the
    // destination which is not supported during the migration.
    PG_CRON_FLAG_ENABLED_IN_REPLICA = 47;

    // This error message indicates that the specified extensions are not
    // enabled on destination instance. For example, before you can migrate
    // data to the destination instance, you must enable the PGAudit extension
    // on the instance.
    EXTENSIONS_NOT_ENABLED_IN_REPLICA = 48;
  }

  // Can be `sql#externalSyncSettingError` or
  // `sql#externalSyncSettingWarning`.
  string kind = 1;

  // Identifies the specific error that occurred.
  SqlExternalSyncSettingErrorType type = 2;

  // Additional information about the error encountered.
  string detail = 3;
}

// IP Management configuration.
message IpConfiguration {
  // The SSL options for database connections.
  enum SslMode {
    // The SSL mode is unknown.
    SSL_MODE_UNSPECIFIED = 0;

    // Allow non-SSL/non-TLS and SSL/TLS connections.
    // For SSL connections to MySQL and PostgreSQL, the client certificate
    // isn't verified.
    //
    // When this value is used, the legacy `require_ssl` flag must be false or
    // cleared to avoid a conflict between the values of the two flags.
    ALLOW_UNENCRYPTED_AND_ENCRYPTED = 1;

    // Only allow connections encrypted with SSL/TLS.
    // For SSL connections to MySQL and PostgreSQL, the client certificate
    // isn't verified.
    //
    // When this value is used, the legacy `require_ssl` flag must be false or
    // cleared to avoid a conflict between the values of the two flags.
    ENCRYPTED_ONLY = 2;

    // Only allow connections encrypted with SSL/TLS and with valid
    // client certificates.
    //
    // When this value is used, the legacy `require_ssl` flag must be true or
    // cleared to avoid the conflict between values of two flags.
    // PostgreSQL clients or users that connect using IAM database
    // authentication must use either the
    // [Cloud SQL Auth
    // Proxy](https://cloud.google.com/sql/docs/postgres/connect-auth-proxy) or
    // [Cloud SQL
    // Connectors](https://cloud.google.com/sql/docs/postgres/connect-connectors)
    // to enforce client identity verification.
    //
    // Only applicable to MySQL and PostgreSQL. Not applicable to SQL Server.
    TRUSTED_CLIENT_CERTIFICATE_REQUIRED = 3;
  }

  // Whether the instance is assigned a public IP address or not.
  google.protobuf.BoolValue ipv4_enabled = 1;

  // The resource link for the VPC network from which the Cloud SQL instance is
  // accessible for private IP. For example,
  // `/projects/myProject/global/networks/default`. This setting can
  // be updated, but it cannot be removed after it is set.
  string private_network = 2;

  // Use `ssl_mode` instead.
  //
  // Whether SSL/TLS connections over IP are enforced.
  // If set to false, then allow both non-SSL/non-TLS and SSL/TLS connections.
  // For SSL/TLS connections, the client certificate won't be verified. If
  // set to true, then only allow connections encrypted with SSL/TLS and with
  // valid client certificates. If you want to enforce SSL/TLS without enforcing
  // the requirement for valid client certificates, then use the `ssl_mode` flag
  // instead of the legacy `require_ssl` flag.
  google.protobuf.BoolValue require_ssl = 3;

  // The list of external networks that are allowed to connect to the instance
  // using the IP. In 'CIDR' notation, also known as 'slash' notation (for
  // example: `*************/24`).
  repeated AclEntry authorized_networks = 4;

  // The name of the allocated ip range for the private ip Cloud SQL instance.
  // For example: "google-managed-services-default". If set, the instance ip
  // will be created in the allocated range. The range name must comply with
  // [RFC 1035](https://tools.ietf.org/html/rfc1035). Specifically, the name
  // must be 1-63 characters long and match the regular expression
  // `[a-z]([-a-z0-9]*[a-z0-9])?.`
  string allocated_ip_range = 6;

  // Controls connectivity to private IP instances from Google services,
  // such as BigQuery.
  google.protobuf.BoolValue enable_private_path_for_google_cloud_services = 7;

  // Specify how SSL/TLS is enforced in database connections. If you must use
  // the `require_ssl` flag for backward compatibility, then only the following
  // value pairs are valid:
  //
  // For PostgreSQL and MySQL:
  //
  // * `ssl_mode=ALLOW_UNENCRYPTED_AND_ENCRYPTED` and `require_ssl=false`
  // * `ssl_mode=ENCRYPTED_ONLY` and `require_ssl=false`
  // * `ssl_mode=TRUSTED_CLIENT_CERTIFICATE_REQUIRED` and `require_ssl=true`
  //
  // For SQL Server:
  //
  // * `ssl_mode=ALLOW_UNENCRYPTED_AND_ENCRYPTED` and `require_ssl=false`
  // * `ssl_mode=ENCRYPTED_ONLY` and `require_ssl=true`
  //
  // The value of `ssl_mode` has priority over the value of `require_ssl`.
  //
  // For example, for the pair `ssl_mode=ENCRYPTED_ONLY` and
  // `require_ssl=false`, `ssl_mode=ENCRYPTED_ONLY` means accept only SSL
  // connections, while `require_ssl=false` means accept both non-SSL
  // and SSL connections. In this case, MySQL and PostgreSQL databases respect
  // `ssl_mode` and accepts only SSL connections.
  SslMode ssl_mode = 8;

  // PSC settings for this instance.
  optional PscConfig psc_config = 9;
}

// PSC settings for a Cloud SQL instance.
message PscConfig {
  // Whether PSC connectivity is enabled for this instance.
  optional bool psc_enabled = 1;

  // Optional. The list of consumer projects that are allow-listed for PSC
  // connections to this instance. This instance can be connected to with PSC
  // from any network in these projects.
  //
  // Each consumer project in this list may be represented by a project number
  // (numeric) or by a project id (alphanumeric).
  repeated string allowed_consumer_projects = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Database instance IP mapping
message IpMapping {
  // The type of this IP address. A `PRIMARY` address is a public address that
  // can accept incoming connections. A `PRIVATE` address is a private address
  // that can accept incoming connections. An `OUTGOING` address is the source
  // address of connections originating from the instance, if supported.
  SqlIpAddressType type = 1;

  // The IP address assigned.
  string ip_address = 2;

  // The due time for this IP to be retired in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`. This field is only available when
  // the IP is scheduled to be retired.
  google.protobuf.Timestamp time_to_retire = 3;
}

// Preferred location. This specifies where a Cloud SQL instance is located.
// Note that if the preferred location is not available, the instance will be
// located as close as possible within the region. Only one location may be
// specified.
message LocationPreference {
  // The App Engine application to follow, it must be in the same region as the
  // Cloud SQL instance. WARNING: Changing this might restart the instance.
  string follow_gae_application = 1 [deprecated = true];

  // The preferred Compute Engine zone (for example: us-central1-a,
  // us-central1-b, etc.). WARNING: Changing this might restart the instance.
  string zone = 2;

  // The preferred Compute Engine zone for the secondary/failover
  // (for example: us-central1-a, us-central1-b, etc.).
  // To disable this field, set it to 'no_secondary_zone'.
  string secondary_zone = 4;

  // This is always `sql#locationPreference`.
  string kind = 3;
}

// Maintenance window. This specifies when a Cloud SQL instance
// is restarted for system maintenance purposes.
message MaintenanceWindow {
  // hour of day - 0 to 23.
  google.protobuf.Int32Value hour = 1;

  // day of week (1-7), starting on Monday.
  google.protobuf.Int32Value day = 2;

  // Maintenance timing setting: `canary` (Earlier) or `stable` (Later).
  // [Learn
  // more](https://cloud.google.com/sql/docs/mysql/instance-settings#maintenance-timing-2ndgen).
  SqlUpdateTrack update_track = 3;

  // This is always `sql#maintenanceWindow`.
  string kind = 4;
}

// Deny Maintenance Periods. This specifies a date range during when all CSA
// rollout will be denied.
message DenyMaintenancePeriod {
  // "deny maintenance period" start date. If the year of the start date is
  // empty, the year of the end date also must be empty. In this case, it means
  // the deny maintenance period recurs every year. The date is in format
  // yyyy-mm-dd i.e., 2020-11-01, or mm-dd, i.e., 11-01
  string start_date = 1;

  // "deny maintenance period" end date. If the year of the end date is empty,
  // the year of the start date also must be empty. In this case, it means the
  // deny maintenance period recurs every year. The date is in format yyyy-mm-dd
  // i.e., 2020-11-01, or mm-dd, i.e., 11-01
  string end_date = 2;

  // Time in UTC when the "deny maintenance period" starts on start_date and
  // ends on end_date. The time is in format: HH:mm:SS, i.e., 00:00:00
  string time = 3;
}

// Insights configuration. This specifies when Cloud SQL Insights feature is
// enabled and optional configuration.
message InsightsConfig {
  // Whether Query Insights feature is enabled.
  bool query_insights_enabled = 1;

  // Whether Query Insights will record client address when enabled.
  bool record_client_address = 2;

  // Whether Query Insights will record application tags from query when
  // enabled.
  bool record_application_tags = 3;

  // Maximum query length stored in bytes. Default value: 1024 bytes.
  // Range: 256-4500 bytes. Query length more than this field value will be
  // truncated to this value. When unset, query length will be the default
  // value. Changing query length will restart the database.
  google.protobuf.Int32Value query_string_length = 4;

  // Number of query execution plans captured by Insights per minute
  // for all queries combined. Default is 5.
  google.protobuf.Int32Value query_plans_per_minute = 5;
}

// Read-replica configuration specific to MySQL databases.
message MySqlReplicaConfiguration {
  // Path to a SQL dump file in Google Cloud Storage from which the replica
  // instance is to be created. The URI is in the form gs://bucketName/fileName.
  // Compressed gzip files (.gz) are also supported.
  // Dumps have the binlog co-ordinates from which replication
  // begins. This can be accomplished by setting --master-data to 1 when using
  // mysqldump.
  string dump_file_path = 1;

  // The username for the replication connection.
  string username = 2;

  // The password for the replication connection.
  string password = 3;

  // Seconds to wait between connect retries. MySQL's default is 60 seconds.
  google.protobuf.Int32Value connect_retry_interval = 4;

  // Interval in milliseconds between replication heartbeats.
  google.protobuf.Int64Value master_heartbeat_period = 5;

  // PEM representation of the trusted CA's x509 certificate.
  string ca_certificate = 6;

  // PEM representation of the replica's x509 certificate.
  string client_certificate = 7;

  // PEM representation of the replica's private key. The corresponsing public
  // key is encoded in the client's certificate.
  string client_key = 8;

  // A list of permissible ciphers to use for SSL encryption.
  string ssl_cipher = 9;

  // Whether or not to check the primary instance's Common Name value in the
  // certificate that it sends during the SSL handshake.
  google.protobuf.BoolValue verify_server_certificate = 10;

  // This is always `sql#mysqlReplicaConfiguration`.
  string kind = 11;
}

// On-premises instance configuration.
message OnPremisesConfiguration {
  // The host and port of the on-premises instance in host:port format
  string host_port = 1;

  // This is always `sql#onPremisesConfiguration`.
  string kind = 2;

  // The username for connecting to on-premises instance.
  string username = 3;

  // The password for connecting to on-premises instance.
  string password = 4;

  // PEM representation of the trusted CA's x509 certificate.
  string ca_certificate = 5;

  // PEM representation of the replica's x509 certificate.
  string client_certificate = 6;

  // PEM representation of the replica's private key. The corresponsing public
  // key is encoded in the client's certificate.
  string client_key = 7;

  // The dump file to create the Cloud SQL replica.
  string dump_file_path = 8;

  // The reference to Cloud SQL instance if the source is Cloud SQL.
  InstanceReference source_instance = 15;
}

// Disk encryption configuration for an instance.
message DiskEncryptionConfiguration {
  // Resource name of KMS key for disk encryption
  string kms_key_name = 1;

  // This is always `sql#diskEncryptionConfiguration`.
  string kind = 2;
}

// Disk encryption status for an instance.
message DiskEncryptionStatus {
  // KMS key version used to encrypt the Cloud SQL instance resource
  string kms_key_version_name = 1;

  // This is always `sql#diskEncryptionStatus`.
  string kind = 2;
}

// An Operation resource.&nbsp;For successful operations that return an
// Operation resource, only the fields relevant to the operation are populated
// in the resource.
message Operation {
  // The type of Cloud SQL operation.
  enum SqlOperationType {
    // Unknown operation type.
    SQL_OPERATION_TYPE_UNSPECIFIED = 0;

    // Imports data into a Cloud SQL instance.
    IMPORT = 1;

    // Exports data from a Cloud SQL instance to a Cloud Storage
    // bucket.
    EXPORT = 2;

    // Creates a new Cloud SQL instance.
    CREATE = 3;

    // Updates the settings of a Cloud SQL instance.
    UPDATE = 4;

    // Deletes a Cloud SQL instance.
    DELETE = 5;

    // Restarts the Cloud SQL instance.
    RESTART = 6;

    BACKUP = 7 [deprecated = true];

    SNAPSHOT = 8 [deprecated = true];

    // Performs instance backup.
    BACKUP_VOLUME = 9;

    // Deletes an instance backup.
    DELETE_VOLUME = 10;

    // Restores an instance backup.
    RESTORE_VOLUME = 11;

    // Injects a privileged user in mysql for MOB instances.
    INJECT_USER = 12;

    // Clones a Cloud SQL instance.
    CLONE = 14;

    // Stops replication on a Cloud SQL read replica instance.
    STOP_REPLICA = 15;

    // Starts replication on a Cloud SQL read replica instance.
    START_REPLICA = 16;

    // Promotes a Cloud SQL replica instance.
    PROMOTE_REPLICA = 17;

    // Creates a Cloud SQL replica instance.
    CREATE_REPLICA = 18;

    // Creates a new user in a Cloud SQL instance.
    CREATE_USER = 19;

    // Deletes a user from a Cloud SQL instance.
    DELETE_USER = 20;

    // Updates an existing user in a Cloud SQL instance.
    UPDATE_USER = 21;

    // Creates a database in the Cloud SQL instance.
    CREATE_DATABASE = 22;

    // Deletes a database in the Cloud SQL instance.
    DELETE_DATABASE = 23;

    // Updates a database in the Cloud SQL instance.
    UPDATE_DATABASE = 24;

    // Performs failover of an HA-enabled Cloud SQL
    // failover replica.
    FAILOVER = 25;

    // Deletes the backup taken by a backup run.
    DELETE_BACKUP = 26;

    RECREATE_REPLICA = 27;

    // Truncates a general or slow log table in MySQL.
    TRUNCATE_LOG = 28;

    // Demotes the stand-alone instance to be a Cloud SQL
    // read replica for an external database server.
    DEMOTE_MASTER = 29;

    // Indicates that the instance is currently in maintenance. Maintenance
    // typically causes the instance to be unavailable for 1-3 minutes.
    MAINTENANCE = 30;

    // This field is deprecated, and will be removed in future version of API.
    ENABLE_PRIVATE_IP = 31 [deprecated = true];

    DEFER_MAINTENANCE = 32 [deprecated = true];

    // Creates clone instance.
    CREATE_CLONE = 33 [deprecated = true];

    // Reschedule maintenance to another time.
    RESCHEDULE_MAINTENANCE = 34;

    // Starts external sync of a Cloud SQL EM replica to an external primary
    // instance.
    START_EXTERNAL_SYNC = 35;

    // Recovers logs from an instance's old data disk.
    LOG_CLEANUP = 36;

    // Performs auto-restart of an HA-enabled Cloud SQL database for auto
    // recovery.
    AUTO_RESTART = 37;

    // Re-encrypts CMEK instances with latest key version.
    REENCRYPT = 38;

    // Switches the roles of the primary and replica pair. The target instance
    // should be the replica.
    SWITCHOVER = 39;

    // Acquire a lease for the setup of SQL Server Reporting Services (SSRS).
    ACQUIRE_SSRS_LEASE = 42;

    // Release a lease for the setup of SQL Server Reporting Services (SSRS).
    RELEASE_SSRS_LEASE = 43;

    // Reconfigures old primary after a promote replica operation. Effect of a
    // promote operation to the old primary is executed in this operation,
    // asynchronously from the promote replica operation executed to the
    // replica.
    RECONFIGURE_OLD_PRIMARY = 44;

    // Indicates that the instance, its read replicas, and its cascading
    // replicas are in maintenance. Maintenance typically gets initiated on
    // groups of replicas first, followed by the primary instance. For each
    // instance, maintenance typically causes the instance to be unavailable for
    // 1-3 minutes.
    CLUSTER_MAINTENANCE = 45;

    // Indicates that the instance (and any of its replicas) are currently in
    // maintenance. This is initiated as a self-service request by using SSM.
    // Maintenance typically causes the instance to be unavailable for 1-3
    // minutes.
    SELF_SERVICE_MAINTENANCE = 46;

    // Switches a primary instance to a replica. This operation runs as part of
    // a switchover operation to the original primary instance.
    SWITCHOVER_TO_REPLICA = 47;
  }

  // The status of an operation.
  enum SqlOperationStatus {
    // The state of the operation is unknown.
    SQL_OPERATION_STATUS_UNSPECIFIED = 0;

    // The operation has been queued, but has not started yet.
    PENDING = 1;

    // The operation is running.
    RUNNING = 2;

    // The operation completed.
    DONE = 3;
  }

  // This is always `sql#operation`.
  string kind = 1;

  string target_link = 2;

  // The status of an operation.
  SqlOperationStatus status = 3;

  // The email address of the user who initiated this operation.
  string user = 4;

  // The time this operation was enqueued in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp insert_time = 5;

  // The time this operation actually started in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp start_time = 6;

  // The time this operation finished in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp end_time = 7;

  // If errors occurred during processing of this operation, this field will be
  // populated.
  OperationErrors error = 8;

  // An Admin API warning message.
  ApiWarning api_warning = 19;

  // The type of the operation. Valid values are:
  // *  `CREATE`
  // *  `DELETE`
  // *  `UPDATE`
  // *  `RESTART`
  // *  `IMPORT`
  // *  `EXPORT`
  // *  `BACKUP_VOLUME`
  // *  `RESTORE_VOLUME`
  // *  `CREATE_USER`
  // *  `DELETE_USER`
  // *  `CREATE_DATABASE`
  // *  `DELETE_DATABASE`
  SqlOperationType operation_type = 9;

  // The context for import operation, if applicable.
  ImportContext import_context = 10;

  // The context for export operation, if applicable.
  ExportContext export_context = 11;

  // The context for backup operation, if applicable.
  BackupContext backup_context = 17;

  // An identifier that uniquely identifies the operation. You can use this
  // identifier to retrieve the Operations resource that has information about
  // the operation.
  string name = 12;

  // Name of the database instance related to this operation.
  string target_id = 13;

  // The URI of this resource.
  string self_link = 14;

  // The project ID of the target instance related to this operation.
  string target_project = 15;

  // The context for acquire SSRS lease operation, if applicable.
  AcquireSsrsLeaseContext acquire_ssrs_lease_context = 20;
}

// Database instance operation error.
message OperationError {
  // This is always `sql#operationError`.
  string kind = 1;

  // Identifies the specific error that occurred.
  string code = 2;

  // Additional information about the error encountered.
  string message = 3;
}

// Database instance operation errors list wrapper.
message OperationErrors {
  // This is always `sql#operationErrors`.
  string kind = 1;

  // The list of errors encountered while processing this operation.
  repeated OperationError errors = 2;
}

// Database instance local user password validation policy
message PasswordValidationPolicy {
  // The complexity choices of the password.
  enum Complexity {
    // Complexity check is not specified.
    COMPLEXITY_UNSPECIFIED = 0;

    // A combination of lowercase, uppercase, numeric, and non-alphanumeric
    // characters.
    COMPLEXITY_DEFAULT = 1;
  }

  // Minimum number of characters allowed.
  google.protobuf.Int32Value min_length = 1;

  // The complexity of the password.
  Complexity complexity = 2;

  // Number of previous passwords that cannot be reused.
  google.protobuf.Int32Value reuse_interval = 3;

  // Disallow username as a part of the password.
  google.protobuf.BoolValue disallow_username_substring = 4;

  // Minimum interval after which the password can be changed. This flag is only
  // supported for PostgreSQL.
  google.protobuf.Duration password_change_interval = 5;

  // Whether the password policy is enabled or not.
  google.protobuf.BoolValue enable_password_policy = 6;

  // This field is deprecated and will be removed in a future version of the
  // API.
  google.protobuf.BoolValue disallow_compromised_credentials = 7
      [deprecated = true];
}

// Operations list response.
message OperationsListResponse {
  // This is always `sql#operationsList`.
  string kind = 1;

  // List of operation resources.
  repeated Operation items = 2;

  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 3;
}

// Read-replica configuration for connecting to the primary instance.
message ReplicaConfiguration {
  // This is always `sql#replicaConfiguration`.
  string kind = 1;

  // MySQL specific configuration when replicating from a MySQL on-premises
  // primary instance. Replication configuration information such as the
  // username, password, certificates, and keys are not stored in the instance
  // metadata. The configuration information is used only to set up the
  // replication connection and is stored by MySQL in a file named
  // `master.info` in the data directory.
  MySqlReplicaConfiguration mysql_replica_configuration = 2;

  // Specifies if the replica is the failover target. If the field is set to
  // `true` the replica will be designated as a failover replica. In case the
  // primary instance fails, the replica instance will be promoted as the new
  // primary instance. Only one replica can be specified as failover target, and
  // the replica has to be in different zone with the primary instance.
  google.protobuf.BoolValue failover_target = 3;

  // Optional. Specifies if a SQL Server replica is a cascadable replica. A
  // cascadable replica is a SQL Server cross region replica that supports
  // replica(s) under it.
  google.protobuf.BoolValue cascadable_replica = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Database instance restore from backup context.
// Backup context contains source instance id and project id.
message RestoreBackupContext {
  // This is always `sql#restoreBackupContext`.
  string kind = 1;

  // The ID of the backup run to restore from.
  int64 backup_run_id = 2;

  // The ID of the instance that the backup was taken from.
  string instance_id = 3;

  // The full project ID of the source instance.
  string project = 4;
}

// Instance rotate server CA context.
message RotateServerCaContext {
  // This is always `sql#rotateServerCaContext`.
  string kind = 1;

  // The fingerprint of the next version to be rotated to. If left unspecified,
  // will be rotated to the most recently added server CA version.
  string next_version = 2;
}

// Data cache configurations.
message DataCacheConfig {
  // Whether data cache is enabled for the instance.
  bool data_cache_enabled = 1;
}

// Database instance settings.
message Settings {
  // Specifies when the instance is activated.
  enum SqlActivationPolicy {
    // Unknown activation plan.
    SQL_ACTIVATION_POLICY_UNSPECIFIED = 0;

    // The instance is always up and running.
    ALWAYS = 1;

    // The instance never starts.
    NEVER = 2;

    // The instance starts upon receiving requests.
    ON_DEMAND = 3 [deprecated = true];
  }

  // The edition of the instance, can be ENTERPRISE or ENTERPRISE_PLUS.
  enum Edition {
    // The instance did not specify the edition.
    EDITION_UNSPECIFIED = 0;

    // The instance is an enterprise edition.
    ENTERPRISE = 2;

    // The instance is an Enterprise Plus edition.
    ENTERPRISE_PLUS = 3;
  }

  // The options for enforcing Cloud SQL connectors in the instance.
  enum ConnectorEnforcement {
    // The requirement for Cloud SQL connectors is unknown.
    CONNECTOR_ENFORCEMENT_UNSPECIFIED = 0;

    // Do not require Cloud SQL connectors.
    NOT_REQUIRED = 1;

    // Require all connections to use Cloud SQL connectors, including the
    // Cloud SQL Auth Proxy and Cloud SQL Java, Python, and Go connectors.
    // Note: This disables all existing authorized networks.
    REQUIRED = 2;
  }

  // The version of instance settings. This is a required field for update
  // method to make sure concurrent updates are handled properly. During update,
  // use the most recent settingsVersion value for this instance and do not try
  // to update this value.
  google.protobuf.Int64Value settings_version = 1;

  // The App Engine app IDs that can access this instance.
  // (Deprecated) Applied to First Generation instances only.
  repeated string authorized_gae_applications = 2 [deprecated = true];

  // The tier (or machine type) for this instance, for example
  // `db-custom-1-3840`. WARNING: Changing this restarts the instance.
  string tier = 3;

  // This is always `sql#settings`.
  string kind = 4;

  // User-provided labels, represented as a dictionary where each label is a
  // single key value pair.
  map<string, string> user_labels = 5;

  // Availability type. Potential values:
  // *  `ZONAL`: The instance serves data from only one zone. Outages in that
  // zone affect data accessibility.
  // *  `REGIONAL`: The instance can serve data from more than one zone in a
  // region (it is highly available)./
  //
  // For more information, see [Overview of the High Availability
  // Configuration](https://cloud.google.com/sql/docs/mysql/high-availability).
  SqlAvailabilityType availability_type = 6;

  // The pricing plan for this instance. This can be either `PER_USE` or
  // `PACKAGE`. Only `PER_USE` is supported for Second Generation instances.
  SqlPricingPlan pricing_plan = 7;

  // The type of replication this instance uses. This can be either
  // `ASYNCHRONOUS` or `SYNCHRONOUS`. (Deprecated) This property was only
  // applicable to First Generation instances.
  SqlReplicationType replication_type = 8 [deprecated = true];

  // The maximum size to which storage capacity can be automatically increased.
  // The default value is 0, which specifies that there is no limit.
  google.protobuf.Int64Value storage_auto_resize_limit = 9;

  // The activation policy specifies when the instance is activated; it is
  // applicable only when the instance state is RUNNABLE. Valid values:
  // *  `ALWAYS`: The instance is on, and remains so even in the absence of
  // connection requests.
  // *  `NEVER`: The instance is off; it is not activated, even if a
  // connection request arrives.
  SqlActivationPolicy activation_policy = 10;

  // The settings for IP Management. This allows to enable or disable the
  // instance IP and manage which external networks can connect to the instance.
  // The IPv4 address cannot be disabled for Second Generation instances.
  IpConfiguration ip_configuration = 11;

  // Configuration to increase storage size automatically. The default value is
  // true.
  google.protobuf.BoolValue storage_auto_resize = 12;

  // The location preference settings. This allows the instance to be located as
  // near as possible to either an App Engine app or Compute Engine zone for
  // better performance. App Engine co-location was only applicable to First
  // Generation instances.
  LocationPreference location_preference = 13;

  // The database flags passed to the instance at startup.
  repeated DatabaseFlags database_flags = 14;

  // The type of data disk: `PD_SSD` (default) or `PD_HDD`. Not used for
  // First Generation instances.
  SqlDataDiskType data_disk_type = 15;

  // The maintenance window for this instance. This specifies when the instance
  // can be restarted for maintenance purposes.
  MaintenanceWindow maintenance_window = 16;

  // The daily backup configuration for the instance.
  BackupConfiguration backup_configuration = 17;

  // Configuration specific to read replica instances. Indicates whether
  // replication is enabled or not. WARNING: Changing this restarts the
  // instance.
  google.protobuf.BoolValue database_replication_enabled = 18;

  // Configuration specific to read replica instances. Indicates whether
  // database flags for crash-safe replication are enabled. This property was
  // only applicable to First Generation instances.
  google.protobuf.BoolValue crash_safe_replication_enabled = 19
      [deprecated = true];

  // The size of data disk, in GB. The data disk size minimum is 10GB.
  google.protobuf.Int64Value data_disk_size_gb = 20;

  // Active Directory configuration, relevant only for Cloud SQL for SQL Server.
  SqlActiveDirectoryConfig active_directory_config = 22;

  // The name of server Instance collation.
  string collation = 23;

  // Deny maintenance periods
  repeated DenyMaintenancePeriod deny_maintenance_periods = 24;

  // Insights configuration, for now relevant only for Postgres.
  InsightsConfig insights_config = 25;

  // The local user password validation policy of the instance.
  PasswordValidationPolicy password_validation_policy = 27;

  // SQL Server specific audit configuration.
  SqlServerAuditConfig sql_server_audit_config = 29;

  // Optional. The edition of the instance.
  Edition edition = 38 [(google.api.field_behavior) = OPTIONAL];

  // Specifies if connections must use Cloud SQL connectors.
  // Option values include the following: `NOT_REQUIRED` (Cloud SQL instances
  // can be connected without Cloud SQL
  // Connectors) and `REQUIRED` (Only allow connections that use Cloud SQL
  // Connectors)
  //
  // Note that using REQUIRED disables all existing authorized networks. If
  // this field is not specified when creating a new instance, NOT_REQUIRED is
  // used. If this field is not specified when patching or updating an existing
  // instance, it is left unchanged in the instance.
  ConnectorEnforcement connector_enforcement = 32;

  // Configuration to protect against accidental instance deletion.
  google.protobuf.BoolValue deletion_protection_enabled = 33;

  // Server timezone, relevant only for Cloud SQL for SQL Server.
  string time_zone = 34;

  // Specifies advanced machine configuration for the instances relevant only
  // for SQL Server.
  AdvancedMachineFeatures advanced_machine_features = 35;

  // Configuration for data cache.
  DataCacheConfig data_cache_config = 37;

  // Optional. When this parameter is set to true, Cloud SQL instances can
  // connect to Vertex AI to pass requests for real-time predictions and
  // insights to the AI. The default value is false. This applies only to Cloud
  // SQL for PostgreSQL instances.
  google.protobuf.BoolValue enable_google_ml_integration = 40
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. By default, Cloud SQL instances have schema extraction disabled
  // for Dataplex. When this parameter is set to true, schema extraction for
  // Dataplex on Cloud SQL instances is activated.
  google.protobuf.BoolValue enable_dataplex_integration = 41
      [(google.api.field_behavior) = OPTIONAL];
}

// Specifies options for controlling advanced machine features.
message AdvancedMachineFeatures {
  // The number of threads per physical core.
  int32 threads_per_core = 1;
}

// SslCerts Resource
message SslCert {
  // This is always `sql#sslCert`.
  string kind = 1;

  // Serial number, as extracted from the certificate.
  string cert_serial_number = 2;

  // PEM representation.
  string cert = 3;

  // The time when the certificate was created in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp create_time = 4;

  // User supplied name.  Constrained to [a-zA-Z.-_ ]+.
  string common_name = 5;

  // The time when the certificate expires in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp expiration_time = 6;

  // Sha1 Fingerprint.
  string sha1_fingerprint = 7;

  // Name of the database instance.
  string instance = 8;

  // The URI of this resource.
  string self_link = 9;
}

// SslCertDetail.
message SslCertDetail {
  // The public information about the cert.
  SslCert cert_info = 1;

  // The private key for the client cert, in pem format.  Keep private in order
  // to protect your security.
  string cert_private_key = 2;
}

// SslCerts create ephemeral certificate request.
message SslCertsCreateEphemeralRequest {
  // PEM encoded public key to include in the signed certificate.
  string public_key = 1;

  // Access token to include in the signed certificate.
  string access_token = 2;
}

// SslCerts insert request.
message SslCertsInsertRequest {
  // User supplied name.  Must be a distinct name from the other certificates
  // for this instance.
  string common_name = 1;
}

// Reschedule options for maintenance windows.
message SqlInstancesRescheduleMaintenanceRequestBody {
  enum RescheduleType {
    RESCHEDULE_TYPE_UNSPECIFIED = 0;

    // Reschedules maintenance to happen now (within 5 minutes).
    IMMEDIATE = 1;

    // Reschedules maintenance to occur within one week from the originally
    // scheduled day and time.
    NEXT_AVAILABLE_WINDOW = 2;

    // Reschedules maintenance to a specific time and day.
    SPECIFIC_TIME = 3;
  }

  message Reschedule {
    // Required. The type of the reschedule.
    RescheduleType reschedule_type = 1;

    // Optional. Timestamp when the maintenance shall be rescheduled to if
    // reschedule_type=SPECIFIC_TIME, in
    // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
    // `2012-11-15T16:19:00.094Z`.
    google.protobuf.Timestamp schedule_time = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The type of the reschedule the user wants.
  Reschedule reschedule = 3;
}

// SslCert insert response.
message SslCertsInsertResponse {
  // This is always `sql#sslCertsInsert`.
  string kind = 1;

  // The operation to track the ssl certs insert request.
  Operation operation = 2;

  // The server Certificate Authority's certificate.  If this is missing you can
  // force a new one to be generated by calling resetSslConfig method on
  // instances resource.
  SslCert server_ca_cert = 3;

  // The new client certificate and private key.
  SslCertDetail client_cert = 4;
}

// SslCerts list response.
message SslCertsListResponse {
  // This is always `sql#sslCertsList`.
  string kind = 1;

  // List of client certificates for the instance.
  repeated SslCert items = 2;
}

// Database Instance truncate log context.
message TruncateLogContext {
  // This is always `sql#truncateLogContext`.
  string kind = 1;

  // The type of log to truncate. Valid values are `MYSQL_GENERAL_TABLE` and
  // `MYSQL_SLOW_TABLE`.
  string log_type = 2;
}

// Active Directory configuration, relevant only for Cloud SQL for SQL Server.
message SqlActiveDirectoryConfig {
  // This is always sql#activeDirectoryConfig.
  string kind = 1;

  // The name of the domain (e.g., mydomain.com).
  string domain = 2;
}

// SQL Server specific audit configuration.
message SqlServerAuditConfig {
  // This is always sql#sqlServerAuditConfig
  string kind = 1;

  // The name of the destination bucket (e.g., gs://mybucket).
  string bucket = 2;

  // How long to keep generated audit files.
  google.protobuf.Duration retention_interval = 3;

  // How often to upload generated audit files.
  google.protobuf.Duration upload_interval = 4;
}

// The status of a backup run.
enum SqlBackupRunStatus {
  // The status of the run is unknown.
  SQL_BACKUP_RUN_STATUS_UNSPECIFIED = 0;

  // The backup operation was enqueued.
  ENQUEUED = 1;

  // The backup is overdue across a given backup window. Indicates a
  // problem. Example: Long-running operation in progress during
  // the whole window.
  OVERDUE = 2;

  // The backup is in progress.
  RUNNING = 3;

  // The backup failed.
  FAILED = 4;

  // The backup was successful.
  SUCCESSFUL = 5;

  // The backup was skipped (without problems) for a given backup
  // window. Example: Instance was idle.
  SKIPPED = 6;

  // The backup is about to be deleted.
  DELETION_PENDING = 7;

  // The backup deletion failed.
  DELETION_FAILED = 8;

  // The backup has been deleted.
  DELETED = 9;
}

enum SqlBackupRunType {
  // This is an unknown BackupRun type.
  SQL_BACKUP_RUN_TYPE_UNSPECIFIED = 0;

  // The backup schedule automatically triggers a backup.
  AUTOMATED = 1;

  // The user manually triggers a backup.
  ON_DEMAND = 2;
}

// Defines the supported backup kinds
enum SqlBackupKind {
  // This is an unknown BackupKind.
  SQL_BACKUP_KIND_UNSPECIFIED = 0;

  // The snapshot based backups
  SNAPSHOT = 1;

  // Physical backups
  PHYSICAL = 2;
}

enum SqlBackendType {
  // This is an unknown backend type for instance.
  SQL_BACKEND_TYPE_UNSPECIFIED = 0;

  // V1 speckle instance.
  FIRST_GEN = 1 [deprecated = true];

  // V2 speckle instance.
  SECOND_GEN = 2;

  // On premises instance.
  EXTERNAL = 3;
}

enum SqlIpAddressType {
  // This is an unknown IP address type.
  SQL_IP_ADDRESS_TYPE_UNSPECIFIED = 0;

  // IP address the customer is supposed to connect to. Usually this is the
  // load balancer's IP address
  PRIMARY = 1;

  // Source IP address of the connection a read replica establishes to its
  // external primary instance. This IP address can be allowlisted by the
  // customer in case it has a firewall that filters incoming connection to its
  // on premises primary instance.
  OUTGOING = 2;

  // Private IP used when using private IPs and network peering.
  PRIVATE = 3;

  // V1 IP of a migrated instance. We want the user to
  // decommission this IP as soon as the migration is complete.
  // Note: V1 instances with V1 ip addresses will be counted as PRIMARY.
  MIGRATED_1ST_GEN = 4;
}

enum SqlInstanceType {
  // This is an unknown Cloud SQL instance type.
  SQL_INSTANCE_TYPE_UNSPECIFIED = 0;

  // A regular Cloud SQL instance that is not replicating from a primary
  // instance.
  CLOUD_SQL_INSTANCE = 1;

  // An instance running on the customer's premises that is not managed by
  // Cloud SQL.
  ON_PREMISES_INSTANCE = 2;

  // A Cloud SQL instance acting as a read-replica.
  READ_REPLICA_INSTANCE = 3;
}

// The database engine type and version.
enum SqlDatabaseVersion {
  // This is an unknown database version.
  SQL_DATABASE_VERSION_UNSPECIFIED = 0;

  // The database version is MySQL 5.1.
  MYSQL_5_1 = 2 [deprecated = true];

  // The database version is MySQL 5.5.
  MYSQL_5_5 = 3 [deprecated = true];

  // The database version is MySQL 5.6.
  MYSQL_5_6 = 5;

  // The database version is MySQL 5.7.
  MYSQL_5_7 = 6;

  // The database version is SQL Server 2017 Standard.
  SQLSERVER_2017_STANDARD = 11;

  // The database version is SQL Server 2017 Enterprise.
  SQLSERVER_2017_ENTERPRISE = 14;

  // The database version is SQL Server 2017 Express.
  SQLSERVER_2017_EXPRESS = 15;

  // The database version is SQL Server 2017 Web.
  SQLSERVER_2017_WEB = 16;

  // The database version is PostgreSQL 9.6.
  POSTGRES_9_6 = 9;

  // The database version is PostgreSQL 10.
  POSTGRES_10 = 18;

  // The database version is PostgreSQL 11.
  POSTGRES_11 = 10;

  // The database version is PostgreSQL 12.
  POSTGRES_12 = 19;

  // The database version is PostgreSQL 13.
  POSTGRES_13 = 23;

  // The database version is PostgreSQL 14.
  POSTGRES_14 = 110;

  // The database version is PostgreSQL 15.
  POSTGRES_15 = 172;

  // The database version is PostgreSQL 16.
  POSTGRES_16 = 272;

  // The database version is MySQL 8.
  MYSQL_8_0 = 20;

  // The database major version is MySQL 8.0 and the minor version is 18.
  MYSQL_8_0_18 = 41;

  // The database major version is MySQL 8.0 and the minor version is 26.
  MYSQL_8_0_26 = 85;

  // The database major version is MySQL 8.0 and the minor version is 27.
  MYSQL_8_0_27 = 111;

  // The database major version is MySQL 8.0 and the minor version is 28.
  MYSQL_8_0_28 = 132;

  // The database major version is MySQL 8.0 and the minor version is 29.
  MYSQL_8_0_29 = 148 [deprecated = true];

  // The database major version is MySQL 8.0 and the minor version is 30.
  MYSQL_8_0_30 = 174;

  // The database major version is MySQL 8.0 and the minor version is 31.
  MYSQL_8_0_31 = 197;

  // The database major version is MySQL 8.0 and the minor version is 32.
  MYSQL_8_0_32 = 213;

  // The database major version is MySQL 8.0 and the minor version is 33.
  MYSQL_8_0_33 = 238;

  // The database major version is MySQL 8.0 and the minor version is 34.
  MYSQL_8_0_34 = 239;

  // The database major version is MySQL 8.0 and the minor version is 35.
  MYSQL_8_0_35 = 240;

  // The database major version is MySQL 8.0 and the minor version is 36.
  MYSQL_8_0_36 = 241;

  // The database major version is MySQL 8.0 and the minor version is 37.
  MYSQL_8_0_37 = 355;

  // The database major version is MySQL 8.0 and the minor version is 38.
  MYSQL_8_0_38 = 356;

  // The database major version is MySQL 8.0 and the minor version is 39.
  MYSQL_8_0_39 = 357;

  // The database major version is MySQL 8.0 and the minor version is 40.
  MYSQL_8_0_40 = 358;

  // The database version is MySQL 8.4.
  MYSQL_8_4 = 398;

  // The database version is MySQL 8.4 and the patch version is 0.
  MYSQL_8_4_0 = 399;

  // The database version is SQL Server 2019 Standard.
  SQLSERVER_2019_STANDARD = 26;

  // The database version is SQL Server 2019 Enterprise.
  SQLSERVER_2019_ENTERPRISE = 27;

  // The database version is SQL Server 2019 Express.
  SQLSERVER_2019_EXPRESS = 28;

  // The database version is SQL Server 2019 Web.
  SQLSERVER_2019_WEB = 29;

  // The database version is SQL Server 2022 Standard.
  SQLSERVER_2022_STANDARD = 199;

  // The database version is SQL Server 2022 Enterprise.
  SQLSERVER_2022_ENTERPRISE = 200;

  // The database version is SQL Server 2022 Express.
  SQLSERVER_2022_EXPRESS = 201;

  // The database version is SQL Server 2022 Web.
  SQLSERVER_2022_WEB = 202;
}

// The suspension reason of the database instance if the state is SUSPENDED.
enum SqlSuspensionReason {
  // This is an unknown suspension reason.
  SQL_SUSPENSION_REASON_UNSPECIFIED = 0;

  // The instance is suspended due to billing issues (for example:, GCP account
  // issue)
  BILLING_ISSUE = 2;

  // The instance is suspended due to illegal content (for example:, child
  // pornography, copyrighted material, etc.).
  LEGAL_ISSUE = 3;

  // The instance is causing operational issues (for example:, causing the
  // database to crash).
  OPERATIONAL_ISSUE = 4;

  // The KMS key used by the instance is either revoked or denied access to
  KMS_KEY_ISSUE = 5;
}

// The pricing plan for this instance.
enum SqlPricingPlan {
  // This is an unknown pricing plan for this instance.
  SQL_PRICING_PLAN_UNSPECIFIED = 0;

  // The instance is billed at a monthly flat rate.
  PACKAGE = 1;

  // The instance is billed per usage.
  PER_USE = 2;
}

enum SqlReplicationType {
  // This is an unknown replication type for a Cloud SQL instance.
  SQL_REPLICATION_TYPE_UNSPECIFIED = 0;

  // The synchronous replication mode for First Generation instances. It is the
  // default value.
  SYNCHRONOUS = 1;

  // The asynchronous replication mode for First Generation instances. It
  // provides a slight performance gain, but if an outage occurs while this
  // option is set to asynchronous, you can lose up to a few seconds of updates
  // to your data.
  ASYNCHRONOUS = 2;
}

// The type of disk that is used for a v2 instance to use.
enum SqlDataDiskType {
  // This is an unknown data disk type.
  SQL_DATA_DISK_TYPE_UNSPECIFIED = 0;

  // An SSD data disk.
  PD_SSD = 1;

  // An HDD data disk.
  PD_HDD = 2;

  // This field is deprecated and will be removed from a future version of the
  // API.
  OBSOLETE_LOCAL_SSD = 3 [deprecated = true];
}

// The availability type of the given Cloud SQL instance.
enum SqlAvailabilityType {
  // This is an unknown Availability type.
  SQL_AVAILABILITY_TYPE_UNSPECIFIED = 0;

  // Zonal available instance.
  ZONAL = 1;

  // Regional available instance.
  REGIONAL = 2;
}

enum SqlUpdateTrack {
  // This is an unknown maintenance timing preference.
  SQL_UPDATE_TRACK_UNSPECIFIED = 0;

  // For instance update that requires a restart, this update track indicates
  // your instance prefer to restart for new version early in maintenance
  // window.
  canary = 1;

  // For instance update that requires a restart, this update track indicates
  // your instance prefer to let Cloud SQL choose the timing of restart (within
  // its Maintenance window, if applicable).
  stable = 2;

  // For instance update that requires a restart, this update track indicates
  // your instance prefer to let Cloud SQL choose the timing of restart (within
  // its Maintenance window, if applicable) to be at least 5 weeks after the
  // notification.
  week5 = 3;
}

// Acquire SSRS lease context.
message AcquireSsrsLeaseContext {
  // The username to be used as the setup login to connect to the database
  // server for SSRS setup.
  optional string setup_login = 1;

  // The username to be used as the service login to connect to the report
  // database for SSRS setup.
  optional string service_login = 2;

  // The report database to be used for the SSRS setup.
  optional string report_database = 3;

  // Lease duration needed for the SSRS setup.
  optional google.protobuf.Duration duration = 4;
}

enum SqlFlagType {
  // This is an unknown flag type.
  SQL_FLAG_TYPE_UNSPECIFIED = 0;

  // Boolean type flag.
  BOOLEAN = 1;

  // String type flag.
  STRING = 2;

  // Integer type flag.
  INTEGER = 3;

  // Flag type used for a server startup option.
  NONE = 4;

  // Type introduced specially for MySQL TimeZone offset. Accept a string value
  // with the format [-12:59, 13:00].
  MYSQL_TIMEZONE_OFFSET = 5;

  // Float type flag.
  FLOAT = 6;

  // Comma-separated list of the strings in a SqlFlagType enum.
  REPEATED_STRING = 7;
}
