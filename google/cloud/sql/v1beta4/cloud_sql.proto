// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1beta4;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/sql/v1beta4/cloud_sql_resources.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/sql/apiv1beta4/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlServiceProto";
option java_package = "com.google.cloud.sql.v1beta4";

service SqlBackupRunsService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Deletes the backup taken by a backup run.
  rpc Delete(SqlBackupRunsDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/sql/v1beta4/projects/{project}/instances/{instance}/backupRuns/{id}"
    };
  }

  // Retrieves a resource containing information about a backup run.
  rpc Get(SqlBackupRunsGetRequest) returns (BackupRun) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/backupRuns/{id}"
    };
  }

  // Creates a new backup run on demand.
  rpc Insert(SqlBackupRunsInsertRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/backupRuns"
      body: "body"
    };
  }

  // Lists all backup runs associated with the project or a given instance and
  // configuration in the reverse chronological order of the backup initiation
  // time.
  rpc List(SqlBackupRunsListRequest) returns (BackupRunsListResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/backupRuns"
    };
  }
}

service SqlDatabasesService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Deletes a database from a Cloud SQL instance.
  rpc Delete(SqlDatabasesDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}"
    };
  }

  // Retrieves a resource containing information about a database inside a Cloud
  // SQL instance.
  rpc Get(SqlDatabasesGetRequest) returns (Database) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}"
    };
  }

  // Inserts a resource containing information about a database inside a Cloud
  // SQL instance.
  //
  // **Note:** You can't modify the default character set and collation.
  rpc Insert(SqlDatabasesInsertRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/databases"
      body: "body"
    };
  }

  // Lists databases in the specified Cloud SQL instance.
  rpc List(SqlDatabasesListRequest) returns (DatabasesListResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/databases"
    };
  }

  // Partially updates a resource containing information about a database inside
  // a Cloud SQL instance. This method supports patch semantics.
  rpc Patch(SqlDatabasesUpdateRequest) returns (Operation) {
    option (google.api.http) = {
      patch: "/sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}"
      body: "body"
    };
  }

  // Updates a resource containing information about a database inside a Cloud
  // SQL instance.
  rpc Update(SqlDatabasesUpdateRequest) returns (Operation) {
    option (google.api.http) = {
      put: "/sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}"
      body: "body"
    };
  }
}

service SqlFlagsService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Lists all available database flags for Cloud SQL instances.
  rpc List(SqlFlagsListRequest) returns (FlagsListResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/flags"
    };
  }
}

service SqlInstancesService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Add a new trusted Certificate Authority (CA) version for the specified
  // instance. Required to prepare for a certificate rotation. If a CA version
  // was previously added but never used in a certificate rotation, this
  // operation replaces that version. There cannot be more than one CA version
  // waiting to be rotated in.
  rpc AddServerCa(SqlInstancesAddServerCaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/addServerCa"
    };
  }

  // Creates a Cloud SQL instance as a clone of the source instance. Using this
  // operation might cause your instance to restart.
  rpc Clone(SqlInstancesCloneRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/clone"
      body: "body"
    };
  }

  // Deletes a Cloud SQL instance.
  rpc Delete(SqlInstancesDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/sql/v1beta4/projects/{project}/instances/{instance}"
    };
  }

  // Demotes the stand-alone instance to be a Cloud SQL read replica for an
  // external database server.
  rpc DemoteMaster(SqlInstancesDemoteMasterRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/demoteMaster"
      body: "body"
    };
  }

  // Demotes an existing standalone instance to be a Cloud SQL read replica
  // for an external database server.
  rpc Demote(SqlInstancesDemoteRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/demote"
      body: "body"
    };
  }

  // Exports data from a Cloud SQL instance to a Cloud Storage bucket as a SQL
  // dump or CSV file.
  rpc Export(SqlInstancesExportRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/export"
      body: "body"
    };
  }

  // Initiates a manual failover of a high availability (HA) primary instance
  // to a standby instance, which becomes the primary instance. Users are
  // then rerouted to the new primary. For more information, see the
  // [Overview of high
  // availability](https://cloud.google.com/sql/docs/mysql/high-availability)
  // page in the Cloud SQL documentation.
  // If using Legacy HA (MySQL only), this causes the instance to failover to
  // its failover replica instance.
  rpc Failover(SqlInstancesFailoverRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/failover"
      body: "body"
    };
  }

  // Reencrypt CMEK instance with latest key version.
  rpc Reencrypt(SqlInstancesReencryptRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/reencrypt"
      body: "body"
    };
  }

  // Retrieves a resource containing information about a Cloud SQL instance.
  rpc Get(SqlInstancesGetRequest) returns (DatabaseInstance) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}"
    };
  }

  // Imports data into a Cloud SQL instance from a SQL dump  or CSV file in
  // Cloud Storage.
  rpc Import(SqlInstancesImportRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/import"
      body: "body"
    };
  }

  // Creates a new Cloud SQL instance.
  rpc Insert(SqlInstancesInsertRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances"
      body: "body"
    };
  }

  // Lists instances under a given project.
  rpc List(SqlInstancesListRequest) returns (InstancesListResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances"
    };
  }

  // Lists all of the trusted Certificate Authorities (CAs) for the specified
  // instance. There can be up to three CAs listed: the CA that was used to sign
  // the certificate that is currently in use, a CA that has been added but not
  // yet used to sign a certificate, and a CA used to sign a certificate that
  // has previously rotated out.
  rpc ListServerCas(SqlInstancesListServerCasRequest)
      returns (InstancesListServerCasResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/listServerCas"
    };
  }

  // Partially updates settings of a Cloud SQL instance by merging the request
  // with the current configuration. This method supports patch semantics.
  rpc Patch(SqlInstancesPatchRequest) returns (Operation) {
    option (google.api.http) = {
      patch: "/sql/v1beta4/projects/{project}/instances/{instance}"
      body: "body"
    };
  }

  // Promotes the read replica instance to be an independent Cloud SQL
  // primary instance.
  // Using this operation might cause your instance to restart.
  rpc PromoteReplica(SqlInstancesPromoteReplicaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/promoteReplica"
    };
  }

  // Switches over from the primary instance to the designated DR replica
  // instance.
  rpc Switchover(SqlInstancesSwitchoverRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/switchover"
    };
  }

  // Deletes all client certificates and generates a new server SSL certificate
  // for the instance.
  rpc ResetSslConfig(SqlInstancesResetSslConfigRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/resetSslConfig"
    };
  }

  // Restarts a Cloud SQL instance.
  rpc Restart(SqlInstancesRestartRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/restart"
    };
  }

  // Restores a backup of a Cloud SQL instance. Using this operation might cause
  // your instance to restart.
  rpc RestoreBackup(SqlInstancesRestoreBackupRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/restoreBackup"
      body: "body"
    };
  }

  // Rotates the server certificate to one signed by the Certificate Authority
  // (CA) version previously added with the addServerCA method.
  rpc RotateServerCa(SqlInstancesRotateServerCaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/rotateServerCa"
      body: "body"
    };
  }

  // Starts the replication in the read replica instance.
  rpc StartReplica(SqlInstancesStartReplicaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/startReplica"
    };
  }

  // Stops the replication in the read replica instance.
  rpc StopReplica(SqlInstancesStopReplicaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/stopReplica"
    };
  }

  // Truncate MySQL general and slow query log tables
  // MySQL only.
  rpc TruncateLog(SqlInstancesTruncateLogRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/truncateLog"
      body: "body"
    };
  }

  // Updates settings of a Cloud SQL instance. Using this operation might cause
  // your instance to restart.
  rpc Update(SqlInstancesUpdateRequest) returns (Operation) {
    option (google.api.http) = {
      put: "/sql/v1beta4/projects/{project}/instances/{instance}"
      body: "body"
    };
  }

  // Generates a short-lived X509 certificate containing the provided public key
  // and signed by a private key specific to the target instance. Users may use
  // the certificate to authenticate as themselves when connecting to the
  // database.
  rpc CreateEphemeral(SqlInstancesCreateEphemeralCertRequest)
      returns (SslCert) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/createEphemeral"
      body: "body"
    };
  }

  // Reschedules the maintenance on the given instance.
  rpc RescheduleMaintenance(SqlInstancesRescheduleMaintenanceRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/rescheduleMaintenance"
      body: "body"
    };
  }

  // Verify External primary instance external sync settings.
  rpc VerifyExternalSyncSettings(SqlInstancesVerifyExternalSyncSettingsRequest)
      returns (SqlInstancesVerifyExternalSyncSettingsResponse) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/verifyExternalSyncSettings"
      body: "*"
    };
  }

  // Start External primary instance migration.
  rpc StartExternalSync(SqlInstancesStartExternalSyncRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/startExternalSync"
      body: "*"
    };
  }

  // Perform Disk Shrink on primary instance.
  rpc PerformDiskShrink(SqlInstancesPerformDiskShrinkRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/performDiskShrink"
      body: "body"
    };
  }

  // Get Disk Shrink Config for a given instance.
  rpc GetDiskShrinkConfig(SqlInstancesGetDiskShrinkConfigRequest)
      returns (SqlInstancesGetDiskShrinkConfigResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/getDiskShrinkConfig"
    };
  }

  // Reset Replica Size to primary instance disk size.
  rpc ResetReplicaSize(SqlInstancesResetReplicaSizeRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/resetReplicaSize"
      body: "*"
    };
  }

  // Get Latest Recovery Time for a given instance.
  rpc GetLatestRecoveryTime(SqlInstancesGetLatestRecoveryTimeRequest)
      returns (SqlInstancesGetLatestRecoveryTimeResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/getLatestRecoveryTime"
    };
  }

  // Acquire a lease for the setup of SQL Server Reporting Services (SSRS).
  rpc AcquireSsrsLease(SqlInstancesAcquireSsrsLeaseRequest)
      returns (SqlInstancesAcquireSsrsLeaseResponse) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/acquireSsrsLease"
      body: "body"
    };
  }

  // Release a lease for the setup of SQL Server Reporting Services (SSRS).
  rpc ReleaseSsrsLease(SqlInstancesReleaseSsrsLeaseRequest)
      returns (SqlInstancesReleaseSsrsLeaseResponse) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/releaseSsrsLease"
    };
  }
}

service SqlOperationsService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Retrieves an instance operation that has been performed on an instance.
  rpc Get(SqlOperationsGetRequest) returns (Operation) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/operations/{operation}"
    };
  }

  // Lists all instance operations that have been performed on the given Cloud
  // SQL instance in the reverse chronological order of the start time.
  rpc List(SqlOperationsListRequest) returns (OperationsListResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/operations"
    };
  }

  // Cancels an instance operation that has been performed on an instance.
  rpc Cancel(SqlOperationsCancelRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/operations/{operation}/cancel"
    };
  }
}

service SqlSslCertsService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Deletes the SSL certificate. For First Generation instances, the
  // certificate remains valid until the instance is restarted.
  rpc Delete(SqlSslCertsDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/sql/v1beta4/projects/{project}/instances/{instance}/sslCerts/{sha1_fingerprint}"
    };
  }

  // Retrieves a particular SSL certificate.  Does not include the private key
  // (required for usage).  The private key must be saved from the response to
  // initial creation.
  rpc Get(SqlSslCertsGetRequest) returns (SslCert) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/sslCerts/{sha1_fingerprint}"
    };
  }

  // Creates an SSL certificate and returns it along with the private key and
  // server certificate authority.  The new certificate will not be usable until
  // the instance is restarted.
  rpc Insert(SqlSslCertsInsertRequest) returns (SslCertsInsertResponse) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/sslCerts"
      body: "body"
    };
  }

  // Lists all of the current SSL certificates for the instance.
  rpc List(SqlSslCertsListRequest) returns (SslCertsListResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/sslCerts"
    };
  }
}

message SqlBackupRunsDeleteRequest {
  // The ID of the backup run to delete. To find a backup run ID, use the
  // [list](https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1beta4/backupRuns/list)
  // method.
  int64 id = 1;

  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;
}

message SqlBackupRunsGetRequest {
  // The ID of this backup run.
  int64 id = 1;

  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;
}

message SqlBackupRunsInsertRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  BackupRun body = 100;
}

message SqlBackupRunsListRequest {
  // Cloud SQL instance ID, or "-" for all instances. This does not include
  // the project ID.
  string instance = 1;

  // Maximum number of backup runs per response.
  int32 max_results = 2;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 3;

  // Project ID of the project that contains the instance.
  string project = 4;
}

message SqlDatabasesDeleteRequest {
  // Name of the database to be deleted in the instance.
  string database = 1;

  // Database instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;
}

message SqlDatabasesGetRequest {
  // Name of the database in the instance.
  string database = 1;

  // Database instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;
}

message SqlDatabasesInsertRequest {
  // Database instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  Database body = 100;
}

message SqlDatabasesListRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

message SqlDatabasesUpdateRequest {
  // Name of the database to be updated in the instance.
  string database = 1;

  // Database instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;

  Database body = 100;
}

message SqlFlagsListRequest {
  // Database type and version you want to retrieve flags for. By default, this
  // method returns flags for all database types and versions.
  string database_version = 1;
}

message SqlInstancesAddServerCaRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

message SqlInstancesCloneRequest {
  // The ID of the Cloud SQL instance to be cloned (source). This does not
  // include the project ID.
  string instance = 1;

  // Project ID of the source as well as the clone Cloud SQL instance.
  string project = 2;

  InstancesCloneRequest body = 100;
}

message SqlInstancesDeleteRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance to be deleted.
  string project = 2;
}

message SqlInstancesDemoteMasterRequest {
  // Cloud SQL instance name.
  string instance = 1;

  // ID of the project that contains the instance.
  string project = 2;

  InstancesDemoteMasterRequest body = 100;
}

// Instance demote request.
message SqlInstancesDemoteRequest {
  // Required. The name of the Cloud SQL instance.
  string instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The project ID of the project that contains the instance.
  string project = 2 [(google.api.field_behavior) = REQUIRED];

  // The request body.
  InstancesDemoteRequest body = 100;
}

message SqlInstancesExportRequest {
  // The Cloud SQL instance ID. This doesn't include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance to be exported.
  string project = 2;

  InstancesExportRequest body = 100;
}

message SqlInstancesFailoverRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;

  InstancesFailoverRequest body = 100;
}

message SqlInstancesGetRequest {
  // Database instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

message SqlInstancesImportRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  InstancesImportRequest body = 100;
}

message SqlInstancesInsertRequest {
  // Project ID of the project to which the newly created Cloud SQL instances
  // should belong.
  string project = 1;

  DatabaseInstance body = 100;
}

message SqlInstancesListRequest {
  // A filter expression that filters resources listed in the response.
  // The expression is in the form of field:value. For example,
  // 'instanceType:CLOUD_SQL_INSTANCE'. Fields can be nested as needed as per
  // their JSON representation, such as 'settings.userLabels.auto_start:true'.
  //
  // Multiple filter queries are space-separated. For example.
  // 'state:RUNNABLE instanceType:CLOUD_SQL_INSTANCE'. By default, each
  // expression is an AND expression. However, you can include AND and OR
  // expressions explicitly.
  string filter = 1;

  // The maximum number of instances to return. The service may return fewer
  // than this value.
  // If unspecified, at most 500 instances are returned.
  // The maximum value is 1000; values above 1000 are coerced to 1000.
  uint32 max_results = 2;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 3;

  // Project ID of the project for which to list Cloud SQL instances.
  string project = 4;
}

message SqlInstancesListServerCasRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

message SqlInstancesPatchRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  DatabaseInstance body = 100;
}

message SqlInstancesPromoteReplicaRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;

  // Set to true to invoke a replica failover to the designated DR replica.
  // As part of replica failover, the promote operation attempts
  // to add the original primary instance as a replica of the promoted
  // DR replica when the original primary instance comes back online.
  // If set to false or not specified, then the original primary
  // instance becomes an independent Cloud SQL primary instance.
  // Only applicable to MySQL.
  bool failover = 3;
}

// Instance switchover request.
message SqlInstancesSwitchoverRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the replica.
  string project = 2;

  // Optional. (MySQL only) Cloud SQL instance operations timeout, which is a
  // sum of all database operations. Default value is 10 minutes and can be
  // modified to a maximum value of 24 hours.
  google.protobuf.Duration db_timeout = 3
      [(google.api.field_behavior) = OPTIONAL];
}

message SqlInstancesResetSslConfigRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

message SqlInstancesRestartRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance to be restarted.
  string project = 2;
}

message SqlInstancesRestoreBackupRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  InstancesRestoreBackupRequest body = 100;
}

message SqlInstancesRotateServerCaRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  InstancesRotateServerCaRequest body = 100;
}

message SqlInstancesStartReplicaRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;
}

message SqlInstancesStopReplicaRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;
}

message SqlInstancesTruncateLogRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the Cloud SQL project.
  string project = 2;

  InstancesTruncateLogRequest body = 100;
}

message SqlInstancesUpdateRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  DatabaseInstance body = 100;
}

// Instance reencrypt request.
message SqlInstancesReencryptRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // ID of the project that contains the instance.
  string project = 2;

  // Reencrypt body that users request
  InstancesReencryptRequest body = 3;
}

// Database Instance reencrypt request.
message InstancesReencryptRequest {
  // Configuration specific to backup re-encryption
  optional BackupReencryptionConfig backup_reencryption_config = 1;
}

// Backup Reencryption Config
message BackupReencryptionConfig {
  // Backup type for re-encryption
  enum BackupType {
    // Unknown backup type, will be defaulted to AUTOMATIC backup type
    BACKUP_TYPE_UNSPECIFIED = 0;

    // Reencrypt automatic backups
    AUTOMATED = 1;

    // Reencrypt on-demand backups
    ON_DEMAND = 2;
  }

  // Backup re-encryption limit
  optional int32 backup_limit = 1;

  // Type of backups users want to re-encrypt.
  optional BackupType backup_type = 2;
}

message SqlInstancesRescheduleMaintenanceRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // ID of the project that contains the instance.
  string project = 2;

  SqlInstancesRescheduleMaintenanceRequestBody body = 100;
}

// Instance perform disk shrink request.
message SqlInstancesPerformDiskShrinkRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Perform disk shrink context.
  PerformDiskShrinkContext body = 100;
}

// External Sync parallel level.
enum ExternalSyncParallelLevel {
  // Unknown sync parallel level. Will be defaulted to OPTIMAL.
  EXTERNAL_SYNC_PARALLEL_LEVEL_UNSPECIFIED = 0;

  // Minimal parallel level.
  MIN = 1;

  // Optimal parallel level.
  OPTIMAL = 2;

  // Maximum parallel level.
  MAX = 3;
}

message SqlInstancesVerifyExternalSyncSettingsRequest {
  enum ExternalSyncMode {
    // Unknown external sync mode, will be defaulted to ONLINE mode
    EXTERNAL_SYNC_MODE_UNSPECIFIED = 0;

    // Online external sync will set up replication after initial data external
    // sync
    ONLINE = 1;

    // Offline external sync only dumps and loads a one-time snapshot of
    // the primary instance's data
    OFFLINE = 2;
  }

  // MigrationType determines whether the migration is a physical file-based
  // migration or a logical dump file-based migration.
  enum MigrationType {
    // Default value is a logical dump file-based migration
    MIGRATION_TYPE_UNSPECIFIED = 0;

    // Logical dump file-based migration
    LOGICAL = 1;

    // Physical file-based migration
    PHYSICAL = 2;
  }

  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Flag to enable verifying connection only
  bool verify_connection_only = 3;

  // External sync mode
  ExternalSyncMode sync_mode = 4;

  // Optional. Flag to verify settings required by replication setup only
  bool verify_replication_only = 5 [(google.api.field_behavior) = OPTIONAL];

  oneof sync_config {
    // Optional. MySQL-specific settings for start external sync.
    MySqlSyncConfig mysql_sync_config = 6
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. MigrationType configures the migration to use physical files or
  // logical dump files. If not set, then the logical dump file configuration is
  // used. Valid values are `LOGICAL` or `PHYSICAL`. Only applicable to MySQL.
  MigrationType migration_type = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Parallel level for initial data sync. Only applicable for
  // PostgreSQL.
  ExternalSyncParallelLevel sync_parallel_level = 8
      [(google.api.field_behavior) = OPTIONAL];
}

message SqlInstancesStartExternalSyncRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // ID of the project that contains the instance.
  string project = 2;

  // External sync mode.
  SqlInstancesVerifyExternalSyncSettingsRequest.ExternalSyncMode sync_mode = 3;

  // Whether to skip the verification step (VESS).
  bool skip_verification = 4;

  oneof sync_config {
    // MySQL-specific settings for start external sync.
    MySqlSyncConfig mysql_sync_config = 6;
  }

  // Optional. Parallel level for initial data sync. Currently only applicable
  // for MySQL.
  ExternalSyncParallelLevel sync_parallel_level = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. MigrationType configures the migration to use physical files or
  // logical dump files. If not set, then the logical dump file configuration is
  // used. Valid values are `LOGICAL` or `PHYSICAL`. Only applicable to MySQL.
  SqlInstancesVerifyExternalSyncSettingsRequest.MigrationType migration_type = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Instance reset replica size request.
message SqlInstancesResetReplicaSizeRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;
}

message SqlOperationsGetRequest {
  // Instance operation ID.
  string operation = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

message SqlOperationsListRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Maximum number of operations per response.
  uint32 max_results = 2;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 3;

  // Project ID of the project that contains the instance.
  string project = 4;
}

// The request payload to cancel an operation.
message SqlOperationsCancelRequest {
  // Instance operation ID.
  string operation = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

message SqlInstancesCreateEphemeralCertRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the Cloud SQL project.
  string project = 2;

  SslCertsCreateEphemeralRequest body = 100;
}

message SqlSslCertsDeleteRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Sha1 FingerPrint.
  string sha1_fingerprint = 3;
}

message SqlSslCertsGetRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Sha1 FingerPrint.
  string sha1_fingerprint = 3;
}

message SqlSslCertsInsertRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  SslCertsInsertRequest body = 100;
}

message SqlSslCertsListRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance get disk shrink config request.
message SqlInstancesGetDiskShrinkConfigRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance get latest recovery time request.
message SqlInstancesGetLatestRecoveryTimeRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance get latest recovery time response.
message SqlInstancesGetLatestRecoveryTimeResponse {
  // This is always `sql#getLatestRecoveryTime`.
  string kind = 1;

  // Timestamp, identifies the latest recovery time of the source instance.
  google.protobuf.Timestamp latest_recovery_time = 2;
}

// Request to release a lease for SSRS.
message SqlInstancesReleaseSsrsLeaseRequest {
  // Required. The Cloud SQL instance ID. This doesn't include the project ID.
  // It's composed of lowercase letters, numbers, and hyphens, and it must start
  // with a letter. The total length must be 98 characters or less (Example:
  // instance-id).
  string instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the project that contains the instance (Example:
  // project-id).
  string project = 2 [(google.api.field_behavior) = REQUIRED];
}

// The response for the release of the SSRS lease.
message SqlInstancesReleaseSsrsLeaseResponse {
  // The operation ID.
  string operation_id = 1;
}

// Request to acquire a lease for SSRS.
message SqlInstancesAcquireSsrsLeaseRequest {
  // Required. Cloud SQL instance ID. This doesn't include the project ID. It's
  // composed of lowercase letters, numbers, and hyphens, and it must start with
  // a letter. The total length must be 98 characters or less (Example:
  // instance-id).
  string instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the project that contains the instance (Example:
  // project-id).
  string project = 2 [(google.api.field_behavior) = REQUIRED];

  // The body for request to acquire an SSRS lease.
  InstancesAcquireSsrsLeaseRequest body = 100;
}

// Acquire SSRS lease response.
message SqlInstancesAcquireSsrsLeaseResponse {
  // The unique identifier for this operation.
  optional string operation_id = 1;
}
