{"methodConfig": [{"name": [{"service": "google.cloud.sql.v1beta4.SqlBackupRunsService"}, {"service": "google.cloud.sql.v1beta4.SqlDatabasesService"}, {"service": "google.cloud.sql.v1beta4.SqlFlagsService"}, {"service": "google.cloud.sql.v1beta4.SqlInstancesService"}, {"service": "google.cloud.sql.v1beta4.SqlOperationsService"}, {"service": "google.cloud.sql.v1beta4.SqlSslCertsService"}, {"service": "google.cloud.sql.v1beta4.SqlTiersService"}, {"service": "google.cloud.sql.v1beta4.SqlUsersService"}], "timeout": "60s"}]}