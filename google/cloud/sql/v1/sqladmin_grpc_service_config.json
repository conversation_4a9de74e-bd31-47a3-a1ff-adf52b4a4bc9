{"methodConfig": [{"name": [{"service": "google.cloud.sql.v1.SqlBackupRunsService"}, {"service": "google.cloud.sql.v1.SqlDatabasesService"}, {"service": "google.cloud.sql.v1.SqlAvailableDatabaseVersionsService"}, {"service": "google.cloud.sql.v1.SqlEventsService"}, {"service": "google.cloud.sql.v1.SqlFlagsService"}, {"service": "google.cloud.sql.v1.SqlInstancesService"}, {"service": "google.cloud.sql.v1.SqlOperationsService"}, {"service": "google.cloud.sql.v1.SqlSslCertsService"}, {"service": "google.cloud.sql.v1.SqlTiersService"}, {"service": "google.cloud.sql.v1.SqlUsersService"}], "timeout": "60s"}]}