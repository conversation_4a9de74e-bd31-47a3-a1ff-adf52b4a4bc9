// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/sql/v1/cloud_sql_resources.proto";
import "google/protobuf/empty.proto";

option go_package = "cloud.google.com/go/sql/apiv1/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlOperationsProto";
option java_package = "com.google.cloud.sql.v1";

// LINT: LEGACY_NAMES

// Service to fetch operations for database instances.
service SqlOperationsService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Retrieves an instance operation that has been performed on an instance.
  rpc Get(SqlOperationsGetRequest) returns (Operation) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/operations/{operation}"
    };
  }

  // Lists all instance operations that have been performed on the given Cloud
  // SQL instance in the reverse chronological order of the start time.
  rpc List(SqlOperationsListRequest) returns (OperationsListResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/operations"
    };
  }

  // Cancels an instance operation that has been performed on an instance.
  rpc Cancel(SqlOperationsCancelRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/operations/{operation}/cancel"
    };
  }
}

// Operations get request.
message SqlOperationsGetRequest {
  // Instance operation ID.
  string operation = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Operations list request.
message SqlOperationsListRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Maximum number of operations per response.
  uint32 max_results = 2;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 3;

  // Project ID of the project that contains the instance.
  string project = 4;
}

// Operations list response.
message OperationsListResponse {
  // This is always `sql#operationsList`.
  string kind = 1;

  // List of operation resources.
  repeated Operation items = 2;

  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 3;
}

// Operations cancel request.
message SqlOperationsCancelRequest {
  // Instance operation ID.
  string operation = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}
