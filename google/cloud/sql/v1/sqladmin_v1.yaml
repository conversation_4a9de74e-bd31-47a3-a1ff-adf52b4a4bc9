type: google.api.Service
config_version: 3
name: sqladmin.googleapis.com
title: Cloud SQL Admin API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.sql.v1.SqlBackupRunsService
- name: google.cloud.sql.v1.SqlConnectService
- name: google.cloud.sql.v1.SqlDatabasesService
- name: google.cloud.sql.v1.SqlFlagsService
- name: google.cloud.sql.v1.SqlInstancesService
- name: google.cloud.sql.v1.SqlOperationsService
- name: google.cloud.sql.v1.SqlSslCertsService
- name: google.cloud.sql.v1.SqlTiersService
- name: google.cloud.sql.v1.SqlUsersService
- name: google.longrunning.Operations

documentation:
  summary: API for Cloud SQL database instance management
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.sql.v1.SqlBackupRunsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1.SqlConnectService.GenerateEphemeralCert
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1.SqlConnectService.GetConnectSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.cloud.sql.v1.SqlDatabasesService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1.SqlFlagsService.List
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.cloud.sql.v1.SqlInstancesService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1.SqlInstancesService.Export
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.sql.v1.SqlInstancesService.Import
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.sql.v1.SqlOperationsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.cloud.sql.v1.SqlSslCertsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: google.cloud.sql.v1.SqlTiersService.List
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.cloud.sql.v1.SqlUsersService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/sqlservice.admin
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
