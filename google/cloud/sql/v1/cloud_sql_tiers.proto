// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";

option go_package = "cloud.google.com/go/sql/apiv1/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlTiersProto";
option java_package = "com.google.cloud.sql.v1";

// LINT: LEGACY_NAMES

// Service for providing machine types (tiers) for Cloud SQL instances.
service SqlTiersService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Lists all available machine types (tiers) for Cloud SQL, for example,
  // `db-custom-1-3840`. For more information, see
  // https://cloud.google.com/sql/pricing.
  rpc List(SqlTiersListRequest) returns (TiersListResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/tiers"
    };
  }
}

// Tiers list request.
message SqlTiersListRequest {
  // Project ID of the project for which to list tiers.
  string project = 1;
}

// Tiers list response.
message TiersListResponse {
  // This is always `sql#tiersList`.
  string kind = 1;

  // List of tiers.
  repeated Tier items = 2;
}

// A Google Cloud SQL service tier resource.
message Tier {
  // An identifier for the machine type, for example, `db-custom-1-3840`. For
  // related information, see [Pricing](/sql/pricing).
  string tier = 1;

  // The maximum RAM usage of this tier in bytes.
  int64 RAM = 2;

  // This is always `sql#tier`.
  string kind = 3;

  // The maximum disk size of this tier in bytes.
  int64 Disk_Quota = 4;

  // The applicable regions for this tier.
  repeated string region = 5;
}
