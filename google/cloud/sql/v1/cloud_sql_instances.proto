// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/sql/v1/cloud_sql_resources.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/sql/apiv1/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlServiceProto";
option java_package = "com.google.cloud.sql.v1";

// Service to manage Cloud SQL instances.
service SqlInstancesService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Adds a new trusted Certificate Authority (CA) version for the specified
  // instance. Required to prepare for a certificate rotation. If a CA version
  // was previously added but never used in a certificate rotation, this
  // operation replaces that version. There cannot be more than one CA version
  // waiting to be rotated in. For instances that have enabled Certificate
  // Authority Service (CAS) based server CA, please use AddServerCertificate to
  // add a new server certificate.
  rpc AddServerCa(SqlInstancesAddServerCaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/addServerCa"
    };
  }

  // Creates a Cloud SQL instance as a clone of the source instance. Using this
  // operation might cause your instance to restart.
  rpc Clone(SqlInstancesCloneRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/clone"
      body: "body"
    };
  }

  // Deletes a Cloud SQL instance.
  rpc Delete(SqlInstancesDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/v1/projects/{project}/instances/{instance}"
    };
  }

  // Demotes the stand-alone instance to be a Cloud SQL read replica for an
  // external database server.
  rpc DemoteMaster(SqlInstancesDemoteMasterRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/demoteMaster"
      body: "body"
    };
  }

  // Demotes an existing standalone instance to be a Cloud SQL read replica
  // for an external database server.
  rpc Demote(SqlInstancesDemoteRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/demote"
      body: "body"
    };
  }

  // Exports data from a Cloud SQL instance to a Cloud Storage bucket as a SQL
  // dump or CSV file.
  rpc Export(SqlInstancesExportRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/export"
      body: "body"
    };
  }

  // Initiates a manual failover of a high availability (HA) primary instance
  // to a standby instance, which becomes the primary instance. Users are
  // then rerouted to the new primary. For more information, see the
  // [Overview of high
  // availability](https://cloud.google.com/sql/docs/mysql/high-availability)
  // page in the Cloud SQL documentation.
  // If using Legacy HA (MySQL only), this causes the instance to failover to
  // its failover replica instance.
  rpc Failover(SqlInstancesFailoverRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/failover"
      body: "body"
    };
  }

  // Reencrypt CMEK instance with latest key version.
  rpc Reencrypt(SqlInstancesReencryptRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/reencrypt"
      body: "body"
    };
  }

  // Retrieves a resource containing information about a Cloud SQL instance.
  rpc Get(SqlInstancesGetRequest) returns (DatabaseInstance) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}"
    };
  }

  // Imports data into a Cloud SQL instance from a SQL dump  or CSV file in
  // Cloud Storage.
  rpc Import(SqlInstancesImportRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/import"
      body: "body"
    };
  }

  // Creates a new Cloud SQL instance.
  rpc Insert(SqlInstancesInsertRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances"
      body: "body"
    };
  }

  // Lists instances under a given project.
  rpc List(SqlInstancesListRequest) returns (InstancesListResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances"
    };
  }

  // Lists all of the trusted Certificate Authorities (CAs) for the specified
  // instance. There can be up to three CAs listed: the CA that was used to sign
  // the certificate that is currently in use, a CA that has been added but not
  // yet used to sign a certificate, and a CA used to sign a certificate that
  // has previously rotated out.
  rpc ListServerCas(SqlInstancesListServerCasRequest)
      returns (InstancesListServerCasResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/listServerCas"
    };
  }

  // Partially updates settings of a Cloud SQL instance by merging the request
  // with the current configuration. This method supports patch semantics.
  rpc Patch(SqlInstancesPatchRequest) returns (Operation) {
    option (google.api.http) = {
      patch: "/v1/projects/{project}/instances/{instance}"
      body: "body"
    };
  }

  // Promotes the read replica instance to be an independent Cloud SQL
  // primary instance.
  // Using this operation might cause your instance to restart.
  rpc PromoteReplica(SqlInstancesPromoteReplicaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/promoteReplica"
    };
  }

  // Switches over from the primary instance to the designated DR replica
  // instance.
  rpc Switchover(SqlInstancesSwitchoverRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/switchover"
    };
  }

  // Deletes all client certificates and generates a new server SSL certificate
  // for the instance.
  rpc ResetSslConfig(SqlInstancesResetSslConfigRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/resetSslConfig"
    };
  }

  // Restarts a Cloud SQL instance.
  rpc Restart(SqlInstancesRestartRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/restart"
    };
  }

  // Restores a backup of a Cloud SQL instance. Using this operation might cause
  // your instance to restart.
  rpc RestoreBackup(SqlInstancesRestoreBackupRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/restoreBackup"
      body: "body"
    };
  }

  // Rotates the server certificate to one signed by the Certificate Authority
  // (CA) version previously added with the addServerCA method. For instances
  // that have enabled Certificate Authority Service (CAS) based server CA,
  // please use RotateServerCertificate to rotate the server certificate.
  rpc RotateServerCa(SqlInstancesRotateServerCaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/rotateServerCa"
      body: "body"
    };
  }

  // Starts the replication in the read replica instance.
  rpc StartReplica(SqlInstancesStartReplicaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/startReplica"
    };
  }

  // Stops the replication in the read replica instance.
  rpc StopReplica(SqlInstancesStopReplicaRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/stopReplica"
    };
  }

  // Truncate MySQL general and slow query log tables
  // MySQL only.
  rpc TruncateLog(SqlInstancesTruncateLogRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/truncateLog"
      body: "body"
    };
  }

  // Updates settings of a Cloud SQL instance. Using this operation might cause
  // your instance to restart.
  rpc Update(SqlInstancesUpdateRequest) returns (Operation) {
    option (google.api.http) = {
      put: "/v1/projects/{project}/instances/{instance}"
      body: "body"
    };
  }

  // Generates a short-lived X509 certificate containing the provided public key
  // and signed by a private key specific to the target instance. Users may use
  // the certificate to authenticate as themselves when connecting to the
  // database.
  rpc CreateEphemeral(SqlInstancesCreateEphemeralCertRequest)
      returns (SslCert) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/createEphemeral"
      body: "body"
    };
  }

  // Reschedules the maintenance on the given instance.
  rpc RescheduleMaintenance(SqlInstancesRescheduleMaintenanceRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/rescheduleMaintenance"
      body: "body"
    };
  }

  // Verify External primary instance external sync settings.
  rpc VerifyExternalSyncSettings(SqlInstancesVerifyExternalSyncSettingsRequest)
      returns (SqlInstancesVerifyExternalSyncSettingsResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/verifyExternalSyncSettings"
      body: "*"
    };
  }

  // Start External primary instance migration.
  rpc StartExternalSync(SqlInstancesStartExternalSyncRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/startExternalSync"
      body: "*"
    };
  }

  // Perform Disk Shrink on primary instance.
  rpc PerformDiskShrink(SqlInstancesPerformDiskShrinkRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/performDiskShrink"
      body: "body"
    };
  }

  // Get Disk Shrink Config for a given instance.
  rpc GetDiskShrinkConfig(SqlInstancesGetDiskShrinkConfigRequest)
      returns (SqlInstancesGetDiskShrinkConfigResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/getDiskShrinkConfig"
    };
  }

  // Reset Replica Size to primary instance disk size.
  rpc ResetReplicaSize(SqlInstancesResetReplicaSizeRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/resetReplicaSize"
      body: "*"
    };
  }

  // Get Latest Recovery Time for a given instance.
  rpc GetLatestRecoveryTime(SqlInstancesGetLatestRecoveryTimeRequest)
      returns (SqlInstancesGetLatestRecoveryTimeResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/getLatestRecoveryTime"
    };
  }

  // Acquire a lease for the setup of SQL Server Reporting Services (SSRS).
  rpc AcquireSsrsLease(SqlInstancesAcquireSsrsLeaseRequest)
      returns (SqlInstancesAcquireSsrsLeaseResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/acquireSsrsLease"
      body: "body"
    };
  }

  // Release a lease for the setup of SQL Server Reporting Services (SSRS).
  rpc ReleaseSsrsLease(SqlInstancesReleaseSsrsLeaseRequest)
      returns (SqlInstancesReleaseSsrsLeaseResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/releaseSsrsLease"
    };
  }
}

// Instance add server CA request.
message SqlInstancesAddServerCaRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance clone request.
message SqlInstancesCloneRequest {
  // The ID of the Cloud SQL instance to be cloned (source). This does not
  // include the project ID.
  string instance = 1;

  // Project ID of the source as well as the clone Cloud SQL instance.
  string project = 2;

  InstancesCloneRequest body = 100;
}

// Instance delete request.
message SqlInstancesDeleteRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance to be deleted.
  string project = 2;
}

// Instance demote master request.
message SqlInstancesDemoteMasterRequest {
  // Cloud SQL instance name.
  string instance = 1;

  // ID of the project that contains the instance.
  string project = 2;

  InstancesDemoteMasterRequest body = 100;
}

// Instance demote request.
message SqlInstancesDemoteRequest {
  // Required. Cloud SQL instance name.
  string instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the project that contains the instance.
  string project = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The request body.
  InstancesDemoteRequest body = 100 [(google.api.field_behavior) = REQUIRED];
}

// Instance export request.
message SqlInstancesExportRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance to be exported.
  string project = 2;

  InstancesExportRequest body = 100;
}

// Instance failover request.
message SqlInstancesFailoverRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;

  InstancesFailoverRequest body = 100;
}

// Instance get request.
message SqlInstancesGetRequest {
  // Database instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance import request.
message SqlInstancesImportRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  InstancesImportRequest body = 100;
}

// Instance insert request.
message SqlInstancesInsertRequest {
  // Project ID of the project to which the newly created Cloud SQL instances
  // should belong.
  string project = 1;

  DatabaseInstance body = 100;
}

// Instance list request.
message SqlInstancesListRequest {
  // A filter expression that filters resources listed in the response.
  // The expression is in the form of field:value. For example,
  // 'instanceType:CLOUD_SQL_INSTANCE'. Fields can be nested as needed as per
  // their JSON representation, such as 'settings.userLabels.auto_start:true'.
  //
  // Multiple filter queries are space-separated. For example.
  // 'state:RUNNABLE instanceType:CLOUD_SQL_INSTANCE'. By default, each
  // expression is an AND expression. However, you can include AND and OR
  // expressions explicitly.
  string filter = 1;

  // The maximum number of instances to return. The service may return fewer
  // than this value.
  // If unspecified, at most 500 instances are returned.
  // The maximum value is 1000; values above 1000 are coerced to 1000.
  uint32 max_results = 2;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 3;

  // Project ID of the project for which to list Cloud SQL instances.
  string project = 4;
}

// Instance list server CAs request.
message SqlInstancesListServerCasRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance patch request.
message SqlInstancesPatchRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  DatabaseInstance body = 100;
}

// Instance promote replica request.
message SqlInstancesPromoteReplicaRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;

  // Set to true to invoke a replica failover to the designated DR
  // replica. As part of replica failover, the promote operation attempts
  // to add the original primary instance as a replica of the promoted
  // DR replica when the original primary instance comes back online.
  // If set to false or not specified, then the original primary
  // instance becomes an independent Cloud SQL primary instance.
  // Only applicable to MySQL.
  bool failover = 3;
}

// Instance switchover request.
message SqlInstancesSwitchoverRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the replica.
  string project = 2;

  // Optional. (MySQL only) Cloud SQL instance operations timeout, which is a
  // sum of all database operations. Default value is 10 minutes and can be
  // modified to a maximum value of 24 hours.
  google.protobuf.Duration db_timeout = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Instance reset SSL config request.
message SqlInstancesResetSslConfigRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance restart request.
message SqlInstancesRestartRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance to be restarted.
  string project = 2;
}

// Instance restore backup request.
message SqlInstancesRestoreBackupRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  InstancesRestoreBackupRequest body = 100;
}

// Instance rotate server CA request.
message SqlInstancesRotateServerCaRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  InstancesRotateServerCaRequest body = 100;
}

// Instance start replica request.
message SqlInstancesStartReplicaRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;
}

// Instance stop replica request.
message SqlInstancesStopReplicaRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;
}

// Instance truncate log request.
message SqlInstancesTruncateLogRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the Cloud SQL project.
  string project = 2;

  InstancesTruncateLogRequest body = 100;
}

// Instance perform disk shrink request.
message SqlInstancesPerformDiskShrinkRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Perform disk shrink context.
  PerformDiskShrinkContext body = 100;
}

// Instance update request.
message SqlInstancesUpdateRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  DatabaseInstance body = 100;
}

// Instance reschedule maintenance request.
message SqlInstancesRescheduleMaintenanceRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // ID of the project that contains the instance.
  string project = 2;

  SqlInstancesRescheduleMaintenanceRequestBody body = 100;
}

// Instance reencrypt request.
message SqlInstancesReencryptRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // ID of the project that contains the instance.
  string project = 2;

  // Reencrypt body that users request
  InstancesReencryptRequest body = 3;
}

// Database Instance reencrypt request.
message InstancesReencryptRequest {
  // Configuration specific to backup re-encryption
  optional BackupReencryptionConfig backup_reencryption_config = 1;
}

// Backup Reencryption Config
message BackupReencryptionConfig {
  // Backup type for re-encryption
  enum BackupType {
    // Unknown backup type, will be defaulted to AUTOMATIC backup type
    BACKUP_TYPE_UNSPECIFIED = 0;

    // Reencrypt automatic backups
    AUTOMATED = 1;

    // Reencrypt on-demand backups
    ON_DEMAND = 2;
  }

  // Backup re-encryption limit
  optional int32 backup_limit = 1;

  // Type of backups users want to re-encrypt.
  optional BackupType backup_type = 2;
}

// External Sync parallel level.
enum ExternalSyncParallelLevel {
  // Unknown sync parallel level. Will be defaulted to OPTIMAL.
  EXTERNAL_SYNC_PARALLEL_LEVEL_UNSPECIFIED = 0;

  // Minimal parallel level.
  MIN = 1;

  // Optimal parallel level.
  OPTIMAL = 2;

  // Maximum parallel level.
  MAX = 3;
}

// Instance get disk shrink config request.
message SqlInstancesGetDiskShrinkConfigRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance verify external sync settings request.
message SqlInstancesVerifyExternalSyncSettingsRequest {
  enum ExternalSyncMode {
    // Unknown external sync mode, will be defaulted to ONLINE mode
    EXTERNAL_SYNC_MODE_UNSPECIFIED = 0;

    // Online external sync will set up replication after initial data external
    // sync
    ONLINE = 1;

    // Offline external sync only dumps and loads a one-time snapshot of
    // the primary instance's data
    OFFLINE = 2;
  }

  // MigrationType determines whether the migration is a physical file-based
  // migration or a logical dump file-based migration.
  enum MigrationType {
    // Default value is a logical dump file-based migration
    MIGRATION_TYPE_UNSPECIFIED = 0;

    // Logical dump file-based migration
    LOGICAL = 1;

    // Physical file-based migration
    PHYSICAL = 2;
  }

  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Flag to enable verifying connection only
  bool verify_connection_only = 3;

  // External sync mode
  ExternalSyncMode sync_mode = 4;

  // Optional. Flag to verify settings required by replication setup only
  bool verify_replication_only = 5 [(google.api.field_behavior) = OPTIONAL];

  oneof sync_config {
    // Optional. MySQL-specific settings for start external sync.
    MySqlSyncConfig mysql_sync_config = 6
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. MigrationType configures the migration to use physical files or
  // logical dump files. If not set, then the logical dump file configuration is
  // used. Valid values are `LOGICAL` or `PHYSICAL`. Only applicable to MySQL.
  MigrationType migration_type = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Parallel level for initial data sync. Only applicable for
  // PostgreSQL.
  ExternalSyncParallelLevel sync_parallel_level = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Instance start external sync request.
message SqlInstancesStartExternalSyncRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // ID of the project that contains the instance.
  string project = 2;

  // External sync mode.
  SqlInstancesVerifyExternalSyncSettingsRequest.ExternalSyncMode sync_mode = 3;

  // Whether to skip the verification step (VESS).
  bool skip_verification = 4;

  oneof sync_config {
    // MySQL-specific settings for start external sync.
    MySqlSyncConfig mysql_sync_config = 6;
  }

  // Optional. Parallel level for initial data sync. Currently only applicable
  // for MySQL.
  ExternalSyncParallelLevel sync_parallel_level = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. MigrationType configures the migration to use physical files or
  // logical dump files. If not set, then the logical dump file configuration is
  // used. Valid values are `LOGICAL` or `PHYSICAL`. Only applicable to MySQL.
  SqlInstancesVerifyExternalSyncSettingsRequest.MigrationType migration_type = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Instance reset replica size request.
message SqlInstancesResetReplicaSizeRequest {
  // Cloud SQL read replica instance name.
  string instance = 1;

  // ID of the project that contains the read replica.
  string project = 2;
}

// Instance create ephemeral certificate request.
message SqlInstancesCreateEphemeralCertRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the Cloud SQL project.
  string project = 2;

  SslCertsCreateEphemeralRequest body = 100;
}

// Database instance clone request.
message InstancesCloneRequest {
  // Contains details about the clone operation.
  CloneContext clone_context = 1;
}

// Database demote primary instance request.
message InstancesDemoteMasterRequest {
  // Contains details about the demoteMaster operation.
  DemoteMasterContext demote_master_context = 1;
}

// This request is used to demote an existing standalone instance to be a
// Cloud SQL read replica for an external database server.
message InstancesDemoteRequest {
  // Required. Contains details about the demote operation.
  DemoteContext demote_context = 1 [(google.api.field_behavior) = REQUIRED];
}

// Database instance export request.
message InstancesExportRequest {
  // Contains details about the export operation.
  ExportContext export_context = 1;
}

// Instance failover request.
message InstancesFailoverRequest {
  // Failover Context.
  FailoverContext failover_context = 1;
}

// SslCerts create ephemeral certificate request.
message SslCertsCreateEphemeralRequest {
  // PEM encoded public key to include in the signed certificate.
  string public_key = 1;

  // Access token to include in the signed certificate.
  string access_token = 2;
}

// Database instance import request.
message InstancesImportRequest {
  // Contains details about the import operation.
  ImportContext import_context = 1;
}

// Database instances list response.
message InstancesListResponse {
  // This is always `sql#instancesList`.
  string kind = 1;

  // List of warnings that occurred while handling the request.
  repeated ApiWarning warnings = 2;

  // List of database instance resources.
  repeated DatabaseInstance items = 3;

  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 4;
}

// Instances ListServerCas response.
message InstancesListServerCasResponse {
  // List of server CA certificates for the instance.
  repeated SslCert certs = 1;

  string active_version = 2;

  // This is always `sql#instancesListServerCas`.
  string kind = 3;
}

// Database instance restore backup request.
message InstancesRestoreBackupRequest {
  // Parameters required to perform the restore backup operation.
  RestoreBackupContext restore_backup_context = 1;
}

// Rotate server CA request.
message InstancesRotateServerCaRequest {
  // Contains details about the rotate server CA operation.
  RotateServerCaContext rotate_server_ca_context = 1;
}

// Instance truncate log request.
message InstancesTruncateLogRequest {
  // Contains details about the truncate log operation.
  TruncateLogContext truncate_log_context = 1;
}

// Request to acquire a lease for SSRS.
message InstancesAcquireSsrsLeaseRequest {
  // Contains details about the acquire SSRS lease operation.
  AcquireSsrsLeaseContext acquire_ssrs_lease_context = 1;
}

// Instance verify external sync settings response.
message SqlInstancesVerifyExternalSyncSettingsResponse {
  // This is always `sql#migrationSettingErrorList`.
  string kind = 1;

  // List of migration violations.
  repeated SqlExternalSyncSettingError errors = 2;

  // List of migration warnings.
  repeated SqlExternalSyncSettingError warnings = 3;
}

// Instance get disk shrink config response.
message SqlInstancesGetDiskShrinkConfigResponse {
  // This is always `sql#getDiskShrinkConfig`.
  string kind = 1;

  // The minimum size to which a disk can be shrunk in GigaBytes.
  int64 minimal_target_size_gb = 2;

  // Additional message to customers.
  string message = 3;
}

// Instance get latest recovery time request.
message SqlInstancesGetLatestRecoveryTimeRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Instance get latest recovery time response.
message SqlInstancesGetLatestRecoveryTimeResponse {
  // This is always `sql#getLatestRecoveryTime`.
  string kind = 1;

  // Timestamp, identifies the latest recovery time of the source instance.
  google.protobuf.Timestamp latest_recovery_time = 2;
}

// Database instance clone context.
message CloneContext {
  // This is always `sql#cloneContext`.
  string kind = 1;

  // Reserved for future use.
  int64 pitr_timestamp_ms = 2;

  // Name of the Cloud SQL instance to be created as a clone.
  string destination_instance_name = 3;

  // Binary log coordinates, if specified, identify the position up to which the
  // source instance is cloned. If not specified, the source instance is
  // cloned up to the most recent binary log coordinates.
  BinLogCoordinates bin_log_coordinates = 4;

  // Timestamp, if specified, identifies the time to which the source instance
  // is cloned.
  google.protobuf.Timestamp point_in_time = 5;

  // The name of the allocated ip range for the private ip Cloud SQL instance.
  // For example: "google-managed-services-default". If set, the cloned instance
  // ip will be created in the allocated range. The range name must comply with
  // [RFC 1035](https://tools.ietf.org/html/rfc1035). Specifically, the name
  // must be 1-63 characters long and match the regular expression
  // [a-z]([-a-z0-9]*[a-z0-9])?.
  // Reserved for future use.
  string allocated_ip_range = 6;

  // (SQL Server only) Clone only the specified databases from the source
  // instance. Clone all databases if empty.
  repeated string database_names = 9;

  // Optional. Copy clone and point-in-time recovery clone of an instance to the
  // specified zone. If no zone is specified, clone to the same primary zone as
  // the source instance. This field applies to all DB types.
  optional string preferred_zone = 10 [(google.api.field_behavior) = OPTIONAL];
}

// Binary log coordinates.
message BinLogCoordinates {
  // Name of the binary log file for a Cloud SQL instance.
  string bin_log_file_name = 1;

  // Position (offset) within the binary log file.
  int64 bin_log_position = 2;

  // This is always `sql#binLogCoordinates`.
  string kind = 3;
}

// A Cloud SQL instance resource.
message DatabaseInstance {
  // The current serving state of the database instance.
  enum SqlInstanceState {
    // The state of the instance is unknown.
    SQL_INSTANCE_STATE_UNSPECIFIED = 0;

    // The instance is running, or has been stopped by owner.
    RUNNABLE = 1;

    // The instance is not available, for example due to problems with billing.
    SUSPENDED = 2;

    // The instance is being deleted.
    PENDING_DELETE = 3;

    // The instance is being created.
    PENDING_CREATE = 4;

    // The instance is down for maintenance.
    MAINTENANCE = 5;

    // The creation of the instance failed or a fatal error occurred during
    // maintenance.
    FAILED = 6;

    // Deprecated
    ONLINE_MAINTENANCE = 7 [deprecated = true];
  }

  message SqlFailoverReplica {
    // The name of the failover replica. If specified at instance creation, a
    // failover replica is created for the instance. The name
    // doesn't include the project ID.
    string name = 1;

    // The availability status of the failover replica. A false status indicates
    // that the failover replica is out of sync. The primary instance can only
    // failover to the failover replica when the status is true.
    google.protobuf.BoolValue available = 2;
  }

  // Any scheduled maintenance for this instance.
  message SqlScheduledMaintenance {
    // The start time of any upcoming scheduled maintenance for this instance.
    google.protobuf.Timestamp start_time = 1;

    bool can_defer = 2 [deprecated = true];

    // If the scheduled maintenance can be rescheduled.
    bool can_reschedule = 3;

    // Maintenance cannot be rescheduled to start beyond this deadline.
    optional google.protobuf.Timestamp schedule_deadline_time = 4;
  }

  // This message wraps up the information written by out-of-disk detection job.
  message SqlOutOfDiskReport {
    // This enum lists all possible states regarding out-of-disk issues.
    enum SqlOutOfDiskState {
      // Unspecified state
      SQL_OUT_OF_DISK_STATE_UNSPECIFIED = 0;

      // The instance has plenty space on data disk
      NORMAL = 1;

      // Data disk is almost used up. It is shutdown to prevent data
      // corruption.
      SOFT_SHUTDOWN = 2;
    }

    // This field represents the state generated by the proactive database
    // wellness job for OutOfDisk issues.
    // *  Writers:
    //   *  the proactive database wellness job for OOD.
    // *  Readers:
    //   *  the proactive database wellness job
    optional SqlOutOfDiskState sql_out_of_disk_state = 1;

    // The minimum recommended increase size in GigaBytes
    // This field is consumed by the frontend
    // *  Writers:
    //   *  the proactive database wellness job for OOD.
    // *  Readers:
    optional int32 sql_min_recommended_increase_size_gb = 2;
  }

  // The SQL network architecture for the instance.
  enum SqlNetworkArchitecture {
    SQL_NETWORK_ARCHITECTURE_UNSPECIFIED = 0;

    // The instance uses the new network architecture.
    NEW_NETWORK_ARCHITECTURE = 1;

    // The instance uses the old network architecture.
    OLD_NETWORK_ARCHITECTURE = 2;
  }

  // This is always `sql#instance`.
  string kind = 1;

  // The current serving state of the Cloud SQL instance.
  SqlInstanceState state = 2;

  // The database engine type and version. The `databaseVersion` field cannot
  // be changed after instance creation.
  SqlDatabaseVersion database_version = 3;

  // The user settings.
  Settings settings = 4;

  // This field is deprecated and will be removed from a future version of the
  // API. Use the `settings.settingsVersion` field instead.
  string etag = 5;

  // The name and status of the failover replica.
  SqlFailoverReplica failover_replica = 6;

  // The name of the instance which will act as primary in the replication
  // setup.
  string master_instance_name = 7;

  // The replicas of the instance.
  repeated string replica_names = 8;

  // The maximum disk size of the instance in bytes.
  google.protobuf.Int64Value max_disk_size = 9 [deprecated = true];

  // The current disk usage of the instance in bytes. This property has been
  // deprecated. Use the
  // "cloudsql.googleapis.com/database/disk/bytes_used" metric in Cloud
  // Monitoring API instead. Please see [this
  // announcement](https://groups.google.com/d/msg/google-cloud-sql-announce/I_7-F9EBhT0/BtvFtdFeAgAJ)
  // for details.
  google.protobuf.Int64Value current_disk_size = 10 [deprecated = true];

  // The assigned IP addresses for the instance.
  repeated IpMapping ip_addresses = 11;

  // SSL configuration.
  SslCert server_ca_cert = 12;

  // The instance type.
  SqlInstanceType instance_type = 13;

  // The project ID of the project containing the Cloud SQL instance. The Google
  // apps domain is prefixed if applicable.
  string project = 14;

  // The IPv6 address assigned to the instance.
  // (Deprecated) This property was applicable only
  // to First Generation instances.
  string ipv6_address = 15 [deprecated = true];

  // The service account email address assigned to the instance.\This
  // property is read-only.
  string service_account_email_address = 16;

  // Configuration specific to on-premises instances.
  OnPremisesConfiguration on_premises_configuration = 17;

  // Configuration specific to failover replicas and read replicas.
  ReplicaConfiguration replica_configuration = 18;

  // The backend type.
  // `SECOND_GEN`: Cloud SQL database instance.
  // `EXTERNAL`: A database server that is not managed by Google.
  //
  // This property is read-only; use the `tier` property in the `settings`
  // object to determine the database type.
  SqlBackendType backend_type = 19;

  // The URI of this resource.
  string self_link = 20;

  // If the instance state is SUSPENDED, the reason for the suspension.
  repeated SqlSuspensionReason suspension_reason = 21;

  // Connection name of the Cloud SQL instance used in connection strings.
  string connection_name = 22;

  // Name of the Cloud SQL instance. This does not include the project ID.
  string name = 23;

  // The geographical region of the Cloud SQL instance.
  //
  // It can be one of the
  // [regions](https://cloud.google.com/sql/docs/mysql/locations#location-r)
  // where Cloud SQL operates:
  //
  // For example,  `asia-east1`, `europe-west1`, and  `us-central1`.
  // The default value is `us-central1`.
  string region = 24;

  // The Compute Engine zone that the instance is currently serving from. This
  // value could be different from the zone that was specified when the instance
  // was created if the instance has failed over to its secondary zone. WARNING:
  // Changing this might restart the instance.
  string gce_zone = 25;

  // The Compute Engine zone that the failover instance is currently serving
  // from for a regional instance. This value could be different
  // from the zone that was specified when the instance
  // was created if the instance has failed over to its secondary/failover zone.
  string secondary_gce_zone = 34;

  // Disk encryption configuration specific to an instance.
  DiskEncryptionConfiguration disk_encryption_configuration = 26;

  // Disk encryption status specific to an instance.
  DiskEncryptionStatus disk_encryption_status = 27;

  // Initial root password. Use only on creation. You must set root passwords
  // before you can connect to PostgreSQL instances.
  string root_password = 29;

  // The start time of any upcoming scheduled maintenance for this instance.
  SqlScheduledMaintenance scheduled_maintenance = 30;

  // This status indicates whether the instance satisfies PZS.
  //
  // The status is reserved for future use.
  google.protobuf.BoolValue satisfies_pzs = 35;

  // Output only. Stores the current database version running on the instance
  // including minor version such as `MYSQL_8_0_18`.
  string database_installed_version = 40
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // This field represents the report generated by the proactive database
  // wellness job for OutOfDisk issues.
  // *  Writers:
  //   *  the proactive database wellness job for OOD.
  // *  Readers:
  //   *  the proactive database wellness job
  optional SqlOutOfDiskReport out_of_disk_report = 38;

  // Output only. The time when the instance was created in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp create_time = 39
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List all maintenance versions applicable on the instance
  repeated string available_maintenance_versions = 41
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The current software version on the instance.
  string maintenance_version = 42;

  // Output only. All database versions that are available for upgrade.
  repeated AvailableDatabaseVersion upgradable_database_versions = 45
      [(google.api.field_behavior) = OUTPUT_ONLY];

  optional SqlNetworkArchitecture sql_network_architecture = 47;

  // Output only. The link to service attachment of PSC instance.
  optional string psc_service_attachment_link = 48
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The dns name of the instance.
  optional string dns_name = 49 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. DEPRECATED: please use write_endpoint instead.
  optional string primary_dns_name = 51
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The dns name of the primary instance in a replication group.
  optional string write_endpoint = 52
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A primary instance and disaster recovery (DR) replica pair.
  // A DR replica is a cross-region replica that you designate
  // for failover in the event that the primary instance
  // experiences regional failure. Only applicable to MySQL.
  ReplicationCluster replication_cluster = 54
      [(google.api.field_behavior) = OPTIONAL];

  // Gemini instance configuration.
  optional GeminiInstanceConfig gemini_config = 55;

  // Output only. This status indicates whether the instance satisfies PZI.
  //
  // The status is reserved for future use.
  google.protobuf.BoolValue satisfies_pzi = 56
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. Whether Cloud SQL is enabled to switch storing point-in-time
  // recovery log files from a data disk to Cloud Storage.
  optional google.protobuf.BoolValue
      switch_transaction_logs_to_cloud_storage_enabled = 57
      [(google.api.field_behavior) = INPUT_ONLY];
}

// Gemini instance configuration.
message GeminiInstanceConfig {
  // Output only. Whether Gemini is enabled.
  optional bool entitled = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the vacuum management is enabled.
  optional bool google_vacuum_mgmt_enabled = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether canceling the out-of-memory (OOM) session is enabled.
  optional bool oom_session_cancel_enabled = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the active query is enabled.
  optional bool active_query_enabled = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the index advisor is enabled.
  optional bool index_advisor_enabled = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the flag recommender is enabled.
  optional bool flag_recommender_enabled = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A primary instance and disaster recovery (DR) replica pair.
// A DR replica is a cross-region replica that you designate for failover in
// the event that the primary instance experiences regional failure.
// Only applicable to MySQL.
message ReplicationCluster {
  // Output only. If set, it indicates this instance has a private service
  // access (PSA) dns endpoint that is pointing to the primary instance of the
  // cluster. If this instance is the primary, the dns should be pointing to
  // this instance. After Switchover or Replica failover, this DNS endpoint
  // points to the promoted instance. This is a read-only field, returned to the
  // user as information. This field can exist even if a standalone instance
  // does not yet have a replica, or had a DR replica that was deleted.
  string psa_write_endpoint = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. If the instance is a primary instance, then this field identifies
  // the disaster recovery (DR) replica. A DR replica is an optional
  // configuration for Enterprise Plus edition instances. If the instance is a
  // read replica, then the field is not set. Set this field to a replica name
  // to designate a DR replica for a primary instance. Remove the replica name
  // to remove the DR replica designation.
  string failover_dr_replica_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Read-only field that indicates whether the replica is a DR
  // replica. This field is not set if the instance is a primary instance.
  bool dr_replica = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An available database version. It can be a major or a minor version.
message AvailableDatabaseVersion {
  // The version's major version name.
  optional string major_version = 3;

  // The database version name. For MySQL 8.0, this string provides the database
  // major and minor version.
  optional string name = 8;

  // The database version's display name.
  optional string display_name = 9;
}

// Reschedule options for maintenance windows.
message SqlInstancesRescheduleMaintenanceRequestBody {
  enum RescheduleType {
    RESCHEDULE_TYPE_UNSPECIFIED = 0;

    // Reschedules maintenance to happen now (within 5 minutes).
    IMMEDIATE = 1;

    // Reschedules maintenance to occur within one week from the originally
    // scheduled day and time.
    NEXT_AVAILABLE_WINDOW = 2;

    // Reschedules maintenance to a specific time and day.
    SPECIFIC_TIME = 3;
  }

  message Reschedule {
    // Required. The type of the reschedule.
    RescheduleType reschedule_type = 1;

    // Optional. Timestamp when the maintenance shall be rescheduled to if
    // reschedule_type=SPECIFIC_TIME, in
    // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
    // `2012-11-15T16:19:00.094Z`.
    google.protobuf.Timestamp schedule_time = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The type of the reschedule the user wants.
  Reschedule reschedule = 3;
}

// Database instance demote primary instance context.
message DemoteMasterContext {
  // This is always `sql#demoteMasterContext`.
  string kind = 1;

  // Verify the GTID consistency for demote operation. Default value:
  // `True`. Setting this flag to `false` enables you to bypass the GTID
  // consistency check between on-premises primary instance and Cloud SQL
  // instance during the demotion operation but also exposes you to the risk of
  // future replication failures. Change the value only if you know the reason
  // for the GTID divergence and are confident that doing so will not cause any
  // replication issues.
  google.protobuf.BoolValue verify_gtid_consistency = 2;

  // The name of the instance which will act as on-premises primary instance
  // in the replication setup.
  string master_instance_name = 3;

  // Configuration specific to read-replicas replicating from the on-premises
  // primary instance.
  DemoteMasterConfiguration replica_configuration = 4;

  // Flag to skip replication setup on the instance.
  bool skip_replication_setup = 5;
}

// This context is used to demote an existing standalone instance to be
// a Cloud SQL read replica for an external database server.
message DemoteContext {
  // This is always `sql#demoteContext`.
  string kind = 1;

  // Required. The name of the instance which acts as the on-premises primary
  // instance in the replication setup.
  string source_representative_instance_name = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Database instance failover context.
message FailoverContext {
  // The current settings version of this instance. Request will be rejected if
  // this version doesn't match the current settings version.
  int64 settings_version = 1;

  // This is always `sql#failoverContext`.
  string kind = 2;
}

// Database instance restore from backup context.
// Backup context contains source instance id and project id.
message RestoreBackupContext {
  // This is always `sql#restoreBackupContext`.
  string kind = 1;

  // The ID of the backup run to restore from.
  int64 backup_run_id = 2;

  // The ID of the instance that the backup was taken from.
  string instance_id = 3;

  // The full project ID of the source instance.
  string project = 4;
}

// Instance rotate server CA context.
message RotateServerCaContext {
  // This is always `sql#rotateServerCaContext`.
  string kind = 1;

  // The fingerprint of the next version to be rotated to. If left unspecified,
  // will be rotated to the most recently added server CA version.
  string next_version = 2;
}

// Database Instance truncate log context.
message TruncateLogContext {
  // This is always `sql#truncateLogContext`.
  string kind = 1;

  // The type of log to truncate. Valid values are `MYSQL_GENERAL_TABLE` and
  // `MYSQL_SLOW_TABLE`.
  string log_type = 2;
}

// External primary instance migration setting error/warning.
message SqlExternalSyncSettingError {
  enum SqlExternalSyncSettingErrorType {
    SQL_EXTERNAL_SYNC_SETTING_ERROR_TYPE_UNSPECIFIED = 0;

    CONNECTION_FAILURE = 1;

    BINLOG_NOT_ENABLED = 2;

    INCOMPATIBLE_DATABASE_VERSION = 3;

    REPLICA_ALREADY_SETUP = 4;

    // The replication user is missing privileges that are required.
    INSUFFICIENT_PRIVILEGE = 5;

    // Unsupported migration type.
    UNSUPPORTED_MIGRATION_TYPE = 6;

    // No pglogical extension installed on databases, applicable for postgres.
    NO_PGLOGICAL_INSTALLED = 7;

    // pglogical node already exists on databases, applicable for postgres.
    PGLOGICAL_NODE_ALREADY_EXISTS = 8;

    // The value of parameter wal_level is not set to logical.
    INVALID_WAL_LEVEL = 9;

    // The value of parameter shared_preload_libraries does not include
    // pglogical.
    INVALID_SHARED_PRELOAD_LIBRARY = 10;

    // The value of parameter max_replication_slots is not sufficient.
    INSUFFICIENT_MAX_REPLICATION_SLOTS = 11;

    // The value of parameter max_wal_senders is not sufficient.
    INSUFFICIENT_MAX_WAL_SENDERS = 12;

    // The value of parameter max_worker_processes is not sufficient.
    INSUFFICIENT_MAX_WORKER_PROCESSES = 13;

    // Extensions installed are either not supported or having unsupported
    // versions.
    UNSUPPORTED_EXTENSIONS = 14;

    // The value of parameter rds.logical_replication is not set to 1.
    INVALID_RDS_LOGICAL_REPLICATION = 15;

    // The primary instance logging setup doesn't allow EM sync.
    INVALID_LOGGING_SETUP = 16;

    // The primary instance database parameter setup doesn't allow EM sync.
    INVALID_DB_PARAM = 17;

    // The gtid_mode is not supported, applicable for MySQL.
    UNSUPPORTED_GTID_MODE = 18;

    // SQL Server Agent is not running.
    SQLSERVER_AGENT_NOT_RUNNING = 19;

    // The table definition is not support due to missing primary key or replica
    // identity, applicable for postgres.
    UNSUPPORTED_TABLE_DEFINITION = 20;

    // The customer has a definer that will break EM setup.
    UNSUPPORTED_DEFINER = 21;

    // SQL Server @@SERVERNAME does not match actual host name.
    SQLSERVER_SERVERNAME_MISMATCH = 22;

    // The primary instance has been setup and will fail the setup.
    PRIMARY_ALREADY_SETUP = 23;

    // The primary instance has unsupported binary log format.
    UNSUPPORTED_BINLOG_FORMAT = 24;

    // The primary instance's binary log retention setting.
    BINLOG_RETENTION_SETTING = 25;

    // The primary instance has tables with unsupported storage engine.
    UNSUPPORTED_STORAGE_ENGINE = 26;

    // Source has tables with limited support
    // eg: PostgreSQL tables without primary keys.
    LIMITED_SUPPORT_TABLES = 27;

    // The replica instance contains existing data.
    EXISTING_DATA_IN_REPLICA = 28;

    // The replication user is missing privileges that are optional.
    MISSING_OPTIONAL_PRIVILEGES = 29;

    // Additional BACKUP_ADMIN privilege is granted to the replication user
    // which may lock source MySQL 8 instance for DDLs during initial sync.
    RISKY_BACKUP_ADMIN_PRIVILEGE = 30;

    // The Cloud Storage bucket is missing necessary permissions.
    INSUFFICIENT_GCS_PERMISSIONS = 31;

    // The Cloud Storage bucket has an error in the file or contains invalid
    // file information.
    INVALID_FILE_INFO = 32;

    // The source instance has unsupported database settings for migration.
    UNSUPPORTED_DATABASE_SETTINGS = 33;

    // The replication user is missing parallel import specific privileges.
    // (e.g. LOCK TABLES) for MySQL.
    MYSQL_PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE = 34;

    // The global variable local_infile is off on external server replica.
    LOCAL_INFILE_OFF = 35;

    // This code instructs customers to turn on point-in-time recovery manually
    // for the instance after promoting the Cloud SQL for PostgreSQL instance.
    TURN_ON_PITR_AFTER_PROMOTE = 36;

    // The minor version of replica database is incompatible with the source.
    INCOMPATIBLE_DATABASE_MINOR_VERSION = 37;

    // This warning message indicates that Cloud SQL uses the maximum number of
    // subscriptions to migrate data from the source to the destination.
    SOURCE_MAX_SUBSCRIPTIONS = 38;

    // Unable to verify definers on the source for MySQL.
    UNABLE_TO_VERIFY_DEFINERS = 39;

    // If a time out occurs while the subscription counts are calculated, then
    // this value is set to 1. Otherwise, this value is set to 2.
    SUBSCRIPTION_CALCULATION_STATUS = 40;

    // Count of subscriptions needed to sync source data for PostgreSQL
    // database.
    PG_SUBSCRIPTION_COUNT = 41;

    // Final parallel level that is used to do migration.
    PG_SYNC_PARALLEL_LEVEL = 42;

    // The disk size of the replica instance is smaller than the data size of
    // the source instance.
    INSUFFICIENT_DISK_SIZE = 43;

    // The data size of the source instance is greater than 1 TB, the number of
    // cores of the replica instance is less than 8, and the memory of the
    // replica is less than 32 GB.
    INSUFFICIENT_MACHINE_TIER = 44;

    // The warning message indicates the unsupported extensions will not be
    // migrated to the destination.
    UNSUPPORTED_EXTENSIONS_NOT_MIGRATED = 45;

    // The warning message indicates the pg_cron extension and settings will not
    // be migrated to the destination.
    EXTENSIONS_NOT_MIGRATED = 46;

    // The error message indicates that pg_cron flags are enabled on the
    // destination which is not supported during the migration.
    PG_CRON_FLAG_ENABLED_IN_REPLICA = 47;

    // This error message indicates that the specified extensions are not
    // enabled on destination instance. For example, before you can migrate
    // data to the destination instance, you must enable the PGAudit extension
    // on the instance.
    EXTENSIONS_NOT_ENABLED_IN_REPLICA = 48;

    // The source database has generated columns that can't be migrated. Please
    // change them to regular columns before migration.
    UNSUPPORTED_COLUMNS = 49;
  }

  // Can be `sql#externalSyncSettingError` or
  // `sql#externalSyncSettingWarning`.
  string kind = 1;

  // Identifies the specific error that occurred.
  SqlExternalSyncSettingErrorType type = 2;

  // Additional information about the error encountered.
  string detail = 3;
}

// On-premises instance configuration.
message OnPremisesConfiguration {
  // The host and port of the on-premises instance in host:port format
  string host_port = 1;

  // This is always `sql#onPremisesConfiguration`.
  string kind = 2;

  // The username for connecting to on-premises instance.
  string username = 3;

  // The password for connecting to on-premises instance.
  string password = 4;

  // PEM representation of the trusted CA's x509 certificate.
  string ca_certificate = 5;

  // PEM representation of the replica's x509 certificate.
  string client_certificate = 6;

  // PEM representation of the replica's private key. The corresponsing public
  // key is encoded in the client's certificate.
  string client_key = 7;

  // The dump file to create the Cloud SQL replica.
  string dump_file_path = 8;

  // The reference to Cloud SQL instance if the source is Cloud SQL.
  InstanceReference source_instance = 15;
}

// Read-replica configuration for connecting to the primary instance.
message ReplicaConfiguration {
  // This is always `sql#replicaConfiguration`.
  string kind = 1;

  // MySQL specific configuration when replicating from a MySQL on-premises
  // primary instance. Replication configuration information such as the
  // username, password, certificates, and keys are not stored in the instance
  // metadata. The configuration information is used only to set up the
  // replication connection and is stored by MySQL in a file named
  // `master.info` in the data directory.
  MySqlReplicaConfiguration mysql_replica_configuration = 2;

  // Specifies if the replica is the failover target. If the field is set to
  // `true`, the replica will be designated as a failover replica. In case the
  // primary instance fails, the replica instance will be promoted as the new
  // primary instance. Only one replica can be specified as failover target, and
  // the replica has to be in different zone with the primary instance.
  google.protobuf.BoolValue failover_target = 3;

  // Optional. Specifies if a SQL Server replica is a cascadable replica. A
  // cascadable replica is a SQL Server cross region replica that supports
  // replica(s) under it.
  google.protobuf.BoolValue cascadable_replica = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Request to acquire a lease for SSRS.
message SqlInstancesAcquireSsrsLeaseRequest {
  // Required. Cloud SQL instance ID. This doesn't include the project ID. It's
  // composed of lowercase letters, numbers, and hyphens, and it must start with
  // a letter. The total length must be 98 characters or less (Example:
  // instance-id).
  string instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Project ID of the project that contains the instance (Example:
  // project-id).
  string project = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The request body.
  InstancesAcquireSsrsLeaseRequest body = 100
      [(google.api.field_behavior) = REQUIRED];
}

// Response for the acquire SSRS lease request.
message SqlInstancesAcquireSsrsLeaseResponse {
  // The unique identifier for this operation.
  string operation_id = 1;
}

// Request to release a lease for SSRS.
message SqlInstancesReleaseSsrsLeaseRequest {
  // Required. The Cloud SQL instance ID. This doesn't include the project ID.
  // The instance ID contains lowercase letters, numbers, and hyphens, and it
  // must start with a letter. This ID can have a maximum length of 98
  // characters.
  string instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The project ID that contains the instance.
  string project = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response for the release SSRS lease request.
message SqlInstancesReleaseSsrsLeaseResponse {
  // The unique identifier for this operation.
  string operation_id = 1;
}

enum SqlInstanceType {
  // This is an unknown Cloud SQL instance type.
  SQL_INSTANCE_TYPE_UNSPECIFIED = 0;

  // A regular Cloud SQL instance that is not replicating from a primary
  // instance.
  CLOUD_SQL_INSTANCE = 1;

  // An instance running on the customer's premises that is not managed by
  // Cloud SQL.
  ON_PREMISES_INSTANCE = 2;

  // A Cloud SQL instance acting as a read-replica.
  READ_REPLICA_INSTANCE = 3;
}

// The suspension reason of the database instance if the state is SUSPENDED.
enum SqlSuspensionReason {
  // This is an unknown suspension reason.
  SQL_SUSPENSION_REASON_UNSPECIFIED = 0;

  // The instance is suspended due to billing issues (for example:, GCP account
  // issue)
  BILLING_ISSUE = 2;

  // The instance is suspended due to illegal content (for example:, child
  // pornography, copyrighted material, etc.).
  LEGAL_ISSUE = 3;

  // The instance is causing operational issues (for example:, causing the
  // database to crash).
  OPERATIONAL_ISSUE = 4;

  // The KMS key used by the instance is either revoked or denied access to
  KMS_KEY_ISSUE = 5;
}
