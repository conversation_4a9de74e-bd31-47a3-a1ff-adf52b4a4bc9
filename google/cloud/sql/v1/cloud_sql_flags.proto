// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/sql/v1/cloud_sql_resources.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/sql/apiv1/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlFlagsProto";
option java_package = "com.google.cloud.sql.v1";

// LINT: LEGACY_NAMES

// Service to manage database flags for Cloud SQL instances.
service SqlFlagsService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Lists all available database flags for Cloud SQL instances.
  rpc List(SqlFlagsListRequest) returns (FlagsListResponse) {
    option (google.api.http) = {
      get: "/v1/flags"
    };
  }
}

// Flags list request.
message SqlFlagsListRequest {
  // Database type and version you want to retrieve flags for. By default, this
  // method returns flags for all database types and versions.
  string database_version = 1;
}

// Flags list response.
message FlagsListResponse {
  // This is always `sql#flagsList`.
  string kind = 1;

  // List of flags.
  repeated Flag items = 2;
}

// A flag resource.
message Flag {
  // This is the name of the flag. Flag names always use underscores, not
  // hyphens, for example: `max_allowed_packet`
  string name = 1;

  // The type of the flag. Flags are typed to being `BOOLEAN`, `STRING`,
  // `INTEGER` or `NONE`. `NONE` is used for flags that do not take a
  // value, such as `skip_grant_tables`.
  SqlFlagType type = 2;

  // The database version this flag applies to. Can be
  // MySQL instances: `MYSQL_8_0`, `MYSQL_8_0_18`, `MYSQL_8_0_26`, `MYSQL_5_7`,
  // or `MYSQL_5_6`. PostgreSQL instances: `POSTGRES_9_6`, `POSTGRES_10`,
  // `POSTGRES_11` or `POSTGRES_12`. SQL Server instances:
  // `SQLSERVER_2017_STANDARD`, `SQLSERVER_2017_ENTERPRISE`,
  // `SQLSERVER_2017_EXPRESS`, `SQLSERVER_2017_WEB`, `SQLSERVER_2019_STANDARD`,
  // `SQLSERVER_2019_ENTERPRISE`, `SQLSERVER_2019_EXPRESS`, or
  // `SQLSERVER_2019_WEB`.
  // See [the complete
  // list](/sql/docs/mysql/admin-api/rest/v1/SqlDatabaseVersion).
  repeated SqlDatabaseVersion applies_to = 3;

  // For `STRING` flags, a list of strings that the value can be set to.
  repeated string allowed_string_values = 4;

  // For `INTEGER` flags, the minimum allowed value.
  google.protobuf.Int64Value min_value = 5;

  // For `INTEGER` flags, the maximum allowed value.
  google.protobuf.Int64Value max_value = 6;

  // Indicates whether changing this flag will trigger a database restart. Only
  // applicable to Second Generation instances.
  google.protobuf.BoolValue requires_restart = 7;

  // This is always `sql#flag`.
  string kind = 8;

  // Whether or not the flag is considered in beta.
  google.protobuf.BoolValue in_beta = 9;

  // Use this field if only certain integers are accepted. Can be combined
  // with min_value and max_value to add additional values.
  repeated int64 allowed_int_values = 10;
}

enum SqlFlagType {
  // This is an unknown flag type.
  SQL_FLAG_TYPE_UNSPECIFIED = 0;

  // Boolean type flag.
  BOOLEAN = 1;

  // String type flag.
  STRING = 2;

  // Integer type flag.
  INTEGER = 3;

  // Flag type used for a server startup option.
  NONE = 4;

  // Type introduced specially for MySQL TimeZone offset. Accept a string value
  // with the format [-12:59, 13:00].
  MYSQL_TIMEZONE_OFFSET = 5;

  // Float type flag.
  FLOAT = 6;

  // Comma-separated list of the strings in a SqlFlagType enum.
  REPEATED_STRING = 7;
}
