// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/sql/v1/cloud_sql_resources.proto";

option go_package = "cloud.google.com/go/sql/apiv1/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlSslCertsProto";
option java_package = "com.google.cloud.sql.v1";

// LINT: LEGACY_NAMES

// Service to manage SSL certs for Cloud SQL instances.
service SqlSslCertsService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Deletes the SSL certificate. For First Generation instances, the
  // certificate remains valid until the instance is restarted.
  rpc Delete(SqlSslCertsDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/v1/projects/{project}/instances/{instance}/sslCerts/{sha1_fingerprint}"
    };
  }

  // Retrieves a particular SSL certificate.  Does not include the private key
  // (required for usage).  The private key must be saved from the response to
  // initial creation.
  rpc Get(SqlSslCertsGetRequest) returns (SslCert) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/sslCerts/{sha1_fingerprint}"
    };
  }

  // Creates an SSL certificate and returns it along with the private key and
  // server certificate authority.  The new certificate will not be usable until
  // the instance is restarted.
  rpc Insert(SqlSslCertsInsertRequest) returns (SslCertsInsertResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/sslCerts"
      body: "body"
    };
  }

  // Lists all of the current SSL certificates for the instance.
  rpc List(SqlSslCertsListRequest) returns (SslCertsListResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/sslCerts"
    };
  }
}

message SqlSslCertsDeleteRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Sha1 FingerPrint.
  string sha1_fingerprint = 3;
}

message SqlSslCertsGetRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  // Sha1 FingerPrint.
  string sha1_fingerprint = 3;
}

message SqlSslCertsInsertRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  SslCertsInsertRequest body = 100;
}

message SqlSslCertsListRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// SslCerts insert request.
message SslCertsInsertRequest {
  // User supplied name.  Must be a distinct name from the other certificates
  // for this instance.
  string common_name = 1;
}

// SslCert insert response.
message SslCertsInsertResponse {
  // This is always `sql#sslCertsInsert`.
  string kind = 1;

  // The operation to track the ssl certs insert request.
  Operation operation = 2;

  // The server Certificate Authority's certificate.  If this is missing you can
  // force a new one to be generated by calling resetSslConfig method on
  // instances resource.
  SslCert server_ca_cert = 3;

  // The new client certificate and private key.
  SslCertDetail client_cert = 4;
}

// SslCerts list response.
message SslCertsListResponse {
  // This is always `sql#sslCertsList`.
  string kind = 1;

  // List of client certificates for the instance.
  repeated SslCert items = 2;
}
