// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/sql/v1/cloud_sql_resources.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/sql/apiv1/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlBackupRunsProto";
option java_package = "com.google.cloud.sql.v1";

// LINT: LEGACY_NAMES

// Service for managing database backups.
service SqlBackupRunsService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Deletes the backup taken by a backup run.
  rpc Delete(SqlBackupRunsDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/v1/projects/{project}/instances/{instance}/backupRuns/{id}"
    };
  }

  // Retrieves a resource containing information about a backup run.
  rpc Get(SqlBackupRunsGetRequest) returns (BackupRun) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/backupRuns/{id}"
    };
  }

  // Creates a new backup run on demand.
  rpc Insert(SqlBackupRunsInsertRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/backupRuns"
      body: "body"
    };
  }

  // Lists all backup runs associated with the project or a given instance
  // and configuration in the reverse chronological order of the backup
  // initiation time.
  rpc List(SqlBackupRunsListRequest) returns (BackupRunsListResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/backupRuns"
    };
  }
}

// Backup runs delete request.
message SqlBackupRunsDeleteRequest {
  // The ID of the backup run to delete. To find a backup run ID, use the
  // [list](https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1/backupRuns/list)
  // method.
  int64 id = 1;

  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;
}

// Backup runs get request.
message SqlBackupRunsGetRequest {
  // The ID of this backup run.
  int64 id = 1;

  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;
}

// Backup runs insert request.
message SqlBackupRunsInsertRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  BackupRun body = 100;
}

// Backup runs list request.
message SqlBackupRunsListRequest {
  // Cloud SQL instance ID, or "-" for all instances. This does not include
  // the project ID.
  string instance = 1;

  // Maximum number of backup runs per response.
  int32 max_results = 2;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 3;

  // Project ID of the project that contains the instance.
  string project = 4;
}

// A BackupRun resource.
message BackupRun {
  // This is always `sql#backupRun`.
  string kind = 1;

  // The status of this run.
  SqlBackupRunStatus status = 2;

  // The time the run was enqueued in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp enqueued_time = 3;

  // The identifier for this backup run. Unique only for a specific Cloud SQL
  // instance.
  int64 id = 4;

  // The time the backup operation actually started in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp start_time = 5;

  // The time the backup operation completed in UTC timezone in
  // [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp end_time = 6;

  // Information about why the backup operation failed. This is only present if
  // the run has the FAILED status.
  OperationError error = 7;

  // The type of this run; can be either "AUTOMATED" or "ON_DEMAND" or "FINAL".
  // This field defaults to "ON_DEMAND" and is ignored, when specified for
  // insert requests.
  SqlBackupRunType type = 8;

  // The description of this run, only applicable to on-demand backups.
  string description = 9;

  // The start time of the backup window during which this the backup was
  // attempted in [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for
  // example `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp window_start_time = 10;

  // Name of the database instance.
  string instance = 11;

  // The URI of this resource.
  string self_link = 12;

  // Location of the backups.
  string location = 13;

  // Encryption configuration specific to a backup.
  DiskEncryptionConfiguration disk_encryption_configuration = 16;

  // Encryption status specific to a backup.
  DiskEncryptionStatus disk_encryption_status = 17;

  // Specifies the kind of backup, PHYSICAL or DEFAULT_SNAPSHOT.
  SqlBackupKind backup_kind = 19;

  // Backup time zone to prevent restores to an instance with
  // a different time zone. Now relevant only for SQL Server.
  string time_zone = 23;

  // Output only. The maximum chargeable bytes for the backup.
  optional int64 max_chargeable_bytes = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Backup run list results.
message BackupRunsListResponse {
  // This is always `sql#backupRunsList`.
  string kind = 1;

  // A list of backup runs in reverse chronological order of the enqueued time.
  repeated BackupRun items = 2;

  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 3;
}

// The status of a backup run.
enum SqlBackupRunStatus {
  // The status of the run is unknown.
  SQL_BACKUP_RUN_STATUS_UNSPECIFIED = 0;

  // The backup operation was enqueued.
  ENQUEUED = 1;

  // The backup is overdue across a given backup window. Indicates a
  // problem. Example: Long-running operation in progress during
  // the whole window.
  OVERDUE = 2;

  // The backup is in progress.
  RUNNING = 3;

  // The backup failed.
  FAILED = 4;

  // The backup was successful.
  SUCCESSFUL = 5;

  // The backup was skipped (without problems) for a given backup
  // window. Example: Instance was idle.
  SKIPPED = 6;

  // The backup is about to be deleted.
  DELETION_PENDING = 7;

  // The backup deletion failed.
  DELETION_FAILED = 8;

  // The backup has been deleted.
  DELETED = 9;
}

// Defines the supported backup kinds.
enum SqlBackupKind {
  // This is an unknown BackupKind.
  SQL_BACKUP_KIND_UNSPECIFIED = 0;

  // The snapshot based backups
  SNAPSHOT = 1;

  // Physical backups
  PHYSICAL = 2;
}

// Type of backup (i.e. automated, on demand, etc).
enum SqlBackupRunType {
  // This is an unknown BackupRun type.
  SQL_BACKUP_RUN_TYPE_UNSPECIFIED = 0;

  // The backup schedule automatically triggers a backup.
  AUTOMATED = 1;

  // The user manually triggers a backup.
  ON_DEMAND = 2;
}
