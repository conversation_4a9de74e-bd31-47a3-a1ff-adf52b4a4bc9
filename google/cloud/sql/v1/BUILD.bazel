# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

load("@com_google_googleapis_imports//:imports.bzl", "cc_grpc_library", "cc_proto_library", "csharp_grpc_library", "csharp_proto_library", "go_proto_library", "java_grpc_library", "java_proto_library", "nodejs_gapic_assembly_pkg", "nodejs_gapic_library", "php_gapic_assembly_pkg", "php_gapic_library", "php_proto_library", "proto_library_with_info", "py_gapic_assembly_pkg", "py_gapic_library", "py_test", "ruby_cloud_gapic_library", "ruby_gapic_assembly_pkg", "ruby_grpc_library", "ruby_proto_library")

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "sql_proto",
    srcs = [
        "cloud_sql_available_database_versions.proto",
        "cloud_sql_backup_runs.proto",
        "cloud_sql_connect.proto",
        "cloud_sql_databases.proto",
        "cloud_sql_events.proto",
        "cloud_sql_flags.proto",
        "cloud_sql_iam_policies.proto",
        "cloud_sql_instance_names.proto",
        "cloud_sql_instances.proto",
        "cloud_sql_operations.proto",
        "cloud_sql_regions.proto",
        "cloud_sql_resources.proto",
        "cloud_sql_ssl_certs.proto",
        "cloud_sql_tiers.proto",
        "cloud_sql_users.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "sql_proto_with_info",
    deps = [
        ":sql_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

java_proto_library(
    name = "sql_java_proto",
    deps = [":sql_proto"],
)

java_grpc_library(
    name = "sql_java_grpc",
    srcs = [":sql_proto"],
    deps = [":sql_java_proto"],
)

# Excluded because of generation errors.
#
# java_gapic_library(
#     name = "sql_java_gapic",
#     srcs = [":sql_proto_with_info"],
#     gapic_yaml = None,
#     grpc_service_config = "sqladmin_grpc_service_config.json",
#     service_yaml = "sqladmin_v1.yaml",
#     test_deps = [
#         ":sql_java_grpc",
#     ],
#     deps = [
#         ":sql_java_proto",
#         "//google/api:api_java_proto",
#     ],
# )

# java_gapic_test(
#     name = "sql_java_gapic_test_suite",
#     test_classes = [
#         "com.google.cloud.sql.v1.SqlBackupRunsServiceClientTest",
#         "com.google.cloud.sql.v1.SqlConnectServiceClientTest",
#         "com.google.cloud.sql.v1.SqlDatabasesServiceClientTest",
#         "com.google.cloud.sql.v1.SqlFlagsServiceClientTest",
#         "com.google.cloud.sql.v1.SqlInstanceNamesServiceClientTest",
#         "com.google.cloud.sql.v1.SqlInstancesServiceClientTest",
#         "com.google.cloud.sql.v1.SqlOperationsServiceClientTest",
#         "com.google.cloud.sql.v1.SqlRegionsServiceClientTest",
#         "com.google.cloud.sql.v1.SqlSslCertsServiceClientTest",
#         "com.google.cloud.sql.v1.SqlTiersServiceClientTest",
#         "com.google.cloud.sql.v1.SqlUsersServiceClientTest",
#     ],
#     runtime_deps = [":sql_java_gapic_test"],
# )

# Open Source Packages
# java_gapic_assembly_gradle_pkg(
#     name = "google-cloud-sql-v1-java",
#     deps = [
#         ":sql_java_gapic",
#         ":sql_java_grpc",
#         ":sql_java_proto",
#         ":sql_proto",
#     ],
#    include_samples = True,
# )

go_proto_library(
    name = "sql_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/sql/apiv1/sqlpb",
    protos = [":sql_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

# Excluded because of generation errors.
#
# go_gapic_library(
#     name = "sql_go_gapic",
#     srcs = [":sql_proto_with_info"],
#     grpc_service_config = "sqladmin_grpc_service_config.json",
#     importpath = "cloud.google.com/go/sql/apiv1;sql",
#     service_yaml = "sqladmin_v1.yaml",
#     metadata = True,
#     release_level = "beta",
#     deps = [
#         ":sql_go_proto",
#     ],
# )

# Open Source Packages
# go_gapic_assembly_pkg(
#     name = "gapi-cloud-sql-v1-go",
#     deps = [
#         ":sql_go_gapic",
#         ":sql_go_gapic_srcjar-snippets.srcjar",
#         ":sql_go_gapic_srcjar-test.srcjar",
#         ":sql_go_gapic_srcjar-metadata.srcjar",
#         ":sql_go_proto",
#     ],
# )

py_gapic_library(
    name = "sql_py_gapic",
    srcs = [":sql_proto"],
    grpc_service_config = "sqladmin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "sqladmin_v1.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "sql_py_gapic_test",
    srcs = [
        "sql_py_gapic_pytest.py",
        "sql_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":sql_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "sql-v1-py",
    deps = [
        ":sql_py_gapic",
    ],
)

php_proto_library(
    name = "sql_php_proto",
    deps = [":sql_proto"],
)

php_gapic_library(
    name = "sql_php_gapic",
    srcs = [":sql_proto_with_info"],
    grpc_service_config = "sqladmin_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "sqladmin_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":sql_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-sql-v1-php",
    deps = [
        ":sql_php_gapic",
        ":sql_php_proto",
    ],
)

nodejs_gapic_library(
    name = "sql_nodejs_gapic",
    package_name = "@google-cloud/sql",
    src = ":sql_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "sqladmin_grpc_service_config.json",
    package = "google.cloud.sql.v1",
    rest_numeric_enums = True,
    service_yaml = "sqladmin_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "sql-v1-nodejs",
    deps = [
        ":sql_nodejs_gapic",
        ":sql_proto",
    ],
)

ruby_proto_library(
    name = "sql_ruby_proto",
    deps = [":sql_proto"],
)

ruby_grpc_library(
    name = "sql_ruby_grpc",
    srcs = [":sql_proto"],
    deps = [":sql_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "sql_ruby_gapic",
    srcs = [":sql_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-sql-v1"],
    grpc_service_config = "sqladmin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "sqladmin_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":sql_ruby_grpc",
        ":sql_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-sql-v1-ruby",
    deps = [
        ":sql_ruby_gapic",
        ":sql_ruby_grpc",
        ":sql_ruby_proto",
    ],
)

csharp_proto_library(
    name = "sql_csharp_proto",
    extra_opts = [],
    deps = [":sql_proto"],
)

csharp_grpc_library(
    name = "sql_csharp_grpc",
    srcs = [":sql_proto"],
    deps = [":sql_csharp_proto"],
)

# Excluded because of generation errors.
#
# csharp_gapic_library(
#     name = "sql_csharp_gapic",
#     srcs = [":sql_proto_with_info"],
#     common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
#     grpc_service_config = "sqladmin_grpc_service_config.json",
#     service_yaml = "sqladmin_v1.yaml",
#     deps = [
#         ":sql_csharp_grpc",
#         ":sql_csharp_proto",
#     ],
# )

# # Open Source Packages
# csharp_gapic_assembly_pkg(
#     name = "google-cloud-sql-v1-csharp",
#     deps = [
#         ":sql_csharp_gapic",
#         ":sql_csharp_grpc",
#         ":sql_csharp_proto",
#     ],
# )

cc_proto_library(
    name = "sql_cc_proto",
    deps = [":sql_proto"],
)

cc_grpc_library(
    name = "sql_cc_grpc",
    srcs = [":sql_proto"],
    grpc_only = True,
    deps = [":sql_cc_proto"],
)
