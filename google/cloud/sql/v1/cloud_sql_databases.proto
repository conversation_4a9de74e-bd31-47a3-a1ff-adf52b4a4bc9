// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/sql/v1/cloud_sql_resources.proto";

option go_package = "cloud.google.com/go/sql/apiv1/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlDatabasesProto";
option java_package = "com.google.cloud.sql.v1";

// LINT: LEGACY_NAMES

// Service to manage databases.
service SqlDatabasesService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Deletes a database from a Cloud SQL instance.
  rpc Delete(SqlDatabasesDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/v1/projects/{project}/instances/{instance}/databases/{database}"
    };
  }

  // Retrieves a resource containing information about a database inside a Cloud
  // SQL instance.
  rpc Get(SqlDatabasesGetRequest) returns (Database) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/databases/{database}"
    };
  }

  // Inserts a resource containing information about a database inside a Cloud
  // SQL instance.
  //
  // **Note:** You can't modify the default character set and collation.
  rpc Insert(SqlDatabasesInsertRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1/projects/{project}/instances/{instance}/databases"
      body: "body"
    };
  }

  // Lists databases in the specified Cloud SQL instance.
  rpc List(SqlDatabasesListRequest) returns (DatabasesListResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project}/instances/{instance}/databases"
    };
  }

  // Partially updates a resource containing information about a database inside
  // a Cloud SQL instance. This method supports patch semantics.
  rpc Patch(SqlDatabasesUpdateRequest) returns (Operation) {
    option (google.api.http) = {
      patch: "/v1/projects/{project}/instances/{instance}/databases/{database}"
      body: "body"
    };
  }

  // Updates a resource containing information about a database inside a Cloud
  // SQL instance.
  rpc Update(SqlDatabasesUpdateRequest) returns (Operation) {
    option (google.api.http) = {
      put: "/v1/projects/{project}/instances/{instance}/databases/{database}"
      body: "body"
    };
  }
}

// Database delete request.
message SqlDatabasesDeleteRequest {
  // Name of the database to be deleted in the instance.
  string database = 1;

  // Database instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;
}

// Database get request.
message SqlDatabasesGetRequest {
  // Name of the database in the instance.
  string database = 1;

  // Database instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;
}

// Database insert request.
message SqlDatabasesInsertRequest {
  // Database instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  Database body = 100;
}

// Database list request.
message SqlDatabasesListRequest {
  // Cloud SQL instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

// Database update request.
message SqlDatabasesUpdateRequest {
  // Name of the database to be updated in the instance.
  string database = 1;

  // Database instance ID. This does not include the project ID.
  string instance = 2;

  // Project ID of the project that contains the instance.
  string project = 3;

  Database body = 100;
}

// Database list response.
message DatabasesListResponse {
  // This is always `sql#databasesList`.
  string kind = 1;

  // List of database resources in the instance.
  repeated Database items = 2;
}
