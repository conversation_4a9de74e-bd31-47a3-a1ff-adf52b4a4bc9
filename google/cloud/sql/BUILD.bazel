# This build file includes a target for the Ruby wrapper library for
# google-cloud-sql.

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

# Generates a Ruby wrapper client for sqladmin.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "sqladmin_ruby_wrapper",
    srcs = ["//google/cloud/sql/v1:sql_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-sql",
        "ruby-cloud-wrapper-of=v1:0.0",
    ],
    service_yaml = "//google/cloud/sql/v1:sqladmin_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-sql-ruby",
    deps = [
        ":sqladmin_ruby_wrapper",
    ],
)
