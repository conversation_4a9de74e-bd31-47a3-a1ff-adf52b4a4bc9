// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.saasaccelerator.management.logs.v1;

import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/saasaccelerator/management/logs/apiv1/logspb;logspb";
option java_multiple_files = true;
option java_outer_classname = "NotificationServicePayloadProto";
option java_package = "com.google.cloud.saasaccelerator.management.logs.v1";

// Payload proto for Notification logs.
message NotificationStage {
  // Types of Notification Status.
  enum Stage {
    // Default.
    STAGE_UNSPECIFIED = 0;

    // Notification was sent.
    SENT = 1;

    // Notification failed to send.
    SEND_FAILURE = 2;

    // Notification was dropped.
    DROPPED = 3;
  }

  // Event that triggered the notification.
  enum Event {
    // Default value.
    EVENT_UNSPECIFIED = 0;

    // When a health status has been changed.
    HEALTH_STATUS_CHANGE = 1;
  }

  // The type of the Notification Service event.
  Stage stage = 1;

  // Time of the NotificationServiceEvent.
  google.protobuf.Timestamp event_time = 2;

  // The id of the notification.
  string notification_id = 3;

  // The event that triggered the notification.
  Event event = 4;

  // Message to denote the error related to the event if applicable.
  string message = 5;
}
