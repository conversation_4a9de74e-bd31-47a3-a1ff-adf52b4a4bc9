// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.saasaccelerator.management.logs.v1;

option go_package = "cloud.google.com/go/saasaccelerator/management/logs/apiv1/logspb;logspb";
option java_multiple_files = true;
option java_outer_classname = "SaasInstancePayloadProto";
option java_package = "com.google.cloud.saasaccelerator.management.logs.v1";

message InstanceEvent {
  // The type of the event, e.g. Create, Update, etc.
  string verb = 1;

  // The state of the instance, e.g. "RETRYING_CREATE_INSTANCE".
  string stage = 2;

  // A human-readable log message, e.g. "error in stage: CREATING, err: location
  // not available".
  string msg = 3;

  // The ID to uniquely locate all logs associated with a given request.
  string trace_id = 4;

  // The instance node which is the subject of the operation, if known.
  // Currently unused as tf actuation does not manage nodes.
  string node_id = 5;
}
