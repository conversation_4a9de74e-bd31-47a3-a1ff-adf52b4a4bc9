// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.parametermanager.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/iam/v1/resource_policy_member.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ParameterManager.V1";
option go_package = "cloud.google.com/go/parametermanager/apiv1/parametermanagerpb;parametermanagerpb";
option java_multiple_files = true;
option java_outer_classname = "V1mainProto";
option java_package = "com.google.cloud.parametermanager.v1";
option php_namespace = "Google\\Cloud\\ParameterManager\\V1";
option ruby_package = "Google::Cloud::ParameterManager::V1";

// Service describing handlers for resources
service ParameterManager {
  option (google.api.default_host) = "parametermanager.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Parameters in a given project and location.
  rpc ListParameters(ListParametersRequest) returns (ListParametersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/parameters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Parameter.
  rpc GetParameter(GetParameterRequest) returns (Parameter) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/parameters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Parameter in a given project and location.
  rpc CreateParameter(CreateParameterRequest) returns (Parameter) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/parameters"
      body: "parameter"
    };
    option (google.api.method_signature) = "parent,parameter,parameter_id";
  }

  // Updates a single Parameter.
  rpc UpdateParameter(UpdateParameterRequest) returns (Parameter) {
    option (google.api.http) = {
      patch: "/v1/{parameter.name=projects/*/locations/*/parameters/*}"
      body: "parameter"
    };
    option (google.api.method_signature) = "parameter,update_mask";
  }

  // Deletes a single Parameter.
  rpc DeleteParameter(DeleteParameterRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/parameters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ParameterVersions in a given project, location, and parameter.
  rpc ListParameterVersions(ListParameterVersionsRequest)
      returns (ListParameterVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/parameters/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ParameterVersion.
  rpc GetParameterVersion(GetParameterVersionRequest)
      returns (ParameterVersion) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/parameters/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets rendered version of a ParameterVersion.
  rpc RenderParameterVersion(RenderParameterVersionRequest)
      returns (RenderParameterVersionResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/parameters/*/versions/*}:render"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ParameterVersion in a given project, location, and parameter.
  rpc CreateParameterVersion(CreateParameterVersionRequest)
      returns (ParameterVersion) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/parameters/*}/versions"
      body: "parameter_version"
    };
    option (google.api.method_signature) =
        "parent,parameter_version,parameter_version_id";
  }

  // Updates a single ParameterVersion.
  rpc UpdateParameterVersion(UpdateParameterVersionRequest)
      returns (ParameterVersion) {
    option (google.api.http) = {
      patch: "/v1/{parameter_version.name=projects/*/locations/*/parameters/*/versions/*}"
      body: "parameter_version"
    };
    option (google.api.method_signature) = "parameter_version,update_mask";
  }

  // Deletes a single ParameterVersion.
  rpc DeleteParameterVersion(DeleteParameterVersionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/parameters/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Option to specify the format of a Parameter resource (UNFORMATTED / YAML /
// JSON). This option is user specified at the time of creation of the resource
// and is immutable.
enum ParameterFormat {
  // The default / unset value.
  // The API will default to the UNFORMATTED format.
  PARAMETER_FORMAT_UNSPECIFIED = 0;

  // Unformatted.
  UNFORMATTED = 1;

  // YAML format.
  YAML = 2;

  // JSON format.
  JSON = 3;
}

// Option for requesting only metadata, or user provided payload
// of a ParameterVersion resource.
enum View {
  // The default / unset value.
  // The API will default to the FULL view..
  VIEW_UNSPECIFIED = 0;

  // Include only the metadata for the resource.
  BASIC = 1;

  // Include metadata & other relevant payload data as well.
  // This is the default view.
  FULL = 2;
}

// Message describing Parameter resource
message Parameter {
  option (google.api.resource) = {
    type: "parametermanager.googleapis.com/Parameter"
    pattern: "projects/{project}/locations/{location}/parameters/{parameter}"
    plural: "parameters"
    singular: "parameter"
  };

  // Identifier. [Output only] The resource name of the Parameter in the format
  // `projects/*/locations/*/parameters/*`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. [Output only] Create time stamp
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update time stamp
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels as key value pairs
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the format of a Parameter.
  ParameterFormat format = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. [Output-only] policy member strings of a Google Cloud
  // resource.
  google.iam.v1.ResourcePolicyMember policy_member = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of Parameters
message ListParametersRequest {
  // Required. Parent value for ListParametersRequest in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "parametermanager.googleapis.com/Parameter"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing Parameters
message ListParametersResponse {
  // The list of Parameters
  repeated Parameter parameters = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Unordered list. Locations that could not be reached.
  repeated string unreachable = 3
      [(google.api.field_behavior) = UNORDERED_LIST];
}

// Message for getting a Parameter
message GetParameterRequest {
  // Required. Name of the resource in the format
  // `projects/*/locations/*/parameters/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parametermanager.googleapis.com/Parameter"
    }
  ];
}

// Message for creating a Parameter
message CreateParameterRequest {
  // Required. Value for parent in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "parametermanager.googleapis.com/Parameter"
    }
  ];

  // Required. Id of the Parameter resource
  string parameter_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Parameter resource being created
  Parameter parameter = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for updating a Parameter
message UpdateParameterRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // Parameter resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A mutable field will be overwritten if it is in the
  // mask. If the user does not provide a mask then all mutable fields present
  // in the request will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The Parameter resource being updated
  Parameter parameter = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for deleting a Parameter
message DeleteParameterRequest {
  // Required. Name of the resource in the format
  // `projects/*/locations/*/parameters/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parametermanager.googleapis.com/Parameter"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message describing ParameterVersion resource
message ParameterVersion {
  option (google.api.resource) = {
    type: "parametermanager.googleapis.com/ParameterVersion"
    pattern: "projects/{project}/locations/{location}/parameters/{parameter}/versions/{parameter_version}"
    plural: "parameterVersions"
    singular: "parameterVersion"
  };

  // Identifier. [Output only] The resource name of the ParameterVersion in the
  // format `projects/*/locations/*/parameters/*/versions/*`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. [Output only] Create time stamp
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update time stamp
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Disabled boolean to determine if a ParameterVersion acts as a
  // metadata only resource (payload is never returned if disabled is true). If
  // true any calls will always default to BASIC view even if the user
  // explicitly passes FULL view as part of the request. A render call on a
  // disabled resource fails with an error. Default value is False.
  bool disabled = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. Payload content of a ParameterVersion resource.  This
  // is only returned when the request provides the View value of FULL (default
  // for GET request).
  ParameterVersionPayload payload = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];
}

// Message for storing a ParameterVersion resource's payload data
message ParameterVersionPayload {
  // Required. bytes data for storing payload.
  bytes data = 1 [(google.api.field_behavior) = REQUIRED];
}

// Message for requesting list of ParameterVersions
message ListParameterVersionsRequest {
  // Required. Parent value for ListParameterVersionsRequest in the format
  // `projects/*/locations/*/parameters/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "parametermanager.googleapis.com/ParameterVersion"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing ParameterVersions
message ListParameterVersionsResponse {
  // The list of ParameterVersions
  repeated ParameterVersion parameter_versions = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Unordered list. Locations that could not be reached.
  repeated string unreachable = 3
      [(google.api.field_behavior) = UNORDERED_LIST];
}

// Message for getting a ParameterVersion
message GetParameterVersionRequest {
  // Required. Name of the resource in the format
  // `projects/*/locations/*/parameters/*/versions/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parametermanager.googleapis.com/ParameterVersion"
    }
  ];

  // Optional. View of the ParameterVersion.
  // In the default FULL view, all metadata & payload associated with the
  // ParameterVersion will be returned.
  View view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Message for getting a ParameterVersionRender
message RenderParameterVersionRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parametermanager.googleapis.com/ParameterVersion"
    }
  ];
}

// Message describing RenderParameterVersionResponse resource
message RenderParameterVersionResponse {
  // Output only. Resource identifier of a ParameterVersion in the format
  // `projects/*/locations/*/parameters/*/versions/*`.
  string parameter_version = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "parametermanager.googleapis.com/ParameterVersion"
    }
  ];

  // Payload content of a ParameterVersion resource.
  ParameterVersionPayload payload = 2;

  // Output only. Server generated rendered version of the user provided payload
  // data (ParameterVersionPayload) which has substitutions of all (if any)
  // references to a SecretManager SecretVersion resources. This substituion
  // only works for a Parameter which is in JSON or YAML format.
  bytes rendered_payload = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for creating a ParameterVersion
message CreateParameterVersionRequest {
  // Required. Value for parent in the format
  // `projects/*/locations/*/parameters/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "parametermanager.googleapis.com/ParameterVersion"
    }
  ];

  // Required. Id of the ParameterVersion resource
  string parameter_version_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ParameterVersion resource being created
  ParameterVersion parameter_version = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for updating a ParameterVersion
message UpdateParameterVersionRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // ParameterVersion resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A mutable field will be overwritten if it is in the
  // mask. If the user does not provide a mask then all mutable fields present
  // in the request will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The ParameterVersion resource being updated
  ParameterVersion parameter_version = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for deleting a ParameterVersion
message DeleteParameterVersionRequest {
  // Required. Name of the resource in the format
  // `projects/*/locations/*/parameters/*/versions/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parametermanager.googleapis.com/ParameterVersion"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}
