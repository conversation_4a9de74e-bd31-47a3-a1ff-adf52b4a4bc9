# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "parametermanager_proto",
    srcs = [
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:resource_policy_member_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "parametermanager_proto_with_info",
    deps = [
        ":parametermanager_proto",
        "//google/cloud/location:location_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "parametermanager_java_proto",
    deps = [":parametermanager_proto"],
)

java_grpc_library(
    name = "parametermanager_java_grpc",
    srcs = [":parametermanager_proto"],
    deps = [":parametermanager_java_proto"],
)

java_gapic_library(
    name = "parametermanager_java_gapic",
    srcs = [":parametermanager_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "parametermanager_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "parametermanager_v1.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":parametermanager_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":parametermanager_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "parametermanager_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.parametermanager.v1.ParameterManagerClientHttpJsonTest",
        "com.google.cloud.parametermanager.v1.ParameterManagerClientTest",
    ],
    runtime_deps = [":parametermanager_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-parametermanager-v1-java",
    transport = "grpc+rest",
    deps = [
        ":parametermanager_java_gapic",
        ":parametermanager_java_grpc",
        ":parametermanager_java_proto",
        ":parametermanager_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "parametermanager_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/parametermanager/apiv1/parametermanagerpb",
    protos = [":parametermanager_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

go_gapic_library(
    name = "parametermanager_go_gapic",
    srcs = [":parametermanager_proto_with_info"],
    grpc_service_config = "parametermanager_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/parametermanager/apiv1;parametermanager",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "parametermanager_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":parametermanager_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-parametermanager-v1-go",
    deps = [
        ":parametermanager_go_gapic",
        ":parametermanager_go_gapic_srcjar-test.srcjar",
        ":parametermanager_go_gapic_srcjar-metadata.srcjar",
        ":parametermanager_go_gapic_srcjar-snippets.srcjar",
        ":parametermanager_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "parametermanager_py_gapic",
    srcs = [":parametermanager_proto"],
    grpc_service_config = "parametermanager_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "parametermanager_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "parametermanager_py_gapic_test",
    srcs = [
        "parametermanager_py_gapic_pytest.py",
        "parametermanager_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":parametermanager_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "parametermanager-v1-py",
    deps = [
        ":parametermanager_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "parametermanager_php_proto",
    deps = [":parametermanager_proto"],
)

php_gapic_library(
    name = "parametermanager_php_gapic",
    srcs = [":parametermanager_proto_with_info"],
    grpc_service_config = "parametermanager_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "parametermanager_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":parametermanager_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-parametermanager-v1-php",
    deps = [
        ":parametermanager_php_gapic",
        ":parametermanager_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "parametermanager_nodejs_gapic",
    package_name = "@google-cloud/parametermanager",
    src = ":parametermanager_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "parametermanager_v1_grpc_service_config.json",
    package = "google.cloud.parametermanager.v1",
    rest_numeric_enums = True,
    service_yaml = "parametermanager_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "parametermanager-v1-nodejs",
    deps = [
        ":parametermanager_nodejs_gapic",
        ":parametermanager_proto",
        "//google/iam/v1:resource_policy_member_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "parametermanager_ruby_proto",
    deps = [":parametermanager_proto"],
)

ruby_grpc_library(
    name = "parametermanager_ruby_grpc",
    srcs = [":parametermanager_proto"],
    deps = [":parametermanager_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "parametermanager_ruby_gapic",
    srcs = [":parametermanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-parametermanager-v1",
    ],
    grpc_service_config = "parametermanager_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "parametermanager_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":parametermanager_ruby_grpc",
        ":parametermanager_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-parametermanager-v1-ruby",
    deps = [
        ":parametermanager_ruby_gapic",
        ":parametermanager_ruby_grpc",
        ":parametermanager_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "parametermanager_csharp_proto",
    extra_opts = [],
    deps = [":parametermanager_proto"],
)

csharp_grpc_library(
    name = "parametermanager_csharp_grpc",
    srcs = [":parametermanager_proto"],
    deps = [":parametermanager_csharp_proto"],
)

csharp_gapic_library(
    name = "parametermanager_csharp_gapic",
    srcs = [":parametermanager_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "parametermanager_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "parametermanager_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":parametermanager_csharp_grpc",
        ":parametermanager_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-parametermanager-v1-csharp",
    deps = [
        ":parametermanager_csharp_gapic",
        ":parametermanager_csharp_grpc",
        ":parametermanager_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "parametermanager_cc_proto",
    deps = [":parametermanager_proto"],
)

cc_grpc_library(
    name = "parametermanager_cc_grpc",
    srcs = [":parametermanager_proto"],
    grpc_only = True,
    deps = [":parametermanager_cc_proto"],
)
