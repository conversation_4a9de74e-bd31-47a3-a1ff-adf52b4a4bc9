type: google.api.Service
config_version: 3
name: parametermanager.googleapis.com
title: Parameter Manager API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.parametermanager.v1.ParameterManager

documentation:
  summary: |-
    (Public Preview) Parameter Manager is a single source of truth to store,
    access and manage the lifecycle of your workload parameters. Parameter
    Manager aims to make management of sensitive application parameters
    effortless for customers without diminishing focus on security.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.parametermanager.v1.ParameterManager.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1706749&template=0
  documentation_uri: https://cloud.google.com/secret-manager/parameter-manager/docs/overview
  api_short_name: parametermanager
  github_label: 'api: parametermanager'
  doc_tag_prefix: parametermanager
  organization: CLOUD
  library_settings:
  - version: google.cloud.parametermanager.v1
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/secret-manager/parameter-manager/docs/reference/rpc
