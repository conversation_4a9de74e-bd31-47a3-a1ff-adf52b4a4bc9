{"methodConfig": [{"name": [{"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "ListParameters"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "GetParameter"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "ListParameterVersions"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "GetParameterVersion"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "RenderParameterVersion"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "CreateParameter"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "UpdateParameter"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "DeleteParameter"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "CreateParameterVersion"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "UpdateParameterVersion"}, {"service": "google.cloud.parametermanager.v1.ParameterManager", "method": "DeleteParameterVersion"}], "timeout": "60s"}]}