{"methodConfig": [{"name": [{"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "ListSecrets"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "CreateSecret"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "AddSecretVersion"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "GetSecret"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "UpdateSecret"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "DeleteSecret"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "ListSecretVersions"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "GetSecretVersion"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "DisableSecretVersion"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "EnableSecretVersion"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "DestroySecretVersion"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "SetIamPolicy"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "GetIamPolicy"}, {"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "TestIamPermissions"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.secretmanager.v1beta2.SecretManagerService", "method": "AccessSecretVersion"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "2s", "maxBackoff": "60s", "backoffMultiplier": 2.0, "retryableStatusCodes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}]}