type: google.api.Service
config_version: 3
name: secretmanager.googleapis.com
title: Secret Manager API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.secretmanager.v1beta2.SecretManagerService

documentation:
  summary: |-
    Stores sensitive data such as API keys, passwords, and certificates.
    Provides convenience while improving security.
  overview: Secret Manager Overview
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1beta2/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1beta2/{name=projects/*}/locations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.secretmanager.v1beta2.SecretManagerService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=784854&template=1380926
  documentation_uri: https://cloud.google.com/secret-manager/docs/overview
  api_short_name: secretmanager
  github_label: 'api: secretmanager'
  doc_tag_prefix: secretmanager
  organization: CLOUD
  library_settings:
  - version: google.cloud.secretmanager.v1beta2
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/secret-manager/docs/reference/rpc
