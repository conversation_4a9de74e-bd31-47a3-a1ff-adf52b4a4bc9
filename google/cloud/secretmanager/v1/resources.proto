// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.secretmanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecretManager.V1";
option go_package = "cloud.google.com/go/secretmanager/apiv1/secretmanagerpb;secretmanagerpb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.cloud.secretmanager.v1";
option objc_class_prefix = "GSM";
option php_namespace = "Google\\Cloud\\SecretManager\\V1";
option ruby_package = "Google::Cloud::SecretManager::V1";

// A [Secret][google.cloud.secretmanager.v1.Secret] is a logical secret whose
// value and versions can be accessed.
//
// A [Secret][google.cloud.secretmanager.v1.Secret] is made up of zero or more
// [SecretVersions][google.cloud.secretmanager.v1.SecretVersion] that represent
// the secret data.
message Secret {
  option (google.api.resource) = {
    type: "secretmanager.googleapis.com/Secret"
    pattern: "projects/{project}/secrets/{secret}"
    pattern: "projects/{project}/locations/{location}/secrets/{secret}"
    plural: "secrets"
    singular: "secret"
  };

  // Output only. The resource name of the
  // [Secret][google.cloud.secretmanager.v1.Secret] in the format
  // `projects/*/secrets/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. The replication policy of the secret data attached to
  // the [Secret][google.cloud.secretmanager.v1.Secret].
  //
  // The replication policy cannot be changed after the Secret has been created.
  Replication replication = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Output only. The time at which the
  // [Secret][google.cloud.secretmanager.v1.Secret] was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels assigned to this Secret.
  //
  // Label keys must be between 1 and 63 characters long, have a UTF-8 encoding
  // of maximum 128 bytes, and must conform to the following PCRE regular
  // expression: `[\p{Ll}\p{Lo}][\p{Ll}\p{Lo}\p{N}_-]{0,62}`
  //
  // Label values must be between 0 and 63 characters long, have a UTF-8
  // encoding of maximum 128 bytes, and must conform to the following PCRE
  // regular expression: `[\p{Ll}\p{Lo}\p{N}_-]{0,63}`
  //
  // No more than 64 labels can be assigned to a given resource.
  map<string, string> labels = 4;

  // Optional. A list of up to 10 Pub/Sub topics to which messages are published
  // when control plane operations are called on the secret or its versions.
  repeated Topic topics = 5 [(google.api.field_behavior) = OPTIONAL];

  // Expiration policy attached to the
  // [Secret][google.cloud.secretmanager.v1.Secret]. If specified the
  // [Secret][google.cloud.secretmanager.v1.Secret] and all
  // [SecretVersions][google.cloud.secretmanager.v1.SecretVersion] will be
  // automatically deleted at expiration. Expired secrets are irreversibly
  // deleted.
  //
  // Expiration is *not* the recommended way to set time-based permissions. [IAM
  // Conditions](https://cloud.google.com/secret-manager/docs/access-control#conditions)
  // is recommended for granting time-based permissions because the operation
  // can be reversed.
  oneof expiration {
    // Optional. Timestamp in UTC when the
    // [Secret][google.cloud.secretmanager.v1.Secret] is scheduled to expire.
    // This is always provided on output, regardless of what was sent on input.
    google.protobuf.Timestamp expire_time = 6
        [(google.api.field_behavior) = OPTIONAL];

    // Input only. The TTL for the
    // [Secret][google.cloud.secretmanager.v1.Secret].
    google.protobuf.Duration ttl = 7 [(google.api.field_behavior) = INPUT_ONLY];
  }

  // Optional. Etag of the currently stored
  // [Secret][google.cloud.secretmanager.v1.Secret].
  string etag = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Rotation policy attached to the
  // [Secret][google.cloud.secretmanager.v1.Secret]. May be excluded if there is
  // no rotation policy.
  Rotation rotation = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Mapping from version alias to version name.
  //
  // A version alias is a string with a maximum length of 63 characters and can
  // contain uppercase and lowercase letters, numerals, and the hyphen (`-`)
  // and underscore ('_') characters. An alias string must start with a
  // letter and cannot be the string 'latest' or 'NEW'.
  // No more than 50 aliases can be assigned to a given secret.
  //
  // Version-Alias pairs will be viewable via GetSecret and modifiable via
  // UpdateSecret. Access by alias is only be supported on
  // GetSecretVersion and AccessSecretVersion.
  map<string, int64> version_aliases = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom metadata about the secret.
  //
  // Annotations are distinct from various forms of labels.
  // Annotations exist to allow client tools to store their own state
  // information without requiring a database.
  //
  // Annotation keys must be between 1 and 63 characters long, have a UTF-8
  // encoding of maximum 128 bytes, begin and end with an alphanumeric character
  // ([a-z0-9A-Z]), and may have dashes (-), underscores (_), dots (.), and
  // alphanumerics in between these symbols.
  //
  // The total size of annotation keys and values must be less than 16KiB.
  map<string, string> annotations = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Secret Version TTL after destruction request
  //
  // This is a part of the Delayed secret version destroy feature.
  // For secret with TTL>0, version destruction doesn't happen immediately
  // on calling destroy instead the version goes to a disabled state and
  // destruction happens after the TTL expires.
  google.protobuf.Duration version_destroy_ttl = 14
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The customer-managed encryption configuration of the regionalized
  // secrets. If no configuration is provided, Google-managed default encryption
  // is used.
  //
  // Updates to the [Secret][google.cloud.secretmanager.v1.Secret] encryption
  // configuration only apply to
  // [SecretVersions][google.cloud.secretmanager.v1.SecretVersion] added
  // afterwards. They do not apply retroactively to existing
  // [SecretVersions][google.cloud.secretmanager.v1.SecretVersion].
  CustomerManagedEncryption customer_managed_encryption = 15
      [(google.api.field_behavior) = OPTIONAL];
}

// A secret version resource in the Secret Manager API.
message SecretVersion {
  option (google.api.resource) = {
    type: "secretmanager.googleapis.com/SecretVersion"
    pattern: "projects/{project}/secrets/{secret}/versions/{secret_version}"
    pattern: "projects/{project}/locations/{location}/secrets/{secret}/versions/{secret_version}"
    plural: "secretVersions"
    singular: "secretVersion"
  };

  // The state of a
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion], indicating if
  // it can be accessed.
  enum State {
    // Not specified. This value is unused and invalid.
    STATE_UNSPECIFIED = 0;

    // The [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] may be
    // accessed.
    ENABLED = 1;

    // The [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] may not
    // be accessed, but the secret data is still available and can be placed
    // back into the
    // [ENABLED][google.cloud.secretmanager.v1.SecretVersion.State.ENABLED]
    // state.
    DISABLED = 2;

    // The [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] is
    // destroyed and the secret data is no longer stored. A version may not
    // leave this state once entered.
    DESTROYED = 3;
  }

  // Output only. The resource name of the
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] in the format
  // `projects/*/secrets/*/versions/*`.
  //
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] IDs in a
  // [Secret][google.cloud.secretmanager.v1.Secret] start at 1 and are
  // incremented for each subsequent version of the secret.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] was destroyed.
  // Only present if [state][google.cloud.secretmanager.v1.SecretVersion.state]
  // is
  // [DESTROYED][google.cloud.secretmanager.v1.SecretVersion.State.DESTROYED].
  google.protobuf.Timestamp destroy_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion].
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The replication status of the
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion].
  ReplicationStatus replication_status = 5;

  // Output only. Etag of the currently stored
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion].
  string etag = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if payload checksum specified in
  // [SecretPayload][google.cloud.secretmanager.v1.SecretPayload] object has
  // been received by
  // [SecretManagerService][google.cloud.secretmanager.v1.SecretManagerService]
  // on
  // [SecretManagerService.AddSecretVersion][google.cloud.secretmanager.v1.SecretManagerService.AddSecretVersion].
  bool client_specified_payload_checksum = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Output only. Scheduled destroy time for secret version.
  // This is a part of the Delayed secret version destroy feature. For a
  // Secret with a valid version destroy TTL, when a secert version is
  // destroyed, version is moved to disabled state and it is scheduled for
  // destruction Version is destroyed only after the scheduled_destroy_time.
  google.protobuf.Timestamp scheduled_destroy_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The customer-managed encryption status of the
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion]. Only
  // populated if customer-managed encryption is used and
  // [Secret][google.cloud.secretmanager.v1.Secret] is a regionalized secret.
  CustomerManagedEncryptionStatus customer_managed_encryption = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A policy that defines the replication and encryption configuration of data.
message Replication {
  // A replication policy that replicates the
  // [Secret][google.cloud.secretmanager.v1.Secret] payload without any
  // restrictions.
  message Automatic {
    // Optional. The customer-managed encryption configuration of the
    // [Secret][google.cloud.secretmanager.v1.Secret]. If no configuration is
    // provided, Google-managed default encryption is used.
    //
    // Updates to the [Secret][google.cloud.secretmanager.v1.Secret] encryption
    // configuration only apply to
    // [SecretVersions][google.cloud.secretmanager.v1.SecretVersion] added
    // afterwards. They do not apply retroactively to existing
    // [SecretVersions][google.cloud.secretmanager.v1.SecretVersion].
    CustomerManagedEncryption customer_managed_encryption = 1
        [(google.api.field_behavior) = OPTIONAL];
  }

  // A replication policy that replicates the
  // [Secret][google.cloud.secretmanager.v1.Secret] payload into the locations
  // specified in
  // [Replication.UserManaged.replicas][google.cloud.secretmanager.v1.Replication.UserManaged.replicas]
  message UserManaged {
    // Represents a Replica for this
    // [Secret][google.cloud.secretmanager.v1.Secret].
    message Replica {
      // The canonical IDs of the location to replicate data.
      // For example: `"us-east1"`.
      string location = 1;

      // Optional. The customer-managed encryption configuration of the
      // [User-Managed Replica][Replication.UserManaged.Replica]. If no
      // configuration is provided, Google-managed default encryption is used.
      //
      // Updates to the [Secret][google.cloud.secretmanager.v1.Secret]
      // encryption configuration only apply to
      // [SecretVersions][google.cloud.secretmanager.v1.SecretVersion] added
      // afterwards. They do not apply retroactively to existing
      // [SecretVersions][google.cloud.secretmanager.v1.SecretVersion].
      CustomerManagedEncryption customer_managed_encryption = 2
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Required. The list of Replicas for this
    // [Secret][google.cloud.secretmanager.v1.Secret].
    //
    // Cannot be empty.
    repeated Replica replicas = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // The replication policy for this secret.
  oneof replication {
    // The [Secret][google.cloud.secretmanager.v1.Secret] will automatically be
    // replicated without any restrictions.
    Automatic automatic = 1;

    // The [Secret][google.cloud.secretmanager.v1.Secret] will only be
    // replicated into the locations specified.
    UserManaged user_managed = 2;
  }
}

// Configuration for encrypting secret payloads using customer-managed
// encryption keys (CMEK).
message CustomerManagedEncryption {
  // Required. The resource name of the Cloud KMS CryptoKey used to encrypt
  // secret payloads.
  //
  // For secrets using the
  // [UserManaged][google.cloud.secretmanager.v1.Replication.UserManaged]
  // replication policy type, Cloud KMS CryptoKeys must reside in the same
  // location as the [replica location][Secret.UserManaged.Replica.location].
  //
  // For secrets using the
  // [Automatic][google.cloud.secretmanager.v1.Replication.Automatic]
  // replication policy type, Cloud KMS CryptoKeys must reside in `global`.
  //
  // The expected format is `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  string kms_key_name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The replication status of a
// [SecretVersion][google.cloud.secretmanager.v1.SecretVersion].
message ReplicationStatus {
  // The replication status of a
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] using
  // automatic replication.
  //
  // Only populated if the parent [Secret][google.cloud.secretmanager.v1.Secret]
  // has an automatic replication policy.
  message AutomaticStatus {
    // Output only. The customer-managed encryption status of the
    // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion]. Only
    // populated if customer-managed encryption is used.
    CustomerManagedEncryptionStatus customer_managed_encryption = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The replication status of a
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] using
  // user-managed replication.
  //
  // Only populated if the parent [Secret][google.cloud.secretmanager.v1.Secret]
  // has a user-managed replication policy.
  message UserManagedStatus {
    // Describes the status of a user-managed replica for the
    // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion].
    message ReplicaStatus {
      // Output only. The canonical ID of the replica location.
      // For example: `"us-east1"`.
      string location = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The customer-managed encryption status of the
      // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion]. Only
      // populated if customer-managed encryption is used.
      CustomerManagedEncryptionStatus customer_managed_encryption = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Output only. The list of replica statuses for the
    // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion].
    repeated ReplicaStatus replicas = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The replication status of the
  // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion].
  oneof replication_status {
    // Describes the replication status of a
    // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] with
    // automatic replication.
    //
    // Only populated if the parent
    // [Secret][google.cloud.secretmanager.v1.Secret] has an automatic
    // replication policy.
    AutomaticStatus automatic = 1;

    // Describes the replication status of a
    // [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] with
    // user-managed replication.
    //
    // Only populated if the parent
    // [Secret][google.cloud.secretmanager.v1.Secret] has a user-managed
    // replication policy.
    UserManagedStatus user_managed = 2;
  }
}

// Describes the status of customer-managed encryption.
message CustomerManagedEncryptionStatus {
  // Required. The resource name of the Cloud KMS CryptoKeyVersion used to
  // encrypt the secret payload, in the following format:
  // `projects/*/locations/*/keyRings/*/cryptoKeys/*/versions/*`.
  string kms_key_version_name = 1 [(google.api.field_behavior) = REQUIRED];
}

// A Pub/Sub topic which Secret Manager will publish to when control plane
// events occur on this secret.
message Topic {
  option (google.api.resource) = {
    type: "pubsub.googleapis.com/Topic"
    pattern: "projects/{project}/topics/{topic}"
  };

  // Identifier. The resource name of the Pub/Sub topic that will be published
  // to, in the following format: `projects/*/topics/*`. For publication to
  // succeed, the Secret Manager service agent must have the
  // `pubsub.topic.publish` permission on the topic. The Pub/Sub Publisher role
  // (`roles/pubsub.publisher`) includes this permission.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];
}

// The rotation time and period for a
// [Secret][google.cloud.secretmanager.v1.Secret]. At next_rotation_time, Secret
// Manager will send a Pub/Sub notification to the topics configured on the
// Secret. [Secret.topics][google.cloud.secretmanager.v1.Secret.topics] must be
// set to configure rotation.
message Rotation {
  // Optional. Timestamp in UTC at which the
  // [Secret][google.cloud.secretmanager.v1.Secret] is scheduled to rotate.
  // Cannot be set to less than 300s (5 min) in the future and at most
  // 3153600000s (100 years).
  //
  // [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
  // MUST  be set if
  // [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
  // is set.
  google.protobuf.Timestamp next_rotation_time = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Input only. The Duration between rotation notifications. Must be in seconds
  // and at least 3600s (1h) and at most 3153600000s (100 years).
  //
  // If
  // [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
  // is set,
  // [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
  // must be set.
  // [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
  // will be advanced by this period when the service automatically sends
  // rotation notifications.
  google.protobuf.Duration rotation_period = 2
      [(google.api.field_behavior) = INPUT_ONLY];
}

// A secret payload resource in the Secret Manager API. This contains the
// sensitive secret payload that is associated with a
// [SecretVersion][google.cloud.secretmanager.v1.SecretVersion].
message SecretPayload {
  // The secret data. Must be no larger than 64KiB.
  bytes data = 1;

  // Optional. If specified,
  // [SecretManagerService][google.cloud.secretmanager.v1.SecretManagerService]
  // will verify the integrity of the received
  // [data][google.cloud.secretmanager.v1.SecretPayload.data] on
  // [SecretManagerService.AddSecretVersion][google.cloud.secretmanager.v1.SecretManagerService.AddSecretVersion]
  // calls using the crc32c checksum and store it to include in future
  // [SecretManagerService.AccessSecretVersion][google.cloud.secretmanager.v1.SecretManagerService.AccessSecretVersion]
  // responses. If a checksum is not provided in the
  // [SecretManagerService.AddSecretVersion][google.cloud.secretmanager.v1.SecretManagerService.AddSecretVersion]
  // request, the
  // [SecretManagerService][google.cloud.secretmanager.v1.SecretManagerService]
  // will generate and store one for you.
  //
  // The CRC32C value is encoded as a Int64 for compatibility, and can be
  // safely downconverted to uint32 in languages that support this type.
  // https://cloud.google.com/apis/design/design_patterns#integer_types
  optional int64 data_crc32c = 2 [(google.api.field_behavior) = OPTIONAL];
}
