# This build file includes a target for the Ruby wrapper library for
# google-cloud-secret_manager.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for secretmanager.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "secretmanager_ruby_wrapper",
    srcs = ["//google/cloud/secretmanager/v1:secretmanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-secret_manager",
        "ruby-cloud-env-prefix=SECRET_MANAGER",
        "ruby-cloud-wrapper-of=v1:0.19;v1beta1:0.3",
        "ruby-cloud-product-url=https://cloud.google.com/secret-manager",
        "ruby-cloud-api-id=secretmanager.googleapis.com",
        "ruby-cloud-api-shortname=secretmanager",
    ],
    ruby_cloud_description = "Secret Manager is a secure and convenient storage system for API keys, passwords, certificates, and other sensitive data. Secret Manager provides a central place and single source of truth to manage, access, and audit secrets across Google Cloud.",
    ruby_cloud_title = "Secret Manager",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-secretmanager-ruby",
    deps = [
        ":secretmanager_ruby_wrapper",
    ],
)
