// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.secretmanager.logging.v1;

option go_package = "cloud.google.com/go/secretmanager/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "SecretEventProto";
option java_package = "com.google.cloud.secretmanager.logging.v1";
option csharp_namespace = "Google.Cloud.SecretManager.Logging.V1";
option php_namespace = "Google\\Cloud\\SecretManager\\Logging\\V1";
option ruby_package = "Google::Cloud::SecretManager::Logging::V1";

// Logged event relating to a specific secret
message SecretEvent {
  // Describes the type of event that is being logged. All logs have exactly one
  // EventType.
  enum EventType {
    // An unrecognized event type. Should never be used.
    EVENT_TYPE_UNSPECIFIED = 0;

    // The secret is scheduled to expire in 30 days.
    EXPIRES_IN_30_DAYS = 1;

    // The secret is scheduled to expire in 7 days.
    EXPIRES_IN_7_DAYS = 2;

    // The secret is scheduled to expire in 1 day.
    EXPIRES_IN_1_DAY = 3;

    // The secret is scheduled to expire in 6 hours.
    EXPIRES_IN_6_HOURS = 4;

    // The secret is scheduled to expire in 1 hour.
    EXPIRES_IN_1_HOUR = 5;

    // The secret's expire-time has passed and it has expired.
    EXPIRED = 6;

    // A Pub/Sub topic configured on the secret could not be found.
    TOPIC_NOT_FOUND = 7;

    // A Pub/Sub topic configured on the secret does not have the needed
    // permissions. The Secret Manager P4SA must be granted
    // 'pubsub.topic.publish' permission (or 'roles/pubsub.publisher') on the
    // topic.
    TOPIC_PERMISSION_DENIED = 8;
  }

  // Resource name of the secret in the format `projects/*/secrets/*`
  string name = 1;

  // Type of event that is being logged for the secret
  EventType type = 2;

  // Human readable message describing the event
  string log_message = 3;
}
