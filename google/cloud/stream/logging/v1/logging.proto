// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.stream.logging.v1;

import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/stream/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggingProto";
option java_package = "com.google.cloud.stream.logging.v1";

// OperationEventLog contains the time series data of the operations on the
// stream resources. Internally, these logs represent events happening in
// the control plane as result of API invocations against the stream resource
// entities.
message OperationEventLog {
  // Enum for type of event being logged.
  OperationEventType event_type = 1;

  // Timestamp when the event occurred.
  google.protobuf.Timestamp event_time = 2;

  // Operation resource identifier.
  string operation = 3;

  // Artifact created as a result of the operation.
  repeated OperationArtifact operation_artifacts = 4;
}

// OperationArtifact contains the information about the artifact created as
// result of the operation.
message OperationArtifact {
  // Type of the artifact.
  string artifact_type = 1;

  // Location of the artifact.
  string artifact_uri = 2;
}

// SessionEventLog contains the time series data regarding the streaming session
// to serve the end consumer. Internally, these logs represent events in the
// data plane streamer instance as a result of end consumer interacting with the
// stream resources.
message SessionEventLog {
  // Enum for type of event being logged.
  SessionEventType event_type = 1;

  // Timestamp when the event occurred.
  google.protobuf.Timestamp event_time = 2;

  // Session identifier generated from the server.
  string session_id = 3;
}

// OperationEventType is the enum value for the state of operation.
enum OperationEventType {
  // Unspecified operation event type.
  OPERATION_EVENT_TYPE_UNSPECIFIED = 0;

  // Operation for create stream content started.
  OPERATION_EVENT_CREATE_CONTENT_STARTED = 1;

  // Operation for create stream content ended.
  OPERATION_EVENT_CREATE_CONTENT_ENDED = 2;

  // Operation for build stream content started.
  OPERATION_EVENT_BUILD_CONTENT_STARTED = 3;

  // Operation for build stream content ended.
  OPERATION_EVENT_BUILD_CONTENT_ENDED = 4;

  // Operation for update stream content started.
  OPERATION_EVENT_UPDATE_CONTENT_STARTED = 5;

  // Operation for update stream content ended.
  OPERATION_EVENT_UPDATE_CONTENT_ENDED = 6;

  // Operation for delete stream content started.
  OPERATION_EVENT_DELETE_CONTENT_STARTED = 7;

  // Operation for delete stream content ended.
  OPERATION_EVENT_DELETE_CONTENT_ENDED = 8;

  // Operation for create stream instance started.
  OPERATION_EVENT_CREATE_INSTANCE_STARTED = 9;

  // Operation for create stream instance ended.
  OPERATION_EVENT_CREATE_INSTANCE_ENDED = 10;

  // Operation for update stream instance started.
  OPERATION_EVENT_UPDATE_INSTANCE_STARTED = 11;

  // Operation for update stream instance ended.
  OPERATION_EVENT_UPDATE_INSTANCE_ENDED = 12;

  // Operation for delete stream instance started.
  OPERATION_EVENT_DELETE_INSTANCE_STARTED = 13;

  // Operation for delete stream instance ended.
  OPERATION_EVENT_DELETE_INSTANCE_ENDED = 14;
}

// SessionEventType is the enum value for the state of session.
enum SessionEventType {
  // Unspecified session event type.
  SESSION_EVENT_TYPE_UNSPECIFIED = 0;

  // Session in streamer shutting down state.
  SESSION_EVENT_SERVER_STREAMER_SHUTTING_DOWN = 1;

  // Session in streamer ready state.
  SESSION_EVENT_SERVER_STREAMER_READY = 2;

  // Session in streamer binary started state.
  SESSION_EVENT_SERVER_STREAMER_BINARY_STARTED = 3;

  // Session in streamer read pod image names state.
  SESSION_EVENT_SERVER_STREAMER_READ_POD_IMAGE_NAMES = 4;

  // Session in streamer connected to game state.
  SESSION_EVENT_SERVER_STREAMER_CONNECTED_TO_GAME = 5;

  // Session in streamer connected to client state.
  SESSION_EVENT_SERVER_STREAMER_CONNECTED_TO_CLIENT = 6;

  // Session in streamer disconnected from client state.
  SESSION_EVENT_SERVER_STREAMER_DISCONNECTED_FROM_CLIENT = 7;

  // Session in streamer received create session request state.
  SESSION_EVENT_SERVER_STREAMER_RECEIVED_CREATE_SESSION_REQUEST = 8;

  // Session in streamer game message to stream closed state.
  SESSION_EVENT_SERVER_STREAMER_GAME_MESSAGE_STREAM_CLOSED = 9;

  // Session in streamer game frame stream closed state.
  SESSION_EVENT_SERVER_STREAMER_GAME_FRAME_STREAM_CLOSED = 10;

  // Session in streamer game message stream error state.
  SESSION_EVENT_SERVER_STREAMER_GAME_MESSAGE_STREAM_ERROR = 11;

  // Session in streamer game audio stream error state.
  SESSION_EVENT_SERVER_STREAMER_GAME_AUDIO_STREAM_ERROR = 12;

  // Session in streamer game audio stream closed state.
  SESSION_EVENT_SERVER_STREAMER_GAME_AUDIO_STREAM_CLOSED = 13;

  // Session in streamer game frame stream error state.
  SESSION_EVENT_SERVER_STREAMER_GAME_FRAME_STREAM_ERROR = 14;

  // Session in game disconnecting after paused too long state.
  SESSION_EVENT_SERVER_GAME_DISCONNECTING_AFTER_PAUSED_TOO_LONG = 15;

  // Session in streamer received experiment configuration state.
  SESSION_EVENT_SERVER_STREAMER_RECEIVED_EXPERIMENT_CONFIGURATION = 16;

  // Session in game connected to logging service state.
  SESSION_EVENT_SERVER_GAME_CONNECTED_TO_LOGGING_SERVICE = 17;

  // Session in streamer determined session options state.
  SESSION_EVENT_SERVER_STREAMER_DETERMINED_SESSION_OPTIONS = 18;

  // Session in streamer killed in middle of session state.
  SESSION_EVENT_SERVER_STREAMER_KILLED_IN_MIDDLE_OF_SESSION = 19;

  // Session in game updated frame pipeline state.
  SESSION_EVENT_SERVER_GAME_UPDATED_FRAME_PIPELINE = 20;

  // Session in server error state.
  SESSION_EVENT_SERVER_ERROR = 21;
}
