// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oslogin.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/oslogin/common/common.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.OsLogin.V1Beta";
option go_package = "cloud.google.com/go/oslogin/apiv1beta/osloginpb;osloginpb";
option java_multiple_files = true;
option java_outer_classname = "OsLoginProto";
option java_package = "com.google.cloud.oslogin.v1beta";
option php_namespace = "Google\\Cloud\\OsLogin\\V1beta";
option ruby_package = "Google::Cloud::OsLogin::V1beta";

// Cloud OS Login API
//
// The Cloud OS Login API allows you to manage users and their associated SSH
// public keys for logging into virtual machines on Google Cloud Platform.
service OsLoginService {
  option (google.api.default_host) = "oslogin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only,"
      "https://www.googleapis.com/auth/compute,"
      "https://www.googleapis.com/auth/compute.readonly";

  // Create an SSH public key
  rpc CreateSshPublicKey(CreateSshPublicKeyRequest)
      returns (google.cloud.oslogin.common.SshPublicKey) {
    option (google.api.http) = {
      post: "/v1beta/{parent=users/*}/sshPublicKeys"
      body: "ssh_public_key"
    };
    option (google.api.method_signature) = "parent,ssh_public_key";
  }

  // Deletes a POSIX account.
  rpc DeletePosixAccount(DeletePosixAccountRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=users/*/projects/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes an SSH public key.
  rpc DeleteSshPublicKey(DeleteSshPublicKeyRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=users/*/sshPublicKeys/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves the profile information used for logging in to a virtual machine
  // on Google Compute Engine.
  rpc GetLoginProfile(GetLoginProfileRequest) returns (LoginProfile) {
    option (google.api.http) = {
      get: "/v1beta/{name=users/*}/loginProfile"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves an SSH public key.
  rpc GetSshPublicKey(GetSshPublicKeyRequest)
      returns (google.cloud.oslogin.common.SshPublicKey) {
    option (google.api.http) = {
      get: "/v1beta/{name=users/*/sshPublicKeys/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Adds an SSH public key and returns the profile information. Default POSIX
  // account information is set when no username and UID exist as part of the
  // login profile.
  rpc ImportSshPublicKey(ImportSshPublicKeyRequest)
      returns (ImportSshPublicKeyResponse) {
    option (google.api.http) = {
      post: "/v1beta/{parent=users/*}:importSshPublicKey"
      body: "ssh_public_key"
    };
    option (google.api.method_signature) = "parent,ssh_public_key";
    option (google.api.method_signature) = "parent,ssh_public_key,project_id";
  }

  // Updates an SSH public key and returns the profile information. This method
  // supports patch semantics.
  rpc UpdateSshPublicKey(UpdateSshPublicKeyRequest)
      returns (google.cloud.oslogin.common.SshPublicKey) {
    option (google.api.http) = {
      patch: "/v1beta/{name=users/*/sshPublicKeys/*}"
      body: "ssh_public_key"
    };
    option (google.api.method_signature) = "name,ssh_public_key";
    option (google.api.method_signature) = "name,ssh_public_key,update_mask";
  }

  // Signs an SSH public key for a user to authenticate to an instance.
  rpc SignSshPublicKey(SignSshPublicKeyRequest)
      returns (SignSshPublicKeyResponse) {
    option (google.api.http) = {
      post: "/v1beta/{parent=users/*/projects/*/zones/*}:signSshPublicKey"
      body: "*"
      additional_bindings {
        post: "/v1beta/{parent=users/*/projects/*/locations/*}:signSshPublicKey"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,ssh_public_key";
  }
}

// The user profile information used for logging in to a virtual machine on
// Google Compute Engine.
message LoginProfile {
  // Required. A unique user ID.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of POSIX accounts associated with the user.
  repeated google.cloud.oslogin.common.PosixAccount posix_accounts = 2;

  // A map from SSH public key fingerprint to the associated key object.
  map<string, google.cloud.oslogin.common.SshPublicKey> ssh_public_keys = 3;

  // The registered security key credentials for a user.
  repeated SecurityKey security_keys = 5;
}

// A request message for creating an SSH public key.
message CreateSshPublicKeyRequest {
  // Required. The unique ID for the user in format `users/{user}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "oslogin.googleapis.com/SshPublicKey"
    }
  ];

  // Required. The SSH public key and expiration time.
  google.cloud.oslogin.common.SshPublicKey ssh_public_key = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A request message for deleting a POSIX account entry.
message DeletePosixAccountRequest {
  // Required. A reference to the POSIX account to update. POSIX accounts are
  // identified by the project ID they are associated with. A reference to the
  // POSIX account is in format `users/{user}/projects/{project}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oslogin.googleapis.com/PosixAccount"
    }
  ];
}

// A request message for deleting an SSH public key.
message DeleteSshPublicKeyRequest {
  // Required. The fingerprint of the public key to update. Public keys are
  // identified by their SHA-256 fingerprint. The fingerprint of the public key
  // is in format `users/{user}/sshPublicKeys/{fingerprint}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oslogin.googleapis.com/SshPublicKey"
    }
  ];
}

// A request message for retrieving the login profile information for a user.
message GetLoginProfileRequest {
  // Required. The unique ID for the user in format `users/{user}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "oslogin.googleapis.com/User" }
  ];

  // The project ID of the Google Cloud Platform project.
  string project_id = 2;

  // A system ID for filtering the results of the request.
  string system_id = 3;

  // The view configures whether to retrieve security keys information.
  LoginProfileView view = 4;
}

// A request message for retrieving an SSH public key.
message GetSshPublicKeyRequest {
  // Required. The fingerprint of the public key to retrieve. Public keys are
  // identified by their SHA-256 fingerprint. The fingerprint of the public key
  // is in format `users/{user}/sshPublicKeys/{fingerprint}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oslogin.googleapis.com/SshPublicKey"
    }
  ];
}

// A request message for importing an SSH public key.
message ImportSshPublicKeyRequest {
  // The unique ID for the user in format `users/{user}`.
  string parent = 1 [(google.api.resource_reference) = {
    child_type: "oslogin.googleapis.com/SshPublicKey"
  }];

  // Required. The SSH public key and expiration time.
  google.cloud.oslogin.common.SshPublicKey ssh_public_key = 2
      [(google.api.field_behavior) = REQUIRED];

  // The project ID of the Google Cloud Platform project.
  string project_id = 3;

  // The view configures whether to retrieve security keys information.
  LoginProfileView view = 4;

  // Optional. The regions to which to assert that the key was written.
  // If unspecified, defaults to all regions.
  // Regions are listed at https://cloud.google.com/about/locations#region.
  repeated string regions = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A response message for importing an SSH public key.
message ImportSshPublicKeyResponse {
  // The login profile information for the user.
  LoginProfile login_profile = 1;

  // Detailed information about import results.
  string details = 2;
}

// A request message for updating an SSH public key.
message UpdateSshPublicKeyRequest {
  // Required. The fingerprint of the public key to update. Public keys are
  // identified by their SHA-256 fingerprint. The fingerprint of the public key
  // is in format `users/{user}/sshPublicKeys/{fingerprint}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oslogin.googleapis.com/SshPublicKey"
    }
  ];

  // Required. The SSH public key and expiration time.
  google.cloud.oslogin.common.SshPublicKey ssh_public_key = 2
      [(google.api.field_behavior) = REQUIRED];

  // Mask to control which fields get updated. Updates all if not present.
  google.protobuf.FieldMask update_mask = 3;
}

// The credential information for a Google registered security key.
message SecurityKey {
  // Public key text in SSH format, defined by
  // [RFC4253]("https://www.ietf.org/rfc/rfc4253.txt") section 6.6.
  string public_key = 1;

  // Hardware-backed private key text in SSH format.
  string private_key = 2;

  // The FIDO protocol type used to register this credential.
  oneof protocol_type {
    // The U2F protocol type.
    UniversalTwoFactor universal_two_factor = 3;

    // The Web Authentication protocol type.
    WebAuthn web_authn = 4;
  }

  // The security key nickname explicitly set by the user.
  optional string device_nickname = 5;
}

// Security key information specific to the U2F protocol.
message UniversalTwoFactor {
  // Application ID for the U2F protocol.
  string app_id = 1;
}

// Security key information specific to the Web Authentication protocol.
message WebAuthn {
  // Relying party ID for Web Authentication.
  string rp_id = 1;
}

message SignSshPublicKeyRequest {
  // The SSH public key to sign.
  string ssh_public_key = 1;

  // The parent project and region for the signing request.
  string parent = 2;
}

message SignSshPublicKeyResponse {
  // The signed SSH public key to use in the SSH handshake.
  string signed_ssh_public_key = 1;
}

// The login profile view limits the user content retrieved.
enum LoginProfileView {
  // The default login profile view. The API defaults to the BASIC view.
  LOGIN_PROFILE_VIEW_UNSPECIFIED = 0;

  // Includes POSIX and SSH key information.
  BASIC = 1;

  // Include security key information for the user.
  SECURITY_KEY = 2;
}
