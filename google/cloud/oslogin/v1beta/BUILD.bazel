load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

_PROTO_SUBPACKAGE_DEPS = [
    "//google/cloud/oslogin/common:common_proto",
]

proto_library(
    name = "oslogin_proto",
    srcs = [
        "oslogin.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
    ] + _PROTO_SUBPACKAGE_DEPS,
)

proto_library_with_info(
    name = "oslogin_proto_with_info",
    deps = [
        ":oslogin_proto",
        "//google/cloud:common_resources_proto",
    ] + _PROTO_SUBPACKAGE_DEPS,
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

_JAVA_PROTO_SUBPACKAGE_DEPS = [
    "//google/cloud/oslogin/common:common_java_proto",
]

_JAVA_GRPC_SUBPACKAGE_DEPS = [
    "//google/cloud/oslogin/common:common_java_grpc",
]

java_proto_library(
    name = "oslogin_java_proto",
    deps = [":oslogin_proto"],
)

java_grpc_library(
    name = "oslogin_java_grpc",
    srcs = [":oslogin_proto"],
    deps = [":oslogin_java_proto"] + _JAVA_PROTO_SUBPACKAGE_DEPS,
)

java_gapic_library(
    name = "oslogin_java_gapic",
    srcs = [":oslogin_proto_with_info"],
    grpc_service_config = "oslogin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "oslogin_v1beta.yaml",
    test_deps = [":oslogin_java_grpc"],
    transport = "grpc+rest",
    deps = [
        ":oslogin_java_proto",
    ] + _JAVA_PROTO_SUBPACKAGE_DEPS,
)

java_gapic_test(
    name = "oslogin_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.oslogin.v1beta.OsLoginServiceClientHttpJsonTest",
        "com.google.cloud.oslogin.v1beta.OsLoginServiceClientTest",
    ],
    runtime_deps = [":oslogin_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-oslogin-v1beta-java",
    transport = "grpc+rest",
    deps = [
        ":oslogin_java_gapic",
        ":oslogin_java_grpc",
        ":oslogin_java_proto",
        ":oslogin_proto",
    ] + _JAVA_PROTO_SUBPACKAGE_DEPS + _PROTO_SUBPACKAGE_DEPS,
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "oslogin_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/oslogin/apiv1beta/osloginpb",
    protos = [":oslogin_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/cloud/oslogin/common:common_go_proto",
    ],
)

go_gapic_library(
    name = "oslogin_go_gapic",
    srcs = [":oslogin_proto_with_info"],
    grpc_service_config = "oslogin_grpc_service_config.json",
    importpath = "cloud.google.com/go/oslogin/apiv1beta;oslogin",
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "oslogin_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":oslogin_go_proto",
        "//google/cloud/oslogin/common:common_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-oslogin-v1beta-go",
    deps = [
        ":oslogin_go_gapic",
        ":oslogin_go_gapic_srcjar-snippets.srcjar",
        ":oslogin_go_gapic_srcjar-test.srcjar",
        ":oslogin_go_proto",
        "//google/cloud/oslogin/common:common_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
)

py_gapic_library(
    name = "oslogin_py_gapic",
    srcs = [
        ":oslogin_proto",
    ],
    grpc_service_config = "oslogin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "oslogin_v1beta.yaml",
    transport = "grpc+rest",
)

# Uncomment once https://github.com/googleapis/gapic-generator-python/issues/1376 is fixed
#py_test(
#    name = "oslogin_py_gapic_test",
#    srcs = [
#        "oslogin_py_gapic_pytest.py",
#        "oslogin_py_gapic_test.py",
#    ],
#    legacy_create_init = False,
#    deps = [":oslogin_py_gapic"],
#)

py_gapic_assembly_pkg(
    name = "oslogin-v1beta-py",
    deps = [
        ":oslogin_py_gapic",
        "//google/cloud/oslogin/common:common_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

_PHP_SUBPACKAGE_DEPS = [
    "//google/cloud/oslogin/common:common_php_proto",
]

php_proto_library(
    name = "oslogin_php_proto",
    deps = [":oslogin_proto"],
)

php_gapic_library(
    name = "oslogin_php_gapic",
    srcs = [":oslogin_proto_with_info"],
    grpc_service_config = "oslogin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "oslogin_v1beta.yaml",
    transport = "grpc+rest",
    deps = [":oslogin_php_proto"] + _PHP_SUBPACKAGE_DEPS,
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-oslogin-v1beta-php",
    deps = [
        ":oslogin_php_gapic",
        ":oslogin_php_proto",
    ] + _PHP_SUBPACKAGE_DEPS,
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "oslogin_nodejs_gapic",
    package_name = "@google-cloud/os-login",
    src = ":oslogin_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "oslogin_grpc_service_config.json",
    package = "google.cloud.oslogin.v1beta",
    rest_numeric_enums = True,
    service_yaml = "oslogin_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "oslogin-v1beta-nodejs",
    deps = [
        ":oslogin_nodejs_gapic",
        ":oslogin_proto",
    ] + _PROTO_SUBPACKAGE_DEPS,
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

_RUBY_SUBPACKAGE_DEPS = [
    "//google/cloud/oslogin/common:common_ruby_proto",
    "//google/cloud/oslogin/common:common_ruby_grpc",
]

ruby_proto_library(
    name = "oslogin_ruby_proto",
    deps = [":oslogin_proto"],
)

ruby_grpc_library(
    name = "oslogin_ruby_grpc",
    srcs = [":oslogin_proto"],
    deps = [":oslogin_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "oslogin_ruby_gapic",
    srcs = [":oslogin_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-os_login-v1beta",
        "ruby-cloud-env-prefix=OS_LOGIN",
        "ruby-cloud-product-url=https://cloud.google.com/compute/docs/oslogin",
        "ruby-cloud-api-id=oslogin.googleapis.com",
        "ruby-cloud-api-shortname=oslogin",
    ],
    grpc_service_config = "oslogin_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Use OS Login to manage SSH access to your instances using IAM without having to create and manage individual SSH keys. OS Login maintains a consistent Linux user identity across VM instances and is the recommended way to manage many users across multiple instances or projects.",
    ruby_cloud_title = "Cloud OS Login V1beta",
    service_yaml = "oslogin_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":oslogin_ruby_grpc",
        ":oslogin_ruby_proto",
    ] + _RUBY_SUBPACKAGE_DEPS,
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-oslogin-v1beta-ruby",
    deps = [
        ":oslogin_ruby_gapic",
        ":oslogin_ruby_grpc",
        ":oslogin_ruby_proto",
    ] + _RUBY_SUBPACKAGE_DEPS,
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

_CSHARP_SUBPACKAGE_DEPS = [
    "//google/cloud/oslogin/common:common_csharp_proto",
    "//google/cloud/oslogin/common:common_csharp_grpc",
]

csharp_proto_library(
    name = "oslogin_csharp_proto",
    deps = [":oslogin_proto"],
)

csharp_grpc_library(
    name = "oslogin_csharp_grpc",
    srcs = [":oslogin_proto"],
    deps = [":oslogin_csharp_proto"],
)

csharp_gapic_library(
    name = "oslogin_csharp_gapic",
    srcs = [":oslogin_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "oslogin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "oslogin_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":oslogin_csharp_grpc",
        ":oslogin_csharp_proto",
    ] + _CSHARP_SUBPACKAGE_DEPS,
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-oslogin-v1beta-csharp",
    deps = [
        ":oslogin_csharp_gapic",
        ":oslogin_csharp_grpc",
        ":oslogin_csharp_proto",
    ] + _CSHARP_SUBPACKAGE_DEPS,
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
