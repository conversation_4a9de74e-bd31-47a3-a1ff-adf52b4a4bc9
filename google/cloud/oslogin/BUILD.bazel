# This build file includes a target for the Ruby wrapper library for
# google-cloud-os_login.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for oslogin.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "oslogin_ruby_wrapper",
    srcs = ["//google/cloud/oslogin/v1:oslogin_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-os_login",
        "ruby-cloud-env-prefix=OS_LOGIN",
        "ruby-cloud-wrapper-of=v1:0.11;v1beta:0.14",
        "ruby-cloud-product-url=https://cloud.google.com/compute/docs/oslogin",
        "ruby-cloud-api-id=oslogin.googleapis.com",
        "ruby-cloud-api-shortname=oslogin",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Use OS Login to manage SSH access to your instances using IAM without having to create and manage individual SSH keys. OS Login maintains a consistent Linux user identity across VM instances and is the recommended way to manage many users across multiple instances or projects.",
    ruby_cloud_title = "Cloud OS Login",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-oslogin-ruby",
    deps = [
        ":oslogin_ruby_wrapper",
    ],
)
