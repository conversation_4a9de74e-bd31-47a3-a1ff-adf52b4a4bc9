# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "common_proto",
    srcs = [
        "common.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
    ],
)

proto_library_with_info(
    name = "common_proto_with_info",
    deps = [
        ":common_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "common_java_proto",
    deps = [":common_proto"],
)

java_grpc_library(
    name = "common_java_grpc",
    srcs = [":common_proto"],
    deps = [":common_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_proto_library",
)

go_proto_library(
    name = "common_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/oslogin/common/commonpb",
    protos = [":common_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-oslogin-common-go",
    deps = [
        ":common_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_library",
    "py_proto_library",
    "py_gapic_assembly_pkg",
)

py_gapic_library(
    name = "common_py_gapic",
    srcs = [":common_proto"],
    rest_numeric_enums = False,
    transport = "grpc",
)

py_proto_library(
    name = "common_py_proto",
    deps = [":common_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "common-py",
    deps = [
        ":common_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "common_php_proto",
    deps = [":common_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "common_ruby_proto",
    deps = [":common_proto"],
)

ruby_grpc_library(
    name = "common_ruby_grpc",
    srcs = [":common_proto"],
    deps = [":common_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "common_csharp_proto",
    deps = [":common_proto"],
)

csharp_grpc_library(
    name = "common_csharp_grpc",
    srcs = [":common_proto"],
    deps = [":common_csharp_proto"],
)

csharp_gapic_library(
    name = "common_csharp_gapic",
    srcs = [":common_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    rest_numeric_enums = False,
    deps = [
        ":common_csharp_grpc",
        ":common_csharp_proto",
    ],
)

csharp_gapic_assembly_pkg(
    name = "google-cloud-oslogin-common-csharp",
    deps = [
        ":common_csharp_gapic",
        ":common_csharp_grpc",
        ":common_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "common_cc_proto",
    deps = [":common_proto"],
)

cc_grpc_library(
    name = "common_cc_grpc",
    srcs = [":common_proto"],
    grpc_only = True,
    deps = [":common_cc_proto"],
)
