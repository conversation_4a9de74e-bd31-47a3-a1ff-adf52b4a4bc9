// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apphub.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AppHub.V1";
option go_package = "cloud.google.com/go/apphub/apiv1/apphubpb;apphubpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProjectAttachmentProto";
option java_package = "com.google.cloud.apphub.v1";
option php_namespace = "Google\\Cloud\\AppHub\\V1";
option ruby_package = "Google::Cloud::AppHub::V1";

// ServiceProjectAttachment represents an attachment from a service project to a
// host project. Service projects contain the underlying cloud
// infrastructure resources, and expose these resources to the host project
// through a ServiceProjectAttachment. With the attachments, the host project
// can provide an aggregated view of resources across all service projects.
message ServiceProjectAttachment {
  option (google.api.resource) = {
    type: "apphub.googleapis.com/ServiceProjectAttachment"
    pattern: "projects/{project}/locations/{location}/serviceProjectAttachments/{service_project_attachment}"
    plural: "serviceProjectAttachments"
    singular: "serviceProjectAttachment"
  };

  // ServiceProjectAttachment state.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The ServiceProjectAttachment is being created.
    CREATING = 1;

    // The ServiceProjectAttachment is ready.
    // This means Services and Workloads under the corresponding
    // ServiceProjectAttachment is ready for registration.
    ACTIVE = 2;

    // The ServiceProjectAttachment is being deleted.
    DELETING = 3;
  }

  // Identifier. The resource name of a ServiceProjectAttachment. Format:
  // "projects/{host-project-id}/locations/global/serviceProjectAttachments/{service-project-id}."
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Immutable. Service project name in the format: "projects/abc" or
  // "projects/123". As input, project name with either project id or number are
  // accepted. As output, this field will contain project number.
  string service_project = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Output only. Create time.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A globally unique identifier (in UUID4 format) for the
  // `ServiceProjectAttachment`.
  string uid = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. ServiceProjectAttachment state.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
