// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apphub.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/apphub/v1/application.proto";
import "google/cloud/apphub/v1/service.proto";
import "google/cloud/apphub/v1/service_project_attachment.proto";
import "google/cloud/apphub/v1/workload.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AppHub.V1";
option go_package = "cloud.google.com/go/apphub/apiv1/apphubpb;apphubpb";
option java_multiple_files = true;
option java_outer_classname = "ApphubServiceProto";
option java_package = "com.google.cloud.apphub.v1";
option php_namespace = "Google\\Cloud\\AppHub\\V1";
option ruby_package = "Google::Cloud::AppHub::V1";

// The App Hub API allows you to manage App Hub resources.
service AppHub {
  option (google.api.default_host) = "apphub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists a service project attachment for a given service project. You can
  // call this API from any project to find if it is attached to a host project.
  rpc LookupServiceProjectAttachment(LookupServiceProjectAttachmentRequest)
      returns (LookupServiceProjectAttachmentResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*}:lookupServiceProjectAttachment"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists service projects attached to the host project.
  rpc ListServiceProjectAttachments(ListServiceProjectAttachmentsRequest)
      returns (ListServiceProjectAttachmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/serviceProjectAttachments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Attaches a service project to the host project.
  rpc CreateServiceProjectAttachment(CreateServiceProjectAttachmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/serviceProjectAttachments"
      body: "service_project_attachment"
    };
    option (google.api.method_signature) =
        "parent,service_project_attachment,service_project_attachment_id";
    option (google.longrunning.operation_info) = {
      response_type: "ServiceProjectAttachment"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets a service project attachment.
  rpc GetServiceProjectAttachment(GetServiceProjectAttachmentRequest)
      returns (ServiceProjectAttachment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/serviceProjectAttachments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a service project attachment.
  rpc DeleteServiceProjectAttachment(DeleteServiceProjectAttachmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/serviceProjectAttachments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Detaches a service project from a host project.
  // You can call this API from any service project without needing access to
  // the host project that it is attached to.
  rpc DetachServiceProjectAttachment(DetachServiceProjectAttachmentRequest)
      returns (DetachServiceProjectAttachmentResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*}:detachServiceProjectAttachment"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Discovered Services that can be added to an Application in a host
  // project and location.
  rpc ListDiscoveredServices(ListDiscoveredServicesRequest)
      returns (ListDiscoveredServicesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/discoveredServices"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a Discovered Service in a host project and location.
  rpc GetDiscoveredService(GetDiscoveredServiceRequest)
      returns (DiscoveredService) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/discoveredServices/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists a Discovered Service in a host project and location, with a
  // given resource URI.
  rpc LookupDiscoveredService(LookupDiscoveredServiceRequest)
      returns (LookupDiscoveredServiceResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/discoveredServices:lookup"
    };
    option (google.api.method_signature) = "parent,uri";
  }

  // Lists Services in an Application.
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/applications/*}/services"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a Service in an Application.
  rpc CreateService(CreateServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/applications/*}/services"
      body: "service"
    };
    option (google.api.method_signature) = "parent,service,service_id";
    option (google.longrunning.operation_info) = {
      response_type: "Service"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets a Service in an Application.
  rpc GetService(GetServiceRequest) returns (Service) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/applications/*/services/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Service in an Application.
  rpc UpdateService(UpdateServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{service.name=projects/*/locations/*/applications/*/services/*}"
      body: "service"
    };
    option (google.api.method_signature) = "service,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Service"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a Service from an Application.
  rpc DeleteService(DeleteServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/applications/*/services/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Discovered Workloads that can be added to an Application in a host
  // project and location.
  rpc ListDiscoveredWorkloads(ListDiscoveredWorkloadsRequest)
      returns (ListDiscoveredWorkloadsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/discoveredWorkloads"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a Discovered Workload in a host project and location.
  rpc GetDiscoveredWorkload(GetDiscoveredWorkloadRequest)
      returns (DiscoveredWorkload) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/discoveredWorkloads/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists a Discovered Workload in a host project and location, with a
  // given resource URI.
  rpc LookupDiscoveredWorkload(LookupDiscoveredWorkloadRequest)
      returns (LookupDiscoveredWorkloadResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/discoveredWorkloads:lookup"
    };
    option (google.api.method_signature) = "parent,uri";
  }

  // Lists Workloads in an Application.
  rpc ListWorkloads(ListWorkloadsRequest) returns (ListWorkloadsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/applications/*}/workloads"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a Workload in an Application.
  rpc CreateWorkload(CreateWorkloadRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/applications/*}/workloads"
      body: "workload"
    };
    option (google.api.method_signature) = "parent,workload,workload_id";
    option (google.longrunning.operation_info) = {
      response_type: "Workload"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets a Workload in an Application.
  rpc GetWorkload(GetWorkloadRequest) returns (Workload) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/applications/*/workloads/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Workload in an Application.
  rpc UpdateWorkload(UpdateWorkloadRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{workload.name=projects/*/locations/*/applications/*/workloads/*}"
      body: "workload"
    };
    option (google.api.method_signature) = "workload,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Workload"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a Workload from an Application.
  rpc DeleteWorkload(DeleteWorkloadRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/applications/*/workloads/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Applications in a host project and location.
  rpc ListApplications(ListApplicationsRequest)
      returns (ListApplicationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/applications"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an Application in a host project and location.
  rpc CreateApplication(CreateApplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/applications"
      body: "application"
    };
    option (google.api.method_signature) = "parent,application,application_id";
    option (google.longrunning.operation_info) = {
      response_type: "Application"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets an Application in a host project and location.
  rpc GetApplication(GetApplicationRequest) returns (Application) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/applications/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an Application in a host project and location.
  rpc UpdateApplication(UpdateApplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{application.name=projects/*/locations/*/applications/*}"
      body: "application"
    };
    option (google.api.method_signature) = "application,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Application"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes an Application in a host project and location.
  rpc DeleteApplication(DeleteApplicationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/applications/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Request for LookupServiceProjectAttachment.
message LookupServiceProjectAttachmentRequest {
  // Required. Service project ID and location to lookup service project
  // attachment for. Only global location is supported. Expected format:
  // `projects/{project}/locations/{location}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Response for LookupServiceProjectAttachment.
message LookupServiceProjectAttachmentResponse {
  // Service project attachment for a project if exists, empty otherwise.
  ServiceProjectAttachment service_project_attachment = 1;
}

// Request for ListServiceProjectAttachments.
message ListServiceProjectAttachmentsRequest {
  // Required. Host project ID and location to list service project attachments.
  // Only global location is supported. Expected format:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/ServiceProjectAttachment"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListServiceProjectAttachments.
message ListServiceProjectAttachmentsResponse {
  // List of service project attachments.
  repeated ServiceProjectAttachment service_project_attachments = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for CreateServiceProjectAttachment.
message CreateServiceProjectAttachmentRequest {
  // Required. Host project ID and location to which service project is being
  // attached. Only global location is supported. Expected format:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/ServiceProjectAttachment"
    }
  ];

  // Required. The service project attachment identifier must contain the
  // project id of the service project specified in the
  // service_project_attachment.service_project field.
  string service_project_attachment_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  ServiceProjectAttachment service_project_attachment = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for GetServiceProjectAttachment.
message GetServiceProjectAttachmentRequest {
  // Required. Fully qualified name of the service project attachment to
  // retrieve. Expected format:
  // `projects/{project}/locations/{location}/serviceProjectAttachments/{serviceProjectAttachment}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apphub.googleapis.com/ServiceProjectAttachment"
    }
  ];
}

// Request for DeleteServiceProjectAttachment.
message DeleteServiceProjectAttachmentRequest {
  // Required. Fully qualified name of the service project attachment to delete.
  // Expected format:
  // `projects/{project}/locations/{location}/serviceProjectAttachments/{serviceProjectAttachment}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apphub.googleapis.com/ServiceProjectAttachment"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for DetachServiceProjectAttachment.
message DetachServiceProjectAttachmentRequest {
  // Required. Service project id and location to detach from a host project.
  // Only global location is supported. Expected format:
  // `projects/{project}/locations/{location}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Response for DetachServiceProjectAttachment.
message DetachServiceProjectAttachmentResponse {}

// Request for ListServices.
message ListServicesRequest {
  // Required. Fully qualified name of the parent Application to list Services
  // for. Expected format:
  // `projects/{project}/locations/{location}/applications/{application}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/Service"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListServices.
message ListServicesResponse {
  // List of Services.
  repeated Service services = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for ListDiscoveredServices.
message ListDiscoveredServicesRequest {
  // Required. Project and location to list Discovered Services on.
  // Expected format: `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/DiscoveredService"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListDiscoveredServices.
message ListDiscoveredServicesResponse {
  // List of Discovered Services.
  repeated DiscoveredService discovered_services = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for CreateService.
message CreateServiceRequest {
  // Required. Fully qualified name of the parent Application to create the
  // Service in. Expected format:
  // `projects/{project}/locations/{location}/applications/{application}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/Service"
    }
  ];

  // Required. The Service identifier.
  // Must contain only lowercase letters, numbers
  // or hyphens, with the first character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string service_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Service service = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for GetService.
message GetServiceRequest {
  // Required. Fully qualified name of the Service to fetch.
  // Expected format:
  // `projects/{project}/locations/{location}/applications/{application}/services/{service}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apphub.googleapis.com/Service" }
  ];
}

// Request for GetDiscoveredService.
message GetDiscoveredServiceRequest {
  // Required. Fully qualified name of the Discovered Service to fetch.
  // Expected format:
  // `projects/{project}/locations/{location}/discoveredServices/{discoveredService}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apphub.googleapis.com/DiscoveredService"
    }
  ];
}

// Request for LookupDiscoveredService.
message LookupDiscoveredServiceRequest {
  // Required. Host project ID and location to lookup Discovered Service in.
  // Expected format: `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/DiscoveredService"
    }
  ];

  // Required. Resource URI to find DiscoveredService for.
  // Accepts both project number and project ID and does translation when
  // needed.
  string uri = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response for LookupDiscoveredService.
message LookupDiscoveredServiceResponse {
  // Discovered Service if exists, empty otherwise.
  DiscoveredService discovered_service = 1;
}

// Request for UpdateService.
message UpdateServiceRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Service resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request.
  // The API changes the values of the fields as specified in the update_mask.
  // The API ignores the values of all fields not covered by the update_mask.
  // You can also unset a field by not specifying it in the updated message, but
  // adding the field to the mask. This clears whatever value the field
  // previously had.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Service service = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for DeleteService.
message DeleteServiceRequest {
  // Required. Fully qualified name of the Service to delete from an
  // Application. Expected format:
  // `projects/{project}/locations/{location}/applications/{application}/services/{service}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apphub.googleapis.com/Service" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for ListApplications.
message ListApplicationsRequest {
  // Required. Project and location to list Applications on.
  // Expected format: `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/Application"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListApplications.
message ListApplicationsResponse {
  // List of Applications.
  repeated Application applications = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for CreateApplication.
message CreateApplicationRequest {
  // Required. Project and location to create Application in.
  // Expected format: `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/Application"
    }
  ];

  // Required. The Application identifier.
  // Must contain only lowercase letters, numbers
  // or hyphens, with the first character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string application_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Application application = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for GetApplication.
message GetApplicationRequest {
  // Required. Fully qualified name of the Application to fetch.
  // Expected format:
  // `projects/{project}/locations/{location}/applications/{application}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apphub.googleapis.com/Application"
    }
  ];
}

// Request for UpdateApplication.
message UpdateApplicationRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Application resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request.
  // The API changes the values of the fields as specified in the update_mask.
  // The API ignores the values of all fields not covered by the update_mask.
  // You can also unset a field by not specifying it in the updated message, but
  // adding the field to the mask. This clears whatever value the field
  // previously had.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Application application = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for DeleteApplication.
message DeleteApplicationRequest {
  // Required. Fully qualified name of the Application to delete.
  // Expected format:
  // `projects/{project}/locations/{location}/applications/{application}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apphub.googleapis.com/Application"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for ListWorkloads.
message ListWorkloadsRequest {
  // Required. Fully qualified name of the parent Application to list Workloads
  // for. Expected format:
  // `projects/{project}/locations/{location}/applications/{application}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/Workload"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListWorkloads.
message ListWorkloadsResponse {
  // List of Workloads.
  repeated Workload workloads = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for ListDiscoveredWorkloads.
message ListDiscoveredWorkloadsRequest {
  // Required. Project and location to list Discovered Workloads on.
  // Expected format: `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/DiscoveredWorkload"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListDiscoveredWorkloads.
message ListDiscoveredWorkloadsResponse {
  // List of Discovered Workloads.
  repeated DiscoveredWorkload discovered_workloads = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for CreateWorkload.
message CreateWorkloadRequest {
  // Required. Fully qualified name of the Application to create Workload in.
  // Expected format:
  // `projects/{project}/locations/{location}/applications/{application}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/Workload"
    }
  ];

  // Required. The Workload identifier.
  // Must contain only lowercase letters, numbers
  // or hyphens, with the first character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string workload_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Workload workload = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for GetWorkload.
message GetWorkloadRequest {
  // Required. Fully qualified name of the Workload to fetch.
  // Expected format:
  // `projects/{project}/locations/{location}/applications/{application}/workloads/{workload}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apphub.googleapis.com/Workload" }
  ];
}

// Request for GetDiscoveredWorkload.
message GetDiscoveredWorkloadRequest {
  // Required. Fully qualified name of the Discovered Workload to fetch.
  // Expected format:
  // `projects/{project}/locations/{location}/discoveredWorkloads/{discoveredWorkload}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apphub.googleapis.com/DiscoveredWorkload"
    }
  ];
}

// Request for LookupDiscoveredWorkload.
message LookupDiscoveredWorkloadRequest {
  // Required. Host project ID and location to lookup Discovered Workload in.
  // Expected format: `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/DiscoveredWorkload"
    }
  ];

  // Required. Resource URI to find Discovered Workload for.
  // Accepts both project number and project ID and does translation when
  // needed.
  string uri = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response for LookupDiscoveredWorkload.
message LookupDiscoveredWorkloadResponse {
  // Discovered Workload if exists, empty otherwise.
  DiscoveredWorkload discovered_workload = 1;
}

// Request for UpdateWorkload.
message UpdateWorkloadRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Workload resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request.
  // The API changes the values of the fields as specified in the update_mask.
  // The API ignores the values of all fields not covered by the update_mask.
  // You can also unset a field by not specifying it in the updated message, but
  // adding the field to the mask. This clears whatever value the field
  // previously had.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Workload workload = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for DeleteWorkload.
message DeleteWorkloadRequest {
  // Required. Fully qualified name of the Workload to delete from an
  // Application. Expected format:
  // `projects/{project}/locations/{location}/applications/{application}/workloads/{workload}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apphub.googleapis.com/Workload" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
