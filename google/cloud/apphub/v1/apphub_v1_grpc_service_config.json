{"methodConfig": [{"name": [{"service": "google.cloud.apphub.v1.AppHub", "method": "LookupServiceProjectAttachment"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "ListServiceProjectAttachments"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "GetServiceProjectAttachment"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "ListDiscoveredServices"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "GetDiscoveredService"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "LookupDiscoveredService"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "ListServices"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "GetService"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "ListDiscoveredWorkloads"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "GetDiscoveredWorkload"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "LookupDiscoveredWorkload"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "ListWorkloads"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "GetWorkload"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "ListApplications"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "GetApplication"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.apphub.v1.AppHub", "method": "CreateServiceProjectAttachment"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "DeleteServiceProjectAttachment"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "DetachServiceProjectAttachment"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "CreateService"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "UpdateService"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "DeleteService"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "CreateWorkload"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "UpdateWorkload"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "DeleteWorkload"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "CreateApplication"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "UpdateApplication"}, {"service": "google.cloud.apphub.v1.AppHub", "method": "DeleteApplication"}], "timeout": "60s"}]}