// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apphub.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/apphub/v1/attributes.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AppHub.V1";
option go_package = "cloud.google.com/go/apphub/apiv1/apphubpb;apphubpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.apphub.v1";
option php_namespace = "Google\\Cloud\\AppHub\\V1";
option ruby_package = "Google::Cloud::AppHub::V1";

// Service is an App Hub data model that contains a discovered service, which
// represents a network/api interface that exposes some functionality to clients
// for consumption over the network.
message Service {
  option (google.api.resource) = {
    type: "apphub.googleapis.com/Service"
    pattern: "projects/{project}/locations/{location}/applications/{application}/services/{service}"
    plural: "services"
    singular: "service"
  };

  // Service state.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The service is being created.
    CREATING = 1;

    // The service is ready.
    ACTIVE = 2;

    // The service is being deleted.
    DELETING = 3;

    // The underlying networking resources have been deleted.
    DETACHED = 4;
  }

  // Identifier. The resource name of a Service. Format:
  // "projects/{host-project-id}/locations/{location}/applications/{application-id}/services/{service-id}"
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. User-defined name for the Service.
  // Can have a maximum length of 63 characters.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User-defined description of a Service.
  // Can have a maximum length of 2048 characters.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Reference to an underlying networking resource that can
  // comprise a Service. These are immutable.
  ServiceReference service_reference = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Properties of an underlying compute resource that can comprise
  // a Service. These are immutable.
  ServiceProperties service_properties = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Consumer provided attributes.
  Attributes attributes = 6 [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. The resource name of the original discovered service.
  string discovered_service = 7 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/DiscoveredService"
    }
  ];

  // Output only. Create time.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A universally unique identifier (UUID) for the `Service` in
  // the UUID4 format.
  string uid = 10 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Service state.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Reference to an underlying networking resource that can comprise a Service.
message ServiceReference {
  // Output only. The underlying resource URI (For example, URI of Forwarding
  // Rule, URL Map, and Backend Service).
  string uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Properties of an underlying cloud resource that can comprise a Service.
message ServiceProperties {
  // Output only. The service project identifier that the underlying cloud
  // resource resides in.
  string gcp_project = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The location that the underlying resource resides in, for
  // example, us-west1.
  string location = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The location that the underlying resource resides in if it is
  // zonal, for example, us-west1-a).
  string zone = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// DiscoveredService is a network/api interface that exposes some functionality
// to clients for consumption over the network. A discovered service can be
// registered to a App Hub service.
message DiscoveredService {
  option (google.api.resource) = {
    type: "apphub.googleapis.com/DiscoveredService"
    pattern: "projects/{project}/locations/{location}/discoveredServices/{discovered_service}"
    plural: "discoveredServices"
    singular: "discoveredService"
  };

  // Identifier. The resource name of the discovered service. Format:
  // "projects/{host-project-id}/locations/{location}/discoveredServices/{uuid}""
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Reference to an underlying networking resource that can
  // comprise a Service. These are immutable.
  ServiceReference service_reference = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Properties of an underlying compute resource that can comprise
  // a Service. These are immutable.
  ServiceProperties service_properties = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
