// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apphub.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/apphub/v1/attributes.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AppHub.V1";
option go_package = "cloud.google.com/go/apphub/apiv1/apphubpb;apphubpb";
option java_multiple_files = true;
option java_outer_classname = "ApplicationProto";
option java_package = "com.google.cloud.apphub.v1";
option php_namespace = "Google\\Cloud\\AppHub\\V1";
option ruby_package = "Google::Cloud::AppHub::V1";

// Application defines the governance boundary for App Hub Entities that
// perform a logical end-to-end business function.
// App Hub supports application level IAM permission to align with governance
// requirements.
message Application {
  option (google.api.resource) = {
    type: "apphub.googleapis.com/Application"
    pattern: "projects/{project}/locations/{location}/applications/{application}"
    plural: "applications"
    singular: "application"
  };

  // Application state.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The Application is being created.
    CREATING = 1;

    // The Application is ready to register Services and Workloads.
    ACTIVE = 2;

    // The Application is being deleted.
    DELETING = 3;
  }

  // Identifier. The resource name of an Application. Format:
  // "projects/{host-project-id}/locations/{location}/applications/{application-id}"
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. User-defined name for the Application.
  // Can have a maximum length of 63 characters.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User-defined description of an Application.
  // Can have a maximum length of 2048 characters.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Consumer provided attributes.
  Attributes attributes = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Create time.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Defines what data can be included into this
  // Application. Limits which Services and Workloads can be registered.
  Scope scope = 9 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. A universally unique identifier (in UUID4 format) for the
  // `Application`.
  string uid = 10 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Application state.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Scope of an application.
message Scope {
  // Scope Type.
  enum Type {
    // Unspecified type.
    TYPE_UNSPECIFIED = 0;

    // Regional type.
    REGIONAL = 1;
  }

  // Required. Scope Type.
  Type type = 1 [(google.api.field_behavior) = REQUIRED];
}
