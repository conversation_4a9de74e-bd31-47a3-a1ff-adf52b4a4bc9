// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apphub.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/apphub/v1/attributes.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AppHub.V1";
option go_package = "cloud.google.com/go/apphub/apiv1/apphubpb;apphubpb";
option java_multiple_files = true;
option java_outer_classname = "WorkloadProto";
option java_package = "com.google.cloud.apphub.v1";
option php_namespace = "Google\\Cloud\\AppHub\\V1";
option ruby_package = "Google::Cloud::AppHub::V1";

// Workload is an App Hub data model that contains a discovered workload, which
// represents a binary deployment (such as managed instance groups (MIGs) and
// GKE deployments) that performs the smallest logical subset of business
// functionality.
message Workload {
  option (google.api.resource) = {
    type: "apphub.googleapis.com/Workload"
    pattern: "projects/{project}/locations/{location}/applications/{application}/workloads/{workload}"
    plural: "workloads"
    singular: "workload"
  };

  // Workload state.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The Workload is being created.
    CREATING = 1;

    // The Workload is ready.
    ACTIVE = 2;

    // The Workload is being deleted.
    DELETING = 3;

    // The underlying compute resources have been deleted.
    DETACHED = 4;
  }

  // Identifier. The resource name of the Workload. Format:
  // "projects/{host-project-id}/locations/{location}/applications/{application-id}/workloads/{workload-id}"
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. User-defined name for the Workload.
  // Can have a maximum length of 63 characters.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User-defined description of a Workload.
  // Can have a maximum length of 2048 characters.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Reference of an underlying compute resource represented by the
  // Workload. These are immutable.
  WorkloadReference workload_reference = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Properties of an underlying compute resource represented by
  // the Workload. These are immutable.
  WorkloadProperties workload_properties = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The resource name of the original discovered workload.
  string discovered_workload = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      child_type: "apphub.googleapis.com/DiscoveredWorkload"
    }
  ];

  // Optional. Consumer provided attributes.
  Attributes attributes = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Create time.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A universally unique identifier (UUID) for the `Workload` in
  // the UUID4 format.
  string uid = 10 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Workload state.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Reference of an underlying compute resource represented by the Workload.
message WorkloadReference {
  // Output only. The underlying compute resource uri.
  string uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Properties of an underlying compute resource represented by the Workload.
message WorkloadProperties {
  // Output only. The service project identifier that the underlying cloud
  // resource resides in. Empty for non cloud resources.
  string gcp_project = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The location that the underlying compute resource resides in
  // (e.g us-west1).
  string location = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The location that the underlying compute resource resides in
  // if it is zonal (e.g us-west1-a).
  string zone = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// DiscoveredWorkload is a binary deployment (such as managed instance groups
// (MIGs) and GKE deployments) that performs the smallest logical subset of
// business functionality. A discovered workload can be registered to an App Hub
// Workload.
message DiscoveredWorkload {
  option (google.api.resource) = {
    type: "apphub.googleapis.com/DiscoveredWorkload"
    pattern: "projects/{project}/locations/{location}/discoveredWorkloads/{discovered_workload}"
    plural: "discoveredWorkloads"
    singular: "discoveredWorkload"
  };

  // Identifier. The resource name of the discovered workload. Format:
  // "projects/{host-project-id}/locations/{location}/discoveredWorkloads/{uuid}"
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Reference of an underlying compute resource represented by the
  // Workload. These are immutable.
  WorkloadReference workload_reference = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Properties of an underlying compute resource represented by
  // the Workload. These are immutable.
  WorkloadProperties workload_properties = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
