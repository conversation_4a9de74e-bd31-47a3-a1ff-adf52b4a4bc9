// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apphub.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AppHub.V1";
option go_package = "cloud.google.com/go/apphub/apiv1/apphubpb;apphubpb";
option java_multiple_files = true;
option java_outer_classname = "AttributesProto";
option java_package = "com.google.cloud.apphub.v1";
option php_namespace = "Google\\Cloud\\AppHub\\V1";
option ruby_package = "Google::Cloud::AppHub::V1";

// Consumer provided attributes.
message Attributes {
  // Optional. User-defined criticality information.
  Criticality criticality = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User-defined environment information.
  Environment environment = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Developer team that owns development and coding.
  repeated ContactInfo developer_owners = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Operator team that ensures runtime and operations.
  repeated ContactInfo operator_owners = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Business team that ensures user needs are met and value is
  // delivered
  repeated ContactInfo business_owners = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Criticality of the Application, Service, or Workload
message Criticality {
  // Criticality Type.
  enum Type {
    // Unspecified type.
    TYPE_UNSPECIFIED = 0;

    // Mission critical service, application or workload.
    MISSION_CRITICAL = 1;

    // High impact.
    HIGH = 2;

    // Medium impact.
    MEDIUM = 3;

    // Low impact.
    LOW = 4;
  }

  // Required. Criticality Type.
  Type type = 3 [(google.api.field_behavior) = REQUIRED];
}

// Environment of the Application, Service, or Workload
message Environment {
  // Environment Type.
  enum Type {
    // Unspecified type.
    TYPE_UNSPECIFIED = 0;

    // Production environment.
    PRODUCTION = 1;

    // Staging environment.
    STAGING = 2;

    // Test environment.
    TEST = 3;

    // Development environment.
    DEVELOPMENT = 4;
  }

  // Required. Environment Type.
  Type type = 2 [(google.api.field_behavior) = REQUIRED];
}

// Contact information of stakeholders.
message ContactInfo {
  // Optional. Contact's name.
  // Can have a maximum length of 63 characters.
  string display_name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Email address of the contacts.
  string email = 2 [(google.api.field_behavior) = REQUIRED];
}
