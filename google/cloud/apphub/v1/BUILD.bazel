# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "apphub_proto",
    srcs = [
        "apphub_service.proto",
        "application.proto",
        "attributes.proto",
        "service.proto",
        "service_project_attachment.proto",
        "workload.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "apphub_proto_with_info",
    deps = [
        ":apphub_proto",
        "//google/cloud/location:location_proto",
        "//google/cloud:common_resources_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "apphub_java_proto",
    deps = [":apphub_proto"],
)

java_grpc_library(
    name = "apphub_java_grpc",
    srcs = [":apphub_proto"],
    deps = [":apphub_java_proto"],
)

java_gapic_library(
    name = "apphub_java_gapic",
    srcs = [":apphub_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "apphub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apphub_v1.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":apphub_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":apphub_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "apphub_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.apphub.v1.AppHubClientHttpJsonTest",
        "com.google.cloud.apphub.v1.AppHubClientTest",
    ],
    runtime_deps = [":apphub_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-apphub-v1-java",
    transport = "grpc+rest",
    deps = [
        ":apphub_java_gapic",
        ":apphub_java_grpc",
        ":apphub_java_proto",
        ":apphub_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "apphub_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/apphub/apiv1/apphubpb",
    protos = [":apphub_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "apphub_go_gapic",
    srcs = [":apphub_proto_with_info"],
    grpc_service_config = "apphub_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/apphub/apiv1;apphub",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "apphub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apphub_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-apphub-v1-go",
    deps = [
        ":apphub_go_gapic",
        ":apphub_go_gapic_srcjar-test.srcjar",
        ":apphub_go_gapic_srcjar-metadata.srcjar",
        ":apphub_go_gapic_srcjar-snippets.srcjar",
        ":apphub_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "apphub_py_gapic",
    srcs = [":apphub_proto"],
    grpc_service_config = "apphub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apphub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "apphub_py_gapic_test",
    srcs = [
        "apphub_py_gapic_pytest.py",
        "apphub_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":apphub_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "apphub-v1-py",
    deps = [
        ":apphub_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "apphub_php_proto",
    deps = [":apphub_proto"],
)

php_gapic_library(
    name = "apphub_php_gapic",
    srcs = [":apphub_proto_with_info"],
    grpc_service_config = "apphub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "apphub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apphub_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-apphub-v1-php",
    deps = [
        ":apphub_php_gapic",
        ":apphub_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "apphub_nodejs_gapic",
    package_name = "@google-cloud/apphub",
    src = ":apphub_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "apphub_v1_grpc_service_config.json",
    package = "google.cloud.apphub.v1",
    rest_numeric_enums = True,
    service_yaml = "apphub_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "apphub-v1-nodejs",
    deps = [
        ":apphub_nodejs_gapic",
        ":apphub_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "apphub_ruby_proto",
    deps = [":apphub_proto"],
)

ruby_grpc_library(
    name = "apphub_ruby_grpc",
    srcs = [":apphub_proto"],
    deps = [":apphub_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "apphub_ruby_gapic",
    srcs = [":apphub_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-app_hub-v1",
    ],
    grpc_service_config = "apphub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apphub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apphub_ruby_grpc",
        ":apphub_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-app_hub-v1-ruby",
    deps = [
        ":apphub_ruby_gapic",
        ":apphub_ruby_grpc",
        ":apphub_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "apphub_csharp_proto",
    extra_opts = [],
    deps = [":apphub_proto"],
)

csharp_grpc_library(
    name = "apphub_csharp_grpc",
    srcs = [":apphub_proto"],
    deps = [":apphub_csharp_proto"],
)

csharp_gapic_library(
    name = "apphub_csharp_gapic",
    srcs = [":apphub_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "apphub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apphub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apphub_csharp_grpc",
        ":apphub_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-apphub-v1-csharp",
    deps = [
        ":apphub_csharp_gapic",
        ":apphub_csharp_grpc",
        ":apphub_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "apphub_cc_proto",
    deps = [":apphub_proto"],
)

cc_grpc_library(
    name = "apphub_cc_grpc",
    srcs = [":apphub_proto"],
    grpc_only = True,
    deps = [":apphub_cc_proto"],
)
