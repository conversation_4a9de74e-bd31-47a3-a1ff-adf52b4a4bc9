// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.edgecontainer.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.EdgeContainer.V1";
option go_package = "cloud.google.com/go/edgecontainer/apiv1/edgecontainerpb;edgecontainerpb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.cloud.edgecontainer.v1";
option php_namespace = "Google\\Cloud\\EdgeContainer\\V1";
option ruby_package = "Google::Cloud::EdgeContainer::V1";
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKeyVersion"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}/cryptoKeyVersions/{crypto_key_version}"
};

// Represents the accessibility state of a customer-managed KMS key used for
// CMEK integration.
enum KmsKeyState {
  // Unspecified.
  KMS_KEY_STATE_UNSPECIFIED = 0;

  // The key is available for use, and dependent resources should be accessible.
  KMS_KEY_STATE_KEY_AVAILABLE = 1;

  // The key is unavailable for an unspecified reason. Dependent resources may
  // be inaccessible.
  KMS_KEY_STATE_KEY_UNAVAILABLE = 2;
}

// Represents if the resource is in lock down state or pending.
enum ResourceState {
  // Default value.
  RESOURCE_STATE_UNSPECIFIED = 0;

  // The resource is in LOCK DOWN state.
  RESOURCE_STATE_LOCK_DOWN = 1;

  // The resource is pending lock down.
  RESOURCE_STATE_LOCK_DOWN_PENDING = 2;
}

// A Google Distributed Cloud Edge Kubernetes cluster.
message Cluster {
  option (google.api.resource) = {
    type: "edgecontainer.googleapis.com/Cluster"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}"
  };

  // Configuration of the cluster control plane.
  message ControlPlane {
    // Configuration specific to clusters with a control plane hosted remotely.
    message Remote {}

    // Configuration specific to clusters with a control plane hosted locally.
    //
    // Warning: Local control plane clusters must be created in their own
    // project. Local control plane clusters cannot coexist in the same
    // project with any other type of clusters, including non-GDCE clusters.
    // Mixing local control plane GDCE clusters with any other type of
    // clusters in the same project can result in data loss.
    message Local {
      // Name of the Google Distributed Cloud Edge zones where this node pool
      // will be created. For example: `us-central1-edge-customer-a`.
      string node_location = 1;

      // The number of nodes to serve as replicas of the Control Plane.
      int32 node_count = 2;

      // Only machines matching this filter will be allowed to host control
      // plane nodes. The filtering language accepts strings like "name=<name>",
      // and is documented here: [AIP-160](https://google.aip.dev/160).
      string machine_filter = 3;

      // Policy configuration about how user applications are deployed.
      SharedDeploymentPolicy shared_deployment_policy = 4;

      // Optional. Name for the storage schema of control plane nodes.
      //
      // Warning: Configurable node local storage schema feature is an
      // experimental feature, and is not recommended for general use
      // in production clusters/nodepools.
      string control_plane_node_storage_schema = 5
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Represents the policy configuration about how user applications are
    // deployed.
    enum SharedDeploymentPolicy {
      // Unspecified.
      SHARED_DEPLOYMENT_POLICY_UNSPECIFIED = 0;

      // User applications can be deployed both on control plane and worker
      // nodes.
      ALLOWED = 1;

      // User applications can not be deployed on control plane nodes and can
      // only be deployed on worker nodes.
      DISALLOWED = 2;
    }

    oneof config {
      // Remote control plane configuration.
      Remote remote = 1;

      // Local control plane configuration.
      //
      // Warning: Local control plane clusters must be created in their own
      // project. Local control plane clusters cannot coexist in the same
      // project with any other type of clusters, including non-GDCE clusters.
      // Mixing local control plane GDCE clusters with any other type of
      // clusters in the same project can result in data loss.
      Local local = 2;
    }
  }

  // Config that customers are allowed to define for GDCE system add-ons.
  message SystemAddonsConfig {
    // Config for the Ingress add-on which allows customers to create an Ingress
    // object to manage external access to the servers in a cluster. The add-on
    // consists of istiod and istio-ingress.
    message Ingress {
      // Optional. Whether Ingress is disabled.
      bool disabled = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Ingress VIP.
      string ipv4_vip = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // VMServiceConfig defines the configuration for GDCE VM Service.
    message VMServiceConfig {
      // Optional. Whether VMM is enabled.
      bool vmm_enabled = 1 [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. Config for Ingress.
    Ingress ingress = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Config for VM Service.
    VMServiceConfig vm_service_config = 4
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Configuration for Customer-managed KMS key support for control plane nodes.
  message ControlPlaneEncryption {
    // Optional. The Cloud KMS CryptoKey e.g.
    // projects/{project}/locations/{location}/keyRings/{keyRing}/cryptoKeys/{cryptoKey}
    // to use for protecting control plane disks. If not specified, a
    // Google-managed key will be used instead.
    string kms_key = 1 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "cloudkms.googleapis.com/CryptoKey"
      }
    ];

    // Output only. The Cloud KMS CryptoKeyVersion currently in use for
    // protecting control plane disks. Only applicable if kms_key is set.
    string kms_key_active_version = 2 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "cloudkms.googleapis.com/CryptoKeyVersion"
      }
    ];

    // Output only. Availability of the Cloud KMS CryptoKey. If not
    // `KEY_AVAILABLE`, then nodes may go offline as they cannot access their
    // local data. This can be caused by a lack of permissions to use the key,
    // or if the key is disabled or deleted.
    KmsKeyState kms_key_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Error status returned by Cloud KMS when using this key. This
    // field may be populated only if `kms_key_state` is not
    // `KMS_KEY_STATE_KEY_AVAILABLE`. If populated, this field contains the
    // error status reported by Cloud KMS.
    google.rpc.Status kms_status = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The current resource state associated with the cmek.
    ResourceState resource_state = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // A Maintenance Event is an operation that could cause temporary disruptions
  // to the cluster workloads, including Google-driven or user-initiated cluster
  // upgrades, user-initiated cluster configuration changes that require
  // restarting nodes, etc.
  message MaintenanceEvent {
    // Indicates the maintenance event type.
    enum Type {
      // Unspecified.
      TYPE_UNSPECIFIED = 0;

      // Upgrade initiated by users.
      USER_INITIATED_UPGRADE = 1;

      // Upgrade driven by Google.
      GOOGLE_DRIVEN_UPGRADE = 2;
    }

    // Indicates when the maintenance event should be performed.
    enum Schedule {
      // Unspecified.
      SCHEDULE_UNSPECIFIED = 0;

      // Immediately after receiving the request.
      IMMEDIATELY = 1;
    }

    // Indicates the maintenance event state.
    enum State {
      // Unspecified.
      STATE_UNSPECIFIED = 0;

      // The maintenance event is ongoing. The cluster might be unusable.
      RECONCILING = 1;

      // The maintenance event succeeded.
      SUCCEEDED = 2;

      // The maintenance event failed.
      FAILED = 3;
    }

    // Output only. UUID of the maintenance event.
    string uuid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The target version of the cluster.
    string target_version = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The operation for running the maintenance event. Specified
    // in the format projects/*/locations/*/operations/*. If the maintenance
    // event is split into multiple operations (e.g. due to maintenance
    // windows), the latest one is recorded.
    string operation = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The type of the maintenance event.
    Type type = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The schedule of the maintenance event.
    Schedule schedule = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The state of the maintenance event.
    State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when the maintenance event request was created.
    google.protobuf.Timestamp create_time = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when the maintenance event started.
    google.protobuf.Timestamp start_time = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when the maintenance event ended, either
    // successfully or not. If the maintenance event is split into multiple
    // maintenance windows, end_time is only updated when the whole flow ends.
    google.protobuf.Timestamp end_time = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when the maintenance event message was updated.
    google.protobuf.Timestamp update_time = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Configuration of the cluster survivability, e.g., for the case when network
  // connectivity is lost.
  message SurvivabilityConfig {
    // Optional. Time period that allows the cluster nodes to be rebooted and
    // become functional without network connectivity to Google. The default 0
    // means not allowed. The maximum is 7 days.
    google.protobuf.Duration offline_reboot_ttl = 1
        [(google.api.field_behavior) = OPTIONAL];
  }

  // ConnectionState holds the current connection state from the cluster to
  // Google.
  message ConnectionState {
    // The connection state.
    enum State {
      // Unknown connection state.
      STATE_UNSPECIFIED = 0;

      // This cluster is currently disconnected from Google.
      DISCONNECTED = 1;

      // This cluster is currently connected to Google.
      CONNECTED = 2;

      // This cluster is currently connected to Google, but may have recently
      // reconnected after a disconnection. It is still syncing back.
      CONNECTED_AND_SYNCING = 3;
    }

    // Output only. The current connection state.
    State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when the connection state was last changed.
    google.protobuf.Timestamp update_time = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Indicates the status of the cluster.
  enum Status {
    // Status unknown.
    STATUS_UNSPECIFIED = 0;

    // The cluster is being created.
    PROVISIONING = 1;

    // The cluster is created and fully usable.
    RUNNING = 2;

    // The cluster is being deleted.
    DELETING = 3;

    // The status indicates that some errors occurred while reconciling/deleting
    // the cluster.
    ERROR = 4;

    // The cluster is undergoing some work such as version upgrades, etc.
    RECONCILING = 5;
  }

  // The release channel a cluster is subscribed to.
  enum ReleaseChannel {
    // Unspecified release channel. This will default to the REGULAR channel.
    RELEASE_CHANNEL_UNSPECIFIED = 0;

    // No release channel.
    NONE = 1;

    // Regular release channel.
    REGULAR = 2;
  }

  // Required. The resource name of the cluster.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/Cluster"
    }
  ];

  // Output only. The time when the cluster was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the cluster was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels associated with this resource.
  map<string, string> labels = 4;

  // Required. Fleet configuration.
  Fleet fleet = 11 [(google.api.field_behavior) = REQUIRED];

  // Required. Cluster-wide networking configuration.
  ClusterNetworking networking = 7 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. RBAC policy that will be applied and managed by GEC.
  Authorization authorization = 9 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. The default maximum number of pods per node used if a maximum
  // value is not specified explicitly for a node pool in this cluster. If
  // unspecified, the Kubernetes default value will be used.
  int32 default_max_pods_per_node = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The IP address of the Kubernetes API server.
  string endpoint = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The port number of the Kubernetes API server.
  int32 port = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The PEM-encoded public certificate of the cluster's CA.
  string cluster_ca_certificate = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Cluster-wide maintenance policy configuration.
  MaintenancePolicy maintenance_policy = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The control plane release version
  string control_plane_version = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The lowest release version among all worker nodes. This field
  // can be empty if the cluster does not have any worker nodes.
  string node_version = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The configuration of the cluster control plane.
  ControlPlane control_plane = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The configuration of the system add-ons.
  SystemAddonsConfig system_addons_config = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. IPv4 address pools for cluster data plane external load
  // balancing.
  repeated string external_load_balancer_ipv4_address_pools = 17
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Remote control plane disk encryption options. This field is only
  // used when enabling CMEK support.
  ControlPlaneEncryption control_plane_encryption = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current status of the cluster.
  Status status = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. All the maintenance events scheduled for the cluster,
  // including the ones ongoing, planned for the future and done in the past (up
  // to 90 days).
  repeated MaintenanceEvent maintenance_events = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The target cluster version. For example: "1.5.0".
  string target_version = 22 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The release channel a cluster is subscribed to.
  ReleaseChannel release_channel = 23 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration of the cluster survivability, e.g., for the case
  // when network connectivity is lost. Note: This only applies to local control
  // plane clusters.
  SurvivabilityConfig survivability_config = 24
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. IPv6 address pools for cluster data plane external load
  // balancing.
  repeated string external_load_balancer_ipv6_address_pools = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current connection state of the cluster.
  ConnectionState connection_state = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Cluster-wide networking configuration.
message ClusterNetworking {
  // Required. All pods in the cluster are assigned an RFC1918 IPv4 address from
  // these blocks. Only a single block is supported. This field cannot be
  // changed after creation.
  repeated string cluster_ipv4_cidr_blocks = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. All services in the cluster are assigned an RFC1918 IPv4 address
  // from these blocks. Only a single block is supported. This field cannot be
  // changed after creation.
  repeated string services_ipv4_cidr_blocks = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Fleet related configuration.
//
// Fleets are a Google Cloud concept for logically organizing clusters,
// letting you use and manage multi-cluster capabilities and apply
// consistent policies across your systems.
message Fleet {
  // Required. The name of the Fleet host project where this cluster will be
  // registered.
  //
  // Project names are formatted as
  // `projects/<project-number>`.
  string project = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The name of the managed Hub Membership resource associated to
  // this cluster.
  //
  // Membership names are formatted as
  // `projects/<project-number>/locations/global/membership/<cluster-id>`.
  string membership = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A user principal for an RBAC policy.
message ClusterUser {
  // Required. An active Google username.
  string username = 1 [(google.api.field_behavior) = REQUIRED];
}

// RBAC policy that will be applied and managed by GEC.
message Authorization {
  // Required. User that will be granted the cluster-admin role on the cluster,
  // providing full access to the cluster. Currently, this is a singular field,
  // but will be expanded to allow multiple admins in the future.
  ClusterUser admin_users = 1 [(google.api.field_behavior) = REQUIRED];
}

// A set of Kubernetes nodes in a cluster with common configuration and
// specification.
message NodePool {
  option (google.api.resource) = {
    type: "edgecontainer.googleapis.com/NodePool"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/nodePools/{node_pool}"
  };

  // Configuration for CMEK support for edge machine local disk encryption.
  message LocalDiskEncryption {
    // Optional. The Cloud KMS CryptoKey e.g.
    // projects/{project}/locations/{location}/keyRings/{keyRing}/cryptoKeys/{cryptoKey}
    // to use for protecting node local disks. If not specified, a
    // Google-managed key will be used instead.
    string kms_key = 1 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "cloudkms.googleapis.com/CryptoKey"
      }
    ];

    // Output only. The Cloud KMS CryptoKeyVersion currently in use for
    // protecting node local disks. Only applicable if kms_key is set.
    string kms_key_active_version = 2 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "cloudkms.googleapis.com/CryptoKeyVersion"
      }
    ];

    // Output only. Availability of the Cloud KMS CryptoKey. If not
    // `KEY_AVAILABLE`, then nodes may go offline as they cannot access their
    // local data. This can be caused by a lack of permissions to use the key,
    // or if the key is disabled or deleted.
    KmsKeyState kms_key_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Error status returned by Cloud KMS when using this key. This
    // field may be populated only if `kms_key_state` is not
    // `KMS_KEY_STATE_KEY_AVAILABLE`. If populated, this field contains the
    // error status reported by Cloud KMS.
    google.rpc.Status kms_status = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The current resource state associated with the cmek.
    ResourceState resource_state = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Configuration for each node in the NodePool
  message NodeConfig {
    // Optional. The Kubernetes node labels
    map<string, string> labels = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Name for the storage schema of worker nodes.
    //
    // Warning: Configurable node local storage schema feature is an
    // experimental feature, and is not recommended for general use
    // in production clusters/nodepools.
    string node_storage_schema = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The resource name of the node pool.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/NodePool"
    }
  ];

  // Output only. The time when the node pool was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the node pool was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels associated with this resource.
  map<string, string> labels = 4;

  // Name of the Google Distributed Cloud Edge zone where this node pool will be
  // created. For example: `us-central1-edge-customer-a`.
  string node_location = 8;

  // Required. The number of nodes in the pool.
  int32 node_count = 6 [(google.api.field_behavior) = REQUIRED];

  // Only machines matching this filter will be allowed to join the node pool.
  // The filtering language accepts strings like "name=<name>", and is
  // documented in more detail in [AIP-160](https://google.aip.dev/160).
  string machine_filter = 7;

  // Optional. Local disk encryption options. This field is only used when
  // enabling CMEK support.
  LocalDiskEncryption local_disk_encryption = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The lowest release version among all worker nodes.
  string node_version = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Configuration for each node in the NodePool
  NodeConfig node_config = 11 [(google.api.field_behavior) = OPTIONAL];
}

// A Google Distributed Cloud Edge machine capable of acting as a Kubernetes
// node.
message Machine {
  option (google.api.resource) = {
    type: "edgecontainer.googleapis.com/Machine"
    pattern: "projects/{project}/locations/{location}/machines/{machine}"
  };

  // Required. The resource name of the machine.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/Machine"
    }
  ];

  // Output only. The time when the node pool was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the node pool was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels associated with this resource.
  map<string, string> labels = 4;

  // Canonical resource name of the node that this machine is responsible for
  // hosting e.g.
  // projects/{project}/locations/{location}/clusters/{cluster_id}/nodePools/{pool_id}/{node},
  // Or empty if the machine is not assigned to assume the role of a node.
  //
  // For control plane nodes hosted on edge machines, this will return
  // the following format:
  //   "projects/{project}/locations/{location}/clusters/{cluster_id}/controlPlaneNodes/{node}".
  string hosted_node = 5;

  // The Google Distributed Cloud Edge zone of this machine.
  string zone = 6;

  // Output only. The software version of the machine.
  string version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the machine is disabled. If disabled, the machine is
  // unable to enter service.
  bool disabled = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A VPN connection .
message VpnConnection {
  option (google.api.resource) = {
    type: "edgecontainer.googleapis.com/VpnConnection"
    pattern: "projects/{project}/locations/{location}/vpnConnections/{vpn_connection}"
  };

  // Project detail of the VPC network.
  message VpcProject {
    // The project of the VPC to connect to. If not specified, it is the same as
    // the cluster project.
    string project_id = 1;

    // Optional. Deprecated: do not use.
    string service_account = 2
        [deprecated = true, (google.api.field_behavior) = OPTIONAL];
  }

  // The created connection details.
  message Details {
    // The Cloud Router info.
    message CloudRouter {
      // The associated Cloud Router name.
      string name = 1;
    }

    // The Cloud VPN info.
    message CloudVpn {
      // The created Cloud VPN gateway name.
      string gateway = 1;
    }

    // The current connection state.
    enum State {
      // Unknown.
      STATE_UNSPECIFIED = 0;

      // Connected.
      STATE_CONNECTED = 1;

      // Still connecting.
      STATE_CONNECTING = 2;

      // Error occurred.
      STATE_ERROR = 3;
    }

    // The state of this connection.
    State state = 1;

    // The error message. This is only populated when state=ERROR.
    string error = 2;

    // The Cloud Router info.
    CloudRouter cloud_router = 3;

    // Each connection has multiple Cloud VPN gateways.
    repeated CloudVpn cloud_vpns = 4;
  }

  // Routing mode.
  enum BgpRoutingMode {
    // Unknown.
    BGP_ROUTING_MODE_UNSPECIFIED = 0;

    // Regional mode.
    REGIONAL = 1;

    // Global mode.
    GLOBAL = 2;
  }

  // Required. The resource name of VPN connection
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/VpnConnection"
    }
  ];

  // Output only. The time when the VPN connection was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the VPN connection was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels associated with this resource.
  map<string, string> labels = 4;

  // NAT gateway IP, or WAN IP address. If a customer has multiple NAT IPs, the
  // customer needs to configure NAT such that only one external IP maps to the
  // GMEC Anthos cluster. This is empty if NAT is not used.
  string nat_gateway_ip = 5;

  // Dynamic routing mode of the VPC network, `regional` or `global`.
  BgpRoutingMode bgp_routing_mode = 6 [deprecated = true];

  // The canonical Cluster name to connect to. It is in the form of
  // projects/{project}/locations/{location}/clusters/{cluster}.
  string cluster = 7 [(google.api.resource_reference) = {
    type: "edgecontainer.googleapis.com/Cluster"
  }];

  // The network ID of VPC to connect to.
  string vpc = 8;

  // Optional. Project detail of the VPC network. Required if VPC is in a
  // different project than the cluster project.
  VpcProject vpc_project = 11 [(google.api.field_behavior) = OPTIONAL];

  // Whether this VPN connection has HA enabled on cluster side. If enabled,
  // when creating VPN connection we will attempt to use 2 ANG floating IPs.
  bool enable_high_availability = 9;

  // Optional. The VPN connection Cloud Router name.
  string router = 12 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The created connection details.
  Details details = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata for a given
// [google.cloud.location.Location][google.cloud.location.Location].
message LocationMetadata {
  // The set of available Google Distributed Cloud Edge zones in the location.
  // The map is keyed by the lowercase ID of each zone.
  map<string, ZoneMetadata> available_zones = 1;
}

// A Google Distributed Cloud Edge zone where edge machines are located.
message ZoneMetadata {
  // Type of the rack.
  enum RackType {
    // Unspecified rack type, single rack also belongs to this type.
    RACK_TYPE_UNSPECIFIED = 0;

    // Base rack type, a pair of two modified Config-1 racks containing
    // Aggregation switches.
    BASE = 1;

    // Expansion rack type, also known as standalone racks,
    // added by customers on demand.
    EXPANSION = 2;
  }

  // Quota for resources in this zone.
  repeated Quota quota = 1;

  // The map keyed by rack name and has value of RackType.
  map<string, RackType> rack_types = 2;

  // Config data for the zone.
  ConfigData config_data = 3;
}

// Config data holds all the config related data for the zone.
message ConfigData {
  // list of available v4 ip pools for external loadbalancer
  repeated string available_external_lb_pools_ipv4 = 1;

  // list of available v6 ip pools for external loadbalancer
  repeated string available_external_lb_pools_ipv6 = 2;
}

// Represents quota for Edge Container resources.
message Quota {
  // Name of the quota metric.
  string metric = 1;

  // Quota limit for this metric.
  double limit = 2;

  // Current usage of this metric.
  double usage = 3;
}

// Maintenance policy configuration.
message MaintenancePolicy {
  // Specifies the maintenance window in which maintenance may be performed.
  MaintenanceWindow window = 1;

  // Optional. Exclusions to automatic maintenance. Non-emergency maintenance
  // should not occur in these windows. Each exclusion has a unique name and may
  // be active or expired. The max number of maintenance exclusions allowed at a
  // given time is 3.
  repeated MaintenanceExclusionWindow maintenance_exclusions = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Maintenance window configuration
message MaintenanceWindow {
  // Configuration of a recurring maintenance window.
  RecurringTimeWindow recurring_window = 1;
}

// Represents an arbitrary window of time that recurs.
message RecurringTimeWindow {
  // The window of the first recurrence.
  TimeWindow window = 1;

  // An RRULE (https://tools.ietf.org/html/rfc5545#section-3.8.5.3) for how
  // this window recurs. They go on for the span of time between the start and
  // end time.
  string recurrence = 2;
}

// Represents a maintenance exclusion window.
message MaintenanceExclusionWindow {
  // Optional. The time window.
  TimeWindow window = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A unique (per cluster) id for the window.
  string id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents an arbitrary window of time.
message TimeWindow {
  // The time that the window first starts.
  google.protobuf.Timestamp start_time = 1;

  // The time that the window ends. The end time must take place after the
  // start time.
  google.protobuf.Timestamp end_time = 2;
}

// Server configuration for supported versions and release channels.
message ServerConfig {
  // Output only. Mapping from release channel to channel config.
  map<string, ChannelConfig> channels = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Supported versions, e.g.: ["1.4.0", "1.5.0"].
  repeated Version versions = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Default version, e.g.: "1.4.0".
  string default_version = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for a release channel.
message ChannelConfig {
  // Output only. Default version for this release channel, e.g.: "1.4.0".
  string default_version = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Version of a cluster.
message Version {
  // Output only. Name of the version, e.g.: "1.4.0".
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}
