// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.edgecontainer.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/edgecontainer/v1/resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.EdgeContainer.V1";
option go_package = "cloud.google.com/go/edgecontainer/apiv1/edgecontainerpb;edgecontainerpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.edgecontainer.v1";
option php_namespace = "Google\\Cloud\\EdgeContainer\\V1";
option ruby_package = "Google::Cloud::EdgeContainer::V1";

// EdgeContainer API provides management of Kubernetes Clusters on Google Edge
// Cloud deployments.
service EdgeContainer {
  option (google.api.default_host) = "edgecontainer.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Clusters in a given project and location.
  rpc ListClusters(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/clusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Cluster.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Cluster in a given project and location.
  rpc CreateCluster(CreateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/clusters"
      body: "cluster"
    };
    option (google.api.method_signature) = "parent,cluster,cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Cluster.
  rpc UpdateCluster(UpdateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{cluster.name=projects/*/locations/*/clusters/*}"
      body: "cluster"
    };
    option (google.api.method_signature) = "cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Upgrades a single cluster.
  rpc UpgradeCluster(UpgradeClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/clusters/*}:upgrade"
      body: "*"
    };
    option (google.api.method_signature) = "name,target_version,schedule";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Cluster.
  rpc DeleteCluster(DeleteClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Generates an access token for a Cluster.
  rpc GenerateAccessToken(GenerateAccessTokenRequest)
      returns (GenerateAccessTokenResponse) {
    option (google.api.http) = {
      get: "/v1/{cluster=projects/*/locations/*/clusters/*}:generateAccessToken"
    };
    option (google.api.method_signature) = "cluster";
  }

  // Generates an offline credential for a Cluster.
  rpc GenerateOfflineCredential(GenerateOfflineCredentialRequest)
      returns (GenerateOfflineCredentialResponse) {
    option (google.api.http) = {
      get: "/v1/{cluster=projects/*/locations/*/clusters/*}:generateOfflineCredential"
    };
    option (google.api.method_signature) = "cluster";
  }

  // Lists NodePools in a given project and location.
  rpc ListNodePools(ListNodePoolsRequest) returns (ListNodePoolsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/clusters/*}/nodePools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single NodePool.
  rpc GetNodePool(GetNodePoolRequest) returns (NodePool) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*/nodePools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new NodePool in a given project and location.
  rpc CreateNodePool(CreateNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}/nodePools"
      body: "node_pool"
    };
    option (google.api.method_signature) = "parent,node_pool,node_pool_id";
    option (google.longrunning.operation_info) = {
      response_type: "NodePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single NodePool.
  rpc UpdateNodePool(UpdateNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{node_pool.name=projects/*/locations/*/clusters/*/nodePools/*}"
      body: "node_pool"
    };
    option (google.api.method_signature) = "node_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "NodePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single NodePool.
  rpc DeleteNodePool(DeleteNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clusters/*/nodePools/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Machines in a given project and location.
  rpc ListMachines(ListMachinesRequest) returns (ListMachinesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/machines"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Machine.
  rpc GetMachine(GetMachineRequest) returns (Machine) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/machines/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists VPN connections in a given project and location.
  rpc ListVpnConnections(ListVpnConnectionsRequest)
      returns (ListVpnConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/vpnConnections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single VPN connection.
  rpc GetVpnConnection(GetVpnConnectionRequest) returns (VpnConnection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/vpnConnections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new VPN connection in a given project and location.
  rpc CreateVpnConnection(CreateVpnConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/vpnConnections"
      body: "vpn_connection"
    };
    option (google.api.method_signature) =
        "parent,vpn_connection,vpn_connection_id";
    option (google.longrunning.operation_info) = {
      response_type: "VpnConnection"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single VPN connection.
  rpc DeleteVpnConnection(DeleteVpnConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/vpnConnections/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets the server config.
  rpc GetServerConfig(GetServerConfigRequest) returns (ServerConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*}/serverConfig"
    };
    option (google.api.method_signature) = "name";
  }
}

// Long-running operation metadata for Edge Container API methods.
message OperationMetadata {
  // Indicates the reason for the status of the operation.
  enum StatusReason {
    // Reason unknown.
    STATUS_REASON_UNSPECIFIED = 0;

    // The cluster upgrade is currently paused.
    UPGRADE_PAUSED = 1;
  }

  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // The verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_message = 5;

  // Identifies whether the user has requested cancellation of the operation.
  // Operations that have successfully been cancelled have [Operation.error][]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6;

  // API version used to start the operation.
  string api_version = 7;

  // Warnings that do not block the operation, but still hold relevant
  // information for the end user to receive.
  repeated string warnings = 8;

  // Machine-readable status of the operation, if any.
  StatusReason status_reason = 9;
}

// Lists clusters in a location.
message ListClustersRequest {
  // Required. The parent location, which owns this collection of clusters.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgecontainer.googleapis.com/Cluster"
    }
  ];

  // The maximum number of resources to list.
  int32 page_size = 2;

  // A page token received from previous list request.
  // A page token received from previous list request.
  string page_token = 3;

  // Only resources matching this filter will be listed.
  string filter = 4;

  // Specifies the order in which resources will be listed.
  string order_by = 5;
}

// List of clusters in a location.
message ListClustersResponse {
  // Clusters in the location.
  repeated Cluster clusters = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Gets a cluster.
message GetClusterRequest {
  // Required. The resource name of the cluster.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/Cluster"
    }
  ];
}

// Creates a cluster.
message CreateClusterRequest {
  // Required. The parent location where this cluster will be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgecontainer.googleapis.com/Cluster"
    }
  ];

  // Required. A client-specified unique identifier for the cluster.
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The cluster to create.
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // A unique identifier for this request. Restricted to 36 ASCII characters. A
  // random UUID is recommended. This request is only idempotent if
  // `request_id` is provided.
  string request_id = 4;
}

// Updates a cluster.
message UpdateClusterRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // Cluster resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // The updated cluster.
  Cluster cluster = 2;

  // A unique identifier for this request. Restricted to 36 ASCII characters.
  // A random UUID is recommended.
  // This request is only idempotent if `request_id` is provided.
  string request_id = 3;
}

// Upgrades a cluster.
message UpgradeClusterRequest {
  // Represents the schedule about when the cluster is going to be upgraded.
  enum Schedule {
    // Unspecified. The default is to upgrade the cluster immediately which is
    // the only option today.
    SCHEDULE_UNSPECIFIED = 0;

    // The cluster is going to be upgraded immediately after receiving the
    // request.
    IMMEDIATELY = 1;
  }

  // Required. The resource name of the cluster.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/Cluster"
    }
  ];

  // Required. The version the cluster is going to be upgraded to.
  string target_version = 2 [(google.api.field_behavior) = REQUIRED];

  // The schedule for the upgrade.
  Schedule schedule = 3;

  // A unique identifier for this request. Restricted to 36 ASCII characters. A
  // random UUID is recommended. This request is only idempotent if
  // `request_id` is provided.
  string request_id = 4;
}

// Deletes a cluster.
message DeleteClusterRequest {
  // Required. The resource name of the cluster.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/Cluster"
    }
  ];

  // A unique identifier for this request. Restricted to 36 ASCII characters. A
  // random UUID is recommended. This request is only idempotent if
  // `request_id` is provided.
  string request_id = 2;
}

// Generates an access token for a cluster.
message GenerateAccessTokenRequest {
  // Required. The resource name of the cluster.
  string cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/Cluster"
    }
  ];
}

// An access token for a cluster.
message GenerateAccessTokenResponse {
  // Output only. Access token to authenticate to k8s api-server.
  string access_token = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp at which the token will expire.
  google.protobuf.Timestamp expire_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Generates an offline credential(offline) for a cluster.
message GenerateOfflineCredentialRequest {
  // Required. The resource name of the cluster.
  string cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/Cluster"
    }
  ];
}

// An offline credential for a cluster.
message GenerateOfflineCredentialResponse {
  // Output only. Client certificate to authenticate to k8s api-server.
  string client_certificate = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Client private key to authenticate to k8s api-server.
  string client_key = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Client's identity.
  string user_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp at which this credential will expire.
  google.protobuf.Timestamp expire_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Lists node pools in a cluster.
message ListNodePoolsRequest {
  // Required. The parent cluster, which owns this collection of node pools.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgecontainer.googleapis.com/NodePool"
    }
  ];

  // The maximum number of resources to list.
  int32 page_size = 2;

  // A page token received from previous list request.
  string page_token = 3;

  // Only resources matching this filter will be listed.
  string filter = 4;

  // Specifies the order in which resources will be listed.
  string order_by = 5;
}

// List of node pools in a cluster.
message ListNodePoolsResponse {
  // Node pools in the cluster.
  repeated NodePool node_pools = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Gets a node pool.
message GetNodePoolRequest {
  // Required. The resource name of the node pool.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/NodePool"
    }
  ];
}

// Creates a node pool.
message CreateNodePoolRequest {
  // Required. The parent cluster where this node pool will be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgecontainer.googleapis.com/NodePool"
    }
  ];

  // Required. A client-specified unique identifier for the node pool.
  string node_pool_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The node pool to create.
  NodePool node_pool = 3 [(google.api.field_behavior) = REQUIRED];

  // A unique identifier for this request. Restricted to 36 ASCII characters. A
  // random UUID is recommended. This request is only idempotent if
  // `request_id` is provided.
  string request_id = 4;
}

// Updates a node pool.
message UpdateNodePoolRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // NodePool resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // The updated node pool.
  NodePool node_pool = 2;

  // A unique identifier for this request. Restricted to 36 ASCII characters. A
  // random UUID is recommended. This request is only idempotent if
  // `request_id` is provided.
  string request_id = 3;
}

// Deletes a node pool.
message DeleteNodePoolRequest {
  // Required. The resource name of the node pool.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/NodePool"
    }
  ];

  // A unique identifier for this request. Restricted to 36 ASCII characters. A
  // random UUID is recommended. This request is only idempotent if
  // `request_id` is provided.
  string request_id = 2;
}

// Lists machines in a site.
message ListMachinesRequest {
  // Required. The parent site, which owns this collection of machines.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgecontainer.googleapis.com/Machine"
    }
  ];

  // The maximum number of resources to list.
  int32 page_size = 2;

  // A page token received from previous list request.
  string page_token = 3;

  // Only resources matching this filter will be listed.
  string filter = 4;

  // Specifies the order in which resources will be listed.
  string order_by = 5;
}

// List of machines in a site.
message ListMachinesResponse {
  // Machines in the site.
  repeated Machine machines = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Gets a machine.
message GetMachineRequest {
  // Required. The resource name of the machine.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/Machine"
    }
  ];
}

// Lists VPN connections.
message ListVpnConnectionsRequest {
  // Required. The parent location, which owns this collection of VPN
  // connections.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgecontainer.googleapis.com/VpnConnection"
    }
  ];

  // The maximum number of resources to list.
  int32 page_size = 2;

  // A page token received from previous list request.
  string page_token = 3;

  // Only resources matching this filter will be listed.
  string filter = 4;

  // Specifies the order in which resources will be listed.
  string order_by = 5;
}

// List of VPN connections in a location.
message ListVpnConnectionsResponse {
  // VpnConnections in the location.
  repeated VpnConnection vpn_connections = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Gets a VPN connection.
message GetVpnConnectionRequest {
  // Required. The resource name of the vpn connection.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/VpnConnection"
    }
  ];
}

// Creates a VPN connection.
message CreateVpnConnectionRequest {
  // Required. The parent location where this vpn connection will be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgecontainer.googleapis.com/VpnConnection"
    }
  ];

  // Required. The VPN connection identifier.
  string vpn_connection_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The VPN connection to create.
  VpnConnection vpn_connection = 3 [(google.api.field_behavior) = REQUIRED];

  // A unique identifier for this request. Restricted to 36 ASCII characters. A
  // random UUID is recommended. This request is only idempotent if
  // `request_id` is provided.
  string request_id = 4;
}

// Deletes a vpn connection.
message DeleteVpnConnectionRequest {
  // Required. The resource name of the vpn connection.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgecontainer.googleapis.com/VpnConnection"
    }
  ];

  // A unique identifier for this request. Restricted to 36 ASCII characters. A
  // random UUID is recommended. This request is only idempotent if
  // `request_id` is provided.
  string request_id = 2;
}

// Gets the server config.
message GetServerConfigRequest {
  // Required. The name (project and location) of the server config to get,
  // specified in the format `projects/*/locations/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}
