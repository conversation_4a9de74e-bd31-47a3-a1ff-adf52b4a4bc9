{"methodConfig": [{"name": [{"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "ListClusters"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "GetCluster"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "ListNodePools"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "GetNodePool"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "ListVpnConnections"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "GetVpnConnection"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "ListMachines"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "GetMachine"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "GenerateAccessToken"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "GenerateOfflineCredential"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "GetServerConfig"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "CreateCluster"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "UpdateCluster"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "DeleteCluster"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "CreateNodePool"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "UpdateNodePool"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "DeleteNodePool"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "CreateVpnConnection"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "DeleteVpnConnection"}, {"service": "google.cloud.edgecontainer.v1.EdgeContainer", "method": "UpgradeCluster"}], "timeout": "60s"}]}