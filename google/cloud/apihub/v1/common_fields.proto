// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apihub.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ApiHub.V1";
option go_package = "cloud.google.com/go/apihub/apiv1/apihubpb;apihubpb";
option java_multiple_files = true;
option java_outer_classname = "CommonFieldsProto";
option java_package = "com.google.cloud.apihub.v1";
option php_namespace = "Google\\Cloud\\ApiHub\\V1";
option ruby_package = "Google::Cloud::ApiHub::V1";
option (google.api.resource_definition) = {
  type: "aiplatform.googleapis.com/Extension"
  pattern: "projects/{project}/locations/{location}/extensions/{extension}"
};

// Lint state represents success or failure for linting.
enum LintState {
  // Lint state unspecified.
  LINT_STATE_UNSPECIFIED = 0;

  // Linting was completed successfully.
  LINT_STATE_SUCCESS = 1;

  // Linting encountered errors.
  LINT_STATE_ERROR = 2;
}

// Enumeration of linter types.
enum Linter {
  // Linter type unspecified.
  LINTER_UNSPECIFIED = 0;

  // Linter type spectral.
  SPECTRAL = 1;

  // Linter type other.
  OTHER = 2;
}

// Severity of the issue.
enum Severity {
  // Severity unspecified.
  SEVERITY_UNSPECIFIED = 0;

  // Severity error.
  SEVERITY_ERROR = 1;

  // Severity warning.
  SEVERITY_WARNING = 2;

  // Severity info.
  SEVERITY_INFO = 3;

  // Severity hint.
  SEVERITY_HINT = 4;
}

// An API resource in the API Hub.
message Api {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/Api"
    pattern: "projects/{project}/locations/{location}/apis/{api}"
    plural: "apis"
    singular: "api"
  };

  // Identifier. The name of the API resource in the API Hub.
  //
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The display name of the API resource.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the API resource.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The documentation for the API resource.
  Documentation documentation = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Owner details for the API resource.
  Owner owner = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The list of versions present in an API resource.
  // Note: An API resource can be associated with more than 1 version.
  // Format is
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  repeated string versions = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Version" }
  ];

  // Output only. The time at which the API resource was created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the API resource was last updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The target users for the API.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-target-user`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues target_user = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The team owning the API.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-team`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues team = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The business unit owning the API.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-business-unit`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues business_unit = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maturity level of the API.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-maturity-level`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues maturity_level = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of user defined attributes associated with the API
  // resource. The key is the attribute name. It will be of the format:
  // `projects/{project}/locations/{location}/attributes/{attribute}`.
  // The value is the attribute values associated with the resource.
  map<string, AttributeValues> attributes = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The style of the API.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-api-style`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues api_style = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The selected version for an API resource.
  // This can be used when special handling is needed on client side for
  // particular version of the API. Format is
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  string selected_version = 15 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Version" }
  ];
}

// Represents a version of the API resource in API hub. This is also referred
// to as the API version.
message Version {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/Version"
    pattern: "projects/{project}/locations/{location}/apis/{api}/versions/{version}"
    plural: "versions"
    singular: "version"
  };

  // Identifier. The name of the version.
  //
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The display name of the version.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the version.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The documentation of the version.
  Documentation documentation = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The specs associated with this version.
  // Note that an API version can be associated with multiple specs.
  // Format is
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  repeated string specs = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Spec" }
  ];

  // Output only. The operations contained in the API version.
  // These operations will be added to the version when a new spec is
  // added or when an existing spec is updated. Format is
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`
  repeated string api_operations = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/ApiOperation"
    }
  ];

  // Output only. The definitions contained in the API version.
  // These definitions will be added to the version when a new spec is
  // added or when an existing spec is updated. Format is
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/definitions/{definition}`
  repeated string definitions = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Definition"
    }
  ];

  // Optional. The deployments linked to this API version.
  // Note: A particular API version could be deployed to multiple deployments
  // (for dev deployment, UAT deployment, etc)
  // Format is
  // `projects/{project}/locations/{location}/deployments/{deployment}`
  repeated string deployments = 8 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Deployment"
    }
  ];

  // Output only. The time at which the version was created.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the version was last updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The lifecycle of the API version.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-lifecycle`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues lifecycle = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The compliance associated with the API version.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-compliance`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues compliance = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The accreditations associated with the API version.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-accreditation`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues accreditation = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of user defined attributes associated with the Version
  // resource. The key is the attribute name. It will be of the format:
  // `projects/{project}/locations/{location}/attributes/{attribute}`.
  // The value is the attribute values associated with the resource.
  map<string, AttributeValues> attributes = 14
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The selected deployment for a Version resource.
  // This can be used when special handling is needed on client side for a
  // particular deployment linked to the version.
  // Format is
  // `projects/{project}/locations/{location}/deployments/{deployment}`
  string selected_deployment = 16 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Deployment"
    }
  ];
}

// Represents a spec associated with an API version in the API
// Hub. Note that specs of various types can be uploaded, however
// parsing of details is supported for OpenAPI spec currently.
message Spec {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/Spec"
    pattern: "projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}"
    plural: "specs"
    singular: "spec"
  };

  // Specifies the parsing mode for API specifications during creation and
  // update.
  // - `RELAXED`: Parsing errors in the specification content do not fail the
  // API call.
  // - `STRICT`: Parsing errors in the specification content result in failure
  // of the API call.
  // If not specified, defaults to `RELAXED`.
  enum ParsingMode {
    // Defaults to `RELAXED`.
    PARSING_MODE_UNSPECIFIED = 0;

    // Parsing of the Spec on create and update is relaxed, meaning that
    // parsing errors the spec contents will not fail the API call.
    RELAXED = 1;

    // Parsing of the Spec on create and update is strict, meaning that
    // parsing errors in the spec contents will fail the API call.
    STRICT = 2;
  }

  // Identifier. The name of the spec.
  //
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The display name of the spec.
  // This can contain the file name of the spec.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of spec.
  // The value should be one of the allowed values defined for
  // `projects/{project}/locations/{location}/attributes/system-spec-type`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API.
  //
  // Note, this field is mandatory if content is provided.
  AttributeValues spec_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Input only. The contents of the uploaded spec.
  SpecContents contents = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Output only. Details parsed from the spec.
  SpecDetails details = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The URI of the spec source in case file is uploaded
  // from an external version control system.
  string source_uri = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time at which the spec was created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the spec was last updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The lint response for the spec.
  LintResponse lint_response = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of user defined attributes associated with the spec.
  // The key is the attribute name. It will be of the format:
  // `projects/{project}/locations/{location}/attributes/{attribute}`.
  // The value is the attribute values associated with the resource.
  map<string, AttributeValues> attributes = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The documentation of the spec.
  // For OpenAPI spec, this will be populated from `externalDocs` in OpenAPI
  // spec.
  Documentation documentation = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Input only. Enum specifying the parsing mode for OpenAPI
  // Specification (OAS) parsing.
  ParsingMode parsing_mode = 12 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = INPUT_ONLY
  ];
}

// Details of the deployment where APIs are hosted.
// A deployment could represent an Apigee proxy, API gateway, other Google Cloud
// services or non-Google Cloud services as well. A deployment entity is a root
// level entity in the API hub and exists independent of any API.
message Deployment {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/Deployment"
    pattern: "projects/{project}/locations/{location}/deployments/{deployment}"
    plural: "deployments"
    singular: "deployment"
  };

  // Identifier. The name of the deployment.
  //
  // Format:
  // `projects/{project}/locations/{location}/deployments/{deployment}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The display name of the deployment.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the deployment.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The documentation of the deployment.
  Documentation documentation = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The type of deployment.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-deployment-type`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues deployment_type = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. A URI to the runtime resource. This URI can be used to manage the
  // resource. For example, if the runtime resource is of type APIGEE_PROXY,
  // then this field will contain the URI to the management UI of the proxy.
  string resource_uri = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. The endpoints at which this deployment resource is listening for
  // API requests. This could be a list of complete URIs, hostnames or an IP
  // addresses.
  repeated string endpoints = 7 [(google.api.field_behavior) = REQUIRED];

  // Output only. The API versions linked to this deployment.
  // Note: A particular deployment could be linked to multiple different API
  // versions (of same or different APIs).
  repeated string api_versions = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the deployment was created.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the deployment was last updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The SLO for this deployment.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-slo`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues slo = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The environment mapping to this deployment.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-environment`
  // attribute.
  // The number of values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues environment = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of user defined attributes associated with the
  // deployment resource. The key is the attribute name. It will be of the
  // format: `projects/{project}/locations/{location}/attributes/{attribute}`.
  // The value is the attribute values associated with the resource.
  map<string, AttributeValues> attributes = 13
      [(google.api.field_behavior) = OPTIONAL];
}

// Represents an operation contained in an API version in the API Hub.
// An operation is added/updated/deleted in an API version when a new spec is
// added or an existing spec is updated/deleted in a version.
// Currently, an operation will be created only corresponding to OpenAPI spec as
// parsing is supported for OpenAPI spec.
message ApiOperation {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/ApiOperation"
    pattern: "projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}"
    plural: "apiOperations"
    singular: "apiOperation"
  };

  // Identifier. The name of the operation.
  //
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The name of the spec from where the operation was parsed.
  // Format is
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  string spec = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Spec" }
  ];

  // Output only. Operation details.
  OperationDetails details = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the operation was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the operation was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The list of user defined attributes associated with the API
  // operation resource. The key is the attribute name. It will be of the
  // format: `projects/{project}/locations/{location}/attributes/{attribute}`.
  // The value is the attribute values associated with the resource.
  map<string, AttributeValues> attributes = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Represents a definition for example schema, request, response definitions
// contained in an API version.
// A definition is added/updated/deleted in an API version when a new spec is
// added or an existing spec is updated/deleted in a version. Currently,
// definition will be created only corresponding to OpenAPI spec as parsing is
// supported for OpenAPI spec. Also, within OpenAPI spec, only `schema` object
// is supported.
message Definition {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/Definition"
    pattern: "projects/{project}/locations/{location}/apis/{api}/versions/{version}/definitions/{definition}"
    plural: "definitions"
    singular: "definition"
  };

  // Enumeration of definition types.
  enum Type {
    // Definition type unspecified.
    TYPE_UNSPECIFIED = 0;

    // Definition type schema.
    SCHEMA = 1;
  }

  oneof value {
    // Output only. The value of a schema definition.
    Schema schema = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Identifier. The name of the definition.
  //
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/definitions/{definition}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The name of the spec from where the definition was parsed.
  // Format is
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  string spec = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Spec" }
  ];

  // Output only. The type of the definition.
  Type type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the definition was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the definition was last updated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The list of user defined attributes associated with the
  // definition resource. The key is the attribute name. It will be of the
  // format: `projects/{project}/locations/{location}/attributes/{attribute}`.
  // The value is the attribute values associated with the resource.
  map<string, AttributeValues> attributes = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// An attribute in the API Hub.
// An attribute is a name value pair which can be attached to different
// resources in the API hub based on the scope of the attribute. Attributes can
// either be pre-defined by the API Hub or created by users.
message Attribute {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/Attribute"
    pattern: "projects/{project}/locations/{location}/attributes/{attribute}"
    plural: "attributes"
    singular: "attribute"
  };

  // The value that can be assigned to the attribute when the data type is
  // enum.
  message AllowedValue {
    // Required. The ID of the allowed value.
    // * If provided, the same will be used. The service will throw an error if
    // the specified id is already used by another allowed value in the same
    // attribute resource.
    // * If not provided, a system generated id derived from the display name
    // will be used. In this case, the service will handle conflict resolution
    // by adding a system generated suffix in case of duplicates.
    //
    // This value should be 4-63 characters, and valid characters
    // are /[a-z][0-9]-/.
    string id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The display name of the allowed value.
    string display_name = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. The detailed description of the allowed value.
    string description = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. When set to true, the allowed value cannot be updated or
    // deleted by the user. It can only be true for System defined attributes.
    bool immutable = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Enumeration of attribute definition types.
  enum DefinitionType {
    // Attribute definition type unspecified.
    DEFINITION_TYPE_UNSPECIFIED = 0;

    // The attribute is predefined by the API Hub. Note that only the list of
    // allowed values can be updated in this case via UpdateAttribute method.
    SYSTEM_DEFINED = 1;

    // The attribute is defined by the user.
    USER_DEFINED = 2;
  }

  // Enumeration for the scope of the attribute representing the resource in the
  // API Hub to which the attribute can be linked.
  enum Scope {
    // Scope Unspecified.
    SCOPE_UNSPECIFIED = 0;

    // Attribute can be linked to an API.
    API = 1;

    // Attribute can be linked to an API version.
    VERSION = 2;

    // Attribute can be linked to a Spec.
    SPEC = 3;

    // Attribute can be linked to an API Operation.
    API_OPERATION = 4;

    // Attribute can be linked to a Deployment.
    DEPLOYMENT = 5;

    // Attribute can be linked to a Dependency.
    DEPENDENCY = 6;

    // Attribute can be linked to a definition.
    DEFINITION = 7;

    // Attribute can be linked to a ExternalAPI.
    EXTERNAL_API = 8;

    // Attribute can be linked to a Plugin.
    PLUGIN = 9;
  }

  // Enumeration of attribute's data type.
  enum DataType {
    // Attribute data type unspecified.
    DATA_TYPE_UNSPECIFIED = 0;

    // Attribute's value is of type enum.
    ENUM = 1;

    // Attribute's value is of type json.
    JSON = 2;

    // Attribute's value is of type string.
    STRING = 3;
  }

  // Identifier. The name of the attribute in the API Hub.
  //
  // Format:
  // `projects/{project}/locations/{location}/attributes/{attribute}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The display name of the attribute.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the attribute.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The definition type of the attribute.
  DefinitionType definition_type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The scope of the attribute. It represents the resource in the API
  // Hub to which the attribute can be linked.
  Scope scope = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the data of the attribute.
  DataType data_type = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. The list of allowed values when the attribute value is of type
  // enum. This is required when the data_type of the attribute is ENUM. The
  // maximum number of allowed values of an attribute will be 1000.
  repeated AllowedValue allowed_values = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of values that the attribute can have when
  // associated with an API Hub resource. Cardinality 1 would represent a
  // single-valued attribute. It must not be less than 1 or greater than 20. If
  // not specified, the cardinality would be set to 1 by default and represent a
  // single-valued attribute.
  int32 cardinality = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. When mandatory is true, the attribute is mandatory for the
  // resource specified in the scope. Only System defined attributes can be
  // mandatory.
  bool mandatory = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the attribute was created.
  google.protobuf.Timestamp create_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the attribute was last updated.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The spec contents.
message SpecContents {
  // Required. The contents of the spec.
  bytes contents = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The mime type of the content for example application/json,
  // application/yaml, application/wsdl etc.
  string mime_type = 2 [(google.api.field_behavior) = REQUIRED];
}

// SpecDetails contains the details parsed from supported
// spec types.
message SpecDetails {
  oneof details {
    // Output only. Additional details apart from `OperationDetails` parsed from
    // an OpenAPI spec. The OperationDetails parsed from the spec can be
    // obtained by using
    // [ListAPIOperations][google.cloud.apihub.v1.ApiHub.ListApiOperations]
    // method.
    OpenApiSpecDetails open_api_spec_details = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The description of the spec.
  string description = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// OpenApiSpecDetails contains the details parsed from an OpenAPI spec in
// addition to the fields mentioned in
// [SpecDetails][google.cloud.apihub.v1.SpecDetails].
message OpenApiSpecDetails {
  // Enumeration of spec formats.
  enum Format {
    // SpecFile type unspecified.
    FORMAT_UNSPECIFIED = 0;

    // OpenAPI Spec v2.0.
    OPEN_API_SPEC_2_0 = 1;

    // OpenAPI Spec v3.0.
    OPEN_API_SPEC_3_0 = 2;

    // OpenAPI Spec v3.1.
    OPEN_API_SPEC_3_1 = 3;
  }

  // Output only. The format of the spec.
  Format format = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The version in the spec.
  // This maps to `info.version` in OpenAPI spec.
  string version = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Owner details for the spec.
  // This maps to `info.contact` in OpenAPI spec.
  Owner owner = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The operation details parsed from the spec.
message OperationDetails {
  oneof operation {
    // The HTTP Operation.
    HttpOperation http_operation = 4;
  }

  // Output only. Description of the operation behavior.
  // For OpenAPI spec, this will map to `operation.description` in the
  // spec, in case description is empty, `operation.summary` will be used.
  string description = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional external documentation for this operation.
  // For OpenAPI spec, this will map to `operation.documentation` in the spec.
  Documentation documentation = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For OpenAPI spec, this will be set if `operation.deprecated`is
  // marked as `true` in the spec.
  bool deprecated = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The HTTP Operation.
message HttpOperation {
  // Enumeration of Method types.
  enum Method {
    // Method unspecified.
    METHOD_UNSPECIFIED = 0;

    // Get Operation type.
    GET = 1;

    // Put Operation type.
    PUT = 2;

    // Post Operation type.
    POST = 3;

    // Delete Operation type.
    DELETE = 4;

    // Options Operation type.
    OPTIONS = 5;

    // Head Operation type.
    HEAD = 6;

    // Patch Operation type.
    PATCH = 7;

    // Trace Operation type.
    TRACE = 8;
  }

  // Output only. The path details for the Operation.
  Path path = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Operation method
  Method method = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The path details derived from the spec.
message Path {
  // Output only. Complete path relative to server endpoint.
  string path = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A short description for the path applicable to all operations.
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The schema details derived from the spec. Currently, this entity is supported
// for OpenAPI spec only.
// For OpenAPI spec, this maps to the schema defined in
// the  `definitions` section for OpenAPI 2.0 version and in
// `components.schemas` section for OpenAPI 3.0 and 3.1 version.
message Schema {
  // Output only. The display name of the schema.
  // This will map to the name of the schema in the spec.
  string display_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The raw value of the schema definition corresponding to the
  // schema name in the spec.
  bytes raw_value = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Owner details.
message Owner {
  // Optional. The name of the owner.
  string display_name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The email of the owner.
  string email = 2 [(google.api.field_behavior) = REQUIRED];
}

// Documentation details.
message Documentation {
  // Optional. The uri of the externally hosted documentation.
  string external_uri = 1 [(google.api.field_behavior) = OPTIONAL];
}

// The attribute values associated with resource.
message AttributeValues {
  // The attribute values of data type enum.
  message EnumAttributeValues {
    // Required. The attribute values in case attribute data type is enum.
    repeated Attribute.AllowedValue values = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // The attribute values of data type string or JSON.
  message StringAttributeValues {
    // Required. The attribute values in case attribute data type is string or
    // JSON.
    repeated string values = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // The attribute values associated with the resource.
  oneof Value {
    // The attribute values associated with a resource in case attribute data
    // type is enum.
    EnumAttributeValues enum_values = 2;

    // The attribute values associated with a resource in case attribute data
    // type is string.
    StringAttributeValues string_values = 3;

    // The attribute values associated with a resource in case attribute data
    // type is JSON.
    StringAttributeValues json_values = 4;
  }

  // Output only. The name of the attribute.
  // Format: projects/{project}/locations/{location}/attributes/{attribute}
  string attribute = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Attribute"
    }
  ];
}

// A dependency resource defined in the API hub describes a dependency directed
// from a consumer to a supplier entity. A dependency can be defined between two
// [Operations][google.cloud.apihub.v1.Operation] or between
// an [Operation][google.cloud.apihub.v1.Operation] and [External
// API][google.cloud.apihub.v1.ExternalApi].
message Dependency {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/Dependency"
    pattern: "projects/{project}/locations/{location}/dependencies/{dependency}"
    plural: "dependencies"
    singular: "dependency"
  };

  // Possible states for a dependency.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // Dependency will be in a proposed state when it is newly identified by the
    // API hub on its own.
    PROPOSED = 1;

    // Dependency will be in a validated state when it is validated by the
    // admin or manually created in the API hub.
    VALIDATED = 2;
  }

  // Possible modes of discovering the dependency.
  enum DiscoveryMode {
    // Default value. This value is unused.
    DISCOVERY_MODE_UNSPECIFIED = 0;

    // Manual mode of discovery when the dependency is defined by the user.
    MANUAL = 1;
  }

  // Identifier. The name of the dependency in the API Hub.
  //
  // Format: `projects/{project}/locations/{location}/dependencies/{dependency}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Immutable. The entity acting as the consumer in the dependency.
  DependencyEntityReference consumer = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. Immutable. The entity acting as the supplier in the dependency.
  DependencyEntityReference supplier = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // Output only. State of the dependency.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Human readable description corresponding of the dependency.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Discovery mode of the dependency.
  DiscoveryMode discovery_mode = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Error details of a dependency if the system has detected it
  // internally.
  DependencyErrorDetail error_detail = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the dependency was created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the dependency was last updated.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The list of user defined attributes associated with the
  // dependency resource. The key is the attribute name. It will be of the
  // format: `projects/{project}/locations/{location}/attributes/{attribute}`.
  // The value is the attribute values associated with the resource.
  map<string, AttributeValues> attributes = 10
      [(google.api.field_behavior) = OPTIONAL];
}

// Reference to an entity participating in a dependency.
message DependencyEntityReference {
  // Required. Unique identifier for the participating entity.
  oneof identifier {
    // The resource name of an operation in the API Hub.
    //
    // Format:
    // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`
    string operation_resource_name = 2;

    // The resource name of an external API in the API Hub.
    //
    // Format:
    // `projects/{project}/locations/{location}/externalApis/{external_api}`
    string external_api_resource_name = 3;
  }

  // Output only. Display name of the entity.
  string display_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Details describing error condition of a dependency.
message DependencyErrorDetail {
  // Possible values representing an error in the dependency.
  enum Error {
    // Default value used for no error in the dependency.
    ERROR_UNSPECIFIED = 0;

    // Supplier entity has been deleted.
    SUPPLIER_NOT_FOUND = 1;

    // Supplier entity has been recreated.
    SUPPLIER_RECREATED = 2;
  }

  // Optional. Error in the dependency.
  Error error = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Timestamp at which the error was found.
  google.protobuf.Timestamp error_time = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// LintResponse contains the response from the linter.
message LintResponse {
  // Count of issues with a given severity.
  message SummaryEntry {
    // Required. Severity of the issue.
    Severity severity = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Count of issues with the given severity.
    int32 count = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Optional. Array of issues found in the analyzed document.
  repeated Issue issues = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Summary of all issue types and counts for each severity level.
  repeated SummaryEntry summary = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Lint state represents success or failure for linting.
  LintState state = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the linting application.
  string source = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the linter used.
  Linter linter = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Timestamp when the linting response was generated.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = REQUIRED];
}

// Issue contains the details of a single issue found by the linter.
message Issue {
  // Required. Rule code unique to each rule defined in linter.
  string code = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. An array of strings indicating the location in the analyzed
  // document where the rule was triggered.
  repeated string path = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Human-readable message describing the issue found by the linter.
  string message = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Severity level of the rule violation.
  Severity severity = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. Object describing where in the file the issue was found.
  Range range = 5 [(google.api.field_behavior) = REQUIRED];
}

// Object describing where in the file the issue was found.
message Range {
  // Required. Start of the issue.
  Point start = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. End of the issue.
  Point end = 2 [(google.api.field_behavior) = REQUIRED];
}

// Point within the file (line and character).
message Point {
  // Required. Line number (zero-indexed).
  int32 line = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Character position within the line (zero-indexed).
  int32 character = 2 [(google.api.field_behavior) = REQUIRED];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An ApiHubInstance represents the instance resources of the API Hub.
// Currently, only one ApiHub instance is allowed for each project.
message ApiHubInstance {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/ApiHubInstance"
    pattern: "projects/{project}/locations/{location}/apiHubInstances/{api_hub_instance}"
    plural: "apiHubInstances"
    singular: "apiHubInstance"
  };

  // Available configurations to provision an ApiHub Instance.
  message Config {
    // Required. The Customer Managed Encryption Key (CMEK) used for data
    // encryption. The CMEK name should follow the format of
    // `projects/([^/]+)/locations/([^/]+)/keyRings/([^/]+)/cryptoKeys/([^/]+)`,
    // where the location must match the instance location.
    string cmek_key_name = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // State of the ApiHub Instance.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The ApiHub instance has not been initialized or has been deleted.
    INACTIVE = 1;

    // The ApiHub instance is being created.
    CREATING = 2;

    // The ApiHub instance has been created and is ready for use.
    ACTIVE = 3;

    // The ApiHub instance is being updated.
    UPDATING = 4;

    // The ApiHub instance is being deleted.
    DELETING = 5;

    // The ApiHub instance encountered an error during a state change.
    FAILED = 6;
  }

  // Identifier. Format:
  // `projects/{project}/locations/{location}/apiHubInstances/{apiHubInstance}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Creation timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the ApiHub instance.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Extra information about ApiHub instance state. Currently the
  // message would be populated when state is `FAILED`.
  string state_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Config of the ApiHub instance.
  Config config = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Instance labels to represent user-provided metadata.
  // Refer to cloud documentation on labels for more details.
  // https://cloud.google.com/compute/docs/labeling-resources
  map<string, string> labels = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the ApiHub instance.
  string description = 8 [(google.api.field_behavior) = OPTIONAL];
}

// An external API represents an API being provided by external sources. This
// can be used to model third-party APIs and can be used to define dependencies.
message ExternalApi {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/ExternalApi"
    pattern: "projects/{project}/locations/{location}/externalApis/{external_api}"
    plural: "externalApis"
    singular: "externalApi"
  };

  // Identifier. Format:
  // `projects/{project}/locations/{location}/externalApi/{externalApi}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Display name of the external API. Max length is 63 characters
  // (Unicode Code Points).
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description of the external API. Max length is 2000 characters
  // (Unicode Code Points).
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List of endpoints on which this API is accessible.
  repeated string endpoints = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List of paths served by this API.
  repeated string paths = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Documentation of the external API.
  Documentation documentation = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of user defined attributes associated with the Version
  // resource. The key is the attribute name. It will be of the format:
  // `projects/{project}/locations/{location}/attributes/{attribute}`.
  // The value is the attribute values associated with the resource.
  map<string, AttributeValues> attributes = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Creation timestamp.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
