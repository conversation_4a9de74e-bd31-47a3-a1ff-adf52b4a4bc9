type: google.api.Service
config_version: 3
name: apihub.googleapis.com
title: API hub API

apis:
- name: google.cloud.apihub.v1.ApiHub
- name: google.cloud.apihub.v1.ApiHubDependencies
- name: google.cloud.apihub.v1.ApiHubPlugin
- name: google.cloud.apihub.v1.HostProjectRegistrationService
- name: google.cloud.apihub.v1.LintingService
- name: google.cloud.apihub.v1.Provisioning
- name: google.cloud.apihub.v1.RuntimeProjectAttachmentService
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.apihub.v1.OperationMetadata

documentation:
  overview: |-
    API hub lets you consolidate and organize information about all of the APIs
    of interest to your organization. API hub lets you capture critical
    information about APIs that allows developers to discover and evaluate
    them easily and leverage the work of other teams wherever possible. API
    platform teams can use API hub to have visibility into and manage their
    portfolio of APIs.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.apihub.v1.ApiHub.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.apihub.v1.ApiHubDependencies.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.apihub.v1.ApiHubPlugin.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.apihub.v1.HostProjectRegistrationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.apihub.v1.LintingService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.apihub.v1.Provisioning.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.apihub.v1.RuntimeProjectAttachmentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1447560
  documentation_uri: https://cloud.google.com/apigee/docs/apihub/what-is-api-hub
  api_short_name: apihub
  github_label: 'api: apihub'
  doc_tag_prefix: apihub
  organization: CLOUD
  library_settings:
  - version: google.cloud.apihub.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/apigee/docs/reference/apis/apihub/rpc
