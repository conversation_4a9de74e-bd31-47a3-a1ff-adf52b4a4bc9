// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apihub.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ApiHub.V1";
option go_package = "cloud.google.com/go/apihub/apiv1/apihubpb;apihubpb";
option java_multiple_files = true;
option java_outer_classname = "HostProjectRegistrationServiceProto";
option java_package = "com.google.cloud.apihub.v1";
option php_namespace = "Google\\Cloud\\ApiHub\\V1";
option ruby_package = "Google::Cloud::ApiHub::V1";

// This service is used for managing the host project registrations.
service HostProjectRegistrationService {
  option (google.api.default_host) = "apihub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a host project registration.
  // A Google cloud project can be registered as a host project if it is not
  // attached as a runtime project to another host project.
  // A project can be registered as a host project only once. Subsequent
  // register calls for the same project will fail.
  rpc CreateHostProjectRegistration(CreateHostProjectRegistrationRequest)
      returns (HostProjectRegistration) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/hostProjectRegistrations"
      body: "host_project_registration"
    };
    option (google.api.method_signature) =
        "parent,host_project_registration,host_project_registration_id";
  }

  // Get a host project registration.
  rpc GetHostProjectRegistration(GetHostProjectRegistrationRequest)
      returns (HostProjectRegistration) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/hostProjectRegistrations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists host project registrations.
  rpc ListHostProjectRegistrations(ListHostProjectRegistrationsRequest)
      returns (ListHostProjectRegistrationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/hostProjectRegistrations"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The
// [CreateHostProjectRegistration][google.cloud.apihub.v1.HostProjectRegistrationService.CreateHostProjectRegistration]
// method's request.
message CreateHostProjectRegistrationRequest {
  // Required. The parent resource for the host project.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/HostProjectRegistration"
    }
  ];

  // Required. The ID to use for the Host Project Registration, which will
  // become the final component of the host project registration's resource
  // name. The ID must be the same as the Google cloud project specified in the
  // host_project_registration.gcp_project field.
  string host_project_registration_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The host project registration to register.
  HostProjectRegistration host_project_registration = 3
      [(google.api.field_behavior) = REQUIRED];
}

// The
// [GetHostProjectRegistration][google.cloud.apihub.v1.HostProjectRegistrationService.GetHostProjectRegistration]
// method's request.
message GetHostProjectRegistrationRequest {
  // Required. Host project registration resource name.
  // projects/{project}/locations/{location}/hostProjectRegistrations/{host_project_registration_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/HostProjectRegistration"
    }
  ];
}

// The
// [ListHostProjectRegistrations][google.cloud.apihub.v1.HostProjectRegistrationService.ListHostProjectRegistrations]
// method's request.
message ListHostProjectRegistrationsRequest {
  // Required. The parent, which owns this collection of host projects.
  // Format: `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/HostProjectRegistration"
    }
  ];

  // Optional. The maximum number of host project registrations to return. The
  // service may return fewer than this value. If unspecified, at most 50 host
  // project registrations will be returned. The maximum value is 1000; values
  // above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListHostProjectRegistrations` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all other parameters (except page_size) provided to
  // `ListHostProjectRegistrations` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression that filters the list of HostProjectRegistrations.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string. All
  // standard operators as documented at https://google.aip.dev/160 are
  // supported.
  //
  // The following fields in the `HostProjectRegistration` are eligible for
  // filtering:
  //
  //   * `name` - The name of the HostProjectRegistration.
  //   * `create_time` - The time at which the HostProjectRegistration was
  //   created. The value should be in the
  //   (RFC3339)[https://tools.ietf.org/html/rfc3339] format.
  //   * `gcp_project` - The Google cloud project associated with the
  //   HostProjectRegistration.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The
// [ListHostProjectRegistrations][google.cloud.apihub.v1.HostProjectRegistrationService.ListHostProjectRegistrations]
// method's response.
message ListHostProjectRegistrationsResponse {
  // The list of host project registrations.
  repeated HostProjectRegistration host_project_registrations = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Host project registration refers to the registration of a Google cloud
// project with Api Hub as a host project. This is the project where Api Hub is
// provisioned. It acts as the consumer project for the Api Hub instance
// provisioned. Multiple runtime projects can be attached to the host project
// and these attachments define the scope of Api Hub.
message HostProjectRegistration {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/HostProjectRegistration"
    pattern: "projects/{project}/locations/{location}/hostProjectRegistrations/{host_project_registration}"
    plural: "hostProjectRegistrations"
    singular: "hostProjectRegistration"
  };

  // Identifier. The name of the host project registration.
  // Format:
  // "projects/{project}/locations/{location}/hostProjectRegistrations/{host_project_registration}".
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Immutable. Google cloud project name in the format:
  // "projects/abc" or "projects/123". As input, project name with either
  // project id or number are accepted. As output, this field will contain
  // project number.
  string gcp_project = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Output only. The time at which the host project registration was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
