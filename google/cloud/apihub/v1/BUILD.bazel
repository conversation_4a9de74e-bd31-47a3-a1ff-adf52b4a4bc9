# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "apihub_proto",
    srcs = [
        "apihub_service.proto",
        "common_fields.proto",
        "host_project_registration_service.proto",
        "linting_service.proto",
        "plugin_service.proto",
        "provisioning_service.proto",
        "runtime_project_attachment_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "apihub_proto_with_info",
    deps = [
        ":apihub_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "apihub_java_proto",
    deps = [":apihub_proto"],
)

java_grpc_library(
    name = "apihub_java_grpc",
    srcs = [":apihub_proto"],
    deps = [":apihub_java_proto"],
)

java_gapic_library(
    name = "apihub_java_gapic",
    srcs = [":apihub_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "apihub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apihub_v1.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        ":apihub_java_grpc",
    ],
    transport = "rest",
    deps = [
        ":apihub_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "apihub_java_gapic_test_suite",
    test_classes = [
        #"com.google.cloud.apihub.v1.ApiHubClientHttpJsonTest",
        "com.google.cloud.apihub.v1.ApiHubClientTest",
        #"com.google.cloud.apihub.v1.ApiHubDependenciesClientHttpJsonTest",
        "com.google.cloud.apihub.v1.ApiHubDependenciesClientTest",
        #"com.google.cloud.apihub.v1.ApiHubPluginClientHttpJsonTest",
        "com.google.cloud.apihub.v1.ApiHubPluginClientTest",
        #"com.google.cloud.apihub.v1.HostProjectRegistrationServiceClientHttpJsonTest",
        "com.google.cloud.apihub.v1.HostProjectRegistrationServiceClientTest",
        #"com.google.cloud.apihub.v1.LintingServiceClientHttpJsonTest",
        "com.google.cloud.apihub.v1.LintingServiceClientTest",
        #"com.google.cloud.apihub.v1.ProvisioningClientHttpJsonTest",
        "com.google.cloud.apihub.v1.ProvisioningClientTest",
        #"com.google.cloud.apihub.v1.RuntimeProjectAttachmentServiceClientHttpJsonTest",
        "com.google.cloud.apihub.v1.RuntimeProjectAttachmentServiceClientTest",
    ],
    runtime_deps = [":apihub_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-apihub-v1-java",
    transport = "rest",
    deps = [
        ":apihub_java_gapic",
        ":apihub_java_grpc",
        ":apihub_java_proto",
        ":apihub_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "apihub_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/apihub/apiv1/apihubpb",
    protos = [":apihub_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "apihub_go_gapic",
    srcs = [":apihub_proto_with_info"],
    grpc_service_config = "apihub_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/apihub/apiv1;apihub",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "apihub_v1.yaml",
    transport = "rest",
    deps = [
        ":apihub_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-apihub-v1-go",
    deps = [
        ":apihub_go_gapic",
        ":apihub_go_gapic_srcjar-metadata.srcjar",
        ":apihub_go_gapic_srcjar-snippets.srcjar",
        ":apihub_go_gapic_srcjar-test.srcjar",
        ":apihub_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "apihub_py_gapic",
    srcs = [":apihub_proto"],
    grpc_service_config = "apihub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apihub_v1.yaml",
    transport = "rest",
    deps = [],
)

py_test(
    name = "apihub_py_gapic_test",
    srcs = [
        "apihub_py_gapic_pytest.py",
        "apihub_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":apihub_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "apihub-v1-py",
    deps = [
        ":apihub_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "apihub_php_proto",
    deps = [":apihub_proto"],
)

php_gapic_library(
    name = "apihub_php_gapic",
    srcs = [":apihub_proto_with_info"],
    grpc_service_config = "apihub_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "apihub_v1.yaml",
    transport = "rest",
    deps = [
        ":apihub_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-apihub-v1-php",
    deps = [
        ":apihub_php_gapic",
        ":apihub_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "apihub_nodejs_gapic",
    package_name = "@google-cloud/apihub",
    src = ":apihub_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "apihub_v1_grpc_service_config.json",
    package = "google.cloud.apihub.v1",
    rest_numeric_enums = True,
    service_yaml = "apihub_v1.yaml",
    transport = "rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "apihub-v1-nodejs",
    deps = [
        ":apihub_nodejs_gapic",
        ":apihub_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "apihub_ruby_proto",
    deps = [":apihub_proto"],
)

ruby_grpc_library(
    name = "apihub_ruby_grpc",
    srcs = [":apihub_proto"],
    deps = [":apihub_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "apihub_ruby_gapic",
    srcs = [":apihub_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-api_hub-v1",
    ],
    grpc_service_config = "apihub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apihub_v1.yaml",
    transport = "rest",
    deps = [
        ":apihub_ruby_grpc",
        ":apihub_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-apihub-v1-ruby",
    deps = [
        ":apihub_ruby_gapic",
        ":apihub_ruby_grpc",
        ":apihub_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "apihub_csharp_proto",
    extra_opts = [],
    deps = [":apihub_proto"],
)

csharp_grpc_library(
    name = "apihub_csharp_grpc",
    srcs = [":apihub_proto"],
    deps = [":apihub_csharp_proto"],
)

csharp_gapic_library(
    name = "apihub_csharp_gapic",
    srcs = [":apihub_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "apihub_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apihub_v1.yaml",
    transport = "rest",
    deps = [
        ":apihub_csharp_grpc",
        ":apihub_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-apihub-v1-csharp",
    deps = [
        ":apihub_csharp_gapic",
        ":apihub_csharp_grpc",
        ":apihub_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "apihub_cc_proto",
    deps = [":apihub_proto"],
)

cc_grpc_library(
    name = "apihub_cc_grpc",
    srcs = [":apihub_proto"],
    grpc_only = True,
    deps = [":apihub_cc_proto"],
)
