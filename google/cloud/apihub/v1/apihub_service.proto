// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apihub.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/apihub/v1/common_fields.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.ApiHub.V1";
option go_package = "cloud.google.com/go/apihub/apiv1/apihubpb;apihubpb";
option java_multiple_files = true;
option java_outer_classname = "ApiHubServiceProto";
option java_package = "com.google.cloud.apihub.v1";
option php_namespace = "Google\\Cloud\\ApiHub\\V1";
option ruby_package = "Google::Cloud::ApiHub::V1";

// This service provides all methods related to the API hub.
service ApiHub {
  option (google.api.default_host) = "apihub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create an API resource in the API hub.
  // Once an API resource is created, versions can be added to it.
  rpc CreateApi(CreateApiRequest) returns (Api) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/apis"
      body: "api"
    };
    option (google.api.method_signature) = "parent,api,api_id";
  }

  // Get API resource details including the API versions contained in it.
  rpc GetApi(GetApiRequest) returns (Api) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List API resources in the API hub.
  rpc ListApis(ListApisRequest) returns (ListApisResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/apis"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update an API resource in the API hub. The following fields in the
  // [API][] can be updated:
  //
  // * [display_name][google.cloud.apihub.v1.Api.display_name]
  // * [description][google.cloud.apihub.v1.Api.description]
  // * [owner][google.cloud.apihub.v1.Api.owner]
  // * [documentation][google.cloud.apihub.v1.Api.documentation]
  // * [target_user][google.cloud.apihub.v1.Api.target_user]
  // * [team][google.cloud.apihub.v1.Api.team]
  // * [business_unit][google.cloud.apihub.v1.Api.business_unit]
  // * [maturity_level][google.cloud.apihub.v1.Api.maturity_level]
  // * [attributes][google.cloud.apihub.v1.Api.attributes]
  //
  // The
  // [update_mask][google.cloud.apihub.v1.UpdateApiRequest.update_mask]
  // should be used to specify the fields being updated.
  //
  // Updating the owner field requires complete owner message
  // and updates both owner and email fields.
  rpc UpdateApi(UpdateApiRequest) returns (Api) {
    option (google.api.http) = {
      patch: "/v1/{api.name=projects/*/locations/*/apis/*}"
      body: "api"
    };
    option (google.api.method_signature) = "api,update_mask";
  }

  // Delete an API resource in the API hub. API can only be deleted if all
  // underlying versions are deleted.
  rpc DeleteApi(DeleteApiRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create an API version for an API resource in the API hub.
  rpc CreateVersion(CreateVersionRequest) returns (Version) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/apis/*}/versions"
      body: "version"
    };
    option (google.api.method_signature) = "parent,version,version_id";
  }

  // Get details about the API version of an API resource. This will include
  // information about the specs and operations present in the API
  // version as well as the deployments linked to it.
  rpc GetVersion(GetVersionRequest) returns (Version) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List API versions of an API resource in the API hub.
  rpc ListVersions(ListVersionsRequest) returns (ListVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/apis/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update API version. The following fields in the
  // [version][google.cloud.apihub.v1.Version] can be updated currently:
  //
  // * [display_name][google.cloud.apihub.v1.Version.display_name]
  // * [description][google.cloud.apihub.v1.Version.description]
  // * [documentation][google.cloud.apihub.v1.Version.documentation]
  // * [deployments][google.cloud.apihub.v1.Version.deployments]
  // * [lifecycle][google.cloud.apihub.v1.Version.lifecycle]
  // * [compliance][google.cloud.apihub.v1.Version.compliance]
  // * [accreditation][google.cloud.apihub.v1.Version.accreditation]
  // * [attributes][google.cloud.apihub.v1.Version.attributes]
  //
  // The
  // [update_mask][google.cloud.apihub.v1.UpdateVersionRequest.update_mask]
  // should be used to specify the fields being updated.
  rpc UpdateVersion(UpdateVersionRequest) returns (Version) {
    option (google.api.http) = {
      patch: "/v1/{version.name=projects/*/locations/*/apis/*/versions/*}"
      body: "version"
    };
    option (google.api.method_signature) = "version,update_mask";
  }

  // Delete an API version. Version can only be deleted if all underlying specs,
  // operations, definitions and linked deployments are deleted.
  rpc DeleteVersion(DeleteVersionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Add a spec to an API version in the API hub.
  // Multiple specs can be added to an API version.
  // Note, while adding a spec, at least one of `contents` or `source_uri` must
  // be provided. If `contents` is provided, then `spec_type` must also be
  // provided.
  //
  // On adding a spec with contents to the version, the operations present in it
  // will be added to the version.Note that the file contents in the spec should
  // be of the same type as defined in the
  // `projects/{project}/locations/{location}/attributes/system-spec-type`
  // attribute associated with spec resource. Note that specs of various types
  // can be uploaded, however parsing of details is supported for OpenAPI spec
  // currently.
  //
  // In order to access the information parsed from the spec, use the
  // [GetSpec][google.cloud.apihub.v1.ApiHub.GetSpec] method.
  // In order to access the raw contents for a particular spec, use the
  // [GetSpecContents][google.cloud.apihub.v1.ApiHub.GetSpecContents] method.
  // In order to access the operations parsed from the spec, use the
  // [ListAPIOperations][google.cloud.apihub.v1.ApiHub.ListApiOperations]
  // method.
  rpc CreateSpec(CreateSpecRequest) returns (Spec) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/apis/*/versions/*}/specs"
      body: "spec"
    };
    option (google.api.method_signature) = "parent,spec,spec_id";
  }

  // Get details about the information parsed from a spec.
  // Note that this method does not return the raw spec contents.
  // Use [GetSpecContents][google.cloud.apihub.v1.ApiHub.GetSpecContents] method
  // to retrieve the same.
  rpc GetSpec(GetSpecRequest) returns (Spec) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get spec contents.
  rpc GetSpecContents(GetSpecContentsRequest) returns (SpecContents) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}:contents"
    };
    option (google.api.method_signature) = "name";
  }

  // List specs corresponding to a particular API resource.
  rpc ListSpecs(ListSpecsRequest) returns (ListSpecsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/apis/*/versions/*}/specs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update spec. The following fields in the
  // [spec][google.cloud.apihub.v1.Spec] can be updated:
  //
  // * [display_name][google.cloud.apihub.v1.Spec.display_name]
  // * [source_uri][google.cloud.apihub.v1.Spec.source_uri]
  // * [lint_response][google.cloud.apihub.v1.Spec.lint_response]
  // * [attributes][google.cloud.apihub.v1.Spec.attributes]
  // * [contents][google.cloud.apihub.v1.Spec.contents]
  // * [spec_type][google.cloud.apihub.v1.Spec.spec_type]
  //
  // In case of an OAS spec, updating spec contents can lead to:
  // 1. Creation, deletion and update of operations.
  // 2. Creation, deletion and update of definitions.
  // 3. Update of other info parsed out from the new spec.
  //
  // In case of contents or source_uri being present in update mask, spec_type
  // must also be present. Also, spec_type can not be present in update mask if
  // contents or source_uri is not present.
  //
  // The
  // [update_mask][google.cloud.apihub.v1.UpdateSpecRequest.update_mask]
  // should be used to specify the fields being updated.
  rpc UpdateSpec(UpdateSpecRequest) returns (Spec) {
    option (google.api.http) = {
      patch: "/v1/{spec.name=projects/*/locations/*/apis/*/versions/*/specs/*}"
      body: "spec"
    };
    option (google.api.method_signature) = "spec,update_mask";
  }

  // Delete a spec.
  // Deleting a spec will also delete the associated operations from the
  // version.
  rpc DeleteSpec(DeleteSpecRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get details about a particular operation in API version.
  rpc GetApiOperation(GetApiOperationRequest) returns (ApiOperation) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/operations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List operations in an API version.
  rpc ListApiOperations(ListApiOperationsRequest)
      returns (ListApiOperationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/apis/*/versions/*}/operations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get details about a definition in an API version.
  rpc GetDefinition(GetDefinitionRequest) returns (Definition) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/versions/*/definitions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a deployment resource in the API hub.
  // Once a deployment resource is created, it can be associated with API
  // versions.
  rpc CreateDeployment(CreateDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/deployments"
      body: "deployment"
    };
    option (google.api.method_signature) = "parent,deployment,deployment_id";
  }

  // Get details about a deployment and the API versions linked to it.
  rpc GetDeployment(GetDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List deployment resources in the API hub.
  rpc ListDeployments(ListDeploymentsRequest)
      returns (ListDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/deployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update a deployment resource in the API hub. The following fields in the
  // [deployment resource][google.cloud.apihub.v1.Deployment] can be
  // updated:
  //
  // * [display_name][google.cloud.apihub.v1.Deployment.display_name]
  // * [description][google.cloud.apihub.v1.Deployment.description]
  // * [documentation][google.cloud.apihub.v1.Deployment.documentation]
  // * [deployment_type][google.cloud.apihub.v1.Deployment.deployment_type]
  // * [resource_uri][google.cloud.apihub.v1.Deployment.resource_uri]
  // * [endpoints][google.cloud.apihub.v1.Deployment.endpoints]
  // * [slo][google.cloud.apihub.v1.Deployment.slo]
  // * [environment][google.cloud.apihub.v1.Deployment.environment]
  // * [attributes][google.cloud.apihub.v1.Deployment.attributes]
  //
  // The
  // [update_mask][google.cloud.apihub.v1.UpdateDeploymentRequest.update_mask]
  // should be used to specify the fields being updated.
  rpc UpdateDeployment(UpdateDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      patch: "/v1/{deployment.name=projects/*/locations/*/deployments/*}"
      body: "deployment"
    };
    option (google.api.method_signature) = "deployment,update_mask";
  }

  // Delete a deployment resource in the API hub.
  rpc DeleteDeployment(DeleteDeploymentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a user defined attribute.
  //
  // Certain pre defined attributes are already created by the API hub. These
  // attributes will have type as `SYSTEM_DEFINED` and can be listed via
  // [ListAttributes][google.cloud.apihub.v1.ApiHub.ListAttributes] method.
  // Allowed values for the same can be updated via
  // [UpdateAttribute][google.cloud.apihub.v1.ApiHub.UpdateAttribute] method.
  rpc CreateAttribute(CreateAttributeRequest) returns (Attribute) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/attributes"
      body: "attribute"
    };
    option (google.api.method_signature) = "parent,attribute,attribute_id";
  }

  // Get details about the attribute.
  rpc GetAttribute(GetAttributeRequest) returns (Attribute) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/attributes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update the attribute.  The following fields in the
  // [Attribute resource][google.cloud.apihub.v1.Attribute] can be updated:
  //
  // * [display_name][google.cloud.apihub.v1.Attribute.display_name]
  // The display name can be updated for user defined attributes only.
  // * [description][google.cloud.apihub.v1.Attribute.description]
  // The description can be updated for user defined attributes only.
  // * [allowed_values][google.cloud.apihub.v1.Attribute.allowed_values]
  // To update the list of allowed values, clients need to use the fetched list
  // of allowed values and add or remove values to or from the same list.
  // The mutable allowed values can be updated for both user defined and System
  // defined attributes. The immutable allowed values cannot be updated or
  // deleted. The updated list of allowed values cannot be empty. If an allowed
  // value that is already used by some resource's attribute is deleted, then
  // the association between the resource and the attribute value will also be
  // deleted.
  // * [cardinality][google.cloud.apihub.v1.Attribute.cardinality]
  // The cardinality can be updated for user defined attributes only.
  // Cardinality can only be increased during an update.
  //
  // The
  // [update_mask][google.cloud.apihub.v1.UpdateAttributeRequest.update_mask]
  // should be used to specify the fields being updated.
  rpc UpdateAttribute(UpdateAttributeRequest) returns (Attribute) {
    option (google.api.http) = {
      patch: "/v1/{attribute.name=projects/*/locations/*/attributes/*}"
      body: "attribute"
    };
    option (google.api.method_signature) = "attribute,update_mask";
  }

  // Delete an attribute.
  //
  // Note: System defined attributes cannot be deleted. All
  // associations of the attribute being deleted with any API hub resource will
  // also get deleted.
  rpc DeleteAttribute(DeleteAttributeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/attributes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List all attributes.
  rpc ListAttributes(ListAttributesRequest) returns (ListAttributesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/attributes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Search across API-Hub resources.
  rpc SearchResources(SearchResourcesRequest)
      returns (SearchResourcesResponse) {
    option (google.api.http) = {
      post: "/v1/{location=projects/*/locations/*}:searchResources"
      body: "*"
    };
    option (google.api.method_signature) = "location,query";
  }

  // Create an External API resource in the API hub.
  rpc CreateExternalApi(CreateExternalApiRequest) returns (ExternalApi) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/externalApis"
      body: "external_api"
    };
    option (google.api.method_signature) =
        "parent,external_api,external_api_id";
  }

  // Get details about an External API resource in the API hub.
  rpc GetExternalApi(GetExternalApiRequest) returns (ExternalApi) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/externalApis/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update an External API resource in the API hub. The following fields can be
  // updated:
  //
  // * [display_name][google.cloud.apihub.v1.ExternalApi.display_name]
  // * [description][google.cloud.apihub.v1.ExternalApi.description]
  // * [documentation][google.cloud.apihub.v1.ExternalApi.documentation]
  // * [endpoints][google.cloud.apihub.v1.ExternalApi.endpoints]
  // * [paths][google.cloud.apihub.v1.ExternalApi.paths]
  //
  // The
  // [update_mask][google.cloud.apihub.v1.UpdateExternalApiRequest.update_mask]
  // should be used to specify the fields being updated.
  rpc UpdateExternalApi(UpdateExternalApiRequest) returns (ExternalApi) {
    option (google.api.http) = {
      patch: "/v1/{external_api.name=projects/*/locations/*/externalApis/*}"
      body: "external_api"
    };
    option (google.api.method_signature) = "external_api,update_mask";
  }

  // Delete an External API resource in the API hub.
  rpc DeleteExternalApi(DeleteExternalApiRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/externalApis/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List External API resources in the API hub.
  rpc ListExternalApis(ListExternalApisRequest)
      returns (ListExternalApisResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/externalApis"
    };
    option (google.api.method_signature) = "parent";
  }
}

// This service provides methods for various operations related to a
// [Dependency][google.cloud.apihub.v1.Dependency] in the API hub.
service ApiHubDependencies {
  option (google.api.default_host) = "apihub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a dependency between two entities in the API hub.
  rpc CreateDependency(CreateDependencyRequest) returns (Dependency) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/dependencies"
      body: "dependency"
    };
    option (google.api.method_signature) = "parent,dependency,dependency_id";
  }

  // Get details about a dependency resource in the API hub.
  rpc GetDependency(GetDependencyRequest) returns (Dependency) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dependencies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update a dependency based on the
  // [update_mask][google.cloud.apihub.v1.UpdateDependencyRequest.update_mask]
  // provided in the request.
  //
  // The following fields in the [dependency][google.cloud.apihub.v1.Dependency]
  // can be updated:
  // * [description][google.cloud.apihub.v1.Dependency.description]
  rpc UpdateDependency(UpdateDependencyRequest) returns (Dependency) {
    option (google.api.http) = {
      patch: "/v1/{dependency.name=projects/*/locations/*/dependencies/*}"
      body: "dependency"
    };
    option (google.api.method_signature) = "dependency,update_mask";
  }

  // Delete the dependency resource.
  rpc DeleteDependency(DeleteDependencyRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/dependencies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List dependencies based on the provided filter and pagination parameters.
  rpc ListDependencies(ListDependenciesRequest)
      returns (ListDependenciesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/dependencies"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The [CreateApi][google.cloud.apihub.v1.ApiHub.CreateApi] method's request.
message CreateApiRequest {
  // Required. The parent resource for the API resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Api"
    }
  ];

  // Optional. The ID to use for the API resource, which will become the final
  // component of the API's resource name. This field is optional.
  //
  // * If provided, the same will be used. The service will throw an error if
  // the specified id is already used by another API resource in the API hub.
  // * If not provided, a system generated id will be used.
  //
  // This value should be 4-500 characters, and valid characters
  // are /[a-z][A-Z][0-9]-_/.
  string api_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The API resource to create.
  Api api = 3 [(google.api.field_behavior) = REQUIRED];
}

// The [GetApi][google.cloud.apihub.v1.ApiHub.GetApi] method's request.
message GetApiRequest {
  // Required. The name of the API resource to retrieve.
  // Format: `projects/{project}/locations/{location}/apis/{api}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Api" }
  ];
}

// The [UpdateApi][google.cloud.apihub.v1.ApiHub.UpdateApi] method's request.
message UpdateApiRequest {
  // Required. The API resource to update.
  //
  // The API resource's `name` field is used to identify the API resource to
  // update.
  // Format: `projects/{project}/locations/{location}/apis/{api}`
  Api api = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The [DeleteApi][google.cloud.apihub.v1.ApiHub.DeleteApi] method's request.
message DeleteApiRequest {
  // Required. The name of the API resource to delete.
  // Format: `projects/{project}/locations/{location}/apis/{api}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Api" }
  ];

  // Optional. If set to true, any versions from this API will also be deleted.
  // Otherwise, the request will only work if the API has no versions.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListApis][google.cloud.apihub.v1.ApiHub.ListApis] method's request.
message ListApisRequest {
  // Required. The parent, which owns this collection of API resources.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Api"
    }
  ];

  // Optional. An expression that filters the list of ApiResources.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string. The
  // comparison operator must be one of: `<`, `>`, `:` or `=`. Filters are not
  // case sensitive.
  //
  // The following fields in the `ApiResource` are eligible for filtering:
  //
  //   * `owner.email` - The email of the team which owns the ApiResource.
  //   Allowed comparison operators: `=`.
  //   * `create_time` - The time at which the ApiResource was created. The
  //   value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339]
  //   format. Allowed comparison operators: `>` and `<`.
  //   * `display_name` - The display name of the ApiResource. Allowed
  //   comparison operators: `=`.
  //   * `target_user.enum_values.values.id` - The allowed value id of the
  //   target users attribute associated with the ApiResource. Allowed
  //   comparison operator is `:`.
  //   * `target_user.enum_values.values.display_name` - The allowed value
  //   display name of the target users attribute associated with the
  //   ApiResource. Allowed comparison operator is `:`.
  //   * `team.enum_values.values.id` - The allowed value id of the team
  //   attribute associated with the ApiResource. Allowed comparison operator is
  //   `:`.
  //   * `team.enum_values.values.display_name` - The allowed value display name
  //   of the team attribute associated with the ApiResource. Allowed comparison
  //   operator is `:`.
  //   * `business_unit.enum_values.values.id` - The allowed value id of the
  //   business unit attribute associated with the ApiResource. Allowed
  //   comparison operator is `:`.
  //   * `business_unit.enum_values.values.display_name` - The allowed value
  //   display name of the business unit attribute associated with the
  //   ApiResource. Allowed comparison operator is `:`.
  //   * `maturity_level.enum_values.values.id` - The allowed value id of the
  //   maturity level attribute associated with the ApiResource. Allowed
  //   comparison operator is `:`.
  //   * `maturity_level.enum_values.values.display_name` - The allowed value
  //   display name of the maturity level attribute associated with the
  //   ApiResource. Allowed comparison operator is `:`.
  //   * `api_style.enum_values.values.id` - The allowed value id of the
  //   api style attribute associated with the ApiResource. Allowed
  //   comparison operator is `:`.
  //   * `api_style.enum_values.values.display_name` - The allowed value display
  //   name of the api style attribute associated with the ApiResource. Allowed
  //   comparison operator is `:`.
  //
  // Expressions are combined with either `AND` logic operator or `OR` logical
  // operator but not both of them together i.e. only one of the `AND` or `OR`
  // operator can be used throughout the filter string and both the operators
  // cannot be used together. No other logical operators are supported. At most
  // three filter fields are allowed in the filter string and if provided
  // more than that then `INVALID_ARGUMENT` error is returned by the API.
  //
  // Here are a few examples:
  //
  //   * `owner.email = \"<EMAIL>\"` -  - The owner team email is
  //   _apihub@google.com_.
  //   * `owner.email = \"<EMAIL>\" AND create_time <
  //   \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
  //   The owner team <NAME_EMAIL>_ and the api was created
  //   before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_.
  //   * `owner.email = \"<EMAIL>\" OR team.enum_values.values.id:
  //   apihub-team-id` - The filter string specifies the APIs where the owner
  //   team <NAME_EMAIL>_ or the id of the allowed value
  //   associated with the team attribute is _apihub-team-id_.
  //   * `owner.email = \"<EMAIL>\" OR
  //   team.enum_values.values.display_name: ApiHub Team` - The filter string
  //   specifies the APIs where the owner team <NAME_EMAIL>_ or
  //   the display name of the allowed value associated with the team attribute
  //   is `ApiHub Team`.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of API resources to return. The service may
  // return fewer than this value. If unspecified, at most 50 Apis will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListApis` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters (except page_size) provided to
  // `ListApis` must match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListApis][google.cloud.apihub.v1.ApiHub.ListApis] method's response.
message ListApisResponse {
  // The API resources present in the API hub.
  repeated Api apis = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The [CreateVersion][google.cloud.apihub.v1.ApiHub.CreateVersion] method's
// request.
message CreateVersionRequest {
  // Required. The parent resource for API version.
  // Format: `projects/{project}/locations/{location}/apis/{api}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Api" }
  ];

  // Optional. The ID to use for the API version, which will become the final
  // component of the version's resource name. This field is optional.
  //
  // * If provided, the same will be used. The service will throw an error if
  // the specified id is already used by another version in the API resource.
  // * If not provided, a system generated id will be used.
  //
  // This value should be 4-500 characters, and valid characters
  // are /[a-z][A-Z][0-9]-_/.
  string version_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The version to create.
  Version version = 3 [(google.api.field_behavior) = REQUIRED];
}

// The [GetVersion][google.cloud.apihub.v1.ApiHub.GetVersion] method's request.
message GetVersionRequest {
  // Required. The name of the API version to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Version" }
  ];
}

// The [UpdateVersion][google.cloud.apihub.v1.ApiHub.UpdateVersion] method's
// request.
message UpdateVersionRequest {
  // Required. The API version to update.
  //
  // The version's `name` field is used to identify the API version to update.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  Version version = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The [DeleteVersion][google.cloud.apihub.v1.ApiHub.DeleteVersion] method's
// request.
message DeleteVersionRequest {
  // Required. The name of the version to delete.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Version" }
  ];

  // Optional. If set to true, any specs from this version will also be deleted.
  // Otherwise, the request will only work if the version has no specs.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListVersions][google.cloud.apihub.v1.ApiHub.ListVersions] method's
// request.
message ListVersionsRequest {
  // Required. The parent which owns this collection of API versions i.e., the
  // API resource Format: `projects/{project}/locations/{location}/apis/{api}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Version"
    }
  ];

  // Optional. An expression that filters the list of Versions.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string, a
  // number, or a boolean. The comparison operator must be one of: `<`, `>` or
  // `=`. Filters are not case sensitive.
  //
  // The following fields in the `Version` are eligible for filtering:
  //
  //   * `display_name` - The display name of the Version. Allowed
  //   comparison operators: `=`.
  //   * `create_time` - The time at which the Version was created. The
  //   value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339]
  //   format. Allowed comparison operators: `>` and `<`.
  //   * `lifecycle.enum_values.values.id` - The allowed value id of the
  //   lifecycle attribute associated with the Version. Allowed comparison
  //   operators: `:`.
  //   * `lifecycle.enum_values.values.display_name` - The allowed value display
  //   name of the lifecycle attribute associated with the Version. Allowed
  //   comparison operators: `:`.
  //   * `compliance.enum_values.values.id` -  The allowed value id of the
  //   compliances attribute associated with the Version. Allowed comparison
  //   operators: `:`.
  //   * `compliance.enum_values.values.display_name` -  The allowed value
  //   display name of the compliances attribute associated with the Version.
  //   Allowed comparison operators: `:`.
  //   * `accreditation.enum_values.values.id` - The allowed value id of the
  //   accreditations attribute associated with the Version. Allowed
  //   comparison operators: `:`.
  //   * `accreditation.enum_values.values.display_name` - The allowed value
  //   display name of the accreditations attribute associated with the Version.
  //   Allowed comparison operators: `:`.
  //
  // Expressions are combined with either `AND` logic operator or `OR` logical
  // operator but not both of them together i.e. only one of the `AND` or `OR`
  // operator can be used throughout the filter string and both the operators
  // cannot be used together. No other logical operators are
  // supported. At most three filter fields are allowed in the filter
  // string and if provided more than that then `INVALID_ARGUMENT` error is
  // returned by the API.
  //
  // Here are a few examples:
  //
  //   * `lifecycle.enum_values.values.id: preview-id` - The filter string
  //   specifies that the id of the allowed value associated with the lifecycle
  //   attribute of the Version is _preview-id_.
  //   * `lifecycle.enum_values.values.display_name: \"Preview Display Name\"` -
  //   The filter string specifies that the display name of the allowed value
  //   associated with the lifecycle attribute of the Version is `Preview
  //   Display Name`.
  //   * `lifecycle.enum_values.values.id: preview-id AND create_time <
  //   \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
  //   The id of the allowed value associated with the lifecycle attribute of
  //   the Version is _preview-id_ and it was created before _2021-08-15
  //   14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_.
  //   * `compliance.enum_values.values.id: gdpr-id OR
  //   compliance.enum_values.values.id: pci-dss-id`
  //   - The id of the allowed value associated with the compliance attribute is
  //   _gdpr-id_ or _pci-dss-id_.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of versions to return. The service may return
  // fewer than this value. If unspecified, at most 50 versions will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListVersions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters (except page_size) provided to
  // `ListVersions` must match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListVersions][google.cloud.apihub.v1.ApiHub.ListVersions] method's
// response.
message ListVersionsResponse {
  // The versions corresponding to an API.
  repeated Version versions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The [CreateSpec][google.cloud.apihub.v1.ApiHub.CreateSpec] method's request.
message CreateSpecRequest {
  // Required. The parent resource for Spec.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Version" }
  ];

  // Optional. The ID to use for the spec, which will become the final component
  // of the spec's resource name. This field is optional.
  //
  // * If provided, the same will be used. The service will throw an error if
  // the specified id is already used by another spec in the API
  // resource.
  // * If not provided, a system generated id will be used.
  //
  // This value should be 4-500 characters, and valid characters
  // are /[a-z][A-Z][0-9]-_/.
  string spec_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The spec to create.
  Spec spec = 3 [(google.api.field_behavior) = REQUIRED];
}

// The [GetSpec][google.cloud.apihub.v1.ApiHub.GetSpec] method's request.
message GetSpecRequest {
  // Required. The name of the spec to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Spec" }
  ];
}

// The [UpdateSpec][google.cloud.apihub.v1.ApiHub.UpdateSpec] method's request.
message UpdateSpecRequest {
  // Required. The spec to update.
  //
  // The spec's `name` field is used to identify the spec to
  // update. Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  Spec spec = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The [DeleteSpec][google.cloud.apihub.v1.ApiHub.DeleteSpec] method's request.
message DeleteSpecRequest {
  // Required. The name of the spec  to delete.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Spec" }
  ];
}

// The [ListSpecs][ListSpecs] method's request.
message ListSpecsRequest {
  // Required. The parent, which owns this collection of specs.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Spec"
    }
  ];

  // Optional. An expression that filters the list of Specs.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string. The
  // comparison operator must be one of: `<`, `>`, `:` or `=`. Filters are not
  // case sensitive.
  //
  // The following fields in the `Spec` are eligible for filtering:
  //
  //   * `display_name` - The display name of the Spec. Allowed comparison
  //   operators: `=`.
  //   * `create_time` - The time at which the Spec was created. The
  //   value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339]
  //   format. Allowed comparison operators: `>` and `<`.
  //   * `spec_type.enum_values.values.id` - The allowed value id of the
  //   spec_type attribute associated with the Spec. Allowed comparison
  //   operators: `:`.
  //   * `spec_type.enum_values.values.display_name` - The allowed value display
  //   name of the spec_type attribute associated with the Spec. Allowed
  //   comparison operators: `:`.
  //   * `lint_response.json_values.values` - The json value of the
  //   lint_response attribute associated with the Spec. Allowed comparison
  //   operators: `:`.
  //   * `mime_type` - The MIME type of the Spec. Allowed comparison
  //   operators: `=`.
  //
  // Expressions are combined with either `AND` logic operator or `OR` logical
  // operator but not both of them together i.e. only one of the `AND` or `OR`
  // operator can be used throughout the filter string and both the operators
  // cannot be used together. No other logical operators are
  // supported. At most three filter fields are allowed in the filter
  // string and if provided more than that then `INVALID_ARGUMENT` error is
  // returned by the API.
  //
  // Here are a few examples:
  //
  //   * `spec_type.enum_values.values.id: rest-id` -  The filter
  //   string specifies that the id of the allowed value associated with the
  //   spec_type attribute is _rest-id_.
  //   * `spec_type.enum_values.values.display_name: \"Rest Display Name\"` -
  //   The filter string specifies that the display name of the allowed value
  //   associated with the spec_type attribute is `Rest Display Name`.
  //   * `spec_type.enum_values.values.id: grpc-id AND create_time <
  //   \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
  //   The id of the allowed value associated with the spec_type attribute is
  //   _grpc-id_ and the spec was created before _2021-08-15 14:50:00 UTC_ and
  //   after _2021-08-10 12:00:00 UTC_.
  //   * `spec_type.enum_values.values.id: rest-id OR
  //   spec_type.enum_values.values.id: grpc-id`
  //   - The id of the allowed value associated with the spec_type attribute is
  //   _rest-id_ or _grpc-id_.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of specs to return. The service may return
  // fewer than this value. If unspecified, at most 50 specs will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListSpecs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListSpecs` must
  // match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListSpecs][google.cloud.apihub.v1.ApiHub.ListSpecs] method's response.
message ListSpecsResponse {
  // The specs corresponding to an API.
  repeated Spec specs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The [GetSpecContents][google.cloud.apihub.v1.ApiHub.GetSpecContents] method's
// request.
message GetSpecContentsRequest {
  // Required. The name of the spec whose contents need to be retrieved.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Spec" }
  ];
}

// The [GetApiOperation][google.cloud.apihub.v1.ApiHub.GetApiOperation] method's
// request.
message GetApiOperationRequest {
  // Required. The name of the operation to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/ApiOperation"
    }
  ];
}

// The [ListApiOperations][google.cloud.apihub.v1.ApiHub.ListApiOperations]
// method's request.
message ListApiOperationsRequest {
  // Required. The parent which owns this collection of operations i.e., the API
  // version. Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/ApiOperation"
    }
  ];

  // Optional. An expression that filters the list of ApiOperations.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string or a
  // boolean. The comparison operator must be one of: `<`, `>` or
  // `=`. Filters are not case sensitive.
  //
  // The following fields in the `ApiOperation` are eligible for filtering:
  //   * `name` - The ApiOperation resource name. Allowed comparison
  //   operators:
  //   `=`.
  //   * `details.http_operation.path.path` - The http operation's complete path
  //   relative to server endpoint. Allowed comparison operators: `=`.
  //   * `details.http_operation.method` - The http operation method type.
  //   Allowed comparison operators: `=`.
  //   * `details.deprecated` - Indicates if the ApiOperation is deprecated.
  //   Allowed values are True / False indicating the deprycation status of the
  //   ApiOperation. Allowed comparison operators: `=`.
  //   * `create_time` - The time at which the ApiOperation was created. The
  //   value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339]
  //   format. Allowed comparison operators: `>` and `<`.
  //
  // Expressions are combined with either `AND` logic operator or `OR` logical
  // operator but not both of them together i.e. only one of the `AND` or `OR`
  // operator can be used throughout the filter string and both the operators
  // cannot be used together. No other logical operators are supported. At most
  // three filter fields are allowed in the filter string and if provided
  // more than that then `INVALID_ARGUMENT` error is returned by the API.
  //
  // Here are a few examples:
  //
  //   * `details.deprecated = True` -  The ApiOperation is deprecated.
  //   * `details.http_operation.method = GET AND create_time <
  //   \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
  //   The method of the http operation of the ApiOperation is _GET_ and the
  //   spec was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10
  //   12:00:00 UTC_.
  //   * `details.http_operation.method = GET OR details.http_operation.method =
  //   POST`. - The http operation of the method of ApiOperation is _GET_ or
  //   _POST_.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of operations to return. The service may
  // return fewer than this value. If unspecified, at most 50 operations will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListApiOperations` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters (except page_size) provided to
  // `ListApiOperations` must match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListApiOperations][google.cloud.apihub.v1.ApiHub.ListApiOperations]
// method's response.
message ListApiOperationsResponse {
  // The operations corresponding to an API version.
  // Only following field will be populated in the response: name,
  // spec, details.deprecated, details.http_operation.path.path,
  // details.http_operation.method and details.documentation.external_uri.
  repeated ApiOperation api_operations = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The [GetDefinition][google.cloud.apihub.v1.ApiHub.GetDefinition] method's
// request.
message GetDefinitionRequest {
  // Required. The name of the definition to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/definitions/{definition}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Definition"
    }
  ];
}

// The [CreateDeployment][google.cloud.apihub.v1.ApiHub.CreateDeployment]
// method's request.
message CreateDeploymentRequest {
  // Required. The parent resource for the deployment resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Deployment"
    }
  ];

  // Optional. The ID to use for the deployment resource, which will become the
  // final component of the deployment's resource name. This field is optional.
  //
  // * If provided, the same will be used. The service will throw an error if
  // the specified id is already used by another deployment resource in the API
  // hub.
  // * If not provided, a system generated id will be used.
  //
  // This value should be 4-500 characters, and valid characters
  // are /[a-z][A-Z][0-9]-_/.
  string deployment_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The deployment resource to create.
  Deployment deployment = 3 [(google.api.field_behavior) = REQUIRED];
}

// The [GetDeployment][google.cloud.apihub.v1.ApiHub.GetDeployment] method's
// request.
message GetDeploymentRequest {
  // Required. The name of the deployment resource to retrieve.
  // Format: `projects/{project}/locations/{location}/deployments/{deployment}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Deployment"
    }
  ];
}

// The [UpdateDeployment][google.cloud.apihub.v1.ApiHub.UpdateDeployment]
// method's request.
message UpdateDeploymentRequest {
  // Required. The deployment resource to update.
  //
  // The deployment resource's `name` field is used to identify the deployment
  // resource to update.
  // Format: `projects/{project}/locations/{location}/deployments/{deployment}`
  Deployment deployment = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The [DeleteDeployment][google.cloud.apihub.v1.ApiHub.DeleteDeployment]
// method's request.
message DeleteDeploymentRequest {
  // Required. The name of the deployment resource to delete.
  // Format: `projects/{project}/locations/{location}/deployments/{deployment}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Deployment"
    }
  ];
}

// The [ListDeployments][google.cloud.apihub.v1.ApiHub.ListDeployments] method's
// request.
message ListDeploymentsRequest {
  // Required. The parent, which owns this collection of deployment resources.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Deployment"
    }
  ];

  // Optional. An expression that filters the list of Deployments.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string. The
  // comparison operator must be one of: `<`, `>` or
  // `=`. Filters are not case sensitive.
  //
  // The following fields in the `Deployments` are eligible for filtering:
  //
  //   * `display_name` - The display name of the Deployment. Allowed
  //   comparison operators: `=`.
  //   * `create_time` - The time at which the Deployment was created. The
  //   value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339]
  //   format. Allowed comparison operators: `>` and `<`.
  //   * `resource_uri` - A URI to the deployment resource. Allowed
  //   comparison operators: `=`.
  //   * `api_versions` - The API versions linked to this deployment. Allowed
  //   comparison operators: `:`.
  //   * `deployment_type.enum_values.values.id` - The allowed value id of the
  //   deployment_type attribute associated with the Deployment. Allowed
  //   comparison operators: `:`.
  //   * `deployment_type.enum_values.values.display_name` - The allowed value
  //   display name of the deployment_type attribute associated with the
  //   Deployment. Allowed comparison operators: `:`.
  //   * `slo.string_values.values` -The allowed string value of the slo
  //   attribute associated with the deployment. Allowed comparison
  //   operators: `:`.
  //   * `environment.enum_values.values.id` - The allowed value id of the
  //   environment attribute associated with the deployment. Allowed
  //   comparison operators: `:`.
  //   * `environment.enum_values.values.display_name` - The allowed value
  //   display name of the environment attribute associated with the deployment.
  //   Allowed comparison operators: `:`.
  //
  // Expressions are combined with either `AND` logic operator or `OR` logical
  // operator but not both of them together i.e. only one of the `AND` or `OR`
  // operator can be used throughout the filter string and both the operators
  // cannot be used together. No other logical operators are supported. At most
  // three filter fields are allowed in the filter string and if provided
  // more than that then `INVALID_ARGUMENT` error is returned by the API.
  //
  // Here are a few examples:
  //
  //   * `environment.enum_values.values.id: staging-id` - The allowed value id
  //   of the environment attribute associated with the Deployment is
  //   _staging-id_.
  //   * `environment.enum_values.values.display_name: \"Staging Deployment\"` -
  //   The allowed value display name of the environment attribute associated
  //   with the Deployment is `Staging Deployment`.
  //   * `environment.enum_values.values.id: production-id AND create_time <
  //   \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
  //   The allowed value id of the environment attribute associated with the
  //   Deployment is _production-id_ and Deployment was created before
  //   _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_.
  //   * `environment.enum_values.values.id: production-id OR
  //   slo.string_values.values: \"99.99%\"`
  //   - The allowed value id of the environment attribute Deployment is
  //   _production-id_ or string value of the slo attribute is _99.99%_.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of deployment resources to return. The service
  // may return fewer than this value. If unspecified, at most 50 deployments
  // will be returned. The maximum value is 1000; values above 1000 will be
  // coerced to 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListDeployments` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters (except page_size) provided to
  // `ListDeployments` must match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListDeployments][google.cloud.apihub.v1.ApiHub.ListDeployments] method's
// response.
message ListDeploymentsResponse {
  // The deployment resources present in the API hub.
  repeated Deployment deployments = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The [CreateAttribute][google.cloud.apihub.v1.ApiHub.CreateAttribute] method's
// request.
message CreateAttributeRequest {
  // Required. The parent resource for Attribute.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Attribute"
    }
  ];

  // Optional. The ID to use for the attribute, which will become the final
  // component of the attribute's resource name. This field is optional.
  //
  // * If provided, the same will be used. The service will throw an error if
  // the specified id is already used by another attribute resource in the API
  // hub.
  // * If not provided, a system generated id will be used.
  //
  // This value should be 4-500 characters, and valid characters
  // are /[a-z][A-Z][0-9]-_/.
  string attribute_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The attribute to create.
  Attribute attribute = 3 [(google.api.field_behavior) = REQUIRED];
}

// The [GetAttribute][google.cloud.apihub.v1.ApiHub.GetAttribute] method's
// request.
message GetAttributeRequest {
  // Required. The name of the attribute to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/attributes/{attribute}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Attribute"
    }
  ];
}

// The [UpdateAttribute][google.cloud.apihub.v1.ApiHub.UpdateAttribute] method's
// request.
message UpdateAttributeRequest {
  // Required. The attribute to update.
  //
  // The attribute's `name` field is used to identify the attribute to update.
  // Format:
  // `projects/{project}/locations/{location}/attributes/{attribute}`
  Attribute attribute = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The [DeleteAttribute][google.cloud.apihub.v1.ApiHub.DeleteAttribute] method's
// request.
message DeleteAttributeRequest {
  // Required. The name of the attribute to delete.
  // Format:
  // `projects/{project}/locations/{location}/attributes/{attribute}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Attribute"
    }
  ];
}

// The [ListAttributes][google.cloud.apihub.v1.ApiHub.ListAttributes] method's
// request.
message ListAttributesRequest {
  // Required. The parent resource for Attribute.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Attribute"
    }
  ];

  // Optional. An expression that filters the list of Attributes.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string or a
  // boolean. The comparison operator must be one of: `<`, `>` or
  // `=`. Filters are not case sensitive.
  //
  // The following fields in the `Attribute` are eligible for filtering:
  //
  //   * `display_name` - The display name of the Attribute. Allowed
  //   comparison operators: `=`.
  //   * `definition_type` - The definition type of the attribute. Allowed
  //   comparison operators: `=`.
  //   * `scope` - The scope of the attribute. Allowed comparison operators:
  //   `=`.
  //   * `data_type` - The type of the data of the attribute. Allowed
  //   comparison operators: `=`.
  //   * `mandatory` - Denotes whether the attribute is mandatory or not.
  //   Allowed comparison operators: `=`.
  //   * `create_time` - The time at which the Attribute was created. The
  //   value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339]
  //   format. Allowed comparison operators: `>` and `<`.
  //
  // Expressions are combined with either `AND` logic operator or `OR` logical
  // operator but not both of them together i.e. only one of the `AND` or `OR`
  // operator can be used throughout the filter string and both the operators
  // cannot be used together. No other logical operators are
  // supported. At most three filter fields are allowed in the filter
  // string and if provided more than that then `INVALID_ARGUMENT` error is
  // returned by the API.
  //
  // Here are a few examples:
  //
  //   * `display_name = production` -  - The display name of the attribute is
  //   _production_.
  //   * `(display_name = production) AND (create_time <
  //   \"2021-08-15T14:50:00Z\") AND (create_time > \"2021-08-10T12:00:00Z\")` -
  //   The display name of the attribute is _production_ and the attribute was
  //   created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00
  //   UTC_.
  //   * `display_name = production OR scope = api` -
  //   The attribute where the display name is _production_ or the scope is
  //   _api_.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of attribute resources to return. The service
  // may return fewer than this value. If unspecified, at most 50 attributes
  // will be returned. The maximum value is 1000; values above 1000 will be
  // coerced to 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListAttributes` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAttributes` must
  // match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListAttributes][google.cloud.apihub.v1.ApiHub.ListAttributes] method's
// response.
message ListAttributesResponse {
  // The list of all attributes.
  repeated Attribute attributes = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The [SearchResources][google.cloud.apihub.v1.ApiHub.SearchResources] method's
// request.
message SearchResourcesRequest {
  // Required. The resource name of the location which will be of the type
  // `projects/{project_id}/locations/{location_id}`. This field is used to
  // identify the instance of API-Hub in which resources should be searched.
  string location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The free text search query. This query can contain keywords which
  // could be related to any detail of the API-Hub resources such display names,
  // descriptions, attributes etc.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An expression that filters the list of search results.
  //
  // A filter expression consists of a field name, a comparison operator,
  // and a value for filtering. The value must be a string, a number, or a
  // boolean. The comparison operator must be `=`. Filters are not case
  // sensitive.
  //
  // The following field names are eligible for filtering:
  //    * `resource_type` - The type of resource in the search results.
  //    Must be one of the following: `Api`, `ApiOperation`, `Deployment`,
  //    `Definition`, `Spec` or `Version`. This field can only be specified once
  //    in the filter.
  //
  // Here are is an example:
  //
  //   * `resource_type = Api` - The resource_type is _Api_.
  string filter = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of search results to return. The service may
  // return fewer than this value. If unspecified at most 10 search results will
  // be returned. If value is negative then `INVALID_ARGUMENT` error is
  // returned. The maximum value is 25; values above 25 will be coerced to 25.
  // While paginating, you can specify a new page size parameter for each page
  // of search results to be listed.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // [SearchResources][SearchResources]
  // call. Specify this parameter to retrieve the next page of transactions.
  //
  // When paginating, you must specify the `page_token` parameter and all the
  // other parameters except
  // [page_size][google.cloud.apihub.v1.SearchResourcesRequest.page_size]
  // should be specified with the same value which was used in the previous
  // call. If the other fields are set with a different value than the previous
  // call then `INVALID_ARGUMENT` error is returned.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];
}

// ApiHubResource is one of the resources such as Api, Operation, Deployment,
// Definition, Spec and Version resources stored in API-Hub.
message ApiHubResource {
  oneof resource {
    // This represents Api resource in search results. Only name, display_name,
    // description and owner fields are populated in search results.
    Api api = 1;

    // This represents ApiOperation resource in search results. Only name,
    // and description fields are populated in search results.
    ApiOperation operation = 2;

    // This represents Deployment resource in search results. Only name,
    // display_name and description fields are populated in search results.
    Deployment deployment = 3;

    // This represents Spec resource in search results. Only name,
    // display_name and description fields are populated in search results.
    Spec spec = 4;

    // This represents Definition resource in search results.
    // Only name field is populated in search results.
    Definition definition = 5;

    // This represents Version resource in search results. Only name,
    // display_name and description fields are populated in search results.
    Version version = 6;
  }
}

// Represents the search results.
message SearchResult {
  // This represents the ApiHubResource.
  // Note: Only selected fields of the resources are populated in response.
  ApiHubResource resource = 1;
}

// Response for the
// [SearchResources][google.cloud.apihub.v1.ApiHub.SearchResources] method.
message SearchResourcesResponse {
  // List of search results according to the filter and search query specified.
  // The order of search results represents the ranking.
  repeated SearchResult search_results = 1;

  // Pass this token in the
  // [SearchResourcesRequest][google.cloud.apihub.v1.SearchResourcesRequest]
  // to continue to list results. If all results have been returned, this field
  // is an empty string or not present in the response.
  string next_page_token = 2;
}

// The
// [CreateDependency][google.cloud.apihub.v1.ApiHubDependencies.CreateDependency]
// method's request.
message CreateDependencyRequest {
  // Required. The parent resource for the dependency resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Dependency"
    }
  ];

  // Optional. The ID to use for the dependency resource, which will become the
  // final component of the dependency's resource name. This field is optional.
  // * If provided, the same will be used. The service will throw an error if
  // duplicate id is provided by the client.
  // * If not provided, a system generated id will be used.
  //
  // This value should be 4-500 characters, and valid characters
  // are `[a-z][A-Z][0-9]-_`.
  string dependency_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The dependency resource to create.
  Dependency dependency = 3 [(google.api.field_behavior) = REQUIRED];
}

// The [GetDependency][.ApiHubDependencies.GetDependency]
// method's request.
message GetDependencyRequest {
  // Required. The name of the dependency resource to retrieve.
  // Format: `projects/{project}/locations/{location}/dependencies/{dependency}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Dependency"
    }
  ];
}

// The
// [UpdateDependency][google.cloud.apihub.v1.ApiHubDependencies.UpdateDependency]
// method's request.
message UpdateDependencyRequest {
  // Required. The dependency resource to update.
  //
  // The dependency's `name` field is used to identify the dependency to update.
  // Format: `projects/{project}/locations/{location}/dependencies/{dependency}`
  Dependency dependency = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The
// [DeleteDependency][google.cloud.apihub.v1.ApiHubDependencies.DeleteDependency]
// method's request.
message DeleteDependencyRequest {
  // Required. The name of the dependency resource to delete.
  // Format: `projects/{project}/locations/{location}/dependencies/{dependency}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/Dependency"
    }
  ];
}

// The
// [ListDependencies][google.cloud.apihub.v1.ApiHubDependencies.ListDependencies]
// method's request.
message ListDependenciesRequest {
  // Required. The parent which owns this collection of dependency resources.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/Dependency"
    }
  ];

  // Optional. An expression that filters the list of Dependencies.
  //
  // A filter expression consists of a field name, a comparison operator, and
  // a value for filtering. The value must be a string. Allowed comparison
  // operator is `=`. Filters are not case sensitive.
  //
  // The following fields in the `Dependency` are eligible for filtering:
  //
  //   * `consumer.operation_resource_name` - The operation resource name for
  //   the consumer entity involved in a dependency. Allowed comparison
  //   operators: `=`.
  //   * `consumer.external_api_resource_name` - The external api resource name
  //   for the consumer entity involved in a dependency. Allowed comparison
  //   operators: `=`.
  //   * `supplier.operation_resource_name` - The operation resource name for
  //   the supplier entity involved in a dependency. Allowed comparison
  //   operators: `=`.
  //   * `supplier.external_api_resource_name` - The external api resource name
  //   for the supplier entity involved in a dependency. Allowed comparison
  //   operators: `=`.
  //
  // Expressions are combined with either `AND` logic operator or `OR` logical
  // operator but not both of them together i.e. only one of the `AND` or `OR`
  // operator can be used throughout the filter string and both the operators
  // cannot be used together. No other logical operators are supported. At most
  // three filter fields are allowed in the filter string and if provided
  // more than that then `INVALID_ARGUMENT` error is returned by the API.
  //
  // For example, `consumer.operation_resource_name =
  // \"projects/p1/locations/global/apis/a1/versions/v1/operations/o1\" OR
  // supplier.operation_resource_name =
  // \"projects/p1/locations/global/apis/a1/versions/v1/operations/o1\"` - The
  // dependencies with either consumer or supplier operation resource name as
  // _projects/p1/locations/global/apis/a1/versions/v1/operations/o1_.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of dependency resources to return. The service
  // may return fewer than this value. If unspecified, at most 50 dependencies
  // will be returned. The maximum value is 1000; values above 1000 will be
  // coerced to 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListDependencies` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDependencies` must
  // match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The
// [ListDependencies][google.cloud.apihub.v1.ApiHubDependencies.ListDependencies]
// method's response.
message ListDependenciesResponse {
  // The dependency resources present in the API hub.
  // Only following field will be populated in the response: name.
  repeated Dependency dependencies = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The [CreateExternalApi][google.cloud.apihub.v1.ApiHub.CreateExternalApi]
// method's request.
message CreateExternalApiRequest {
  // Required. The parent resource for the External API resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/ExternalApi"
    }
  ];

  // Optional. The ID to use for the External API resource, which will become
  // the final component of the External API's resource name. This field is
  // optional.
  //
  // * If provided, the same will be used. The service will throw an error if
  // the specified id is already used by another External API resource in the
  // API hub.
  // * If not provided, a system generated id will be used.
  //
  // This value should be 4-500 characters, and valid characters
  // are /[a-z][A-Z][0-9]-_/.
  string external_api_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The External API resource to create.
  ExternalApi external_api = 3 [(google.api.field_behavior) = REQUIRED];
}

// The [GetExternalApi][google.cloud.apihub.v1.ApiHub.GetExternalApi] method's
// request.
message GetExternalApiRequest {
  // Required. The name of the External API resource to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/externalApis/{externalApi}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/ExternalApi"
    }
  ];
}

// The [UpdateExternalApi][google.cloud.apihub.v1.ApiHub.UpdateExternalApi]
// method's request.
message UpdateExternalApiRequest {
  // Required. The External API resource to update.
  //
  // The External API resource's `name` field is used to identify the External
  // API resource to update. Format:
  // `projects/{project}/locations/{location}/externalApis/{externalApi}`
  ExternalApi external_api = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The [DeleteExternalApi][google.cloud.apihub.v1.ApiHub.DeleteExternalApi]
// method's request.
message DeleteExternalApiRequest {
  // Required. The name of the External API resource to delete.
  // Format:
  // `projects/{project}/locations/{location}/externalApis/{externalApi}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/ExternalApi"
    }
  ];
}

// The [ListExternalApis][google.cloud.apihub.v1.ApiHub.ListExternalApis]
// method's request.
message ListExternalApisRequest {
  // Required. The parent, which owns this collection of External API resources.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/ExternalApi"
    }
  ];

  // Optional. The maximum number of External API resources to return. The
  // service may return fewer than this value. If unspecified, at most 50
  // ExternalApis will be returned. The maximum value is 1000; values above 1000
  // will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListExternalApis` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters (except page_size) provided to
  // `ListExternalApis` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The [ListExternalApis][google.cloud.apihub.v1.ApiHub.ListExternalApis]
// method's response.
message ListExternalApisResponse {
  // The External API resources present in the API hub.
  // Only following fields will be populated in the response: name,
  // display_name, documentation.external_uri.
  repeated ExternalApi external_apis = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
