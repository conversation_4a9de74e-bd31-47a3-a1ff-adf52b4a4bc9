// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apihub.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/apihub/v1/common_fields.proto";

option csharp_namespace = "Google.Cloud.ApiHub.V1";
option go_package = "cloud.google.com/go/apihub/apiv1/apihubpb;apihubpb";
option java_multiple_files = true;
option java_outer_classname = "PluginServiceProto";
option java_package = "com.google.cloud.apihub.v1";
option php_namespace = "Google\\Cloud\\ApiHub\\V1";
option ruby_package = "Google::Cloud::ApiHub::V1";

// This service is used for managing plugins inside the API Hub.
service ApiHubPlugin {
  option (google.api.default_host) = "apihub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Get details about an API Hub plugin.
  rpc GetPlugin(GetPluginRequest) returns (Plugin) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/plugins/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Enables a plugin.
  // The `state` of the plugin after enabling is `ENABLED`
  rpc EnablePlugin(EnablePluginRequest) returns (Plugin) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/plugins/*}:enable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Disables a plugin.
  // The `state` of the plugin after disabling is `DISABLED`
  rpc DisablePlugin(DisablePluginRequest) returns (Plugin) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/plugins/*}:disable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// A plugin resource in the API Hub.
message Plugin {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/Plugin"
    pattern: "projects/{project}/locations/{location}/plugins/{plugin}"
    plural: "plugins"
    singular: "plugin"
  };

  // Possible states a plugin can have. Note that this enum may receive new
  // values in the future. Consumers are advised to always code against the
  // enum values expecting new states can be added later on.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The plugin is enabled.
    ENABLED = 1;

    // The plugin is disabled.
    DISABLED = 2;
  }

  // Identifier. The name of the plugin.
  // Format: `projects/{project}/locations/{location}/plugins/{plugin}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The display name of the plugin. Max length is 50 characters
  // (Unicode code points).
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the API.
  // This maps to the following system defined attribute:
  // `projects/{project}/locations/{location}/attributes/system-plugin-type`
  // attribute.
  // The number of allowed values for this attribute will be based on the
  // cardinality of the attribute. The same can be retrieved via GetAttribute
  // API. All values should be from the list of allowed values defined for the
  // attribute.
  AttributeValues type = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The plugin description. Max length is 2000 characters (Unicode
  // code points).
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Represents the state of the plugin.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The [GetPlugin][google.cloud.apihub.v1.ApiHubPlugin.GetPlugin] method's
// request.
message GetPluginRequest {
  // Required. The name of the plugin to retrieve.
  // Format: `projects/{project}/locations/{location}/plugins/{plugin}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Plugin" }
  ];
}

// The [EnablePlugin][google.cloud.apihub.v1.ApiHubPlugin.EnablePlugin] method's
// request.
message EnablePluginRequest {
  // Required. The name of the plugin to enable.
  // Format: `projects/{project}/locations/{location}/plugins/{plugin}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Plugin" }
  ];
}

// The [DisablePlugin][google.cloud.apihub.v1.ApiHubPlugin.DisablePlugin]
// method's request.
message DisablePluginRequest {
  // Required. The name of the plugin to disable.
  // Format: `projects/{project}/locations/{location}/plugins/{plugin}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Plugin" }
  ];
}
