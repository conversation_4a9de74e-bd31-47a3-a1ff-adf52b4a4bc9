// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apihub.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/apihub/v1/common_fields.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.ApiHub.V1";
option go_package = "cloud.google.com/go/apihub/apiv1/apihubpb;apihubpb";
option java_multiple_files = true;
option java_outer_classname = "LintingServiceProto";
option java_package = "com.google.cloud.apihub.v1";
option php_namespace = "Google\\Cloud\\ApiHub\\V1";
option ruby_package = "Google::Cloud::ApiHub::V1";

// This service provides all methods related to the 1p Linter.
service LintingService {
  option (google.api.default_host) = "apihub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Get the style guide being used for linting.
  rpc GetStyleGuide(GetStyleGuideRequest) returns (StyleGuide) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/plugins/*/styleGuide}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update the styleGuide to be used for liniting in by API hub.
  rpc UpdateStyleGuide(UpdateStyleGuideRequest) returns (StyleGuide) {
    option (google.api.http) = {
      patch: "/v1/{style_guide.name=projects/*/locations/*/plugins/*/styleGuide}"
      body: "style_guide"
    };
    option (google.api.method_signature) = "style_guide,update_mask";
  }

  // Get the contents of the style guide.
  rpc GetStyleGuideContents(GetStyleGuideContentsRequest)
      returns (StyleGuideContents) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/plugins/*/styleGuide}:contents"
    };
    option (google.api.method_signature) = "name";
  }

  // Lints the requested spec and updates the corresponding API Spec with the
  // lint response. This lint response will be available in all subsequent
  // Get and List Spec calls to Core service.
  rpc LintSpec(LintSpecRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/apis/*/versions/*/specs/*}:lint"
      body: "*"
    };
  }
}

// The [GetStyleGuide][ApiHub.GetStyleGuide] method's request.
message GetStyleGuideRequest {
  // Required. The name of the spec to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/StyleGuide"
    }
  ];
}

// The [UpdateStyleGuide][ApiHub.UpdateStyleGuide] method's request.
message UpdateStyleGuideRequest {
  // Required. The Style guide resource to update.
  StyleGuide style_guide = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// The [GetStyleGuideContents][ApiHub.GetStyleGuideContents] method's request.
message GetStyleGuideContentsRequest {
  // Required. The name of the StyleGuide whose contents need to be retrieved.
  // There is exactly one style guide resource per project per location.
  // The expected format is
  // `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/StyleGuide"
    }
  ];
}

// The [LintSpec][ApiHub.LintSpec] method's request.
message LintSpecRequest {
  // Required. The name of the spec to be linted.
  // Format:
  // `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "apihub.googleapis.com/Spec" }
  ];
}

// The style guide contents.
message StyleGuideContents {
  // Required. The contents of the style guide.
  bytes contents = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The mime type of the content.
  string mime_type = 2 [(google.api.field_behavior) = REQUIRED];
}

// Represents a singleton style guide resource to be used for linting Open API
// specs.
message StyleGuide {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/StyleGuide"
    pattern: "projects/{project}/locations/{location}/plugins/{plugin}/styleGuide"
    plural: "styleGuides"
    singular: "styleGuide"
  };

  // Identifier. The name of the style guide.
  //
  // Format:
  // `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Target linter for the style guide.
  Linter linter = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Input only. The contents of the uploaded style guide.
  StyleGuideContents contents = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = INPUT_ONLY
  ];
}
