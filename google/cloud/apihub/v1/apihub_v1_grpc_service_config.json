{"methodConfig": [{"name": [{"service": "google.cloud.apihub.v1.ApiHub", "method": "GetApi"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "ListApis"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "GetVersion"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "ListVersions"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "GetSpec"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "GetSpecContents"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "ListSpecs"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "GetApiOperation"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "ListApiOperations"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "GetDefinition"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "GetDeployment"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "ListDeployments"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "GetAttribute"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "ListAttributes"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "SearchResources"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "GetExternalApi"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "ListExternalApis"}, {"service": "google.cloud.apihub.v1.ApiHubDependencies", "method": "GetDependency"}, {"service": "google.cloud.apihub.v1.ApiHubDependencies", "method": "ListDependencies"}, {"service": "google.cloud.apihub.v1.HostProjectRegistrationService", "method": "GetHostProjectRegistration"}, {"service": "google.cloud.apihub.v1.HostProjectRegistrationService", "method": "ListHostProjectRegistrations"}, {"service": "google.cloud.apihub.v1.LintingService", "method": "GetStyleGuide"}, {"service": "google.cloud.apihub.v1.LintingService", "method": "GetStyleGuideContents"}, {"service": "google.cloud.apihub.v1.ApiHubPlugin", "method": "GetPlugin"}, {"service": "google.cloud.apihub.v1.Provisioning", "method": "GetApiHubInstance"}, {"service": "google.cloud.apihub.v1.Provisioning", "method": "LookupApiHubInstance"}, {"service": "google.cloud.apihub.v1.RuntimeProjectAttachmentService", "method": "GetRuntimeProjectAttachment"}, {"service": "google.cloud.apihub.v1.RuntimeProjectAttachmentService", "method": "ListRuntimeProjectAttachments"}, {"service": "google.cloud.apihub.v1.RuntimeProjectAttachmentService", "method": "LookupRuntimeProjectAttachment"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.apihub.v1.ApiHub", "method": "CreateApi"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "UpdateA<PERSON>"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "DeleteApi"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "CreateVersion"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "UpdateVersion"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "DeleteVersion"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "CreateSpec"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "UpdateSpec"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "DeleteSpec"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "CreateDeployment"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "UpdateDeployment"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "DeleteDeployment"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "CreateAttribute"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "UpdateAttribute"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "DeleteAttribute"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "CreateExternalApi"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "UpdateExternalApi"}, {"service": "google.cloud.apihub.v1.ApiHub", "method": "DeleteExternalApi"}, {"service": "google.cloud.apihub.v1.ApiHubDependencies", "method": "CreateDependency"}, {"service": "google.cloud.apihub.v1.ApiHubDependencies", "method": "UpdateDependency"}, {"service": "google.cloud.apihub.v1.ApiHubDependencies", "method": "DeleteDependency"}, {"service": "google.cloud.apihub.v1.HostProjectRegistrationService", "method": "CreateHostProjectRegistration"}, {"service": "google.cloud.apihub.v1.LintingService", "method": "UpdateStyleGuide"}, {"service": "google.cloud.apihub.v1.LintingService", "method": "LintSpec"}, {"service": "google.cloud.apihub.v1.ApiHubPlugin", "method": "EnablePlugin"}, {"service": "google.cloud.apihub.v1.ApiHubPlugin", "method": "DisablePlugin"}, {"service": "google.cloud.apihub.v1.Provisioning", "method": "CreateApiHubInstance"}, {"service": "google.cloud.apihub.v1.RuntimeProjectAttachmentService", "method": "CreateRuntimeProjectAttachment"}, {"service": "google.cloud.apihub.v1.RuntimeProjectAttachmentService", "method": "DeleteRuntimeProjectAttachment"}], "timeout": "60s"}]}