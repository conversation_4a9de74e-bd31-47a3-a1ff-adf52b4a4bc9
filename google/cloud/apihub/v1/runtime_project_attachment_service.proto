// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apihub.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ApiHub.V1";
option go_package = "cloud.google.com/go/apihub/apiv1/apihubpb;apihubpb";
option java_multiple_files = true;
option java_outer_classname = "RuntimeProjectAttachmentServiceProto";
option java_package = "com.google.cloud.apihub.v1";
option php_namespace = "Google\\Cloud\\ApiHub\\V1";
option ruby_package = "Google::Cloud::ApiHub::V1";

// This service is used for managing the runtime project attachments.
service RuntimeProjectAttachmentService {
  option (google.api.default_host) = "apihub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Attaches a runtime project to the host project.
  rpc CreateRuntimeProjectAttachment(CreateRuntimeProjectAttachmentRequest)
      returns (RuntimeProjectAttachment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/runtimeProjectAttachments"
      body: "runtime_project_attachment"
    };
    option (google.api.method_signature) =
        "parent,runtime_project_attachment,runtime_project_attachment_id";
  }

  // Gets a runtime project attachment.
  rpc GetRuntimeProjectAttachment(GetRuntimeProjectAttachmentRequest)
      returns (RuntimeProjectAttachment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/runtimeProjectAttachments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List runtime projects attached to the host project.
  rpc ListRuntimeProjectAttachments(ListRuntimeProjectAttachmentsRequest)
      returns (ListRuntimeProjectAttachmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/runtimeProjectAttachments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Delete a runtime project attachment in the API Hub. This call will detach
  // the runtime project from the host project.
  rpc DeleteRuntimeProjectAttachment(DeleteRuntimeProjectAttachmentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/runtimeProjectAttachments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Look up a runtime project attachment. This API can be called in the context
  // of any project.
  rpc LookupRuntimeProjectAttachment(LookupRuntimeProjectAttachmentRequest)
      returns (LookupRuntimeProjectAttachmentResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*}:lookupRuntimeProjectAttachment"
    };
    option (google.api.method_signature) = "name";
  }
}

// The
// [CreateRuntimeProjectAttachment][google.cloud.apihub.v1.RuntimeProjectAttachmentService.CreateRuntimeProjectAttachment]
// method's request.
message CreateRuntimeProjectAttachmentRequest {
  // Required. The parent resource for the Runtime Project Attachment.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/RuntimeProjectAttachment"
    }
  ];

  // Required. The ID to use for the Runtime Project Attachment, which will
  // become the final component of the Runtime Project Attachment's name. The ID
  // must be the same as the project ID of the Google cloud project specified in
  // the runtime_project_attachment.runtime_project field.
  string runtime_project_attachment_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The Runtime Project Attachment to create.
  RuntimeProjectAttachment runtime_project_attachment = 3
      [(google.api.field_behavior) = REQUIRED];
}

// The
// [GetRuntimeProjectAttachment][google.cloud.apihub.v1.RuntimeProjectAttachmentService.GetRuntimeProjectAttachment]
// method's request.
message GetRuntimeProjectAttachmentRequest {
  // Required. The name of the API resource to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/runtimeProjectAttachments/{runtime_project_attachment}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/RuntimeProjectAttachment"
    }
  ];
}

// The
// [ListRuntimeProjectAttachments][google.cloud.apihub.v1.RuntimeProjectAttachmentService.ListRuntimeProjectAttachments]
// method's request.
message ListRuntimeProjectAttachmentsRequest {
  // Required. The parent, which owns this collection of runtime project
  // attachments. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/RuntimeProjectAttachment"
    }
  ];

  // Optional. The maximum number of runtime project attachments to return. The
  // service may return fewer than this value. If unspecified, at most 50
  // runtime project attachments will be returned. The maximum value is 1000;
  // values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListRuntimeProjectAttachments` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all other parameters (except page_size) provided to
  // `ListRuntimeProjectAttachments` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression that filters the list of RuntimeProjectAttachments.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string. All
  // standard operators as documented at https://google.aip.dev/160 are
  // supported.
  //
  // The following fields in the `RuntimeProjectAttachment` are eligible for
  // filtering:
  //
  //   * `name` - The name of the RuntimeProjectAttachment.
  //   * `create_time` - The time at which the RuntimeProjectAttachment was
  //   created. The value should be in the
  //   (RFC3339)[https://tools.ietf.org/html/rfc3339] format.
  //   * `runtime_project` - The Google cloud project associated with the
  //   RuntimeProjectAttachment.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The
// [ListRuntimeProjectAttachments][google.cloud.apihub.v1.RuntimeProjectAttachmentService.ListRuntimeProjectAttachments]
// method's response.
message ListRuntimeProjectAttachmentsResponse {
  // List of runtime project attachments.
  repeated RuntimeProjectAttachment runtime_project_attachments = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The
// [DeleteRuntimeProjectAttachment][google.cloud.apihub.v1.RuntimeProjectAttachmentService.DeleteRuntimeProjectAttachment]
// method's request.
message DeleteRuntimeProjectAttachmentRequest {
  // Required. The name of the Runtime Project Attachment to delete.
  // Format:
  // `projects/{project}/locations/{location}/runtimeProjectAttachments/{runtime_project_attachment}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/RuntimeProjectAttachment"
    }
  ];
}

// The
// [LookupRuntimeProjectAttachment][google.cloud.apihub.v1.RuntimeProjectAttachmentService.LookupRuntimeProjectAttachment]
// method's request.
message LookupRuntimeProjectAttachmentRequest {
  // Required. Runtime project ID to look up runtime project attachment for.
  // Lookup happens across all regions. Expected format:
  // `projects/{project}/locations/{location}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// The
// [ListRuntimeProjectAttachments][google.cloud.apihub.v1.RuntimeProjectAttachmentService.ListRuntimeProjectAttachments]
// method's response.
message LookupRuntimeProjectAttachmentResponse {
  // Runtime project attachment for a project if exists, empty otherwise.
  RuntimeProjectAttachment runtime_project_attachment = 1;
}

// Runtime project attachment represents an attachment from the runtime project
// to the host project. Api Hub looks for deployments in the attached runtime
// projects and creates corresponding resources in Api Hub for the discovered
// deployments.
message RuntimeProjectAttachment {
  option (google.api.resource) = {
    type: "apihub.googleapis.com/RuntimeProjectAttachment"
    pattern: "projects/{project}/locations/{location}/runtimeProjectAttachments/{runtime_project_attachment}"
    plural: "runtimeProjectAttachments"
    singular: "runtimeProjectAttachment"
  };

  // Identifier. The resource name of a runtime project attachment. Format:
  // "projects/{project}/locations/{location}/runtimeProjectAttachments/{runtime_project_attachment}".
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Immutable. Google cloud project name in the format:
  // "projects/abc" or "projects/123". As input, project name with either
  // project id or number are accepted. As output, this field will contain
  // project number.
  string runtime_project = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Output only. Create time.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
