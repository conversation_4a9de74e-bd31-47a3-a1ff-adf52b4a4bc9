// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apihub.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/apihub/v1/common_fields.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.ApiHub.V1";
option go_package = "cloud.google.com/go/apihub/apiv1/apihubpb;apihubpb";
option java_multiple_files = true;
option java_outer_classname = "ProvisioningServiceProto";
option java_package = "com.google.cloud.apihub.v1";
option php_namespace = "Google\\Cloud\\ApiHub\\V1";
option ruby_package = "Google::Cloud::ApiHub::V1";

// This service is used for managing the data plane provisioning of the API hub.
service Provisioning {
  option (google.api.default_host) = "apihub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Provisions instance resources for the API Hub.
  rpc CreateApiHubInstance(CreateApiHubInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/apiHubInstances"
      body: "api_hub_instance"
    };
    option (google.api.method_signature) =
        "parent,api_hub_instance,api_hub_instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "ApiHubInstance"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single API Hub instance.
  rpc GetApiHubInstance(GetApiHubInstanceRequest) returns (ApiHubInstance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apiHubInstances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Looks up an Api Hub instance in a given GCP project. There will always be
  // only one Api Hub instance for a GCP project across all locations.
  rpc LookupApiHubInstance(LookupApiHubInstanceRequest)
      returns (LookupApiHubInstanceResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/apiHubInstances:lookup"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The
// [CreateApiHubInstance][google.cloud.apihub.v1.Provisioning.CreateApiHubInstance]
// method's request.
message CreateApiHubInstanceRequest {
  // Required. The parent resource for the Api Hub instance resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Identifier to assign to the Api Hub instance. Must be unique
  // within scope of the parent resource. If the field is not provided, system
  // generated id will be used.
  //
  // This value should be 4-40 characters, and valid characters
  // are `/[a-z][A-Z][0-9]-_/`.
  string api_hub_instance_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The ApiHub instance.
  ApiHubInstance api_hub_instance = 3 [(google.api.field_behavior) = REQUIRED];
}

// The
// [GetApiHubInstance][google.cloud.apihub.v1.Provisioning.GetApiHubInstance]
// method's request.
message GetApiHubInstanceRequest {
  // Required. The name of the Api Hub instance to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/apiHubInstances/{apiHubInstance}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apihub.googleapis.com/ApiHubInstance"
    }
  ];
}

// The
// [LookupApiHubInstance][google.cloud.apihub.v1.Provisioning.LookupApiHubInstance]
// method's request.
message LookupApiHubInstanceRequest {
  // Required. There will always be only one Api Hub instance for a GCP project
  // across all locations.
  // The parent resource for the Api Hub instance resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "apihub.googleapis.com/ApiHubInstance"
    }
  ];
}

// The
// [LookupApiHubInstance][google.cloud.apihub.v1.Provisioning.LookupApiHubInstance]
// method's response.`
message LookupApiHubInstanceResponse {
  // API Hub instance for a project if it exists, empty otherwise.
  ApiHubInstance api_hub_instance = 1;
}
