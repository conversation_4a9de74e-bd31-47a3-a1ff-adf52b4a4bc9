// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.pubsublite.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/pubsublite/v1/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.PubSubLite.V1";
option go_package = "cloud.google.com/go/pubsublite/apiv1/pubsublitepb;pubsublitepb";
option java_multiple_files = true;
option java_outer_classname = "TopicStatsProto";
option java_package = "com.google.cloud.pubsublite.proto";
option php_namespace = "Google\\Cloud\\PubSubLite\\V1";
option ruby_package = "Google::Cloud::PubSubLite::V1";

// This service allows users to get stats about messages in their topic.
service TopicStatsService {
  option (google.api.default_host) = "pubsublite.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Compute statistics about a range of messages in a given topic and
  // partition.
  rpc ComputeMessageStats(ComputeMessageStatsRequest)
      returns (ComputeMessageStatsResponse) {
    option (google.api.http) = {
      post: "/v1/topicStats/{topic=projects/*/locations/*/topics/*}:computeMessageStats"
      body: "*"
    };
  }

  // Compute the head cursor for the partition.
  // The head cursor's offset is guaranteed to be less than or equal to all
  // messages which have not yet been acknowledged as published, and
  // greater than the offset of any message whose publish has already
  // been acknowledged. It is zero if there have never been messages in the
  // partition.
  rpc ComputeHeadCursor(ComputeHeadCursorRequest)
      returns (ComputeHeadCursorResponse) {
    option (google.api.http) = {
      post: "/v1/topicStats/{topic=projects/*/locations/*/topics/*}:computeHeadCursor"
      body: "*"
    };
  }

  // Compute the corresponding cursor for a publish or event time in a topic
  // partition.
  rpc ComputeTimeCursor(ComputeTimeCursorRequest)
      returns (ComputeTimeCursorResponse) {
    option (google.api.http) = {
      post: "/v1/topicStats/{topic=projects/*/locations/*/topics/*}:computeTimeCursor"
      body: "*"
    };
  }
}

// Compute statistics about a range of messages in a given topic and partition.
message ComputeMessageStatsRequest {
  // Required. The topic for which we should compute message stats.
  string topic = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Topic"
    }
  ];

  // Required. The partition for which we should compute message stats.
  int64 partition = 2 [(google.api.field_behavior) = REQUIRED];

  // The inclusive start of the range.
  Cursor start_cursor = 3;

  // The exclusive end of the range. The range is empty if end_cursor <=
  // start_cursor. Specifying a start_cursor before the first message and an
  // end_cursor after the last message will retrieve all messages.
  Cursor end_cursor = 4;
}

// Response containing stats for messages in the requested topic and partition.
message ComputeMessageStatsResponse {
  // The count of messages.
  int64 message_count = 1;

  // The number of quota bytes accounted to these messages.
  int64 message_bytes = 2;

  // The minimum publish timestamp across these messages. Note that publish
  // timestamps within a partition are not guaranteed to be non-decreasing. The
  // timestamp will be unset if there are no messages.
  google.protobuf.Timestamp minimum_publish_time = 3;

  // The minimum event timestamp across these messages. For the purposes of this
  // computation, if a message does not have an event time, we use the publish
  // time. The timestamp will be unset if there are no messages.
  google.protobuf.Timestamp minimum_event_time = 4;
}

// Compute the current head cursor for a partition.
message ComputeHeadCursorRequest {
  // Required. The topic for which we should compute the head cursor.
  string topic = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Topic"
    }
  ];

  // Required. The partition for which we should compute the head cursor.
  int64 partition = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response containing the head cursor for the requested topic and partition.
message ComputeHeadCursorResponse {
  // The head cursor.
  Cursor head_cursor = 1;
}

// Compute the corresponding cursor for a publish or event time in a topic
// partition.
message ComputeTimeCursorRequest {
  // Required. The topic for which we should compute the cursor.
  string topic = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Topic"
    }
  ];

  // Required. The partition for which we should compute the cursor.
  int64 partition = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The target publish or event time. Specifying a future time will
  // return an unset cursor.
  TimeTarget target = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response containing the cursor corresponding to a publish or event time in a
// topic partition.
message ComputeTimeCursorResponse {
  // If present, the cursor references the first message with time greater than
  // or equal to the specified target time. If such a message cannot be found,
  // the cursor will be unset (i.e. `cursor` is not present).
  Cursor cursor = 1;
}
