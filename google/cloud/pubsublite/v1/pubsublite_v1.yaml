type: google.api.Service
config_version: 3
name: pubsublite.googleapis.com
title: Pub/Sub Lite API

apis:
- name: google.cloud.pubsublite.v1.AdminService
- name: google.cloud.pubsublite.v1.CursorService
- name: google.cloud.pubsublite.v1.PartitionAssignmentService
- name: google.cloud.pubsublite.v1.PublisherService
- name: google.cloud.pubsublite.v1.SubscriberService
- name: google.cloud.pubsublite.v1.TopicStatsService
- name: google.longrunning.Operations

types:
- name: google.cloud.pubsublite.v1.OperationMetadata
- name: google.cloud.pubsublite.v1.SeekSubscriptionResponse

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/admin/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/admin/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/admin/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/admin/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.pubsublite.v1.AdminService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.pubsublite.v1.CursorService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.pubsublite.v1.PartitionAssignmentService.AssignPartitions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.pubsublite.v1.PublisherService.Publish
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.pubsublite.v1.SubscriberService.Subscribe
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.pubsublite.v1.TopicStatsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
