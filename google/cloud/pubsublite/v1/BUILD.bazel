# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "pubsublite_proto",
    srcs = [
        "admin.proto",
        "common.proto",
        "cursor.proto",
        "publisher.proto",
        "subscriber.proto",
        "topic_stats.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "pubsublite_proto_with_info",
    deps = [
        ":pubsublite_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "pubsublite_java_proto",
    deps = [":pubsublite_proto"],
)

java_grpc_library(
    name = "pubsublite_java_grpc",
    srcs = [":pubsublite_proto"],
    deps = [":pubsublite_java_proto"],
)

java_gapic_library(
    name = "pubsublite_java_gapic",
    srcs = [":pubsublite_proto_with_info"],
    gapic_yaml = "gapic.yaml",
    grpc_service_config = "pubsublite_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "pubsublite_v1.yaml",
    test_deps = [
        ":pubsublite_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":pubsublite_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "pubsublite_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.pubsublite.v1.AdminServiceClientTest",
        "com.google.cloud.pubsublite.v1.CursorServiceClientTest",
        "com.google.cloud.pubsublite.v1.PartitionAssignmentServiceClientTest",
        "com.google.cloud.pubsublite.v1.PublisherServiceClientTest",
        "com.google.cloud.pubsublite.v1.SubscriberServiceClientTest",
        "com.google.cloud.pubsublite.v1.TopicStatsServiceClientTest",
    ],
    runtime_deps = [":pubsublite_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-pubsublite-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":pubsublite_java_gapic",
        ":pubsublite_java_grpc",
        ":pubsublite_java_proto",
        ":pubsublite_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "pubsublite_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/pubsublite/apiv1/pubsublitepb",
    protos = [":pubsublite_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "pubsublite_go_gapic",
    srcs = [":pubsublite_proto_with_info"],
    grpc_service_config = "pubsublite_grpc_service_config.json",
    importpath = "cloud.google.com/go/pubsublite/apiv1;pubsublite",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "pubsublite_v1.yaml",
    transport = "grpc",
    deps = [
        ":pubsublite_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-pubsublite-v1-go",
    deps = [
        ":pubsublite_go_gapic",
        ":pubsublite_go_gapic_srcjar-snippets.srcjar",
        ":pubsublite_go_gapic_srcjar-test.srcjar",
        ":pubsublite_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "pubsublite_py_gapic",
    srcs = [":pubsublite_proto"],
    grpc_service_config = "pubsublite_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "pubsublite_v1.yaml",
    transport = "grpc",
)

py_test(
    name = "pubsublite_py_gapic_test",
    srcs = [
        "pubsublite_py_gapic_pytest.py",
        "pubsublite_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":pubsublite_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "pubsublite-v1-py",
    deps = [
        ":pubsublite_py_gapic",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "pubsublite_nodejs_gapic",
    package_name = "@google-cloud/pubsublite",
    src = ":pubsublite_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "pubsublite_grpc_service_config.json",
    package = "google.cloud.pubsublite.v1",
    rest_numeric_enums = False,
    service_yaml = "pubsublite_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "pubsublite-v1-nodejs",
    deps = [
        ":pubsublite_nodejs_gapic",
        ":pubsublite_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "pubsublite_ruby_proto",
    deps = [":pubsublite_proto"],
)

ruby_grpc_library(
    name = "pubsublite_ruby_grpc",
    srcs = [":pubsublite_proto"],
    deps = [":pubsublite_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "pubsublite_ruby_gapic",
    srcs = [":pubsublite_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-pubsublite-v1"],
    grpc_service_config = "pubsublite_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "pubsublite_v1.yaml",
    deps = [
        ":pubsublite_ruby_grpc",
        ":pubsublite_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-pubsublite-v1-ruby",
    deps = [
        ":pubsublite_ruby_gapic",
        ":pubsublite_ruby_grpc",
        ":pubsublite_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "pubsublite_csharp_proto",
    deps = [":pubsublite_proto"],
)

csharp_grpc_library(
    name = "pubsublite_csharp_grpc",
    srcs = [":pubsublite_proto"],
    deps = [":pubsublite_csharp_proto"],
)

csharp_gapic_library(
    name = "pubsublite_csharp_gapic",
    srcs = [":pubsublite_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "pubsublite_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "pubsublite_v1.yaml",
    deps = [
        ":pubsublite_csharp_grpc",
        ":pubsublite_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-pubsublite-v1-csharp",
    deps = [
        ":pubsublite_csharp_gapic",
        ":pubsublite_csharp_grpc",
        ":pubsublite_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "pubsublite_cc_proto",
    deps = [":pubsublite_proto"],
)

cc_grpc_library(
    name = "pubsublite_cc_grpc",
    srcs = [":pubsublite_proto"],
    grpc_only = True,
    deps = [":pubsublite_cc_proto"],
)
