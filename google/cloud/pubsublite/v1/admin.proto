// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.pubsublite.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/pubsublite/v1/common.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.PubSubLite.V1";
option go_package = "cloud.google.com/go/pubsublite/apiv1/pubsublitepb;pubsublitepb";
option java_multiple_files = true;
option java_outer_classname = "AdminProto";
option java_package = "com.google.cloud.pubsublite.proto";
option php_namespace = "Google\\Cloud\\PubSubLite\\V1";
option ruby_package = "Google::Cloud::PubSubLite::V1";

// The service that a client application uses to manage topics and
// subscriptions, such creating, listing, and deleting topics and subscriptions.
service AdminService {
  option (google.api.default_host) = "pubsublite.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new topic.
  rpc CreateTopic(CreateTopicRequest) returns (Topic) {
    option (google.api.http) = {
      post: "/v1/admin/{parent=projects/*/locations/*}/topics"
      body: "topic"
    };
    option (google.api.method_signature) = "parent,topic,topic_id";
  }

  // Returns the topic configuration.
  rpc GetTopic(GetTopicRequest) returns (Topic) {
    option (google.api.http) = {
      get: "/v1/admin/{name=projects/*/locations/*/topics/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the partition information for the requested topic.
  rpc GetTopicPartitions(GetTopicPartitionsRequest) returns (TopicPartitions) {
    option (google.api.http) = {
      get: "/v1/admin/{name=projects/*/locations/*/topics/*}/partitions"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the list of topics for the given project.
  rpc ListTopics(ListTopicsRequest) returns (ListTopicsResponse) {
    option (google.api.http) = {
      get: "/v1/admin/{parent=projects/*/locations/*}/topics"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates properties of the specified topic.
  rpc UpdateTopic(UpdateTopicRequest) returns (Topic) {
    option (google.api.http) = {
      patch: "/v1/admin/{topic.name=projects/*/locations/*/topics/*}"
      body: "topic"
    };
    option (google.api.method_signature) = "topic,update_mask";
  }

  // Deletes the specified topic.
  rpc DeleteTopic(DeleteTopicRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/admin/{name=projects/*/locations/*/topics/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the subscriptions attached to the specified topic.
  rpc ListTopicSubscriptions(ListTopicSubscriptionsRequest)
      returns (ListTopicSubscriptionsResponse) {
    option (google.api.http) = {
      get: "/v1/admin/{name=projects/*/locations/*/topics/*}/subscriptions"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new subscription.
  rpc CreateSubscription(CreateSubscriptionRequest) returns (Subscription) {
    option (google.api.http) = {
      post: "/v1/admin/{parent=projects/*/locations/*}/subscriptions"
      body: "subscription"
    };
    option (google.api.method_signature) =
        "parent,subscription,subscription_id";
  }

  // Returns the subscription configuration.
  rpc GetSubscription(GetSubscriptionRequest) returns (Subscription) {
    option (google.api.http) = {
      get: "/v1/admin/{name=projects/*/locations/*/subscriptions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the list of subscriptions for the given project.
  rpc ListSubscriptions(ListSubscriptionsRequest)
      returns (ListSubscriptionsResponse) {
    option (google.api.http) = {
      get: "/v1/admin/{parent=projects/*/locations/*}/subscriptions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates properties of the specified subscription.
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (Subscription) {
    option (google.api.http) = {
      patch: "/v1/admin/{subscription.name=projects/*/locations/*/subscriptions/*}"
      body: "subscription"
    };
    option (google.api.method_signature) = "subscription,update_mask";
  }

  // Deletes the specified subscription.
  rpc DeleteSubscription(DeleteSubscriptionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/admin/{name=projects/*/locations/*/subscriptions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Performs an out-of-band seek for a subscription to a specified target,
  // which may be timestamps or named positions within the message backlog.
  // Seek translates these targets to cursors for each partition and
  // orchestrates subscribers to start consuming messages from these seek
  // cursors.
  //
  // If an operation is returned, the seek has been registered and subscribers
  // will eventually receive messages from the seek cursors (i.e. eventual
  // consistency), as long as they are using a minimum supported client library
  // version and not a system that tracks cursors independently of Pub/Sub Lite
  // (e.g. Apache Beam, Dataflow, Spark). The seek operation will fail for
  // unsupported clients.
  //
  // If clients would like to know when subscribers react to the seek (or not),
  // they can poll the operation. The seek operation will succeed and complete
  // once subscribers are ready to receive messages from the seek cursors for
  // all partitions of the topic. This means that the seek operation will not
  // complete until all subscribers come online.
  //
  // If the previous seek operation has not yet completed, it will be aborted
  // and the new invocation of seek will supersede it.
  rpc SeekSubscription(SeekSubscriptionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/admin/{name=projects/*/locations/*/subscriptions/*}:seek"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "SeekSubscriptionResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new reservation.
  rpc CreateReservation(CreateReservationRequest) returns (Reservation) {
    option (google.api.http) = {
      post: "/v1/admin/{parent=projects/*/locations/*}/reservations"
      body: "reservation"
    };
    option (google.api.method_signature) = "parent,reservation,reservation_id";
  }

  // Returns the reservation configuration.
  rpc GetReservation(GetReservationRequest) returns (Reservation) {
    option (google.api.http) = {
      get: "/v1/admin/{name=projects/*/locations/*/reservations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the list of reservations for the given project.
  rpc ListReservations(ListReservationsRequest)
      returns (ListReservationsResponse) {
    option (google.api.http) = {
      get: "/v1/admin/{parent=projects/*/locations/*}/reservations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates properties of the specified reservation.
  rpc UpdateReservation(UpdateReservationRequest) returns (Reservation) {
    option (google.api.http) = {
      patch: "/v1/admin/{reservation.name=projects/*/locations/*/reservations/*}"
      body: "reservation"
    };
    option (google.api.method_signature) = "reservation,update_mask";
  }

  // Deletes the specified reservation.
  rpc DeleteReservation(DeleteReservationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/admin/{name=projects/*/locations/*/reservations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the topics attached to the specified reservation.
  rpc ListReservationTopics(ListReservationTopicsRequest)
      returns (ListReservationTopicsResponse) {
    option (google.api.http) = {
      get: "/v1/admin/{name=projects/*/locations/*/reservations/*}/topics"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request for CreateTopic.
message CreateTopicRequest {
  // Required. The parent location in which to create the topic.
  // Structured like `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Configuration of the topic to create. Its `name` field is
  // ignored.
  Topic topic = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the topic, which will become the final
  // component of the topic's name.
  //
  // This value is structured like: `my-topic-name`.
  string topic_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for GetTopic.
message GetTopicRequest {
  // Required. The name of the topic whose configuration to return.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Topic"
    }
  ];
}

// Request for GetTopicPartitions.
message GetTopicPartitionsRequest {
  // Required. The topic whose partition information to return.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Topic"
    }
  ];
}

// Response for GetTopicPartitions.
message TopicPartitions {
  // The number of partitions in the topic.
  int64 partition_count = 1;
}

// Request for ListTopics.
message ListTopicsRequest {
  // Required. The parent whose topics are to be listed.
  // Structured like `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of topics to return. The service may return fewer than
  // this value.
  // If unset or zero, all topics for the parent will be returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListTopics` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListTopics` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Response for ListTopics.
message ListTopicsResponse {
  // The list of topic in the requested parent. The order of the topics is
  // unspecified.
  repeated Topic topics = 1;

  // A token that can be sent as `page_token` to retrieve the next page of
  // results. If this field is omitted, there are no more results.
  string next_page_token = 2;
}

// Request for UpdateTopic.
message UpdateTopicRequest {
  // Required. The topic to update. Its `name` field must be populated.
  Topic topic = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying the topic fields to change.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for DeleteTopic.
message DeleteTopicRequest {
  // Required. The name of the topic to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Topic"
    }
  ];
}

// Request for ListTopicSubscriptions.
message ListTopicSubscriptionsRequest {
  // Required. The name of the topic whose subscriptions to list.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Topic"
    }
  ];

  // The maximum number of subscriptions to return. The service may return fewer
  // than this value.
  // If unset or zero, all subscriptions for the given topic will be returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListTopicSubscriptions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListTopicSubscriptions`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response for ListTopicSubscriptions.
message ListTopicSubscriptionsResponse {
  // The names of subscriptions attached to the topic. The order of the
  // subscriptions is unspecified.
  repeated string subscriptions = 1;

  // A token that can be sent as `page_token` to retrieve the next page of
  // results. If this field is omitted, there are no more results.
  string next_page_token = 2;
}

// Request for CreateSubscription.
message CreateSubscriptionRequest {
  // Required. The parent location in which to create the subscription.
  // Structured like `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Configuration of the subscription to create. Its `name` field is
  // ignored.
  Subscription subscription = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the subscription, which will become the final
  // component of the subscription's name.
  //
  // This value is structured like: `my-sub-name`.
  string subscription_id = 3 [(google.api.field_behavior) = REQUIRED];

  // If true, the newly created subscription will only receive messages
  // published after the subscription was created. Otherwise, the entire
  // message backlog will be received on the subscription. Defaults to false.
  bool skip_backlog = 4;
}

// Request for GetSubscription.
message GetSubscriptionRequest {
  // Required. The name of the subscription whose configuration to return.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Subscription"
    }
  ];
}

// Request for ListSubscriptions.
message ListSubscriptionsRequest {
  // Required. The parent whose subscriptions are to be listed.
  // Structured like `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of subscriptions to return. The service may return fewer
  // than this value.
  // If unset or zero, all subscriptions for the parent will be returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListSubscriptions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListSubscriptions` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response for ListSubscriptions.
message ListSubscriptionsResponse {
  // The list of subscriptions in the requested parent. The order of the
  // subscriptions is unspecified.
  repeated Subscription subscriptions = 1;

  // A token that can be sent as `page_token` to retrieve the next page of
  // results. If this field is omitted, there are no more results.
  string next_page_token = 2;
}

// Request for UpdateSubscription.
message UpdateSubscriptionRequest {
  // Required. The subscription to update. Its `name` field must be populated.
  // Topic field must not be populated.
  Subscription subscription = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying the subscription fields to change.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for DeleteSubscription.
message DeleteSubscriptionRequest {
  // Required. The name of the subscription to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Subscription"
    }
  ];
}

// Request for SeekSubscription.
message SeekSubscriptionRequest {
  // A named position with respect to the message backlog.
  enum NamedTarget {
    // Unspecified named target. Do not use.
    NAMED_TARGET_UNSPECIFIED = 0;

    // Seek to the oldest retained message.
    TAIL = 1;

    // Seek past all recently published messages, skipping the entire message
    // backlog.
    HEAD = 2;
  }

  // Required. The name of the subscription to seek.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Subscription"
    }
  ];

  // The target to seek to. Must be set.
  oneof target {
    // Seek to a named position with respect to the message backlog.
    NamedTarget named_target = 2;

    // Seek to the first message whose publish or event time is greater than or
    // equal to the specified query time. If no such message can be located,
    // will seek to the end of the message backlog.
    TimeTarget time_target = 3;
  }
}

// Response for SeekSubscription long running operation.
message SeekSubscriptionResponse {}

// Metadata for long running operations.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running. Not set if the operation has not
  // completed.
  google.protobuf.Timestamp end_time = 2;

  // Resource path for the target of the operation. For example, targets of
  // seeks are subscription resources, structured like:
  // projects/{project_number}/locations/{location}/subscriptions/{subscription_id}
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;
}

// Request for CreateReservation.
message CreateReservationRequest {
  // Required. The parent location in which to create the reservation.
  // Structured like `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Configuration of the reservation to create. Its `name` field is
  // ignored.
  Reservation reservation = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the reservation, which will become the final
  // component of the reservation's name.
  //
  // This value is structured like: `my-reservation-name`.
  string reservation_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for GetReservation.
message GetReservationRequest {
  // Required. The name of the reservation whose configuration to return.
  // Structured like:
  // projects/{project_number}/locations/{location}/reservations/{reservation_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Reservation"
    }
  ];
}

// Request for ListReservations.
message ListReservationsRequest {
  // Required. The parent whose reservations are to be listed.
  // Structured like `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of reservations to return. The service may return fewer
  // than this value. If unset or zero, all reservations for the parent will be
  // returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListReservations` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListReservations` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response for ListReservations.
message ListReservationsResponse {
  // The list of reservation in the requested parent. The order of the
  // reservations is unspecified.
  repeated Reservation reservations = 1;

  // A token that can be sent as `page_token` to retrieve the next page of
  // results. If this field is omitted, there are no more results.
  string next_page_token = 2;
}

// Request for UpdateReservation.
message UpdateReservationRequest {
  // Required. The reservation to update. Its `name` field must be populated.
  Reservation reservation = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying the reservation fields to change.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for DeleteReservation.
message DeleteReservationRequest {
  // Required. The name of the reservation to delete.
  // Structured like:
  // projects/{project_number}/locations/{location}/reservations/{reservation_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Reservation"
    }
  ];
}

// Request for ListReservationTopics.
message ListReservationTopicsRequest {
  // Required. The name of the reservation whose topics to list.
  // Structured like:
  // projects/{project_number}/locations/{location}/reservations/{reservation_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsublite.googleapis.com/Reservation"
    }
  ];

  // The maximum number of topics to return. The service may return fewer
  // than this value.
  // If unset or zero, all topics for the given reservation will be returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListReservationTopics` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListReservationTopics`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response for ListReservationTopics.
message ListReservationTopicsResponse {
  // The names of topics attached to the reservation. The order of the
  // topics is unspecified.
  repeated string topics = 1;

  // A token that can be sent as `page_token` to retrieve the next page of
  // results. If this field is omitted, there are no more results.
  string next_page_token = 2;
}
