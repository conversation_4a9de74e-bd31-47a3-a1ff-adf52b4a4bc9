{"methodConfig": [{"name": [{"service": "google.cloud.pubsublite.v1.AdminService"}, {"service": "google.cloud.pubsublite.v1.CursorService", "method": "CommitCursor"}, {"service": "google.cloud.pubsublite.v1.CursorService", "method": "ListPartitionCursors"}, {"service": "google.cloud.pubsublite.v1.TopicStatsService"}, {"service": "google.longrunning.Operations", "method": "GetOperation"}, {"service": "google.longrunning.Operations", "method": "ListOperations"}], "timeout": "600s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE", "ABORTED", "INTERNAL", "UNKNOWN"]}}]}