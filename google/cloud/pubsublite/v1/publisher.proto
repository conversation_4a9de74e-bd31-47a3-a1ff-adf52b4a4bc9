// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.pubsublite.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/pubsublite/v1/common.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.PubSubLite.V1";
option go_package = "cloud.google.com/go/pubsublite/apiv1/pubsublitepb;pubsublitepb";
option java_multiple_files = true;
option java_outer_classname = "PublisherProto";
option java_package = "com.google.cloud.pubsublite.proto";
option php_namespace = "Google\\Cloud\\PubSubLite\\V1";
option ruby_package = "Google::Cloud::PubSubLite::V1";

// The service that a publisher client application uses to publish messages to
// topics. Published messages are retained by the service for the duration of
// the retention period configured for the respective topic, and are delivered
// to subscriber clients upon request (via the `SubscriberService`).
service PublisherService {
  option (google.api.default_host) = "pubsublite.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Establishes a stream with the server for publishing messages. Once the
  // stream is initialized, the client publishes messages by sending publish
  // requests on the stream. The server responds with a PublishResponse for each
  // PublishRequest sent by the client, in the same order that the requests
  // were sent. Note that multiple PublishRequests can be in flight
  // simultaneously, but they will be processed by the server in the order that
  // they are sent by the client on a given stream.
  rpc Publish(stream PublishRequest) returns (stream PublishResponse) {}
}

// The first request that must be sent on a newly-opened stream.
message InitialPublishRequest {
  // The topic to which messages will be written.
  string topic = 1;

  // The partition within the topic to which messages will be written.
  // Partitions are zero indexed, so `partition` must be in the range [0,
  // topic.num_partitions).
  int64 partition = 2;

  // Unique identifier for a publisher client. If set, enables publish
  // idempotency within a publisher client session.
  //
  // The length of this field must be exactly 16 bytes long and should be
  // populated with a 128 bit uuid, generated by standard uuid algorithms like
  // uuid1 or uuid4. The same identifier should be reused following
  // disconnections with retryable stream errors.
  bytes client_id = 3;
}

// Response to an InitialPublishRequest.
message InitialPublishResponse {}

// Request to publish messages to the topic.
message MessagePublishRequest {
  // The messages to publish.
  repeated PubSubMessage messages = 1;

  // The sequence number corresponding to the first message in `messages`.
  // Messages within a batch are ordered and the sequence numbers of all
  // subsequent messages in the batch are assumed to be incremental.
  //
  // Sequence numbers are assigned at the message level and the first message
  // published in a publisher client session must have a sequence number of 0.
  // All messages must have contiguous sequence numbers, which uniquely identify
  // the messages accepted by the publisher client. Since messages are ordered,
  // the client only needs to specify the sequence number of the first message
  // in a published batch. The server deduplicates messages with the same
  // sequence number from the same publisher `client_id`.
  int64 first_sequence_number = 2;
}

// Response to a MessagePublishRequest.
message MessagePublishResponse {
  // Cursors for a subrange of published messages.
  message CursorRange {
    // The cursor of the message at the start index. The cursors for remaining
    // messages up to the end index (exclusive) are sequential.
    Cursor start_cursor = 1;

    // Index of the message in the published batch that corresponds to the
    // start cursor. Inclusive.
    int32 start_index = 2;

    // Index of the last message in this range. Exclusive.
    int32 end_index = 3;
  }

  // The cursor of the first published message in the batch. The cursors for any
  // remaining messages in the batch are guaranteed to be sequential.
  Cursor start_cursor = 1;

  // Cursors for messages published in the batch. There will exist multiple
  // ranges when cursors are not contiguous within the batch.
  //
  // The cursor ranges may not account for all messages in the batch when
  // publish idempotency is enabled. A missing range indicates that cursors
  // could not be determined for messages within the range, as they were
  // deduplicated and the necessary data was not available at publish time.
  // These messages will have offsets when received by a subscriber.
  repeated CursorRange cursor_ranges = 2;
}

// Request sent from the client to the server on a stream.
message PublishRequest {
  // The type of request this is.
  oneof request_type {
    // Initial request on the stream.
    InitialPublishRequest initial_request = 1;

    // Request to publish messages.
    MessagePublishRequest message_publish_request = 2;
  }
}

// Response to a PublishRequest.
message PublishResponse {
  // The type of response this is.
  oneof response_type {
    // Initial response on the stream.
    InitialPublishResponse initial_response = 1;

    // Response to publishing messages.
    MessagePublishResponse message_response = 2;
  }
}
