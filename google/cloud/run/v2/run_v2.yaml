type: google.api.Service
config_version: 3
name: run.googleapis.com
title: Cloud Run Admin API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.run.v2.Builds
- name: google.cloud.run.v2.Executions
- name: google.cloud.run.v2.Jobs
- name: google.cloud.run.v2.Revisions
- name: google.cloud.run.v2.Services
- name: google.cloud.run.v2.Tasks
- name: google.longrunning.Operations

documentation:
  summary: |-
    Deploy and manage user provided container images that scale automatically
    based on incoming requests. The Cloud Run Admin API v1 follows the Knative
    Serving API specification, while v2 is aligned with Google Cloud AIP-based
    API standards, as described in https://google.aip.dev/.
  overview: API for managing Cloud Run services
  rules:
  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v2/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=projects/*/locations/*}/operations'
  - selector: google.longrunning.Operations.WaitOperation
    post: '/v2/{name=projects/*/locations/*/operations/*}:wait'
    body: '*'

authentication:
  rules:
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.run.v2.Builds.SubmitBuild
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.run.v2.Executions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.run.v2.Jobs.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.run.v2.Revisions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.run.v2.Services.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.run.v2.Tasks.GetTask
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.run.v2.Tasks.ListTasks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
