// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/run/v2/k8s.min.proto";
import "google/cloud/run/v2/vendor_settings.proto";
import "google/protobuf/duration.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "RevisionTemplateProto";
option java_package = "com.google.cloud.run.v2";

// RevisionTemplate describes the data a revision should have when created from
// a template.
message RevisionTemplate {
  // Optional. The unique name for the revision. If this field is omitted, it
  // will be automatically generated based on the Service name.
  string revision = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "run.googleapis.com/Revision" }
  ];

  // Optional. Unstructured key value map that can be used to organize and
  // categorize objects. User-provided labels are shared with Google's billing
  // system, so they can be used to filter, or break down billing charges by
  // team, component, environment, state, etc. For more information, visit
  // https://cloud.google.com/resource-manager/docs/creating-managing-labels or
  // https://cloud.google.com/run/docs/configuring/labels.
  //
  // <p>Cloud Run API v2 does not support labels with `run.googleapis.com`,
  // `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev`
  // namespaces, and they will be rejected. All system labels in v1 now have a
  // corresponding field in v2 RevisionTemplate.
  map<string, string> labels = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Unstructured key value map that may be set by external tools to
  // store and arbitrary metadata. They are not queryable and should be
  // preserved when modifying objects.
  //
  // <p>Cloud Run API v2 does not support annotations with `run.googleapis.com`,
  // `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev`
  // namespaces, and they will be rejected. All system annotations in v1 now
  // have a corresponding field in v2 RevisionTemplate.
  //
  // <p>This field follows Kubernetes annotations' namespacing, limits, and
  // rules.
  map<string, string> annotations = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Scaling settings for this Revision.
  RevisionScaling scaling = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. VPC Access configuration to use for this Revision. For more
  // information, visit
  // https://cloud.google.com/run/docs/configuring/connecting-vpc.
  VpcAccess vpc_access = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Max allowed time for an instance to respond to a request.
  google.protobuf.Duration timeout = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Email address of the IAM service account associated with the
  // revision of the service. The service account represents the identity of the
  // running revision, and determines what permissions the revision has. If not
  // provided, the revision will use the project's default service account.
  string service_account = 9 [(google.api.field_behavior) = OPTIONAL];

  // Holds the single container that defines the unit of execution for this
  // Revision.
  repeated Container containers = 10;

  // Optional. A list of Volumes to make available to containers.
  repeated Volume volumes = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The sandbox environment to host this Revision.
  ExecutionEnvironment execution_environment = 13
      [(google.api.field_behavior) = OPTIONAL];

  // A reference to a customer managed encryption key (CMEK) to use to encrypt
  // this container image. For more information, go to
  // https://cloud.google.com/run/docs/securing/using-cmek
  string encryption_key = 14 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // Optional. Sets the maximum number of requests that each serving instance
  // can receive. If not specified or 0, concurrency defaults to 80 when
  // requested `CPU >= 1` and defaults to 1 when requested `CPU < 1`.
  int32 max_instance_request_concurrency = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Enables service mesh connectivity.
  ServiceMesh service_mesh = 16 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The action to take if the encryption key is revoked.
  EncryptionKeyRevocationAction encryption_key_revocation_action = 17
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If encryption_key_revocation_action is SHUTDOWN, the duration
  // before shutting down all instances. The minimum increment is 1 hour.
  google.protobuf.Duration encryption_key_shutdown_duration = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Enable session affinity.
  bool session_affinity = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Disables health checking containers during deployment.
  bool health_check_disabled = 20 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The node selector for the revision template.
  NodeSelector node_selector = 21 [(google.api.field_behavior) = OPTIONAL];
}
