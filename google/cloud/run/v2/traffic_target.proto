// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/resource.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "TrafficTargetProto";
option java_package = "com.google.cloud.run.v2";

// Holds a single traffic routing entry for the Service. Allocations can be done
// to a specific Revision name, or pointing to the latest Ready Revision.
message TrafficTarget {
  // The allocation type for this traffic target.
  TrafficTargetAllocationType type = 1;

  // Revision to which to send this portion of traffic, if traffic allocation is
  // by revision.
  string revision = 2 [
    (google.api.resource_reference) = { type: "run.googleapis.com/Revision" }
  ];

  // Specifies percent of the traffic to this Revision.
  // This defaults to zero if unspecified.
  int32 percent = 3;

  // Indicates a string to be part of the URI to exclusively reference this
  // target.
  string tag = 4;
}

// Represents the observed state of a single `TrafficTarget` entry.
message TrafficTargetStatus {
  // The allocation type for this traffic target.
  TrafficTargetAllocationType type = 1;

  // Revision to which this traffic is sent.
  string revision = 2 [
    (google.api.resource_reference) = { type: "run.googleapis.com/Revision" }
  ];

  // Specifies percent of the traffic to this Revision.
  int32 percent = 3;

  // Indicates the string used in the URI to exclusively reference this target.
  string tag = 4;

  // Displays the target URI.
  string uri = 5;
}

// The type of instance allocation.
enum TrafficTargetAllocationType {
  // Unspecified instance allocation type.
  TRAFFIC_TARGET_ALLOCATION_TYPE_UNSPECIFIED = 0;

  // Allocates instances to the Service's latest ready Revision.
  TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST = 1;

  // Allocates instances to a Revision by name.
  TRAFFIC_TARGET_ALLOCATION_TYPE_REVISION = 2;
}
