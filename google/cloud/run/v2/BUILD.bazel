# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "run_proto",
    srcs = [
        "build.proto",
        "condition.proto",
        "execution.proto",
        "execution_template.proto",
        "job.proto",
        "k8s.min.proto",
        "revision.proto",
        "revision_template.proto",
        "service.proto",
        "status.proto",
        "task.proto",
        "task_template.proto",
        "traffic_target.proto",
        "vendor_settings.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:launch_stage_proto",
        "//google/api:resource_proto",
        "//google/api:routing_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "run_proto_with_info",
    deps = [
        ":run_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "run_java_proto",
    deps = [":run_proto"],
)

java_grpc_library(
    name = "run_java_grpc",
    srcs = [":run_proto"],
    deps = [":run_java_proto"],
)

java_gapic_library(
    name = "run_java_gapic",
    srcs = [":run_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "run_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "run_v2.yaml",
    test_deps = [
        ":run_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":run_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "run_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.run.v2.BuildsClientHttpJsonTest",
        "com.google.cloud.run.v2.BuildsClientTest",
        "com.google.cloud.run.v2.ExecutionsClientHttpJsonTest",
        "com.google.cloud.run.v2.ExecutionsClientTest",
        "com.google.cloud.run.v2.JobsClientHttpJsonTest",
        "com.google.cloud.run.v2.JobsClientTest",
        "com.google.cloud.run.v2.RevisionsClientHttpJsonTest",
        "com.google.cloud.run.v2.RevisionsClientTest",
        "com.google.cloud.run.v2.ServicesClientHttpJsonTest",
        "com.google.cloud.run.v2.ServicesClientTest",
        "com.google.cloud.run.v2.TasksClientHttpJsonTest",
        "com.google.cloud.run.v2.TasksClientTest",
    ],
    runtime_deps = [":run_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-run-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":run_java_gapic",
        ":run_java_grpc",
        ":run_java_proto",
        ":run_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "run_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/run/apiv2/runpb",
    protos = [":run_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:api_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "run_go_gapic",
    srcs = [":run_proto_with_info"],
    grpc_service_config = "run_grpc_service_config.json",
    importpath = "cloud.google.com/go/run/apiv2;run",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "run_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":run_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-run-v2-go",
    deps = [
        ":run_go_gapic",
        ":run_go_gapic_srcjar-metadata.srcjar",
        ":run_go_gapic_srcjar-snippets.srcjar",
        ":run_go_gapic_srcjar-test.srcjar",
        ":run_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "run_py_gapic",
    srcs = [":run_proto"],
    grpc_service_config = "run_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "run_v2.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "run_py_gapic_test",
    srcs = [
        "run_py_gapic_pytest.py",
        "run_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":run_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "run-v2-py",
    deps = [
        ":run_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "run_php_proto",
    deps = [":run_proto"],
)

php_gapic_library(
    name = "run_php_gapic",
    srcs = [":run_proto_with_info"],
    grpc_service_config = "run_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "run_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":run_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-run-v2-php",
    deps = [
        ":run_php_gapic",
        ":run_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "run_nodejs_gapic",
    package_name = "@google-cloud/run",
    src = ":run_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "run_grpc_service_config.json",
    package = "google.cloud.run.v2",
    rest_numeric_enums = True,
    service_yaml = "run_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "run-v2-nodejs",
    deps = [
        ":run_nodejs_gapic",
        ":run_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "run_ruby_proto",
    deps = [":run_proto"],
)

ruby_grpc_library(
    name = "run_ruby_grpc",
    srcs = [":run_proto"],
    deps = [":run_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "run_ruby_gapic",
    srcs = [":run_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=run.googleapis.com",
        "ruby-cloud-api-shortname=run",
        "ruby-cloud-gem-name=google-cloud-run-v2",
        "ruby-cloud-product-url=https://cloud.google.com/run/",
        "ruby-cloud-wrapper-gem-override=google-cloud-run-client",
    ],
    grpc_service_config = "run_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Run deploys and manages user provided container images that scale automatically based on incoming requests.",
    ruby_cloud_title = "Cloud Run V2",
    service_yaml = "run_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":run_ruby_grpc",
        ":run_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-run-v2-ruby",
    deps = [
        ":run_ruby_gapic",
        ":run_ruby_grpc",
        ":run_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "run_csharp_proto",
    deps = [":run_proto"],
)

csharp_grpc_library(
    name = "run_csharp_grpc",
    srcs = [":run_proto"],
    deps = [":run_csharp_proto"],
)

csharp_gapic_library(
    name = "run_csharp_gapic",
    srcs = [":run_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "run_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "run_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":run_csharp_grpc",
        ":run_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-run-v2-csharp",
    deps = [
        ":run_csharp_gapic",
        ":run_csharp_grpc",
        ":run_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "run_cc_proto",
    deps = [":run_proto"],
)

cc_grpc_library(
    name = "run_cc_grpc",
    srcs = [":run_proto"],
    grpc_only = True,
    deps = [":run_cc_proto"],
)
