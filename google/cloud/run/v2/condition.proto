// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "ConditionProto";
option java_package = "com.google.cloud.run.v2";

// Defines a status condition for a resource.
message Condition {
  // Represents the possible Condition states.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // Transient state: Reconciliation has not started yet.
    CONDITION_PENDING = 1;

    // Transient state: reconciliation is still in progress.
    CONDITION_RECONCILING = 2;

    // Terminal state: Reconciliation did not succeed.
    CONDITION_FAILED = 3;

    // Terminal state: Reconciliation completed successfully.
    CONDITION_SUCCEEDED = 4;
  }

  // Represents the severity of the condition failures.
  enum Severity {
    // Unspecified severity
    SEVERITY_UNSPECIFIED = 0;

    // Error severity.
    ERROR = 1;

    // Warning severity.
    WARNING = 2;

    // Info severity.
    INFO = 3;
  }

  // Reasons common to all types of conditions.
  enum CommonReason {
    // Default value.
    COMMON_REASON_UNDEFINED = 0;

    // Reason unknown. Further details will be in message.
    UNKNOWN = 1;

    // Revision creation process failed.
    REVISION_FAILED = 3;

    // Timed out waiting for completion.
    PROGRESS_DEADLINE_EXCEEDED = 4;

    // The container image path is incorrect.
    CONTAINER_MISSING = 6;

    // Insufficient permissions on the container image.
    CONTAINER_PERMISSION_DENIED = 7;

    // Container image is not authorized by policy.
    CONTAINER_IMAGE_UNAUTHORIZED = 8;

    // Container image policy authorization check failed.
    CONTAINER_IMAGE_AUTHORIZATION_CHECK_FAILED = 9;

    // Insufficient permissions on encryption key.
    ENCRYPTION_KEY_PERMISSION_DENIED = 10;

    // Permission check on encryption key failed.
    ENCRYPTION_KEY_CHECK_FAILED = 11;

    // At least one Access check on secrets failed.
    SECRETS_ACCESS_CHECK_FAILED = 12;

    // Waiting for operation to complete.
    WAITING_FOR_OPERATION = 13;

    // System will retry immediately.
    IMMEDIATE_RETRY = 14;

    // System will retry later; current attempt failed.
    POSTPONED_RETRY = 15;

    // An internal error occurred. Further information may be in the message.
    INTERNAL = 16;
  }

  // Reasons specific to Revision resource.
  enum RevisionReason {
    // Default value.
    REVISION_REASON_UNDEFINED = 0;

    // Revision in Pending state.
    PENDING = 1;

    // Revision is in Reserve state.
    RESERVE = 2;

    // Revision is Retired.
    RETIRED = 3;

    // Revision is being retired.
    RETIRING = 4;

    // Revision is being recreated.
    RECREATING = 5;

    // There was a health check error.
    HEALTH_CHECK_CONTAINER_ERROR = 6;

    // Health check failed due to user error from customized path of the
    // container. System will retry.
    CUSTOMIZED_PATH_RESPONSE_PENDING = 7;

    // A revision with min_instance_count > 0 was created and is reserved, but
    // it was not configured to serve traffic, so it's not live. This can also
    // happen momentarily during traffic migration.
    MIN_INSTANCES_NOT_PROVISIONED = 8;

    // The maximum allowed number of active revisions has been reached.
    ACTIVE_REVISION_LIMIT_REACHED = 9;

    // There was no deployment defined.
    // This value is no longer used, but Services created in older versions of
    // the API might contain this value.
    NO_DEPLOYMENT = 10;

    // A revision's container has no port specified since the revision is of a
    // manually scaled service with 0 instance count
    HEALTH_CHECK_SKIPPED = 11;

    // A revision with min_instance_count > 0 was created and is waiting for
    // enough instances to begin a traffic migration.
    MIN_INSTANCES_WARMING = 12;
  }

  // Reasons specific to Execution resource.
  enum ExecutionReason {
    // Default value.
    EXECUTION_REASON_UNDEFINED = 0;

    // Internal system error getting execution status. System will retry.
    JOB_STATUS_SERVICE_POLLING_ERROR = 1;

    // A task reached its retry limit and the last attempt failed due to the
    // user container exiting with a non-zero exit code.
    NON_ZERO_EXIT_CODE = 2;

    // The execution was cancelled by users.
    CANCELLED = 3;

    // The execution is in the process of being cancelled.
    CANCELLING = 4;

    // The execution was deleted.
    DELETED = 5;
  }

  // type is used to communicate the status of the reconciliation process.
  // See also:
  // https://github.com/knative/serving/blob/main/docs/spec/errors.md#error-conditions-and-reporting
  // Types common to all resources include:
  // * "Ready": True when the Resource is ready.
  string type = 1;

  // State of the condition.
  State state = 2;

  // Human readable message indicating details about the current status.
  string message = 3;

  // Last time the condition transitioned from one status to another.
  google.protobuf.Timestamp last_transition_time = 4;

  // How to interpret failures of this condition, one of Error, Warning, Info
  Severity severity = 5;

  // The reason for this condition. Depending on the condition type,
  // it will populate one of these fields.
  // Successful conditions cannot have a reason.
  oneof reasons {
    // Output only. A common (service-level) reason for this condition.
    CommonReason reason = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A reason for the revision condition.
    RevisionReason revision_reason = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A reason for the execution condition.
    ExecutionReason execution_reason = 11
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
