// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/launch_stage.proto";
import "google/api/resource.proto";
import "google/api/routing.proto";
import "google/cloud/run/v2/condition.proto";
import "google/cloud/run/v2/revision_template.proto";
import "google/cloud/run/v2/traffic_target.proto";
import "google/cloud/run/v2/vendor_settings.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.run.v2";

// Cloud Run Service Control Plane API
service Services {
  option (google.api.default_host) = "run.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new Service in a given project and location.
  rpc CreateService(CreateServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/services"
      body: "service"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "parent,service,service_id";
    option (google.longrunning.operation_info) = {
      response_type: "Service"
      metadata_type: "Service"
    };
  }

  // Gets information about a Service.
  rpc GetService(GetServiceRequest) returns (Service) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/services/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Services. Results are sorted by creation time, descending.
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/services"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a Service.
  rpc UpdateService(UpdateServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{service.name=projects/*/locations/*/services/*}"
      body: "service"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "service.name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "service";
    option (google.api.method_signature) = "service,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Service"
      metadata_type: "Service"
    };
  }

  // Deletes a Service.
  // This will cause the Service to stop serving traffic and will delete all
  // revisions.
  rpc DeleteService(DeleteServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/services/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Service"
      metadata_type: "Service"
    };
  }

  // Gets the IAM Access Control policy currently in effect for the given
  // Cloud Run Service. This result does not include any inherited policies.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      get: "/v2/{resource=projects/*/locations/*/services/*}:getIamPolicy"
    };
  }

  // Sets the IAM Access control policy for the specified Service. Overwrites
  // any existing policy.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/locations/*/services/*}:setIamPolicy"
      body: "*"
    };
  }

  // Returns permissions that a caller has on the specified Project.
  //
  // There are no permissions required for making this API call.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/locations/*/services/*}:testIamPermissions"
      body: "*"
    };
  }
}

// Request message for creating a Service.
message CreateServiceRequest {
  // Required. The location and project in which this service should be created.
  // Format: projects/{project}/locations/{location}, where {project} can be
  // project id or number. Only lowercase characters, digits, and hyphens.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "run.googleapis.com/Service"
    }
  ];

  // Required. The Service instance to create.
  Service service = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The unique identifier for the Service. It must begin with letter,
  // and cannot end with hyphen; must contain fewer than 50 characters.
  // The name of the service becomes {parent}/services/{service_id}.
  string service_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Indicates that the request should be validated and default values
  // populated, without persisting the request or creating any resources.
  bool validate_only = 4;
}

// Request message for updating a service.
message UpdateServiceRequest {
  // Optional. The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The Service to be updated.
  Service service = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates that the request should be validated and default values
  // populated, without persisting the request or updating any resources.
  bool validate_only = 3;

  // Optional. If set to true, and if the Service does not exist, it will create
  // a new one. The caller must have 'run.services.create' permissions if this
  // is set to true and the Service does not exist.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for retrieving a list of Services.
message ListServicesRequest {
  // Required. The location and project to list resources on.
  // Location must be a valid Google Cloud region, and cannot be the "-"
  // wildcard. Format: projects/{project}/locations/{location}, where {project}
  // can be project id or number.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "run.googleapis.com/Service"
    }
  ];

  // Maximum number of Services to return in this call.
  int32 page_size = 2;

  // A page token received from a previous call to ListServices.
  // All other parameters must match.
  string page_token = 3;

  // If true, returns deleted (but unexpired) resources along with active ones.
  bool show_deleted = 4;
}

// Response message containing a list of Services.
message ListServicesResponse {
  // The resulting list of Services.
  repeated Service services = 1;

  // A token indicating there are more items than page_size. Use it in the next
  // ListServices request to continue.
  string next_page_token = 2;
}

// Request message for obtaining a Service by its full name.
message GetServiceRequest {
  // Required. The full name of the Service.
  // Format: projects/{project}/locations/{location}/services/{service}, where
  // {project} can be project id or number.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Service" }
  ];
}

// Request message to delete a Service by its full name.
message DeleteServiceRequest {
  // Required. The full name of the Service.
  // Format: projects/{project}/locations/{location}/services/{service}, where
  // {project} can be project id or number.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Service" }
  ];

  // Indicates that the request should be validated without actually
  // deleting any resources.
  bool validate_only = 2;

  // A system-generated fingerprint for this version of the
  // resource. May be used to detect modification conflict during updates.
  string etag = 3;
}

// Service acts as a top-level container that manages a set of
// configurations and revision templates which implement a network service.
// Service exists to provide a singular abstraction which can be access
// controlled, reasoned about, and which encapsulates software lifecycle
// decisions such as rollout policy and team resource ownership.
message Service {
  option (google.api.resource) = {
    type: "run.googleapis.com/Service"
    pattern: "projects/{project}/locations/{location}/services/{service}"
    style: DECLARATIVE_FRIENDLY
  };

  // The fully qualified name of this Service. In CreateServiceRequest, this
  // field is ignored, and instead composed from CreateServiceRequest.parent and
  // CreateServiceRequest.service_id.
  //
  // Format:
  // projects/{project}/locations/{location}/services/{service_id}
  string name = 1;

  // User-provided description of the Service. This field currently has a
  // 512-character limit.
  string description = 2;

  // Output only. Server assigned unique identifier for the trigger. The value
  // is a UUID4 string and guaranteed to remain unchanged until the resource is
  // deleted.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A number that monotonically increases every time the user
  // modifies the desired state.
  // Please note that unlike v1, this is an int64 value. As with most Google
  // APIs, its JSON representation will be a `string` instead of an `integer`.
  int64 generation = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Unstructured key value map that can be used to organize and
  // categorize objects. User-provided labels are shared with Google's billing
  // system, so they can be used to filter, or break down billing charges by
  // team, component, environment, state, etc. For more information, visit
  // https://cloud.google.com/resource-manager/docs/creating-managing-labels or
  // https://cloud.google.com/run/docs/configuring/labels.
  //
  // <p>Cloud Run API v2 does not support labels with  `run.googleapis.com`,
  // `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev`
  // namespaces, and they will be rejected. All system labels in v1 now have a
  // corresponding field in v2 Service.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Unstructured key value map that may be set by external tools to
  // store and arbitrary metadata. They are not queryable and should be
  // preserved when modifying objects.
  //
  // <p>Cloud Run API v2 does not support annotations with `run.googleapis.com`,
  // `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev`
  // namespaces, and they will be rejected in new resources. All system
  // annotations in v1 now have a corresponding field in v2 Service.
  //
  // <p>This field follows Kubernetes
  // annotations' namespacing, limits, and rules.
  map<string, string> annotations = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The deletion time. It is only populated as a response to a
  // Delete request.
  google.protobuf.Timestamp delete_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a deleted resource, the time after which it will be
  // permamently deleted.
  google.protobuf.Timestamp expire_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the authenticated creator.
  string creator = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the last authenticated modifier.
  string last_modifier = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Arbitrary identifier for the API client.
  string client = 13;

  // Arbitrary version identifier for the API client.
  string client_version = 14;

  // Optional. Provides the ingress settings for this Service. On output,
  // returns the currently observed ingress settings, or
  // INGRESS_TRAFFIC_UNSPECIFIED if no revision is active.
  IngressTraffic ingress = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The launch stage as defined by [Google Cloud Platform
  // Launch Stages](https://cloud.google.com/terms/launch-stages).
  // Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA
  // is assumed.
  // Set the launch stage to a preview stage on input to allow use of preview
  // features in that stage. On read (or output), describes whether the resource
  // uses preview features.
  // <p>
  // For example, if ALPHA is provided as input, but only BETA and GA-level
  // features are used, this field will be BETA on output.
  google.api.LaunchStage launch_stage = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Settings for the Binary Authorization feature.
  BinaryAuthorization binary_authorization = 17
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The template used to create revisions for this Service.
  RevisionTemplate template = 18 [(google.api.field_behavior) = REQUIRED];

  // Optional. Specifies how to distribute traffic over a collection of
  // Revisions belonging to the Service. If traffic is empty or not provided,
  // defaults to 100% traffic to the latest `Ready` Revision.
  repeated TrafficTarget traffic = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies service-level scaling settings
  ServiceScaling scaling = 20 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Disables IAM permission check for run.routes.invoke for callers
  // of this service. This feature is available by invitation only. For more
  // information, visit
  // https://cloud.google.com/run/docs/securing/managing-access#invoker_check.
  bool invoker_iam_disabled = 21 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Disables public resolution of the default URI of this service.
  bool default_uri_disabled = 22 [(google.api.field_behavior) = OPTIONAL];

  // Output only. All URLs serving traffic for this Service.
  repeated string urls = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // One or more custom audiences that you want this service to support. Specify
  // each custom audience as the full URL in a string. The custom audiences are
  // encoded in the token and used to authenticate requests. For more
  // information, see
  // https://cloud.google.com/run/docs/configuring/custom-audiences.
  repeated string custom_audiences = 37;

  // Output only. The generation of this Service currently serving traffic. See
  // comments in `reconciling` for additional information on reconciliation
  // process in Cloud Run. Please note that unlike v1, this is an int64 value.
  // As with most Google APIs, its JSON representation will be a `string`
  // instead of an `integer`.
  int64 observed_generation = 30 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Condition of this Service, containing its readiness
  // status, and detailed error information in case it did not reach a serving
  // state. See comments in `reconciling` for additional information on
  // reconciliation process in Cloud Run.
  Condition terminal_condition = 31 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Conditions of all other associated sub-resources. They
  // contain additional diagnostics information in case the Service does not
  // reach its Serving state. See comments in `reconciling` for additional
  // information on reconciliation process in Cloud Run.
  repeated Condition conditions = 32
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the latest revision that is serving traffic. See
  // comments in `reconciling` for additional information on reconciliation
  // process in Cloud Run.
  string latest_ready_revision = 33 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Revision" }
  ];

  // Output only. Name of the last created revision. See comments in
  // `reconciling` for additional information on reconciliation process in Cloud
  // Run.
  string latest_created_revision = 34 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Revision" }
  ];

  // Output only. Detailed status information for corresponding traffic targets.
  // See comments in `reconciling` for additional information on reconciliation
  // process in Cloud Run.
  repeated TrafficTargetStatus traffic_statuses = 35
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The main URI in which this Service is serving traffic.
  string uri = 36 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 38 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Returns true if the Service is currently being acted upon by
  // the system to bring it into the desired state.
  //
  // When a new Service is created, or an existing one is updated, Cloud Run
  // will asynchronously perform all necessary steps to bring the Service to the
  // desired serving state. This process is called reconciliation.
  // While reconciliation is in process, `observed_generation`,
  // `latest_ready_revison`, `traffic_statuses`, and `uri` will have transient
  // values that might mismatch the intended state: Once reconciliation is over
  // (and this field is false), there are two possible outcomes: reconciliation
  // succeeded and the serving state matches the Service, or there was an error,
  // and reconciliation failed. This state can be found in
  // `terminal_condition.state`.
  //
  // If reconciliation succeeded, the following fields will match: `traffic` and
  // `traffic_statuses`, `observed_generation` and `generation`,
  // `latest_ready_revision` and `latest_created_revision`.
  //
  // If reconciliation failed, `traffic_statuses`, `observed_generation`, and
  // `latest_ready_revision` will have the state of the last serving revision,
  // or empty for newly created Services. Additional information on the failure
  // can be found in `terminal_condition` and `conditions`.
  bool reconciling = 98 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A system-generated fingerprint for this version of the
  // resource. May be used to detect modification conflict during updates.
  string etag = 99 [(google.api.field_behavior) = OUTPUT_ONLY];
}
