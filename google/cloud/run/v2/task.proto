// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/run/v2/condition.proto";
import "google/cloud/run/v2/k8s.min.proto";
import "google/cloud/run/v2/vendor_settings.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "TaskProto";
option java_package = "com.google.cloud.run.v2";

// Cloud Run Task Control Plane API.
service Tasks {
  option (google.api.default_host) = "run.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets information about a Task.
  rpc GetTask(GetTaskRequest) returns (Task) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/jobs/*/executions/*/tasks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Tasks from an Execution of a Job.
  rpc ListTasks(ListTasksRequest) returns (ListTasksResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/jobs/*/executions/*}/tasks"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for obtaining a Task by its full name.
message GetTaskRequest {
  // Required. The full name of the Task.
  // Format:
  // projects/{project}/locations/{location}/jobs/{job}/executions/{execution}/tasks/{task}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Task" }
  ];
}

// Request message for retrieving a list of Tasks.
message ListTasksRequest {
  // Required. The Execution from which the Tasks should be listed.
  // To list all Tasks across Executions of a Job, use "-" instead of Execution
  // name. To list all Tasks across Jobs, use "-" instead of Job name. Format:
  // projects/{project}/locations/{location}/jobs/{job}/executions/{execution}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "run.googleapis.com/Task" }
  ];

  // Maximum number of Tasks to return in this call.
  int32 page_size = 2;

  // A page token received from a previous call to ListTasks.
  // All other parameters must match.
  string page_token = 3;

  // If true, returns deleted (but unexpired) resources along with active ones.
  bool show_deleted = 4;
}

// Response message containing a list of Tasks.
message ListTasksResponse {
  // The resulting list of Tasks.
  repeated Task tasks = 1;

  // A token indicating there are more items than page_size. Use it in the next
  // ListTasks request to continue.
  string next_page_token = 2;
}

// Task represents a single run of a container to completion.
message Task {
  option (google.api.resource) = {
    type: "run.googleapis.com/Task"
    pattern: "projects/{project}/locations/{location}/jobs/{job}/executions/{execution}/tasks/{task}"
    style: DECLARATIVE_FRIENDLY
  };

  // Output only. The unique name of this Task.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server assigned unique identifier for the Task. The value is a
  // UUID4 string and guaranteed to remain unchanged until the resource is
  // deleted.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A number that monotonically increases every time the user
  // modifies the desired state.
  int64 generation = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unstructured key value map that can be used to organize and
  // categorize objects. User-provided labels are shared with Google's billing
  // system, so they can be used to filter, or break down billing charges by
  // team, component, environment, state, etc. For more information, visit
  // https://cloud.google.com/resource-manager/docs/creating-managing-labels or
  // https://cloud.google.com/run/docs/configuring/labels
  map<string, string> labels = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unstructured key value map that may
  // be set by external tools to store and arbitrary metadata.
  // They are not queryable and should be preserved
  // when modifying objects.
  map<string, string> annotations = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents time when the task was created by the system.
  // It is not guaranteed to be set in happens-before order across separate
  // operations.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents time when the task was scheduled to run by the
  // system. It is not guaranteed to be set in happens-before order across
  // separate operations.
  google.protobuf.Timestamp scheduled_time = 34
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents time when the task started to run.
  // It is not guaranteed to be set in happens-before order across separate
  // operations.
  google.protobuf.Timestamp start_time = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents time when the Task was completed. It is not
  // guaranteed to be set in happens-before order across separate operations.
  google.protobuf.Timestamp completion_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a deleted resource, the deletion time. It is only
  // populated as a response to a Delete request.
  google.protobuf.Timestamp delete_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a deleted resource, the time after which it will be
  // permamently deleted. It is only populated as a response to a Delete
  // request.
  google.protobuf.Timestamp expire_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the parent Job.
  string job = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Job" }
  ];

  // Output only. The name of the parent Execution.
  string execution = 13 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Execution" }
  ];

  // Holds the single container that defines the unit of execution for this
  // task.
  repeated Container containers = 14;

  // A list of Volumes to make available to containers.
  repeated Volume volumes = 15;

  // Number of retries allowed per Task, before marking this Task failed.
  int32 max_retries = 16;

  // Max allowed time duration the Task may be active before the system will
  // actively try to mark it failed and kill associated containers. This applies
  // per attempt of a task, meaning each retry can run for the full timeout.
  google.protobuf.Duration timeout = 17;

  // Email address of the IAM service account associated with the Task of a
  // Job. The service account represents the identity of the
  // running task, and determines what permissions the task has. If
  // not provided, the task will use the project's default service account.
  string service_account = 18;

  // The execution environment being used to host this Task.
  ExecutionEnvironment execution_environment = 20;

  // Output only. Indicates whether the resource's reconciliation is still in
  // progress. See comments in `Job.reconciling` for additional information on
  // reconciliation process in Cloud Run.
  bool reconciling = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Condition of this Task, containing its readiness status,
  // and detailed error information in case it did not reach the desired state.
  repeated Condition conditions = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The generation of this Task. See comments in `Job.reconciling`
  // for additional information on reconciliation process in Cloud Run.
  int64 observed_generation = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Index of the Task, unique per execution, and beginning at 0.
  int32 index = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of times this Task was retried.
  // Tasks are retried when they fail up to the maxRetries limit.
  int32 retried = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Result of the last attempt of this Task.
  TaskAttemptResult last_attempt_result = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A reference to a customer managed encryption key (CMEK) to use
  // to encrypt this container image. For more information, go to
  // https://cloud.google.com/run/docs/securing/using-cmek
  string encryption_key = 28 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Output only. VPC Access configuration to use for this Task. For more
  // information, visit
  // https://cloud.google.com/run/docs/configuring/connecting-vpc.
  VpcAccess vpc_access = 29 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URI where logs for this execution can be found in Cloud
  // Console.
  string log_uri = 32 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 33 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A system-generated fingerprint for this version of the
  // resource. May be used to detect modification conflict during updates.
  string etag = 99 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Result of a task attempt.
message TaskAttemptResult {
  // Output only. The status of this attempt.
  // If the status code is OK, then the attempt succeeded.
  google.rpc.Status status = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The exit code of this attempt.
  // This may be unset if the container was unable to exit cleanly with a code
  // due to some other failure.
  // See status field for possible failure details.
  int32 exit_code = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
