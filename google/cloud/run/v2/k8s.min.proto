// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "K8sMinProto";
option java_package = "com.google.cloud.run.v2";
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/Secret"
  pattern: "projects/{project}/secrets/{secret}"
};
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/SecretVersion"
  pattern: "projects/{project}/secrets/{secret}/versions/{version}"
};
option (google.api.resource_definition) = {
  type: "vpcaccess.googleapis.com/Connector"
  pattern: "projects/{project}/locations/{location}/connectors/{connector}"
};

// A single application container.
// This specifies both the container to run, the command to run in the container
// and the arguments to supply to it.
// Note that additional arguments can be supplied by the system to the container
// at runtime.
message Container {
  // Name of the container specified as a DNS_LABEL (RFC 1123).
  string name = 1;

  // Required. Name of the container image in Dockerhub, Google Artifact
  // Registry, or Google Container Registry. If the host is not provided,
  // Dockerhub is assumed.
  string image = 2 [(google.api.field_behavior) = REQUIRED];

  // Entrypoint array. Not executed within a shell.
  // The docker image's ENTRYPOINT is used if this is not provided.
  repeated string command = 3;

  // Arguments to the entrypoint.
  // The docker image's CMD is used if this is not provided.
  repeated string args = 4;

  // List of environment variables to set in the container.
  repeated EnvVar env = 5;

  // Compute Resource requirements by this container.
  ResourceRequirements resources = 6;

  // List of ports to expose from the container. Only a single port can be
  // specified. The specified ports must be listening on all interfaces
  // (0.0.0.0) within the container to be accessible.
  //
  // If omitted, a port number will be chosen and passed to the container
  // through the PORT environment variable for the container to listen on.
  repeated ContainerPort ports = 7;

  // Volume to mount into the container's filesystem.
  repeated VolumeMount volume_mounts = 8;

  // Container's working directory.
  // If not specified, the container runtime's default will be used, which
  // might be configured in the container image.
  string working_dir = 9;

  // Periodic probe of container liveness.
  // Container will be restarted if the probe fails.
  Probe liveness_probe = 10;

  // Startup probe of application within the container.
  // All other probes are disabled if a startup probe is provided, until it
  // succeeds. Container will not be added to service endpoints if the probe
  // fails.
  Probe startup_probe = 11;

  // Names of the containers that must start before this container.
  repeated string depends_on = 12;
}

// ResourceRequirements describes the compute resource requirements.
message ResourceRequirements {
  // Only `memory` and `cpu` keys in the map are supported.
  //
  // <p>Notes:
  //  * The only supported values for CPU are '1', '2', '4', and '8'. Setting 4
  // CPU requires at least 2Gi of memory. For more information, go to
  // https://cloud.google.com/run/docs/configuring/cpu.
  //   * For supported 'memory' values and syntax, go to
  //  https://cloud.google.com/run/docs/configuring/memory-limits
  map<string, string> limits = 1;

  // Determines whether CPU is only allocated during requests (true by default).
  // However, if ResourceRequirements is set, the caller must explicitly
  // set this field to true to preserve the default behavior.
  bool cpu_idle = 2;

  // Determines whether CPU should be boosted on startup of a new container
  // instance above the requested CPU threshold, this can help reduce cold-start
  // latency.
  bool startup_cpu_boost = 3;
}

// EnvVar represents an environment variable present in a Container.
message EnvVar {
  // Required. Name of the environment variable. Must not exceed 32768
  // characters.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  oneof values {
    // Literal value of the environment variable.
    // Defaults to "", and the maximum length is 32768 bytes.
    // Variable references are not supported in Cloud Run.
    string value = 2;

    // Source for the environment variable's value.
    EnvVarSource value_source = 3;
  }
}

// EnvVarSource represents a source for the value of an EnvVar.
message EnvVarSource {
  // Selects a secret and a specific version from Cloud Secret Manager.
  SecretKeySelector secret_key_ref = 1;
}

// SecretEnvVarSource represents a source for the value of an EnvVar.
message SecretKeySelector {
  // Required. The name of the secret in Cloud Secret Manager.
  // Format: {secret_name} if the secret is in the same project.
  // projects/{project}/secrets/{secret_name} if the secret is
  // in a different project.
  string secret = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/Secret"
    }
  ];

  // The Cloud Secret Manager secret version.
  // Can be 'latest' for the latest version, an integer for a specific version,
  // or a version alias.
  string version = 2 [(google.api.resource_reference) = {
    type: "secretmanager.googleapis.com/SecretVersion"
  }];
}

// ContainerPort represents a network port in a single container.
message ContainerPort {
  // If specified, used to specify which protocol to use.
  // Allowed values are "http1" and "h2c".
  string name = 1;

  // Port number the container listens on.
  // This must be a valid TCP port number, 0 < container_port < 65536.
  int32 container_port = 3;
}

// VolumeMount describes a mounting of a Volume within a container.
message VolumeMount {
  // Required. This must match the Name of a Volume.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Path within the container at which the volume should be mounted.
  // Must not contain ':'. For Cloud SQL volumes, it can be left empty, or must
  // otherwise be `/cloudsql`. All instances defined in the Volume will be
  // available as `/cloudsql/[instance]`. For more information on Cloud SQL
  // volumes, visit https://cloud.google.com/sql/docs/mysql/connect-run
  string mount_path = 3 [(google.api.field_behavior) = REQUIRED];
}

// Volume represents a named volume in a container.
message Volume {
  // Required. Volume's name.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  oneof volume_type {
    // Secret represents a secret that should populate this volume.
    SecretVolumeSource secret = 2;

    // For Cloud SQL volumes, contains the specific instances that should be
    // mounted. Visit https://cloud.google.com/sql/docs/mysql/connect-run for
    // more information on how to connect Cloud SQL and Cloud Run.
    CloudSqlInstance cloud_sql_instance = 3;

    // Ephemeral storage used as a shared volume.
    EmptyDirVolumeSource empty_dir = 4;

    // For NFS Voumes, contains the path to the nfs Volume
    NFSVolumeSource nfs = 5;

    // Persistent storage backed by a Google Cloud Storage bucket.
    GCSVolumeSource gcs = 6;
  }
}

// The secret's value will be presented as the content of a file whose
// name is defined in the item path. If no items are defined, the name of
// the file is the secret.
message SecretVolumeSource {
  // Required. The name of the secret in Cloud Secret Manager.
  // Format: {secret} if the secret is in the same project.
  // projects/{project}/secrets/{secret} if the secret is
  // in a different project.
  string secret = 1 [(google.api.field_behavior) = REQUIRED];

  // If unspecified, the volume will expose a file whose name is the
  // secret, relative to VolumeMount.mount_path.
  // If specified, the key will be used as the version to fetch from Cloud
  // Secret Manager and the path will be the name of the file exposed in the
  // volume. When items are defined, they must specify a path and a version.
  repeated VersionToPath items = 2;

  // Integer representation of mode bits to use on created files by default.
  // Must be a value between 0000 and 0777 (octal), defaulting to 0444.
  // Directories within the path are not affected by  this setting.
  //
  // Notes
  //
  // * Internally, a umask of 0222 will be applied to any non-zero value.
  // * This is an integer representation of the mode bits. So, the octal
  // integer value should look exactly as the chmod numeric notation with a
  // leading zero. Some examples: for chmod 640 (u=rw,g=r), set to 0640 (octal)
  // or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx), set to 0755 (octal) or
  // 493 (base-10).
  // * This might be in conflict with other options that affect the
  // file mode, like fsGroup, and the result can be other mode bits set.
  //
  // This might be in conflict with other options that affect the
  // file mode, like fsGroup, and as a result, other mode bits could be set.
  int32 default_mode = 3;
}

// VersionToPath maps a specific version of a secret to a relative file to mount
// to, relative to VolumeMount's mount_path.
message VersionToPath {
  // Required. The relative path of the secret in the container.
  string path = 1 [(google.api.field_behavior) = REQUIRED];

  // The Cloud Secret Manager secret version.
  // Can be 'latest' for the latest value, or an integer or a secret alias for a
  // specific version.
  string version = 2;

  // Integer octal mode bits to use on this file, must be a value between
  // 01 and 0777 (octal). If 0 or not set, the Volume's default mode will be
  // used.
  //
  // Notes
  //
  // * Internally, a umask of 0222 will be applied to any non-zero value.
  // * This is an integer representation of the mode bits. So, the octal
  // integer value should look exactly as the chmod numeric notation with a
  // leading zero. Some examples: for chmod 640 (u=rw,g=r), set to 0640 (octal)
  // or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx), set to 0755 (octal) or
  // 493 (base-10).
  // * This might be in conflict with other options that affect the
  // file mode, like fsGroup, and the result can be other mode bits set.
  int32 mode = 3;
}

// Represents a set of Cloud SQL instances. Each one will be available under
// /cloudsql/[instance]. Visit
// https://cloud.google.com/sql/docs/mysql/connect-run for more information on
// how to connect Cloud SQL and Cloud Run.
message CloudSqlInstance {
  // The Cloud SQL instance connection names, as can be found in
  // https://console.cloud.google.com/sql/instances. Visit
  // https://cloud.google.com/sql/docs/mysql/connect-run for more information on
  // how to connect Cloud SQL and Cloud Run. Format:
  // {project}:{location}:{instance}
  repeated string instances = 1;
}

// In memory (tmpfs) ephemeral storage.
// It is ephemeral in the sense that when the sandbox is taken down, the data is
// destroyed with it (it does not persist across sandbox runs).
message EmptyDirVolumeSource {
  // The different types of medium supported for EmptyDir.
  enum Medium {
    // When not specified, falls back to the default implementation which
    // is currently in memory (this may change over time).
    MEDIUM_UNSPECIFIED = 0;

    // Explicitly set the EmptyDir to be in memory. Uses tmpfs.
    MEMORY = 1;
  }

  // The medium on which the data is stored. Acceptable values today is only
  // MEMORY or none. When none, the default will currently be backed by memory
  // but could change over time. +optional
  Medium medium = 1;

  // Limit on the storage usable by this EmptyDir volume.
  // The size limit is also applicable for memory medium.
  // The maximum usage on memory medium EmptyDir would be the minimum value
  // between the SizeLimit specified here and the sum of memory limits of all
  // containers. The default is nil which means that the limit is undefined.
  // More info:
  // https://cloud.google.com/run/docs/configuring/in-memory-volumes#configure-volume.
  // Info in Kubernetes:
  // https://kubernetes.io/docs/concepts/storage/volumes/#emptydir
  string size_limit = 2;
}

// Represents an NFS mount.
message NFSVolumeSource {
  // Hostname or IP address of the NFS server
  string server = 1;

  // Path that is exported by the NFS server.
  string path = 2;

  // If true, the volume will be mounted as read only for all mounts.
  bool read_only = 3;
}

// Represents a volume backed by a Cloud Storage bucket using Cloud Storage
// FUSE.
message GCSVolumeSource {
  // Cloud Storage Bucket name.
  string bucket = 1;

  // If true, the volume will be mounted as read only for all mounts.
  bool read_only = 2;

  // A list of additional flags to pass to the gcsfuse CLI.
  // Options should be specified without the leading "--".
  repeated string mount_options = 3;
}

// Probe describes a health check to be performed against a container to
// determine whether it is alive or ready to receive traffic.
message Probe {
  // Optional. Number of seconds after the container has started before the
  // probe is initiated. Defaults to 0 seconds. Minimum value is 0. Maximum
  // value for liveness probe is 3600. Maximum value for startup probe is 240.
  int32 initial_delay_seconds = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of seconds after which the probe times out.
  // Defaults to 1 second. Minimum value is 1. Maximum value is 3600.
  // Must be smaller than period_seconds.
  int32 timeout_seconds = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. How often (in seconds) to perform the probe.
  // Default to 10 seconds. Minimum value is 1. Maximum value for liveness probe
  // is 3600. Maximum value for startup probe is 240.
  // Must be greater or equal than timeout_seconds.
  int32 period_seconds = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum consecutive failures for the probe to be considered
  // failed after having succeeded. Defaults to 3. Minimum value is 1.
  int32 failure_threshold = 4 [(google.api.field_behavior) = OPTIONAL];

  oneof probe_type {
    // Optional. HTTPGet specifies the http request to perform.
    // Exactly one of httpGet, tcpSocket, or grpc must be specified.
    HTTPGetAction http_get = 5 [(google.api.field_behavior) = OPTIONAL];

    // Optional. TCPSocket specifies an action involving a TCP port.
    // Exactly one of httpGet, tcpSocket, or grpc must be specified.
    TCPSocketAction tcp_socket = 6 [(google.api.field_behavior) = OPTIONAL];

    // Optional. GRPC specifies an action involving a gRPC port.
    // Exactly one of httpGet, tcpSocket, or grpc must be specified.
    GRPCAction grpc = 7 [(google.api.field_behavior) = OPTIONAL];
  }
}

// HTTPGetAction describes an action based on HTTP Get requests.
message HTTPGetAction {
  // Optional. Path to access on the HTTP server. Defaults to '/'.
  string path = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom headers to set in the request. HTTP allows repeated
  // headers.
  repeated HTTPHeader http_headers = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Port number to access on the container. Must be in the range 1 to
  // 65535. If not specified, defaults to the exposed port of the container,
  // which is the value of container.ports[0].containerPort.
  int32 port = 5 [(google.api.field_behavior) = OPTIONAL];
}

// HTTPHeader describes a custom header to be used in HTTP probes
message HTTPHeader {
  // Required. The header field name
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The header field value
  string value = 2 [(google.api.field_behavior) = OPTIONAL];
}

// TCPSocketAction describes an action based on opening a socket
message TCPSocketAction {
  // Optional. Port number to access on the container. Must be in the range 1 to
  // 65535. If not specified, defaults to the exposed port of the container,
  // which is the value of container.ports[0].containerPort.
  int32 port = 1 [(google.api.field_behavior) = OPTIONAL];
}

// GRPCAction describes an action involving a GRPC port.
message GRPCAction {
  // Optional. Port number of the gRPC service. Number must be in the range 1 to
  // 65535. If not specified, defaults to the exposed port of the container,
  // which is the value of container.ports[0].containerPort.
  int32 port = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Service is the name of the service to place in the gRPC
  // HealthCheckRequest (see
  // https://github.com/grpc/grpc/blob/master/doc/health-checking.md ). If this
  // is not specified, the default behavior is defined by gRPC.
  string service = 2 [(google.api.field_behavior) = OPTIONAL];
}
